###
 # @Author: liulianming
 # @Date: 2025-07-29 09:33:57
 # @LastEditors: liulianming
 # @LastEditTime: 2025-07-29 09:46:26
 # @Description:
###
###
# 自定义的环境变量（命名必须以 VITE_ 开头）
## 系统标题
VITE_SYS_TITLE = '供电远程支持系统'
VITE_SYS_ENTITLE = 'Power Supply Remote Support System'
VITE_SYS_VERSION = '正式版'
VITE_SYS_APP_MODE = 'BASELINE'
VITE_SYS_LOGO = ''

## 后端接口公共路径（如果解决跨域问题采用 CORS 就需要写全路径）
VITE_BASE_API = http://*************:9700
## 系统通用上传地址
VITE_APP_BASE_UPLOAD_URL = /pitaya/system/common/upload

## 系统通用下载地址
VITE_APP_BASE_DOWNLOAD_URL = /pitaya/system/common/static/

## 路由模式 hash 或 html5
VITE_ROUTER_HISTORY = 'hash'

## 打包路径
VITE_PUBLIC_PATH = '/'

## webscoket地址
VITE_APP_BASE_WS = 'http://*************:9700/websocket'

## webscoket 失败重试次数
VITE_APP_BASE_WS_Number_Failures = 5

## websocket 心跳时间
VITE_APP_BASE_WS_TIMER = 5000

##高德地图key
VITE_AMAP_KEY = "1449a66b64d650e97e719c9e19ec73d6"

## 录屏软件地址
VITE_RPSTSS_APP_PATH = "rpstssApp://"
