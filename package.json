{"name": "material-management-system", "version": "1.4.7", "description": "material-management-system-1.4.7", "repository": {"type": "git", "url": "https://codeup.aliyun.com/bmzymtr/bmzy/pitaya-framework-v3-web/tree/master"}, "type": "module", "scripts": {"dev": "vite", "dev:test": "vite --mode testing", "build:debug": "vite build --mode development", "build:test": "vite build --mode testing", "build:prod": "vite build --mode production", "preview:prod": "npm build:prod && vite preview", "lint:eslint": "eslint --cache --max-warnings 0 \"{src,tests,types}/**/*.{vue,js,jsx,ts,tsx}\" --fix", "lint:prettier": "prettier --write \"{src,tests,types}/**/*.{vue,js,jsx,ts,tsx,json,css,less,scss,html,md}\"", "lint": "npm lint:eslint && npm lint:prettier", "test": "vitest"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@chenfengyuan/vue-qrcode": "^2.0.0", "@element-plus/icons-vue": "2.1.0", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-regular-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.4.0", "@fortawesome/vue-fontawesome": "^3.0.8", "@fullcalendar/core": "^6.1.9", "@fullcalendar/daygrid": "^6.1.9", "@fullcalendar/interaction": "^6.1.9", "@fullcalendar/list": "^6.1.9", "@fullcalendar/multimonth": "^6.1.9", "@fullcalendar/react": "^6.1.9", "@fullcalendar/timegrid": "^6.1.9", "@fullcalendar/vue3": "^6.1.9", "@stomp/stompjs": "^7.0.0", "@wecom/jssdk": "^2.2.6", "axios": "1.4.0", "bpmn-js": "^7.3.1", "bpmn-js-properties-panel": "^0.37.2", "bpmn-js-token-simulation": "^0.31.1", "camunda-bpmn-moddle": "^6.0.1", "cropperjs": "^1.6.2", "dayjs": "1.11.9", "echarts": "^5.4.3", "element-plus": "2.3.7", "honkit": "^5.1.1", "js-cookie": "3.0.5", "jsencrypt": "^3.3.2", "less": "^4.2.0", "lodash-es": "4.17.21", "mitt": "^3.0.1", "normalize.css": "8.0.1", "nprogress": "0.2.0", "path-browserify": "1.0.1", "path-to-regexp": "6.2.1", "pinia": "2.1.4", "pinyin": "^4.0.0", "print-js": "^1.6.0", "qrcode.vue": "^3.4.1", "qs": "^6.12.1", "s": "^1.0.0", "screenfull": "6.0.2", "sockjs-client": "^1.6.1", "vue": "3.3.4", "vue-router": "4.2.4", "vxe-table": "4.4.1", "vxe-table-plugin-element": "3.0.7", "x2js": "^3.4.4", "xe-utils": "3.5.11"}, "devDependencies": {"@types/js-cookie": "3.0.3", "@types/lodash-es": "^4.17.9", "@types/node": "20.4.0", "@types/nprogress": "0.2.0", "@types/path-browserify": "1.0.0", "@types/qs": "^6.9.15", "@types/sockjs-client": "^1.5.1", "@typescript-eslint/eslint-plugin": "5.61.0", "@typescript-eslint/parser": "5.61.0", "@vitejs/plugin-vue": "4.2.3", "@vitejs/plugin-vue-jsx": "3.0.1", "@vue/eslint-config-prettier": "7.1.0", "@vue/eslint-config-typescript": "11.0.3", "@vue/test-utils": "2.4.0", "@vuemap/unplugin-resolver": "^2.0.0", "consola": "^3.2.3", "eslint": "8.44.0", "eslint-plugin-prettier": "4.2.1", "eslint-plugin-vue": "9.15.1", "jsdom": "22.1.0", "lint-staged": "13.2.3", "prettier": "2.8.8", "sass": "1.63.6", "terser": "5.18.2", "typescript": "5.1.6", "unplugin-auto-import": "^0.16.7", "unplugin-icons": "^0.16.3", "unplugin-vue-components": "^0.25.2", "vite": "4.4.1", "vite-plugin-checker": "0.6.1", "vite-plugin-node-polyfills": "^0.16.0", "vite-plugin-style-import": "^2.0.0", "vite-plugin-svg-icons": "2.0.1", "vite-svg-loader": "4.0.0", "vitest": "0.33.0", "vue-eslint-parser": "9.3.1", "vue-tsc": "1.8.4"}, "lint-staged": {"*.{vue,js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{css,less,scss,html,md}": ["prettier --write"], "package.json": ["prettier --write"]}, "keywords": ["vue", "vue3", "admin", "vue-admin", "vue3-admin", "vite", "vite-admin", "element-plus", "element-plus-admin", "ts", "typescript"], "license": "MIT"}