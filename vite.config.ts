/// <reference types="vitest" />

import { type ConfigEnv, type UserConfigExport, loadEnv } from "vite"
import path, { resolve } from "path"
import { createSvgIconsPlugin } from "vite-plugin-svg-icons"
import svgLoader from "vite-svg-loader"
import { vitePlugins } from "./src/app/platform/plugins/index"
import Icons from "unplugin-icons/vite"
import fs from "fs"
import checker from "vite-plugin-checker"
import { nodePolyfills } from "vite-plugin-node-polyfills"

//根据获取api 地址
let currentServerIP = ""
let websocket = ""

export default (configEnv: ConfigEnv): UserConfigExport => {
	const viteEnv = loadEnv(configEnv.mode, process.cwd()) as ImportMetaEnv
	const { VITE_PUBLIC_PATH, VITE_BASE_API, VITE_APP_BASE_WS } = viteEnv

	currentServerIP = VITE_BASE_API
	websocket = VITE_APP_BASE_WS

	const optimizeDepsElementPlusIncludes = [
		"element-plus/es",
		"dayjs",
		"path-browserify",
		"@element-plus/icons-vue"
	]
	fs.readdirSync("node_modules/element-plus/es/components").map(
		(dirname: any) => {
			fs.access(
				`node_modules/element-plus/es/components/${dirname}/style/css.mjs`,
				(err: any) => {
					if (!err) {
						optimizeDepsElementPlusIncludes.push(
							`element-plus/es/components/${dirname}/style/css`
						)
					}
				}
			)
		}
	)
	return {
		/** 打包时根据实际情况修改 base */
		base: VITE_PUBLIC_PATH,
		resolve: {
			alias: {
				/** @ 符号指向 src 目录 */
				"@": resolve(__dirname, "./src")
			}
		},
		optimizeDeps: {
			include: optimizeDepsElementPlusIncludes
		},
		server: {
			/** 是否开启 HTTPS */
			https: false,
			/** 设置 host: true 才可以使用 Network 的形式，以 IP 访问项目 */
			host: true, // host: "0.0.0.0"
			/** 端口号 */
			port: 9728,
			/** 是否自动打开浏览器 */
			open: false,
			/** 跨域设置允许 */
			cors: true,
			/** 端口被占用时，是否直接退出 */
			strictPort: false,
			/** 接口代理 */

			proxy: {
				"/pitaya/system": {
					target: currentServerIP,
					ws: true,
					changeOrigin: true
				},
				"/api/device": {
					target: currentServerIP,
					ws: true,
					changeOrigin: true
				},
				"/api/location": {
					target: currentServerIP,
					ws: true,
					changeOrigin: true
				},
				"/websocket": {
					target: websocket,
					ws: true,
					changeOrigin: true
				}
			}
		},
		build: {
			/** 消除打包大小超过 500kb 警告 */
			chunkSizeWarningLimit: 2000,
			/** Vite 2.6.x 以上需要配置 minify: "terser", terserOptions 才能生效 */
			minify: "terser",
			/** 在打包代码时移除 console.log、debugger 和 注释 */
			terserOptions: {
				compress: {
					drop_console: false,
					drop_debugger: true,
					pure_funcs: ["console.log"]
				},
				format: {
					/** 移除注释 */
					comments: false
				}
			},
			/** 打包后静态资源目录 */
			assetsDir: "static"
		},
		/** Vite 插件 */
		plugins: [
			/** 自动导入 */
			...vitePlugins,
			nodePolyfills(),
			checker({
				typescript: true
			}),
			Icons({
				autoInstall: true
			}),
			/** 将 SVG 静态图转化为 Vue 组件 */
			svgLoader({ defaultImport: "url" }),
			/** SVG */
			createSvgIconsPlugin({
				iconDirs: [path.resolve(process.cwd(), "src/icons/svg")],
				symbolId: "icon-[dir]-[name]"
			})
		]
	}
}
