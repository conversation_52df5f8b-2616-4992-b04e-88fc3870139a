{"globals": {"ChineseCharacterAlphanumeric": true, "Component": true, "ComponentPublicInstance": true, "ComputedRef": true, "CustomMessageBox": true, "DirectiveBinding": true, "EffectScope": true, "ExtractDefaultPropTypes": true, "ExtractPropTypes": true, "ExtractPublicPropTypes": true, "InjectionKey": true, "MaybeRef": true, "MaybeRefOrGetter": true, "PropType": true, "Ref": true, "VNode": true, "WritableComputedRef": true, "_matchFormProp": true, "acceptHMRUpdate": true, "alphanumeric": true, "checkPermission": true, "checkPermissionType": true, "checkTree": true, "commonDownLoadFile": true, "computed": true, "copeCVTree": true, "createApp": true, "createPinia": true, "ctrlVTree": true, "customRef": true, "defineAsyncComponent": true, "defineComponent": true, "defineStore": true, "effectScope": true, "email": true, "events": true, "findAllEndNode": true, "findAllEndNodeV2": true, "findAllParentNode": true, "findAllParentNodeV2": true, "formatDateTime": true, "generateMixed": true, "generatePermissionKey": true, "getActivePinia": true, "getActiveThemeName": true, "getCachedViews": true, "getConfigLayout": true, "getCssVariableValue": true, "getCurrentInstance": true, "getCurrentScope": true, "getDicDescEcho": true, "getDictionaryInfo": true, "getDictionaryTree": true, "getFileExtension": true, "getFileHttpUrl": true, "getFileType": true, "getIdList": true, "getNodeList": true, "getOneDic": true, "getParentNode": true, "getSidebarStatus": true, "getToken": true, "getTreeTitle": true, "getValueArrarByKey": true, "getVisitedViews": true, "h": true, "inject": true, "is24H": true, "isArray": true, "isAudioFile": true, "isAudioFileAmr": true, "isCheckPermission": true, "isChineseIdCard": true, "isDomain": true, "isEmail": true, "isEmpty": true, "isExternal": true, "isIPv4": true, "isImageFile": true, "isLicensePlate": true, "isMAC": true, "isNumber": true, "isPhoneNumber": true, "isProxy": true, "isReactive": true, "isReadonly": true, "isRef": true, "isString": true, "isUrl": true, "isUrlPort": true, "isVersion": true, "isVideoFile": true, "letterAndNumber": true, "mapActions": true, "mapGetters": true, "mapState": true, "mapStores": true, "mapWritableState": true, "markRaw": true, "matchPermissionBtnList": true, "matchPermissionBtnListV2": true, "nextTick": true, "number": true, "objectToFormData": true, "onActivated": true, "onBeforeMount": true, "onBeforeRouteLeave": true, "onBeforeRouteUpdate": true, "onBeforeUnmount": true, "onBeforeUpdate": true, "onDeactivated": true, "onErrorCaptured": true, "onMounted": true, "onRenderTracked": true, "onRenderTriggered": true, "onScopeDispose": true, "onServerPrefetch": true, "onUnmounted": true, "onUpdated": true, "onWatcherCleanup": true, "onlyItemArr": true, "password": true, "phone": true, "provide": true, "reactive": true, "readonly": true, "ref": true, "refreshTitle": true, "reg": true, "removeConfigLayout": true, "removeToken": true, "removeTreeItem": true, "removeTreeNode": true, "request": true, "resetConfigLayout": true, "resolveComponent": true, "secIdList": true, "selectTreeNodeByNoRelevance": true, "setActivePinia": true, "setActiveThemeName": true, "setCachedViews": true, "setConfigLayout": true, "setCssVariableValue": true, "setMapStoreSuffix": true, "setSidebarStatus": true, "setToken": true, "setVisitedViews": true, "shallowReactive": true, "shallowReadonly": true, "shallowRef": true, "smallNumber": true, "sortArr": true, "storeToRefs": true, "toRaw": true, "toRef": true, "toRefs": true, "toValue": true, "triggerRef": true, "unref": true, "upwardAccumulationByOneKey": true, "useAttrs": true, "useCssModule": true, "useCssVars": true, "useId": true, "useLink": true, "useModel": true, "useRoute": true, "useRouter": true, "useSlots": true, "useTemplateRef": true, "validateCodeName": true, "validateEmpty": true, "validateLengthAndEmpty": true, "validateProcessCode": true, "versionList": true, "watch": true, "watchEffect": true, "watchPostEffect": true, "watchSyncEffect": true, "ElMessage": true}}