// core
import { createApp } from "vue"
import App from "@/App.vue"
import store from "@/app/platform/store"
import router from "@/router"
import "@/router/permission"
import { loadVxeTablePlugins } from "@/app/platform/plugins/vxeTable"
import { loadDirectives } from "@/app/platform/directives/index"
import button from "@/config/button"
import { debounceDirective } from "@/app/platform/directives/debounce"
// css
// import "element-plus/dist/index.css"
import "element-plus/theme-chalk/dark/css-vars.css"
import "element-plus/theme-chalk/el-message.css"
import "element-plus/theme-chalk/el-message-box.css"
import "@/styles/index.scss"
// svg
import "virtual:svg-icons-register"

// 以下为bpmn工作流绘图工具的样式
// 左边工具栏以及编辑节点的样式
import "bpmn-js/dist/assets/diagram-js.css"
import "bpmn-js/dist/assets/bpmn-font/css/bpmn.css"
import "bpmn-js/dist/assets/bpmn-font/css/bpmn-codes.css"
import "bpmn-js/dist/assets/bpmn-font/css/bpmn-embedded.css"
// 右边工具栏样式
import "bpmn-js-properties-panel/dist/assets/bpmn-js-properties-panel.css"

// fontawesome
import { library } from "@fortawesome/fontawesome-svg-core"
import { fas, faB } from "@fortawesome/free-solid-svg-icons"
import { far } from "@fortawesome/free-regular-svg-icons"
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome"

// @ts-ignore
library.add(fas, far, faB)

// 日期时间本地化
import dayjs from "dayjs"
import "dayjs/locale/zh-cn"
dayjs.locale("zh-cn")

const app = createApp(App)
app.component("font-awesome-icon", FontAwesomeIcon)

/** 加载插件 */
loadVxeTablePlugins(app)
/** 加载自定义指令 */
loadDirectives(app)
app.use(button)
app.use(store).use(router)
app.directive("debounce", debounceDirective)

router.isReady().then(() => {
	app.mount("#app")
})
