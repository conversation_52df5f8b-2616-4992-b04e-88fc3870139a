<script lang="ts" setup>
import { useTheme } from "@/app/platform/hooks/useTheme"
// 将 Element Plus 的语言设置为中文
import zhCn from "element-plus/es/locale/lang/zh-cn"
import { onBeforeUnmount, onMounted } from "vue"
import { closeTimeLog } from "../src/app/platform/api/login"
import { getToken } from "../src/app/platform/utils/cache/cookies"

console.log("获取getToken", getToken())
const { initTheme } = useTheme()

/** 初始化主题 */
initTheme()

//刷新调用接口记录用户登录记录
if (window.sessionStorage["tempFlag"]) {
	if (getToken()) {
		getCloseTimeLogn()
	}
}

function getCloseTimeLogn() {
	closeTimeLog().then((res: any) => {})
}
const handleBeforeUnload = (event: BeforeUnloadEvent) => {
	if (getToken()) {
		getCloseTimeLogn()
	}
}
window.removeEventListener("beforeunload", handleBeforeUnload)

onMounted(() => {
	window.sessionStorage["tempFlag"] = true
	window.addEventListener("beforeunload", handleBeforeUnload)
})

onBeforeUnmount(() => {
	window.removeEventListener("beforeunload", handleBeforeUnload)
	// 此时，你可以认为事件监听器已经被销毁了
	console.log("beforeunload 事件监听器已移除")
})
</script>

<template>
	<ElConfigProvider :locale="zhCn">
		<router-view />
	</ElConfigProvider>
</template>
