@import "define";
.el-timeline.customer {
	padding: $---spacing-m 0px;
	.el-timeline-item {
		padding-bottom: $---spacing-m;
		:deep(.el-timeline-item__tail) {
			left: 6px;
		}
		:deep(.el-timeline-item__node--normal) {
			border-radius: 100%;
			width: $---font-size-l;
			height: $---font-size-l;
			left: 0px;
			.el-timeline-item__icon {
				font-size: $---font-size-s;
				color: #ffffff;
			}
		}
		&.status-success {
			:deep(.el-timeline-item__node--normal) {
				background: $---color-success;
			}
		}
		&.status-faild {
			:deep(.el-timeline-item__node--normal) {
				background: $---color-error;
			}
		}
	}
	.el-timeline-item__content {
		> * {
			padding-bottom: $---spacing-m;
		}
		svg {
			padding-right: $---spacing-s;
		}
	}

	.result {
		font-size: $---font-size-m;
		color: $---font-color-2;
		background: var(--pitaya-body-bg-color);
		padding: $---spacing-s;
		border-radius: $---border-radius-m;
		border: 1px solid var(--pitaya-border-color);
		margin-bottom: $---spacing-m;
	}

	.el-form {
		.el-form-item {
			margin-bottom: $---spacing-m;
			&:first-child {
				border: 1px solid var(--pitaya-border-color);
				background: var(--pitaya-body-bg-color);
				border-bottom: 0px;
				margin-bottom: 0px;
				border-radius: $---border-radius-m $---border-radius-m 0px 0px;
				:deep(.el-form-item__label) {
					margin: 0px $---spacing-l;
					padding: 0px;
				}
			}
		}
		.el-select {
			width: 100%;
			:deep(.el-input__wrapper) {
				box-shadow: none;
				border-left: 1px solid var(--el-border-color);
				border-radius: 0px $---border-radius-m 0px 0px;
			}
		}
		:deep(textarea) {
			border-radius: 0px 0px $---border-radius-m $---border-radius-m;
			border: 1px solid var(--pitaya-border-color);
			box-shadow: none;
		}
	}
}
.el-descriptions {
	color: var(--pitaya-table-font-color);
	:deep(.el-descriptions__header) {
		margin-bottom: 10px;
	}
	:deep(.el-descriptions__title) {
		color: var(--pitaya-table-font-color) !important;
		font-size: 14px;
	}
	:deep(.el-descriptions__cell) {
		border: 1px solid var(--pitaya-border-color) !important;
		color: var(--pitaya-table-font-color) !important;
		&.title {
			border: none !important;
			background: transparent;
			font-size: 14px;
			font-weight: bold;
			padding: 10px 0px !important;
			~ * {
				border: none !important;
				background: transparent;
			}
		}
	}
	:deep(.el-descriptions__label) {
		width: 180px;
		padding: 0 10px;
		background-color: #f6f6f6;
		height: 100%;
		min-height: 31px;
		font-weight: bold;
		text-align: center !important;
	}
	:deep(.el-descriptions__content) {
		width: 300px;
		word-break: break-all;
	}
	.el-rate {
		height: 16px;
		display: flex;
	}
}
.el-button {
	color: $---font-color-4;
	svg {
		color: $---font-color-4 !important;
	}

	&.is-disabled {
		color: $---font-color-3 !important;
		svg {
			color: $---font-color-3 !important;
		}
		.table-inner-btn {
			color: $---font-color-3 !important;
		}
	}
}
.form-base {
	.el-form-item {
		margin-bottom: $---spacing-m !important;
	}

	::v-deep(.el-input-group__append) {
		font-size: $---font-size-m;
		padding: 0 $---spacing-m !important;
	}

	::v-deep(.el-date-editor--daterange) {
		.el-input__icon {
			position: relative;
			left: calc(100% - 15px);
		}
		.el-range-input,
		.el-range-separator {
			position: relative;
			left: -20px;
		}
	}
}
:deep(.el-table__cell) {
	.number-input {
		height: 26px;
		line-height: 26px;
		width: 90px;
		.el-input {
			height: 100%;
		}
		:deep(.el-input__inner) {
			height: 100%;
		}
	}
}

.pitaya-table {
	:deep(.underline-link) {
		line-height: 12px !important;
	}
	/* ::v-deep(.el-table__row) {
		td:not(.is-hidden):last-child {
			border-left: 1px solid var(--el-border-color);
		}
	}
	::v-deep(.el-table__header) {
		th:not(.is-hidden):last-child {
			border-left: 1px solid var(--el-border-color);
		}
	} */
}

.no-arrows {
	:deep() {
		input::-webkit-outer-spin-button,
		input::-webkit-inner-spin-button {
			-webkit-appearance: none !important;
		}

		input[type="number"] {
			-moz-appearance: textfield;
		}
	}
}
