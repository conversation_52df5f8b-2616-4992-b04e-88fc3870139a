@import "define";

//主要文字-1
.font-1 {
	font-size: $---font-size-l;
	color: $---font-color-1;
}

//主要文字-2
.font-2 {
	font-size: $---font-size-m;
	color: $---font-color-1;
}

//辅助文字-1
.font-3 {
	font-size: $---font-size-l;
	color: $---font-color-2;
}

//辅助文字-2
.font-4 {
	font-size: $---font-size-m;
	color: $---font-color-2;
}

//次要文字-1
.font-5 {
	font-size: $---font-size-l;
	color: $---font-color-3;
}

//辅助文字-2
.font-6 {
	font-size: $---font-size-m;
	color: $---font-color-3;
}

//链接文字-1
.font-7 {
	font-size: $---font-size-l;
	color: $---font-color-4;
}

//链接文字-2
.font-7 {
	font-size: $---font-size-m;
	color: $---font-color-4;
}

.fix {
	width: 100%;
	height: 100%;
}

.flex-row-between {
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: space-between;
}

.common-btn-list-wrapper {
	&.end {
		justify-content: flex-end;
	}
}

.content {
	padding: $---spacing-m;
	height: calc(100% - $---spacing-m * 2);
}

.app-container {
	display: flex;
	align-items: center;
	height: 100%;
	justify-content: space-between;
	flex-direction: row;

	.top-frame,
	.bottom-frame,
	.left-frame,
	.right-frame,
	.whole-frame {
		display: flex;
		flex-direction: column;
		position: relative;
		box-sizing: border-box;
		height: 100%;
		width: 100%;
	}

	.top-frame {
		width: 100%;
		margin-bottom: $---spacing-m;
		height: auto;
		&.unborder {
			border: none;
		}
		& + * {
			margin-top: 0px;
		}
	}

	.left-frame {
		width: $--app-container-left-width;
	}

	.right-frame {
		width: calc(100% - $--app-container-left-width - $---spacing-m);
	}
}

.drawer-container {
	display: flex;
	align-items: center;
	height: 100%;

	.drawer-column {
		display: flex;
		flex-direction: column;
		position: relative;
		box-sizing: border-box;
		height: 100%;
		//overflow-x: auto;

		.rows {
			height: 0;
			flex: 1;
			overflow: hidden;
		}

		.rows-auto {
			flex: 0;
			height: auto;
			padding-bottom: $---spacing-m;
		}

		.rows,
		.rows-auto {
			.content {
				padding: $---spacing-m;
			}
			.common-title-wrapper {
				justify-content: flex-start;
				gap: ceil($---spacing-l * 2);
				.common-title-left {
					background: red;
				}
			}
		}

		.footer {
			justify-content: flex-end;
			flex: 0;
			border-top: 1px solid $---border-color;
			padding: $---spacing-m $---spacing-m 0 0;
		}

		.title-tabs {
			position: absolute;
			left: 130px;
			cursor: pointer;
		}

		.title-tabs::after {
			content: "";
			position: absolute;
			left: 0;
			bottom: -10px;
			height: 2px;
			width: 100%;
			background-color: var(--pitaya-btn-background);
		}
	}

	.right {
		padding-left: $---spacing-m;
	}

	.mid {
		padding: 0px $---spacing-m;
	}

	.left {
		padding-right: $---spacing-m;
	}

	.pdr10 {
		padding-right: 10px;
	}

	.left::after,
	.mid::after {
		content: "";
		position: absolute;
		right: 0;
		top: -10px;
		width: 1px;
		height: calc(100% + 20px);
		background-color: #ccc;
	}
}

.el-drawer {
	.app-container {
		padding: 0px;
		.right-frame {
			width: calc(100% - $--app-container-left-width);
		}
	}
}

.disabled {
	pointer-events: none;
	opacity: 0.3;
	/* 其他样式，根据需要自定义 */
}
