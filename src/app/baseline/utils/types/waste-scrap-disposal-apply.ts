/**
 * @description 废旧 - 报废处置 TS
 */

/**
 * 报废处置单 主列表分页 入参
 *
 * MatWasteScrapDisposalApplyVoRequest
 */
export interface MatWasteScrapDisposalApplyVoRequest {
	/**
	 * 审批状态:0待提交 1审批中 2已审批 3已驳回 4审批任务
	 */
	bpmStatus?: string
	/**
	 * 报废处置编号
	 */
	code?: string
	currentPage?: number
	/**
	 * 处置方式
	 */
	disposalType?: string
	/**
	 * 报废申请名称
	 */
	label?: string
	/**
	 * 废旧物资分类,值=0 1 2 (0:A类 1:B类 2:C类)
	 */
	materialType?: string
	pageSize?: number
	/**
	 * 申请人姓名
	 */
	realname?: string
	sidx?: string
	sord?: string
	/**
	 * 报废处置状态:0草稿 1审批中 2待询价 3待处置 4已完成
	 */
	status?: string
	[property: string]: any
}

/**
 * 报废处置-状态数量 返回数据
 *
 * MatWasteScrapDisposalApplyStatusVo
 */
export interface MatWasteScrapDisposalApplyStatusVo {
	/**
	 * 审批中数量
	 */
	activatingCnt?: number | null
	/**
	 * 已完成状态数量
	 */
	completedCnt?: number | null
	/**
	 * 草稿状态数量
	 */
	draftCnt?: number | null
	/**
	 * 待处置状态数量
	 */
	tobedisposedCnt?: number | null
	/**
	 * 待询价状态数量
	 */
	tobeevaluatedCnt?: number | null
	[property: string]: any
}

/**
 * 报废处置单 添加/编辑 入参
 * MatWasteScrapDisposalApplyDTO
 */
export interface MatWasteScrapDisposalApplyDTO {
	/**
	 * 审批状态_view
	 */
	bpmStatus?: null | string
	/**
	 * 申请人_view
	 */
	createdBy?: null | string
	/**
	 * 申请时间
	 */
	createdDate?: null | string
	/**
	 * 处置决策依据
	 */
	decisionBasis?: null | string
	/**
	 * 处置方式
	 */
	disposalType?: null | string
	/**
	 * 报废处置主键
	 */
	id?: number | null
	/**
	 * 报废申请名称
	 */
	label?: null | string
	/**
	 * 废旧物资分类,值=0 1 2 (0:A类 1:B类 2:C类)
	 */
	materialType?: null | string
	/**
	 * 备注
	 */
	remark?: null | string
	/**
	 * 报废处置状态:1草稿 2待询价 3待处置 4已完成
	 */
	status?: null | string
	/**
	 * 所属公司_view
	 */
	sysCommunityId?: number | null
	[property: string]: any
}

/**
 * 报废处置单 添加/编辑/主列表分页 返回数据
 *
 * MatWasteScrapDisposalApplyVo
 */
export interface MatWasteScrapDisposalApplyVo {
	/**
	 * 审批状态_view
	 */
	bpmStatus?: null | string
	/**
	 * 报废处置编号
	 */
	code?: null | string
	/**
	 * 申请人_view
	 */
	createdBy?: null | string
	/**
	 * 申请时间
	 */
	createdDate?: null | string
	/**
	 * 处置决策依据
	 */
	decisionBasis?: null | string
	/**
	 * 处置方式
	 */
	disposalType?: null | string
	/**
	 * 报废处置主键
	 */
	id?: number | null
	/**
	 * 报废申请名称
	 */
	label?: null | string
	/**
	 * 废旧物资分类,值=0 1 2 (0:A类 1:B类 2:C类)
	 */
	materialType?: null | string
	/**
	 * 备注
	 */
	remark?: null | string
	/**
	 * 报废处置状态:1草稿 2待询价 3待处置 4已完成
	 */
	status?: null | string
	/**
	 * 所属公司_view
	 */
	sysCommunityId?: number | null
	[property: string]: any
}

/**
 * 报废处置-扩展信息 - 分页 入参
 */
export interface MatWasteScrapDisposalApplyItemVoQuery {
	currentPage?: number
	id: number
	pageSize?: number
	sidx?: string
	sord?: string
	[property: string]: any
}
/**
 * 报废处置-扩展信息-物资明细-存放仓库 - 分页 入参
 */
export interface CkWasteDisposalStoreMaterialVoQuery {
	currentPage?: number
	id: number
	materialId: number
	pageSize?: number
	sidx?: string
	sord?: string
	[property: string]: any
}

/**
 * 报废处置-待询价-报废询价报告-添加报废询价报告 入参
 * MatWasteScrapDisposalReportDTO
 */
export interface MatWasteScrapDisposalReportDTO {
	/**
	 * 报废询价金额
	 */
	amount?: number | null
	/**
	 * 对应matWasteScrapDisposalApply的主键
	 */
	applyId?: number | null
	/**
	 * 附件多个id，JSON数组形式
	 */
	attachmentId?: { key?: { [key: string]: any }; [property: string]: any }[]
	/**
	 * 报废处置-报废询价报告主键
	 */
	id?: number | null
	/**
	 * 报废询价机构
	 */
	orgLabel?: null | string
	/**
	 * 报废询价报告书编号
	 */
	reportCode?: null | string
	/**
	 * 所属公司_view
	 */
	sysCommunityId?: number | null
	[property: string]: any
}

/**
 * 报废处置-待询价-报废询价报告 返回参数
 *
 * MatWasteScrapDisposalReportVo
 */
export interface MatWasteScrapDisposalReportVo {
	/**
	 * 报废询价金额
	 */
	amount?: number | null
	/**
	 * 对应matWasteScrapDisposalApply的主键
	 */
	applyId?: number | null
	/**
	 * 附件多个id，JSON数组形式
	 */
	attachmentId?: { key?: { [key: string]: any } }[] | null
	/**
	 * 附件信息列表
	 */
	attachmentList?: AttachmentVo[] | null
	/**
	 * 报废处置-报废询价报告主键
	 */
	id?: number | null
	/**
	 * 报废询价机构
	 */
	orgLabel?: null | string
	/**
	 * 报废询价报告书编号
	 */
	reportCode?: null | string
	/**
	 * 所属公司_view
	 */
	sysCommunityId?: number | null
	[property: string]: any
}

/**
 * 报废处置-待询价-报废询价报告 返回参数 附件
 *
 * AttachmentVo
 */
export interface AttachmentVo {
	businessId?: number | null
	businessType?: number | null
	createdBy?: null | string
	createdDate?: null | string
	customFileName?: null | string
	fileName?: null | string
	fileNo?: null | string
	filePath?: null | string
	fileSize?: number | null
	fileType?: null | string
	id?: number | null
	lastModifiedBy?: null | string
	lastModifiedDate?: null | string
	readableFileSize?: null | string
	versionControlId?: number | null
	[property: string]: any
}

/**
 * 报废处置-待询价-处置结果  返回数据
 *
 * MatWasteScrapDisposalResultVo
 */
export interface MatWasteScrapDisposalResultVo {
	/**
	 * 代理机构
	 */
	agency?: null | string
	/**
	 * 对应matWasteScrapDisposalApply的主键
	 */
	applyId?: number | null
	/**
	 * 受让方名称
	 */
	assigneeName?: null | string
	/**
	 * 附件多个id，JSON数组形式
	 */
	attachmentId?: { key?: { [key: string]: any } }[] | null
	/**
	 * 附件信息列表
	 */
	attachmentList?: AttachmentVo[] | null
	/**
	 * 处置协议合同编号
	 */
	contractCode?: null | string
	/**
	 * 处置收益
	 */
	dispoalIncome?: number | null
	/**
	 * 处置成本
	 */
	disposalcost?: number | null
	/**
	 * 报废处置-处置结果主键
	 */
	id?: number | null
	/**
	 * 所属公司_view
	 */
	sysCommunityId?: number | null
	[property: string]: any
}
