/**
 * 段区-分页查询 返参
 *
 * SystemDepotVo
 */
export interface SystemDepotVo {
	/**
	 * 段区编码
	 */
	code?: null | string
	/**
	 * 创建人
	 */
	createdBy?: null | string
	/**
	 * 创建时间
	 */
	createdDate?: null | string
	id?: number | null
	/**
	 * 段区名称
	 */
	label?: null | string
	/**
	 * 更新人
	 */
	lastModifiedBy?: null | string
	/**
	 * 更新时间
	 */
	lastModifiedDate?: null | string
	/**
	 * 备注
	 */
	remark?: null | string
	/**
	 * 段区状态:0待启用、1已启用、2已停用
	 */
	status?: null | string
	/**
	 * 公司ID
	 */
	sysCommunityId?: number | null
	[property: string]: any
}

/**
 * 段区-分页查询 入参
 */
export interface systemDepotPagedRequest {
	currentPage?: number
	pageSize?: number
	sidx?: string
	sord?: string
	[property: string]: any
}

/**
 * 段区-新建 入参
 * SystemDepotDTO
 */
export interface SystemDepotDTO {
	/**
	 * 段区编码
	 */
	code?: null | string
	id?: number | null
	/**
	 * id集合
	 */
	idList?: number[] | null
	/**
	 * 段区名称
	 */
	label?: null | string
	/**
	 * 备注
	 */
	remark?: null | string
	/**
	 * 段区状态:0待启用、1已启用、2已停用
	 */
	status?: null | string
	/**
	 * 公司ID
	 */
	sysCommunityId?: number | null
	[property: string]: any
}

/**
 * 段区状态:0待启用、1已启用、2已停用
 */
export enum IDepotStatus {
	Drafted = "0", //待启用/草稿
	Started = "1", //已启用
	Disabled = "2" //停用
}
