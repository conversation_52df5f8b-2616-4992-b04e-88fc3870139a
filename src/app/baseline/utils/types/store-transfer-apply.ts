/**
 * 调拨申请 主列表 参数
 */
export interface MatStoreAllocationApplyRequestParams {
	/**
	 * 出库成本中心
	 */
	costCenterId?: number
	/**
	 * 入库成本中心
	 */
	costCenterInId?: number
	currentPage?: number
	/**
	 * 出库区段
	 */
	depotId?: number
	/**
	 * 入库区段
	 */
	depotInId?: number
	/**
	 * ID
	 */
	id?: number
	/**
	 * 是否跨成本中心
	 */
	isCrossCostCenter?: boolean
	/**
	 * 调拨申请名称
	 */
	label?: string
	pageSize?: number
	/**
	 * 调拨原因说明
	 */
	reason?: string
	sidx?: string
	sord?: string
	/**
	 * 出库仓库
	 */
	storeId?: number
	/**
	 * 入库仓库
	 */
	storeInId?: number
	/**
	 * 申请部门
	 */
	sysOrgId?: number
	/**
	 * tab页类型(1:草稿箱；2：审批中；3：已审批)
	 */
	tableType?: number
	/**
	 * 移库类型
	 */
	type?: string
	[property: string]: any
}

export interface MatStoreAllocationApplyByIdRequestParams {
	id: number
	[property: string]: any
}

/**
 * MatStoreAllocationApplyVO
 */
export interface MatStoreAllocationApplyVO {
	/**
	 * 移库总金额
	 */
	amount?: number | null
	/**
	 * 申请时间
	 */
	applyDate?: null | string
	/**
	 * 审批状态：0-待提交、1-审批中、2-已审批、3-已驳回
	 */
	bpmStatus?: null | string
	/**
	 * 调拨单号
	 */
	code?: null | string
	/**
	 * 出库成本中心
	 */
	costCenterId?: number | null
	/**
	 * 入库成本中心
	 */
	costCenterInId?: number | null
	/**
	 * 出库区段
	 */
	depotId?: number | null
	/**
	 * 入库区段
	 */
	depotInId?: number | null
	/**
	 * ID
	 */
	id?: number | null
	/**
	 * 是否跨成本中心
	 */
	isCrossCostCenter?: boolean | null
	/**
	 * 调拨申请名称
	 */
	label?: null | string
	/**
	 * 物资编码
	 */
	materialNum?: number | null
	/**
	 * 调拨原因说明
	 */
	reason?: null | string
	/**
	 * 出库仓库Id
	 */
	storeId?: number | null
	/**
	 * 入库仓库
	 */
	storeInId?: number | null
	/**
	 * 入库仓库名称
	 */
	storeInName?: null | string
	/**
	 * 出库仓库名称
	 */
	storeName?: null | string
	/**
	 * 申请部门
	 */
	sysOrgId?: number | null
	/**
	 * 申请部门名称
	 */
	sysOrgName?: null | string
	/**
	 * 移库类型
	 */
	type?: null | string
	/**
	 * 申请人
	 */
	userName?: null | string
	[property: string]: any
}

/**
 * 调拨申请 详情  参数
 */
export interface MatStoreAllocationApplyItemRequestParams {
	/**
	 * 采购单价
	 */
	amount?: string
	/**
	 * 调拨申请Id
	 */
	applyId?: number
	/**
	 * 领料出库单明细ID
	 */
	applyItemId?: number
	/**
	 * 批次号
	 */
	batchNo?: string
	createdBy?: string
	createdDate?: string
	currentPage?: number
	/**
	 * ID
	 */
	id?: number
	lastModifiedBy?: string
	lastModifiedDate?: string
	/**
	 * 物资编码
	 */
	materialCode?: string
	/**
	 * 物资编码Id
	 */
	materialId?: number
	/**
	 * 物资名称
	 */
	materialName?: string
	/**
	 * 移库数量
	 */
	num?: string
	pageSize?: number
	/**
	 * 来源货位Id
	 */
	roomId?: number
	/**
	 * 目标货位Id
	 */
	roomInId?: number
	sidx?: string
	sord?: string
	/**
	 * 来源仓库Id
	 */
	storeId?: number
	/**
	 * 目标仓库Id
	 */
	storeInId?: number
	[property: string]: any
}
/**
 * 调拨申请明细VO
 *
 * MatStoreAllocationApplyItemVO
 */
export interface MatStoreAllocationApplyItemVO {
	/**
	 * 单价
	 */
	amount?: number | null
	/**
	 * 调拨申请Id
	 */
	applyId?: number | null
	/**
	 * 领料出库单明细ID
	 */
	applyItemId?: number | null
	/**
	 * 批次号
	 */
	batchNo?: null | string
	/**
	 * 冻结数量
	 */
	frozenNum?: number | null
	/**
	 * ID
	 */
	id?: number | null
	/**
	 * 物资编码
	 */
	materialCode?: null | string
	/**
	 * 物资编码Id
	 */
	materialId?: number | null
	/**
	 * 物资名称
	 */
	materialName?: null | string
	/**
	 * 移库数量
	 */
	num?: number | null
	/**
	 * 来源区域ID
	 */
	regionId?: number | null
	/**
	 * 目标区域ID
	 */
	regionInId?: number | null
	/**
	 * 目标区域Name
	 */
	regionInLabel?: null | string
	/**
	 * 来源区域名称
	 */
	regionLabel?: null | string
	/**
	 * 库位数量
	 */
	roomNum?: number | null
	/**
	 * 来源货位编码
	 */
	sourceRoomCode?: null | string
	/**
	 * 来源货位
	 */
	sourceRoomId?: number | null
	/**
	 * 目标货位编码
	 */
	targetRoomCode?: null | string
	/**
	 * 目标货位
	 */
	targetRoomId?: number | null
	/**
	 * 技术参数
	 */
	technicalParameter?: null | string
	/**
	 * 库存单位
	 */
	useUnit?: null | string
	/**
	 * 品牌型号
	 */
	version?: null | string
	[property: string]: any
}

/**
 * MatStoreAllocationApplyItemAddAndEditDTO
 */
export interface MatStoreAllocationApplyItemDTORequestParams {
	/**
	 * 调拨申请Id
	 */
	applyId?: number | null
	/**
	 * 添加的明细集合
	 */
	dtoList?: MatStoreAllocationApplyItemDTO[] | null
	[property: string]: any
}
export interface MatStoreAllocationApplyItemAddResp {
	items?: MatStoreAllocationApplyItemVO[]
	isOutOfStore: Boolean
	ids?: number[] | null
}
/**
 * 领料申请明细DTO
 *
 * MatStoreAllocationApplyItemDTO
 */
export interface MatStoreAllocationApplyItemDTO {
	/**
	 * 采购单价
	 */
	amount?: number | null
	/**
	 * 调拨申请Id
	 */
	applyId?: number | null
	/**
	 * 领料出库单明细ID
	 */
	applyItemId?: number | null
	/**
	 * 批次号
	 */
	batchNo?: null | string
	createdBy?: null | string
	createdDate?: null | string
	/**
	 * ID
	 */
	id?: number | null
	lastModifiedBy?: null | string
	lastModifiedDate?: null | string
	/**
	 * 物资编码
	 */
	materialCode?: null | string
	/**
	 * 物资编码Id
	 */
	materialId?: number | null
	/**
	 * 物资名称
	 */
	materialName?: null | string
	/**
	 * 移库数量
	 */
	num?: number | null
	parameterMap?: MapString
	/**
	 * 货位Id
	 */
	roomId?: number | null
	/**
	 * 目标货位Id
	 */
	roomInId?: number | null
	/**
	 * 仓库Id
	 */
	storeId?: number | null
	/**
	 * 目标仓库Id
	 */
	storeInId?: number | null
	[property: string]: any
}

/**
 * Map«String[]»
 */
export interface MapString {
	key?: string[] | null
	[property: string]: any
}

/**
 * 调拨申请 删除参数
 *
 * MatStoreAllocationApplyItemDTO
 */
export interface DelTransferApplyRequestParams {
	id?: string
	[property: string]: any
}

/**
 * 调拨申请 物资明细 - 删除参数
 *
 */
export interface DelMatTransferApplyRequestParams {
	ids?: string
	[property: string]: any
}

/**
 * 添加/编辑 保存草搞
 * MatStoreAllocationApplyDTO
 */
export interface MatStoreAllocationApplyDTO {
	/**
	 * 出库成本中心
	 */
	costCenterId?: number | null
	/**
	 * 入库成本中心
	 */
	costCenterInId?: number | null
	/**
	 * 出库区段
	 */
	depotId?: number | null
	/**
	 * 入库区段
	 */
	depotInId?: number | null
	/**
	 * ID
	 */
	id?: number | null
	/**
	 * 是否跨成本中心
	 */
	isCrossCostCenter?: boolean | null
	/**
	 * 调拨申请名称
	 */
	label?: null | string
	parameterMap?: MapString
	/**
	 * 调拨原因说明
	 */
	reason?: null | string
	/**
	 * 出库仓库
	 */
	storeId?: number | null
	/**
	 * 入库仓库
	 */
	storeInId?: number | null
	/**
	 * 申请部门
	 */
	sysOrgId?: number | null
	/**
	 * tab页类型(1:草稿箱；2：审批中；3：已审批)
	 */
	tableType?: number | null
	/**
	 * 移库类型
	 */
	type?: null | string
	[property: string]: any
}

/**
 * 物资列表
 * BatchMaterialParams
 */
export interface BatchMaterialRequestParams {
	storeId: number
}

/**
 * 调拨申请--物资明细--冻结数量下钻 入参
 */
export interface MatStoreFreezeQueryParams {
	/**
	 * 批次号
	 */
	batchNo: string
	currentPage: number
	/**
	 * 物资编码Id
	 */
	materialId: number
	pageSize: number
	sidx?: string
	sord?: string
	/**
	 * 来源货位Id
	 */
	sourceRoomId: number
	[property: string]: any
}
