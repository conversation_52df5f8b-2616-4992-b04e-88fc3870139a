/* 出库管理 */
/**
 * 领料出库 Params
 */
export interface MatOutStorePickRequestParams {
	/**
	 * 出库单号
	 */
	code?: string
	/**
	 * 公司code
	 */
	companyCode?: string
	currentPage?: number
	/**
	 * id
	 */
	id?: number
	pageSize?: number
	/**
	 * 前置业务单号/领料申请单号
	 */
	preBusinessCode?: string
	/**
	 * 出库仓库ID
	 */
	storeId?: string
	/**
	 * 公司Id
	 */
	sysCommunityId?: number
	/**
	 * table类型【必传字段：1：待出库、2：已出库、3：已关闭 4:部分出库】
	 */
	tableType: string
	/**
	 * 申请人姓名
	 */
	userName?: string
	[property: string]: any
}
/**
 * 领料出库 VO
 */
export interface MatOutStorePickVO {
	/**
	 * 申请时间
	 */
	applyDate?: null | string
	/**
	 * 出库完成时间
	 */
	applyFinishDate?: null | string
	/**
	 * 出库单号
	 */
	code?: null | string
	/**
	 * ID
	 */
	id?: number | null
	/**
	 * 出库物资编码
	 */
	outCount?: number | null
	/**
	 * 已出库物资编码
	 */
	outedCount?: number | null
	/**
	 * 前置业务单号/领料申请单号
	 */
	preBusinessCode?: null | string
	/**
	 * 前置业务Id
	 */
	preBusinessId?: number | null
	/**
	 * 出库状态 1：未出库、2：部分出库、3：已出库、 4.已关闭
	 */
	status?: null | string
	/**
	 * 仓库ID
	 */
	storeId?: number | null
	/**
	 * 出库仓库
	 */
	storeName?: null | string
	/**
	 * 待出库物资编码
	 */
	toOutCount?: number | null
	/**
	 * 入库类型
	 */
	type?: null | string
	/**
	 * 申请人姓名
	 */
	userId?: number | null
	/**
	 * 申请人姓名
	 */
	userName?: null | string
	/**
	 * 关联业务单号
	 */
	workOrderCode?: null | string
	/**
	 * 关联业务单Id
	 */
	workOrderId?: number | null
	[property: string]: any
}

/**
 * 领料出库明细 Params
 */
export interface MatOutStorePickItemRequestParams {
	/**
	 * 领料出库ID
	 */
	applyId?: number
	currentPage?: number
	/**
	 * ID
	 */
	id?: number
	pageSize?: number
	/**
	 * 状态
	 */
	status?: string
	[property: string]: any
}

/**
 * 领料出库明细对象VO
 *
 * MatOutStorePickItemVO
 */
export interface MatOutStorePickItemVO {
	/**
	 * 申请单Id
	 */
	applyId?: number | null
	/**
	 * 领料申请数量
	 */
	applyOutCount?: number | null
	/**
	 * 批次ID
	 */
	batchId?: number | null
	/**
	 * 出库批次号
	 */
	batchNo?: null | string
	/**
	 * 本次出库数量
	 */
	currentOutCount?: number | null
	/**
	 * ID
	 */
	id?: number | null
	/**
	 * 入库单价
	 */
	inStorePrice?: number | null
	/**
	 * 入库时间
	 */
	inStoreUpdateTime?: null | string
	/**
	 * 物资编码
	 */
	materialCode?: null | string
	/**
	 * 物资ID
	 */
	materialId?: number | null
	/**
	 * 物资名称
	 */
	materialName?: null | string
	/**
	 * 出库批次
	 */
	outBatchTimes?: number | null
	/**
	 * 已出库数量
	 */
	outedCount?: number | null
	/**
	 * 区域名称
	 */
	regionLabel?: null | string
	/**
	 * 货位Id
	 */
	roomId?: number | null
	/**
	 * 货位名称
	 */
	roomName?: null | string
	/**
	 * 状态：1：未出库、2：已出库
	 */
	status?: null | string
	/**
	 * 出库仓库ID
	 */
	storeId?: number | null
	/**
	 * 出库仓库
	 */
	storeName?: null | string
	/**
	 * 技术参数
	 */
	technicalParameter?: null | string
	/**
	 * 待出库数量
	 */
	toOutCount?: number | null
	/**
	 * 操作人
	 */
	userName?: null | string
	/**
	 * 库存单位
	 */
	useUnit?: null | string
	/**
	 * 质保期
	 */
	validityPeriod?: null | string
	/**
	 * 品牌型号
	 */
	version?: null | string
	/**
	 * 关联业务单号
	 */
	workOrderCode?: null | string
	/**
	 * 关联业务单Id
	 */
	workOrderId?: number | null
	[property: string]: any
}

/**
 * 查询批次 searchBatch
 */
export interface MatOutStorePickSearchBatchRequestParams {
	/**
	 * 领料出库单明细ID
	 */
	applyItemId: number
	currentPage?: number
	/**
	 * ID
	 */
	id?: number
	pageSize?: number
	[property: string]: any
}

/**
 * 查询货位 参数 MatOutStorePickRoompageDto
 */
export interface MatOutStorePickRoompageDto {
	currentPage: number
	/**
	 * 物资明细ID
	 */
	id: number
	pageSize: number
	sidx?: string
	sord?: string
	/**
	 * 要查询的状态：1-全部数量 2-待出库数量 3-已出库数量
	 */
	status: string
	[property: string]: any
}

export enum IMatOutStoreRoomStatus {
	all = "1",
	wait = "2",
	out = "3"
}
/**
 * 货位 详细信息
 *
 * MatOutStorePickRoomInfoVo
 */
export interface MatOutStorePickRoomInfoVo {
	/**
	 * ID
	 */
	id?: number | null
	/**
	 * 物资编码
	 */
	materialCode?: null | string
	/**
	 * 物资名称
	 */
	materialName?: null | string
	/**
	 * 领料数量
	 */
	num?: number | null
	/**
	 * 出库仓库
	 */
	storeName?: null | string
	/**
	 * 技术参数
	 */
	technicalParameter?: null | string
	/**
	 * 品牌型号
	 */
	version?: null | string
	[property: string]: any
}

/**
 * 查询货位 MatOutStorePickRoompageVo
 */
export interface MatOutStorePickRoompageVo {
	/**
	 * ID
	 */
	id?: number | null
	/**
	 * 出库数量
	 */
	num?: number | null
	/**
	 * 出库区域id
	 */
	regionId?: number | null
	/**
	 * 出库区域
	 */
	regionLabel?: null | string
	/**
	 * 出库货位
	 */
	roomCode?: null | string
	/**
	 * 出库货位ID
	 */
	roomId?: number | null
	[property: string]: any
}

/**
 * 领料出库 关闭 close
 */
export interface MatOutStorePickCloseRequestParams {
	/**
	 * 领料出库单ID
	 */
	id?: string
	[property: string]: any
}

/**
 * 领料出库 批量关闭 入参
 */
export interface BatchMatOutStorePickCloseRequestParams {
	/**
	 * 关闭id集合
	 */
	ids?: string
	/**
	 * 关闭原因
	 */
	reason?: string
	[property: string]: any
}

/**
 * 领料出库 获取出库批次 getBatches
 */
export interface MatOutStorePickBatchesRequestParams {
	/**
	 * 领料出库单明细ID
	 */
	applyItemId?: number
	[property: string]: any
}

/**
 * 领料出库 出库 out
 */
export interface MatOutStorePickOutRequestParams {
	/**
	 * 本次出库数量
	 */
	completeNum?: number | null
	/**
	 * 领料出库明细ID
	 */
	id?: number | null
	[property: string]: any
}

/**
 * 领料出库--查询本次出库的区域和货位 返回数据Vo
 */
export interface MatPickRegionRoomVO {
	/**
	 * 出库区域编码
	 */
	regionCode?: null | string
	/**
	 * 出库区域数量
	 */
	regionNum?: number | null
	/**
	 * 出库货位
	 */
	roomCode?: null | string
	/**
	 * 出库货位数量
	 */
	roomNum?: number | null
	[property: string]: any
}

/**
 * 领料出库--查询本次出库的区域和货位的分页列表 VO
 */
export interface MatOutStorePickBatchVO {
	/**
	 * 单价
	 */
	amount?: number | null
	/**
	 * 批次号
	 */
	batchNo?: null | string
	/**
	 * 出库数量
	 */
	completeNum?: number | null
	/**
	 * ID
	 */
	id?: number | null
	/**
	 * 操作人
	 */
	lastModifiedBy?: null | string
	/**
	 * 出库时间
	 */
	outStoreTime?: null | string
	/**
	 * 出库区域编码
	 */
	regionCode?: null | string
	/**
	 * 出库区域名称
	 */
	regionLabel?: null | string
	/**
	 * 出库区域数量
	 */
	regionNum?: number | null
	/**
	 * 出库货位
	 */
	roomCode?: null | string
	/**
	 * 出库货位ID
	 */
	roomId?: number | null
	/**
	 * 出库货位数量
	 */
	roomNum?: number | null
	/**
	 * 质保期
	 */
	validityPeriod?: null | string
	[property: string]: any
}
