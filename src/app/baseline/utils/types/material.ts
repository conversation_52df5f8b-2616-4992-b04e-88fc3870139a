/* 物资 TS */

/**
 * 相似物资 入参
 */
export interface SimilarityMaterialCodeVoRequest {
	/**
	 * 物资性质
	 */
	attribute?: string
	currentPage: number
	/**
	 * 物资名称
	 */
	label: string
	/**
	 * 物资分类ID
	 */
	materialTypeId?: number
	pageSize: number
	/**
	 * 主要材质;{"铜":0.5，"铁":0.8}
	 */
	quality?: string
	sidx?: string
	/**
	 * 相似物资查询类型 0-一期接口  1-对接丰数接口
	 */
	similarityType: string
	sord?: string
	/**
	 * 技术参数
	 */
	technicalParameter?: string
	/**
	 * 规格型号
	 */
	version: string
	[property: string]: any
}

/**
 * 相似物资 返参
 *
 * SimilarityMaterialCodeVo
 */
export interface SimilarityMaterialCodeVo {
	/**
	 * 物资性质
	 */
	attribute?: null | string
	/**
	 * 审批状态;0待提交，1审批中，2已审批，3已驳回
	 */
	bpmStatus?: null | string
	/**
	 * 采购单位
	 */
	buyUnit?: number | null
	/**
	 * 物资编码
	 */
	code?: null | string
	/**
	 * 库存转换比例
	 */
	conversionUnit?: number | null
	/**
	 * 创建人
	 */
	createdBy?: null | string
	/**
	 * 创建时间
	 */
	createdDate?: null | string
	/**
	 * 预估采购单价
	 */
	evaluation?: number | null
	/**
	 * 物资编码ID
	 */
	id?: number | null
	/**
	 * 编码名称
	 */
	label?: null | string
	/**
	 * 物资分类编码
	 */
	materialTypeCode?: null | string
	/**
	 * 物资分类组合名称
	 */
	materialTypeCombineLabel?: null | string
	/**
	 * 物资分类ID
	 */
	materialTypeId?: number | null
	/**
	 * 物资分类名称
	 */
	materialTypeLabel?: null | string
	/**
	 * 图片
	 */
	photoName?: null | string
	/**
	 * 主要材质;{"铜":0.5，"铁":0.8}
	 */
	quality?: null | string
	/**
	 * 相似度
	 */
	similarityRate?: number | null
	/**
	 * 物资编码状态;0正常 1作废 2冻结 3新增
	 */
	status?: null | string
	/**
	 * 所属公司ID
	 */
	sysCommunityId?: number | null
	/**
	 * 技术参数
	 */
	technicalParameter?: null | string
	/**
	 * 库存单位
	 */
	useUnit?: number | null
	/**
	 * 规格型号
	 */
	version?: null | string
	[property: string]: any
}

/**
 * 操作状态:0正常 1正在冻结 2正在作废 3正在解冻 4正在更新中
 */
export enum IMateOperateStatus {
	normal = "0",
	freeze = "1",
	cancel = "2",
	thawing = "3",
	update = "4"
}

/**
 * 更新类型：0-物资性质 1-主要材质 2-辅助材质 3-安全库存 4-预估采购单价
 */
export enum IMatUpdateType {
	materialType = "0",
	materialQuality = "1",
	auxiliaryQuality = "2",
	materialSafeStock = "3",
	materialEvaluation = "4"
}

/**
 * 物资业务类型
 */
export enum IMaterialBusinessType {
	/**
	 * 安全库存
	 */
	materialSafeStock = "1",
	/**
	 * 预估采购单价
	 */
	materialEvaluation = "2",
	/**
	 * 物资材质
	 */
	materialQuality = "3",
	/**
	 * 物质性质
	 */
	materialType = "4",

	/**
	 * 物资冻结
	 */
	materialFreeze = "5",

	/**
	 * 物资作废
	 */
	materialCancel = "6",

	/**
	 * 物资解冻
	 */
	materialThawing = "7"
}

/**
 * 相似物资查询类型 0-一期接口  1-对接丰数接口
 */
export enum ISimilarityType {
	default = 0,
	otherSimilar = 1
}

/**
 * 枚举：相似物资展示类型
 */
export enum IShowType {
	/**
	 * 列表
	 */
	list = "0",
	/**
	 * 图片
	 */
	img = "1"
}

/**
 * 物资编码-冻结|解冻|作废|更新物资性质
 * MaterialOperateDTO
 */
export interface MaterialOperateDTO {
	/**
	 * 物资id集合
	 */
	materialIdList?: number[] | null
	/**
	 * 操作类型 1更新安全库存 2更新预估采购单价 3更新物资材质 4更新物资性质 5冻结 6作废 7解冻
	 */
	operateType?: null | string
	/**
	 * 可替代物资id
	 */
	replaceMaterialId?: number | null
	[property: string]: any
}

/**
 * 业务消息接收人配置
 *
 * MessageReceiveUserVo
 */
export interface MessageReceiveUserVo {
	/**
	 * 所属公司ID
	 */
	companyId?: number | null
	/**
	 * ID
	 */
	id?: number | null
	/**
	 * 手机号
	 */
	phone?: null | string
	/**
	 * 姓名
	 */
	realname?: null | string
	/**
	 * 接收人id
	 */
	receiveUserId?: number | null
	/**
	 * 性别
	 */
	sex?: number | null
	/**
	 * 所属部门ID
	 */
	sysOrgId?: number | null
	/**
	 * 业务类型 1安全库存 2预估采购单价 3物资材质 4物质性质 5物资冻结 6物资作废 7物资解冻
	 */
	type?: null | string
	/**
	 * 账号
	 */
	username?: null | string
	[property: string]: any
}

/**
 * 业务消息接收人配置 入参
 *
 * MessageReceiveUserVo
 */
export interface MessageReceiveUserVoRequest {
	/**
	 * 所属公司id
	 */
	companyId: number
	currentPage: number
	pageSize: number
	sidx?: string
	sord?: string
	/**
	 * 业务类型 1安全库存 2预估采购单价 3物资材质 4物质性质 5物资冻结 6物资作废 7物资解冻
	 */
	type: string
	[property: string]: any
}

/**
 * 业务消息接收人-新增
 * MessageReceiveUserDTO
 */
export interface MessageReceiveUserDTO {
	/**
	 * 所属公司id
	 */
	companyId: number
	/**
	 * 业务类型 1安全库存 2预估采购单价 3物资材质 4物质性质 5物资冻结 6物资作废 7物资解冻
	 */
	type: string
	/**
	 * 用户id集合
	 */
	userIdList?: number[] | null
	[property: string]: any
}

/**
 * 物资分类扩展属性-保存入参
 * MaterialTypePropertyUpdateDTO
 */
export interface MaterialTypePropertyUpdateDTO {
	/**
	 * 物资分类ID
	 */
	materialTypeId: number | null
	/**
	 * 物资分类扩展属性集合
	 */
	propertys?: MaterialTypePropertyDTO[] | null
	[property: string]: any
}

/**
 * com.bmzymtr.baseline.mms.material.dto.MaterialTypePropertyDTO
 *
 * MaterialTypePropertyDTO
 */
export interface MaterialTypePropertyDTO {
	/**
	 * 物资分类扩展属性ID
	 */
	id?: number | null
	/**
	 * 物资分类ID
	 */
	materialTypeId: number | null
	/**
	 * 属性选项列表：下拉框选择值，多个值用逗号隔开
	 */
	optionList?: null | string
	/**
	 * 属性名称
	 */
	propertyName: null | string
	/**
	 * 属性类型： 1:文本框  2:下拉框
	 */
	propertyType: number | null
	/**
	 * 排序
	 */
	sortedBy: number | null
	[property: string]: any
}
