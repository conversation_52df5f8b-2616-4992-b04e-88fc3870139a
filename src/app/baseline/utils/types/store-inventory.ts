/**
 * 库存管理-库存查询 类型声明
 */

/**
 * 库存物资状态:0正常、1盘点中
 */
export enum IStoreMaterialStatus {
	normal = 0,
	inventoryInProgress = 1
}
export interface MatGetInventoryParams {
	costCenterId?: number
	id: number
	storeId?: number
	storeNumZero?: boolean
}

export interface ListWarehouseBatchInventoryBatchParams {
	currentPage?: number
	id: number
	pageSize?: number
	sidx?: string
	sord?: string
	[property: string]: any
}

/**
 * 仓库批次库存批次 vo
 * MatStoreScreenBatchDetailVo
 */
export interface MatStoreScreenBatchDetailVo {
	/**
	 * 采购单价
	 */
	actualCost?: number | null
	/**
	 * 批次号
	 */
	batchNo?: null | string
	/**
	 * 操作人
	 */
	createdBy?: null | string
	/**
	 * id
	 */
	id?: number | null
	/**
	 * 入库时间
	 */
	inStoreTime?: null | string
	/**
	 * 区域
	 */
	regionCode?: null | string
	/**
	 * 货位
	 */
	roomCode?: null | string
	/**
	 * 货位Id
	 */
	roomId?: number | null
	/**
	 * 库存数量
	 */
	storeNum?: number | null
	/**
	 * 入库金额
	 */
	totalPrice?: number | null
	/**
	 * 质保期
	 */
	validityPeriod?: null | string
}

/**
 * 枚举：库存业务类型
 */
export enum IInventoryBusinessType {
	/**
	 * 1.采购入库 - 入库申请页面
	 */
	purchaseToWarehouse = "CK-CG-RK",

	/**
	 * 2.其他入库 - 备件入库
	 */
	sparePartsToWarehouse = "CK-BJ-RK",

	/**
	 * 3.其他入库 - 配件入库
	 */
	partsToWarehouse = "CK-PJ-RK",

	/**
	 * 4.其他入库 - 交旧完修入库
	 */
	repairToWarehouse = "CK-FX-RK",

	/**
	 * 4.其他入库 - 实物物资入库
	 */
	physicalObject = "CK-SW-RK",

	/**
	 * 5.领料申请
	 */
	materialGetApply = "CK-LL-SQ",

	/**
	 * 6.退库申请
	 */
	warehouseReturnApply = "CK-TK-SQ",

	/**
	 * 7.退货申请
	 */
	goodsReturnApply = "CK-TH-SQ",

	/**
	 * 8.调拨申请 - 库内移库
	 */
	transferApplyKN = "CK-DB-KN",

	/**
	 * 9.调拨申请 - 调外移库
	 */
	transferApplyKW = "CK-DB-KW",

	/**
	 * 10.交旧申请
	 */
	storeWasteHandoverApply = "CK-JJ-SQ",

	/**
	 * 11.返修申请
	 */
	wasteRepairApply = "CK-FX-SQ",

	/**
	 * 12.报废申请
	 */
	wasteBFApply = "BF-SQ",

	/**
	 * 13.报废处置申请
	 */
	wasteBFCZApply = "BF-CZ",

	/**
	 * 14.领用申请
	 */
	lowValueUseApply = "DZ-LY",

	/**
	 * 15.归还申请
	 */
	lowValueReturnApply = "DZ-GH",

	/**
	 * 16.质量检验
	 */
	qualityInspection = "CK-ZJ-RK",

	/**
	 * 17.接收入库
	 */
	receiveToWarehouse = "CK-JS-RK",

	/**
	 * 18.领料出库
	 */
	materialGetOutWarehouse = "CK-LL-CK",

	/**
	 * 19.退库入库
	 */
	warehouseReturnToWarehouse = "CK-TK-RK",

	/**
	 * 20.调拨出库
	 */
	transferOutWarehouse = "CK-DB-CK",

	/**
	 * 21.调拨入库
	 */
	transferToWarehouse = "CK-DB-RK",

	/**
	 * 22.退货出库
	 */
	goodsReturnOutWarehouse = "CK-TH-CK",

	// 交旧返修入库,交旧废旧入库 => 交旧入库

	/**
	 * 交旧入库
	 */
	/* handoverStoredReceived = "CK-JJ-RK", */
	/**
	 * 23.交旧返修入库 - 交旧入库
	 */
	handoverStoredReceivedFXRK = "CK-JJ-FX-RK",

	/**
	 * 29.交旧备用入库 - 交旧入库
	 */
	handoverStoredReceivedBYRK = "CK-JJ-BY-RK",

	/**
	 * 24.交旧废旧入库 - 交旧入库
	 */
	handoverStoredReceivedFJRK = "CK-JJ-FJ-RK",

	/**
	 * 25.返修出库
	 */
	repairOutWarehouse = "CK-FX-CK",

	/**
	 * 26.盘点计划
	 */
	inventoryManagePlan = "CK-PD-JH",

	/**
	 * 27.盘盈入库
	 */
	inventoryProfitToWarehouse = "CK-PY-RK",

	/**
	 * 28.盘亏出库
	 */
	inventoryProfitOutWarehouse = "CK-PK-CK",

	/**
	 * 29.其他出库
	 */
	otherOutWarehouse = "CK-QT-CK",

	// ------------------------------------

	/**
	 * 交旧申请详情页
	 */
	wasteHandoverApply = "JJSQ"
}

/**
 * 枚举：库存查询类型
 */
export enum IInventoryFilterType {
	/**
	 * 按分类
	 */
	category = "1",
	/**
	 * 按仓库
	 */
	store = "2"
}

/**
 * 仓库 tree item vo
 * MatStoreScreenTreeVo
 */
export interface MatStoreScreenTreeVo {
	/**
	 * 后代节点
	 */
	children?: MatStoreScreenTreeVo[] | null
	/**
	 * code
	 */
	code?: null | string
	/**
	 * id
	 */
	id?: null | string
	/**
	 * label
	 */
	label?: null | string
	/**
	 * type
	 */
	type?: null | string
}

/**
 * 库存分页 查询参数
 */
export interface MatStoreScreenListReqParams {
	/**
	 * 物资性质
	 */
	attribute?: string
	/**
	 * 成本中心
	 * 成本中心ID
	 */
	costCenterId?: number
	currentPage?: number
	/**
	 * 物资编码
	 */
	materialCode?: string
	/**
	 * 物资名称
	 */
	materialName?: string
	/**
	 * 物资分类编码
	 */
	materialTypeCode?: string
	/**
	 * 物资分类ID
	 */
	materialTypeId?: number
	pageSize?: number
	sidx?: string
	sord?: string
	/**
	 * 仓库ID
	 */
	storeId?: number
	[property: string]: any
}

/**
 * 库存分页 vo
 * MatStoreScreenListVo
 */
export interface MatStoreScreenListVo {
	/**
	 * 图片
	 */
	photoName?: string | null
	/**
	 * 物资性质
	 */
	attribute?: null | string
	/**
	 * 预估采购单价
	 */
	evaluation?: number | null
	/**
	 * 预估采购周期
	 */
	evaluationCycle?: number | null
	/**
	 * 冻结数量
	 */
	freezeNum?: number | null
	/**
	 * id
	 */
	id?: number | null
	/**
	 * 物资编码
	 */
	materialCode?: null | string
	/**
	 * 物资ID
	 */
	materialId?: number | null
	/**
	 * 编码名称
	 */
	materialName?: null | string
	/**
	 * 物资分类编码
	 */
	materialTypeCode?: null | string
	/**
	 * 安全库存
	 */
	safeStock?: number | null
	/**
	 * 库存数量
	 */
	storeNum?: number | null
	/**
	 * 技术参数
	 */
	technicalParameter?: null | string
	/**
	 * 库存单位
	 */
	useUnit?: number | null
	/**
	 * 规格型号
	 */
	version?: null | string
	[property: string]: any
}

/**
 * 库存模块 详情分页 通用参数
 */
export interface MatStoreScreenCommonListReqParams {
	currentPage?: number
	id: number
	pageSize?: number
	sidx?: string
	sord?: string
	[property: string]: any
}

/**
 * 库存数量 vo
 * MatStoreScreenNumVo
 */
export interface MatStoreScreenNumVo {
	/**
	 * 月初库存量
	 */
	monthBeginNum?: number | null
	/**
	 * 本月入库量
	 */
	monthInNum?: number | null
	/**
	 * 标准成本
	 */
	standardCost?: number | null
	/**
	 * 库存数量
	 */
	storeNum?: number | null
	/**
	 * 年初库存量
	 */
	yearBeginNum?: number | null
	/**
	 * 本年入库量
	 */
	yearInNum?: number | null
	[property: string]: any
}

/**
 * 仓库批次 vo
 * MatStoreScreenBatchVo
 */
export interface MatStoreScreenBatchVo {
	/**
	 * 采购单价
	 */
	actualCost?: number | null
	/**
	 * 批次号
	 */
	batchNo?: null | string
	/**
	 * 冻结数量
	 */
	freezeNum?: number | null
	/**
	 * id
	 */
	id?: number | null
	/**
	 * 月初库存量
	 */
	monthBeginNum?: number | null
	/**
	 * 本月入库量
	 */
	monthInNum?: number | null
	/**
	 * 区域
	 */
	regionCode?: null | string
	/**
	 * 货位
	 */
	roomCode?: null | string
	/**
	 * 货位Id
	 */
	roomId?: number | null
	/**
	 * 仓库编码
	 */
	storeCode?: null | string
	/**
	 * 仓库Id
	 */
	storeId?: number | null
	/**
	 * 仓库名称
	 */
	storeLabel?: null | string
	/**
	 * 库存数量
	 */
	storeNum?: number | null
	[property: string]: any
}

/**
 * 库存事务 vo
 * MatStoreScreenAffairVo
 */
export interface MatStoreScreenAffairVo {
	/**
	 * 批次号
	 */
	batchNo?: null | string
	/**
	 * 单号
	 */
	code?: null | string
	/**
	 * 操作人
	 * 创建人
	 */
	createdBy?: null | string
	/**
	 * 操作时间
	 */
	createdDate?: null | string
	/**
	 * id
	 */
	id?: number | null
	/**
	 * 数量
	 */
	num?: number | null
	/**
	 * 前置业务单号
	 */
	preBusinessCode?: null | string
	/**
	 * 前置业务id
	 */
	preBusinessId?: number | null
	/**
	 * 前置业务单号
	 */
	preBusinessType?: null | string
	/**
	 * 货位
	 */
	roomCode?: null | string
	/**
	 * 来源ID
	 */
	sourceId?: number | null
	/**
	 * 事务类型
	 */
	sourceType?: null | string
	/**
	 * 仓库编码
	 */
	storeCode?: null | string
	/**
	 * 仓库名称
	 */
	storeLabel?: null | string
	/**
	 * 采购单价
	 */
	unitPrice?: number | null
	/**
	 * 货位ID
	 */
	货位ID?: number | null
	[property: string]: any
}
/**
 * 在途订单 vo
 * MatStoreInTransOrderVo
 */
export interface MatStoreInTransOrderVo {
	/**
	 * 采购订单号
	 */
	code?: null | string
	/**
	 * 采购订单名称
	 */
	label?: null | string
	/**
	 * 订单数量
	 */
	num?: number | null
	/**
	 * 采购项目编号
	 */
	projectCode?: null | string
	/**
	 * 采购项目名称
	 */
	projectLabel?: null | string
	/**
	 * 采购订单ID
	 */
	purchaseOrderId?: number | null
	/**
	 * 订单来源
	 */
	source?: null | string
	[property: string]: any
}

/**
 * 历史价格 vo
 * MatStoreScreenHisPricerVo
 */
export interface MatStoreScreenHisPricerVo {
	/**
	 * 合同编号
	 */
	contractCode?: null | string
	/**
	 * 采购项目编号
	 */
	projectCode?: null | string
	/**
	 * 采购项目名称
	 */
	projectLabel?: null | string
	/**
	 * 采购价格
	 */
	purchasePrice?: number | null
	/**
	 * 采购方式
	 */
	purchaseType?: null | string
	/**
	 * 供货商名称
	 */
	supplierLabel?: null | string

	/**
	 * 采购时间
	 */
	purchaseCreateDate?: string | null
	[property: string]: any
}

/**
 * 物资库龄 vo
 * MatStoreScreenAgeVo
 */
export interface MatStoreScreenAgeVo {
	/**
	 * 库龄
	 */
	age?: number | null
	/**
	 * 批次号
	 */
	batchNo?: null | string
	id?: number | null
	/**
	 * 货位编号
	 */
	roomCode?: null | string
	/**
	 * 货位id
	 */
	roomId?: number | null
	/**
	 * 仓库编码
	 */
	storeCode?: null | string
	/**
	 * 仓库Id
	 */
	storeId?: number | null
	/**
	 * 仓库名称
	 */
	storeLabel?: null | string
	/**
	 * 仓库类型
	 */
	storeType?: null | string
	/**
	 * 批次有效日期
	 */
	validityPeriod?: null | string
	[property: string]: any
}

/**
 * 库存冻结信息 入参
 */
export interface MatStoreFreezeVoReqParams {
	/**
	 * 成本中心id
	 */
	costCenterId?: number
	currentPage: number
	/**
	 * 物资id
	 */
	id: number
	pageSize: number
	sidx?: string
	sord?: string
	/**
	 * 仓库id
	 */
	storeId?: number
	[property: string]: any
}

/**
 * 库存冻结信息VO
 *
 * MatStoreFreezeVo
 */
export interface MatStoreFreezeVo {
	/**
	 * 业务ID
	 */
	businessId?: number | null
	/**
	 * 业务单号
	 * 业务ID
	 */
	code?: null | string
	/**
	 * 申请人
	 */
	createdBy?: null | string
	/**
	 * 申请时间
	 */
	createdDate?: null | string
	/**
	 * 冻结数量
	 */
	freezeNum?: number | null
	/**
	 * 主键
	 */
	id?: number | null
	/**
	 * 申请部门
	 */
	sysOrgId?: number | null
	/**
	 * 业务类型
	 */
	type?: null | string
	[property: string]: any
}

/**
 * 货位物资信息/批次物资信息 返回参数
 * MatStoreScreenStoreVo
 */
export interface MatStoreScreenStoreVo {
	/**
	 * 采购单价
	 */
	actualCost?: number | null
	/**
	 * 批次号
	 */
	batchNo?: null | string
	/**
	 * 库存批次
	 */
	batchNum?: number | null
	/**
	 * 冻结数量
	 */
	freezeNum?: number | null
	/**
	 * id
	 */
	id?: number | null
	/**
	 * 月初库存量
	 */
	monthBeginNum?: number | null
	/**
	 * 本月入库量
	 */
	monthInNum?: number | null
	/**
	 * 区域编号
	 */
	regionCode?: null | string
	/**
	 * 货位编号
	 */
	roomCode?: null | string
	/**
	 * 货位id
	 */
	roomId?: number | null
	/**
	 * 货位类型
	 */
	roomType?: null | string
	/**
	 * 标准成本
	 */
	standardCost?: number | null
	/**
	 * 仓库编码
	 */
	storeCode?: null | string
	/**
	 * 仓库Id
	 */
	storeId?: number | null
	/**
	 * 仓库名称
	 */
	storeLabel?: null | string
	/**
	 * 库存数量
	 */
	storeNum?: number | null
	[property: string]: any
}

/**
 * 库存业务单据信息VO
 *
 * MatStoreApplyBusinessVo
 */
export interface MatStoreApplyBusinessVo {
	/**
	 * 批次号数量
	 */
	batchNum?: number | null
	/**
	 * 业务ID
	 */
	businessId?: number | null
	/**
	 * 业务单号
	 */
	code?: null | string
	/**
	 * 操作人
	 * 申请人
	 */
	createdBy?: null | string
	/**
	 * 操作时间
	 */
	createdDate?: null | string
	/**
	 * 主键
	 */
	id?: number | null
	/**
	 * 数量
	 */
	num?: number | null
	/**
	 * 仓库id
	 */
	storeId?: number | null
	/**
	 * 仓库名称
	 */
	storeLabel?: null | string
	/**
	 * 业务类型
	 */
	type?: null | string
	[property: string]: any
}
