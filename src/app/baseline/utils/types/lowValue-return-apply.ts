/**
 * @description 低值 - 归还申请 TS
 */

/**
 * 归还申请 主表 入参
 */
export interface MatLowValueReturnApplyVORequest {
	/**
	 * 审批状态
	 */
	bpmStatus?: string
	/**
	 * 归还业务单号
	 */
	code?: string
	currentPage?: number
	/**
	 * 归还业务名称
	 */
	label?: string
	pageSize?: number
	/**
	 * 关联领用单号
	 */
	preApplyCode?: string
	/**
	 * 归还人=realname
	 */
	realname?: string
	sidx?: string
	sord?: string
	/**
	 * 部门ID
	 */
	sysOrgId?: number
	[property: string]: any
}

/**
 * 归还申请 主表 返参
 *
 * MatLowValueReturnApplyVO
 */
export interface MatLowValueReturnApplyVO {
	/**
	 * 申请主键id
	 */
	applyId?: number | null
	/**
	 * 审批状态_view
	 */
	bpmStatus?: null | string
	/**
	 * 申请单号
	 */
	code?: null | string
	/**
	 * 申请人(归还人)_view
	 */
	createdBy?: null | string
	/**
	 * 申请时间
	 */
	createdDate?: null | string
	/**
	 * 申请主键id
	 */
	id?: number | null
	/**
	 * 归还原因
	 */
	illustrate?: null | string
	/**
	 * 申请名称
	 */
	label?: null | string
	/**
	 * 物资编码数量
	 */
	materialCodeCnt?: number | null
	/**
	 * 关联领用单号
	 */
	preApplyCode?: null | string
	/**
	 * 备注说明
	 */
	remark?: null | string
	/**
	 * 归还物资数量
	 */
	returnInNum?: number | null
	/**
	 * 公司ID_view
	 */
	sysCommunityId?: number | null
	/**
	 * 部门ID_view
	 */
	sysOrgId?: number | null
	[property: string]: any
}

/**
 * 归还申请 添加草稿 入参
 * MatLowValueReturnApplyDTO
 */
export interface MatLowValueReturnApplyDTO {
	/**
	 * 申请单主键
	 */
	applyId?: number | null
	/**
	 * 归还原因
	 */
	illustrate?: null | string
	/**
	 * 申请单名称
	 */
	label?: null | string
	/**
	 * 选择前置领用单号id
	 */
	preApplyId?: number | null
	/**
	 * 备注
	 */
	remark?: null | string
	/**
	 * 归还部门ID
	 */
	sysOrgId?: number | null
	[property: string]: any
}

/**
 * 归还申请-扩展信息-归还物资(分页) 返参
 *
 * MatLowValueReturnApplyItemVO
 */
export interface MatLowValueReturnApplyItemVO {
	/**
	 * 分配数量
	 */
	allocationNum?: number | null
	/**
	 * 申请ID
	 */
	applyId?: number | null
	/**
	 * 可归还数量
	 */
	canNum?: number | null
	/**
	 * 已归还数量
	 */
	hasReturnNum?: number | null
	/**
	 * 物资明细ID
	 */
	id?: number | null
	/**
	 * 低值类型_view
	 */
	lowValueType?: null | string
	/**
	 * 物资编码
	 */
	materialCode?: null | string
	/**
	 * 物资ID
	 */
	materialId?: number | null
	/**
	 * 物资名称
	 */
	materialLabel?: null | string
	/**
	 * 物资分类编码
	 */
	materialTypeCode?: null | string
	/**
	 * 物资分类名称
	 */
	materialTypeLabel?: null | string
	/**
	 * 本次归还数量
	 */
	num?: number | null
	/**
	 * 仓库id
	 */
	storeId?: number | null
	/**
	 * 仓库名称
	 */
	storeLabel?: null | string
	/**
	 * 使用人_view
	 */
	username?: null | string
	/**
	 * 规格型号
	 */
	version?: null | string
	[property: string]: any
}

/**
 * 归还申请-扩展信息-归还物资-添加物资-保存 入参 dtoList
 *
 * AddReturnApplyItemSelectDTO
 */
export interface AddReturnApplyItemSelectDTO {
	/**
	 * 领用分配ID
	 */
	batchId: number
	/**
	 * 物资ID
	 */
	materialId: number
	[property: string]: any
}

/**
 * 归还申请-扩展信息-归还物资-(本次归还数量+归还仓库-编辑)
 *
 * EditReturnApplyItemDTO
 */
export interface EditReturnApplyItemDTO {
	/**
	 * 明细ID=mat_low_value_apply_item.id
	 */
	id?: number | null
	/**
	 * 本次归还数量
	 */
	num?: number | null
	[property: string]: any
}
