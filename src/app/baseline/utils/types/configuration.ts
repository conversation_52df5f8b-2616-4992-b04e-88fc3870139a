/*
 * @Author: liulianming
 * @Date: 2025-07-12 00:08:12
 * @LastEditors: liulianming
 * @LastEditTime: 2025-07-30 21:43:08
 * @Description:
 */
/**
 * 配置-GIS信息  分页入参
 */
export interface configurationGisInformationPagedRequest {
	currentPage?: number
	pageSize?: number
	objectTypeCode?: string
	[property: string]: any
}

export interface configurationGisInformationVo {
	/**
	 * 主键
	 */
	id: number,
	/**
	 * 绘制对象类型
	 */
	objectType: string,
	/**
	 * 绘制对象类型编码
	 */
	objectTypeCode: string,
	/**
	 * 绘制对象名称
	 */
	objectLabel: string,
	/**
	 * 坐标点数量
	 */
	plotPointCount: number,
	/**
	 * 高德绘制类型
	 */
	amapObjectType: string,
	/**
	 * 绘制点
	 */
	plotPoint: string,
	/**
	 * 备注
	 */
	remark: string,
	/**
	 * 文件批次ID
	 */
	fileBatchId: string,
	/**
	 * 源文件名称
	 */
	sourceFileName: string,
	/**
	 * 导入时间
	 */
	importTime: string,
	/**
	 * 删除标志
	 */
	delFlag: string,
	/**
	 * 创建时间
	 */
	createdDate: string,
	/**
	 * 最后修改时间
	 */
	lastModifiedDate: string,
	/**
	 * 创建人
	 */
	createdBy: string,
	/**
	 * 最后修改人
	 */
	lastModifiedBy: string
}

export interface configurationGisCateVo {
	id: number
	cateName: string
	cateCode: string
	sort: number
	delFlag: string
	createdDate: string
	lastModifiedDate: string
	createdBy: string
	lastModifiedBy: string
}
