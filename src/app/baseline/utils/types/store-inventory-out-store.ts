/**
 * 盘亏出库 主列表 参数
 * CheckLossesOutStorePageVoQuery
 */
export interface CheckLossesOutStorePageVoQuery {
	currentPage?: number
	/**
	 * 盘点人员名称
	 */
	firstCheckRealname?: string
	/**
	 * 盘盈入库单号
	 * 盘亏出库单号
	 */
	inStoreCode?: string
	pageSize?: number
	/**
	 * 关联盘点计划编码
	 */
	planCode?: string
	/**
	 * 盘点计划名称
	 */
	planName?: string
	/**
	 * 复核人员名称
	 */
	secondCheckRealname?: string
	sidx?: string
	sord?: string
	/**
	 * 仓库编号
	 */
	storeCode?: string
	/**
	 * 关联盘点任务编码
	 */
	taskCode?: string
	[property: string]: any
}

/**
 * 盘亏出库 主列表
 * CheckLossesOutStorePageVo
 */
export interface CheckLossesOutStorePageVo {
	/**
	 * 盘点人员
	 */
	firstCheckRealname?: null | string
	/**
	 * 入库物资编码数量
	 */
	materialCodeCount?: number | null
	/**
	 * 盘亏出库单号
	 */
	outStoreCode?: null | string
	/**
	 * 出库时间
	 */
	outStoreDate?: null | string
	/**
	 * 关联盘点计划编码
	 */
	planCode?: null | string
	/**
	 * 盘点计划名称
	 */
	planName?: null | string
	/**
	 * 复核人员
	 */
	secondCheckRealname?: null | string
	/**
	 * 仓库编号
	 */
	storeCode?: null | string
	/**
	 * 出库仓库名称
	 */
	storeName?: null | string
	/**
	 * 关联盘点任务编码
	 */
	taskCode?: null | string
	[property: string]: any
}

/**
 * 盘亏出库 详情 - 返回数据
 *
 * CheckTaskProfitOutStoreDetailVo
 */
export interface CheckTaskProfitOutStoreDetailVo {
	/**
	 * 入库物资编码数量
	 */
	materialCodeCount?: number | null
	/**
	 * 盘亏出库单号
	 */
	outStoreCode?: null | string
	/**
	 * 出库时间
	 */
	outStoreDate?: { [key: string]: any }
	/**
	 * 关联盘点计划编码
	 */
	planCode?: null | string
	/**
	 * 盘点计划名称
	 */
	planName?: null | string
	/**
	 * 入库仓库名称
	 */
	storeName?: null | string
	/**
	 * 关联盘点任务编码
	 */
	taskCode?: null | string
	[property: string]: any
}

/**
 * 盘亏出库 - 物资明细 参数
 * CheckTaskLossesOutStoreMaterialPageVoQuery
 */
export interface CheckTaskLossesOutStoreMaterialPageVoQuery {
	currentPage?: number
	/**
	 * 搜索关键词
	 */
	keyword?: string
	pageSize?: number
	sidx?: string
	sord?: string
	/**
	 * 任务id
	 */
	taskId?: number
	[property: string]: any
}

/**
 * 盘亏出库 - 物资明细
 * CheckTaskLossesOutStoreMaterialPageVo
 */
export interface CheckTaskLossesOutStoreMaterialPageVo {
	/**
	 * 物资编码
	 */
	materialCode?: null | string
	/**
	 * 物资名称
	 */
	materialName?: null | string
	/**
	 * 规格型号
	 */
	materialVersion?: null | string
	/**
	 * 出库数量
	 */
	outStoreNum?: number | null
	/**
	 * 出库区域
	 * 入库区域
	 */
	regionLabel?: null | string
	/**
	 * 出库货位编码
	 * 入库货位
	 */
	roomCode?: null | string
	/**
	 * 技术参数
	 */
	technicalParameter?: null | string
	/**
	 * 库存单位
	 */
	useUnit?: null | string
	[property: string]: any
}
