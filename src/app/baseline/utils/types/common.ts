/**
 * 业务 申请 code
 */
export enum MatGetApplyCode {
	/**
	 * 等待人数较多（需要建立 socket 链接）
	 */
	needWait = "9494",

	/**
	 * 成功
	 */
	success = "200"
}

export enum IModalType {
	/**
	 * 查看
	 */
	view = "view",
	/**
	 * 编辑
	 */
	edit = "edit",
	/**
	 * 创建
	 */
	create = "create"
}

/**
 * 字典 vo
 */
export interface IDictVo {
	id?: number
	createdDate?: string
	lastModifiedDate?: string
	createdBy?: string
	lastModifiedBy?: string
	subitemName?: string
	subitemValue?: string
	deleted?: boolean
	disable?: boolean
	sortedBy?: number
	dataDictionaryCode?: string
	dataDictionary?: null
	label?: string
	value?: string
	[k: string]: any
}

/**
 * 通用分页类型
 */
export interface PageResVo<T> {
	currentPage?: number | null
	pageSize?: number | null
	records?: number | null
	rows?: T[] | null
	total?: number | null
	[property: string]: any
}

/**
 * 线路 vo
 */
export interface LineVo {
	id: string
	code: string
	name: string
	colour: string
	length: number
	stationNumber: number
	firstStation: string
	lastStation: string
	companyId: null
	sortedBy: number
	createdDate: Date
	lastModifiedDate: Date
	createdBy: string
	lastModifiedBy: string
	firstEnableTime: null
}

/**
 * TODO
 * 幂等较验 token 前置
 */
export enum IIdempotentTokenTypePre {
	apply = "APPLY",
	other = "OTHER",
	batch = "BATCH"
}

/**
 * 批量操作类型
 */
export enum IIdempotentTokenTypeAction {
	/**
	 * 批量生成月度计划
	 */
	purchasePlan = "YDJH",

	/**
	 * 批量生成采购订单
	 */
	purchaseOrder = "CGDD",

	/**
	 * 批量推送质检
	 */
	pushInspect = "TSZJ",

	/**
	 * 批量出库
	 */
	outStore = "OUT",

	/**
	 * 批量关闭
	 */
	close = "CLOSE",

	/**
	 * 批量更新 物资性质
	 */
	update = "UPDATE"
}
/**
 * TODO
 * 幂等较验 业务类型
 */
export enum IIdempotentTokenType {
	/**
	 * 段区
	 */
	depot = "DEPOT",

	/**
	 * 费用类别
	 */
	costCategory = "COST-CATEGORY",

	/**
	 * 成本中心
	 */
	costCenter = "COST-CENTER",

	/**
	 * 物资 - 编码分类
	 */
	materialType = "MAT-FL",

	/**
	 * 物资 - 物资编码
	 */
	materialCode = "MAT-BM",

	/**
	 * 计划 - 需求清单
	 */
	planNeedList = "PLAN-QD",

	/**
	 * 计划 -年度计划
	 */
	planYearPlan = "PLAN-ND",

	/**
	 * 计划 -需求计划
	 */
	planNeedPlan = "PLAN-XQ",

	/**
	 * 计划 -需求计划-月度
	 */
	planNeedPlanItem = "PLAN-XQ-YD",

	/**
	 * 计划 -合并计划
	 */
	planMerge = "PLAN-HB",

	/**
	 * 计划 -采购计划
	 */
	planPurchase = "PLAN-CG",

	/**
	 * 采购 - 采购分包
	 */
	purchaseDistribution = "CG-FB",

	/**
	 * 采购 - 采购项目
	 */
	purchaseProject = "CG-XM",

	/**
	 * 采购 - 订货计划
	 */
	purchasePlan = "CG-JH",
	/**
	 * 采购 - 订单
	 */
	purchaseOrder = "CG-DD",

	/**
	 * 采购 - 合同
	 */
	purchaseContract = "CG-HT",

	/**
	 * 采购 - 厂商管理
	 */
	purchaseSupplier = "CG-CS",

	/**
	 * 采购 - 发票管理
	 */
	purchaseInvoice = "CG-FP",

	/**
	 * 仓库
	 */
	storeApply = "CK-SQ",

	/**
	 * 货位
	 */
	storeRoomApply = "CK-HW-SQ",

	/**
	 * 入库申请页面
	 */
	warehouseApply = "CK-RK-SQ",

	/**
	 * 2.其他入库申请
	 */
	otherToWarehouseApply = "CK-QT-SQ",

	/**
	 * 5.领料申请
	 */
	materialGetApply = "CK-LL-SQ",

	/**
	 * 6.退库申请
	 */
	warehouseReturnApply = "CK-TK-SQ",

	/**
	 * 7.退货申请
	 */
	goodsReturnApply = "CK-TH-SQ",

	/**
	 * 质量检验
	 */
	qualityInspection = "CK-ZJ-RK",

	/**
	 * 接收入库
	 */
	receiveToWarehouse = "CK-JS-RK",

	/**
	 * 交旧入库
	 */
	handoverStoredReceived = "CK-JJ-RK",

	/**
	 * 退库入库
	 */
	warehouseReturnToWarehouse = "CK-TK-RK",

	/**
	 * 领料出库
	 */
	materialGetOutWarehouse = "CK-LL-CK",

	/**
	 * 退货出库
	 */
	goodsReturnOutWarehouse = "CK-TH-CK",

	/**
	 * 返修出库
	 */
	repairOutWarehouse = "CK-FX-CK",

	/**
	 * 调拨申请
	 */
	transferApply = "CK-DB-SQ",

	/**
	 * 调拨入库
	 */
	transferToWarehouse = "CK-DB-RK",

	/**
	 * 盘点计划
	 */
	inventoryManagePlan = "CK-PD-JH",

	/**
	 * 盘点任务
	 */
	inventoryManageTask = "CK-PD-RW",

	/**
	 * 交旧申请
	 */
	storeWasteHandoverApply = "CK-JJ-SQ",

	/**
	 * 返修申请
	 */
	wasteRepairApply = "CK-FX-SQ",

	/**
	 * 报废申请
	 */
	wasteBFApply = "BF-SQ",

	/**
	 * 报废处置申请
	 */
	wasteBFCZApply = "BF-CZ-SQ",

	/**
	 * 废旧 - 厂商管理
	 */
	wasteSupplier = "FJ-CS",

	/**
	 * 领用申请
	 */
	lowValueUseApply = "DZ-LY-SQ",

	/**
	 * 归还申请
	 */
	lowValueReturnApply = "DZ-GH-SQ",

	/**
	 * 低值盘点计划
	 */
	lowValueInventoryManagePlan = "DZ-PD-JH",

	/**
	 * 低值盘点任务
	 */
	lowValueInventoryManageTask = "DZ-PD-RW",

	/**
	 * 低值盘点任务-报告
	 */
	lowValueInventoryManageTaskReport = "DZ-PD-BG",

	/**
	 * 通用
	 */
	common = "COMMON"
}
