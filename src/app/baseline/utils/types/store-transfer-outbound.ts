/**
 * MatStoreAllocationOutDTO
 */
export interface MatStoreAllocationOutDTO {
	/**
	 * 出库单号
	 */
	code?: null | string
	/**
	 * ID
	 */
	id?: number | null
	/**
	 * 关闭id集合
	 */
	ids?: null | string
	parameterMap?: MapString
	/**
	 * tab页类型(1:待入库；2：已入库；3：已关闭)
	 */
	tableType?: number | null
	/**
	 * 关联单号
	 */
	type?: null | string
	/**
	 * 申请人姓名
	 */
	userName?: null | string
	[property: string]: any
}
/**
 * Map«String[]»
 */
export interface MapString {
	key?: string[] | null
	[property: string]: any
}

/**
 * MatStoreAllocationOutVO
 */
export interface MatStoreAllocationOutVO {
	/**
	 * 申请时间
	 */
	applyDate?: null | string
	/**
	 * 出库单号
	 */
	code?: null | string
	/**
	 * ID
	 */
	id?: number | null
	/**
	 * 关联单号Code
	 */
	preBusinessCode?: null | string
	/**
	 * 关联单号id
	 */
	preBusinessId?: number | null
	/**
	 * 调拨状态
	 */
	status?: null | string
	/**
	 * 出库仓库Id
	 */
	storeId?: number | null
	/**
	 * 入库仓库Id
	 */
	storeInId?: number | null
	/**
	 * 入库仓库名称
	 */
	storeInName?: null | string
	/**
	 * 出库仓库名称
	 */
	storeName?: null | string
	/**
	 * 移库类型
	 */
	type?: null | string
	/**
	 * 申请人
	 */
	userName?: null | string
	[property: string]: any
}

/**
 * MatStoreAllocationOutRequestParams
 */
export interface MatStoreAllocationOutRequestParams {
	id?: number
	/**
	 * tab页类型(1:待入库；2：已入库；3：已关闭)
	 */
	tableType?: number
	code?: string
	type?: string
	userName?: string
	ids?: string
	currentPage?: number
	pageSize?: number
	sidx?: string
	sord?: string
	[property: string]: any
}
