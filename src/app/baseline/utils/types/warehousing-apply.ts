/**
 * 入库状态
 */
export enum IWarehousingStatus {
	/**
	 * 待处理
	 */
	pending = "1",
	/**
	 * 已完成
	 */
	completed = "2",

	/**
	 * 已完成
	 */
	close = "3"
}

/**
 * 入库申请list query参数
 */
export interface MatInStoreApplyRequestParams {
	/**
	 * 金额
	 */
	amount?: string
	/**
	 * 审批状态
	 */
	bpmStatus?: string
	/**
	 * 入库申请单号
	 */
	code?: string
	/**
	 * 创建人
	 */
	createdBy?: string
	/**
	 * 创建日期
	 */
	createdDate?: string
	currentPage?: number
	/**
	 * del_flag
	 */
	delFlag?: string
	/**
	 * 段区ID
	 */
	depotId?: number
	/**
	 * 费用类别
	 */
	expenseCategory?: string
	/**
	 * 说明
	 */
	explain1?: string
	/**
	 * ID
	 */
	id?: number
	/**
	 * 更新人
	 */
	lastModifiedBy?: string
	/**
	 * 更新日期
	 */
	lastModifiedDate?: string
	/**
	 * 线路ID
	 */
	lineNoId?: number
	/**
	 * 专业ID
	 */
	majorId?: number
	pageSize?: number
	/**
	 * 采购订单号
	 */
	preBusinessCode?: string
	/**
	 * 前置业务类型
	 */
	preBusinessType?: string
	/**
	 * 备注
	 */
	remark?: string
	sidx?: string
	sord?: string
	/**
	 * 状态
	 * 状态  1是待处理,2是已完成
	 */
	status?: string
	/**
	 * 仓库ID
	 */
	storeId?: number
	/**
	 * 仓库ID
	 */
	storeInId?: number
	/**
	 * 供应商ID
	 */
	supplierId?: number
	/**
	 * 公司id
	 */
	sysCommunityId?: number
	/**
	 * 申请人ID
	 */
	userId?: number
	/**
	 * 关联工单号
	 */
	workOrderId?: number

	[property: string]: any
}

/**
 * 入库申请list vo
 *
 * MatInStoreApplyVo
 */
export interface MatInStoreApplyVo {
	/**
	 * 金额
	 */
	amount?: number | null
	/**
	 * 申请单号
	 */
	code?: null | string
	/**
	 * 合同编号
	 */
	contractCode?: null | string
	/**
	 * 创建人
	 */
	createdBy?: null | string
	/**
	 * 创建日期
	 */
	createdDate?: null | string
	/**
	 * ID
	 */
	id?: number | null
	/**
	 * 采购订单号
	 */
	preBusinessCode?: null | string
	/**
	 * 前置业务ID
	 */
	preBusinessId?: number | null
	/**
	 * 采购项目编号
	 */
	projectCode?: null | string
	/**
	 * 采购项目名称
	 */
	projectLabel?: null | string
	/**
	 * 仓库ID
	 */
	storeId?: number | null
	/**
	 * 仓库名称
	 */
	storeLabel?: null | string
	/**
	 * 供应商ID
	 */
	supplierId?: number | null
	/**
	 * 公司ID
	 */
	sysCommunityId?: number | null
	/**
	 * 申请人ID
	 */
	userId?: number | null
	[property: string]: any
}
/**
 * 入库申请 物资 list 查询参数
 */
export interface MatInStoreApplyItemListQueryParams {
	/**
	 * 金额
	 */
	amount?: string
	/**
	 * 申请ID
	 */
	applyId?: number
	/**
	 * 完成数量
	 */
	completeNum?: string
	/**
	 * 创建人
	 */
	createdBy?: string
	/**
	 * 创建日期
	 */
	createdDate?: string
	currentPage?: number
	/**
	 * del_flag
	 */
	delFlag?: string
	/**
	 * 申请项目表单
	 */
	formContent?: string
	/**
	 * 申请项目表单
	 */
	formId?: number
	/**
	 * ID
	 * id
	 */
	id?: number
	/**
	 * 更新人
	 */
	lastModifiedBy?: string
	/**
	 * 更新日期
	 */
	lastModifiedDate?: string
	/**
	 * 物资ID
	 * material_id
	 */
	materialId?: number
	/**
	 * 数量
	 */
	num?: string
	pageSize?: number
	sidx?: string
	sord?: string
	/**
	 * 状态
	 */
	status?: string
	/**
	 * 供应商ID
	 */
	validityPeriod?: string
	[property: string]: any
}

/**
 * 入库申请 物资vo
 * MatInStoreApplyItemVo
 */
export interface MatInStoreApplyItemVo {
	/**
	 * 金额
	 */
	amount?: number | null
	/**
	 * ID
	 * id
	 */
	id?: number | null
	/**
	 * 物资编码
	 */
	materialCode?: null | string
	/**
	 * 物资ID
	 * material_id
	 */
	materialId?: number | null
	/**
	 * 编码名称
	 */
	materialName?: null | string
	/**
	 * 数量
	 */
	num?: number | null
	/**
	 * 技术参数
	 */
	technicalParameter?: null | string
	/**
	 * 库存单位
	 */
	useUnit?: number | null
	/**
	 * 规格型号
	 */
	version?: null | string
	[property: string]: any
}

/**
 * 分配质检员 dto
 */
export interface PushInspectPersonDto {
	itemIds: any[]
	userId: string
	[property: string]: any
}

/**
 * 推送质检 dto
 */
export interface PushWarehousingInspectDto {
	/**
	 * 金额
	 */
	amount?: number | null
	/**
	 * 审批状态
	 */
	bpmStatus?: null | string
	/**
	 * 申请单号
	 */
	code?: null | string
	/**
	 * 创建人
	 */
	createdBy?: null | string
	/**
	 * 创建日期
	 */
	createdDate?: null | string
	/**
	 * del_flag
	 */
	delFlag?: null | string
	/**
	 * 段区ID
	 */
	depotId?: number | null
	/**
	 * 费用类别
	 */
	expenseCategory?: null | string
	/**
	 * 说明
	 */
	explain1?: null | string
	/**
	 * ID
	 */
	id?: number | null
	/**
	 * 更新人
	 */
	lastModifiedBy?: null | string
	/**
	 * 更新日期
	 */
	lastModifiedDate?: null | string
	/**
	 * 线路ID
	 */
	lineNoId?: number | null
	/**
	 * 专业ID
	 */
	majorId?: number | null
	/**
	 * 采购订单号
	 */
	preBusinessCode?: null | string
	/**
	 * 前置业务类型
	 */
	preBusinessType?: null | string
	/**
	 * 备注
	 */
	remark?: null | string
	/**
	 * 状态
	 */
	status?: null | string
	/**
	 * 仓库ID
	 */
	storeId?: number | null
	/**
	 * 仓库ID
	 */
	storeInId?: number | null
	/**
	 * 供应商ID
	 */
	supplierId?: number | null
	sysCommunityId?: number | null
	/**
	 * 申请人ID
	 */
	userId?: number | null
	/**
	 * 关联工单号
	 */
	workOrderId?: number | null
	/**
	 * 申请类型（前端不需要关注）
	 */
	"申请类型（前端不需要关注)"?: null | string
	[property: string]: any
}

export interface BatchPushWarehousingInspectRequest {
	/**
	 * 入库申请id集合
	 */
	inStoreApplyIdList?: number[] | null
	/**
	 * 用户id
	 */
	userId?: null | string
	[property: string]: any
}

/**
 * 关闭入参
 */
export interface CloseMatInStoreApplyRequest {
	/**
	 * ID
	 */
	id: number
	/**
	 * 关闭原因
	 */
	reason: string
	[property: string]: any
}

/**
 * 入库类型枚举
 */
export enum IWarehousingType {
	/**
	 * 备品备件入库
	 */
	back = "CK-BJ-RK",

	/**
	 * 随车配件入库
	 */
	parts = "CK-PJ-RK",

	/**
	 * 维修件入库
	 */
	repair = "CK-FX-RK",

	/**
	 * 实物物资入库（北京项目所用）
	 */
	physicalObject = "CK-SW-RK"
}
