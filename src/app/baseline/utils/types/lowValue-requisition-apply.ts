/**
 * @description 低值 - 领用申请 TS
 */

/**
 * 领用申请-审核状态数量 返回数据
 *
 * ApplyBpmStatusVo
 */
export interface LowValueApplyBpmStatusVo {
	/**
	 * 审批中数量
	 */
	activatingCnt?: number | null
	/**
	 * 已审批数量
	 */
	completedCnt?: number | null
	/**
	 * 待提交数量
	 */
	draftCnt?: number | null
	/**
	 * 已驳回数量
	 */
	rejectCnt?: number | null
	/**
	 * 审批任务数量
	 */
	taskCnt?: number | null
	[property: string]: any
}

/**
 * 领用申请 主表 入参
 *
 * MatLowValueApplyVORequest
 */
export interface MatLowValueApplyVORequest {
	/**
	 * 审批状态
	 */
	bpmStatus: string
	/**
	 * 申请单号
	 */
	code?: string
	currentPage: number
	/**
	 * 申请名称
	 */
	label?: string
	pageSize: number
	/**
	 * 申请人姓名=realname
	 */
	realname?: string
	sidx?: string
	sord?: string
	/**
	 * 仓库ID
	 */
	storeId?: number
	/**
	 * 部门ID
	 */
	sysOrgId?: number
	[property: string]: any
}

/**
 * 领用申请 主表 返参
 *
 * MatLowValueApplyVO
 */
export interface MatLowValueApplyVO {
	/**
	 * 已经分配数量
	 */
	allocationNum?: number | null
	/**
	 * 审批状态_view
	 */
	bpmStatus?: null | string
	/**
	 * 申请单号
	 */
	code?: null | string
	/**
	 * 申请人_view
	 */
	createdBy?: null | string
	/**
	 * 申请时间
	 */
	createdDate?: null | string
	/**
	 * 费用类别_view
	 */
	expenseCategory?: null | string
	/**
	 * 申请主键id
	 */
	id?: number | null
	/**
	 * 领用用途
	 */
	illustrate?: null | string
	/**
	 * 申请名称
	 */
	label?: null | string
	/**
	 * 物资编码数量
	 */
	materialCodeNum?: number | null
	/**
	 * 领料部门Id
	 */
	pickSysOrgId?: number | null
	/**
	 * 领料人Id
	 */
	pickUserId?: null | string
	/**
	 * 备注
	 */
	remark?: null | string
	/**
	 * 出库状态
	 */
	status?: null | string
	/**
	 * 仓库id
	 */
	storeId?: number
	/**
	 * 仓库名称
	 */
	storeLabel?: string
	/**
	 * 公司ID_view
	 */
	sysCommunityId?: number | null
	/**
	 * 部门ID_view
	 */
	sysOrgId?: number | null
	/**
	 * 领用数量
	 */
	useOutNum?: number | null
	[property: string]: any
}

/**
 * 领用申请 添加 入参
 * MatLowValueApplyDTO
 */
export interface MatLowValueApplyDTO {
	/**
	 * 申请单号
	 */
	code?: null | string
	/**
	 * 申请单创建人=领用人
	 */
	createdBy?: null | string
	/**
	 * 费用类别
	 */
	expenseCategory: string
	/**
	 * 申请单主键
	 */
	id?: number | null
	/**
	 * 说明-领料用途
	 */
	illustrate?: null | string
	/**
	 * 申请单名称
	 */
	label?: null | string
	/**
	 * 领料部门ID=申请部门ID
	 */
	pickSysOrgId?: number | null
	/**
	 * 领料人=低值领用申请负责人(部门负责人)
	 */
	pickUserId?: null | string
	/**
	 * 备注
	 */
	remark?: null | string
	/**
	 * 仓库id
	 */
	storeId: number
	/**
	 * 申请部门ID
	 */
	sysOrgId?: number | null
	/**
	 * 申请类型:MatStoreInOutStorageConstant.LOW_USE_REQUEST = DZ-LY
	 */
	type?: null | string
	[property: string]: any
}

/**
 * 领用申请 --- 物资明细/可选物资分页查询 入参
 */
export interface MatLowValueApplyItemVORequest {
	/**
	 * 物资编码
	 */
	code?: string
	currentPage: number
	/**
	 * 申请单ID
	 */
	id: number
	/**
	 * 物资名称
	 */
	label?: string
	pageSize: number
	sidx?: string
	sord?: string
	[property: string]: any
}
/**
 * 领用申请-扩展信息-领用物资(分页) 返参
 *
 * MatLowValueApplyItemVO
 */
export interface MatLowValueApplyItemVO {
	/**
	 * 已分配数量
	 */
	allocationNum?: number | null
	/**
	 * 申请ID
	 */
	applyId?: number | null
	/**
	 * 可领用数量=台账数量-已领用数量-冻结数量
	 */
	canUseNum?: number | null
	/**
	 * 冻结数量
	 */
	freezeNum?: number | null
	/**
	 * 已领用数量
	 */
	hasUsedNum?: number | null
	/**
	 * 物资明细ID|物资ID
	 */
	id?: number | null
	/**
	 * 台账数量
	 */
	lowValueBookNum?: number | null
	/**
	 * 低值类型_view
	 */
	lowValueType?: null | string
	/**
	 * 物资编码
	 */
	materialCode?: null | string
	/**
	 * 物资ID
	 */
	materialId?: number | null
	/**
	 * 物资名称
	 */
	materialLabel?: null | string
	/**
	 * 物资分类编码
	 */
	materialTypeCode?: null | string
	/**
	 * 物资分类ID
	 */
	materialTypeId?: number | null
	/**
	 * 物资分类名称
	 */
	materialTypeLabel?: null | string
	/**
	 * 本次领用数量
	 */
	num?: number | null
	/**
	 * 规格型号
	 */
	version?: null | string
	[property: string]: any
}

/**
 * 领用申请-扩展信息-领用物资-添加物资-保存 入参
 * AddApplyItemSelectDTO
 */
export interface AddApplyItemSelectDTO {
	/**
	 * 申请ID
	 */
	applyId: number
	/**
	 * 明细列表
	 */
	itemList: AddApplyItemSelectItemList[]
	[property: string]: any
}

export interface AddApplyItemSelectItemList {
	/**
	 * 物资ID
	 */
	materialId: number
	[property: string]: any
}

/**
 * 领用申请-扩展信息-领用物资-(本次领用-编辑) 入参
 * EditApplyItemUseLowNumDTO
 */
export interface EditApplyItemUseLowNumDTO {
	/**
	 * 物资明细ID
	 */
	id: number
	/**
	 * 领用数量
	 */
	num: number
	[property: string]: any
}

/**
 * 返回数据
 *
 * PageInfo«MatLowValueAllocationVO»
 */
export interface PageInfoMatLowValueAllocationVO {
	currentPage?: number | null
	pageSize?: number | null
	records?: number | null
	rows?: MatLowValueAllocationVO[] | null
	total?: number | null
	[property: string]: any
}

/**
 * 领用申请-分配-扩展信息-领用物资-使用分配(列表) 入参
 */
export interface MatLowValueAllocationVORequest {
	/**
	 * 申请单明细ID
	 */
	applyItemId: number
	currentPage: number
	pageSize: number
	sidx?: string
	sord?: string
	/**
	 * 申请部门ID
	 */
	sysOrgId: number
	[property: string]: any
}

/**
 * 领用申请-分配-扩展信息-领用物资-使用分配(列表) 返参
 *
 * MatLowValueAllocationVO
 */
export interface MatLowValueAllocationVO {
	/**
	 * 领用申请ID
	 */
	applyId?: number | null
	/**
	 * 领用申请明细ID
	 */
	applyItemId?: number | null
	/**
	 * 领用单号
	 */
	code?: null | string
	/**
	 * 完成数量
	 */
	completeNum?: number | null
	/**
	 * 冻结数量
	 */
	freezeNum?: number | null
	/**
	 * 领用分配ID
	 */
	id?: null | string
	/**
	 * 分配数量
	 */
	num?: number | null
	/**
	 * 手机号
	 */
	phone?: null | string
	/**
	 * 使用人
	 */
	realname?: null | string
	/**
	 * 已归还数量
	 */
	returnNum?: number | null
	/**
	 * 性别
	 */
	sex?: number | null
	/**
	 * 存放位置
	 */
	storeLocation?: null | string
	/**
	 * 公司ID_view
	 */
	sysCommunityId?: number | null
	/**
	 * 部门ID_view
	 */
	sysOrgId?: number | null
	/**
	 * 账号
	 */
	username?: null | string
	/**
	 * 已交旧数量
	 */
	wasteOldNum?: number | null
	[property: string]: any
}

/**
 * 领用申请-分配-扩展信息-领用物资-使用分配-添加使用人-保存 入参
 *
 * MatLowValueAllocationDTO
 */
export interface MatLowValueAllocationDTO {
	/**
	 * 申请ID
	 */
	applyId: number | string
	/**
	 * 领用申请物资明细ID
	 */
	applyItemId: number | string
	/**
	 * 物资ID
	 */
	materialId: number | string
	/**
	 * 账号
	 */
	usernameList: Record<string, any>[]
	[property: string]: any
}

/**
 * 领用申请 --- 物资明细 --- 分配 --- 编辑保存 入参
 *
 * MatLowValueAllocationEditDTO
 */
export interface MatLowValueAllocationEditDTO {
	/**
	 * 领用分配ID
	 */
	allocationId: number
	/**
	 * 分配数量
	 */
	num: number
	/**
	 * 存放位置
	 */
	storeLocation: string
	[property: string]: any
}

/**
 * 归还申请/交旧申请 选择领料单后 -扩展信息-归还物资-添加物资(分页) 入参
 * MatReturnApplyItemSelect
 */
export interface MatCanUseApplyItemSelectRequest {
	/**
	 * 申请单ID
	 */
	applyId: number
	/**
	 * 物资编码
	 */
	code?: string
	currentPage: number
	/**
	 * 物资名称
	 */
	label?: string
	/**
	 * 低值类型
	 */
	lowValueType?: string
	pageSize: number
	/**
	 * 使用人姓名模糊查询
	 */
	realname?: string
	sidx?: string
	sord?: string
	/**
	 * 申请单类型: 交旧申请（CK-JJ-SQ）、归还申请（DZ-GH）
	 */
	type: string
	/**
	 * 领用申请单ID
	 */
	useApplyId: number
	[property: string]: any
}

/**
 * 归还申请/交旧申请 选择领料单后 -扩展信息-归还物资-添加物资(分页) 返参
 *
 * MatCanUseApplyItemVO
 */
export interface MatCanUseApplyItemVO {
	/**
	 * 完成数量
	 */
	completeNum: number
	/**
	 * 冻结数量
	 */
	freezeNum: number
	/**
	 * 领用分配ID
	 */
	id?: number | null
	/**
	 * 低值类型_view
	 */
	lowValueType?: null | string
	/**
	 * 物资编码
	 */
	materialCode?: null | string
	/**
	 * 物资ID
	 */
	materialId: number
	/**
	 * 物资名称
	 */
	materialLabel?: null | string
	/**
	 * 物资分类编码
	 */
	materialTypeCode?: null | string
	/**
	 * 物资分类名称
	 */
	materialTypeLabel?: null | string
	/**
	 * 分配数量
	 */
	num?: number | null
	/**
	 * 主要材质_view
	 */
	quality?: null | string
	/**
	 * 已归还数量
	 */
	returnNum: number
	/**
	 * 使用人_view
	 */
	username: string
	/**
	 * 库存单位
	 */
	useUnit?: null | string
	/**
	 * 规格型号
	 */
	version?: null | string
	/**
	 * 已交旧数量
	 */
	wasteOldNum: number
	[property: string]: any
}

/**
 * 领用分配记录分配查询 返参
 *
 * MatLowValueAllocationLogDateVO
 */
export interface MatLowValueAllocationLogDateVO {
	/**
	 * 分配时间
	 */
	allocationDate?: null | string
	/**
	 * 分配数量
	 */
	num?: number | null
	[property: string]: any
}

/**
 * 领用分配记录分配明细查询 返参
 *
 * MatLowValueAllocationLogVO
 */
export interface MatLowValueAllocationLogVO {
	/**
	 * 领用申请ID
	 */
	applyId?: number | null
	/**
	 * 领用申请明细ID
	 */
	applyItemId?: number | null
	/**
	 * 领用分配记录ID
	 */
	id?: number | null
	/**
	 * 分配数量
	 */
	num?: number | null
	/**
	 * 手机号
	 */
	phone?: null | string
	/**
	 * 使用人
	 */
	realname?: null | string
	/**
	 * 性别
	 */
	sex?: number | null
	/**
	 * 存放位置
	 */
	storeLocation?: null | string
	/**
	 * 公司ID_view
	 */
	sysCommunityId?: number | null
	/**
	 * 部门ID_view
	 */
	sysOrgId?: number | null
	/**
	 * 账号
	 */
	username?: null | string
	[property: string]: any
}
