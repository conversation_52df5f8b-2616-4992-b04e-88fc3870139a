/**
 * 盘盈入库 主列表 参数
 * CheckProfitInStorePageVoQuery
 */
export interface CheckProfitInStorePageVoQuery {
	currentPage?: number
	/**
	 * 盘点人员名称
	 */
	firstCheckRealname?: string
	/**
	 * 盘盈入库单号
	 */
	inStoreCode?: string
	pageSize?: number
	/**
	 * 关联盘点计划编码
	 */
	planCode?: string
	/**
	 * 盘点计划名称
	 */
	planName?: string
	/**
	 * 复核人员名称
	 */
	secondCheckRealname?: string
	sidx?: string
	sord?: string
	/**
	 * 仓库编号
	 */
	storeCode?: string
	/**
	 * 关联盘点任务编码
	 */
	taskCode?: string
	[property: string]: any
}

/**
 * 盘盈入库 主列表
 * CheckProfitInStorePageVo
 */
export interface CheckProfitInStorePageVo {
	/**
	 * 盘点人员
	 */
	firstCheckRealname?: null | string
	/**
	 * 盘盈入库单号
	 */
	inStoreCode?: null | string
	/**
	 * 入库时间
	 */
	inStoreDate?: null | string
	/**
	 * 入库物资编码数量
	 */
	materialCodeCount?: number | null
	/**
	 * 关联盘点计划编码
	 */
	planCode?: null | string
	/**
	 * 盘点计划名称
	 */
	planName?: null | string
	/**
	 * 复核人员
	 */
	secondCheckRealname?: null | string
	/**
	 * 仓库编号
	 */
	storeCode?: null | string
	/**
	 * 入库仓库名称
	 */
	storeName?: null | string
	/**
	 * 关联盘点任务编码
	 */
	taskCode?: null | string
	[property: string]: any
}

/**
 * 盘盈入库 详情 - 返回数据
 *
 * CheckTaskProfitInStoreDetailVo
 */
export interface CheckTaskProfitInStoreDetailVo {
	/**
	 * 盘盈入库单号
	 */
	inStoreCode?: null | string
	/**
	 * 入库时间
	 */
	inStoreDate?: { [key: string]: any }
	/**
	 * 入库物资编码数量
	 */
	materialCodeCount?: number | null
	/**
	 * 关联盘点计划编码
	 */
	planCode?: null | string
	/**
	 * 盘点计划名称
	 */
	planName?: null | string
	/**
	 * 入库仓库名称
	 */
	storeName?: null | string
	/**
	 * 关联盘点任务编码
	 */
	taskCode?: null | string
	[property: string]: any
}
/**
 * 盘盈入库 - 物资明细 参数
 * CheckTaskProfitInStoreMaterialPageVoQuery
 */
export interface CheckTaskProfitInStoreMaterialPageVoQuery {
	currentPage?: number
	/**
	 * 搜索关键词
	 */
	keyword?: string
	pageSize?: number
	sidx?: string
	sord?: string
	/**
	 * 任务id
	 */
	taskId?: number
	[property: string]: any
}

/**
 * 盘盈入库 - 物资明细
 * CheckTaskProfitInStoreMaterialPageVo
 */
export interface CheckTaskProfitInStoreMaterialPageVo {
	/**
	 * 入库数量
	 */
	inStoreNum?: number | null
	/**
	 * 物资编码
	 */
	materialCode?: null | string
	/**
	 * 物资名称
	 */
	materialName?: null | string
	/**
	 * 规格型号
	 */
	materialVersion?: null | string
	/**
	 * 入库区域
	 */
	regionLabel?: null | string
	/**
	 * 入库货位编码
	 * 入库货位
	 */
	roomCode?: null | string
	/**
	 * 技术参数
	 */
	technicalParameter?: null | string
	/**
	 * 库存单位
	 */
	useUnit?: null | string
	[property: string]: any
}
