/**
 * @description 退库申请 类型声明
 */

/**
 * 退库申请 查询参数
 */
export interface MatReturnStoreApplyReqParams {
	/**
	 * 退库单号
	 */
	code?: string
	/**
	 * 申请人
	 */
	createdByName?: string
	currentPage?: number
	/**
	 * ID
	 */
	id?: number
	/**
	 * 退库单名称
	 */
	label?: string
	pageSize?: number
	/**
	 * 关联领料单号
	 */
	preBusinessCode?: string
	/**
	 * 关联领料单Id
	 */
	preBusinessId?: number
	/**
	 * 退库原因说明
	 */
	reason?: string
	sidx?: string
	sord?: string
	/**
	 * 状态
	 * 状态  1是待处理,2是已完成
	 */
	status?: string
	/**
	 * 仓库ID
	 */
	storeId?: number
	/**
	 * 部门ID
	 */
	sysOrgId?: number
	[property: string]: any
}

/**
 * 退库申请 物资分页 请求参数
 */
export interface MatReturnStoreApplyItemReqParams {
	/**
	 * 申请单Id
	 */
	applyId?: number
	currentPage?: number
	/**
	 * 是否分配质检员  0是否 1是"
	 * 是否分配质检员 0是否 1是
	 */
	existFlag?: string
	/**
	 * ID
	 * id
	 */
	id?: number
	/**
	 * 物资编码
	 */
	materialCode?: string
	/**
	 * 编码名称
	 */
	materialName?: string
	pageSize?: number
	sidx?: string
	sord?: string
	[property: string]: any
}

/**
 * 退库申请 vo
 *
 * MatReturnStoreApplyVo
 */
export interface MatReturnStoreApplyVo {
	/**
	 * 金额
	 */
	amount?: number | null
	/**
	 * 审批状态
	 */
	bpmStatus?: null | string
	/**
	 * 申请单号
	 */
	code?: null | string
	/**
	 * 创建人
	 */
	createdBy?: null | string
	/**
	 * 创建日期
	 */
	createdDate?: null | string
	/**
	 * ID
	 */
	id?: number | null
	/**
	 * 申请单名称
	 */
	label?: null | string
	/**
	 * 物资编码数量
	 */
	materialCodeNum?: number | null
	/**
	 * 关联领料单号
	 */
	preBusinessCode?: null | string
	/**
	 * 前置业务ID
	 */
	preBusinessId?: number | null
	/**
	 * 申请原因
	 */
	reason?: null | string
	/**
	 * 退库数量
	 */
	returnNum?: number | null
	/**
	 * 仓库ID
	 */
	storeId?: number | null
	/**
	 * 入库仓库
	 */
	storeLabel?: null | string
	/**
	 * 部门ID
	 */
	sysOrgId?: number | null
	[property: string]: any
}
/**
 * 退库申请 DTO
 */
export interface MatReturnStoreApplyDTO {
	/**
	 * 申请单号
	 */
	code?: null | string
	/**
	 * 申请人
	 */
	createdByName?: null | string
	/**
	 * ID
	 */
	id?: number | null
	/**
	 * 申请单名称
	 */
	label?: null | string
	/**
	 * 关联领料单号
	 */
	preBusinessCode?: null | string
	/**
	 * 关联领料单Id
	 */
	preBusinessId?: number | null
	/**
	 * 退库原因说明
	 */
	reason?: null | string
	/**
	 * 状态
	 */
	status?: null | string
	/**
	 * 仓库ID
	 */
	storeId?: number | null
	/**
	 * 部门ID
	 */
	sysOrgId?: number | null
	[property: string]: any
}

/**
 * 退库申请物资明细 vo
 *
 * MatReturnStoreApplyItemVo
 */
export interface MatReturnStoreApplyItemVo {
	/**
	 * 金额
	 */
	amount?: number | null
	/**
	 * 申请ID
	 */
	applyId?: number | null
	/**
	 * 批次Id
	 */
	batchId?: null | string
	/**
	 * 批次号
	 */
	batchNo?: null | string
	/**
	 * 完成数量
	 */
	completeNum?: number | null
	/**
	 * 已退库数量
	 */
	finishNum?: number | null
	/**
	 * ID
	 * id
	 */
	id?: number | null
	/**
	 * 质检员
	 */
	inspectionPersonId?: null | string
	/**
	 * 物资编码
	 */
	materialCode?: null | string
	/**
	 * 编码名称
	 */
	materialName?: null | string
	/**
	 * 数量
	 */
	num?: number | null
	/**
	 * 货位Id
	 */
	roomId?: null | string
	/**
	 * 货位名称
	 */
	roomName?: null | string
	/**
	 * 技术参数
	 */
	technicalParameter?: null | string
	/**
	 * 退库金额
	 */
	totalPrice?: number | null
	/**
	 * 库存单位
	 */
	useUnit?: number | null
	/**
	 * 供应商ID
	 */
	validityPeriod?: null | string
	/**
	 * 规格型号
	 */
	version?: null | string

	[property: string]: any
}

/**
 * MatReturnStoreApplyAddItemDTO
 */
export interface MatReturnStoreApplyAddItemDTO {
	/**
	 * 申请单Id
	 */
	applyId: number
	/**
	 * 添加物资列表id集合
	 */
	itemIdList: any[]
}

/**
 * 退库申请 - 编辑明细数量
 * MatReturnStoreApplyEditItemDTO
 */
export interface MatReturnStoreApplyEditItemDTO {
	/**
	 * 申请单Id
	 */
	applyId?: number | null
	/**
	 * 申请单Id
	 * 明细列表
	 */
	itemList?: MatReturnStoreApplyEditItemListDTO[] | null
	[property: string]: any
}

/**
 * MatReturnStoreApplyEditItemListDTO
 */
export interface MatReturnStoreApplyEditItemListDTO {
	/**
	 * 退库数量
	 */
	completeNum?: number | null
	/**
	 * ID
	 * id
	 */
	id?: number | null
	[property: string]: any
}

/**
 * 查看退库批次 参数
 * MatReturnStoreApplyBatchpageReqParams
 */

export interface MatReturnStoreApplyBatchpageReqParams {
	id: number
	currentPage?: number
	pageSize?: number
	sidx?: string
	sord?: string
	[property: string]: any
}

/**
 * 查看退库批次
 * MatReturnStoreApplyBatchpageVo
 */
export interface MatReturnStoreApplyBatchpageVo {
	/**
	 * 采购单价
	 */
	amount?: number | null
	/**
	 * 批次号
	 */
	batchNo?: null | string
	/**
	 * 退库数量
	 */
	completeNum?: number | null
	/**
	 * ID
	 */
	id?: number | null
	/**
	 * 入库时间
	 */
	inStoreTime?: null | string
	/**
	 * 区域id
	 */
	regionId?: number | null
	/**
	 * 区域
	 */
	regionLabel?: null | string
	/**
	 * 货位
	 */
	roomCode?: null | string
	/**
	 * 货位ID
	 */
	roomId?: number | null
	/**
	 * 金额
	 */
	totalPrice?: number | null
	/**
	 * 质保期
	 */
	validityPeriod?: null | string
	[property: string]: any
}

/**
 * 查看退库批次 详情 返回数据
 *
 * MatReturnStoreApplyBatchInfoVo
 */
export interface MatReturnStoreApplyBatchInfoVo {
	/**
	 * 退库数量
	 */
	completeNum?: number | null
	/**
	 * ID
	 */
	id?: number | null
	/**
	 * 物资编码
	 */
	materialCode?: null | string
	/**
	 * 物资名称
	 */
	materialName?: null | string
	/**
	 * 出库仓库
	 */
	storeName?: null | string
	/**
	 * 技术参数
	 */
	technicalParameter?: null | string
	/**
	 * 规格型号
	 */
	version?: null | string
	[property: string]: any
}
