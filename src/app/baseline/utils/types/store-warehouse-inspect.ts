/* 入库管理 - 质量检验 - 获取列表 传参 */
export interface MatInStoreInspectRequestParams {
	/**
	 * 质检单号
	 */
	code?: string
	currentPage?: number
	pageSize?: number
	/**
	 * 待处理列表：1，已完成列表：2
	 */
	tableType: number
	/**
	 * 质检员ID
	 */
	userId?: number
	[property: string]: any
}

/* 入库管理 - 质量检验 - 获取列表 VO */
export interface MatInStoreInspectVO {
	/**
	 * 入库单号
	 */
	code?: null | string
	/**
	 * ID
	 */
	id?: number | null
	/**
	 * 已质检物资编码
	 */
	inspectedNum?: number | null
	/**
	 * 质检完成时间
	 */
	inspectedTime?: null | string
	/**
	 * 质检物资编码
	 */
	inspectNum?: number | null
	/**
	 * 质检员姓名
	 */
	inspectUsername?: null | string
	/**
	 * 入库单号
	 */
	parentCode?: null | string
	/**
	 * 入库Id
	 */
	parentId?: number | null
	/**
	 * 入库状态
	 */
	status?: null | string
	/**
	 * 仓库Id
	 */
	storeId?: number | null
	/**
	 * 仓库名称
	 */
	storeName?: null | string
	/**
	 * 待质检物资编码
	 */
	toInspectNum?: number | null

	toStoreNum?: number | null
	/**
	 * 推送入库状态
	 */
	toStoreStatus?: null | string
	[property: string]: any
}

/* 入库管理 - 质量检验 - 物资明细 传参 */
export interface MatInStoreInspectItemRequestParams {
	/**
	 * 质检单ID
	 */
	applyId: number
	currentPage?: number
	pageSize?: number
	/**
	 * 质检状态
	 */
	status?: string
	/**
	 * 推送入库状态
	 */
	toStoreStatus?: string
	[property: string]: any
}

/* 入库管理 - 质量检验 - 物资明细 返回体 */
/* export interface MatInStoreInspectItemVO {
	currentPage?: number | null
	pageSize?: number | null
	records?: number | null
	rows?: MatInStoreInspectItemVO[] | null
	total?: number | null
	[property: string]: any
} */

/**
 * 质量检验 详情 VO
 *
 * MatInStoreInspectItemVO
 */
export interface MatInStoreInspectItemVO {
	/**
	 * 金额
	 */
	amount?: number | null
	/**
	 * 申请类型
	 */
	applyId?: number | null
	/**
	 * 采购单位
	 */
	buyUnit?: null | string
	/**
	 * 已检验量
	 */
	checkedNum?: number | null
	/**
	 * 申请单名称
	 */
	completeNum?: number | null
	/**
	 * 创建人
	 */
	createdBy?: null | string
	/**
	 * 创建日期
	 */
	createdDate?: null | string
	/**
	 * del_flag
	 */
	delFlag?: null | string
	/**
	 * 仓库ID
	 */
	formContent?: null | string
	/**
	 * 申请人ID
	 */
	formId?: number | null
	/**
	 * ID
	 */
	id?: number | null
	/**
	 * 更新人
	 */
	lastModifiedBy?: null | string
	/**
	 * 更新日期
	 */
	lastModifiedDate?: null | string
	/**
	 * 物资编码
	 */
	materialCode?: null | string
	/**
	 * 物资ID
	 */
	materialId?: number | null
	/**
	 * 物资名称
	 */
	materialName?: null | string
	/**
	 * 申请单号
	 */
	num?: number | null
	/**
	 * 采购金额
	 */
	purchaseAmount?: number | null
	/**
	 * 采购单价
	 */
	purchasePrice?: number | null
	/**
	 * 合格量
	 */
	qualifiedNum?: number | null
	/**
	 * 质检情况说明
	 */
	remark?: null | string
	/**
	 * 状态
	 */
	status?: null | string
	/**
	 * 技术参数
	 * 规格型号
	 */
	technicalParameter?: null | string
	/**
	 * 待质检量
	 */
	toCheckNum?: number | null
	/**
	 * 不合格量
	 */
	unQualifiedNum?: number | null
	/**
	 * 供应商ID
	 */
	validityPeriod?: null | string
	/**
	 * 规格型号
	 */
	version?: null | string
}

/**
 * 质检 传参
 *
 */
export interface EditInspectRequestParams {
	/**
	 * 质检合格量
	 */
	completeNum?: string
	/**
	 * ID
	 */
	id?: number
	/**
	 * 质检情况说明
	 */
	remark?: string
	[property: string]: any
}
/**
 * 推送入库 传参
 *
 */
export interface PushInstoreRequestParams {
	/**
	 * 推送入库id集合
	 */
	ids?: string
	[property: string]: any
}

/**
 * 质量检验记录VO 入参
 *
 * MatInStoreInspectRecordVORequestParams
 */
export interface MatInStoreInspectRecordVORequestParams {
	currentPage: number
	/**
	 * 质检物资明细id
	 */
	itemId: number
	pageSize: number
	sidx?: string
	sord?: string
	[property: string]: any
}

/**
 * 质量检验记录VO
 *
 * MatInStoreInspectRecordVO
 */
export interface MatInStoreInspectRecordVO {
	/**
	 * 创建人
	 */
	createdBy?: null | string
	/**
	 * 创建日期
	 */
	createdDate?: null | string
	/**
	 * ID
	 */
	id?: number | null
	/**
	 * 质检物资明细ID
	 */
	itemId?: number | null
	/**
	 * 更新人
	 */
	lastModifiedBy?: null | string
	/**
	 * 更新日期
	 */
	lastModifiedDate?: null | string
	/**
	 * 质检合格数量
	 */
	qualifiedNum?: number | null
	/**
	 * 质检情况说明
	 */
	remark?: null | string
	/**
	 * 推送入库状态 1：未推送  3：已推送
	 */
	toStoreStatus?: null | string
	/**
	 * 质检不合格数量
	 */
	unqualifiedNum?: number | null
	[property: string]: any
}

export interface TransferInspectRequest {
	/**
	 * 质检单id
	 */
	id: number
	/**
	 * 转派原因
	 */
	reason: string
	/**
	 * 接收人账号
	 */
	receiveUsername: string
	[property: string]: any
}
