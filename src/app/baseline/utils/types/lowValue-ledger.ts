/**
 * 低值台账 主列表 入参
 *
 * MatLowValueBookVORequest
 */

export interface MatLowValueBookVORequest {
	currentPage: number
	/**
	 * 物资分类的低值类型(dictCode:LOW_VALUE_TYPE)
	 */
	lowValueType?: string
	/**
	 * 物资编码
	 */
	materialCode?: string
	/**
	 * 物资名称
	 */
	materialLabel?: string
	/**
	 * 物资分类编码
	 */
	materialTypeCode?: string
	/**
	 * 物资分类名称
	 */
	materialTypeLabel?: string
	pageSize: number
	sidx?: string
	sord?: string
	[property: string]: any
}
/**
 * 低值台账 主列表 返回数据
 *
 * MatLowValueBookVO
 */
export interface MatLowValueBookVO {
	/**
	 * 台账数量
	 */
	bookNum?: number | null
	/**
	 * 可领用数量
	 */
	canUseNum?: number | null
	/**
	 * 冻结数量
	 */
	freezeNum?: number | null
	/**
	 * 物资分类的低值类型(dictCode:LOW_VALUE_TYPE)
	 */
	lowValueType?: null | string
	/**
	 * 物资编码
	 */
	materialCode?: null | string
	/**
	 * 物资ID
	 */
	materialId?: number | null
	/**
	 * 物资名称
	 */
	materialLabel?: null | string
	/**
	 * 物资分类编码
	 */
	materialTypeCode?: null | string
	/**
	 * 物资分类名称
	 */
	materialTypeLabel?: null | string
	/**
	 * 标准成本
	 */
	standardCost?: number | null
	/**
	 * 技术参数
	 */
	technicalParameter?: null | string
	/**
	 * 已领用数量
	 */
	useNum?: number | null
	/**
	 * 库存单位
	 */
	useUnit?: null | string
	/**
	 * 规格型号
	 */
	version?: null | string
	[property: string]: any
}

/**
 * 低值台账记录分页查询（除分配记录外） 入参
 */
export interface MatLowValueBookDetailVORequest {
	currentPage: number
	/**
	 * 物资ID
	 */
	materialId: number
	pageSize: number
	sidx?: string
	sord?: string

	/**
	 * 来源类型
	 */
	sourceTypes: string
	[property: string]: any
}

/**
 * 低值台账记录分页查询（除分配记录外） 返参
 */
export interface MatLowValueBookDetailVO {
	/**
	 * 业务单号
	 */
	businessCode?: null | string
	/**
	 * 业务ID
	 */
	businessId?: number | null
	/**
	 * 业务名称
	 */
	businessLabel?: null | string
	/**
	 * 业务类型
	 */
	businessType?: null | string
	/**
	 * 申请人_view
	 */
	createdBy?: null | string
	/**
	 * 申请时间
	 */
	createdDate?: null | string
	/**
	 * 领用用途
	 */
	illustrate?: null | string
	/**
	 * 物资数量
	 */
	num?: number | null
	/**
	 * 前置业务单号
	 */
	preBusinessCode?: null | string
	/**
	 * 前置业务ID
	 */
	preBusinessId?: number | null
	/**
	 * 前置业务类型
	 */
	preBusinessType?: null | string
	/**
	 * 货位编码
	 */
	roomCode?: null | string
	/**
	 * 货位ID
	 */
	roomId?: number | null
	/**
	 * 来源类型 0入库 1领用 2归还 3交旧 5退货 6盘盈 7盘亏
	 */
	sourceType: string
	/**
	 * 仓库ID
	 */
	storeId?: number | null
	/**
	 * 仓库名称
	 */
	storeLabel?: null | string
	/**
	 * 库管员
	 */
	storeManage?: null | string
	/**
	 * 所属部门ID_view
	 */
	sysOrgId?: number | null
	/**
	 * 使用人_view
	 */
	username?: number | null
	[property: string]: any
}

/**
 * 低值台账记录分页查询（分配记录） 返参
 *
 * MatLowValueAllocationVO
 */
export interface MatLowValueAllocationVO {
	/**
	 * 领用申请ID
	 */
	applyId?: number | null
	/**
	 * 领用申请明细ID
	 */
	applyItemId?: number | null
	/**
	 * 领用单号
	 */
	code?: null | string
	/**
	 * 完成数量
	 */
	completeNum?: number | null
	/**
	 * 冻结数量
	 */
	freezeNum?: number | null
	/**
	 * 领用分配ID
	 */
	id?: null | string
	/**
	 * 分配数量
	 */
	num?: number | null
	/**
	 * 手机号
	 */
	phone?: null | string
	/**
	 * 使用人
	 */
	realname?: null | string
	/**
	 * 已归还数量
	 */
	returnNum?: number | null
	/**
	 * 性别
	 */
	sex?: number | null
	/**
	 * 存放位置
	 */
	storeLocation?: null | string
	/**
	 * 公司ID_view
	 */
	sysCommunityId?: number | null
	/**
	 * 部门ID_view
	 */
	sysOrgId?: number | null
	/**
	 * 账号
	 */
	username?: null | string
	/**
	 * 已交旧数量
	 */
	wasteOldNum?: number | null
	[property: string]: any
}

/**
 * 来源类型 0入库 1领用 2归还 3交旧 5退货 6盘盈 7盘亏
 */
export enum IMatLowValueBookDetailVOSourceType {
	/**
	 * 0: 入库
	 */
	toWarehouse = 0,
	/**
	 * 1：领用
	 */
	lowValueUse = 1,
	/**
	 * 2: 归还
	 */
	lowValueReturn = 2,
	/**
	 * 3: 交旧
	 */
	wasteHandover = 3,
	/**
	 * 4: 分配
	 */
	lowValueAllocation = 4,
	/**
	 * 5：退货
	 */
	goodsReturn = 5,
	/**
	 * 6：盘盈
	 */
	inventoryProfit = 6,
	/**
	 * 7：盘亏
	 */
	inventoryLoss = 7,

	/**
	 * 8：调拨出库
	 */
	transferOut = 8,
	/**
	 * 8：调拨入库
	 */
	transferIn = 9
}
