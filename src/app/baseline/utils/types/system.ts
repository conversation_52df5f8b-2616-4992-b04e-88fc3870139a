export interface MatStoreJobOrderDTO {
	currentPage?: number
	/**
	 * ID
	 */
	id?: number
	/**
	 * 工单名称
	 */
	jobName?: string
	/**
	 * 工单号
	 */
	jobNo?: string
	pageSize?: number
	sidx?: string
	sord?: string
	[property: string]: any
}

/**
 * SystemUserVo
 */
export interface SystemUserVo {
	/**
	 * ID
	 */
	id?: null | string
	/**
	 * 手机号
	 */
	phone?: null | string
	/**
	 * 性别
	 */
	sex?: number | null
	/**
	 * 岗位
	 */
	station?: null | string
	/**
	 * 公司ID
	 */
	sysCommunityId?: number | null
	/**
	 * 所属部门ID
	 */
	sysOrgId?: number | null
	/**
	 * 班组
	 */
	team?: null | string
	/**
	 * 账号
	 */
	username?: null | string

	/**
	 * 真实姓名
	 */
	realname?: string
	[property: string]: any
}

export interface IInventoryUserSelectorParams {
	currentPage?: number
	pageSize?: number
	/**
	 * realname
	 */
	realname?: string
	/**
	 * 已选择用户名列表
	 */
	selectedUserNames?: string[]
	sidx?: string
	sord?: string
	/**
	 * 所属部门ID
	 */
	sysOrgId?: number
	/**
	 * username
	 */
	username?: string
	/**
	 * ID
	 * usernameList
	 */
	usernameList?: string[]
	[property: string]: any
}

export interface SystemUserDTO {
	currentPage?: number
	pageSize?: number
	/**
	 * username
	 * realname
	 */
	realname?: string
	sidx?: string
	sord?: string
	/**
	 * 所属部门ID
	 */
	sysOrgId?: number
	/**
	 * username
	 */
	username?: string

	/**
	 *过滤部门Id
	 */
	filterSysOrgId?: number
	/**
	 * ID
	 * usernameList
	 */
	usernameList?: string[]
	[property: string]: any
}

/**
 * MatStoreJobOrderVo
 */
export interface MatStoreJobOrderVo {
	/**
	 * ID
	 */
	id?: number | null
	/**
	 * 工单名称
	 */
	jobName?: null | string
	/**
	 * 工单号
	 */
	jobNo?: null | string
	/**
	 * 工单来源
	 */
	jobSource?: null | string
	/**
	 * 工单类型
	 */
	jobType?: null | string
	/**
	 * 线路
	 */
	lineNoId?: null | string
	/**
	 * 工单执行班组
	 */
	workGroupName?: null | string
	[property: string]: any
}

export interface MatStoreJobOrderGoodsDTO {
	currentPage?: number
	/**
	 * ID
	 */
	id?: number
	/**
	 * 工单id（查询明细所需字段）
	 */
	jobId?: number
	pageSize?: number
	sidx?: string
	sord?: string
	[property: string]: any
}

/**
 * MatStoreJobOrderDetailVo
 */
export interface MatStoreJobOrderDetailVo {
	/**
	 * 冻结数量
	 */
	freezeNum?: number | null
	/**
	 * ID
	 */
	id?: number | null
	/**
	 * 工单id
	 */
	jobId?: number | null
	/**
	 * 物资名称
	 */
	materialName?: null | string
	/**
	 * 物资编码
	 */
	materialNo?: null | string
	/**
	 * 库存数量
	 */
	storeNum?: number | null
	/**
	 * 技术参数
	 */
	technicalParameter?: null | string
	/**
	 * 物资分类编码
	 */
	typeCode?: null | string
	/**
	 * 物资分类名称
	 */
	typeLabel?: null | string
	/**
	 * 库存单位
	 */
	useUnit?: null | string
	/**
	 * 规格型号
	 */
	version?: null | string
	[property: string]: any
}
