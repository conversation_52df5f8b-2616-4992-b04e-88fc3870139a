/**
 * @description 废旧 - 返修台账 TS类型
 */

/**
 * 主列表 入参
 * RepairBookPageVo
 */
export interface RepairBookPageVoRequest {
	/**
	 * 物资编码
	 */
	code?: string
	currentPage?: number
	/**
	 * 物资名称
	 */
	label?: string
	pageSize?: number
	/**
	 * 主要材质
	 */
	quality?: string
	sidx?: string
	sord?: string
	/**
	 * 物资分类id列表(逗号隔开)
	 */
	typeIds?: string
	[property: string]: any
}

/**
 * 主列表 返回参数
 * RepairBookPageVo
 */
export interface RepairBookPageVo {
	/**
	 * 已返修数量
	 */
	completeRepairNum?: number | null
	/**
	 * 台账id
	 */
	id?: number | null
	/**
	 * 物资编码
	 */
	materialCode?: null | string
	/**
	 * 物资id
	 */
	materialId?: number | null
	/**
	 * 物资名称
	 */
	materialName?: null | string
	/**
	 * 主要材质
	 */
	quality?: null | string
	/**
	 * 预估回收重量
	 */
	recoveryWeight?: number | null
	/**
	 * 返修中数量
	 */
	repairingNum?: number | null
	/**
	 * 返修数量
	 */
	repairNum?: number | null
	/**
	 * 技术参数
	 */
	technicalParameter?: null | string
	/**
	 * 库存单位
	 */
	useUnit?: null | string
	/**
	 * 规格型号
	 */
	version?: null | string
	/**
	 * 待返修数量
	 */
	waitingRepairNum?: number | null
	/**
	 * 废旧物资分类
	 */
	wasteMaterialType?: null | string
	[property: string]: any
}

/**
 * 详情 返参
 *
 * RepairBookDetailVo
 */
export interface RepairBookDetailVo {
	/**
	 * 已返修数量
	 */
	completeRepairNum?: number | null
	/**
	 * 台账id
	 */
	id?: number | null
	/**
	 * 物资编码
	 */
	materialCode?: null | string
	/**
	 * 物资id
	 */
	materialId?: number | null
	/**
	 * 物资名称
	 */
	materialName?: null | string
	/**
	 * 主要材质
	 */
	quality?: null | string
	/**
	 * 预估回收重量
	 */
	recoveryWeight?: number | null
	/**
	 * 返修中数量
	 */
	repairingNum?: number | null
	/**
	 * 返修数量
	 */
	repairNum?: number | null
	/**
	 * 技术参数
	 */
	technicalParameter?: null | string
	/**
	 * 库存单位
	 */
	useUnit?: null | string
	/**
	 * 规格型号
	 */
	version?: null | string
	/**
	 * 待返修数量
	 */
	waitingRepairNum?: number | null
	/**
	 * 废旧物资分类
	 */
	wasteMaterialType?: null | string
	/**
	 * 交旧类型-0领料单交旧 1历史物资交旧 2低值易耗交旧
	 */
	wasteOldType?: null | string
	[property: string]: any
}

/**
 * 库存查询 返参
 * RepairBookStorePageVo
 */
export interface RepairBookStorePageVo {
	/**
	 * 成本中心id
	 */
	costCenterId?: number | null
	/**
	 * 成本中心名称
	 */
	costCenterName?: null | string
	/**
	 * 段区id
	 */
	depotId?: number | null
	/**
	 * 物资id
	 */
	materialId?: number | null
	/**
	 * 库存数量
	 */
	num?: number | null
	/**
	 * 仓库位置
	 */
	positionId?: null | string
	/**
	 * 仓库编码
	 */
	storeCode?: null | string
	/**
	 * 库管员
	 */
	storeManage?: null | string
	/**
	 * 仓库名称
	 */
	storeName?: null | string
	/**
	 * 公司id
	 */
	sysCommunityId?: number | null
	[property: string]: any
}

/**
 * 交旧记录 返参
 * RepairBookWasteOldRecordPageVo
 */
export interface RepairBookWasteOldRecordPageVo {
	/**
	 * 申请单号
	 */
	applyCode?: null | string
	/**
	 * 申请时间
	 */
	applyDate?: null | string
	/**
	 * 交旧业务名称
	 */
	applyName?: null | string
	/**
	 * 关联领料单号
	 */
	businessApplyCode?: null | string
	/**
	 * 物资id
	 */
	materialId?: number | null
	/**
	 * 申请部门
	 */
	sysOrgId?: number | null
	/**
	 * 申请人姓名
	 */
	userName?: null | string
	/**
	 * 交旧数量
	 */
	wasteOldNum?: number | null
	[property: string]: any
}

/**
 * 返修记录 返参
 * RepairBookRepairRecordPageVo
 */
export interface RepairBookRepairRecordPageVo {
	/**
	 * 申请时间
	 */
	applyDate?: null | string
	/**
	 * 返修单号
	 */
	businessApplyCode?: null | string
	/**
	 * 返修业务名称
	 */
	businessApplyLabel?: null | string
	/**
	 * 物资id
	 */
	materialId?: number | null
	/**
	 * 维修公司
	 */
	repairCompanyName?: null | string
	/**
	 * 维修时间（天）
	 */
	repairDayCount?: number | null
	/**
	 * 维修费用
	 */
	repairFee?: number | null
	/**
	 * 返修数量
	 */
	repairNum?: number | null
	/**
	 * 申请部门
	 */
	sysOrgId?: number | null
	/**
	 * 申请人姓名
	 */
	userName?: null | string
	[property: string]: any
}

/**
 * 明细 入参
 *
 * RepairBookItemRequest
 */
export interface RepairBookItemRequest {
	/**
	 * 台账id
	 */
	bookId: number
	currentPage?: number
	pageSize?: number
	sidx?: string
	sord?: string
	[property: string]: any
}
