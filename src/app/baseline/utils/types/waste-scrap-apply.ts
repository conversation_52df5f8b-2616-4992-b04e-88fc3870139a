/**
 * 报废申请分页 入参
 */
export interface MatWasteScrapApplyPageVoQuery {
	/**
	 * 审批状态
	 */
	bpmStatus: string
	/**
	 * 申请人
	 */
	createdByName?: string
	currentPage?: number
	pageSize?: number
	/**
	 * 主要材质
	 */
	quality?: string
	sidx?: string
	sord?: string
	/**
	 * 公司id
	 */
	sysCommunityId?: number
	/**
	 * 废旧物资分类
	 */
	wasteMaterialType?: string
	[property: string]: any
}

/**
 * 报废申请分页Vo
 *
 * MatWasteScrapApplyPageVo
 */
export interface MatWasteScrapApplyPageVo {
	/**
	 * 审批状态_view
	 */
	bpmStatus?: null | string
	/**
	 * 报废申请单号
	 */
	code?: null | string
	/**
	 * 申请人_view
	 */
	createdBy?: null | string
	/**
	 * 申请时间
	 */
	createdDate?: null | string
	/**
	 * 报废申请主键
	 */
	id?: number | null
	/**
	 * 报废申请名称
	 */
	label?: null | string
	/**
	 * 物资编码数量
	 */
	materialCodeCnt?: number | null
	/**
	 * 物资分类数量
	 */
	materialTypeCnt?: number | null
	/**
	 * 主要材质种类
	 */
	qualityCnt?: number | null
	/**
	 * 预估回收总重量
	 */
	recoveryWeight?: number | null
	/**
	 * 所属公司_view
	 */
	sysCommunityId?: number | null
	/**
	 * 申请部门_view
	 */
	sysOrgId?: number | null
	/**
	 * 废旧物资类型-数据字典-废旧物资类型 直接返回value 0 1 2
	 * 0: A类（金属性材料价值较高）
	 * 1: B类（列入国家危险品目录）
	 * 2: C类（除A/B类以外）
	 * 废旧物资分类
	 */
	wasteMaterialType?: null | string
	[property: string]: any
}

/**
 * 报废申请 添加DTO
 * MatWasteScrapApplyDTO
 */
export interface MatWasteScrapApplyDTO {
	/**
	 * 报废申请单号
	 */
	code?: null | string
	/**
	 * 废旧认定决策依据
	 */
	decisionBasis?: null | string
	/**
	 * 报废申请主键
	 */
	id?: number | null
	/**
	 * 报废申请名称
	 */
	label?: null | string
	/**
	 * 报废原因
	 */
	reason?: null | string
	/**
	 * 备注
	 */
	remark?: null | string
	/**
	 * 废旧物资类型-数据字典-废旧物资类型 直接返回value 0 1 2
	 * 0: A类（金属性材料价值较高）
	 * 1: B类（列入国家危险品目录）
	 * 2: C类（除A/B类以外）
	 * 废旧物资分类
	 */
	wasteMaterialType?: null | string
	[property: string]: any
}

/**
 * 报废申请 -> 返回数据
 *
 * MatWasteScrapApplyVo
 */
export interface MatWasteScrapApplyVo {
	/**
	 * 审批状态_view
	 */
	bpmStatus?: null | string
	/**
	 * 报废申请单号
	 */
	code?: null | string
	/**
	 * 申请人_view
	 */
	createdBy?: null | string
	/**
	 * 申请时间
	 */
	createdDate?: null | string
	/**
	 * 废旧认定决策依据
	 */
	decisionBasis?: null | string
	/**
	 * 报废申请主键
	 */
	id?: number | null
	/**
	 * 报废申请名称
	 */
	label?: null | string
	/**
	 * 物资编码数量
	 */
	materialCodeCnt?: number | null
	/**
	 * 物资分类数量
	 */
	materialTypeCnt?: number | null
	/**
	 * 主要材质种类
	 */
	qualityCnt?: number | null
	/**
	 * 报废原因
	 */
	reason?: null | string
	/**
	 * 预估回收总重量
	 */
	recoveryWeight?: number | null
	/**
	 * 备注
	 */
	remark?: null | string
	/**
	 * 所属公司_view
	 */
	sysCommunityId?: number | null
	/**
	 * 申请部门_view
	 */
	sysOrgId?: number | null
	/**
	 * 废旧物资类型-数据字典-废旧物资类型 直接返回value 0 1 2
	 * 0: A类（金属性材料价值较高）
	 * 1: B类（列入国家危险品目录）
	 * 2: C类（除A/B类以外）
	 * 废旧物资分类
	 */
	wasteMaterialType?: null | string
	[property: string]: any
}

/**
 * 报废申请-扩展信息-物资明细 分页 入参
 */
export interface MatWasteScrapApplyItemVoQuery {
	currentPage?: number
	pageSize?: number
	scrapId?: number
	sidx?: string
	sord?: string
	[property: string]: any
}

/**
 * 报废申请-扩展信息-物资明细 分页 ->返回数据
 *
 * MatWasteScrapApplyItemVo
 */
export interface MatWasteScrapApplyItemVo {
	/**
	 * 报废数量
	 */
	completeNum?: number | null
	/**
	 * 物资编码
	 */
	materialCode?: null | string
	/**
	 * 物资ID
	 */
	materialId?: number | null
	/**
	 * 物资名称
	 */
	materialLabel?: null | string
	/**
	 * 物资分类编码
	 */
	materialTypeCode?: null | string
	/**
	 * 物资分类名称
	 */
	materialTypeLabel?: null | string
	/**
	 * 可报废数量
	 */
	num?: number | null
	/**
	 * 主要材质
	 */
	quality?: null | string
	/**
	 * 预估回收重量
	 */
	recoveryWeight?: number | null
	/**
	 * 报废申请单号
	 */
	scrapId?: number | null
	/**
	 * 存放仓库数量
	 */
	storeIdCnt?: number | null
	/**
	 * 存放仓库(逗号分割的id字符串)
	 */
	storeIds?: null | string
	/**
	 * 库存单位
	 */
	useUnit?: null | string
	/**
	 * 规格型号
	 */
	version?: null | string
	/**
	 * 废旧物资类型-数据字典-废旧物资类型 直接返回value 0 1 2
	 * 0: A类（金属性材料价值较高）
	 * 1: B类（列入国家危险品目录）
	 * 2: C类（除A/B类以外）
	 * 废旧物资分类
	 */
	wasteMaterialType?: null | string
	[property: string]: any
}

/**
 * 报废申请-扩展信息-物资明细-添加物资 分页 入参
 *
 * WasteMaterialStoreVoQuery
 */

export interface WasteMaterialStoreVoQuery {
	/**
	 * 物资编码
	 */
	code?: string
	currentPage?: number
	/**
	 * 物资名称
	 */
	label?: string
	pageSize?: number
	sidx?: string
	sord?: string
	/**
	 * 仓库类型: 传默认值=1 (1.废旧库)
	 */
	storeType?: string
	/**
	 * 公司id
	 */
	sysCommunityId?: number
	/**
	 * 废旧物资分类,值=0 1 2 (0:A类 1:B类 2:C类)
	 */
	wasteMaterialType?: string
	[property: string]: any
}

/**
 * 报废申请-扩展信息-物资明细-添加物资 分页 返回值
 *
 * WasteMaterialStoreVo
 */
export interface WasteMaterialStoreVo {
	/**
	 * 物资编码
	 */
	materialCode?: null | string
	/**
	 * 物资ID
	 */
	materialId?: number | null
	/**
	 * 物资名称
	 */
	materialLabel?: null | string
	/**
	 * 物资分类编码
	 */
	materialTypeCode?: null | string
	/**
	 * 物资分类ID
	 */
	materialTypeId?: number | null
	/**
	 * 物资分类名称
	 */
	materialTypeLabel?: null | string
	/**
	 * 主要材质_view
	 */
	quality?: null | string
	/**
	 * 预估回收重量,!!!说明:显示值需要 * 可报废数量!!!
	 */
	recoveryWeight?: number | null
	/**
	 * 可报废数量 = mat_store_material_store.store_num - mat_store_material_store.freeze_num
	 * 可报废数量
	 */
	completeNum?: number | null
	/**
	 * 存放仓库
	 */
	storeId?: number | null
	/**
	 * 仓库名称
	 */
	storeLabel?: null | string
	/**
	 * 库存单位_view
	 */
	useUnit?: null | string
	/**
	 * 规格型号
	 */
	version?: null | string
	/**
	 * 废旧物资分类,值=0 1 2 (0:A类 1:B类 2:C类)
	 */
	wasteMaterialType?: null | string
	[property: string]: any
}

/**
 * 报废申请-扩展信息-物资明细-批量移除物资
 * MatWasteScrapApplyItemBatchDelDTO
 */
export interface MatWasteScrapApplyItemBatchDelDTO {
	/**
	 * 物资IDList
	 */
	materialIdList?: number[] | null
	/**
	 * 报废申请主键
	 */
	scrapId?: number | null
	[property: string]: any
}

/**
 * 报废申请-扩展信息-仓库明细 返回参数
 *
 * WasteStoreVo
 */
export interface WasteStoreVo {
	/**
	 * 所属段区_view
	 */
	depotId?: number | null
	/**
	 * 物资编码数量
	 */
	materialIdCnt?: number | null
	/**
	 * 物资ids(逗号分割的id字符串)
	 */
	materialIds?: null | string
	/**
	 * 物资分类数量
	 */
	materialTypeCnt?: number | null
	/**
	 * 仓库位置
	 */
	positionId?: null | string
	/**
	 * 预估回收总重量
	 */
	recoveryWeight?: number | null
	/**
	 * 可报废数量 = mat_store_material_store.store_num - mat_store_material_store.freeze_num
	 * 可报废数量
	 */
	completeNum?: number | null
	/**
	 * 仓库编码
	 */
	storeCode?: null | string
	/**
	 * 存放仓库
	 */
	storeId?: number | null
	/**
	 * 仓库名称
	 */
	storeLabel?: null | string
	/**
	 * 库管员
	 */
	storeManage?: null | string
	[property: string]: any
}

/**
 * 报废申请-扩展信息-物资明细 存放仓库 入参
 *
 * CkWasteStoreMaterialVoQuery
 */
export interface CkWasteStoreMaterialVoQuery {
	currentPage?: number
	materialId: number
	pageSize?: number
	scrapId: number
	sidx?: string
	sord?: string
	[property: string]: any
}

/**
 * 报废申请-扩展信息-物资明细 存放仓库 返回参数
 *
 * CkWasteStoreMaterialVo
 */
export interface CkWasteStoreMaterialVo {
	/**
	 * 可报废数量 = mat_store_material_store.store_num - mat_store_material_store.freeze_num
	 * 可报废数量
	 */
	completeNum?: number | null
	/**
	 * 所属段区_view
	 */
	depotId?: number | null
	/**
	 * 库存数量 = mat_store_material_store.store_num
	 * 库存数量
	 */
	num?: number | null
	/**
	 * 仓库位置
	 */
	positionId?: null | string
	/**
	 * 仓库编码
	 */
	storeCode?: null | string
	/**
	 * 仓库主键
	 */
	storeId?: number | null
	/**
	 * 仓库名称
	 */
	storeLabel?: null | string
	/**
	 * 库管员
	 */
	storeManage?: null | string
	[property: string]: any
}

/**
 * 报废申请-扩展信息-物资明细-添加物资 参数
 */
export interface MatWasteScrapApplyItemDTOQuery {
	/**
	 * 添加的明细集合
	 */
	dtoList?: MatWasteScrapApplyItemDTO[] | null
	/**
	 * 报废申请主键
	 */
	scrapId?: number | null
	[property: string]: any
}

/**
 * 报废申请-扩展信息-物资明细-添加物资 参数:dtoList  MatWasteScrapApplyItemDTO
 *
 * MatWasteScrapApplyItemDTO
 */
export interface MatWasteScrapApplyItemDTO {
	/**
	 * 可报废数量
	 */
	completeNum?: number | null
	/**
	 * 创建人
	 */
	createdBy?: null | string
	/**
	 * 创建时间
	 */
	createdDate?: null | string
	/**
	 * 主键
	 */
	id?: number | null
	/**
	 * 修改人
	 */
	lastModifiedBy?: null | string
	/**
	 * 修改时间
	 */
	lastModifiedDate?: null | string
	/**
	 * 物资ID
	 */
	materialId?: number | null
	/**
	 * 库存数量
	 */
	num?: number | null
	/**
	 * 报废申请主键
	 */
	scrapId?: number | null
	/**
	 * 仓库ID
	 */
	storeId?: null | string
	/**
	 * 所属公司
	 */
	sysCommunityId?: number | null
	[property: string]: any
}
