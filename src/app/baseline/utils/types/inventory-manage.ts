/**
 * @description 盘点管理 类型声明
 */

/**
 * 枚举：差异复盘机会
 */
export enum IInventoryJobHasDiffChance {
	/**
	 * 有差异复盘机会
	 */
	have = "0",

	/**
	 * 无差异复盘机会
	 */
	none = "1"
}

/**
 * 枚举：是否初盘
 */
export enum IInventoryJobIsFirst {
	/**
	 * 未初盘
	 */
	notYet = "0",

	/**
	 * 已初盘
	 */
	already = "1"
}

/**
 * 枚举：是否复盘
 */
export enum IInventoryJobIsSecond {
	/**
	 * 未复盘
	 */
	notYet = "0",

	/**
	 * 已复盘
	 */
	already = "1"
}

/**
 * 枚举：盘点类型
 */
export enum IInventoryType {
	/**
	 * 全面盘点
	 */
	all = "0",

	/**
	 * 自动盘点
	 */
	part = "1",

	/**
	 * 手动盘点
	 */
	special = "2",

	/**
	 * 动态盘点
	 */
	trends = "3",

	/**
	 * 循环盘点
	 */
	circulate = "4"
}

/**
 * 盘点计划状态
 */
export enum IInventoryPlanStatus {
	/**
	 * 未开始
	 */
	notStart = 0,

	/**
	 * 盘点中
	 */
	progress = 1,

	/**
	 * 已完成
	 */
	completed = 2
}

/**
 * 盘点任务阶段
 */
export enum IInventoryTaskStatus {
	/**
	 * 初盘
	 */
	firstTask = 0,

	/**
	 * 复核
	 */
	secondTask = 1,

	/**
	 * 已完成
	 */
	completed = 2
}

/**
 * 盘点任务中 盘点人员 盘点状态
 */
export enum IInventoryTaskCheckStatus {
	/**
	 * 未开始
	 */
	notStart = 1,

	/**
	 * 盘点中
	 */
	progress = 2,

	/**
	 * 盘点完成
	 */
	completed = 3
}
/**
 * 枚举：盘点状态
 */
export enum IInventoryStatus {
	/**
	 * 盘点中
	 */
	progress = 1,

	/**
	 * 未开始
	 */
	notStart = 0,

	/**
	 * 已完成
	 */
	completed = 2,

	/**
	 * 差异处理中
	 */
	error = 3
}

/**
 * 枚举：财务过账状态
 */
export enum IFinancialPostStatus {
	/**
	 * 未过账
	 */
	unPost = "0",

	/**
	 * 已过帐
	 */
	posted = "1"
}

/**
 * 枚举：盘点计划结果
 */
export enum IInventoryPlanResultStatus {
	/**
	 * 异常
	 */
	danger = 1,

	/**
	 * 正常
	 */
	normal = 0
}

/**
 * 枚举：盘点结果
 */
export enum IInventoryResultType {
	/**
	 * 盘盈
	 */
	profitable = 2,

	/**
	 * 盘亏
	 */
	unprofitable = 1,

	/**
	 * 正常
	 */
	normal = 0
}

/**
 * 枚举：盘点人类型
 */

export enum IInventoryUserType {
	/**
	 * 初盘
	 */
	first = "1",

	/**
	 * 复盘
	 */
	second = "2"
}

/**
 * 盘点分页 query params
 */
export interface MatStoreCheckPlanVoQueryParams {
	/**
	 * 开始时间
	 */
	beginDate?: string
	/**
	 * 审批状态(0: 待提交, 1: 审批中, 2: 已审批, 3: 已驳回)
	 */
	bpmStatus?: number
	/**
	 * 编码
	 */
	code?: string
	/**
	 * 创建人帐号
	 */
	createdBy?: string
	/**
	 * 创建时间
	 */
	createdDate?: string
	currentPage?: number
	/**
	 * 抽盘比例
	 */
	drawRatio?: string
	/**
	 * 结束时间
	 */
	endDate?: string
	/**
	 * 主键
	 */
	id?: number
	/**
	 * 更新人帐号
	 */
	lastModifiedBy?: string
	/**
	 * 更新时间
	 */
	lastModifiedDate?: string
	/**
	 * 方式(0: 静态盘点, 1: 动态盘点)
	 */
	mode?: number
	/**
	 * 名称
	 */
	name?: string
	pageSize?: number
	/**
	 * 说明
	 */
	remark?: string
	sidx?: string
	sord?: string
	/**
	 * 状态(0: 未启动, 1: 盘点中, 2: 已完成)
	 */
	status?: number
	/**
	 * 类型
	 */
	type?: number
	[property: string]: any
}

/**
 * 盘点计划 vo
 *
 * MatStoreCheckPlanVo
 */
export interface MatStoreCheckPlanVo {
	/**
	 * 申请人
	 */
	applyBy?: null | string
	/**
	 * 开始时间
	 */
	beginDate?: Instant
	/**
	 * 审批状态(0: 待提交, 1: 审批中, 2: 已审批, 3: 已驳回)
	 */
	bpmStatus?: number | null
	/**
	 * 盘点进度
	 */
	checkProcessRate?: number | null
	/**
	 * 编码
	 */
	code?: null | string
	/**
	 * 抽盘比例
	 */
	drawRatio?: number | null
	/**
	 * 结束时间
	 */
	endDate?: Instant
	/**
	 * 主键
	 */
	id?: number | null
	/**
	 * 盘亏数量
	 */
	lossesNum?: number | null
	/**
	 * 盘点物料编码数量
	 */
	materialCodeNum?: number | null
	/**
	 * 方式
	 */
	mode?: number | null
	/**
	 * 名称
	 */
	name?: null | string
	/**
	 * 盘盈数量
	 */
	profitNum?: number | null
	/**
	 * 说明
	 */
	remark?: null | string
	/**
	 * 状态(0: 未启动, 1: 盘点中, 2: 已完成)
	 */
	status?: number | null
	/**
	 * 仓库数量
	 */
	storeNum?: number | null
	/**
	 * 类型
	 */
	type?: number | null
	[property: string]: any
}
/**
 * 开始时间
 *
 * Instant
 *
 * 结束时间
 */
export interface Instant {
	/**
	 * The number of nanoseconds, later along the time-line, from the seconds field.
	 * This is always positive, and never exceeds 999,999,999.
	 */
	nanos?: number | null
	/**
	 * The number of seconds from the epoch of 1970-01-01T00:00:00Z.
	 */
	seconds?: number | null
	[property: string]: any
}

/**
 * 盘点计划 dto
 *
 * MatStoreCheckPlanDTO
 */
export interface MatStoreCheckPlanDTO {
	/**
	 * 开始时间
	 */
	beginDate?: null | string
	/**
	 * 审批状态(0: 待提交, 1: 审批中, 2: 已审批, 3: 已驳回)
	 */
	bpmStatus?: number | null
	/**
	 * 编码
	 */
	code?: null | string
	/**
	 * 创建人帐号
	 */
	createdBy?: null | string
	/**
	 * 创建时间
	 */
	createdDate?: null | string
	/**
	 * 抽盘比例
	 */
	drawRatio?: number | null
	/**
	 * 结束时间
	 */
	endDate?: null | string
	/**
	 * 主键
	 */
	id?: number | null
	/**
	 * 更新人帐号
	 */
	lastModifiedBy?: null | string
	/**
	 * 更新时间
	 */
	lastModifiedDate?: null | string
	/**
	 * 方式(0: 静态盘点, 1: 动态盘点)
	 */
	mode?: number | null
	/**
	 * 名称
	 */
	name?: null | string
	/**
	 * 说明
	 */
	remark?: null | string
	/**
	 * 状态(0: 未启动, 1: 盘点中, 2: 已完成)
	 */
	status?: number | null
	/**
	 * 类型
	 */
	type?: number | null
	[property: string]: any
}

/**
 * 盘点任务
 *
 * MatStoreCheckPlanTaskDTO
 */
export interface MatStoreCheckPlanTaskDTO {
	/**
	 * 开始时间
	 */
	beginDate?: null | string
	/**
	 * 盘点计划id
	 */
	checkPlanId?: number | null
	/**
	 * 编码
	 */
	code?: null | string
	/**
	 * 结束时间
	 */
	endDate?: null | string
	/**
	 * 财务过账状态(0: 未过账, 1: 已过账)
	 */
	financialPostingStatus?: { [key: string]: any } | null
	/**
	 * 初盘人员id
	 */
	firstCheckUserId?: number | null
	/**
	 * 初盘人员名称
	 */
	firstCheckUserName?: null | string
	/**
	 * 主键
	 */
	id?: number | null
	/**
	 * 复盘人员id
	 */
	secondCheckUserId?: number | null
	/**
	 * 复盘人员名称
	 */
	secondCheckUserName?: null | string
	/**
	 * 状态(0: 未启动, 1: 盘点中, 2: 已完成)
	 */
	status?: number | null
	/**
	 * 仓库编码
	 */
	storeCode?: null | string
	/**
	 * 仓库id
	 */
	storeId?: number | null
	/**
	 * 仓库名称
	 */
	storeName?: null | string
	[property: string]: any
}

/**
 * 仓库所有物资 查询参数
 */
export interface MatStoreCheckPlanAllMaterialQueryParams {
	currentPage?: number
	pageSize?: number
	sidx?: string
	sord?: string
	/**
	 * 仓库id列表(逗号分隔)
	 */
	taskId: number
	[property: string]: any
}

/**
 * 盘点任务编辑物料分页 查询参数
 */
export interface MatStoreCheckPlanEditMaterialQueryParams {
	currentPage?: number
	pageSize?: number
	sidx?: string
	sord?: string
	/**
	 * 仓库id列表(逗号分隔)
	 */
	planTaskId: number
	[property: string]: any
}

/**
 * MatStoreCheckPlanMaterialVo
 */
export interface MatStoreCheckPlanMaterialVo {
	/**
	 * 货位库存物资id
	 */
	id?: number | null
	/**
	 * 物料编码
	 */
	materialCode?: null | string
	/**
	 * 物料id
	 */
	materialId?: number | null
	/**
	 * 物料名称
	 */
	materialName?: null | string
	/**
	 * 规格型号
	 */
	materialVersion?: null | string
	/**
	 * 货位编码
	 */
	roomCode?: null | string
	/**
	 * 货位id
	 */
	roomId?: number | null
	/**
	 * 仓库id
	 */
	storeId?: number | null
	/**
	 * 库存数量
	 */
	storeNum?: number | null
	/**
	 * 技术参数
	 */
	technicalParameter?: null | string
	/**
	 * 库存单位
	 */
	useUnit?: null | string
	[property: string]: any
}

/**
 * 盘点仓库物资分页 查询参数
 */
export interface MatStoreCheckPlanMaterialQueryParams {
	/**
	 * 开始时间
	 */
	beginDate?: string
	/**
	 * 盘点计划id
	 */
	checkPlanId?: number
	/**
	 * 编码
	 */
	code?: string
	currentPage?: number
	/**
	 * 结束时间
	 */
	endDate?: string
	/**
	 * 财务过账状态(0: 未过账, 1: 已过账)
	 */
	financialPostingStatus?: string
	/**
	 * 初盘人员id
	 */
	firstCheckUserId?: number
	/**
	 * 初盘人员名称
	 */
	firstCheckUserName?: string
	/**
	 * 主键
	 */
	id?: number
	pageSize?: number
	/**
	 * 复盘人员id
	 */
	secondCheckUserId?: number
	/**
	 * 复盘人员名称
	 */
	secondCheckUserName?: string
	sidx?: string
	sord?: string
	/**
	 * 状态(0: 未启动, 1: 盘点中, 2: 已完成)
	 */
	status?: number
	/**
	 * 仓库编码
	 */
	storeCode?: string
	/**
	 * 仓库id
	 */
	storeId?: number
	/**
	 * 仓库名称
	 */
	storeName?: string
	[property: string]: any
}

/**
 * 更新盘点物料DTO
 *
 * MatStoreCheckMaterialUpdateDTO
 */
export interface MatStoreCheckMaterialUpdateDTO {
	/**
	 * 盘点计划id
	 */
	checkPlanId?: number | null
	/**
	 * 盘点任务id
	 */
	checkPlanTaskId?: number | null
	/**
	 * 物料id
	 * 物料id列表
	 */
	materialIds?: number[] | null
	/**
	 * 仓库id
	 */
	storeId?: number | null
	[property: string]: any
}

/**
 * 更新盘点人员内容
 *
 * MatStoreCheckPlanTaskUserUpdateDTO
 */
export interface MatStoreCheckPlanTaskUserUpdateDTO {
	/**
	 * 初盘人员姓名
	 */
	firstCheckRealName?: null | string
	/**
	 * 初盘人员账号
	 */
	firstCheckUserName?: null | string
	/**
	 * 复盘人员姓名
	 */
	secondCheckRealName?: null | string
	/**
	 * 复盘人员账号
	 */
	secondCheckUserName?: null | string
	/**
	 * 盘点任务id
	 */
	taskIdList?: string[] | null
	[property: string]: any
}

/**
 * 盘点计划 仓库明细分页 参数
 */
export interface MatStoreCheckPlanTaskPageReqParams {
	currentPage?: number
	pageSize?: number
	/**
	 * 盘点计划id
	 */
	planId: number
	sidx?: string
	sord?: string
	[property: string]: any
}

/**
 * MatStoreCheckPlanTaskPageVo
 */
export interface MatStoreCheckPlanTaskPageVo {
	/**
	 * 计划id
	 */
	checkPlanId?: number | null
	/**
	 * 编码
	 */
	code?: null | string
	/**
	 * 初盘人员姓名
	 */
	firstCheckRealName?: null | string
	/**
	 * 初盘人员账号
	 */
	firstCheckUserName?: null | string
	/**
	 * 主键
	 */
	id?: number | null
	/**
	 * 复盘人员姓名
	 */
	secondCheckRealName?: null | string
	/**
	 * 复盘人员账号
	 */
	secondCheckUserName?: null | string
	/**
	 * 仓库编码
	 */
	storeCode?: null | string
	/**
	 * 成本中心id
	 */
	storeCostCenterId?: number | null
	/**
	 * 成本中心名称
	 */
	storeCostCenterName?: null | string
	/**
	 * 仓库区段id
	 * 区段id
	 */
	storeDepotId?: number | null
	/**
	 * 仓库区段名称
	 */
	storeDepotName?: null | string
	/**
	 * 仓库id
	 */
	storeId?: number | null
	/**
	 * 仓库级别
	 */
	storeLevel?: null | string
	/**
	 * 仓库线路id
	 */
	storeLineId?: null | string
	/**
	 * 仓库线路名称
	 */
	storeLineName?: null | string
	/**
	 * 仓库名称
	 */
	storeName?: null | string
	/**
	 * 仓库类型
	 */
	storeType?: null | string
	[property: string]: any
}

/**
 * 新增计划任务 参数
 */
export interface MatStoreCheckPlanTaskAddStoreListParams {
	/**
	 * 物资数量
	 */
	materialCodeNum?: number | null
	/**
	 * 仓库id
	 */
	storeId: number
	[property: string]: any
}
/**
 * 新增盘点物料DTO列表
 *
 * 更新盘点物料DTO
 *
 * MatStoreCheckMaterialAddDTO
 */
export interface MatStoreCheckMaterialAddDTO {
	/**
	 * 物料id
	 */
	materialIdList: number[]
	/**
	 * 盘点任务id
	 */
	planTaskId: number
	[property: string]: any
}

export interface MatStorePlanTaskResultQueryParams {
	/**
	 * 盘点类型(0: 全面盘点, 1: 自动抽盘, 2: 手动抽盘)
	 */
	checkType?: number
	currentPage?: number
	/**
	 * 初盘人姓名
	 */
	firstCheckPersonName?: string
	pageSize?: number
	/**
	 * 盘点计划编号
	 */
	planCode?: string
	/**
	 * 盘点计划名称
	 */
	planName?: string
	/**
	 * 复盘人姓名
	 */
	secondCheckPersonName?: string
	sidx?: string
	sord?: string
	/**
	 * 仓库编号
	 */
	storeCode?: string
	/**
	 * 仓库id
	 */
	storeId?: number
	/**
	 * 盘点任务编号
	 */
	taskCode?: string
	/**
	 * 盘点任务状态(0: 未开始, 1: 盘点中, 2: 已完成)
	 */
	taskStatus?: number
	[property: string]: any
}

/**
 * 盘点任务vo
 *
 * MatStoreCheckPlanTaskPageSearchResultVo
 */
export interface MatStoreCheckPlanTaskPageSearchResultVo {
	/**
	 * 任务开始时间
	 */
	beginDate?: null | string
	/**
	 * 是否差异复盘(0: 未差异复盘, 1: 已差异复盘)
	 */
	checkDiffIs?: null | string
	/**
	 * 计划编码
	 */
	checkPlanCode?: null | string
	/**
	 * 计划id
	 */
	checkPlanId?: number | null
	/**
	 * 计划名称
	 */
	checkPlanName?: null | string
	/**
	 * 盘点结果(0:正常, 1:盘亏, 2:盘盈)
	 */
	checkResult?: number | null
	/**
	 * 编码
	 */
	code?: null | string
	/**
	 * 任务结束时间
	 */
	endDate?: null | string
	/**
	 * 财务过账状态(0: 未过账, 1: 已过账)
	 */
	financialPostingStatus?: { [key: string]: any } | null
	/**
	 * 财务过账状态
	 */
	financialPostingStatusLabel?: null | string
	/**
	 * 是否初盘(0: 未初盘, 1: 已初盘)
	 */
	firstCheckIs?: null | string
	/**
	 * 初盘人员姓名
	 */
	firstCheckRealName?: null | string
	/**
	 * 初盘人员账号
	 */
	firstCheckUserName?: null | string
	/**
	 * 主键
	 */
	id?: number | null
	/**
	 * 更新时间
	 */
	lastModifiedDate?: null | string
	/**
	 * 盘亏数量
	 */
	lossesNum?: number | null
	/**
	 * 盘点物料编码数量
	 */
	materialCodeCount?: number | null
	/**
	 * 盘点开始时间
	 */
	planBeginDate?: null | string
	/**
	 * 盘点结束时间
	 */
	planEndDate?: null | string
	/**
	 * 盘点类型
	 */
	planType?: number | null
	/**
	 * 盘点类型名称
	 */
	planTypeLabel?: number | null
	/**
	 * 盘盈数量
	 */
	profitNum?: number | null
	/**
	 * 是否复盘(0: 未复盘, 1: 已复盘)
	 */
	secondCheckIs?: null | string
	/**
	 * 复盘人员姓名
	 */
	secondCheckRealName?: null | string
	/**
	 * 复盘人员账号
	 */
	secondCheckUserName?: null | string
	/**
	 * 任务状态(0: 未启动, 1: 盘点中, 2: 已完成)
	 * 状态(0: 未启动, 1: 盘点中, 2: 已完成)
	 */
	status?: number | null
	/**
	 * 仓库编码
	 */
	storeCode?: null | string
	/**
	 * 仓库id
	 */
	storeId?: number | null
	/**
	 * 仓库名称
	 */
	storeName?: null | string
	/**
	 * 任务盘点进度
	 */
	taskProcessRate?: number | null
	[property: string]: any
}

export interface MatStoreCheckPlanTaskMaterialDetailPageQueryParams {
	currentPage?: number
	pageSize?: number
	sidx?: string
	sord?: string
	/**
	 * 盘点任务id
	 */
	taskId: number
	[property: string]: any
}

/**
 * 物料明细分页 vo
 * MatStoreCheckPlanTaskMaterialDetailPageVo
 */
export interface MatStoreCheckPlanTaskMaterialDetailPageVo {
	/**
	 * 差异数量
	 */
	checkDiffNum?: number | null
	/**
	 * 盘点结果(0: 正常, 1: 盘亏, 2: 盘盈)
	 */
	checkResult?: number | null
	/**
	 * 初盘数量
	 */
	firstCheckNum?: number | null
	/**
	 * 初盘状态(0: 异常, 1: 正常)
	 */
	firstCheckStatus?: number | null
	/**
	 * 盘点物料id
	 */
	id?: number | null
	/**
	 * 物料编码
	 */
	materialCode?: null | string
	/**
	 * 物料id
	 */
	materialId?: number | null
	/**
	 * 物料名称
	 */
	materialName?: null | string
	/**
	 * 规格型号
	 */
	materialVersion?: null | string
	/**
	 * 情况说明
	 */
	remark?: null | string
	/**
	 * 货位编码
	 */
	roomCode?: null | string
	/**
	 * 货位id
	 */
	roomId?: number | null
	/**
	 * 复盘数量
	 */
	secondCheckNum?: number | null
	/**
	 * 仓库id
	 */
	storeId?: number | null
	/**
	 * 库存数量
	 */
	storeNum?: number | null
	/**
	 * 技术参数
	 */
	technicalParameter?: null | string
	/**
	 * 库存单位
	 */
	useUnit?: number | null
	[property: string]: any
}
/**
 * 盘点任务详情 vo
 *
 * MatStoreCheckPlanTaskDetailVo
 */
export interface MatStoreCheckPlanTaskDetailVo {
	/**
	 * 开始时间
	 */
	beginDate?: null | string
	/**
	 * 是否差异复盘(0: 未差异复盘, 1: 已差异复盘)
	 */
	checkDiffIs?: string
	/**
	 * 计划编码
	 */
	checkPlanCode?: null | string
	/**
	 * 计划id
	 */
	checkPlanId?: number | null
	/**
	 * 计划名称
	 */
	checkPlanName?: null | string
	/**
	 * 盘点结果(0:正常, 1:盘亏, 2:盘盈)
	 */
	checkResult?: number | null
	/**
	 * 盘点结果名称
	 */
	checkResultLabel?: null | string
	/**
	 * 编码
	 */
	code?: null | string
	/**
	 * 结束时间
	 */
	endDate?: null | string
	/**
	 * 异常物料id列表
	 */
	exceptionMaterialIds?: number[] | null
	/**
	 * 财务过账状态(0: 未过账, 1: 已过账)
	 */
	financialPostingStatus?: string
	/**
	 * 财务过账状态
	 */
	financialPostingStatusLabel?: null | string
	/**
	 * 是否初盘(0: 未初盘, 1: 已初盘)
	 * 是否差异复盘(0: 未差异复盘, 1: 已差异复盘)
	 */
	firstCheckIs?: string
	/**
	 * 初盘人员姓名
	 */
	firstCheckRealName?: null | string
	/**
	 * 初盘人员账号
	 */
	firstCheckUserName?: null | string
	/**
	 * 主键
	 */
	id?: number | null
	/**
	 * 更新时间
	 */
	lastModifiedDate?: null | string
	/**
	 * 盘点物料编码数量
	 */
	materialCodeCount?: number | null
	/**
	 * 盘点类型
	 */
	planType?: number | null
	/**
	 * 盘点类型名称
	 */
	planTypeLabel?: number | null
	/**
	 * 是否复盘(0: 未复盘, 1: 已复盘)
	 * 是否差异复盘(0: 未差异复盘, 1: 已差异复盘)
	 */
	secondCheckIs?: string
	/**
	 * 复盘人员姓名
	 */
	secondCheckRealName?: null | string
	/**
	 * 复盘人员账号
	 */
	secondCheckUserName?: null | string
	/**
	 * 任务状态(0: 未启动, 1: 盘点中, 2: 已完成)
	 * 状态(0: 未启动, 1: 盘点中, 2: 已完成)
	 */
	status?: number | null
	/**
	 * 任务状态名称
	 */
	statusLabel?: null | string
	/**
	 * 仓库编码
	 */
	storeCode?: null | string
	/**
	 * 仓库id
	 */
	storeId?: number | null
	/**
	 * 仓库名称
	 */
	storeName?: null | string
	/**
	 * 任务盘点进度
	 */
	taskProcessRate?: number | null
	[property: string]: any
}

/**
 * 盘点任务 物资更新 dto
 *
 * MatStoreCheckMaterialUpdateDTO
 */
export interface MatStoreInventoryJobCheckMaterialUpdateDTO {
	/**
	 * 盘点任务id
	 */
	checkPlanTaskId: number | null
	/**
	 * 物料明细列表
	 */
	items: MatStoreCheckMaterialUpdateItemDTO[] | null
	[property: string]: any
}

/**
 * MatStoreCheckMaterialUpdateItemDTO
 */
export interface MatStoreCheckMaterialUpdateItemDTO {
	/**
	 * 初盘数量
	 */
	firstCheckNum?: number | null
	/**
	 * 主键
	 */
	id: number | null
	/**
	 * 说明
	 */
	remark?: null | string
	/**
	 * 复盘数量
	 */
	secondCheckNum?: number | null
	[property: string]: any
}

/**
 * Result«List«MatStoreCheckPlanDetaiAddlVo»»
 */
export interface MatStoreCheckPlanDetaiAddlVoResponse {
	/**
	 * 状态码
	 */
	code?: null | string
	/**
	 * 返回数据
	 */
	data?: MatStoreCheckPlanDetaiAddlVo[] | null
	/**
	 * 接口描述
	 */
	msg?: null | string
	[property: string]: any
}

/**
 * 仓库id列表
 *
 * MatStoreCheckPlanDetaiAddlVo
 */
export interface MatStoreCheckPlanDetaiAddlVo {
	/**
	 * 物资数量
	 */
	materialCodeNum?: number | null
	/**
	 * 最大物资数量
	 */
	maxMaterialCodeNum: number
	/**
	 * 仓库id
	 */
	storeId?: number | null
	/**
	 * 仓库名称
	 */
	storeName?: null | string
	[property: string]: any
}
