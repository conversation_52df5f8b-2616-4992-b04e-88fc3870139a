/**
 * 报表-分公司报表 类型声明
 */

/**
 * 报表-分公司报表 列表 入参
 */
export interface StatementVORequest {
	label?: string
	sysCommunityId?: number
}

/**
 * 报表-分公司报表 列表 返参
 * StatementVO
 */
export interface Datum {
	chirdren: StatementVO[]
	reportTreeCode: string
	reportTreeName: string
	[property: string]: any
}

export interface StatementVO {
	/**
	 * 附件ID
	 */
	attachmentId: string
	/**
	 * 分类编码
	 */
	categoryCode: string
	/**
	 * 报表描述
	 */
	description: string
	/**
	 * ID
	 */
	id: number
	/**
	 * 报表名称
	 */
	label: string
	/**
	 * 排序
	 */
	sortedBy: number
	/**
	 * 公司ID
	 */
	sysCommunityId: number
	/**
	 * 报表地址
	 */
	url: string
	[property: string]: any
}
