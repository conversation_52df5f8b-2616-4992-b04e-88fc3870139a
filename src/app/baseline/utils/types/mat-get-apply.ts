/**
 * @description 领料申请 类型声明
 */

/**
 * MatOutStorePickApplyVo
 */
export interface MatOutStorePickApplyVo {
	/**
	 * 领料金额（元）
	 */
	amount?: number | null
	/**
	 * 审批状态
	 */
	bpmStatus?: null | string
	/**
	 * 领料单号
	 */
	code?: null | string
	/**
	 * 申请时间
	 */
	createdDate?: null | string
	/**
	 * 段区ID
	 * 段区ID （字典）
	 */
	depotId?: number | null
	/**
	 * 费用类别
	 * 费用类别(字典)
	 */
	expenseCategory?: null | string
	/**
	 * ID
	 */
	id?: number | null
	/**
	 * 领料单名称
	 */
	label?: null | string
	/**
	 * 线路ID
	 * 线路ID(字典)
	 */
	lineNoId?: string | null
	/**
	 * 专业ID
	 * 专业ID（字典）
	 */
	majorId?: number | null
	/**
	 * 物资编码数量
	 */
	materialCodeNum?: number | null
	/**
	 * 部门ID(字典)
	 */
	pickSysOrgId?: number | null
	/**
	 * 领料人Id(字典)
	 */
	pickUserId?: number | null
	/**
	 * 领料用途
	 */
	purpose?: number | null
	/**
	 * 申请原因
	 */
	reason?: null | string
	/**
	 * 仓库ID
	 */
	storeId?: number | null
	/**
	 * 出库仓库
	 */
	storeLabel?: number | null
	/**
	 * 关联工单编号
	 */
	workOrderCode?: number | null
	/**
	 * 关联工单Id
	 */
	workOrderId?: number | null
	[property: string]: any
}

/**
 * MatOutStorePickApplyDTO
 */
export interface MatOutStorePickApplyDTO {
	/**
	 * 审批状态
	 */
	bpmStatus?: null | string
	/**
	 * 领料单号
	 */
	code?: null | string
	/**
	 * 段区ID
	 */
	depotId?: number | null
	/**
	 * 费用类别
	 */
	expenseCategory?: null | string
	/**
	 * ID
	 */
	id?: number | null
	/**
	 * 领料单名称
	 */
	label?: null | string
	/**
	 * 线路ID
	 */
	lineNoId?: number | null
	/**
	 * 专业ID
	 */
	majorId?: number | null
	/**
	 * 领料部门Id
	 */
	pickSysOrgId?: number | null
	/**
	 * 领料人
	 */
	pickUserName?: null | string
	/**
	 * 领料用途
	 */
	purpose?: number | null
	/**
	 * 关联工单号
	 */
	workOrderCode?: number | null
	[property: string]: any
}

/**
 * @description 领料申请明细 vo
 *
 * MatOutStorePickApplyItemVo
 */
export interface MatOutStorePickApplyItemVo {
	/**
	 * 冻结数量
	 */
	freezeNum?: number | null
	/**
	 * ID
	 * id
	 */
	id?: number | null
	/**
	 * 物资编码
	 */
	materialCode?: null | string
	/**
	 * 物资ID
	 * material_id
	 */
	materialId?: number | null
	/**
	 * 编码名称
	 */
	materialName?: null | string
	/**
	 * 领料数量
	 */
	num?: number | null
	/**
	 * 库存数量
	 */
	storeNum?: number | null
	/**
	 * 技术参数
	 */
	technicalParameter?: null | string
	/**
	 * 库存单位
	 */
	useUnit?: number | null
	/**
	 * 规格型号
	 */
	version?: null | string
	[property: string]: any
}

/**
 * @description 领料申请 添加物资 dto
 *
 * MatOutStorePickApplyItemAddDTO
 */
export interface MatOutStorePickApplyItemAddDTO {
	/**
	 * 业务主表id
	 */
	applyId?: number | null
	/**
	 * 物资ID 集合
	 * 物资ID集合
	 */
	materialIdList?: number[] | null
	[property: string]: any
}

/**
 * 领料申请批量编辑物资明细数量 dto
 *
 * MatOutStorePickApplyItemEditDTO
 */
export interface MatOutStorePickApplyItemEditDTO {
	/**
	 * ID
	 * id （编辑）
	 */
	id?: number | null
	/**
	 * 领料数量
	 * 领料数量 （编辑）
	 */
	num?: number | null
	[property: string]: any
}

export interface MatOutStorePickApplyItemDTO {
	applyId?: number
	currentPage?: number
	pageSize?: number
	sidx?: string
	sord?: string
	[property: string]: any
}

/**
 * 领料申请 物资批次 dto
 */
export interface MatOutStorePickApplyItemBatchDTO {
	createdDate?: string
	lastModifiedDate?: string
	createdBy?: string
	lastModifiedBy?: string
	id?: number
	itemId?: number
	currentPage?: number
	pageSize?: number
	[property: string]: any
}

/**
 * 领料出库明细批次DTO
 *
 * MatOutStorePickApplyItemBatchVo
 */
export interface MatOutStorePickApplyItemBatchVo {
	/**
	 * 单价
	 */
	amount?: number | null
	/**
	 * 批次号
	 */
	batchNo?: null | string
	createdBy?: null | string
	createdDate?: null | string
	/**
	 * ID
	 */
	id?: number | null
	/**
	 * 入库时间
	 */
	inStoreTime?: null | string
	/**
	 * 业务明细ID
	 */
	itemId?: number | null
	lastModifiedBy?: null | string
	lastModifiedDate?: null | string
	/**
	 * 领料数量
	 */
	num?: number | null
	/**
	 * 区域名称
	 */
	regionLabel?: null | string
	/**
	 * 货位id
	 */
	roomId?: number | null
	/**
	 * 货位名称
	 */
	roomLabel?: null | string
	/**
	 * 质保期
	 */
	validityPeriod?: null | string
	[property: string]: any
}

export interface CanPickApplyMaterialCodeParams {
	/**
	 * 领料申请id
	 */
	pickApplyId: number
	/**
	 * 申请单id
	 */
	applyId: number
	/**
	 * 申请单类型：交旧申请（CK-JJ-SQ）、退库申请（CK-TK-SQ）
	 */
	type: string
	/**
	 * 物资名称
	 */
	label: string | null

	/**
	 * 物料编码
	 */
	code: string | null
	currentPage?: number
	pageSize?: number
	sidx?: string
	sord?: string
	[property: string]: any
}
/**
 * 可选领料申请明细分页 VO
 */
export interface CanPickApplyMaterialCodeVo {
	/**
	 * 可用数量
	 */
	canNum?: number | null
	/**
	 * 已退库数量
	 */
	completeReturnNum?: number | null
	/**
	 * 已交旧数量
	 */
	completeWasteOldNum?: number | null
	/**
	 * 领料单出库明细id
	 */
	id?: number | null
	/**
	 * 物资编码
	 */
	materialCode?: null | string
	/**
	 * 物资ID
	 * material_id
	 */
	materialId?: number | null
	/**
	 * 编码名称
	 */
	materialName?: null | string
	/**
	 * 领料出库数量
	 */
	num?: number | null
	/**
	 * 主要材质
	 */
	quality?: null | string
	/**
	 * 预估回收重量
	 */
	recoveryWeight?: number | null
	/**
	 * 出库仓库
	 */
	storeLabel?: null | string
	/**
	 * 技术参数
	 */
	technicalParameter?: null | string
	/**
	 * 库存单位
	 */
	useUnit?: number | null
	/**
	 * 规格型号
	 */
	version?: null | string
	/**
	 * 废旧物资分类
	 */
	wasteMaterialType?: null | string
	[property: string]: any
}
