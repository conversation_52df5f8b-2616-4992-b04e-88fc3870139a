/**
 * 废旧台账/报废台账分页 入参
 * ScrapBookPageVo
 */
export interface ScrapBookPageVoQuery {
	/**
	 * 物资编码
	 */
	code?: string
	currentPage?: number
	/**
	 * 物资名称
	 */
	label?: string
	pageSize?: number
	/**
	 * 主要材质
	 */
	quality?: string
	sidx?: string
	sord?: string
	/**
	 * 物资分类id列表(逗号隔开)
	 */
	typeIds?: string
	[property: string]: any
}

/**
 * 废旧台账/报废台账分页 返参
 * ScrapBookPageVo
 */
export interface ScrapBookPageVo {
	/**
	 * 已处置数量
	 */
	completeScrapNum?: number | null
	/**
	 * 已交旧数量
	 */
	completeWasteOldNum?: number | null
	/**
	 * 台账id
	 */
	id?: number | null
	/**
	 * 物资编码
	 */
	materialCode?: null | string
	/**
	 * 物资id
	 */
	materialId?: number | null
	/**
	 * 物资名称
	 */
	materialName?: null | string
	/**
	 * 主要材质
	 */
	quality?: null | string
	/**
	 * 预估回收重量
	 */
	recoveryWeight?: number | null
	/**
	 * 当前库存
	 */
	storeNum?: number | null
	/**
	 * 技术参数
	 */
	technicalParameter?: null | string
	/**
	 * 库存单位
	 */
	useUnit?: null | string
	/**
	 * 规格型号
	 */
	version?: null | string
	/**
	 * 废旧物资分类
	 */
	wasteMaterialType?: null | string
	[property: string]: any
}

/**
 * 报废台账详情 返回数据
 *
 * ScrapBookDetailVo
 */
export interface ScrapBookDetailVo {
	/**
	 * 已处置数量
	 */
	completeScrapNum?: number | null
	/**
	 * 已交旧数量
	 */
	completeWasteOldNum?: number | null
	/**
	 * 台账id
	 */
	id?: number | null
	/**
	 * 物资编码
	 */
	materialCode?: null | string
	/**
	 * 物资id
	 */
	materialId?: number | null
	/**
	 * 物资名称
	 */
	materialName?: null | string
	/**
	 * 主要材质
	 */
	quality?: null | string
	/**
	 * 预估回收重量
	 */
	recoveryWeight?: number | null
	/**
	 * 当前库存
	 */
	storeNum?: number | null
	/**
	 * 技术参数
	 */
	technicalParameter?: null | string
	/**
	 * 库存单位
	 */
	useUnit?: null | string
	/**
	 * 规格型号
	 */
	version?: null | string
	/**
	 * 废旧物资分类
	 */
	wasteMaterialType?: null | string
	/**
	 * 交旧类型-0领料单交旧 1历史物资交旧 2低值易耗交旧
	 */
	wasteOldType?: null | string
	[property: string]: any
}

/**
 * 库存查询 返回数据
 *
 * ScrapBookStorePageVo
 */
export interface ScrapBookStorePageVo {
	/**
	 * 成本中心id
	 */
	costCenterId?: number | null
	/**
	 * 成本中心名称
	 */
	costCenterName?: null | string
	/**
	 * 段区id
	 */
	depotId?: number | null
	/**
	 * 物资id
	 */
	materialId?: number | null
	/**
	 * 库存数量
	 */
	num?: number | null
	/**
	 * 仓库位置
	 */
	positionId?: null | string
	/**
	 * 仓库编码
	 */
	storeCode?: null | string
	/**
	 * 库管员
	 */
	storeManage?: null | string
	/**
	 * 仓库名称
	 */
	storeName?: null | string
	/**
	 * 公司id
	 */
	sysCommunityId?: number | null
	[property: string]: any
}

/**
 * 交旧记录 返回数据
 *
 * ScrapBookWasteOldRecordPageVo
 */
export interface ScrapBookWasteOldRecordPageVo {
	/**
	 * 交旧申请单号
	 */
	code?: null | string
	/**
	 * 申请人姓名
	 */
	createdBy?: null | string
	/**
	 * 申请时间
	 */
	createdDate?: null | string
	/**
	 * 交旧申请id
	 */
	id?: number | null
	/**
	 * 交旧申请名称
	 */
	label?: null | string
	/**
	 * 上级业务单号
	 */
	preBusinessCode?: null | string
	/**
	 * 申请部门
	 */
	sysOrgId?: number | null
	/**
	 * 交旧类型
	 */
	type?: null | string
	/**
	 * 交旧数量
	 */
	wasteOldNum?: number | null
	[property: string]: any
}

/**
 * 报废记录 返回数据
 *
 * ScrapBookScrapRecordPageVo
 */
export interface ScrapBookScrapRecordPageVo {
	/**
	 * 申请单号
	 */
	code?: null | string
	/**
	 * 申请人姓名
	 */
	createdBy?: null | string
	/**
	 * 申请时间
	 */
	createdDate?: null | string
	/**
	 * 报废申请单id
	 */
	id?: number | null
	/**
	 * 报废业务名称
	 */
	label?: null | string
	/**
	 * 报废数量
	 */
	scrapNum?: number | null
	/**
	 * 申请部门
	 */
	sysOrgId?: number | null
	[property: string]: any
}

/**
 * 在库时长 返回数据
 *
 * ScrapBookInStoreAgePageVo
 */
export interface ScrapBookInStoreAgePageVo {
	/**
	 * 库龄
	 */
	age?: number | null
	/**
	 * 批次号
	 */
	batchNo?: null | string
	id?: number | null
	/**
	 * 入库时间
	 */
	inStoreTime?: null | string
	/**
	 * 货位编号
	 */
	roomCode?: null | string
	/**
	 * 货位id
	 */
	roomId?: number | null
	/**
	 * 仓库编码
	 */
	storeCode?: null | string
	/**
	 * 仓库Id
	 */
	storeId?: number | null
	/**
	 * 仓库名称
	 */
	storeLabel?: null | string
	/**
	 * 仓库类型
	 */
	storeType?: null | string
	[property: string]: any
}

/**
 * 明细 入参
 *
 * ScrapBookItemRequest
 */
export interface ScrapBookItemRequest {
	/**
	 * 台账id
	 */
	bookId: number
	currentPage?: number
	pageSize?: number
	sidx?: string
	sord?: string
	[property: string]: any
}

export enum IScrapBookFilterType {
	/**
	 * 按分类
	 */
	category = "1",

	/**
	 * 材质
	 */
	quality = "2"
}
