/**
 * @description 低值 盘点管理 类型声明
 */
export interface MatLowValueCheckPlanPageVoRequest {
	/**
	 * 计划编码
	 */
	code?: string
	currentPage?: number
	/**
	 * 计划名称
	 */
	label?: string
	pageSize?: number
	sidx?: string
	sord?: string
	/**
	 * 计划状态(0:待开始, 1:进行中, 2:已完成)
	 */
	status: string
	[property: string]: any
}

/**
 * 低值盘点计划分页Vo
 *
 * MatLowValueCheckPlanPageVo
 */
export interface MatLowValueCheckPlanPageVo {
	/**
	 * 开始时间
	 */
	beginDate?: null | string
	/**
	 * 审批状态
	 */
	bpmStatus?: null | string
	/**
	 * 编码
	 */
	code?: null | string
	/**
	 * 申请人
	 */
	createdBy?: null | string
	/**
	 * 结束时间
	 */
	endDate?: null | string
	/**
	 * 主键
	 */
	id?: number | null
	/**
	 * 名称
	 */
	label?: null | string
	/**
	 * 更新时间
	 */
	lastModifiedDate?: null | string
	/**
	 * 说明
	 */
	remark?: null | string
	/**
	 * 范围
	 */
	scope?: { [key: string]: any } | null
	/**
	 * 任务状态
	 */
	status?: { [key: string]: any } | null
	/**
	 * 类型
	 */
	type?: { [key: string]: any } | null
	[property: string]: any
}

/**
 * 盘点计划新增 入参
 *
 * MatLowValueCheckPlanAddDTO
 */
export interface MatLowValueCheckPlanAddDTO {
	/**
	 * 开始日期
	 */
	beginDate: null | string
	/**
	 * 结束日期
	 */
	endDate: null | string
	/**
	 * 名称
	 */
	label: null | string
	remark: null | string
	/**
	 * 范围
	 */
	scope: { [key: string]: any } | null
	/**
	 * 类型
	 */
	type: { [key: string]: any } | null
	[property: string]: any
}

/**
 * 盘点计划编辑 入参
 *
 * MatLowValueCheckPlanEditDTO
 */
export interface MatLowValueCheckPlanEditDTO {
	/**
	 * 开始日期
	 */
	beginDate: null | string
	/**
	 * 结束日期
	 */
	endDate: null | string
	/**
	 * id
	 */
	id: number | null
	/**
	 * 名称
	 */
	label: null | string
	remark: null | string
	/**
	 * 范围
	 */
	scope: { [key: string]: any } | null
	/**
	 * 类型
	 */
	type: { [key: string]: any } | null
	[property: string]: any
}

/**
 * 盘点计划 添加 - 返回数据
 *
 * MatLowValueCheckPlanDetailVo
 */
export interface MatLowValueCheckPlanDetailVo {
	/**
	 * 开始时间
	 */
	beginDate?: null | string
	/**
	 * 审批状态
	 */
	bpmStatus?: { [key: string]: any } | null
	/**
	 * 编码
	 */
	code?: null | string
	/**
	 * 申请人
	 */
	createdBy?: null | string
	/**
	 * 结束时间
	 */
	endDate?: null | string
	/**
	 * 主键
	 */
	id?: number | null
	/**
	 * 名称
	 */
	label?: null | string
	/**
	 * 更新时间
	 */
	lastModifiedDate?: null | string
	/**
	 * 物资编码数量
	 */
	materialCodeNum?: number | null
	/**
	 * 物资分类数量
	 */
	materialTypeNum?: number | null
	/**
	 * 说明
	 */
	remark?: null | string
	/**
	 * 范围
	 */
	scope?: string | null
	/**
	 * 任务状态
	 */
	status?: string | null
	/**
	 * 类型
	 */
	type?: string | null
	[property: string]: any
}

/**
 * 盘点计划- 盘点部门分页 -> 返参 Vo
 *
 * MatLowValueCheckTaskPageVo
 */
export interface MatLowValueCheckTaskOrgPageVo {
	/**
	 * 负责人用户名
	 */
	chargeUsername?: null | string
	/**
	 * 盘点部门id
	 */
	checkOrgId?: number | null
	/**
	 * 盘点计划id
	 */
	checkPlanId?: number | null
	/**
	 * 主键
	 */
	id?: number | null
	[property: string]: any
}

/**
 * 批量添加部门  入参
 *
 * MatLowValueCheckTaskBatchAddDTO
 */
export interface MatLowValueCheckTaskBatchAddDTO {
	/**
	 * 盘点计划id
	 */
	planId: number | null
	/**
	 * 部门id列表
	 */
	sysOrgId: number[] | null
	[property: string]: any
}

/**
 * 盘点部门 - 盘点负责人 编辑 - 入参
 *
 * MatLowValueCheckTaskUpdateChargeUserDTO
 */
export interface MatLowValueCheckTaskUpdateChargeUserDTO {
	/**
	 * 负责人用户名
	 */
	chargeUsername: null | string
	/**
	 * 任务id
	 */
	taskId: number | null
	[property: string]: any
}

/**
 * 盘点计划- 盘点任务分页 -> 返参 Vo
 *
 * MatLowValueCheckTaskPageVo
 */
export interface MatLowValueCheckTaskPageVo {
	/**
	 * 负责人用户名
	 */
	chargeUsername?: null | string
	/**
	 * 盘点部门id
	 */
	checkOrgId?: number | null
	/**
	 * 盘点计划id
	 */
	checkPlanId?: number | null
	/**
	 * 盘点结果
	 */
	checkResult?: null | string
	/**
	 * 盘点范围
	 */
	checkScope?: null | string
	/**
	 * 盘点类型
	 */
	checkType?: null | string
	/**
	 * 盘点人
	 */
	checkUserName?: null | string
	/**
	 * 盘点任务编号
	 */
	code?: null | string
	/**
	 * 主键
	 */
	id?: number | null
	/**
	 * 下发标识(0未下发,1已下发)
	 */
	issueFlag?: string | null
	/**
	 * 更新时间
	 */
	lastModifiedDate?: null | string
	/**
	 * 盘点计划号
	 */
	planCode?: null | string
	/**
	 * 盘点计划名称
	 */
	planLabel?: null | string
	/**
	 * 盘点进度
	 */
	processRate?: number | null
	/**
	 * 盘点报告id
	 */
	reportId?: string | null
	/**
	 * 盘点报告状态(0未提交,1已提交)
	 */
	reportStatus?: string | null
	/**
	 * 盘点状态
	 */
	status?: null | string
	/**
	 * 监盘人
	 */
	superviseCheckUserName?: null | string
	/**
	 * 物资品类
	 */
	typeNum?: number | null
	[property: string]: any
}

/**
 * 盘点计划- 盘点部门/盘点任务 分页 -> 入参 Vo
 *
 * MatLowValueCheckTaskPageVoRequest
 */
export interface MatLowValueCheckTaskPageVoRequest {
	/**
	 * 盘点负责人
	 */
	chargeRealname?: string
	/**
	 * 盘点人
	 */
	checkRealname?: string
	/**
	 * 盘点范围
	 */
	checkScope?: string
	/**
	 * 盘点类型
	 */
	checkType?: string
	currentPage?: number
	pageSize?: number
	/**
	 * 盘点计划号
	 */
	planCode?: string
	/**
	 * 盘点计划名称
	 */
	planLabel?: string
	sidx?: string
	sord?: string
	/**
	 * 监盘人
	 */
	superviseRealname?: string
	/**
	 * 盘点部门
	 */
	sysOrgId?: number
	/**
	 * 盘点任务编号
	 */
	taskCode?: string
	[property: string]: any
}

/**
 * 盘点任务 - 指定盘点人员 入参
 *
 * MatLowValueCheckTaskAssignUserDTO
 */
export interface MatLowValueCheckTaskAssignUserDTO {
	/**
	 * 盘点人姓名
	 */
	checkRealName: null | string
	/**
	 * 盘点人用户名
	 */
	checkUserName: null | string
	/**
	 * 任务id
	 */
	id: number | null
	/**
	 * 监盘人姓名
	 */
	superviseCheckRealName: null | string
	/**
	 * 监盘人用户名
	 */
	superviseCheckUserName: null | string
	[property: string]: any
}

/**
 * 盘点任务详情  返回数据  返参
 *
 * MatLowValueCheckTaskDetailVo
 */
export interface MatLowValueCheckTaskDetailVo {
	/**
	 * 盘点负责人
	 */
	chargeUsername?: null | string
	/**
	 * 盘点部门
	 */
	checkOrgId?: number | null
	/**
	 * 盘点结果
	 */
	checkResult?: null | string
	/**
	 * 盘点人
	 */
	checkUserName?: null | string
	/**
	 * 任务编号
	 */
	code?: null | string
	/**
	 * 主键
	 */
	id?: number | null
	/**
	 * 盘点时间
	 */
	lastModifiedDate?: null | string
	/**
	 * 盘点物资总量
	 */
	materialCodeNum?: number | null
	/**
	 * 物资品类（项）
	 */
	materialTypeNum?: number | null
	/**
	 * 盘点计划号
	 */
	planCode?: null | string
	/**
	 * 盘点计划名称
	 */
	planLabel?: null | string
	/**
	 * 盘点范围
	 */
	planScope?: null | string
	/**
	 * 盘点类型
	 */
	planType?: null | string
	/**
	 * 关联领用单数量
	 */
	receiveNum?: number | null
	/**
	 * 监盘人
	 */
	superviseCheckUserName?: null | string
	[property: string]: any
}

/**
 * 低值 盘点任务 盘点结果 - 分页
 * MatLowValueCheckMaterialPageVo
 */
export interface MatLowValueCheckMaterialPageVo {
	/**
	 * 应盘数量
	 */
	allocationCount?: number | null
	/**
	 * 计划id
	 */
	checkPlanId?: number | null
	/**
	 * 任务id
	 */
	checkPlanTaskId?: number | null
	/**
	 * 盘点结果(0异常, 1正常)
	 */
	checkResult?: null | string
	/**
	 * 物资编码
	 */
	code?: null | string
	/**
	 * 实盘数量
	 */
	completedCount?: number | null
	/**
	 * 差异数量
	 */
	diffNum?: number | null
	/**
	 * 主键
	 */
	id?: number | null
	/**
	 * 编码名称
	 */
	label?: null | string
	/**
	 * 物资id
	 */
	materialId?: number | null
	/**
	 * 物资分类ID
	 */
	materialTypeId?: number | null
	/**
	 * 物资分类名称
	 */
	materialTypeLabel?: null | string
	/**
	 * 规格型号
	 */
	version?: null | string
	[property: string]: any
}

/**
 * 低值 盘点任务 盘点结果 详情 返回数据
 *
 * MatLowValueCheckMaterialDetailVo
 */
export interface MatLowValueCheckMaterialDetailVo {
	/**
	 * 应盘数量
	 */
	checkNum?: number | null
	/**
	 * 盘点人
	 */
	checkUserName?: null | string
	/**
	 * 主键
	 */
	id?: number | null
	/**
	 * 低值类型
	 */
	lowValueTypeLabel?: null | string
	/**
	 * 物资编码
	 */
	materialCode?: null | string
	/**
	 * 物资id
	 */
	materialId?: number | null
	/**
	 * 物资名称
	 */
	materialLabel?: null | string
	/**
	 * 物资分类编码
	 */
	materialTypeCode?: null | string
	/**
	 * 物资分类id
	 */
	materialTypeId?: number | null
	/**
	 * 物资分类名称
	 */
	materialTypeLabel?: null | string
	/**
	 * 监盘人
	 */
	superviseCheckUserName?: null | string
	/**
	 * 规格型号
	 */
	version?: null | string
	[property: string]: any
}

/**
 * 低值 盘点任务 盘点结果 - 盘点分配 分页Vo
 *
 * MatLowValueCheckAllocationPageVo
 */
export interface MatLowValueCheckAllocationPageVo {
	/**
	 * 分配领用分配时间
	 */
	allocationDate?: null | string
	/**
	 * 分配数量
	 */
	allocationNum?: number | null
	/**
	 * 分配人用户名
	 */
	allocationUsername?: null | string
	/**
	 * 手机号
	 */
	allocationUserPhone?: null | string
	/**
	 * 可用数量
	 */
	availableNum?: number | null
	/**
	 * 物资id
	 */
	checkMaterialId?: number | null
	/**
	 * 盘点数量
	 */
	checkNum?: number | null
	/**
	 * 主键
	 */
	id?: number | null
	/**
	 * 说明
	 */
	remark?: null | string
	/**
	 * 存放位置
	 */
	storeLocation?: null | string
	/**
	 * 不可用数量
	 */
	unavailableNum?: number | null
	[property: string]: any
}

/**
 * 低值 盘点任务 盘点结果 - 盘点分配 编辑 入参
 * MatLowValueCheckAllocationDTO
 */
export interface MatLowValueCheckAllocationDTO {
	/**
	 * 分配时间
	 */
	allocationDate?: null | string
	/**
	 * 分配数量
	 */
	allocationNum?: number | null
	/**
	 * 分配人
	 */
	allocationUsername?: null | string
	/**
	 * 可用数量
	 */
	availableNum?: number | null
	/**
	 * 物资id
	 */
	checkMaterialId?: number | null
	/**
	 * 盘点数量
	 */
	checkNum?: number | null
	/**
	 * 计划id
	 */
	checkPlanId?: number | null
	/**
	 * 任务id
	 */
	checkTaskId?: number | null
	/**
	 * 主键
	 */
	id?: number | null
	/**
	 * 说明
	 */
	remark?: null | string
	/**
	 * 不可用数量
	 */
	unavailableNum?: number | null
	[property: string]: any
}

/**
 * 盘点报告分页 返回参数
 *
 * MatLowValueCheckReportPageVo
 */
export interface MatLowValueCheckReportPageVo {
	/**
	 * 开始时间
	 */
	beginDate?: null | string
	/**
	 * 盘点负责人
	 */
	chargeUsername?: null | string
	/**
	 * 盘点部门
	 */
	checkOrgId?: number | null
	/**
	 * 盘点范围
	 */
	checkScope?: null | string
	/**
	 * 任务id
	 */
	checkTaskId?: number | null
	/**
	 * 盘点类型
	 */
	checkType?: null | string
	/**
	 * 报告编码
	 */
	code?: null | string
	/**
	 * 编制人
	 */
	createdBy?: null | string
	/**
	 * 结束时间
	 */
	endDate?: null | string
	/**
	 * 报告id
	 */
	id?: number | null
	/**
	 * 更新时间
	 */
	lastModifiedDate?: null | string
	/**
	 * 物资品类（项）
	 */
	materialTypeNum?: number | null
	/**
	 * 计划编码
	 */
	planCode?: null | string
	/**
	 * 状态
	 */
	status?: { [key: string]: any } | null
	/**
	 * 任务编码
	 */
	taskCode?: null | string
	[property: string]: any
}

/**
 * 低值 盘点报告 详情 - 返回数据
 *
 * MatLowValueCheckReportVo
 */
export interface MatLowValueCheckReportVo {
	/**
	 * 建议
	 */
	advice?: null | string
	/**
	 * 盘点任务id
	 */
	checkTaskId?: number | null
	/**
	 * 编码
	 */
	code?: null | string
	/**
	 * 创建人
	 */
	createdBy?: null | string
	/**
	 * 创建时间
	 */
	createdDate?: null | string
	/**
	 * id
	 */
	id?: number | null
	/**
	 * 更新人
	 */
	lastModifiedBy?: null | string
	/**
	 * 更新时间
	 */
	lastModifiedDate?: null | string
	/**
	 * 原因
	 */
	reason?: null | string
	/**
	 * 状态
	 */
	status?: { [key: string]: any } | null
	/**
	 * 总结
	 */
	summary?: null | string
	[property: string]: any
}

/**
 * 低值 盘点报告 新增信息
 *
 * MatLowValueCheckReportDTO
 */
export interface MatLowValueCheckReportDTO {
	advice?: null | string
	checkTaskId?: number | null
	code?: null | string
	createdBy?: null | string
	createdDate?: null | string
	delFlag?: { [key: string]: any } | null
	id?: number | null
	lastModifiedBy?: null | string
	lastModifiedDate?: null | string
	reason?: null | string
	status?: { [key: string]: any } | null
	summary?: null | string
	[property: string]: any
}

/**
 * 枚举：低值盘点类型
 */
export enum ILowValueInventoryType {
	/**
	 * 年度盘点
	 */
	year = "0",

	/**
	 * 季度盘
	 */
	quarter = "1",

	/**
	 * 抽样盘
	 */
	sample = "2"
}

/**
 * 枚举：低值盘点结果
 */
export enum ILowValueInventoryCheckStatus {
	/**
	 * 正常
	 */
	default = "0",

	/**
	 * 异常
	 */
	danger = "1"
}

/**
 * 枚举：低值盘点状态
 */
export enum ILowValueInventoryCheckJobStatus {
	/**
	 * 待开始
	 */
	noStart = "0",

	/**
	 * 进行中
	 */
	startTask = "1",

	/**
	 * 已完成
	 */
	endTask = "2"
}
