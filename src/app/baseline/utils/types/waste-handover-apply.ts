/**
 * 交旧申请分页 params
 */

export interface MatWasteOldApplyPageQuery {
	/**
	 * 审批状态列表
	 */
	bpmStatus: string
	/**
	 * 公司id
	 */
	companyId?: number
	currentPage?: number
	/**
	 * 部门id
	 */
	deptId?: number
	/**
	 * 关键词
	 */
	keyword?: string
	pageSize?: number
	sidx?: string
	sord?: string
	/**
	 * 交旧类型
	 */
	type?: string
	[property: string]: any
}

/**
 * 交旧申请分页Vo
 *
 * MatWasteOldApplyPageVo
 */
export interface MatWasteOldApplyPageVo {
	/**
	 * 关联业务单号
	 */
	applyCode?: null | string
	/**
	 * 申请时间
	 */
	applyDate?: null | string
	/**
	 * 申请单id
	 */
	applyId?: number | null
	/**
	 * 申请人
	 */
	applyRealname?: null | string
	/**
	 * 审批状态
	 */
	bpmStatus?: string | null
	/**
	 * 申请单号
	 */
	code?: null | string
	/**
	 * 交旧申请主键
	 */
	id?: number | null
	/**
	 * 交旧业务名称
	 */
	label?: null | string
	/**
	 * 公司id
	 */
	sysCommunityId?: number | null
	/**
	 * 申请部门did
	 */
	sysDepartmentId?: number | null
	/**
	 * 交旧类型
	 */
	type?: string | null
	[property: string]: any
}

/**
 * 新增交旧申请 入参
 *
 * MatWasteOldApplyAddDTO
 */
export interface MatWasteOldApplyAddDTO {
	/**
	 * 领料单id
	 */
	applyId: number | null
	/**
	 * 交旧申请名称
	 */
	label: null | string
	/**
	 * 备注说明
	 */
	remark?: null | string
	/**
	 * C
	 */
	type: null | string
	[property: string]: any
}

/**
 * 新增交旧申请 返回数据
 *
 * MatWasteOldApplyVo
 */
export interface MatWasteOldApplyVo {
	/**
	 * 申请单id
	 */
	applyId?: number | null
	/**
	 * 审批状态
	 */
	bpmStatus?: null | string
	/**
	 * 编码
	 */
	code?: null | string
	/**
	 * 创建人
	 */
	createdBy?: null | string
	/**
	 * 创建时间
	 */
	createdDate?: null | string
	/**
	 * 删除标识
	 */
	delFlag?: null | string
	/**
	 * 主键
	 */
	id?: number | null
	/**
	 * 名称
	 */
	label?: null | string
	/**
	 * 最后修改人
	 */
	lastModifiedBy?: null | string
	/**
	 * 最后修改时间
	 */
	lastModifiedDate?: null | string
	/**
	 * 领用单id
	 */
	lowValueRequisitionId?: number | null
	/**
	 * 物资id
	 */
	materialId?: number | null
	/**
	 * 领料单id
	 */
	requisitionId?: number | null
	/**
	 * 公司id
	 */
	sysCommunityId?: number | null
	/**
	 * 类型
	 */
	type?: null | string
	[property: string]: any
}

/**
 * 获取交旧申请详情 返回数据
 *
 * MatWasteOldDetailVo
 */
export interface MatWasteOldDetailVo {
	applyId: number | null
	bpmStatus?: null | string
	code?: null | string
	createdBy?: null | string
	createdDate?: null | string
	delFlag?: null | string
	id?: number | null
	label?: null | string
	lastModifiedBy?: null | string
	lastModifiedDate?: null | string
	lowValueRequisitionId?: number | null
	materialId: number | null
	requisitionId?: number | null
	sysCommunityId: number | null
	type: null | string
	[property: string]: any
}

/**
 * 交旧申请 添加物资 入参
 *
 * MatWasteOldApplyItemBatchAddDTO
 */
export interface MatWasteOldApplyItemBatchAddDTO {
	/**
	 * 交旧申请单id
	 */
	applyId: number | null
	/**
	 * 交旧申请明细列表
	 */
	items: MatWasteOldApplyItemBatchAddDetailDTO[] | null
	[property: string]: any
}

/**
 * 交旧申请明细批量新增DTO
 *
 * MatWasteOldApplyItemBatchAddDetailDTO
 */
export interface MatWasteOldApplyItemBatchAddDetailDTO {
	/**
	 * 低值领用分配ID
	 */
	allocationId?: number | null
	/**
	 * 低值分配数量
	 */
	allocationNum?: number | null
	/**
	 * 低值领用人用户名
	 */
	allocationUsername?: string | null
	/**
	 * 物资id
	 */
	materialId?: number | null
	[property: string]: any
}

/**
 * 交旧申请 编辑 物资 入参
 *
 * MatWasteOldApplyItemBatchUpdateDTO
 */
export interface MatWasteOldApplyItemBatchUpdateDTO {
	/**
	 * 交旧申请单id
	 */
	applyId: number | null
	/**
	 * 交旧申请单明细列表
	 */
	items: MatWasteOldApplyItemBatchUpdateDetailDTO[] | null
	[property: string]: any
}

/**
 * 交旧申请明细-批量更新仓库 入参
 * MatWasteOldApplyItemBatchUpdateStoreDTO
 */
export interface MatWasteOldApplyItemBatchUpdateStoreDTO {
	/**
	 * 交旧申请单明细id
	 */
	itemIdList: number[] | null
	/**
	 * 仓库ID
	 */
	storeId: number | null
	/**
	 * 更新仓库类型 1维修 2废旧
	 */
	type: null | string
	[property: string]: any
}

/**
 * 交旧申请明细批量编辑DTO
 *
 * MatWasteOldApplyItemBatchUpdateDetailDTO
 */
export interface MatWasteOldApplyItemBatchUpdateDetailDTO {
	/**
	 * 申请单明细id
	 */
	applyItemId?: number | null
	/**
	 * 预估回收重量
	 */
	recoveryWeight: number | null
	/**
	 * 交旧申请明细id
	 */
	id: number | null
	/**
	 * 物资id
	 */
	materialId?: number | null
	/**
	 * 返修数量
	 */
	repairNum?: number | null
	/**
	 * 返修仓库id
	 */
	repairStoreId?: number | null
	/**
	 * 报废数量
	 */
	scrapNum?: number | null
	/**
	 * 报废仓库id
	 */
	scrapStoreId?: number | null
	[property: string]: any
}

/**
 * 交旧申请明细分页Vo
 *
 * MatWasteOldApplyItemPageVo
 */
export interface MatWasteOldApplyItemPageVo {
	/**
	 * 业务申请明细id
	 */
	applyItemId?: number | null
	/**
	 * 可交旧数量
	 */
	canOldNum?: number | null
	/**
	 * 已交旧数量
	 */
	completeOldNum?: number | null
	/**
	 * 预估回收重量
	 */
	recoveryWeight?: number | null
	/**
	 * id
	 */
	id?: number | null
	/**
	 * 物资编码
	 */
	materialCode?: null | string
	/**
	 * 物资id
	 */
	materialId?: number | null
	/**
	 * 物资名称
	 */
	materialName?: null | string
	/**
	 * 技术参数
	 */
	materialTechnicalParameter?: null | string
	/**
	 * 库存单位
	 */
	materialUseUnit?: null | string
	/**
	 * 规格型号
	 */
	materialVersion?: null | string
	/**
	 * 领料出库数量
	 */
	outNum?: number | null
	/**
	 * 主要材质
	 */
	quality?: { [key: string]: any } | null
	/**
	 * 返修数量
	 */
	repairNum?: number | null
	/**
	 * 返修仓库id
	 */
	repairStoreId?: number | null
	/**
	 * 返修仓库名称
	 */
	repairStoreName?: null | string
	/**
	 * 报废数量
	 */
	scrapNum?: number | null
	/**
	 * 报废仓库id
	 */
	scrapStoreId?: number | null
	/**
	 * 报废仓库名称
	 */
	scrapStoreName?: null | string
	/**
	 * 公司id
	 */
	sysCommunityId?: number | null
	/**
	 * 交旧申请单id
	 */
	wasteApplyId?: number | null
	/**
	 * 废旧物资类型
	 */
	wasteMaterialType?: { [key: string]: any } | null
	[property: string]: any
}

/**
 * 交旧类型
 */
export enum IWasteOldType {
	/**
	 * 领料单交旧
	 */
	matPick = "0",

	/**
	 * 历史物资 - > 物资编码交旧
	 */
	matHistory = "1",

	/**
	 * 低值易耗交旧
	 */
	matLow = "2",

	/**
	 * 返修物资交旧
	 */
	matRepair = "3",

	/**
	 * 在库物资交旧
	 */
	matInStore = "4"
}

/**
 *
 */
export enum IWasteRepairAndScrap {
	repair = "repair",
	scrap = "scrap",
	standby = "standby"
}
