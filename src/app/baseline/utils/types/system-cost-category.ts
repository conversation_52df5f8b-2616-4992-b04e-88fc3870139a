/**
 * 查询费用类别树 入参
 * SystemCostCategoryDTO
 */
export interface SystemCostCategoryDTO {
	/**
	 * 费用类别编码
	 */
	code?: null | string
	/**
	 * 上级费用类别ID
	 */
	fid?: number | null
	id?: number | null
	/**
	 * id集合
	 */
	idList?: number[] | null
	/**
	 * 费用类别名称
	 */
	label?: null | string
	/**
	 * 备注
	 */
	remark?: null | string
	/**
	 * 费用类别状态:1已启用、2已冻结
	 */
	status?: null | string
	/**
	 * 公司ID
	 */
	sysCommunityId?: number | null
	[property: string]: any
}

/**
 * 查询费用类别树 返参
 *
 * SystemCostCategoryTreeVo
 */
export interface SystemCostCategoryTreeVo {
	/**
	 * 组合id
	 */
	ancestors?: null | string
	/**
	 * 子费用类别
	 */
	children?: SystemCostCategoryTreeVo[] | null
	/**
	 * 编码
	 */
	code?: null | string
	/**
	 * 组合名称
	 */
	combineLabel?: null | string
	/**
	 * 费用类别ID
	 */
	costCategoryId?: number | null
	/**
	 * 上级ID
	 */
	fid?: number | null
	/**
	 * 公司ID|费用类别id
	 */
	id?: number | null
	/**
	 * 是否是公司
	 */
	isCompany?: boolean | null
	/**
	 * 名称
	 */
	label?: null | string
	/**
	 * 费用类别状态:0待启用、1已启用、2已冻结
	 */
	status?: null | string
	/**
	 * 公司ID
	 */
	sysCommunityId?: number | null
	[property: string]: any
}

/**
 * 费用类别-分页查询 入参
 */
export interface systemCostCategoryPagedRequest {
	currentPage?: number
	pageSize?: number
	sidx?: string
	sord?: string
	[property: string]: any
}

/**
 * 费用类别--分页查询 返参
 *
 * SystemCostCategoryVo
 */
export interface SystemCostCategoryVo {
	/**
	 * 费用类别组合id
	 */
	ancestors?: null | string
	/**
	 * 费用类别编码
	 */
	code?: null | string
	/**
	 * 费用类别组合名称
	 */
	combineLabel?: null | string
	/**
	 * 上级费用类别ID
	 */
	fid?: number | null
	id?: number | null
	/**
	 * 费用类别名称
	 */
	label?: null | string
	/**
	 * 备注
	 */
	remark?: null | string
	/**
	 * 费用类别状态:0待启用、1已启用、2已冻结
	 */
	status?: null | string
	/**
	 * 公司ID
	 */
	sysCommunityId?: number | null
	[property: string]: any
}

/**
 * 费用类别 状态
 * 0待启用、1已启用、2已冻结
 */
export enum ICostCategoryStatus {
	Drafted = "0", //草稿
	Started = "1", //已启用
	Freeze = "2" //停用
}
