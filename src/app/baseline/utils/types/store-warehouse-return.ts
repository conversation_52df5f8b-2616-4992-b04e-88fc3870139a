/**
 * 入库管理 退库入库 列表 传参
 *
 */
export interface MatReturnStoreRequestParams {
	/**
	 * 公司
	 */
	companyCode?: string
	/**
	 * 费用类别
	 */
	costType?: string
	currentPage?: number
	/**
	 * 入库单号
	 */
	inStoreNo?: string
	pageSize?: number
	/**
	 * 入库id
	 */
	preBusinessId?: string
	/**
	 * 入库类型
	 */
	preBusinessType?: string
	sidx?: string
	sord?: string
	/**
	 * 公司Id
	 */
	sysCommunityId?: number
	/**
	 * table类型
	 */
	tableType?: number
	[property: string]: any
}

/**
 * 入库管理 - 退库入库 列表
 *
 * MatInStoreReceiveVO
 */
export interface MatReturnStoreVO {
	/**
	 * 退库金额
	 */
	amount?: number | null
	/**
	 * 申请时间
	 */
	applyDate?: null | string
	/**
	 * 入库完成时间
	 */
	applyFinishDate?: null | string
	/**
	 * 入库单号
	 */
	code?: null | string
	/**
	 * ID
	 */
	id?: number | null
	/**
	 * 已入库物资编码
	 */
	inspectedCount?: number | null
	/**
	 * 入库物资编码
	 */
	matCount?: number | null
	/**
	 * 待入库物资编码
	 */
	notInspectedCount?: number | null
	/**
	 * 前置业务单号/关联业务单号
	 */
	preBusinessCode?: null | string
	/**
	 * 前置业务Id/关联业务ID
	 */
	preBusinessId?: number | null
	/**
	 * 入库状态 1：未入库、2：部分入库、 3：已入库
	 */
	status?: null | string
	/**
	 * 仓库ID
	 */
	storeId?: number | null
	/**
	 * 退库仓库
	 */
	storeName?: null | string
	/**
	 * 申请部门ID
	 */
	sysOrgId?: number | null
	/**
	 * 申请部门名称
	 */
	sysOrgName?: null | string
	/**
	 * 入库类型
	 */
	type?: null | string
	/**
	 * 申请人ID
	 */
	userId?: number | null
	/**
	 * 申请人姓名
	 */
	userName?: null | string
	[property: string]: any
}

/**
 * 入库管理 退库入库 详情 传参
 *
 */
export interface MatReturnStoreItemRequestParams {
	/**
	 * 接收入库ID
	 */
	applyId?: number
	/**
	 * 本次入库数量
	 */
	completeNum?: string
	currentPage?: number
	/**
	 * ID
	 */
	id?: number
	pageSize?: number
	/**
	 * 入库区域
	 */
	regionId?: number
	/**
	 * 状态说明
	 */
	remark?: string
	/**
	 * 入库货位
	 */
	roomId?: number
	sidx?: string
	sord?: string
	/**
	 * 状态
	 */
	status?: string
	/**
	 * 入库仓库
	 */
	storeId?: number
	/**
	 * 质保期
	 */
	validityPeriod?: string
	[property: string]: any
}

/**
 * 入库管理 退库入库 详情 返回体
 *
 * MatInStoreReceiveVO
 */
export interface MatReturnStoreItemVO {
	/**
	 * 入库金额
	 */
	amount?: number | null
	/**
	 * 申请单Id
	 */
	applyId?: number | null
	/**
	 * 批次号
	 */
	batchNo?: null | string
	/**
	 * 采购单位
	 */
	buyUnit?: null | string
	/**
	 * 入库单号
	 */
	code?: null | string
	/**
	 * 已完成量
	 */
	completeNum?: number | null
	/**
	 * ID
	 */
	id?: number | null
	/**
	 * 已入库批次
	 */
	inStoreBatchTimes?: number | null
	/**
	 * 已入库量
	 * 已入库数量
	 */
	inStoredNum?: number | null
	/**
	 * 可入库量
	 * 可入库数量
	 */
	inStoreNum?: number | null
	/**
	 * 入库单价
	 */
	inStorePrice?: number | null
	/**
	 * 操作时间
	 */
	inStoreUpdateTime?: null | string
	/**
	 * 物资编码
	 */
	materialCode?: null | string
	/**
	 * 物资ID
	 */
	materialId?: number | null
	/**
	 * 物资名称
	 */
	materialName?: null | string
	/**
	 * 入库数量
	 */
	num?: number | null
	/**
	 * 采购订单号
	 */
	purchaseCode?: null | string
	/**
	 * 采购单价
	 */
	purchasePrice?: number | null
	/**
	 * 区域名称
	 */
	regionLabel?: null | string
	/**
	 * 货位Id
	 */
	roomId?: number | null
	/**
	 * 货位名称
	 */
	roomName?: null | string
	/**
	 * 状态：1：未入库、2：已入库
	 */
	status?: null | string
	/**
	 * 入库仓库ID
	 */
	storeId?: number | null
	/**
	 * 入库仓库
	 */
	storeName?: null | string
	/**
	 * 技术参数
	 */
	technicalParameter?: null | string
	/**
	 * 待入库量
	 * 待入库数量
	 */
	toInStoreNum?: number | null
	/**
	 * 操作人
	 */
	userName?: null | string
	/**
	 * 库存单位
	 */
	useUnit?: null | string
	/**
	 * 质保期
	 */
	validityPeriod?: null | string
	/**
	 * 品牌型号
	 */
	version?: null | string
	/**
	 * 关联业务单号
	 */
	workOrderCode?: null | string
	/**
	 * 关联业务单Id
	 */
	workOrderId?: number | null
	[property: string]: any
}

/**
 * 入库管理 入库 action 传参
 *
 */
export interface EditMatInStoreReturnRequestParams {
	id?: number
	[property: string]: any
}

/**
 * 入库管理 货位 传参 getReceiveBatch
 *
 */
export interface MatInStoreReturnSearchRoomRequestParams {
	currentPage?: number
	/**
	 * ID：退库入库明细ID
	 */
	id?: number
	pageSize?: number
	[property: string]: any
}
