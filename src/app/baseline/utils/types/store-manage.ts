/**
 * @description 仓库管理 api 相关的类型
 */

/**
 * 枚举：仓库人员类型
 */

export enum IWarehouseMemberType {
	/**
	 * 库管员
	 */
	storekeeper = "1",
	/**
	 * 审核员
	 */
	auditor = "2"
}

/**
 * 枚举：仓库类型
 */
export enum IWarehouseType {
	/**
	 *  标准库
	 */
	default = "0",

	/**
	 * 废旧库
	 */
	waste = "1",

	/**
	 * 备品备件库
	 */
	spareParts = "2",

	/**
	 * 周转件正常库
	 */
	rotablesNormal = "3",

	/**
	 * 周转件待修库
	 */
	rotablesWaitingRepair = "4",
	/**
	 * 危险品仓库
	 */
	dangerousWarehouse = "5",

	/**
	 * 危险备件库
	 */
	sparePartsWarehouse = "6",

	/**
	 * 危险废旧库
	 */
	dangerousWaste = "7",

	/**
	 * 实物仓库 （北京项目所用）
	 */
	physicalObject = "8"
}

/**
 * 枚举 - 仓库状态
 */
export enum IWarehouseStatus {
	/**
	 * 盘点中
	 */
	inventoryInProgress = "4",
	/**
	 * 启用
	 */
	activated = "2",
	/**
	 * 冻结
	 */
	freezed = "5",
	/**
	 * 停用
	 */
	disabled = "3",
	/**
	 * 草稿/待启用
	 */
	draft = "1"
}

/**
 * 仓库 vo
 *
 * MatStoreVo
 */
export interface MatStoreVo {
	/**
	 * 仓库地址
	 */
	address?: null | string
	/**
	 * 审批状态;0待提交 1审批中 2已审批 3已驳回
	 */
	bpmStatus?: null | string
	/**
	 * code
	 */
	code?: null | string
	/**
	 * 成本中心ID
	 */
	costCenterId?: number | null
	/**
	 * 段区ID
	 */
	depotId?: number | null
	/**
	 * ID
	 */
	id?: number | null
	/**
	 * label
	 */
	label?: null | string
	/**
	 * 仓库级别
	 */
	level?: null | string
	/**
	 * 线路ID
	 */
	lineId?: null | string
	/**
	 * 仓库位置
	 */
	positionId?: null | string
	/**
	 * 区域数量
	 */
	regionNum?: number | null
	/**
	 * 备注
	 */
	remark?: null | string
	/**
	 * 货位数量
	 * 区域数量
	 */
	roomNum?: number | null
	/**
	 * 仓库状态
	 */
	status?: null | string
	/**
	 * 审核员数量
	 */
	storeAuditNum?: number | null
	/**
	 * 库管员数量
	 */
	storeManageNum?: number | null
	/**
	 * 公司ID
	 */
	sysCommunityId?: number | null
	/**
	 * 所属部门ID
	 */
	sysOrgId?: number | null
	/**
	 * 仓库类型
	 */
	type?: null | string
	[property: string]: any
}
/**
 * 仓库信息
 * MatStoreDTO
 */
export interface MatStoreDTO {
	/**
	 * 仓库地址
	 */
	address?: null | string
	/**
	 * 审批状态;0待提交 1审批中 2已审批 3已驳回
	 */
	bpmStatus?: null | string
	/**
	 * code
	 */
	code?: null | string
	/**
	 * 成本中心ID
	 */
	costCenterId?: number | null
	/**
	 * 段区ID
	 */
	depotId?: number | null
	/**
	 * ID
	 */
	id?: number | null
	/**
	 * label
	 */
	label?: null | string
	/**
	 * 仓库级别
	 */
	level?: null | string
	/**
	 * 线路ID
	 */
	lineId?: null | string
	/**
	 * 仓库位置
	 */
	positionId?: null | string
	/**
	 * 备注
	 */
	remark?: null | string
	/**
	 * 仓库状态
	 */
	status?: null | string
	/**
	 * 公司ID
	 */
	sysCommunityId?: number | null
	/**
	 * 所属部门ID
	 */
	sysOrgId?: number | null
	/**
	 * 仓库类型
	 */
	type?: null | string
	[property: string]: any
}

/**
 * 仓库信息查询参数
 *
 */
export interface MatStoreRequestParams {
	/**
	 * 仓库地址
	 */
	address?: string
	/**
	 * 审批状态;0待提交 1审批中 2已审批 3已驳回
	 */
	bpmStatus?: string
	/**
	 * code
	 */
	code?: string
	/**
	 * 成本中心ID
	 */
	costCenterId?: number
	currentPage?: number
	/**
	 * 段区ID
	 */
	depotId?: number
	/**
	 * ID
	 */
	id?: number
	/**
	 * label
	 */
	label?: string
	/**
	 * 仓库级别
	 */
	level?: string
	/**
	 * 线路ID
	 */
	lineId?: string
	pageSize?: number
	/**
	 * 仓库位置
	 */
	positionId?: string
	/**
	 * 备注
	 */
	remark?: string
	sidx?: string
	sord?: string
	/**
	 * 仓库状态
	 */
	status?: string
	/**
	 * 公司ID
	 */
	sysCommunityId?: number
	/**
	 * 所属部门ID
	 */
	sysOrgId?: number
	/**
	 * 仓库类型
	 */
	type?: string
	[property: string]: any
}

/**
 * MatStoreRegionVo
 */
export interface MatStoreRegionVo {
	/**
	 * 编码
	 * code
	 */
	code?: null | string
	createdBy?: null | string
	createdDate?: null | string
	/**
	 * ID
	 * id
	 */
	id?: number | null
	/**
	 * 名称
	 * label
	 */
	label?: null | string
	lastModifiedBy?: null | string
	lastModifiedDate?: null | string
	/**
	 * 备注
	 * remark
	 */
	remark?: null | string
	/**
	 * 仓库ID
	 * store_id
	 */
	storeId?: number | null
	/**
	 * 类型
	 * type
	 */
	type?: null | string
	/**
	 * 仓库人员类型
	 */
	仓库人员类型?: null | string
	[property: string]: any
}

/**
 * 参数
 *
 * Map«String[]»
 */
export interface MapString {
	key?: string[] | null
	[property: string]: any
}

/**
 * 区域分页查询参数
 */
export interface MatStoreRegionQueryParam {
	/**
	 * 编码
	 * code
	 */
	code?: string
	createdBy?: string
	createdDate?: string
	currentPage?: number
	/**
	 * 逻辑删除标识
	 * del_flag
	 */
	delFlag?: string
	/**
	 * ID
	 * id
	 */
	id?: number
	/**
	 * 名称
	 * label
	 */
	label?: string
	lastModifiedBy?: string
	lastModifiedDate?: string
	pageSize?: number
	/**
	 * 备注
	 * remark
	 */
	remark?: string
	sidx?: string
	sord?: string
	/**
	 * 仓库ID
	 * store_id
	 */
	storeId?: number
	/**
	 * 类型
	 * type
	 */
	type?: string
	/**
	 * 仓库人员类型
	 */
	仓库人员类型?: string
	[property: string]: any
}

/**
 * MatStoreRegionDTO
 */
export interface MatStoreRegionDTO {
	/**
	 * 编码
	 * code
	 */
	code?: null | string
	createdBy?: null | string
	createdDate?: null | string
	/**
	 * 逻辑删除标识
	 * del_flag
	 */
	delFlag?: null | string
	/**
	 * ID
	 * id
	 */
	id?: number | null
	/**
	 * 名称
	 * label
	 */
	label?: null | string
	lastModifiedBy?: null | string
	lastModifiedDate?: null | string
	/**
	 * 备注
	 * remark
	 */
	remark?: null | string
	/**
	 * 仓库ID
	 * store_id
	 */
	storeId?: number | null
	/**
	 * 类型
	 * type
	 */
	type?: null | string
	/**
	 * 仓库人员类型
	 */
	仓库人员类型?: null | string
	[property: string]: any
}

/**
 * 货位 Dto
 * MatStoreRoomDTO
 */
export interface MatStoreRoomDTO {
	/**
	 * 编码
	 * code
	 */
	code?: null | string
	createdBy?: null | string
	createdDate?: null | string
	/**
	 * del_flag
	 */
	delFlag?: null | string
	/**
	 * ID
	 * id
	 */
	id?: number | null
	/**
	 * 名称
	 * label
	 */
	label?: null | string
	lastModifiedBy?: null | string
	lastModifiedDate?: null | string
	/**
	 * 区域ID
	 * region_id
	 */
	regionId?: number | null
	/**
	 * 备注
	 * remark
	 */
	remark?: null | string
	/**
	 * 类型
	 * type
	 */
	type?: null | string
	[property: string]: any
}

/**
 * 返回数据
 *
 * MatStoreRoomVo
 */
export interface MatStoreRoomVo {
	/**
	 * 编码
	 * code
	 */
	code?: null | string
	createdBy?: null | string
	createdDate?: null | string
	/**
	 * ID
	 * id
	 */
	id?: number | null
	/**
	 * 名称
	 * label
	 */
	label?: null | string
	lastModifiedBy?: null | string
	lastModifiedDate?: null | string
	/**
	 * 区域ID
	 * region_id
	 */
	regionId?: number | null
	/**
	 * 备注
	 * remark
	 */
	remark?: null | string
	/**
	 * 类型
	 * type
	 */
	type?: null | string
	[property: string]: any
}

/**
 * 货位列表查询参数
 */
export interface MatStoreRoomQueryParams {
	/**
	 * 编码
	 * code
	 */
	code?: string
	createdBy?: string
	createdDate?: string
	currentPage?: number
	/**
	 * del_flag
	 */
	delFlag?: string
	/**
	 * ID
	 * id
	 */
	id?: number
	/**
	 * 名称
	 * label
	 */
	label?: string
	lastModifiedBy?: string
	lastModifiedDate?: string
	pageSize?: number
	/**
	 * 区域ID
	 * region_id
	 */
	regionId?: number
	/**
	 * 备注
	 * remark
	 */
	remark?: string
	sidx?: string
	sord?: string
	/**
	 * 类型
	 * type
	 */
	type?: string
	[property: string]: any
}

/**
 * 仓库人员 vo
 * MatStoreUserVo
 */
/**
 * MatStoreUserVo
 */
export interface MatStoreUserVo {
	/**
	 * ID
	 */
	id?: number | null
	/**
	 * 手机号
	 */
	phone?: null | string
	/**
	 * 性别
	 */
	sex?: number | null
	/**
	 * 岗位
	 */
	station?: null | string
	/**
	 * 仓库ID
	 */
	storeId?: number | null
	/**
	 * 仓库人员类型
	 * 仓库人员类型  库管员值为1 审核员为2
	 */
	storePersonType?: null | string
	/**
	 * 部门ID
	 */
	sysOrgId?: number | null
	/**
	 * 人员ID
	 */
	userId?: null | string
	[property: string]: any
}

/**
 * 仓库人员查询 params
 */
export interface MatStoreUserQueryParams {
	currentPage?: number
	/**
	 * del_flag
	 */
	delFlag?: string
	/**
	 * ID
	 * id
	 */
	id?: number
	pageSize?: number
	sidx?: string
	sord?: string
	/**
	 * 仓库ID
	 * store_id
	 */
	storeId?: number
	/**
	 * 人员ID
	 * user_id
	 */
	userId?: number
	[property: string]: any
}

/**
 * MatStoreUserDTO
 */
export interface MatStoreUserDTO {
	/**
	 * del_flag
	 */
	delFlag?: null | string
	/**
	 * ID
	 * id
	 */
	id?: number | null
	/**
	 * 仓库ID
	 * store_id
	 */
	storeId?: number | null
	/**
	 * 人员ID
	 * user_id
	 */
	userId?: number | null
	/**
	 * 仓库人员类型
	 */
	storePersonType?: null | string
	[property: string]: any
}

/**
 * 批量添加仓库人员
 *
 * AddBatchMatStoreUserRequest
 */
export interface AddBatchMatStoreUserRequest {
	/**
	 * ID
	 * id
	 */
	id?: number | null
	/**
	 * 仓库ID
	 * store_id
	 */
	storeId?: number | null
	/**
	 * 仓库人员类型
	 */
	storePersonType?: null | string
	/**
	 * 人员信息
	 */
	userIdList: any[]
	[property: string]: any
}

/**
 * 查询区域tree params
 */
export interface MatStoreRegionTreeParams {
	/**
	 * 编码
	 * code
	 */
	code?: string
	createdBy?: string
	createdDate?: string
	/**
	 * ID
	 * id
	 */
	id?: number
	/**
	 * 名称
	 * label
	 */
	label?: string
	lastModifiedBy?: string
	lastModifiedDate?: string
	/**
	 * 仓库ID
	 * store_id
	 */
	storeId?: number
	[property: string]: any
}

/**
 * 区域tree data
 *
 * MatStoreRegionTreeVo
 */
export interface MatStoreRegionTreeVo {
	/**
	 * 孩子节点
	 */
	children?: MatStoreRegionTreeVo[] | null
	/**
	 * 仓库名称
	 */
	label?: null | string
	[property: string]: any
}

/**
 * 查询库管理分页 参数
 */
export interface IListStoreManagerPagedParams {
	currentPage?: number
	pageSize?: number
	/**
	 * 帐号
	 */
	realname?: string
	sidx?: string
	sord?: string
	/**
	 * 仓库id
	 */
	storeId: number
	/**
	 * 仓库人员类型, 1: 库管员, 2: 审核员
	 */
	storePersonType: string
	/**
	 * 部门id
	 */
	sysOrgId?: number
	[property: string]: any
}

/**
 * 查询货位不分页列表 入参
 */

export interface MatStoreRoomRequest {
	/**
	 * 货位编码
	 */
	code?: string
	/**
	 * 货位名称
	 */
	label?: string
	/**
	 * 区域编码
	 */
	regionCode?: string
	/**
	 * 区域ID
	 */
	regionId?: number
	/**
	 * 区域名称
	 */
	regionLabel?: string
	/**
	 * 仓库ID
	 */
	storeId?: number
	/**
	 * 货位类型
	 */
	type?: string
	[property: string]: any
}
