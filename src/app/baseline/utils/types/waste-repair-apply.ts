/**
 * 返修申请列表对象
 */
export interface MatWasteRepairApplyVo {
	/**
	 * 维修预估费用（元）
	 */
	amount?: number | null
	/**
	 * 审批状态
	 */
	bpmStatus?: null | string
	/**
	 * 返修单号
	 */
	code?: null | string
	/**
	 * 申请人(字典)
	 */
	createdBy?: null | string
	/**
	 * 申请时间
	 */
	createdDate?: null | string
	/**
	 * ID
	 */
	id?: number | null
	/**
	 * 返修单名称
	 */
	label?: null | string
	/**
	 * 物资编码数量
	 */
	materialCodeNum?: number | null
	/**
	 * 维修备注说明
	 */
	reason?: null | string
	/**
	 * 维修公司id
	 */
	repairCommunityId?: null | string
	/**
	 * 维修公司名称
	 */
	repairCommunityLabel?: null | string
	/**
	 * 预估维修时间（天）
	 */
	repairDay?: number | null
	/**
	 * 返修数量
	 */
	repairNum?: number | null
	/**
	 * 维修部门
	 */
	repairOrgId?: number | null
	/**
	 * 维修部门名称
	 */
	repairOrgId_view?: null | string
	/**
	 * 维修方式：1、委外维修，2、自主维修
	 */
	repairWay?: number | null
	/**
	 * 仓库ID
	 */
	storeId?: number | null
	/**
	 * 仓库名称
	 */
	storeLabel?: number | null
	/**
	 * 申请部门
	 */
	sysOrgId?: number | null
	[property: string]: any
}

/**
 * 列表请求参数
 */

export interface MatWasteRepairApplyPageQuery {
	/**
	 * 审批状态列表
	 */
	bpmStatus: string
	currentPage?: number
	/**
	 * 部门id
	 */
	sysOrgId?: number
	/**
	 * 关键词
	 */
	code?: string
	label?: string
	preBusinessCode?: string
	pageSize?: number
	sidx?: string
	sord?: string
	/**
	 * 交旧类型
	 */
	type?: string
	[property: string]: any
}

/**
 * 新增返修申请
 */
export interface MatWasteRepairApplyAddVo {
	id?: number | null
	/**
	 * 维修预估费用（元）
	 */
	amount?: number | null
	/**
	 * 返修单名称
	 */
	label?: null | string
	/**
	 * 关联交旧单Id
	 */
	preBusinessId?: number | null
	/**
	 * 维修备注说明
	 */
	reason?: null | string
	/**
	 * 维修公司
	 */
	repairCommunity?: null | string
	/**
	 * 预估维修时间（天）
	 */
	repairDay?: number | null
	[property: string]: any
}

/**
 * 新增、编辑返回数据
 *
 */
export interface MatWasteRepairApplyResVo {
	/**
	 * 维修预估费用（元）
	 */
	amount?: number | null
	/**
	 * 审批状态
	 */
	bpmStatus?: null | string
	/**
	 * 返修单号
	 */
	code?: null | string
	/**
	 * 申请人(字典)
	 */
	createdBy?: null | string
	/**
	 * 申请时间
	 */
	createdDate?: null | string
	/**
	 * ID
	 */
	id?: number | null
	/**
	 * 返修单名称
	 */
	label?: null | string
	/**
	 * 物资编码数量
	 */
	materialCodeNum?: number | null
	/**
	 * 关联交旧单号
	 */
	preBusinessCode?: null | string
	/**
	 * 关联交旧单Id
	 */
	preBusinessId?: number | null
	/**
	 * 维修备注说明
	 */
	reason?: null | string
	/**
	 * 维修公司
	 */
	repairCommunity?: null | string
	/**
	 * 预估维修时间（天）
	 */
	repairDay?: number | null
	/**
	 * 返修数量
	 */
	repairNum?: number | null
	/**
	 * 申请部门
	 */
	sysOrgId?: number | null
}

/**
 * 物资明细
 */
export interface repairApplyItemVo {
	/**
	 * 申请单Id
	 */
	applyId?: number | null
	/**
	 * 批次号
	 */
	batchNo?: null | string
	/**
	 * 已完修数量
	 */
	finishNum?: number | null
	/**
	 * 冻结数量
	 */
	freezeNum?: number | null
	/**
	 * 返修申请明细id
	 */
	id?: number | null
	/**
	 * 物资编码
	 */
	materialCode?: null | string
	/**
	 * 物资ID
	 */
	materialId?: number | null
	/**
	 * 物资名称
	 */
	materialLabel?: null | string
	/**
	 * 返修数量
	 */
	num?: number | null
	/**
	 * 返修出库数量
	 */
	outStoreNum?: number | null
	/**
	 * 区域
	 */
	regionCode?: null | string
	/**
	 * 货位
	 */
	roomCode?: null | string
	/**
	 * 货位ID
	 */
	roomId?: number | null
	/**
	 * 库存数量
	 */
	storeNum?: number | null
	/**
	 * 技术参数
	 */
	technicalParameter?: null | string
	/**
	 * 库存单位
	 */
	useUnit?: null | string
	/**
	 * 规格型号
	 */
	version?: null | string
	[property: string]: any
}

export interface WasteRepairMaterialStoreVo {
	/**
	 * 可返修数量
	 */
	canNum?: number | null
	/**
	 * 每页唯一标识
	 */
	id?: number | null
	/**
	 * 物资编码
	 */
	materialCode?: null | string
	/**
	 * 物资ID
	 */
	materialId?: number | null
	/**
	 * 物资名称
	 */
	materialLabel?: null | string
	/**
	 * 技术参数
	 */
	technicalParameter?: null | string
	/**
	 * 库存单位
	 */
	useUnit?: number | null
	/**
	 * 规格型号
	 */
	version?: null | string
}
