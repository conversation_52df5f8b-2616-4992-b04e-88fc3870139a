/**
 * @description 退货申请 类型声明
 */

/**
 * MatOutStoreRemoveApplyDTO
 */
export interface MatOutStoreRemoveApplyDTO {
	/**
	 * 审批状态
	 */
	bpmStatus?: null | string
	/**
	 * 退货单号
	 */
	code?: null | string
	/**
	 * 申请人名称
	 */
	createdByName?: null | string
	/**
	 * ID
	 */
	id?: number | null
	/**
	 * 退货单名称
	 */
	label?: null | string
	/**
	 * 关联采购单号
	 */
	preBusinessCode?: null | string
	/**
	 * 退货原因
	 */
	reason?: null | string
	/**
	 * 部门
	 */
	sysOrgId?: number | null
	[property: string]: any
}

/**
 * 退货申请分页 请求参数
 */
export interface MatOutStoreRemoveApplyReqParams {
	/**
	 * 审批状态
	 */
	bpmStatus?: string
	/**
	 * 退货单号
	 */
	code?: string
	/**
	 * 申请人名称
	 */
	createdByName?: string
	currentPage?: number
	/**
	 * ID
	 */
	id?: number
	/**
	 * 退货单名称
	 */
	label?: string
	pageSize?: number
	/**
	 * 关联采购单号
	 */
	preBusinessCode?: string
	/**
	 * 退货原因
	 */
	reason?: string
	sidx?: string
	sord?: string
	/**
	 * 部门
	 */
	sysOrgId?: number
	[property: string]: any
}

/**
 * 退货申请 vo
 */
export interface MatOutStoreRemoveApplyVo {
	/**
	 * 退货金额
	 */
	amount?: number | null
	/**
	 * 审批状态
	 */
	bpmStatus?: null | string
	/**
	 * 退货单号
	 */
	code?: null | string
	/**
	 * 合同编号
	 */
	contractCode?: null | string
	/**
	 * 申请人(字典)
	 */
	createdBy?: null | string
	/**
	 * 申请时间
	 */
	createdDate?: null | string
	/**
	 * ID
	 */
	id?: number | null
	/**
	 * 退货单名称
	 */
	label?: null | string
	/**
	 * 物资编码数量
	 */
	materialCodeNum?: number | null
	/**
	 * 退货数量
	 */
	num?: number | null
	/**
	 * 关联采购单号
	 */
	preBusinessCode?: null | string
	/**
	 * 采购单号ID
	 */
	preBusinessId?: number | null
	/**
	 * 采购项目编号
	 */
	projectCode?: null | string
	/**
	 * 采购方式
	 */
	projectPurchaseType?: null | string
	/**
	 * 采购员名称
	 */
	purchaseUserName?: null | string
	/**
	 * 退货原因
	 */
	reason?: null | string
	/**
	 * 供货商名称
	 */
	supplierLabel?: null | string
	/**
	 * 申请部门
	 */
	sysOrgId?: number | null
	[property: string]: any
}

/**
 * 退货申请 物资 vo
 */
export interface MatOutStoreRemoveApplyItemVo {
	/**
	 * 库存单价
	 */
	amount?: number | null
	/**
	 * 批次号
	 */
	batchNo?: null | string
	/**
	 * 退货数量
	 */
	completeNum?: number | null
	/**
	 * ID
	 * id
	 */
	id?: number | null
	/**
	 * 物资编码
	 */
	materialCode?: null | string
	/**
	 * 编码名称
	 */
	materialName?: null | string
	/**
	 * 可退货数量
	 */
	num?: number | null
	/**
	 * 货位Id
	 */
	roomId?: null | string
	/**
	 * 货位名称
	 */
	roomName?: null | string
	/**
	 * 技术参数
	 */
	technicalParameter?: null | string
	/**
	 * 金额
	 */
	totalPrice?: number | null
	/**
	 * 库存单位
	 */
	useUnit?: number | null
	/**
	 * 规格型号
	 */
	version?: null | string
	[property: string]: any
}

/**
 * 退货申请-添加物资 dto
 *
 * MatOutStoreRemoveApplyItemAddDTO
 */
export interface MatOutStoreRemoveApplyItemAddDTO {
	/**
	 * 采购订单批次Id集合
	 */
	batchIdList?: any[]
	/**
	 * ID
	 * id
	 */
	id?: number | null
	[property: string]: any
}

/**
 * 退货申请-编辑物资 dto
 * MatReturnStoreApplyEditItemDTO
 */
export interface MatReturnStoreApplyEditItemDTO {
	/**
	 * 申请单Id
	 */
	applyId?: number | null
	/**
	 * 申请单Id
	 * 明细列表
	 */
	itemList?: MatReturnStoreApplyEditItemListDTO[] | null
}

/**
 * MatReturnStoreApplyEditItemListDTO
 */
export interface MatReturnStoreApplyEditItemListDTO {
	/**
	 * 退库数量
	 */
	completeNum?: number | null
	/**
	 * ID
	 * id
	 */
	id?: number | null
}

/**
 * 退货申请明细分页 请求参数
 */
export interface MatOutStoreRemoveApplyItemReqParams {
	/**
	 * 申请单Id
	 */
	applyId?: number
	currentPage?: number
	/**
	 * ID
	 * id
	 */
	id?: number
	pageSize?: number
	sidx?: string
	sord?: string
	[property: string]: any
}
