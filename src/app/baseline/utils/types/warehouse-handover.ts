/**
 * @description 入库管理-交旧入库 TS
 */

/**
 * 交旧入库分页 入参
 * MatInStoreWasteOldApplyPageVo
 */
export interface MatInStoreWasteOldApplyPageVoRequest {
	/**
	 * 交旧入库单号
	 */
	code?: string
	currentPage?: number
	pageSize?: number
	sidx?: string
	sord?: string
	/**
	 * 仓库ID
	 */
	storeId?: number
	/**
	 * 入库类型
	 */
	type?: string
	/**
	 * 交旧申请单号
	 */
	wasteOldApplyCode?: string
	/**
	 * 交旧申请单类型
	 */
	wasteOldApplyType?: string
	[property: string]: any
}

/**
 * 交旧入库分页 返回参数
 * MatInStoreWasteOldApplyPageVo
 */
export interface MatInStoreWasteOldApplyPageVo {
	/**
	 * 申请单号
	 */
	code?: null | string
	/**
	 * 申请人ID
	 */
	createdBy?: null | string
	/**
	 * 申请时间
	 */
	createdDate?: null | string
	/**
	 * ID
	 */
	id?: number | null
	/**
	 * 申请单名称
	 */
	label?: null | string
	/**
	 * 物资编码数量
	 */
	materialCodeNum?: number | null
	/**
	 * 前置业务ID
	 */
	preBusinessId?: number | null
	/**
	 * 前置业务类型
	 */
	preBusinessType?: null | string
	/**
	 * 入库Id
	 */
	storeId?: number | null
	/**
	 * 入库仓库
	 */
	storeName?: null | string
	/**
	 * 公司ID
	 */
	sysCommunityId?: number | null
	/**
	 * 入库类型
	 */
	type?: null | string
	/**
	 * 交旧申请编码
	 */
	wasteOldApplyCode?: null | string
	[property: string]: any
}

/**
 * 交旧入库详情 返回数据
 *
 * MatInStoreWasteOldApplyDetailVo
 */
export interface MatInStoreWasteOldApplyDetailVo {
	/**
	 * 申请单号
	 */
	code?: null | string
	/**
	 * 申请人ID
	 */
	createdBy?: null | string
	/**
	 * 申请时间
	 */
	createdDate?: null | string
	/**
	 * ID
	 */
	id?: number | null
	/**
	 * 申请单名称
	 */
	label?: null | string
	/**
	 * 物资编码数量
	 */
	materialCodeNum?: number | null
	/**
	 * 入库Id
	 */
	storeId?: number | null
	/**
	 * 入库仓库
	 */
	storeName?: null | string
	/**
	 * 申请部门
	 */
	sysCommunityId?: number | null
	/**
	 * 入库类型
	 */
	type?: null | string
	/**
	 * 交旧申请单号
	 */
	wasteOldApplyCode?: null | string
	/**
	 * 交旧申请单类型
	 */
	wasteOldApplyType?: null | string
	[property: string]: any
}

/**
 * 交旧入库 明细  入参
 * MatInStoreWasteOldApplyItemPageVoRequest
 */

export interface MatInStoreWasteOldApplyItemPageVoRequest {
	/**
	 * 申请ID
	 */
	applyId?: number
	currentPage?: number
	/**
	 * 物资编码
	 */
	materialCode?: string
	/**
	 * material_id
	 */
	materialId?: number
	/**
	 * 编码名称
	 */
	materialName?: string
	/**
	 * 数量
	 */
	num?: string
	pageSize?: number
	sidx?: string
	sord?: string
	[property: string]: any
}

/**
 * 交旧入库 明细  返回数据
 * MatInStoreWasteOldApplyItemPageVo
 */
export interface MatInStoreWasteOldApplyItemPageVo {
	/**
	 * 申请ID
	 */
	applyId?: number | null
	/**
	 * 可入库数量
	 */
	canInStoreNum?: number | null
	/**
	 * 已入库数量
	 */
	completeInStoreNum?: number | null
	/**
	 * id
	 */
	id?: number | null
	/**
	 * 入库状态
	 */
	inStoreStatus?: null | string
	/**
	 * 物资编码
	 */
	materialCode?: null | string
	/**
	 * 物资id
	 */
	materialId?: number | null
	/**
	 * 编码名称
	 */
	materialName?: null | string
	/**
	 * 主要材质
	 */
	quality?: null | string
	/**
	 * 预估回收重量
	 */
	recoveryWeight?: number | null
	/**
	 * 入库货位数量
	 */
	roomCountNum?: number | null
	/**
	 * 技术参数
	 */
	technicalParameter?: null | string
	/**
	 * 库存单位
	 */
	useUnit?: number | null
	/**
	 * 规格型号
	 */
	version?: null | string
	/**
	 * 待入库数量
	 */
	waitingInStoreNum?: number | null
	/**
	 * 废旧物资类型
	 */
	wasteMaterialType?: null | string
	[property: string]: any
}

/**
 * 交旧入库 - 入库信息 参数
 *
 * MatInStoreReceiveItemDTO
 */
export interface MatInStoreReceiveItemDTO {
	/**
	 * 接收入库ID
	 */
	applyId?: number | null
	/**
	 * 本次入库数量
	 */
	completeNum?: number | null
	/**
	 * ID
	 */
	id?: number | null
	/**
	 * 明细Id
	 */
	itemId?: number | null
	/**
	 * 物资编码
	 */
	materialCode?: null | string
	/**
	 * 物资名称
	 */
	materialName?: null | string
	parameterMap?: MapString
	/**
	 * 入库区域
	 */
	regionId?: number | null
	/**
	 * 状态说明
	 */
	remark?: null | string
	/**
	 * 入库货位
	 */
	roomId?: number | null
	/**
	 * 状态
	 */
	status?: null | string
	/**
	 * 入库仓库
	 */
	storeId?: number | null
	/**
	 * 质保期
	 */
	validityPeriod?: null | string
	[property: string]: any
}

/**
 * Map«String[]»
 */
export interface MapString {
	key?: string[] | null
	[property: string]: any
}

/**
 * 明细 -> 查看货位 返回参数
 * MatInStoreWasteOldItemRoomPageVoRequest
 */
export interface MatInStoreWasteOldItemRoomPageVoRequest {
	currentPage?: number
	/**
	 * 交旧申请明细id
	 */
	itemId: number
	pageSize?: number
	sidx?: string
	sord?: string
	[property: string]: any
}

/**
 * 明细 -> 查看货位 返回参数
 * MatInStoreWasteOldItemRoomPageVo
 */
export interface MatInStoreWasteOldItemRoomPageVo {
	/**
	 * 申请人
	 */
	createBy?: null | string
	/**
	 * id
	 */
	id?: number | null
	/**
	 * 入库时间
	 */
	inStoreTime?: null | string
	/**
	 * 数量
	 */
	num?: number | null
	/**
	 * 货位编号
	 */
	roomCode?: null | string
	/**
	 * 货位id
	 */
	roomId?: number | null
	/**
	 * 质保期
	 */
	validityPeriod?: null | string
	[property: string]: any
}
