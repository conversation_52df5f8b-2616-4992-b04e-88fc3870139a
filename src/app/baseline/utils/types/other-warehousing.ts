/**
 * 其他入库申请 vo
 *
 * MatInStoreOtherApplyVo
 */
export interface MatInStoreOtherApplyVo {
	/**
	 * 入库金额
	 */
	amount?: number | null
	/**
	 * 审批状态
	 */
	bpmStatus?: null | string
	/**
	 * 申请单号
	 */
	code?: null | string
	/**
	 * 申请人ID
	 */
	createBy?: null | string
	/**
	 * 申请时间
	 */
	createdDate?: null | string
	/**
	 * ID
	 */
	id?: number | null
	/**
	 * 申请单名称
	 */
	label?: null | string
	/**
	 * 物资编码数量
	 */
	materialCodeNum?: number | null
	/**
	 * 入库原因说明
	 */
	reason?: null | string
	/**
	 * 入库Id
	 */
	storeId?: number | null
	/**
	 * 入库仓库
	 */
	storeName?: null | string
	/**
	 * 公司ID
	 */
	sysCommunityId?: number | null
	/**
	 * 入库总量
	 */
	totalNum?: number | null
	/**
	 * 入库类型
	 */
	type?: null | string
	[property: string]: any
}
/**
 * 其他入库申请 dto
 * MatInStoreOtherApplyDTO
 */
export interface MatInStoreOtherApplyDTO {
	/**
	 * 申请人ID
	 */
	createBy?: number | null
	/**
	 * 创建人姓名
	 */
	createdName?: null | string
	/**
	 * ID
	 */
	id?: number | null
	/**
	 * 其他入库名称
	 */
	label?: null | string
	/**
	 * 申请原因
	 */
	reason?: null | string
	/**
	 * 状态 1是待处理 2 是已完成
	 */
	status?: null | string
	/**
	 * 仓库ID
	 */
	storeId?: number | null
	/**
	 * 仓库ID
	 */
	storeInId?: number | null
	/**
	 * 公司ID
	 */
	sysCommunityId?: number | null
	/**
	 * 入库类型
	 */
	type?: null | string
	[property: string]: any
}

/**
 *
 * 其他入库物资 dto
 * MatInStoreOtherApplyItemDTO
 */
export interface MatInStoreOtherApplyItemDTO {
	/**
	 * 金额
	 */
	amount?: number | null
	/**
	 * 申请ID
	 */
	applyId?: number | null
	/**
	 * 完成数量
	 */
	completeNum?: number | null
	/**
	 * ID
	 * id
	 */
	id?: number | null
	/**
	 * 质检员
	 */
	inspectionPersonId?: null | string
	/**
	 * 物资编码
	 */
	materialCode?: null | string
	/**
	 * 物资ID
	 * material_id
	 */
	materialId?: number | null
	/**
	 * 编码名称
	 */
	materialName?: null | string
	/**
	 * 数量
	 */
	num?: number | null
	/**
	 * 技术参数
	 */
	technicalParameter?: null | string
	/**
	 * 库存单位
	 */
	useUnit?: number | null
	/**
	 * 规格型号
	 */
	version?: null | string
	[property: string]: any
}

/**
 * 其他入库 物资 vo
 *
 * MatInStoreOtherApplyItemVo
 */
export interface MatInStoreOtherApplyItemVo {
	/**
	 * 单价
	 */
	amount?: number | null
	/**
	 * 申请ID
	 */
	applyId?: number | null
	/**
	 * 返修数量
	 */
	completeNum?: number | null
	/**
	 * ID
	 * id
	 */
	id?: number | null
	/**
	 * 质检员
	 */
	inspectionPersonId?: null | string
	/**
	 * 物资编码
	 */
	materialCode?: null | string
	/**
	 * 物资ID
	 * material_id
	 */
	materialId?: number | null
	/**
	 * 编码名称
	 */
	materialName?: null | string
	/**
	 * 数量
	 */
	num?: number | null
	/**
	 * 技术参数
	 */
	technicalParameter?: null | string
	/**
	 * 库存单位
	 */
	useUnit?: number | null
	/**
	 * 规格型号
	 */
	version?: null | string
	[property: string]: any
}

/**
 * MatInStoreOtherApplyItemAddDTO
 */
export interface MatInStoreOtherApplyItemAddDTO {
	/**
	 * 业务主表id
	 */
	applyId?: number | null
	/**
	 * 物资ID
	 * 物资ID集合
	 */
	materialIdList?: number[] | null
	[property: string]: any
}

/**
 * MatInStoreApplyInspectDTO
 */
export interface MatInStoreApplyInspectDTO {
	/**
	 * 明细id
	 */
	itemIds: number[] | null
	/**
	 * 用户id
	 */
	userId: null | string
}
