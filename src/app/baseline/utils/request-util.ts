import { AxiosRequestConfig } from "axios"

/**
 * 请求工具类
 */
export default class ReqUtil {
	static post<R>(path: string, config?: AxiosRequestConfig) {
		return request<R>({
			url: path,
			method: "post",
			...config
		})
	}

	static delete<R>(
		path: string,

		config?: AxiosRequestConfig
	) {
		return request<R>({
			url: path,
			method: "delete",
			...config
		})
	}

	static put<R>(
		path: string,

		config?: AxiosRequestConfig
	) {
		return request<R>({
			url: path,
			method: "put",
			...config
		})
	}

	static patch<R>(
		path: string,

		config?: AxiosRequestConfig
	) {
		return request<R>({
			url: path,
			method: "patch",
			...config
		})
	}

	static get<R>(path: string, config?: AxiosRequestConfig) {
		return request<R>({
			url: path,
			method: "get",
			...config
		})
	}
}
