import { useUserStore } from "@/app/platform/store/modules/user"
import { appStatus } from "@/app/baseline/api/dict"
import { IDictVo, IModalType } from "./types/common"
import {
	find,
	flowRight,
	get,
	isNil,
	map,
	now,
	padStart,
	round,
	toNumber
} from "lodash-es"
import XEUtils from "xe-utils"

/**
 * 字节数转换，默认显示M，小于0.1M时 显示K
 * @param bytes
 */
export const convertBytesToMegabytes = (bytes: any) => {
	if (bytes) {
		const megabytes = bytes / (1024 * 1024) // 转换为兆字节
		if (megabytes < 0.1) {
			return (bytes / 1024).toFixed(2) + " K" // 转换为千字节
		}
		return megabytes.toFixed(2) + " M" // 保留两位小数
	} else {
		return "---"
	}
}
/**
 * 将对象的content对象转换为主对象
 * @param tableData
 */
export const convertContentTableData = (
	tableData: any[],
	extFields: any[] = []
) => {
	const lastTableData: any[] = []
	tableData.forEach((item) => {
		if (item.hasOwnProperty("content") && item.content) {
			const lastItem = item.content
			lastItem.contentId = item.content.id
			lastItem.id = item.id //这个不能错

			extFields.forEach((f) => {
				lastItem[f] = item[f]
			})
			lastTableData.push(lastItem)
		}
	})
	return lastTableData
}
/**
 * 用户是否有编辑权限
 * @param user
 */
export const hasPermi = (user: string) => {
	const userStore = useUserStore()
	const { userInfo } = storeToRefs(userStore)
	return user === userInfo.value.userName
}
/**
 * 用户和数据是否有编辑权限
 * @param user
 */
export const hasEditByBpm = (bpmStatus: any, user: string) => {
	return (
		[appStatus.pendingApproval, appStatus.rejected].includes(bpmStatus) &&
		hasPermi(user)
	)
}
/**
 * 数据是否有查看权限
 * @param user
 */
export const hasViewByBpm = (bpmStatus: any) => {
	return ![appStatus.pendingApproval].includes(bpmStatus)
}

export const getOnlyDate = (dateTime: string) => {
	if (dateTime) {
		const arrDate = dateTime.split(" ")
		if (arrDate.length > 0) {
			return arrDate[0]
		}
	}
	return null
}

/**
 * modal 的类型 label
 * @param mode modal 类型
 */
export const getModalTypeLabel = (mode: IModalType, suffix?: string) => {
	const label =
		{
			[IModalType.create]: "新建",
			[IModalType.edit]: "编辑",
			[IModalType.view]: "查看"
		}[mode] ?? ""

	return label + suffix
}

/**
 * 根据 dict value 获取 dict label
 *
 * @param dicts 字典列表
 * @param value 字典值
 */
export const getDictLabelFromValue = (dicts: IDictVo[], value: any) => {
	const ls = find(dicts, (v) => v.subitemValue === value)
	return get(ls, "subitemName")
}

/**
 * 获取数字默认值，通过筛选 num 结尾的字段
 *
 * @param key 字段
 * @param val 对应值
 * @param pattern 自定义匹配规则
 */
export const getNumDefByNumKey = (key: string, val: any, pattern?: RegExp) => {
	const reg = pattern ?? /num$/i

	if (reg.test(key)) {
		// 判断是否为 num 结尾的字段

		return val ?? 0
	}

	return val ?? "---"
}

/**
 * 获取数字默认值，通过筛选 num 结尾的字段 (判断是否需要 格式化)
 *
 * @param key 字段
 * @param val 对应值
 * @param pattern 自定义匹配规则
 */
export const isFormatterNum = (key: string, val: any, pattern?: RegExp) => {
	const reg = pattern ?? /num$/i

	return reg.test(key)
}

/**
 * 批量格式化 num 结尾的字段 *num_view
 * @param data
 */
export const batchFormatterNumView = (
	data: Record<string, any>[],
	pattern?: RegExp,
	digits?: number
) => {
	const reg = pattern ?? /num$/i
	map(data, (item: Record<string, any>) => {
		Object.keys(item).forEach((key) => {
			if (reg.test(key)) {
				// 字段名称以 Num结尾的字段，格式化千分符
				item[`${key + "_view"}`] = toFixedTwo(
					item[key],
					digits
				) as unknown as number
			}
		})
	})
}

/**
 * 设置 input 默认数字值为 '0'
 *
 * @param rawVal 原始值
 */
export const setInputDefNumVal = (rawVal?: number | string | null) => {
	return rawVal === undefined ||
		rawVal === null ||
		rawVal === "" ||
		rawVal === 0
		? "0"
		: rawVal
}

/**
 * 金额转化
 */
export const toMoney = (val?: number | null) => {
	return isNil(val)
		? "￥0.00000"
		: `￥${XEUtils.commafy(toNumber(val), { digits: 5 })}`
}

/**
 * 保留两位小数
 */
export const toFixedTwo = (val?: number | null, digits = 4) => {
	return isNil(val)
		? digits == 4
			? "0.0000"
			: "0"
		: `${XEUtils.commafy(toNumber(val), { digits: digits })}`
}

/**
 * table col 过滤器
 */
export const tableColFilter = (
	cols: TableColumnType[],
	excludeCols: string[]
) => {
	return cols.filter((c) => !excludeCols.includes(c.label))
}

/**
 * 单位转换 百 千 万亿
 */

export function convertToUnit(num: number) {
	if (num < 1000) {
		return { num: num, unit: "" }
	}

	const units = ["", "千", "万", "亿"]
	let exponent = Math.floor(Math.log(num) / Math.log(1000))

	// 防止越界
	exponent = Math.min(exponent, units.length - 1)

	return {
		num: round(num / Math.pow(1000, exponent), 2),
		unit: units[exponent]
	}
}

/**
 * 抽屉 - 未有查询条件时 计算表格最大高度
 */
const windowH = ref(document.body.clientHeight)
window.onresize = () => {
	windowH.value = document.body.clientHeight
}
export const maxTableHeight = computed(() => {
	return windowH.value - 150
})

/**
 * 获取最近一个月的日期
 */
export function getLastMonthDate() {
	const now = new Date()
	const lastMonthDate = flowRight(
		// 将日期转换为字符串格式YYYY-MM-DD
		(date) => date.toISOString().split("T")[0],
		// 获取最近一个月的日期
		() => new Date(now.getFullYear(), now.getMonth() - 1, now.getDate())
	)

	return lastMonthDate()
}

/**
 * 获取当前年的1月1日
 */
export function getCurentYearFirstDay() {
	const now = new Date()

	const currentYear = now.getFullYear()
	return `${currentYear}-01-01`
}

/**
 * 获取当前日期
 */
export function getNowDate() {
	const date = new Date(now())
	const formattedDate =
		padStart(date.getFullYear() as any, 4, "0") +
		"-" +
		padStart((date.getMonth() + 1) as any, 2, "0") +
		"-" +
		padStart(date.getDate() as any, 2, "0")

	return formattedDate
}
/**
 * 将秒数转换为时分秒
 * @param seconds 秒数
 * @returns 时分秒
 */
export const formatSecondsToTime = (seconds: number | string) => {
	if (!seconds && seconds !== 0) return ''
	const sec = Number(seconds)
	const h = Math.floor(sec / 3600)
	const m = Math.floor((sec % 3600) / 60)
	const s = sec % 60
	return [h, m, s].map(n => String(n).padStart(2, '0')).join(':')
}
