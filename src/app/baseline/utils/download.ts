export function downloadExcel(res: any, fileName: any) {
	const blob = new Blob([res], {
		type: "application/vnd.ms-excel;charset=utf-8"
	})
	const dom = document.createElement("a")
	const url = window.URL.createObjectURL(blob)
	dom.href = url
	dom.download = decodeURI(fileName)
	dom.style.display = "none"
	document.body.appendChild(dom)
	dom.click()
	dom.parentNode?.removeChild(dom)
	window.URL.revokeObjectURL(url)
}

export function downloadTxt(txt: string, fileName: string) {
	// 定义你想要导出的内容
	const content = txt

	// 创建一个新的 Blob 对象，类型为 'text/plain' 表示纯文本
	const blob = new Blob([content], { type: "text/plain" })

	// 创建一个 URL 对象，指向刚刚创建的 Blob
	const url = URL.createObjectURL(blob)

	// 创建一个 <a> 标签用于触发下载
	const a = document.createElement("a")
	a.href = url
	a.download = `${fileName}.txt` // 设置下载的文件名

	// 将 <a> 标签添加到 DOM 中，然后立即触发点击事件
	document.body.appendChild(a)
	a.click()

	// 触发点击后移除 <a> 标签，并释放 URL 对象
	document.body.removeChild(a)
	URL.revokeObjectURL(url)
}
