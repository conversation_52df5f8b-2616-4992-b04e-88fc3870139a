/**
 * 下载 PDF
 * @param txt
 * @param fileName
 */
export function downloadPDF(txt: string, fileName: string) {
	// 定义你想要导出的内容
	const content = txt

	// 创建一个新的 Blob 对象，类型为 'text/plain' 表示纯文本
	const blob = new Blob([content], { type: "application/pdf" })

	// 创建一个 URL 对象，指向刚刚创建的 Blob
	const url = URL.createObjectURL(blob)

	// 创建一个 <a> 标签用于触发下载
	const a = document.createElement("a")
	a.href = url
	a.download = `${fileName}.pdf` // 设置下载的文件名

	// 将 <a> 标签添加到 DOM 中，然后立即触发点击事件
	document.body.appendChild(a)
	a.click()

	// 触发点击后移除 <a> 标签，并释放 URL 对象
	document.body.removeChild(a)
	URL.revokeObjectURL(url)
}
