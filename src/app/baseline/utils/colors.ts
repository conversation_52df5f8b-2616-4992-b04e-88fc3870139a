import {
	IFinancialPostStatus,
	IInventoryPlanResultStatus,
	IInventoryPlanStatus,
	IInventoryResultType,
	IInventoryStatus,
	IInventoryTaskCheckStatus,
	IInventoryTaskStatus,
	IInventoryType
} from "./types/inventory-manage"
import {
	ILowValueInventoryCheckStatus,
	ILowValueInventoryType
} from "./types/lowValue-inventory-manage"
import { IWarehouseType } from "./types/store-manage"

/**
 * tag 颜色主题
 */
export const tagColorTheme = {
	success: "#4BAE89",
	successLight: "rgba(75, 174, 137, 0.1)",

	warning: "#F59B22",
	warningLight: "rgba(245, 155, 34, 0.1)",

	danger: "#E25E59",
	dangerLight: "rgba(226, 94, 89, 0.1)",

	info: "rgb(18, 136, 255)",
	infoLight: "rgba(18, 136, 255, 0.1)",

	purple: "rgb(140, 92, 209)",
	purpleLight: "rgb(230, 220, 246)"
}

/**
 * 仓库类型 tag 色池
 */
export const warehouseTypeTagColorMap = {
	/**
	 * rotablesNormal
	 */
	[IWarehouseType.rotablesNormal]: "rgb(38,175,174)",
	/**
	 * 标准库
	 */
	[IWarehouseType.default]: "rgb(18,136,255)",
	/**
	 * 废旧件库
	 */
	[IWarehouseType.waste]: "#F59B22",
	/**
	 * 备品备件库
	 */
	[IWarehouseType.spareParts]: tagColorTheme.success,

	/**
	 * 周转件待修库
	 */
	[IWarehouseType.rotablesWaitingRepair]: tagColorTheme.danger,

	/**
	 * 危险品仓库
	 */
	[IWarehouseType.dangerousWarehouse]: tagColorTheme.danger,

	/**
	 * 危险备件库
	 */
	[IWarehouseType.sparePartsWarehouse]: tagColorTheme.danger,

	/**
	 * 危险废旧库
	 */
	[IWarehouseType.dangerousWaste]: tagColorTheme.danger,

	[IWarehouseType.physicalObject]: tagColorTheme.danger
}

/**
 * 盘点类型颜色池
 */
export const inventoryCheckTypeColorConf = {
	[IInventoryType.all]: {
		borderColor: tagColorTheme.success,
		bgColor: tagColorTheme.successLight,
		color: tagColorTheme.success
	},
	[IInventoryType.part]: {
		borderColor: tagColorTheme.danger,
		bgColor: tagColorTheme.dangerLight,
		color: tagColorTheme.danger
	},
	[IInventoryType.special]: {
		borderColor: tagColorTheme.warning,
		bgColor: tagColorTheme.warningLight,
		color: tagColorTheme.warning
	},
	[IInventoryType.trends]: {
		borderColor: tagColorTheme.purple,
		bgColor: tagColorTheme.purpleLight,
		color: tagColorTheme.purple
	},
	[IInventoryType.circulate]: {
		borderColor: tagColorTheme.info,
		bgColor: tagColorTheme.infoLight,
		color: tagColorTheme.info
	}
}

/**
 * 库存 - 盘点计划 - 盘点结果 正常/异常
 */
export const IInventoryPlanResultStatusColorConf = {
	[IInventoryPlanResultStatus.normal]: {
		borderColor: tagColorTheme.success,
		bgColor: tagColorTheme.successLight,
		color: tagColorTheme.success
	},
	[IInventoryPlanResultStatus.danger]: {
		borderColor: tagColorTheme.danger,
		bgColor: tagColorTheme.dangerLight,
		color: tagColorTheme.danger
	}
}

/**
 * 盘点状态颜色池
 */
export const inventoryCheckStatusColorConf = {
	[IInventoryStatus.progress]: {
		borderColor: tagColorTheme.success,
		bgColor: tagColorTheme.successLight,
		color: tagColorTheme.success
	},
	[IInventoryStatus.completed]: {
		borderColor: tagColorTheme.info,
		bgColor: tagColorTheme.infoLight,
		color: tagColorTheme.info
	},
	[IInventoryStatus.notStart]: {
		borderColor: tagColorTheme.warning,
		bgColor: tagColorTheme.warningLight,
		color: tagColorTheme.warning
	},
	[IInventoryStatus.error]: {
		borderColor: tagColorTheme.danger,
		bgColor: tagColorTheme.dangerLight,
		color: tagColorTheme.danger
	}
}

/**
 * 盘点-财务过账类型 颜色池
 */
export const financialPostStatusColorConf = {
	[IFinancialPostStatus.unPost]: {
		borderColor: tagColorTheme.warning,
		bgColor: tagColorTheme.warningLight,
		color: tagColorTheme.warning
	},
	[IFinancialPostStatus.posted]: {
		borderColor: tagColorTheme.success,
		bgColor: tagColorTheme.successLight,
		color: tagColorTheme.success
	}
}

/**
 * 盘点-盘点结果 颜色池
 */
export const inventoryResultColorConf = {
	[IInventoryResultType.profitable]: {
		borderColor: tagColorTheme.success,
		bgColor: tagColorTheme.successLight,
		color: tagColorTheme.success
	},
	[IInventoryResultType.unprofitable]: {
		borderColor: tagColorTheme.danger,
		bgColor: tagColorTheme.dangerLight,
		color: tagColorTheme.danger
	},
	[IInventoryResultType.normal]: {
		borderColor: tagColorTheme.info,
		bgColor: tagColorTheme.infoLight,
		color: tagColorTheme.info
	}
}

/**
 * 盘点计划 盘点状态
 */
export const inventoryPlanStatusColorConf = {
	[IInventoryPlanStatus.progress]: {
		borderColor: tagColorTheme.success,
		bgColor: tagColorTheme.successLight,
		color: tagColorTheme.success
	},
	[IInventoryPlanStatus.completed]: {
		borderColor: tagColorTheme.info,
		bgColor: tagColorTheme.infoLight,
		color: tagColorTheme.info
	},
	[IInventoryPlanStatus.notStart]: {
		borderColor: tagColorTheme.warning,
		bgColor: tagColorTheme.warningLight,
		color: tagColorTheme.warning
	}
}

/**
 * 盘点任务 阶段 颜色池
 */
export const inventoryTaskStatusColorConf = {
	[IInventoryTaskStatus.firstTask]: {
		borderColor: tagColorTheme.warning,
		bgColor: tagColorTheme.warningLight,
		color: tagColorTheme.warning
	},
	[IInventoryTaskStatus.secondTask]: {
		borderColor: tagColorTheme.success,
		bgColor: tagColorTheme.successLight,
		color: tagColorTheme.success
	},
	[IInventoryTaskStatus.completed]: {
		borderColor: tagColorTheme.info,
		bgColor: tagColorTheme.infoLight,
		color: tagColorTheme.info
	}
}

/**
 * 盘点任务中 盘点人员 盘点状态
 */
export const inventoryTaskCheckStatusColorConf = {
	[IInventoryTaskCheckStatus.progress]: {
		borderColor: tagColorTheme.success,
		bgColor: tagColorTheme.successLight,
		color: tagColorTheme.success
	},
	[IInventoryTaskCheckStatus.completed]: {
		borderColor: tagColorTheme.info,
		bgColor: tagColorTheme.infoLight,
		color: tagColorTheme.info
	},
	[IInventoryTaskCheckStatus.notStart]: {
		borderColor: tagColorTheme.warning,
		bgColor: tagColorTheme.warningLight,
		color: tagColorTheme.warning
	}
}

/**
 * 低值 - 盘点类型颜色池
 */
export const lowValueInventoryCheckTypeColorConf = {
	[ILowValueInventoryType.year]: {
		borderColor: tagColorTheme.success,
		bgColor: tagColorTheme.successLight,
		color: tagColorTheme.success
	},
	[ILowValueInventoryType.quarter]: {
		borderColor: tagColorTheme.danger,
		bgColor: tagColorTheme.dangerLight,
		color: tagColorTheme.danger
	},
	[ILowValueInventoryType.sample]: {
		borderColor: tagColorTheme.warning,
		bgColor: tagColorTheme.warningLight,
		color: tagColorTheme.warning
	}
}

/**
 * 低值 - 盘点状态颜色池 正常/异常
 */
export const lowValueInventoryCheckStatusColorConf = {
	[ILowValueInventoryCheckStatus.default]: {
		borderColor: tagColorTheme.success,
		bgColor: tagColorTheme.successLight,
		color: tagColorTheme.success
	},
	[ILowValueInventoryCheckStatus.danger]: {
		borderColor: tagColorTheme.danger,
		bgColor: tagColorTheme.dangerLight,
		color: tagColorTheme.danger
	}
}
