import SockJS from "sockjs-client/dist/sockjs.min.js"
import { CompatClient, Stomp } from "@stomp/stompjs"

// 请求地址
const baseUrl = import.meta.env.VITE_APP_BASE_WS

export class SocketUtils {
	callback?: (params?: any) => any

	stompClient?: CompatClient

	url?: string

	headers?: any

	//构造函数
	constructor() {
		this.callback = undefined // 回调函数
		this.stompClient == undefined
		this.url = undefined
		this.headers = undefined
	}

	/** socket连接 subscribeEvent*/
	subscribeEvent(headers = {}, url?: string, callback?: any) {
		if (this.stompClient) {
			try {
				this.stompClient.disconnect()
			} catch (e) {
				console.log(e)
			}
		}
		//连接SockJS
		// 获取STOMP子协议的客户端对象
		this.stompClient = Stomp.over(() => {
			return new SockJS(baseUrl)
		})

		this.url = url

		if (headers) {
			this.headers = headers
		}
		if (callback) {
			this.callback = callback
		}
		this.stompClient.heartbeat.outgoing = 5000
		this.stompClient.heartbeat.incoming = 0
		this.stompClient.reconnect_delay = 10000
		//日志不打印
		this.stompClient.debug = (res) => {
			console.debug(res + "\n")
		}
		this.stompClient.connect(
			headers,
			() => {
				this.stompClient?.subscribe(this.url!, (response) => {
					console.info("接收到消息--", this.url, response)
					callback("success", response)
				})
			},
			(err: any) => {
				console.info("连接失败", err)
			}
		)
	}
	destroy() {
		this.stompClient?.disconnect()
		this.stompClient = undefined
	}
}
