import { FormItemRule } from "element-plus"
import { toNumber } from "lodash-es"
import {
	IIdempotentTokenType,
	IIdempotentTokenTypeAction,
	IIdempotentTokenTypePre
} from "./types/common"

/**
 * 设置数值最大值
 */
export const maxValidateNum = toNumber(1000000000)

export const maxValidateErrorInfo = "十亿"
export const validateMatCode = (rule: any, value: any, callback: any) => {
	const reg = /^\d{2}$/ // 使用正则表达式匹配最多 8 位数字
	if (!reg.test(value)) {
		return callback(new Error("必须为2位数字"))
	}
	callback()
}
export const validateNumber = (rule: any, value: any, callback: any) => {
	if (!rule.required && !value) {
		callback()
	} else {
		const reg = /^(\d+|\d+\.\d{1,4})$/ // 正则表达式，匹配整数或带两位小数的数字
		if (!reg.test(value)) {
			return callback(new Error("请输入正确数字，最多4位小数"))
		}
		callback()
	}
}

/**
 * 较验 数值是否符合 最多4位小数
 * @param val
 * @returns
 */
export const isValidateNumber = (val: string) => {
	const reg = /^(\d+|\d+\.\d{1,4})$/ // 正则表达式，匹配整数或带两位小数的数字
	return reg.test(val)
}

export const validatePrice = (rule: any, value: any, callback: any) => {
	if (!rule.required && !value) {
		callback()
	} else {
		const reg = /^(\d+|\d+\.\d{1,5})$/ // 正则表达式，匹配整数或带两位小数的数字
		if (!reg.test(value)) {
			return callback(new Error("请输入正确数字，最多5位小数"))
		}
		callback()
	}
}

/**
 * 判断是否为整数
 * @param val
 * @returns
 */
export const isValidateInt = (val: string) => {
	const reg = /^(\d+)$/ // 正则表达式，匹配整数
	return reg.test(val)
}
export const validateInt = (rule: any, value: any, callback: any) => {
	if (!rule.required && !value) {
		callback()
	} else {
		const reg = /^(\d+)$/ // 正则表达式
		if (!reg.test(value)) {
			return callback(new Error("请输入正确数字"))
		}
		callback()
	}
}
export const validatePhone = (rule: any, value: any, callback: any) => {
	if (!rule.required && !value) {
		callback()
	} else {
		const reg = /^1[3456789]\d{9}$/
		if (!reg.test(value)) {
			return callback(new Error("手机号码格式不正确"))
		}
		callback()
	}
}
export const validateEmail = (rule: any, value: any, callback: any) => {
	if (!rule.required && !value) {
		callback()
	} else {
		const reg = /^[A-Za-z0-9\u4e00-\u9fa5]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/
		if (!reg.test(value)) {
			return callback(new Error("邮箱格式不正确"))
		}
		callback()
	}
}

/**
 * 非空校验器
 */
export const requiredValidator = (label: string): FormItemRule => {
	return {
		required: true,
		message: label + "不能为空",
		trigger: "change"
	}
}

/**
 * input number类型 过滤 ["e", "E", "+", "-"]
 * @param e
 * @returns
 */
export const inputLimit = (e: any) => {
	const key = e.key
	const notAllowList = ["e", "E", "+", "-"]

	if (notAllowList.includes(key)) {
		e.returnValue = false
		return false
	}

	return true
}

/**
 * 获取字符串实际长度（字符串），返回字符串实际长度
 * @param str
 * @returns
 */
export function getRealLength(str?: string) {
	if (str == null || str == undefined || str == "") {
		return 0
	}
	let len = 0
	const strLen = str.length

	for (let i = 0; i < strLen; i++) {
		const chartCode = str.charCodeAt(i)
		if (chartCode >= 0 && chartCode <= 128) {
			len += 1
		} else {
			len += 2
		}
	}

	return len
}

/**
 * 截取字符串，返回字符串截取后的字符串
 * @param str
 * @param len
 * @returns
 */
export function setString(str: string, len: number) {
	let strLen = 0
	let s = ""
	for (let i = 0; i < str.length; i++) {
		if (str.charCodeAt(i) > 128) {
			strLen += 2
		} else {
			strLen++
		}
		s += str.charAt(i)

		if (strLen >= len) {
			return s + "..."
		}
	}

	return s
}

export function validateAndCorrectInput(value: any, precision?: any) {
	// 去除非法字符，仅保留数字、小数点和负号
	let correctedValue = value.replace(/[^0-9.-]/g, "")

	// 如果负号不在第一个字符位置，则删除多余的负号
	if (correctedValue.indexOf("-") > 0) {
		correctedValue = correctedValue.replace(/-/g, "")
	}

	// 处理多个小数点的情况，只保留第一个小数点
	if (correctedValue.indexOf(".") !== -1) {
		const parts = correctedValue.split(".")
		correctedValue = parts[0] + "." + parts.slice(1).join("").replace(/\./g, "")
	}

	if (/^00+/.test(correctedValue)) {
		correctedValue = correctedValue.replace(/^00+/, "0")
	}

	// 限制小数点前最多10位数字
	const match =
		precision === 0
			? correctedValue.match(/^-?\d{0,10}(\.\d{0})?/)
			: precision === 5
			? correctedValue.match(/^-?\d{0,10}(\.\d{0,5})?/)
			: correctedValue.match(/^-?\d{0,10}(\.\d{0,4})?/)
	correctedValue = match ? match[0] : ""

	console.log("validateAndCorrectInput", value, correctedValue)
	return correctedValue
}

/**
 * 过滤多个空格
 * @param str
 * @returns
 */
export function filterMultipleSpaces(str: string) {
	return str.replace(/\s+/g, " ")
}

/**
 * 获取时间戳
 * @returns
 */
export function getTimestamp() {
	return Date.now()
}

/**
 * 前端生成 idempotentToken
 * 表单: 表单前缀(APPLY)+业务缩写(CK_LL_SQ)+时间戳,时间戳60秒过期 手动redis实现(防止网络抖动造成的重复请求);
 * 明细: 业务前缀(OTHER)+业务缩写(CK_LL_SQ)+表单ID redisson锁实现;
 * 推送入库,出入库,后置业务数据,更改状态,提交审批:业务前缀(OTHER)+业务缩写(CK_LL_SQ)+表单ID redisson锁实现
 * 批量操作单据(例:领料-批量关闭):批量前缀(BATCH)+业务类型+操作类型;添加业务校验,需要验证;
 */
export function getIdempotentToken(
	tokenPre: IIdempotentTokenTypePre,
	businessType: IIdempotentTokenType,
	businessId?: any,
	actionType?: IIdempotentTokenTypeAction
) {
	console.log("tokenPre", tokenPre)
	console.log("businessType", businessType)
	console.log("businessId", businessId)
	console.log("actionType", actionType)
	if (tokenPre === IIdempotentTokenTypePre.apply && businessType) {
		return `${tokenPre}-${businessType}-${getTimestamp()}`
	}

	if (
		tokenPre === IIdempotentTokenTypePre.other &&
		businessType &&
		businessId
	) {
		return `${tokenPre}-${businessType}-${businessId}`
	}

	if (
		tokenPre === IIdempotentTokenTypePre.batch &&
		businessType &&
		actionType
	) {
		return `${tokenPre}-${businessType}-${actionType}`
	}

	return ""
}
