<script setup lang="ts">
// 编码申请
import DetailViewer from "@/app/baseline/views/material/components/matApplyViewDrawer.vue"

const props = defineProps({
	formValue: {
		type: Object,
		default: () => {},
		requeired: false
	}
})
const formValue = toRef(props, "formValue")

const viewer = ref()

/* const props = defineProps({
	formValue: {
		type: Object,
		default: () => {},
		requeired: false
	}
})
const businessId = props.formValue.formData.businessId
const isView = toRef(props.formValue?.form ? false : true) */

//Inject (注入)
onMounted(() => {
	const { greet, updateGreet } = inject("greet") // 注意这里的 someProperty 应该是你期望解构的对象的属性名
	if (updateGreet) {
		updateGreet(
			() =>
				new Promise((resolve, reject) => {
					viewer.value
						.saveMatProcureInfo()
						.then(() => resolve("success"))
						.catch(() => reject("数据保存失败"))
				})
		)
	}
})
</script>
<template>
	<DetailViewer
		style="padding-right: 10px"
		ref="viewer"
		:mod="formValue.form && !formValue.form?.timeEnd ? 'edit' : 'view'"
		:id="formValue.formData.businessId"
	/>
</template>
