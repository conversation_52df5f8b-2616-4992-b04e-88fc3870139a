<script setup lang="ts">
// 变更采购单价
/* import orderDetail from "@/app/baseline/views/purchase/order/orderDetail.vue"

const props = defineProps({
	formValue: {
		type: Object,
		default: () => {},
		requeired: false
	}
})
const formValue = toRef(props, "formValue") */
</script>
<template>
	<div>变更采购单价:暂时废弃</div>
	<!-- <order-detail
		style="padding-right: 10px"
		model="view"
		:id="formValue.formData.businessId"
		:footer-btn-visible="false"
	/> -->
</template>
