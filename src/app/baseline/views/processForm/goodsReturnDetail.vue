<!-- 退货申请 -->
<script setup lang="ts">
import GoodsReturnDetail from "@/app/baseline/views/store/business/goodsReturn/goodsReturnDetail.vue"

const props = defineProps({
	formValue: {
		type: Object,
		default: () => {},
		requeired: false
	}
})
const formValue = toRef(props, "formValue")
</script>
<template>
	<goods-return-detail
		:id="formValue.formData.businessId"
		:footer-btn-visible="false"
	/>
</template>
