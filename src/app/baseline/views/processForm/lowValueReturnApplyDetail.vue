<!-- 低值 - 归还申请  -->
<script setup lang="ts">
import returnApplyDetail from "@/app/baseline/views/lowValue/returnApply/returnApplyDetail.vue"

const props = defineProps({
	formValue: {
		type: Object,
		default: () => {},
		requeired: false
	}
})
const formValue = toRef(props, "formValue")
</script>
<template>
	<return-apply-detail
		:id="formValue.formData.businessId"
		:footer-btn-visible="false"
	/>
</template>
