<!-- 盘点计划 -->
<script setup lang="ts">
import InventoryPlanDetail from "@/app/baseline/views/store/inventoryManage/inventoryPlan/inventoryPlanDetail.vue"
import { modalSize } from "../../utils/layout-config"

const emit = defineEmits<{
	(e: "sizeChange", inputForm: any): void
}>()

const props = defineProps({
	formValue: {
		type: Object,
		default: () => {},
		requeired: false
	}
})
const formValue = toRef(props, "formValue")

onMounted(() => {
	emit("sizeChange", modalSize.xxxl)
})
</script>
<template>
	<inventory-plan-detail
		:id="formValue.formData.businessId"
		:footer-btn-visible="false"
	/>
</template>
