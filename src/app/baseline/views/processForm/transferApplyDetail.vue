<!-- 调拨申请 -->
<script setup lang="ts">
import transferApplyDetail from "@/app/baseline/views/store/transfer/transferApplication/viewDrawer.vue"

const props = defineProps({
	formValue: {
		type: Object,
		default: () => {},
		requeired: false
	}
})
const formValue = toRef(props, "formValue")
</script>
<template>
	<transfer-apply-detail
		:id="formValue.formData.businessId"
		:footer-btn-visible="false"
	/>
</template>
