<!-- 编码手册  物资性质 -->
<script setup lang="ts">
import matManualAttrDetail from "@/app/baseline/views/material/matManual/matManualAttrDetail.vue"
import { IModalType } from "@/app/baseline/utils/types/common"

const props = defineProps({
	formValue: {
		type: Object,
		default: () => {},
		requeired: false
	}
})
const formValue = toRef(props, "formValue")
</script>
<template>
	<mat-manual-attr-detail
		style="padding-right: 10px"
		:mode="
			formValue.form && !formValue.form?.timeEnd
				? IModalType.edit
				: IModalType.view
		"
		:business-id="formValue.formData.businessId"
	/>
</template>
