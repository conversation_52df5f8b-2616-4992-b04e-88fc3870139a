<!-- 领用申请 -->
<script setup lang="ts">
import requisitionApplyDetail from "@/app/baseline/views/lowValue/requisitionApply/requisitionApplyDetail.vue"

const props = defineProps({
	formValue: {
		type: Object,
		default: () => {},
		requeired: false
	}
})
const formValue = toRef(props, "formValue")
</script>
<template>
	<requisition-apply-detail
		:id="formValue.formData.businessId"
		:footer-btn-visible="false"
	/>
</template>
