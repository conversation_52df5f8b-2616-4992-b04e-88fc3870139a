<!-- 交旧申请 -->
<script setup lang="ts">
import handoverApplyDetail from "@/app/baseline/views/waste/handoverApply/handoverApplyDetail.vue"
import { modalSize } from "../../utils/layout-config"

const emit = defineEmits<{
	(e: "sizeChange", inputForm: any): void
}>()

const props = defineProps({
	formValue: {
		type: Object,
		default: () => {},
		requeired: false
	}
})
const formValue = toRef(props, "formValue")

onMounted(() => {
	emit("sizeChange", modalSize.xxl)
})
</script>
<template>
	<handover-apply-detail
		:id="formValue.formData.businessId"
		:footer-btn-visible="false"
	/>
</template>
