<!-- 报废处置 -->
<script setup lang="ts">
import scrapDisposalApplyDetail from "@/app/baseline/views/waste/scrapDisposalApply/scrapDisposalApplyDetail.vue"

const props = defineProps({
	formValue: {
		type: Object,
		default: () => {},
		requeired: false
	}
})
const formValue = toRef(props, "formValue")
</script>
<template>
	<scrap-disposal-apply-detail
		:id="formValue.formData.businessId"
		:footer-btn-visible="false"
	/>
</template>
