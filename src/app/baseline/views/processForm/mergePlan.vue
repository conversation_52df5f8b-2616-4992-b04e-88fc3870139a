<script setup lang="ts">
// 合并计划
import mergePlanDetail from "@/app/baseline/views/plan/mergePlan/mergePlanDetail.vue"
import { IModalType } from "@/app/baseline/utils/types/common"

const props = defineProps({
	formValue: {
		type: Object,
		default: () => {},
		requeired: false
	}
})
const formValue = toRef(props, "formValue")
</script>
<template>
	<merge-plan-detail
		:id="formValue.formData.businessId"
		:footer-btn-visible="false"
		:mode="IModalType.view"
	/>
</template>
