<!-- 订货计划 -->
<script setup lang="ts">
import planDetail from "@/app/baseline/views/purchase/plan/planDetail.vue"
import { IModalType } from "@/app/baseline/utils/types/common"
import { modalSize } from "../../utils/layout-config"

const emit = defineEmits<{
	(e: "sizeChange", inputForm: any): void
}>()

const props = defineProps({
	formValue: {
		type: Object,
		default: () => {},
		requeired: false
	}
})
const formValue = toRef(props, "formValue")

onMounted(() => {
	emit("sizeChange", modalSize.xxl)
})
</script>
<template>
	<plan-detail
		:id="formValue.formData.businessId"
		:footer-btn-visible="false"
		:model="IModalType.view"
	/>
</template>
