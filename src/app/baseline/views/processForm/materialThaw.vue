<!-- 编码解冻 -->
<script setup lang="ts">
import matManualStatusDetail from "@/app/baseline/views/material/matManual/matManualStatusDetail.vue"
import { IModalType } from "@/app/baseline/utils/types/common"
import { IMaterialBusinessType } from "../../utils/types/material"

const props = defineProps({
	formValue: {
		type: Object,
		default: () => {},
		requeired: false
	}
})
const formValue = toRef(props, "formValue")
</script>
<template>
	<mat-manual-status-detail
		style="padding-right: 10px"
		:operate-type="IMaterialBusinessType.materialThawing"
		:mode="
			formValue.form && !formValue.form?.timeEnd
				? IModalType.edit
				: IModalType.view
		"
		:business-id="formValue.formData.businessId"
	/>
</template>
