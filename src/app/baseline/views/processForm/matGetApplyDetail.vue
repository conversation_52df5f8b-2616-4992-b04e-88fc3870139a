<!-- 领料申请 -->
<script setup lang="ts">
import MatGetApplyDetail from "@/app/baseline/views/store/business/matGetApply/matGetApplyDetail.vue"

const props = defineProps({
	formValue: {
		type: Object,
		default: () => {},
		requeired: false
	}
})
const formValue = toRef(props, "formValue")
</script>
<template>
	<mat-get-apply-detail
		:id="formValue.formData.businessId"
		:footer-btn-visible="false"
	/>
</template>
