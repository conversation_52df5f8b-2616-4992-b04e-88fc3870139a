<script setup lang="ts">
import { reactive, ref,onMounted,inject } from 'vue'
import type { FormProps } from 'element-plus'
const emit = defineEmits<{
	(e: "sizeChange", inputForm: any): void
}>()
const labelPosition = ref<FormProps['labelPosition']>('right')
const formLabelAlign = reactive({
  name: '',
  region: '',
  type: '',
})

const props = defineProps({
	formValue: {
		type: Object,
		default: () => {},
		requeired: false
	},
	isMySatrt: {
		type: Boolean,
		default:false
	}
})

const { updateGreet, greet} = inject('greet');  
onMounted(() => {
	emit("sizeChange", size.value)
	//
	updateGreet(()=>{
		return new Promise((resolve, reject) => {  
        // 假设这是一个模拟的异步操作  
        setTimeout(() => {  
          if (Math.random() > 0.5) {  
            resolve('success');  
          } else {  
            reject('失败');  
          }  
        }, 1000);  
      });  
	})	
})
const size = ref('980')
const sizeChange = ()=>{
	emit("sizeChange", size.value)

}
</script>
<template>
	<div class="box" >
		11111baseline页面bbb{{ props.formValue }}/{{ props.isMySatrt }}
		<el-form
			:label-position="labelPosition"
			label-width="100px"
			:model="formLabelAlign"
			style="max-width: 460px"
		>
			<el-form-item label="名称">
				<el-input v-model="props.formValue.businessTitle" />
			</el-form-item>
			<el-form-item label="组建taskId">
				<el-input v-model="props.formValue.processDefinitionName" />
			</el-form-item>
			<el-button @click="sizeChange">点击改变弹窗size</el-button>
		</el-form>
	</div>
</template>
<style scoped lang="scss">
.box{
	padding: 20px ;
}

</style>
