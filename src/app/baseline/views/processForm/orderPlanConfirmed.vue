<script setup lang="ts">
// 确认采购订单
/* import DetailViewer from "@/app/baseline/views/purchase/order/viewDrawer.vue"

const props = defineProps({
	formValue: {
		type: Object,
		default: () => {},
		requeired: false
	}
})
const businessId = props.formValue.formData.businessId */
</script>
<template>
	<div>确认采购订单 ： 暂时废弃</div>
	<!-- <DetailViewer
		style="padding-right: 10px"
		model="confirm"
		:is-publish="true"
		:id="businessId"
	/> -->
</template>
