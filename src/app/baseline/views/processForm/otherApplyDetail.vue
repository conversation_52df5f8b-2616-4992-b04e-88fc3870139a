<!-- 其他入库入申 -->
<script setup lang="ts">
import OtherApplyDetail from "@/app/baseline/views/store/business/otherWarehousing/otherApplyDetail.vue"

const props = defineProps({
	formValue: {
		type: Object,
		default: () => {},
		requeired: false
	}
})
const formValue = toRef(props, "formValue")
</script>
<template>
	<other-apply-detail
		:id="formValue.formData.businessId"
		:footer-btn-visible="false"
	/>
</template>
