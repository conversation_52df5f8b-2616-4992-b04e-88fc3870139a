<!-- 采购分包 -->
<script setup lang="ts">
import distributionDetail from "@/app/baseline/views/purchase/distribution/distributionDetail.vue"

const props = defineProps({
	formValue: {
		type: Object,
		default: () => {},
		requeired: false
	}
})
const formValue = toRef(props, "formValue")
</script>
<template>
	<distribution-detail
		:id="formValue.formData.businessId"
		:footer-btn-visible="false"
	/>
</template>
