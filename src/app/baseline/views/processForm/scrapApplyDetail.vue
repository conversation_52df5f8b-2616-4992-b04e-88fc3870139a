<!-- 废旧申请 -->
<script setup lang="ts">
import scrapApplyDetail from "@/app/baseline/views/waste/scrapApply/scrapApplyDetail.vue"

const props = defineProps({
	formValue: {
		type: Object,
		default: () => {},
		requeired: false
	}
})
const formValue = toRef(props, "formValue")
</script>
<template>
	<scrap-apply-detail
		:id="formValue.formData.businessId"
		:footer-btn-visible="false"
	/>
</template>
