<!-- 采购计划 -->
<script setup lang="ts">
import purchasePlanDetail from "@/app/baseline/views/plan/purchasePlan/purchasePlanDetail.vue"
import { modalSize } from "../../utils/layout-config"

const emit = defineEmits<{
	(e: "sizeChange", inputForm: any): void
}>()

const props = defineProps({
	formValue: {
		type: Object,
		default: () => {},
		requeired: false
	}
})
const formValue = toRef(props, "formValue")

onMounted(() => {
	emit("sizeChange", modalSize.xxxl)
})
</script>
<template>
	<purchase-plan-detail
		:id="formValue.formData.businessId"
		:footer-btn-visible="false"
	/>
</template>

<!-- <script setup lang="ts">
// 采购计划
import DetailViewer from "@/app/baseline/views/plan/components/purchasePlanViewDrawer.vue"

const props = defineProps({
	formValue: {
		type: Object,
		default: () => {},
		requeired: false
	}
})
const businessId = props.formValue.formData.businessId
</script>
<template>
	<DetailViewer
		style="padding-right: 10px"
		model="view"
		:matMode="false"
		:id="businessId"
	/>
</template> -->
