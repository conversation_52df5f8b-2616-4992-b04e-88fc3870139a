<!-- 退库申请 -->
<script setup lang="ts">
import WarehouseReturnApplyDetail from "@/app/baseline/views/store/business/warehouseReturnApply/warehouseReturnApplyDetail.vue"

const props = defineProps({
	formValue: {
		type: Object,
		default: () => {},
		requeired: false
	}
})
const formValue = toRef(props, "formValue")
</script>
<template>
	<warehouse-return-apply-detail
		:id="formValue.formData.businessId"
		:footer-btn-visible="false"
	/>
</template>
