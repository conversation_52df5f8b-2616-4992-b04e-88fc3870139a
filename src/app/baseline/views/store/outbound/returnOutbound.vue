<script lang="ts" setup>
import { onMounted, ref, computed } from "vue"
import {
	checkPermission,
	isCheckPermission
} from "@/app/platform/utils/permission"
import { powerList } from "../../components/define.d"

import LinkTag from "@/app/baseline/views/components/linkTag.vue"
import ViewDrawer from "./returnOutbound/viewDrawer.vue"
import { first, omit } from "lodash-es"
import goodsReturnDetail from "../business/goodsReturn/goodsReturnDetail.vue"
import { listMatStoragePaged } from "@/app/baseline/api/store/manage-api"
import {
	getStoreRemoveBpmStatusCnt,
	getStoreRemoveList
} from "../../../api/store/outbound/return"
import {
	MatOutStoreRemoveRequestParams,
	MatOutStoreRemoveVO
} from "../../../utils/types/store-outbound-return"
import { modalSize } from "@/app/baseline/utils/layout-config"
import { useTbInit } from "../../components/tableBase"
import { OutStoreStatus } from "./outbound"
import { tableColFilter } from "@/app/baseline/utils"
import { IModalType } from "@/app/baseline/utils/types/common"
import closeEditor from "./returnOutbound/closeEditor.vue"

/**
 * 查询条件 配置
 */
const queryArrList = computed(() => {
	return [
		{
			name: "出库单号",
			key: "code",
			placeholder: "请输入出库单号",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "关联退货单号",
			key: "preBusinessCode",
			placeholder: "请输入关联退货单号",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "申请人",
			key: "userName",
			placeholder: "请输入申请人",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "出库仓库",
			key: "storeId",
			placeholder: "请选择出库仓库",
			type: "tableSelect",
			tableInfo: [
				{
					title: "请选择出库仓库",
					tableApi: listMatStoragePaged, //表格接口
					tableColumns: [
						{ label: "仓库编码", prop: "code", width: 120 },
						{ label: "仓库名称", prop: "label" },
						{
							label: "仓库级别",
							prop: "level_view",
							width: 100
						},
						{
							label: "仓库类型",
							prop: "type_view",
							width: 120
						},
						{
							label: "所属段区",
							prop: "depotId_view",
							width: 150
						}
					],
					queryArrList: [
						{
							name: "仓库编码",
							key: "code",
							type: "input",
							placeholder: "请输入仓库编码"
						},
						{
							name: "仓库名称",
							key: "label",
							type: "input",
							placeholder: "请输入仓库名称"
						}
					],
					params: {
						currentPage: 1,
						pageSize: 20
					}, //表格接口参数

					labelName: {
						key: "id", //回显标识
						label: "label", //input框要展示的字段
						value: "id" //要给后台传的字段
					}
				}
			]
		}
	]
})

/**
 * table 数据源
 * @param data
 */
const getTableData = (data?: Record<string, any>) => {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		tableType: tabStatus,
		sord: "desc",
		sidx: "createdDate",
		...data
	}
	handleUpdate()
}

/**
 * title 配置
 */
const rightTitle = {
	name: ["退货出库"],
	icon: ["fas", "square-share-nodes"]
}

/**
 * tab 配置
 */
const statusCnt = ref<any[]>([])
const tabList = [
	{
		name: "待出库",
		value: OutStoreStatus.noOut,
		icon: ["fas", "square-plus"]
	},
	{
		name: "已出库",
		value: OutStoreStatus.out,
		icon: ["fas", "square-plus"]
	},
	{
		name: "已关闭",
		value: OutStoreStatus.close,
		icon: ["fas", "square-plus"]
	}
]
const tabStatus = ref<string>(tabList[0].value)
const activeName = ref<string>(tabList[0].name)
const handleTabsClick = (tab: any) => {
	activeName.value = tab.paneName
	tabStatus.value = tabList[tab.index].value
	getTableData()
}
const tbInit = useTbInit<MatOutStoreRemoveVO, MatOutStoreRemoveRequestParams>()
const {
	tableData,
	tableRef,
	tableLoading,
	currentPage,
	pageTotal,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = tbInit

/**
 * table 列 配置
 */
tbInit.tableProp = computed(() => {
	const tableCols: TableColumnType[] = [
		{
			label: "出库单号",
			prop: "code",
			width: 180,
			fixed: "left"
		},
		{
			label: "关联退货单号",
			prop: "preBusinessCode",
			needSlot: true,
			width: 180
		},
		{ label: "申请人", prop: "userName_view" },
		{ label: "申请时间", prop: "applyDate", width: 160, sortable: true },
		{ label: "出库仓库名称", prop: "storeName" },
		{ label: "出库物资编码", prop: "outedCount" },
		{ label: "关闭原因", prop: "reason" },
		{ label: "关闭时间", prop: "closeDate", width: 160, sortable: true },
		{
			label: "操作",
			width: 100,
			prop: "operations",
			needSlot: true
		}
	]
	switch (tabStatus.value) {
		case OutStoreStatus.close:
			return tableCols
		default:
			return tableColFilter(tableCols, ["关闭原因", "关闭时间"])
	}
})

fetchFunc.value = getStoreRemoveList

/**
 * 按钮 配置
 */
const tableFooterBtns = computed(() => {
	return [
		{
			name: "出库",
			roles: powerList.storeOutboundReturnOutboundBtnEdit,
			icon: ["fas", "arrow-up"],
			disabled: selectedTableList.value.length > 0 ? false : true
		},
		{
			name: "关闭",
			roles: powerList.storeOutboundReturnOutboundBtnClose,
			icon: ["fas", "times-circle"],
			disabled: selectedTableList.value.length > 0 ? false : true
		}
	]
})
const tbBtnLoading = ref(false)
const closeEditorVisible = ref(false)

/**
 * 出库 操作
 */
const handleTabFooterAction = async (btnName: string | undefined) => {
	if (btnName === "出库") {
		model.value = IModalType.edit
		showViewDrawer.value = true

		curRowId.value = first(selectedTableList.value)?.id
		curRowData.value = first(selectedTableList.value) || {}
	} else if (btnName === "关闭") {
		closeEditorVisible.value = true
	}
}

const curRowId = ref<any>("")
const curRowData = ref<MatOutStoreRemoveVO>({})
const showViewDrawer = ref<boolean>(false)
const model = ref<IModalType>(IModalType.view)

/**
 * 查看 操作
 * @param row
 */
const onRowView = (row: MatOutStoreRemoveVO) => {
	curRowId.value = row.id
	curRowData.value = row
	showViewDrawer.value = true
	model.value = IModalType.view
}

/**
 * 关联退货单号
 */
const showRelativeApplyDrawer = ref(false)
const onRowBusinessApply = (row: MatOutStoreRemoveVO) => {
	if (!row.preBusinessCode) {
		return false
	}
	curRowId.value = row.id
	curRowData.value = row
	showRelativeApplyDrawer.value = true
}

/**
 * 更新主表/tab count
 */
const handleUpdate = async () => {
	statusCnt.value = await getStoreRemoveBpmStatusCnt(
		omit(
			{
				...fetchParam.value
			},
			"tableType",
			"currentPage",
			"pageSize"
		) as any
	)
	fetchTableData()
}

/**
 * 排序
 * @param column
 * @param prop 字段名称
 * @param order ascending 升序 descending 降序
 */
function handleSortChange(column: any, prop: any, order: any) {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		sord:
			order === "descending" ? "desc" : order === "ascending" ? "asc" : "desc",
		sidx: order ? (prop === "applyDate" ? "createdDate" : prop) : "createdDate" // 排序字段
	}

	fetchTableData()
}

onMounted(() => {
	getTableData()
})
</script>
<template>
	<div class="app-container">
		<div class="whole-frame">
			<ModelFrame class="top-frame">
				<Query
					:queryArrList="queryArrList"
					@getQueryData="getTableData"
					class="ml10"
				/>
			</ModelFrame>
			<ModelFrame class="bottom-frame">
				<Title :title="rightTitle">
					<div class="app-tabs-wrapper">
						<el-tabs v-model="activeName" @tab-click="handleTabsClick">
							<el-tab-pane
								v-for="(tab, index) in tabList"
								:key="index"
								:label="`${tab.name}（${statusCnt[index] ?? 0}）`"
								:name="tab.name"
								:index="tab.name"
							/>
						</el-tabs>
					</div>
				</Title>

				<PitayaTable
					ref="tableRef"
					:columns="(tbInit.tableProp as any)"
					:tableData="tableData"
					:total="pageTotal"
					:need-index="true"
					:single-select="tabStatus == tabList[0].value ? true : false"
					:need-selection="tabStatus == tabList[0].value ? true : false"
					@on-current-page-change="onCurrentPageChange"
					@onSelectionChange="selectedTableList = $event"
					:table-loading="tableLoading"
					@on-table-sort-change="handleSortChange"
				>
					<!-- 关联退货单号 -->
					<template #preBusinessCode="{ rowData }">
						<link-tag
							:value="rowData.preBusinessCode"
							@click.stop="onRowBusinessApply(rowData)"
						/>
					</template>

					<template #operations="{ rowData }">
						<slot
							v-if="
								isCheckPermission(
									powerList.storeOutboundReturnOutboundBtnPreview
								)
							"
						>
							<el-button
								v-btn
								link
								@click.stop="onRowView(rowData)"
								:disabled="
									checkPermission(
										powerList.storeOutboundReturnOutboundBtnPreview
									)
								"
								v-if="
									isCheckPermission(
										powerList.storeOutboundReturnOutboundBtnPreview
									)
								"
							>
								<font-awesome-icon :icon="['fas', 'eye']" />
								<span class="table-inner-btn">查看</span>
							</el-button>
						</slot>
						<slot v-else>---</slot>
					</template>
					<template #footerOperateLeft v-if="tabStatus === tabList[0].value">
						<ButtonList
							class="btn-list"
							:is-not-radius="true"
							:button="tableFooterBtns"
							:loading="tbBtnLoading"
							@on-btn-click="handleTabFooterAction"
						/>
					</template>
				</PitayaTable>

				<!-- 编辑/查看弹窗 -->
				<Drawer
					:size="modalSize.xl"
					v-model:drawer="showViewDrawer"
					:destroyOnClose="true"
				>
					<view-drawer
						:id="curRowId"
						:row="curRowData"
						:model="model"
						@update="handleUpdate"
						@close="showViewDrawer = false"
					/>
				</Drawer>

				<!-- 关联退货单号 -->
				<Drawer
					v-model:drawer="showRelativeApplyDrawer"
					:size="modalSize.xl"
					destroy-on-close
				>
					<goods-return-detail
						:id="curRowData.preBusinessId!"
						@close="showRelativeApplyDrawer = false"
					/>
				</Drawer>

				<!-- 关闭原因 -->
				<Drawer
					v-model:drawer="closeEditorVisible"
					:size="350"
					destroy-on-close
				>
					<close-editor
						:id="first(selectedTableList)?.id"
						@close="closeEditorVisible = false"
						@update="handleUpdate"
					/>
				</Drawer>
			</ModelFrame>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
