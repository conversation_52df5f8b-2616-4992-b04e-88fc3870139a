<script setup lang="ts">
import { ref } from "vue"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import { DictApi } from "@/app/baseline/api/dict"
import {
	IMatOutStoreRoomStatus,
	MatOutStorePickRoomInfoVo,
	MatOutStorePickRoompageDto,
	MatOutStorePickRoompageVo
} from "../../../../utils/types/store-outbound-material"
import { useTbInit } from "../../../components/tableBase"
import {
	getMatPickRoomInfo,
	getMatPickSearchRoomInfoPage
} from "@/app/baseline/api/store/outbound/materialPick"
import { useDictInit } from "../../../components/dictBase"
import { OutStoreStatus } from "../outbound"
import { batchFormatterNumView, tableColFilter } from "@/app/baseline/utils"
import { getRealLength, setString } from "@/app/baseline/utils/validate"

export interface Props {
	id: string | number //
	status: IMatOutStoreRoomStatus
	outStatus: OutStoreStatus
}

const { getDictByCodeList, dictFilter } = useDictInit()

const props = defineProps<Props>()
const emits = defineEmits(["close"])

const loading = ref(false)
const formBtnList = [{ name: "取消", icon: ["fas", "circle-minus"] }]

const descData = ref<MatOutStorePickRoomInfoVo>({})

/**
 * 左侧 title 配置
 */
const drawerLeftTitle = {
	name: ["物资信息"],
	icon: ["fas", "square-share-nodes"]
}

/**
 * 左侧 表单 配置
 */
const formEl = computed(() => {
	const defCols = [
		{ label: "物资编码", name: "materialCode" },
		{ label: "物资名称", name: "materialName" },
		{ label: "品牌型号", name: "version" },
		{ label: "技术参数", name: "technicalParameter", needTooltip: true },
		{ label: "物资性质", name: "attribute" },
		{ label: "库存单位", name: "useUnit" },
		{ label: "出库仓库名称", name: "storeName" }
	]

	if (props.status == IMatOutStoreRoomStatus.all) {
		defCols.push({ label: "领料数量", name: "applyOutNum_view" })
		return defCols
	} else if (props.status == IMatOutStoreRoomStatus.wait) {
		defCols.push({
			label: `${
				props.outStatus == OutStoreStatus.close ? "关闭数量" : "待出库数量"
			}`,
			name: "toOutNum_view"
		})
		return defCols
	} else if (props.status == IMatOutStoreRoomStatus.out) {
		defCols.push({ label: "已出库数量", name: "outedNum_view" })
		return defCols
	} else {
		return defCols
	}
})

/**
 * 左侧 title 配置
 */
const drawerRightTitle = {
	name: ["出库货位"],
	icon: ["fas", "square-share-nodes"]
}

const tbInit = useTbInit<
	MatOutStorePickRoompageVo,
	MatOutStorePickRoompageDto
>()
const {
	tableData,
	tableRef,
	tableLoading,
	currentPage,
	pageTotal,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange,
	onDataSelected
} = tbInit

/**
 * table 列 配置
 */
tbInit.tableProp = computed(() => {
	const defCols: TableColumnType[] = [
		{ label: "区域编码", prop: "regionCode" },
		{ label: "货位编码", prop: "roomCode" },
		{ label: "领料数量", prop: "applyOutNum_view", align: "right" },
		{ label: "待出库数量", prop: "toOutNum_view", align: "right" },
		{ label: "关闭数量", prop: "toOutNum_view", align: "right" },
		{ label: "出库数量", prop: "outedNum_view", align: "right" },
		{ label: "批次号", prop: "batchNo", width: 180 }
	]

	if (props.status == IMatOutStoreRoomStatus.all) {
		return tableColFilter(defCols, ["待出库数量", "关闭数量", "出库数量"])
	} else if (props.status == IMatOutStoreRoomStatus.wait) {
		if (props.outStatus == OutStoreStatus.close) {
			// 关闭
			return tableColFilter(defCols, ["领料数量", "待出库数量", "出库数量"])
		} else {
			// 待出库数量
			return tableColFilter(defCols, ["领料数量", "出库数量", "关闭数量"])
		}
	} else if (props.status == IMatOutStoreRoomStatus.out) {
		return tableColFilter(defCols, ["领料数量", "待出库数量", "关闭数量"])
	}
	return defCols
})

watch(tableData, () => {
	batchFormatterNumView(tableData.value)
})

/**
 * table 数据源
 * @param data
 */
const getTableData = (data?: any) => {
	if (props.id) {
		fetchFunc.value = getMatPickSearchRoomInfoPage

		currentPage.value = 1
		tableRef.value?.resetCurrentPage()

		fetchParam.value = {
			id: props.id,
			status: props.status,
			sord: "desc",
			sidx: "createdDate",
			...data
		}

		fetchTableData()
	}
}

const drawerLoading = ref(false)
const getDetail = () => {
	if (!props.id) {
		return false
	}
	drawerLoading.value = true
	getMatPickRoomInfo(props.id as number)
		.then((res: any) => {
			descData.value = res
			batchFormatterNumView([descData.value])
		})
		.finally(() => {
			drawerLoading.value = false
		})
}

onMounted(() => {
	getDictByCodeList(["INVENTORY_UNIT"])
	getDetail()
	getTableData()
})
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<!-- 左侧表单区域 -->
		<div class="drawer-column left">
			<div class="rows">
				<Title :title="drawerLeftTitle" />
				<el-descriptions size="small" :column="1" border class="content">
					<template>
						<el-descriptions-item
							v-for="(el, index) in formEl"
							label-align="center"
							:label="el.label"
							:key="index"
						>
							<span v-if="el.name == 'useUnit'">
								{{
									dictFilter("INVENTORY_UNIT", descData[el.name])
										?.subitemName || "---"
								}}
							</span>

							<dict-tag
								v-else-if="el.name === 'attribute'"
								:options="DictApi.getMatAttr()"
								:value="descData.attribute"
							/>
							<span v-else-if="el.needTooltip">
								<el-tooltip
									effect="dark"
									:content="descData?.[el.name]"
									:disabled="
										getRealLength(descData?.[el.name]) <= 100 ? true : false
									"
								>
									{{
										getRealLength(descData?.[el.name]) > 100
											? setString(descData?.[el.name], 100)
											: descData?.[el.name] || "---"
									}}
								</el-tooltip>
							</span>
							<span v-else>
								{{
									descData[el.name] || descData[el.name] == 0
										? descData[el.name]
										: "---"
								}}
							</span>
						</el-descriptions-item>
					</template>
				</el-descriptions>
			</div>
		</div>

		<!-- 右侧table区域 -->
		<div class="drawer-column right">
			<div class="rows">
				<Title :title="drawerRightTitle" />
				<el-scrollbar class="tab-mat">
					<PitayaTable
						ref="tableRef"
						:columns="(tbInit.tableProp as any)"
						:table-data="tableData"
						:need-index="true"
						:need-pagination="true"
						:single-select="false"
						:need-selection="false"
						:total="pageTotal"
						@onSelectionChange="onDataSelected"
						@on-current-page-change="onCurrentPageChange"
						:table-loading="tableLoading"
					/>
				</el-scrollbar>
			</div>

			<ButtonList
				class="footer"
				:button="formBtnList"
				:loading="loading"
				@on-btn-click="emits('close')"
			/>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.drawer-container {
	.left {
		width: 310px;
	}

	.right {
		width: calc(100% - 310px);
	}

	.tab-mat {
		height: calc(100% - 43px);
	}
}
</style>
