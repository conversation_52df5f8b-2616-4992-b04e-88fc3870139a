<script setup lang="ts">
import { FormElementType } from "@/app/baseline/views/components/define"
import { ref, toRef } from "vue"
import { modalSize } from "@/app/baseline/utils/layout-config"

import TableFile from "@/app/baseline/views/components/tableFile.vue"
import { DictApi, fileBusinessType } from "@/app/baseline/api/dict"
import {
	MatOutStorePickItemRequestParams,
	MatOutStorePickItemVO,
	MatOutStorePickVO,
	IMatOutStoreRoomStatus
} from "../../../../utils/types/store-outbound-material"
import {
	getMatPickApplyDetail,
	getMatPickById,
	matPickOutStoreAll
} from "@/app/baseline/api/store/outbound/materialPick"
import {
	IIdempotentTokenType,
	IIdempotentTokenTypePre,
	IModalType
} from "@/app/baseline/utils/types/common"
import { useMessageBoxInit } from "../../../components/messageBox"
import { tableColFilter, toFixedTwo } from "@/app/baseline/utils"
import { useTbInit } from "../../../components/tableBase"
import { useDictInit } from "../../../components/dictBase"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import LinkTag from "@/app/baseline/views/components/linkTag.vue"
import batchDrawer from "./batchDrawer.vue"
import roomDrawer from "./roomDrawer.vue"
import outboundForm from "./outboundForm.vue"
import { OutStoreStatus, getOutStoreStatus } from "../outbound"
import { first, includes } from "lodash-es"
import { getIdempotentToken } from "@/app/baseline/utils/validate"

const { dictOptions, getDictByCodeList, dictFilter } = useDictInit()
const { showWarnConfirm } = useMessageBoxInit()

export interface Props {
	id: string | number //
	model?: IModalType //显示模式 view, viewPlan
}

const props = defineProps<Props>()
const emits = defineEmits(["close", "update"])
const currentId = toRef(props, "id")
const loading = ref(false)
const formBtnList = computed(() => {
	switch (props.model) {
		case IModalType.edit:
			return [
				{ name: "取消", icon: ["fas", "circle-minus"] },
				{ name: "确认", icon: ["fas", "circle-check"] }
			]
		default:
			return [{ name: "取消", icon: ["fas", "circle-minus"] }]
	}
})

const descData = ref<MatOutStorePickVO>({})

const drawerLoading = ref(false)

/**
 * 物资明细 table 行样式
 *
 * 库存不足物资，用红色标记该行
 */
const errorGoodsIdList = ref<number[]>([])
function tbCellClassName({ row }: any) {
	return includes(errorGoodsIdList.value, row.id) ? "error" : ""
}

/**
 * 左侧title 配置
 */
const drawerLeftTitle = {
	name: ["领料出库"],
	icon: ["fas", "square-share-nodes"]
}

const isDisabledBtn = computed(() => {
	return selectedTableList.value.length > 0 &&
		selectedTableList.value![0].status != OutStoreStatus.out
		? false
		: true
})

/**
 * 入库按钮 配置
 */
const tableFooterBtns = computed(() => {
	return [
		{
			name: "出库",
			icon: ["fas", "arrow-up"],
			disabled: isDisabledBtn.value
		}
	]
})

/**
 * 左侧 表单 配置
 */
const formEl = computed<FormElementType[]>(() => {
	const descOpt = [
		{ label: "出库单号", name: "code" },
		{ label: "关联领料单号", name: "preBusinessCode" },
		{ label: "领料用途", name: "purpose_view" },
		{ label: "出库仓库名称", name: "storeName" },
		{ label: "出库物资编码", name: "outCount" },
		{ label: "待出库物资编码", name: "toOutCount" },
		{ label: "已出库物资编码", name: "outedCount" },
		{ label: "已关闭物资编码", name: "toOutCount" },
		{ label: "领料人", name: "pickUserId_view" },
		{ label: "申请时间", name: "createdDate" },
		{ label: "出库状态", name: "status" }
	]

	switch (descData.value.status) {
		case OutStoreStatus.noOut:
			return descOpt.filter((v) => v.label != "已关闭物资编码")
		case OutStoreStatus.partOut:
			return descOpt.filter((v) => v.label != "已关闭物资编码")
		case OutStoreStatus.out:
			return descOpt.filter(
				(v) =>
					v.label != "已关闭物资编码" &&
					v.label != "已出库物资编码" &&
					v.label != "待出库物资编码"
			)
		default:
			return descOpt.filter((v) => v.label != "待出库物资编码")
	}
})

/**
 * title 配置
 */
const drawerRightTitle = {
	name: ["扩展信息"],
	icon: ["fas", "square-share-nodes"]
}

const tabList = ["物资明细", "相关附件"]
const activeTab = ref<number>(0)
const handleTabChange = (index: number) => {
	activeTab.value = index

	if (index === 0) {
		fetchParam.value = {
			applyId: props.id,
			sord: "desc",
			sidx: "createdDate"
		}
		pageSize.value = 20
		getTableData()
	}
}

/**
 * 表单按钮 操作
 * @param btnName
 */
const onFormBtnList = async (btnName: string | undefined) => {
	if (btnName === "取消") {
		emits("close")
		return
	} else if (btnName === "确认") {
		await showWarnConfirm("请确认是否将物资全部出库？")
		try {
			loading.value = true
			const idempotentToken = getIdempotentToken(
				IIdempotentTokenTypePre.other,
				IIdempotentTokenType.materialGetOutWarehouse,
				currentId.value
			)

			const { code, msg, data } = await matPickOutStoreAll(
				currentId.value as any,
				idempotentToken
			)

			if (data && code != 200) {
				errorGoodsIdList.value = data || []
				ElMessage.error(msg)
				getTableData()
			} else {
				ElMessage.success("操作成功")
				emits("update")
				emits("close")
			}
		} finally {
			loading.value = false
		}
	}
}

/**
 * 详情
 */
const getDetail = () => {
	if (!props.id) {
		return false
	}
	drawerLoading.value = true
	getMatPickById(props.id as number)
		.then((res: any) => {
			descData.value = res
		})
		.finally(() => {
			drawerLoading.value = false
		})
}
onMounted(() => {
	getDictByCodeList(["INVENTORY_UNIT", "MATERIAL_NATURE"])
	getDetail()
	getTableData()
})

/**
 * 查询条件配置
 */
const queryArrList = computed(() => {
	const queryArrConfig = [
		{
			name: "出库状态",
			key: "status",
			placeholder: "请选择出库状态",
			type: "select",
			children: getOutStoreStatus().filter((v) => v.label != "已关闭")
		},
		{
			name: "物资编码",
			key: "materialCode",
			placeholder: "请输入物资编码",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "物资名称",
			key: "materialLabel",
			placeholder: "请输入物资名称",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "规格型号",
			key: "version",
			placeholder: "请输入规格型号",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "物资性质",
			key: "attribute",
			type: "select",
			children: dictOptions.value.MATERIAL_NATURE,
			placeholder: "请选择物资性质"
		}
	]

	return queryArrConfig
})

const tbInit = useTbInit<
	MatOutStorePickItemVO,
	MatOutStorePickItemRequestParams
>()
const {
	tableData,
	tableLoading,
	tableRef,
	currentPage,
	pageTotal,
	pageSize,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = tbInit

/**
 * table 列配置
 */
tbInit.tableProp = computed(() => {
	const defCols: TableColumnType[] = [
		{ label: "物资编码", prop: "materialCode", width: 130 },
		{ label: "物资名称", prop: "materialName", minWidth: 150 },
		{ label: "规格型号", prop: "version", minWidth: 150 },
		{ label: "技术参数", prop: "technicalParameter", minWidth: 200 },
		{ label: "物资性质", prop: "attribute", needSlot: true, width: 120 },
		{ label: "库存单位", prop: "useUnit", needSlot: true, width: 90 },
		{
			label: "领料数量",
			prop: "applyOutNum",
			needSlot: true,
			align: "right",
			minWidth: 100
		},
		{
			label: "待出库数量",
			prop: "toOutNum",
			needSlot: true,
			align: "right",
			minWidth: 100
		},
		{
			label: "已出库数量",
			prop: "outedNum",
			needSlot: true,
			align: "right",
			minWidth: 100
		},
		{
			label: "关闭数量",
			prop: "toOutNum",
			needSlot: true,
			align: "right",
			minWidth: 100
		},
		{
			label: "出库状态",
			prop: "status",
			needSlot: true,
			minWidth: 100,
			fixed: "right"
		},
		{
			label: "出库批次",
			prop: "outBatchTimes",
			needSlot: true,
			width: 100,
			fixed: "right"
		}
		/* {
			label: "出库货位",
			prop: "outRoomTimes",
			needSlot: true,
			width: 100,
			fixed: "right"
		} */
	]

	if (
		descData.value.status == OutStoreStatus.noOut ||
		descData.value.status == OutStoreStatus.partOut
	) {
		// todo 待出库 & 部分出库
		return tableColFilter(defCols, ["关闭数量", "出库批次"])
	} else if (descData.value.status == OutStoreStatus.out) {
		// 已出库显示 批次
		return tableColFilter(defCols, ["领料数量", "待出库数量", "关闭数量"])
	} else {
		return tableColFilter(defCols, ["出库批次", "待出库数量"])
	}
})

/**
 * table 数据源
 * @param data
 */
const getTableData = (data?: any) => {
	if (props.id) {
		fetchFunc.value = getMatPickApplyDetail

		currentPage.value = 1
		tableRef.value?.resetCurrentPage()

		fetchParam.value = {
			applyId: props.id,
			sord: "desc",
			sidx: "createdDate",
			...data
		}
		fetchTableData()
	}
}

/**
 * 关联批次 操作
 */
const showBatchDrawer = ref(false)
const currentRow = ref<MatOutStorePickItemVO>({})
const onRowBatch = (row: MatOutStorePickItemVO) => {
	if (!row.outBatchTimes) {
		return false
	}
	currentRow.value = row
	showBatchDrawer.value = true
}

/**
 * 关联货位 操作
 */
const roomDetailVisible = ref(false)
const matRoomStatus = ref<IMatOutStoreRoomStatus>(IMatOutStoreRoomStatus.all)
const handleRoomView = (
	row: MatOutStorePickItemVO,
	status: IMatOutStoreRoomStatus
) => {
	if (!row.applyOutNum && status == IMatOutStoreRoomStatus.all) {
		return false
	} else if (!row.outedNum && status == IMatOutStoreRoomStatus.out) {
		return false
	} else if (!row.toOutNum && status == IMatOutStoreRoomStatus.wait) {
		return false
	}
	currentRow.value = row
	matRoomStatus.value = status
	roomDetailVisible.value = true
}

const outboundEditVisible = ref(false)
/**
 * 出库操作
 */
function handleBtnOut() {
	outboundEditVisible.value = true
}
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<!-- 左侧表单区域 -->
		<div class="drawer-column left">
			<div class="rows">
				<Title :title="drawerLeftTitle" />
				<el-descriptions size="small" :column="1" border class="content">
					<template>
						<el-descriptions-item
							v-for="(el, index) in formEl"
							label-align="center"
							:label="el.label"
							:key="index"
						>
							<!-- 出库状态 -->
							<span v-if="el.name == 'status'">
								<dict-tag
									:value="descData[el.name]!"
									:options="getOutStoreStatus()"
								/>
							</span>
							<span v-else>
								{{
									descData[el.name] || descData[el.name] == 0
										? descData[el.name]
										: "---"
								}}
							</span>
						</el-descriptions-item>
					</template>
				</el-descriptions>
			</div>
		</div>

		<!-- 右侧table区域 -->
		<div class="drawer-column right">
			<div class="rows">
				<Title :title="drawerRightTitle">
					<Tabs class="tabs" :tabs="tabList" @on-tab-change="handleTabChange" />
				</Title>

				<el-scrollbar
					v-if="activeTab === 0"
					class="tab-mat editor-table-wrapper"
				>
					<Query
						:queryArrList="queryArrList"
						@getQueryData="getTableData"
						class="custom-q"
					/>

					<PitayaTable
						ref="tableRef"
						:columns="(tbInit.tableProp as any)"
						:table-data="tableData"
						:need-index="true"
						:need-pagination="true"
						:single-select="props.model == IModalType.create ? true : false"
						:need-selection="props.model == IModalType.create ? true : false"
						:total="pageTotal"
						@onSelectionChange="selectedTableList = $event"
						@on-current-page-change="onCurrentPageChange"
						:table-loading="tableLoading"
						:cell-class-name="tbCellClassName"
					>
						<!-- 物资性质 -->
						<template #attribute="{ rowData }">
							<dict-tag
								:options="DictApi.getMatAttr()"
								:value="rowData.attribute"
							/>
						</template>

						<template #useUnit="{ rowData }">
							{{
								dictFilter("INVENTORY_UNIT", rowData.useUnit)?.subitemName ||
								"---"
							}}
						</template>

						<template #applyOutNum="{ rowData }">
							<link-tag
								:value="toFixedTwo(rowData.applyOutNum)"
								@click.stop="
									handleRoomView(rowData, IMatOutStoreRoomStatus.all)
								"
							/>
						</template>

						<template #outedNum="{ rowData }">
							<link-tag
								:value="toFixedTwo(rowData.outedNum)"
								@click.stop="
									handleRoomView(rowData, IMatOutStoreRoomStatus.out)
								"
							/>
						</template>

						<template #toOutNum="{ rowData }">
							<link-tag
								:value="toFixedTwo(rowData.toOutNum)"
								@click.stop="
									handleRoomView(rowData, IMatOutStoreRoomStatus.wait)
								"
							/>
						</template>

						<!-- 入库状态 -->
						<template #status="{ rowData }">
							<dict-tag
								:options="getOutStoreStatus()"
								:value="rowData.status"
							/>
						</template>

						<!-- 批次 -->
						<template #outBatchTimes="{ rowData }">
							<link-tag
								:value="rowData.outBatchTimes"
								@click.stop="onRowBatch(rowData)"
							/>
						</template>

						<!-- 货位 -->
						<!-- <template #outRoomTimes="{ rowData }">
							<link-tag
								:value="rowData.outRoomTimes"
								@on-click="handleRoomView(rowData)"
							/>
						</template> -->

						<template
							#footerOperateLeft
							v-if="props.model == IModalType.create"
						>
							<ButtonList
								class="btn-list"
								:is-not-radius="true"
								:button="tableFooterBtns"
								@on-btn-click="handleBtnOut"
							/>
						</template>
					</PitayaTable>
				</el-scrollbar>

				<div v-if="activeTab === 1" :class="currentId ? '' : 'disabled'">
					<TableFile
						:business-type="fileBusinessType.outStorePick"
						:business-id="currentId"
						:mod="props.model"
					/>
				</div>
			</div>

			<ButtonList
				class="footer"
				:button="formBtnList"
				:loading="loading"
				@on-btn-click="onFormBtnList"
			/>

			<!-- 批次 -->
			<Drawer
				:size="modalSize.lg"
				v-model:drawer="showBatchDrawer"
				:destroyOnClose="true"
			>
				<batch-drawer
					:id="currentRow.id!"
					:row="currentRow"
					@close="showBatchDrawer = false"
				/>
			</Drawer>

			<!-- 货位 -->
			<Drawer
				:size="modalSize.md"
				v-model:drawer="roomDetailVisible"
				:destroyOnClose="true"
			>
				<room-drawer
					:id="currentRow.id!"
					:status="matRoomStatus"
					:outStatus="(descData.status as OutStoreStatus)"
					@close="roomDetailVisible = false"
				/>
			</Drawer>

			<Drawer
				:size="modalSize.sm"
				v-model:drawer="outboundEditVisible"
				:destroyOnClose="true"
			>
				<outbound-form
					:row="first(selectedTableList)"
					:id="props.id"
					:store-name="descData.storeName!"
					@update="
						() => {
							fetchTableData()
							getDetail()
							emits('update')
						}
					"
					@close="outboundEditVisible = false"
				/>
			</Drawer>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.drawer-container {
	.left {
		width: 330px;
	}

	.right {
		width: calc(100% - 330px);
	}

	.tab-mat {
		height: 100%;
	}
}

.custom-q {
	margin: 10px 0px -10px 10px !important;
}

.editor-table-wrapper {
	&:deep(.pitaya-table) {
		.error {
			.column-inner-item,
			.cell,
			.el-input__inner {
				color: red;
			}
		}
	}
}
</style>
