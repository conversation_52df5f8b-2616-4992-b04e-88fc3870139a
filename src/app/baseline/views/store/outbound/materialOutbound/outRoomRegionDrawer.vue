<script setup lang="ts">
import { FormElementType } from "@/app/baseline/views/components/define"
import { ref } from "vue"

import {
	MatOutStorePickBatchVO,
	MatOutStorePickOutRequestParams
} from "../../../../utils/types/store-outbound-material"
import { useTbInit } from "../../../components/tableBase"
import { getRegionRoomListPaged } from "@/app/baseline/api/store/outbound/materialPick"
import { useDictInit } from "../../../components/dictBase"
import { toFixedTwo } from "@/app/baseline/utils"
import { getRealLength, setString } from "@/app/baseline/utils/validate"

export interface Props {
	desData: Record<string, any>
	storeName: string
}

const { getDictByCodeList, dictFilter } = useDictInit()

const props = defineProps<Props>()
const emits = defineEmits(["close"])

const loading = ref(false)
const formBtnList = [{ name: "取消", icon: ["fas", "circle-minus"] }]

const descData = ref<Record<string, any>>({
	...props.desData,
	storeName: props.storeName
})
descData.value.completeNum_view = toFixedTwo(descData.value.completeNum)

/**
 * 左侧 title 配置
 */
const drawerLeftTitle = {
	name: ["物资信息"],
	icon: ["fas", "square-share-nodes"]
}

/**
 * 左侧 表单 配置
 */
const formEl = computed(() => {
	const defCols = [
		{ label: "物资编码", name: "materialCode" },
		{ label: "物资名称", name: "materialName" },
		{ label: "品牌型号", name: "version" },
		{ label: "技术参数", name: "technicalParameter", needTooltip: true },
		{ label: "库存单位", name: "useUnit" },
		{ label: "出库仓库名称", name: "storeName" },
		{ label: "出库数量", name: "completeNum_view" }
	]

	return defCols
})

/**
 * 左侧 title 配置
 */
const drawerRightTitle = {
	name: ["出库货位"],
	icon: ["fas", "square-share-nodes"]
}

const tbInit = useTbInit<
	MatOutStorePickBatchVO,
	MatOutStorePickOutRequestParams
>()
const {
	tableData,
	tableRef,
	tableLoading,
	currentPage,
	pageTotal,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange,
	onDataSelected
} = tbInit

/**
 * table 列 配置
 */
tbInit.tableProp = computed(() => {
	const defCols: TableColumnType[] = [
		{ label: "出库出域编码", prop: "regionCode" },
		{ label: "出库货位编码", prop: "roomCode" },
		{ label: "出库数量", prop: "completeNum", needSlot: true, align: "right" },
		{ label: "批次号", prop: "batchNo", width: 180 }
	]
	return defCols
})

/**
 * table 数据源
 * @param data
 */
const getTableData = (data?: any) => {
	if (descData.value.id) {
		fetchFunc.value = getRegionRoomListPaged

		currentPage.value = 1
		tableRef.value?.resetCurrentPage()

		fetchParam.value = {
			id: descData.value.id,
			completeNum: descData.value.completeNum,
			...data
		}

		fetchTableData()
	}
}

const drawerLoading = ref(false)

onMounted(() => {
	getDictByCodeList(["INVENTORY_UNIT"])
	getTableData()
})
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<!-- 左侧表单区域 -->
		<div class="drawer-column left">
			<div class="rows">
				<Title :title="drawerLeftTitle" />
				<el-descriptions size="small" :column="1" border class="content">
					<template>
						<el-descriptions-item
							v-for="(el, index) in formEl"
							label-align="center"
							:label="el.label"
							:key="index"
						>
							<span v-if="el.name == 'useUnit'">
								{{
									dictFilter("INVENTORY_UNIT", descData[el.name])
										?.subitemName || "---"
								}}
							</span>
							<span v-else-if="el.needTooltip">
								<el-tooltip
									effect="dark"
									:content="descData?.[el.name]"
									:disabled="
										getRealLength(descData?.[el.name]) <= 100 ? true : false
									"
								>
									{{
										getRealLength(descData?.[el.name]) > 100
											? setString(descData?.[el.name], 100)
											: descData?.[el.name] || "---"
									}}
								</el-tooltip>
							</span>
							<span v-else>
								{{
									descData[el.name] || descData[el.name] == 0
										? descData[el.name]
										: "---"
								}}
							</span>
						</el-descriptions-item>
					</template>
				</el-descriptions>
			</div>
		</div>

		<!-- 右侧table区域 -->
		<div class="drawer-column right">
			<div class="rows">
				<Title :title="drawerRightTitle" />
				<el-scrollbar class="tab-mat">
					<PitayaTable
						ref="tableRef"
						:columns="(tbInit.tableProp as any)"
						:table-data="tableData"
						:need-index="true"
						:need-pagination="true"
						:single-select="false"
						:need-selection="false"
						:total="pageTotal"
						@onSelectionChange="onDataSelected"
						@on-current-page-change="onCurrentPageChange"
						:table-loading="tableLoading"
					>
						<template #completeNum="{ rowData }">
							{{ toFixedTwo(rowData.completeNum) }}
						</template>
					</PitayaTable>
				</el-scrollbar>
			</div>

			<ButtonList
				class="footer"
				:button="formBtnList"
				:loading="loading"
				@on-btn-click="emits('close')"
			/>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.drawer-container {
	.left {
		width: 310px;
	}

	.right {
		width: calc(100% - 310px);
	}

	.tab-mat {
		height: calc(100% - 43px);
	}
}
</style>
