<script setup lang="ts">
import { computed, onMounted, ref, toRef } from "vue"
import FormElement from "@/app/baseline/views/components/formElement.vue"
import { FormElementType } from "@/app/baseline/views/components/define.d"
import { ElMessage, type FormInstance, type FormRules } from "element-plus"
import { MatInStoreInspectItemVO } from "../../../../utils/types/store-warehouse-inspect"
import { useDictInit } from "../../../components/dictBase"
import { toMoney } from "@/app/baseline/utils"
import {
	getIdempotentToken,
	validateAndCorrectInput
} from "@/app/baseline/utils/validate"
import {
	getMatPickRegionRoom,
	matPickOutStoreHalf
} from "@/app/baseline/api/store/outbound/materialPick"
import outRoomRegionDrawer from "./outRoomRegionDrawer.vue"
import { modalSize } from "@/app/baseline/utils/layout-config"
import { toNumber } from "lodash-es"
import { useMessageBoxInit } from "../../../components/messageBox"
import {
	IIdempotentTokenType,
	IIdempotentTokenTypePre
} from "@/app/baseline/utils/types/common"

const { getDictByCodeList, dictFilter } = useDictInit()

const { showWarnConfirm } = useMessageBoxInit()

export interface Props {
	row?: MatInStoreInspectItemVO
	id: any // 主表Id
	storeName: string
}

const props = defineProps<Props>()
const curRowData = toRef(props, "row")

const roomRegionDetailVisible = ref(false)

/**
 * Title 配置
 */
const qualityDrawerTitle = {
	name: ["领料出库"],
	icon: ["fas", "square-share-nodes"]
}

/**
 * 按钮 配置
 */
const formBtnList = ref([
	{ name: "取消", icon: ["fas", "circle-minus"] },
	{ name: "确定", icon: ["fas", "check-circle"], disabled: false }
])

const emits = defineEmits<{
	(e: "close"): void
	(e: "update"): void
}>()
const ruleFormRef = ref<FormInstance>()

const formData = ref<{ [propName: string]: any }>({
	...curRowData.value,
	completeNum: 0
})
formData.value.purchasePrice_view = toMoney(formData.value.purchasePrice)

formData.value.useUnit_view = computed(() => {
	return (
		dictFilter("INVENTORY_UNIT", formData.value.useUnit)?.subitemName || "---"
	)
})
formData.value.attribute_view = computed(() => {
	return (
		dictFilter("MATERIAL_NATURE", formData.value.attribute)?.subitemName ||
		"---"
	)
})

/* 表单 配置 */
const formEl = computed<FormElementType[][]>(() => [
	[{ label: "物资编码", name: "materialCode", disabled: true }],
	[{ label: "物资名称", name: "materialName", disabled: true }],
	[{ label: "规格型号", name: "version", disabled: true }],
	[
		{
			label: "技术参数",
			name: "technicalParameter",
			type: "textarea",
			disabled: true
		}
	],
	[{ label: "物资性质", name: "attribute_view", disabled: true }],
	[{ label: "库存单位", name: "useUnit_view", disabled: true }],
	[{ label: "领料数量", name: "applyOutNum", disabled: true, type: "number" }],
	[{ label: "待出库数量", name: "toOutNum", disabled: true, type: "number" }],
	[
		{
			label: "本次出库数量",
			name: "completeNum",
			type: "number",
			min: 0,
			input: (value: any) => {
				formData.value.completeNum = validateAndCorrectInput(value)
			},
			blur: (event: any) => {
				formData.value.completeNum = toNumber(event.target.value)
			},
			change: async () => {
				const res = await getMatPickRegionRoom({
					id: formData.value.id,
					completeNum: formData.value.completeNum
				} as any)

				formData.value = {
					...formData.value,
					regionCode: res.regionCode,
					regionNum: res.regionNum,
					roomCode: res.roomCode,
					roomNum: res.roomNum
				}
			}
		}
	],
	[
		{
			label: "出库区域",
			name: formData.value.regionNum > 1 ? "regionNum" : "regionCode",
			type: formData.value.regionNum > 1 ? "link" : "text",
			disabled: true,
			clickApi: () => {
				roomRegionDetailVisible.value = true
			}
		}
	],
	[
		{
			label: "出库货位",
			name: formData.value.roomNum > 1 ? "roomNum" : "roomCode",
			type: formData.value.roomNum > 1 ? "link" : "text",
			disabled: true,
			clickApi: () => {
				roomRegionDetailVisible.value = true
			}
		}
	]
])

/**
 * 较验 本次出库数量 < 待出库数量
 * @param rule
 * @param value
 * @param callback
 */
const validateOutStoreNum = (rule: any, value: any, callback: any) => {
	if (formData.value.toOutNum > 0) {
		if (formData.value.completeNum > formData.value.toOutNum) {
			return callback(new Error("不能大于待出库数量"))
		} else if (formData.value.completeNum == 0) {
			return callback(new Error("本次出库数量不能为0"))
		} else {
			callback()
		}
	}
	callback()
}

/* 表单校验 配置 */
const rules = computed<FormRules<typeof formData>>(() => ({
	completeNum: [
		{
			required: true,
			message: "本次出库数量不能为空",
			trigger: "change"
		},
		{ validator: validateOutStoreNum, required: true, trigger: "change" }
	]
}))

/* 表单 操作 */
const drawerBtnLoading = ref(false)
const onFormBtnList = (btnName: string | undefined) => {
	if (btnName === "确定") {
		if (ruleFormRef.value) {
			ruleFormRef.value.validate(async (valid) => {
				if (valid) {
					await showWarnConfirm("请确认是否领料出库？")

					drawerBtnLoading.value = true
					try {
						const idempotentToken = getIdempotentToken(
							IIdempotentTokenTypePre.other,
							IIdempotentTokenType.materialGetOutWarehouse,
							props.id
						)

						await matPickOutStoreHalf(
							{
								id: curRowData.value?.id,
								completeNum: formData.value.completeNum
							},
							idempotentToken
						)
						ElMessage.success("操作成功")
						emits("update")
						emits("close")
					} finally {
						drawerBtnLoading.value = false
					}
				}
			})
		}
	} else if (btnName === "取消") {
		emits("close")
	}
}
onMounted(() => {
	getDictByCodeList(["INVENTORY_UNIT", "MATERIAL_NATURE"])
})
</script>
<template>
	<div class="drawer-container">
		<div class="drawer-column">
			<div class="rows">
				<Title :title="qualityDrawerTitle" />
				<el-scrollbar>
					<el-form
						style="padding-bottom: 30px"
						class="content form-base"
						ref="ruleFormRef"
						:model="formData"
						:rules="rules"
						label-position="top"
					>
						<FormElement :form-element="formEl" :form-data="formData" />
					</el-form>
				</el-scrollbar>
			</div>
			<ButtonList
				class="footer"
				:button="formBtnList"
				:loading="drawerBtnLoading"
				@onBtnClick="onFormBtnList"
			/>
		</div>

		<Drawer
			:size="modalSize.md"
			v-model:drawer="roomRegionDetailVisible"
			:destroyOnClose="true"
		>
			<out-room-region-drawer
				:desData="formData"
				:store-name="props.storeName"
				@close="roomRegionDetailVisible = false"
			/>
		</Drawer>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.drawer-column {
	width: 100%;
}
</style>
