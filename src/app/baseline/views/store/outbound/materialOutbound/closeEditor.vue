<!-- 领料出库 关闭窗 -->
<script setup lang="ts">
import { ElMessage, FormInstance, FormRules } from "element-plus"
import { onMounted, reactive, ref } from "vue"

import { useMessageBoxInit } from "../../../components/messageBox"
import formElement from "../../../components/formElement.vue"
import { inputMaxLength } from "@/app/baseline/utils/layout-config"
import { batchUpdateMatPickClose } from "@/app/baseline/api/store/outbound/materialPick"

import { MatOutStorePickVO } from "@/app/baseline/utils/types/store-outbound-material"
import { map } from "lodash-es"
import { getIdempotentToken } from "@/app/baseline/utils/validate"
import {
	IIdempotentTokenType,
	IIdempotentTokenTypeAction,
	IIdempotentTokenTypePre
} from "@/app/baseline/utils/types/common"

export interface Props {
	rowList: MatOutStorePickVO[]
}
const { showWarnConfirm } = useMessageBoxInit()

const props = defineProps<Props>()

const emits = defineEmits<{
	(e: "update"): void
	(e: "close"): void
}>()
const loading = ref(false)

const drawerLoading = ref(false)

const formRef = ref<FormInstance>()

/**
 * 按钮 配置
 */
const formBtnListEdit = [
	{ name: "取消", icon: ["fas", "circle-minus"] },
	{ name: "确认", icon: ["fas", "circle-check"] }
]

/**
 * 表单数据源
 */
const formModal = ref<Record<string, any>>({
	reason: ""
})
const descData = ref<MatOutStorePickVO>({})

/**
 * 左侧title 配置
 */
const drawerLeftTitle = {
	name: ["关闭领料出库"],
	icon: ["fas", "square-share-nodes"]
}

/**
 *表 单按钮 操作
 * @param btnName
 */
const onFormBtnList = async (btnName: string | undefined) => {
	if (btnName === "取消") {
		emits("close")
	} else if (btnName === "确认") {
		if (!formRef.value) {
			return
		}
		formRef.value?.validate(async (valid) => {
			if (!valid) {
				return
			}
			await showWarnConfirm("确定要关闭吗？")
			loading.value = true
			try {
				const idempotentToken = getIdempotentToken(
					IIdempotentTokenTypePre.batch,
					IIdempotentTokenType.materialGetOutWarehouse,
					"",
					IIdempotentTokenTypeAction.close
				)

				await batchUpdateMatPickClose(
					{
						ids: idList.value.toString(),
						reason: formModal.value.reason
					},
					idempotentToken
				)
				ElMessage.success("操作成功")
				emits("update")
				emits("close")
			} finally {
				loading.value = false
			}
		})
	}
}
const formElBase = [
	[
		{
			label: "关闭原因",
			placeholder: "请输入关闭领料出库的原因",
			name: "reason",
			type: "textarea",
			maxlength: inputMaxLength.textarea
		}
	]
]

// 左侧表单校验
const formRules = reactive<FormRules<typeof formModal.value>>({
	reason: {
		required: true,
		message: "关闭原因不能为空",
		trigger: "change"
	}
})

const idList = ref()

onMounted(async () => {
	idList.value = map(props.rowList || [], ({ id }) => id)
})
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<!-- 左侧表单区域 -->
		<div class="drawer-column" style="width: 100%">
			<Title :title="drawerLeftTitle" />
			<el-scrollbar class="rows">
				<div class="tips-wrap">
					<font-awesome-icon
						:icon="['fas', 'exclamation-circle']"
						style="color: #f59b22; cursor: pointer; padding-right: 2px"
					/>
					您选中
					<span class="font-bold">{{ idList?.length }}</span>
					条数据
				</div>

				<el-form
					class="content"
					:model="formModal"
					ref="formRef"
					label-position="top"
					label-width="100px"
					:rules="formRules"
				>
					<form-element :form-element="formElBase" :form-data="formModal" />
				</el-form>
			</el-scrollbar>
			<ButtonList
				class="footer"
				:button="formBtnListEdit"
				:loading="loading"
				@on-btn-click="onFormBtnList"
			/>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.tips-wrap {
	padding-left: 20px;
	font-size: 12px;
	height: 50px;
	line-height: 50px;
	border: 1px solid #f59b22;
	border-radius: 5px;
	margin: 20px 10px 10px;
	background-color: rgb(252.5, 245.7, 235.5);
	.font-bold {
		padding: 0 2px;
	}
}
</style>
