<script setup lang="ts">
import { reactive, ref } from "vue"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import { DictApi } from "@/app/baseline/api/dict"
import CostTag from "@/app/baseline/views/components/costTag.vue"
import {
	MatOutStorePickItemVO,
	MatOutStorePickSearchBatchRequestParams
} from "../../../../utils/types/store-outbound-material"
import { useDictInit } from "../../../components/dictBase"
import { useTbInit } from "../../../components/tableBase"
import { getMatPickSearchBatch } from "@/app/baseline/api/store/outbound/materialPick"
import { toFixedTwo } from "@/app/baseline/utils"
import { getRealLength, setString } from "@/app/baseline/utils/validate"

export interface Props {
	id: string | number //
	row: MatOutStorePickItemVO
}

const { getDictByCodeList, dictFilter } = useDictInit()

const props = defineProps<Props>()
const emits = defineEmits(["close"])

const loading = ref(false)
const formBtnList = [{ name: "取消", icon: ["fas", "circle-minus"] }]

const descData = reactive<MatOutStorePickItemVO>({
	...props.row,
	outedNum_view: toFixedTwo(props.row.outedNum)
})

/**
 * 左侧 title 配置
 */
const drawerLeftTitle = {
	name: ["物资信息"],
	icon: ["fas", "square-share-nodes"]
}

/**
 * 左侧 表单 配置
 */
const formEl = reactive([
	{ label: "物资编码", name: "materialCode" },
	{ label: "物资名称", name: "materialName" },
	{ label: "规格型号", name: "version" },
	{ label: "技术参数", name: "technicalParameter", needTooltip: true },
	{ label: "物资性质", name: "attribute" },
	{ label: "库存单位", name: "useUnit" },
	{ label: "出库仓库名称", name: "storeName" },
	{ label: "出库数量", name: "outedNum_view" },
	{ label: "出库批次数量", name: "outBatchTimes" }
])

/**
 * 左侧 title 配置
 */
const drawerRightTitle = {
	name: ["出库批次信息"],
	icon: ["fas", "square-share-nodes"]
}

const {
	tableData,
	tableProp,
	tableRef,
	tableLoading,
	currentPage,
	pageTotal,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange,
	onDataSelected
} = useTbInit<MatOutStorePickItemVO, MatOutStorePickSearchBatchRequestParams>()

/**
 * table 列 配置
 */
tableProp.value = [
	{
		label: "出库批次号",
		prop: "batchNo",
		minWidth: 150,
		fixed: "left"
	},
	{ label: "出库区域编码", prop: "regionCode", minWidth: 130 },
	{ label: "出库货位编码", prop: "roomCode", minWidth: 130 },

	{
		label: "出库单价",
		prop: "amount",
		align: "right",
		needSlot: true,
		minWidth: 130
	},
	{
		label: "质保有效日期",
		prop: "validityPeriod",
		minWidth: 130
	},
	{ label: "操作人", prop: "lastModifiedBy_view", minWidth: 130 },
	{ label: "出库时间", prop: "outStoreTime", minWidth: 150 }
]

/**
 * table 数据源
 * @param data
 */
const getTableData = (data?: any) => {
	if (props.id) {
		fetchFunc.value = getMatPickSearchBatch

		currentPage.value = 1
		tableRef.value?.resetCurrentPage()

		fetchParam.value = {
			id: props.id,
			sord: "desc",
			sidx: "createdDate",
			...data
		} // applyItemId:领料出库明细 ID

		fetchTableData()
	}
}

onMounted(() => {
	getDictByCodeList(["INVENTORY_UNIT"])
	getTableData()
})
</script>
<template>
	<div class="drawer-container">
		<!-- 左侧表单区域 -->
		<div class="drawer-column left">
			<div class="rows">
				<Title :title="drawerLeftTitle" />
				<el-descriptions size="small" :column="1" border class="content">
					<template>
						<el-descriptions-item
							v-for="(el, index) in formEl"
							label-align="center"
							:label="el.label"
							:key="index"
						>
							<span v-if="el.name === 'useUnit'">
								{{
									dictFilter("INVENTORY_UNIT", descData[el.name]!)
										?.subitemName || "---"
								}}
							</span>

							<dict-tag
								v-else-if="el.name === 'attribute'"
								:options="DictApi.getMatAttr()"
								:value="descData.attribute"
							/>

							<span v-else-if="el.needTooltip">
								<el-tooltip
									effect="dark"
									:content="descData?.[el.name]"
									:disabled="
										getRealLength(descData?.[el.name]) <= 100 ? true : false
									"
								>
									{{
										getRealLength(descData?.[el.name]) > 100
											? setString(descData?.[el.name], 100)
											: descData?.[el.name] || "---"
									}}
								</el-tooltip>
							</span>
							<span v-else>
								{{
									descData[el.name] || descData[el.name] == 0
										? descData[el.name]
										: "---"
								}}
							</span>
						</el-descriptions-item>
					</template>
				</el-descriptions>
			</div>
		</div>

		<!-- 右侧table区域 -->
		<div class="drawer-column right">
			<div class="rows">
				<Title :title="drawerRightTitle" />
				<el-scrollbar class="tab-mat">
					<PitayaTable
						ref="tableRef"
						:columns="tableProp"
						:table-data="tableData"
						:need-index="true"
						:need-pagination="true"
						:single-select="false"
						:need-selection="false"
						:total="pageTotal"
						@onSelectionChange="onDataSelected"
						@on-current-page-change="onCurrentPageChange"
						:table-loading="tableLoading"
					>
						<template #useUnit="{ rowData }">
							{{
								dictFilter("INVENTORY_UNIT", rowData.useUnit)?.subitemName ||
								"---"
							}}
						</template>
						<template #amount="{ rowData }">
							<cost-tag :value="rowData.amount" />
						</template>
					</PitayaTable>
				</el-scrollbar>
			</div>

			<ButtonList
				class="footer"
				:button="formBtnList"
				:loading="loading"
				@on-btn-click="emits('close')"
			/>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.drawer-container {
	.left {
		width: 310px;
	}

	.right {
		width: calc(100% - 310px);
	}

	.tab-mat {
		height: calc(100% - 43px);
	}
}
</style>
