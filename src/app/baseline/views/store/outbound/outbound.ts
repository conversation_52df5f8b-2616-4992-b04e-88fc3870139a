/* 出库状态 */
export enum OutStoreStatus {
	noOut = "1", // 未出库
	out = "2", // 已出库
	close = "3", // 已关闭
	partOut = "4" // 部分出库
}

/**
 * 出库状态 class
 * @returns
 */
export function getOutStoreStatus() {
	return [
		{
			label: "待出库",
			value: OutStoreStatus.noOut,
			raw: { class: "warning" }
		},
		{
			label: "部分出库",
			value: OutStoreStatus.partOut,
			raw: { class: "primary" }
		},
		{
			label: "已出库",
			value: OutStoreStatus.out,
			raw: { class: "success" }
		},
		{
			label: "已关闭",
			value: OutStoreStatus.close,
			raw: { class: "danger" }
		}
	]
}
