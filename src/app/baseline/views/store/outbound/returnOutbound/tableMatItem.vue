<script setup lang="ts">
import { defineProps, onMounted } from "vue"
import CostTag from "@/app/baseline/views/components/costTag.vue"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import { DictApi } from "@/app/baseline/api/dict"
import { getStoreRemoveDetail } from "../../../../api/store/outbound/return"
import {
	MatOutStoreRemoveItemRequestParams,
	MatOutStoreRemoveItemVO
} from "../../../../utils/types/store-outbound-return"
import { useDictInit } from "../../../components/dictBase"
import { useTbInit } from "../../../components/tableBase"
import { toFixedTwo } from "@/app/baseline/utils"
import { IModalType } from "@/app/baseline/utils/types/common"

const { dictOptions, getDictByCodeList, dictFilter } = useDictInit()

export interface Props {
	id: string | number //出库ID
	model?: IModalType //显示模式  view
	tbCellClassName?: (params: any) => string
}

const props = withDefaults(defineProps<Props>(), {
	id: "",
	model: IModalType.view
})

/**
 * 查询条件配置
 */
const queryArrList = computed(() => [
	{
		name: "物资编码",
		key: "materialCode",
		placeholder: "请输入物资编码",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资名称",
		key: "materialLabel",
		placeholder: "请输入物资名称",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "规格型号",
		key: "version",
		placeholder: "请输入规格型号",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资性质",
		key: "attribute",
		type: "select",
		children: dictOptions.value.MATERIAL_NATURE,
		placeholder: "请选择物资性质"
	}
])

const {
	tableData,
	tableLoading,
	tableProp,
	tableRef,
	currentPage,
	pageTotal,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = useTbInit<MatOutStoreRemoveItemVO, MatOutStoreRemoveItemRequestParams>()

/**
 * table 列配置
 */
tableProp.value = [
	{ label: "物资编码", prop: "materialCode", width: 130, fixed: "left" },
	{ label: "物资名称", prop: "materialName", minWidth: 150 },
	{ label: "规格型号", prop: "version", minWidth: 100 },
	{
		label: "技术参数",
		prop: "technicalParameter",
		minWidth: 200
	},
	{ label: "物资性质", prop: "attribute", needSlot: true, width: 120 },
	{ label: "库存单位", prop: "useUnit", needSlot: true, width: 90 },
	{ label: "货位编码", prop: "roomCode", minWidth: 100 },
	{ label: "批次号", prop: "batchNo", minWidth: 160 },
	{ label: "采购订单号", prop: "purchaseOrderCode", minWidth: 160 },
	{
		label: "采购单价",
		prop: "price",
		needSlot: true,
		minWidth: 120,
		align: "right"
	},
	{
		label: "退货数量",
		prop: "returnNum",
		needSlot: true,
		minWidth: 120,
		align: "right",
		fixed: "right"
	}
]

/**
 * table 数据源
 * @param data
 */
const getTableData = (data?: any) => {
	if (props.id) {
		fetchFunc.value = getStoreRemoveDetail

		currentPage.value = 1
		tableRef.value?.resetCurrentPage()

		fetchParam.value = {
			applyId: props.id,
			sord: "desc",
			sidx: "createdDate",
			...data
		}
		fetchTableData()
	}
}

onMounted(() => {
	getDictByCodeList(["INVENTORY_UNIT", "MATERIAL_NATURE"])
	getTableData()
})
defineExpose({ getTableData: getTableData })
</script>
<template>
	<div class="tab-mat">
		<div class="tab-left editor-table-wrapper">
			<Query
				:queryArrList="queryArrList"
				@getQueryData="getTableData"
				class="custom-q"
			/>

			<PitayaTable
				ref="tableRef"
				:columns="tableProp"
				:table-data="tableData"
				:need-index="true"
				:need-pagination="true"
				:single-select="false"
				:need-selection="false"
				:total="pageTotal"
				@on-current-page-change="onCurrentPageChange"
				:table-loading="tableLoading"
				:cell-class-name="tbCellClassName"
			>
				<!-- 物资性质 -->
				<template #attribute="{ rowData }">
					<dict-tag
						:options="DictApi.getMatAttr()"
						:value="rowData.attribute"
					/>
				</template>

				<!-- 库存单位 -->
				<template #useUnit="{ rowData }">
					{{
						dictFilter("INVENTORY_UNIT", rowData.useUnit)?.subitemName || "---"
					}}
				</template>

				<!-- 采购单价 -->
				<template #price="{ rowData }">
					<cost-tag :value="rowData.price" />
				</template>

				<!-- 退货数量 -->
				<template #returnNum="{ rowData }">
					{{ toFixedTwo(rowData.returnNum) }}
				</template>
			</PitayaTable>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.custom-q {
	margin: 10px 0px -10px 10px !important;
}

.el-input-number {
	width: 95%;
}

.tab-mat {
	display: flex;
	justify-content: space-between;
	flex-direction: row;
	height: 100%;
	width: 100%;

	.tab-left {
		width: calc(100% - 500px);
		flex: 1;
		height: 100%;
	}
}

.editor-table-wrapper {
	&:deep(.pitaya-table) {
		.error {
			.column-inner-item,
			.cell,
			.el-input__inner {
				color: red;
			}
		}
	}
}
</style>
