<script setup lang="ts">
import { FormElementType } from "@/app/baseline/views/components/define"
import { reactive, ref, toRef } from "vue"
import TableMatItem from "./tableMatItem.vue"
import TableFile from "@/app/baseline/views/components/tableFile.vue"
import { fileBusinessType } from "@/app/baseline/api/dict"
import { MatOutStoreRemoveVO } from "../../../../utils/types/store-outbound-return"
import {
	getStoreRemoveById,
	updateStoreRemove
} from "@/app/baseline/api/store/outbound/return"
import {
	IIdempotentTokenType,
	IIdempotentTokenTypePre,
	IModalType
} from "@/app/baseline/utils/types/common"
import { useMessageBoxInit } from "../../../components/messageBox"
import { includes } from "lodash-es"
import { getIdempotentToken } from "@/app/baseline/utils/validate"

const { showWarnConfirm } = useMessageBoxInit()

const props = defineProps<{
	id: any //
	model?: IModalType //显示模式 view, viewPlan
	row: MatOutStoreRemoveVO
}>()

const emits = defineEmits<{
	(e: "update"): void
	(e: "close"): void
}>()

const matTableRef = ref()

/**
 * 物资明细 table 行样式
 *
 * 库存不足物资，用红色标记该行
 */
const errorGoodsIdList = ref<number[]>([])
function tbCellClassName({ row }: any) {
	return includes(errorGoodsIdList.value, row.id) ? "error" : ""
}

const currentId = toRef(props, "id")
const drawerLoading = ref(false)

const formBtnLoading = ref(false)
const formBtnList = computed(() => {
	if (props.model === IModalType.view) {
		return [{ name: "取消", icon: ["fas", "circle-minus"] }]
	} else {
		return [
			{ name: "取消", icon: ["fas", "circle-minus"] },
			{ name: "确认", icon: ["fas", "circle-check"] }
		]
	}
})

const formModal = ref<MatOutStoreRemoveVO>({
	...props.row
})

/**
 * 左侧title 配置
 */
const drawerLeftTitle = {
	name: ["退货出库"],
	icon: ["fas", "square-share-nodes"]
}

/**
 * 左侧 表单 配置
 */
const formEl = reactive<FormElementType[]>([
	{
		label: "出库单号",
		name: "code"
	},
	{
		label: "关联退货单号",
		name: "preBusinessCode"
	},
	{
		label: "出库仓库名称",
		name: "storeName"
	},
	{
		label: "出库物资编码",
		name: "outedCount"
	},
	{
		label: "申请人",
		name: "userName_view"
	},
	{
		label: "申请时间",
		name: "applyDate"
	}
])

/**
 * title 配置
 */
const drawerRightTitle = {
	name: ["扩展信息"],
	icon: ["fas", "square-share-nodes"]
}

/**
 * tab 配置
 */
const tabList = ["物资明细", "相关附件"]
const activeTab = ref<number>(0)
const handleTabChange = (index: number) => {
	activeTab.value = index
}

/**
 * 表单按钮 操作
 * @param btnName
 */
const onFormBtnList = async (btnName: string | undefined) => {
	if (btnName === "确认") {
		await showWarnConfirm("请确认是否退货出库？")
		formBtnLoading.value = true
		try {
			const idempotentToken = getIdempotentToken(
				IIdempotentTokenTypePre.other,
				IIdempotentTokenType.goodsReturnOutWarehouse,
				props.id
			)

			const { code, msg, data } = await updateStoreRemove(
				{
					ids: props.id as any
				},
				idempotentToken
			)
			if (data && code != 200) {
				errorGoodsIdList.value = data || []
				matTableRef.value?.getTableData()
				ElMessage.error(msg)
			} else {
				ElMessage.success("操作成功")
				emits("update")
				emits("close")
			}
		} finally {
			formBtnLoading.value = false
		}
	} else {
		emits("close")
	}
}

/**
 * 详情
 */
const getDetail = () => {
	if (!props.id) {
		return false
	}
	drawerLoading.value = true
	getStoreRemoveById(props.id as number)
		.then((res: any) => {
			formModal.value = res
		})
		.finally(() => {
			drawerLoading.value = false
		})
}
onMounted(() => {
	getDetail()
})
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<!-- 左侧表单区域 -->
		<div class="drawer-column left">
			<div class="rows">
				<Title :title="drawerLeftTitle" />
				<el-descriptions size="small" :column="1" border class="content">
					<template>
						<el-descriptions-item
							v-for="(el, index) in formEl"
							label-align="center"
							:label="el.label"
							:key="index"
						>
							<span>
								{{
									formModal[el.name] || formModal[el.name] == 0
										? formModal[el.name]
										: "---"
								}}
							</span>
						</el-descriptions-item>
					</template>
				</el-descriptions>
			</div>
		</div>

		<!-- 右侧table区域 -->
		<div class="drawer-column right">
			<div class="rows">
				<Title :title="drawerRightTitle">
					<Tabs class="tabs" :tabs="tabList" @on-tab-change="handleTabChange" />
				</Title>

				<div v-if="activeTab === 0">
					<TableMatItem
						:id="currentId"
						:model="props.model"
						:need-query="true"
						:tb-cell-class-name="tbCellClassName"
						ref="matTableRef"
					/>
				</div>
				<div v-if="activeTab === 1" :class="currentId ? '' : 'disabled'">
					<TableFile
						:business-type="fileBusinessType.returnOutbound"
						:business-id="currentId"
						mod="view"
					/>
				</div>
			</div>

			<ButtonList
				class="footer"
				:button="formBtnList"
				:loading="formBtnLoading"
				@on-btn-click="onFormBtnList"
			/>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.drawer-container {
	.left {
		width: 310px;
	}

	.right {
		width: calc(100% - 310px);
	}

	.tab-mat {
		height: 100%;
	}
}
</style>
