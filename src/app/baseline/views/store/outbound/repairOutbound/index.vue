<!-- 返修出库 -->
<script lang="ts" setup>
import { onMounted, ref, computed } from "vue"
import {
	checkPermission,
	isCheckPermission
} from "@/app/platform/utils/permission"
import { powerList } from "../../../components/define.d"
import LinkTag from "@/app/baseline/views/components/linkTag.vue"
import costTag from "@/app/baseline/views/components/costTag.vue"
import repairOutboundDetail from "./repairOutboundDetail.vue"
import { first, omit } from "lodash-es"
import repairApplyDetail from "../../../waste/repairApply/repairDetail.vue"
import { listMatStoragePaged } from "@/app/baseline/api/store/manage-api"
import { modalSize } from "@/app/baseline/utils/layout-config"
import { useTbInit } from "../../../components/tableBase"
import { IModalType } from "@/app/baseline/utils/types/common"
import {
	getRepairOutBpmStatusCnt,
	listRepairOutPaged
} from "@/app/baseline/api/store/outbound/repair"
import {
	MatOutStoreRepairPageVO,
	MatOutStoreRepairPageVORequest
} from "@/app/baseline/utils/types/store-outbound-repair"
import { OutStoreStatus } from "../outbound"
import { tableColFilter } from "@/app/baseline/utils"
import closeEditor from "./closeEditor.vue"

const closeEditorVisible = ref(false)

/**
 * 查询条件 配置
 */
const queryArrList = computed(() => {
	return [
		{
			name: "出库单号",
			key: "code",
			placeholder: "请输入出库单号",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "关联返修单号",
			key: "preBusinessCode",
			placeholder: "请输入关联返修单号",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "申请人",
			key: "userName",
			placeholder: "请输入申请人",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "出库仓库",
			key: "storeId",
			placeholder: "请选择出库仓库",
			type: "tableSelect",
			tableInfo: [
				{
					title: "请选择出库仓库",
					tableApi: listMatStoragePaged, //表格接口
					tableColumns: [
						{ label: "仓库编码", prop: "code", width: 120 },
						{ label: "仓库名称", prop: "label" },
						{
							label: "仓库级别",
							prop: "level_view",
							width: 100
						},
						{
							label: "仓库类型",
							prop: "type_view",
							width: 120
						},
						{
							label: "所属段区",
							prop: "depotId_view",
							width: 150
						}
					],
					queryArrList: [
						{
							name: "仓库编码",
							key: "code",
							type: "input",
							placeholder: "请输入仓库编码"
						},
						{
							name: "仓库名称",
							key: "label",
							type: "input",
							placeholder: "请输入仓库名称"
						}
					],
					params: {
						currentPage: 1,
						pageSize: 20
					}, //表格接口参数

					labelName: {
						key: "id", //回显标识
						label: "label", //input框要展示的字段
						value: "id" //要给后台传的字段
					}
				}
			]
		}
	]
})

/**
 * table 数据源
 * @param data
 */
const getTableData = (data?: Record<string, any>) => {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		tableType: tabStatus,
		sord: "desc",
		sidx: "createdDate",
		...data
	}
	handleUpdate()
}

/**
 * title 配置
 */
const rightTitle = {
	name: ["返修出库"],
	icon: ["fas", "square-share-nodes"]
}

/**
 * tab 配置
 */
const statusCnt = ref<number[]>([])
const tabList = [
	{
		name: "待出库",
		value: OutStoreStatus.noOut,
		icon: ["fas", "square-plus"]
	},
	{
		name: "已出库",
		value: OutStoreStatus.out,
		icon: ["fas", "square-plus"]
	},
	{
		name: "已关闭",
		value: OutStoreStatus.close,
		icon: ["fas", "square-plus"]
	}
]
const tabStatus = ref<string>(tabList[0].value)
const activeName = ref<string>(tabList[0].name)
const handleTabsClick = (tab: any) => {
	activeName.value = tab.paneName
	tabStatus.value = tabList[tab.index].value
	getTableData()
}
const tbInit = useTbInit<
	MatOutStoreRepairPageVO,
	MatOutStoreRepairPageVORequest
>()
const {
	tableData,
	tableRef,
	tableLoading,
	currentPage,
	pageTotal,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = tbInit

/**
 * table 列 配置
 */
tbInit.tableProp = computed(() => {
	const tableCols: TableColumnType[] = [
		{ label: "出库单号", prop: "code", width: 180 },
		{
			label: "关联返修单号",
			prop: "preBusinessCode",
			needSlot: true,
			width: 180
		},
		{ label: "维修方式", prop: "repairWay", needSlot: true, minWidth: 120 },
		{ label: "返修时间（天）", prop: "repairDay", minWidth: 120 },
		{
			label: "返修预估费用",
			prop: "amount",
			needSlot: true,
			minWidth: 120,
			align: "right"
		},
		{ label: "出库仓库名称", prop: "storeLabel", minWidth: 120 },
		{ label: "出库物资编码", prop: "materialCodeNum", minWidth: 120 },
		{ label: "关闭原因", prop: "reason", minWidth: 150 },
		{ label: "关闭时间", prop: "closeDate", width: 150, sortable: true },
		{ label: "申请人", prop: "userName_view", minWidth: 120 },
		{ label: "申请时间", prop: "applyDate", width: 150, sortable: true },
		{
			label: "操作",
			width: 100,
			prop: "operations",
			needSlot: true
		}
	]

	switch (tabStatus.value) {
		case OutStoreStatus.close:
			return tableCols
		default:
			return tableColFilter(tableCols, ["关闭原因", "关闭时间"])
	}
})
fetchFunc.value = listRepairOutPaged

/**
 * 按钮 配置
 */
const tableFooterBtns = computed(() => {
	return [
		{
			name: "出库",
			roles: powerList.storeOutboundRepairOutboundBtnEdit,
			icon: ["fas", "arrow-up"],
			disabled: selectedTableList.value.length == 1 ? false : true
		},
		{
			name: "关闭",
			roles: powerList.storeOutboundRepairOutboundBtnClose,
			icon: ["fas", "times-circle"],
			disabled: selectedTableList.value.length > 0 ? false : true
		}
	]
})

/**
 * 出库 操作
 */
const handleTabFooterAction = async (btnName: string | undefined) => {
	if (btnName === "出库") {
		curRowId.value = first(selectedTableList.value)?.id
		editModal.value = IModalType.edit
		showViewDrawer.value = true
	} else if (btnName === "关闭") {
		closeEditorVisible.value = true
	}
}

const curRowId = ref<any>("")
const curRowData = ref<MatOutStoreRepairPageVO>({})
const showViewDrawer = ref<boolean>(false)
const editModal = ref(IModalType.view)

/**
 * 查看 操作
 * @param row
 */
const onRowView = (row: MatOutStoreRepairPageVO) => {
	curRowId.value = row.id
	curRowData.value = row
	showViewDrawer.value = true
	editModal.value = IModalType.view
}

/**
 * 关联返修单号
 */
const repairApplyVisible = ref(false)
const onRowBusinessApply = (row: MatOutStoreRepairPageVO) => {
	if (!row.preBusinessCode) {
		return false
	}
	curRowId.value = row.id
	curRowData.value = row
	repairApplyVisible.value = true
}

/**
 * 关闭抽屉
 * @param msg
 */
const closeDrawer = (msg?: string) => {
	if (msg === "pub") {
		handleUpdate()
	} else if (msg === "save") {
		handleUpdate()
		showViewDrawer.value = false
	} else {
		showViewDrawer.value = false
		repairApplyVisible.value = false
	}
}

/**
 * 更新主表/tab count
 */
const handleUpdate = async () => {
	statusCnt.value = await getRepairOutBpmStatusCnt(
		omit(
			{
				...fetchParam.value
			},
			"tableType",
			"currentPage",
			"pageSize"
		) as any
	)
	fetchTableData()
}

/**
 * 排序
 * @param column
 * @param prop 字段名称
 * @param order ascending 升序 descending 降序
 */
function handleSortChange(column: any, prop: any, order: any) {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		sord:
			order === "descending" ? "desc" : order === "ascending" ? "asc" : "desc",
		sidx: order ? (prop === "applyDate" ? "createdDate" : prop) : "createdDate" // 排序字段
	}

	fetchTableData()
}
onMounted(() => {
	getTableData()
})
</script>
<template>
	<div class="app-container">
		<div class="whole-frame">
			<ModelFrame class="top-frame">
				<Query
					:queryArrList="queryArrList"
					@getQueryData="getTableData"
					class="ml10"
				/>
			</ModelFrame>
			<ModelFrame class="bottom-frame">
				<Title :title="rightTitle">
					<div class="app-tabs-wrapper">
						<el-tabs v-model="activeName" @tab-click="handleTabsClick">
							<el-tab-pane
								v-for="(tab, index) in tabList"
								:key="index"
								:label="`${tab.name}（${statusCnt[index] ?? 0}）`"
								:name="tab.name"
								:index="tab.name"
							/>
						</el-tabs>
					</div>
				</Title>

				<PitayaTable
					ref="tableRef"
					:columns="(tbInit.tableProp as any)"
					:tableData="tableData"
					:total="pageTotal"
					:need-index="true"
					:single-select="tabStatus == tabList[0].value ? true : false"
					:need-selection="tabStatus == tabList[0].value ? true : false"
					@on-current-page-change="onCurrentPageChange"
					@onSelectionChange="selectedTableList = $event"
					:table-loading="tableLoading"
					@on-table-sort-change="handleSortChange"
				>
					<!-- 关联返修单号 -->
					<template #preBusinessCode="{ rowData }">
						<link-tag
							:value="rowData.preBusinessCode"
							@click.stop="onRowBusinessApply(rowData)"
						/>
					</template>

					<template #amount="{ rowData }">
						<cost-tag :value="rowData.amount" />
					</template>

					<template #repairWay="{ rowData }">
						{{ rowData.repairWay == 1 ? "委外维修" : "自主维修" }}
					</template>

					<template #operations="{ rowData }">
						<slot
							v-if="
								isCheckPermission(
									powerList.storeOutboundRepairOutboundBtnPreview
								)
							"
						>
							<el-button
								v-btn
								link
								@click.stop="onRowView(rowData)"
								:disabled="
									checkPermission(
										powerList.storeOutboundRepairOutboundBtnPreview
									)
								"
								v-if="
									isCheckPermission(
										powerList.storeOutboundRepairOutboundBtnPreview
									)
								"
							>
								<font-awesome-icon :icon="['fas', 'eye']" />
								<span class="table-inner-btn">查看</span>
							</el-button>
						</slot>
						<slot v-else>---</slot>
					</template>
					<template #footerOperateLeft v-if="tabStatus === tabList[0].value">
						<ButtonList
							class="btn-list"
							:is-not-radius="true"
							:button="tableFooterBtns"
							@on-btn-click="handleTabFooterAction"
						/>
					</template>
				</PitayaTable>

				<!-- 编辑/查看弹窗 -->
				<Drawer
					:size="modalSize.xl"
					v-model:drawer="showViewDrawer"
					:destroyOnClose="true"
				>
					<repair-outbound-detail
						:id="curRowId"
						:modal="editModal"
						@on-save-or-close="closeDrawer"
					/>
				</Drawer>

				<!-- 关联返修单号 -->
				<Drawer
					v-model:drawer="repairApplyVisible"
					:size="modalSize.xl"
					destroy-on-close
				>
					<repair-apply-detail
						:id="curRowData.preBusinessId!"
						@close="repairApplyVisible = false"
					/>
				</Drawer>

				<!-- 关闭原因 -->
				<Drawer
					v-model:drawer="closeEditorVisible"
					:size="350"
					destroy-on-close
				>
					<close-editor
						:id="first(selectedTableList)?.id"
						@close="closeEditorVisible = false"
						@update="handleUpdate"
					/>
				</Drawer>
			</ModelFrame>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
