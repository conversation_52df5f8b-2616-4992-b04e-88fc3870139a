<!-- 出库货位 -->
<script setup lang="ts">
import { ref } from "vue"
import { useTbInit } from "../../../components/tableBase"
import {
	getStoreRepairOutItemById,
	listRepairOutItemRoomPaged
} from "@/app/baseline/api/store/outbound/repair"
import {
	MatOutStoreRepairDetailVO,
	MatOutStoreRepairItemBatchPageVo,
	MatOutStoreRepairItemPageVO
} from "@/app/baseline/utils/types/store-outbound-repair"
import {
	toFixedTwo,
	maxTableHeight,
	tableColFilter
} from "@/app/baseline/utils"
import { OutStoreStatus } from "../outbound"
import { getRealLength, setString } from "@/app/baseline/utils/validate"

export interface Props {
	id: string | number //

	desData: MatOutStoreRepairDetailVO
}

const props = defineProps<Props>()
const emits = defineEmits(["close"])

const loading = ref(false)
const formBtnList = [{ name: "取消", icon: ["fas", "circle-minus"] }]

const formModal = ref<MatOutStoreRepairItemPageVO>({})

/**
 * 左侧 title 配置
 */
const drawerLeftTitle = {
	name: ["物资信息"],
	icon: ["fas", "square-share-nodes"]
}

/**
 * 左侧 表单 配置
 */
const formEl = computed(() => {
	const defCols = [
		{ label: "物资编码", name: "materialCode" },
		{ label: "物资名称", name: "materialName" },
		{ label: "品牌型号", name: "version" },
		{ label: "技术参数", name: "technicalParameter", needTooltip: true },
		{ label: "出库仓库名称", name: "storeName" },
		{ label: "待出库数量", name: "returnNum_view" },
		{ label: "出库数量", name: "outNum_view" }
	]

	switch (props.desData.status) {
		case OutStoreStatus.noOut:
			return defCols.filter((v) => v.label !== "出库数量")
		default:
			return defCols.filter((v) => v.label !== "待出库数量")
	}
})

/**
 * 左侧 title 配置
 */
const drawerRightTitle = {
	name: ["出库货位"],
	icon: ["fas", "square-share-nodes"]
}

const tbInit = useTbInit<
	MatOutStoreRepairItemBatchPageVo,
	Record<string, any>
>()
const {
	tableData,
	tableRef,
	tableLoading,
	currentPage,
	pageTotal,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange,
	onDataSelected
} = tbInit

/**
 * table 列 配置
 */
tbInit.tableProp = computed(() => {
	const tableCols: TableColumnType[] = [
		{ label: "出库区域", prop: "regionLabel" },
		{ label: "出库货位编码", prop: "roomCode" },
		{ label: "待出库货位编码", prop: "roomCode" },
		{ label: "待出库数量", prop: "num", align: "right", needSlot: true },
		{ label: "出库数量", prop: "num", align: "right", needSlot: true },
		{ label: "出库批次", prop: "batchNo", align: "right", width: 160 }
	]

	switch (props.desData.status) {
		case OutStoreStatus.noOut:
			return tableColFilter(tableCols, ["出库数量", "出库货位编码", "出库批次"])
		case OutStoreStatus.out:
			return tableColFilter(tableCols, ["待出库数量", "待出库货位编码"])
		default:
			return tableColFilter(tableCols, [
				"待出库数量",
				"待出库货位编码",
				"出库批次"
			])
	}
})

/**
 * table 数据源
 * @param data
 */
const getTableData = (data?: any) => {
	if (props.id) {
		fetchFunc.value = listRepairOutItemRoomPaged

		currentPage.value = 1
		tableRef.value?.resetCurrentPage()

		fetchParam.value = {
			id: props.id,
			sord: "desc",
			sidx: "createdDate",
			...data
		} // applyItemId:出库明细 ID

		fetchTableData()
	}
}

const drawerLoading = ref(false)
const getDetail = () => {
	if (!props.id) {
		return false
	}
	drawerLoading.value = true
	getStoreRepairOutItemById(props.id as number)
		.then((res: any) => {
			formModal.value = res
			formModal.value.outNum_view = toFixedTwo(res.outNum)
			formModal.value.returnNum_view = toFixedTwo(res.returnNum)
		})
		.finally(() => {
			drawerLoading.value = false
		})
}

onMounted(() => {
	getDetail()
	getTableData()
})
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<!-- 左侧表单区域 -->
		<div class="drawer-column left">
			<div class="rows">
				<Title :title="drawerLeftTitle" />
				<el-descriptions size="small" :column="1" border class="content">
					<template>
						<el-descriptions-item
							v-for="(el, index) in formEl"
							label-align="center"
							:label="el.label"
							:key="index"
						>
							<span v-if="el.needTooltip">
								<el-tooltip
									effect="dark"
									:content="formModal?.[el.name]"
									:disabled="
										getRealLength(formModal?.[el.name]) <= 100 ? true : false
									"
								>
									{{
										getRealLength(formModal?.[el.name]) > 100
											? setString(formModal?.[el.name], 100)
											: formModal?.[el.name] || "---"
									}}
								</el-tooltip>
							</span>
							<span v-else>
								{{
									formModal[el.name] || formModal[el.name] == 0
										? formModal[el.name]
										: "---"
								}}
							</span>
						</el-descriptions-item>
					</template>
				</el-descriptions>
			</div>
		</div>

		<!-- 右侧table区域 -->
		<div class="drawer-column right">
			<div class="rows">
				<Title :title="drawerRightTitle" />
				<el-scrollbar class="tab-mat">
					<PitayaTable
						ref="tableRef"
						:columns="(tbInit.tableProp as any)"
						:table-data="tableData"
						:need-index="true"
						:need-pagination="true"
						:single-select="false"
						:need-selection="false"
						:total="pageTotal"
						:max-height="maxTableHeight"
						@onSelectionChange="onDataSelected"
						@on-current-page-change="onCurrentPageChange"
						:table-loading="tableLoading"
					>
						<template #num="{ rowData }">
							{{ toFixedTwo(rowData.num) }}
						</template>
					</PitayaTable>
				</el-scrollbar>
			</div>

			<ButtonList
				class="footer"
				:button="formBtnList"
				:loading="loading"
				@on-btn-click="emits('close')"
			/>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.drawer-container {
	.left {
		width: 310px;
	}

	.right {
		width: calc(100% - 310px);
	}

	.tab-mat {
		height: calc(100% - 43px);
	}
}
</style>
