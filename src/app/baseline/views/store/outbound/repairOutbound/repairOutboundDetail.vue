<!-- 返修出库 详情 -->
<script setup lang="ts">
import { FormElementType } from "@/app/baseline/views/components/define"
import { ref } from "vue"
import costTag from "@/app/baseline/views/components/costTag.vue"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import TableFile from "@/app/baseline/views/components/tableFile.vue"
import { DictApi, fileBusinessType } from "@/app/baseline/api/dict"
import {
	IIdempotentTokenType,
	IIdempotentTokenTypePre,
	IModalType
} from "@/app/baseline/utils/types/common"
import { useMessageBoxInit } from "../../../components/messageBox"
import {
	getStoreRepairOutById,
	listRepairOutItemPaged,
	updateStoreRepairOut
} from "@/app/baseline/api/store/outbound/repair"
import {
	MatOutStoreRepairItemPageVO,
	MatOutStoreRepairPageVO
} from "@/app/baseline/utils/types/store-outbound-repair"
import { useDictInit } from "../../../components/dictBase"
import { useTbInit } from "../../../components/tableBase"
import { tableColFilter, toFixedTwo } from "@/app/baseline/utils"
import { OutStoreStatus } from "../outbound"
import { includes } from "lodash-es"
import { getIdempotentToken } from "@/app/baseline/utils/validate"

export interface Props {
	id: string | number //
	modal?: IModalType //显示模式 view, viewPlan
}
const props = withDefaults(defineProps<Props>(), { modal: IModalType.view })

const { showWarnConfirm } = useMessageBoxInit()

const { dictOptions, getDictByCodeList, dictFilter } = useDictInit()

const emits = defineEmits<{
	(e: "onSaveOrClose", msg?: string | undefined): void
}>()

/**
 * 物资明细 table 行样式
 *
 * 库存不足物资，用红色标记该行
 */
const errorGoodsIdList = ref<number[]>([])
function tbCellClassName({ row }: any) {
	return includes(errorGoodsIdList.value, row.id) ? "error" : ""
}

const formBtnLoading = ref(false)
const drawerLoading = ref(false)
const formBtnList = computed(() => {
	if (props.modal === IModalType.view) {
		return [{ name: "取消", icon: ["fas", "circle-minus"] }]
	} else {
		return [
			{ name: "取消", icon: ["fas", "circle-minus"] },
			{ name: "确认", icon: ["fas", "circle-check"] }
		]
	}
})

const formModal = ref<MatOutStoreRepairPageVO>({})

/**
 * 左侧title 配置
 */
const drawerLeftTitle = {
	name: ["返修出库"],
	icon: ["fas", "square-share-nodes"]
}

/**
 * 左侧 表单 配置
 */
const formEl = computed<FormElementType[]>(() => {
	const ls = [
		{ label: "出库单号", name: "code" },
		{ label: "关联返修单号", name: "preBusinessCode" },
		{ label: "出库仓库名称", name: "storeLabel" },
		{ label: "返修方式", name: "repairWay" },
		{ label: "返修公司", name: "repairCommunityLabel" },
		{ label: "返修部门", name: "repairOrgId_view" },
		{ label: "返修时间（天）", name: "repairDay" },
		{ label: "返修预估费用", name: "amount" },
		{ label: "出库物资编码", name: "materialCodeNum" },
		{ label: "申请人", name: "userName_view" },
		{ label: "申请时间", name: "applyDate" }
	]

	if (formModal.value.repairWay == 1) {
		return ls.filter((item) => item.label != "返修部门")
	} else {
		return ls.filter((item) => item.label != "返修公司")
	}
})

/**
 * title 配置
 */
const drawerRightTitle = {
	name: ["扩展信息"],
	icon: ["fas", "square-share-nodes"]
}

/**
 * tab 配置
 */
const tabList = ["物资明细", "相关附件"]
const activeTab = ref<number>(0)
const handleTabChange = (index: number) => {
	activeTab.value = index

	if (index === 0) {
		fetchParam.value = {
			applyId: props.id,
			sord: "desc",
			sidx: "createdDate"
		}
		pageSize.value = 20
		getTableData()
	}
}

/**
 * 表单按钮 操作
 * @param btnName
 */
const onFormBtnList = async (btnName: string | undefined) => {
	if (btnName === "确认") {
		await showWarnConfirm("请确认是否返修出库？")
		formBtnLoading.value = true
		try {
			const idempotentToken = getIdempotentToken(
				IIdempotentTokenTypePre.other,
				IIdempotentTokenType.repairOutWarehouse,
				props.id
			)
			const { code, msg, data } = await updateStoreRepairOut(
				props.id as number,
				idempotentToken
			)
			if (data && code != 200) {
				errorGoodsIdList.value = data || []
				ElMessage.error(msg)
				getTableData()
			} else {
				ElMessage.success("操作成功")
				emits("onSaveOrClose", "save")
			}
		} finally {
			formBtnLoading.value = false
		}
	} else {
		emits("onSaveOrClose")
	}
}

/**
 * 详情
 */
const getDetail = () => {
	if (!props.id) {
		return false
	}
	drawerLoading.value = true
	getStoreRepairOutById(props.id as number)
		.then((res: any) => {
			formModal.value = res
		})
		.finally(() => {
			drawerLoading.value = false
		})
}
onMounted(() => {
	getDictByCodeList(["INVENTORY_UNIT", "MATERIAL_NATURE"])
	getDetail()

	getTableData()
})

/**
 * 查询条件配置
 */
const queryArrList = computed(() => [
	{
		name: "物资编码",
		key: "materialCode",
		placeholder: "请输入物资编码",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资名称",
		key: "materialLabel",
		placeholder: "请输入物资名称",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "规格型号",
		key: "version",
		placeholder: "请输入规格型号",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资性质",
		key: "attribute",
		type: "select",
		children: dictOptions.value.MATERIAL_NATURE,
		placeholder: "请选择物资性质"
	}
])

const tbInit = useTbInit<MatOutStoreRepairItemPageVO, Record<string, any>>()

const {
	tableData,
	tableLoading,
	tableRef,
	currentPage,
	pageTotal,
	pageSize,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = tbInit

/**
 * table 列配置
 */
tbInit.tableProp = computed(() => {
	const tableCols: TableColumnType[] = [
		{ label: "物资编码", prop: "materialCode", width: 130, fixed: "left" },
		{ label: "物资名称", prop: "materialName", minWidth: 120 },
		{ label: "规格型号", prop: "version", minWidth: 100 },
		{
			label: "技术参数",
			prop: "technicalParameter",
			minWidth: 150
		},
		{ label: "物资性质", prop: "attribute", needSlot: true, width: 120 },
		{ label: "库存单位", prop: "useUnit", needSlot: true, width: 90 },
		{ label: "出库区域编码", prop: "regionCode" },
		{ label: "出库货位编码", prop: "roomCode" },
		{ label: "出库批次", prop: "batchNo", width: 180 },
		{ label: "返修数量", prop: "num", align: "right", needSlot: true },
		{ label: "出库数量", prop: "num", align: "right", needSlot: true },
		{ label: "关闭数量", prop: "num", align: "right", needSlot: true }
	]

	switch (formModal.value.status) {
		case OutStoreStatus.noOut:
			return tableColFilter(tableCols, ["出库数量", "关闭数量"])
		case OutStoreStatus.out:
			return tableColFilter(tableCols, ["返修数量", "关闭数量"])
		default:
			return tableColFilter(tableCols, ["出库数量", "返修数量"])
	}
})

/**
 * table 数据源
 * @param data
 */
const getTableData = (data?: any) => {
	if (props.id) {
		fetchFunc.value = listRepairOutItemPaged

		currentPage.value = 1
		tableRef.value?.resetCurrentPage()

		fetchParam.value = {
			applyId: props.id,
			sord: "desc",
			sidx: "createdDate",
			...data
		}
		fetchTableData()
	}
}
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<!-- 左侧表单区域 -->
		<div class="drawer-column left">
			<div class="rows">
				<Title :title="drawerLeftTitle" />
				<el-descriptions size="small" :column="1" border class="content">
					<template>
						<el-descriptions-item
							v-for="(el, index) in formEl"
							label-align="center"
							:label="el.label"
							:key="index"
						>
							<span v-if="el.name == 'amount'">
								<cost-tag :value="formModal[el.name]" />
							</span>
							<span v-else-if="el.name == 'repairWay'">
								{{ formModal[el.name] == 1 ? "委外维修" : "自主维修" }}
							</span>
							<span v-else>
								{{
									formModal[el.name] || formModal[el.name] == 0
										? formModal[el.name]
										: "---"
								}}
							</span>
						</el-descriptions-item>
					</template>
				</el-descriptions>
			</div>
		</div>

		<!-- 右侧table区域 -->
		<div class="drawer-column right">
			<div class="rows">
				<Title :title="drawerRightTitle">
					<Tabs class="tabs" :tabs="tabList" @on-tab-change="handleTabChange" />
				</Title>

				<el-scrollbar v-if="activeTab === 0" class="editor-table-wrapper">
					<Query
						:queryArrList="queryArrList"
						@getQueryData="getTableData"
						class="custom-q"
					/>
					<PitayaTable
						ref="tableRef"
						:columns="(tbInit.tableProp as any)"
						:table-data="tableData"
						:need-index="true"
						:need-pagination="true"
						:single-select="false"
						:need-selection="false"
						:total="pageTotal"
						@on-current-page-change="onCurrentPageChange"
						:table-loading="tableLoading"
						:cell-class-name="tbCellClassName"
					>
						<!-- 物资性质 -->
						<template #attribute="{ rowData }">
							<dict-tag
								:options="DictApi.getMatAttr()"
								:value="rowData.attribute"
							/>
						</template>

						<!-- 库存单位 -->
						<template #useUnit="{ rowData }">
							{{
								dictFilter("INVENTORY_UNIT", rowData.useUnit)?.subitemName ||
								"---"
							}}
						</template>

						<!-- 返修数量 -->
						<template #num="{ rowData }">
							{{ toFixedTwo(rowData.num) }}
						</template>
					</PitayaTable>
				</el-scrollbar>
				<div v-if="activeTab === 1">
					<TableFile
						:business-type="fileBusinessType.repairOutbound"
						:business-id="props.id"
						:mod="props.modal"
					/>
				</div>
			</div>

			<ButtonList
				class="footer"
				:button="formBtnList"
				:loading="formBtnLoading"
				@on-btn-click="onFormBtnList"
			/>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.custom-q {
	margin: 10px 0px -10px 10px !important;
}

.drawer-container {
	.left {
		width: 310px;
	}

	.right {
		width: calc(100% - 310px);
	}

	.tab-mat {
		height: 100%;
	}
}

.editor-table-wrapper {
	&:deep(.pitaya-table) {
		.error {
			.column-inner-item,
			.cell,
			.el-input__inner {
				color: red;
			}
		}
	}
}
</style>
