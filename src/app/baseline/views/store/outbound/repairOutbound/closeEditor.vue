<!-- 返修出库 关闭窗 -->
<script setup lang="ts">
import { ElMessage, FormInstance, FormRules } from "element-plus"
import { FormElementType } from "@/app/baseline/views/components/define"
import { onMounted, reactive, ref } from "vue"
import costTag from "@/app/baseline/views/components/costTag.vue"

import { useMessageBoxInit } from "../../../components/messageBox"
import formElement from "../../../components/formElement.vue"
import { inputMaxLength } from "@/app/baseline/utils/layout-config"

import {
	getStoreRepairOutById,
	updateStoreRepairOutClose
} from "@/app/baseline/api/store/outbound/repair"
import { MatOutStoreRepairDetailVO } from "@/app/baseline/utils/types/store-outbound-repair"
import { getIdempotentToken } from "@/app/baseline/utils/validate"
import {
	IIdempotentTokenType,
	IIdempotentTokenTypePre
} from "@/app/baseline/utils/types/common"

export interface Props {
	id: any
}
const { showWarnConfirm } = useMessageBoxInit()

const props = defineProps<Props>()

const emits = defineEmits<{
	(e: "update"): void
	(e: "close"): void
}>()
const loading = ref(false)

const drawerLoading = ref(false)

const formRef = ref<FormInstance>()

/**
 * 按钮 配置
 */
const formBtnListEdit = [
	{ name: "取消", icon: ["fas", "circle-minus"] },
	{ name: "确认", icon: ["fas", "circle-check"] }
]

/**
 * 表单数据源
 */
const formModal = ref<Record<string, any>>({
	reason: ""
})

const descData = ref<MatOutStoreRepairDetailVO>({})

/**
 * 左侧title 配置
 */
const drawerLeftTitle = {
	name: ["关闭返修出库"],
	icon: ["fas", "square-share-nodes"]
}

/**
 * 左侧 表单 配置
 */
const formEl = computed<FormElementType[]>(() => {
	const ls = [
		{ label: "出库单号", name: "code" },
		{ label: "关联返修单号", name: "preBusinessCode" },
		{ label: "出库仓库名称", name: "storeLabel" },
		{ label: "返修方式", name: "repairWay" },
		{ label: "返修公司", name: "repairCommunityLabel" },
		{ label: "返修部门", name: "repairOrgId_view" },
		{ label: "返修时间（天）", name: "repairDay" },
		{ label: "返修预估费用", name: "amount" },
		{ label: "出库物资编码", name: "materialCodeNum" },
		{ label: "申请人", name: "userName_view" },
		{ label: "申请时间", name: "applyDate" }
	]

	if (descData.value.repairWay == 1) {
		return ls.filter((item) => item.label != "返修部门")
	} else {
		return ls.filter((item) => item.label != "返修公司")
	}
})

/**
 *表 单按钮 操作
 * @param btnName
 */
const onFormBtnList = async (btnName: string | undefined) => {
	if (btnName === "取消") {
		emits("close")
	} else if (btnName === "确认") {
		if (!formRef.value) {
			return
		}
		formRef.value?.validate(async (valid) => {
			if (!valid) {
				return
			}
			await showWarnConfirm("请确认是否关闭此出库单？")
			loading.value = true
			try {
				const idempotentToken = getIdempotentToken(
					IIdempotentTokenTypePre.other,
					IIdempotentTokenType.repairOutWarehouse,
					props.id
				)

				await updateStoreRepairOutClose(
					{
						ids: props.id,
						reason: formModal.value.reason
					},
					idempotentToken
				)
				ElMessage.success("操作成功")
				emits("update")
				emits("close")
			} finally {
				loading.value = false
			}
		})
	}
}
const formElBase = [
	[
		{
			label: "关闭原因",
			placeholder: "请输入关闭返修出库的原因",
			name: "reason",
			type: "textarea",
			maxlength: inputMaxLength.textarea
		}
	]
]

// 左侧表单校验
const formRules = reactive<FormRules<typeof formModal.value>>({
	reason: {
		required: true,
		message: "关闭原因不能为空",
		trigger: "change"
	}
})
const getDetail = () => {
	if (!props.id) {
		return false
	}
	drawerLoading.value = true

	getStoreRepairOutById(props.id as number)
		.then((res: any) => {
			descData.value = res
		})
		.finally(() => {
			drawerLoading.value = false
		})
}

onMounted(async () => {
	getDetail()
})
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<!-- 左侧表单区域 -->
		<div class="drawer-column">
			<Title :title="drawerLeftTitle" />
			<el-scrollbar class="rows">
				<el-descriptions size="small" :column="1" border class="content">
					<template>
						<el-descriptions-item
							v-for="(el, index) in formEl"
							label-align="center"
							:label="el.label"
							:key="index"
						>
							<span v-if="el.name == 'amount'">
								<cost-tag :value="descData[el.name]" />
							</span>
							<span v-else-if="el.name == 'repairWay'">
								{{ descData[el.name] == 1 ? "委外维修" : "自主维修" }}
							</span>
							<span v-else>
								{{
									descData[el.name] || descData[el.name] == 0
										? descData[el.name]
										: "---"
								}}
							</span>
						</el-descriptions-item>
					</template>
				</el-descriptions>

				<el-form
					class="content"
					:model="formModal"
					ref="formRef"
					label-position="top"
					label-width="100px"
					:rules="formRules"
				>
					<form-element :form-element="formElBase" :form-data="formModal" />
				</el-form>
			</el-scrollbar>
			<ButtonList
				class="footer"
				:button="formBtnListEdit"
				:loading="loading"
				@on-btn-click="onFormBtnList"
			/>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
