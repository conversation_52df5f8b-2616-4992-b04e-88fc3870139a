<!-- 其他出库 -->
<script lang="ts" setup>
import { onMounted, ref, computed } from "vue"
import {
	checkPermission,
	isCheckPermission
} from "@/app/platform/utils/permission"
import { powerList } from "../../../components/define.d"
import { useTbInit } from "../../../components/tableBase"
import LinkTag from "@/app/baseline/views/components/linkTag.vue"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import otherOutboundDetail from "./otherOutboundDetail.vue"
import {
	MatOutStorePickRequestParams,
	MatOutStorePickVO
} from "../../../../utils/types/store-outbound-material"
import { omit } from "lodash-es"
import handoverApplyDetail from "../../../waste/handoverApply/handoverApplyDetail.vue"
import { listMatStoragePaged } from "@/app/baseline/api/store/manage-api"
import { modalSize } from "@/app/baseline/utils/layout-config"
import { OutStoreStatus, getOutStoreStatus } from "../outbound"

import { tableColFilter } from "@/app/baseline/utils"
import { IModalType } from "@/app/baseline/utils/types/common"
import {
	getMatOutOtherBpmStatus,
	getMatOutOtherList
} from "@/app/baseline/api/store/outbound/other"

/**
 * 查询条件 配置
 */
const queryArrList = computed(() => {
	return [
		{
			name: "出库单号",
			key: "code",
			placeholder: "请输入出库单号",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "关联业务单号",
			key: "preBusinessCode",
			placeholder: "请输入关联业务单号",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "申请人",
			key: "userName",
			placeholder: "请输入申请人",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "出库仓库",
			key: "storeId",
			placeholder: "请选择出库仓库",
			type: "tableSelect",
			tableInfo: [
				{
					title: "请选择出库仓库",
					tableApi: listMatStoragePaged, //表格接口
					tableColumns: [
						{ label: "仓库编码", prop: "code", width: 120 },
						{ label: "仓库名称", prop: "label" },
						{
							label: "仓库级别",
							prop: "level_view",
							width: 100
						},
						{
							label: "仓库类型",
							prop: "type_view",
							width: 120
						},
						{
							label: "所属段区",
							prop: "depotId_view",
							width: 150
						}
					],
					queryArrList: [
						{
							name: "仓库编码",
							key: "code",
							type: "input",
							placeholder: "请输入仓库编码"
						},
						{
							name: "仓库名称",
							key: "label",
							type: "input",
							placeholder: "请输入仓库名称"
						}
					],
					params: {
						currentPage: 1,
						pageSize: 20
					}, //表格接口参数

					labelName: {
						key: "id", //回显标识
						label: "label", //input框要展示的字段
						value: "id" //要给后台传的字段
					}
				}
			]
		}
	]
})

/**
 * table 数据源
 * @param data
 */
const getTableData = (data?: Record<string, any>) => {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		tableType: tabStatus,
		sord: "desc",
		sidx: "createdDate",
		...data
	}

	handleUpdate()
}

/**
 * title 配置
 */
const statusCnt = ref<number[]>([])
const rightTitle = {
	name: ["其他出库"],
	icon: ["fas", "square-share-nodes"]
}

/**
 * tab 配置
 */
const tabList = [
	{
		name: "待出库",
		value: `${OutStoreStatus.noOut},${OutStoreStatus.partOut}`,
		icon: ["fas", "square-plus"]
	},
	{
		name: "已出库",
		value: OutStoreStatus.out,
		icon: ["fas", "square-plus"]
	}
]
const tabStatus = ref<string>(tabList[0].value)
const activeName = ref<string>(tabList[0].name)
const handleTabsClick = (tab: any) => {
	activeName.value = tab.paneName
	tabStatus.value = tabList[tab.index].value
	getTableData()
}

const tbInit = useTbInit<MatOutStorePickVO, MatOutStorePickRequestParams>()
const {
	tableData,
	tableRef,
	tableLoading,
	currentPage,
	pageTotal,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = tbInit

/**
 * table 列配置
 */
tbInit.tableProp = computed(() => {
	const tableCols: TableColumnType[] = [
		{
			label: "出库单号",
			prop: "code",
			width: 180
		},
		{
			label: "关联业务单号",
			prop: "preBusinessCode",
			needSlot: true,
			width: 180
		},
		{ label: "业务类型", prop: "wasteOldType_view" },
		{ label: "出库仓库名称", prop: "storeName", minWidth: 120 },
		{ label: "出库货位编码", prop: "roomCode", width: 120 },
		{ label: "出库物资编码", prop: "outCount", width: 120 },
		{ label: "待出库物资编码", prop: "toOutCount", minWidth: 120 },
		{ label: "已出库物资编码", prop: "outedCount", minWidth: 120 },
		{ label: "出库状态", prop: "status", needSlot: true, width: 120 },
		{ label: "出库完成时间", prop: "applyFinishDate", width: 160 },
		{ label: "申请人", prop: "userName_view", width: 120 },
		{ label: "申请时间", prop: "createdDate", width: 160, sortable: true },
		{
			label: "操作",
			width: 100,
			prop: "operations",
			needSlot: true,
			fixed: "right"
		}
	]

	switch (tabStatus.value) {
		case `${OutStoreStatus.noOut},${OutStoreStatus.partOut}`:
			return tableColFilter(tableCols, ["出库完成时间"])

		default:
			return tableColFilter(tableCols, [
				"出库状态",
				"待出库物资编码",
				"已出库物资编码"
			])
	}
})

fetchFunc.value = getMatOutOtherList

const curRowId = ref<any>("")
const curRowData = ref<MatOutStorePickVO>({})
const showViewDrawer = ref<boolean>(false)
const model = ref(IModalType.view)

/**
 * 查看 操作
 * @param row
 */
const onRowView = (row: MatOutStorePickVO) => {
	curRowId.value = row.id
	curRowData.value = row
	showViewDrawer.value = true
	model.value = IModalType.view
}

/**
 * 关联领料业务单号
 */
const showRelativeCodeDrawer = ref(false)
const onRowBusinessOrder = (row: MatOutStorePickVO) => {
	curRowId.value = row.id
	curRowData.value = row
	showRelativeCodeDrawer.value = true
}

/**
 * 更新主表/tab count
 */
const handleUpdate = async () => {
	fetchTableData()
	statusCnt.value = await getMatOutOtherBpmStatus(
		omit(
			{
				...fetchParam.value
			},
			"tableType",
			"currentPage",
			"pageSize"
		) as any
	)
}

/**
 * 排序
 * @param column
 * @param prop 字段名称
 * @param order ascending 升序 descending 降序
 */
function handleSortChange(column: any, prop: any, order: any) {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		sord:
			order === "descending" ? "desc" : order === "ascending" ? "asc" : "desc",
		sidx: order ? prop : "createdDate" // 排序字段
	}

	fetchTableData()
}

onMounted(() => {
	getTableData()
})
</script>
<template>
	<div class="app-container">
		<div class="whole-frame">
			<ModelFrame class="top-frame">
				<Query
					:queryArrList="queryArrList"
					@getQueryData="getTableData"
					class="ml10"
				/>
			</ModelFrame>
			<ModelFrame class="bottom-frame">
				<Title :title="rightTitle">
					<div class="app-tabs-wrapper">
						<el-tabs v-model="activeName" @tab-click="handleTabsClick">
							<el-tab-pane
								v-for="(tab, index) in tabList"
								:key="index"
								:label="`${tab.name}（${statusCnt[index] ?? 0}）`"
								:name="tab.name"
								:index="tab.name"
							/>
						</el-tabs>
					</div>
				</Title>

				<PitayaTable
					ref="tableRef"
					:columns="(tbInit.tableProp as any)"
					:tableData="tableData"
					:total="pageTotal"
					:need-index="true"
					:single-select="false"
					:need-selection="false"
					@on-current-page-change="onCurrentPageChange"
					@onSelectionChange="selectedTableList = $event"
					:table-loading="tableLoading"
					@on-table-sort-change="handleSortChange"
				>
					<!-- 关联领料单号 -->
					<template #preBusinessCode="{ rowData }">
						<link-tag
							:value="rowData.preBusinessCode"
							@on-click="onRowBusinessOrder(rowData)"
						/>
					</template>

					<template #status="{ rowData }">
						<dict-tag :value="rowData.status" :options="getOutStoreStatus()" />
					</template>

					<template #operations="{ rowData }">
						<slot
							v-if="
								isCheckPermission(
									powerList.storeOutboundOtherOutboundBtnPreview
								)
							"
						>
							<el-button
								v-btn
								link
								@click="onRowView(rowData)"
								:disabled="
									checkPermission(
										powerList.storeOutboundOtherOutboundBtnPreview
									)
								"
								v-if="
									isCheckPermission(
										powerList.storeOutboundOtherOutboundBtnPreview
									)
								"
							>
								<font-awesome-icon :icon="['fas', 'eye']" />
								<span class="table-inner-btn">查看</span>
							</el-button>
						</slot>
						<slot v-else>---</slot>
					</template>
				</PitayaTable>

				<!-- 编辑/查看弹窗 -->
				<Drawer
					:size="modalSize.xl"
					v-model:drawer="showViewDrawer"
					:destroyOnClose="true"
				>
					<other-outbound-detail
						:id="curRowId"
						:model="model"
						@close="showViewDrawer = false"
					/>
				</Drawer>

				<!-- 关联领料单号 -->
				<Drawer
					:size="modalSize.xl"
					v-model:drawer="showRelativeCodeDrawer"
					:destroyOnClose="true"
				>
					<handover-apply-detail
						:id="curRowData.preBusinessId!"
						@close="showRelativeCodeDrawer = false"
					/>
				</Drawer>
			</ModelFrame>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
