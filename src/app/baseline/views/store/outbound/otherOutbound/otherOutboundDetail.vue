<script setup lang="ts">
import { FormElementType } from "@/app/baseline/views/components/define"
import { ref } from "vue"
import { modalSize } from "@/app/baseline/utils/layout-config"
import { DictApi } from "@/app/baseline/api/dict"
import {
	MatOutStorePickItemRequestParams,
	MatOutStorePickItemVO,
	MatOutStorePickVO,
	IMatOutStoreRoomStatus
} from "../../../../utils/types/store-outbound-material"
import { IModalType } from "@/app/baseline/utils/types/common"
import { tableColFilter, toFixedTwo } from "@/app/baseline/utils"
import { useTbInit } from "../../../components/tableBase"
import { useDictInit } from "../../../components/dictBase"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import LinkTag from "@/app/baseline/views/components/linkTag.vue"
import batchDetail from "./batchDetail.vue"
import roomDetail from "./roomDetail.vue"
import { OutStoreStatus, getOutStoreStatus } from "../outbound"
import {
	getMatOutOtherById,
	getMatOutOtherDetail
} from "@/app/baseline/api/store/outbound/other"
import { IWasteOldType } from "@/app/baseline/utils/types/waste-handover-apply"

const { dictOptions, getDictByCodeList, dictFilter } = useDictInit()

const props = withDefaults(
	defineProps<{
		id: string | number
		model?: IModalType
	}>(),
	{
		model: IModalType.view
	}
)
const emits = defineEmits<{
	(e: "close"): void
	(e: "update"): void
}>()

const formBtnList = [{ name: "取消", icon: ["fas", "circle-minus"] }]

const descData = ref<MatOutStorePickVO>({})

const drawerLoading = ref(false)

/**
 * 左侧title 配置
 */
const drawerLeftTitle = {
	name: ["其他出库"],
	icon: ["fas", "square-share-nodes"]
}

/**
 * 左侧 表单 配置
 */
const formEl = computed<FormElementType[]>(() => {
	const descOpt = [
		{ label: "出库单号", name: "code" },
		{ label: "关联业务单号", name: "preBusinessCode" },
		{ label: "业务类型", name: "wasteOldType_view" },
		{ label: "出库仓库名称", name: "storeName" },
		{ label: "出库货位编码", name: "roomCode" },
		{ label: "出库物资编码", name: "outCount" },
		{ label: "待出库物资编码", name: "toOutCount" },
		{ label: "已出库物资编码", name: "outedCount" },
		{ label: "出库完成时间", name: "applyFinishDate" },
		{ label: "申请人", name: "userName_view" },
		{ label: "申请时间", name: "createdDate" },
		{ label: "出库状态", name: "status" }
	]

	switch (descData.value.status) {
		case OutStoreStatus.out:
			return descOpt.filter(
				(v) =>
					!["已出库物资编码", "待出库物资编码", "出库状态"].includes(v.label)
			)
		default:
			return descOpt.filter((v) => v.label != "出库完成时间")
	}
})

/**
 * title 配置
 */
const drawerRightTitle = {
	name: ["扩展信息"],
	icon: ["fas", "square-share-nodes"]
}

const activeTab = ref<number>(0)
const handleTabChange = (index: number) => {
	activeTab.value = index
}

/**
 * 详情
 */
const getDetail = () => {
	if (!props.id) {
		return false
	}
	drawerLoading.value = true
	getMatOutOtherById(props.id as number)
		.then((res: any) => {
			descData.value = res
		})
		.finally(() => {
			drawerLoading.value = false
		})
}
onMounted(() => {
	getDictByCodeList(["INVENTORY_UNIT", "MATERIAL_NATURE"])
	getDetail()
	getTableData()
})

/**
 * 查询条件配置
 */
const queryArrList = computed(() => {
	const queryArrConfig = [
		/* {
			name: "出库状态",
			key: "status",
			placeholder: "请选择出库状态",
			type: "select",
			children: getOutStoreStatus().filter((v) => v.label != "已关闭")
		}, */
		{
			name: "物资编码",
			key: "materialCode",
			placeholder: "请输入物资编码",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "物资名称",
			key: "materialLabel",
			placeholder: "请输入物资名称",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "规格型号",
			key: "version",
			placeholder: "请输入规格型号",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "物资性质",
			key: "attribute",
			type: "select",
			children: dictOptions.value.MATERIAL_NATURE,
			placeholder: "请选择物资性质"
		}
	]

	return queryArrConfig
})

const tbInit = useTbInit<
	MatOutStorePickItemVO,
	MatOutStorePickItemRequestParams
>()
const {
	tableData,
	tableLoading,
	tableRef,
	currentPage,
	pageTotal,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = tbInit

/**
 * table 列配置
 */
tbInit.tableProp = computed(() => {
	const defCols: TableColumnType[] = [
		{ label: "物资编码", prop: "materialCode", width: 130 },
		{ label: "物资名称", prop: "materialName", minWidth: 150 },
		{ label: "规格型号", prop: "version", minWidth: 150 },
		{ label: "技术参数", prop: "technicalParameter", minWidth: 200 },
		{ label: "物资性质", prop: "attribute", needSlot: true, width: 120 },
		{ label: "库存单位", prop: "useUnit", needSlot: true, width: 90 },
		{ label: "批次号", prop: "batchNo", width: 180 },
		{
			label: "申请数量",
			prop: "applyOutNum",
			needSlot: true,
			align: "right",
			minWidth: 100
		},
		{
			label: "待出库数量",
			prop: "toOutNum",
			needSlot: true,
			align: "right",
			minWidth: 100
		},
		{
			label: "已出库数量",
			prop: "outedNum",
			needSlot: true,
			align: "right",
			minWidth: 100
		},
		{
			label: "出库状态",
			prop: "status",
			needSlot: true,
			minWidth: 100,
			fixed: "right"
		},
		{
			label: "出库批次",
			prop: "outBatchTimes",
			needSlot: true,
			width: 100,
			fixed: "right"
		}
	]

	if (
		descData.value.status == OutStoreStatus.noOut ||
		descData.value.status == OutStoreStatus.partOut
	) {
		// todo 待出库 & 部分出库
		if (descData.value.wasteOldType === IWasteOldType.matInStore) {
			return tableColFilter(defCols, ["出库批次"])
		} else {
			return tableColFilter(defCols, ["出库批次", "批次号"])
		}
	} else {
		if (descData.value.wasteOldType === IWasteOldType.matInStore) {
			return tableColFilter(defCols, ["申请数量", "待出库数量", "出库状态"])
		} else {
			return tableColFilter(defCols, [
				"申请数量",
				"待出库数量",
				"出库状态",
				"批次号"
			])
		}
	}
})

/**
 * table 数据源
 * @param data
 */
const getTableData = (data?: any) => {
	if (props.id) {
		fetchFunc.value = getMatOutOtherDetail

		currentPage.value = 1
		tableRef.value?.resetCurrentPage()

		fetchParam.value = {
			applyId: props.id,
			sord: "desc",
			sidx: "createdDate",
			...data
		}
		fetchTableData()
	}
}

/**
 * 关联批次 操作
 */
const showBatchDrawer = ref(false)
const currentRow = ref<MatOutStorePickItemVO>({})
const onRowBatch = (row: MatOutStorePickItemVO) => {
	if (!row.outBatchTimes) {
		return false
	}
	currentRow.value = row
	showBatchDrawer.value = true
}

/**
 * 关联货位 操作
 */
const roomDetailVisible = ref(false)
const matRoomStatus = ref<IMatOutStoreRoomStatus>(IMatOutStoreRoomStatus.all)
const handleRoomView = (
	row: MatOutStorePickItemVO,
	status: IMatOutStoreRoomStatus
) => {
	if (!row.applyOutNum && status == IMatOutStoreRoomStatus.all) {
		return false
	} else if (!row.outedNum && status == IMatOutStoreRoomStatus.out) {
		return false
	} else if (!row.toOutNum && status == IMatOutStoreRoomStatus.wait) {
		return false
	}

	currentRow.value = row
	matRoomStatus.value = status
	roomDetailVisible.value = true
}
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<!-- 左侧表单区域 -->
		<div class="drawer-column left">
			<div class="rows">
				<Title :title="drawerLeftTitle" />
				<el-descriptions size="small" :column="1" border class="content">
					<template>
						<el-descriptions-item
							v-for="(el, index) in formEl"
							label-align="center"
							:label="el.label"
							:key="index"
						>
							<!-- 出库状态 -->
							<span v-if="el.name == 'status'">
								<dict-tag
									:value="descData[el.name]!"
									:options="getOutStoreStatus()"
								/>
							</span>
							<span v-else>
								{{
									descData[el.name] || descData[el.name] == 0
										? descData[el.name]
										: "---"
								}}
							</span>
						</el-descriptions-item>
					</template>
				</el-descriptions>
			</div>
		</div>

		<!-- 右侧table区域 -->
		<div class="drawer-column right">
			<div class="rows">
				<Title :title="drawerRightTitle">
					<Tabs
						class="tabs"
						:tabs="['物资明细']"
						@on-tab-change="handleTabChange"
					/>
				</Title>

				<el-scrollbar v-if="activeTab === 0" class="tab-mat">
					<Query
						:queryArrList="queryArrList"
						@getQueryData="getTableData"
						class="custom-q"
					/>

					<PitayaTable
						ref="tableRef"
						:columns="(tbInit.tableProp as any)"
						:table-data="tableData"
						:need-index="true"
						:need-pagination="true"
						:single-select="props.model == IModalType.create ? true : false"
						:need-selection="props.model == IModalType.create ? true : false"
						:total="pageTotal"
						@onSelectionChange="selectedTableList = $event"
						@on-current-page-change="onCurrentPageChange"
						:table-loading="tableLoading"
					>
						<!-- 物资性质 -->
						<template #attribute="{ rowData }">
							<dict-tag
								:options="DictApi.getMatAttr()"
								:value="rowData.attribute"
							/>
						</template>

						<template #useUnit="{ rowData }">
							{{
								dictFilter("INVENTORY_UNIT", rowData.useUnit)?.subitemName ||
								"---"
							}}
						</template>

						<template #applyOutNum="{ rowData }">
							<link-tag
								:value="toFixedTwo(rowData.applyOutNum)"
								@click.stop="
									handleRoomView(rowData, IMatOutStoreRoomStatus.all)
								"
							/>
						</template>

						<template #outedNum="{ rowData }">
							<link-tag
								:value="toFixedTwo(rowData.outedNum)"
								@click.stop="
									handleRoomView(rowData, IMatOutStoreRoomStatus.out)
								"
							/>
						</template>

						<template #toOutNum="{ rowData }">
							<link-tag
								:value="toFixedTwo(rowData.toOutNum)"
								@click.stop="
									handleRoomView(rowData, IMatOutStoreRoomStatus.wait)
								"
							/>
						</template>

						<!-- 入库状态 -->
						<template #status="{ rowData }">
							<dict-tag
								:options="getOutStoreStatus()"
								:value="rowData.status"
							/>
						</template>

						<!-- 批次 -->
						<template #outBatchTimes="{ rowData }">
							<link-tag
								:value="rowData.outBatchTimes"
								@click.stop="onRowBatch(rowData)"
							/>
						</template>
					</PitayaTable>
				</el-scrollbar>
			</div>

			<ButtonList
				class="footer"
				:button="formBtnList"
				@on-btn-click="emits('close')"
			/>

			<!-- 批次 -->
			<Drawer
				:size="modalSize.lg"
				v-model:drawer="showBatchDrawer"
				:destroyOnClose="true"
			>
				<batch-detail
					:id="currentRow.id!"
					:row="currentRow"
					@close="showBatchDrawer = false"
				/>
			</Drawer>

			<!-- 货位 -->
			<Drawer
				:size="modalSize.md"
				v-model:drawer="roomDetailVisible"
				:destroyOnClose="true"
			>
				<room-detail
					:id="currentRow.id!"
					:status="matRoomStatus"
					:outStatus="(descData.status as OutStoreStatus)"
					@close="roomDetailVisible = false"
				/>
			</Drawer>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.drawer-container {
	.left {
		width: 330px;
	}

	.right {
		width: calc(100% - 330px);
	}

	.tab-mat {
		height: 100%;
	}
}

.custom-q {
	margin: 10px 0px -10px 10px !important;
}
</style>
