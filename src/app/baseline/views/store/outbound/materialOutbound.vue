<script lang="ts" setup>
import { onMounted, ref, computed } from "vue"
import {
	checkPermission,
	isCheckPermission
} from "@/app/platform/utils/permission"
import { powerList } from "../../components/define.d"
import { useTbInit } from "../../components/tableBase"
import LinkTag from "@/app/baseline/views/components/linkTag.vue"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import ViewDrawer from "./materialOutbound/viewDrawer.vue"
import {
	batchMatPickOutStoreAll,
	getMatPickApplyList,
	getMatPickOutBpmStatus
} from "../../../api/store/outbound/materialPick"
import {
	MatOutStorePickRequestParams,
	MatOutStorePickVO
} from "../../../utils/types/store-outbound-material"
import { first, map, omit } from "lodash-es"
import matGetApplyDetail from "../business/matGetApply/matGetApplyDetail.vue"
import { listMatStoragePaged } from "@/app/baseline/api/store/manage-api"
import { modalSize } from "@/app/baseline/utils/layout-config"
import { OutStoreStatus, getOutStoreStatus } from "./outbound"
import { MaterialPurpose } from "@/app/baseline/api/dict"

import { tableColFilter } from "@/app/baseline/utils"
import {
	IIdempotentTokenType,
	IIdempotentTokenTypeAction,
	IIdempotentTokenTypePre,
	IModalType
} from "@/app/baseline/utils/types/common"
import closeEditor from "./materialOutbound/closeEditor.vue"
import { useMessageBoxInit } from "../../components/messageBox"
import { getIdempotentToken } from "@/app/baseline/utils/validate"

const { showWarnConfirm, showErrorConfirm } = useMessageBoxInit()

const closeEditorVisible = ref(false)

/**
 * 查询条件 配置
 */
const queryArrList = computed(() => {
	return [
		{
			name: "出库单号",
			key: "code",
			placeholder: "请输入出库单号",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "领料单号",
			key: "preBusinessCode",
			placeholder: "请输入领料单号",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "领料人",
			key: "userName",
			placeholder: "请输入领料人",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "出库仓库",
			key: "storeId",
			placeholder: "请选择出库仓库",
			type: "tableSelect",
			tableInfo: [
				{
					title: "请选择出库仓库",
					tableApi: listMatStoragePaged, //表格接口
					tableColumns: [
						{ label: "仓库编码", prop: "code", width: 120 },
						{ label: "仓库名称", prop: "label" },
						{
							label: "仓库级别",
							prop: "level_view",
							width: 100
						},
						{
							label: "仓库类型",
							prop: "type_view",
							width: 120
						},
						{
							label: "所属段区",
							prop: "depotId_view",
							width: 150
						}
					],
					queryArrList: [
						{
							name: "仓库编码",
							key: "code",
							type: "input",
							placeholder: "请输入仓库编码"
						},
						{
							name: "仓库名称",
							key: "label",
							type: "input",
							placeholder: "请输入仓库名称"
						}
					],
					params: {
						currentPage: 1,
						pageSize: 20
					}, //表格接口参数

					labelName: {
						key: "id", //回显标识
						label: "label", //input框要展示的字段
						value: "id" //要给后台传的字段
					}
				}
			]
		}
	]
})

/**
 * table 数据源
 * @param data
 */
const getTableData = (data?: Record<string, any>) => {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		tableType: tabStatus,
		sord: "desc",
		sidx: "createdDate",
		...data
	}

	handleUpdate()
}

/**
 * title 配置
 */
const statusCnt = ref<number[]>([])
const rightTitle = {
	name: ["领料出库"],
	icon: ["fas", "square-share-nodes"]
}

/**
 * tab 配置
 */
const tabList = [
	{
		name: "待出库",
		value: `${OutStoreStatus.noOut},${OutStoreStatus.partOut}`,
		icon: ["fas", "square-plus"]
	},
	{
		name: "已出库",
		value: OutStoreStatus.out,
		icon: ["fas", "square-plus"]
	},
	{
		name: "已关闭",
		value: OutStoreStatus.close,
		icon: ["fas", "square-plus"]
	}
]
const tabStatus = ref<string>(tabList[0].value)
const activeName = ref<string>(tabList[0].name)
const handleTabsClick = (tab: any) => {
	activeName.value = tab.paneName
	tabStatus.value = tabList[tab.index].value
	getTableData()
}

const tbBtnLoading = ref(false)
const tbInit = useTbInit<MatOutStorePickVO, MatOutStorePickRequestParams>()
const {
	tableData,
	tableRef,
	tableLoading,
	currentPage,
	pageTotal,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = tbInit

/**
 * table 列配置
 */
tbInit.tableProp = computed(() => {
	const tableCols: TableColumnType[] = [
		{
			label: "出库单号",
			prop: "code",
			minWidth: 150,
			sortable: true
		},
		{
			label: "关联领料单号",
			prop: "preBusinessCode",
			needSlot: true,
			minWidth: 180
		},
		{ label: "领料用途", prop: "purpose_view", minWidth: 100 },
		{ label: "领料人", prop: "pickUserId_view", minWidth: 120 },
		{ label: "出库状态", prop: "status", needSlot: true, minWidth: 150 },
		{ label: "出库仓库", prop: "storeName", minWidth: 150 },
		{ label: "出库物资编码", prop: "outCount", minWidth: 120 },
		{ label: "待出库物资编码", prop: "toOutCount", minWidth: 120 },
		{ label: "已出库物资编码", prop: "outedCount", minWidth: 120 },
		{ label: "已关闭物资编码", prop: "toOutCount", minWidth: 120 },
		{ label: "申请时间", prop: "createdDate", minWidth: 150, sortable: true },

		{ label: "出库完成时间", prop: "applyFinishDate", minWidth: 150 },

		{ label: "关闭原因", prop: "reason", minWidth: 150 },
		{ label: "关闭时间", prop: "closeDate", minWidth: 150 },

		{
			label: "操作",
			width: 150,
			prop: "operations",
			needSlot: true,
			fixed: "right"
		}
	]

	switch (tabStatus.value) {
		case `${OutStoreStatus.noOut},${OutStoreStatus.partOut}`:
			return tableColFilter(tableCols, [
				"出库完成时间",
				"关闭原因",
				"关闭时间",
				"已关闭物资编码"
			])
		case OutStoreStatus.out:
			return tableColFilter(tableCols, [
				"关闭原因",
				"关闭时间",
				"出库状态",
				"待出库物资编码",
				"已出库物资编码",
				"已关闭物资编码"
			])
		default:
			return tableColFilter(tableCols, [
				"出库完成时间",
				"出库状态",
				"已出库物资编码"
			])
	}
})

fetchFunc.value = getMatPickApplyList

/**
 * 按钮 配置
 */
const tableFooterBtns = computed(() => {
	return [
		{
			name: "一键出库",
			roles: powerList.storeOutboundMaterialOutboundBtnEdit,
			icon: ["fas", "arrow-up"],
			disabled: selectedTableList.value.length > 0 ? false : true
		},
		{
			name: "关闭",
			roles: powerList.storeOutboundMaterialOutboundBtnClose,
			icon: ["fas", "times-circle"],
			disabled: selectedTableList.value.length > 0 ? false : true
		}
	]
})

/**
 * 关闭 操作
 */
const handleMatterialCloseAction = async (btnName: string | undefined) => {
	if (btnName === "一键出库") {
		const canOut = selectedTableList.value.every(
			(item) => item.status != OutStoreStatus.partOut
		)

		if (canOut) {
			await showWarnConfirm(
				`您选中 ${selectedTableList.value?.length} 条数据进行批量出库，请确定！`
			)

			try {
				tbBtnLoading.value = true

				const idempotentToken = getIdempotentToken(
					IIdempotentTokenTypePre.batch,
					IIdempotentTokenType.materialGetOutWarehouse,
					"",
					IIdempotentTokenTypeAction.outStore
				)
				const { code, msg, data } = await batchMatPickOutStoreAll(
					map(selectedTableList.value, ({ id }) => id).toString(),
					idempotentToken
				)

				if (data && code != 200) {
					await showErrorConfirm(msg)
				} else {
					ElMessage.success("操作成功")
				}
				getTableData()
			} finally {
				tbBtnLoading.value = false
			}
		} else {
			await showErrorConfirm("出库单中有已出库物资，批量出库失败，请检查数据！")
		}
	} else if (btnName === "关闭") {
		closeEditorVisible.value = true
	}
}

const curRowId = ref<any>("")
const curRowData = ref<MatOutStorePickVO>({})
const showViewDrawer = ref<boolean>(false)
const model = ref(IModalType.view)

/**
 * 查看 操作
 * @param row
 */
const onRowView = (row: MatOutStorePickVO) => {
	curRowId.value = row.id
	curRowData.value = row
	showViewDrawer.value = true
	model.value = IModalType.view
}
/**
 * 编辑 操作
 * model = 'create'; 一键出库用的 "edit"
 */
const onRowCreate = (row: MatOutStorePickVO) => {
	curRowId.value = row.id
	curRowData.value = row
	showViewDrawer.value = true
	model.value = IModalType.create
}

/**
 * 关联领料业务单号
 */
const showRelativeCodeDrawer = ref(false)
const onRowBusinessOrder = (row: MatOutStorePickVO) => {
	if (!row.preBusinessCode) {
		return false
	}
	curRowId.value = row.id
	curRowData.value = row
	showRelativeCodeDrawer.value = true
}

/**
 * 更新主表/tab count
 */
const handleUpdate = async () => {
	fetchTableData()
	statusCnt.value = await getMatPickOutBpmStatus(
		omit(
			{
				...fetchParam.value
			},
			"tableType",
			"currentPage",
			"pageSize"
		) as any
	)
}

onMounted(() => {
	getTableData()
})

/**
 * 排序
 * @param column
 * @param prop 字段名称
 * @param order ascending 升序 descending 降序
 */
function handleSortChange(column: any, prop: any, order: any) {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		sord:
			order === "descending" ? "desc" : order === "ascending" ? "asc" : "desc",
		sidx: order ? prop : "createdDate" // 排序字段
	}

	fetchTableData()
}
</script>
<template>
	<div class="app-container">
		<div class="whole-frame">
			<ModelFrame class="top-frame">
				<Query
					:queryArrList="queryArrList"
					@getQueryData="getTableData"
					class="ml10"
				/>
			</ModelFrame>
			<ModelFrame class="bottom-frame">
				<Title :title="rightTitle">
					<div class="app-tabs-wrapper">
						<el-tabs v-model="activeName" @tab-click="handleTabsClick">
							<el-tab-pane
								v-for="(tab, index) in tabList"
								:key="index"
								:label="`${tab.name}（${statusCnt[index] ?? 0}）`"
								:name="tab.name"
								:index="tab.name"
							/>
						</el-tabs>
					</div>
				</Title>

				<PitayaTable
					ref="tableRef"
					:columns="(tbInit.tableProp as any)"
					:tableData="tableData"
					:total="pageTotal"
					:need-index="true"
					:single-select="false"
					:need-selection="tabStatus == tabList[0].value ? true : false"
					@on-current-page-change="onCurrentPageChange"
					@onSelectionChange="selectedTableList = $event"
					:table-loading="tableLoading"
					@on-table-sort-change="handleSortChange"
				>
					<!-- 关联领料单号 -->
					<template #preBusinessCode="{ rowData }">
						<link-tag
							:value="rowData.preBusinessCode"
							@click.stop="onRowBusinessOrder(rowData)"
						/>
					</template>

					<template #status="{ rowData }">
						<dict-tag :value="rowData.status" :options="getOutStoreStatus()" />
					</template>

					<template #operations="{ rowData }">
						<slot
							v-if="
								(tabStatus ==
									`${OutStoreStatus.noOut},${OutStoreStatus.partOut}` &&
									rowData.purpose != MaterialPurpose.lowvalueMaterial &&
									isCheckPermission(
										powerList.storeOutboundMaterialOutboundBtnEdit
									)) ||
								isCheckPermission(
									powerList.storeOutboundMaterialOutboundBtnPreview
								)
							"
						>
							<el-button
								v-btn
								link
								@click.stop="onRowCreate(rowData)"
								v-if="
									tabStatus ==
										`${OutStoreStatus.noOut},${OutStoreStatus.partOut}` &&
									rowData.purpose != MaterialPurpose.lowvalueMaterial &&
									isCheckPermission(
										powerList.storeOutboundMaterialOutboundBtnEdit
									)
								"
								:disabled="
									checkPermission(
										powerList.storeOutboundMaterialOutboundBtnEdit
									)
								"
							>
								<!-- 低值物资 不允许分批出库 -->
								<font-awesome-icon :icon="['fas', 'edit']" />
								<span class="table-inner-btn">编辑</span>
							</el-button>
							<el-button
								v-btn
								link
								@click.stop="onRowView(rowData)"
								:disabled="
									checkPermission(
										powerList.storeOutboundMaterialOutboundBtnPreview
									)
								"
								v-if="
									isCheckPermission(
										powerList.storeOutboundMaterialOutboundBtnPreview
									)
								"
							>
								<font-awesome-icon :icon="['fas', 'eye']" />
								<span class="table-inner-btn">查看</span>
							</el-button>
						</slot>
						<slot v-else>---</slot>
					</template>
					<template #footerOperateLeft v-if="tabStatus === tabList[0].value">
						<ButtonList
							class="btn-list"
							:is-not-radius="true"
							:button="tableFooterBtns"
							:loading="tbBtnLoading"
							@on-btn-click="handleMatterialCloseAction"
						/>
						<!-- @on-btn-click="onBtnClick" -->
					</template>
				</PitayaTable>

				<!-- 编辑/查看弹窗 -->
				<Drawer
					:size="modalSize.xl"
					v-model:drawer="showViewDrawer"
					:destroyOnClose="true"
				>
					<view-drawer
						:id="curRowId"
						:model="model"
						@close="showViewDrawer = false"
						@update="handleUpdate"
					/>
				</Drawer>

				<!-- 关联领料单号 -->
				<Drawer
					:size="modalSize.xl"
					v-model:drawer="showRelativeCodeDrawer"
					:destroyOnClose="true"
				>
					<mat-get-apply-detail
						:id="curRowData.preBusinessId!"
						@close="showRelativeCodeDrawer = false"
					/>
				</Drawer>

				<!-- 关闭 -->
				<Drawer
					:size="350"
					v-model:drawer="closeEditorVisible"
					:destroyOnClose="true"
				>
					<close-editor
						:rowList="selectedTableList"
						@close="closeEditorVisible = false"
						@update="handleUpdate"
					/>
				</Drawer>
			</ModelFrame>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
