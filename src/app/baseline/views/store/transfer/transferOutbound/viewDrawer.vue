<script setup lang="ts">
import { FormElementType } from "@/app/baseline/views/components/define"
import { onMounted, reactive, ref } from "vue"

import { MatStoreAllocationOutVO } from "@/app/baseline/utils/types/store-transfer-outbound"
import { useTbInit } from "../../../components/tableBase"
import { useDictInit } from "../../../components/dictBase"
import {
	MatStoreAllocationApplyItemRequestParams,
	MatStoreAllocationApplyItemVO
} from "@/app/baseline/utils/types/store-transfer-apply"
import {
	getAllocationOutById,
	getAllocationOutPageBatch
} from "@/app/baseline/api/store/transfer/outbound"
import { IModalType } from "@/app/baseline/utils/types/common"
import { batchFormatterNumView } from "@/app/baseline/utils"
import { DictApi } from "@/app/baseline/api/dict"
import DictTag from "@/app/baseline/views/components/dictTag.vue"

export interface Props {
	id: string | number //
	model?: string //显示模式 view
}
const props = withDefaults(defineProps<Props>(), { model: IModalType.view })
const emits = defineEmits(["close", "update"])
const loading = ref(false)
const drawerLoading = ref(false)

/**
 * 按钮 配置
 */
const formBtnListView = [{ name: "取消", icon: ["fas", "circle-minus"] }]

/**
 * 表单数据源
 */
const formModal = ref<MatStoreAllocationOutVO>({})

/**
 * 左侧title 配置
 */
const drawerLeftTitle = {
	name: ["调拨出库"],
	icon: ["fas", "square-share-nodes"]
}

/**
 * 左侧表单 配置
 */
const formEl = reactive<FormElementType[]>([
	{ label: "出库单号", name: "code" },
	{ label: "关联调拨单号", name: "preBusinessCode" },
	{ label: "入库仓库名称", name: "storeInName" },
	{ label: "出库仓库名称", name: "storeName" },
	{ label: "申请人", name: "userName_view" },
	{ label: "申请时间", name: "applyDate" }
])

/**
 * 右侧title 配置
 */
const drawerRightTitle = {
	name: ["扩展信息"],
	icon: ["fas", "square-share-nodes"]
}

/**
 * 扩展栏标签页切换
 */
const tabList = ["物资明细"]
const activeTab = ref<number>(0)
const handleTabChange = (index: number) => {
	activeTab.value = index
}

/**
 *表 单按钮 操作
 * @param btnName
 */
/* const onFormBtnList = async (btnName: string | undefined) => {
	if (btnName === "取消") {
		emits("onSaveOrClose", false)
	} else if (btnName === "确认") {
		await showWarnConfirm(`请确认是否将调拨出库单中的物资全部出库？`)
		loading.value = true
		updateAllocationOutAllocation(props.id as number)
			.then(() => {
				ElMessage.success("操作成功")
				emits("onSaveOrClose", "save")
			})
			.finally(() => {
				loading.value = false
			})
	}
} */
const { dictOptions, getDictByCodeList, dictFilter } = useDictInit()

const {
	tableData,
	tableProp,
	tableRef,
	tableLoading,
	currentPage,
	pageTotal,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange,
	onDataSelected
} = useTbInit<
	MatStoreAllocationApplyItemVO,
	MatStoreAllocationApplyItemRequestParams
>()

/**
 * table列 配置
 */
tableProp.value = [
	{ label: "物资编码", prop: "materialCode" },
	{ label: "物资名称", prop: "materialLabel" },
	{ label: "规格型号", prop: "version" },
	{ label: "技术参数", prop: "technicalParameter" },
	{ label: "批次号", prop: "batchNo", width: 160 },
	{ label: "物资性质", prop: "attribute", needSlot: true, width: 120 },
	{ label: "库存单位", prop: "useUnit", needSlot: true, width: 90 },
	{ label: "出库数量", prop: "num_view", align: "right" },
	{ label: "出库区域名称", prop: "regionLabel" },
	{ label: "出库货位编码", prop: "sourceRoomCode" }
]

/**
 * 格式化数量
 */
watch(tableData, () => {
	batchFormatterNumView(tableData.value as any[])
})

/**
 * 查询条件 配置
 */
const queryArrList = computed(() => [
	{
		name: "物资编码",
		key: "materialCode",
		placeholder: "请输入物资编码",
		type: "input",
		enableFuzzy: true
	},
	{
		name: "物资名称",
		key: "materialLabel",
		placeholder: "请输入物资名称",
		type: "input",
		enableFuzzy: true
	},
	{
		name: "规格型号",
		key: "version",
		placeholder: "请输入规格型号",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资性质",
		key: "attribute",
		type: "select",
		children: dictOptions.value.MATERIAL_NATURE,
		placeholder: "请选择物资性质"
	}
])

/**
 * 数据源
 * @param data
 */
const getTableData = (data?: Record<string, any>) => {
	if (props.id) {
		fetchFunc.value = getAllocationOutPageBatch

		currentPage.value = 1
		tableRef.value?.resetCurrentPage()

		fetchParam.value = {
			...fetchParam.value,
			applyId: props.id,
			sord: "desc",
			sidx: "createdDate",
			...data
		}
		fetchTableData()
	}
}

const getDetail = () => {
	if (props.id) {
		drawerLoading.value = true
		getAllocationOutById(props.id as number)
			.then((res: any) => {
				formModal.value = { ...res }
			})
			.finally(() => {
				drawerLoading.value = false
			})
	}
}

onMounted(() => {
	getDictByCodeList(["INVENTORY_UNIT", "MATERIAL_NATURE"])
	getDetail()
	getTableData()
})
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<!-- 左侧表单区域 -->
		<div class="drawer-column left">
			<div class="rows">
				<Title :title="drawerLeftTitle" />
				<el-descriptions size="small" :column="1" border class="content">
					<template>
						<el-descriptions-item
							v-for="(el, index) in formEl"
							label-align="center"
							:label="el.label"
							:key="index"
						>
							<span>
								{{
									formModal[el.name] || formModal[el.name] == 0
										? formModal[el.name]
										: "---"
								}}
							</span>
						</el-descriptions-item>
					</template>
				</el-descriptions>
			</div>
		</div>

		<!-- 右侧table区域 -->
		<div class="drawer-column right">
			<div class="rows">
				<Title :title="drawerRightTitle">
					<Tabs class="tabs" :tabs="tabList" @on-tab-change="handleTabChange" />
				</Title>

				<el-scrollbar class="tab-mat" v-if="activeTab === 0">
					<Query
						:queryArrList="queryArrList"
						@getQueryData="getTableData"
						class="custom-q"
					/>
					<PitayaTable
						ref="tableRef"
						:columns="tableProp"
						:table-data="tableData"
						:need-index="true"
						:need-pagination="true"
						:single-select="false"
						:need-selection="false"
						:total="pageTotal"
						@onSelectionChange="onDataSelected"
						@on-current-page-change="onCurrentPageChange"
						:table-loading="tableLoading"
					>
						<!-- 物资性质 -->
						<template #attribute="{ rowData }">
							<dict-tag
								:options="DictApi.getMatAttr()"
								:value="rowData.attribute"
							/>
						</template>
						<template #useUnit="{ rowData }">
							{{
								dictFilter("INVENTORY_UNIT", rowData.useUnit)?.subitemName ||
								"---"
							}}
						</template>
					</PitayaTable>
				</el-scrollbar>

				<!-- <div v-if="activeTab === 1" :class="currentId ? '' : 'disabled'">
					<TableFile
						:business-type="fileBusinessType.transferOutbound"
						:business-id="currentId"
						:table-loading="childTableLoading"
						:mod="props.model"
					/>
				</div> -->
			</div>
			<ButtonList
				class="footer"
				:button="formBtnListView"
				:loading="loading"
				@on-btn-click="emits('close')"
			/>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.drawer-container {
	.left {
		width: 310px;
	}

	.right {
		width: calc(100% - 310px);
	}

	.tab-mat {
		height: calc(100% - 43px);
	}
}
.custom-q {
	margin: 10px 0px -10px 10px !important;
}

.el-input-number {
	width: 95%;
}
</style>
