<script lang="ts" setup>
import { computed, onMounted, ref } from "vue"
import {
	checkPermission,
	isCheckPermission
} from "@/app/platform/utils/permission"
import { powerList } from "../../components/define.d"
import LinkTag from "@/app/baseline/views/components/linkTag.vue"
import { modalSize } from "@/app/baseline/utils/layout-config"
import viewDrawer from "./transferReceipt/viewDrawer.vue"
import { IModalType } from "../../../utils/types/common"
import { listMatStoragePaged } from "@/app/baseline/api/store/manage-api"
import { useTbInit } from "../../components/tableBase"
import {
	getAllocationInPage,
	getInAllocationBmpStatusCnt
} from "@/app/baseline/api/store/transfer/receipt"
import {
	MatStoreAllocationInRequestParams,
	MatStoreAllocationInVO
} from "@/app/baseline/utils/types/store-transfer-receipt"
import transferApplyDetail from "./transferApplication/viewDrawer.vue"
import { InStoreStatus } from "../warehouse/warehouse"
import closeDrawer from "./transferReceipt/closeDrawer.vue"
import { first, omit } from "lodash-es"
import { tableColFilter } from "@/app/baseline/utils"

/**
 * 查询条件 配置
 */
const queryArrList = computed(() => {
	return [
		{
			name: "入库单号",
			key: "code",
			placeholder: "请输入入库单号",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "关联调拨单号",
			key: "preBusinessCode",
			placeholder: "请输入关联调拨单号",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "申请人",
			key: "userName",
			placeholder: "请输入申请人",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "出库仓库",
			key: "storeId",
			placeholder: "请选择出库仓库",
			type: "tableSelect",
			tableInfo: [
				{
					title: "请选择出库仓库",
					tableApi: listMatStoragePaged, //表格接口
					tableColumns: [
						{ label: "仓库编码", prop: "code", width: 120 },
						{ label: "仓库名称", prop: "label" },
						{
							label: "仓库级别",
							prop: "level_view",
							width: 100
						},
						{
							label: "仓库类型",
							prop: "type_view",
							width: 120
						},
						{
							label: "所属段区",
							prop: "depotId_view",
							width: 150
						}
					],
					queryArrList: [
						{
							name: "仓库编码",
							key: "code",
							type: "input",
							placeholder: "请输入仓库编码"
						},
						{
							name: "仓库名称",
							key: "label",
							type: "input",
							placeholder: "请输入仓库名称"
						}
					],
					params: {
						currentPage: 1,
						pageSize: 20,
						storekeeperFalg: "true"
					}, //表格接口参数

					labelName: {
						key: "id", //回显标识
						label: "label", //input框要展示的字段
						value: "id" //要给后台传的字段
					}
				}
			]
		},
		{
			name: "入库仓库",
			key: "storeInId",
			placeholder: "请选择入库仓库",
			type: "tableSelect",
			tableInfo: [
				{
					title: "请选择入库仓库",
					tableApi: listMatStoragePaged, //表格接口
					tableColumns: [
						{ label: "仓库编码", prop: "code", width: 120 },
						{ label: "仓库名称", prop: "label" },
						{
							label: "仓库级别",
							prop: "level_view",
							width: 100
						},
						{
							label: "仓库类型",
							prop: "type_view",
							width: 120
						},
						{
							label: "所属段区",
							prop: "depotId_view",
							width: 150
						}
					],
					queryArrList: [
						{
							name: "仓库编码",
							key: "code",
							type: "input",
							placeholder: "请输入仓库编码"
						},
						{
							name: "仓库名称",
							key: "label",
							type: "input",
							placeholder: "请输入仓库名称"
						}
					],
					params: {
						currentPage: 1,
						pageSize: 20,
						storekeeperFalg: "true"
					}, //表格接口参数

					labelName: {
						key: "id", //回显标识
						label: "label", //input框要展示的字段
						value: "id" //要给后台传的字段
					}
				}
			]
		}
	]
})

/**
 * Title 配置
 */
const rightTitle = {
	name: ["调拨入库"],
	icon: ["fas", "square-share-nodes"]
}

const tbBtnLoading = ref(false)
/**
 * tab 配置项
 */
const statusCnt = ref<any[]>([])
const tabList = [
	{ name: "待入库", value: InStoreStatus.noIn },
	{ name: "已入库", value: InStoreStatus.in },
	{ name: "已关闭", value: InStoreStatus.close }
]
const tabStatus = ref(tabList[0].value)
const activeName = ref(tabList[0].name)
const handleTabsClick = (tab: any) => {
	activeName.value = tab.paneName
	tabStatus.value = tabList[tab.index].value
	getTableData()
}

const tbInit = useTbInit<
	MatStoreAllocationInVO,
	MatStoreAllocationInRequestParams
>()
const {
	tableData,
	tableRef,
	tableLoading,
	currentPage,
	pageTotal,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange,
	selectedTableList
} = tbInit

/**
 * table 列配置
 */
tbInit.tableProp = computed(() => {
	const tableCols: TableColumnType[] = [
		{ label: "入库单号", prop: "code", width: 180 },
		{ label: "移库类型", prop: "preBusinessType_view" },
		{
			label: "关联调拨单号",
			prop: "preBusinessCode",
			needSlot: true,
			width: 180
		},
		{ label: "出库仓库名称", prop: "storeName" },
		{ label: "入库仓库名称", prop: "storeInName" },
		{ label: "物资编码", prop: "materialCodeNum" },
		{ label: "关闭原因", prop: "reason", minWidth: 100 },
		{ label: "关闭时间", prop: "closeDate" },
		{ label: "申请人", prop: "userName_view" },
		{ label: "申请时间", prop: "createdDate", sortable: true },
		{
			label: "操作",
			width: 150,
			prop: "operations",
			needSlot: true
		}
	]
	switch (tabStatus.value) {
		case InStoreStatus.close:
			return tableCols
		default:
			// 已关闭
			return tableColFilter(tableCols, ["关闭原因", "关闭时间"])
	}
})

/**
 * table 数据源
 */
fetchFunc.value = getAllocationInPage

const getTableData = (data?: any) => {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		tableType: tabStatus.value,
		sord: "desc",
		sidx: "createdDate",
		...data
	}

	handleUpdate()
}

/**
 *  table footer 按钮配置
 */
const tableFooterBtns = computed(() => {
	return [
		{
			name: "关闭",
			roles: powerList.storeTransferReceiptBtnClose,
			icon: ["fas", "times-circle"],
			disabled: selectedTableList.value.length > 0 ? false : true
		}
	]
})

const closeVisible = ref(false)
/**
 * table footer 按钮操作
 */
const handleTableAction = (name?: string) => {
	if (name === "关闭") {
		closeVisible.value = true
	}
}

const curRowId = ref<any>("")
const curRowData = ref<MatStoreAllocationInVO>({})
const viewDrawerVisible = ref<boolean>(false)
const model = ref<string>("view") // view | edit

/**
 * 编辑 操作
 * @param row
 */
const onRowEdit = (row: MatStoreAllocationInVO) => {
	curRowId.value = row.id
	curRowData.value = row
	viewDrawerVisible.value = true
	model.value = IModalType.edit
}

/**
 * 查看 操作
 * @param row
 */
const onRowView = (row: MatStoreAllocationInVO) => {
	curRowId.value = row.id
	curRowData.value = row
	viewDrawerVisible.value = true
	model.value = IModalType.view
}

/**
 * 关联调拨单号 操作
 */
const relativeDrawerVisible = ref(false)
const onRowBusinessOrder = (row: MatStoreAllocationInVO) => {
	if (!row.preBusinessId) {
		return false
	}
	curRowId.value = row.id
	curRowData.value = row
	relativeDrawerVisible.value = true
}

/**
 * 抽屉关闭 回调操作
 * @param msg
 */
/* const closeDrawer = (msg: string) => {
	if (msg === "save") {
		handleUpdate()
		viewDrawerVisible.value = false
	} else {
		viewDrawerVisible.value = false
		relativeDrawerVisible.value = false
	}
} */

const handleUpdate = async () => {
	statusCnt.value = await getInAllocationBmpStatusCnt(
		omit(
			{
				...fetchParam.value
			},
			"tableType",
			"currentPage",
			"pageSize"
		) as any
	)
	fetchTableData()
}

onMounted(() => {
	getTableData()
})

/**
 * 排序
 * @param column
 * @param prop 字段名称
 * @param order ascending 升序 descending 降序
 */
function handleSortChange(column: any, prop: any, order: any) {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		sord:
			order === "descending" ? "desc" : order === "ascending" ? "asc" : "desc",
		sidx: order ? prop : "createdDate" // 排序字段
	}

	fetchTableData()
}
</script>
<template>
	<div class="app-container">
		<div class="whole-frame">
			<ModelFrame class="top-frame">
				<Query
					:queryArrList="queryArrList"
					@getQueryData="getTableData"
					class="ml10"
				/>
			</ModelFrame>

			<ModelFrame class="bottom-frame">
				<Title :title="rightTitle">
					<div class="app-tabs-wrapper">
						<el-tabs v-model="activeName" @tab-click="handleTabsClick">
							<el-tab-pane
								v-for="(tab, index) in tabList"
								:key="index"
								:label="`${tab.name}（${statusCnt[index] ?? 0}）`"
								:name="tab.name"
								:index="tab.name"
							/>
						</el-tabs>
					</div>
				</Title>

				<PitayaTable
					ref="tableRef"
					:columns="((tbInit.tableProp) as any)"
					:tableData="tableData"
					:total="pageTotal"
					:need-index="true"
					:single-select="false"
					:need-selection="tabStatus == tabList[0].value ? true : false"
					@onSelectionChange="selectedTableList = $event"
					@on-current-page-change="onCurrentPageChange"
					:table-loading="tableLoading"
					@on-table-sort-change="handleSortChange"
				>
					<!-- 关联调拨单号 -->
					<template #preBusinessCode="{ rowData }">
						<link-tag
							:value="rowData.preBusinessCode"
							@click.stop="onRowBusinessOrder(rowData)"
						/>
					</template>

					<template #operations="{ rowData }">
						<slot
							v-if="
								(tabStatus == InStoreStatus.noIn &&
									isCheckPermission(powerList.storeTransferReceiptBtnEdit)) ||
								isCheckPermission(powerList.storeTransferReceiptBtnPreview)
							"
						>
							<el-button
								v-btn
								link
								@click.stop="onRowEdit(rowData)"
								v-if="
									tabStatus == InStoreStatus.noIn &&
									isCheckPermission(powerList.storeTransferReceiptBtnEdit)
								"
								:disabled="
									checkPermission(powerList.storeTransferReceiptBtnEdit)
								"
							>
								<font-awesome-icon :icon="['fas', 'edit']" />
								<span class="table-inner-btn">编辑</span>
							</el-button>

							<el-button
								v-btn
								link
								@click.stop="onRowView(rowData)"
								:disabled="
									checkPermission(powerList.storeTransferReceiptBtnPreview)
								"
								v-if="
									isCheckPermission(powerList.storeTransferReceiptBtnPreview)
								"
							>
								<font-awesome-icon :icon="['fas', 'eye']" />
								<span class="table-inner-btn">查看</span>
							</el-button>
						</slot>
						<slot v-else>---</slot>
					</template>

					<template #footerOperateLeft v-if="tabStatus == InStoreStatus.noIn">
						<ButtonList
							class="btn-list"
							:is-not-radius="true"
							:button="tableFooterBtns"
							:loading="tbBtnLoading"
							@on-btn-click="handleTableAction"
						/>
					</template>
				</PitayaTable>

				<!-- 编辑/查看弹窗 -->
				<Drawer
					:size="modalSize.xl"
					v-model:drawer="viewDrawerVisible"
					:destroyOnClose="true"
				>
					<view-drawer
						:id="curRowId"
						:model="model"
						@close="viewDrawerVisible = false"
						@update="handleUpdate"
					/>
				</Drawer>

				<!-- 关联业务单号 -->
				<Drawer
					:size="modalSize.xl"
					v-model:drawer="relativeDrawerVisible"
					:destroyOnClose="true"
				>
					<transfer-apply-detail
						mode="view"
						:id="curRowData.preBusinessId!"
						:row="curRowData"
						@close="relativeDrawerVisible = false"
					/>
				</Drawer>

				<!-- 关闭弹窗 -->
				<Drawer
					:size="modalSize.sm"
					v-model:drawer="closeVisible"
					:destroyOnClose="true"
				>
					<close-drawer
						:rowList="selectedTableList"
						@close="closeVisible = false"
						@update="handleUpdate"
					/>
				</Drawer>
			</ModelFrame>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
