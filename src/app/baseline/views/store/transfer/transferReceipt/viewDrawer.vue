<script setup lang="ts">
import { ElMessage } from "element-plus"
import { FormElementType } from "@/app/baseline/views/components/define"
import { onMounted, reactive, ref, toRef } from "vue"

import TableFile from "@/app/baseline/views/components/tableFile.vue"
import { fileBusinessType } from "@/app/baseline/api/dict"
import {
	IIdempotentTokenType,
	IIdempotentTokenTypePre,
	IModalType
} from "../../../../utils/types/common"
import { MatStoreAllocationInVO } from "@/app/baseline/utils/types/store-transfer-receipt"
import { useDictInit } from "../../../components/dictBase"
import { useTbInit } from "../../../components/tableBase"
import {
	editRoomAllocationInAllocation,
	getAllocationInById,
	getAllocationInPageBatch,
	updateAllocationInAllocation
} from "@/app/baseline/api/store/transfer/receipt"
import {
	MatStoreAllocationApplyItemRequestParams,
	MatStoreAllocationApplyItemVO
} from "@/app/baseline/utils/types/store-transfer-apply"
import { useMessageBoxInit } from "../../../components/messageBox"
import { map, includes } from "lodash-es"
import { modalSize } from "@/app/baseline/utils/layout-config"
import areaStorage from "../../warehouse/receivedStored/areaStorage.vue"
import { listMatStoreRoomPaged } from "../../../../api/store/manage-api"
import { batchFormatterNumView } from "@/app/baseline/utils"
import { DictApi } from "@/app/baseline/api/dict"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import { getIdempotentToken } from "@/app/baseline/utils/validate"

export interface Props {
	id: string | number //
	model?: string //显示模式 view
}
const { showWarnConfirm } = useMessageBoxInit()

const props = withDefaults(defineProps<Props>(), { model: IModalType.view })
const emits = defineEmits(["close", "update"])
const currentId = toRef(props, "id")
const loading = ref(false)
const childTableLoading = ref(false)

const drawerLoading = ref(false)

/**
 * 按钮 配置
 */
const formBtnListView = [{ name: "取消", icon: ["fas", "circle-minus"] }]
const formBtnListEdit = [
	{ name: "取消", icon: ["fas", "circle-minus"] },
	{ name: "确认", icon: ["fas", "circle-check"] }
]

/**
 * 表单数据源
 */
const formModal = ref<MatStoreAllocationInVO>({})

/**
 * 左侧title 配置
 */
const drawerLeftTitle = {
	name: ["调拨入库"],
	icon: ["fas", "square-share-nodes"]
}

/**
 * 左侧表单 配置
 */
const formEl = reactive<FormElementType[]>([
	{ label: "入库单号", name: "code" },
	{ label: "关联调拨单号", name: "preBusinessCode" },
	{ label: "入库仓库名称", name: "storeInName" },
	{ label: "出库仓库名称", name: "storeName" },
	{ label: "申请人", name: "userName_view" },
	{ label: "申请时间", name: "createdDate" }
])

/**
 * 右侧title 配置
 */
const drawerRightTitle = {
	name: ["扩展信息"],
	icon: ["fas", "square-share-nodes"]
}

/**
 * 扩展栏标签页切换
 */
const tabList = ["物资明细", "相关附件"]
const activeTab = ref<number>(0)
const handleTabChange = (index: number) => {
	activeTab.value = index

	if (index === 0) {
		fetchParam.value = {
			applyId: props.id || formModal.value.id,
			sord: "desc",
			sidx: "createdDate"
		}
		pageSize.value = 20
		getTableData()
	}
}

/**
 *表 单按钮 操作
 * @param btnName
 */
const onFormBtnList = async (btnName: string | undefined) => {
	if (btnName === "取消") {
		emits("close")
	} else if (btnName === "确认") {
		await showWarnConfirm(`请确认是否将调拨入库单中的物资全部入库？`)

		loading.value = true
		try {
			const idempotentToken = getIdempotentToken(
				IIdempotentTokenTypePre.other,
				IIdempotentTokenType.transferToWarehouse,
				props.id
			)
			const { code, msg, data } = await updateAllocationInAllocation(
				props.id as number,
				idempotentToken
			)

			if (data && code != 200) {
				errorGoodsIdList.value = data || []
				ElMessage.error(msg)
				getTableData()
			} else {
				ElMessage.success("操作成功")
				emits("update")
				emits("close")
			}
		} finally {
			loading.value = false
		}
		/* updateAllocationInAllocation(props.id as number)
			.then(() => {
				ElMessage.success("操作成功")
				emits("update")
				emits("close")
			})
			.finally(() => {
				loading.value = false
			}) */
	}
}

const { dictOptions, getDictByCodeList, dictFilter } = useDictInit()

const {
	tableData,
	tableProp,
	tableRef,
	tableLoading,
	currentPage,
	pageTotal,
	pageSize,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange,
	selectedTableList
} = useTbInit<
	MatStoreAllocationApplyItemVO,
	MatStoreAllocationApplyItemRequestParams
>()

/**
 * table列 配置
 */
tableProp.value = [
	{ label: "物资编码", prop: "materialCode" },
	{ label: "物资名称", prop: "materialLabel" },
	{ label: "规格型号", prop: "version" },
	{ label: "技术参数", prop: "technicalParameter" },
	{ label: "物资性质", prop: "attribute", needSlot: true, width: 120 },
	{ label: "批次号", prop: "batchNo", width: 160 },
	{ label: "库存单位", prop: "useUnit", needSlot: true, width: 100 },
	{ label: "入库数量", prop: "num_view", align: "right" },
	{ label: "入库区域名称", prop: "regionInLabel" },
	{ label: "入库货位编码", prop: "targetRoomCode", width: 120 }
]

/**
 * 格式化数量
 */
watch(tableData, () => {
	batchFormatterNumView(tableData.value as any[])
})

/**
 * 查询条件 配置
 */
const queryArrList = computed(() => [
	{
		name: "物资编码",
		key: "materialCode",
		placeholder: "请输入物资编码",
		type: "input",
		enableFuzzy: true
	},
	{
		name: "物资名称",
		key: "materialLabel",
		placeholder: "请输入物资名称",
		type: "input",
		enableFuzzy: true
	},
	{
		name: "规格型号",
		key: "version",
		placeholder: "请输入规格型号",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资性质",
		key: "attribute",
		type: "select",
		children: dictOptions.value.MATERIAL_NATURE,
		placeholder: "请选择物资性质"
	}
])

// 添加物资 按钮配置
const tbBtnConf = computed(() => {
	return [
		{
			name: "选择货位",
			icon: ["fas", "circle-plus"],
			disabled: selectedTableList.value.length > 0 ? false : true
		}
	]
})

/* const selectRoomIds = ref("") */
const roomSelectorVisible = ref(false)

/**
 * 选择货位操作
 */
const handleRoomSelected = (name?: string) => {
	if (name === "选择货位") {
		/* selectRoomIds.value = map(
			selectedTableList.value,
			({ sourceRoomId }) => sourceRoomId
		).toString() */
		roomSelectorVisible.value = true
	}
}

/**
 * 保存货位
 * @param name
 * @param treeList
 * @param row
 */
const handleAddRoom = async (name: string, treeList: any, row: any) => {
	if (name === "取消") {
		roomSelectorVisible.value = false
		return
	}
	if (name === "保存") {
		await editRoomAllocationInAllocation({
			idList: map(selectedTableList.value, ({ id }) => id),
			roomId: row.id
		})

		ElMessage.success("操作成功")
		fetchTableData()
		roomSelectorVisible.value = false
	}
}

/**
 * 数据源
 * @param data
 */
const getTableData = (data?: Record<string, any>) => {
	if (props.id) {
		fetchFunc.value = getAllocationInPageBatch

		currentPage.value = 1
		tableRef.value?.resetCurrentPage()

		fetchParam.value = {
			...fetchParam.value,
			applyId: props.id,
			sord: "desc",
			sidx: "createdDate",
			...data
		}
		fetchTableData()
	}
}

const getDetail = () => {
	if (props.id) {
		drawerLoading.value = true
		getAllocationInById(props.id as number)
			.then((res: any) => {
				formModal.value = { ...res }
			})
			.finally(() => {
				drawerLoading.value = false
			})
	}
}

/**
 * 物资明细 table 行样式
 *
 * 库存异常物资，用红色标记该行
 */
const errorGoodsIdList = ref<number[]>([])
function tbCellClassName({ row }: any) {
	return includes(errorGoodsIdList.value, row.id) ||
		includes(errorGoodsIdList.value, row.outItemId)
		? "error"
		: ""
}

onMounted(() => {
	getDictByCodeList(["INVENTORY_UNIT", "MATERIAL_NATURE"])
	getDetail()
	getTableData()
})
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<!-- 左侧表单区域 -->
		<div class="drawer-column left">
			<div class="rows">
				<Title :title="drawerLeftTitle" />
				<el-descriptions size="small" :column="1" border class="content">
					<template>
						<el-descriptions-item
							v-for="(el, index) in formEl"
							label-align="center"
							:label="el.label"
							:key="index"
						>
							<span>
								{{
									formModal[el.name] || formModal[el.name] == 0
										? formModal[el.name]
										: "---"
								}}
							</span>
						</el-descriptions-item>
					</template>
				</el-descriptions>
			</div>
		</div>

		<!-- 右侧table区域 -->
		<div class="drawer-column right">
			<div class="rows">
				<Title :title="drawerRightTitle">
					<Tabs class="tabs" :tabs="tabList" @on-tab-change="handleTabChange" />
				</Title>
				<el-scrollbar
					class="tab-mat transfer-receipt-editor-table-wrapper"
					v-if="activeTab === 0"
				>
					<Query
						:queryArrList="queryArrList"
						@getQueryData="getTableData"
						class="custom-q"
					/>
					<PitayaTable
						ref="tableRef"
						:columns="tableProp"
						:table-data="tableData"
						:need-index="true"
						:need-pagination="true"
						:single-select="false"
						:need-selection="props.model == IModalType.edit ? true : false"
						:total="pageTotal"
						@on-selection-change="selectedTableList = $event"
						@on-current-page-change="onCurrentPageChange"
						:table-loading="tableLoading"
						:cell-class-name="tbCellClassName"
					>
						<!-- 物资性质 -->
						<template #attribute="{ rowData }">
							<dict-tag
								:options="DictApi.getMatAttr()"
								:value="rowData.attribute"
							/>
						</template>

						<template #useUnit="{ rowData }">
							{{
								dictFilter("INVENTORY_UNIT", rowData.useUnit)?.subitemName ||
								"---"
							}}
						</template>
						<template #footerOperateLeft v-if="props.model == IModalType.edit">
							<button-list
								:button="tbBtnConf"
								:loading="loading"
								:is-not-radius="true"
								@on-btn-click="handleRoomSelected"
							/>
						</template>
					</PitayaTable>
				</el-scrollbar>
				<div v-if="activeTab === 1" :class="currentId ? '' : 'disabled'">
					<TableFile
						:business-type="fileBusinessType.transferReceipt"
						:business-id="currentId"
						:table-loading="childTableLoading"
						:mod="props.model"
					/>
				</div>
			</div>
			<ButtonList
				class="footer"
				:button="
					props.model === IModalType.view ? formBtnListView : formBtnListEdit
				"
				:loading="loading"
				@on-btn-click="onFormBtnList"
			/>

			<!-- 选择货位 -->
			<Drawer
				v-model:drawer="roomSelectorVisible"
				:size="modalSize.lg"
				destroy-on-close
			>
				<area-storage
					:store-id="formModal.storeInId!"
					:store-name="formModal.storeInName!"
					:store-code="formModal.storeInCode"
					:table-api="listMatStoreRoomPaged"
					@onSaveOrClose="handleAddRoom"
				/>
			</Drawer>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.drawer-container {
	.left {
		width: 310px;
	}

	.right {
		width: calc(100% - 310px);
	}

	.tab-mat {
		height: calc(100% - 43px);
	}
}
.custom-q {
	margin: 10px 0px -10px 10px !important;
}

.transfer-receipt-editor-table-wrapper {
	&:deep(.pitaya-table) {
		.error {
			.column-inner-item,
			.cell,
			.el-input__inner {
				color: red;
			}
		}
	}
}
</style>
