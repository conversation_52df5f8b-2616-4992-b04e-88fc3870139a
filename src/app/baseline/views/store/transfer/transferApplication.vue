<script lang="ts" setup>
import { onMounted, ref, computed } from "vue"
import {
	checkPermission,
	isCheckPermission
} from "@/app/platform/utils/permission"
import { powerList } from "../../components/define.d"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import { useDictInit } from "../../components/dictBase"
import { useMessageBoxInit } from "../../components/messageBox"
import EditDrawer from "./transferApplication/editorDrawer.vue"
import transferApplyDetail from "./transferApplication/viewDrawer.vue"

import {
	delTransferApply,
	getAllocationBmpStatusCnt,
	getTransferApplyList
} from "../../../api/store/transfer/apply"
import { listMatStoragePaged } from "@/app/baseline/api/store/manage-api"
import { IModalType } from "../../../utils/types/common"
import { modalSize } from "@/app/baseline/utils/layout-config"

import { ElMessage } from "element-plus"
import { BaseLineSysApi, getTaskByBusinessIds } from "../../../api/system"

import { getTransferApplyStatus } from "./transferApplication/transferApply"
import { appStatus, DictApi } from "@/app/baseline/api/dict"
import {
	MatStoreAllocationApplyRequestParams,
	MatStoreAllocationApplyVO
} from "../../../utils/types/store-transfer-apply"
import { useTbInit } from "../../components/tableBase"
import { omit } from "lodash-es"
import { useUserStore } from "@/app/platform/store/modules/user"
import ApprovedDrawer from "@/app/baseline/views/components/approvedDrawer.vue"
import { IInventoryBusinessType } from "@/app/baseline/utils/types/store-inventory"

const { dictOptions, getDictByCodeList } = useDictInit()
const { showDelConfirm } = useMessageBoxInit()
const { userInfo } = storeToRefs(useUserStore())

/**
 * 查询条件 配置
 */
const queryArrList = computed(() => {
	return [
		{
			name: "调拨单号",
			key: "code",
			placeholder: "请输入调拨单号",
			enableFuzzy: true,
			type: "input" // TODO
		},
		{
			name: "调拨单名称",
			key: "label",
			placeholder: "请输入调拨单名称",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "申请人",
			key: "userName",
			placeholder: "请输入申请人",
			enableFuzzy: true,
			type: "input" // TODO
		},
		{
			name: "移库类型",
			key: "type",
			placeholder: "请选择",
			type: "select",
			children: dictOptions.value["TRANSFER_TYPE"]
		},
		{
			name: "调拨状态",
			key: "status",
			placeholder: "请选择",
			type: "select",
			children: getTransferApplyStatus()
		},
		{
			name: "出库仓库",
			key: "storeId",
			placeholder: "请选择出库仓库",
			type: "tableSelect",
			tableInfo: [
				{
					title: "请选择出库仓库",
					tableApi: listMatStoragePaged, //表格接口
					tableColumns: [
						{ label: "仓库编码", prop: "code", width: 120 },
						{ label: "仓库名称", prop: "label" },
						{
							label: "仓库级别",
							prop: "level_view",
							width: 100
						},
						{
							label: "仓库类型",
							prop: "type_view",
							width: 120
						},
						{
							label: "所属段区",
							prop: "depotId_view",
							width: 150
						}
					],
					queryArrList: [
						{
							name: "仓库编码",
							key: "code",
							type: "input",
							placeholder: "请输入仓库编码"
						},
						{
							name: "仓库名称",
							key: "label",
							type: "input",
							placeholder: "请输入仓库名称"
						}
					],
					params: {
						currentPage: 1,
						pageSize: 20
					}, //表格接口参数

					labelName: {
						key: "id", //回显标识
						label: "label", //input框要展示的字段
						value: "id" //要给后台传的字段
					}
				}
			]
		},
		{
			name: "入库仓库",
			key: "storeInId",
			placeholder: "请选择入库仓库",
			type: "tableSelect",
			tableInfo: [
				{
					title: "请选择入库仓库",
					tableApi: listMatStoragePaged, //表格接口
					tableColumns: [
						{ label: "仓库编码", prop: "code", width: 120 },
						{ label: "仓库名称", prop: "label" },
						{
							label: "仓库级别",
							prop: "level_view",
							width: 100
						},
						{
							label: "仓库类型",
							prop: "type_view",
							width: 120
						},
						{
							label: "所属段区",
							prop: "depotId_view",
							width: 150
						}
					],
					queryArrList: [
						{
							name: "仓库编码",
							key: "code",
							type: "input",
							placeholder: "请输入仓库编码"
						},
						{
							name: "仓库名称",
							key: "label",
							type: "input",
							placeholder: "请输入仓库名称"
						}
					],
					params: {
						currentPage: 1,
						pageSize: 20
					}, //表格接口参数

					labelName: {
						key: "id", //回显标识
						label: "label", //input框要展示的字段
						value: "id" //要给后台传的字段
					}
				}
			]
		},
		{
			name: "申请部门",
			key: "sysOrgId",
			placeholder: "请选择申请部门",
			type: "treeSelect",
			treeApi: () =>
				BaseLineSysApi.getDepartmentTreeWithCompanyDisabled(
					userInfo.value.companyId
				)
		}
	]
})

/**
 * tab 切换数据源
 * @param data
 */
const getTableData = (data?: Record<string, any>) => {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	tableProp.value = [...tablePropsGroup.value] as TableColumnType[]
	fetchParam.value = {
		...fetchParam.value,
		bpmStatus: tabStatus,
		sord: "desc",
		sidx: "createdDate",
		...data
	}
	handleUpdate()
}

/**
 * title 配置
 */
const rightTitle = {
	name: ["调拨申请"],
	icon: ["fas", "square-share-nodes"]
}

const titleBtnConf = [
	{
		name: "新建调拨申请",
		roles: powerList.storeTransferApplyBtnCreate,
		icon: ["fas", "circle-plus"]
	}
]
const editorMode = ref(IModalType.edit)

/**
 * tab 配置
 */
const statusCnt = ref<number[]>([])
const tabList = [
	{
		name: "草稿箱",
		value: `${appStatus.pendingApproval},${appStatus.rejected}`
	},
	{
		name: "审批中",
		value: appStatus.underApproval
	},
	{
		name: "已审批",
		value: appStatus.approved
	}
]
const tabStatus = ref<string>(tabList[0].value)
const activeName = ref<string>(tabList[0].name)
const handleTabsClick = (tab: any) => {
	activeName.value = tab.paneName
	tabStatus.value = tabList[tab.index].value
	getTableData()
}

const {
	tableProp,
	tableRef,
	tableLoading,
	tableData,
	currentPage,
	pageTotal,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange,
	onDataSelected
} = useTbInit<MatStoreAllocationApplyVO, MatStoreAllocationApplyRequestParams>()

/**
 * table 列配置
 */
const tablePropsGroup = computed(() => {
	const all = {
		baseTop: [
			{
				label: "调拨单号",
				prop: "code",
				minWidth: 180,
				fixed: "left"
			},
			{
				label: "调拨单名称",
				prop: "label",
				width: 200
			},
			{ label: "移库类型", prop: "type_view", width: 150 },
			{
				label: "是否跨成本中心",
				prop: "crossCostCenter",
				needSlot: true,
				minWidth: 120
			},
			{ label: "出库仓库名称", prop: "storeName", minWidth: 150 },
			{ label: "入库仓库名称", prop: "storeInName", minWidth: 150 },
			{ label: "物资编码", prop: "materialNum", width: 100 }
		],
		baseCenter1: [
			{ label: "审批状态", prop: "bpmStatus", needSlot: true, minWidth: 100 } // 审批状态：0-待提交、1-审批中、2-已审批、3-已驳回
		],
		baseCenter3: [
			{ label: "调拨状态", prop: "status", needSlot: true, minWidth: 100 } // TODO 调拨状态：0-待提交、1-审批中、2-已审批、3-已驳回
		],
		baesBottom: [
			{ label: "申请人", prop: "userName_view", minWidth: 120 },
			{ label: "申请部门", prop: "sysOrgId_view", minWidth: 140 },
			{ label: "申请时间", prop: "createdDate", minWidth: 150, sortable: true },
			{
				label: "操作",
				width: 200,
				prop: "operations",
				needSlot: true,
				fixed: "right"
			}
		]
	}

	if (tabStatus.value === tabList[0].value) {
		return [...all.baseTop, ...all.baseCenter1, ...all.baesBottom]
	} else if (tabStatus.value === tabList[2].value) {
		return [...all.baseTop, ...all.baseCenter3, ...all.baesBottom]
	} else {
		return [...all.baseTop, ...all.baesBottom]
	}
})
fetchFunc.value = getTransferApplyList

const curRowId = ref<any>("")
const curRowData = ref<MatStoreAllocationApplyVO>({})
const viewVisible = ref<boolean>(false)
const approvedVisible = ref(false)
const editTask = ref<Record<string, any>>()

/**
 * 新建 操作
 */
const editorVisible = ref(false)
const handleClickAddBtn = () => {
	editorMode.value = IModalType.create
	editorVisible.value = true
	curRowId.value = ""
	curRowData.value = {}
}

/**
 * 查看 操作
 * @param row
 */
const onRowView = async (row: MatStoreAllocationApplyVO) => {
	curRowId.value = row.id
	curRowData.value = row
	editorMode.value = IModalType.view

	if (
		row.bpmStatus == appStatus.pendingApproval ||
		row.type === IInventoryBusinessType.transferApplyKN
	) {
		viewVisible.value = true
	} else {
		const res = await getTaskByBusinessIds({
			businessIds: [row.id],
			camundaKey: row.crossCostCenter
				? "allocation_spanned_apply"
				: "allocation_unspanned_apply"
		})

		if (Array.isArray(res) && res.length > 0) {
			editTask.value = { ...res[res.length - 1] }
			approvedVisible.value = true
		} else {
			viewVisible.value = true
		}
	}
}

/**
 * 编辑 操作
 * @param row
 */
const onRowEdit = (row: MatStoreAllocationApplyVO) => {
	curRowData.value = row
	curRowId.value = row.id
	editorMode.value = IModalType.edit
	editorVisible.value = true
}

/**
 * 移除 操作
 * @param row
 */
const onRowDel = async (row: MatStoreAllocationApplyVO) => {
	await showDelConfirm()
	await delTransferApply({ id: row.id as any })
	ElMessage.success("操作成功")
	handleUpdate()
}

const handleUpdate = async () => {
	statusCnt.value = await getAllocationBmpStatusCnt(
		omit({ ...fetchParam.value }, "bpmStatus")
	)
	fetchTableData()
}
onMounted(() => {
	getDictByCodeList(["TRANSFER_TYPE"])
	getTableData()
})

/**
 * 排序
 * @param column
 * @param prop 字段名称
 * @param order ascending 升序 descending 降序
 */
function handleSortChange(column: any, prop: any, order: any) {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		sord:
			order === "descending" ? "desc" : order === "ascending" ? "asc" : "desc",
		sidx: order ? prop : "createdDate" // 排序字段
	}

	fetchTableData()
}
</script>
<template>
	<div class="app-container">
		<div class="whole-frame">
			<ModelFrame class="top-frame">
				<Query
					:queryArrList="queryArrList"
					@getQueryData="getTableData"
					class="ml10"
				/>
			</ModelFrame>
			<ModelFrame class="bottom-frame">
				<Title
					:title="rightTitle"
					:button="(titleBtnConf as any)"
					@on-btn-click="handleClickAddBtn"
				>
					<div class="app-tabs-wrapper">
						<el-tabs v-model="activeName" @tab-click="handleTabsClick">
							<el-tab-pane
								v-for="(tab, index) in tabList"
								:key="index"
								:label="`${tab.name}（${statusCnt[index] ?? 0}）`"
								:name="tab.name"
								:index="tab.name"
							/>
						</el-tabs>
					</div>
				</Title>

				<PitayaTable
					ref="tableRef"
					:columns="tableProp"
					:tableData="tableData"
					:total="pageTotal"
					:need-index="true"
					:multi-select="false"
					:need-selection="false"
					@on-current-page-change="onCurrentPageChange"
					@onSelectionChange="onDataSelected"
					:table-loading="tableLoading"
					@on-table-sort-change="handleSortChange"
				>
					<!-- 是否跨成本中心 -->
					<template #crossCostCenter="{ rowData }">
						{{ rowData.crossCostCenter ? "是" : "否" }}
					</template>

					<!-- 审批状态 -->
					<template
						#bpmStatus="{ rowData }"
						v-if="tabStatus == tabList[0].value"
					>
						<dict-tag
							:options="DictApi.getBpmStatus()"
							:value="rowData.bpmStatus"
						/>
					</template>

					<!-- 调拨状态 -->
					<template #status="{ rowData }" v-if="tabStatus == tabList[2].value">
						<dict-tag
							:options="getTransferApplyStatus()"
							:value="rowData.status"
						/>
					</template>

					<template #operations="{ rowData }">
						<slot
							v-if="
								(tabStatus == tabList[0].value &&
									isCheckPermission(powerList.storeTransferApplyBtnEdit)) ||
								isCheckPermission(powerList.storeTransferApplyBtnPreview) ||
								(tabStatus == tabList[0].value &&
									isCheckPermission(powerList.storeTransferApplyBtnDrop))
							"
						>
							<el-button
								v-btn
								link
								@click="onRowEdit(rowData)"
								v-if="
									tabStatus == tabList[0].value &&
									isCheckPermission(powerList.storeTransferApplyBtnEdit)
								"
								:disabled="checkPermission(powerList.storeTransferApplyBtnEdit)"
							>
								<font-awesome-icon :icon="['fas', 'pen-to-square']" />
								<span class="table-inner-btn">编辑</span>
							</el-button>
							<el-button
								v-btn
								link
								@click="onRowView(rowData)"
								:disabled="
									checkPermission(powerList.storeTransferApplyBtnPreview)
								"
								v-if="isCheckPermission(powerList.storeTransferApplyBtnPreview)"
							>
								<font-awesome-icon :icon="['fas', 'eye']" />
								<span class="table-inner-btn">查看</span>
							</el-button>
							<el-button
								v-btn
								link
								@click="onRowDel(rowData)"
								v-if="
									tabStatus == tabList[0].value &&
									isCheckPermission(powerList.storeTransferApplyBtnDrop)
								"
								:disabled="checkPermission(powerList.storeTransferApplyBtnDrop)"
							>
								<font-awesome-icon :icon="['fas', 'trash-can']" />
								<span class="table-inner-btn">移除</span>
							</el-button>
						</slot>
						<slot v-else>---</slot>
					</template>
				</PitayaTable>

				<!-- 编辑/新建 弹窗 -->
				<Drawer
					:size="modalSize.xl"
					v-model:drawer="editorVisible"
					:destroyOnClose="true"
				>
					<edit-drawer
						:mode="editorMode"
						:id="curRowId"
						:row="curRowData"
						@close="editorVisible = false"
						@update="handleUpdate"
					/>
				</Drawer>

				<!-- 查看 弹窗 -->
				<Drawer
					:size="modalSize.xl"
					v-model:drawer="viewVisible"
					:destroyOnClose="true"
				>
					<transfer-apply-detail
						:mode="editorMode"
						:id="curRowId"
						@close="viewVisible = false"
					/>
				</Drawer>

				<!-- 审批、查看弹窗 -->
				<Drawer
					:size="modalSize.xxl"
					v-model:drawer="approvedVisible"
					:destroyOnClose="true"
				>
					<approved-drawer
						:mod="editorMode"
						:task-value="editTask"
						@on-save-or-close="approvedVisible = false"
					/>
				</Drawer>
			</ModelFrame>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
