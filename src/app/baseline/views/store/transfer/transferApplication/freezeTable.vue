<!-- 冻结信息 -->
<template>
	<div class="drawer-container">
		<div class="drawer-column" style="width: 100%">
			<Title :title="titleConf" />

			<el-scrollbar class="rows">
				<!-- 明细信息 -->
				<pitaya-table
					ref="tableRef"
					:columns="tableProp"
					:table-data="tableData"
					:need-selection="false"
					:single-select="false"
					:need-index="true"
					:need-pagination="true"
					:total="pageTotal"
					:table-loading="tableLoading"
					:max-height="maxTableHeight"
					@on-selection-change="selectedTableList = $event"
					@on-current-page-change="onCurrentPageChange"
				>
					<!-- 冻结数量 -->
					<template #freezeNum="{ rowData }">
						{{ toFixedTwo(rowData.freezeNum) }}
					</template>
				</pitaya-table>
			</el-scrollbar>

			<button-list
				class="footer"
				:button="btnConf"
				@on-btn-click="emit('close')"
			/>
		</div>
	</div>
</template>

<script setup lang="ts">
import { useTbInit } from "../../../components/tableBase"

import { toFixedTwo, maxTableHeight } from "@/app/baseline/utils"
import { getTransferApplyListFreezePage } from "@/app/baseline/api/store/transfer/apply"
import { MatStoreFreezeVo } from "@/app/baseline/utils/types/store-inventory"
import { MatStoreFreezeQueryParams } from "@/app/baseline/utils/types/store-transfer-apply"

const props = defineProps<{
	/**
	 * 物资id
	 */
	materialId: any

	/**
	 * 货位Id
	 */
	sourceRoomId?: any

	/**
	 * 批次号
	 */
	batchNo?: any
}>()

const emit = defineEmits<{
	(e: "close"): void
}>()

const titleConf = computed(() => ({
	name: ["冻结信息"],
	icon: ["fas", "square-share-nodes"]
}))

const btnConf = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	}
]

const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = useTbInit<MatStoreFreezeVo, MatStoreFreezeQueryParams>()

fetchFunc.value = getTransferApplyListFreezePage

tableProp.value = [
	{ label: "业务类型", prop: "type_view", width: 180 },
	{ label: "业务单据号", prop: "code" },
	{ label: "冻结量", prop: "freezeNum", needSlot: true, align: "right" },
	{ label: "申请人", prop: "createdBy_view" },
	{ label: "申请部门", prop: "sysOrgId_view" },
	{ label: "申请时间", prop: "createdDate" }
]

onMounted(() => {
	fetchParam.value = {
		...fetchParam.value,
		materialId: props.materialId,
		sourceRoomId: props.sourceRoomId,
		batchNo: props.batchNo,
		sord: "desc",
		sidx: "createdDate"
	}

	fetchTableData()
})
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
