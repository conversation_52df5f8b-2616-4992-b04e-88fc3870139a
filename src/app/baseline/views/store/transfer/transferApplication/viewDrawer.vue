<script setup lang="ts">
import { FormElementType } from "@/app/baseline/views/components/define"
import { modalSize } from "@/app/baseline/utils/layout-config"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import LinkTag from "@/app/baseline/views/components/linkTag.vue"
import { useDictInit } from "../../../components/dictBase"
import { useTbInit } from "../../../components/tableBase"
import { onMounted, reactive, ref, toRef } from "vue"
import TableFile from "@/app/baseline/views/components/tableFile.vue"
import { DictApi, fileBusinessType, appStatus } from "@/app/baseline/api/dict"
import {
	MatStoreAllocationApplyItemRequestParams,
	MatStoreAllocationApplyItemVO,
	MatStoreAllocationApplyVO
} from "../../../../utils/types/store-transfer-apply"
import {
	getTransferApplyById,
	getTransferApplyDetail
} from "../../../../api/store/transfer/apply"
import { batchFormatterNumView } from "@/app/baseline/utils"
import { IModalType } from "@/app/baseline/utils/types/common"
import FreezeTable from "./freezeTable.vue"

export interface Props {
	id: string | number //
	mode?: string //显示模式 view || 审批 approved
	footerBtnVisible?: boolean
}

const props = withDefaults(defineProps<Props>(), { footerBtnVisible: true })
const emits = defineEmits(["close"])
const currentId = toRef(props, "id")
const loading = ref(false)
const formBtnList = [{ name: "取消", icon: ["fas", "circle-minus"] }]

const formModal = ref<MatStoreAllocationApplyVO>({})
const drawerLoading = ref(false)

/**
 * 左侧 Title 配置
 */
const drawerLeftTitle = {
	name: ["调拨申请"],
	icon: ["fas", "square-share-nodes"]
}

/**
 * 左侧 表单 配置
 */
const formEl = reactive<FormElementType[]>([
	{
		label: "调拨单号",
		name: "code"
	},
	{
		label: "调拨单名称",
		name: "label"
	},
	{
		label: "移库类型",
		name: "type_view"
	},
	{
		label: "出库仓库名称",
		name: "storeName"
	},
	{
		label: "入库仓库名称",
		name: "storeInName"
	},
	{
		label: "物资编码",
		name: "materialNum"
	},
	{
		label: "申请人",
		name: "userName_view"
	},
	{
		label: "申请部门",
		name: "sysOrgId_view"
	},
	{
		label: "申请时间",
		name: "createdDate"
	}
])

/**
 * 右侧 title 配置
 */
const drawerRightTitle = {
	name: ["扩展信息"],
	icon: ["fas", "square-share-nodes"]
}

/**
 * 扩展栏标签页切换
 */
const tabList = ["物资明细", "相关附件"]
const activeTab = ref<number>(0)
const handleTabChange = (index: number) => {
	activeTab.value = index

	if (index === 0) {
		fetchParam.value = {
			applyId: props.id,
			sord: "desc",
			sidx: "createdDate"
		}
		pageSize.value = 20
		getTableData()
	}
}

const { dictOptions, getDictByCodeList, dictFilter } = useDictInit()

/**
 * 查询条件配置
 */
const queryArrList = computed(() => [
	{
		name: "物资编码",
		key: "materialCode",
		placeholder: "请输入物资编码",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资名称",
		key: "materialLabel",
		placeholder: "请输入物资名称",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "规格型号",
		key: "version",
		placeholder: "请输入规格型号",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资性质",
		key: "attribute",
		type: "select",
		children: dictOptions.value.MATERIAL_NATURE,
		placeholder: "请选择物资性质"
	}
])

const {
	currentPage,
	tableData,
	tableProp,
	tableRef,
	tableLoading,
	pageTotal,
	pageSize,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange,
	onDataSelected
} = useTbInit<
	MatStoreAllocationApplyItemVO,
	MatStoreAllocationApplyItemRequestParams
>()

/**
 * table 列 配置
 */
tableProp.value = [
	{ label: "物资编码", prop: "materialCode", width: 130, fixed: "left" },
	{ label: "物资名称", prop: "materialLabel", minWidth: 100 },
	{ label: "规格型号", prop: "version", minWidth: 100 },
	{ label: "技术参数", prop: "technicalParameter", minWidth: 100 },
	{ label: "物资性质", prop: "attribute", needSlot: true, width: 120 },
	{ label: "库存单位", prop: "useUnit", needSlot: true, width: 80 },
	{ label: "来源区域名称", prop: "regionLabel", minWidth: 100 },
	{ label: "来源货位编码", prop: "sourceRoomCode", width: 100 },
	{ label: "批次", prop: "batchNo", width: 180 },
	{ label: "库存数量", prop: "roomNum_view", align: "right", minWidth: 100 },
	{
		label: "冻结量",
		prop: "frozenNum_view",
		align: "right",
		minWidth: 100,
		needSlot: true
	},
	{ label: "移库数量", prop: "num_view", align: "right", minWidth: 100 }
	/* { label: "目标区域名称", prop: "regionInLabel" },
	{ label: "目标货位编码", prop: "targetRoomCode", minWidth: 100 } */
]

/**
 * 格式化数量
 */
watch(tableData, () => {
	batchFormatterNumView(tableData.value as any[])
})

/**
 * 查看 冻结数量
 */
const freezeNumTableVisible = ref(false)
const editorRow = ref<MatStoreAllocationApplyItemVO>({})
function showFreezeNumDetail(e: MatStoreAllocationApplyItemVO) {
	editorRow.value = { ...e }
	freezeNumTableVisible.value = true
}

fetchFunc.value = getTransferApplyDetail
const getDetail = () => {
	fetchParam.value = {
		...fetchParam.value,
		applyId: props.id,
		sord: "desc",
		sidx: "createdDate"
	}
	fetchTableData()
}

onMounted(() => {
	drawerLoading.value = true

	getTransferApplyById({ id: props.id as number })
		.then((res: any) => {
			formModal.value = { ...res }
		})
		.finally(() => {
			drawerLoading.value = false
		})

	getDictByCodeList(["INVENTORY_UNIT", "MATERIAL_NATURE"])
	getDetail()
})

const getTableData = (data?: any) => {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		...data
	}
	fetchTableData()
}

defineOptions({
	name: "transferApplyDetail"
})
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<!-- 左侧表单区域 -->
		<div class="drawer-column left">
			<div class="rows">
				<Title :title="drawerLeftTitle" />
				<el-descriptions size="small" :column="1" border class="content">
					<template>
						<el-descriptions-item
							v-for="(el, index) in formEl"
							label-align="center"
							:label="el.label"
							:key="index"
						>
							<span>
								{{
									formModal[el.name] || formModal[el.name] == 0
										? formModal[el.name]
										: "---"
								}}
							</span>
						</el-descriptions-item>
					</template>
				</el-descriptions>
			</div>
		</div>

		<!-- 右侧table区域 -->
		<div class="drawer-column right" :class="{ pdr10: !footerBtnVisible }">
			<div class="rows">
				<Title :title="drawerRightTitle">
					<Tabs class="tabs" :tabs="tabList" @on-tab-change="handleTabChange" />
				</Title>

				<el-scrollbar class="tab-mat" v-if="activeTab === 0">
					<Query
						:queryArrList="queryArrList"
						@getQueryData="getTableData"
						class="custom-q"
					/>
					<PitayaTable
						ref="tableRef"
						:columns="tableProp"
						:table-data="tableData"
						:need-index="true"
						:need-pagination="true"
						:single-select="false"
						:need-selection="false"
						:total="pageTotal"
						@onSelectionChange="onDataSelected"
						@on-current-page-change="onCurrentPageChange"
						:table-loading="tableLoading"
					>
						<!-- 冻结量下钻 -->
						<template #frozenNum_view="{ rowData }">
							<link-tag
								:value="rowData.frozenNum_view"
								@on-click="showFreezeNumDetail(rowData)"
							/>
						</template>

						<!-- 物资性质 -->
						<template #attribute="{ rowData }">
							<dict-tag
								:options="DictApi.getMatAttr()"
								:value="rowData.attribute"
							/>
						</template>

						<template #useUnit="{ rowData }">
							{{
								dictFilter("INVENTORY_UNIT", rowData.useUnit)?.subitemName ||
								"---"
							}}
						</template>
					</PitayaTable>
				</el-scrollbar>
				<div v-if="activeTab === 1">
					<TableFile
						:business-type="fileBusinessType.transferApply"
						:business-id="currentId"
						:mod="
							footerBtnVisible && formModal.bpmStatus === appStatus.approved
								? IModalType.edit
								: IModalType.view
						"
					/>
				</div>
			</div>

			<ButtonList
				v-if="footerBtnVisible"
				class="footer"
				:button="formBtnList"
				:loading="loading"
				@on-btn-click="emits('close')"
			/>
		</div>

		<!-- 冻结量 查看下钻 -->
		<Drawer
			v-model:drawer="freezeNumTableVisible"
			:size="modalSize.lg"
			destroy-on-close
		>
			<freeze-table
				:material-id="editorRow.materialId"
				:sourceRoomId="editorRow.sourceRoomId"
				:batchNo="editorRow.batchNo"
				@close="freezeNumTableVisible = false"
			/>
		</Drawer>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.custom-q {
	margin: 10px 0px -10px 10px !important;
}
.drawer-container {
	.left {
		width: 310px;
	}

	.right {
		width: calc(100% - 310px);
	}

	.tab-mat {
		height: calc(100% - 43px);
	}
}
</style>
