<script setup lang="ts">
import { computed, onMounted, reactive, ref } from "vue"
import {
	IIdempotentTokenType,
	IIdempotentTokenTypePre,
	IModalType
} from "../../../../utils/types/common"
import { ElMessage, FormInstance, FormRules } from "element-plus"
import { useDictInit } from "../../../components/dictBase"
import formElement from "../../../components/formElement.vue"
import tableFile from "../../../components/tableFile.vue"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import LinkTag from "@/app/baseline/views/components/linkTag.vue"
import gridPanel from "../../components/gridPanel.vue"
import { FormElementType } from "../../../components/define"
import { useUserStore } from "@/app/platform/store/modules/user"
import { DictApi, fileBusinessType } from "@/app/baseline/api/dict"
import { storeToRefs } from "pinia"

import { useTbInit } from "../../../components/tableBase"
import { modalSize, inputMaxLength } from "@/app/baseline/utils/layout-config"
import {
	debounce,
	map,
	pick,
	includes,
	findIndex,
	isNil,
	toNumber
} from "lodash-es"
import { MatStoreVo } from "../../../../utils/types/store-manage"

import storeTable from "../../components/storeTable.vue"
import matSelector from "../../components/matSelector.vue"
import areaStorage from "../../warehouse/receivedStored/areaStorage.vue"

import {
	IsCrossCostCenter,
	IsCrossCostCenterStatus,
	getIsCrossCostCenter,
	isCKDBKN
} from "./transferApply"
import {
	delMatTransferApply,
	editMatTransferApply,
	getTransferApplyDetail,
	updateTransferApply,
	getMatTransferApplyList,
	addMatTransferApply,
	publishTransferApply,
	getTransferApplyById,
	saveTransferApply
} from "../../../../api/store/transfer/apply"
import { useMessageBoxInit } from "../../../components/messageBox"
import {
	MatStoreAllocationApplyItemRequestParams,
	MatStoreAllocationApplyItemVO,
	MatStoreAllocationApplyVO
} from "../../../../utils/types/store-transfer-apply"
import { listMatStoreRoomPaged } from "../../../../api/store/manage-api"
import { useApplyResultUtils } from "../../hooks/apply-result-utils"
import {
	getIdempotentToken,
	validateAndCorrectInput
} from "@/app/baseline/utils/validate"
import { batchFormatterNumView, getModalTypeLabel } from "@/app/baseline/utils"
import { IInventoryBusinessType } from "@/app/baseline/utils/types/store-inventory"
import FreezeTable from "./freezeTable.vue"
const { dictOptions, dictFilter, getDictByCodeList } = useDictInit()
const { showDelConfirm, showWarnConfirm } = useMessageBoxInit()
const { userInfo } = storeToRefs(useUserStore())

const props = defineProps<{
	row?: MatStoreAllocationApplyVO
	id: any // 调拨申请ID
	mode: IModalType // create || edit || create
}>()

const emits = defineEmits<{
	(e: "update"): void
	(e: "close"): void
}>()

const warehouseSelectorVisible = ref(false) // 仓库选择器 visible
const selStoreType = ref("") // 仓库类型 "" || 'In'

/**
 * 保存旧的表单数据
 */
const oldFormData = ref<string>()

const formRef = ref<FormInstance>()
const formData = ref<Record<string, any>>({})

const tabsConf = ["物资明细", "相关附件"]
const activatedTab = ref(0)

const drawerLoading = ref(false)

// 左侧title 配置
const titleConf = computed(() => ({
	name: [
		getModalTypeLabel(
			canEditExtra.value ? IModalType.edit : IModalType.create,
			"调拨申请"
		)
	],
	icon: ["fas", "square-share-nodes"]
}))

// 左侧btn 配置
const drawerBtnLoading = ref(false)
const drawerBtnConf = computed(() => [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "保存草稿",
		icon: ["fas", "floppy-disk"]
	}
	/*  */
])

const drawerBtnRightConf = computed(() => [
	{
		name: isCKDBKN(formData.value?.type) ? "提交" : "提交审核",
		icon: ["fas", "circle-check"]
	}
])

const gridPanelOptions = computed(() => {
	return [
		{
			label: "物资编码",
			value: formData.value.materialNum ?? 0
		},
		{
			label: "调拨数量",
			value: parseInt(formData.value.totalNum) || 0
		}
	]
})

// 左侧表单 配置
const formElBase = computed<FormElementType[][]>(() => {
	return [
		[
			{
				label: "调拨单名称",
				name: "label",
				maxlength: inputMaxLength.input
			}
		],
		[
			{
				label: "移库类型",
				name: "type",
				type: "select",
				placeholder: "请选择",
				data: dictOptions.value["TRANSFER_TYPE"],
				disabled: canEditExtra.value,
				width: 12,
				change: (e?: string) => {
					if (!e) {
						return
					}

					if (isCKDBKN(formData.value?.type)) {
						// 如果是库内移库  入库 = 仓库
						formData.value.storeInType = formData.value.storeType
						formData.value.storeInId = formData.value.storeId
						formData.value.storeInName = formData.value.storeName
						formData.value.storeInCode = formData.value.storeCode
						formData.value.depotInId = formData.value.depotId
						formData.value.depotInId_view = formData.value.depotId_view
						formData.value.costCenterInId = formData.value.costCenterId
						formData.value.costCenterInId_view =
							formData.value.costCenterId_view

						formData.value.crossCostCenter = IsCrossCostCenter[e as any]
					} else {
						formData.value.crossCostCenter = ""
						formData.value.storeInType = ""
						formData.value.storeInId = ""
						formData.value.storeInName = ""
						formData.value.storeInCode = ""
						formData.value.depotInId = ""
						formData.value.depotInId_view = ""
						formData.value.costCenterInId = ""
						formData.value.costCenterInId_view = ""
					}

					setTimeout(() => {
						formRef.value?.clearValidate()
					}, 0)
				}
			},
			{
				label: "是否跨成本中心",
				name: "crossCostCenter",
				type: "select",
				placeholder: "请选择",
				disabled: true,
				//isCKDBKN(formData.value?.type) || props.mode == IModalType.edit,
				data: getIsCrossCostCenter(),
				width: 12
			}
		],
		[
			{
				label: "调拨原因说明",
				name: "reason",
				type: "textarea",
				maxlength: inputMaxLength.textarea
			}
		]
	]
})
const formElInfo = computed<FormElementType[][]>(() => {
	return [
		[
			{
				label: "出库仓库",
				name: "storeName",
				type: "drawer",
				width: 12,
				placeholder: "请选择",
				disabled: canEditExtra.value,
				clickApi: () => {
					warehouseSelectorVisible.value = true
					selStoreType.value = ""
				}
			},
			{
				label: "入库仓库",
				name: "storeInName",
				type: "drawer",
				placeholder: "请选择",
				disabled: isCKDBKN(formData.value?.type) || canEditExtra.value, // 是库内移库 disabled
				width: 12,
				clickApi: () => {
					warehouseSelectorVisible.value = true
					selStoreType.value = "In"
				}
			}
		],
		[
			{
				label: "出库段区",
				name: "depotId_view",
				type: "input",
				disabled: true,
				width: 12
			},
			{
				label: "入库段区",
				name: "depotInId_view",
				type: "input",
				disabled: true,
				width: 12
			}
		],
		[
			{
				label: "出库成本中心",
				name: "costCenterId_view",
				type: "input",
				disabled: true,
				width: 12
			},
			{
				label: "入库成本中心",
				name: "costCenterInId_view",
				type: "input",
				disabled: true,
				width: 12
			}
		],
		[
			{
				label: "申请部门",
				placeholder: "请选择",
				name: "sysOrgId_view",
				type: "drawer",
				disabled: true,
				clear: false
			}
		]
	]
})
// 出库仓库
const validateStore = (rule: any, value: any, callback: any) => {
	if (!formData.value?.storeInId || !formData.value?.type) {
		callback()
	}

	if (isCKDBKN(formData.value?.type)) {
		// 库内移库
		if (formData.value?.storeId != formData.value?.storeInId) {
			return callback("出库和入库仓库需相同")
		}
		callback()
	} else {
		if (formData.value?.storeId == formData.value?.storeInId) {
			return callback("出库和入库仓库不能相同")
		}
		callback()
	}
}

// 入库仓库
const validateInStore = (rule: any, value: any, callback: any) => {
	if (!formData.value?.storeId || !formData.value?.type) {
		callback()
	}

	if (isCKDBKN(formData.value?.type)) {
		// 库内移库
		if (formData.value?.storeId != formData.value?.storeInId) {
			return callback("出库和入库仓库需相同")
		}
		callback()
	} else {
		if (formData.value?.storeId == formData.value?.storeInId) {
			return callback("出库和入库仓库不能相同")
		}
		callback()
	}
}

// 左侧表单校验
const formRules = reactive<FormRules<typeof formData>>({
	label: {
		required: true,
		message: "调拨单名称不能为空",
		trigger: "change"
	},
	crossCostCenter: {
		required: true,
		message: "是否跨成本中心不能为空",
		trigger: "change"
	},
	type: {
		required: true,
		message: "移库类型不能为空",
		trigger: "change"
	},
	storeName: [
		{ required: true, message: "出库仓库不能为空", trigger: "change" },
		{ validator: validateStore, required: true, trigger: "change" }
	],
	storeInName: [
		{ required: true, message: "入库仓库不能为空", trigger: "change" },
		{ validator: validateInStore, required: true, trigger: "change" }
	]
})

// 初始化 个人用户信息 - 申请部门
const initUserInfo = () => {
	formData.value.sysOrgId = userInfo.value.orgId
	formData.value.sysOrgId_view = userInfo.value.orgName
}

/**
 * 仓库选择 handler
 * @param btnName 按钮名称
 * @param storeIds 仓库id列表
 */
const handleStoreSelect = (btnName: string, store?: MatStoreVo) => {
	if (btnName === "取消") {
		warehouseSelectorVisible.value = false
		return
	}
	formData.value[`store${selStoreType.value}Type`] = store?.type // 仓库类型
	formData.value[`store${selStoreType.value}Id`] = store?.id
	formData.value[`store${selStoreType.value}Name`] = store?.label
	formData.value[`store${selStoreType.value}Code`] = store?.code
	formData.value[`depot${selStoreType.value}Id`] = store?.depotId
	formData.value[`depot${selStoreType.value}Id_view`] = store?.depotId_view

	formData.value[`costCenter${selStoreType.value}Id`] = store?.costCenterId
	formData.value[`costCenter${selStoreType.value}Id_view`] =
		store?.costCenterId_view
	//store?.costCenterId_view
	warehouseSelectorVisible.value = false

	if (isCKDBKN(formData.value?.type)) {
		// 如果是库内移库  入库 = 仓库
		formData.value.storeInType = store?.type
		formData.value.storeInId = store?.id
		formData.value.storeInName = store?.label
		formData.value.storeInCode = store?.code
		formData.value.depotInId = store?.depotId
		formData.value.depotInId_view = store?.depotId_view

		formData.value.costCenterInId = store?.costCenterId
		formData.value.costCenterInId_view = store?.costCenterId_view
	} else {
		if (
			!isNil(formData.value[`costCenterId`]) &&
			!isNil(formData.value[`costCenterInId`])
		) {
			formData.value.crossCostCenter =
				formData.value[`costCenterId`] == formData.value[`costCenterInId`]
					? IsCrossCostCenterStatus.no
					: IsCrossCostCenterStatus.yes
		}
	}
}

/**
 * 保存草搞 || 提交审核 || 取消
 * @param name
 */
const handleDrawerBtnClick = (name?: string) => {
	if (name == "保存草稿") {
		if (!formRef.value) {
			return
		}
		formRef.value?.validate(async (valid) => {
			if (!valid) {
				return
			}
			drawerBtnLoading.value = true

			try {
				const api = canEditExtra.value ? updateTransferApply : saveTransferApply

				let idempotentToken = ""
				if (!canEditExtra.value) {
					idempotentToken = getIdempotentToken(
						IIdempotentTokenTypePre.apply,
						IIdempotentTokenType.transferApply
					)
				}

				const r = await api(
					pick(
						formData.value,
						"id",
						"label",
						"costCenterId",
						"costCenterInId",
						"depotId",
						"depotInId",
						"crossCostCenter",
						"storeId",
						"storeInId",
						"sysOrgId",
						"type",
						"reason"
					),
					idempotentToken
				)

				ElMessage.success("操作成功")
				emits("update")

				formData.value.id = r.id

				fetchParam.value.applyId = r.id

				oldFormData.value = JSON.stringify(formData.value)

				getTableData()
			} finally {
				drawerBtnLoading.value = false
			}
		})
	} else if (name == "提交审核" || name == "提交") {
		if (!formRef.value) {
			return
		}

		formRef.value?.validate(async (valid) => {
			if (!valid) {
				return
			}
			await showWarnConfirm("请确认是否提交本次数据？")

			drawerBtnLoading.value = true
			drawerLoading.value = true

			try {
				// 如果主表有修改，则先更新主表数据
				if (oldFormData.value != JSON.stringify(formData.value)) {
					await updateTransferApply(formData.value as any)
				}

				const idempotentToken = getIdempotentToken(
					IIdempotentTokenTypePre.other,
					IIdempotentTokenType.transferApply,
					formData.value.id
				)

				const { code, msg, data } = await publishTransferApply(
					props.id || formData.value.id,
					idempotentToken
				)
				if (data && code != 200) {
					handleApplyResultByCode(code, msg, data)
				} else {
					emits("update")
					emits("close")
				}
			} finally {
				drawerBtnLoading.value = false
				drawerLoading.value = false
			}
		})
	} else if (name === "取消") {
		emits("close")
	}
}

// 右侧 title 配置
const extraTitleConf = {
	name: ["相关信息"],
	icon: ["fas", "square-share-nodes"]
}

const handleTabChange = (index: number) => {
	activatedTab.value = index

	if (index === 0) {
		fetchParam.value = {
			applyId: props.id || formData.value.id,
			sord: "desc",
			sidx: "createdDate"
		}
		pageSize.value = 20
		getTableData()
	}
}

// 能否编辑扩展信息
const canEditExtra = computed(() => Boolean(formData.value?.id || props.id))

/**
 * 查询条件配置
 */
const queryArrList = computed(() => [
	{
		name: "物资编码",
		key: "materialCode",
		placeholder: "请输入物资编码",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资名称",
		key: "materialLabel",
		placeholder: "请输入物资名称",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "规格型号",
		key: "version",
		placeholder: "请输入规格型号",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资性质",
		key: "attribute",
		type: "select",
		children: dictOptions.value.MATERIAL_NATURE,
		placeholder: "请选择物资性质"
	}
])

const getTableData = (data?: any) => {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		...data
	}
	fetchTableData()
}

const {
	currentPage,
	tableCache,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	pageSize,
	selectedTableList,
	fetchTableData,
	fetchFunc,
	tableProp,
	fetchParam,
	onCurrentPageChange
} = useTbInit<
	MatStoreAllocationApplyItemVO,
	MatStoreAllocationApplyItemRequestParams
>()

const formDataId = computed(() => {
	return formData.value.id
})
const {
	handleApplyResultByCode,
	errorGoodsList,
	cancelRollPolling,
	clearTimer
} = useApplyResultUtils({
	fetchSubmitApi: publishTransferApply,
	fetchTableData: getTableData,
	id: formDataId,
	successCb: () => {
		emits("update")
		emits("close")
	}
})

/**
 * 物资明细 table 行样式
 *
 * 库存不足物资，用红色标记该行
 */
function tbCellClassName({ row }: any) {
	return includes(errorGoodsList.value, row.id) ? "error" : ""
}

tableProp.value = [
	{
		prop: "materialCode",
		label: "物资编码",
		width: 130,
		fixed: "left"
	},
	{
		prop: "materialLabel",
		label: "物资名称",
		minWidth: 120
	},
	{
		prop: "version",
		label: "规格型号",
		width: 120
	},
	{
		prop: "technicalParameter",
		label: "技术参数",
		minWidth: 120
	},
	{ label: "物资性质", prop: "attribute", needSlot: true, width: 120 },
	{
		prop: "useUnit",
		label: "库存单位",
		needSlot: true,
		width: 80
	},
	{
		prop: "sourceRoomCode",
		label: "来源货位编码",
		width: 100
	},
	{
		prop: "batchNo",
		label: "批次",
		width: 180
	},
	{
		prop: "roomNum_view",
		label: "库存数量",
		align: "right"
	},
	{
		prop: "frozenNum_view",
		label: "冻结量",
		align: "right",
		needSlot: true
	},
	{
		prop: "num",
		label: "移库数量",
		width: 120,
		needSlot: true,
		fixed: "right"
	}
	/* {
		prop: "regionInLabel",
		label: "目标区域",
		width: 120,
		fixed: "right"
	},
	{
		prop: "targetRoomCode",
		label: "目标货位",
		minWidth: 100,
		fixed: "right"
	} */
]

/**
 * 格式化数量
 */
watch(tableData, () => {
	batchFormatterNumView(tableData.value as any[])
})

fetchFunc.value = getTransferApplyDetail

const validateNum = (e: MatStoreAllocationApplyItemVO) => {
	const canGetNum = (e.roomNum ?? 0) - (e.frozenNum ?? 0) // 最大移库数量
	const num = toNumber(e.num)

	e.num = num

	if (num > canGetNum) {
		// 最大不能超过最大移库数量
		ElMessage.warning("移库数量不能大于库存数量-冻结量")
		e!.num = canGetNum
	} else if (e.num <= 0) {
		// 移库数量不能为负数
		/* e.num = 0
		return (e.num = 0) */
		const oldRow = tableCache.find((v) => v.id == e.id)
		e.num = oldRow.num
		ElMessage.warning("移库数量不能小于等于0！")
		return
	}

	updateMatTransferApply(e)
}
// 更新移库数量
const updateMatTransferApply = debounce(
	async (e: MatStoreAllocationApplyItemVO) => {
		await editMatTransferApply({
			applyId: props.id || formData.value.id,
			dtoList: [{ ...e, num: e.num }]
		})

		//  更新 tableCache
		const rowIdx = findIndex(tableCache, (v) => v.id === e.id)
		if (rowIdx !== -1) {
			tableCache.splice(rowIdx, 1, { ...e })
		}

		getDetail()
		ElMessage.success("操作成功")
	},
	300
)

/**
 * 查看 冻结数量
 */
const freezeNumTableVisible = ref(false)
const editorRow = ref<MatStoreAllocationApplyItemVO>({})
function showFreezeNumDetail(e: MatStoreAllocationApplyItemVO) {
	editorRow.value = { ...e }
	freezeNumTableVisible.value = true
}

// 添加物资 按钮配置
const tbBtnConf = computed(() => {
	return [
		{
			name: "添加物资",
			icon: ["fas", "circle-plus"]
		},
		/* {
			name: "选择货位",
			icon: ["fas", "circle-plus"],
			disabled:
				selectedTableList.value.length > 0 && formData.value.storeInId
					? false
					: true
		}, */
		{
			name: "批量删除",
			icon: ["fas", "trash-can"],
			disabled: selectedTableList.value.length > 0 ? false : true
		}
	]
})
const selectRoomIds = ref("")
const handleAddGoodAndRoom = (name?: string) => {
	if (name === "添加物资") {
		goodsSelectorVisible.value = true
		selectMatIds.value = []
	} else if (name === "选择货位") {
		selectRoomIds.value = map(
			selectedTableList.value,
			({ sourceRoomId }) => sourceRoomId
		).toString()
		roomSelectorVisible.value = true
	} else if (name === "批量删除") {
		delMatTransfer()
	}
}

/**
 * 删除物资
 */
const delMatTransfer = async () => {
	drawerBtnLoading.value = true
	try {
		const ids = map(selectedTableList.value, ({ id }) => id).toString()
		await showDelConfirm()
		await delMatTransferApply({ ids })
		ElMessage.success("操作成功")
		fetchTableData()
		getDetail()
		emits("update")
	} finally {
		drawerBtnLoading.value = false
	}
}

// 添加物资
const goodsSelectorVisible = ref(false)
const selectMatIds = ref<any[]>([])
const handleAddGoods = async (params: MatStoreAllocationApplyItemVO[]) => {
	const idempotentToken = getIdempotentToken(
		IIdempotentTokenTypePre.other,
		IIdempotentTokenType.transferApply,
		formData.value.id
	)
	const res = await addMatTransferApply(
		{
			applyId: props.id || formData.value.id,
			dtoList: params || []
		},
		idempotentToken
	)
	if (res.isOutOfStore) {
		selectMatIds.value = res.ids!
		return await showWarnConfirm(
			`存在${selectMatIds.value.length}种物资库存数量不足，请重新选择物资！`,
			false
		)
	} else {
		selectMatIds.value = []
	}

	goodsSelectorVisible.value = false
	ElMessage.success("操作成功")
	getTableData()
	emits("update")
}

/**
 *
 * @param name : 保存 || 取消
 * @param roomList ： 货位
 * @param regionList : 区域
 */
const roomSelectorVisible = ref(false)
const handleAddRoom = async (name: string, treeList: any, row: any) => {
	if (name === "取消") {
		roomSelectorVisible.value = false
		return
	}
	if (name === "保存") {
		const params = map(selectedTableList.value, (v) => ({
			...v,
			targetRoomCode: row.code,
			targetRoomId: row.id,
			regionInId: treeList.id,
			regionInLabel: treeList.label
		}))

		await editMatTransferApply({
			applyId: props.id || formData.value.id,
			dtoList: params || []
		})

		ElMessage.success("操作成功")
		fetchTableData()
		roomSelectorVisible.value = false
	}
}

/**
 * 物资编码手册选择器 查询条件
 */
const matSelectQueryConf = computed(() => {
	return [
		{
			name: "物资编码",
			key: "materialCode",
			type: "input",
			placeholder: "请输入物资编码"
		},
		{
			name: "物资名称",
			key: "materialLabel",
			type: "input",
			placeholder: "请输入物资名称"
		},
		{
			name: "规格型号",
			key: "version",
			placeholder: "请输入规格型号",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "物资性质",
			key: "attribute",
			type: "select",
			children: dictOptions.value.MATERIAL_NATURE,
			placeholder: "请选择物资性质"
		}
	]
})

/**
 * 物资编码手册选择器 table 列配置
 */
const matSelectColumns: TableColumnType[] = [
	{ prop: "materialCode", label: "物资编码" },
	{ prop: "materialLabel", label: "物资名称" },
	{ prop: "version", label: "规格型号" },
	{ prop: "attribute", label: "物资性质", needSlot: true, width: 120 },
	{ prop: "useUnit", label: "库存单位", needSlot: true },
	{ prop: "sourceRoomCode", label: "来源货位编码" },
	{ prop: "batchNo", label: "批次" },
	{ prop: "roomNum_view", align: "right", label: "库存数量" },
	{ prop: "frozenNum_view", align: "right", label: "冻结量", needSlot: true }
]
function getDetail() {
	drawerLoading.value = true
	getTransferApplyById({ id: props.id || formData.value.id })
		.then((res: any) => {
			formData.value = {
				...res,
				crossCostCenter: res.crossCostCenter ? 1 : 0
			}

			oldFormData.value = JSON.stringify(formData.value)
		})
		.finally(() => {
			drawerLoading.value = false
		})
}
onMounted(async () => {
	await getDictByCodeList([
		"TRANSFER_TYPE",
		"INVENTORY_UNIT",
		"STORE_LEVEL",
		"STORE_TYPE",
		"MATERIAL_NATURE"
	])
	initUserInfo()

	fetchParam.value.sord = "desc"
	fetchParam.value.sidx = "createdDate"

	if (props.mode === IModalType.edit) {
		getDetail()
		fetchParam.value.applyId = props.id
		fetchTableData()
	}
})

onUnmounted(() => {
	clearTimer()
	cancelRollPolling()
})
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<div class="drawer-column left" style="width: 310px">
			<Title :title="titleConf" />

			<el-scrollbar>
				<grid-panel :options="gridPanelOptions" />
				<el-form
					class="content"
					:model="formData"
					ref="formRef"
					label-position="top"
					label-width="100px"
					:rules="formRules"
				>
					<form-element :form-element="formElBase" :form-data="formData" />
					<el-form-item>
						<el-button-group class="btn-group">
							<el-button type="warning" style="width: 50%; cursor: default">
								出库
							</el-button>
							<el-button
								type="success"
								style="width: 50%; margin-left: 0 !important; cursor: default"
							>
								入库
							</el-button>
						</el-button-group>
					</el-form-item>
					<form-element :form-element="formElInfo" :form-data="formData" />
				</el-form>
			</el-scrollbar>
			<button-list
				class="footer"
				:button="drawerBtnConf"
				:loading="drawerBtnLoading"
				@on-btn-click="handleDrawerBtnClick"
			/>
		</div>
		<div
			class="drawer-column right"
			:class="!canEditExtra ? 'disabled' : ''"
			style="width: calc(100% - 310px)"
		>
			<Title :title="extraTitleConf">
				<Tabs
					style="margin-right: auto; margin-left: 30px"
					:tabs="tabsConf"
					@on-tab-change="handleTabChange"
				/>
			</Title>

			<el-scrollbar
				class="rows transfer-apply-editor-table-wrapper"
				v-if="activatedTab === 0"
			>
				<Query
					:queryArrList="queryArrList"
					@getQueryData="getTableData"
					class="custom-q"
				/>
				<pitaya-table
					ref="tableRef"
					:columns="tableProp"
					:table-data="tableData"
					:need-selection="true"
					:single-select="false"
					:need-index="true"
					:need-pagination="true"
					:total="pageTotal"
					:table-loading="tableLoading"
					:cell-class-name="tbCellClassName"
					@on-selection-change="selectedTableList = $event"
					@on-current-page-change="onCurrentPageChange"
				>
					<!-- 冻结量下钻 -->
					<template #frozenNum_view="{ rowData }">
						<link-tag
							:value="rowData.frozenNum_view"
							@on-click="showFreezeNumDetail(rowData)"
						/>
					</template>

					<!-- 物资性质 -->
					<template #attribute="{ rowData }">
						<dict-tag
							:options="DictApi.getMatAttr()"
							:value="rowData.attribute"
						/>
					</template>

					<template #useUnit="{ rowData }">
						{{
							dictFilter("INVENTORY_UNIT", rowData.useUnit)?.subitemName ||
							"---"
						}}
					</template>

					<template #num="{ rowData }">
						<el-input
							class="no-arrows"
							v-model="rowData.num"
							@click.stop
							@input="rowData.num = validateAndCorrectInput($event)"
							@change="validateNum(rowData)"
						/>
					</template>

					<template #footerOperateLeft>
						<button-list
							:button="tbBtnConf"
							:loading="drawerBtnLoading"
							:is-not-radius="true"
							@on-btn-click="handleAddGoodAndRoom"
						/>
					</template>
				</pitaya-table>
			</el-scrollbar>
			<el-scrollbar class="rows" v-else>
				<!-- 相关附件 table -->
				<table-file
					:business-type="fileBusinessType.transferApply"
					:business-id="formDataId"
					:mod="canEditExtra ? IModalType.edit : IModalType.view"
				/>
			</el-scrollbar>
			<button-list
				class="footer"
				:button="drawerBtnRightConf"
				:loading="drawerBtnLoading"
				@on-btn-click="handleDrawerBtnClick"
			/>
		</div>

		<!-- 物资编码手册选择器 -->
		<Drawer
			v-model:drawer="goodsSelectorVisible"
			:size="modalSize.lg"
			destroy-on-close
		>
			<mat-selector
				:table-req-params="{
					applyId: formData.id,
					storeId: formData.storeId,
					sord: 'desc',
					sidx: 'batchNo'
				}"
				:business-type="formData.type"
				:table-api="getMatTransferApplyList"
				:columns="matSelectColumns"
				:multiple="true"
				:queryArrList="matSelectQueryConf"
				:error-id-list="selectMatIds"
				@save="handleAddGoods"
				@close="goodsSelectorVisible = false"
			/>
		</Drawer>

		<!-- 仓库选择 -->
		<Drawer
			v-model:drawer="warehouseSelectorVisible"
			:size="modalSize.lg"
			destroy-on-close
		>
			<store-table
				@on-save="handleStoreSelect"
				:selected-ids="[formData[`store${selStoreType}Id`]]"
				:table-api-params="{
					type:
						!isNil(formData.type) &&
						formData.type == IInventoryBusinessType.transferApplyKW
							? formData[`store${selStoreType ? '' : 'In'}Type`]
							: ''
				}"
			/>
		</Drawer>

		<!-- 选择货位 -->
		<Drawer
			v-model:drawer="roomSelectorVisible"
			:size="modalSize.lg"
			destroy-on-close
		>
			<area-storage
				:store-id="formData.storeInId!"
				:store-name="formData.storeInName!"
				:store-code="formData.storeInCode"
				:table-api="listMatStoreRoomPaged"
				:table-req-params="{ roomIds: selectRoomIds }"
				@onSaveOrClose="handleAddRoom"
			/>
		</Drawer>

		<!-- 冻结量 查看下钻 -->
		<Drawer
			v-model:drawer="freezeNumTableVisible"
			:size="modalSize.lg"
			destroy-on-close
		>
			<freeze-table
				:material-id="editorRow.materialId"
				:sourceRoomId="editorRow.sourceRoomId"
				:batchNo="editorRow.batchNo"
				@close="freezeNumTableVisible = false"
			/>
		</Drawer>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.custom-q {
	margin: 10px 0px -10px 10px !important;
}

.content {
	.btn-group {
		width: 100%;
		display: flex;
		.el-button--warning {
			background-color: #e6a23c !important;
		}
		.el-button--success {
			background-color: #67c23a !important;
		}
		.el-button {
			width: 50% !important;
		}
	}
}
.transfer-apply-editor-table-wrapper {
	&:deep(.pitaya-table) {
		.error {
			.column-inner-item,
			.cell,
			.el-input__inner {
				color: red;
			}
		}
	}
}
</style>
