import { OutStoreStatus } from "../../outbound/outbound"

/**
 * 调拨管理 - 列表 调拨状态
 * 1-待出库、2-待入库、3-已关闭、4-已完成
 */
export const getTransferApplyStatus = () => {
	return [
		{
			label: "待入库",
			value: OutStoreStatus.noOut,
			raw: { class: "warning" }
		},
		{
			label: "已完成",
			value: OutStoreStatus.out,
			raw: { class: "success" }
		},
		{
			label: "已关闭",
			value: OutStoreStatus.close,
			raw: { class: "danger" }
		}
	]
}

/**
 * 判断是否是库内移库 "CK-DB-KN"
 */
export const isCKDBKN = (type?: string) => {
	if (type == "CK-DB-KN") {
		return true
	}
	return false
}

/**
 * 调拨管理 - 移库类型与是否跨成本中心 匹配
 */
export enum IsCrossCostCenter {
	"CK-DB-KW" = 1,
	"CK-DB-KN" = 0
}

export enum IsCrossCostCenterStatus {
	"yes" = 1,
	"no" = 0
}

/**
 * 调拨管理 - 是否跨成本中心
 */

export const getIsCrossCostCenter = () => {
	return [
		{ label: "是", value: IsCrossCostCenterStatus.yes },
		{ label: "否", value: IsCrossCostCenterStatus.no }
	]
}
