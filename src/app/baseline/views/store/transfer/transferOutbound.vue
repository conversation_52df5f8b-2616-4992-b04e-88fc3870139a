<script lang="ts" setup>
import { computed, onMounted, ref } from "vue"
import {
	checkPermission,
	isCheckPermission
} from "@/app/platform/utils/permission"
import { powerList } from "../../components/define.d"
import LinkTag from "@/app/baseline/views/components/linkTag.vue"
import { modalSize } from "@/app/baseline/utils/layout-config"
import viewDrawer from "./transferOutbound/viewDrawer.vue"
import { IModalType } from "../../../utils/types/common"
import {
	MatStoreAllocationOutRequestParams,
	MatStoreAllocationOutVO
} from "@/app/baseline/utils/types/store-transfer-outbound"
import {
	getAllocationOutPage,
	getOutAllocationBmpStatusCnt
} from "@/app/baseline/api/store/transfer/outbound"
import { useTbInit } from "../../components/tableBase"
import { listMatStoragePaged } from "@/app/baseline/api/store/manage-api"
import transferApplyDetail from "./transferApplication/viewDrawer.vue"
import { OutStoreStatus } from "../outbound/outbound"
import { tableColFilter } from "@/app/baseline/utils"
import { omit } from "lodash-es"

/**
 * 查询条件 配置
 */
const queryArrList = computed(() => {
	return [
		{
			name: "出库单号",
			key: "code",
			placeholder: "请输入出库单号",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "关联调拨单号",
			key: "preBusinessCode",
			placeholder: "请输入关联调拨单号",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "申请人",
			key: "userName",
			placeholder: "请输入申请人",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "出库仓库",
			key: "storeId",
			placeholder: "请选择仓库",
			type: "tableSelect",
			tableInfo: [
				{
					title: "请选择出库仓库",
					tableApi: listMatStoragePaged, //表格接口
					tableColumns: [
						{ label: "仓库编码", prop: "code", width: 120 },
						{ label: "仓库名称", prop: "label" },
						{
							label: "仓库级别",
							prop: "level_view",
							width: 100
						},
						{
							label: "仓库类型",
							prop: "type_view",
							width: 120
						},
						{
							label: "所属段区",
							prop: "depotId_view",
							width: 150
						}
					],
					queryArrList: [
						{
							name: "仓库编码",
							key: "code",
							type: "input",
							placeholder: "请输入仓库编码"
						},
						{
							name: "仓库名称",
							key: "label",
							type: "input",
							placeholder: "请输入仓库名称"
						}
					],
					params: {
						currentPage: 1,
						pageSize: 20
					}, //表格接口参数

					labelName: {
						key: "id", //回显标识
						label: "label", //input框要展示的字段
						value: "id" //要给后台传的字段
					}
				}
			]
			//treeApi: fmtListMatStoreApi
		},
		{
			name: "入库仓库",
			key: "storeInId",
			placeholder: "请选择入库仓库",
			type: "tableSelect",
			//treeApi: fmtListMatStoreApi
			tableInfo: [
				{
					title: "请选择入库仓库",
					tableApi: listMatStoragePaged, //表格接口
					tableColumns: [
						{ label: "仓库编码", prop: "code", width: 120 },
						{ label: "仓库名称", prop: "label" },
						{
							label: "仓库级别",
							prop: "level_view",
							width: 100
						},
						{
							label: "仓库类型",
							prop: "type_view",
							width: 120
						},
						{
							label: "所属段区",
							prop: "depotId_view",
							width: 150
						}
					],
					queryArrList: [
						{
							name: "仓库编码",
							key: "code",
							type: "input",
							placeholder: "请输入仓库编码"
						},
						{
							name: "仓库名称",
							key: "label",
							type: "input",
							placeholder: "请输入仓库名称"
						}
					],
					params: {
						currentPage: 1,
						pageSize: 20
					}, //表格接口参数

					labelName: {
						key: "id", //回显标识
						label: "label", //input框要展示的字段
						value: "id" //要给后台传的字段
					}
				}
			]
		}
	]
})

/**
 * Title 配置
 */
const rightTitle = {
	name: ["调拨出库"],
	icon: ["fas", "square-share-nodes"]
}

/**
 * tab 配置项
 */
const statusCnt = ref<number[]>([])
const tabList = [
	{ name: "待出库", value: OutStoreStatus.noOut },
	{ name: "已出库", value: OutStoreStatus.out },
	{ name: "已关闭", value: OutStoreStatus.close }
]
const tabStatus = ref(tabList[0].value)
const activeName = ref(tabList[0].name)
const handleTabsClick = (tab: any) => {
	activeName.value = tab.paneName
	tabStatus.value = tabList[tab.index].value
	getTableData()
}
const tbInit = useTbInit<
	MatStoreAllocationOutVO,
	MatStoreAllocationOutRequestParams
>()
const {
	tableData,
	tableRef,
	tableLoading,
	currentPage,
	pageTotal,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange,
	selectedTableList
} = tbInit

/**
 * table 列配置
 */
tbInit.tableProp = computed(() => {
	const tableCols = [
		{ label: "出库单号", prop: "code", width: 180 },
		{ label: "移库类型", prop: "preBusinessType_view" },
		{
			label: "关联调拨单号",
			prop: "preBusinessCode",
			needSlot: true,
			width: 180
		},
		{ label: "出库仓库名称", prop: "storeName" },
		{ label: "物资编码", prop: "materialCodeNum" },
		{ label: "入库仓库名称", prop: "storeInName" },
		{ label: "关闭原因", prop: "reason", minWidth: 100 },
		{ label: "关闭时间", prop: "closeDate", sortable: true },
		{ label: "申请人", prop: "userName_view" },
		{ label: "申请时间", prop: "applyDate", sortable: true },
		{ label: "操作", width: 100, prop: "operations", needSlot: true }
	]

	switch (tabStatus.value) {
		case OutStoreStatus.close:
			return tableCols
		default:
			// 已关闭
			return tableColFilter(tableCols, ["关闭原因", "关闭时间"])
	}
	//return tableCols
})

/**
 * table 数据源
 */
fetchFunc.value = getAllocationOutPage
const getTableData = (data?: any) => {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		tableType: tabStatus.value,
		sord: "desc",
		sidx: "createdDate",
		...data
	}
	handleUpdate()
}

const curRowId = ref<any>("")
const curRowData = ref<MatStoreAllocationOutVO>({})
const viewDrawerVisible = ref<boolean>(false)
const model = ref<string>("view") // view | edit

/**
 * 查看 操作
 * @param row
 */
const onRowView = (row: MatStoreAllocationOutVO) => {
	curRowId.value = row.id
	curRowData.value = row
	viewDrawerVisible.value = true
	model.value = IModalType.view
}

/**
 * 关联调拨单号 操作
 */
const relativeDrawerVisible = ref(false)
const onRowBusinessOrder = (row: MatStoreAllocationOutVO) => {
	curRowId.value = row.id
	curRowData.value = row
	relativeDrawerVisible.value = true
}

const handleUpdate = async () => {
	statusCnt.value = await getOutAllocationBmpStatusCnt(
		omit(
			{
				...fetchParam.value
			},
			"tableType",
			"currentPage",
			"pageSize"
		) as any
	)
	fetchTableData()
}

/**
 * 排序
 * @param column
 * @param prop 字段名称
 * @param order ascending 升序 descending 降序
 */
function handleSortChange(column: any, prop: any, order: any) {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		sord:
			order === "descending" ? "desc" : order === "ascending" ? "asc" : "desc",
		sidx: order ? (prop === "applyDate" ? "createdDate" : prop) : "createdDate" // 排序字段
	}

	fetchTableData()
}

onMounted(() => {
	getTableData()
})
</script>
<template>
	<div class="app-container">
		<div class="whole-frame">
			<ModelFrame class="top-frame">
				<Query
					:queryArrList="queryArrList"
					@getQueryData="getTableData"
					class="ml10"
				/>
			</ModelFrame>

			<ModelFrame class="bottom-frame">
				<Title :title="rightTitle">
					<div class="app-tabs-wrapper">
						<el-tabs v-model="activeName" @tab-click="handleTabsClick">
							<el-tab-pane
								v-for="(tab, index) in tabList"
								:key="index"
								:label="`${tab.name}（${statusCnt[index] ?? 0}）`"
								:name="tab.name"
								:index="tab.name"
							/>
						</el-tabs>
					</div>
				</Title>

				<PitayaTable
					ref="tableRef"
					:columns="(tbInit.tableProp as any)"
					:tableData="tableData"
					:total="pageTotal"
					:need-index="true"
					:single-select="true"
					:need-selection="false"
					@onSelectionChange="selectedTableList = $event"
					@on-current-page-change="onCurrentPageChange"
					:table-loading="tableLoading"
					@on-table-sort-change="handleSortChange"
				>
					<!-- 关联调拨单号 -->
					<template #preBusinessCode="{ rowData }">
						<link-tag
							:value="rowData.preBusinessCode"
							@on-click="onRowBusinessOrder(rowData)"
						/>
					</template>

					<template #operations="{ rowData }">
						<slot
							v-if="
								isCheckPermission(powerList.storeTransferOutboundBtnPreview)
							"
						>
							<el-button
								v-btn
								link
								@click="onRowView(rowData)"
								:disabled="
									checkPermission(powerList.storeTransferOutboundBtnPreview)
								"
								v-if="
									isCheckPermission(powerList.storeTransferOutboundBtnPreview)
								"
							>
								<font-awesome-icon :icon="['fas', 'eye']" />
								<span class="table-inner-btn">查看</span>
							</el-button>
						</slot>
						<slot v-else>---</slot>
					</template>

					<!-- <template #footerOperateLeft v-if="tabStatus === tabList[0].value">
						<ButtonList
							class="btn-list"
							:is-not-radius="true"
							:button="tableFooterBtns"
							v-loading="tbBtnLoading"
							@on-btn-click="handleTableAction"
						/>
					</template> -->
				</PitayaTable>

				<!-- 编辑/查看弹窗 -->
				<Drawer
					:size="modalSize.xl"
					v-model:drawer="viewDrawerVisible"
					:destroyOnClose="true"
				>
					<view-drawer
						:id="curRowId"
						:model="model"
						@close="viewDrawerVisible = false"
						@update="handleUpdate"
					/>
				</Drawer>

				<!-- 关联业务单号 -->
				<Drawer
					:size="modalSize.xl"
					v-model:drawer="relativeDrawerVisible"
					:destroyOnClose="true"
				>
					<transfer-apply-detail
						mode="view"
						:id="curRowData.preBusinessId!"
						:row="curRowData"
						@close="relativeDrawerVisible = false"
					/>
				</Drawer>
			</ModelFrame>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
