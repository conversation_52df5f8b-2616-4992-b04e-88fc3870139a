<!-- 入库申请-详情 -->
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<div class="drawer-column left" style="width: 300px">
			<Title :title="titleConf" />

			<el-descriptions size="small" :column="1" border class="content">
				<el-descriptions-item
					v-for="desc in descList"
					:key="desc.label"
					:label="desc.label"
				>
					<!-- 入库金额 -->
					<cost-tag v-if="desc.key === 'amount'" :value="applyData?.amount" />
					<span v-else>
						{{ applyData?.[desc.key] || "---" }}
					</span>
				</el-descriptions-item>
			</el-descriptions>
		</div>
		<div class="drawer-column right" style="width: calc(100% - 300px)">
			<div class="rows">
				<Title :title="exTitleConf">
					<Tabs
						style="margin-right: auto"
						:tabs="['物资明细', '相关附件']"
						@on-tab-change="activatedTab = $event"
					/>
				</Title>

				<!-- 物资详情 table -->
				<goods-detail-table
					v-if="activatedTab === 0"
					:user-options-api="listInspectorPaged"
					:list-goods-api="listMatInStoreApplyGoodsPaged"
					:add-inspector-api="addWarehousingInspector"
					:apply-id="id"
					:mode="mode"
					:tb-cell-class-name="tbCellClassName"
					:hidden-query-key="['existFlag']"
					ref="matTableRef"
				/>
				<!-- 相关附件 table -->
				<table-file
					v-else
					:business-type="fileBusinessType.warehousingApply"
					:business-id="id"
					:mod="
						props.parentActivatedTab == 0 ? IModalType.edit : IModalType.view
					"
				/>
			</div>

			<button-list
				v-if="footerBtnVisible"
				class="footer"
				:button="drawerBtnConf"
				:loading="drawerBtnLoading"
				@on-btn-click="handleDrawerBtnClick"
			/>
		</div>
	</div>
</template>

<script setup lang="ts">
import { MatInStoreApplyVo } from "@/app/baseline/utils/types/warehousing-apply"
import tableFile from "../../../components/tableFile.vue"
import {
	addWarehousingInspector,
	getMatInStoreApply,
	listMatInStoreApplyGoodsPaged,
	pushWarehousingInspect
} from "@/app/baseline/api/store/warehousing-apply-api"
import { fileBusinessType } from "@/app/baseline/api/dict"
import {
	IIdempotentTokenType,
	IIdempotentTokenTypePre,
	IModalType
} from "@/app/baseline/utils/types/common"
import goodsDetailTable from "../../components/goodsDetailTable.vue"
import CostTag from "../../../components/costTag.vue"
import { listInspectorPaged } from "@/app/baseline/api/system"
import { includes } from "lodash-es"
import { useMessageBoxInit } from "../../../components/messageBox"
import { getIdempotentToken } from "@/app/baseline/utils/validate"

const props = withDefaults(
	defineProps<{
		/**
		 * 申请id
		 */
		id: any

		/**
		 * 模式，编辑/查看
		 */
		mode?: IModalType

		/**
		 * 列表页标签位置 待处理0/已完成1
		 */
		parentActivatedTab?: number

		footerBtnVisible?: boolean
	}>(),
	{ mode: IModalType.view, footerBtnVisible: true }
)

const emit = defineEmits<{
	(e: "close"): void
	(e: "save"): void
}>()

const { showWarnConfirm } = useMessageBoxInit()

const matTableRef = ref()

const drawerBtnLoading = ref(false)

const titleConf = {
	name: ["入库申请"],
	icon: ["fas", "square-share-nodes"]
}

const exTitleConf = {
	name: ["扩展信息"],
	icon: ["fas", "square-share-nodes"]
}

const drawerBtnConf = computed(() => {
	if (props.mode == IModalType.edit) {
		return [
			{
				name: "取消",
				icon: ["fas", "circle-minus"]
			},
			{
				name: "推送质检",
				icon: ["fas", "stamp"]
			}
		]
	} else {
		return [
			{
				name: "取消",
				icon: ["fas", "circle-minus"]
			}
		]
	}
})

const activatedTab = ref(0)

const drawerLoading = ref()

const applyData = ref<MatInStoreApplyVo>()

const descList = computed(() => {
	const ls = [
		{
			label: "入库单号",
			key: "code"
		},
		{
			label: "关联采购订单号",
			key: "preBusinessCode"
		},
		{
			label: "采购项目编号",
			key: "projectCode"
		},
		{
			label: "采购项目名称",
			key: "projectLabel"
		},
		{
			label: "合同编号",
			key: "contractCode"
		},
		{
			label: "供应商名称",
			key: "supplierName"
		},
		{
			label: "入库仓库名称",
			key: "storeLabel"
		},
		{
			label: "入库金额",
			key: "amount"
		},
		{
			label: "申请人",
			key: "createdBy_view"
		},
		{
			label: "申请时间",
			key: "createdDate"
		},
		{
			label: "关闭原因",
			key: "reason"
		},
		{
			key: "closeDate",
			label: "关闭时间"
		}
	]

	if (props.parentActivatedTab === 0) {
		// 待处理状态的入库申请
		return ls.filter(
			(v) =>
				v.label !== "入库金额" &&
				v.label !== "关闭原因" &&
				v.label !== "关闭时间"
		)
	} else if (props.parentActivatedTab === 1) {
		// 待处理状态的入库申请
		return ls.filter((v) => v.label !== "关闭原因" && v.label !== "关闭时间")
	} else {
		return ls
	}
})

onMounted(() => {
	getDetail()
})

function getDetail() {
	getMatInStoreApply(props.id).then((r) => (applyData.value = r))
}

/**
 * 物资明细 table 行样式
 *
 * 库存物资异常，用红色标记该行
 */
const errorGoodsIdList = ref<number[]>([])
function tbCellClassName({ row }: any) {
	return includes(errorGoodsIdList.value, row.id) ? "error" : ""
}

async function handleDrawerBtnClick(name?: string) {
	if (name === "取消") {
		emit("close")
		return
	}

	await showWarnConfirm("请确认是否推送质检本次数据？")

	drawerBtnLoading.value = true
	try {
		const idempotentToken = getIdempotentToken(
			IIdempotentTokenTypePre.other,
			IIdempotentTokenType.warehouseApply,
			props.id
		)
		const { code, data, msg } = await pushWarehousingInspect(
			{ id: props.id },
			idempotentToken
		)

		if (data && code != 200) {
			errorGoodsIdList.value = data || []
			ElMessage.error(msg)
			matTableRef.value?.getTableData()
		} else {
			ElMessage.success("操作成功")
			emit("save")
		}
	} finally {
		drawerBtnLoading.value = false
	}

	/* handleApplyResultByCode(code, msg) */

	/* // 推送质检逻辑
	pushWarehousingInspect({ id: props.id })
		.then(() => {
			ElMessage.success("操作成功")
			emit("save")
		})
		.finally(() => (drawerBtnLoading.value = false)) */
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
