<template>
	<div class="app-container">
		<div class="whole-frame">
			<model-frame class="top-frame">
				<Query
					:query-arr-list="(queryArrList as any)"
					@get-query-data="handleQuery"
					class="ml10"
				/>
			</model-frame>
			<model-frame class="bottom-frame">
				<Title :title="titleConf">
					<div class="app-tabs-wrapper">
						<el-tabs v-model="activeName" @tab-click="handleTabChanged">
							<el-tab-pane
								v-for="(tab, index) in tabsConf"
								:key="index"
								:label="`${tab}（${statusCnt[index] ?? 0}）`"
								:name="tab"
								:index="tab"
							/>
						</el-tabs>
					</div>

					<!-- <Tabs
						:tabs="tabsConf"
						style="margin-right: auto; margin-left: 30px"
						@on-tab-change="handleTabChanged"
					/> -->
				</Title>
				<pitaya-table
					ref="tableRef"
					:columns="(tbInit.tableProp as any)"
					:table-data="tableData"
					:need-selection="activatedTab === 0"
					:single-select="false"
					:need-index="true"
					:need-pagination="true"
					:total="pageTotal"
					:table-loading="tableLoading"
					@on-selection-change="handleTbSelect"
					@on-current-page-change="onCurrentPageChange"
					@on-table-sort-change="handleSortChange"
				>
					<template #amount="{ rowData }">
						<cost-tag :value="rowData.amount" />
					</template>
					<template #preBusinessCode="{ rowData }">
						<link-tag
							:value="rowData.preBusinessCode"
							@click.stop="showPurchaseOrderDetail(rowData)"
						/>
					</template>
					<template #actions="{ rowData }">
						<slot
							v-if="
								isCheckPermission(
									powerList.storeBusinessWarehousingApplyBtnPreview
								)
							"
						>
							<el-button
								v-btn
								link
								@click.stop="onRowView(rowData)"
								:disabled="
									checkPermission(
										powerList.storeBusinessWarehousingApplyBtnPreview
									)
								"
								v-if="
									isCheckPermission(
										powerList.storeBusinessWarehousingApplyBtnPreview
									)
								"
							>
								<font-awesome-icon
									:icon="['fas', 'eye']"
									style="color: var(--pitaya-btn-background)"
								/>
								<span class="table-inner-btn">查看</span>
							</el-button>
						</slot>
						<slot v-else>---</slot>
					</template>

					<template v-if="activatedTab === 0" #footerOperateLeft>
						<button-list
							:is-not-radius="true"
							class="footer"
							:button="btnConf"
							@on-btn-click="handleBtnClick"
						/>
					</template>
				</pitaya-table>
			</model-frame>
		</div>

		<!-- 申请详情 -->
		<Drawer v-model:drawer="showDrawer" :size="modalSize.xl" destroy-on-close>
			<apply-detail
				:id="selTableRowId"
				:mode="detailMode"
				:parent-activated-tab="activatedTab"
				@close="showDrawer = false"
				@save="handleAfterPushInspect"
			/>
		</Drawer>

		<!-- 采购订单详情 -->
		<Drawer
			v-model:drawer="purchaseOrderDrawerVisible"
			:size="modalSize.xl"
			destroy-on-close
		>
			<purchase-order-detail
				:purchase-order-id="browsingPurchaseOrderId"
				@close="purchaseOrderDrawerVisible = false"
			/>
		</Drawer>

		<Drawer
			v-model:drawer="allPushQualityVisible"
			:size="modalSize.md"
			destroy-on-close
		>
			<user-selector
				:table-api="listInspectorPaged"
				:table-props="userSelectorTableProps"
				@close="allPushQualityVisible = false"
				@save="handleUserSelected"
			/>
		</Drawer>

		<!-- 关闭 -->
		<Drawer
			:size="350"
			v-model:drawer="closeEditorVisible"
			:destroyOnClose="true"
		>
			<close-editor
				:id="first(selectedTableList)?.id"
				@close="closeEditorVisible = false"
				@update="handleUpdate"
			/>
		</Drawer>
	</div>
</template>

<script setup lang="ts">
import {
	checkPermission,
	isCheckPermission
} from "@/app/platform/utils/permission"
import { powerList } from "../../../components/define.d"
import { listMatStoragePaged } from "@/app/baseline/api/store/manage-api"
import { useTbInit } from "../../../components/tableBase"
import applyDetail from "./applyDetail.vue"
import { modalSize } from "@/app/baseline/utils/layout-config"
import { first, map, omit } from "lodash-es"
import {
	batchPushWarehousingInspect,
	getMatInStoreApplyBmpStatusCnt,
	listMatInStoreApplyPaged
} from "@/app/baseline/api/store/warehousing-apply-api"
import {
	IWarehousingStatus,
	MatInStoreApplyRequestParams,
	MatInStoreApplyVo
} from "@/app/baseline/utils/types/warehousing-apply"
import {
	IIdempotentTokenType,
	IIdempotentTokenTypeAction,
	IIdempotentTokenTypePre,
	IModalType
} from "@/app/baseline/utils/types/common"
import { BaseLineSysApi } from "@/app/baseline/api/system"
import CostTag from "../../../components/costTag.vue"
import LinkTag from "../../../components/linkTag.vue"
import PurchaseOrderDetail from "../../components/purchaseOrderDetail.vue"
import userSelector from "../../components/userSelector.vue"
import { listInspectorPaged } from "@/app/baseline/api/system"
import { SystemUserVo } from "@/app/baseline/utils/types/system"
import { useMessageBoxInit } from "../../../components/messageBox"
import { PurchaseSupplierApi } from "@/app/baseline/api/purchase/purchaseSupplier"
import closeEditor from "./closeEditor.vue"
import { getIdempotentToken } from "@/app/baseline/utils/validate"

const { showWarnConfirm } = useMessageBoxInit()

/**
 * 当前选中的 table row id
 */
const selTableRowId = ref()

/**
 * 当前查看的采购订单id
 */
const browsingPurchaseOrderId = ref()

const showDrawer = ref()

const allPushQualityVisible = ref(false)

const purchaseOrderDrawerVisible = ref(false)

/**
 * 关闭
 */
const closeEditorVisible = ref(false)

/**
 * 详情编辑模式
 */
const detailMode = ref(IModalType.view)

const btnConf = computed(() => {
	return [
		{
			name: "分配质检",
			roles: powerList.storeBusinessWarehousingApplyBtnQuality,
			icon: ["fas", "stamp"],
			disabled: selectedTableList.value.length != 1
		},
		{
			name: "批量推送质检",
			roles: powerList.storeBusinessWarehousingApplyBtnBatchQuality,
			icon: ["fas", "stamp"],
			disabled: selectedTableList.value.length < 1
		},
		{
			name: "关闭",
			roles: powerList.storeBusinessWarehousingApplyBtnClose,
			icon: ["fas", "times-circle"],
			disabled: selectedTableList.value.length !== 1
		}
	]
})

const activatedTab = ref(0)

/**
 * 公司列表
 */
const companyList = ref([])

const queryArrList = computed<querySetting[]>(() => [
	{
		name: "入库申请单号",
		key: "code",
		type: "input",
		placeholder: "请输入入库申请单号"
	},
	{
		name: "采购订单号",
		key: "preBusinessCode",
		type: "input",
		placeholder: "请输入采购订单号"
	},
	{
		name: "申请人",
		key: "createdBy",
		type: "input",
		placeholder: "请输入申请人"
	},
	/* {
		name: "公司",
		key: "sysCommunityId",
		type: "select",
		children: companyList.value
	}, */
	{
		name: "入库仓库",
		key: "storeId",
		placeholder: "请选择入库仓库",
		type: "tableSelect",
		tableInfo: [
			{
				title: "请选择入库仓库",
				tableApi: listMatStoragePaged, //表格接口
				tableColumns: [
					{ label: "仓库编码", prop: "code", width: 120 },
					{ label: "仓库名称", prop: "label" },
					{
						label: "仓库级别",
						prop: "level_view",
						width: 100
					},
					{
						label: "仓库类型",
						prop: "type_view",
						width: 120
					},
					{
						label: "所属段区",
						prop: "depotId_view",
						width: 150
					}
				],
				queryArrList: [
					{
						name: "仓库编码",
						key: "code",
						type: "input",
						placeholder: "请输入仓库编码"
					},
					{
						name: "仓库名称",
						key: "label",
						type: "input",
						placeholder: "请输入仓库名称"
					}
				],
				params: {
					currentPage: 1,
					pageSize: 20
				}, //表格接口参数

				labelName: {
					key: "id", //回显标识
					label: "label", //input框要展示的字段
					value: "id" //要给后台传的字段
				}
			}
		]
	},
	{
		name: "供应商",
		key: "supplierId",
		type: "tableSelect",
		placeholder: "请选择供应商",
		tableInfo: [
			{
				title: "请选择供应商",
				tableApi: PurchaseSupplierApi.getPurchaseSupplierList, //表格接口
				tableColumns: [
					{ label: "供应商编码", prop: "code", width: 160 },
					{ label: "供应商名称", prop: "label" }
				],
				queryArrList: [
					{
						name: "供应商编码",
						key: "code",
						type: "input",
						placeholder: "请输入供应商编码"
					},
					{
						name: "供应商名称",
						key: "label",
						type: "input",
						placeholder: "请输入供应商名称"
					}
				],
				params: {
					currentPage: 1,
					pageSize: 20
				}, //表格接口参数

				labelName: {
					key: "id", //回显标识
					label: "label", //input框要展示的字段
					value: "id" //要给后台传的字段
				}
			}
		]
	}
])

const tabsConf = ["待处理", "已完成", "已关闭"]
const activeName = ref<string>(tabsConf[0])
const statusCnt = ref<number[]>([])

const titleConf = {
	name: ["入库申请"],
	icon: ["fas", "square-share-nodes"]
}

const tbInit = useTbInit<MatInStoreApplyVo, MatInStoreApplyRequestParams>()

const {
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	currentPage,
	selectedTableList,
	fetchTableData,
	fetchFunc,
	fetchParam,
	onCurrentPageChange
} = tbInit

fetchFunc.value = listMatInStoreApplyPaged

tbInit.tableProp = computed(() => {
	const ls: TableColumnType[] = [
		{
			prop: "code",
			label: "入库申请单号",
			fixed: "left",
			sortable: true
		},
		{
			prop: "preBusinessCode",
			label: "关联采购订单号",
			needSlot: true,
			width: 180
		},
		{
			prop: "storeLabel",
			label: "入库仓库名称"
		},
		{
			prop: "amount",
			label: "入库金额",
			needSlot: true,
			align: "right",
			sortable: true
		},
		{
			prop: "supplierName",
			label: "供应商名称"
		},
		{
			prop: "createdBy_view",
			label: "申请人"
		},
		{
			prop: "createdDate",
			label: "申请时间",
			sortable: true
		},
		{
			prop: "reason",
			label: "关闭原因"
		},
		{
			prop: "closeDate",
			label: "关闭时间"
		},
		{
			prop: "actions",
			label: "操作",
			needSlot: true,
			width: 100,
			fixed: "right"
		}
	]

	if (activatedTab.value === 0) {
		// 待处理情况
		return ls.filter(
			(v) =>
				v.label !== "入库金额" &&
				v.label !== "关闭原因" &&
				v.label !== "关闭时间"
		)
	} else if (activatedTab.value === 1) {
		// 待处理情况
		return ls.filter((v) => v.label !== "关闭原因" && v.label !== "关闭时间")
	} else {
		return ls
	}
})

onMounted(() => {
	fetchParam.value.sord = "desc"
	fetchParam.value.sidx = "createdDate"
	fetchParam.value.status = IWarehousingStatus.pending

	handleUpdate()
	BaseLineSysApi.getCompanyAllList().then((r) => (companyList.value = r))
})

function handleQuery(e: any) {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		...e
	}

	handleUpdate()
}

function onRowView(e: any) {
	selTableRowId.value = e.id
	showDrawer.value = true
	detailMode.value = IModalType.view
}

function handleTbSelect(e?: any[]) {
	selectedTableList.value = e ?? []
	selTableRowId.value = e?.[0]?.id
}

/**
 * after 推送质检 handler
 */
function handleAfterPushInspect() {
	handleUpdate()
	showDrawer.value = false
}

function handleBtnClick(name?: any) {
	if (name === "分配质检") {
		showDrawer.value = true
		detailMode.value = IModalType.edit
	} else if (name === "批量推送质检") {
		allPushQualityVisible.value = true
	} else if (name === "关闭") {
		closeEditorVisible.value = true
	}
}

const userSelectorTableProps = [
	{
		prop: "realname",
		label: "用户姓名"
	},
	{
		prop: "username",
		label: "用户账号"
	},
	{
		prop: "sex",
		label: "性别",
		needSlot: true
	},
	{
		prop: "phone",
		label: "手机号"
	},
	{
		prop: "team",
		label: "质检组"
	},
	{
		prop: "sysOrgId_view",
		label: "部门"
	}
]

/**
 * 选择用户 handler
 */
async function handleUserSelected(e?: SystemUserVo[], callback?: any) {
	const username = first(e)?.username
	if (!username) {
		callback()
		return ElMessage.warning("请至少选择一个人员！")
	}

	await showWarnConfirm("确定将所选入库单据推送给质检员吗？")

	// 入库申请id集合
	const inStoreApplyIdList = map(selectedTableList.value, ({ id }) => id) ?? []

	const idempotentToken = getIdempotentToken(
		IIdempotentTokenTypePre.batch,
		IIdempotentTokenType.warehouseApply,
		"",
		IIdempotentTokenTypeAction.pushInspect
	)
	// 添加质检员
	await batchPushWarehousingInspect(
		{ inStoreApplyIdList, userId: username },
		idempotentToken
	)
	handleUpdate()
	allPushQualityVisible.value = false
}

/**
 * tab change handler
 *
 * @param tabIdx 当前标签位置
 */
function handleTabChanged(tab: any) {
	activeName.value = tab.paneName
	activatedTab.value = Number(tab.index)
	const status =
		activatedTab.value == 0
			? IWarehousingStatus.pending
			: activatedTab.value == 1
			? IWarehousingStatus.completed
			: IWarehousingStatus.close

	fetchParam.value = {
		...fetchParam.value,
		status
	}
	handleUpdate()
}

/**
 * 查看采购订单详情
 */
function showPurchaseOrderDetail({ preBusinessId }: MatInStoreApplyVo) {
	if (!preBusinessId) {
		return false
	}
	browsingPurchaseOrderId.value = preBusinessId
	purchaseOrderDrawerVisible.value = true
}

/**
 * 更新主表/ tab 状态统计
 */
async function handleUpdate() {
	statusCnt.value = await getMatInStoreApplyBmpStatusCnt(
		omit(
			{
				...fetchParam.value
			},
			"status",
			"currentPage",
			"pageSize"
		) as any
	)
	fetchTableData()
}

/**
 * 排序
 * @param column
 * @param prop 字段名称
 * @param order ascending 升序 descending 降序
 */
function handleSortChange(column: any, prop: any, order: any) {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		sord:
			order === "descending" ? "desc" : order === "ascending" ? "asc" : "desc",
		sidx: order ? prop : "createdDate" // 排序字段
	}

	fetchTableData()
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
