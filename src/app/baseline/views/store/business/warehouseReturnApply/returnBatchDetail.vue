<script setup lang="ts">
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import { DictApi } from "@/app/baseline/api/dict"

import { reactive, ref } from "vue"
import { useTbInit } from "../../../components/tableBase"
import CostTag from "@/app/baseline/views/components/costTag.vue"
import {
	MatReturnStoreApplyBatchInfoVo,
	MatReturnStoreApplyBatchpageReqParams,
	MatReturnStoreApplyBatchpageVo
} from "@/app/baseline/utils/types/warehouse-return-apply"
import {
	listOutStoreReturnPageBatchInfo,
	listOutStoreReturnPageBatchInfoPage
} from "@/app/baseline/api/store/warehouse-return-apply-api"
import { maxTableHeight, batchFormatterNumView } from "@/app/baseline/utils"
import { getRealLength, setString } from "@/app/baseline/utils/validate"

export interface Props {
	id: string | number //
}

const props = defineProps<Props>()
const emits = defineEmits(["close"])

const loading = ref(false)
const formBtnList = [{ name: "取消", icon: ["fas", "circle-minus"] }]

const formModal = ref<MatReturnStoreApplyBatchInfoVo>({})

/**
 * 左侧 title 配置
 */
const drawerLeftTitle = {
	name: ["物资信息"],
	icon: ["fas", "square-share-nodes"]
}

/**
 * 左侧 表单 配置
 */
const formEl = reactive([
	{ label: "物资编码", name: "materialCode" },
	{ label: "物资名称", name: "materialName" },
	{ label: "品牌型号", name: "version" },
	{ label: "技术参数", name: "technicalParameter", needTooltip: true },
	{ label: "物资性质", name: "attribute" },
	{ label: "出库仓库名称", name: "storeName" },
	//{ label: "退库数量", name: "completeNum" },
	{ label: "金额", name: "amount" }
])

/**
 * 右侧 title 配置
 */
const drawerRightTitle = {
	name: ["批次信息"],
	icon: ["fas", "square-share-nodes"]
}

const {
	tableData,
	tableProp,
	tableRef,
	tableLoading,
	currentPage,
	pageTotal,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange,
	onDataSelected
} = useTbInit<
	MatReturnStoreApplyBatchpageVo,
	MatReturnStoreApplyBatchpageReqParams
>()

/**
 * table 列 配置
 */
tableProp.value = [
	{ label: "批次号", prop: "batchNo", width: 170 },
	{ label: "区域名称", prop: "regionLabel" },
	{ label: "货位编码", prop: "roomCode" },
	{ label: "退库数量", prop: "completeNum_view", align: "right" },
	{ label: "采购单价", prop: "amount", needSlot: true, align: "right" },
	{ label: "金额", prop: "totalPrice", needSlot: true, align: "right" },
	{ label: "质保有效日期", prop: "validityPeriod", minWidth: 90 }
	/* { label: "入库时间", prop: "inStoreTime", minWidth: 90 } */
]

/**
 * 格式化数量
 */
watch(tableData, () => {
	batchFormatterNumView(tableData.value as any[])
})

/**
 * table 数据源
 * @param data
 */
const getTableData = (data?: any) => {
	if (props.id) {
		fetchFunc.value = listOutStoreReturnPageBatchInfoPage

		currentPage.value = 1
		fetchParam.value = {
			id: props.id,
			sord: "desc",
			sidx: "batchNo",
			...data
		}

		fetchTableData()
	}
}

const drawerLoading = ref(false)
const getDetail = () => {
	if (!props.id) {
		return false
	}
	drawerLoading.value = true
	listOutStoreReturnPageBatchInfo(props.id as number)
		.then((res: any) => {
			formModal.value = res
		})
		.finally(() => {
			drawerLoading.value = false
		})
}

onMounted(() => {
	getDetail()
	getTableData()
})
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<!-- 左侧表单区域 -->
		<div class="drawer-column left">
			<div class="rows">
				<Title :title="drawerLeftTitle" />
				<el-descriptions size="small" :column="1" border class="content">
					<template>
						<el-descriptions-item
							v-for="(el, index) in formEl"
							label-align="center"
							:label="el.label"
							:key="index"
						>
							<!-- 出库状态 -->
							<span v-if="el.name == 'amount'">
								<cost-tag :value="formModal[el.name]!" />
							</span>
							<dict-tag
								v-else-if="el.name === 'attribute'"
								:options="DictApi.getMatAttr()"
								:value="formModal.attribute"
							/>
							<span v-else-if="el.needTooltip">
								<el-tooltip
									effect="dark"
									:content="formModal?.[el.name]"
									:disabled="
										getRealLength(formModal?.[el.name]) <= 100 ? true : false
									"
								>
									{{
										getRealLength(formModal?.[el.name]) > 100
											? setString(formModal?.[el.name], 100)
											: formModal?.[el.name] || "---"
									}}
								</el-tooltip>
							</span>
							<span v-else>
								{{
									formModal[el.name] || formModal[el.name] == 0
										? formModal[el.name]
										: "---"
								}}
							</span>
						</el-descriptions-item>
					</template>
				</el-descriptions>
			</div>
		</div>

		<!-- 右侧table区域 -->
		<div class="drawer-column right">
			<div class="rows">
				<Title :title="drawerRightTitle" />
				<el-scrollbar class="tab-mat">
					<PitayaTable
						ref="tableRef"
						:columns="tableProp"
						:table-data="tableData"
						:need-index="true"
						:need-pagination="true"
						:single-select="false"
						:need-selection="false"
						:total="pageTotal"
						:max-height="maxTableHeight"
						@onSelectionChange="onDataSelected"
						@on-current-page-change="onCurrentPageChange"
						:table-loading="tableLoading"
					>
						<template #amount="{ rowData }">
							<cost-tag :value="rowData.amount" />
						</template>
						<template #totalPrice="{ rowData }">
							<cost-tag :value="rowData.totalPrice" />
						</template>
					</PitayaTable>
				</el-scrollbar>
			</div>

			<ButtonList
				class="footer"
				:button="formBtnList"
				:loading="loading"
				@on-btn-click="emits('close')"
			/>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.drawer-container {
	.left {
		width: 310px;
	}

	.right {
		width: calc(100% - 310px);
	}

	.tab-mat {
		height: calc(100% - 43px);
	}
}
</style>
