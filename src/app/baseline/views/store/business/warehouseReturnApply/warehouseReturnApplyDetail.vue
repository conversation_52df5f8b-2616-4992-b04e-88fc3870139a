<!-- 退库申请-详情 -->
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<div class="drawer-column left" style="width: 310px">
			<Title :title="titleConf" />

			<el-scrollbar class="rows">
				<!-- 明细信息 -->
				<el-descriptions size="small" :column="1" border class="content">
					<el-descriptions-item
						v-for="desc in descOptions"
						:key="desc.label"
						:label="desc.label"
					>
						<!-- 退货金额（元） -->
						<cost-tag
							v-if="desc.prop === 'amount'"
							:value="detailData.amount"
						/>
						<span v-else-if="desc.needTooltip">
							<el-tooltip
								effect="dark"
								:content="detailData?.[desc.prop]"
								:disabled="
									getRealLength(detailData?.[desc.prop]) <= 100 ? true : false
								"
							>
								{{
									getRealLength(detailData?.[desc.prop]) > 100
										? setString(detailData?.[desc.prop], 100)
										: detailData?.[desc.prop] || "---"
								}}
							</el-tooltip>
						</span>
						<span v-else>
							{{ getNumDefByNumKey(desc.prop, detailData?.[desc.prop]) }}
						</span>

						<!-- {{ getNumDefByNumKey(desc.prop, detailData[desc.prop]) }} -->
					</el-descriptions-item>
				</el-descriptions>
			</el-scrollbar>
		</div>

		<!-- 扩展信息 -->
		<div
			class="drawer-column right"
			style="width: calc(100% - 310px)"
			:class="{ pdr10: !footerBtnVisible }"
		>
			<Title :title="extraTitleConf">
				<Tabs
					:tabs="tabsConf"
					style="margin-right: auto; margin-left: 30px"
					@on-tab-change="handleTabChange"
				/>
			</Title>
			<el-scrollbar class="rows" v-if="activatedTab === 0">
				<Query
					:query-arr-list="(queryConf as any)"
					style="margin: 10px 10px -10px"
					@get-query-data="handleQuery"
				/>
				<pitaya-table
					ref="tableRef"
					:columns="(tbInit.tableProp as any)"
					:table-data="tableData"
					:need-selection="false"
					:single-select="false"
					:need-index="true"
					:need-pagination="true"
					:total="pageTotal"
					:table-loading="tableLoading"
					@on-selection-change="selectedTableList = $event"
					@on-current-page-change="fetchTableData"
				>
					<!-- 物资性质 -->
					<template #attribute="{ rowData }">
						<dict-tag
							:options="DictApi.getMatAttr()"
							:value="rowData.attribute"
						/>
					</template>

					<template #amount="{ rowData }">
						<cost-tag :value="rowData.amount" />
					</template>
					<template #returnBatchNum="{ rowData }">
						<link-tag
							:value="rowData.returnBatchNum"
							@on-click="onRowReturnBatch(rowData)"
						/>
						<!-- @on-click="onRowBusinessOrder(rowData)" -->
					</template>
					<template #useUnit="{ rowData }">
						{{
							dictFilter("INVENTORY_UNIT", rowData.useUnit)?.subitemName ||
							"---"
						}}
					</template>
				</pitaya-table>
			</el-scrollbar>

			<el-scrollbar class="rows" v-else>
				<table-file
					:business-type="fileBusinessType.warehouseReturnApply"
					:business-id="id"
					:mod="
						footerBtnVisible && detailData.bpmStatus === appStatus.approved
							? IModalType.edit
							: IModalType.view
					"
				/>
			</el-scrollbar>

			<button-list
				v-if="footerBtnVisible"
				class="footer"
				:button="btnConf"
				@on-btn-click="emit('close')"
			/>

			<Drawer
				:size="modalSize.lg"
				v-model:drawer="returnBatchVisible"
				:destroyOnClose="true"
			>
				<return-batch-detail
					:id="currentRow.id!"
					@close="returnBatchVisible = false"
				/>
			</Drawer>
		</div>
	</div>
</template>

<script setup lang="ts">
import { IModalType } from "@/app/baseline/utils/types/common"
import {
	getWarehouseReturnApply,
	listWarehouseReturnApplyGoodsPaged
} from "@/app/baseline/api/store/warehouse-return-apply-api"
import { useTbInit } from "../../../components/tableBase"
import {
	MatReturnStoreApplyItemReqParams,
	MatReturnStoreApplyItemVo
} from "@/app/baseline/utils/types/warehouse-return-apply"
import { useDictInit } from "../../../components/dictBase"
import CostTag from "../../../components/costTag.vue"
import {
	batchFormatterNumView,
	getNumDefByNumKey,
	tableColFilter
} from "@/app/baseline/utils"
import LinkTag from "@/app/baseline/views/components/linkTag.vue"
import { modalSize } from "@/app/baseline/utils/layout-config"
import returnBatchDetail from "./returnBatchDetail.vue"
import { DictApi, appStatus, fileBusinessType } from "@/app/baseline/api/dict"
import { getRealLength, setString } from "@/app/baseline/utils/validate"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import TableFile from "../../../components/tableFile.vue"

const props = withDefaults(
	defineProps<{
		/**
		 * 业务id
		 */
		id: any

		footerBtnVisible?: boolean
	}>(),
	{ footerBtnVisible: true }
)

const emit = defineEmits<{
	(e: "close"): void
}>()

const btnConf = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	}
]

const activatedTab = ref(0)

const { dictOptions, dictFilter, getDictByCodeList } = useDictInit()

const titleConf = computed(() => ({
	name: ["退库单信息"],
	icon: ["fas", "square-share-nodes"]
}))

const extraTitleConf = {
	name: ["扩展信息"],
	icon: ["fas", "square-share-nodes"]
}

const tabsConf = ["物资明细", "相关附件"]

const handleTabChange = (index: number) => {
	activatedTab.value = index

	if (index === 0) {
		fetchParam.value = {
			applyId: props.id || detailData.value?.id,
			sord: "desc",
			sidx: "createdDate"
		}
		pageSize.value = 20
		handleQuery()
	}
}

const queryConf = computed(() => [
	{
		name: "物资编码",
		key: "materialCode",
		type: "input",
		placeholder: "请输入物资编码"
	},
	{
		name: "物资名称",
		key: "materialLabel",
		type: "input",
		placeholder: "请输入物资名称"
	},
	{
		name: "规格型号",
		key: "version",
		placeholder: "请输入规格型号",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资性质",
		key: "attribute",
		type: "select",
		children: dictOptions.value.MATERIAL_NATURE,
		placeholder: "请选择物资性质"
	}
])

/**
 * 明细配置
 */
const descOptions = computed<Record<string, any>[]>(() => {
	const allDesc = [
		{
			label: "退库单号",
			prop: "code"
		},
		{
			label: "关联领料单号",
			prop: "preBusinessCode"
		},
		{
			label: "入库仓库名称",
			prop: "storeLabel"
		},
		{
			label: "物资编码",
			prop: "materialCodeNum"
		},
		{
			label: "退库金额",
			prop: "amount"
		},
		/* {
		label: "退库数量",
		prop: "returnNum"
	}, */
		{
			label: "申请时间",
			prop: "createdDate"
		},
		{
			label: "退库原因说明",
			prop: "reason",
			needTooltip: true
		}
	]

	if (detailData.value.bpmStatus == appStatus.pendingApproval) {
		// 待提交 状态；不显示退库金额
		return tableColFilter(allDesc, ["退库金额"])
	} else {
		return allDesc
	}
})

/**
 * 详情数据
 */
const detailData = ref<any>({})

const drawerLoading = ref(false)

const tbInit = useTbInit<
	MatReturnStoreApplyItemVo,
	MatReturnStoreApplyItemReqParams
>()
const {
	currentPage,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	pageSize,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc
} = tbInit

fetchFunc.value = listWarehouseReturnApplyGoodsPaged

tbInit.tableProp = computed(() => {
	const defCols: TableColumnType[] = [
		{
			prop: "materialCode",
			label: "物资编码"
		},
		{
			prop: "materialName",
			label: "物资名称"
		},
		{
			prop: "version",
			label: "规格型号"
		},
		{
			prop: "technicalParameter",
			label: "技术参数"
		},
		{
			prop: "attribute",
			label: "物资性质",
			needSlot: true,
			width: 120
		},
		{
			prop: "useUnit",
			label: "库存单位",
			needSlot: true,
			width: 80
		},
		{
			prop: "amount",
			label: "退库金额",
			needSlot: true,
			align: "right"
		},
		{
			prop: "num_view",
			label: "领料出库数量",
			align: "right"
		},
		{
			prop: "completeNum_view",
			label: "退库数量",
			align: "right"
		},
		{
			prop: "returnBatchNum",
			label: "退库批次",
			needSlot: true
		},
		{
			prop: "inspectionPersonId_view",
			label: "质检员"
		}
	]

	if (
		detailData.value.bpmStatus == appStatus.pendingApproval ||
		detailData.value.bpmStatus == appStatus.rejected
	) {
		// 待提交 状态；不显示退库批次
		return tableColFilter(defCols, ["退库批次", "退库金额", "质检员"])
	} else {
		return defCols
	}
})

/**
 * 格式化数量
 */
watch(tableData, () => {
	batchFormatterNumView(tableData.value as any[])
})

const returnBatchVisible = ref(false)
const currentRow = ref<MatReturnStoreApplyItemVo>({})
function onRowReturnBatch(e: MatReturnStoreApplyItemVo) {
	currentRow.value = e
	returnBatchVisible.value = true
}
onMounted(() => {
	fetchParam.value.applyId = props.id
	fetchParam.value.sord = "desc"
	fetchParam.value.sidx = "createdDate"

	getDictByCodeList(["INVENTORY_UNIT", "MATERIAL_NATURE"])
	getDetail()
	fetchTableData()
})

/**
 * 获取详情数据
 */
async function getDetail() {
	drawerLoading.value = true
	try {
		detailData.value = await getWarehouseReturnApply(props.id)
	} finally {
		drawerLoading.value = false
	}
}

/**
 * 筛选 handler
 */
function handleQuery(e?: any) {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()
	fetchParam.value = {
		...fetchParam.value,
		...e
	}

	fetchTableData()
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
