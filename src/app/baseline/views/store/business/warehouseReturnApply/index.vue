<!-- 库存管理-业务管理-退库申请 -->
<template>
	<div class="app-container">
		<div class="whole-frame">
			<model-frame class="top-frame">
				<!-- 筛选 -->
				<Query
					:query-arr-list="(queryConf as any)"
					@get-query-data="handleQuery"
					class="ml10"
				/>
			</model-frame>

			<model-frame class="bottom-frame">
				<Title
					:title="titleConf"
					:button="titleBtnConf"
					@on-btn-click="showCreatorDrawer"
				>
					<div class="app-tabs-wrapper">
						<el-tabs v-model="activeName" @tab-click="handleTabChange">
							<el-tab-pane
								v-for="(tab, index) in tabsConf"
								:key="index"
								:label="`${tab}（${statusCnt[index] ?? 0}）`"
								:name="tab"
								:index="tab"
							/>
						</el-tabs>
					</div>

					<!-- <Tabs
						style="margin-right: auto; margin-left: 30px"
						:tabs="tabsConf"
						@on-tab-change="handleTabChange"
					/> -->
				</Title>

				<pitaya-table
					ref="tableRef"
					:columns="tableProp"
					:table-data="tableData"
					:need-selection="true"
					:single-select="true"
					:need-index="true"
					:need-pagination="true"
					:total="pageTotal"
					:table-loading="tableLoading"
					@on-selection-change="selectedTableList = $event"
					@on-current-page-change="onCurrentPageChange"
					@on-table-sort-change="handleSortChange"
				>
					<!-- 审批状态 -->
					<template #bpmStatus="{ rowData }">
						<dict-tag
							:options="DictApi.getBpmStatus()"
							:value="rowData.bpmStatus"
						/>
					</template>

					<!-- 关联业务单号 -->
					<template #preBusinessCode="{ rowData }">
						<link-tag
							:value="rowData.preBusinessCode"
							@click.stop="onRowBusiness(rowData)"
						/>
					</template>

					<template #actions="{ rowData }">
						<slot
							v-if="
								(canShowTableEditAction(rowData.bpmStatus) &&
									isCheckPermission(
										powerList.storeBusinessInventoryReturnApplyBtnEdit
									) &&
									hasPermi(rowData.createdBy)) ||
								isCheckPermission(
									powerList.storeBusinessInventoryReturnApplyBtnPreview
								) ||
								(canShowTableEditAction(rowData.bpmStatus) &&
									isCheckPermission(
										powerList.storeBusinessInventoryReturnApplyBtnDrop
									) &&
									hasPermi(rowData.createdBy))
							"
						>
							<el-button
								v-if="
									canShowTableEditAction(rowData.bpmStatus) &&
									isCheckPermission(
										powerList.storeBusinessInventoryReturnApplyBtnEdit
									) &&
									hasPermi(rowData.createdBy)
								"
								v-btn
								link
								@click.stop="showEditorDrawer(rowData)"
								:disabled="
									checkPermission(
										powerList.storeBusinessInventoryReturnApplyBtnEdit
									)
								"
							>
								<font-awesome-icon
									:icon="['fas', 'pen-to-square']"
									style="color: var(--pitaya-btn-background)"
								/>
								<span class="table-inner-btn">编辑</span>
							</el-button>

							<el-button
								v-btn
								link
								@click.stop="showDetailDrawer(rowData)"
								:disabled="
									checkPermission(
										powerList.storeBusinessInventoryReturnApplyBtnPreview
									)
								"
								v-if="
									isCheckPermission(
										powerList.storeBusinessInventoryReturnApplyBtnPreview
									)
								"
							>
								<font-awesome-icon
									:icon="['fas', 'eye']"
									style="color: var(--pitaya-btn-background)"
								/>
								<span class="table-inner-btn">查看</span>
							</el-button>

							<el-button
								v-if="
									canShowTableEditAction(rowData.bpmStatus) &&
									isCheckPermission(
										powerList.storeBusinessInventoryReturnApplyBtnDrop
									) &&
									hasPermi(rowData.createdBy)
								"
								v-btn
								link
								@click.stop="handleDelRow(rowData)"
								:disabled="
									checkPermission(
										powerList.storeBusinessInventoryReturnApplyBtnDrop
									)
								"
							>
								<font-awesome-icon
									:icon="['fas', 'trash-can']"
									style="color: var(--pitaya-btn-background)"
								/>
								<span class="table-inner-btn">移除</span>
							</el-button>
						</slot>
						<slot v-else>---</slot>
					</template>

					<template v-if="activatedTab === 0" #footerOperateLeft>
						<button-list
							class="footer"
							:is-not-radius="true"
							:button="tbFooterBtnConf"
							@on-btn-click="handleClickTbFooterBtn"
						/>
					</template>
				</pitaya-table>
			</model-frame>
		</div>

		<!-- 查看 drawer -->
		<Drawer
			v-model:drawer="detailVisible"
			:size="modalSize.xl"
			destroy-on-close
		>
			<warehouse-return-apply-detail
				:id="editingTableRowId"
				@close="detailVisible = false"
			/>
		</Drawer>

		<!-- 编辑 drawer -->
		<Drawer
			v-model:drawer="editorVisible"
			:size="modalSize.xl"
			destroy-on-close
		>
			<warehouse-return-apply-editor
				:id="editingTableRowId"
				:mode="editorMode"
				@close="editorVisible = false"
				@save="handleDrawerSave"
				@update="fetchTableData"
			/>
		</Drawer>

		<!-- 推送质检 drawer -->
		<Drawer
			v-model:drawer="pushInspectVisible"
			:size="modalSize.xl"
			destroy-on-close
		>
			<push-inspect-detail
				:id="editingTableRowId"
				:mode="IModalType.edit"
				@close="pushInspectVisible = false"
				@save="handleInspectSaved"
			/>
		</Drawer>

		<!-- 关联业务单号 -->
		<Drawer
			:size="modalSize.xl"
			v-model:drawer="businessDetilVisible"
			destroyOnClose
		>
			<component
				:is="businessPreComponent"
				:id="preBusinessId"
				@close="businessDetilVisible = false"
			/>
		</Drawer>

		<!-- 审批、查看弹窗 -->
		<Drawer
			:size="modalSize.xxl"
			v-model:drawer="approvedVisible"
			:destroyOnClose="true"
		>
			<approved-drawer
				:mod="editorMode"
				:task-value="editTask"
				@on-save-or-close="approvedVisible = false"
			/>
		</Drawer>
	</div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from "vue"
import {
	checkPermission,
	isCheckPermission
} from "@/app/platform/utils/permission"
import { powerList } from "../../../components/define.d"

import { IModalType } from "@/app/baseline/utils/types/common"
import { useMessageBoxInit } from "../../../components/messageBox"
import { useTbInit } from "../../../components/tableBase"
import { modalSize } from "@/app/baseline/utils/layout-config"
import { listMatStoragePaged } from "@/app/baseline/api/store/manage-api"
import { first, omit } from "lodash-es"
import { DictApi, appStatus } from "@/app/baseline/api/dict"
import DictTag from "../../../components/dictTag.vue"
import pushInspectDetail from "./pushInspectDetail.vue"
import {
	deleteWarehouseReturnApply,
	getOutStoreReturnApplyBmpStatusCnt,
	listWarehouseReturnApplyPaged
} from "@/app/baseline/api/store/warehouse-return-apply-api"
import warehouseReturnApplyDetail from "./warehouseReturnApplyDetail.vue"
import warehouseReturnApplyEditor from "./warehouseReturnApplyEditor.vue"
import {
	MatReturnStoreApplyDTO,
	MatReturnStoreApplyVo
} from "@/app/baseline/utils/types/warehouse-return-apply"
import { IWarehousingStatus } from "@/app/baseline/utils/types/warehousing-apply"
import LinkTag from "@/app/baseline/views/components/linkTag.vue"
import businessComponentMap from "../inventory/business-component-map"
import { IInventoryBusinessType } from "@/app/baseline/utils/types/store-inventory"
import { hasPermi } from "@/app/baseline/utils"
import { getTaskByBusinessIds } from "@/app/baseline/api/system"
import ApprovedDrawer from "@/app/baseline/views/components/approvedDrawer.vue"

const { showDelConfirm } = useMessageBoxInit()

/**
 * 编辑模式：编辑/新建
 */
const editorMode = ref(IModalType.create)

const pushInspectVisible = ref(false)

const detailVisible = ref(false)

const editorVisible = ref(false)

const approvedVisible = ref(false)
const editTask = ref<Record<string, any>>()

/**
 * 推送质检按钮
 *
 * 已审批的退库申请才能推送质检
 */
const tbFooterBtnConf = computed(() => {
	const rowData = first(selectedTableList.value)

	const disabled = !rowData || rowData.bpmStatus !== appStatus.approved

	return [
		{
			name: "分配质检",
			roles: powerList.storeBusinessInventoryReturnApplyBtnQuality,
			icon: ["fas", "stamp"],
			disabled
		}
	]
})

/**
 * 按钮配置：新建
 */
const titleBtnConf = [
	{
		name: "新建退库申请",
		roles: powerList.storeBusinessInventoryReturnApplyBtnCreate,
		icon: ["fas", "square-plus"]
	}
] as any

/**
 * 标题配置
 */
const titleConf = {
	name: ["退库申请"],
	icon: ["fas", "square-share-nodes"]
}

/**
 * 筛选配置
 */
const queryConf = computed<querySetting[]>(() => {
	return [
		{
			name: "退库申请单号",
			key: "code",
			type: "input",
			placeholder: "请输入退库申请单号"
		},
		{
			name: "申请人",
			key: "createdByName",
			type: "input",
			placeholder: "请输入申请人姓名"
		},
		{
			name: "入库仓库",
			key: "storeId",
			placeholder: "请选择入库仓库",
			type: "tableSelect",
			tableInfo: [
				{
					title: "请选择入库仓库",
					tableApi: listMatStoragePaged, //表格接口
					tableColumns: [
						{ label: "仓库编码", prop: "code", width: 120 },
						{ label: "仓库名称", prop: "label" },
						{
							label: "仓库级别",
							prop: "level_view",
							width: 100
						},
						{
							label: "仓库类型",
							prop: "type_view",
							width: 120
						},
						{
							label: "所属段区",
							prop: "depotId_view",
							width: 150
						}
					],
					queryArrList: [
						{
							name: "仓库编码",
							key: "code",
							type: "input",
							placeholder: "请输入仓库编码"
						},
						{
							name: "仓库名称",
							key: "label",
							type: "input",
							placeholder: "请输入仓库名称"
						}
					],
					params: {
						currentPage: 1,
						pageSize: 20
					}, //表格接口参数

					labelName: {
						key: "id", //回显标识
						label: "label", //input框要展示的字段
						value: "id" //要给后台传的字段
					}
				}
			]
		}
	]
})

/**
 * 标签页配置
 */
const tabsConf = ["待处理", "已完成"]
const activeName = ref<string>(tabsConf[0])
const statusCnt = ref<number[]>([])

/**
 * 当前激活的标签页位置
 */
const activatedTab = ref(0)

const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	currentPage,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = useTbInit<MatReturnStoreApplyVo, MatReturnStoreApplyDTO>()

/**
 * table api function conf
 */
fetchFunc.value = listWarehouseReturnApplyPaged

fetchParam.value = {
	status: IWarehousingStatus.pending,
	sord: "desc",
	sidx: "createdDate"
}

/**
 * table 的 columns 配置
 */
tableProp.value = [
	{
		prop: "code",
		label: "退库单号",
		width: 180,
		sortable: true
	},
	{
		prop: "label",
		label: "退库申请名称"
	},
	{
		prop: "preBusinessCode",
		label: "关联业务单号",
		needSlot: true,
		width: 180
	},
	{
		prop: "storeLabel",
		label: "入库仓库名称"
	},
	{
		prop: "materialCodeNum",
		label: "物资编码",
		width: 100
	},
	{
		prop: "createdBy_view",
		label: "申请人"
	},
	{
		prop: "sysOrgId_view",
		label: "申请部门"
	},
	{
		prop: "bpmStatus",
		label: "审批状态",
		width: 90,
		needSlot: true
	},
	{
		prop: "createdDate",
		label: "申请时间",
		width: 150,
		sortable: true
	},
	{
		prop: "reason",
		label: "退库原因说明"
	},
	{
		prop: "actions",
		label: "操作",
		width: 200,
		needSlot: true
	}
]

/**
 * 当前编辑/查看的 table row id
 */
const editingTableRowId = ref()

onMounted(() => {
	handleQuery()
})

/**
 * 关联业务单号 操作
 * @param e
 */
const businessDetilVisible = ref(false)
const preBusinessId = ref()
const preBusinessType = ref()
function onRowBusiness(e: MatReturnStoreApplyVo) {
	if (!e.preBusinessId) {
		return false
	}
	businessDetilVisible.value = true
	preBusinessId.value = e.preBusinessId
	preBusinessType.value = e.preBusinessType
}
const businessPreComponent = computed(() => {
	return businessComponentMap[preBusinessType.value as IInventoryBusinessType]
})

/**
 * 是否可见 - table 操作按钮 编辑/移除
 */
function canShowTableEditAction(bpmStatus: appStatus) {
	const isValidBpmStatus = [
		appStatus.pendingApproval,
		appStatus.rejected
	].includes(bpmStatus)
	const isPending = activatedTab.value === 0

	return isPending && isValidBpmStatus
}

/**
 * 筛选 handler
 */
function handleQuery(e?: any) {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	const status =
		activatedTab.value === 0
			? IWarehousingStatus.pending
			: IWarehousingStatus.completed

	// 聚合筛选条件
	fetchParam.value = {
		...fetchParam.value,
		...e,
		status
	}

	handleUpdate()
}

/**
 * 展示详情 drawer
 */
async function showDetailDrawer(e: any) {
	editingTableRowId.value = e.id
	editorMode.value = IModalType.view
	/* detailVisible.value = true */

	if (e?.bpmStatus == appStatus.pendingApproval) {
		detailVisible.value = true
	} else {
		const res = await getTaskByBusinessIds({
			businessIds: [e?.id],
			camundaKey: "return_apply"
		})

		if (Array.isArray(res) && res.length > 0) {
			editTask.value = { ...res[res.length - 1] }
			approvedVisible.value = true
		} else {
			detailVisible.value = true
		}
	}
}

function handleTabChange(tab: any) {
	activeName.value = tab.paneName
	activatedTab.value = Number(tab.index)

	handleQuery()
}

/**
 * 展示编辑器 drawer
 */
function showEditorDrawer(e: any) {
	editingTableRowId.value = e.id
	editorMode.value = IModalType.edit
	editorVisible.value = true
}

/**
 * 展示新建 drawer
 */
function showCreatorDrawer(e: any) {
	editingTableRowId.value = e.id
	editorMode.value = IModalType.create
	editorVisible.value = true
}

/**
 * 删除 table row
 */
async function handleDelRow(e: any) {
	await showDelConfirm()
	// 删除 api 逻辑处理
	await deleteWarehouseReturnApply(e.id)
	ElMessage.success("操作成功")
	handleUpdate()
}

/**
 * drawer save handler
 *
 * 处理 drawer 保存之后的逻辑
 *
 * @param id - table row id
 */
function handleDrawerSave(id: any, visible = false) {
	editorMode.value = IModalType.edit
	editorVisible.value = visible
	editingTableRowId.value = id
	handleUpdate()
}

/**
 * 点击推送质检 handler
 */
function handleClickTbFooterBtn() {
	editingTableRowId.value = first(selectedTableList.value)?.id
	pushInspectVisible.value = true
}

/**
 * 推送质检 保存后 handler
 */
function handleInspectSaved() {
	handleUpdate()
	pushInspectVisible.value = false
}

/**
 * 更新主表/ tab 状态统计
 */
async function handleUpdate() {
	fetchTableData()
	statusCnt.value = await getOutStoreReturnApplyBmpStatusCnt(
		omit(
			{
				...fetchParam.value
			},
			"status",
			"currentPage",
			"pageSize"
		) as any
	)
}

/**
 * 排序
 * @param column
 * @param prop 字段名称
 * @param order ascending 升序 descending 降序
 */
function handleSortChange(column: any, prop: any, order: any) {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		sord:
			order === "descending" ? "desc" : order === "ascending" ? "asc" : "desc",
		sidx: order ? prop : "createdDate" // 排序字段
	}

	fetchTableData()
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
