<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<div class="drawer-column left" style="width: 310px">
			<Title :title="titleConf" />

			<el-scrollbar class="rows">
				<grid-panel :options="gridPanelOptions" />

				<el-form
					ref="formRef"
					class="content"
					:model="formData"
					:rules="formRules"
					label-position="top"
					label-width="100px"
				>
					<form-element :form-element="formEls" :form-data="formData" />
				</el-form>
			</el-scrollbar>

			<button-list
				class="footer"
				:button="btnConf"
				:loading="btnLoading"
				@on-btn-click="handleDrawerBtnClick"
			/>
		</div>

		<!-- 扩展信息 -->
		<div
			class="drawer-column right"
			:class="mode === IModalType.create ? 'disabled' : ''"
			style="width: calc(100% - 310px)"
		>
			<Title :title="extraTitleConf">
				<Tabs
					:tabs="tabsConf"
					style="margin-right: auto; margin-left: 30px"
					@on-tab-change="handleTabChange"
				/>
			</Title>

			<el-scrollbar
				v-if="mode === IModalType.edit"
				class="rows warehouse-return-apply-editor-table-wrapper"
			>
				<Query
					:query-arr-list="(queryArrList as any)"
					style="margin: 10px 10px -10px"
					@get-query-data="handleQuery"
					v-if="activatedTab === 0"
				/>

				<pitaya-table
					v-if="activatedTab === 0"
					ref="tableRef"
					:columns="tableProp"
					:table-data="tableData"
					:need-selection="false"
					:single-select="false"
					:need-index="true"
					:need-pagination="true"
					:total="pageTotal"
					:table-loading="tableLoading"
					@on-selection-change="selectedTableList = $event"
					@on-current-page-change="onCurrentPageChange"
					:cell-class-name="tbCellClassName"
				>
					<!-- 物资性质 -->
					<template #attribute="{ rowData }">
						<dict-tag
							:options="DictApi.getMatAttr()"
							:value="rowData.attribute"
						/>
					</template>

					<!-- 退库数量编辑 -->
					<template #completeNum="{ rowData }">
						<el-input
							class="no-arrows"
							v-model="rowData.completeNum"
							@click.stop
							@input="rowData.completeNum = validateAndCorrectInput($event)"
							@change="validateWarehouseReturnNum(rowData)"
						/>
					</template>

					<template #useUnit="{ rowData }">
						{{
							dictFilter("INVENTORY_UNIT", rowData.useUnit)?.subitemName ||
							"---"
						}}
					</template>

					<template #actions="{ rowData }">
						<el-button v-btn link @click.stop="handleDelGoods(rowData)">
							<font-awesome-icon
								:icon="['fas', 'trash-can']"
								style="color: var(--pitaya-btn-background)"
							/>
							<span class="table-inner-btn">移除</span>
						</el-button>
					</template>

					<template #footerOperateLeft>
						<button-list
							:button="tbBtnConf"
							:is-not-radius="true"
							@on-btn-click="goodsSelectorVisible = true"
						/>
					</template>
				</pitaya-table>

				<table-file
					v-else
					:business-type="fileBusinessType.warehouseReturnApply"
					:business-id="id"
					:mod="IModalType.edit"
				/>
			</el-scrollbar>

			<button-list
				v-if="mode === IModalType.edit"
				class="footer"
				:loading="submitBtnLoading"
				:button="submitBtnConf"
				@on-btn-click="handleSubmit"
			/>
		</div>

		<!-- 物资选择器 -->
		<Drawer
			v-model:drawer="goodsSelectorVisible"
			:size="modalSize.lg"
			destroy-on-close
		>
			<mat-selector
				multiple
				:columns="goodsSelectorColumns"
				:table-req-params="{
					applyId: id,
					pickApplyId: formData.preBusinessId,
					type: IInventoryBusinessType.warehouseReturnApply,
					sord: 'asc',
					sidx: 'materialCode'
				}"
				:table-api="listCanPickApplyItemPaged"
				@save="handleAddGoods"
				@close="goodsSelectorVisible = false"
			/>
		</Drawer>

		<!-- 领料单选择器 -->
		<Drawer
			v-model:drawer="matGetTicketSelectorVisible"
			:size="modalSize.lg"
			destroy-on-close
		>
			<mat-get-ticket-selector
				:table-api="listCanPickApplyPaged"
				:selected="(formData.preBusinessId as number)"
				@close="matGetTicketSelectorVisible = false"
				@save="handleSelectedMatGetTicket"
				:tableApiParams="{ purpose: MaterialPurpose.lowvalueMaterial }"
			/>
		</Drawer>

		<!-- 仓库选择器 -->
		<Drawer
			v-model:drawer="warehouseSelectorVisible"
			:size="modalSize.lg"
			destroy-on-close
		>
			<store-table
				:table-api-params="{
					type: formData.storeType
				}"
				:selectedIds="[formData.storeId]"
				@on-save="handleSelectWarehouse"
			/>
		</Drawer>
	</div>
</template>

<script setup lang="ts">
import {
	getModalTypeLabel,
	toMoney,
	batchFormatterNumView
} from "@/app/baseline/utils"
import {
	IIdempotentTokenType,
	IIdempotentTokenTypePre,
	IModalType
} from "@/app/baseline/utils/types/common"
import { FormItemRule, FormInstance } from "element-plus"
import GridPanel from "../../components/gridPanel.vue"
import FormElement from "../../../components/formElement.vue"
import { FormElementType } from "../../../components/define"
import {
	addWarehouseReturnApplyGoods,
	deleteWarehouseReturnApplyGoods,
	getWarehouseReturnApply,
	listWarehouseReturnApplyGoodsPaged,
	saveWarehouseReturnApply,
	submitWarehouseReturnApply,
	updateWarehouseReturnApply,
	updateWarehouseReturnApplyGoods
} from "@/app/baseline/api/store/warehouse-return-apply-api"
import {
	listCanPickApplyPaged,
	listCanPickApplyItemPaged
} from "@/app/baseline/api/store/mat-get-apply-api"
import { useTbInit } from "../../../components/tableBase"
import { DictApi, fileBusinessType } from "@/app/baseline/api/dict"
import { useMessageBoxInit } from "../../../components/messageBox"
import MatSelector from "../../components/matSelector.vue"
import { inputMaxLength, modalSize } from "@/app/baseline/utils/layout-config"
import { debounce, findIndex, map, toNumber } from "lodash-es"
import TableFile from "../../../components/tableFile.vue"
import matGetTicketSelector from "./matGetTicketSelector.vue"
import StoreTable from "../../components/storeTable.vue"
import { useUserStore } from "@/app/platform/store/modules/user"
import {
	MatReturnStoreApplyDTO,
	MatReturnStoreApplyItemReqParams,
	MatReturnStoreApplyItemVo,
	MatReturnStoreApplyVo
} from "@/app/baseline/utils/types/warehouse-return-apply"
import {
	getIdempotentToken,
	requiredValidator,
	validateAndCorrectInput
} from "@/app/baseline/utils/validate"
import { useDictInit } from "../../../components/dictBase"
import { MaterialPurpose } from "@/app/baseline/api/dict"
import { IInventoryBusinessType } from "@/app/baseline/utils/types/store-inventory"
import { includes } from "lodash-es"
import DictTag from "@/app/baseline/views/components/dictTag.vue"

const props = defineProps<{
	/**
	 * 业务id
	 */
	id: any

	/**
	 * 编辑模式：编辑/新建
	 */
	mode: IModalType
}>()

const emit = defineEmits<{
	(e: "update"): void
	(e: "close"): void
	/**
	 * 保存事件
	 *
	 * @param id 主业务id
	 * @param visible editor 是否展示
	 */
	(e: "save", id: any, visible?: boolean): void
}>()

const { showDelConfirm, showWarnConfirm } = useMessageBoxInit()
const { dictOptions, dictFilter, getDictByCodeList } = useDictInit()

const titleConf = computed(() => ({
	name: [getModalTypeLabel(props.mode, "退库申请")],
	icon: ["fas", "square-share-nodes"]
}))

/**
 * 保存旧的表单数据
 */
const oldFormData = ref<string>()

/**
 * 物资选择器
 */
const goodsSelectorVisible = ref(false)

/**
 * 领料单选择器 visible
 */
const matGetTicketSelectorVisible = ref(false)

/**
 * 仓库选择器 visible
 */
const warehouseSelectorVisible = ref(false)

const extraTitleConf = {
	name: ["扩展信息"],
	icon: ["fas", "square-share-nodes"]
}

const handleTabChange = (index: number) => {
	activatedTab.value = index

	if (index === 0) {
		fetchParam.value = {
			applyId: props.id || formData.value?.id,
			sord: "desc",
			sidx: "createdDate"
		}
		pageSize.value = 20
		handleQuery()
	}
}

const btnConf = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "保存草稿",
		icon: ["fas", "floppy-disk"]
	}
]

const submitBtnConf = computed(() => {
	return [
		{
			name: "提交审核",
			icon: ["fas", "circle-check"],
			disabled: props.mode === IModalType.create || tableData.value.length < 1
		}
	]
})

const btnLoading = ref(false)

const submitBtnLoading = ref(false)

/**
 * 表单实例
 */
const formRef = ref<FormInstance>()

/**
 * 表单 validator 规则
 */
const formRules: Record<string, FormItemRule> = {
	label: requiredValidator("退库申请名称"),
	preBusinessCode: requiredValidator("领料单号"),
	reason: requiredValidator("退库原因说明"),
	storeLabel: requiredValidator("入库仓库")
}

/**
 * 表单数据
 */
const formData = ref<MatReturnStoreApplyDTO | MatReturnStoreApplyVo>({
	amount_view: "￥0.00"
})

/**
 * 物资编码 & 领料数量的展示
 */
const gridPanelOptions = computed(() => {
	return [
		{
			label: "物资编码",
			value: formData.value.materialCodeNum ?? 0
		},
		{
			label: "退库数量",
			value: parseInt(formData.value.returnNum) || 0
		}
	]
})

/**
 * 表单项配置
 */
const formEls = computed<FormElementType[][]>(() => {
	return [
		[
			{
				label: "退库申请名称",
				name: "label",
				maxlength: inputMaxLength.input
			},
			{
				label: "选择领料单号",
				name: "preBusinessCode",
				type: "drawer",
				disabled: props.mode === IModalType.edit,
				clear: false,
				clickApi: () => (matGetTicketSelectorVisible.value = true)
			},
			{
				label: "入库仓库",
				name: "storeLabel",
				type: "drawer",
				disabled: props.mode === IModalType.edit,
				clear: false,
				clickApi: () => (warehouseSelectorVisible.value = true)
			},
			/**
			 * 申请人即创建人
			 */
			{
				label: "申请人",
				name: "createdBy_view",
				type: "input",
				disabled: true,
				clear: false
			},
			/**
			 * 申请人部门即创建人部门
			 */
			{
				label: "申请部门",
				name: "sysOrgId_view",
				type: "drawer",
				disabled: true,
				clear: false
			},
			/* {
				label: "退库金额（元）",
				name: "amount_view",
				append: "元",
				disabled: true
			}, */
			{
				label: "退库原因说明",
				name: "reason",
				type: "select",
				data: dictOptions.value["RETURN_REASON"]
			}
		]
	]
})
const tabsConf = ["物资明细", "相关附件"]

const tbBtnConf = [
	{
		name: "添加物资",
		icon: ["fas", "circle-plus"]
	}
]

const activatedTab = ref(0)

const queryArrList = computed<querySetting[]>(() => [
	{
		name: "物资编码",
		key: "materialCode",
		type: "input",
		placeholder: "请输入物资编码"
	},
	{
		name: "物资名称",
		key: "materialLabel",
		type: "input",
		placeholder: "请输入物资名称"
	},
	{
		name: "规格型号",
		key: "version",
		placeholder: "请输入规格型号",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资性质",
		key: "attribute",
		type: "select",
		children: dictOptions.value.MATERIAL_NATURE,
		placeholder: "请选择物资性质"
	}
])

const {
	currentPage,
	tableCache,
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	pageSize,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = useTbInit<
	MatReturnStoreApplyItemVo,
	Ref<MatReturnStoreApplyItemReqParams>
>()

fetchFunc.value = listWarehouseReturnApplyGoodsPaged

tableProp.value = [
	{
		prop: "materialCode",
		label: "物资编码"
	},
	{
		prop: "materialName",
		label: "物资名称"
	},
	{
		prop: "version",
		label: "规格型号"
	},
	{
		prop: "technicalParameter",
		label: "技术参数"
	},
	{
		prop: "attribute",
		label: "物资性质",
		needSlot: true,
		width: 120
	},
	{
		prop: "useUnit",
		label: "库存单位",
		needSlot: true,
		width: 80
	},
	{
		prop: "num_view",
		label: "领料出库数量",
		align: "right"
	},
	{
		prop: "canNum_view",
		label: "可退库数量",
		align: "right"
	},
	{
		prop: "completeNum",
		label: "退库数量",
		needSlot: true
	},
	{
		prop: "actions",
		label: "操作",
		width: 100,
		needSlot: true
	}
]

/**
 * 格式化数量
 */
watch(tableData, () => {
	batchFormatterNumView(tableData.value as any[])
})

function handleQuery(e?: any) {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = { ...fetchParam.value, ...e }
	fetchTableData()
}

/**
 * 物资选择器 table column 配置
 */
const goodsSelectorColumns: TableColumnType[] = [
	{
		prop: "materialCode",
		label: "物资编码"
	},
	{
		prop: "materialName",
		label: "物资名称"
	},

	{
		prop: "version",
		label: "规格型号"
	},
	{
		prop: "technicalParameter",
		label: "技术参数"
	},
	{
		prop: "attribute",
		label: "物资性质",
		needSlot: true,
		width: 120
	},
	{
		prop: "useUnit",
		label: "库存单位",
		needSlot: true,
		width: 80
	},
	{
		prop: "outStoreNum_view",
		label: "领料出库数量",
		align: "right"
	},
	{
		prop: "canNum_view",
		label: "可退库数量",
		align: "right"
	},
	{
		prop: "completeReturnNum_view",
		label: "已退库数量",
		align: "right"
	},
	{
		prop: "completeWasteOldNum_view",
		label: "已交旧数量",
		align: "right"
	}
]

const drawerLoading = ref(false)

const { userInfo } = storeToRefs(useUserStore())

onMounted(() => {
	getDictByCodeList(["INVENTORY_UNIT", "RETURN_REASON", "MATERIAL_NATURE"])

	fetchParam.value.applyId = props.id

	fetchParam.value.sord = "desc"
	fetchParam.value.sidx = "createdDate"

	if (props.mode === IModalType.edit) {
		getDetail()
		fetchTableData()
	} else {
		// 新建退库申请
		initApplyUserInfo()
	}
})

/**
 * 物资明细 table 行样式
 *
 * 库存不足物资，用红色标记该行
 */
const errorGoodsIdList = ref<number[]>([])
function tbCellClassName({ row }: any) {
	return includes(errorGoodsIdList.value, row.id) ? "error" : ""
}

/**
 * 获取详情数据
 */
async function getDetail() {
	drawerLoading.value = true
	try {
		const r = await getWarehouseReturnApply(props.id)
		formData.value = r
		formData.value.amount_view = toMoney(r.amount)

		oldFormData.value = JSON.stringify(formData.value)
	} finally {
		drawerLoading.value = false
	}
}

/**
 * 初始化申请人信息
 */
function initApplyUserInfo() {
	formData.value.createdBy_view = userInfo.value.realName as any
	formData.value.createdByName = userInfo.value.userName as any
	formData.value.sysOrgId = userInfo.value.orgId as any
	formData.value.sysOrgId_view = userInfo.value.orgName
}

/**
 * 处理保存草稿（包含新增和编辑）
 */
async function handleSaveDraft() {
	if (!formRef.value) {
		return
	}

	formRef.value.validate(async (valid) => {
		if (!valid) {
			return
		}
		btnLoading.value = true
		// 表单校验通过
		try {
			const api =
				props.mode === IModalType.create
					? saveWarehouseReturnApply
					: updateWarehouseReturnApply

			let idempotentToken = ""
			if (props.mode === IModalType.create) {
				idempotentToken = getIdempotentToken(
					IIdempotentTokenTypePre.apply,
					IIdempotentTokenType.warehouseReturnApply
				)
			}
			const r = await api(formData.value, idempotentToken)
			formData.value.id = r.id
			formData.value.materialCodeNum = r.materialCodeNum ?? 0
			formData.value.returnNum = r.returnNum ?? 0

			fetchParam.value.applyId = r.id!

			oldFormData.value = JSON.stringify(formData.value)
			emit("save", r.id, true)
			ElMessage.success("操作成功")
			handleQuery()
		} finally {
			btnLoading.value = false
		}
	})
}

/**
 * 处理提交审核
 *
 * 需要表单校验
 */
function handleSubmit() {
	if (!formRef.value) {
		return
	}

	formRef.value.validate(async (valid) => {
		if (!valid) {
			submitBtnLoading.value = false
			drawerLoading.value = false
			return
		}
		await showWarnConfirm("请确认是否提交本次数据？")

		submitBtnLoading.value = true
		drawerLoading.value = true
		try {
			// 如果主表有修改，则先更新主表数据
			if (oldFormData.value != JSON.stringify(formData.value)) {
				await updateWarehouseReturnApply(formData.value as any)
			}

			const idempotentToken = getIdempotentToken(
				IIdempotentTokenTypePre.other,
				IIdempotentTokenType.warehouseReturnApply,
				formData.value.id
			)

			const { code, data, msg } = await submitWarehouseReturnApply(
				formData.value.id,
				idempotentToken
			)

			if (data && code != 200) {
				errorGoodsIdList.value = data || []
				ElMessage.error(msg)
				handleQuery()
			} else {
				ElMessage.success("操作成功")
				emit("save", undefined)
			}
		} finally {
			submitBtnLoading.value = false
			drawerLoading.value = false
		}
	})
}

function handleDrawerBtnClick(name?: string) {
	if (name === "保存草稿") {
		return handleSaveDraft()
	}

	emit("close")
}

/**
 * 选择领料单 handler
 *
 * 选择领料单之后，同时选择单子中的仓库
 */
function handleSelectedMatGetTicket(e: any) {
	matGetTicketSelectorVisible.value = false
	formData.value.preBusinessId = e.id
	formData.value.preBusinessCode = e.code
	formData.value.storeId = e.storeId
	formData.value.storeLabel = e.storeLabel
	formData.value.storeType = e.storeType
}

/**
 * 删除物资
 */
async function handleDelGoods(e: any) {
	await showDelConfirm()
	await deleteWarehouseReturnApplyGoods(e.id)
	fetchTableData()
	getDetail()
	emit("update")
}

/**
 * 校验退库数量
 *
 * 编辑的退库数量 <= 可退库数量
 */
function validateWarehouseReturnNum(e: any) {
	const completeNum = toNumber(e.completeNum)
	const canNum = toNumber(e.canNum)

	e.completeNum = completeNum

	if (completeNum > canNum) {
		ElMessage.warning("退库数量不能大于可退库数量！")
		e.completeNum = canNum
	} else if (completeNum <= 0) {
		const oldRow = tableCache.find((v) => v.id == e.id)
		e.completeNum = oldRow.completeNum

		ElMessage.warning("退库数量不能小于等于0！")
		return
	}

	updateWarehouseReturnNum(e)
}

/**
 * 更新物资退库数量
 */
const updateWarehouseReturnNum = debounce(async (e: any) => {
	await updateWarehouseReturnApplyGoods({
		applyId: props.id,
		itemList: [
			{
				id: e.id,
				completeNum: e.completeNum
			}
		]
	})

	//  更新 tableCache
	const rowIdx = findIndex(tableCache, (v) => v.id === e.id)
	if (rowIdx !== -1) {
		tableCache.splice(rowIdx, 1, { ...e })
	}

	ElMessage.success("操作成功")
	fetchTableData()
	getDetail()
}, 300)

/**
 * 添加物资 handler
 */
async function handleAddGoods(e?: any) {
	const idempotentToken = getIdempotentToken(
		IIdempotentTokenTypePre.other,
		IIdempotentTokenType.warehouseReturnApply,
		formData.value.id
	)
	await addWarehouseReturnApplyGoods(
		{
			applyId: props.id,
			itemIdList: map(e, ({ id }) => id)
		},
		idempotentToken
	)

	ElMessage.success("操作成功")
	handleQuery()
	getDetail()
	emit("update")
	goodsSelectorVisible.value = false
}

/**
 * 选择仓库 handler
 */
function handleSelectWarehouse(btnName: string, v: any) {
	warehouseSelectorVisible.value = false

	if (btnName === "保存") {
		// 保存逻辑
		formData.value.storeId = v.id
		formData.value.storeLabel = v.label
	}
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.warehouse-return-apply-editor-table-wrapper {
	&:deep(.pitaya-table) {
		.error {
			.column-inner-item,
			.cell,
			.el-input__inner {
				color: red;
			}
		}
	}
}
</style>
