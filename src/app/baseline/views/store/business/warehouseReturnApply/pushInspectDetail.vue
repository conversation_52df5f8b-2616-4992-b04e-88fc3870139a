<!-- 退库申请：推送质检详情 -->
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<div class="drawer-column left" style="width: 300px">
			<Title :title="titleConf" />

			<el-descriptions size="small" :column="1" border class="content">
				<el-descriptions-item
					v-for="desc in descList"
					:key="desc.label"
					:label="desc.label"
				>
					{{ getNumDefByNumKey(desc.key, applyData?.[desc.key]) }}
				</el-descriptions-item>
			</el-descriptions>
		</div>
		<div class="drawer-column right" style="width: calc(100% - 300px)">
			<div class="rows">
				<Title :title="exTitleConf">
					<Tabs
						style="margin-right: auto"
						:tabs="['物资明细', '相关附件']"
						@on-tab-change="activatedTab = $event"
					/>
				</Title>

				<!-- 物资详情 table -->
				<goods-detail-table
					v-if="activatedTab === 0"
					:user-options-api="listInspectorPaged"
					:list-goods-api="listWarehouseReturnApplyGoodsPaged"
					:add-inspector-api="addWarehouseReturnApplyInspector"
					:apply-id="id"
					:mode="mode"
					:columns="tableColumnsConf"
					:tb-cell-class-name="tbCellClassName"
					ref="matTableRef"
				/>
				<!-- 相关附件 table -->
				<table-file
					v-else
					:business-type="fileBusinessType.warehouseReturnApply"
					:business-id="id"
					:mod="mode !== IModalType.view ? IModalType.edit : mode"
				/>
			</div>

			<button-list
				class="footer"
				:button="drawerBtnConf"
				:loading="drawerBtnLoading"
				@on-btn-click="handleDrawerBtnClick"
			/>
		</div>
	</div>
</template>

<script setup lang="ts">
import { MatInStoreApplyVo } from "@/app/baseline/utils/types/warehousing-apply"
import tableFile from "../../../components/tableFile.vue"
import { fileBusinessType } from "@/app/baseline/api/dict"
import {
	IIdempotentTokenType,
	IIdempotentTokenTypePre,
	IModalType
} from "@/app/baseline/utils/types/common"
import goodsDetailTable from "../../components/goodsDetailTable.vue"
import {
	getWarehouseReturnApply,
	addWarehouseReturnApplyInspector,
	listWarehouseReturnApplyGoodsPaged,
	pushWarehouseReturnInspect
} from "@/app/baseline/api/store/warehouse-return-apply-api"
import { listInspectorPaged } from "@/app/baseline/api/system"
import { getNumDefByNumKey } from "@/app/baseline/utils"
import { includes } from "lodash-es"
import { getIdempotentToken } from "@/app/baseline/utils/validate"

const props = withDefaults(
	defineProps<{
		/**
		 * 申请id
		 */
		id: any

		/**
		 * 模式，编辑/查看
		 */
		mode?: IModalType
	}>(),
	{ mode: IModalType.view }
)

const emit = defineEmits<{
	(e: "close"): void
	(e: "save"): void
}>()

const matTableRef = ref()

const drawerBtnLoading = ref(false)

const titleConf = {
	name: ["退库单信息"],
	icon: ["fas", "square-share-nodes"]
}

const exTitleConf = {
	name: ["扩展信息"],
	icon: ["fas", "square-share-nodes"]
}

const drawerBtnConf = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "推送质检",
		icon: ["fas", "stamp"]
	}
]

const tableColumnsConf: TableColumnType[] = [
	{
		prop: "materialCode",
		label: "物资编码"
	},
	{
		prop: "materialName",
		label: "物资名称"
	},
	{
		prop: "version",
		label: "规格型号"
	},
	{
		prop: "technicalParameter",
		label: "技术参数"
	},
	{
		prop: "attribute",
		label: "物资性质",
		needSlot: true,
		width: 120
	},
	{
		prop: "useUnit",
		label: "库存单位",
		needSlot: true,
		width: 90
	},
	{
		prop: "num_view",
		label: "领料出库数量",
		align: "right"
	},
	/* {
		prop: "canNum",
		label: "可退库数量"
	}, */
	{
		prop: "completeNum_view",
		label: "退库数量",
		align: "right"
	},
	{
		prop: "inspectionPersonId_view",
		label: "质检人员"
	}
]

const activatedTab = ref(0)

const drawerLoading = ref()

const applyData = ref<MatInStoreApplyVo>()

const descList = ref([
	{
		label: "退库单号",
		key: "code"
	},
	{
		label: "关联领料单号",
		key: "preBusinessCode"
	},

	{
		label: "入库仓库名称",
		key: "storeLabel"
	},
	{
		label: "物资编码",
		key: "materialCodeNum"
	},
	/* {
		label: "退库数量",
		key: "returnNum"
	}, */
	{
		label: "申请时间",
		key: "createdDate"
	},
	{
		label: "退库原因说明",
		key: "reason"
	}
])

/**
 * 物资明细 table 行样式
 *
 * 库存物资异常，用红色标记该行
 */
const errorGoodsIdList = ref<number[]>([])
function tbCellClassName({ row }: any) {
	return includes(errorGoodsIdList.value, row.id) ? "error" : ""
}

onMounted(() => {
	getDetail()
})

function getDetail() {
	getWarehouseReturnApply(props.id).then((r) => (applyData.value = r))
}

async function handleDrawerBtnClick(name?: string) {
	if (name === "取消") {
		emit("close")
		return
	}

	drawerBtnLoading.value = true
	// 推送质检逻辑
	try {
		const idempotentToken = getIdempotentToken(
			IIdempotentTokenTypePre.other,
			IIdempotentTokenType.warehouseReturnApply,
			props.id
		)
		const { code, msg, data } = await pushWarehouseReturnInspect(
			props.id,
			idempotentToken
		)
		if (data && code != 200) {
			errorGoodsIdList.value = data || []
			ElMessage.error(msg)
			matTableRef.value?.getTableData()
		} else {
			ElMessage.success("操作成功")
			emit("save")
		}
	} finally {
		drawerBtnLoading.value = false
	}
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
