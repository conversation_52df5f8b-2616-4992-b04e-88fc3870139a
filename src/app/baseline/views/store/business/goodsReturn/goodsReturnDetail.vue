<!-- 退库申请-详情 -->
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<div class="drawer-column left" style="width: 310px">
			<Title :title="titleConf" />
			<el-scrollbar class="rows">
				<grid-panel :options="gridPanelConf" />

				<!-- 明细信息 -->
				<el-descriptions size="small" :column="1" border class="content">
					<el-descriptions-item
						v-for="desc in descOptions"
						:key="desc.label"
						:label="desc.label"
					>
						<!-- 退货金额（元） -->
						<cost-tag v-if="desc.key === 'amount'" :value="detailData.amount" />
						<span v-else>
							{{ getNumDefByNumKey(desc.key, detailData?.[desc.key]) }}
						</span>
					</el-descriptions-item>
				</el-descriptions>
			</el-scrollbar>
		</div>

		<!-- 扩展信息 -->
		<div
			class="drawer-column right"
			style="width: calc(100% - 310px)"
			:class="{ pdr10: !footerBtnVisible }"
		>
			<Title :title="extraTitleConf">
				<Tabs
					:tabs="tabsConf"
					style="margin-right: auto; margin-left: 30px"
					@on-tab-change="handleTabChange"
				/>
			</Title>

			<el-scrollbar class="rows">
				<!-- 扩展信息内容 -->
				<Query
					:query-arr-list="(queryConf as any)"
					style="margin: 10px 10px -10px"
					@get-query-data="handleQuery"
					v-if="activatedTab === 0"
				/>
				<pitaya-table
					v-if="activatedTab === 0"
					ref="tableRef"
					:columns="tableProp"
					:table-data="tableData"
					:need-selection="false"
					:single-select="false"
					:need-index="true"
					:need-pagination="true"
					:total="pageTotal"
					:table-loading="tableLoading"
					@on-selection-change="selectedTableList = $event"
					@on-current-page-change="onCurrentPageChange"
				>
					<!-- 物资性质 -->
					<template #attribute="{ rowData }">
						<dict-tag
							:options="DictApi.getMatAttr()"
							:value="rowData.attribute"
						/>
					</template>

					<!-- 库存单位 -->
					<template #useUnit="{ rowData }">
						{{
							dictFilter("INVENTORY_UNIT", rowData.useUnit)?.subitemName ||
							"---"
						}}
					</template>
					<!-- 退货申请编辑：库存单价 -->
					<template #price="{ rowData }">
						<cost-tag :value="rowData.price" />
					</template>
				</pitaya-table>

				<table-file
					v-else
					:business-type="fileBusinessType.goodsReturnApply"
					:business-id="id"
					:mod="
						footerBtnVisible && detailData.bpmStatus === appStatus.approved
							? IModalType.edit
							: IModalType.view
					"
				/>
			</el-scrollbar>

			<button-list
				v-if="footerBtnVisible"
				class="footer"
				:button="btnConf"
				@on-btn-click="emit('close')"
			/>
		</div>
	</div>
</template>

<script setup lang="ts">
import { DictApi, fileBusinessType, appStatus } from "@/app/baseline/api/dict"
import DictTag from "@/app/baseline/views/components/dictTag.vue"

import CostTag from "../../../components/costTag.vue"
import { IModalType } from "@/app/baseline/utils/types/common"
import { useTbInit } from "../../../components/tableBase"
import TableFile from "../../../components/tableFile.vue"
import { useDictInit } from "../../../components/dictBase"
import {
	getGoodsReturnApply,
	listGoodsReturnApplyGoodsPaged
} from "@/app/baseline/api/store/goods-return-apply-api"
import {
	MatOutStoreRemoveApplyItemVo,
	MatOutStoreRemoveApplyVo
} from "@/app/baseline/utils/types/goods-return-apply"
import GridPanel from "../../components/gridPanel.vue"
import {
	toFixedTwo,
	getNumDefByNumKey,
	batchFormatterNumView
} from "@/app/baseline/utils"

const props = withDefaults(
	defineProps<{
		/**
		 * 领料申请id
		 */
		id?: number

		footerBtnVisible?: boolean
	}>(),
	{ footerBtnVisible: true }
)

const emit = defineEmits<{
	(e: "close"): void
}>()

// 退货申请编辑： 物资编码&退货数量 set
const gridPanelConf = computed(() => [
	{
		label: "物资编码",
		value: detailData.value.materialCodeNum ?? 0
	},
	{
		label: "退货数量",
		value: parseInt(detailData.value.num as any) || 0
	}
])

const btnConf = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	}
]

const titleConf = computed(() => ({
	name: ["退货申请"],
	icon: ["fas", "square-share-nodes"]
}))

const extraTitleConf = {
	name: ["扩展信息"],
	icon: ["fas", "square-share-nodes"]
}

/**
 * 明细配置
 */
const descOptions = [
	{ label: "退货单号", key: "code" },
	{ label: "退货单名称", key: "label" },
	{ label: "物资所在仓库名称", key: "storeLabel" },
	{ label: "物资所在货位名称", key: "roomLabel" },
	{ label: "退货金额（元）", key: "amount" },
	{ label: "物资编码", key: "materialCodeNum" },
	{ label: "退货数量", key: "num_view" }
]

const tabsConf = ["物资明细", "相关附件"]

const activatedTab = ref(0)

const handleTabChange = (index: number) => {
	activatedTab.value = index

	if (index === 0) {
		fetchParam.value = {
			applyId: props.id || detailData.value?.id,
			sord: "desc",
			sidx: "createdDate"
		}
		pageSize.value = 20
		handleQuery()
	}
}

const { dictOptions, dictFilter, getDictByCodeList } = useDictInit()

/**
 * 详情数据
 */
const detailData = ref<MatOutStoreRemoveApplyVo>({})

const drawerLoading = ref(false)

const queryConf = computed(() => [
	{
		name: "物资编码",
		key: "materialCode",
		type: "input",
		placeholder: "请输入物资编码"
	},
	{
		name: "物资名称",
		key: "materialLabel",
		type: "input",
		placeholder: "请输入物资名称"
	},
	{
		name: "规格型号",
		key: "version",
		placeholder: "请输入规格型号",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资性质",
		key: "attribute",
		type: "select",
		children: dictOptions.value.MATERIAL_NATURE,
		placeholder: "请选择物资性质"
	}
])

const {
	currentPage,
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	pageSize,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = useTbInit()

fetchFunc.value = listGoodsReturnApplyGoodsPaged

tableProp.value = [
	{
		prop: "materialCode",
		label: "物资编码",
		width: 130,
		fixed: "left"
	},
	{
		prop: "materialName",
		label: "物资名称",
		minWidth: 120
	},
	{
		prop: "version",
		label: "规格型号",
		minWidth: 120
	},
	{
		prop: "technicalParameter",
		label: "技术参数",
		minWidth: 120
	},
	{
		prop: "attribute",
		label: "物资性质",
		needSlot: true,
		width: 120
	},
	{
		prop: "useUnit",
		label: "采购单位",
		needSlot: true,
		width: 80
	},
	{
		prop: "batchNo",
		label: "批次号",
		minWidth: 160
	},
	{
		prop: "storeNum",
		label: "库存数量",
		align: "right"
	},
	{
		prop: "freezeNum",
		label: "冻结量",
		align: "right"
	},
	{
		prop: "purchaseOrderCode",
		label: "采购订单号",
		minWidth: 180
	},
	{
		prop: "supplierName",
		label: "供应商名称",
		minWidth: 120
	},
	{
		prop: "price",
		label: "采购单价",
		needSlot: true,
		align: "right",
		minWidth: 100
	},
	{
		prop: "orderNum",
		label: "配送数量",
		align: "right"
	},
	{
		prop: "completeNum",
		label: "退货数量",
		align: "right",
		fixed: "right"
	},
	{
		prop: "remark",
		label: "退货原因",
		width: 150,
		fixed: "right"
	}
]

/**
 * 格式化数量
 */
watch(tableData, () => {
	batchFormatterNumView(tableData.value as MatOutStoreRemoveApplyItemVo[])
})

function handleQuery(e?: any) {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = { ...fetchParam.value, ...e }
	fetchTableData()
}

onMounted(() => {
	fetchParam.value.applyId = props.id
	fetchParam.value.sord = "desc"
	fetchParam.value.sidx = "createdDate"

	getDictByCodeList(["INVENTORY_UNIT", "MATERIAL_NATURE"])
	getDetail()
	fetchTableData()
})

/**
 * 获取详情数据
 */
async function getDetail() {
	drawerLoading.value = true
	try {
		detailData.value = await getGoodsReturnApply(props.id)
		detailData.value.num_view = toFixedTwo(detailData.value.num)
	} finally {
		drawerLoading.value = false
	}
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
