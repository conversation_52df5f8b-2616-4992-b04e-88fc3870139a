<!-- 退货申请-编辑 -->
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<div class="drawer-column left" style="width: 310px">
			<Title :title="titleConf" />

			<el-scrollbar class="rows">
				<grid-panel :options="gridPanelConf" />

				<el-form
					ref="formRef"
					class="content goods-return-apply-form"
					:model="formData"
					:rules="formRules"
					label-position="top"
					label-width="100px"
				>
					<form-element :form-element="formEls" :form-data="formData" />
				</el-form>
			</el-scrollbar>

			<button-list
				class="footer"
				:button="btnConf"
				:loading="btnLoading"
				@on-btn-click="handleDrawerLeftBtnClick"
			/>
		</div>

		<!-- 扩展信息 -->
		<div
			class="drawer-column right"
			:class="mode === IModalType.create ? 'disabled' : ''"
			style="width: calc(100% - 310px)"
		>
			<Title :title="extraTitleConf">
				<Tabs
					:tabs="tabsConf"
					style="margin-right: auto; margin-left: 30px"
					@on-tab-change="handleTabChange"
				/>
			</Title>

			<el-scrollbar
				v-if="mode === IModalType.edit"
				class="rows goods-return-apply-editor-table-wrapper"
			>
				<Query
					:query-arr-list="(queryConf as any)"
					style="margin: 10px 10px -10px"
					@get-query-data="handleQuery"
					v-if="activatedTab === 0"
				/>
				<pitaya-table
					v-if="activatedTab === 0"
					ref="tableRef"
					:columns="tableProp"
					:table-data="tableData"
					:need-selection="true"
					:single-select="false"
					:need-index="true"
					:need-pagination="true"
					:cell-class-name="tbCellClassName"
					:total="pageTotal"
					:table-loading="tableLoading"
					@on-selection-change="selectedTableList = $event"
					@on-current-page-change="onCurrentPageChange"
				>
					<!-- 物资性质 -->
					<template #attribute="{ rowData }">
						<dict-tag
							:options="DictApi.getMatAttr()"
							:value="rowData.attribute"
						/>
					</template>

					<!-- 配送数量 -->
					<template #orderNum="{ rowData }">
						{{ toFixedTwo(rowData.orderNum) }}
					</template>

					<!-- 冻结数量 -->
					<template #freezeNum="{ rowData }">
						{{ toFixedTwo(rowData.freezeNum) }}
					</template>

					<!--  库存数量 -->
					<template #storeNum="{ rowData }">
						{{ toFixedTwo(rowData.storeNum) }}
					</template>

					<!-- 库存单位 -->
					<template #useUnit="{ rowData }">
						{{
							dictFilter("INVENTORY_UNIT", rowData.useUnit)?.subitemName ||
							"---"
						}}
					</template>
					<!-- 库存单价 -->
					<template #price="{ rowData }">
						<cost-tag :value="rowData.price" />
					</template>
					<!-- 退货数量编辑 -->
					<template #completeNum="{ rowData }">
						<el-input
							class="no-arrows"
							v-model="rowData.completeNum"
							@click.stop
							@input="rowData.completeNum = validateAndCorrectInput($event)"
							@change="validateGoodsReturnNum(rowData)"
						/>
					</template>

					<!-- 退货原因编辑 -->
					<template #remark="{ rowData }">
						<el-input
							v-model="rowData.remark"
							@click.stop
							@change="handleInputBlur(rowData)"
							maxlength="50"
							show-word-limit
						/>
					</template>

					<template #actions="{ rowData }">
						<el-button v-btn link @click.stop="delGoods(rowData)">
							<font-awesome-icon
								:icon="['fas', 'trash-can']"
								style="color: var(--pitaya-btn-background)"
							/>
							<span class="table-inner-btn">移除</span>
						</el-button>
					</template>

					<template #footerOperateLeft>
						<button-list
							:button="tbBtnConf"
							:is-not-radius="true"
							:loading="submitBtnLoading"
							@on-btn-click="handleTableAction"
						/>
					</template>
				</pitaya-table>

				<!-- 相关附件 -->
				<table-file
					v-else
					:business-type="fileBusinessType.goodsReturnApply"
					:business-id="id"
					:mod="IModalType.edit"
				/>
			</el-scrollbar>

			<button-list
				v-if="mode === IModalType.edit"
				class="footer"
				:button="submitBtnConf"
				:loading="submitBtnLoading"
				@on-btn-click="handleSubmit"
			/>
		</div>

		<Drawer
			v-model:drawer="goodsSelectorVisible"
			:size="modalSize.lg"
			destroy-on-close
		>
			<mat-selector
				multiple
				:columns="goodsSelectorColumns"
				:table-req-params="{ applyId: id }"
				:table-api="listGoodsReturnApplyGoodsOptionsPaged"
				:query-arr-list="matQueryConf"
				@save="handleAddGoods"
				@close="goodsSelectorVisible = false"
			/>
		</Drawer>

		<!-- 选择仓库 -->
		<Drawer
			v-model:drawer="storeSelectorVisible"
			:size="modalSize.lg"
			destroy-on-close
		>
			<!-- 标准库 、 危险品仓库 -->
			<store-table
				:selected-ids="[formData.storeId]"
				@onSave="handleSelectStore"
				:table-api-params="{
					type: `${IWarehouseType.default},${IWarehouseType.dangerousWarehouse}`
				}"
			/>
		</Drawer>

		<!-- 选择货位 -->
		<Drawer
			v-model:drawer="roomSelectorVisible"
			:size="modalSize.lg"
			destroy-on-close
		>
			<area-storage
				:store-id="formData.storeId!"
				:store-name="formData.storeLable!"
				:store-code="formData.storeCode"
				:region-id="formData.regionId"
				:room-id="[formData.roomId]"
				@onSaveOrClose="handleSelectoreRoom"
			/>
		</Drawer>
	</div>
</template>

<script setup lang="ts">
import { getModalTypeLabel, toFixedTwo, toMoney } from "@/app/baseline/utils"
import {
	IIdempotentTokenType,
	IIdempotentTokenTypePre,
	IModalType
} from "@/app/baseline/utils/types/common"
import { FormItemRule, FormInstance } from "element-plus"
import { FormElementType } from "../../../components/define"
import FormElement from "../../../components/formElement.vue"
import GridPanel from "../../components/gridPanel.vue"
import CostTag from "../../../components/costTag.vue"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import { useMessageBoxInit } from "../../../components/messageBox"
import { useTbInit } from "../../../components/tableBase"
import MatSelector from "../../components/matSelector.vue"
import { inputMaxLength, modalSize } from "@/app/baseline/utils/layout-config"
import { findIndex, includes, map, round, toNumber } from "lodash-es"
import { DictApi, fileBusinessType } from "@/app/baseline/api/dict"
import { useDictInit } from "../../../components/dictBase"
import TableFile from "../../../components/tableFile.vue"
import {
	addGoodsReturnApplyGoods,
	deleteGoodsReturnApplyGoods,
	getGoodsReturnApply,
	listGoodsReturnApplyGoodsOptionsPaged,
	listGoodsReturnApplyGoodsPaged,
	saveGoodsReturnApply,
	submitGoodsReturnApply,
	updateGoodsReturnApply,
	updateGoodsReturnApplyGoodsNum
} from "@/app/baseline/api/store/goods-return-apply-api"
import { PurchaseOrderVo } from "../../../purchase/order/orderPage"
import {
	MatOutStoreRemoveApplyDTO,
	MatOutStoreRemoveApplyItemReqParams,
	MatOutStoreRemoveApplyItemVo
} from "@/app/baseline/utils/types/goods-return-apply"
import {
	getIdempotentToken,
	requiredValidator,
	validateAndCorrectInput
} from "@/app/baseline/utils/validate"
import { useApplyResultUtils } from "../../hooks/apply-result-utils"
import storeTable from "../../components/storeTable.vue"
import areaStorage from "./areaStorage.vue"
import {
	MatStoreRegionDTO,
	MatStoreRegionTreeVo,
	IWarehouseType
} from "../../../../utils/types/store-manage"

const props = defineProps<{
	/**
	 * 业务id
	 */
	id: any

	/**
	 * 编辑模式：编辑/新建
	 */
	mode: IModalType
}>()

const emit = defineEmits<{
	(e: "update"): void
	(e: "close"): void
	/**
	 * 保存事件
	 *
	 * @param id 主业务id
	 * @param visible 编辑 drawer visible
	 */
	(e: "save", id: any, visible?: boolean): void
}>()

const { dictOptions, dictFilter, getDictByCodeList } = useDictInit()

const { showDelConfirm, showWarnConfirm } = useMessageBoxInit()

/**
 * 保存旧的表单数据
 */
const oldFormData = ref<string>()

/**
 * 编辑后的table row 数据
 */
const editedTableRowStack = ref<any[]>([])

/**
 * 仓库选择器 visible
 */
const storeSelectorVisible = ref(false)

/**
 * 货位选择器 visible
 */
const roomSelectorVisible = ref(false)

const titleConf = computed(() => ({
	name: [getModalTypeLabel(props.mode, "退货申请")],
	icon: ["fas", "square-share-nodes"]
}))

const extraTitleConf = {
	name: ["扩展信息"],
	icon: ["fas", "square-share-nodes"]
}

const handleTabChange = (index: number) => {
	activatedTab.value = index

	if (index === 0) {
		fetchParam.value = {
			applyId: props.id || formData.value?.id,
			sord: "desc",
			sidx: "createdDate"
		}
		pageSize.value = 20
		handleQuery()
	}
}

const btnConf = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "保存草稿",
		icon: ["fas", "floppy-disk"]
	}
]

const submitBtnConf = computed(() => [
	{
		name: "保存",
		icon: ["fas", "file-signature"],
		disabled:
			props.mode === IModalType.create || editedTableRowStack.value.length < 1
	},
	{
		name: "提交审核",
		icon: ["fas", "circle-check"],
		disabled: props.mode === IModalType.create || tableData.value.length < 1
	}
])

const btnLoading = ref(false)

const submitBtnLoading = ref(false)

/**
 * 表单实例
 */
const formRef = ref<FormInstance>()

/**
 * 表单 validator 规则
 */
const formRules: Record<string, FormItemRule> = {
	label: requiredValidator("退货单名称"),
	storeLabel: requiredValidator("物资所在仓库"),
	roomLabel: requiredValidator("物资所在货位")
}

/**
 * 表单数据
 */
const formData = ref<PurchaseOrderVo & MatOutStoreRemoveApplyDTO>({
	/* amount_view: "￥0.00" as any */
})

// 退货申请编辑： 物资编码&退货数量 set
const gridPanelConf = computed(() => [
	{
		label: "物资编码",
		value: formData.value.materialCodeNum ?? 0
	},
	{
		label: "退货数量",
		value: parseInt(formData.value.num) || 0
	}
])

/**
 * 表单项配置
 */
const formEls = computed<FormElementType[][]>(() => {
	return [
		[
			{
				label: "退货单名称",
				name: "label",
				maxlength: inputMaxLength.input
			},
			{
				label: "物资所在仓库",
				name: "storeLabel",
				vname: "storeId",
				type: "drawer",
				disabled: props.mode === IModalType.edit,
				clickApi: () => (storeSelectorVisible.value = true)
			},
			{
				label: "物资所在货位",
				name: "roomLabel",
				vname: "roomId",
				type: "drawer",
				disabled: props.mode === IModalType.edit,
				clickApi: () => {
					if (!formData.value.storeId) {
						ElMessage.warning("请先选择物资所在仓库！")
						return false
					}
					roomSelectorVisible.value = true
				}
			},

			{
				label: "退货金额（元）",
				name: "amount_view",
				disabled: true
			}
		]
	]
})

const tabsConf = ["物资明细", "相关附件"]

const tbBtnConf = computed(() => [
	{
		name: "添加物资",
		icon: ["fas", "circle-plus"]
	},
	{
		name: "批量移除",
		icon: ["fas", "trash-can"],
		disabled: selectedTableList.value.length < 1
	}
])

const activatedTab = ref(0)

const queryConf = computed(() => [
	{
		name: "物资编码",
		key: "materialCode",
		type: "input",
		placeholder: "请输入物资编码"
	},
	{
		name: "物资名称",
		key: "materialLabel",
		type: "input",
		placeholder: "请输入物资名称"
	},
	{
		name: "规格型号",
		key: "version",
		placeholder: "请输入规格型号",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资性质",
		key: "attribute",
		type: "select",
		children: dictOptions.value.MATERIAL_NATURE,
		placeholder: "请选择物资性质"
	}
])

const {
	currentPage,
	tableCache,
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	pageSize,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = useTbInit<
	MatOutStoreRemoveApplyItemVo,
	Ref<MatOutStoreRemoveApplyItemReqParams>
>()

const formDataId = computed(() => {
	return formData.value.id
})
const {
	handleApplyResultByCode,
	errorGoodsList,
	clearTimer,
	cancelRollPolling
} = useApplyResultUtils({
	fetchSubmitApi: submitGoodsReturnApply,
	fetchTableData: handleQuery,
	id: formDataId,
	successCb: () => {
		emit("save", undefined)
	}
})

fetchFunc.value = listGoodsReturnApplyGoodsPaged

tableProp.value = [
	{
		prop: "materialCode",
		label: "物资编码",
		width: 130,
		fixed: "left"
	},
	{
		prop: "materialName",
		label: "物资名称",
		minWidth: 120
	},
	{
		prop: "version",
		label: "规格型号",
		minWidth: 120
	},
	{
		prop: "technicalParameter",
		label: "技术参数",
		minWidth: 120
	},
	{
		prop: "attribute",
		label: "物资性质",
		needSlot: true,
		width: 120
	},
	{
		prop: "useUnit",
		label: "采购单位",
		needSlot: true,
		width: 80
	},
	{
		prop: "batchNo",
		label: "批次号",
		minWidth: 160
	},
	{
		prop: "storeNum",
		label: "库存数量",
		align: "right",
		needSlot: true
	},
	{
		prop: "freezeNum",
		label: "冻结量",
		align: "right",
		needSlot: true
	},
	{
		prop: "purchaseOrderCode",
		label: "采购订单号",
		minWidth: 180
	},
	{
		prop: "supplierName",
		label: "供应商名称",
		minWidth: 120
	},
	{
		prop: "price",
		label: "采购单价",
		needSlot: true,
		align: "right",
		minWidth: 100
	},
	{
		prop: "orderNum",
		label: "配送数量",
		align: "right",
		needSlot: true
	},
	{
		prop: "completeNum",
		label: "退货数量",
		needSlot: true,
		width: 150,
		fixed: "right"
	},
	{
		prop: "remark",
		label: "退货原因",
		needSlot: true,
		width: 150,
		fixed: "right"
	},
	{
		prop: "actions",
		label: "操作",
		width: 100,
		needSlot: true,
		fixed: "right"
	}
]

function handleQuery(e?: any) {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = { ...fetchParam.value, ...e }
	fetchTableData()
}

/**
 * 物资选择器 table 列配置
 */
const goodsSelectorColumns: TableColumnType[] = [
	{
		prop: "materialCode",
		label: "物资编码",
		width: 130
	},
	{
		prop: "materialName",
		label: "物资名称",
		minWidth: 120
	},
	{
		prop: "version",
		label: "规格型号",
		minWidth: 120
	},
	{
		prop: "technicalParameter",
		label: "技术参数",
		minWidth: 120
	},
	{
		prop: "attribute",
		label: "物资性质",
		needSlot: true,
		width: 120
	},
	{
		prop: "useUnit",
		label: "采购单位",
		needSlot: true,
		width: 80
	},
	{
		prop: "batchNo",
		label: "批次号",
		minWidth: 160
	},
	{
		prop: "storeNum_view",
		label: "库存数量",
		align: "right"
	},
	{
		prop: "freezeNum_view",
		label: "冻结量",
		align: "right"
	},
	{
		prop: "purchaseOrderCode",
		label: "采购订单号",
		width: 160
	},
	{
		prop: "amount",
		label: "采购单价",
		needSlot: true,
		align: "right",
		minWidth: 120
	},
	{
		prop: "orderNum_view",
		label: "配送数量",
		align: "right",
		minWidth: 100
	},
	{
		prop: "supplierName",
		label: "供应商名称",
		minWidth: 120
	}
]

/**
 * 物资选择器 查询条件 配置
 */
const matQueryConf = computed(() => [
	{
		name: "物资编码",
		key: "materialCode",
		type: "input",
		placeholder: "请输入物资编码"
	},
	{
		name: "物资名称",
		key: "materialLabel",
		type: "input",
		placeholder: "请输入物资名称"
	},
	{
		name: "规格型号",
		key: "version",
		placeholder: "请输入规格型号",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资性质",
		key: "attribute",
		type: "select",
		children: dictOptions.value.MATERIAL_NATURE,
		placeholder: "请选择物资性质"
	},
	{
		name: "采购订单号",
		key: "purchaseOrderCode",
		type: "input",
		placeholder: "请输入采购订单号"
	},
	{
		name: "供应商名称",
		key: "supplierName",
		type: "input",
		placeholder: "请输入供应商名称"
	}
])

const goodsSelectorVisible = ref(false)

const drawerLoading = ref(false)

onMounted(() => {
	fetchParam.value.applyId = props.id
	fetchParam.value.sord = "desc"
	fetchParam.value.sidx = "createdDate"

	getDictByCodeList(["INVENTORY_UNIT", "MATERIAL_NATURE"])

	if (props.mode === IModalType.edit) {
		getDetail()
		fetchTableData()
	}
})

onUnmounted(() => {
	clearTimer()
	cancelRollPolling()
})

/**
 * 获取详情数据
 */
async function getDetail() {
	drawerLoading.value = true
	try {
		const r = await getGoodsReturnApply(props.id)
		formData.value = r

		formData.value.amount_view = toMoney(r.amount)

		oldFormData.value = JSON.stringify(formData.value)
	} finally {
		drawerLoading.value = false
	}
}

/**
 * 选择仓库 handler
 */
function handleSelectStore(btnName: string, e: any) {
	if (btnName == "保存") {
		if (e.id !== formData.value.storeId) {
			formData.value.roomId = null
			formData.value.regionLabel = ""
			formData.value.roomLabel = ""
			formData.value.roomId = null
		}
		formData.value.storeId = e.id
		formData.value.storeLabel = e.label
		formData.value.storeCode = e.code
	}

	storeSelectorVisible.value = false
}

/**
 * 选择货位 handler
 */
function handleSelectoreRoom(
	btnName: string,
	treeRow?: MatStoreRegionTreeVo,
	row?: MatStoreRegionDTO
) {
	if (btnName == "保存") {
		// 区域
		formData.value.regionLabel = treeRow?.label
		formData.value.regionId = treeRow?.id

		// 货位
		formData.value.roomLabel = row?.code // 货位label
		formData.value.roomId = row?.id // 货位ID
	}

	roomSelectorVisible.value = false
}

/**
 * 处理保存草稿（包含新增和编辑）
 */
async function handleSaveDraft() {
	if (!formRef.value) {
		return
	}

	formRef.value.validate(async (valid) => {
		if (!valid) {
			return
		}
		btnLoading.value = true
		// 表单校验通过
		try {
			const api =
				props.mode === IModalType.create
					? saveGoodsReturnApply
					: updateGoodsReturnApply

			let idempotentToken = ""
			if (props.mode === IModalType.create) {
				idempotentToken = getIdempotentToken(
					IIdempotentTokenTypePre.apply,
					IIdempotentTokenType.goodsReturnApply
				)
			}

			const r = await api(formData.value, idempotentToken)

			formData.value.id = r.id
			formData.value.materialCodeNum = r.materialCodeNum ?? 0
			formData.value.num = r.num ?? 0
			formData.value.amount_view = toMoney(r.amount)

			fetchParam.value.applyId = r.id!

			oldFormData.value = JSON.stringify(formData.value)

			ElMessage.success("操作成功")
			emit("save", r.id, true)
			handleQuery()
		} finally {
			btnLoading.value = false
		}
	})
}

/**
 * 处理提交审核
 *
 * 需要表单校验
 */
function handleSubmit(btnName?: string) {
	if (btnName === "提交审核") {
		if (!formRef.value) {
			return
		}

		formRef.value.validate(async (valid) => {
			if (!valid) {
				return
			}

			await showWarnConfirm("请确认是否提交本次数据？")

			submitBtnLoading.value = true
			drawerLoading.value = true

			try {
				// 如果主表有修改，则先更新主表数据
				if (oldFormData.value != JSON.stringify(formData.value)) {
					await updateGoodsReturnApply(formData.value as any)
				}

				if (editedTableRowStack.value.length > 0) {
					const items = map(editedTableRowStack.value, (v) => ({
						id: v.id,
						completeNum: v.completeNum,
						remark: v.remark
					}))

					await updateGoodsReturnApplyGoodsNum({
						applyId: props.id,
						itemList: items
					})

					editedTableRowStack.value = []
				}

				const idempotentToken = getIdempotentToken(
					IIdempotentTokenTypePre.other,
					IIdempotentTokenType.goodsReturnApply,
					formData.value.id
				)

				const { code, msg, data } = await submitGoodsReturnApply(
					formData.value.id,
					idempotentToken
				)
				if (data && code != 200) {
					handleApplyResultByCode(code, msg, data)
				} else {
					ElMessage.success("操作成功")
					emit("save", undefined)
				}
			} finally {
				submitBtnLoading.value = false
				drawerLoading.value = false
			}
		})
	} else if (btnName === "保存") {
		updateGoodsReturnNum()
	}
}

/**
 * 物资明细 table 行样式
 *
 * 库存不足物资，用红色标记该行
 */
function tbCellClassName({ row }: any) {
	return includes(errorGoodsList.value, row.id) ? "error" : ""
}

function handleDrawerLeftBtnClick(name?: string) {
	if (name === "保存草稿") {
		return handleSaveDraft()
	}

	emit("close")
}

/**
 * 添加物资 && 批量移除 操作
 * @param btnName
 */
async function handleTableAction(btnName?: string) {
	if (btnName == "添加物资") {
		goodsSelectorVisible.value = true
	} else if (btnName == "批量移除") {
		const ids = map(selectedTableList.value, ({ id }) => id)

		for (let i = 0; i < ids.length; i++) {
			const rowIdx = findIndex(
				editedTableRowStack.value,
				(v) => v.id === ids[i]
			)
			if (rowIdx !== -1) {
				editedTableRowStack.value.splice(rowIdx, 1)
			}
		}

		await showDelConfirm()

		submitBtnLoading.value = true
		try {
			await deleteGoodsReturnApplyGoods(ids)
			ElMessage.success("操作成功")
			getDetail()
			fetchTableData()
			emit("update")
		} finally {
			submitBtnLoading.value = false
		}
	}
}

/**
 * 删除物资 单个
 * @param e
 */
async function delGoods(e: any) {
	await showDelConfirm()
	await deleteGoodsReturnApplyGoods([e.id])

	const rowIdx = findIndex(editedTableRowStack.value, (v) => v.id === e.id)
	if (rowIdx !== -1) {
		editedTableRowStack.value.splice(rowIdx, 1)
	}

	ElMessage.success("操作成功")
	getDetail()
	fetchTableData()
	emit("update")
}

/**
 * 添加物资到申请中
 */
async function handleAddGoods(e: any) {
	const idempotentToken = getIdempotentToken(
		IIdempotentTokenTypePre.other,
		IIdempotentTokenType.goodsReturnApply,
		formData.value.id
	)

	await addGoodsReturnApplyGoods(
		{
			applyId: props.id,
			itemList: map(e, ({ id }) => ({
				batchId: id
			}))
		},
		idempotentToken
	)

	ElMessage.success("操作成功")
	handleQuery()
	getDetail()
	emit("update")
	goodsSelectorVisible.value = false
}

/**
 * 校验退库物资数量
 *
 * [退货数量]不大于[应退货数量]
 */
function validateGoodsReturnNum(e: MatOutStoreRemoveApplyItemVo) {
	// 退货数量
	const completeNum = toNumber(e.completeNum)

	e.completeNum = completeNum

	// 可退货数量 = 库存数量 - 冻结数量
	const num = round(toNumber(e.storeNum) - toNumber(e.freezeNum), 4)

	if (completeNum > num) {
		ElMessage.warning("退货数量不能大于 库存数量-冻结量！")
		e!.completeNum = num
	} else if (completeNum <= 0) {
		const oldRow = tableCache.find((v) => v.id == e.id)
		e.completeNum = oldRow.completeNum
		ElMessage.warning("退货数量不能小于等于0！")
		return
	}

	handleInputBlur(e)
}

/**
 * 输入框失焦 handler
 *
 * 失焦后，记录编辑过的数据，用于保存任务数据
 */
function handleInputBlur(e: any) {
	if (!editedTableRowStack.value.length) {
		editedTableRowStack.value.push({ ...e })
		return
	}

	/**
	 * 队列存在数据，验证重复
	 *
	 * - 不重复->push
	 * - 重复->更新该条数据
	 */
	const rowIdx = findIndex(editedTableRowStack.value, (v) => v.id === e.id)
	if (rowIdx !== -1) {
		editedTableRowStack.value.splice(rowIdx, 1, { ...e })
		return
	}

	editedTableRowStack.value.push({ ...e })
}

/**
 * 翻页 回显已编辑的数据
 */
watch(
	() => tableData.value,
	() => {
		if (editedTableRowStack.value.length) {
			editedTableRowStack.value.map((e) => {
				const rowIdx = findIndex(tableData.value, (v) => v.id === e.id)
				if (rowIdx !== -1) {
					tableData.value.splice(rowIdx, 1, { ...e })
				}
			})
		}
	}
)

/**
 * 编辑退货物资数量
 */
async function updateGoodsReturnNum() {
	const items = map(editedTableRowStack.value, (v) => ({
		id: v.id,
		completeNum: v.completeNum,
		remark: v.remark
	}))

	if (items.length < 1) {
		ElMessage.warning("当前物资列表未修改")
		return false
	}

	submitBtnLoading.value = true
	try {
		// 如果主表有修改，则先更新主表数据
		if (oldFormData.value != JSON.stringify(formData.value)) {
			await updateGoodsReturnApply(formData.value as any)
			oldFormData.value = JSON.stringify(formData.value)
		}

		await updateGoodsReturnApplyGoodsNum({
			applyId: props.id,
			itemList: items
		})

		ElMessage.success("操作成功")
		getDetail()
		fetchTableData()
		emit("update")

		editedTableRowStack.value = []
	} finally {
		submitBtnLoading.value = false
	}
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.goods-return-apply-form {
	&:deep(.el-col:nth-child(6)) {
		max-width: 50%;
		flex: 0 0 50%;
	}

	&:deep(.el-col:nth-child(7)) {
		max-width: 50%;
		flex: 0 0 50%;
	}
}
.goods-return-apply-editor-table-wrapper {
	&:deep(.pitaya-table) {
		.error {
			.column-inner-item,
			.cell,
			.el-input__inner {
				color: red;
			}
		}
	}
}
</style>
