<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<div class="drawer-column" style="width: 100%">
			<Title :title="titleConf" />
			<el-scrollbar class="rows">
				<pitaya-table
					ref="tableRef"
					:columns="tableProp"
					:table-data="tableData"
					:need-selection="true"
					:single-select="!multiple"
					:need-index="true"
					:need-pagination="true"
					:total="pageTotal"
					:table-loading="tableLoading"
					@on-selection-change="selectedTableList = $event"
					@on-current-page-change="onCurrentPageChange"
				>
					<!-- 订单来源 -->
					<template #source="{ rowData }">
						<dict-tag
							:options="DictApi.getPurchaseOrderSource()"
							:value="rowData.source"
						/>
					</template>
					<!-- 采购方式 -->
					<template #projectPurchaseType="{ rowData }">
						<dict-tag
							:value="rowData.projectPurchaseType"
							:options="dictOptions.PURCHASE_TYPE"
						/>
					</template>
					<!-- 订单金额 -->
					<template #orderingAmount="{ rowData }">
						<cost-tag :value="rowData.orderingAmount" />
					</template>
				</pitaya-table>
			</el-scrollbar>

			<button-list
				class="footer"
				:button="btnConf"
				@on-btn-click="handleBtnClick"
			/>
		</div>
	</div>
</template>

<script setup lang="ts">
import { PurchaseOrderApi } from "@/app/baseline/api/purchase/purchaseOrder"
import { useTbInit } from "../../../components/tableBase"
import { first } from "lodash-es"
import CostTag from "../../../components/costTag.vue"
import { useDictInit } from "../../../components/dictBase"
import DictTag from "../../../components/dictTag.vue"
import { DictApi } from "@/app/baseline/api/dict"

const props = defineProps<{
	/**
	 * 当前选中的数据
	 */
	selected?: any

	/**
	 * 是否多选
	 */
	multiple?: boolean
}>()

const emit = defineEmits<{
	(e: "close"): void
	(e: "select", v: any): void
}>()

const titleConf = computed(() => ({
	name: ["选择采购订单"],
	icon: ["fas", "square-share-nodes"]
}))

const { dictOptions, getDictByCodeList } = useDictInit()

const drawerLoading = ref(false)

const btnConf = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "保存",
		icon: ["fas", "file-signature"]
	}
]

const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	selectedTableList,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = useTbInit()

fetchFunc.value = PurchaseOrderApi.getPurchaseOrderList

tableProp.value = [
	{
		prop: "code",
		label: "采购订单号"
	},
	{
		prop: "label",
		label: "采购订单名称"
	},
	{
		prop: "supplierLabel",
		label: "供应商名称"
	},
	{
		prop: "contractCode",
		label: "合同编号"
	},
	{
		prop: "projectPurchaseType",
		label: "采购方式",
		needSlot: true
	},
	{
		prop: "orderingAmount",
		label: "订货金额",
		needSlot: true
	},
	{
		prop: "purchaseUserName",
		label: "采购员"
	},
	{
		prop: "source",
		label: "订单来源",
		needSlot: true
	}
]

onMounted(() => {
	getDictByCodeList(["PURCHASE_TYPE"])
	fetchTableData()
})

function handleBtnClick(name?: string) {
	if (name === "取消") {
		return emit("close")
	}

	emit(
		"select",
		props.multiple ? selectedTableList.value : first(selectedTableList.value)
	)
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
