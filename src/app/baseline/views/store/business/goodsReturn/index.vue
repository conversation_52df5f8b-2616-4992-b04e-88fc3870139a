<!-- 库存管理-业务管理-退货申请 -->
<template>
	<div class="app-container">
		<div class="whole-frame">
			<model-frame class="top-frame">
				<!-- 筛选 -->
				<Query
					:query-arr-list="(queryConf as any)"
					@get-query-data="handleQuery"
					class="ml10"
				/>
			</model-frame>

			<model-frame class="bottom-frame">
				<Title
					:title="titleConf"
					:button="(titleBtnConf as any)"
					@on-btn-click="showCreatorDrawer"
				>
					<div class="app-tabs-wrapper">
						<el-tabs v-model="activeName" @tab-click="handleTabChanged">
							<el-tab-pane
								v-for="(tab, index) in tabsConf"
								:key="index"
								:label="`${tab}（${statusCnt[index] ?? 0}）`"
								:name="tab"
								:index="tab"
							/>
						</el-tabs>
					</div>

					<!-- <Tabs
						style="margin-right: auto; margin-left: 30px"
						:tabs="tabsConf"
						@on-tab-change="handleTabChanged"
					/> -->
				</Title>

				<pitaya-table
					ref="tableRef"
					:columns="(tbInit.tableProp as any)"
					:table-data="tableData"
					:need-selection="false"
					:single-select="false"
					:need-index="true"
					:need-pagination="true"
					:total="pageTotal"
					:table-loading="tableLoading"
					@on-selection-change="selectedTableList = $event"
					@on-current-page-change="onCurrentPageChange"
					@on-table-sort-change="handleSortChange"
				>
					<!-- 退货数量 -->
					<template #num="{ rowData }">
						{{ toFixedTwo(rowData.num) }}
					</template>

					<!-- 退货金额 -->
					<template #amount="{ rowData }">
						<cost-tag :value="rowData.amount" />
					</template>

					<!-- 审批状态 -->
					<template #bpmStatus="{ rowData }">
						<dict-tag
							:options="DictApi.getBpmStatus()"
							:value="rowData.bpmStatus"
						/>
					</template>

					<!-- 出库状态 -->
					<template #status="{ rowData }">
						<dict-tag :options="getOutStoreStatus()" :value="rowData.status" />
					</template>

					<template #actions="{ rowData }">
						<slot
							v-if="
								(canShowTableEditAction(rowData.bpmStatus) &&
									isCheckPermission(
										powerList.storeBusinessGoodsReturnApplyBtnEdit
									) &&
									hasPermi(rowData.createdBy)) ||
								isCheckPermission(
									powerList.storeBusinessGoodsReturnApplyBtnPreview
								) ||
								(canShowTableEditAction(rowData.bpmStatus) &&
									isCheckPermission(
										powerList.storeBusinessGoodsReturnApplyBtnDrop
									) &&
									hasPermi(rowData.createdBy))
							"
						>
							<el-button
								v-if="
									canShowTableEditAction(rowData.bpmStatus) &&
									isCheckPermission(
										powerList.storeBusinessGoodsReturnApplyBtnEdit
									) &&
									hasPermi(rowData.createdBy)
								"
								v-btn
								link
								@click="showEditorDrawer(rowData)"
								:disabled="
									checkPermission(
										powerList.storeBusinessGoodsReturnApplyBtnEdit
									)
								"
							>
								<font-awesome-icon
									:icon="['fas', 'pen-to-square']"
									style="color: var(--pitaya-btn-background)"
								/>
								<span class="table-inner-btn">编辑</span>
							</el-button>

							<el-button
								v-btn
								link
								@click="showDetailDrawer(rowData)"
								:disabled="
									checkPermission(
										powerList.storeBusinessGoodsReturnApplyBtnPreview
									)
								"
								v-if="
									isCheckPermission(
										powerList.storeBusinessGoodsReturnApplyBtnPreview
									)
								"
							>
								<font-awesome-icon
									:icon="['fas', 'eye']"
									style="color: var(--pitaya-btn-background)"
								/>
								<span class="table-inner-btn">查看</span>
							</el-button>
							<el-button
								v-if="
									canShowTableEditAction(rowData.bpmStatus) &&
									isCheckPermission(
										powerList.storeBusinessGoodsReturnApplyBtnDrop
									) &&
									hasPermi(rowData.createdBy)
								"
								v-btn
								link
								@click="handleDelRow(rowData)"
								:disabled="
									checkPermission(
										powerList.storeBusinessGoodsReturnApplyBtnDrop
									)
								"
							>
								<font-awesome-icon
									:icon="['fas', 'trash-can']"
									style="color: var(--pitaya-btn-background)"
								/>
								<span class="table-inner-btn">移除</span>
							</el-button>
						</slot>
						<slot v-else>---</slot>
					</template>
				</pitaya-table>
			</model-frame>
		</div>

		<!-- 查看 drawer -->
		<Drawer
			v-model:drawer="detailVisible"
			:size="modalSize.xl"
			destroy-on-close
		>
			<goods-return-detail
				:id="editingTableRowId"
				@close="detailVisible = false"
			/>
		</Drawer>

		<!-- 编辑 drawer -->
		<Drawer
			v-model:drawer="editorVisible"
			:size="modalSize.xl"
			destroy-on-close
		>
			<goods-return-editor
				:id="editingTableRowId"
				:mode="editorMode"
				@save="handleDrawerSave"
				@update="fetchTableData"
				@close="editorVisible = false"
			/>
		</Drawer>

		<!-- 审批、查看弹窗 -->
		<Drawer
			:size="modalSize.xxl"
			v-model:drawer="approvedVisible"
			:destroyOnClose="true"
		>
			<approved-drawer
				:mod="editorMode"
				:task-value="editTask"
				@on-save-or-close="approvedVisible = false"
			/>
		</Drawer>
	</div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from "vue"
import {
	checkPermission,
	isCheckPermission
} from "@/app/platform/utils/permission"
import { powerList } from "../../../components/define.d"

import { IModalType } from "@/app/baseline/utils/types/common"
import { useMessageBoxInit } from "../../../components/messageBox"
import { useTbInit } from "../../../components/tableBase"
import { modalSize } from "@/app/baseline/utils/layout-config"
import { BaseLineSysApi, getTaskByBusinessIds } from "@/app/baseline/api/system"
import DictTag from "../../../components/dictTag.vue"
import { DictApi, appStatus } from "@/app/baseline/api/dict"
import CostTag from "../../../components/costTag.vue"
import goodsReturnDetail from "./goodsReturnDetail.vue"
import goodsReturnEditor from "./goodsReturnEditor.vue"
import {
	deleteGoodsReturnApply,
	getMatOutStoreRemoveApplyBmpStatusCnt,
	listGoodsReturnApplyPaged
} from "@/app/baseline/api/store/goods-return-apply-api"
import { listMatStoragePaged } from "@/app/baseline/api/store/manage-api"
import { omit } from "lodash-es"
import { tableColFilter, toFixedTwo } from "@/app/baseline/utils"
import { getOutStoreStatus } from "../../outbound/outbound"
import { useUserStore } from "@/app/platform/store/modules/user"
import { hasPermi } from "@/app/baseline/utils"
import ApprovedDrawer from "@/app/baseline/views/components/approvedDrawer.vue"

const { showDelConfirm } = useMessageBoxInit()

const { userInfo } = storeToRefs(useUserStore())

/**
 * 编辑模式：编辑/新建
 */
const editorMode = ref(IModalType.create)

const detailVisible = ref(false)

const editorVisible = ref(false)

const approvedVisible = ref(false)
const editTask = ref<Record<string, any>>()

/**
 * 按钮配置：新建
 */
const titleBtnConf = [
	{
		name: "新建退货申请",
		roles: powerList.storeBusinessGoodsReturnApplyBtnCreate,
		icon: ["fas", "square-plus"]
	}
]

/**
 * 标题配置
 */
const titleConf = {
	name: ["退货申请"],
	icon: ["fas", "square-share-nodes"]
}

/**
 * 筛选配置
 */
const queryConf = computed<querySetting[]>(() => {
	return [
		{
			name: "退货单号",
			key: "code",
			type: "input",
			placeholder: "请输入退货单号"
		},
		{
			name: "退货单名称",
			key: "label",
			type: "input",
			placeholder: "请输入退货单名称"
		},
		{
			name: "入库仓库",
			key: "storeId",
			placeholder: "请选择入库仓库",
			type: "tableSelect",
			tableInfo: [
				{
					title: "请选择入库仓库",
					tableApi: listMatStoragePaged, //表格接口
					tableColumns: [
						{ label: "仓库编码", prop: "code", width: 120 },
						{ label: "仓库名称", prop: "label" },
						{
							label: "仓库级别",
							prop: "level_view",
							width: 100
						},
						{
							label: "仓库类型",
							prop: "type_view",
							width: 120
						},
						{
							label: "所属段区",
							prop: "depotId_view",
							width: 150
						}
					],
					queryArrList: [
						{
							name: "仓库编码",
							key: "code",
							type: "input",
							placeholder: "请输入仓库编码"
						},
						{
							name: "仓库名称",
							key: "label",
							type: "input",
							placeholder: "请输入仓库名称"
						}
					],
					params: {
						currentPage: 1,
						pageSize: 20
					}, //表格接口参数

					labelName: {
						key: "id", //回显标识
						label: "label", //input框要展示的字段
						value: "id" //要给后台传的字段
					}
				}
			]
		},

		{
			name: "申请人",
			key: "createdByName",
			type: "input",
			placeholder: "请输入关联申请人"
		},
		{
			name: "申请部门",
			key: "sysOrgId",
			type: "treeSelect",
			placeholder: "请选择",
			treeApi: () =>
				BaseLineSysApi.getDepartmentTreeWithCompanyDisabled(
					userInfo.value.companyId
				)
		}
	]
})

/**
 * 标签页配置
 */
const tabsConf = ["草稿箱", "审批中", "已审批"]
const activeName = ref<string>(tabsConf[0])
const statusCnt = ref<number[]>([])

/**
 * 当前激活的标签页位置
 */
const activatedTab = ref(0)
const tbInit = useTbInit()
const {
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	currentPage,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = tbInit

/**
 * 默认筛选 待提交/草稿箱
 */
fetchParam.value = {
	bpmStatus: appStatus.pendingApproval,
	sord: "desc",
	sidx: "createdDate"
}

/**
 * table api function conf
 */
fetchFunc.value = listGoodsReturnApplyPaged

/**
 * table 的 columns 配置
 */
tbInit.tableProp = computed(() => {
	const defCols: TableColumnType[] = [
		{
			prop: "code",
			label: "退货单号",
			width: 180,
			fixed: "left",
			sortable: true
		},
		{
			prop: "label",
			label: "退货单名称"
		},
		{
			prop: "storeLabel",
			label: "仓库名称",
			minWidth: 120
		},
		{
			prop: "roomLabel",
			label: "货位编码",
			width: 100
		},
		{
			prop: "materialCodeNum",
			label: "物资编码"
		},
		{
			prop: "num",
			label: "退货数量",
			needSlot: true,
			align: "right"
		},
		{
			prop: "amount",
			label: "退货金额",
			needSlot: true,
			align: "right"
		},
		{
			prop: "bpmStatus",
			label: "审批状态",
			needSlot: true,
			width: 100
		},
		{
			prop: "status",
			label: "出库状态",
			needSlot: true,
			width: 100
		},
		{
			prop: "sysOrgId_view",
			label: "申请部门",
			width: 140
		},
		{
			prop: "createdBy_view",
			label: "申请人"
		},
		{
			prop: "createdDate",
			label: "申请时间",
			width: 180,
			sortable: true
		},
		{
			prop: "actions",
			label: "操作",
			width: 200,
			needSlot: true,
			fixed: "right"
		}
	]

	switch (activatedTab.value) {
		case 2:
			// 未启动
			return tableColFilter(defCols, ["审批状态"])
		default:
			return tableColFilter(defCols, ["出库状态"])
	}
})

/**
 * 当前编辑/查看的 table row id
 */
const editingTableRowId = ref()

onMounted(() => {
	handleQuery()
})

/**
 * 是否可见 - table 操作按钮 编辑/移除
 */
function canShowTableEditAction(bpmStatus: appStatus) {
	const isValidBpmStatus = [
		appStatus.pendingApproval,
		appStatus.rejected
	].includes(bpmStatus)
	const isDraft = activatedTab.value === 0

	return isDraft && isValidBpmStatus
}

/**
 * 筛选 handler
 */
function handleQuery(e?: any) {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	const bpmStatusMap = [
		`${appStatus.pendingApproval},${appStatus.rejected}`,
		appStatus.underApproval,
		appStatus.approved
	]

	// 聚合筛选条件
	fetchParam.value = {
		...fetchParam.value,
		...e,
		bpmStatus: bpmStatusMap[activatedTab.value]
	}

	handleUpdate()
}

/**
 * 展示详情 drawer
 */
async function showDetailDrawer(e: any) {
	editingTableRowId.value = e.id

	editorMode.value = IModalType.view

	/* detailVisible.value = true */

	if (e?.bpmStatus == appStatus.pendingApproval) {
		detailVisible.value = true
	} else {
		const res = await getTaskByBusinessIds({
			businessIds: [e?.id],
			camundaKey: "remove_apply"
		})

		if (Array.isArray(res) && res.length > 0) {
			editTask.value = { ...res[res.length - 1] }
			approvedVisible.value = true
		} else {
			detailVisible.value = true
		}
	}
}

/**
 * 展示编辑器 drawer
 */
function showEditorDrawer(e: any) {
	editingTableRowId.value = e.id
	editorMode.value = IModalType.edit
	editorVisible.value = true
}

/**
 * 展示新建 drawer
 */
function showCreatorDrawer(e: any) {
	editingTableRowId.value = e.id
	editorMode.value = IModalType.create
	editorVisible.value = true
}

/**
 * 删除 table row
 */
async function handleDelRow(e: any) {
	await showDelConfirm()
	await deleteGoodsReturnApply(e.id)
	ElMessage.success("操作成功")
	handleUpdate()
}

/**
 * drawer save handler
 *
 * 处理 drawer 保存之后的逻辑
 *
 * @param id - table row id
 */
function handleDrawerSave(id: any, visible = false) {
	editorMode.value = IModalType.edit
	editorVisible.value = visible
	editingTableRowId.value = id
	handleUpdate()
}

function handleTabChanged(tab: any) {
	activeName.value = tab.paneName
	activatedTab.value = Number(tab.index)

	handleQuery()
}

/**
 * 更新主表/ tab 状态统计
 */
async function handleUpdate() {
	fetchTableData()
	statusCnt.value = await getMatOutStoreRemoveApplyBmpStatusCnt(
		omit(
			{
				...fetchParam.value
			},
			"bpmStatus",
			"currentPage",
			"pageSize"
		) as any
	)
}

/**
 * 排序
 * @param column
 * @param prop 字段名称
 * @param order ascending 升序 descending 降序
 */
function handleSortChange(column: any, prop: any, order: any) {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		sord:
			order === "descending" ? "desc" : order === "ascending" ? "asc" : "desc",
		sidx: order ? prop : "createdDate" // 排序字段
	}

	fetchTableData()
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
