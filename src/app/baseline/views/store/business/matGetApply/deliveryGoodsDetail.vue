<!-- 领料申请-出库物资详情 -->
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<div class="drawer-column left" style="width: 310px">
			<Title :title="titleConf" />
			<el-descriptions size="small" :column="1" border class="content">
				<el-descriptions-item
					v-for="desc in descOptions"
					:key="desc.label"
					:label="desc.label"
				>
					<cost-tag v-if="desc.key === 'amount'" :value="descData.amount" />

					<dict-tag
						v-else-if="desc.key === 'attribute'"
						:options="DictApi.getMatAttr()"
						:value="descData.attribute"
					/>
					<span v-else-if="desc.needTooltip">
						<el-tooltip
							effect="dark"
							:content="descData?.[desc.key]"
							:disabled="
								getRealLength(descData?.[desc.key]) <= 100 ? true : false
							"
						>
							{{
								getRealLength(descData?.[desc.key]) > 100
									? setString(descData?.[desc.key], 100)
									: descData?.[desc.key] || "---"
							}}
						</el-tooltip>
					</span>
					<span v-else>
						{{ getNumDefByNumKey(desc.key, descData[desc.key]) }}
					</span>
				</el-descriptions-item>
			</el-descriptions>
		</div>
		<div class="drawer-column right" style="width: calc(100% - 310px)">
			<Title :title="exTitleConf" />
			<pitaya-table
				ref="tableRef"
				:columns="tableProp"
				:table-data="tableData"
				:need-selection="false"
				:single-select="false"
				:need-index="true"
				:need-pagination="true"
				:total="pageTotal"
				:table-loading="tableLoading"
				@on-selection-change="(e) => (selectedTableList = e)"
				@on-current-page-change="onCurrentPageChange"
			>
				<template #amount="{ rowData }">
					<cost-tag :value="rowData.amount" />
				</template>
				<template #totalPrice="{ rowData }">
					<cost-tag :value="rowData.totalPrice" />
				</template>
			</pitaya-table>
		</div>
	</div>
</template>

<script setup lang="ts">
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import { DictApi } from "@/app/baseline/api/dict"
import {
	getMatPickApplyGoodsBatch,
	listMatPickApplyDeliveryBatchPaged
} from "@/app/baseline/api/store/mat-get-apply-api"
import { useTbInit } from "../../../components/tableBase"
import {
	MatOutStorePickApplyItemBatchDTO,
	MatOutStorePickApplyItemBatchVo,
	MatOutStorePickApplyItemVo
} from "@/app/baseline/utils/types/mat-get-apply"
import CostTag from "../../../components/costTag.vue"
import { batchFormatterNumView, getNumDefByNumKey } from "@/app/baseline/utils"
import { getRealLength, setString } from "@/app/baseline/utils/validate"

const props = defineProps<{
	/**
	 * 物资id
	 */
	id: any
}>()

const drawerLoading = ref(false)

const titleConf = {
	name: ["物资信息"],
	icon: ["fas", "square-share-nodes"]
}

const exTitleConf = {
	name: ["出库批次信息"],
	icon: ["fas", "square-share-nodes"]
}

const descOptions = [
	{
		label: "物资编码",
		key: "materialCode"
	},
	{
		label: "物资名称",
		key: "materialName"
	},
	{
		label: "规格型号",
		key: "version"
	},
	{
		label: "技术参数",
		key: "technicalParameter",
		needTooltip: true
	},
	{
		label: "物资性质",
		key: "attribute"
	},
	{
		label: "出库仓库名称",
		key: "storeLabel"
	},
	{
		label: "领料数量",
		key: "num"
	},
	{
		label: "金额",
		key: "amount"
	}
]

const descData = ref<MatOutStorePickApplyItemVo>({})

const {
	fetchParam,
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	selectedTableList,
	fetchFunc,
	fetchTableData,
	onCurrentPageChange
} = useTbInit<
	MatOutStorePickApplyItemBatchVo,
	MatOutStorePickApplyItemBatchDTO
>()

fetchFunc.value = listMatPickApplyDeliveryBatchPaged

tableProp.value = [
	{
		label: "批次号",
		prop: "batchNo",
		width: 170
	},
	{
		label: "区域名称",
		prop: "regionLabel",
		minWidth: 120
	},
	{
		label: "货位名称",
		prop: "roomLabel",
		minWidth: 120
	},
	{
		label: "领料数量",
		prop: "num_view",
		align: "right",
		width: 120
	},
	{
		label: "采购单价",
		prop: "amount",
		align: "right",
		needSlot: true,
		width: 120
	},
	{
		label: "金额",
		prop: "totalPrice",
		align: "right",
		needSlot: true,
		width: 120
	},
	{
		label: "质保有效日期",
		prop: "validityPeriod",
		width: 150
	}
]

/**
 * 格式化数量
 */
watch(tableData, () => {
	batchFormatterNumView(tableData.value as any[])
})

onMounted(() => {
	fetchParam.value.id = props.id
	fetchTableData()
	getDetail()
})

function getDetail() {
	drawerLoading.value = true
	getMatPickApplyGoodsBatch(props.id)
		.then((r) => (descData.value = r ?? {}))
		.finally(() => (drawerLoading.value = false))
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
