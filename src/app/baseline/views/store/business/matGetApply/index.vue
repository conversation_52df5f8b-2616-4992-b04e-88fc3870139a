<!-- 库存管理-业务管理-领料申请 -->
<template>
	<div class="app-container">
		<div class="whole-frame">
			<model-frame class="top-frame">
				<Query
					:query-arr-list="(queryArrList as any)"
					@get-query-data="getQueryData"
					class="ml10"
				/>
			</model-frame>
			<model-frame class="bottom-frame">
				<Title
					:title="titleConf"
					:button="(btnConf as any)"
					@on-btn-click="showCreateDrawer"
				>
					<div class="app-tabs-wrapper">
						<el-tabs v-model="activeName" @tab-click="handleTabChanged">
							<el-tab-pane
								v-for="(tab, index) in tabsConf"
								:key="index"
								:label="`${tab}（${statusCnt[index] ?? 0}）`"
								:name="tab"
								:index="tab"
							/>
						</el-tabs>
					</div>

					<!-- <Tabs
						style="margin-right: auto; margin-left: 30px"
						:tabs="tabsConf"
						@on-tab-change="handleTabChanged"
					/> -->
				</Title>

				<pitaya-table
					ref="tableRef"
					:columns="(tbInit.tableProp as any)"
					:table-data="tableData"
					:need-selection="true"
					:single-select="true"
					:need-index="true"
					:need-pagination="true"
					:total="pageTotal"
					:table-loading="tableLoading"
					@on-selection-change="selectedTableList = $event"
					@on-current-page-change="onCurrentPageChange"
					@on-table-sort-change="handleSortChange"
				>
					<!-- 审批状态 -->
					<template #bpmStatus="{ rowData }">
						<dict-tag
							:options="DictApi.getBpmStatus()"
							:value="rowData.bpmStatus"
						/>
					</template>

					<!-- 出库状态 -->
					<template #status="{ rowData }">
						<dict-tag :options="getOutStoreStatus()" :value="rowData.status" />
					</template>

					<template #amount="{ rowData }">
						<cost-tag :value="rowData.amount" />
					</template>

					<template #actions="{ rowData }">
						<slot
							v-if="
								(canShowTableEditAction() &&
									isCheckPermission(
										powerList.storeBusinessMatPickApplyBtnEdit
									) &&
									hasPermi(rowData.createdBy)) ||
								(canShowTableEditAction() &&
									isCheckPermission(
										powerList.storeBusinessMatPickApplyBtnDrop
									) &&
									hasPermi(rowData.createdBy)) ||
								isCheckPermission(powerList.storeBusinessMatPickApplyBtnPreview)
							"
						>
							<el-button
								v-if="
									canShowTableEditAction() &&
									isCheckPermission(
										powerList.storeBusinessMatPickApplyBtnEdit
									) &&
									hasPermi(rowData.createdBy)
								"
								v-btn
								link
								@click.stop="showApplyEditor(rowData)"
								:disabled="
									checkPermission(powerList.storeBusinessMatPickApplyBtnEdit)
								"
							>
								<font-awesome-icon
									:icon="['fas', 'pen-to-square']"
									style="color: var(--pitaya-btn-background)"
								/>
								<span class="table-inner-btn">编辑</span>
							</el-button>

							<el-button
								v-btn
								link
								@click.stop="showApplyDetail(rowData)"
								:disabled="
									checkPermission(powerList.storeBusinessMatPickApplyBtnPreview)
								"
								v-if="
									isCheckPermission(
										powerList.storeBusinessMatPickApplyBtnPreview
									)
								"
							>
								<font-awesome-icon
									:icon="['fas', 'eye']"
									style="color: var(--pitaya-btn-background)"
								/>
								<span class="table-inner-btn">查看</span>
							</el-button>

							<el-button
								v-if="
									canShowTableEditAction() &&
									isCheckPermission(
										powerList.storeBusinessMatPickApplyBtnDrop
									) &&
									hasPermi(rowData.createdBy)
								"
								v-btn
								link
								@click.stop="delApply(rowData)"
								:disabled="
									checkPermission(powerList.storeBusinessMatPickApplyBtnDrop)
								"
							>
								<font-awesome-icon
									:icon="['fas', 'trash-can']"
									style="color: var(--pitaya-btn-background)"
								/>
								<span class="table-inner-btn">移除</span>
							</el-button>
						</slot>
						<slot v-else>---</slot>
					</template>

					<!-- 领料交旧（已审批显示） -->
					<template v-if="activatedTab === 2" #footerOperateLeft>
						<button-list
							class="footer"
							:is-not-radius="true"
							:button="tbBtnConf"
							@on-btn-click="handleReturnOld"
						/>
					</template>
				</pitaya-table>
			</model-frame>
		</div>

		<!-- 领料申请编辑 -->
		<Drawer
			v-model:drawer="matGetApplyEditorVisible"
			:size="modalSize.xl"
			destroy-on-close
		>
			<mat-get-apply-editor
				:mode="drawerType"
				:id="editingMatApplyId"
				@close="matGetApplyEditorVisible = false"
				@save="handleDrawerSave"
				@update="fetchTableData"
			/>
		</Drawer>

		<!-- 领料申请详情 -->
		<Drawer
			v-model:drawer="matGetApplyDetailVisible"
			:size="modalSize.xl"
			destroy-on-close
		>
			<mat-get-apply-detail
				:id="editingMatApplyId"
				@close="matGetApplyDetailVisible = false"
			/>
		</Drawer>

		<!-- 物资交旧申请 -->
		<Drawer
			v-model:drawer="wasteHandoverApplyVisible"
			:size="modalSize.xl"
			destroy-on-close
		>
			<handover-apply-editor
				:mode="IModalType.create"
				:formValue="first(selectedTableList)"
				@on-save-or-close=" (msg?: string) => {
					if (msg === 'save') {
						handleUpdate();
					}
					wasteHandoverApplyVisible = false
				}"
				@update="handleUpdate"
			/>
		</Drawer>

		<!-- 审批、查看弹窗 -->
		<Drawer
			:size="modalSize.xxl"
			v-model:drawer="approvedVisible"
			:destroyOnClose="true"
		>
			<approved-drawer
				:mod="drawerType"
				:task-value="editTask"
				@on-save-or-close="approvedVisible = false"
			/>
		</Drawer>
	</div>
</template>

<script setup lang="ts">
import {
	checkPermission,
	isCheckPermission
} from "@/app/platform/utils/permission"
import { powerList } from "../../../components/define.d"

import { IModalType } from "@/app/baseline/utils/types/common"
import { useTbInit } from "../../../components/tableBase"
import { modalSize } from "@/app/baseline/utils/layout-config"
import { DictApi, MaterialPurpose, appStatus } from "@/app/baseline/api/dict"
import DictTag from "../../../components/dictTag.vue"
import matGetApplyDetail from "./matGetApplyDetail.vue"
import matGetApplyEditor from "./matGetApplyEditor.vue"
import { BaseLineSysApi, getTaskByBusinessIds } from "@/app/baseline/api/system"
import { useMessageBoxInit } from "../../../components/messageBox"
import {
	canWasteOld,
	deleteMatPickApply,
	getMatPickApplyBpmStatusCnt,
	listMatPickApplyPaged
} from "@/app/baseline/api/store/mat-get-apply-api"
import {
	MatOutStorePickApplyDTO,
	MatOutStorePickApplyVo
} from "@/app/baseline/utils/types/mat-get-apply"
import { useDictInit } from "../../../components/dictBase"
import CostTag from "../../../components/costTag.vue"
import handoverApplyEditor from "../../../waste/handoverApply/handoverApplyEditor.vue"
import { first, omit } from "lodash-es"
import { getOutStoreStatus } from "../../outbound/outbound"
import { useUserStore } from "@/app/platform/store/modules/user"
import { ICostCategoryStatus } from "@/app/baseline/utils/types/system-cost-category"
import { SystemDepotVo } from "@/app/baseline/utils/types/system-depot"
import { hasPermi } from "@/app/baseline/utils"
import ApprovedDrawer from "@/app/baseline/views/components/approvedDrawer.vue"

const { userInfo } = storeToRefs(useUserStore())

const tabsConf = ["草稿箱", "审批中", "已审批"]
const activeName = ref<string>(tabsConf[0])
const statusCnt = ref<number[]>([])

const activatedTab = ref(0)

const { showDelConfirm, showWarnConfirm } = useMessageBoxInit()

const drawerType = ref(IModalType.view)

const { dictOptions, getDictByCodeList } = useDictInit()

/**
 * 查询数据
 */
const queryData = ref<MatOutStorePickApplyDTO>({})

/**
 * 编辑中的领料申请id
 */
const editingMatApplyId = ref()

const titleConf = {
	name: ["领料申请"],
	icon: ["fas", "square-share-nodes"]
}

const btnConf = [
	{
		name: "新建领料申请",
		roles: powerList.storeBusinessMatPickApplyBtnCreate,
		icon: ["fas", "square-plus"]
	}
]

const matGetApplyDetailVisible = ref(false)

const matGetApplyEditorVisible = ref(false)

const approvedVisible = ref(false)
const editTask = ref<Record<string, any>>()

const queryArrList = computed<querySetting[]>(() => [
	{
		name: "领料单号",
		key: "code",
		type: "input",
		placeholder: "请输入领料单号"
	},
	{
		name: "领料单名称",
		key: "label",
		type: "input",
		placeholder: "请输入领料单名称"
	},
	{
		name: "关联业务单号",
		key: "workOrderCode",
		type: "input",
		placeholder: "请输入关联业务单号"
	},
	{
		name: "领料人",
		key: "pickUserName",
		type: "input",
		placeholder: "请输入领料人"
	},
	{
		name: "领料用途",
		key: "purpose",
		type: "select",
		placeholder: "请选择领料用途",
		children: dictOptions.value.MATERIAL_PURPOSE
	},
	{
		name: "线路",
		key: "lineNoId",
		type: "select",
		placeholder: "请选择线路",
		children: lineOptions.value
	},
	{
		name: "专业",
		key: "majorId",
		type: "treeSelect",
		treeApi: () => BaseLineSysApi.getProfessionTree(),
		placeholder: "请选择专业"
	},
	{
		name: "费用类别",
		key: "parentExpenseCategory",
		type: "treeSelect",
		placeholder: "请选择费用类别",
		treeApi: () =>
			BaseLineSysApi.getCostCategoryTree({
				status: ICostCategoryStatus.Started
			})
	},
	{
		name: "领料部门",
		key: "sysOrgId",
		type: "treeSelect",
		placeholder: "请选择领料部门",
		treeApi: () =>
			BaseLineSysApi.getDepartmentTreeWithCompanyDisabled(
				userInfo.value.companyId
			)
	},
	{
		name: "段区",
		key: "depotId",
		type: "select",
		children: depotList.value,
		placeholder: "请选择段区"
	}
])

const lineOptions = ref([])
const depotList = ref<SystemDepotVo[]>([])

const tbInit = useTbInit<MatOutStorePickApplyVo, MatOutStorePickApplyDTO>()
const {
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	currentPage,
	selectedTableList,
	fetchTableData,
	fetchFunc,
	fetchParam,
	onCurrentPageChange
} = tbInit

fetchFunc.value = listMatPickApplyPaged

fetchParam.value = {
	...fetchParam.value,
	bpmStatus: `${appStatus.pendingApproval},${appStatus.rejected}`,
	sord: "desc",
	sidx: "createdDate"
}

/**
 * table col 过滤器
 */
function tableColFilter(cols: TableColumnType[], excludeCols: string[]) {
	return cols.filter((c) => !excludeCols.includes(c.label))
}

tbInit.tableProp = computed(() => {
	const defCols: TableColumnType[] = [
		{
			prop: "code",
			label: "领料单号",
			width: 180,
			fixed: "left",
			sortable: true
		},
		{
			prop: "label",
			label: "领料单名称",
			width: 150
		},
		{
			prop: "purpose_view",
			label: "领料用途",
			width: 100
		},
		{
			prop: "depotId_view",
			label: "段区",
			width: 150
		},
		{
			prop: "lineNoId_view",
			label: "线路",
			width: 70
		},
		{
			prop: "majorId_view",
			label: "专业",
			width: 120
		},
		{
			prop: "workOrderCode",
			label: "关联业务单号",
			width: 180
		},
		{
			prop: "storeLabel",
			label: "出库仓库名称",
			width: 150
		},
		{
			prop: "materialCodeNum",
			label: "物资编码"
		},
		{
			prop: "amount",
			label: "领料金额（元）",
			needSlot: true,
			align: "right",
			width: 120
		},
		{
			prop: "expenseCategory_view",
			label: "费用类别",
			width: 80
		},
		{
			prop: "bpmStatus",
			label: "审批状态",
			needSlot: true,
			width: 100
		},
		{
			prop: "status",
			label: "出库状态",
			needSlot: true,
			width: 100
		},
		{
			prop: "pickUserId_view",
			label: "领料人",
			width: 150
		},
		{
			prop: "pickSysOrgId_view",
			label: "领料部门",
			width: 150
		},
		{
			prop: "createdDate",
			label: "申请时间",
			width: 150,
			sortable: true
		},
		{
			prop: "lastModifiedDate",
			label: "更新时间",
			width: 150,
			sortable: true
		},
		{
			prop: "actions",
			label: "操作",
			width: 200,
			needSlot: true,
			fixed: "right"
		}
	]

	switch (activatedTab.value) {
		case 0:
			// 草搞箱
			return tableColFilter(defCols, ["领料金额（元）", "出库状态", "更新时间"])
		case 1:
			// 审批中
			return tableColFilter(defCols, ["出库状态", "更新时间"])
		default:
			// 已审批
			return tableColFilter(defCols, ["审批状态"])
	}
})

const tbBtnConf = computed(() => {
	return [
		{
			name: "物资领料交旧",
			roles: powerList.storeBusinessMatPickApplyBtnHandover,
			icon: ["fas", "right-left"],
			disabled:
				selectedTableList.value.length < 1 ||
				first(selectedTableList.value)?.purpose ==
					MaterialPurpose.lowvalueMaterial
		}
	]
})

onMounted(() => {
	getDictByCodeList(["MATERIAL_PURPOSE"])
	getLineOptions()
	getDepotList()
	handleUpdate()
})

/**
 * table 操作按钮 编辑/移除 可见性
 */
function canShowTableEditAction() {
	return activatedTab.value === 0
}

/**
 * 领料交旧处理
 */
const wasteHandoverApplyVisible = ref(false)
async function handleReturnOld() {
	const res = await canWasteOld(first(selectedTableList.value)?.id as number)
	if (res) {
		wasteHandoverApplyVisible.value = true
	} else {
		return await showWarnConfirm(
			"该领料单中没有可交旧物资！无法进行交旧申请！",
			false
		)
	}
}

/**
 * 查看申请详情
 */
async function showApplyDetail(e: any) {
	editingMatApplyId.value = e.id
	drawerType.value = IModalType.view
	/* matGetApplyDetailVisible.value = true */

	if (e?.bpmStatus == appStatus.pendingApproval) {
		matGetApplyDetailVisible.value = true
	} else {
		const res = await getTaskByBusinessIds({
			businessIds: [e?.id],
			camundaKey: "pick_apply"
		})

		if (Array.isArray(res) && res.length > 0) {
			editTask.value = { ...res[res.length - 1] }
			approvedVisible.value = true
		} else {
			matGetApplyDetailVisible.value = true
		}
	}
}

/**
 * 查看申请编辑器
 */
function showApplyEditor(e: any) {
	editingMatApplyId.value = e.id
	drawerType.value = IModalType.edit
	matGetApplyEditorVisible.value = true
}

/**
 * 删除申请
 */
async function delApply(e: MatOutStorePickApplyVo) {
	await showDelConfirm()
	await deleteMatPickApply(e.id!.toString()!)
	ElMessage.success("操作成功")
	handleUpdate()
}

function getQueryData(e?: any) {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	const bpmStatusMap = [
		`${appStatus.pendingApproval},${appStatus.rejected}`,
		appStatus.underApproval,
		appStatus.approved
	]

	queryData.value = e
	fetchParam.value = {
		...fetchParam.value,
		...e,
		bpmStatus: bpmStatusMap[activatedTab.value]
	}

	handleUpdate()
}

async function handleTabChanged(tab: any) {
	activeName.value = tab.paneName
	activatedTab.value = Number(tab.index)

	getQueryData()
}

/**
 * 显示创建申请 drawer
 */
function showCreateDrawer() {
	matGetApplyEditorVisible.value = true
	drawerType.value = IModalType.create
	editingMatApplyId.value = undefined
}

/**
 * 获取线路配置
 */
function getLineOptions() {
	BaseLineSysApi.getLineOptions().then((r) => (lineOptions.value = r))
}

/**
 * 获取段区配置
 */
function getDepotList() {
	BaseLineSysApi.getDepotList().then((res) => {
		depotList.value = res
	})
}

/**
 * drawer save handler
 *
 * 处理 drawer 保存之后的逻辑
 *
 * @param id - table row id
 */
function handleDrawerSave(id: any, visible = false) {
	drawerType.value = IModalType.edit
	matGetApplyEditorVisible.value = visible
	editingMatApplyId.value = id
	handleUpdate()
}

/**
 * 更新主表/ tab 状态统计
 */
async function handleUpdate() {
	fetchTableData()
	statusCnt.value = await getMatPickApplyBpmStatusCnt(
		omit(
			{
				...fetchParam.value
			},
			"bpmStatus",
			"currentPage",
			"pageSize"
		) as any
	)
}

/**
 * 排序
 * @param column
 * @param prop 字段名称
 * @param order ascending 升序 descending 降序
 */
function handleSortChange(column: any, prop: any, order: any) {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		sord:
			order === "descending" ? "desc" : order === "ascending" ? "asc" : "desc",
		sidx: order ? prop : "createdDate" // 排序字段
	}

	fetchTableData()
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
