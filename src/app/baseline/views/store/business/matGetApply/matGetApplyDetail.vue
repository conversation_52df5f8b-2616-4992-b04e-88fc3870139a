<!-- 库存管理-业务管理-领料申请详情 -->
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<div class="drawer-column left" style="width: 310px">
			<Title :title="titleConf" />

			<!-- 领料申请信息 -->
			<el-descriptions size="small" :column="1" border class="content">
				<el-descriptions-item
					v-for="desc in descOptions"
					:key="desc.label"
					:label="desc.label"
				>
					<cost-tag v-if="desc.key === 'amount'" :value="descData.amount" />
					<span v-else>
						{{ getNumDefByNumKey(desc.key, descData[desc.key]) }}
					</span>
				</el-descriptions-item>
			</el-descriptions>
		</div>
		<div
			class="drawer-column right"
			style="width: calc(100% - 310px)"
			:class="{ pdr10: !footerBtnVisible }"
		>
			<div class="rows">
				<Title :title="extTitleConf">
					<Tabs
						style="margin-right: auto"
						:tabs="['物资明细', '相关附件']"
						@on-tab-change="handleTabChange"
					/>
				</Title>

				<Query
					:query-arr-list="(queryArrList as any)"
					style="margin: 10px 10px -10px"
					@get-query-data="handleQuery"
					v-if="activatedTab === 0"
				/>

				<pitaya-table
					v-if="activatedTab === 0"
					ref="tableRef"
					:columns="(tbInit.tableProp as any)"
					:table-data="tableData"
					:need-selection="false"
					:single-select="false"
					:need-index="true"
					:need-pagination="true"
					:total="pageTotal"
					:table-loading="tableLoading"
					@on-selection-change="selectedTableList = $event"
					@on-current-page-change="onCurrentPageChange"
				>
					<!-- 物资性质 -->
					<template #attribute="{ rowData }">
						<dict-tag
							:options="DictApi.getMatAttr()"
							:value="rowData.attribute"
						/>
					</template>

					<template #useUnit="{ rowData }">
						{{
							dictFilter("INVENTORY_UNIT", rowData.useUnit)?.subitemName ||
							"---"
						}}
					</template>
					<template #amount="{ rowData }">
						<cost-tag :value="rowData.amount" />
					</template>
					<template #batchNum="{ rowData }">
						<link-tag
							:value="rowData.batchNum"
							@on-click="showDeliveryGoodsDetail(rowData)"
						/>
					</template>
				</pitaya-table>

				<table-file
					v-else
					:business-type="fileBusinessType.matGetApply"
					:business-id="id"
					:mod="
						descData.bpmStatus === appStatus.approved
							? IModalType.edit
							: IModalType.view
					"
				/>
			</div>

			<button-list
				v-if="footerBtnVisible"
				class="footer"
				:button="btnConf"
				@on-btn-click="emit('close')"
			/>
		</div>
	</div>

	<Drawer
		v-model:drawer="deliveryGoodsDetailVisible"
		:size="modalSize.lg"
		destroy-on-close
	>
		<delivery-goods-detail :id="editingTableRowId" />
	</Drawer>
</template>

<script setup lang="ts">
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import { IModalType } from "@/app/baseline/utils/types/common"
import { useTbInit } from "../../../components/tableBase"
import { DictApi, appStatus, fileBusinessType } from "@/app/baseline/api/dict"
import deliveryGoodsDetail from "./deliveryGoodsDetail.vue"
import { modalSize } from "@/app/baseline/utils/layout-config"
import {
	getMatPickApply,
	listMatPickApplyGoodsPaged
} from "@/app/baseline/api/store/mat-get-apply-api"
import {
	MatOutStorePickApplyItemDTO,
	MatOutStorePickApplyItemVo
} from "@/app/baseline/utils/types/mat-get-apply"
import TableFile from "../../../components/tableFile.vue"
import LinkTag from "../../../components/linkTag.vue"
import { useDictInit } from "../../../components/dictBase"
import CostTag from "../../../components/costTag.vue"
import { getNumDefByNumKey, batchFormatterNumView } from "@/app/baseline/utils"

const props = withDefaults(
	defineProps<{
		/**
		 * 领料申请id
		 */
		id?: number

		footerBtnVisible?: boolean
	}>(),
	{ footerBtnVisible: true }
)

const emit = defineEmits<{
	(e: "close"): void
}>()

const editingTableRowId = ref()

const { dictOptions, dictFilter, getDictByCodeList } = useDictInit()

const activatedTab = ref(0)

/**
 * 表单列 过滤器
 */
function descOptionFilter(cols: Record<string, any>[], excludeCols: string[]) {
	return cols.filter((c) => !excludeCols.includes(c.label))
}
const descOptions = computed(() => {
	const all = [
		{
			label: "领料单号",
			key: "code"
		},
		{
			label: "关联业务单号",
			key: "workOrderCode"
		},
		{
			label: "领料单名称",
			key: "label"
		},
		{
			label: "领料用途",
			key: "purpose_view"
		},
		{
			label: "段区",
			key: "depotId_view"
		},
		{
			label: "线路",
			key: "lineNoId_view"
		},
		{
			label: "专业",
			key: "majorId_view"
		},
		{
			label: "出库仓库名称",
			key: "storeLabel"
		},
		{
			label: "物资编码",
			key: "materialCodeNum"
		},
		{
			label: "领料金额（元）",
			key: "amount"
		},
		{
			label: "费用类别",
			key: "expenseCategory_view"
		},
		{
			label: "领料人",
			key: "pickUserId_view"
		},
		{
			label: "申请时间",
			key: "createdDate"
		}
	]
	// 待提交 已驳回
	const bpmStatusMap = [appStatus.pendingApproval, appStatus.rejected]
	if (bpmStatusMap.includes(descData.value.bpmStatus)) {
		return descOptionFilter(all, ["领料金额（元）"])
	} else if (descData.value.purpose == "3") {
		// 领料用途 == 低值物资领料 过滤字段
		return descOptionFilter(all, ["段区", "线路", "专业", "费用类别"])
	} else {
		return all
	}
})

const deliveryGoodsDetailVisible = ref(false)

const drawerLoading = ref(false)

const titleConf = {
	name: ["领料申请"],
	icon: ["fas", "square-share-nodes"]
}

const extTitleConf = {
	name: ["扩展信息"],
	icon: ["fas", "square-share-nodes"]
}

const handleTabChange = (index: number) => {
	activatedTab.value = index

	if (index === 0) {
		fetchParam.value = {
			applyId: props.id || descData.value?.id,
			sord: "desc",
			sidx: "createdDate"
		}
		pageSize.value = 20
		handleQuery()
	}
}

const btnConf = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	}
]

const queryArrList = computed<querySetting[]>(() => [
	{
		name: "物资编码",
		key: "materialCode",
		type: "input",
		placeholder: "请输入物资编码"
	},
	{
		name: "物资名称",
		key: "materialLabel",
		type: "input",
		placeholder: "请输入物资名称"
	},
	{
		name: "规格型号",
		key: "version",
		placeholder: "请输入规格型号",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资性质",
		key: "attribute",
		type: "select",
		children: dictOptions.value.MATERIAL_NATURE,
		placeholder: "请选择物资性质"
	}
])

const descData = ref<any>({})

const tbInit = useTbInit<
	MatOutStorePickApplyItemVo,
	MatOutStorePickApplyItemDTO
>()
const {
	currentPage,
	fetchParam,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	pageSize,
	selectedTableList,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = tbInit

fetchFunc.value = listMatPickApplyGoodsPaged

tbInit.tableProp = computed(() => {
	const defCols: TableColumnType[] = [
		{
			prop: "materialCode",
			label: "物资编码",
			width: 130,
			fixed: "left"
		},
		{
			prop: "materialName",
			label: "物资名称",
			minWidth: 120
		},
		{
			prop: "version",
			label: "规格型号",
			minWidth: 120
		},
		{
			prop: "technicalParameter",
			label: "技术参数",
			minWidth: 120
		},
		{
			prop: "attribute",
			label: "物资性质",
			needSlot: true,
			width: 120
		},
		{
			prop: "useUnit",
			label: "库存单位",
			needSlot: true,
			width: 80
		},
		{
			prop: "num_view",
			label: "领料数量",
			align: "right"
		},
		{
			prop: "amount",
			label: "金额",
			needSlot: true,
			align: "right",
			width: 120
		},
		{
			prop: "outStoreNum_view",
			label: "出库数量",
			align: "right",
			width: 120
		},
		{
			prop: "completeWasteOldNum_view",
			label: "已交旧数量",
			align: "right",
			width: 120
		},
		{
			prop: "completeReturnNum_view",
			label: "已退库数量",
			align: "right",
			width: 120
		},
		{
			prop: "batchNum",
			label: "库存批次",
			needSlot: true,
			width: 100
		}
	]

	// 待提交 已驳回
	const bpmStatusMap = [appStatus.pendingApproval, appStatus.rejected]
	if (bpmStatusMap.includes(descData.value.bpmStatus)) {
		return descOptionFilter(defCols, [
			"库存批次",
			"金额",
			"出库数量",
			"已退库数量",
			"已交旧数量"
		]) as TableColumnType[]
	} else {
		return defCols
	}
})

/**
 * 格式化数量
 */
watch(tableData, () => {
	batchFormatterNumView(tableData.value as any[])
})

function handleQuery(e?: any) {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = { ...fetchParam.value, ...e }
	fetchTableData()
}

onMounted(() => {
	getDictByCodeList(["INVENTORY_UNIT", "MATERIAL_NATURE"])
	fetchParam.value.applyId = props.id
	fetchParam.value.sord = "desc"
	fetchParam.value.sidx = "createdDate"

	fetchTableData()
	getDetail()
})

function getDetail() {
	if (!props.id) {
		return
	}
	drawerLoading.value = true
	getMatPickApply(props.id!)
		.then((r) => {
			descData.value = r
		})
		.finally(() => (drawerLoading.value = false))
}

/**
 * 展示出库物资详情
 */
function showDeliveryGoodsDetail(e?: any) {
	deliveryGoodsDetailVisible.value = true
	editingTableRowId.value = e.id
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
