<!-- 库存管理-业务管理-领料申请编辑 -->
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<div class="drawer-column left" style="width: 310px">
			<Title :title="titleConf" />
			<el-scrollbar class="rows">
				<el-form
					class="content"
					:model="formData"
					:rules="formRules"
					ref="formRef"
					label-position="top"
					:validate-on-rule-change="false"
					label-width="100px"
				>
					<form-element :form-element="formEls" :form-data="formData" />
				</el-form>
			</el-scrollbar>

			<button-list
				class="footer"
				:loading="drawerBtnLoading"
				:button="footerBtnConf"
				@on-btn-click="handleDrawerBtnClick"
			/>
		</div>

		<div
			class="drawer-column right"
			:class="mode === IModalType.create ? 'disabled' : ''"
			style="width: calc(100% - 310px)"
		>
			<Title :title="extTitleConf">
				<Tabs
					style="margin-right: auto; margin-left: 30px"
					:tabs="['物资明细', '相关附件']"
					@on-tab-change="handleTabChange"
				/>
			</Title>

			<el-scrollbar
				v-if="mode === IModalType.edit"
				class="rows mat-get-apply-editor-table-wrapper"
			>
				<Query
					:query-arr-list="(queryArrList as any)"
					style="margin: 10px 10px -10px"
					@get-query-data="handleQuery"
					v-if="activatedTab === 0"
				/>
				<pitaya-table
					v-if="activatedTab === 0"
					ref="tableRef"
					:columns="tableProp"
					:table-data="tableData"
					:need-selection="true"
					:single-select="false"
					:need-index="true"
					:need-pagination="true"
					:cell-class-name="tbCellClassName"
					:total="pageTotal"
					:table-loading="tableLoading"
					@on-selection-change="selectedTableList = $event"
					@on-current-page-change="onCurrentPageChange"
				>
					<template #materialCode="{ rowData }">
						<span
							v-if="
								round(
									toNumber(rowData.lowValueNum ?? 0) -
										toNumber(rowData.lowValueFreezeNum ?? 0),
									4
								) > 0
							"
						>
							<el-tooltip
								class="box-item"
								effect="dark"
								:content="`低值可领数量：${round(
									toNumber(rowData.lowValueNum ?? 0) -
										toNumber(rowData.lowValueFreezeNum ?? 0),
									4
								)}，请去低值模块进行领用`"
								placement="top-start"
							>
								<p class="group-approval_title">
									<font-awesome-icon
										:icon="['fas', 'exclamation-circle']"
										style="color: var(--pitaya-disabled-color)"
									/>
									{{ rowData.materialCode }}
								</p>
							</el-tooltip>
						</span>
						<span v-else>{{ rowData.materialCode }}</span>
					</template>

					<!-- 物资性质 -->
					<template #attribute="{ rowData }">
						<dict-tag
							:options="DictApi.getMatAttr()"
							:value="rowData.attribute"
						/>
					</template>

					<template #useUnit="{ rowData }">
						{{
							dictFilter("INVENTORY_UNIT", rowData.useUnit)?.subitemName ||
							"---"
						}}
					</template>

					<template #num="{ rowData }">
						<el-input
							class="no-arrows"
							v-model="rowData.num"
							@click.stop
							@input="rowData.num = validateAndCorrectInput($event)"
							@change="validateNum(rowData)"
						/>
					</template>

					<template #actions="{ rowData }">
						<el-button v-btn link @click.stop="delMat(rowData)">
							<font-awesome-icon
								:icon="['fas', 'trash-can']"
								style="color: var(--pitaya-btn-background)"
							/>
							<span class="table-inner-btn">移除</span>
						</el-button>
					</template>

					<template #footerOperateLeft>
						<button-list
							:button="tbBtnConf"
							:is-not-radius="true"
							@on-btn-click="goodsSelectorVisible = true"
						/>
					</template>
				</pitaya-table>

				<table-file
					v-else
					:business-type="fileBusinessType.matGetApply"
					:business-id="id"
					:mod="IModalType.edit"
				/>
			</el-scrollbar>
			<button-list
				v-if="mode === IModalType.edit"
				class="footer"
				:button="submitBtnConf"
				:loading="submitBtnLoading"
				@on-btn-click="handleSubmit"
			/>
		</div>
	</div>

	<!-- 工单选择器 -->
	<Drawer
		v-model:drawer="jobOrderSelectorVisible"
		:size="modalSize.lg"
		destroy-on-close
	>
		<job-order-selector
			:selectedIds="[formData.workOrderId]"
			@save="handleJobOrderSelect"
			@close="jobOrderSelectorVisible = false"
		/>
	</Drawer>

	<!-- 物资选择器 -->
	<Drawer
		v-model:drawer="goodsSelectorVisible"
		:size="modalSize.lg"
		destroy-on-close
	>
		<mat-selector
			:table-req-params="{
				applyId: id || formData.id,
				sord: 'asc',
				sidx: 'code'
			}"
			:table-api="listMatPickApplyPageMaterialStore"
			:multiple="true"
			@save="handleAddGoods"
			@close="goodsSelectorVisible = false"
		/>
	</Drawer>

	<!-- 仓库选择 -->
	<Drawer
		v-model:drawer="warehouseSelectorVisible"
		:size="modalSize.lg"
		destroy-on-close
	>
		<!-- 不展示废旧仓库和周转件待修库、危险废旧库 -->
		<store-table
			:selected-ids="[formData.storeId]"
			@on-save="handleStoreSelect"
			:table-api-params="{
				lineId: formData.lineNoId,
				depotId: formData.depotId
			}"
			:filterStoreTypes="[
				IWarehouseType.waste,
				IWarehouseType.rotablesWaitingRepair,
				IWarehouseType.dangerousWaste
			]"
		/>
	</Drawer>

	<!-- 领料人选择 -->
	<Drawer
		v-model:drawer="userSelectorVisible"
		:size="modalSize.lg"
		destroy-on-close
	>
		<user-selector
			:selected-ids="[formData.pickUserId]"
			:table-fetch-params="{
				sysCommunityId: userInfo.companyId
			}"
			@save="handleUserSelect"
			@close="userSelectorVisible = false"
		/>
	</Drawer>
</template>

<script setup lang="ts">
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import {
	IIdempotentTokenType,
	IIdempotentTokenTypePre,
	IModalType
} from "@/app/baseline/utils/types/common"
import formElement from "../../../components/formElement.vue"
import { useTbInit } from "../../../components/tableBase"
import { useMessageBoxInit } from "../../../components/messageBox"
import ButtonList from "@/compontents/ButtonList.vue"
import { inputMaxLength, modalSize } from "@/app/baseline/utils/layout-config"
import {
	DictApi,
	fileBusinessType,
	MaterialPurpose
} from "@/app/baseline/api/dict"
import jobOrderSelector from "../../components/jobOrderSelector.vue"
import { BaseLineSysApi } from "@/app/baseline/api/system"
import storeTable from "../../components/storeTable.vue"
import {
	getModalTypeLabel,
	batchFormatterNumView,
	toFixedTwo
} from "@/app/baseline/utils"
import { FormElementType } from "../../../components/define"
import {
	addMatPickApplyGoods,
	deleteMatPickApplyGoods,
	getMatPickApply,
	listMatPickApplyGoodsPaged,
	saveMatPickApply,
	submitMatPickApply,
	updateMatPickApply,
	updateMatPickApplyGoodsBatch,
	listMatPickApplyPageMaterialStore
} from "@/app/baseline/api/store/mat-get-apply-api"
import {
	debounce,
	filter,
	findIndex,
	first,
	includes,
	round,
	toNumber,
	toString
} from "lodash-es"
import {
	MatOutStorePickApplyItemDTO,
	MatOutStorePickApplyItemVo,
	MatOutStorePickApplyVo
} from "@/app/baseline/utils/types/mat-get-apply"
import { FormInstance, FormItemRule } from "element-plus"
import { useDictInit } from "../../../components/dictBase"
import {
	MatStoreJobOrderVo,
	SystemUserVo
} from "@/app/baseline/utils/types/system"
import { useUserStore } from "@/app/platform/store/modules/user"
import {
	MatStoreVo,
	IWarehouseType
} from "@/app/baseline/utils/types/store-manage"
import matSelector from "./matSelector.vue"
import userSelector from "../../components/userSelector.vue"
import TableFile from "../../../components/tableFile.vue"
import {
	getIdempotentToken,
	requiredValidator,
	validateAndCorrectInput
} from "@/app/baseline/utils/validate"
import { useApplyResultUtils } from "../../hooks/apply-result-utils"
import { ICostCategoryStatus } from "@/app/baseline/utils/types/system-cost-category"

const props = withDefaults(
	defineProps<{
		/**
		 * 领料申请id
		 */
		id: any
		/**
		 * 详情类型 编辑/查看/新建
		 */
		mode?: IModalType
	}>(),
	{
		mode: IModalType.create
	}
)

const emit = defineEmits<{
	(e: "update"): void
	(e: "close"): void
	/**
	 * 保存
	 *
	 * @param id 申请id
	 *
	 * @param visible 此编辑 drawer 是否可见
	 */
	(e: "save", id: any, visible?: boolean): void
}>()

const { userInfo } = storeToRefs(useUserStore())

const lineOptions = ref()
const depotOptions = ref()

/**
 * 保存旧的表单数据
 */
const oldFormData = ref<string>()

const formRef = ref<FormInstance>()

const drawerBtnLoading = ref(false)

const submitBtnDisabled = ref(false)

const goodsSelectorVisible = ref(false)

const warehouseSelectorVisible = ref(false)

const userSelectorVisible = ref(false) // 领料人drawer

const { dictOptions, dictFilter, getDictByCodeList } = useDictInit()

/**
 * 工单选择器 visible
 */
const jobOrderSelectorVisible = ref(false)

const tbBtnConf = [
	{
		name: "添加物资",
		icon: ["fas", "circle-plus"]
	}
]

const submitBtnLoading = ref(false)

/**
 * 提交审核按钮配置
 */
const submitBtnConf = computed(() => [
	{
		name: "提交审核",
		icon: ["fas", "circle-check"],
		disabled:
			props.mode === IModalType.create ||
			submitBtnDisabled.value ||
			tableData.value.length < 1
	}
])

const activatedTab = ref(0)

const drawerLoading = ref(false)

const titleConf = computed(() => ({
	name: [getModalTypeLabel(props.mode, "领料申请")],
	icon: ["fas", "square-share-nodes"]
}))

const extTitleConf = {
	name: ["扩展信息"],
	icon: ["fas", "square-share-nodes"]
}

const handleTabChange = (index: number) => {
	activatedTab.value = index

	if (index === 0) {
		fetchParam.value = {
			applyId: props.id || formData.value?.id,
			sord: "desc",
			sidx: "createdDate"
		}
		pageSize.value = 20
		handleQuery()
	}
}

const formData = ref<MatOutStorePickApplyVo>({})

const formRules: Record<string, FormItemRule> = {
	label: requiredValidator("领料单名称"),
	purpose: requiredValidator("领料用途"),
	//workOrderCode: requiredValidator("工单号"),
	majorId_view: requiredValidator("专业"),
	lineNoId: requiredValidator("线路"),
	pickUserId_view: requiredValidator("领料人"),
	pickSysOrgId_view: requiredValidator("领料部门"),
	depotId: requiredValidator("段区"),
	expenseCategory_view: requiredValidator("费用类别"),
	storeLabel: requiredValidator("出库仓库")
}

const { showDelConfirm, showWarnConfirm } = useMessageBoxInit()

const queryArrList = computed<querySetting[]>(() => [
	{
		name: "物资编码",
		key: "materialCode",
		type: "input",
		placeholder: "请输入物资编码"
	},
	{
		name: "物资名称",
		key: "materialLabel",
		type: "input",
		placeholder: "请输入物资名称"
	},
	{
		name: "规格型号",
		key: "version",
		placeholder: "请输入规格型号",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资性质",
		key: "attribute",
		type: "select",
		children: dictOptions.value.MATERIAL_NATURE,
		placeholder: "请选择物资性质"
	}
])

const {
	currentPage,
	tableCache,
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	pageSize,
	fetchParam,
	selectedTableList,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = useTbInit<MatOutStorePickApplyItemVo, Ref<MatOutStorePickApplyItemDTO>>()

const formDataId = computed(() => {
	return formData.value.id
})
const {
	handleApplyResultByCode,
	errorGoodsList,
	clearTimer,
	cancelRollPolling
} = useApplyResultUtils({
	fetchSubmitApi: submitMatPickApply,
	fetchTableData: handleQuery,
	id: formDataId,
	successCb: () => {
		emit("save", undefined)
	}
})

fetchFunc.value = listMatPickApplyGoodsPaged

tableProp.value = [
	{
		prop: "materialCode",
		label: "物资编码",
		width: 150,
		needSlot: true
	},
	{
		prop: "materialName",
		label: "物资名称"
	},
	{
		prop: "version",
		label: "规格型号"
	},
	{
		prop: "technicalParameter",
		label: "技术参数"
	},
	{
		prop: "attribute",
		label: "物资性质",
		needSlot: true,
		width: 120
	},
	{
		prop: "useUnit",
		label: "库存单位",
		needSlot: true,
		width: 80
	},
	{
		prop: "storeNum_view",
		label: "库存数量",
		align: "right"
	},
	{
		prop: "freezeNum_view",
		label: "冻结量",
		align: "right"
	},
	{
		prop: "canNum_view",
		label: "可领料数量",
		align: "right"
	},
	{
		prop: "num",
		label: "领料数量",
		needSlot: true
	},
	{
		prop: "actions",
		label: "操作",
		width: 100,
		needSlot: true
	}
]

function handleQuery(e?: any) {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = { ...fetchParam.value, ...e }
	fetchTableData()
}

/**
 * 格式化数量
 */
watch(tableData, () => {
	batchFormatterNumView(tableData.value as any[])
})

const formEls = computed<FormElementType[][]>(() => [
	[
		{
			label: "领料单名称",
			name: "label",
			maxlength: inputMaxLength.input
		},
		{
			label: "领料用途",
			name: "purpose",
			type: "select",
			data: filter(
				dictOptions.value.MATERIAL_PURPOSE || [],
				(v) => v.value != MaterialPurpose.lowvalueMaterial
			), // 过滤低值易耗选项
			disabled: props.mode === IModalType.edit,
			clear: false
		},
		{
			label: "选择工单号",
			name: "workOrderCode",
			type: "drawer",
			disabled: props.mode === IModalType.edit,
			clickApi: () => (jobOrderSelectorVisible.value = true)
		},

		{
			label: "线路",
			name: "lineNoId",
			type: "select",
			data: lineOptions.value,
			placeholder: "请选择线路",
			disabled:
				props.mode === IModalType.edit || formData.value?.workOrderId
					? true
					: false,
			change: () => {
				formData.value.storeId = null
				formData.value.storeLabel = null
				setTimeout(() => {
					formRef.value?.clearValidate()
				})
			}
		},
		{
			label: "段区",
			name: "depotId",
			type: "select",
			data: depotOptions.value,
			placeholder: "请选择段区",
			disabled: props.mode === IModalType.edit,
			clear: false,
			change: () => {
				formData.value.storeId = null
				formData.value.storeLabel = null
				setTimeout(() => {
					formRef.value?.clearValidate()
				})
			}
		},
		{
			label: "出库仓库",
			name: "storeLabel",
			type: "drawer",
			disabled: props.mode === IModalType.edit,
			clear: false,
			clickApi: () => (warehouseSelectorVisible.value = true)
		},
		{
			label: "专业",
			name: "majorId_view",
			vname: "majorId",
			type: "treeSelect",
			treeApi: BaseLineSysApi.getProfessionTree,
			placeholder: "请选择专业"
		},
		{
			label: "费用类别",
			name: "expenseCategory_view",
			vname: "expenseCategory",
			type: "treeSelect",
			treeApi: () =>
				BaseLineSysApi.getCostCategoryTree({
					status: ICostCategoryStatus.Started
				}),
			disabled: false,
			clear: false
		},
		{
			label: "领料人",
			name: "pickUserId_view",
			type: "drawer",
			disabled: false,
			clickApi: () => (userSelectorVisible.value = true)
		},
		{
			label: "领料部门",
			name: "pickSysOrgId_view",
			type: "drawer",
			disabled: true,
			clear: false
		},
		{
			label: "申请人",
			name: "createdBy_view",
			type: "input",
			clear: false,
			disabled: true
		},
		{
			label: "申请部门",
			name: "sysOrgId_view",
			type: "input",
			clear: false,
			disabled: true
		},
		{
			label: "领料原因",
			name: "reason",
			type: "textarea",
			disabled: false,
			clear: false,
			maxlength: inputMaxLength.textarea
		}
	]
])

const footerBtnConf = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "保存草稿",
		icon: ["fas", "floppy-disk"]
	}
]

onUnmounted(() => {
	clearTimer()
	cancelRollPolling()
})

onMounted(async () => {
	fetchParam.value.applyId = props.id

	fetchParam.value.sord = "desc"
	fetchParam.value.sidx = "createdDate"
	getDictByCodeList([
		"STORE_TYPE",
		"MATERIAL_PURPOSE",
		"INVENTORY_UNIT",
		"STORE_LEVEL",
		"MATERIAL_NATURE"
	])

	lineOptions.value = await BaseLineSysApi.getLineOptions()
	depotOptions.value = await BaseLineSysApi.getDepotList()

	if (props.mode !== IModalType.create) {
		getDetail()
		fetchTableData()
	}

	initMatPickApplyUserInfo()
})

/**
 * 初始化领料人信息
 */
function initMatPickApplyUserInfo() {
	// 申请人
	formData.value.createdBy_view = userInfo.value.realName as any
	formData.value.createdBy = userInfo.value.userName as any
	// 申请部门
	formData.value.sysOrgId = userInfo.value.orgId as any
	formData.value.sysOrgId_view = userInfo.value.orgName
}

/**
 * 保存草稿 handler
 */
async function handleSaveDraft() {
	if (!formRef.value) {
		return
	}

	formRef.value?.validate(async (valid) => {
		if (!valid) {
			return
		}
		drawerBtnLoading.value = true

		const api =
			props.mode === IModalType.create ? saveMatPickApply : updateMatPickApply
		try {
			let idempotentToken = ""
			if (props.mode === IModalType.create) {
				idempotentToken = getIdempotentToken(
					IIdempotentTokenTypePre.apply,
					IIdempotentTokenType.materialGetApply
				)
			}

			const r = await api(formData.value as any, idempotentToken)

			ElMessage.success("操作成功")
			formData.value.id = r?.id
			formData.value.code = r?.code

			fetchParam.value.applyId = r?.id as any

			oldFormData.value = JSON.stringify(formData.value) // 保存旧的表单数据
			emit("save", r?.id, true)
			handleQuery()
		} finally {
			drawerBtnLoading.value = false
		}
	})
}

// 提交审核逻辑
function handleSubmit() {
	if (!formRef.value) {
		return
	}

	formRef.value?.validate(async (valid) => {
		if (!valid) {
			return
		}
		await showWarnConfirm("请确认是否提交本次数据？")

		submitBtnLoading.value = true
		drawerLoading.value = true
		try {
			// 如果主表有修改，则先更新主表数据
			if (oldFormData.value != JSON.stringify(formData.value)) {
				await updateMatPickApply(formData.value as any)
			}

			const idempotentToken = getIdempotentToken(
				IIdempotentTokenTypePre.other,
				IIdempotentTokenType.materialGetApply,
				formData.value.id
			)

			const { code, msg, data } = await submitMatPickApply(
				formData.value.id!,
				idempotentToken
			)
			if (data && code != 200) {
				handleApplyResultByCode(code, msg, data)
			} else {
				ElMessage.success("操作成功")
				emit("save", undefined)
			}
		} finally {
			submitBtnLoading.value = false
			drawerLoading.value = false
		}
	})
}

/**
 * 物资明细 table 行样式
 *
 * 库存不足物资，用红色标记该行
 */
function tbCellClassName({ row }: any) {
	return includes(errorGoodsList.value, row.id) ? "error" : ""
}

/**
 * 点击 drawer 按钮 handler
 */
function handleDrawerBtnClick(name?: string) {
	if (name === "保存草稿") {
		handleSaveDraft()
		return
	}

	emit("close")
}

/**
 * 获取其他入库详情
 */
function getDetail() {
	drawerLoading.value = true

	// 获取详情
	getMatPickApply(props.id)
		.then((r = {}) => {
			formData.value = { ...r }
			formData.value.purpose = (toString(r.purpose) ?? "") as any

			oldFormData.value = JSON.stringify(formData.value)
		})
		.finally(() => (drawerLoading.value = false))
}

/**
 * 工单选择 handler
 *
 * 选择工单之后，回显 工单id+code+线路
 */
function handleJobOrderSelect(e: MatStoreJobOrderVo) {
	formData.value.storeId = null
	formData.value.storeLabel = null
	setTimeout(() => {
		formRef.value?.clearValidate()
	})

	if (!e) {
		formData.value.workOrderId = null
		formData.value.workOrderCode = null

		jobOrderSelectorVisible.value = false
		formData.value.lineNoId = null
		formData.value.storeId = null
		formData.value.storeLabel = null

		return false
	}

	formData.value.workOrderId = e.id
	formData.value.workOrderCode = e.jobNo as any
	formData.value.lineNoId = e.lineNoId

	jobOrderSelectorVisible.value = false
}

function handleStoreSelect(btnName: string, e?: MatStoreVo) {
	if (btnName === "保存") {
		formData.value.storeId = e?.id
		formData.value.storeLabel = e?.label as any
	}
	warehouseSelectorVisible.value = false
}

/**
 * 领料人选择 handler
 * @param e
 */
function handleUserSelect(e?: SystemUserVo[]) {
	const selectRow = first(e)
	if (!selectRow?.username) {
		formData.value.pickUserId = null
		formData.value.pickUserId_view = null
		formData.value.pickUserName = null
		formData.value.pickSysOrgId = null
		formData.value.pickSysOrgId_view = null
		userSelectorVisible.value = false
		return false
	}
	// 领料人
	formData.value.pickUserId = selectRow.username as any
	formData.value.pickUserId_view = selectRow.realname as any
	formData.value.pickUserName = selectRow.username as any

	// 领料部门
	formData.value.pickSysOrgId = selectRow.sysOrgId
	formData.value.pickSysOrgId_view = selectRow.sysOrgId_view

	userSelectorVisible.value = false
}
/**
 * 添加物资 handler
 */
async function handleAddGoods(e?: any) {
	const idempotentToken = getIdempotentToken(
		IIdempotentTokenTypePre.other,
		IIdempotentTokenType.materialGetApply,
		formData.value.id
	)
	await addMatPickApplyGoods(
		{
			applyId: props.id || formData.value.id,
			itemList: e.map((item: any) => ({
				materialId: item.materialId,
				materialCode: item.materialNo,
				materialLabel: item.materialName,
				version: item.version,
				attribute: item.attribute
			}))
		},
		idempotentToken
	)

	ElMessage.success("操作成功")
	handleQuery()
	emit("update")
	goodsSelectorVisible.value = false
}

/**
 * 删除物资
 */
async function delMat(e: any) {
	await showDelConfirm()
	await deleteMatPickApplyGoods(e.id)
	ElMessage.success("操作成功")
	fetchTableData()
	emit("update")
}

/**
 * 验证领料数量
 *
 * - 最大领料数量 > 0，取值范围为 1~最大领料数量
 */
function validateNum(e: MatOutStorePickApplyItemVo) {
	const num = toNumber(e.num)

	// 最大领料数量 = (库存数量 -低值数量) - (冻结数量 - 低值冻结数量)

	const canGetNum = toNumber(e.canNum)

	e.num = num
	if (canGetNum < num) {
		// 领料数量不能大于最大领料数量
		ElMessage.warning("领料数量不能大于可领料数量")
		e.num = round(canGetNum, 4)
	} else if (num <= 0) {
		const oldRow = tableCache.find((v) => v.id == e.id)
		e.num = oldRow.num

		return ElMessage.warning("领料数量不能小于等于0！")
	}

	submitBtnDisabled.value = true

	// 符合校验，变更领料数量
	updateMatGetNum(e)
}

/**
 * 更新领料物资数量 api
 */
const updateMatGetNum = debounce(async (e: MatOutStorePickApplyItemVo) => {
	try {
		await updateMatPickApplyGoodsBatch([{ id: e.id, num: e.num }])

		//  更新 tableCache
		const rowIdx = findIndex(tableCache, (v) => v.id === e.id)
		if (rowIdx !== -1) {
			tableCache.splice(rowIdx, 1, { ...e })
		}

		ElMessage.success("操作成功")
		//getDetail()
	} finally {
		submitBtnDisabled.value = false
	}
}, 300)
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.mat-get-apply-editor-table-wrapper {
	&:deep(.pitaya-table) {
		.error {
			.column-inner-item,
			.cell,
			.el-input__inner {
				color: red;
			}
		}
	}
}
</style>
