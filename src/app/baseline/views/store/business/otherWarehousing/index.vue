<!-- 库存管理-业务管理-其他入库 -->
<template>
	<div class="app-container">
		<div class="whole-frame">
			<model-frame class="top-frame">
				<Query
					:query-arr-list="(queryArrList as any)"
					@get-query-data="handleQuery"
					class="ml10"
				/>
			</model-frame>
			<model-frame class="bottom-frame">
				<Title
					:title="titleConf"
					:button="(titleBtnConf as any)"
					@on-btn-click="handleClickCreateApplyBtn"
				>
					<div class="app-tabs-wrapper">
						<el-tabs v-model="activeName" @tab-click="handleTabChanged">
							<el-tab-pane
								v-for="(tab, index) in tabsConf"
								:key="index"
								:label="`${tab}（${statusCnt[index] ?? 0}）`"
								:name="tab"
								:index="tab"
							/>
						</el-tabs>
					</div>

					<!-- <Tabs
						style="margin-left: 30px; margin-right: auto"
						:tabs="['待处理', '已完成']"
						@on-tab-change="handleTabChanged"
					/> -->
				</Title>

				<pitaya-table
					ref="tableRef"
					:columns="tableProp"
					:table-data="tableData"
					:need-selection="activatedTab === 0"
					:single-select="true"
					:need-index="true"
					:need-pagination="true"
					:total="pageTotal"
					:table-loading="tableLoading"
					@on-selection-change="selectedTableList = $event"
					@on-current-page-change="onCurrentPageChange"
					@on-table-sort-change="handleSortChange"
				>
					<!-- 线路 -->
					<template #lineNoId="{ rowData }">
						<line-tag :options="lineList" :value="rowData.lineNoId" />
					</template>

					<template #bpmStatus="{ rowData }">
						<dict-tag
							:options="DictApi.getBpmStatus()"
							:value="rowData.bpmStatus"
						/>
					</template>
					<!-- 关联返修申请单 -->
					<template #preBusinessCode="{ rowData }">
						<link-tag
							:value="rowData.preBusinessCode"
							@click.stop="onRowBusinessPick(rowData)"
						/>
					</template>
					<template #actions="{ rowData }">
						<slot
							v-if="
								(canShowTableEditAction(rowData) &&
									isCheckPermission(
										powerList.storeBusinessOtherWarehouseApplyBtnEdit
									) &&
									hasPermi(rowData.createdBy)) ||
								isCheckPermission(
									powerList.storeBusinessOtherWarehouseApplyBtnPreview
								) ||
								(canShowTableEditAction(rowData) &&
									isCheckPermission(
										powerList.storeBusinessOtherWarehouseApplyBtnDrop
									) &&
									hasPermi(rowData.createdBy))
							"
						>
							<el-button
								v-if="
									canShowTableEditAction(rowData) &&
									isCheckPermission(
										powerList.storeBusinessOtherWarehouseApplyBtnEdit
									) &&
									hasPermi(rowData.createdBy)
								"
								v-btn
								link
								@click.stop="editDetail(rowData)"
								:disabled="
									checkPermission(
										powerList.storeBusinessOtherWarehouseApplyBtnEdit
									)
								"
							>
								<font-awesome-icon
									:icon="['fas', 'pen-to-square']"
									style="color: var(--pitaya-btn-background)"
								/>
								<span class="table-inner-btn">编辑</span>
							</el-button>

							<el-button
								v-btn
								link
								@click.stop="showDetail(rowData)"
								:disabled="
									checkPermission(
										powerList.storeBusinessOtherWarehouseApplyBtnPreview
									)
								"
								v-if="
									isCheckPermission(
										powerList.storeBusinessOtherWarehouseApplyBtnPreview
									)
								"
							>
								<font-awesome-icon
									:icon="['fas', 'eye']"
									style="color: var(--pitaya-btn-background)"
								/>
								<span class="table-inner-btn">查看</span>
							</el-button>

							<el-button
								v-if="
									canShowTableEditAction(rowData) &&
									isCheckPermission(
										powerList.storeBusinessOtherWarehouseApplyBtnDrop
									) &&
									hasPermi(rowData.createdBy)
								"
								v-btn
								link
								@click.stop="delRow(rowData)"
								:disabled="
									checkPermission(
										powerList.storeBusinessOtherWarehouseApplyBtnDrop
									)
								"
							>
								<font-awesome-icon
									:icon="['fas', 'trash-can']"
									style="color: var(--pitaya-btn-background)"
								/>
								<span class="table-inner-btn">移除</span>
							</el-button>
						</slot>
						<slot v-else>---</slot>
					</template>

					<template v-if="activatedTab === 0" #footerOperateLeft>
						<button-list
							:is-not-radius="true"
							:button="tableSuffixBtnConf"
							@on-btn-click="pushInspectEditorVisible = true"
						/>
					</template>
				</pitaya-table>
			</model-frame>
		</div>

		<!-- 其他申请详情 -->
		<Drawer
			v-model:drawer="otherApplyDetailVisible"
			:size="modalSize.xl"
			destroy-on-close
		>
			<other-apply-detail
				:id="editingTableRowId"
				@close="otherApplyDetailVisible = false"
			/>
		</Drawer>

		<!-- 其他申请编辑 -->
		<Drawer
			v-model:drawer="otherApplyEditorVisible"
			:size="modalSize.xl"
			destroy-on-close
		>
			<other-apply-editor
				:mode="editorMode"
				:id="editingTableRowId"
				@close="otherApplyEditorVisible = false"
				@save="handleDrawerSave"
				@update="fetchTableData"
			/>
		</Drawer>

		<!-- 推送质检 -->
		<Drawer
			v-model:drawer="pushInspectEditorVisible"
			:size="modalSize.xl"
			destroy-on-close
		>
			<push-inspect-editor
				:id="selectedTableList?.[0]?.id"
				:mode="IModalType.edit"
				@save="handleSubmitOtherApply"
			/>
		</Drawer>

		<!-- 查看关联返修申请单  -->
		<Drawer
			:size="modalSize.xl"
			v-model:drawer="businessVisible"
			:destroyOnClose="true"
		>
			<repairDetail
				:id="curRowId"
				:mode="IModalType.view"
				@close="businessVisible = false"
			/>
		</Drawer>

		<!-- 审批、查看弹窗 -->
		<Drawer
			:size="modalSize.xxl"
			v-model:drawer="approvedVisible"
			:destroyOnClose="true"
		>
			<approved-drawer
				:mod="editorMode"
				:task-value="editTask"
				@on-save-or-close="approvedVisible = false"
			/>
		</Drawer>
	</div>
</template>

<script setup lang="ts">
import {
	checkPermission,
	isCheckPermission
} from "@/app/platform/utils/permission"
import { powerList } from "../../../components/define.d"

import Query from "@/compontents/Query.vue"
import { useTbInit } from "../../../components/tableBase"
import { modalSize } from "@/app/baseline/utils/layout-config"
import { useDictInit } from "../../../components/dictBase"
import { first, omit } from "lodash-es"
import {
	deleteMatInStoreOtherApply,
	getMatInStoreOtherApplyBmpStatusCnt,
	listMatInStoreOtherApplyPaged
} from "@/app/baseline/api/store/other-warehousing-api"
import { IWarehousingStatus } from "@/app/baseline/utils/types/warehousing-apply"
import otherApplyEditor from "./otherApplyEditor.vue"
import {
	MatInStoreOtherApplyDTO,
	MatInStoreOtherApplyVo
} from "@/app/baseline/utils/types/other-warehousing"
import { IModalType, LineVo } from "@/app/baseline/utils/types/common"
import { useMessageBoxInit } from "../../../components/messageBox"
import { DictApi, appStatus } from "@/app/baseline/api/dict"
import pushInspectEditor from "./pushInspectEditor.vue"
import dictTag from "../../../components/dictTag.vue"
import otherApplyDetail from "./otherApplyDetail.vue"
import { listMatStoragePaged } from "@/app/baseline/api/store/manage-api"
import LinkTag from "@/app/baseline/views/components/linkTag.vue"
import LineTag from "@/app/baseline/views/components/lineTag.vue"
import repairDetail from "@/app/baseline/views/waste/repairApply/repairDetail.vue"
import { hasPermi } from "@/app/baseline/utils"
import { BaseLineSysApi, getTaskByBusinessIds } from "@/app/baseline/api/system"
import ApprovedDrawer from "@/app/baseline/views/components/approvedDrawer.vue"

const titleConf = {
	name: ["其他入库"],
	icon: ["fas", "square-share-nodes"]
}

const tabsConf = ["待处理", "已完成"]
const activeName = ref<string>(tabsConf[0])
const statusCnt = ref<number[]>([])

// 线路列表
const lineList = ref<LineVo[]>([])

/**
 * 其他入库编辑 编辑模式
 */
const editorMode = ref(IModalType.edit)
/**
 * 推送质检编辑 visible
 */
const pushInspectEditorVisible = ref(false)

/**
 * 编辑中的 row id
 */
const editingTableRowId = ref()

const activatedTab = ref(0)

const { showDelConfirm } = useMessageBoxInit()

const { dictOptions, getDictByCodeList } = useDictInit()

const otherApplyEditorVisible = ref(false)
const otherApplyDetailVisible = ref(false)

const approvedVisible = ref(false)
const editTask = ref<Record<string, any>>()

const titleBtnConf = [
	{
		name: "新建其他入库",
		roles: powerList.storeBusinessOtherWarehouseApplyBtnCreate,
		icon: ["fas", "circle-plus"]
	}
]

const queryArrList = computed<querySetting[]>(() => [
	{
		name: "其他入库单号",
		key: "code",
		type: "input",
		placeholder: "请输入其他入库单号"
	},
	{
		name: "其他入库名称",
		key: "label",
		type: "input",
		placeholder: "请输入其他入库名称"
	},
	{
		name: "申请人",
		key: "createdName",
		type: "input",
		placeholder: "请输入申请人"
	},
	{
		name: "入库类型",
		key: "type",
		type: "select",
		placeholder: "请选择",
		children: dictOptions.value["IN_STORE_OTHER_TYPE"]
	},
	{
		name: "仓库",
		key: "storeId",
		placeholder: "请选择",
		type: "tableSelect",
		tableInfo: [
			{
				title: "请选择仓库",
				tableApi: listMatStoragePaged, //表格接口
				tableColumns: [
					{ label: "仓库编码", prop: "code", width: 120 },
					{ label: "仓库名称", prop: "label" },
					{
						label: "仓库级别",
						prop: "level_view",
						width: 100
					},
					{
						label: "仓库类型",
						prop: "type_view",
						width: 120
					},
					{
						label: "所属段区",
						prop: "depotId_view",
						width: 150
					}
				],
				queryArrList: [
					{
						name: "仓库编码",
						key: "code",
						type: "input",
						placeholder: "请输入仓库编码"
					},
					{
						name: "仓库名称",
						key: "label",
						type: "input",
						placeholder: "请输入仓库名称"
					}
				],
				params: {
					currentPage: 1,
					pageSize: 20
				}, //表格接口参数

				labelName: {
					key: "id", //回显标识
					label: "label", //input框要展示的字段
					value: "id" //要给后台传的字段
				}
			}
		]
	},
	{
		name: "线路",
		key: "lineNoId",
		type: "select",
		children: lineList.value,
		placeholder: "请选择线路"
	},
	{
		name: "专业",
		key: "majorId",
		type: "treeSelect",
		treeApi: () => BaseLineSysApi.getProfessionTree(),
		placeholder: "请选择专业"
	}
])

const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	currentPage,
	selectedTableList,
	fetchTableData,
	fetchFunc,
	fetchParam,
	onCurrentPageChange
} = useTbInit<MatInStoreOtherApplyVo, MatInStoreOtherApplyDTO>()

fetchFunc.value = listMatInStoreOtherApplyPaged

fetchParam.value = {
	status: IWarehousingStatus.pending,
	sord: "desc",
	sidx: "createdDate"
}

const businessVisible = ref(false)
const curRowId = ref("")
const onRowBusinessPick = (row: any) => {
	if (!row.preBusinessId) {
		return false
	}
	curRowId.value = row.preBusinessId
	businessVisible.value = true
}

/**
 * 质检按钮
 */
const tableSuffixBtnConf = computed(() => {
	const hasApproved =
		first(selectedTableList.value)?.bpmStatus === appStatus.approved

	return [
		{
			name: "分配质检",
			icon: ["fas", "stamp"],
			roles: powerList.storeBusinessOtherWarehouseApplyBtnQuality,
			/**
			 * 只有已审批的才可以推送质检
			 */
			disabled: !hasApproved
		}
	]
})

tableProp.value = [
	{
		prop: "code",
		label: "其他入库申请单号",
		width: 180,
		sortable: true
	},
	{
		prop: "label",
		label: "其他入库名称"
	},
	{
		prop: "type_view",
		label: "入库类型",
		width: 100
	},
	{
		prop: "preBusinessCode",
		label: "关联返修申请单",
		needSlot: true,
		width: 180
	},
	{
		prop: "storeName",
		label: "入库仓库名称"
	},
	{
		prop: "majorId_view",
		label: "专业"
	},
	{
		prop: "lineNoId",
		label: "线路",
		needSlot: true
	},
	{
		prop: "materialCodeNum",
		label: "物资编码数量"
	},
	{
		prop: "reason",
		label: "入库原因说明"
	},
	{
		prop: "bpmStatus",
		label: "审批状态",
		needSlot: true,
		width: 100
	},
	{
		prop: "createdBy_view",
		label: "申请人",
		width: 100
	},
	{
		prop: "createdDate",
		label: "申请时间",
		width: 150,
		sortable: true
	},
	{
		prop: "actions",
		label: "操作",
		needSlot: true,
		width: 200
	}
]

/**
 * 获取线路配置
 */
function getLineList() {
	BaseLineSysApi.getLineOptions().then((res) => {
		lineList.value = res
	})
}

onMounted(() => {
	getDictByCodeList(["IN_STORE_OTHER_TYPE"])
	getLineList()
	handleQuery()
})

/**
 * table 操作按钮 编辑/移除 可见性
 *
 * 已完成/审批中不能操作
 */
function canShowTableEditAction(e: MatInStoreOtherApplyVo) {
	/**
	 * 审核状态相关的隐藏规则
	 */
	const approvalStatusRule = [
		appStatus.pendingApproval,
		appStatus.rejected
	].includes(e.bpmStatus as appStatus)

	/**
	 * 是否为已完成状态
	 */
	const isCompleted = activatedTab.value === 1

	if (isCompleted) {
		return false
	}

	return approvalStatusRule
}

/**
 * tab change handler
 */
function handleTabChanged(tab: any) {
	activeName.value = tab.paneName
	activatedTab.value = Number(tab.index)

	handleQuery()
}

function handleQuery(e?: any) {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		...e,
		status:
			activatedTab.value === 0
				? IWarehousingStatus.pending
				: IWarehousingStatus.completed
	}
	handleUpdate()
}

/**
 * drawer save handler
 *
 * 处理 drawer 保存之后的逻辑
 *
 * @param id - table row id
 */
function handleDrawerSave(id: any, visible = false) {
	editorMode.value = IModalType.edit
	otherApplyEditorVisible.value = visible
	editingTableRowId.value = id
	handleUpdate()
}
/**
 * 提交申请 handler
 */
function handleSubmitOtherApply() {
	otherApplyEditorVisible.value = false
	pushInspectEditorVisible.value = false
	handleUpdate()
}

/**
 * 展示申请详情
 */
async function showDetail(e?: MatInStoreOtherApplyVo) {
	editingTableRowId.value = e?.id
	editorMode.value = IModalType.view
	/* otherApplyDetailVisible.value = true */

	if (e?.bpmStatus == appStatus.pendingApproval) {
		otherApplyDetailVisible.value = true
	} else {
		const res = await getTaskByBusinessIds({
			businessIds: [e?.id],
			camundaKey: "other"
		})

		if (Array.isArray(res) && res.length > 0) {
			editTask.value = { ...res[res.length - 1] }
			approvedVisible.value = true
		} else {
			otherApplyDetailVisible.value = true
		}
	}
}

/**
 * 编辑申请
 */
function editDetail(e?: MatInStoreOtherApplyVo) {
	editorMode.value = IModalType.edit
	editingTableRowId.value = e?.id
	otherApplyEditorVisible.value = true
}

/**
 * 点击新建申请按钮
 */
function handleClickCreateApplyBtn() {
	editorMode.value = IModalType.create
	otherApplyEditorVisible.value = true
	editingTableRowId.value = undefined
}

/**
 * 删除申请
 */
async function delRow(e: MatInStoreOtherApplyVo) {
	await showDelConfirm()
	await deleteMatInStoreOtherApply(e.id as any)
	ElMessage.success("操作成功")
	handleUpdate()
}

/**
 * 更新主表/ tab 状态统计
 */
async function handleUpdate() {
	fetchTableData()
	statusCnt.value = await getMatInStoreOtherApplyBmpStatusCnt(
		omit(
			{
				...fetchParam.value
			},
			"status",
			"currentPage",
			"pageSize"
		) as any
	)
}

/**
 * 排序
 * @param column
 * @param prop 字段名称
 * @param order ascending 升序 descending 降序
 */
function handleSortChange(column: any, prop: any, order: any) {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		sord:
			order === "descending" ? "desc" : order === "ascending" ? "asc" : "desc",
		sidx: order ? prop : "createdDate" // 排序字段
	}

	fetchTableData()
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
