<!-- 其他入库申请 详情 -->
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<div class="drawer-column left" style="width: 300px">
			<Title :title="titleConf" />

			<el-descriptions size="small" :column="1" border class="content">
				<el-descriptions-item
					v-for="desc in descList"
					:key="desc.label"
					:label="desc.label"
				>
					<!-- <cost-tag v-if="desc.key === 'amount'" :value="applyData?.amount" /> -->
					<span>
						{{ applyData?.[desc.key] || "----" }}
					</span>
				</el-descriptions-item>
			</el-descriptions>
		</div>
		<div
			class="drawer-column right"
			style="width: calc(100% - 300px)"
			:class="{ pdr10: !footerBtnVisible }"
		>
			<div class="rows">
				<Title :title="exTitleConf">
					<Tabs
						style="margin-right: auto"
						:tabs="['物资明细', '相关附件']"
						@on-tab-change="handleTabChange"
					/>
				</Title>

				<Query
					:query-arr-list="(queryArrList as any)"
					style="margin: 10px 10px -10px"
					@get-query-data="handleQuery"
					v-if="activatedTab === 0"
				/>

				<pitaya-table
					v-if="activatedTab === 0"
					ref="tableRef"
					:columns="(tbInit.tableProp as any)"
					:table-data="tableData"
					:need-selection="false"
					:single-select="false"
					:need-index="true"
					:need-pagination="true"
					:total="pageTotal"
					:table-loading="tableLoading"
					@on-selection-change="selectedTableList = $event"
					@on-current-page-change="onCurrentPageChange"
				>
					<!-- 物资性质 -->
					<template #attribute="{ rowData }">
						<dict-tag
							:options="DictApi.getMatAttr()"
							:value="rowData.attribute"
						/>
					</template>

					<template #useUnit="{ rowData }">
						{{
							dictFilter("INVENTORY_UNIT", rowData.useUnit)?.subitemName ||
							"---"
						}}
					</template>
					<!-- <template #amount="{ rowData }">
						<cost-tag :value="rowData?.amount" />
					</template> -->
				</pitaya-table>
				<!-- 相关附件 table -->
				<table-file
					v-else
					:business-type="fileBusinessType.otherWarehousingApply"
					:business-id="id"
					:mod="IModalType.view"
				/>
			</div>

			<button-list
				v-if="footerBtnVisible"
				class="footer"
				:button="btnConf"
				@on-btn-click="emit('close')"
			/>
		</div>
	</div>
</template>

<script setup lang="ts">
import {
	IWarehousingType,
	MatInStoreApplyVo
} from "@/app/baseline/utils/types/warehousing-apply"
import tableFile from "../../../components/tableFile.vue"
import { appStatus, DictApi, fileBusinessType } from "@/app/baseline/api/dict"
import { IModalType } from "@/app/baseline/utils/types/common"
import {
	getMatInStoreOtherApply,
	listMatInStoreOtherApplyGoodsPaged
} from "@/app/baseline/api/store/other-warehousing-api"
import { useTbInit } from "../../../components/tableBase"
import {
	MatInStoreOtherApplyItemDTO,
	MatInStoreOtherApplyItemVo
} from "@/app/baseline/utils/types/other-warehousing"
// import CostTag from "../../../components/costTag.vue"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import { useDictInit } from "../../../components/dictBase"
import { batchFormatterNumView } from "@/app/baseline/utils"

const props = withDefaults(
	defineProps<{
		/**
		 * 申请id
		 */
		id: any

		footerBtnVisible?: boolean
	}>(),
	{ footerBtnVisible: true }
)

const emit = defineEmits<{
	(e: "close"): void
}>()

const titleConf = {
	name: ["其他入库申请"],
	icon: ["fas", "square-share-nodes"]
}

const exTitleConf = {
	name: ["扩展信息"],
	icon: ["fas", "square-share-nodes"]
}

const handleTabChange = (index: number) => {
	activatedTab.value = index

	if (index === 0) {
		fetchParam.value = {
			applyId: props.id || applyData.value?.id,
			sord: "desc",
			sidx: "createdDate"
		}
		pageSize.value = 20
		handleQuery()
	}
}

const activatedTab = ref(0)

const drawerLoading = ref()

const { dictOptions, dictFilter, getDictByCodeList } = useDictInit()

const applyData = ref<MatInStoreApplyVo>()

const descList = computed(() => {
	const ls = [
		{
			label: "入库单号",
			key: "code"
		},
		{
			label: "入库名称",
			key: "label"
		},
		{
			label: "入库类型",
			key: "type_view"
		},
		{
			label: "关联返修申请单",
			key: "preBusinessCode"
		},
		{
			label: "入库仓库名称",
			key: "storeName"
		},
		{
			label: "专业",
			key: "majorId_view"
		},
		{
			label: "线路",
			key: "lineNoId_view"
		},
		{
			label: "质保期",
			key: "warrantyPeriod_view"
		},
		/* {
		label: "入库金额",
		key: "amount"
	}, */
		{
			label: "申请人",
			key: "createdBy_view"
		},
		{
			label: "申请时间",
			key: "createdDate"
		},
		{
			label: "入库原因说明",
			key: "reason"
		}
	]
	/**
	 * type为 交旧返修入库 时才展示关联返修申请单字段
	 */
	switch (applyData.value?.type) {
		case IWarehousingType.back:
			return ls.filter((v) => v.label !== "关联返修申请单")
		case IWarehousingType.parts:
			return ls.filter((v) => v.label !== "关联返修申请单")
		case IWarehousingType.repair:
			return ls.filter((v) => v.label !== "线路" && v.label !== "专业")
		default:
			return ls.filter(
				(v) =>
					v.label !== "线路" &&
					v.label !== "专业" &&
					v.label !== "关联返修申请单"
			)
	}
})

const btnConf = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	}
]

const queryArrList = computed<querySetting[]>(() => [
	{
		name: "物资编码",
		key: "materialCode",
		type: "input",
		placeholder: "请输入物资编码"
	},
	{
		name: "物资名称",
		key: "materialLabel",
		type: "input",
		placeholder: "请输入物资名称"
	},
	{
		name: "规格型号",
		key: "version",
		placeholder: "请输入规格型号",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资性质",
		key: "attribute",
		type: "select",
		children: dictOptions.value.MATERIAL_NATURE,
		placeholder: "请选择物资性质"
	}
])

const tbInit = useTbInit<
	MatInStoreOtherApplyItemVo,
	MatInStoreOtherApplyItemDTO
>()
const {
	currentPage,
	fetchParam,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	pageSize,
	selectedTableList,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = tbInit

tbInit.tableProp = computed(() => {
	const defTableColumns: TableColumnType[] = [
		{
			prop: "materialCode",
			label: "物资编码"
		},
		{
			prop: "materialName",
			label: "物资名称"
		},
		{
			prop: "version",
			label: "规格型号"
		},
		{
			prop: "technicalParameter",
			label: "技术参数"
		},
		{
			prop: "attribute",
			label: "物资性质",
			needSlot: true,
			width: 120
		},
		{
			prop: "useUnit",
			label: "库存单位",
			needSlot: true,
			width: 80
		},
		{
			prop: "num_view",
			label: "申请入库数量",
			align: "right"
		},
		{
			prop: "toStoreNum_view",
			label: "已入库数量",
			align: "right"
		},
		{
			prop: "qualifiedNum_view",
			label: "合格量",
			align: "right"
		},
		{
			prop: "unQualifiedNum_view",
			label: "不合格量",
			align: "right"
		},
		{
			prop: "warrantyPeriod_view",
			label: "质保期"
		},
		{
			prop: "inspectionPersonId_view",
			label: "质检员"
		}
	]
	if (applyData.value?.type !== IWarehousingType.repair) {
		if (applyData.value?.bpmStatus === appStatus.approved) {
			return applyData.value?.status == "2"
				? defTableColumns
				: defTableColumns.filter(
						(v) => !["已入库数量", "合格量", "不合格量"].includes(v.label)
				  )
		} else {
			return applyData.value?.status == "2"
				? defTableColumns.filter((v) => v.label !== "质检员")
				: defTableColumns.filter(
						(v) =>
							!["质检员", "已入库数量", "合格量", "不合格量"].includes(v.label)
				  )
		}
	} else {
		if (applyData.value?.bpmStatus === appStatus.approved) {
			return applyData.value?.status == "2"
				? defTableColumns.filter((v) => v.label !== "质保期")
				: defTableColumns.filter(
						(v) =>
							!["质保期", "已入库数量", "合格量", "不合格量"].includes(v.label)
				  )
		} else {
			return applyData.value?.status == "2"
				? defTableColumns.filter(
						(v) => v.label !== "质检员" && v.label !== "质保期"
				  )
				: defTableColumns.filter(
						(v) =>
							![
								"质检员",
								"质保期",
								"已入库数量",
								"合格量",
								"不合格量"
							].includes(v.label)
				  )
		}
	}
})

/**
 * 格式化数量
 */
watch(tableData, () => {
	batchFormatterNumView(tableData.value as any[])
})

fetchFunc.value = listMatInStoreOtherApplyGoodsPaged

function handleQuery(e?: any) {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = { ...fetchParam.value, ...e }
	fetchTableData()
}

onMounted(() => {
	fetchParam.value.sord = "desc"
	fetchParam.value.sidx = "createdDate"

	getDictByCodeList(["INVENTORY_UNIT", "MATERIAL_NATURE"])
	getDetail()
})

function getDetail() {
	fetchParam.value.applyId = props.id
	fetchTableData()

	drawerLoading.value = true
	getMatInStoreOtherApply(props.id)
		.then((r) => (applyData.value = r))
		.finally(() => (drawerLoading.value = false))
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
