<!-- 其他入库-推送质检编辑器 -->
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<div class="drawer-column left" style="width: 300px">
			<Title :title="titleConf" />

			<el-descriptions size="small" :column="1" border class="content">
				<el-descriptions-item
					v-for="desc in descList"
					:key="desc.label"
					:label="desc.label"
				>
					<cost-tag v-if="desc.key === 'amount'" :value="applyData?.amount" />
					<span v-else>{{ applyData?.[desc.key] || "----" }}</span>
				</el-descriptions-item>
			</el-descriptions>
		</div>
		<div class="drawer-column right" style="width: calc(100% - 300px)">
			<div class="rows">
				<Title :title="exTitleConf">
					<Tabs
						style="margin-right: auto; margin-left: 30px"
						:tabs="['物资明细', '相关附件']"
						@on-tab-change="activatedTab = $event"
					/>
				</Title>

				<!-- 物资详情 table -->
				<goods-detail-table
					v-if="activatedTab === 0"
					:columns="(tableColumns as TableColumnType[])"
					:user-options-api="listInspectorPaged"
					:list-goods-api="listMatInStoreOtherApplyGoodsPaged"
					:add-inspector-api="addMatInStoreOtherApplyInspector"
					:apply-id="id"
					:mode="mode"
					:tb-cell-class-name="tbCellClassName"
					ref="matTableRef"
				/>
				<!-- 相关附件 table -->
				<table-file
					v-else
					:business-type="fileBusinessType.warehousingApply"
					:business-id="id"
					:mod="IModalType.view"
				/>
			</div>

			<button-list
				class="footer"
				:button="drawerBtnConf"
				:loading="drawerBtnLoading"
				@on-btn-click="handleDrawerBtnClick"
			/>
		</div>
	</div>
</template>

<script setup lang="ts">
import { MatInStoreApplyVo } from "@/app/baseline/utils/types/warehousing-apply"
import tableFile from "../../../components/tableFile.vue"
import { fileBusinessType } from "@/app/baseline/api/dict"
import {
	IIdempotentTokenType,
	IIdempotentTokenTypePre,
	IModalType
} from "@/app/baseline/utils/types/common"
import {
	addMatInStoreOtherApplyInspector,
	getMatInStoreOtherApply,
	listMatInStoreOtherApplyGoodsPaged,
	pushMatInStoreOtherApplyInspect
} from "@/app/baseline/api/store/other-warehousing-api"
import goodsDetailTable from "../../components/goodsDetailTable.vue"
import CostTag from "../../../components/costTag.vue"
import { listInspectorPaged } from "@/app/baseline/api/system"
import { includes } from "lodash-es"
import { useMessageBoxInit } from "../../../components/messageBox"
import { getIdempotentToken } from "@/app/baseline/utils/validate"

const props = withDefaults(
	defineProps<{
		/**
		 * 申请id
		 */
		id: any

		/**
		 * 模式，编辑/查看
		 */
		mode?: IModalType
	}>(),
	{ mode: IModalType.view }
)

const emit = defineEmits<{
	(e: "close"): void
	(e: "save"): void
}>()

const { showWarnConfirm } = useMessageBoxInit()

const matTableRef = ref()

const drawerBtnLoading = ref(false)

const titleConf = {
	name: ["其他入库申请"],
	icon: ["fas", "square-share-nodes"]
}

const exTitleConf = {
	name: ["扩展信息"],
	icon: ["fas", "square-share-nodes"]
}

const drawerBtnConf = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "推送质检",
		icon: ["fas", "stamp"]
	}
]

const tableColumns = [
	{
		prop: "materialCode",
		label: "物资编码"
	},
	{
		prop: "materialName",
		label: "物资名称"
	},
	{
		prop: "version",
		label: "规格型号"
	},
	{
		prop: "technicalParameter",
		label: "技术参数"
	},
	{
		prop: "attribute",
		label: "物资性质",
		needSlot: true,
		width: 120
	},
	{
		prop: "useUnit",
		label: "库存单位",
		needSlot: true,
		width: 90
	},
	{
		prop: "num_view",
		label: "申请入库数量",
		align: "right"
	},
	{
		prop: "inspectionPersonId_view",
		label: "质检员"
	}
]

const activatedTab = ref(0)

const drawerLoading = ref()

const applyData = ref<MatInStoreApplyVo>()

const descList = ref([
	{
		label: "入库单号",
		key: "code"
	},
	{
		label: "入库仓库名称",
		key: "storeName"
	},
	/* {
		label: "入库金额",
		key: "amount"
	}, */
	{
		label: "申请人",
		key: "createdBy_view"
	},
	{
		label: "申请时间",
		key: "createdDate"
	}
])

/**
 * 物资明细 table 行样式
 *
 * 库存物资异常，用红色标记该行
 */
const errorGoodsIdList = ref<number[]>([])
function tbCellClassName({ row }: any) {
	return includes(errorGoodsIdList.value, row.id) ? "error" : ""
}
onMounted(() => {
	getDetail()
})

function getDetail() {
	getMatInStoreOtherApply(props.id).then((r) => (applyData.value = r))
}

async function handleDrawerBtnClick(name?: string) {
	if (name === "取消") {
		emit("close")
		return
	}

	await showWarnConfirm("请确认是否推送质检本次数据？")

	drawerBtnLoading.value = true

	try {
		// 推送质检逻辑
		const idempotentToken = getIdempotentToken(
			IIdempotentTokenTypePre.other,
			IIdempotentTokenType.otherToWarehouseApply,
			props.id
		)
		const { code, msg, data } = await pushMatInStoreOtherApplyInspect(
			props.id,
			idempotentToken
		)
		if (data && code != 200) {
			errorGoodsIdList.value = data || []
			ElMessage.error(msg)
			matTableRef.value?.getTableData()
		} else {
			ElMessage.success("操作成功")
			emit("save")
		}
	} finally {
		drawerBtnLoading.value = false
	}
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
