<!-- 其他入库详情 编辑 -->
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<div class="drawer-column left" style="width: 310px">
			<Title :title="titleConf" />

			<el-scrollbar>
				<grid-panel :options="gridPanelOptions" />

				<el-form
					class="content"
					:model="formData"
					:rules="formRules"
					:validate-on-rule-change="false"
					ref="formRef"
					label-position="top"
					label-width="100px"
				>
					<form-element :form-element="formEls" :form-data="formData" />
				</el-form>
			</el-scrollbar>

			<button-list
				class="footer"
				:button="drawerBtnConf"
				:loading="drawerBtnLoading"
				@on-btn-click="handleDrawerBtnClick"
			/>
		</div>

		<div
			class="drawer-column right"
			:class="!canEditExtra ? 'disabled' : ''"
			style="width: calc(100% - 310px)"
		>
			<Title :title="extraTitleConf">
				<Tabs
					style="margin-right: auto; margin-left: 30px"
					:tabs="tabsConf"
					@on-tab-change="handleTabChange"
				/>
			</Title>

			<el-scrollbar
				v-if="mode === IModalType.edit"
				class="rows other-apply-editor-table-wrapper"
			>
				<Query
					:query-arr-list="(queryArrList as any)"
					style="margin: 10px 10px -10px"
					@get-query-data="handleQuery"
					v-if="activatedTab === 0"
				/>
				<pitaya-table
					v-if="activatedTab === 0"
					ref="tableRef"
					:columns="(tbInitConf.tableProp as any)"
					:table-data="tableData"
					:need-selection="true"
					:single-select="false"
					:need-index="true"
					:need-pagination="true"
					:total="pageTotal"
					:table-loading="tableLoading"
					@on-selection-change="selectedTableList = $event"
					@on-current-page-change="onCurrentPageChange"
					:cell-class-name="tbCellClassName"
				>
					<!-- 物资性质 -->
					<template #attribute="{ rowData }">
						<dict-tag
							:options="DictApi.getMatAttr()"
							:value="rowData.attribute"
						/>
					</template>

					<template #useUnit="{ rowData }">
						{{
							dictFilter("INVENTORY_UNIT", rowData.useUnit)?.subitemName ||
							"---"
						}}
					</template>
					<!-- TODO: 其他入库编辑：物资明细 完修入库 本期不做-->
					<!-- <template #repairNum="{ rowData }">
						<el-input
							v-model="rowData.num"
							type="number"
							@change="updateWarehousingGoodsNum(rowData)"
						/>
					</template> -->

					<!-- <template #amount="{ rowData }">
						<cost-tag :value="rowData.amount" />
					</template> -->

					<template #num="{ rowData }">
						<el-input
							class="no-arrows"
							v-model="rowData.num"
							@click.stop
							@input="rowData.num = validateAndCorrectInput($event)"
							@change="updateWarehousingGoodsNum(rowData)"
						/>
					</template>

					<!-- 质保期 -->
					<template #warrantyPeriod="{ rowData }">
						<el-select
							type="select"
							v-model="rowData.warrantyPeriod"
							placeholder="请选择"
							@change="updateWarehousingWarrantyPeriod(rowData)"
						>
							<el-option
								v-for="(opt, index) in dictOptions.WARRANTY_PERIOD"
								:key="index"
								:label="opt.label"
								:value="opt.value"
							/>
						</el-select>
					</template>
					<template #actions="{ rowData }">
						<el-button v-btn link @click.stop="delRow(rowData)">
							<font-awesome-icon
								:icon="['fas', 'trash-can']"
								style="color: var(--pitaya-btn-background)"
							/>
							<span class="table-inner-btn">移除</span>
						</el-button>
					</template>

					<template #footerOperateLeft>
						<button-list
							:isNotRadius="true"
							:button="tbBtnConf"
							:loading="drawerBtnLoading"
							@on-btn-click="handleTbBtnAction"
						/>
					</template>
				</pitaya-table>
				<!-- 相关附件 table -->
				<table-file
					v-else
					:business-type="fileBusinessType.otherWarehousingApply"
					:business-id="id"
					:mod="canEditExtra ? IModalType.edit : IModalType.view"
				/>
			</el-scrollbar>

			<button-list
				v-if="mode === IModalType.edit"
				class="footer"
				:button="submitBtnConf"
				:loading="submitBtnLoading"
				@on-btn-click="handleSubmit"
			/>
		</div>
	</div>

	<!-- 物资编码手册选择器 -->
	<Drawer
		v-model:drawer="goodsSelectorVisible"
		:size="modalSize.lg"
		destroy-on-close
	>
		<mat-manual-list
			:table-req-params="{
				businessId: formData.id,
				status: `${matStatus.normal},${matStatus.freeze}`,
				sord: 'asc',
				sidx: 'code'
			}"
			:table-api="listMaterialCodePaged"
			:columns="matManualListColumns"
			@on-selected="handleAddGoods"
			@on-closed="goodsSelectorVisible = false"
		/>
	</Drawer>
	<!-- 返修申请选择物资 -->
	<Drawer
		v-model:drawer="chooseRepairGoods"
		:size="modalSize.lg"
		destroy-on-close
	>
		<mat-selector
			:queryArrList="queryArrList"
			:table-req-params="{
				applyId: formData.id,
				repairApplyId: formData.preBusinessId,
				type: formData.type,
				sord: 'asc',
				sidx: 'code'
			}"
			:table-api="listRepairAddPageItem"
			:columns="matSelectColumns"
			:multiple="true"
			@save="handleAddRepairGoods"
			@close="chooseRepairGoods = false"
		/>
	</Drawer>

	<!-- 仓库选择 -->
	<Drawer
		v-model:drawer="warehouseSelectorVisible"
		:size="modalSize.lg"
		destroy-on-close
	>
		<!-- 1. 入库类型为随车配件、备品备件时，选择仓库展示仓库类型为备品备件库、危险备件库的数据
				 2. 入库类型为交旧完修入库时，选择仓库展示仓库类型为周转件正常库的数据 -->
		<store-table
			:selected-ids="[formData.storeId]"
			@on-save="handleStoreSelect"
			:table-api-params="{
				type:
					formData.type == IWarehousingType.repair
						? IWarehouseType.rotablesNormal
						: formData.type === IWarehousingType.physicalObject
						? IWarehouseType.physicalObject
						: IWarehouseType.spareParts +
						  ',' +
						  IWarehouseType.sparePartsWarehouse
			}"
		/>
	</Drawer>
	<Drawer
		v-model:drawer="repairSelectVisible"
		:size="modalSize.lg"
		destroy-on-close
	>
		<repairTable
			:selected-ids="[formData.preBusinessId]"
			@on-save="handleRepairSelect"
		/>
	</Drawer>
</template>

<script setup lang="ts">
import {
	addMatInStoreOtherApplyGoods,
	deleteMatInStoreOtherApplyGoods,
	getMatInStoreOtherApply,
	listMatInStoreOtherApplyGoodsPaged,
	saveMatInStoreOtherApply,
	submitMatInStoreOtherApply,
	updateMatInStoreOtherApply,
	updateMatInStoreOtherApplyGoodsBatch,
	listRepairAddPageItem,
	listMaterialCodePaged
} from "@/app/baseline/api/store/other-warehousing-api"
import tableFile from "../../../components/tableFile.vue"
import { DictApi, fileBusinessType, matStatus } from "@/app/baseline/api/dict"
import {
	IIdempotentTokenType,
	IIdempotentTokenTypePre,
	IModalType
} from "@/app/baseline/utils/types/common"
import { useTbInit } from "../../../components/tableBase"
import {
	MatInStoreOtherApplyDTO,
	MatInStoreOtherApplyItemVo,
	MatInStoreOtherApplyVo
} from "@/app/baseline/utils/types/other-warehousing"
import { useMessageBoxInit } from "../../../components/messageBox"
import formElement from "../../../components/formElement.vue"
import { FormElementType } from "../../../components/define"
import { useDictInit } from "../../../components/dictBase"
import gridPanel from "../../components/gridPanel.vue"
import matManualList from "../../../plan/components/matManualList.vue"
import { inputMaxLength, modalSize } from "@/app/baseline/utils/layout-config"
import { FormInstance, FormItemRule } from "element-plus"
import storeTable from "../../components/storeTable.vue"
import repairTable from "../../components/repairTable.vue"
import {
	MatStoreVo,
	IWarehouseType
} from "@/app/baseline/utils/types/store-manage"
import {
	debounce,
	findIndex,
	omit,
	includes,
	toString,
	toNumber,
	map
} from "lodash-es"
import { IWarehousingType } from "@/app/baseline/utils/types/warehousing-apply"
import {
	getIdempotentToken,
	getTimestamp,
	requiredValidator,
	validateAndCorrectInput
} from "@/app/baseline/utils/validate"
// import CostTag from "../../../components/costTag.vue"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import { toMoney, batchFormatterNumView } from "@/app/baseline/utils"
import matSelector from "@/app/baseline/views/store/components/matSelector.vue"
import { BaseLineSysApi } from "@/app/baseline/api/system"

const props = defineProps<{
	/**
	 * 入库申请id
	 */
	id: any
	/**
	 * 编辑类型 新建/编辑
	 */
	mode: IModalType
}>()

const emit = defineEmits<{
	(e: "update"): void
	(e: "close"): void
	/**
	 * 保存
	 *
	 * @param id
	 * @param visible - 是否隐藏本编辑 drawer
	 */
	(e: "save", id: any, visible?: boolean): void
}>()

/**
 * 保存旧的表单数据
 */
const oldFormData = ref<string>()

const activatedTab = ref(0)

const goodsSelectorVisible = ref(false)

const drawerBtnLoading = ref(false)

const submitBtnLoading = ref(false)

/**
 * 仓库选择器 visible
 */
const warehouseSelectorVisible = ref(false)

const drawerBtnConf = computed(() => [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "保存草稿",
		icon: ["fas", "floppy-disk"]
	}
])

/**
 * 提交审核按钮配置
 */
const submitBtnConf = computed(() => {
	return [
		{
			name: "提交审核",
			icon: ["fas", "circle-check"],
			disabled: props.mode === IModalType.create || tableData.value.length < 1
		}
	]
})

const chooseRepairGoods = ref(false)

/**
 * 返修入库 - 添加物资table 列配置
 */
const matSelectColumns = ref<TableColumnType[]>([
	{ label: "物资编码", prop: "materialCode" },
	{ label: "物资名称", prop: "materialLabel" },
	{
		label: "规格型号",
		prop: "version",
		needSlot: false
	},
	{
		label: "技术参数",
		prop: "technicalParameter",
		needSlot: false
	},
	{ label: "物资性质", prop: "attribute", needSlot: true, width: 120 },
	{
		prop: "useUnit_view",
		label: "库存单位",
		width: 100
	},
	{
		prop: "repairNum_view",
		label: "返修数量",
		align: "right",
		width: 100
	},
	{
		prop: "canNum_view",
		label: "待完修数量",
		align: "right",
		width: 100
	},
	{
		prop: "storeLabel",
		label: "出库仓库名称"
	},
	// {
	// 	prop: "regionCode",
	// 	label: "区域编码",
	// 	width: 100
	// },
	// {
	// 	prop: "roomCode",
	// 	label: "货位编码",
	// 	width: 100
	// },
	{
		prop: "outDate",
		label: "出库时间",
		width: 150
	}
])

/**
 * 除返修入库外 - 添加物资table 列配置
 */
const matManualListColumns = ref<TableColumnType[]>([
	{ label: "物资编码", prop: "code", width: 150 },
	{ label: "物资名称", prop: "label", width: 150 },
	{
		label: "物资分类编码",
		prop: "materialTypeCode",
		width: 100
	},
	{
		label: "物资分类名称",
		prop: "materialTypeLabel",
		width: 200
	},
	{
		label: "规格型号",
		prop: "version",
		needSlot: false,
		width: 150
	},
	{
		label: "技术参数",
		prop: "technicalParameter",
		needSlot: false,
		minWidth: 100,
		align: "left"
	},
	{ label: "物资性质", prop: "attribute", needSlot: true, width: 120 },
	{ label: "采购单位", prop: "buyUnit", needSlot: true, width: 90 }
])

async function handleTbBtnAction(btnName?: string) {
	if (btnName === "添加物资") {
		chooseGoods()
	} else if (btnName === "批量移除") {
		const ids = map(selectedTableList.value, ({ id }) => id)
		await showDelConfirm()
		drawerBtnLoading.value = true
		try {
			await deleteMatInStoreOtherApplyGoods(ids)
			ElMessage.success("操作成功")
			fetchTableData()
			getDetail()
			emit("update")
		} finally {
			drawerBtnLoading.value = false
		}
	}
}
// 选择物资
const chooseGoods = () => {
	if (formData.value.type !== IWarehousingType.repair) {
		goodsSelectorVisible.value = true
	} else {
		chooseRepairGoods.value = true
	}
}

const { dictOptions, dictFilter, getDictByCodeList } = useDictInit()
const { showDelConfirm, showWarnConfirm } = useMessageBoxInit()

const formData = ref<MatInStoreOtherApplyDTO | MatInStoreOtherApplyVo>({
	amount_view: "￥0.00"
})

const formRules = computed(() => {
	const obj: Record<string, FormItemRule> = {
		label: requiredValidator("其他入库名称"),
		type: requiredValidator("入库类型"),
		preBusinessCode: requiredValidator("返修申请单"),
		storeName: requiredValidator("入库仓库"),
		lineNoId_view: requiredValidator("线路"),
		warrantyPeriod: requiredValidator("质保期")
	}

	// TODO: 其他入库编辑：表单rule 其他返修申请单号逻辑 - 本期不做
	/* return formData.value.type === IWarehousingType.repair
		? obj
		: omit(obj, "preBusinessCode") */

	if (!formData.value.type) {
		return omit(obj, "preBusinessCode", "warrantyPeriod", "lineNoId_view")
	} else if (formData.value.type === IWarehousingType.repair) {
		return omit(obj, "warrantyPeriod", "lineNoId_view")
	} else if (
		formData.value.type === IWarehousingType.back ||
		formData.value.type === IWarehousingType.parts
	) {
		return omit(obj, "preBusinessCode")
	} else {
		return omit(obj, "preBusinessCode", "lineNoId_view")
	}
})

/**
 * 能否编辑扩展信息
 */
const canEditExtra = computed(() => Boolean(formData.value?.id || props.id))
const repairSelectVisible = ref<boolean>(false)

const formEls = computed<FormElementType[][]>(() => {
	const ls = [
		{
			label: "其他入库名称",
			name: "label",
			maxlength: inputMaxLength.input
		},
		{
			label: "入库类型",
			name: "type",
			type: "select",
			disabled: canEditExtra.value,
			clear: false,
			data: dictOptions.value["IN_STORE_OTHER_TYPE"],
			change: () => {
				formData.value.storeName = ""
				formData.value.storeId = null
			}
		},
		{
			label: "质保期",
			name: "warrantyPeriod",
			type: "select",
			disabled: canEditExtra.value,
			clear: false,
			data: dictOptions.value["WARRANTY_PERIOD"]
		},
		{
			label: "关联返修申请单号",
			name: "preBusinessCode",
			type: "drawer",
			disabled: canEditExtra.value,
			clear: false,
			clickApi: () => (repairSelectVisible.value = true)
		},
		{
			label: "入库仓库",
			name: "storeName",
			type: "drawer",
			disabled: !formData.value.type,
			clear: false,
			clickApi: () => (warehouseSelectorVisible.value = true)
		},
		{
			label: "专业",
			name: "majorId_view",
			vname: "majorId",
			type: "treeSelect",
			treeApi: BaseLineSysApi.getProfessionTree
		},
		{
			label: "线路",
			name: "lineNoId_view",
			vname: "lineNoId",
			maxlength: 50,
			type: "treeSelect",
			needSingleSelect: true,
			treeApi: BaseLineSysApi.getLineList
		},
		/* {
			label: "入库金额（元）",
			name: "amount_view",
			disabled: true,
			append: "元"
		}, */
		{
			label: "入库原因说明",
			name: "reason",
			type: "textarea",
			disabled: false,
			clear: false,
			maxlength: inputMaxLength.textarea
		}
	]

	if (!formData.value.type) {
		return [
			ls.filter(
				(v) =>
					v.label !== "质保期" &&
					v.label !== "关联返修申请单号" &&
					v.label !== "专业" &&
					v.label !== "线路"
			)
		]
	} else if (formData.value.type === IWarehousingType.repair) {
		return [
			ls.filter(
				(v) => v.label !== "质保期" && v.label !== "专业" && v.label !== "线路"
			)
		]
	} else if (
		formData.value.type === IWarehousingType.back ||
		formData.value.type === IWarehousingType.parts
	) {
		return [ls.filter((v) => v.label !== "关联返修申请单号")]
	} else {
		return [
			ls.filter(
				(v) =>
					v.label !== "关联返修申请单号" &&
					v.label !== "专业" &&
					v.label !== "线路"
			)
		]
	}
})

const tbBtnConf = computed(() => {
	return [
		{
			name: "添加物资",
			icon: ["fas", "circle-plus"]
		},
		{
			name: "批量移除",
			icon: ["fas", "trash-can"],
			disabled: selectedTableList.value.length > 0 ? false : true
		}
	]
})

const formRef = ref<FormInstance>()

const tabsConf = ["物资明细", "相关附件"]

const drawerLoading = ref(false)

const titleConf = computed(() => ({
	name: [props.mode === IModalType.create ? "新建其他入库" : "编辑其他入库"],
	icon: ["fas", "square-share-nodes"]
}))

const extraTitleConf = {
	name: ["扩展信息"],
	icon: ["fas", "square-share-nodes"]
}

const handleTabChange = (index: number) => {
	activatedTab.value = index

	if (index === 0) {
		fetchParam.value = {
			applyId: props.id || formData.value.id,
			sord: "desc",
			sidx: "createdDate"
		}
		pageSize.value = 20
		handleQuery()
	}
}

const queryArrList = computed<querySetting[]>(() => [
	{
		name: "物资编码",
		key: "materialCode",
		type: "input",
		placeholder: "请输入物资编码"
	},
	{
		name: "物资名称",
		key: "materialLabel",
		type: "input",
		placeholder: "请输入物资名称"
	},
	{
		name: "规格型号",
		key: "version",
		placeholder: "请输入规格型号",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资性质",
		key: "attribute",
		type: "select",
		children: dictOptions.value.MATERIAL_NATURE,
		placeholder: "请选择物资性质"
	}
])

const tbInitConf = useTbInit<MatInStoreOtherApplyItemVo, any>()

const {
	fetchParam,
	tableCache,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	pageSize,
	selectedTableList,
	fetchTableData,
	fetchFunc,
	currentPage,
	onCurrentPageChange
} = tbInitConf

tbInitConf.tableProp = computed(() => {
	const ls: TableColumnType[] = [
		{
			prop: "materialCode",
			label: "物资编码"
		},
		{
			prop: "materialName",
			label: "物资名称"
		},
		{
			prop: "version",
			label: "规格型号"
		},
		{
			prop: "technicalParameter",
			label: "技术参数"
		},
		{
			prop: "attribute",
			label: "物资性质",
			needSlot: true,
			width: 120
		},
		{
			prop: "useUnit",
			label: "库存单位",
			needSlot: true,
			width: 80
		},
		/* {
			prop: "amount",
			label: "单价",
			needSlot: true
		}, */
		// TODO: 其他入库编辑：入库类型为交旧返修入库的物资明细编辑逻辑 - 本期不做
		{
			prop: "repairNum_view",
			label: "返修数量",
			align: "right"
		},
		{
			prop: "canNum_view",
			label: "待完修数量",
			align: "right"
		},
		{
			prop: "num",
			label: "入库数量",
			needSlot: true
		},
		{
			prop: "warrantyPeriod",
			label: "质保期",
			needSlot: true
		},
		{
			prop: "num",
			label: "本次完修数量",
			needSlot: true
		},
		{
			prop: "actions",
			label: "操作",
			needSlot: true,
			width: 100
		}
	]

	// TODO: 其他入库编辑：入库类型为交旧返修入库的物资明细编辑逻辑 - 本期不做
	return formData.value.type !== IWarehousingType.repair
		? ls.filter(
				(v) =>
					v.label !== "待完修数量" &&
					v.label !== "返修数量" &&
					v.label !== "本次完修数量"
		  )
		: ls.filter((v) => v.label !== "入库数量" && v.label !== "质保期")
})

/**
 * 格式化数量
 */
watch(tableData, () => {
	tableData.value.map((v) => {
		v.warrantyPeriod = toString(v.warrantyPeriod)
	})

	batchFormatterNumView(tableData.value as any[])
})

fetchFunc.value = listMatInStoreOtherApplyGoodsPaged

const gridPanelOptions = computed(() => {
	return [
		{
			label: "物资编码",
			value: formData.value.materialCodeNum ?? 0
		},
		{
			label: "入库总量",
			value: parseInt(formData.value.totalNum) || 0
		}
	]
})

function handleQuery(e?: any) {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = { ...fetchParam.value, ...e }
	fetchTableData()
}

onMounted(() => {
	getDictByCodeList([
		"IN_STORE_OTHER_TYPE",
		"INVENTORY_UNIT",
		"WARRANTY_PERIOD",
		"MATERIAL_NATURE"
	])

	fetchParam.value.sord = "desc"
	fetchParam.value.sidx = "createdDate"

	if (props.mode === IModalType.edit) {
		fetchParam.value.applyId = props.id
		fetchTableData()
		getDetail()
	}
})

/**
 * 物资明细 table 行样式
 *
 * 库存异常物资，用红色标记该行
 */
const errorGoodsIdList = ref<number[]>([])
function tbCellClassName({ row }: any) {
	return includes(errorGoodsIdList.value, row.id) ? "error" : ""
}

/**
 * 删除物资
 */
async function delRow(e?: MatInStoreOtherApplyItemVo) {
	await showDelConfirm()
	await deleteMatInStoreOtherApplyGoods([e?.id])
	ElMessage.success("操作成功")
	fetchTableData()
	getDetail()
	emit("update")
}

/**
 * 保存草稿 handler
 */
async function handleSaveDraft() {
	if (!formRef.value) {
		return
	}

	formRef.value?.validate(async (valid) => {
		if (!valid) {
			return
		}

		drawerBtnLoading.value = true

		try {
			const api =
				props.mode === IModalType.create
					? saveMatInStoreOtherApply
					: updateMatInStoreOtherApply

			let idempotentToken = ""
			if (props.mode === IModalType.create) {
				idempotentToken = getIdempotentToken(
					IIdempotentTokenTypePre.apply,
					IIdempotentTokenType.otherToWarehouseApply
				)
			}
			const r = await api(formData.value as any, idempotentToken)

			ElMessage.success("操作成功")
			formData.value.id = r.id
			fetchParam.value.applyId = r.id

			oldFormData.value = JSON.stringify(formData.value) // 保存旧的表单数据
			emit("save", r.id, true)
			handleQuery()
		} finally {
			drawerBtnLoading.value = false
		}
	})
}

// 提交审核逻辑
function handleSubmit() {
	if (!formRef.value) {
		return
	}

	formRef.value?.validate(async (valid) => {
		if (!valid) {
			return
		}

		await showWarnConfirm("请确认是否提交本次数据？")

		submitBtnLoading.value = true
		drawerLoading.value = true

		try {
			// 如果主表有修改，则先更新主表数据
			if (oldFormData.value != JSON.stringify(formData.value)) {
				await updateMatInStoreOtherApply(formData.value as any)
			}

			const idempotentToken = getIdempotentToken(
				IIdempotentTokenTypePre.other,
				IIdempotentTokenType.otherToWarehouseApply,
				formData.value.id
			)

			const { code, data, msg } = await submitMatInStoreOtherApply(
				formData.value.id!,
				idempotentToken
			)

			if (data && code != 200) {
				errorGoodsIdList.value = data || []
				ElMessage.error(msg)
				handleQuery()
			} else {
				ElMessage.success("操作成功")
				emit("save", undefined)
			}
		} finally {
			submitBtnLoading.value = false
			drawerLoading.value = false
		}
	})
}

function handleDrawerBtnClick(name?: string) {
	if (name === "保存草稿") {
		handleSaveDraft()
		return
	}

	emit("close")
}

/**
 * 交旧返修入库 - 添加物资
 */
async function handleAddRepairGoods(params: any[]) {
	// await addMatInStoreOtherApplyGoods({
	// 	applyId: props.id || formData.value.id,
	// 	materialId: map(params, ({ id }) => id)
	// })
	const idempotentToken = getIdempotentToken(
		IIdempotentTokenTypePre.other,
		IIdempotentTokenType.otherToWarehouseApply,
		formData.value.id
	)
	await addMatInStoreOtherApplyGoods(
		{
			applyId: props.id || formData.value.id,
			itemList: params.map((item: any) => {
				return {
					materialId: item.materialId,
					materialCode: item.materialCode,
					materialLabel: item.materialLabel,
					version: item.version,
					attribute: item.attribute
					//outItemId: item.id
				}
			})
		},
		idempotentToken
	)
	chooseRepairGoods.value = false
	ElMessage.success("操作成功")
	handleQuery()
	getDetail()
	emit("update")
}

/**
 * 添加物资
 */
async function handleAddGoods(params: any[]) {
	const idempotentToken = getIdempotentToken(
		IIdempotentTokenTypePre.other,
		IIdempotentTokenType.otherToWarehouseApply,
		formData.value.id
	)
	await addMatInStoreOtherApplyGoods(
		{
			applyId: props.id || formData.value.id,
			itemList: params.map((item: any) => {
				return {
					materialId: item.id,
					materialCode: item.code,
					materialLabel: item.label,
					version: item.version,
					attribute: item.attribute
				}
			})
		},
		idempotentToken
	)
	goodsSelectorVisible.value = false
	ElMessage.success("操作成功")
	handleQuery()
	getDetail()
	emit("update")
}

/**
 * 获取其他入库详情
 */
function getDetail() {
	drawerLoading.value = true
	getMatInStoreOtherApply(props.id || formData.value.id)
		.then((r) => {
			formData.value = r as any
			formData.value.amount_view = toMoney(r?.amount)
			formData.value.warrantyPeriod = toString(r?.warrantyPeriod)
			oldFormData.value = JSON.stringify(formData.value)
		})
		.finally(() => (drawerLoading.value = false))
}

/**
 * 仓库选择 handler
 * @param btnName 按钮名称
 * @param storeIds 仓库id列表
 */
function handleStoreSelect(btnName: string, store?: MatStoreVo) {
	if (btnName === "取消") {
		warehouseSelectorVisible.value = false
		return
	}

	formData.value.storeId = store?.id
	formData.value.storeName = store?.label
	warehouseSelectorVisible.value = false
}

function handleRepairSelect(btnName: string, store?: MatStoreVo) {
	if (btnName === "取消") {
		repairSelectVisible.value = false
		return
	}
	formData.value.preBusinessId = store?.id
	formData.value.preBusinessCode = store?.code
	repairSelectVisible.value = false
}

/**
 * 更新入库物资数量
 */

const updateWarehousingGoodsNum = debounce(
	async (e: MatInStoreOtherApplyItemVo) => {
		const num = toNumber(e.num)
		const canNum = toNumber(e.canNum)

		e.num = num
		if (formData.value.type === IWarehousingType.repair) {
			if (num > canNum) {
				e.num = canNum
				ElMessage.warning("入库数量不能大于待完修入库数量！")
			}
		}

		if (num <= 0) {
			const oldRow = tableCache.find((v) => v.id == e.id)
			e.num = oldRow.num

			return ElMessage.warning("入库数量不能小于等于0！")
		}

		await updateMatInStoreOtherApplyGoodsBatch({
			id: e.id,
			num: e.num,
			warrantyPeriod: e.warrantyPeriod
		})

		//  更新 tableCache
		const rowIdx = findIndex(tableCache, (v) => v.id === e.id)
		if (rowIdx !== -1) {
			tableCache.splice(rowIdx, 1, { ...e })
		}

		ElMessage.success("操作成功")
		getDetail()
	},
	300
)

const updateWarehousingWarrantyPeriod = debounce(
	async (e: MatInStoreOtherApplyItemVo) => {
		await updateMatInStoreOtherApplyGoodsBatch({
			id: e.id,
			num: e.num,
			warrantyPeriod: e.warrantyPeriod
		})

		//  更新 tableCache
		const rowIdx = findIndex(tableCache, (v) => v.id === e.id)
		if (rowIdx !== -1) {
			tableCache.splice(rowIdx, 1, { ...e })
		}

		ElMessage.success("操作成功")
	},
	300
)
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.other-apply-editor-table-wrapper {
	&:deep(.pitaya-table) {
		.error {
			.column-inner-item,
			.cell,
			.el-input__inner {
				color: red;
			}
		}
	}
}
</style>
