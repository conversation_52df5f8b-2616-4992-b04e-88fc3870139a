<!-- 历史价格 table -->
<template>
	<el-scrollbar style="height: 100%">
		<Query
			:queryArrList="queryArrList"
			@getQueryData="getTableData"
			style="margin: 10px 0px -10px 10px !important"
		/>
		<pitaya-table
			ref="tableRef"
			:columns="tableProp"
			:table-data="tableData"
			:need-index="true"
			:single-select="false"
			:need-pagination="true"
			:total="pageTotal"
			:table-loading="tableLoading"
			@on-current-page-change="onCurrentPageChange"
			@on-table-sort-change="handleSortChange"
		>
			<!-- 采购订单号 -->
			<template #code="{ rowData }">
				<link-tag
					:value="rowData.code"
					@on-click="showPurchaseOrderDetail(rowData)"
				/>
			</template>

			<!-- 采购项目编号 -->
			<template #projectCode="{ rowData }">
				<link-tag
					:value="rowData.projectCode"
					@on-click="showPurchaseProjectDetail(rowData)"
				/>
			</template>

			<template #purchasePrice="{ rowData }">
				<cost-tag :value="rowData.purchasePrice" />
			</template>

			<template #source="{ rowData }">
				<dict-tag
					:options="DictApi.getPurchaseOrderSource()"
					:value="rowData.source"
				/>
			</template>
		</pitaya-table>
	</el-scrollbar>

	<!-- 采购订单详情 -->
	<Drawer
		v-model:drawer="purchaseOrderVisible"
		:size="modalSize.xl"
		destroy-on-close
	>
		<purchase-order-detail
			:purchase-order-id="editingTableRow.purchaseOrderId"
			@close="purchaseOrderVisible = false"
		/>
	</Drawer>

	<!-- 采购项目编号 -->
	<Drawer
		v-model:drawer="purchaseProjectVisible"
		:size="modalSize.xl"
		destroy-on-close
	>
		<purchase-project-detail
			:id="editingTableRow.projectId"
			@close="purchaseProjectVisible = false"
		/>
	</Drawer>
</template>

<script setup lang="ts">
import { listInventoryHistoryPricePaged } from "@/app/baseline/api/store/inventory-api"
import { useTbInit } from "../../../components/tableBase"
import CostTag from "../../../components/costTag.vue"
import LinkTag from "../../../components/linkTag.vue"
import {
	MatStoreScreenHisPricerVo,
	MatStoreScreenListReqParams
} from "@/app/baseline/utils/types/store-inventory"
import DictTag from "../../../components/dictTag.vue"
import { DictApi } from "@/app/baseline/api/dict"
import { modalSize } from "@/app/baseline/utils/layout-config"
import purchaseOrderDetail from "../../components/purchaseOrderDetail.vue"
import purchaseProjectDetail from "../../components/purchaseProjectDetail.vue"
import { getCurentYearFirstDay, getNowDate } from "@/app/baseline/utils"

const props = defineProps<{ id: any }>()

/**
 * 查询条件 配置
 */
const queryArrList = computed(() => [
	{
		name: "采购订单号",
		key: "purchaseOrderCode",
		type: "input",
		placeholder: "请输入采购订单号"
	},
	{
		name: "采购项目编号",
		key: "purchaseProjectCode",
		type: "input",
		placeholder: "请输入采购项目编号"
	},
	{
		name: "采购时间",
		key: "startAndEndTime",
		type: "startAndEndTime",
		placeholder: "请选择",
		paramsData: [getCurentYearFirstDay(), getNowDate()]
	},
	{
		name: "合同编码",
		key: "contractCode",
		type: "input",
		placeholder: "请输入合同编码"
	},
	{
		name: "订单来源",
		key: "purchaseOrderSource",
		type: "select",
		placeholder: "请选择",
		children: DictApi.getPurchaseOrderSource()
	},
	{
		name: "供应商名称",
		key: "supplierLabel",
		type: "input",
		placeholder: "请输入供应商名称"
	}
])

const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	currentPage,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = useTbInit<MatStoreScreenHisPricerVo, MatStoreScreenListReqParams>()

fetchFunc.value = listInventoryHistoryPricePaged

tableProp.value = [
	{
		prop: "code",
		label: "采购订单号",
		width: 180,
		needSlot: true
	},
	{
		prop: "projectCode",
		label: "采购项目编号",
		width: 180,
		needSlot: true
	},
	{
		prop: "contractCode",
		label: "合同编号"
	},
	{
		prop: "source",
		label: "订单来源",
		needSlot: true,
		width: 130
	},
	/* {
		prop: "projectLabel",
		label: "采购项目名称"
	}, */
	{
		prop: "supplierLabel",
		label: "供应商名称"
	},
	{
		prop: "purchasePrice",
		label: "采购单价",
		needSlot: true,
		align: "right"
	},
	{
		prop: "purchaseCreateDate",
		label: "采购时间",
		width: 160,
		sortable: true
	}
]

const getTableData = (data: Record<string, any>) => {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	const { startAndEndTime } = data

	data.createdDate_start = startAndEndTime ? startAndEndTime[0] : ""
	data.createdDate_end = startAndEndTime ? startAndEndTime[1] : ""

	delete data.startAndEndTime
	if (props.id) {
		fetchParam.value = {
			...fetchParam.value,
			id: props.id,
			sord: "desc",
			sidx: "createdDate",
			...data
		}
		fetchTableData()
	}
}

/**
 * 排序
 * @param column
 * @param prop 字段名称
 * @param order ascending 升序 descending 降序
 */
function handleSortChange(column: any, prop: any, order: any) {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		sord:
			order === "descending" ? "desc" : order === "ascending" ? "asc" : "desc",
		sidx: order
			? prop === "purchaseCreateDate"
				? "purchaseOrder.createdDate"
				: prop
			: "createdDate" // 排序字段
	}

	fetchTableData()
}

onMounted(() => {
	fetchParam.value = {
		id: props.id,
		createdDate_start: getCurentYearFirstDay(),
		createdDate_end: getNowDate(),
		sord: "desc",
		sidx: "createdDate"
	}
	fetchTableData()
})

/**
 * 采购订单 下钻详情
 */
const purchaseOrderVisible = ref(false)
const editingTableRow = ref<MatStoreScreenHisPricerVo>({})
function showPurchaseOrderDetail(e: MatStoreScreenHisPricerVo) {
	editingTableRow.value = e
	purchaseOrderVisible.value = true
}

/**
 *采购项目编号
 */
const purchaseProjectVisible = ref(false)
function showPurchaseProjectDetail(e: MatStoreScreenHisPricerVo) {
	purchaseProjectVisible.value = true
	editingTableRow.value = e
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
