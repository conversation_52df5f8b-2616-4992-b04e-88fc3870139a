<!-- 下钻 - 批次信息 -->
<template>
	<div class="drawer-container">
		<div class="drawer-column" style="width: 100%">
			<Title :title="titleConf" />

			<el-scrollbar class="rows">
				<!-- 明细信息 -->
				<pitaya-table
					ref="tableRef"
					:columns="tableProp"
					:table-data="tableData"
					:need-selection="false"
					:single-select="false"
					:need-index="true"
					:need-pagination="true"
					:total="pageTotal"
					:table-loading="tableLoading"
					:max-height="maxTableHeight"
					@on-selection-change="selectedTableList = $event"
					@on-current-page-change="onCurrentPageChange"
				>
					<template #actualCost="{ rowData }">
						<cost-tag :value="rowData.actualCost" />
					</template>
				</pitaya-table>
			</el-scrollbar>

			<button-list
				class="footer"
				:button="btnConf"
				@on-btn-click="emit('close')"
			/>
		</div>
	</div>
</template>

<script setup lang="ts">
import CostTag from "../../../components/costTag.vue"
import { useTbInit } from "../../../components/tableBase"
import { maxTableHeight } from "@/app/baseline/utils"

const props = withDefaults(
	defineProps<{
		/**
		 * table 数据源
		 */
		tableApi: (params: any) => Promise<any>
		/**
		 * table 请求参数
		 */
		tableReqParams?: any
	}>(),
	{ tableReqParams: () => ({}) }
)

const emit = defineEmits<{
	(e: "close"): void
}>()

const titleConf = computed(() => ({
	name: ["批次信息"],
	icon: ["fas", "square-share-nodes"]
}))

const btnConf = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	}
]

const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = useTbInit()

tableProp.value = [
	{ label: "批次号", prop: "batchNo", width: 180 },
	{ label: "区域编码", prop: "regionCode" },
	{ label: "货位编码", prop: "roomCode" },
	{ label: "单价", prop: "actualCost", needSlot: true, align: "right" }
]

onMounted(() => {
	fetchFunc.value = props.tableApi
	fetchParam.value = {
		...fetchParam.value,
		...props.tableReqParams,
		sord: "desc",
		sidx: "batchNo"
	}
	fetchTableData()
})
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
