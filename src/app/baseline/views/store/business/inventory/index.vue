<!-- 业务管理-库存查询 -->
<template>
	<div class="app-container">
		<!-- 左侧 tree 筛选 -->
		<model-frame class="left-frame">
			<div class="drawer-container">
				<div class="drawer-column" style="width: 100%">
					<div class="rows" style="padding-bottom: 20px">
						<Title :title="titleConf">
							<el-radio-group
								:disabled="treeLoading"
								v-model="treeFilterType"
								size="small"
								class="inventory-radio-group"
								@change="getTreeDataByFilterType"
							>
								<el-radio-button :label="IInventoryFilterType.category"
									>按分类</el-radio-button
								>
								<el-radio-button :label="IInventoryFilterType.store"
									>按仓库</el-radio-button
								>
							</el-radio-group>
						</Title>
						<pitaya-lazy-tree-new
							v-if="treeFilterType == IInventoryFilterType.category"
							ref="treeRef"
							:need-check-box="false"
							:lazy="changeLazy"
							v-model:tree-biz-id="treeSelectId"
							v-loading="treeLoading"
							:tree-data="treeData"
							:tree-props="{
								label: 'fullLabel',
								children: 'children',
								isLeaf: 'isLeaf'
							}"
							:loadTreeChildren="loadTreeChildren"
							check-strictly
							:expand-on-click-node="false"
							need-single-select
							node-key="id"
							:default-expanded-keys="defaultExpandedKeys"
							@on-tree-click="handleTreeClick"
							:on-tree-search="onLocTreeSearch"
						/>
						<!-- @on-tree-change="handleTreeCheck" -->
						<pitaya-tree
							v-if="treeFilterType == IInventoryFilterType.store"
							ref="treeRef"
							v-model:tree-biz-id="treeSelectId"
							v-loading="treeLoading"
							:tree-data="treeData"
							:tree-props="{
								label: 'label',
								children: 'children'
							}"
							check-strictly
							:need-check-box="false"
							:expand-on-click-node="false"
							need-single-select
							:default-expanded-keys="['0']"
							node-key="id"
							@on-tree-click="handleTreeClick"
						/>
					</div>
				</div>
			</div>
		</model-frame>

		<div class="right-frame">
			<model-frame class="top-frame">
				<Query
					:query-arr-list="queryConf"
					@get-query-data="handleQuery"
					class="ml10"
				/>
			</model-frame>
			<model-frame class="bottom-frame">
				<Title :title="tableTitleConf">
					<div style="display: flex">
						<el-switch
							size="small"
							v-model="hasStoreNumZero"
							:active-value="true"
							:inactive-value="false"
							active-color="#0a4e9a"
							@change="handleStoreNumZeroChange"
						/>
						<span class="tip" style="padding-left: 5px; line-height: 24px"
							>包含库存为0的物资</span
						>
					</div>
				</Title>

				<pitaya-table
					ref="tableRef"
					:columns="tableProp"
					:table-data="tableData"
					:need-selection="true"
					:single-select="false"
					:need-index="true"
					:need-pagination="true"
					:total="pageTotal"
					:table-loading="tableLoading"
					@on-selection-change="selectedTableList = $event"
					@on-current-page-change="onCurrentPageChange"
					@on-table-sort-change="handleSortChange"
				>
					<!-- 标准成本 -->
					<template #standardCost="{ rowData }">
						<cost-tag :value="rowData.standardCost" />
					</template>

					<!-- 预估采购单价 -->
					<template #evaluation="{ rowData }">
						<cost-tag :value="rowData.evaluation" />
					</template>

					<!-- 库存单位 -->
					<template #useUnit="{ rowData }">
						{{
							dictFilter("INVENTORY_UNIT", rowData.useUnit)?.subitemName ||
							"---"
						}}
					</template>
					<!-- 物资性质 -->
					<template #attribute="{ rowData }">
						<dict-tag
							:options="DictApi.getMatAttr()"
							:value="rowData.attribute"
						/>
					</template>

					<!-- freezeNum 冻结数量 -->
					<template #freezeNum="{ rowData }">
						<link-tag
							:value="rowData.freezeNum_view"
							@on-click="showFreezeNumDetail(rowData)"
						/>
					</template>

					<!-- 物资状态 -->
					<template #status="{ rowData }">
						<dict-tag
							:options="DictApi.getMatStatus()"
							:value="rowData.status"
						/>
					</template>

					<template #actions="{ rowData }">
						<slot
							v-if="
								isCheckPermission(powerList.storeBusinessInventoryBtnPreview)
							"
						>
							<el-button
								v-btn
								link
								@click="onRowView(rowData)"
								:disabled="
									checkPermission(powerList.storeBusinessInventoryBtnPreview)
								"
								v-if="
									isCheckPermission(powerList.storeBusinessInventoryBtnPreview)
								"
							>
								<font-awesome-icon
									:icon="['fas', 'eye']"
									style="color: var(--pitaya-btn-background)"
								/>
								<span class="table-inner-btn">查看</span>
							</el-button>
						</slot>
						<slot v-else>---</slot>
					</template>

					<template #footerOperateLeft>
						<button-list
							:is-not-radius="true"
							:button="tableFooterBtns"
							@on-btn-click="handleInventoryActions"
						/>
					</template>
				</pitaya-table>
			</model-frame>
		</div>

		<Drawer
			v-model:drawer="detailVisible"
			:size="modalSize.xxl"
			destroy-on-close
		>
			<inventory-detail
				:id="editingTableRowId"
				:material-id="editingTableRowMatId"
				:fetch-param="fetchParam"
				@close="detailVisible = false"
			/>
		</Drawer>

		<Drawer
			v-model:drawer="freezeNumVisible"
			:size="modalSize.xl"
			destroy-on-close
		>
			<freeze-table
				:id="editingTableRowId"
				:costCenterId="fetchParam.costCenterId"
				:storeId="fetchParam.storeId"
				@close="freezeNumVisible = false"
			/>
		</Drawer>

		<!-- 生成货位码 -->
		<Drawer v-model:drawer="matQrCodeVisible" size="60%" destroy-on-close>
			<mat-qr-code
				@close="matQrCodeVisible = false"
				:table-list="selectedTableList"
			/>
		</Drawer>
	</div>
</template>

<script setup lang="ts">
import {
	checkPermission,
	isCheckPermission
} from "@/app/platform/utils/permission"
import { powerList } from "../../../components/define.d"
import { modalSize } from "@/app/baseline/utils/layout-config"
import { useTbInit } from "../../../components/tableBase"
import InventoryDetail from "./inventoryDetail.vue"
import { DictApi, matStatus } from "@/app/baseline/api/dict"
import DictTag from "../../../components/dictTag.vue"
import LinkTag from "../../../components/linkTag.vue"
import { useDictInit } from "../../../components/dictBase"
import CostTag from "../../../components/costTag.vue"
import {
	IInventoryFilterType,
	MatStoreScreenListReqParams,
	MatStoreScreenListVo,
	MatStoreScreenTreeVo
} from "@/app/baseline/utils/types/store-inventory"
import { MatTypeApi } from "@/app/baseline/api/material/matType"
import {
	listStoreWithCostCenterTree,
	listInventoryPaged
} from "@/app/baseline/api/store/inventory-api"
import { debounce, map } from "lodash-es"
import PitayaTree from "@/compontents/PitayaTree.vue"
import freezeTable from "./freezeTable.vue"
import { BaseLineSysApi } from "@/app/baseline/api/system"
import { batchFormatterNumView } from "@/app/baseline/utils"
import XEUtils from "xe-utils"
import matQrCode from "./matQrCode.vue"

const titleConf = {
	name: ["库存查询"],
	icon: ["fas", "square-share-nodes"]
}

/**
 * 是否包含库存为0的物资
 */
const hasStoreNumZero = ref(true)
function handleStoreNumZeroChange() {
	fetchParam.value.storeNumZero = hasStoreNumZero.value
	handleQuery()
}

/**
 * 物资 Row 行id
 */
const editingTableRowId = ref()
const editingTableRowMatId = ref()
const detailVisible = ref()

const treeLoading = ref(false)

const treeSelectId = ref("0")

const treeRef = ref<InstanceType<typeof PitayaTree>>()

const defaultExpandedKeys = ref(["0"])
/**
 * tree 筛选类型 category/store
 */
const treeFilterType = ref(IInventoryFilterType.category)

const treeData = ref<Record<string, any>[]>([
	{
		id: "0",
		code: "",
		fullLabel: "物资全部分类",
		label: "物资全部分类",
		children: [],
		isLeaf: false
	}
])

/**
 * 筛选配置
 */
const companyList = ref([])
const queryConf = computed<querySetting[]>(() => [
	{
		name: "物资编码",
		key: "materialCode",
		type: "input",
		placeholder: "请输入物资编码"
	},
	{
		name: "物资名称",
		key: "materialName",
		type: "input",
		placeholder: "请输入物资名称"
	},
	{
		name: "规格型号",
		key: "version",
		placeholder: "请输入规格型号",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "技术参数",
		key: "technicalParameter",
		placeholder: "请输入技术参数",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资性质",
		key: "attribute",
		type: "select",
		children: dictOptions.value?.MATERIAL_NATURE,
		placeholder: "请选择物资性质"
	},
	{
		name: "分类编码",
		key: "materialTypeCode",
		type: "input",
		placeholder: "请输入分类编码"
	},
	{
		name: "公司",
		key: "sysCommunityId",
		type: "select",
		children: companyList.value
	}
])

const tableTitleConf = {
	name: ["库存查询"],
	icon: ["fas", "square-share-nodes"]
}

const { dictOptions, dictFilter, getDictByCodeList } = useDictInit()

const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	currentPage,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = useTbInit<MatStoreScreenListVo, Ref<MatStoreScreenListReqParams>>()

fetchFunc.value = listInventoryPaged

tableProp.value = [
	{
		prop: "materialCode",
		label: "物资编码",
		width: 130,
		fixed: "left",
		sortable: true
	},
	{
		prop: "materialName",
		label: "物资名称",
		width: 130
	},
	{
		prop: "materialTypeCode",
		label: "分类编码",
		width: 100
	},
	{
		prop: "version",
		label: "规格型号",
		minWidth: 100
	},
	{
		prop: "technicalParameter",
		label: "技术参数",
		minWidth: 100
	},
	{
		prop: "attribute",
		label: "物资性质",
		needSlot: true,
		width: 130
	},
	{
		prop: "useUnit",
		label: "库存单位",
		needSlot: true,
		width: 80
	},
	{
		prop: "standardCost",
		label: "标准成本",
		needSlot: true,
		width: 120,
		align: "right"
	},
	{
		prop: "sysCommunityId_view",
		label: "所属公司",
		width: 120
	},
	{
		prop: "evaluation",
		label: "预估采购单价",
		needSlot: true,
		width: 150,
		align: "right"
	},
	{
		prop: "evaluationCycle",
		label: "预估采购周期(月)",
		width: 120
	},
	{
		prop: "safeStock",
		label: "安全库存",
		align: "right"
	},
	{
		prop: "storeNum_view",
		label: "库存量",
		align: "right",
		fixed: "right",
		width: 120
	},
	{
		prop: "freezeNum",
		label: "冻结量",
		needSlot: true,
		align: "right",
		fixed: "right",
		width: 120
	},
	{
		prop: "status",
		label: "物资状态",
		needSlot: true,
		fixed: "right"
	},
	{
		prop: "actions",
		label: "操作",
		needSlot: true,
		width: 100,
		fixed: "right"
	}
]

/**
 * 格式化数量
 */
watch(tableData, () => {
	batchFormatterNumView(tableData.value as any[])
})

onMounted(() => {
	fetchParam.value.sord = "asc"
	fetchParam.value.sidx = "code"
	getTreeDataByFilterType()
	getDictByCodeList(["INVENTORY_UNIT", "MATERIAL_NATURE"])
	//handleQuery()
	BaseLineSysApi.getCompanyAllList().then((r) => (companyList.value = r))
})

const changeLazy = ref(true)

/**
 * 树形筛选 check handler
 */
/* const handleTreeCheck = debounce(() => {
	const checkedNodes = treeRef.value?.getCheckedNodes()

	fetchParam.value.costCenterId = undefined
	fetchParam.value.materialTypeId = undefined
	fetchParam.value.storeId = undefined

	const e = first(checkedNodes) as any | undefined

	if (!e) {
		handleQuery()
		return
	}

	if (treeFilterType.value === IInventoryFilterType.category) {
		// 按物资分类
		fetchParam.value.materialTypeId = e?.id
		fetchParam.value.costCenterId = undefined
		fetchParam.value.storeId = undefined
	} else {
		fetchParam.value.materialTypeId = undefined
		// 按仓库
		const { type, code } = e as MatStoreScreenTreeVo
		if (type === "costCenterId") {
			// 节点类型为 成本中心
			fetchParam.value.costCenterId = code as any
			fetchParam.value.storeId = undefined
		} else if (type === "StoreId") {
			// 节点类型为 仓库
			fetchParam.value.storeId = code as any
			fetchParam.value.costCenterId = undefined
		} else {
			// 顶部节点，不需要筛选分页
			return
		}
	}

	handleQuery()
}, 300) */

const handleTreeClick = debounce((data?: any) => {
	fetchParam.value.costCenterId = undefined
	fetchParam.value.materialTypeId = undefined
	fetchParam.value.storeId = undefined

	if (!data || data?.type == "top") {
		handleQuery()
		return
	}

	if (treeFilterType.value === IInventoryFilterType.category) {
		// 按物资分类
		fetchParam.value.materialTypeId = data?.id
		fetchParam.value.costCenterId = undefined
		fetchParam.value.storeId = undefined
	} else {
		fetchParam.value.materialTypeId = undefined
		// 按仓库
		const { type, id } = data as MatStoreScreenTreeVo
		if (type === "costCenterId") {
			// 节点类型为 成本中心
			fetchParam.value.costCenterId = id as any
			fetchParam.value.storeId = undefined
		} else if (type === "StoreId") {
			// 节点类型为 仓库
			fetchParam.value.storeId = id as any
			fetchParam.value.costCenterId = undefined
		} else {
			// 顶部节点，不需要筛选分页
			fetchParam.value.storeId = undefined
			fetchParam.value.costCenterId = undefined
		}
	}

	handleQuery()
}, 300)

/**
 * 子类加载
 * @param treeNode
 */
async function loadTreeChildren(treeNode: any) {
	if (treeNode.level == 0) {
		return MatTypeApi.getMatTypeChild({
			//...matParams.value,
			fid: treeNode.data[0].id,
			status: `${matStatus.normal},${matStatus.canceled},${matStatus.freeze}`
		}).then((res: any) => {
			treeData.value[0].children = map(res, (v) => ({
				...v,
				fullLabel:
					v.lowValue == "1"
						? `${v.code} ${
								v.label
						  } <i style='border:1px solid #409eff;color:#409eff;font-size:10px;padding:1px;'>低值</i>${
								v.status === matStatus.freeze
									? `<i style='color:#f59b22;font-size:12px;'>（冻结）</i>`
									: v.status === matStatus.canceled
									? `<i style='color:#e25e59;font-size:12px;'>（作废）</i>`
									: ""
						  }`
						: v.status === matStatus.freeze
						? `${v.code} ${v.label}<i style='color:#f59b22;font-size:12px;'>（冻结）</i>`
						: v.status === matStatus.canceled
						? `${v.code} ${v.label}<i style='color:#e25e59;font-size:12px;'>（作废）</i>`
						: `${v.code} ${v.label}`,
				isLeaf: v.childCount == 0 ? true : false
			}))

			return treeData.value
		})
	} else {
		return MatTypeApi.getMatTypeChild({
			fid: treeNode.data.id,
			status: `${matStatus.normal},${matStatus.canceled},${matStatus.freeze}`
		}).then((res: any) => {
			treeNode.children = map(res, (v) => ({
				...v,
				fullLabel:
					v.lowValue == "1"
						? `${v.code} ${
								v.label
						  } <i style='border:1px solid #409eff;color:#409eff;font-size:10px;padding:1px;'>低值</i>${
								v.status === matStatus.freeze
									? `<i style='color:#f59b22;font-size:12px;'>（冻结）</i>`
									: v.status === matStatus.canceled
									? `<i style='color:#e25e59;font-size:12px;'>（作废）</i>`
									: ""
						  }`
						: v.status === matStatus.freeze
						? `${v.code} ${v.label}<i style='color:#f59b22;font-size:12px;'>（冻结）</i>`
						: v.status === matStatus.canceled
						? `${v.code} ${v.label}<i style='color:#e25e59;font-size:12px;'>（作废）</i>`
						: `${v.code} ${v.label}`,
				isLeaf: v.childCount == 0 ? true : false
			}))

			return treeNode.children
		})
	}
}

/**
 * 根据过滤类型获取 tree data
 */
async function getTreeDataByFilterType() {
	treeLoading.value = true
	treeSelectId.value = "0"
	try {
		if (treeFilterType.value === IInventoryFilterType.category) {
			// 如果是按分类筛选，设置 tree data 为物资分类
			treeData.value = [
				{
					id: "0",
					code: "",
					fullLabel: "物资全部分类",
					label: "物资全部分类",
					children: [],
					isLeaf: false
				}
			]
		} else {
			// 库存查询：按仓库类别下的 tree data
			treeData.value = await listStoreWithCostCenterTree()
		}

		fetchParam.value.storeNumZero = hasStoreNumZero.value

		fetchParam.value.costCenterId = undefined
		fetchParam.value.materialTypeId = undefined
		fetchParam.value.storeId = undefined
		handleQuery()
	} finally {
		treeLoading.value = false
	}
}

const filterText = ref("")
const getFilterTreeData = () => {
	treeLoading.value = true
	MatTypeApi.getMatTypeTree({
		key: filterText.value ? `*${filterText.value}*` : "",
		status: `${matStatus.normal},${matStatus.canceled},${matStatus.freeze}`
	} as any)
		.then((res: any) => {
			if (res.length == 0) {
				return (treeData.value = [])
			}
			defaultExpandedKeys.value = ["0"]
			XEUtils.eachTree(res, (item: any) => {
				defaultExpandedKeys.value.push(item.id)

				item.fullLabel =
					item.lowValue == "1"
						? `${item.code} ${
								item.label
						  } <i style='border:1px solid #409eff;color:#409eff;font-size:10px;padding:1px;'>低值</i>${
								item.status === matStatus.freeze
									? `<i style='color:#f59b22;font-size:12px;'>（冻结）</i>`
									: item.status === matStatus.canceled
									? `<i style='color:#e25e59;font-size:12px;'>（作废）</i>`
									: ""
						  }`
						: item.status === matStatus.freeze
						? `${item.code} ${item.label}<i style='color:#f59b22;font-size:12px;'>（冻结）</i>`
						: item.status === matStatus.canceled
						? `${item.code} ${item.label}<i style='color:#e25e59;font-size:12px;'>（作废）</i>`
						: `${item.code} ${item.label}`
			})

			treeData.value[0].children = res

			//initTreeData(res)
		})
		.finally(() => {
			treeLoading.value = false
		})
}

const onLocTreeSearch = debounce(async (infilterText: any) => {
	if (infilterText && infilterText.length >= 1) {
		filterText.value = infilterText
		changeLazy.value = false
		getFilterTreeData()
	} else if (!infilterText || infilterText.length < 1) {
		filterText.value = ""
		defaultExpandedKeys.value = ["0"]
		treeSelectId.value = "0"

		fetchParam.value.costCenterId = undefined
		fetchParam.value.materialTypeId = undefined
		fetchParam.value.storeId = undefined
		fetchParam.value.storeNumZero = hasStoreNumZero.value
		handleQuery()

		treeData.value = [
			{
				id: "0",
				code: "",
				fullLabel: "物资全部分类",
				label: "物资全部分类",
				children: [],
				isLeaf: false
			}
		]

		changeLazy.value = true
	}
}, 500)

function handleQuery(e?: any) {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()
	fetchParam.value = {
		...fetchParam.value,
		...e
	}

	fetchTableData()
}

function onRowView(e: any) {
	editingTableRowId.value = e.id
	editingTableRowMatId.value = e.materialId
	detailVisible.value = true
}

/**
 * 查看 冻结数量
 */
const freezeNumVisible = ref(false)
function showFreezeNumDetail(e: any) {
	freezeNumVisible.value = true
	editingTableRowId.value = e.id
	editingTableRowMatId.value = e.materialId
}

/**
 * 排序
 * @param column
 * @param prop 字段名称
 * @param order ascending 升序 descending 降序
 */
function handleSortChange(column: any, prop: any, order: any) {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		sord:
			order === "descending" ? "desc" : order === "ascending" ? "asc" : "asc",
		sidx: order ? (prop === "materialCode" ? "code" : prop) : "code" // 排序字段
	}

	fetchTableData()
}

const matQrCodeVisible = ref(false)
/**
 * table 底部操作按钮组配置
 */
const tableFooterBtns = computed(() => {
	return [
		{
			name: "生成物资码",
			roles: powerList.storeBusinessInventoryBtnQrCode,
			icon: ["fas", "qrcode"],
			disabled: selectedTableList.value.length > 0 ? false : true // 生成物资码
		}
	]
})

function handleInventoryActions(btnName?: string) {
	if (btnName === "生成物资码") {
		matQrCodeVisible.value = true
	}
	console.log("btnName", btnName)
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.inventory-radio-group :deep(.el-radio-button__original-radio) {
	&:checked + .el-radio-button__inner {
		color: white !important;
		background-color: $---color-info2;
		border-color: $---color-info2;
		box-shadow: -1px 0 0 0 $---color-info2;
	}

	& + .el-radio-button__inner:hover {
		color: $---color-info2;
	}
}
</style>
