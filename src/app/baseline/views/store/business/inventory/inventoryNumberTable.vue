<!-- 库存信息 table -->
<template>
	<pitaya-table
		ref="tableRef"
		:columns="tableProp"
		:table-data="tableData"
		:need-index="true"
		:single-select="false"
		:need-pagination="true"
		:total="pageTotal"
		:table-loading="tableLoading"
		:max-height="maxTableHeight"
		@on-selection-change="selectedTableList = $event"
		@on-current-page-change="onCurrentPageChange"
	>
		<!-- 库存金额 -->
		<template #storeAmount="{ rowData }">
			<CostTag :value="rowData.storeAmount" />
		</template>

		<!-- 冻结量 -->
		<template #freezeNum_view="{ rowData }">
			<link-tag
				:value="rowData.freezeNum_view"
				@on-click="freezeNumVisible = true"
			/>
		</template>
	</pitaya-table>

	<Drawer
		v-model:drawer="freezeNumVisible"
		:size="modalSize.xl"
		destroy-on-close
	>
		<freeze-table
			:id="props.id"
			:costCenterId="props.params?.costCenterId"
			:storeId="props.params?.storeId"
			@close="freezeNumVisible = false"
		/>
	</Drawer>
</template>

<script setup lang="ts">
import { listInventoryNumberPaged } from "@/app/baseline/api/store/inventory-api"
import { useTbInit } from "../../../components/tableBase"
import CostTag from "../../../components/costTag.vue"
import LinkTag from "../../../components/linkTag.vue"
import { MatGetInventoryParams } from "@/app/baseline/utils/types/store-inventory"
import freezeTable from "./freezeTable.vue"
import { modalSize } from "@/app/baseline/utils/layout-config"
import { maxTableHeight, batchFormatterNumView } from "@/app/baseline/utils"

const props = defineProps<{
	id: any
	params?: MatGetInventoryParams
}>()

const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = useTbInit()

fetchFunc.value = listInventoryNumberPaged

tableProp.value = [
	{
		prop: "storeNum_view",
		label: "库存数量",
		align: "right"
	},
	{
		prop: "freezeNum_view",
		label: "冻结量",
		align: "right",
		needSlot: true
	},
	{
		prop: "storeAmount",
		label: "库存金额",
		needSlot: true,
		align: "right"
	},
	{
		prop: "yearBeginNum_view",
		label: "年初量",
		align: "right"
	},
	{
		prop: "yearInNum_view",
		label: "年入量",
		align: "right"
	},
	{
		prop: "monthBeginNum_view",
		label: "月初量",
		align: "right"
	},
	{
		prop: "monthInNum_view",
		label: "月入量",
		align: "right"
	},
	{
		prop: "monthOutNum_view",
		label: "月出库量",
		align: "right"
	}
]

/**
 * 格式化数量
 */
watch(tableData, () => {
	batchFormatterNumView(tableData.value as any[])
})

onMounted(() => {
	fetchParam.value = {
		id: props.id,
		...fetchParam.value,
		costCenterId: props.params?.costCenterId,
		storeId: props.params?.storeId

		//...props.params
	}
	fetchTableData()
})

/**
 * 查看 冻结数量
 */
const freezeNumVisible = ref(false)
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
