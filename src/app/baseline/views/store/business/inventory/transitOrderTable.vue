<!-- 在途订单 table -->
<template>
	<el-scrollbar style="height: 100%">
		<Query
			:queryArrList="queryArrList"
			@getQueryData="getTableData"
			style="margin: 10px 0px -10px 10px !important"
		/>
		<pitaya-table
			ref="tableRef"
			:columns="tableProp"
			:table-data="tableData"
			:need-index="true"
			:single-select="false"
			:need-pagination="true"
			:total="pageTotal"
			:table-loading="tableLoading"
			@on-selection-change="onDataSelected"
			@on-current-page-change="onCurrentPageChange"
		>
			<!-- 采购订单号 -->
			<template #code="{ rowData }">
				<link-tag
					:value="rowData.code"
					@on-click="showPurchaseOrderDetail(rowData)"
				/>
			</template>

			<!-- 采购项目编号 -->
			<template #projectCode="{ rowData }">
				<link-tag
					:value="rowData.projectCode"
					@on-click="showPurchaseProjectDetail(rowData)"
				/>
			</template>

			<!-- 订单来源 -->
			<template #source="{ rowData }">
				<dict-tag
					:options="DictApi.getPurchaseOrderSource()"
					:value="rowData.source"
				/>
			</template>
		</pitaya-table>
	</el-scrollbar>

	<!-- 采购订单详情 -->
	<Drawer
		v-model:drawer="purchaseOrderVisible"
		:size="modalSize.xl"
		destroy-on-close
	>
		<purchase-order-detail
			:purchase-order-id="editingTableRow.purchaseOrderId"
			@close="purchaseOrderVisible = false"
		/>
	</Drawer>

	<!-- 采购项目编号 -->
	<Drawer
		v-model:drawer="purchaseProjectVisible"
		:size="modalSize.xl"
		destroy-on-close
	>
		<purchase-project-detail
			:id="editingTableRow.projectId"
			@close="purchaseProjectVisible = false"
		/>
	</Drawer>
</template>

<script setup lang="ts">
import { listInventoryOrderInTransitPaged } from "@/app/baseline/api/store/inventory-api"
import { useTbInit } from "../../../components/tableBase"
import DictTag from "../../../components/dictTag.vue"
import LinkTag from "../../../components/linkTag.vue"
import { DictApi } from "@/app/baseline/api/dict"
import {
	MatGetInventoryParams,
	MatStoreInTransOrderVo
} from "@/app/baseline/utils/types/store-inventory"
import { modalSize } from "@/app/baseline/utils/layout-config"
import purchaseOrderDetail from "../../components/purchaseOrderDetail.vue"
import purchaseProjectDetail from "../../components/purchaseProjectDetail.vue"
import { batchFormatterNumView } from "@/app/baseline/utils"

const props = defineProps<{ id: any; params?: MatGetInventoryParams }>()

/**
 * 查询条件 配置
 */
const queryArrList = computed(() => [
	{
		name: "采购订单号",
		key: "purchaseOrderCode",
		type: "input",
		placeholder: "请输入采购订单号"
	},
	{
		name: "采购订单名称",
		key: "purchaseOrderLabel",
		type: "input",
		placeholder: "请输入采购订单名称"
	},
	{
		name: "采购项目编号",
		key: "purchaseProjectCode",
		type: "input",
		placeholder: "请输入采购项目编号"
	},
	{
		name: "采购项目名称",
		key: "purchaseProjectLabel",
		type: "input",
		placeholder: "请输入采购项目名称"
	},
	{
		name: "供应商名称",
		key: "supplierLabel",
		type: "input",
		placeholder: "请输入供应商名称"
	},
	{
		name: "订单来源",
		key: "purchaseOrderSource",
		type: "select",
		placeholder: "请选择",
		children: DictApi.getPurchaseOrderSource()
	}
])

const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	currentPage,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange,
	onDataSelected
} = useTbInit()

fetchFunc.value = listInventoryOrderInTransitPaged

tableProp.value = [
	{
		prop: "code",
		label: "采购订单号",
		width: 180,
		needSlot: true,
		fixed: "left"
	},
	{
		prop: "label",
		label: "采购订单名称",
		minWidth: 120
	},
	{
		prop: "projectCode",
		label: "采购项目编号",
		width: 180,
		needSlot: true
	},
	{
		prop: "projectLabel",
		label: "采购项目名称",
		minWidth: 120
	},
	{
		prop: "supplierLabel",
		label: "供应商名称",
		width: 120
	},
	{
		prop: "source",
		label: "订单来源",
		needSlot: true,
		width: 130
	},
	{
		prop: "num_view",
		label: "订货数量",
		width: 100,
		align: "right"
	},
	{
		prop: "completedArrivedNum_view",
		label: "到货数量",
		width: 100,
		align: "right"
	},
	{
		prop: "qualityPassNum_view",
		label: "合格数量",
		width: 100,
		align: "right"
	},
	{
		prop: "completedStoredNum_view",
		label: "入库数量",
		width: 100,
		align: "right"
	},
	{
		prop: "completedInvoicedNum_view",
		label: "开票数量",
		width: 100,
		align: "right"
	},
	{
		prop: "completedReturnedNum_view",
		label: "退货数量",
		width: 100,
		align: "right"
	}
]

/**
 * 格式化数量
 */
watch(tableData, () => {
	batchFormatterNumView(tableData.value as any[])
})

const getTableData = (data?: Record<string, any>) => {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	if (props.id) {
		fetchParam.value = {
			id: props.id,
			...fetchParam.value,
			costCenterId: props.params?.costCenterId,
			storeId: props.params?.storeId,
			sord: "desc",
			sidx: "createdDate",
			...data
		}
		fetchTableData()
	}
}
onMounted(() => {
	getTableData()
})

/**
 * 采购订单 下钻详情
 */
const purchaseOrderVisible = ref(false)
const editingTableRow = ref<MatStoreInTransOrderVo>({})
function showPurchaseOrderDetail(e: MatStoreInTransOrderVo) {
	editingTableRow.value = e
	purchaseOrderVisible.value = true
}

/**
 *采购项目编号
 */
const purchaseProjectVisible = ref(false)
function showPurchaseProjectDetail(e: MatStoreInTransOrderVo) {
	purchaseProjectVisible.value = true
	editingTableRow.value = e
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
