<!-- 库存事务 table -->
<template>
	<el-scrollbar style="height: 100%">
		<Query
			:queryArrList="queryArrList"
			@getQueryData="getTableData"
			style="margin: 10px 0px -10px 10px !important"
		/>
		<pitaya-table
			ref="tableRef"
			:columns="tableProp"
			:table-data="tableData"
			:need-index="true"
			:single-select="false"
			:need-pagination="true"
			:total="pageTotal"
			:table-loading="tableLoading"
			@on-selection-change="onDataSelected"
			@on-current-page-change="onCurrentPageChange"
			@on-table-sort-change="handleSortChange"
		>
			<template #code="{ rowData }">
				<link-tag
					:value="rowData.code"
					@on-click="showBusinessComponent(rowData)"
				/>
			</template>

			<!-- 数量 -->
			<template #num="{ rowData }">
				{{ toFixedTwo(rowData.num) }}
			</template>

			<!-- 库存查询-库存事务-关联业务单号展示 -->
			<template #preBusinessCode="{ rowData }">
				<link-tag
					:value="rowData.preBusinessCode"
					@on-click="showPreBusinessComponent(rowData)"
				/>
			</template>
		</pitaya-table>
	</el-scrollbar>

	<!-- 前置业务组件详情 -->
	<Drawer
		v-model:drawer="preBusinessComponentVisible"
		:size="modalSize.xl"
		destroy-on-close
	>
		<component
			:is="preBusinessComponent"
			:id="editingTableRow?.preBusinessId"
			@close="preBusinessComponentVisible = false"
		/>
	</Drawer>

	<!-- 业务组件详情 -->
	<Drawer
		v-model:drawer="businessComponentVisible"
		:size="modalSize.xl"
		destroy-on-close
	>
		<component
			:is="businessComponent"
			:id="editingTableRow?.sourceId"
			@close="businessComponentVisible = false"
		/>
	</Drawer>
</template>

<script setup lang="ts">
import { listInventoryBusinessPaged } from "@/app/baseline/api/store/inventory-api"
import { useTbInit } from "../../../components/tableBase"
import LinkTag from "../../../components/linkTag.vue"
import {
	IInventoryBusinessType,
	MatGetInventoryParams,
	MatStoreScreenAffairVo
} from "@/app/baseline/utils/types/store-inventory"
import { modalSize } from "@/app/baseline/utils/layout-config"
import businessComponentMap from "./business-component-map"
import {
	getCurentYearFirstDay,
	getNowDate,
	toFixedTwo
} from "@/app/baseline/utils"
import { useDictInit } from "../../../components/dictBase"

const props = defineProps<{
	id: any
	materialId: any
	params?: MatGetInventoryParams
}>()

const { dictOptions, getDictByCodeList } = useDictInit()

/**
 * 查询条件 配置
 */
const queryArrList = computed(() => [
	{
		name: "事务类型",
		key: "businessType",
		type: "select",
		placeholder: "请选择",
		children: dictOptions.value["TRANSACTION_AFFAIR"]
	},
	{
		name: "事务单号",
		key: "businessCode",
		type: "input",
		placeholder: "请输入事务单号"
	},
	{
		name: "操作时间",
		key: "startAndEndTime",
		type: "startAndEndTime",
		placeholder: "请选择",
		paramsData: [getCurentYearFirstDay(), getNowDate()]
	},
	{
		name: "仓库名称",
		key: "storeLabel",
		type: "input",
		placeholder: "请输入仓库名称"
	},
	{
		name: "区域编码",
		key: "regionCode",
		type: "input",
		placeholder: "请输入区域编码"
	},
	{
		name: "货位编码",
		key: "roomCode",
		type: "input",
		placeholder: "请输入货位编码"
	},
	{
		name: "批次号",
		key: "batchNo",
		type: "input",
		placeholder: "请输入批次号"
	}
])

const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	fetchParam,
	currentPage,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange,
	onDataSelected
} = useTbInit()

const preBusinessComponentVisible = ref(false)
const businessComponentVisible = ref(false)

/**
 * 要查看的采购订单id
 */
const editingTableRow = ref<MatStoreScreenAffairVo>()

fetchFunc.value = listInventoryBusinessPaged

tableProp.value = [
	{
		prop: "sourceType_view",
		label: "事务类型"
	},
	{
		prop: "code",
		label: "事务单号",
		needSlot: true,
		width: 180
	},

	{
		prop: "preBusinessCode",
		label: "关联业务单号",
		needSlot: true,
		width: 180
	},
	{
		prop: "storeLabel",
		label: "仓库名称",
		minWidth: 120
	},
	{
		prop: "regionCode",
		label: "区域编码",
		width: 100
	},
	{
		prop: "roomCode",
		label: "货位编码",
		width: 100
	},
	{
		prop: "batchNo",
		label: "批次号",
		width: 160
	},
	{
		prop: "num",
		label: "操作数量",
		needSlot: true,
		align: "right"
	},
	{
		prop: "createdDate",
		label: "操作时间",
		width: 160,
		sortable: true
	},
	{
		prop: "createdBy_view",
		label: "操作人"
	}
]

/**
 * 业务组件
 *
 * 根据不同事务类型，展示事务的前置业务
 */
const preBusinessComponent = computed(() => {
	return businessComponentMap[
		editingTableRow.value?.preBusinessType as IInventoryBusinessType
	]
})

const businessComponent = computed(() => {
	return businessComponentMap[
		editingTableRow.value?.sourceType as IInventoryBusinessType
	]
})

const getTableData = (data: Record<string, any>) => {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	const { startAndEndTime } = data

	data.createdDate_start = startAndEndTime ? startAndEndTime[0] : ""
	data.createdDate_end = startAndEndTime ? startAndEndTime[1] : ""
	delete data.startAndEndTime

	if (props.id) {
		fetchParam.value = {
			id: props.id,
			...fetchParam.value,
			costCenterId: props.params?.costCenterId,
			storeId: props.params?.storeId,
			...data
		}
		fetchTableData()
	}
}

onMounted(() => {
	getDictByCodeList(["TRANSACTION_AFFAIR"])
	fetchParam.value = {
		id: props.id,
		...fetchParam.value,
		sord: "desc",
		sidx: "createdDate",
		costCenterId: props.params?.costCenterId,
		storeId: props.params?.storeId,
		createdDate_start: getCurentYearFirstDay(),
		createdDate_end: getNowDate()
	}
	fetchTableData()
})

/**
 * 关联前置业务单详情
 */
function showPreBusinessComponent(e?: MatStoreScreenAffairVo) {
	preBusinessComponentVisible.value = true
	editingTableRow.value = e
}

/**
 * 关联业务单详情
 */
function showBusinessComponent(e?: MatStoreScreenAffairVo) {
	businessComponentVisible.value = true
	editingTableRow.value = e
}

/**
 * 排序
 * @param column
 * @param prop 字段名称
 * @param order ascending 升序 descending 降序
 */
function handleSortChange(column: any, prop: any, order: any) {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		sord:
			order === "descending" ? "desc" : order === "ascending" ? "asc" : "desc",
		sidx: order ? prop : "createdDate" // 排序字段
	}

	fetchTableData()
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
