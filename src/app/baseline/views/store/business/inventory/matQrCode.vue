<!-- 物资码 -->
<template>
	<div class="drawer-container">
		<div class="drawer-column" style="width: 100%">
			<Title :title="titleConf" />

			<div class="rows">
				<pitaya-table
					ref="tableRef"
					:columns="tableProp"
					:table-data="tableData"
					:need-index="true"
					:customize-height-number="160"
				>
					<!-- 货位码预览 -->
					<template #qrCode="{ rowData }">
						<div class="qr-code-wrap" style="width: 85mm; padding: 2mm">
							<div class="qr-code-info">
								<div class="qr-code-info-left">
									<!-- 北京地铁logo -->
									<div style="text-align: left">
										<img
											src="~@/icons/svg/logo.svg"
											alt="logo"
											class="label-logo"
										/>
									</div>
									<p class="info-text">物资编码:{{ rowData?.materialCode }}</p>
									<p class="info-text">物资名称:{{ rowData?.materialName }}</p>
								</div>
								<div class="qr-code-info-right">
									<!-- <div class="label-type">
										<span class="type-text">物&emsp;资</span>
									</div> -->

									<vue-qrcode :value="rowData?.qrCode" class="label-qrcode" />
								</div>
							</div>
							<p class="store-label-wrap">规格型号:{{ rowData?.version }}</p>
						</div>
					</template>
				</pitaya-table>
			</div>

			<button-list
				class="footer"
				:button="btnConf"
				:loading="btnLoading"
				@on-btn-click="handleBtnClick"
			/>
		</div>
	</div>
	<div style="height: 0; overflow: hidden">
		<div
			id="print-wrap"
			style="
				position: absolute;
				z-index: -1;
				top: 0;
				left: 0;
				width: 500px;
				height: 100%;
				background: #fff;
			"
		>
			<div
				class="qr-code-wrap"
				v-for="rowData in tableData"
				:key="rowData.id!"
				style="
					page-break-after: always;
					width: 90mm;
					padding: 0;
					height: 50mm;
					justify-content: flex-start;
					border: none;
				"
			>
				<div class="qr-code-info">
					<div class="qr-code-info-left" style="max-height: 35mm">
						<div style="text-align: left">
							<img src="~@/icons/svg/logo.svg" alt="logo" class="label-logo" />
						</div>
						<p class="info-text" style="max-width: 100%">
							物资编码:{{ rowData.materialCode }}
						</p>
						<p class="info-text" style="max-width: 100%">
							物资名称:{{ rowData.materialName }}
						</p>
					</div>
					<div class="qr-code-info-right" style="max-height: 35mm">
						<!-- <div class="label-type">
							<span class="type-text">物&emsp;资</span>
						</div> -->

						<vue-qrcode
							:value="rowData?.qrCode"
							class="label-qrcode-print"
							style="border: none"
						/>
					</div>
				</div>
				<p class="store-label-wrap" style="max-width: 100%">
					规格型号:{{ rowData?.version }}
				</p>
			</div>
		</div>
	</div>
</template>
<script setup lang="ts">
import { MatStoreRoomVo } from "@/app/baseline/utils/types/store-manage"
import { useTbInit } from "../../../components/tableBase"
import VueQrcode from "@chenfengyuan/vue-qrcode"
import printJS from "print-js"

const emit = defineEmits<{
	(e: "close"): void
	(e: "save", v: any[]): void
}>()

const props = withDefaults(
	defineProps<{
		title?: string

		/**
		 * 筛选是否可见
		 */
		queryVisible?: boolean
		tableList?: any[]
	}>(),
	{
		queryVisible: true,
		title: "物资码"
	}
)

const titleConf = computed(() => ({
	name: [props.title],
	icon: ["fas", "square-share-nodes"]
}))

const { tableProp, tableData, tableRef } = useTbInit<
	MatStoreRoomVo,
	Record<string, any>
>()

const btnLoading = ref(false)

tableProp.value = [
	{
		prop: "materialCode",
		label: "物资编码",
		width: 130
	},
	{
		prop: "materialName",
		label: "物资名称"
	},
	{
		prop: "version",
		label: "规格型号"
	},
	{
		prop: "technicalParameter",
		label: "技术参数"
	},
	{
		prop: "qrCode",
		label: "物资码预览",
		needSlot: true,
		width: 350,
		showOverflowTooltip: false
	}
]

const btnConf = computed(() => {
	return [
		{
			name: "关闭",
			icon: ["fas", "circle-minus"]
		},
		{
			name: "打印",
			icon: ["fas", "print"]
		}
	]
})

onMounted(() => {
	tableData.value = props.tableList || []
})

const loadingText = ref<string>("")

async function handleBtnClick(name?: string) {
	if (name === "关闭") {
		return emit("close")
	}

	btnLoading.value = true
	try {
		loadingText.value = "正在唤起浏览器打印界面，请等待..."
		btnLoading.value = true
		setTimeout(async () => {
			await nextTick()
			printJS({
				printable: "print-wrap",
				type: "html",
				targetStyles: ["*"],
				showModal: true,
				modalMessage: "正在打开打印界面...",
				onLoadingEnd: () => {
					loadingText.value = ""
					btnLoading.value = false
				},
				onPdfOpen: () => {
					console.log("更新列表数据")
				}
			})
		}, 200)
		//emit("save", selectedTableList.value)
	} finally {
		btnLoading.value = false
	}
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.qr-code-wrap {
	font-size: 12px;
	border: 1px solid #ccc;

	display: flex;
	flex-direction: column;
	justify-content: center;
	width: 80mm;
	padding: 1mm;
	height: 40mm;
	font-family: "Microsoft YaHei", "Microsoft YaHei UI", "PingFang SC", "Arial",
		"sans-serif";
	color: var(--color-text);
	.qr-code-info {
		display: flex;
		justify-content: space-between;
		.qr-code-info-left {
			display: flex;
			flex-direction: column;
			justify-content: space-around;
			max-height: 25mm;
			width: 60mm;
			.info-text {
				text-align: left;
				max-width: 60mm;
				/* 中文自动换行，英文单词尽量保持完整，数字不被拆分 */
				word-wrap: break-word;
				word-break: normal;
				white-space: normal;
				display: -webkit-box;
				-webkit-box-orient: vertical;
				overflow: hidden;
				text-overflow: ellipsis;
				margin-top: 5px;
				line-height: 18px;
				max-height: 36px;
			}
			.label-logo {
				width: 25mm;
				height: 6.3mm;
			}
		}

		.qr-code-info-right {
			display: flex;
			max-height: 25mm;
			position: relative;
			text-align: center;
			.label-type {
				background-color: #000;
				color: #fff;
				max-height: 25mm;
				// 上下边距 固定参数
				padding: 6mm 1mm;
			}
			.type-text {
				font-size: 12pt;
				font-family: 思源黑;
				font-weight: bold;
				line-height: 1;
				writing-mode: vertical-rl;
			}

			.label-qrcode {
				width: 25mm !important;
				height: 25mm !important;
				border: 1px solid #ccc;
			}

			.label-qrcode-print {
				width: 40mm !important;
				height: 40mm !important;
				margin-top: -2mm;
				margin-right: -6mm;
				border: 1px solid #ccc;
			}
		}
	}

	.store-label-wrap {
		text-align: left;
		max-width: 80mm;
		/* 中文自动换行，英文单词尽量保持完整，数字不被拆分 */
		word-wrap: break-word;
		word-break: normal;
		white-space: normal;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		overflow: hidden;
		text-overflow: ellipsis;
		margin-top: 5px;
		line-height: 18px;
		max-height: 36px;
	}
}
</style>
