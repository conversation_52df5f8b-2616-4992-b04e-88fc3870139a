import { IInventoryBusinessType } from "@/app/baseline/utils/types/store-inventory"

/**
 * 业务类型与业务组件的映射
 */
export default {
	/**
	 * 1.采购入库 - 入库申请
	 */
	[IInventoryBusinessType.purchaseToWarehouse]: defineAsyncComponent(
		() =>
			import(
				`@/app/baseline/views/store/business/warehousingApply/applyDetail.vue`
			)
	),

	/**
	 * 2.备件入库 -> 其他入库
	 */
	[IInventoryBusinessType.sparePartsToWarehouse]: defineAsyncComponent(
		() =>
			import(
				`@/app/baseline/views/store/business/otherWarehousing/otherApplyDetail.vue`
			)
	),

	/**
	 * 3.配件入库 -> 其他入库
	 */
	[IInventoryBusinessType.partsToWarehouse]: defineAsyncComponent(
		() =>
			import(
				`@/app/baseline/views/store/business/otherWarehousing/otherApplyDetail.vue`
			)
	),

	/**
	 * 4.返修入库 -> 其他入库
	 */
	[IInventoryBusinessType.repairToWarehouse]: defineAsyncComponent(
		() =>
			import(
				`@/app/baseline/views/store/business/otherWarehousing/otherApplyDetail.vue`
			)
	),

	/**
	 * 4.实物物资入库 -> 其他入库
	 */
	[IInventoryBusinessType.physicalObject]: defineAsyncComponent(
		() =>
			import(
				`@/app/baseline/views/store/business/otherWarehousing/otherApplyDetail.vue`
			)
	),

	/**
	 * 5.领料申请
	 */
	[IInventoryBusinessType.materialGetApply]: defineAsyncComponent(
		() =>
			import(
				`@/app/baseline/views/store/business/matGetApply/matGetApplyDetail.vue`
			)
	),

	/**
	 * 6.退库申请
	 */
	[IInventoryBusinessType.warehouseReturnApply]: defineAsyncComponent(
		() =>
			import(
				`@/app/baseline/views/store/business/warehouseReturnApply/warehouseReturnApplyDetail.vue`
			)
	),

	/**
	 * 7.退货申请
	 */
	[IInventoryBusinessType.goodsReturnApply]: defineAsyncComponent(
		() =>
			import(
				`@/app/baseline/views/store/business/goodsReturn/goodsReturnDetail.vue`
			)
	),

	/**
	 * 8.调拨申请 - 库内移库
	 */
	[IInventoryBusinessType.transferApplyKN]: defineAsyncComponent(
		() =>
			import(
				`@/app/baseline/views/store/transfer/transferApplication/viewDrawer.vue`
			)
	),

	/**
	 * 9.调拨申请 - 库外移库
	 */
	[IInventoryBusinessType.transferApplyKW]: defineAsyncComponent(
		() =>
			import(
				`@/app/baseline/views/store/transfer/transferApplication/viewDrawer.vue`
			)
	),

	/**
	 * 10.交旧申请
	 */
	[IInventoryBusinessType.storeWasteHandoverApply]: defineAsyncComponent(
		() =>
			import(`@/app/baseline/views/waste/handoverApply/handoverApplyDetail.vue`)
	),

	/**
	 * 11.返修申请
	 */
	[IInventoryBusinessType.wasteRepairApply]: defineAsyncComponent(
		() => import(`@/app/baseline/views/waste/repairApply/repairDetail.vue`)
	),

	/**
	 * 12.报废申请
	 */
	[IInventoryBusinessType.wasteBFApply]: defineAsyncComponent(
		() => import(`@/app/baseline/views/waste/scrapApply/scrapApplyDetail.vue`)
	),

	/**
	 * 13.报废处置申请
	 */
	[IInventoryBusinessType.wasteBFCZApply]: defineAsyncComponent(
		() =>
			import(
				`@/app/baseline/views/waste/scrapDisposalApply/scrapDisposalApplyDetail.vue`
			)
	),

	/**
	 * 14.领用申请
	 */
	[IInventoryBusinessType.lowValueUseApply]: defineAsyncComponent(
		() =>
			import(
				`@/app/baseline/views/lowValue/requisitionApply/requisitionApplyDetail.vue`
			)
	),

	/**
	 * 15.归还申请
	 */
	[IInventoryBusinessType.lowValueReturnApply]: defineAsyncComponent(
		() =>
			import(`@/app/baseline/views/lowValue/returnApply/returnApplyDetail.vue`)
	),

	/**
	 * 16.质量检验
	 */
	[IInventoryBusinessType.qualityInspection]: defineAsyncComponent(
		() =>
			import(
				`@/app/baseline/views/store/warehouse/qualityInspection/viewDrawer.vue`
			)
	),

	/**
	 * 17.接收入库
	 * receiveToWarehouse
	 */
	[IInventoryBusinessType.receiveToWarehouse]: defineAsyncComponent(
		() =>
			import(
				`@/app/baseline/views/store/warehouse/receivedStored/viewDrawer.vue`
			)
	),

	/**
	 * 18.领料出库
	 */
	[IInventoryBusinessType.materialGetOutWarehouse]: defineAsyncComponent(
		() =>
			import(
				`@/app/baseline/views/store/outbound/materialOutbound/viewDrawer.vue`
			)
	),

	/**
	 * 19.退库入库
	 */
	[IInventoryBusinessType.warehouseReturnToWarehouse]: defineAsyncComponent(
		() =>
			import(
				`@/app/baseline/views/store/warehouse/inventoryReturnReceived/viewDrawer.vue`
			)
	),

	/**
	 * 20.调拨出库
	 */
	[IInventoryBusinessType.transferOutWarehouse]: defineAsyncComponent(
		() =>
			import(
				`@/app/baseline/views/store/transfer/transferOutbound/viewDrawer.vue`
			)
	),

	/**
	 * 21.调拨入库
	 */
	[IInventoryBusinessType.transferToWarehouse]: defineAsyncComponent(
		() =>
			import(
				`@/app/baseline/views/store/transfer/transferReceipt/viewDrawer.vue`
			)
	),

	/**
	 * 22.退货出库
	 */
	[IInventoryBusinessType.goodsReturnOutWarehouse]: defineAsyncComponent(
		() =>
			import(
				`@/app/baseline/views/store/outbound/returnOutbound/viewDrawer.vue`
			)
	),

	/**
	 * 23.交旧返修入库 - 交旧入库
	 * handoverStoredReceivedFXRK
	 */
	[IInventoryBusinessType.handoverStoredReceivedFXRK]: defineAsyncComponent(
		() =>
			import(
				`@/app/baseline/views/store/warehouse/handoverStoredReceived/handoverStoredReceivedDetail.vue`
			)
	),

	/**
	 * 29.交旧备用入库 - 交旧入库
	 * handoverStoredReceivedBYRK
	 */
	[IInventoryBusinessType.handoverStoredReceivedBYRK]: defineAsyncComponent(
		() =>
			import(
				`@/app/baseline/views/store/warehouse/handoverStoredReceived/handoverStoredReceivedDetail.vue`
			)
	),

	/**
	 * 24.交旧废旧入库 - 交旧入库
	 * handoverStoredReceived
	 */
	[IInventoryBusinessType.handoverStoredReceivedFJRK]: defineAsyncComponent(
		() =>
			import(
				`@/app/baseline/views/store/warehouse/handoverStoredReceived/handoverStoredReceivedDetail.vue`
			)
	),

	/**
	 * 25.返修出库
	 */
	[IInventoryBusinessType.repairOutWarehouse]: defineAsyncComponent(
		() =>
			import(
				`@/app/baseline/views/store/outbound/repairOutbound/repairOutboundDetail.vue`
			)
	),

	/**
	 * 26.库存 - 盘点计划
	 */
	[IInventoryBusinessType.inventoryManagePlan]: defineAsyncComponent(
		() =>
			import(
				`@/app/baseline/views/store/inventoryManage/inventoryPlan/inventoryPlanDetail.vue`
			)
	),

	/**
	 * 27.盘盈入库
	 */
	[IInventoryBusinessType.inventoryProfitToWarehouse]: defineAsyncComponent(
		() =>
			import(
				`@/app/baseline/views/store/inventoryManage/inventoryInStore/inventoryInStoreDetail.vue`
			)
	),

	/**
	 * 28.盘亏出库
	 */
	[IInventoryBusinessType.inventoryProfitOutWarehouse]: defineAsyncComponent(
		() =>
			import(
				`@/app/baseline/views/store/inventoryManage/inventoryOutStore/inventoryOutStoreDetail.vue`
			)
	),

	/**
	 * 29. 其他出库
	 */
	[IInventoryBusinessType.otherOutWarehouse]: defineAsyncComponent(
		() =>
			import(
				`@/app/baseline/views/store/outbound/otherOutbound/otherOutboundDetail.vue`
			)
	),

	/**
	 * 交旧入库
	 * handoverStoredReceived
	 */
	/* [IInventoryBusinessType.handoverStoredReceived]: defineAsyncComponent(
		() =>
			import(
				`@/app/baseline/views/store/warehouse/handoverStoredReceived/handoverStoredReceivedDetail.vue`
			)
	), */

	/**
	 * 交旧申请
	 */
	[IInventoryBusinessType.wasteHandoverApply]: defineAsyncComponent(
		() =>
			import(`@/app/baseline/views/waste/handoverApply/handoverApplyDetail.vue`)
	)
} as Record<IInventoryBusinessType, any>
