<!-- 物资库龄 table -->
<template>
	<el-scrollbar style="height: 100%">
		<Query
			:queryArrList="queryArrList"
			@getQueryData="getTableData"
			style="margin: 10px 0px -10px 10px !important"
		/>
		<pitaya-table
			ref="tableRef"
			:columns="tableProp"
			:table-data="tableData"
			:need-index="true"
			:single-select="false"
			:need-pagination="true"
			:total="pageTotal"
			:table-loading="tableLoading"
			@on-table-sort-change="handleSortChange"
			@on-current-page-change="onCurrentPageChange"
		>
			<template #age="{ rowData }">
				<span v-if="rowData.age < 1">{{ rowData.age }}</span>

				<span style="color: #f00 !important" v-else>
					{{ rowData.age }}
				</span>
			</template>
		</pitaya-table>
	</el-scrollbar>
</template>

<script setup lang="ts">
import { listInventoryStoreAgePaged } from "@/app/baseline/api/store/inventory-api"
import { useTbInit } from "../../../components/tableBase"
import {
	MatGetInventoryParams,
	MatStoreScreenAgeVo,
	MatStoreScreenCommonListReqParams
} from "@/app/baseline/utils/types/store-inventory"
import { useDictInit } from "../../../components/dictBase"

const { dictOptions, getDictByCodeList } = useDictInit()

const props = defineProps<{
	id: any
	params?: MatGetInventoryParams
}>()

/**
 * 查询条件 配置
 */
const queryArrList = computed(() => [
	{
		name: "仓库编码",
		key: "storeCode",
		type: "input",
		placeholder: "请输入仓库编码"
	},
	{
		name: "仓库名称",
		key: "storeLabel",
		type: "input",
		placeholder: "请输入仓库名称"
	},
	{
		name: "仓库类型",
		key: "storeType",
		type: "select",
		placeholder: "请选择",
		children: dictOptions.value["STORE_TYPE"]
	},
	{
		name: "货位编码",
		key: "roomCode",
		type: "input",
		placeholder: "请输入货位编码"
	},
	{
		name: "批次号",
		key: "batchNo",
		type: "input",
		placeholder: "请输入批次号"
	}
])

const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	currentPage,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = useTbInit<MatStoreScreenAgeVo, MatStoreScreenCommonListReqParams>()

fetchFunc.value = listInventoryStoreAgePaged

tableProp.value = [
	{
		prop: "storeCode",
		label: "仓库编码",
		width: 130
	},
	{
		prop: "storeLabel",
		label: "仓库名称"
	},
	{
		prop: "storeType_view",
		label: "仓库类型"
	},
	{
		prop: "roomCode",
		label: "货位编码"
	},
	{
		prop: "batchNo",
		label: "批次号"
	},
	{
		prop: "validityPeriod",
		label: "质保有效日期"
	},
	{
		prop: "age",
		label: "当日库龄（年）",
		width: 150,
		needSlot: true,
		sortable: true
	}
]

const getTableData = (data: Record<string, any>) => {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	if (props.id) {
		fetchParam.value = {
			id: props.id,
			...fetchParam.value,
			costCenterId: props.params?.costCenterId,
			storeId: props.params?.storeId,
			...data,
			sord: "desc",
			sidx: "age" // 排序字段
		}
		fetchTableData()
	}
}
onMounted(() => {
	getDictByCodeList(["STORE_TYPE"])
	getTableData({})
})

/**
 * 按库龄排序
 * @param column
 * @param prop 字段名称
 * @param order ascending 升序 descending 降序
 */
function handleSortChange(column: any, prop: any, order: any) {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		id: props.id,
		...fetchParam.value,
		costCenterId: props.params?.costCenterId,
		storeId: props.params?.storeId,
		sord: order === "ascending" ? "asc" : "desc",
		sidx: order ? prop : "age" // 排序字段
	}

	fetchTableData()
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
