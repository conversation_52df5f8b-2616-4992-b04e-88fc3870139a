<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<!-- 物资信息 -->
		<div class="drawer-column left" style="width: 350px">
			<Title :title="titleConf" />
			<el-scrollbar>
				<!-- 物资图片展示 -->
				<div class="inventory-detail-goods-img-wrapper">
					<el-image :src="coverUrl" class="inventory-detail-goods-img" />
				</div>
				<el-descriptions
					size="small"
					:column="1"
					border
					class="content"
					style="padding-top: 0"
				>
					<el-descriptions-item
						v-for="desc in descConf"
						:key="desc.label"
						:label="desc.label"
					>
						<!-- 库存查询详情：物资性质 -->
						<dict-tag
							v-if="desc.key === 'attribute'"
							:options="DictApi.getMatAttr()"
							:value="descData?.attribute ?? ''"
						/>
						<!-- 标准成本 -->
						<cost-tag
							v-else-if="desc.key === 'standardCost'"
							:value="descData?.standardCost"
						/>

						<!-- 采购单价 -->
						<cost-tag
							v-else-if="desc.key === 'evaluation'"
							:value="descData?.evaluation"
						/>

						<!-- 库存查询详情：物资性质 -->
						<dict-tag
							v-else-if="desc.key === 'status'"
							:options="DictApi.getMatStatus()"
							:value="descData?.status ?? ''"
						/>

						<!-- 库存查询详情：库存单位 -->
						<span v-else-if="desc.key === 'useUnit'">
							{{
								dictFilter("INVENTORY_UNIT", descData?.useUnit as any)
									?.subitemName || "---"
							}}
						</span>
						<span v-else-if="desc.needTooltip">
							<el-tooltip
								effect="dark"
								:content="descData?.[desc.key]"
								:disabled="
									getRealLength(descData?.[desc.key]) <= 100 ? true : false
								"
							>
								{{
									getRealLength(descData?.[desc.key]) > 100
										? setString(descData?.[desc.key], 100)
										: descData?.[desc.key] || "---"
								}}
							</el-tooltip>
						</span>

						<span v-else>
							{{
								getNumDefByNumKey(
									desc.key,
									descData?.[desc.key],
									/num$|^safeStock/i
								)
							}}
						</span>
					</el-descriptions-item>
				</el-descriptions>
			</el-scrollbar>
		</div>

		<!-- 扩展信息 -->
		<div class="drawer-column right" style="width: calc(100% - 350px)">
			<div class="rows">
				<Title :title="extraTitleConf">
					<Tabs
						style="margin-right: auto"
						:tabs="tabsConf"
						@on-tab-change="handleTabChanged"
					/>
				</Title>

				<component
					:is="tableComponent"
					:id="id"
					:material-id="props.materialId"
					:params="fetchParam"
				/>
			</div>

			<button-list
				class="footer"
				:button="modalRightBtnConf"
				@on-btn-click="emit('close')"
			/>
		</div>
	</div>
</template>

<script setup lang="ts">
import { DictApi } from "@/app/baseline/api/dict"
import CostTag from "../../../components/costTag.vue"
import { useDictInit } from "../../../components/dictBase"
import { getInventory } from "@/app/baseline/api/store/inventory-api"
import {
	MatStoreScreenListReqParams,
	MatStoreScreenListVo
} from "@/app/baseline/utils/types/store-inventory"
import {
	getNumDefByNumKey,
	isFormatterNum,
	toFixedTwo
} from "@/app/baseline/utils"
import DictTag from "../../../components/dictTag.vue"
import { FileApi } from "@/app/baseline/api/file"
import { getRealLength, setString } from "@/app/baseline/utils/validate"
import { omit } from "lodash-es"

const props = defineProps<{
	id: any

	/**
	 * 物资Id
	 */
	materialId: any

	/**
	 * 其他查询参数
	 */
	fetchParam?: MatStoreScreenListReqParams
}>()

const emit = defineEmits<{ (e: "close"): void }>()

const drawerLoading = ref()

const titleConf = {
	name: ["物资信息"],
	icon: ["fas", "square-share-nodes"]
}

const extraTitleConf = {
	name: ["扩展信息"],
	icon: ["fas", "square-share-nodes"]
}

const tabsConf = [
	"库存信息",
	"仓库信息",
	"货位信息",
	"批次信息",
	"在途单据",
	"库存事务",
	"在途订单",
	"历史价格",
	"物资库龄"
]

/**
 * 物资明细数据
 */
const descData = ref<MatStoreScreenListVo>({})

/**
 * 物资封面
 */
const coverUrl = computed(() => {
	return FileApi.buildPreviewUrl() + (descData?.value.photoName ?? "")
})

/**
 * 物资明细配置
 */
const descConf = [
	{
		label: "物资编码",
		key: "materialCode"
	},
	{
		label: "物资名称",
		key: "materialName"
	},
	{
		label: "分类编码",
		key: "materialTypeCode"
	},
	{
		label: "规格型号",
		key: "version"
	},
	{
		label: "技术参数",
		key: "technicalParameter",
		needTooltip: true
	},
	{
		label: "物资性质",
		key: "attribute"
	},
	{
		label: "库存单位",
		key: "useUnit"
	},
	{
		label: "预估采购单价",
		key: "evaluation"
	},
	{
		label: "预估采购周期(月)",
		key: "evaluationCycle"
	},
	{
		label: "标准成本",
		key: "standardCost"
	},
	{
		label: "安全库存",
		key: "safeStock"
	},
	{
		label: "库存数量",
		key: "storeNum_view"
	},
	{
		label: "冻结量",
		key: "freezeNum_view"
	},
	{
		label: "物资状态",
		key: "status"
	}
]

const { dictFilter, getDictByCodeList } = useDictInit()

const selectedTabIdx = ref(0)

const modalRightBtnConf = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	}
]

/**
 * 表格组件
 */
const tableComponent = computed(() => {
	const els = [
		// 库存信息
		defineAsyncComponent(() => import("./inventoryNumberTable.vue")),
		// 仓库信息
		defineAsyncComponent(() => import("./storeInfoTable.vue")),
		// 货位信息
		defineAsyncComponent(() => import("./roomInfoTable.vue")),
		// 批次信息
		defineAsyncComponent(() => import("./batchInfoTable.vue")),
		// 在途单据
		defineAsyncComponent(() => import("./applyBusinessTable.vue")),
		// 库存事物
		defineAsyncComponent(() => import("./inventoryBusinessTable.vue")),
		// 在途订单
		defineAsyncComponent(() => import("./transitOrderTable.vue")),
		// 历史价格
		defineAsyncComponent(() => import("./historyPriceTable.vue")),
		// 物资库龄
		defineAsyncComponent(() => import("./inventoryAge.vue"))
	]

	return els[selectedTabIdx.value]
})

onMounted(() => {
	getDetail()
	getDictByCodeList(["INVENTORY_UNIT"])
})

function handleTabChanged(tabIdx: number) {
	selectedTabIdx.value = tabIdx
}

async function getDetail() {
	drawerLoading.value = true
	try {
		descData.value = {
			...(await getInventory({
				id: props.id,
				...omit({ ...props.fetchParam }, "currentPage", "pageSize")
			}))
		}

		Object.keys(descData.value).forEach((key) => {
			if (isFormatterNum(key, descData.value[key])) {
				// 字段名称以 Num结尾的字段，格式化千分符
				descData.value[`${key + "_view"}`] = toFixedTwo(
					descData.value[key]
				) as unknown as number
			}
		})
	} finally {
		drawerLoading.value = false
	}
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.inventory-detail-goods-img {
	&-wrapper {
		margin: 10px 10px 0;
		text-align: center;
		border: 1px solid #ccc;
		border-bottom: 0;
		height: 268px;
	}

	width: 100%;
	max-height: 268px;

	vertical-align: middle;
}

.el-image {
	max-width: 100%;
	max-height: 268px;
	width: auto;
	overflow: hidden;
	/* margin: auto; */ /* 居中对齐 */
	::v-deep(.el-image__inner) {
		max-width: 100% !important;
		max-height: 268px !important;
		vertical-align: middle !important;
	}
	::v-deep(.el-image__error) {
		height: 268px;
		width: 320px;
	}
}
.error-img {
	width: 100%;
	height: 268px;
}
</style>
