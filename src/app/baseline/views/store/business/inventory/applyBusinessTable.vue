<!-- 在途单据 table -->
<template>
	<el-scrollbar style="height: 100%">
		<Query
			:queryArrList="queryArrList"
			@getQueryData="getTableData"
			style="margin: 10px 0px -10px 10px !important"
		/>
		<pitaya-table
			ref="tableRef"
			:columns="tableProp"
			:table-data="tableData"
			:need-index="true"
			:single-select="false"
			:need-pagination="true"
			:total="pageTotal"
			:table-loading="tableLoading"
			@on-selection-change="onDataSelected"
			@on-current-page-change="onCurrentPageChange"
			@on-table-sort-change="handleSortChange"
		>
			<!-- 库存查询-业务单据 - 业务单号展示 -->
			<template #code="{ rowData }">
				<link-tag
					:value="rowData.code"
					@on-click="showBusinessComponent(rowData)"
				/>
			</template>

			<!-- 库存查询-业务单据 - 批次 -->
			<template #batchNum="{ rowData }">
				<link-tag
					v-if="rowData.batchNum > 0"
					:value="rowData.batchNum"
					@on-click="showBatchComponent(rowData)"
				/>
				<span v-else>---</span>
			</template>
		</pitaya-table>
	</el-scrollbar>

	<!-- 业务组件详情 -->
	<Drawer
		v-model:drawer="businessComponentVisible"
		:size="modalSize.xl"
		destroy-on-close
	>
		<component
			:is="businessComponent"
			:id="editingTableRow?.businessId"
			@close="businessComponentVisible = false"
		/>
	</Drawer>

	<!-- 批次信息详情 -->
	<Drawer
		v-model:drawer="batchDetailVisible"
		:size="modalSize.md"
		destroy-on-close
	>
		<batch-table
			:table-api="listStoreScreenApplyBusinessBatchPaged"
			:table-req-params="{
				id: editingTableRow?.id,
				materialId: props.materialId
			}"
			@close="batchDetailVisible = false"
		/>
	</Drawer>
</template>

<script setup lang="ts">
import {
	listStoreScreenApplyBusinessPaged,
	listStoreScreenApplyBusinessBatchPaged
} from "@/app/baseline/api/store/inventory-api"
import { useTbInit } from "../../../components/tableBase"
import LinkTag from "../../../components/linkTag.vue"
import {
	IInventoryBusinessType,
	MatGetInventoryParams,
	MatStoreApplyBusinessVo
} from "@/app/baseline/utils/types/store-inventory"
import { modalSize } from "@/app/baseline/utils/layout-config"
import businessComponentMap from "./business-component-map"
import batchTable from "./batchTable.vue"
import {
	batchFormatterNumView,
	getCurentYearFirstDay,
	getNowDate
} from "@/app/baseline/utils"
import { useDictInit } from "../../../components/dictBase"

const props = defineProps<{
	id: any
	materialId: any
	params?: MatGetInventoryParams
}>()

const { dictOptions, getDictByCodeList } = useDictInit()

/**
 * 查询条件 配置
 */
const queryArrList = computed(() => [
	{
		name: "业务类型",
		key: "businessType",
		type: "select",
		placeholder: "请选择",
		children: dictOptions.value["TRANSACTION_DOCUMENT"]
	},
	{
		name: "业务单号",
		key: "businessCode",
		type: "input",
		placeholder: "请输入业务单号"
	},

	{
		name: "仓库名称",
		key: "storeLabel",
		type: "input",
		placeholder: "请输入仓库名称"
	},
	{
		name: "操作时间",
		key: "startAndEndTime",
		type: "startAndEndTime",
		placeholder: "请选择",
		paramsData: [getCurentYearFirstDay(), getNowDate()]
	}
])

const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	fetchParam,
	currentPage,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange,
	onDataSelected
} = useTbInit()

const businessComponentVisible = ref(false)

/**
 * 要查看的采购订单id
 */
const editingTableRow = ref<MatStoreApplyBusinessVo>()

fetchFunc.value = listStoreScreenApplyBusinessPaged

tableProp.value = [
	{
		prop: "type_view",
		label: "业务类型"
	},
	{
		prop: "code",
		label: "业务单号",
		needSlot: true,
		width: 180
	},
	{
		prop: "storeLabel",
		label: "仓库名称"
	},
	{
		prop: "num_view",
		label: "申请数量",
		align: "right"
	},
	{
		prop: "happenNum_view",
		label: "发生数量",
		align: "right"
	},
	{
		prop: "batchNum",
		label: "批次",
		width: 170,
		needSlot: true
	},
	{
		prop: "createdDate",
		label: "操作时间",
		width: 160,
		sortable: true
	},
	{
		prop: "createdBy_view",
		label: "操作人"
	}
]

/**
 * 格式化数量
 */
watch(tableData, () => {
	batchFormatterNumView(tableData.value as any[])
})

/**
 * 业务组件
 *
 * 根据不同事务类型，展示事务的前置业务
 */
const businessComponent = computed(() => {
	return businessComponentMap[
		editingTableRow.value?.type as IInventoryBusinessType
	]
})

const getTableData = (data: Record<string, any>) => {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	const { startAndEndTime } = data

	data.createdDate_start = startAndEndTime ? startAndEndTime[0] : ""
	data.createdDate_end = startAndEndTime ? startAndEndTime[1] : ""

	delete data.startAndEndTime

	if (props.id) {
		fetchParam.value = {
			id: props.id,
			...fetchParam.value,
			costCenterId: props.params?.costCenterId,
			storeId: props.params?.storeId,
			sord: "desc",
			sidx: "createdDate",
			...data
		}
		fetchTableData()
	}
}

onMounted(() => {
	getDictByCodeList(["TRANSACTION_DOCUMENT"])

	fetchParam.value = {
		id: props.id,
		...fetchParam.value,
		costCenterId: props.params?.costCenterId,
		storeId: props.params?.storeId,
		sord: "desc",
		sidx: "createdDate",
		createdDate_start: getCurentYearFirstDay(),
		createdDate_end: getNowDate()
	}
	fetchTableData()
})
/**
 * 展示业务详情
 */
function showBusinessComponent(e?: MatStoreApplyBusinessVo) {
	businessComponentVisible.value = true
	editingTableRow.value = e
}

/**
 * 批次 下钻详情
 */
const batchDetailVisible = ref(false)
function showBatchComponent(e?: MatStoreApplyBusinessVo) {
	batchDetailVisible.value = true
	editingTableRow.value = e
}

/**
 * 排序
 * @param column
 * @param prop 字段名称
 * @param order ascending 升序 descending 降序
 */
function handleSortChange(column: any, prop: any, order: any) {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		sord:
			order === "descending" ? "desc" : order === "ascending" ? "asc" : "desc",
		sidx: order ? prop : "createdDate" // 排序字段
	}

	fetchTableData()
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
