<!-- Tab-批次信息 table -->
<template>
	<el-scrollbar style="height: 100%">
		<Query
			:queryArrList="queryArrList"
			@getQueryData="getTableData"
			style="margin: 10px 0px -10px 10px !important"
		/>
		<pitaya-table
			ref="tableRef"
			:columns="tableProp"
			:table-data="tableData"
			:need-index="true"
			:single-select="false"
			:need-pagination="true"
			:total="pageTotal"
			:table-loading="tableLoading"
			@on-selection-change="onDataSelected"
			@on-current-page-change="onCurrentPageChange"
		>
			<!-- 采购单价 -->
			<template #actualCost="{ rowData }">
				<cost-tag :value="rowData.actualCost" />
			</template>

			<template #freezeNum_view="{ rowData }">
				<link-tag
					:value="rowData.freezeNum_view"
					@on-click="showFreezeNumDetail(rowData)"
				/>
			</template>
		</pitaya-table>
	</el-scrollbar>

	<!-- 冻结数量详情 -->
	<Drawer v-model:drawer="detailVisible" :size="modalSize.xl" destroy-on-close>
		<freeze-table
			:id="props.id"
			:costCenterId="props.params?.costCenterId"
			:storeId="editingTableRow.storeId"
			:roomId="editingTableRow.roomId"
			:batchNo="editingTableRow.batchNo"
			@close="detailVisible = false"
		/>
	</Drawer>
</template>

<script setup lang="ts">
import { listStoreScreenBatchPaged } from "@/app/baseline/api/store/inventory-api"
import { useTbInit } from "../../../components/tableBase"
import { MatGetInventoryParams } from "@/app/baseline/utils/types/store-inventory"
import LinkTag from "../../../components/linkTag.vue"
import CostTag from "../../../components/costTag.vue"
import { modalSize } from "@/app/baseline/utils/layout-config"
import freezeTable from "./freezeTable.vue"
import { batchFormatterNumView } from "@/app/baseline/utils"
import { useDictInit } from "../../../components/dictBase"

const props = defineProps<{
	id: any
	params?: MatGetInventoryParams
}>()

const { dictOptions, getDictByCodeList } = useDictInit()

/**
 * 查询条件 配置
 */
const queryArrList = computed(() => [
	{
		name: "批次号",
		key: "batchNo",
		type: "input",
		placeholder: "请输入批次号"
	},
	{
		name: "仓库编码",
		key: "storeCode",
		type: "input",
		placeholder: "请输入仓库编码"
	},
	{
		name: "仓库名称",
		key: "storeLabel",
		type: "input",
		placeholder: "请输入仓库名称"
	},
	{
		name: "仓库类型",
		key: "storeType",
		type: "select",
		placeholder: "请选择",
		children: dictOptions.value["STORE_TYPE"]
	},
	{
		name: "区域编码",
		key: "regionCode",
		type: "input",
		placeholder: "请输入区域编码"
	},
	{
		name: "货位编码",
		key: "roomCode",
		type: "input",
		placeholder: "请输入货位编码"
	}
])

const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	fetchParam,
	currentPage,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange,
	onDataSelected
} = useTbInit()

fetchFunc.value = listStoreScreenBatchPaged

tableProp.value = [
	{
		prop: "batchNo",
		label: "批次号",
		width: 180
	},
	{
		prop: "regionCode",
		label: "区域编码"
	},
	{
		prop: "roomCode",
		label: "货位编码"
	},
	{
		prop: "storeCode",
		label: "仓库编码"
	},
	{
		prop: "storeLabel",
		label: "仓库名称",
		minWidth: 120
	},
	{
		prop: "storeType_view",
		label: "仓库类型"
	},
	{
		prop: "actualCost",
		label: "单价",
		needSlot: true,
		align: "right"
	},
	{
		prop: "monthBeginNum_view",
		label: "月初量",
		align: "right"
	},
	{
		prop: "monthInNum_view",
		label: "月入量",
		align: "right"
	},
	{
		prop: "monthOutNum_view",
		label: "月出库量",
		align: "right"
	},
	{
		prop: "storeNum_view",
		label: "库存量",
		align: "right"
	},
	{
		prop: "freezeNum_view",
		label: "冻结量",
		needSlot: true,
		align: "right"
	}
]

/**
 * 格式化数量
 */
watch(tableData, () => {
	batchFormatterNumView(tableData.value as any[])
})

const getTableData = (data?: Record<string, any>) => {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	if (props.id) {
		fetchParam.value = {
			id: props.id,
			...fetchParam.value,
			costCenterId: props.params?.costCenterId,
			storeId: props.params?.storeId,
			storeNumZero: props.params?.storeNumZero,
			sord: "desc",
			sidx: "batchNo",
			...data
		}
		fetchTableData()
	}
}

onMounted(() => {
	getDictByCodeList(["STORE_TYPE"])
	getTableData()
})

const detailVisible = ref(false)
const editingTableRow = ref()
function showFreezeNumDetail(e: any) {
	editingTableRow.value = e
	detailVisible.value = true
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
