<!-- 仓库管理-仓库区域 table -->
<template>
	<pitaya-table
		ref="tableRef"
		:columns="tableProp"
		:table-data="tableData"
		:need-index="true"
		:need-pagination="true"
		:total="pageTotal"
		:table-loading="tableLoading"
		@on-selection-change="onDataSelected"
		@on-current-page-change="onCurrentPageChange"
	/>
</template>

<script setup lang="ts">
import { listMatStoreRegionPaged } from "@/app/baseline/api/store/manage-api"
import { useTbInit } from "../../components/tableBase"
import { MatStoreRegionVo } from "@/app/baseline/utils/types/store-manage"

const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	tbBtnLoading,
	pageSize,
	currentPage,
	pageTotal,
	selectedTableList,
	fetchParam,
	tbBtns,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange,
	onDataSelected,
	onBtnClick
} = useTbInit<MatStoreRegionVo, any>()

fetchFunc.value = listMatStoreRegionPaged

tableProp.value = [
	{
		prop: "code",
		label: "区域编码"
	},
	{
		prop: "label",
		label: "区域名称"
	}
]

onMounted(() => {
	fetchTableData()
})
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
