<template>
	<div class="app-container">
		<div class="whole-frame">
			<model-frame class="top-frame">
				<Query
					:query-arr-list="queryArrList"
					@get-query-data="handleQuery"
					class="ml10"
				/>
			</model-frame>
			<model-frame class="bottom-frame">
				<Title
					:title="rightTitle"
					:button="addBtn"
					@on-btn-click="showStoreCreator"
				/>
				<pitaya-table
					ref="tableRef"
					:columns="tableProp"
					:table-data="tableData"
					:need-selection="true"
					:single-select="true"
					:need-index="true"
					:need-pagination="true"
					:total="pageTotal"
					:table-loading="tableLoading"
					@on-selection-change="(e) => (selectedTableList = e)"
					@on-current-page-change="onCurrentPageChange"
					@on-table-sort-change="handleSortChange"
				>
					<!-- 仓库类型 -->
					<template #type="{ rowData }">
						<color-tag
							:bg-color="warehouseTypeTagColorMap[rowData.type as IWarehouseType]"
						>
							{{ rowData.type_view }}
						</color-tag>
					</template>

					<!-- 线路 -->
					<template #lineId="{ rowData }">
						<line-tag :options="lineList" :value="rowData.lineId.split(',')" />
					</template>

					<!-- 仓库状态 -->
					<template #status="{ rowData }">
						<dict-tag
							:options="fmtWarehouseStatusDicts"
							:value="rowData.status"
						/>
					</template>

					<!-- 更新时间 -->
					<template #lastModifiedDate="{ rowData }">
						{{ rowData.lastModifiedDate ?? rowData.createdDate }}
					</template>

					<!-- 库管员 -->
					<template #storeManageNum="{ rowData }">
						<link-tag
							:value="rowData.storeManageNum"
							@click.stop="handleStoreManageNumDetail(rowData)"
						/>
					</template>

					<!-- 审核员 -->
					<template #storeAuditNum="{ rowData }">
						<link-tag
							:value="rowData.storeAuditNum"
							@click.stop="handleStoreAuditNumDetail(rowData)"
						/>
					</template>

					<!-- 操作 -->
					<template #actions="{ rowData }">
						<slot
							v-if="
								(rowData.status !== IWarehouseStatus.disabled &&
									((isCheckPermission(powerList.storeManagelBtnEdit) &&
										hasPermi(rowData.createdBy) &&
										rowData.status === IWarehouseStatus.draft) ||
										(isCheckPermission(powerList.storeManagelBtnEdit) &&
											rowData.status !== IWarehouseStatus.draft))) ||
								isCheckPermission(powerList.storeManagelBtnPreview) ||
								(rowData.status === IWarehouseStatus.draft &&
									isCheckPermission(powerList.storeManagelBtnDrop) &&
									hasPermi(rowData.createdBy))
							"
						>
							<el-button
								v-if="
									rowData.status !== IWarehouseStatus.disabled &&
									((isCheckPermission(powerList.storeManagelBtnEdit) &&
										hasPermi(rowData.createdBy) &&
										rowData.status === IWarehouseStatus.draft) ||
										(isCheckPermission(powerList.storeManagelBtnEdit) &&
											rowData.status !== IWarehouseStatus.draft))
								"
								v-btn
								link
								@click.stop="showStoreEditor(rowData.id)"
								:disabled="checkPermission(powerList.storeManagelBtnEdit)"
							>
								<font-awesome-icon :icon="['fas', 'pen-to-square']" />
								<span class="table-inner-btn">编辑</span>
							</el-button>
							<el-button
								v-if="
									rowData.status === IWarehouseStatus.draft &&
									isCheckPermission(powerList.storeManagelBtnDrop) &&
									hasPermi(rowData.createdBy)
								"
								v-btn
								link
								@click.stop="delStore(rowData)"
								:disabled="checkPermission(powerList.storeManagelBtnDrop)"
							>
								<font-awesome-icon
									:icon="['fas', 'trash-can']"
									style="color: var(--pitaya-btn-background)"
								/>
								<span class="table-inner-btn">移除</span>
							</el-button>
							<el-button
								v-btn
								link
								@click.stop="viewDetail(rowData.id)"
								:disabled="checkPermission(powerList.storeManagelBtnPreview)"
								v-if="isCheckPermission(powerList.storeManagelBtnPreview)"
							>
								<font-awesome-icon
									:icon="['fas', 'eye']"
									style="color: var(--pitaya-btn-background)"
								/>
								<span class="table-inner-btn">查看</span>
							</el-button>
						</slot>

						<slot v-else>---</slot>
					</template>

					<template #footerOperateLeft>
						<button-list
							:is-not-radius="true"
							:loading="tbBtnLoading"
							:button="tableFooterBtns"
							@on-btn-click="handleWarehouseActions"
						/>
					</template>
				</pitaya-table>
			</model-frame>
		</div>

		<!-- 仓库编辑 -->
		<Drawer
			v-model:drawer="warehouseEditorVisible"
			:size="modalSize.xl"
			destroy-on-close
		>
			<warehouse-editor
				:id="editingStoreId"
				@save="fetchTableData"
				@close="warehouseEditorVisible = false"
			/>
		</Drawer>

		<!-- 仓库详情 -->
		<Drawer
			v-model:drawer="warehouseDetailVisible"
			:size="modalSize.xl"
			destroy-on-close
		>
			<warehouse-detail
				:id="editingStoreId"
				@close="warehouseDetailVisible = false"
			/>
		</Drawer>

		<!-- 库管员/审核员 下钻 -->
		<Drawer
			v-model:drawer="warehouseMemberDetailVisible"
			:size="modalSize.md"
			destroy-on-close
		>
			<warehouseMemberDetail
				:warehouseId="editingStoreId"
				:storePersonType="storePersonType"
				@close="warehouseMemberDetailVisible = false"
			/>
		</Drawer>

		<!-- 生成货位码 -->
		<Drawer v-model:drawer="roomQrCodeVisible" size="60%" destroy-on-close>
			<room-qr-code
				:table-api="listMatStoreRoomList"
				:table-req-params="{ storeId: first(selectedTableList)?.id }"
				:store-label="first(selectedTableList)?.label"
				@close="roomQrCodeVisible = false"
			/>
		</Drawer>
	</div>
</template>
<script lang="ts" setup>
import { ref, onMounted } from "vue"
import {
	checkPermission,
	isCheckPermission
} from "@/app/platform/utils/permission"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import LinkTag from "@/app/baseline/views/components/linkTag.vue"
import { useTbInit } from "@/app/baseline/views/components/tableBase"
import { powerList } from "../../components/define.d"
import { BaseLineSysApi, listCompanyWithFormat } from "../../../api/system"
import { LineVo } from "@/app/baseline/utils/types/common"
import { modalSize } from "@/app/baseline/utils/layout-config"
import warehouseDetail from "./warehouseDetail.vue"
import warehouseEditor from "./warehouseEditor.vue"
import warehouseMemberDetail from "./warehouseMemberDetail.vue"
import {
	IWarehouseMemberType,
	IWarehouseStatus,
	IWarehouseType,
	MatStoreRequestParams,
	MatStoreVo
} from "@/app/baseline/utils/types/store-manage"
import {
	deleteMatStorage,
	listMatStoragePaged,
	updateMatStorageStatusBatch,
	listMatStoreRoomList
} from "@/app/baseline/api/store/manage-api"
import lineTag from "../../components/lineTag.vue"
import { warehouseTypeTagColorMap } from "@/app/baseline/utils/colors"
import colorTag from "../components/colorTag.vue"
import { useDictInit } from "../../components/dictBase"
import { getDictLabelFromValue } from "@/app/baseline/utils"
import { first, map } from "lodash-es"
import { useUserStore } from "@/app/platform/store/modules/user"
import { useMessageBoxInit } from "../../components/messageBox"
import { hasPermi } from "../../../utils"
import roomQrCode from "./roomQrCode.vue"
import { SystemDepotVo } from "@/app/baseline/utils/types/system-depot"
import { SystemCostCenterVo } from "@/app/baseline/utils/types/system-cost-center"

const { showWarnConfirm } = useMessageBoxInit()

defineOptions({ name: "StoreManage" })

const { userInfo } = storeToRefs(useUserStore())

const { dictFilter, dictOptions, getDictByCodeList } = useDictInit()

const warehouseEditorVisible = ref<boolean>(false)
const warehouseDetailVisible = ref<boolean>(false)
const warehouseMemberDetailVisible = ref<boolean>(false)
// 列表标题
const rightTitle = {
	name: ["仓库管理"],
	icon: ["fas", "square-share-nodes"]
}
// 标题栏操作按钮
const addBtn = [
	{
		name: "新建仓库",
		roles: powerList.storeManagelBtnCreate,
		icon: ["fas", "square-plus"]
	}
]

const tbBtnLoading = ref(false)

/**
 * 编辑中的仓库id
 */
const editingStoreId = ref()

/**
 * table 底部操作按钮组配置
 */
const tableFooterBtns = computed(() => {
	const selectedTableRow = first(selectedTableList.value)

	const { status } = (selectedTableRow ?? {}) as MatStoreVo

	return [
		{
			name: "冻结",
			roles: powerList.storeManagelBtnFreeze,
			icon: ["fas", "lock"],
			confirm: true,
			disabled: !selectedTableRow || status !== IWarehouseStatus.activated,
			status: IWarehouseStatus.freezed
		},
		{
			name: "启用",
			roles: powerList.storeManagelBtnStart,
			icon: ["fas", "circle-play"],
			confirm: true,
			disabled:
				!selectedTableRow ||
				(status !== IWarehouseStatus.disabled &&
					status !== IWarehouseStatus.draft &&
					status !== IWarehouseStatus.freezed),
			status: IWarehouseStatus.activated
		},
		{
			name: "停用",
			roles: powerList.storeManagelBtnStop,
			icon: ["fas", "circle-pause"],
			confirm: true,
			disabled: !selectedTableRow || status !== IWarehouseStatus.freezed, // 只有在冻结状态下
			status: IWarehouseStatus.disabled
		},
		{
			name: "生成货位码",
			roles: powerList.storeManagelBtnQrCode,
			icon: ["fas", "qrcode"],
			confirm: true,
			disabled:
				!selectedTableRow ||
				(status !== IWarehouseStatus.activated &&
					status !== IWarehouseStatus.inventoryInProgress &&
					status !== IWarehouseStatus.freezed) // 生成货位码
		}
	]
})

const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	currentPage,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = useTbInit<MatStoreVo, MatStoreRequestParams>()

/**
 * table 数据源配置
 */
fetchFunc.value = listMatStoragePaged

// table 列配置
tableProp.value = [
	{
		prop: "code",
		label: "仓库编码",
		minWidth: 100,
		fixed: "left",
		sortable: true
	},
	{
		prop: "label",
		label: "仓库名称",
		minWidth: 120,
		fixed: "left"
	},
	{
		prop: "level_view",
		label: "仓库级别",
		width: 100
	},
	{
		label: "仓库类型",
		prop: "type",
		needSlot: true,
		width: 120
	},
	{
		label: "所属公司",
		prop: "sysCommunityId_view",
		width: 120
	},
	{
		label: "仓库状态",
		prop: "status",
		width: 90,
		needSlot: true
	},

	{
		prop: "depotId_view",
		label: "所属段区"
	},
	{
		prop: "costCenterId_view",
		label: "成本中心",
		minWidth: 120
	},
	{
		prop: "positionId",
		label: "仓库地址"
	},
	{ label: "线路", prop: "lineId", needSlot: true, minWidth: 320 },
	{
		prop: "regionNum",
		label: "区域数量",
		width: 80
	},

	{
		prop: "roomNum",
		label: "货位数量",
		width: 80
	},

	{
		prop: "storeManageNum",
		label: "库管员",
		width: 80,
		needSlot: true
	},
	{
		prop: "storeAuditNum",
		label: "审核员",
		width: 80,
		needSlot: true
	},
	{
		prop: "lastModifiedDate",
		label: "更新时间",
		needSlot: true,
		width: 150,
		sortable: true
	},
	{
		label: "操作",
		prop: "actions",
		fixed: "right",
		needSlot: true,
		width: 200
	}
]

/**
 * 仓库状态字典列表
 */
const fmtWarehouseStatusDicts = computed(() => {
	const dicts = dictOptions.value["STORE_STATUS"]
	return [
		/**
		 * 启用
		 */
		{
			label: getDictLabelFromValue(dicts, IWarehouseStatus.activated),
			value: IWarehouseStatus.activated,
			raw: {
				class: "success"
			}
		},
		/**
		 * 待启用
		 */
		{
			label: getDictLabelFromValue(dicts, IWarehouseStatus.draft),
			value: IWarehouseStatus.draft,
			raw: {
				class: "purple"
			}
		},
		/**
		 * 冻结
		 */
		{
			label: getDictLabelFromValue(dicts, IWarehouseStatus.freezed),
			value: IWarehouseStatus.freezed,
			raw: {
				class: "warning"
			}
		},
		/**
		 * 停用
		 */
		{
			label: getDictLabelFromValue(dicts, IWarehouseStatus.disabled),
			value: IWarehouseStatus.disabled,
			raw: {
				class: "danger"
			}
		},
		/**
		 * 盘点中
		 */
		{
			label: getDictLabelFromValue(dicts, IWarehouseStatus.inventoryInProgress),
			value: IWarehouseStatus.inventoryInProgress,
			raw: {
				class: "primary"
			}
		}
	]
})

// query 配置
const queryArrList = computed<querySetting[]>(() => [
	{
		name: "仓库编码",
		key: "code",
		type: "input",
		placeholder: "请输入仓库编码"
	},
	{
		name: "仓库名称",
		key: "label",
		type: "input",
		placeholder: "请输入仓库名称"
	},
	{
		name: "公司",
		key: "sysCommunityId",
		type: "select",
		children: companyOptions.value,
		placeholder: "请选择公司"
	},
	{
		name: "仓库级别",
		key: "level",
		type: "select",
		children: dictOptions.value["STORE_LEVEL"]
	},

	{
		name: "仓库类型",
		key: "type",
		type: "select",
		children: dictOptions.value["STORE_TYPE"]
	},
	{
		name: "仓库状态",
		key: "status",
		type: "select",
		children: dictOptions.value["STORE_STATUS"]
	},
	{
		name: "线路",
		key: "lineId",
		type: "select",
		children: lineList.value,
		placeholder: "请选择线路"
	},
	{
		name: "段区",
		key: "depotId",
		type: "select",
		children: depotList.value,
		placeholder: "请选择段区"
	},
	{
		name: "成本中心",
		key: "costCenterId",
		type: "select",
		children: costCenterList.value,
		placeholder: "请选择成本中心"
	}
])

// 线路列表
const lineList = ref<LineVo[]>([])

// 段区列表
const depotList = ref<SystemDepotVo[]>([])

// 成本中心
const costCenterList = ref<SystemCostCenterVo>([])

/**
 * 公司列表
 */
const companyOptions = ref<any>([])

onMounted(() => {
	fetchParam.value.sord = "desc"
	fetchParam.value.sidx = "createdDate"
	listCompanyWithFormat().then((r) => (companyOptions.value = r))
	handleQuery()
	getLineList()
	getDepotList()
	getCostCenterList()
	getDictByCodeList(["STORE_LEVEL", "STORE_TYPE", "STORE_STATUS"])
})

const storePersonType = ref<IWarehouseMemberType>(
	IWarehouseMemberType.storekeeper
)
/**
 * 库管员 下钻列表
 * @param e
 */
function handleStoreManageNumDetail(e: MatStoreVo) {
	storePersonType.value = IWarehouseMemberType.storekeeper
	editingStoreId.value = e.id
	warehouseMemberDetailVisible.value = true
}

/**
 * 审核员 下钻列表
 * @param e
 */
function handleStoreAuditNumDetail(e: MatStoreVo) {
	storePersonType.value = IWarehouseMemberType.auditor
	editingStoreId.value = e.id
	warehouseMemberDetailVisible.value = true
}

/**
 * 展示创建仓库编辑器
 */
function showStoreCreator() {
	warehouseEditorVisible.value = true
	editingStoreId.value = undefined
}

/**
 * 获取线路配置
 */
function getLineList() {
	BaseLineSysApi.getLineOptions().then((res) => {
		lineList.value = res
	})
}

/**
 * 获取段区配置
 */
function getDepotList() {
	BaseLineSysApi.getDepotList().then((res) => {
		depotList.value = res
	})
}

/**
 * 获取成本中心配置
 */
function getCostCenterList() {
	BaseLineSysApi.getCostCenterList().then((res) => {
		costCenterList.value = res
	})
}

/**
 * 查看仓库详情
 */
function viewDetail(id: any) {
	warehouseDetailVisible.value = true
	editingStoreId.value = id
}

/**
 * 展示仓库编辑器
 */
function showStoreEditor(id: any) {
	warehouseEditorVisible.value = true
	editingStoreId.value = id
}

/**
 * 删除仓库
 */
async function delStore(e: any) {
	const infoData: string[] = []

	if (e?.regionNum > 0) {
		infoData.push("区域")
	}

	if (e?.roomNum > 0) {
		infoData.push("货位")
	}

	if (e?.storeManageNum > 0) {
		infoData.push("库管员")
	}

	if (e?.storeAuditNum > 0) {
		infoData.push("审核员")
	}

	await showWarnConfirm(
		`${
			infoData.length > 0
				? `该仓库存在${infoData.join("、")}，确定要移除吗？`
				: "确定要移除吗？"
		}`
	)
	await deleteMatStorage(e.id)
	ElMessage.success("操作成功")
	fetchTableData()
}

const roomQrCodeVisible = ref(false)
/**
 * 仓库操作
 *
 * @param name 操作按钮名称
 */
async function handleWarehouseActions(name?: string) {
	if (name === "生成货位码") {
		roomQrCodeVisible.value = true
	} else {
		const warehouseStatus =
			name === "冻结"
				? IWarehouseStatus.freezed
				: name === "启用"
				? IWarehouseStatus.activated
				: IWarehouseStatus.disabled

		if (name === "启用") {
			const hasPermiSome = selectedTableList.value.some((item: any) =>
				hasPermi(item.createdBy)
			)

			if (
				!hasPermiSome &&
				first(selectedTableList.value)?.status === IWarehouseStatus.draft
			) {
				return ElMessage.warning("非本人创建的数据，无法进行操作！")
			}

			const selectedTableRow = first(selectedTableList.value)

			if (selectedTableRow?.regionNum < 1) {
				return ElMessage.warning("该仓库未设置区域，无法启用！")
			}

			if (selectedTableRow?.roomNum < 1) {
				return ElMessage.warning("该仓库未设置货位，无法启用！")
			}

			if (selectedTableRow?.storeManageNum < 1) {
				return ElMessage.warning("该仓库未设置库管员，无法启用！")
			}

			if (selectedTableRow?.storeAuditNum < 1) {
				return ElMessage.warning("该仓库未设置审核员，无法启用！")
			}
		}
		await showWarnConfirm(`请确认是否${name}？`)

		const ids = map(selectedTableList.value, ({ id }) => id).toString()

		tbBtnLoading.value = true
		updateMatStorageStatusBatch(ids, warehouseStatus)
			.then(() => {
				fetchTableData()
			})
			.finally(() => {
				tbBtnLoading.value = false
			})
	}
}

/**
 * 筛选 handler
 */
function handleQuery(params?: any) {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		...params
	}
	fetchTableData()
}

/**
 * 排序
 * @param column
 * @param prop 字段名称
 * @param order ascending 升序 descending 降序
 */
function handleSortChange(column: any, prop: any, order: any) {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		sord:
			order === "descending" ? "desc" : order === "ascending" ? "asc" : "desc",
		sidx: order ? prop : "createdDate" // 排序字段
	}

	fetchTableData()
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.line-name-container {
	display: flex;
	align-items: center;
	.line-item {
		margin-right: 10px;
		padding: 0 10px;
		height: 22px;
		border-radius: 3px;
		font-size: var(--pitaya-fs-12);
		color: #fff;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	.line-item:last-child {
		margin-right: 0;
	}
}
</style>
