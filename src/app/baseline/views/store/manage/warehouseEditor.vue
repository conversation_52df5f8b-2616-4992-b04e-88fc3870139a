<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<!-- 左侧表单区域 -->
		<el-row class="drawer-column left" style="width: 310px">
			<Title :title="drawerLeftTitle" />
			<el-scrollbar class="rows">
				<!-- 编辑仓库信息 -->
				<el-form
					class="content"
					:model="warehouseData"
					:rules="formRules"
					ref="formRef"
					label-position="top"
					label-width="100px"
				>
					<form-element
						:form-element="formEls"
						:form-data="warehouseData"
						drawer-tree-destroy-on-close
					/>
				</el-form>
			</el-scrollbar>
			<button-list
				class="footer"
				:button="btnConf"
				:loading="btnLoading"
				@on-btn-click="handleClickBtns"
			/>
		</el-row>
		<!-- 右侧table区域 -->
		<div
			class="drawer-column right"
			:class="!isEdit ? 'disabled' : ''"
			style="width: calc(100% - 310px)"
		>
			<div class="rows">
				<Title :title="drawerRightTitle">
					<Tabs
						class="tabs"
						:tabs="tabList"
						:activeIndex="activeTab"
						style="margin-right: auto"
						@on-tab-change="activeTab = $event"
					/>
				</Title>
				<div v-if="isEdit" class="detail-table">
					<area-goods-aloc
						v-if="activeTab === 0"
						ref="areaGoodsAlocRef"
						:warehouse-id="warehouseData?.id"
						:type="IModalType.edit"
						:warehouse-code="warehouseData?.code ?? ''"
						:warehouse-label="warehouseData?.label ?? ''"
						:warehouse-status="(warehouseData?.status as IWarehouseStatus)"
						@update="emit('save')"
					/>
					<warehouse-member-table
						v-else-if="activeTab === 1"
						:warehouse-id="warehouseData?.id"
						:editor-type="IModalType.edit"
						@update="emit('save')"
					/>
				</div>
			</div>
			<button-list
				class="footer"
				:button="[
					{
						name: '保存',
						icon: ['fas', 'circle-check']
					}
				]"
				@on-btn-click="handleRightSave"
			/>
		</div>
	</div>
</template>
<script lang="ts" setup>
import { ref, defineProps } from "vue"
import { FormInstance, FormRules } from "element-plus"
import { FormElementType } from "../../components/define"
import FormElement from "@/app/baseline/views/components/formElement.vue"
import {
	IIdempotentTokenType,
	IIdempotentTokenTypePre,
	IModalType,
	LineVo
} from "@/app/baseline/utils/types/common"
import warehouseMemberTable from "./warehouseMemberTable.vue"
import areaGoodsAloc from "./areaGoodsAloc.vue"
import {
	getMatStorage,
	saveMatStorage,
	updateMatStorage
} from "@/app/baseline/api/store/manage-api"
import {
	BaseLineSysApi,
	listCompanyWithFormat
} from "@/app/baseline/api/system"
import { useDictInit } from "../../components/dictBase"
import { omit, split, last } from "lodash-es"
import {
	IWarehouseStatus,
	MatStoreVo
} from "@/app/baseline/utils/types/store-manage"
import {
	getIdempotentToken,
	requiredValidator
} from "@/app/baseline/utils/validate"
import { inputMaxLength } from "@/app/baseline/utils/layout-config"
import { useUserStore } from "@/app/platform/store/modules/user"

interface Props {
	id?: number
	model?: IModalType
}
const props = withDefaults(defineProps<Props>(), {
	model: IModalType.view
})

const emit = defineEmits<{
	(e: "save"): void
	(e: "close"): void
}>()

const { isSuperAdmin, userInfo } = storeToRefs(useUserStore())

const { dictOptions, getDictByCodeList } = useDictInit()

/**
 * 仓库数据
 */
const warehouseData = ref<MatStoreVo>({})

/**
 * 主表单是否禁用
 *
 * 已启用/冻结/盘点中 的仓库，禁止编辑主表单
 */
const disabledMainForm = computed(() => {
	return [
		IWarehouseStatus.activated,
		IWarehouseStatus.freezed,
		IWarehouseStatus.inventoryInProgress
	].includes(warehouseData.value?.status as any)
})

const formRef = ref<FormInstance>()

/**
 * 获取区域货位组件实例
 */
const areaGoodsAlocRef = ref<InstanceType<typeof areaGoodsAloc>>()

const btnLoading = ref(false)
const activeTab = ref(0)
const tabList = ["区域货位", "人员信息"]

const drawerLoading = ref(false)

/**
 * 是否为编辑状态
 */
const isEdit = computed(() => Boolean(warehouseData.value?.id || props.id))

const drawerLeftTitle = computed(() => ({
	name: [isEdit.value ? "编辑仓库" : "新建仓库"],
	icon: ["fas", "square-share-nodes"]
}))

const drawerRightTitle = {
	name: ["相关信息"],
	icon: ["fas", "square-share-nodes"]
}
const btnConf = [
	{
		name: "取消",
		icon: ["fas", "minus-circle"]
	},
	{
		name: "保存",
		icon: ["fas", "floppy-disk"]
	}
]

/**
 * 公司列表
 */
const companyOptions = ref<any>([])

// 表单配置
const formEls = computed(
	() =>
		[
			[
				{
					label: "所属公司",
					name: "sysCommunityId",
					type: "select",
					data: companyOptions.value,
					/**
					 * 非超管角色禁用此项目
					 */
					disabled: !isSuperAdmin.value || disabledMainForm.value
				},
				{
					label: "所属部门",
					name: "sysOrgId_view",
					vname: "sysOrgId",
					type: "treeSelect",
					disabled: disabledMainForm.value,
					treeApi: () =>
						BaseLineSysApi.getDepartmentTreeWithCompanyDisabled(
							warehouseData.value.sysCommunityId
						)
				},
				{
					label: "段区",
					name: "depotId_view",
					vname: "depotId",
					type: "treeSelect",
					disabled: disabledMainForm.value,
					treeApi: BaseLineSysApi.getDepotList
				},
				{
					label: "线路",
					name: "lineId_view",
					vname: "lineId",
					type: "treeSelect",
					needSingleSelect: false,
					disabled: disabledMainForm.value,
					treeApi: BaseLineSysApi.getLineList
				},
				{
					label: "仓库名称",
					name: "label",
					maxlength: inputMaxLength.input,
					disabled: disabledMainForm.value
				},
				{
					label: "仓库编码",
					name: "code",
					placeholder: "请输入6位字母或数字",
					maxlength: 6,
					disabled: disabledMainForm.value
				},
				{
					label: "仓库级别",
					name: "level",
					type: "select",
					data: dictOptions.value["STORE_LEVEL"],
					disabled: disabledMainForm.value
				},
				{
					label: "仓库类型",
					name: "type",
					type: "select",
					data: dictOptions.value["STORE_TYPE"],
					disabled: disabledMainForm.value
				},
				{
					label: "仓库位置",
					name: "positionId",
					maxlength: inputMaxLength.input,
					disabled: disabledMainForm.value
				},
				{
					label: "仓库详细地址",
					name: "address",
					type: "textarea",
					rows: 3,
					maxlength: inputMaxLength.textarea,
					disabled: disabledMainForm.value
				},
				{
					label: "成本中心",
					name: "costCenterId_view",
					vname: "costCenterId",
					type: "treeSelect",
					disabled: disabledMainForm.value,
					treeApi: BaseLineSysApi.getCostCenterList
				},
				{
					label: "备注说明",
					name: "remark",
					type: "textarea",
					rows: 3,
					maxlength: inputMaxLength.textarea,
					disabled: disabledMainForm.value
				}
			]
		] as FormElementType[][] | FormElementType[]
)
// {[k:string]:FormItemRules}
const formRules: FormRules<typeof warehouseData.value> = {
	depotId_view: requiredValidator("段区"),
	lineId_view: requiredValidator("线路"),
	label: requiredValidator("仓库名称"),
	code: [
		{ required: true, message: "仓库编码不能为空", trigger: "change" },
		{
			validator: (rule: any, value: any, callback: any) => {
				const reg = /^[a-zA-Z0-9]{6}$/ // 数字/字母 6位
				if (!reg.test(value)) {
					return callback(new Error("请输入6位字母或数字"))
				}
				callback()
			},
			required: true,
			trigger: "change"
		}
	], //requiredValidator("仓库编码"),
	level: requiredValidator("仓库级别"),
	type: requiredValidator("仓库类型"),
	costCenterId_view: requiredValidator("成本中心"),
	positionId: requiredValidator("仓库位置"),
	address: requiredValidator("仓库详细地址")
}

// 线路列表
const lineList = ref<LineVo[]>([])
/**
 * 获取线路配置
 */
function getLineList() {
	BaseLineSysApi.getLineOptions().then((res) => {
		lineList.value = res
	})
}

onMounted(async () => {
	await getDictByCodeList(["STORE_LEVEL", "STORE_TYPE"])

	getLineList()
	listCompanyWithFormat().then((r) => (companyOptions.value = r))
	getWarehouseDetail()
})

/**
 * 点击 drawer 按钮 handler
 */
function handleClickBtns(name?: string) {
	if (name === "保存") {
		if (!formRef.value) {
			return
		}

		formRef.value.validate(async (valid) => {
			if (!valid) {
				return
			}

			btnLoading.value = true

			/**
			 * 如果存在仓库id，使用更新仓库api
			 * 否则新建仓库 api
			 */
			const isEdit = Boolean(warehouseData.value?.id)
			try {
				const reqApi = isEdit ? updateMatStorage : saveMatStorage

				let idempotentToken = ""
				if (!isEdit) {
					idempotentToken = getIdempotentToken(
						IIdempotentTokenTypePre.apply,
						IIdempotentTokenType.storeApply
					)
				}

				const r = await reqApi(
					omit(warehouseData.value, "undefined"),
					idempotentToken
				)
				ElMessage.success("操作成功")
				warehouseData.value!.id = r?.id
				emit("save")

				if (isEdit) {
					// 如果是编辑模式，同步更新区域信息
					areaGoodsAlocRef.value?.getWarehouseAreaTree?.()
				}
			} finally {
				btnLoading.value = false
			}
		})

		return
	}

	emit("close")
}

/**
 * 获取仓库明细
 */
function getWarehouseDetail() {
	if (!props.id) {
		warehouseData.value = {}
		warehouseData.value.sysCommunityId = userInfo.value.companyId
		warehouseData.value.sysCommunityId_view = userInfo.value.companyName
		return
	}

	drawerLoading.value = true

	getMatStorage(props.id)
		.then((r) => {
			warehouseData.value = r ?? {}
			/* const filterLineIdList = filter(lineList.value, (v) =>
				(r?.lineId?.split(",") || []).includes(String(v.id))
			)
			warehouseData.value.lineId_view = map(
				filterLineIdList,
				({ name }) => name
			).toString() */

			warehouseData.value.code = last(split(warehouseData.value?.code, "-"))
		})
		.finally(() => (drawerLoading.value = false))
}

/**
 * 点击右抽屉保存按钮 handler
 */
function handleRightSave() {
	if (activeTab.value === 0) {
		activeTab.value = 1
	} else {
		emit("close")
	}
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.drawer-column {
	&.right {
		.rows {
			height: auto;
			display: flex;
			flex-direction: column;

			.detail-table {
				height: 100%;
			}
		}
	}
}
</style>
