<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<!-- 左侧表单区域 -->
		<el-row class="drawer-column left">
			<div class="rows" style="width: 310px">
				<Title :title="drawerLeftTitle" />
				<el-scrollbar>
					<!-- 查看仓库信息 -->
					<el-descriptions size="small" :column="1" border class="content">
						<el-descriptions-item
							v-for="desc in descConf"
							:key="desc.label"
							:label="desc.label"
						>
							<!-- 仓库类型 -->
							<color-tag
								v-if="desc.key === 'type'"
								:bg-color="warehouseTypeTagColorMap[storeDescInfo.type as IWarehouseType]"
							>
								{{ storeDescInfo.type_view }}
							</color-tag>
							<!-- 线路 tag -->
							<!-- <line-tag
								v-else-if="desc.key === 'lineId'"
								:options="lineOptions"
								:value="storeDescInfo.lineId!"
							/> -->
							<span v-else>{{
								getNumDefByNumKey(desc.key, storeDescInfo[desc.key])
							}}</span>
						</el-descriptions-item>
					</el-descriptions>
				</el-scrollbar>
			</div>
		</el-row>
		<!-- 右侧table区域 -->
		<div class="drawer-column right" style="width: calc(100% - 310px)">
			<div class="rows">
				<Title :title="drawerRightTitle">
					<Tabs class="tabs" :tabs="tabList" @on-tab-change="handleTabChange" />
				</Title>

				<!-- 表格 -->
				<area-goods-aloc
					v-if="activeTab === 0"
					:warehouse-id="id"
					:warehouse-label="storeDescInfo.label ?? ''"
					:warehouse-code="storeDescInfo.code ?? ''"
				/>
				<warehouse-member-table v-else :warehouse-id="id" />
			</div>
			<button-list
				class="footer"
				:button="[
					{
						name: '取消',
						icon: ['fas', 'circle-minus']
					}
				]"
				@on-btn-click="emit('close')"
			/>
		</div>
	</div>
</template>
<script lang="ts" setup>
import { ref, defineProps } from "vue"
import { LineVo } from "@/app/baseline/utils/types/common"
import {
	IWarehouseType,
	MatStoreVo
} from "@/app/baseline/utils/types/store-manage"
import { getMatStorage } from "@/app/baseline/api/store/manage-api"
import areaGoodsAloc from "./areaGoodsAloc.vue"
import warehouseMemberTable from "./warehouseMemberTable.vue"
import { BaseLineSysApi } from "@/app/baseline/api/system"
import { warehouseTypeTagColorMap } from "@/app/baseline/utils/colors"
import colorTag from "../components/colorTag.vue"
import { getNumDefByNumKey } from "@/app/baseline/utils"

const props = defineProps<{
	/**
	 * 仓库id
	 */
	id: any
}>()

const emit = defineEmits<{
	(e: "close"): void
}>()

/**
 * 仓库信息
 */
const storeDescInfo = ref<MatStoreVo>({})

const activeTab = ref(0)

const tabList = ["区域货位", "人员信息"]

/**
 * 线路列表
 */

const lineOptions = ref<LineVo[]>([])

const drawerLoading = ref(false)

const drawerLeftTitle = computed(() => ({
	name: ["查看仓库"],
	icon: ["fas", "square-share-nodes"]
}))

const drawerRightTitle = {
	name: ["相关信息"],
	icon: ["fas", "square-share-nodes"]
}

/**
 * 仓库明细配置
 */
const descConf = [
	{
		label: "仓库编码",
		key: "code"
	},

	{
		label: "仓库名称",
		key: "label"
	},
	{
		label: "仓库级别",
		key: "level_view"
	},
	{
		label: "仓库类型",
		key: "type_view"
	},
	{
		label: "线路",
		key: "lineId_view"
	},
	{
		label: "所属段区",
		key: "depotId_view"
	},
	{
		label: "成本中心",
		key: "costCenterId_view"
	},
	{
		label: "仓库地址",
		key: "address"
	},
	{
		label: "区域数量",
		key: "regionNum"
	},
	{
		label: "货位数量",
		key: "roomNum"
	},
	{
		label: "库管员",
		key: "storeManageNum"
	},
	{
		label: "审核员",
		key: "storeAuditNum"
	}
]

onMounted(async () => {
	getLineList()
	getDetail()
})

function getLineList() {
	BaseLineSysApi.getLineList().then((r) => (lineOptions.value = r ?? []))
}

/**
 * 获取仓库详情数据
 */
function getDetail() {
	if (!props.id) {
		return
	}
	drawerLoading.value = true
	getMatStorage(props.id!)
		.then((r) => {
			storeDescInfo.value = r ?? {}
		})
		.finally(() => (drawerLoading.value = false))
}

function handleTabChange(i: number) {
	activeTab.value = i
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.drawer-column {
	&.right {
		.rows {
			height: auto;
			display: flex;
			flex-direction: column;

			.detail-table {
				height: 100%;
			}
		}
	}
}
</style>
