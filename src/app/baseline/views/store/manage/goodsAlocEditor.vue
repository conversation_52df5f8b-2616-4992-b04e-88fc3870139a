<!-- 库存管理-仓库管理-货位编辑 -->
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<div class="drawer-column">
			<div class="rows">
				<Title :title="leftTitle" />
				<el-scrollbar>
					<el-form
						class="content form-base"
						ref="formRef"
						:model="formData"
						:rules="formRules"
						label-position="top"
					>
						<form-element :form-element="formEls" :form-data="formData" />
					</el-form>
				</el-scrollbar>
			</div>
			<button-list
				class="footer"
				:loading="btnLoading"
				:button="formBtnList"
				@onBtnClick="handleBtnClick"
			/>
		</div>
	</div>
</template>

<script setup lang="ts">
import {
	MatStoreRoomDTO,
	MatStoreRoomVo
} from "@/app/baseline/utils/types/store-manage"
import { FormElementType } from "../../components/define"
import FormElement from "../../components/formElement.vue"
import {
	saveMatStoreRoom,
	updateMatStoreRoom
} from "@/app/baseline/api/store/manage-api"
import { useDictInit } from "../../components/dictBase"
import { FormInstance, FormItemRule } from "element-plus"
import {
	getIdempotentToken,
	requiredValidator
} from "@/app/baseline/utils/validate"
import { inputMaxLength } from "@/app/baseline/utils/layout-config"
import {
	IIdempotentTokenType,
	IIdempotentTokenTypePre
} from "@/app/baseline/utils/types/common"

const props = defineProps<{
	/**
	 * 货位数据
	 */
	data?: MatStoreRoomVo

	/**
	 * 仓库区域id
	 *
	 * 新增货位场景下使用
	 */
	warehouseAreaId: any

	/**
	 * 仓库区域编码
	 */
	warehouseAreaCode: string
}>()

const emit = defineEmits<{
	(e: "close"): void
	(e: "after-save"): void
}>()

const formRef = ref<FormInstance>()

const btnLoading = ref(false)

const drawerLoading = ref(false)

const formData = ref<MatStoreRoomDTO>({})

const { dictOptions, getDictByCodeList } = useDictInit()

/**
 * 是否为编辑状态
 */
const isEdit = computed(() => Boolean(props.data?.id))

const leftTitle = computed(() => {
	return {
		name: [isEdit.value ? "编辑货位" : "新建货位"]
	}
})

const formRules: Record<string, FormItemRule | FormItemRule[]> = {
	code: [
		requiredValidator("货位编码"),
		{ pattern: /\b\d{6}\b/, message: "请输入6位数字编码" }
	],
	type: requiredValidator("货位类型")
}

const formEls = computed(() => {
	return [
		[
			{
				label: "区域编码",
				name: "areaCode",
				disabled: true
			},
			{
				label: "货位编码",
				name: "code",
				placeholder: "请输入6位数字编码",
				maxlength: 6
			},
			{
				label: "货位类型",
				name: "type",
				type: "select",
				data: dictOptions.value["ROOM_TYPE"] ?? []
			},
			{
				label: "备注说明",
				name: "remark",
				type: "textarea",
				maxlength: inputMaxLength.textarea
			}
		]
	] as FormElementType[][]
})

const formBtnList = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{ name: "保存", icon: ["fas", "floppy-disk"] }
]

onMounted(() => {
	formData.value = { ...(props.data ?? {}) } as MatStoreRoomDTO
	formData.value.code = props.data?.code?.substring(3)
	formData.value.areaCode = props.warehouseAreaCode

	getDictByCodeList(["ROOM_TYPE"])
})

function handleBtnClick(name?: string) {
	if (name === "取消") {
		formData.value = {}
		formRef.value?.clearValidate?.()
		return emit("close")
	}
	btnLoading.value = true

	// 保存逻辑

	if (!formRef.value) {
		return
	}

	formRef.value.validate((valid) => {
		if (!valid) {
			return
		}

		const api = isEdit.value ? updateMatStoreRoom : saveMatStoreRoom

		formData.value.regionId = props.warehouseAreaId

		let idempotentToken = ""
		if (!isEdit.value) {
			idempotentToken = getIdempotentToken(
				IIdempotentTokenTypePre.other,
				IIdempotentTokenType.storeRoomApply,
				formData.value.regionId
			)
		}

		api(formData.value, idempotentToken)
			.then(() => {
				ElMessage.success("操作成功")
				formData.value = {}
				formRef.value?.clearValidate?.()
				emit("after-save")
			})
			.finally(() => (btnLoading.value = false))
	})
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
