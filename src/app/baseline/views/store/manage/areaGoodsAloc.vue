<!-- 仓库管理-区域货位 -->
<template>
	<el-row style="height: 100%">
		<!-- 区域 -->
		<el-col :span="6" style="border-right: 1px solid #ccc; height: 100%">
			<warehouseList
				ref="depTreeRef"
				:is-readonly="type === IModalType.view"
				need-search
				:checked-key="checkedAreaData?.id"
				:tree-data="warehouseAreaTreeData"
				:warehouse-status="props.warehouseStatus"
				:search-text="searchText"
				@add="showAreaCreator"
				@edit="handleEditArea"
				@remove="handleRemoveArea"
				@on-tree-click="handleAreaTreeClick"
				@search="getWarehouseAreaTree"
				@update-search-text="updateSearchText"
			/>
		</el-col>

		<!-- 货位 table -->
		<el-col :span="18">
			<goods-aloc-table-toolbar
				v-if="goodsAlocToolbarBreads.length"
				:can-create="Boolean(checkedAreaData && type !== IModalType.view)"
				:breads="(goodsAlocToolbarBreads as string[])"
				@create="showGoodsAlocEditor(undefined)"
			/>

			<pitaya-table
				ref="tableRef"
				:columns="tableProp"
				:table-data="tableData"
				:need-index="true"
				:need-pagination="true"
				:total="pageTotal"
				:table-loading="tableLoading"
				@on-selection-change="onDataSelected"
				@on-current-page-change="onCurrentPageChange"
			>
				<template #type="{ rowData }">
					{{ getDictLabelFromValue(dictOptions["ROOM_TYPE"], rowData.type) }}
				</template>
				<template #actions="{ rowData }">
					<el-button
						v-btn
						link
						:disabled="props.warehouseStatus !== IWarehouseStatus.draft"
						@click="showGoodsAlocEditor(rowData)"
					>
						<font-awesome-icon :icon="['fas', 'pen-to-square']" />
						<span class="table-inner-btn">编辑</span>
					</el-button>
					<el-button
						v-btn
						link
						:disabled="props.warehouseStatus !== IWarehouseStatus.draft"
						@click="delGoodsAlocRow(rowData.id)"
					>
						<font-awesome-icon :icon="['fas', 'trash-can']" />
						<span class="table-inner-btn">移除</span>
					</el-button>
				</template>
			</pitaya-table>
		</el-col>
	</el-row>

	<!-- 区域编辑 -->
	<Drawer
		v-model:drawer="areaEditorVisible"
		:size="modalSize.sm"
		destroy-on-close
	>
		<warehouse-area-editor
			:editor-type="areaEditorType"
			:data="editingAreaData"
			:warehouse-id="warehouseId"
			@save="handleAfterWarehouseAreaSaved"
			@close="areaEditorVisible = false"
		/>
	</Drawer>

	<!-- 货位编辑 -->
	<drawer v-model:drawer="goodsAlocEditorVisible" destroy-on-close size="350">
		<goods-aloc-editor
			:data="editingGoodsAlocData"
			:warehouse-area-code="checkedAreaData?.code ?? ''"
			:warehouse-area-id="checkedAreaData?.id"
			@close="goodsAlocEditorVisible = false"
			@after-save="
				() => {
					goodsAlocEditorVisible = false
					fetchTableData()
					emit('update')
					getWarehouseAreaTree()
				}
			"
		/>
	</drawer>
</template>

<script setup lang="ts">
import { ref } from "vue"
import { useTbInit } from "../../components/tableBase"
import goodsAlocTableToolbar from "./goodsAlocTableToolbar.vue"
import goodsAlocEditor from "./goodsAlocEditor.vue"
import warehouseAreaEditor from "./warehouseAreaEditor.vue"
import { modalSize } from "@/app/baseline/utils/layout-config"
import {
	deleteMatStoreRegion,
	deleteMatStoreRoom,
	listMatStoreRegion,
	listMatStoreRoomPaged
} from "@/app/baseline/api/store/manage-api"
import {
	IWarehouseStatus,
	MatStoreRegionTreeVo,
	MatStoreRegionVo,
	MatStoreRoomQueryParams,
	MatStoreRoomVo
} from "@/app/baseline/utils/types/store-manage"
import { IModalType } from "@/app/baseline/utils/types/common"
import { useMessageBoxInit } from "../../components/messageBox"
import { getDictLabelFromValue } from "@/app/baseline/utils"
import { useDictInit } from "../../components/dictBase"
import warehouseList from "./warehouseList.vue"
import { debounce } from "lodash-es"

const props = withDefaults(
	defineProps<{
		/**
		 * 仓库id
		 */
		warehouseId: any
		/**
		 * 组件类型，编辑/查看
		 */
		type?: IModalType

		/**
		 * 仓库编码
		 */
		warehouseCode: string

		/**
		 * 仓库名称
		 */
		warehouseLabel: string

		/**
		 * 仓库状态
		 */
		warehouseStatus?: IWarehouseStatus
	}>(),
	{
		type: IModalType.view,
		warehouseStatus: IWarehouseStatus.draft
	}
)
const emit = defineEmits<{
	(e: "update"): void
}>()
defineExpose({
	getWarehouseAreaTree
})

/**
 * 区域编辑器类型
 */
const areaEditorType = ref(IModalType.create)

const { dictOptions, getDictByCodeList } = useDictInit()

const { showDelConfirm } = useMessageBoxInit()

/**
 * 货位部分中的面包屑数据
 */
const goodsAlocToolbarBreads = computed(() => {
	return checkedAreaData.value?.id
		? [
				`${props.warehouseCode} ${props.warehouseLabel}`,
				checkedAreaData.value?.label
		  ]
		: []
})

/**
 * 仓库区域列表数据
 */
const warehouseAreaTreeData = ref<MatStoreRegionTreeVo[]>([])

/**
 * 仓库区域 editor drawer visible
 */
const areaEditorVisible = ref(false)

/**
 * 货位编辑器 visible
 */
const goodsAlocEditorVisible = ref(false)

/**
 * 编辑中的区域数据
 */

const editingAreaData = ref<MatStoreRegionVo>()

const checkedAreaData = ref<MatStoreRegionVo>()

const searchText = ref("")

function updateSearchText(txt?: string) {
	searchText.value = txt ?? ""
	debounce(() => {
		getWarehouseAreaTree()
	}, 500)()
}

/**
 * 编辑中的货位数据
 */
const editingGoodsAlocData = ref<MatStoreRoomVo | undefined>({})

const {
	fetchParam,
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange,
	onDataSelected
} = useTbInit<MatStoreRoomVo, MatStoreRoomQueryParams>()

const defTableProps: TableColumnType[] = [
	{
		label: "货位编码",
		prop: "code"
	},
	{
		label: "货位类型",
		prop: "type",
		needSlot: true
	},
	{
		label: "备注说明",
		prop: "remark"
	}
]

tableProp.value = defTableProps

fetchFunc.value = listMatStoreRoomPaged

// fetchParam.value.regionId = editingAreaData.value?.id

onMounted(() => {
	fetchParam.value.sord = "desc"
	fetchParam.value.sidx = "createdDate"

	getDictByCodeList(["ROOM_TYPE"])
})

watch(
	() => props.warehouseId,
	() => {
		if (props.warehouseId) {
			setTableActionColVisible()
			getWarehouseAreaTree()
		}
	},
	{ immediate: true }
)

/**
 * 获取仓库区域 tree data
 */
function getWarehouseAreaTree() {
	listMatStoreRegion({
		storeId: props.warehouseId,
		label: searchText.value ? `*${searchText.value}*` : ""
	}).then((r) => {
		warehouseAreaTreeData.value = r ?? []

		const isChecked = r?.some((item) => item.id === checkedAreaData.value?.id)
		handleAreaTreeClick(isChecked ? checkedAreaData.value : r[0])
	})
}
/**
 * 配置列表操作列
 */
function setTableActionColVisible() {
	tableProp.value = defTableProps

	if (props.type !== IModalType.view) {
		tableProp.value = [
			...defTableProps,
			{
				label: "操作",
				prop: "actions",
				needSlot: true
			}
		]
	}
}

/**
 * 显示货位编辑
 */
function showGoodsAlocEditor(data: any) {
	goodsAlocEditorVisible.value = true
	editingGoodsAlocData.value = data
}

/**
 * 删除货位数据
 */
function delGoodsAlocRow(id: any) {
	showDelConfirm().then(async () => {
		tableLoading.value = true
		try {
			await deleteMatStoreRoom(id)
			await fetchTableData({ regionId: checkedAreaData.value?.id as any })
			getWarehouseAreaTree()
			emit("update")
			ElMessage.success("操作成功")
		} finally {
			tableLoading.value = false
		}
	})
}

/**
 * 编辑区域
 */
function handleEditArea(data: any = {}) {
	editingAreaData.value = { ...data }
	areaEditorVisible.value = true
	areaEditorType.value = IModalType.edit

	if (
		checkedAreaData.value &&
		editingAreaData.value?.id === checkedAreaData.value?.id
	) {
		fetchParam.value.regionId = editingAreaData.value?.id
	}
}

/**
 * 展示新建区域 drawer
 */
function showAreaCreator() {
	areaEditorVisible.value = true
	areaEditorType.value = IModalType.create
}

/**
 * 删除区域
 */
function handleRemoveArea(data: any) {
	editingAreaData.value = data
	showDelConfirm().then(() => {
		if (editingAreaData.value?.id) {
			deleteMatStoreRegion(editingAreaData.value?.id.toString()).then(() => {
				ElMessage.success("操作成功")
				getWarehouseAreaTree()
				emit("update")
				editingAreaData.value = undefined
			})
		}
	})
}

function handleAfterWarehouseAreaSaved() {
	areaEditorVisible.value = false
	getWarehouseAreaTree()
	emit("update")

	if (checkedAreaData.value?.id) {
		fetchTableData()
	}
}

/**
 * 区域 tree node 点击 handler
 */
function handleAreaTreeClick(data?: any) {
	checkedAreaData.value = data
	fetchParam.value.regionId = data?.id

	if (data?.id) {
		fetchTableData()
	} else {
		tableData.value = []
	}
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
