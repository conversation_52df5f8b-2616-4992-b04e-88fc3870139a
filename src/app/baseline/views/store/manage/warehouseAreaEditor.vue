<!-- 仓库管理-仓库区域编辑 -->
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<div class="drawer-column">
			<div class="rows">
				<Title :title="titleConf" />
				<el-scrollbar>
					<el-form
						class="content form-base"
						:model="formData"
						:rules="formRules"
						ref="formRef"
						label-position="top"
					>
						<form-element :form-element="formEls" :form-data="formData" />
					</el-form>
				</el-scrollbar>
			</div>
			<button-list
				class="footer"
				:button="btnConf"
				@on-btn-click="handleBtnClick"
			/>
		</div>
	</div>
</template>

<script setup lang="ts">
import { MatStoreRegionVo } from "@/app/baseline/utils/types/store-manage"
import formElement from "../../components/formElement.vue"
import { FormInstance, FormItemRule } from "element-plus"
import {
	saveMatStoreRegion,
	updateMatStoreRegion
} from "@/app/baseline/api/store/manage-api"
import {
	IIdempotentTokenType,
	IIdempotentTokenTypePre,
	IModalType
} from "@/app/baseline/utils/types/common"
import { getModalTypeLabel } from "@/app/baseline/utils"
import {
	getIdempotentToken,
	requiredValidator
} from "@/app/baseline/utils/validate"
import { FormElementType } from "../../components/define"
import { inputMaxLength } from "@/app/baseline/utils/layout-config"

const props = defineProps<{
	/**
	 * 仓库区域数据
	 */
	data?: MatStoreRegionVo
	/**
	 * 仓库id
	 */
	warehouseId?: any
	/**
	 * 编辑器类型
	 */
	editorType: IModalType
}>()

const emit = defineEmits<{
	(e: "close"): void
	(e: "save"): void
}>()

const drawerLoading = ref(false)

const formRef = ref<FormInstance>()

const btnConf = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "保存",
		icon: ["fas", "floppy-disk"]
	}
]

const titleConf = computed(() => ({
	name: [getModalTypeLabel(props.editorType, "区域")],
	icon: ["fas", "square-share-nodes"]
}))

const formData = ref<MatStoreRegionVo>({})

const formRules: Record<string, FormItemRule | FormItemRule[]> = {
	code: [
		requiredValidator("区域编码"),
		{ message: "请输入3位数字编码", pattern: /\b\d{3}\b/ }
	],
	label: requiredValidator("区域名称")
}

const formEls: FormElementType[][] = [
	[
		{
			label: "区域编码",
			name: "code",
			maxlength: 3
		},
		{
			label: "区域名称",
			name: "label",
			maxlength: inputMaxLength.input
		}
	]
]

onMounted(() => {
	if (props.editorType === IModalType.edit) {
		formData.value = { ...props.data }
	}
})

function handleBtnClick(name?: string) {
	if (name === "保存") {
		formRef.value?.validate((valid) => {
			if (!valid) {
				return
			}
			drawerLoading.value = true

			const api =
				props.editorType === IModalType.edit
					? updateMatStoreRegion
					: saveMatStoreRegion

			formData.value.storeId = props.data?.storeId || props.warehouseId

			let idempotentToken = ""
			if (props.editorType !== IModalType.edit) {
				idempotentToken = getIdempotentToken(
					IIdempotentTokenTypePre.other,
					IIdempotentTokenType.storeApply,
					formData.value.storeId
				)
			}

			api(formData.value, idempotentToken)
				.then(() => {
					ElMessage.success("操作成功")
					emit("save")
				})
				.finally(() => (drawerLoading.value = false))
		})

		return
	}

	emit("close")
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
