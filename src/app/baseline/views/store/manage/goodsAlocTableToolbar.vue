<!-- 货位table toolbar -->
<template>
	<el-row
		align="middle"
		justify="space-between"
		style="margin-top: 10px; padding: 0 10px"
	>
		<!-- 面包屑部分 -->
		<div class="place-wrapper">
			<div class="place-list" v-if="breads && breads.length">
				<template v-for="(place, index) in breads" :key="index">
					<el-tooltip
						v-if="place.length > 36"
						class="box-item"
						effect="dark"
						:content="place"
						placement="top"
					>
						<span class="title">{{ place }}</span>
					</el-tooltip>
					<span v-else class="title">{{ place }}</span>
					<span class="place-space" v-if="index < breads.length - 1">></span>
				</template>
			</div>
		</div>

		<!-- 按钮部分 -->
		<div v-if="canCreate" class="common-title-wrapper">
			<div class="common-title-right">
				<el-row
					:class="['btn-item', _btnStyle(roles ?? '')]"
					style="margin-right: 10px"
					@click="emit('create')"
				>
					<font-awesome-icon
						class="place-icon"
						:icon="['fas', 'circle-plus']"
					/>
					<span>新建货位</span>
				</el-row>
			</div>
		</div>
	</el-row>
</template>

<script setup lang="ts">
const props = withDefaults(
	defineProps<{
		/**
		 * 面包屑数据
		 */
		breads: string[]

		roles?: string

		/**
		 * 能否新增
		 *
		 * 已选择某个区域，且为编辑模式
		 */
		canCreate: boolean
	}>(),
	{
		breads: () => []
	}
)

const emit = defineEmits<{ (e: "create"): void }>()

const _btnStyle = computed(() => {
	return function (btnName: string) {
		const style = checkPermissionType(btnName)
		// invisible   直接不可见;
		//disable   可见但是不可用;
		//visible   可见又可用;
		if (style == "invisible") return "permission-hidden-btn"
		if (style == "disable") return "unusableBtn"
	}
})
</script>

<style lang="scss" scoped>
.place-wrapper {
	height: var(--pitaya-place-height);
	background-color: var(--pitaya-model-bg-color);
	display: flex;
	align-items: center;
	font-size: var(--pitaya-place-font-size);
	color: var(--pitaya-place-font-color);
	.place-icon {
		color: var(--pitaya-place-icon-color);
		margin: 0 10px;
	}
	.place-space {
		padding: 0 5px;
	}
	.place-list {
		display: flex;
		.title {
			cursor: pointer;
			display: inline-block;
			max-width: 420px !important;
			white-space: nowrap; /* 防止文本换行 */
			overflow: hidden; /* 隐藏溢出内容 */
			text-overflow: ellipsis; /* 使用省略号表示被截断的文本 */
		}
	}
}

.common-title-wrapper {
	display: flex;
	flex-wrap: wrap;
	align-items: center;
	justify-content: space-between;
	font-size: 14px;
	color: var(--pitaya-header-bg-color);
	.common-title-left {
		display: flex;
		align-items: center;
	}

	.common-title-right {
		display: flex;
		align-items: center;
		justify-content: flex-end;
		.btn-item {
			display: flex;
			align-items: center;
			cursor: pointer;
			margin-right: 10px;
		}
	}

	.place-icon {
		color: var(--pitaya-header-bg-color);
		margin: 0 5px 0 10px;
	}
	.place-space {
		padding: 0 5px;
	}
}
</style>
