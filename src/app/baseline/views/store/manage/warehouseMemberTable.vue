<!-- 仓库编辑-人员信息维护 -->

<template>
	<pitaya-table
		ref="tableRef"
		:columns="tableProp"
		:table-data="tableData"
		:need-index="true"
		:need-pagination="true"
		:total="pageTotal"
		:table-loading="tableLoading"
		@on-selection-change="selectedTableList = $event"
		@on-current-page-change="onCurrentPageChange"
	>
		<template #storePersonType="{ rowData }">
			{{ rowData.storePersonType === "1" ? "库管员" : "审核员" }}
		</template>

		<template #sex="{ rowData }">
			<div>{{ rowData.sex == 1 ? "男" : "女" }}</div>
		</template>

		<template #actions="{ rowData }">
			<el-button v-btn link @click="delUser(rowData.id)">
				<font-awesome-icon
					:icon="['fas', 'trash-can']"
					style="color: var(--pitaya-btn-background)"
				/>
				<span class="table-inner-btn">移除</span>
			</el-button>
		</template>
		<template v-if="editorType !== IModalType.view" #footerOperateLeft>
			<button-list
				:button="btnList"
				:is-not-radius="true"
				@on-btn-click="addUser"
			/>
		</template>
	</pitaya-table>

	<!-- 选择用户 -->
	<drawer
		v-model:drawer="userSelectorVisible"
		:size="modalSize.md"
		destroy-on-close
	>
		<user-selector
			:multiple="true"
			:table-fetch-params="userSelectorParams"
			:table-api="listStoreManagerPaged"
			@close="userSelectorVisible = false"
			@save="handleUserSelected"
		/>
	</drawer>
</template>

<script setup lang="ts">
import { ref } from "vue"
import userSelector from "../components/userSelector.vue"
import { modalSize } from "@/app/baseline/utils/layout-config"
import { useTbInit } from "../../components/tableBase"
import {
	IListStoreManagerPagedParams,
	IWarehouseMemberType,
	MatStoreUserQueryParams,
	MatStoreUserVo
} from "@/app/baseline/utils/types/store-manage"
import {
	addBatchMatStoreUser,
	deleteMatStoreUser,
	listMatStoreUserPaged,
	listStoreManagerPaged
} from "@/app/baseline/api/store/manage-api"
import {
	IIdempotentTokenType,
	IIdempotentTokenTypePre,
	IModalType
} from "@/app/baseline/utils/types/common"
import { useMessageBoxInit } from "../../components/messageBox"
import { first, map } from "lodash-es"
import { SystemUserVo } from "@/app/baseline/utils/types/system"
import { getIdempotentToken } from "@/app/baseline/utils/validate"

const props = withDefaults(
	defineProps<{
		/**
		 * 仓库id
		 */
		warehouseId: any

		/**
		 * table 类型 同 modalType
		 */
		editorType: IModalType
	}>(),
	{ editorType: IModalType.view }
)

const emit = defineEmits<{
	(e: "update"): void
}>()

const { showDelConfirm } = useMessageBoxInit()

/**
 * 要添加的仓库人员类型
 */
const addWarehouseUserType = ref<IWarehouseMemberType>()

const userSelectorVisible = ref(false)

/**
 * 用户选择器 api 参数
 */
const userSelectorParams = computed<IListStoreManagerPagedParams>(() => ({
	storeId: props.warehouseId,
	storePersonType: addWarehouseUserType.value as any
}))

const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	fetchTableData,
	fetchParam,
	fetchFunc,
	selectedTableList,
	onCurrentPageChange
} = useTbInit<MatStoreUserVo, MatStoreUserQueryParams>()

fetchFunc.value = listMatStoreUserPaged

fetchParam.value = {
	...fetchParam.value,
	storeId: props.warehouseId,
	sord: "desc",
	sidx: "createdDate"
}

tableProp.value = defTableProps()

const btnList = [
	{
		name: "库管员",
		icon: ["fas", "plus-circle"]
	},
	{
		name: "审核员",
		icon: ["fas", "plus-circle"]
	}
]

onMounted(() => {
	updateTablePropsByType()

	fetchTableData()
})

/**
 * 默认的table col 配置
 */
function defTableProps() {
	return [
		{
			label: "姓名",
			prop: "realname"
		},
		{
			label: "性别",
			prop: "sex",
			needSlot: true
		},
		{
			label: "手机号",
			prop: "phone"
		},
		{
			label: "业务权限",
			prop: "storePersonType",
			needSlot: true
		},
		{
			label: "所属部门",
			prop: "sysOrgId_view"
		},
		{
			label: "职务",
			prop: "station"
		}
	]
}

/**
 * 根据表格类型变更 table col 配置
 *
 * 表格如果不可编辑，则去掉 操作列
 */
function updateTablePropsByType() {
	tableProp.value = defTableProps()

	if (props.editorType !== IModalType.view) {
		// 如果表格可编辑
		tableProp.value.push({
			label: "操作",
			prop: "actions",
			needSlot: true
		})
	}
}

/**
 * 移除仓库人员
 * @param id 人员id
 */
async function delUser(id: number) {
	await showDelConfirm()
	tableLoading.value = true
	try {
		await deleteMatStoreUser(id.toString())
		ElMessage.success("操作成功")
		fetchTableData()
		emit("update")
	} finally {
		tableLoading.value = false
	}
}

/**
 * 新增库管员/审核员
 * @param name 操作按钮 name
 */
function addUser(name?: string) {
	userSelectorVisible.value = true

	addWarehouseUserType.value =
		name === "库管员"
			? IWarehouseMemberType.storekeeper
			: IWarehouseMemberType.auditor
}

/**
 * 选中用户之后的操作
 *
 * 目前只有新增操作，所以默认新增仓库人员
 * @param e 用户数据列表
 */
function handleUserSelected(e?: SystemUserVo[], callback?: any) {
	const userIdList = map(e, ({ username }) => username)
	const username = first(e)?.username
	if (!username) {
		callback()
		return ElMessage.warning("请至少选择一个人员")
	}

	tableLoading.value = true

	const idempotentToken = getIdempotentToken(
		IIdempotentTokenTypePre.other,
		IIdempotentTokenType.storeApply,
		props.warehouseId
	)

	addBatchMatStoreUser(
		{
			storeId: props.warehouseId,
			storePersonType: addWarehouseUserType.value,
			userIdList
		},
		idempotentToken
	)
		.then(() => {
			ElMessage.success("操作成功")
			fetchTableData()
			emit("update")
		})
		.finally(() => (tableLoading.value = false))

	userSelectorVisible.value = false
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
