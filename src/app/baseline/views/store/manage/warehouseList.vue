<!-- 仓库 - 区域 -->
<script lang="ts" setup>
import { Search } from "@element-plus/icons-vue"

import { ref } from "vue"
import EmptySearch from "@/assets/images/empty/empty_search.svg?component"
import { IWarehouseStatus } from "@/app/baseline/utils/types/store-manage"
import { Folder } from "@element-plus/icons-vue"

interface Props {
	treeData: any[]
	needSearch?: boolean // 是否需要搜索
	checkedKey?: any
	warehouseStatus?: IWarehouseStatus
	isReadonly?: boolean
	searchText?: string
}

const props = withDefaults(defineProps<Props>(), {
	needSearch: true
})

const filterText = ref(props.searchText)
const emits = defineEmits([
	"onTreeClick",
	"add",
	"remove",
	"edit",
	"updateSearchText"
])

watch(filterText, () => emits("updateSearchText", filterText.value))
</script>
<template>
	<div class="pitaya-tree-container">
		<Button
			title="新增区域"
			icon="folder"
			width="130"
			@onBtnClick="emits('add')"
			v-if="!props.isReadonly"
		>
			<template v-slot:icon>
				<el-icon :size="14" style="vertical-align: -15%">
					<Folder />
				</el-icon>
			</template>
		</Button>

		<el-row>
			<el-input
				v-if="props.needSearch"
				class="tree-search"
				clearable
				v-model.trim="filterText"
				:suffix-icon="Search"
				placeholder="请输入区域名称"
			/>
		</el-row>
		<el-scrollbar class="area-list-wrap rows mt10">
			<ul class="drawer-left">
				<template v-if="treeData.length > 0">
					<li
						class="flex-dr li"
						:class="[checkedKey == item.id ? 'hover' : '']"
						v-for="item in treeData"
						@click="emits('onTreeClick', item)"
						:key="item.id"
					>
						<div class="f12 flex-dr c-3">
							<slot name="headerIcon" :item="item">
								<!-- 文字前按钮 -->
							</slot>
							<div class="text-wrap" style="vertical-align: middle">
								<el-icon :size="12" style="vertical-align: -15%">
									<Folder />
								</el-icon>

								<span class="text">{{ item.code }}</span>
								<span class="text-label">
									<el-tooltip
										class="box-item"
										effect="dark"
										:content="item.label"
										placement="top"
										:disabled="item.label.length < 20"
									>
										<span>{{ item.label }}</span>
									</el-tooltip>
								</span>
								<span class="text">({{ item.roomNum || 0 }})</span>
							</div>
						</div>
						<!-- 按钮 -->
						<div
							style="display: flex; width: 35px; justify-content: space-between"
							v-if="!props.isReadonly"
						>
							<div class="f14 cp flex-dr">
								<!-- 编辑 -->
								<el-button
									v-btn
									link
									:disabled="props.warehouseStatus !== IWarehouseStatus.draft"
									@click.stop="emits('edit', item)"
								>
									<font-awesome-icon
										:icon="['fas', 'pen-to-square']"
										style="color: var(--pitaya-header-bg-color)"
									/>
								</el-button>
							</div>
							<div class="f14 cp flex-dr">
								<el-button
									v-btn
									link
									:disabled="props.warehouseStatus !== IWarehouseStatus.draft"
									@click.stop="emits('remove', item)"
								>
									<font-awesome-icon
										:icon="['fas', 'trash-can']"
										style="color: var(--pitaya-header-bg-color)"
									/>
								</el-button>
							</div>
						</div>
					</li>
				</template>
				<li v-else class="tc">
					<EmptySearch class="empty_img" />
					<p>未查询到相关信息</p>
				</li>
			</ul>
		</el-scrollbar>
	</div>
</template>
<style lang="scss" scoped>
.tc {
	color: #666666;
	font-size: 12px;
	text-align: center;
}
.empty_img {
	width: 150px;
}
.pitaya-tree-container {
	padding: 0 10px;
	height: calc(100% - 40px);
	/* height: 100%; */
	display: flex;
	flex-direction: column;
	.tree-search {
		margin-top: var(--pitaya-margins-base);
	}
}

:deep(.el-button.is-disabled svg) {
	color: #999 !important;
	background-color: transparent !important;
	border-color: transparent !important;
}

.empty_img {
	width: 150px;
}
.area-list-wrap {
	max-height: 100%;
}
.drawer-left {
	position: relative;
	box-sizing: border-box;

	.tree-search {
		margin-top: var(--pitaya-fs-12);
		margin-bottom: var(--pitaya-fs-12);
	}

	.li {
		line-height: 20px;
		padding: 15px;
		border-bottom: 1px dashed var(--el-border-color);
		.text {
			display: inline-block;
			vertical-align: middle;
			margin-left: 5px;
		}
		.text-label {
			display: inline-block;
			max-width: 170px; // 宽度170px
			white-space: nowrap; /* 不换行 */
			overflow: hidden; /* 超出部分隐藏 */
			text-overflow: ellipsis; /* 溢出部分显示省略号 */
			vertical-align: middle;
			margin-left: 5px;
		}
	}

	.flex-dc {
		display: flex;
		flex-direction: column;
	}

	.li:hover {
		color: #666666;
		background: var(--el-fill-color-light);
		cursor: pointer;
	}

	.hover {
		color: #666666;
		background: var(--el-fill-color-light);
	}
}

.flex-dr {
	display: flex;
	flex-direction: row;
	justify-content: space-between;
	align-items: center;
}
.tc {
	color: #666666;
	font-size: 12px;
	text-align: center;
}

.f12 {
	font-size: 12px;
}

.c-9 {
	color: #999;
}
.c-3 {
	color: #333;
}

.mt20 {
	margin-top: 20px;
}

.cp {
	cursor: pointer;
}
</style>
