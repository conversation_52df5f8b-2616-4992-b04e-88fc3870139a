<!-- 仓库 库管员/审核员 下钻列表 -->
<template>
	<div class="drawer-container">
		<div class="drawer-column" style="width: 100%">
			<Title :title="drawerLeftTitle" />
			<div class="rows">
				<pitaya-table
					ref="tableRef"
					:columns="tableProp"
					:table-data="tableData"
					:need-index="true"
					:need-pagination="true"
					:total="pageTotal"
					:table-loading="tableLoading"
					@on-selection-change="selectedTableList = $event"
					@on-current-page-change="onCurrentPageChange"
				>
					<template #storePersonType="{ rowData }">
						{{ rowData.storePersonType === "1" ? "库管员" : "审核员" }}
					</template>

					<template #sex="{ rowData }">
						<div>{{ rowData.sex == 1 ? "男" : "女" }}</div>
					</template>
				</pitaya-table>
			</div>
			<button-list
				class="footer"
				:button="[
					{
						name: '取消',
						icon: ['fas', 'circle-minus']
					}
				]"
				@on-btn-click="emit('close')"
			/>
		</div>
	</div>
</template>

<script setup lang="ts">
import { useTbInit } from "../../components/tableBase"
import {
	IWarehouseMemberType,
	MatStoreUserQueryParams,
	MatStoreUserVo
} from "@/app/baseline/utils/types/store-manage"
import { listMatStoreUserPaged } from "@/app/baseline/api/store/manage-api"

const props = withDefaults(
	defineProps<{
		/**
		 * 仓库id
		 */
		warehouseId: any
		storePersonType: IWarehouseMemberType
	}>(),
	{}
)

const emit = defineEmits<{
	(e: "close"): void
}>()

const drawerLeftTitle = computed(() => ({
	name: [
		`${
			props.storePersonType === IWarehouseMemberType.storekeeper
				? "库管人员"
				: "审核人员"
		}`
	],
	icon: ["fas", "square-share-nodes"]
}))

const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	fetchTableData,
	fetchParam,
	fetchFunc,
	selectedTableList,
	onCurrentPageChange
} = useTbInit<MatStoreUserVo, MatStoreUserQueryParams>()

fetchFunc.value = listMatStoreUserPaged

fetchParam.value = {
	...fetchParam.value,
	storeId: props.warehouseId,
	storePersonType: props.storePersonType,
	sord: "desc",
	sidx: "createdDate"
}

tableProp.value = defTableProps()

onMounted(() => {
	fetchTableData()
})

/**
 * 默认的table col 配置
 */
function defTableProps() {
	return [
		{
			label: "姓名",
			prop: "realname"
		},
		{
			label: "用户账号",
			prop: "userId",
			width: 120
		},
		{
			label: "性别",
			prop: "sex",
			needSlot: true,
			width: 90
		},
		{
			label: "手机号",
			prop: "phone",
			width: 120
		},
		{
			label: "部门",
			prop: "sysOrgId_view"
		}
	]
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
