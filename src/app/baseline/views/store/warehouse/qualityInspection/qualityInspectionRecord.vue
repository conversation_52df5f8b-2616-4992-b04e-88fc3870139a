<!-- 质量检验 查看质检记录 -->
<script setup lang="ts">
import { DictApi } from "@/app/baseline/api/dict"
import { onMounted, reactive, ref } from "vue"

import DictTag from "@/app/baseline/views/components/dictTag.vue"
import { getInspectRecordList } from "../../../../api/store/warehouse/inspect"
import {
	MatInStoreInspectRecordVO,
	MatInStoreInspectRecordVORequestParams
} from "@/app/baseline/utils/types/store-warehouse-inspect"
import { useTbInit } from "../../../components/tableBase"
import { getInspectionMainToStoreStatus } from "./qualityInspection"
import { batchFormatterNumView } from "@/app/baseline/utils"
import { getRealLength, setString } from "@/app/baseline/utils/validate"

export interface Props {
	qualityRow: Record<string, any>
}

const props = defineProps<Props>()

const emits = defineEmits(["close"])

/**
 * 表单数据源
 */
const formModal = ref()
const drawerLoading = ref(false)

/**
 * 左侧 title 配置
 */
const drawerLeftTitle = {
	name: ["物资信息"],
	icon: ["fas", "square-share-nodes"]
}

/**
 * 左侧 表单 配置
 */
const formEl = reactive([
	{ label: "物资编码", name: "materialCode" },
	{ label: "物资名称", name: "materialName" },
	{ label: "规格型号", name: "version" },
	{ label: "技术参数", name: "technicalParameter", needTooltip: true },
	{ label: "物资性质", name: "attribute" },
	{ label: "可质检数量", name: "num_view" }
])

/**
 * 右侧 title 配置
 */
const drawerRightTitle = {
	name: ["质检信息"],
	icon: ["fas", "square-share-nodes"]
}

const formBtnList = [{ name: "取消", icon: ["fas", "circle-minus"] }]

onMounted(() => {
	formModal.value = { ...props.qualityRow }

	getTableData()
})

const {
	tableData,
	tableProp,
	tableRef,
	tableLoading,
	currentPage,
	pageTotal,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = useTbInit<
	MatInStoreInspectRecordVO,
	MatInStoreInspectRecordVORequestParams
>()

/**
 * table 列 配置
 */
tableProp.value = [
	{ label: "质检时间", prop: "createdDate" },
	{ label: "合格数量", prop: "qualifiedNum_view", align: "right" },
	{ label: "不合格数量", prop: "unqualifiedNum_view", align: "right" },
	{ label: "质检情况说明", prop: "remark" },
	{ label: "推送入库状态", prop: "toStoreStatus", needSlot: true }
]

/**
 * 格式化数量
 */
watch(tableData, () => {
	batchFormatterNumView(tableData.value as any[])
})

/**
 * table 数据源
 * @param data
 */
const getTableData = (data?: Record<string, any>) => {
	if (props.qualityRow) {
		fetchFunc.value = getInspectRecordList

		currentPage.value = 1
		tableRef.value?.resetCurrentPage()

		fetchParam.value = {
			sidx: "createdDate",
			sord: "desc",
			...fetchParam.value,
			itemId: props.qualityRow.id,
			...data
		}
		fetchTableData()
	}
}
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<!-- 左侧表单区域 -->
		<div class="drawer-column left">
			<div class="rows">
				<Title :title="drawerLeftTitle" />
				<el-descriptions size="small" :column="1" border class="content">
					<template>
						<el-descriptions-item
							v-for="(el, index) in formEl"
							label-align="center"
							:label="el.label"
							:key="index"
						>
							<dict-tag
								v-if="el.name === 'attribute'"
								:options="DictApi.getMatAttr()"
								:value="formModal?.attribute"
							/>

							<span v-else-if="el.needTooltip">
								<el-tooltip
									effect="dark"
									:content="formModal?.[el.name]"
									:disabled="
										getRealLength(formModal?.[el.name]) <= 100 ? true : false
									"
								>
									{{
										getRealLength(formModal?.[el.name]) > 100
											? setString(formModal?.[el.name], 100)
											: formModal?.[el.name] || "---"
									}}
								</el-tooltip>
							</span>
							<span v-else>
								{{
									formModal?.[el.name] || formModal?.[el.name] == 0
										? formModal?.[el.name]
										: "---"
								}}
							</span>
						</el-descriptions-item>
					</template>
				</el-descriptions>
			</div>
		</div>

		<!-- 右侧table区域 -->
		<div class="drawer-column right">
			<div class="rows">
				<Title :title="drawerRightTitle" />
				<PitayaTable
					ref="tableRef"
					:columns="tableProp"
					:table-data="tableData"
					:need-index="true"
					:need-pagination="true"
					:multi-select="false"
					:need-selection="false"
					:total="pageTotal"
					@onSelectionChange="selectedTableList = $event"
					@on-current-page-change="onCurrentPageChange"
					:table-loading="tableLoading"
				>
					<!-- 推送状态 -->
					<template #toStoreStatus="{ rowData }">
						<dict-tag
							:options="getInspectionMainToStoreStatus()"
							:value="rowData.toStoreStatus"
						/>
					</template>
				</PitayaTable>
			</div>
			<ButtonList
				class="footer"
				:button="formBtnList"
				@on-btn-click="emits('close')"
			/>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.drawer-container {
	.custom-q {
		margin: 10px 0px -10px 10px !important;
	}

	.left {
		width: 310px;
	}

	.right {
		width: calc(100% - 310px);
	}

	/* .tab-mat {
		height: 100%;
	} */
}
</style>
