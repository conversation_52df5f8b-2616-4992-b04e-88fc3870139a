<!-- 质量检验 表单 -->
<script setup lang="ts">
import { ElMessage } from "element-plus"
import { FormElementType } from "@/app/baseline/views/components/define"
import { computed, onMounted, reactive, ref } from "vue"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import TableFile from "@/app/baseline/views/components/tableFile.vue"
import { DictApi, fileBusinessType } from "@/app/baseline/api/dict"
import CostTag from "@/app/baseline/views/components/costTag.vue"

import {
	editInspectMatList,
	getInspectById,
	getInspectDetail,
	pushInspectStore
} from "../../../../api/store/warehouse/inspect"
import {
	MatInStoreInspectItemRequestParams,
	MatInStoreInspectItemVO,
	MatInStoreInspectVO
} from "@/app/baseline/utils/types/store-warehouse-inspect"
import {
	IIdempotentTokenType,
	IIdempotentTokenTypePre,
	IModalType
} from "@/app/baseline/utils/types/common"
import { useMessageBoxInit } from "../../../components/messageBox"
import { useTbInit } from "../../../components/tableBase"
import { useDictInit } from "../../../components/dictBase"
import {
	getInspectionMainStatus,
	getInspectionMainToStoreStatus,
	MatInspectionStatus
} from "./qualityInspection"
import { map } from "lodash"
import { modalSize } from "@/app/baseline/utils/layout-config"
import qualityInspectionForm from "./qualityInspectionForm.vue"
import qualityInspectionRecord from "./qualityInspectionRecord.vue"
import {
	batchFormatterNumView,
	tableColFilter,
	toFixedTwo
} from "@/app/baseline/utils"
import { IInventoryBusinessType } from "@/app/baseline/utils/types/store-inventory"
import { getIdempotentToken } from "@/app/baseline/utils/validate"

export interface Props {
	id: string | number //
	model?: string //显示模式 view, viewPlan
	formBtnLoading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
	model: IModalType.view,
	formBtnLoading: false
})

const { dictOptions, getDictByCodeList, dictFilter } = useDictInit()

const emits = defineEmits(["update:formBtnLoading", "update", "close"])

const { showWarnConfirm } = useMessageBoxInit()

const formBtnLoading = computed({
	get() {
		return props.formBtnLoading
	},
	set(value) {
		emits("update:formBtnLoading", value)
	}
})
/* const formBtnLoading = ref(false) */

/**
 * 按钮 配置
 */
const formBtnList = computed(() => {
	if (props.model == IModalType.edit) {
		return [
			{ name: "取消", icon: ["fas", "circle-minus"] },
			{
				name: "推送入库",
				icon: ["fas", "circle-check"]
			}
		]
	} else {
		return [{ name: "取消", icon: ["fas", "circle-minus"] }]
	}
})

/**
 * 表单数据源
 */
const formModal = ref<MatInStoreInspectVO>({})
const drawerLoading = ref(false)

/**
 * 左侧 title 配置
 */
const drawerLeftTitle = {
	name: ["质检单信息"],
	icon: ["fas", "square-share-nodes"]
}

/**
 * 左侧 表单 配置
 */
const formEl = reactive<FormElementType[]>([
	{ label: "质检单号", name: "code" },
	{ label: "关联业务单号", name: "preBusinessCode" },
	{ label: "入库仓库名称", name: "storeName" },
	{ label: "质检状态", name: "status" },
	{ label: "推送入库状态", name: "toStoreStatus" },
	{ label: "质检物资数量", name: "inspectNum_view" },
	{ label: "已质检物资数量", name: "inspectedNum_view" },
	{ label: "待质检物资数量", name: "toInspectNum_view" },
	{ label: "推送质检时间", name: "createdDate" },
	{ label: "质检员", name: "inspectUsername_view" }
])

/**
 * 右侧 title 配置
 */
const drawerRightTitle = {
	name: ["扩展信息"],
	icon: ["fas", "square-share-nodes"]
}

/**
 * 扩展栏标签页切换
 */
const tabList = ["物资明细", "相关附件"]
const activeTab = ref<number>(0)
const handleTabChange = (index: number) => {
	activeTab.value = index

	if (index === 0) {
		fetchParam.value = {
			applyId: props.id,
			//inspectUsername: formModal.value.inspectUsername,
			sord: "desc",
			sidx: "createdDate"
		}
		pageSize.value = 20
		getTableData()
	}
}

/**
 * 详情
 */
const getDetail = () => {
	if (!props.id) {
		return false
	}
	drawerLoading.value = true
	getInspectById(props.id as number)
		.then((res: any) => {
			formModal.value = res
			formModal.value.toInspectNum_view = toFixedTwo(
				formModal.value.toInspectNum
			)
			formModal.value.inspectedNum_view = toFixedTwo(
				formModal.value.inspectedNum
			)
			formModal.value.inspectNum_view = toFixedTwo(formModal.value.inspectNum)
		})
		.finally(() => {
			drawerLoading.value = false
		})
}
onMounted(async () => {
	getDictByCodeList(["INVENTORY_UNIT", "MATERIAL_NATURE"])

	await getDetail()
	getTableData()
})

/**
 * 查询条件 配置
 */
const queryArrList = computed(() => [
	{
		name: "质检状态",
		key: "status",
		placeholder: "请选择",
		type: "select",
		children: getInspectionMainStatus()
	},
	{
		name: "推送入库状态",
		key: "toStoreStatus",
		placeholder: "请选择",
		type: "select",
		children: getInspectionMainToStoreStatus()
	},
	{
		name: "物资编码",
		key: "materialCode",
		type: "input",
		placeholder: "请输入物资编码"
	},
	{
		name: "物资名称",
		key: "materialLabel",
		type: "input",
		placeholder: "请输入物资名称"
	},
	{
		name: "规格型号",
		key: "version",
		placeholder: "请输入规格型号",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资性质",
		key: "attribute",
		type: "select",
		children: dictOptions.value.MATERIAL_NATURE,
		placeholder: "请选择物资性质"
	}
])

const tbInit = useTbInit<
	MatInStoreInspectItemVO,
	MatInStoreInspectItemRequestParams
>()
const {
	tableData,
	tableRef,
	tableLoading,
	currentPage,
	pageTotal,
	pageSize,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = tbInit

/**
 * table 列 配置
 */
tbInit.tableProp = computed(() => {
	const defCols: TableColumnType[] = [
		{ label: "物资编码", prop: "materialCode", width: 130, fixed: "left" },
		{ label: "物资名称", prop: "materialName", minWidth: 150 },
		{ label: "规格型号", prop: "version", minWidth: 150 },
		{
			label: "技术参数",
			prop: "technicalParameter",
			minWidth: 200
		},
		{ label: "物资性质", prop: "attribute", needSlot: true, width: 120 },
		{ label: "采购单位", prop: "buyUnit", needSlot: true, minWidth: 80 },
		{
			label: "采购单价",
			prop: "price",
			needSlot: true,
			minWidth: 120,
			align: "right"
		},
		{
			label: "金额",
			prop: "amount",
			needSlot: true,
			align: "right",
			minWidth: 120
		},
		{ label: "可质检量", prop: "num_view", width: 100, align: "right" },
		{ label: "待质检量", prop: "canCheckNum_view", width: 100, align: "right" },
		{
			label: "合格量",
			prop: "qualifiedNum_view",
			width: 100,
			fixed: "right",
			align: "right"
		},
		{
			label: "不合格量",
			prop: "unQualifiedNum_view",
			width: 100,
			align: "right",
			fixed: "right"
		},
		{
			label: "质检状态",
			prop: "status",
			needSlot: true,
			width: 100,
			fixed: "right"
		},
		{
			label: "可推送入库数量",
			prop: "toStoreNum_view",
			width: 120,
			align: "right",
			fixed: "right"
		},
		{
			label: "已推送入库数量",
			prop: "toStoreCompleteNum_view",
			width: 120,
			fixed: "right",
			align: "right"
		},
		{
			label: "推送入库状态",
			prop: "toStoreStatus",
			needSlot: true,
			width: 100,
			fixed: "right"
		},
		/* { label: "质检情况说明", prop: "remark", width: 100, fixed: "right" }, */
		{
			label: "操作",
			width: 160,
			prop: "operations",
			needSlot: true,
			fixed: "right"
		}
	]

	/* if (IModalType.view === props.model) {
		return tableColFilter(defCols, ["操作"])
	} */

	if (
		formModal.value.preBusinessType ===
		IInventoryBusinessType.warehouseReturnApply
	) {
		// 退库申请时 不显示此字段
		return tableColFilter(defCols, ["采购单价"])
	} else {
		return defCols
	}
})

/**
 * 格式化数量
 */
watch(tableData, () => {
	batchFormatterNumView(tableData.value as any[])
})

/**
 * table 数据源
 * @param data
 */
const getTableData = (data?: Record<string, any>) => {
	if (props.id) {
		fetchFunc.value = getInspectDetail

		currentPage.value = 1
		tableRef.value?.resetCurrentPage()

		fetchParam.value = {
			...fetchParam.value,
			applyId: props.id,
			//inspectUsername: formModal.value.inspectUsername,
			sord: "desc",
			sidx: "createdDate",
			...data
		}
		fetchTableData()
	}
}

/**
 * 物资明细 -> 一键质检按钮 是否显示
 */
const isDisabledQualityBtn = computed(() => {
	return selectedTableList.value.length > 0 ? false : true
})

/**
 * 所选行；是否包含 已质检状态
 */
const isQuality = computed(() => {
	const statusList = map(selectedTableList.value, (v) => v.status)
	// 判断 所选行；是否包含 已质检状态
	const isQuality = statusList.includes(String(MatInspectionStatus.qualitied))

	// true包含已质检
	return isQuality
})

/**
 * 按钮配置
 */
const tableFooterBtns = computed(() => {
	return [
		{
			name: "一键质检合格",
			icon: ["fas", "file-signature"],
			disabled: isDisabledQualityBtn.value || isQuality.value
		}
	]
})

/**
 * 质量检验操作
 */
const selectedRow = ref()
const qualityEditVisible = ref(false)
function handleEdit(e: MatInStoreInspectItemVO) {
	selectedRow.value = e
	qualityEditVisible.value = true
}

/**
 * 质检记录 查看
 */
const qualityViewVisible = ref(false)
function handleView(e: MatInStoreInspectItemVO) {
	qualityViewVisible.value = true
	selectedRow.value = e
}

/**
 * 一键质检合格 操作
 */
const handleAllQuality = async () => {
	await showWarnConfirm("已选择的物资是否全部质检合格？")
	const params = map(selectedTableList.value, (v) => {
		return {
			id: v.id,
			qualifiedNum:
				v.status == MatInspectionStatus.partialQuality ? v.canCheckNum : v.num,
			remark: "合格",
			unQualifiedNum: 0
		}
	})

	try {
		formBtnLoading.value = true
		await editInspectMatList(params)

		ElMessage.success("操作成功")
		await fetchTableData()
		getDetail()
		emits("update")
	} finally {
		formBtnLoading.value = false
	}
}

/**
 * 表单按钮 操作
 * @param btnName
 */
const onFormBtnList = async (btnName: string | undefined) => {
	if (btnName === "取消") {
		if (props.model == IModalType.edit) {
			if (formModal.value.toStoreNum! > 0) {
				formBtnLoading.value = true
				try {
					await showWarnConfirm("您有已质检的物资还未推送入库，是否推送入库？")

					const idempotentToken = getIdempotentToken(
						IIdempotentTokenTypePre.other,
						IIdempotentTokenType.qualityInspection,
						formModal.value.id
					)
					await pushInspectStore(
						props.id || formModal.value.id,
						idempotentToken
					)
					ElMessage.success(
						`已将已质检物资全部推送至${
							formModal.value?.preBusinessType ==
							IInventoryBusinessType.warehouseReturnApply
								? "退库入库"
								: "接收入库"
						}！`
					)
					emits("update")
				} finally {
					emits("close")
					formBtnLoading.value = false
				}
			}
		}
		emits("close")
	} else if (btnName === "推送入库") {
		await showWarnConfirm(
			`是否将已质检物资全部推送至${
				formModal.value?.preBusinessType ==
				IInventoryBusinessType.warehouseReturnApply
					? "退库入库"
					: "接收入库"
			}？`
		)

		formBtnLoading.value = true
		try {
			const idempotentToken = getIdempotentToken(
				IIdempotentTokenTypePre.other,
				IIdempotentTokenType.qualityInspection,
				formModal.value.id
			)
			await pushInspectStore(props.id || formModal.value.id, idempotentToken)
			ElMessage.success(
				`已将已质检物资全部推送至${
					formModal.value?.preBusinessType ==
					IInventoryBusinessType.warehouseReturnApply
						? "退库入库"
						: "接收入库"
				}！`
			)
			emits("update")
			emits("close")
		} finally {
			formBtnLoading.value = false
		}
	}
}
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<!-- 左侧表单区域 -->
		<div class="drawer-column left">
			<div class="rows">
				<Title :title="drawerLeftTitle" />
				<el-descriptions size="small" :column="1" border class="content">
					<template>
						<el-descriptions-item
							v-for="(el, index) in formEl"
							label-align="center"
							:label="el.label"
							:key="index"
						>
							<!-- 质检状态 -->
							<span v-if="el.name == 'status'">
								<dict-tag
									:value="formModal[el.name]!"
									:options="getInspectionMainStatus()"
								/>
							</span>

							<!-- 推送状态 -->
							<span v-else-if="el.name == 'toStoreStatus'">
								<dict-tag
									:value="formModal[el.name]!"
									:options="getInspectionMainToStoreStatus()"
								/>
							</span>

							<span v-else>
								{{
									formModal[el.name] || formModal[el.name] == 0
										? formModal[el.name]
										: "---"
								}}
							</span>
						</el-descriptions-item>
					</template>
				</el-descriptions>
			</div>
		</div>

		<!-- 右侧table区域 -->
		<div class="drawer-column right">
			<div class="rows">
				<Title :title="drawerRightTitle">
					<Tabs class="tabs" :tabs="tabList" @on-tab-change="handleTabChange" />
				</Title>
				<div v-if="activeTab === 0">
					<el-scrollbar class="tab-mat">
						<Query
							:queryArrList="queryArrList"
							@getQueryData="getTableData"
							class="custom-q"
						/>
						<PitayaTable
							ref="tableRef"
							:columns="(tbInit.tableProp as any)"
							:table-data="tableData"
							:need-index="true"
							:need-pagination="true"
							:multi-select="props.model === 'view' ? false : true"
							:need-selection="props.model === 'view' ? false : true"
							:total="pageTotal"
							@onSelectionChange="selectedTableList = $event"
							@on-current-page-change="onCurrentPageChange"
							:table-loading="tableLoading"
						>
							<!-- 物资性质 -->
							<template #attribute="{ rowData }">
								<dict-tag
									:options="DictApi.getMatAttr()"
									:value="rowData.attribute"
								/>
							</template>

							<template #buyUnit="{ rowData }">
								{{
									dictFilter("INVENTORY_UNIT", rowData.buyUnit)?.subitemName ||
									"---"
								}}
							</template>

							<!-- 采购单价 -->
							<template #price="{ rowData }">
								<cost-tag :value="rowData.price" />
							</template>
							<!-- 采购金额 -->
							<template #amount="{ rowData }">
								<cost-tag :value="rowData.amount" />
							</template>
							<!-- 质检状态 -->
							<template #status="{ rowData }">
								<dict-tag
									:options="getInspectionMainStatus()"
									:value="rowData.status"
								/>
							</template>

							<!-- 质检状态 -->
							<template #toStoreStatus="{ rowData }">
								<dict-tag
									:options="getInspectionMainToStoreStatus()"
									:value="rowData.toStoreStatus"
								/>
							</template>

							<template #operations="{ rowData }">
								<slot>
									<el-button
										v-if="IModalType.edit === props.model"
										v-btn
										link
										:disabled="rowData.status == MatInspectionStatus.qualitied"
										@click.stop="handleEdit(rowData)"
									>
										<font-awesome-icon :icon="['fas', 'pen-to-square']" />
										<span class="table-inner-btn">质量检验</span>
									</el-button>

									<el-button v-btn link @click.stop="handleView(rowData)">
										<font-awesome-icon :icon="['fas', 'eye']" />
										<span class="table-inner-btn">查看</span>
									</el-button>
								</slot>
							</template>

							<template
								#footerOperateLeft
								v-if="props.model == IModalType.edit"
							>
								<ButtonList
									class="btn-list"
									:is-not-radius="true"
									:button="tableFooterBtns"
									:loading="formBtnLoading"
									@on-btn-click="handleAllQuality"
								/>
							</template>
						</PitayaTable>
					</el-scrollbar>
				</div>
				<div v-if="activeTab === 1">
					<TableFile
						:business-type="fileBusinessType.qualityInspect"
						:business-id="props.id"
						:mod="props.model"
					/>
				</div>
			</div>
			<ButtonList
				class="footer"
				:button="formBtnList"
				:loading="formBtnLoading"
				@on-btn-click="onFormBtnList"
			/>

			<!-- 质量检验 Drawer -->
			<Drawer
				:size="modalSize.sm"
				v-model:drawer="qualityEditVisible"
				:destroyOnClose="true"
			>
				<quality-inspection-form
					:qualityRow="selectedRow"
					:descData="formModal"
					@update="
						() => {
							fetchTableData()
							getDetail()
							emits('update')
						}
					"
					@close="qualityEditVisible = false"
				/>
			</Drawer>

			<!-- 查看质检记录 Drawer -->
			<Drawer
				:size="modalSize.lg"
				v-model:drawer="qualityViewVisible"
				:destroyOnClose="true"
			>
				<quality-inspection-record
					:qualityRow="selectedRow"
					@close="qualityViewVisible = false"
				/>
			</Drawer>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.drawer-container {
	.custom-q {
		margin: 10px 0px -10px 10px !important;
	}

	.left {
		width: 340px;
	}

	.right {
		width: calc(100% - 340px);
	}

	.tab-mat {
		height: 100%;
	}
}
</style>
