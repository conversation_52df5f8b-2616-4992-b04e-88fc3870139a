/**
 * 质检 tab 枚举值
 */
export enum MatInspectionMainStatus {
	"noFinish" = "1", // 待处理
	"finish" = "2", // 已完成
	"transfer" = "3" // 转派记录
}

/* 质检状态 */
export enum MatInspectionStatus {
	"noQuality" = "1", // 未质检
	"partialQuality" = "2", // 部分质检
	"qualitied" = "3" // 已质检
}

/**
 * 入库管理 - 列表 质检状态
 */
export function getInspectionMainStatus() {
	return [
		{
			label: "待质检",
			value: MatInspectionStatus.noQuality,
			raw: { class: "warning" }
		},
		{
			label: "部分质检",
			value: MatInspectionStatus.partialQuality,
			raw: { class: "primary" }
		},
		{
			label: "已质检",
			value: MatInspectionStatus.qualitied,
			raw: { class: "success" }
		}
	]
}
/**
 * 入库管理 - 列表 推送状态
 * @returns
 */
export function getInspectionMainToStoreStatus() {
	return [
		{
			label: "待推送",
			value: "1",
			raw: { class: "warning" }
		},
		{
			label: "部分推送",
			value: "2",
			raw: { class: "primary" }
		},
		{
			label: "已推送",
			value: "3",
			raw: { class: "success" }
		}
	]
}
