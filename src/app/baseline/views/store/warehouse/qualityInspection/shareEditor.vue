<!-- 转派 抽屉 -->
<script setup lang="ts">
import { ElMessage, FormInstance, FormRules } from "element-plus"
import { FormElementType } from "@/app/baseline/views/components/define"
import { onMounted, reactive, ref } from "vue"

import FormElement from "@/app/baseline/views/components/formElement.vue"

import {
	getIdempotentToken,
	requiredValidator
} from "@/app/baseline/utils/validate"
import { PurchaseOrderApi } from "@/app/baseline/api/purchase/purchaseOrder"
import {
	IIdempotentTokenType,
	IIdempotentTokenTypePre
} from "@/app/baseline/utils/types/common"
import { useMessageBoxInit } from "../../../components/messageBox"
import {
	getInspectById,
	transferInspect
} from "@/app/baseline/api/store/warehouse/inspect"
import userSelector from "@/app/baseline/views/store/components/userSelector.vue"
import { modalSize } from "@/app/baseline/utils/layout-config"
import { useUserStore } from "@/app/platform/store/modules/user"
import { listInspectorPaged } from "@/app/baseline/api/system"
import { SystemUserVo } from "@/app/baseline/utils/types/system"
import { first } from "lodash-es"

export interface Props {
	id: any
}
const { showWarnConfirm } = useMessageBoxInit()

const props = defineProps<Props>()

const emits = defineEmits<{
	(e: "update"): void
	(e: "close"): void
}>()

const { userInfo } = storeToRefs(useUserStore())
const loading = ref(false)

const drawerLoading = ref(false)

const receiveUsernameUserVisible = ref(false)

const formRef = ref<FormInstance>()

/**
 * 按钮 配置
 */
const formBtnListEdit = [
	{ name: "取消", icon: ["fas", "circle-minus"] },
	{ name: "确认", icon: ["fas", "circle-check"] }
]

/**
 * 表单数据源
 */
const formModal = ref<Record<string, any>>({
	reason: ""
})
const descData = ref<Record<string, any>>({})

/**
 * 左侧title 配置
 */
const drawerLeftTitle = {
	name: ["转派"],
	icon: ["fas", "square-share-nodes"]
}

/**
 *表 单按钮 操作
 * @param btnName
 */
const onFormBtnList = async (btnName: string | undefined) => {
	if (btnName === "取消") {
		emits("close")
	} else if (btnName === "确认") {
		if (!formRef.value) {
			return
		}
		formRef.value?.validate(async (valid) => {
			if (!valid) {
				return
			}
			await showWarnConfirm("确认将质检单转派吗？")
			loading.value = true
			try {
				const idempotentToken = getIdempotentToken(
					IIdempotentTokenTypePre.other,
					IIdempotentTokenType.qualityInspection,
					props.id
				)
				await transferInspect(
					{
						id: props.id,
						reason: descData.value.reason,
						receiveUsername: descData.value.receiveUsername
					},
					idempotentToken
				)

				ElMessage.success("操作成功")
				emits("update")
				emits("close")
			} finally {
				loading.value = false
			}
		})
	}
}
const formElBase = computed(() => [
	[
		{
			label: "质检单号",
			name: "code",
			type: "input",
			disabled: true
		},
		{
			label: "接收人",
			name: "receiveUsername_view",
			type: "drawer",
			placeholder: "请选择",
			clickApi: () => (receiveUsernameUserVisible.value = true)
		},
		{
			label: "转派原因",
			name: "reason",
			type: "textarea",
			maxlength: 200,
			rows: "5"
		}
	]
])

// 左侧表单校验
const formRules = computed<FormRules<typeof formModal.value>>(() => {
	return {
		reason: requiredValidator("转派原因"),
		receiveUsername_view: requiredValidator("接收人")
	}
})
const getDetail = () => {
	if (!props.id) {
		return false
	}
	drawerLoading.value = true
	getInspectById(props.id)
		.then((res: any) => {
			descData.value = res
		})
		.finally(() => {
			drawerLoading.value = false
		})
}

onMounted(async () => {
	getDetail()
})

const userSelectorTableProps = [
	{
		prop: "realname",
		label: "用户姓名"
	},
	{
		prop: "username",
		label: "用户账号"
	},
	{
		prop: "sex",
		label: "性别",
		needSlot: true
	},
	{
		prop: "phone",
		label: "手机号"
	},
	{
		prop: "team",
		label: "质检组"
	},
	{
		prop: "sysOrgId_view",
		label: "部门"
	}
]

function handleSaveReceiveUsername(e?: SystemUserVo[], callback?: any) {
	const username = first(e)?.username

	descData.value.receiveUsername = username
	descData.value.receiveUsername_view = first(e)?.realname

	receiveUsernameUserVisible.value = false
}
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<!-- 左侧表单区域 -->
		<div class="drawer-column">
			<Title :title="drawerLeftTitle" />
			<el-scrollbar class="rows">
				<el-form
					class="content"
					:model="descData"
					ref="formRef"
					label-position="top"
					label-width="100px"
					:rules="formRules"
				>
					<form-element :form-element="formElBase" :form-data="descData" />
				</el-form>
			</el-scrollbar>
			<ButtonList
				class="footer"
				:button="formBtnListEdit"
				:loading="loading"
				@on-btn-click="onFormBtnList"
			/>
		</div>

		<!-- 技术鉴定人选择器 -->
		<Drawer
			v-model:drawer="receiveUsernameUserVisible"
			:size="modalSize.md"
			destroy-on-close
		>
			<user-selector
				:table-api="listInspectorPaged"
				:table-fetch-params="{ filterUsername: userInfo.userName }"
				:table-props="userSelectorTableProps"
				:selected-ids="[descData.receiveUsername]"
				@save="handleSaveReceiveUsername"
				@close="receiveUsernameUserVisible = false"
			/>
		</Drawer>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
