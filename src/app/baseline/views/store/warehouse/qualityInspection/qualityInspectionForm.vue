<script setup lang="ts">
import { computed, onMounted, ref, toRef, toValue } from "vue"
import FormElement from "@/app/baseline/views/components/formElement.vue"
import { FormElementType } from "@/app/baseline/views/components/define.d"
import { ElMessage, type FormInstance, type FormRules } from "element-plus"
import {
	MatInStoreInspectItemVO,
	MatInStoreInspectVO
} from "../../../../utils/types/store-warehouse-inspect"
import { editInspectMat } from "../../../../api/store/warehouse/inspect"
import { pick, toNumber } from "lodash-es"
import { useDictInit } from "../../../components/dictBase"
import { toMoney } from "@/app/baseline/utils"
import { validateAndCorrectInput } from "@/app/baseline/utils/validate"
import { IInventoryBusinessType } from "@/app/baseline/utils/types/store-inventory"
import { useMessageBoxInit } from "../../../components/messageBox"

const { getDictByCodeList, dictFilter } = useDictInit()
const { showWarnConfirm } = useMessageBoxInit()

export interface Props {
	qualityRow?: MatInStoreInspectItemVO
	descData?: MatInStoreInspectVO
}

const props = defineProps<Props>()
const curQualityRowData = toRef(props, "qualityRow")

/* Title 配置 */
const qualityDrawerTitle = {
	name: ["质量检验"],
	icon: ["fas", "square-share-nodes"]
}
/* 按钮 配置 */
const formBtnList = ref([
	{ name: "取消", icon: ["fas", "circle-minus"] },
	{ name: "确定", icon: ["fas", "check-circle"], disabled: false }
])

const emits = defineEmits<{
	(e: "close"): void
	(e: "update"): void
}>()
const ruleFormRef = ref<FormInstance>()

const formModelData = ref<{ [propName: string]: any }>({
	...curQualityRowData.value,
	qualifiedNum: 0,
	unQualifiedNum: 0,
	remark: ""
})
formModelData.value.price_view = toMoney(formModelData.value.price)

formModelData.value.unitLabel = computed(() => {
	return (
		dictFilter("INVENTORY_UNIT", formModelData.value.buyUnit)?.subitemName ||
		"---"
	)
})
formModelData.value.attribute_view = computed(() => {
	return (
		dictFilter("MATERIAL_NATURE", formModelData.value.attribute)?.subitemName ||
		"---"
	)
})

/* 表单 配置 */
const formEl = computed<FormElementType[][]>(() => [
	[{ label: "物资编码", name: "materialCode", disabled: true }],
	[{ label: "物资名称", name: "materialName", disabled: true }],
	[{ label: "规格型号", name: "version", disabled: true }],
	[
		{
			label: "技术参数",
			name: "technicalParameter",
			type: "textarea",
			disabled: true
		}
	],
	[{ label: "物资性质", name: "attribute_view", disabled: true }],
	[{ label: "采购单位", name: "unitLabel", disabled: true }],
	[
		{
			label: "采购单价",
			name: "price_view",
			disabled: true,
			hidden:
				props.descData?.preBusinessType ===
				IInventoryBusinessType.warehouseReturnApply
					? true
					: false
		}
	],
	[
		{
			label: "待质检数量",
			name: "canCheckNum",
			disabled: true,
			type: "number"
		}
	],
	[
		{
			label: "质检合格数量",
			name: "qualifiedNum",
			type: "number",
			min: 0,
			input: (value: any) => {
				formModelData.value.qualifiedNum = validateAndCorrectInput(value)
			},
			blur: (event: any) => {
				formModelData.value.qualifiedNum = toNumber(event.target.value)
			}
		}
	],
	[
		{
			label: "质检不合格数量",
			name: "unQualifiedNum",
			type: "number",
			min: 0,
			input: (value: any) => {
				formModelData.value.unQualifiedNum = validateAndCorrectInput(value)

				setTimeout(() => {
					ruleFormRef.value?.clearValidate()
				}, 0)
			},
			blur: (event: any) => {
				formModelData.value.unQualifiedNum = toNumber(event.target.value)
			}
			//max: formModelData.value.canCheckNum - formModelData.value.qualifiedNum
		}
	],
	[
		{
			label: "质检情况说明",
			name: "remark",
			maxlength: 200,
			rows: 5,
			type: "textarea"
		}
	]
])

/**
 * 较验 质检合格数量 < 待质检数量
 * @param rule
 * @param value
 * @param callback
 */
const validateStoreNum = (rule: any, value: any, callback: any) => {
	if (formModelData.value.canCheckNum > 0) {
		if (formModelData.value.qualifiedNum > formModelData.value.canCheckNum) {
			return callback(new Error("不能大于待质检数量"))
		} else {
			callback()
		}
	}
	callback()
}

/**
 * 较验 质检不合格数量 + 合格数量 <=待质检数量
 * @param rule
 * @param value
 * @param callback
 */
const validateUnQualifiedNum = (rule: any, value: any, callback: any) => {
	/* if (
		formModelData.value.canCheckNum > 0 &&
		formModelData.value.qualifiedNum < formModelData.value.canCheckNum
	) { */
	if (
		parseFloat(formModelData.value.unQualifiedNum) +
			parseFloat(formModelData.value.qualifiedNum) >
		formModelData.value.canCheckNum
	) {
		return callback(new Error("质检不合格数量 + 合格数量 不能小于待质检数量"))
	}
	//}
	callback()
}

/* 表单校验 配置 */
const rules = computed<FormRules<typeof formModelData.value>>(() => ({
	qualifiedNum: [
		{
			required: true,
			message: "质检合格数量不能为空",
			trigger: "change"
		},
		{ validator: validateStoreNum, required: true, trigger: "change" }
	],
	unQualifiedNum: [{ validator: validateUnQualifiedNum, required: false }],
	remark: [
		{
			required: formModelData.value.unQualifiedNum > 0 ? true : false,
			message: "质检情况说明不能为空",
			trigger: "change"
		}
	]
}))

/* 表单 操作 */
const drawerBtnLoading = ref(false)
const onFormBtnList = (btnName: string | undefined) => {
	if (btnName === "确定") {
		if (ruleFormRef.value) {
			ruleFormRef.value.validate(async (valid) => {
				if (valid) {
					await showWarnConfirm("请确认是否质检本次数据？")

					drawerBtnLoading.value = true
					const params: { [propName: string]: any } = toValue(formModelData)

					editInspectMat(
						pick(params, "id", "qualifiedNum", "remark", "unQualifiedNum")
					)
						.then(() => {
							ElMessage.success("操作成功")
							emits("update")
							emits("close")
						})
						.finally(() => {
							drawerBtnLoading.value = false
						})
				}
			})
		}
	} else if (btnName === "取消") {
		emits("close")
	}
}
onMounted(() => {
	getDictByCodeList(["INVENTORY_UNIT", "MATERIAL_NATURE"])
})
</script>
<template>
	<div class="drawer-container">
		<div class="drawer-column">
			<div class="rows">
				<Title :title="qualityDrawerTitle" />
				<el-scrollbar>
					<el-form
						style="padding-bottom: 50px"
						class="content form-base"
						ref="ruleFormRef"
						:model="formModelData"
						:rules="rules"
						label-position="top"
					>
						<FormElement :form-element="formEl" :form-data="formModelData" />
					</el-form>
				</el-scrollbar>
			</div>
			<ButtonList
				class="footer"
				:button="formBtnList"
				:loading="drawerBtnLoading"
				@onBtnClick="onFormBtnList"
			/>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.drawer-column {
	width: 100%;
}
</style>
