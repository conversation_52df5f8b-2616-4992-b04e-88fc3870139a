<script lang="ts" setup>
import { onMounted, ref, computed } from "vue"
import {
	checkPermission,
	isCheckPermission
} from "@/app/platform/utils/permission"
import { powerList } from "../../components/define.d"

import { listCompanyWithFormat } from "@/app/baseline/api/system"
import { useDictInit } from "@/app/baseline/views/components/dictBase"
import { useTbInit } from "@/app/baseline/views/components/tableBase"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import LinkTag from "@/app/baseline/views/components/linkTag.vue"
import CostTag from "@/app/baseline/views/components/costTag.vue"
import ViewDrawer from "./receivedStored/viewDrawer.vue"
import { getInStoreStatus } from "@/app/baseline/views/store/warehouse/receivedStored/receivedStored"
import {
	MatInStoreReceiveRequestParams,
	MatInStoreReceiveVO
} from "../../../utils/types/store-warehouse-receive"
import {
	getInStoreReceiveBpmStatusCnt,
	getReceiveList
} from "../../../api/store/warehouse/receive"
import { modalSize } from "@/app/baseline/utils/layout-config"
import businessComponentMap from "../../store/business/inventory/business-component-map"
import { IInventoryBusinessType } from "@/app/baseline/utils/types/store-inventory"
import { InStoreStatus } from "./warehouse"
import { omit } from "lodash-es"

const { dictOptions, getDictByCodeList } = useDictInit()

/**
 * 公司列表
 */
const companyOptions = ref<any>([])

/**
 * 查询条件 配置
 */
const queryArrList = computed<querySetting[]>(() => {
	return [
		{
			name: "公司",
			key: "companyNo",
			type: "select",
			children: companyOptions.value,
			placeholder: "请选择公司"
		},
		{
			name: "入库类型",
			key: "preBusinessType",
			placeholder: "请选择入库类型",
			type: "select",
			children: dictOptions.value["IN_STORE_TYPE"]
		},
		{
			name: "入库单号",
			key: "code",
			placeholder: "请输入入库单号",
			enableFuzzy: true,
			type: "input"
		}
		/* {
			name: "费用类别",
			key: "costType",
			type: "treeSelect",
			//childen: dictOptions.value["COST_CATEGORY"],
			treeApi: () => Promise.resolve(dictOptions.value["COST_CATEGORY"]),
			placeholder: "请选择费用类别"
		} */
	]
})

/**
 * tab 切换 table数据源
 * @param data
 */
const getTableData = (data?: Record<string, any>) => {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()
	fetchParam.value = {
		...fetchParam.value,
		tableType: tabStatus.value,
		sord: "desc",
		sidx: "createdDate",
		...data
	}
	tableProp.value = [
		...tablePropGroup["base"],
		...tablePropGroup[`status${tabStatus.value}`]
	]
	handleUpdate()
}

/**
 * title 配置
 */
const rightTitle = {
	name: ["接收入库"],
	icon: ["fas", "square-share-nodes"]
}

/**
 * tab 配置
 */
const statusCnt = ref<number[]>([])
const tabList = [
	{
		name: "待入库",
		value: InStoreStatus.noIn
	},
	{
		name: "已入库",
		value: InStoreStatus.in
	}
]

/**
 * tab 操作
 */
const tabStatus = ref(tabList[0].value)
const activeName = ref(tabList[0].name)
const handleTabsClick = (tab: any) => {
	activeName.value = tab.paneName
	tabStatus.value = tabList[tab.index].value
	getTableData()
}

const {
	tableData,
	tableProp,
	tableRef,
	tableLoading,
	currentPage,
	pageTotal,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = useTbInit<MatInStoreReceiveVO, MatInStoreReceiveRequestParams>()

/**
 * table 列 配置
 */
const tablePropGroup: Record<string, any> = {
	base: [
		{
			label: "入库单号",
			prop: "code",
			minWidth: 180,
			fixed: "left",
			sortable: true
		},
		{
			label: "关联质检单号",
			prop: "preBusinessCode",
			needSlot: true,
			minWidth: 200
		},
		{
			label: "关联业务单号",
			prop: "prePreBusinessCode",
			needSlot: true,
			minWidth: 200
		},
		{ label: "入库类型", prop: "prePreBusinessType_view", width: 100 },
		{ label: "入库仓库名称", prop: "storeName", minWidth: 150 },
		{
			label: "入库金额",
			prop: "inStoreMoney",
			needSlot: true,
			align: "right",
			minWidth: 150
		}
		/* { label: "申请时间", prop: "applyDate", minWidth: 150 } */
	],
	status1: [
		{
			label: "入库状态",
			prop: "status", //  枚举值 1:未入库 2:部分入库
			minWidth: 120,
			needSlot: true
		},
		{ label: "入库物资编码", prop: "matCount", minWidth: 100 },
		{
			label: "待入库物资编码",
			prop: "notInspectedCount",
			minWidth: 110
		},
		{
			label: "已入库物资编码",
			prop: "inspectedCount",
			minWidth: 110
		},
		{ label: "申请时间", prop: "createdDate", minWidth: 150, sortable: true },
		{
			label: "操作",
			width: 150,
			prop: "operations",
			needSlot: true,
			fixed: "right"
		}
	],
	status2: [
		{ label: "入库物资编码", prop: "matCount", minWidth: 100 },
		{ label: "入库完成时间", prop: "applyFinishDate", minWidth: 120 },
		{
			label: "操作",
			width: 150,
			prop: "operations",
			needSlot: true,
			fixed: "right"
		}
	]
}
fetchFunc.value = getReceiveList

const curRowId = ref<any>("")
const curRowData = ref<any>("")
const showViewDrawer = ref<boolean>(false)
const model = ref<string>("view")

/**
 * 业务组件
 *
 * 根据不同事务类型，展示事务的前置业务
 */
const businessComponent = computed(() => {
	const curBusinessType =
		businessComponentType.value == "quality"
			? curRowData.value?.preBusinessType
			: curRowData.value?.prePreBusinessType

	return businessComponentMap[curBusinessType as IInventoryBusinessType]
})

/**
 * 查看 操作
 * @param row
 */
const onRowView = (row: MatInStoreReceiveVO) => {
	curRowId.value = row.id
	curRowData.value = row
	showViewDrawer.value = true
	model.value = "view"
}

/**
 * 编辑 操作
 * @param row
 */
const onRowEdit = (row: MatInStoreReceiveVO) => {
	curRowId.value = row.id
	curRowData.value = row
	showViewDrawer.value = true
	model.value = "edit"
}

/**
 * 关联业务单号 操作
 */
const businessComponentVisible = ref(false)
const businessComponentType = ref("quality") // quality(质检单) | business(业务单)
const onRowBusinessOrder = (row: MatInStoreReceiveVO, type: string) => {
	curRowId.value = row.id
	curRowData.value = row
	businessComponentVisible.value = true
	businessComponentType.value = type
}

/**
 * 更新主列表/tab 状态统计
 */
const handleUpdate = async () => {
	statusCnt.value = await getInStoreReceiveBpmStatusCnt(
		omit(
			{
				...fetchParam.value
			},
			"tableType",
			"currentPage",
			"pageSize"
		) as any
	)
	fetchTableData()
}

onMounted(() => {
	getDictByCodeList(["IN_STORE_TYPE"])
	listCompanyWithFormat().then((r) => (companyOptions.value = r))
	getTableData()
})

/**
 * 排序
 * @param column
 * @param prop 字段名称
 * @param order ascending 升序 descending 降序
 */
function handleSortChange(column: any, prop: any, order: any) {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		sord:
			order === "descending" ? "desc" : order === "ascending" ? "asc" : "desc",
		sidx: order ? prop : "createdDate" // 排序字段
	}

	fetchTableData()
}
</script>
<template>
	<div class="app-container">
		<div class="whole-frame">
			<ModelFrame class="top-frame">
				<Query
					:queryArrList="queryArrList"
					@getQueryData="getTableData"
					class="ml10"
				/>
			</ModelFrame>
			<ModelFrame class="bottom-frame">
				<Title :title="rightTitle">
					<div class="app-tabs-wrapper">
						<el-tabs v-model="activeName" @tab-click="handleTabsClick">
							<el-tab-pane
								v-for="(tab, index) in tabList"
								:key="index"
								:label="`${tab.name}（${statusCnt[index] ?? 0}）`"
								:name="tab.name"
								:index="tab.name"
							/>
						</el-tabs>
					</div>
				</Title>

				<PitayaTable
					ref="tableRef"
					:columns="tableProp"
					:tableData="tableData"
					:total="pageTotal"
					:need-index="true"
					@on-current-page-change="onCurrentPageChange"
					:table-loading="tableLoading"
					@on-table-sort-change="handleSortChange"
				>
					<!-- 关联质检单号 -->
					<template #preBusinessCode="{ rowData }">
						<link-tag
							:value="rowData.preBusinessCode"
							@on-click="onRowBusinessOrder(rowData, 'quality')"
						/>
					</template>

					<!-- 关联业务单号 -->
					<template #prePreBusinessCode="{ rowData }">
						<link-tag
							:value="rowData.prePreBusinessCode"
							@on-click="onRowBusinessOrder(rowData, 'business')"
						/>
					</template>

					<!-- 入库金额 -->
					<template #inStoreMoney="{ rowData }">
						<cost-tag :value="rowData.inStoreMoney" />
					</template>

					<!-- 入库状态 -->
					<template #status="{ rowData }">
						<dict-tag :options="getInStoreStatus()" :value="rowData.status" />
					</template>
					<template #operations="{ rowData }">
						<slot
							v-if="
								(tabStatus == InStoreStatus.noIn &&
									isCheckPermission(
										powerList.storeWarehouseReceivedStoredBtnEdit
									)) ||
								isCheckPermission(
									powerList.storeWarehouseReceivedStoredBtnPreview
								)
							"
						>
							<el-button
								v-btn
								link
								@click="onRowEdit(rowData)"
								v-if="
									tabStatus == InStoreStatus.noIn &&
									isCheckPermission(
										powerList.storeWarehouseReceivedStoredBtnEdit
									)
								"
								:disabled="
									checkPermission(powerList.storeWarehouseReceivedStoredBtnEdit)
								"
							>
								<font-awesome-icon :icon="['fas', 'pen-to-square']" />
								<span class="table-inner-btn">编辑</span>
							</el-button>
							<el-button
								v-btn
								link
								@click="onRowView(rowData)"
								:disabled="
									checkPermission(
										powerList.storeWarehouseReceivedStoredBtnPreview
									)
								"
								v-if="
									isCheckPermission(
										powerList.storeWarehouseReceivedStoredBtnPreview
									)
								"
							>
								<font-awesome-icon :icon="['fas', 'eye']" />
								<span class="table-inner-btn">查看</span>
							</el-button>
						</slot>
						<slot v-else>---</slot>
					</template>
				</PitayaTable>

				<!-- 编辑/查看弹窗 -->
				<Drawer
					:size="modalSize.xl"
					v-model:drawer="showViewDrawer"
					:destroyOnClose="true"
				>
					<view-drawer
						:id="curRowId"
						:model="model"
						@close="showViewDrawer = false"
						@update="handleUpdate"
					/>
				</Drawer>

				<!-- 关联业务单号 -->
				<Drawer
					:size="modalSize.xl"
					v-model:drawer="businessComponentVisible"
					:destroyOnClose="true"
				>
					<component
						:is="businessComponent"
						:id="
							businessComponentType == 'quality'
								? curRowData?.preBusinessId
								: curRowData?.prePreBusinessId
						"
						@close="businessComponentVisible = false"
					/>
				</Drawer>
			</ModelFrame>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
