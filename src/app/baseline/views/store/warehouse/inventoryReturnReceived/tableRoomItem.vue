<script setup lang="ts">
import { defineProps, onMounted } from "vue"
import { useTbInit } from "@/app/baseline/views/components/tableBase"
import CostTag from "@/app/baseline/views/components/costTag.vue"
import {
	MatInStoreReturnSearchRoomRequestParams,
	MatReturnStoreItemVO
} from "../../../../utils/types/store-warehouse-return"
import { getReturnSearchRoom } from "../../../../api/store/warehouse/return"
import { batchFormatterNumView } from "@/app/baseline/utils"

export interface Props {
	id: string | number // 物资明细ID
}

const props = defineProps<Props>()
const {
	tableData,
	tableProp,
	tableRef,
	tableLoading,
	currentPage,
	pageTotal,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange,
	onDataSelected
} = useTbInit<MatReturnStoreItemVO, MatInStoreReturnSearchRoomRequestParams>()

/**
 * table 列 配置
 */
tableProp.value = [
	{
		label: "入库货位编码",
		prop: "roomCode",
		minWidth: 130,
		fixed: "left"
	},
	{ label: "单价", prop: "price", needSlot: true, minWidth: 130 },
	{
		label: "已入库数量",
		prop: "completeNum_view",
		align: "right",
		minWidth: 130
	},
	{
		label: "入库金额",
		prop: "amount",
		needSlot: true,
		align: "right",
		minWidth: 130
	},
	{ label: "操作人", prop: "username_view", minWidth: 130 },
	{ label: "入库时间", prop: "inStoreTime", minWidth: 130 }
]

/**
 * 格式化数量
 */
watch(tableData, () => {
	batchFormatterNumView(tableData.value as any[])
})

/**
 * 初始化列表数据
 * @param data
 */
const getTableData = (data?: any) => {
	if (props.id) {
		fetchFunc.value = getReturnSearchRoom

		currentPage.value = 1
		tableRef.value?.resetCurrentPage()

		fetchParam.value = {
			itemId: props.id,
			sord: "desc",
			sidx: "createdDate",
			...data
		}
		fetchTableData()
	}
}
onMounted(() => {
	getTableData()
})
</script>
<template>
	<div class="tab-mat">
		<div class="tab-left">
			<PitayaTable
				ref="tableRef"
				:columns="tableProp"
				:table-data="tableData"
				:need-index="true"
				:need-pagination="true"
				:single-select="false"
				:need-selection="false"
				:total="pageTotal"
				@onSelectionChange="onDataSelected"
				@on-current-page-change="onCurrentPageChange"
				:table-loading="tableLoading"
			>
				<template #amount="{ rowData }">
					<cost-tag :value="rowData.amount" />
				</template>
				<template #price="{ rowData }">
					<cost-tag :value="rowData.price" />
				</template>
			</PitayaTable>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
