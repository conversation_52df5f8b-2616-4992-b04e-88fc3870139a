/**
 * 入库管理 - 退库入库 - 入库状态
 * @returns
 */
export function getReturnStatus() {
	return [
		{
			label: "部分入库",
			value: "2",
			raw: { class: "primary" }
		},
		{
			label: "未入库",
			value: "1",
			raw: { class: "warning" }
		}
	]
}

/**
 * 入库管理 - 退库入库 - 物资明细 入库状态
 */
export enum MatReturnStatus {
	"noInStore" = "1", // 未入库
	"partStore" = "2", // 部分入库
	"inStore" = "3" // 全部入库
}
export function getReturnMatStatus() {
	return [
		{
			label: "未入库",
			value: "1",
			raw: { class: "warning" }
		},
		{
			label: "部分入库",
			value: "2",
			raw: { class: "primary" }
		},
		{
			label: "全部入库",
			value: "3",
			raw: { class: "success" }
		}
	]
}
