<script setup lang="ts">
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import { DictApi } from "@/app/baseline/api/dict"
import CostTag from "@/app/baseline/views/components/costTag.vue"
import tableBatchItem from "./tableBatchItem.vue"
import { onMounted, reactive, ref } from "vue"
import { MatReturnStoreItemVO } from "../../../../utils/types/store-warehouse-return"
import { useDictInit } from "../../../components/dictBase"
import { getRealLength, setString } from "@/app/baseline/utils/validate"

const { getDictByCodeList, dictFilter } = useDictInit()
export interface Props {
	id: string | number //
	model?: string //显示模式 view, viewPlan
	row: MatReturnStoreItemVO
}

const props = defineProps<Props>()
const emits = defineEmits(["onSaveOrClose"])

const loading = ref(false)
const formBtnList = [{ name: "取消", icon: ["fas", "circle-minus"] }]

const formModal = reactive<MatReturnStoreItemVO>({
	...props.row
})

console.log(formModal)

const drawerLoading = ref(false)

/**
 * 左侧 title 配置
 */
const drawerLeftTitle = {
	name: ["物资信息"],
	icon: ["fas", "square-share-nodes"]
}

/**
 * 左侧 表单 配置
 */
const formEl = reactive([
	{ label: "物资编码", name: "materialCode" },
	{ label: "物资名称", name: "materialName" },
	{ label: "规格型号", name: "version" },
	{ label: "技术参数", name: "technicalParameter", needTooltip: true },
	{ label: "物资性质", name: "attribute" },
	{ label: "库存单位", name: "useUnit" },
	{ label: "入库仓库名称", name: "storeName" },
	{ label: "已入库数量", name: "inStoredNum_view" },
	{ label: "金额", name: "amount" }
])

/**
 * 左侧 title 配置
 */
const drawerRightTitle = {
	name: ["批次信息"],
	icon: ["fas", "square-share-nodes"]
}

/**
 * 表单按钮 操作
 * @param btnName
 */
const onFormBtnList = (btnName: string | undefined) => {
	if (btnName === "取消") {
		emits("onSaveOrClose", false)
	}
}
onMounted(() => {
	getDictByCodeList(["INVENTORY_UNIT"])
})
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<!-- 左侧表单区域 -->
		<div class="drawer-column left">
			<div class="rows">
				<Title :title="drawerLeftTitle" />
				<el-descriptions size="small" :column="1" border class="content">
					<template>
						<el-descriptions-item
							v-for="(el, index) in formEl"
							label-align="center"
							:label="el.label"
							:key="index"
						>
							<span v-if="el.name === 'useUnit'">
								{{
									dictFilter("INVENTORY_UNIT", formModal[el.name]!)
										?.subitemName || "---"
								}}
							</span>
							<span v-else-if="el.name === 'amount'">
								<cost-tag :value="formModal[el.name]" />
							</span>
							<dict-tag
								v-else-if="el.name === 'attribute'"
								:options="DictApi.getMatAttr()"
								:value="formModal.attribute"
							/>
							<span v-else-if="el.needTooltip">
								<el-tooltip
									effect="dark"
									:content="formModal?.[el.name]"
									:disabled="
										getRealLength(formModal?.[el.name]) <= 100 ? true : false
									"
								>
									{{
										getRealLength(formModal?.[el.name]) > 100
											? setString(formModal?.[el.name], 100)
											: formModal?.[el.name] || "---"
									}}
								</el-tooltip>
							</span>
							<span v-else>
								{{ formModal[el.name] ? formModal[el.name] : "---" }}
							</span>
						</el-descriptions-item>
					</template>
				</el-descriptions>
			</div>
		</div>

		<!-- 右侧table区域 -->
		<div class="drawer-column right">
			<div class="rows">
				<Title :title="drawerRightTitle" />
				<div>
					<tableBatchItem :id="props.id" />
				</div>
			</div>

			<ButtonList
				class="footer"
				:button="formBtnList"
				:loading="loading"
				@on-btn-click="onFormBtnList"
			/>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.drawer-container {
	.left {
		width: 310px;
	}

	.right {
		width: calc(100% - 310px);
	}

	.tab-mat {
		height: 100%;
	}
}
</style>
