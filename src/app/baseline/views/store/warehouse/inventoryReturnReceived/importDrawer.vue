<script setup lang="ts">
import { computed, onMounted, reactive, ref, toRef, toValue } from "vue"
import FormElement from "@/app/baseline/views/components/formElement.vue"
import { ElMessage, type FormInstance, type FormRules } from "element-plus"
import areaStorage from "../receivedStored/areaStorage.vue"

import {
	MatStoreRegionDTO,
	MatStoreRegionTreeVo
} from "../../../../utils/types/store-manage"
import { pick, toNumber } from "lodash-es"
import { MatReturnStoreItemVO } from "../../../../utils/types/store-warehouse-return"
import { editReturnInStore } from "../../../../api/store/warehouse/return"
import { useDictInit } from "../../../components/dictBase"
import { FormElementType } from "../../../components/define"
import {
	getIdempotentToken,
	validateAndCorrectInput
} from "@/app/baseline/utils/validate"
import { useMessageBoxInit } from "../../../components/messageBox"

import {
	IIdempotentTokenType,
	IIdempotentTokenTypePre
} from "@/app/baseline/utils/types/common"

const { getDictByCodeList, dictFilter } = useDictInit()

const { showWarnConfirm } = useMessageBoxInit()

export interface Props {
	applyId: any
	inStoreRow?: MatReturnStoreItemVO[]
}

const props = defineProps<Props>()
const curInstoreRowData = toRef(props, "inStoreRow")

const matReturnTitle = {
	name: ["退库入库"],
	icon: ["fas", "square-share-nodes"]
}
const formBtnList = ref([
	{ name: "取消", icon: ["fas", "circle-minus"] },
	{ name: "确定", icon: ["fas", "check-circle"], disabled: false }
])
const emits = defineEmits(["onSaveOrClose"])
const ruleFormRef = ref<FormInstance>()

const formModelData = reactive<MatReturnStoreItemVO>({
	...curInstoreRowData.value![0],
	completeNum: curInstoreRowData.value![0].thisInStoreNum
})
formModelData.unitLabel = computed(() => {
	return (
		dictFilter("INVENTORY_UNIT", formModelData.useUnit!)?.subitemName || "---"
	)
})
formModelData.attribute_view = computed(() => {
	return (
		dictFilter("MATERIAL_NATURE", formModelData.attribute)?.subitemName || "---"
	)
})

const showStoreAddressDrawer = ref(false) // 入库货位 drawer

/**
 * 表单 配置
 */
const formEl = computed<FormElementType[][]>(() => {
	return [
		[{ label: "物资编码", name: "materialCode", disabled: true }],
		[{ label: "物资名称", name: "materialName", disabled: true }],
		[{ label: "规格型号", name: "version", disabled: true }],
		[
			{
				label: "技术参数",
				name: "technicalParameter",
				rows: 5,
				type: "textarea",
				disabled: true
			}
		],
		[{ label: "物资性质", name: "attribute_view", disabled: true }],
		[{ label: "库存单位", name: "unitLabel", disabled: true }],
		/* [
			{
				label: "采购单价",
				name: "purchasePrice",
				disabled: true,
				type: "number",
				append: "元"
			}
		], */
		[
			{
				label: "待入库数量",
				name: "toInStoreNum",
				type: "number",
				disabled: true,
				append:
					dictFilter("INVENTORY_UNIT", formModelData.useUnit!)?.subitemName ||
					"---"
			}
		],
		[
			{
				label: "本次入库数量",
				name: "completeNum",
				type: "number",
				input: (value: any) => {
					formModelData.completeNum = validateAndCorrectInput(value)
				},
				blur: (event: any) => {
					formModelData.completeNum = toNumber(event.target.value)
				},
				append:
					dictFilter("INVENTORY_UNIT", formModelData.useUnit!)?.subitemName ||
					"---"
			}
		],

		[
			{
				label: "入库仓库",
				name: "storeName",
				disabled: true
			}
		],
		[
			{ label: "入库区域", name: "regionLabel", disabled: true, width: 10 },
			{
				label: "入库货位",
				name: "roomCode",
				type: "drawer",
				disabled: !formModelData.storeId,
				width: 14,
				clickApi: () => {
					showStoreAddressDrawer.value = true
				}
			}
		],
		[
			{
				label: "备注说明",
				name: "remark",
				maxlength: 200,
				rows: 5,
				type: "textarea"
			}
		]
	]
})

/**
 * 校验：本次入库数量不能大于待入库数量
 * @param rule
 * @param value
 * @param callback
 */
const validateStoreNum = (rule: any, value: any, callback: any) => {
	//if (formModelData.toInStoreNum! > 0) {
	if (formModelData.completeNum! > formModelData.toInStoreNum!) {
		return callback(new Error("本次入库数量不能大于待入库数量"))
	} else if (formModelData.completeNum! < 0) {
		return callback(new Error("本次入库数量不能为负数"))
	}
	//}
	callback()
}

/**
 * 表单校验 配置
 */
const rules = reactive<FormRules<typeof formModelData>>({
	completeNum: [
		{ required: true, message: "本次入库数量不能为空", trigger: "change" },
		{ validator: validateStoreNum, required: true, trigger: "change" }
	],
	roomCode: [{ required: true, message: "入库货位不能为空", trigger: "change" }]
})

/**
 * 接收入库 保存 操作
 */
const drawerBtnLoading = ref(false)
const onFormBtnList = (btnName: string | undefined) => {
	if (btnName === "确定") {
		if (ruleFormRef.value) {
			ruleFormRef.value.validate(async (valid) => {
				if (valid) {
					await showWarnConfirm("请确认是否退库入库？")

					drawerBtnLoading.value = true
					const params: { [propName: string]: any } = toValue(formModelData)

					const idempotentToken = getIdempotentToken(
						IIdempotentTokenTypePre.other,
						IIdempotentTokenType.warehouseReturnToWarehouse,
						props.applyId
					)

					editReturnInStore(
						pick(params, "id", "completeNum", "storeId", "roomId", "remark"),
						idempotentToken
					)
						.then(() => {
							ElMessage.success("操作成功")
							emits("onSaveOrClose", "save")
						})
						.finally(() => {
							drawerBtnLoading.value = false
						})
				}
			})
		}
	} else if (btnName === "取消") {
		emits("onSaveOrClose")
	}
}

/**
 * 选择入库货位 确定
 * @param btnName
 * @param treeRow
 * @param row
 */
const saveSelectedArea = (
	btnName: string,
	treeRow?: MatStoreRegionTreeVo,
	row?: MatStoreRegionDTO
) => {
	showStoreAddressDrawer.value = false
	if (btnName == "保存") {
		// 区域
		formModelData.regionLabel = treeRow?.label
		formModelData.regionId = treeRow?.id

		// 货位
		formModelData.roomCode = row?.code // 货位label
		formModelData.roomId = row?.id // 货位ID
	}
}
onMounted(() => {
	getDictByCodeList(["INVENTORY_UNIT", "MATERIAL_NATURE"])
})
</script>
<template>
	<div class="drawer-container">
		<div class="drawer-column">
			<div class="rows">
				<Title :title="matReturnTitle" />
				<el-scrollbar>
					<el-form
						style="padding-bottom: 30px"
						class="content form-base"
						ref="ruleFormRef"
						:model="formModelData"
						:rules="rules"
						label-position="top"
					>
						<FormElement :form-element="formEl" :form-data="formModelData" />
					</el-form>
				</el-scrollbar>
			</div>
			<ButtonList
				class="footer"
				:button="formBtnList"
				:loading="drawerBtnLoading"
				@onBtnClick="onFormBtnList"
			/>

			<!-- 选择入库货位 -->
			<Drawer
				:size="900"
				v-model:drawer="showStoreAddressDrawer"
				:destroyOnClose="true"
			>
				<area-storage
					:storeId="formModelData.storeId!"
					:storeName="formModelData.storeName!"
					:storeCode="formModelData.storeCode"
					@onSaveOrClose="saveSelectedArea"
					:region-id="formModelData.regionId"
					:room-id="[formModelData.roomId]"
				/>
			</Drawer>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.drawer-column {
	width: 100%;
}
</style>
