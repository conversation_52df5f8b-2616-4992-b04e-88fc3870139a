<script setup lang="ts">
import { FormElementType } from "@/app/baseline/views/components/define"
import { reactive, ref } from "vue"
import { DictApi } from "@/app/baseline/api/dict"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import LinkTag from "@/app/baseline/views/components/linkTag.vue"
import {
	MatReturnStoreItemRequestParams,
	MatReturnStoreItemVO,
	MatReturnStoreVO
} from "../../../../utils/types/store-warehouse-return"
import {
	getReturnById,
	getReturnDetail,
	updateReturnInStoreBatch
} from "@/app/baseline/api/store/warehouse/return"
import {
	IIdempotentTokenType,
	IIdempotentTokenTypePre,
	IModalType
} from "@/app/baseline/utils/types/common"
import { batchFormatterNumView, tableColFilter } from "@/app/baseline/utils"
import { useTbInit } from "../../../components/tableBase"
import { getReturnMatStatus, MatReturnStatus } from "./inventoryReturnReceived"
import { useDictInit } from "../../../components/dictBase"
import { findIndex, first, includes, map, toNumber, round } from "lodash-es"
import { modalSize } from "@/app/baseline/utils/layout-config"
import importDrawer from "./importDrawer.vue"
import roomDrawer from "./roomDrawer.vue"
import batchNoDrawer from "./batchNoDrawer.vue"
import {
	MatStoreRegionDTO,
	MatStoreRegionTreeVo
} from "@/app/baseline/utils/types/store-manage"
import { useMessageBoxInit } from "../../../components/messageBox"
import {
	getIdempotentToken,
	validateAndCorrectInput
} from "@/app/baseline/utils/validate"
import areaStorage from "../receivedStored/areaStorage.vue"

export interface Props {
	id: string | number //
	model?: IModalType //显示模式 view, viewPlan
}

const props = withDefaults(defineProps<Props>(), {
	id: "",
	model: IModalType.view
})
const { dictOptions, getDictByCodeList, dictFilter } = useDictInit()
const { showWarnConfirm } = useMessageBoxInit()

const emits = defineEmits(["update", "close"])
const loading = ref(false)
const formBtnList = [{ name: "取消", icon: ["fas", "circle-minus"] }]

const formModal = ref<MatReturnStoreVO>({})
const drawerLoading = ref(false)

/**
 * 左侧 Title 配置
 */
const drawerLeftTitle = {
	name: ["退库入库单"],
	icon: ["fas", "square-share-nodes"]
}

/**
 * 左侧 表单 配置
 */
const formEl = reactive<FormElementType[]>([
	{
		label: "入库单号",
		name: "code"
	},
	{
		label: "关联退库单号",
		name: "preBusinessCode"
	},
	{ label: "领料用途", name: "purpose_view" },
	{
		label: "入库仓库名称",
		name: "storeName"
	},
	{
		label: "入库物资编码",
		name: "matCount"
	},
	{
		label: "操作人",
		name: "userName_view"
	},
	{
		label: "申请时间",
		name: "createdDate"
	}
])

/**
 * 右侧 title 配置
 */
const drawerRightTitle = {
	name: ["物资明细"],
	icon: ["fas", "square-share-nodes"]
}

/**
 * 取消 操作
 * @param btnName
 */
const onFormBtnList = (btnName: string | undefined) => {
	if (btnName === "取消") {
		emits("close")
		return
	}
}

/* const fetchTableData = () => {
	// 更新主列表
	emits("update")
	// 更新左侧详情
	getDetail()
} */

/**
 * 详情
 */
const getDetail = () => {
	if (!props.id) {
		return false
	}
	drawerLoading.value = true
	getReturnById(props.id as number)
		.then((res: any) => {
			formModal.value = res
		})
		.finally(() => {
			drawerLoading.value = false
		})
}
onMounted(() => {
	getDictByCodeList(["INVENTORY_UNIT", "MATERIAL_NATURE"])
	getDetail()
	getTableData()
})

/**
 * 查询条件 配置
 */
const queryArrList = computed(() => [
	{
		name: "入库状态",
		key: "status",
		placeholder: "请选择入库状态",
		type: "select",
		children: getReturnMatStatus()
	},
	{
		name: "物资编码",
		key: "materialCode",
		placeholder: "请输入物资编码",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资名称",
		key: "materialLabel",
		placeholder: "请输入物资名称",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "规格型号",
		key: "version",
		placeholder: "请输入规格型号",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资性质",
		key: "attribute",
		type: "select",
		children: dictOptions.value.MATERIAL_NATURE,
		placeholder: "请选择物资性质"
	}
])

// 入库抽屉显隐
const showReturnDrawer = ref(false)
const showStoreAddressVisible = ref(false)

/**
 * 编辑后的table row 数据
 */
const editedTableRowStack = ref<any[]>([])

const tbInit = useTbInit<
	MatReturnStoreItemVO,
	MatReturnStoreItemRequestParams
>()
const {
	tableCache,
	tableLoading,
	tableData,
	tableRef,
	currentPage,
	pageTotal,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = tbInit

/**
 * table 列配置
 */
tbInit.tableProp = computed(() => {
	const defCols: TableColumnType[] = [
		{ label: "物资编码", prop: "materialCode", minWidth: 130, fixed: "left" },
		{ label: "物资名称", prop: "materialName", minWidth: 150 },
		{ label: "规格型号", prop: "version", minWidth: 100 },
		{
			label: "技术参数",
			prop: "technicalParameter",
			minWidth: 200
		},
		{ label: "物资性质", prop: "attribute", needSlot: true, width: 120 },
		{ label: "库存单位", prop: "useUnit", needSlot: true, width: 90 },
		{
			label: "入库状态",
			prop: "status",
			needSlot: true,
			minWidth: 100,
			fixed: "right"
		}, // 1:未入库 2:已入库

		{
			label: "可入库数量",
			prop: "inStoreNum_view",
			minWidth: 100,
			align: "right",
			fixed: "right"
		},
		{
			label: "已入库数量",
			prop: "inStoredNum_view",
			minWidth: 100,
			align: "right",
			fixed: "right"
		},
		{
			label: "待入库数量",
			prop: "toInStoreNum_view",
			align: "right",
			minWidth: 100,
			fixed: "right"
		},
		{
			label: "本次入库数量",
			prop: "thisInStoreNum",
			width: 120,
			needSlot: true,
			align: "right",
			fixed: "right"
		},
		{
			label: "入库批次",
			prop: "batchNo",
			minWidth: 100,
			needSlot: true,
			fixed: "right"
		},
		{
			label: "入库货位",
			prop: "inStoreRoomTimes",
			needSlot: true,
			width: 100,
			fixed: "right"
		}
	]

	if (props.model === IModalType.edit) {
		return tableColFilter(defCols, ["入库批次"])
	} else if (formModal.value.status === MatReturnStatus.inStore) {
		if (props.model == IModalType.view) {
			return defCols.filter((v) => v.label !== "本次入库数量")
		} else {
			return defCols
		}
	} else {
		if (props.model == IModalType.view) {
			return tableColFilter(defCols, ["入库货位", "本次入库数量"])
		} else {
			return tableColFilter(defCols, ["入库货位"])
		}
	}
})

/**
 * 格式化数量
 */
watch(tableData, () => {
	batchFormatterNumView(tableData.value as any[])
})

/**
 * 物资明细 table 行样式
 *
 * 库存不足物资，用红色标记该行
 */
const errorGoodsIdList = ref<number[]>([])
function tbCellClassName({ row }: any) {
	return includes(errorGoodsIdList.value, row.id) ? "error" : ""
}

async function handleSaveRoom(
	btnName: string,
	treeRow?: MatStoreRegionTreeVo,
	row?: MatStoreRegionDTO,
	callback?: any
) {
	if (btnName == "保存") {
		try {
			await showWarnConfirm("确定将所选物资退库入库吗？")
			const itemList = map(selectedTableList.value, (v) => ({
				id: v.id,
				completeNum: v.thisInStoreNum
			}))

			const idempotentToken = getIdempotentToken(
				IIdempotentTokenTypePre.other,
				IIdempotentTokenType.warehouseReturnToWarehouse,
				formModal.value.id
			)

			const { code, msg, data } = await updateReturnInStoreBatch(
				{
					roomId: row?.id,
					itemList
				},
				idempotentToken
			)

			if (data && code != 200) {
				errorGoodsIdList.value = data || []
				ElMessage.error(msg)
				getTableData()
			} else {
				ElMessage.success("操作成功")
				getTableData()
				getDetail()
				emits("update")

				for (let i = 0; i < selectedTableList.value.length; i++) {
					//  更新 tableCache
					const rowIdx = findIndex(
						tableCache,
						(v) => v.id === selectedTableList.value[i].id
					)
					if (rowIdx !== -1) {
						tableCache.splice(rowIdx, 1, { ...selectedTableList.value[i] })
					}

					const delRowIdx = findIndex(
						editedTableRowStack.value,
						(v) => v.id === selectedTableList.value[i].id
					)
					if (rowIdx !== -1) {
						editedTableRowStack.value.splice(delRowIdx, 1)
					}
				}
			}
		} finally {
			callback?.()
		}
	}

	showStoreAddressVisible.value = false
}

/**
 * tab切换 table 数据源
 * @param data
 */
const getTableData = (data?: Record<string, any>) => {
	if (props.id) {
		fetchFunc.value = getReturnDetail

		currentPage.value = 1
		tableRef.value?.resetCurrentPage()

		fetchParam.value = {
			applyId: props.id,
			sord: "desc",
			sidx: "createdDate",
			...data
		}
		fetchTableData()
	}
}

/**
 * 入库按钮 配置
 */
const tableFooterBtns = computed(() => {
	const isStatusClick = selectedTableList.value.every((e) => {
		return e.status != MatReturnStatus.inStore
	})
	return [
		{
			name: "入库",
			icon: ["fas", "arrow-down"],
			disabled:
				selectedTableList.value.length == 1 &&
				first(selectedTableList.value)?.status != MatReturnStatus.inStore
					? false
					: true
		},
		{
			name: "批量入库",
			icon: ["fas", "arrow-down"],
			disabled:
				selectedTableList.value.length > 0 && isStatusClick ? false : true
		}
	]
})

/**
 * 入库 操作
 */
const handleTabFooterAction = (btnName?: string) => {
	if (btnName === "入库") {
		showReturnDrawer.value = true
	} else if (btnName === "批量入库") {
		showStoreAddressVisible.value = true
	}
}

/**
 * 关联货位 操作
 */
const showRoomDrawer = ref(false)
const currentRow = ref<MatReturnStoreItemVO>({})
const onRowRoom = (row: MatReturnStoreItemVO) => {
	if (!row.inStoreRoomTimes) {
		return false
	}

	currentRow.value = row
	showRoomDrawer.value = true
}

// 关联批次号
const showBatchRoomDrawer = ref(false)
const currentBatchRow = ref<MatReturnStoreItemVO>({})
const onBatchRoom = (row: MatReturnStoreItemVO) => {
	if (!row.batchNo) {
		return false
	}
	currentBatchRow.value = row
	showBatchRoomDrawer.value = true
}

const closeBatchRoomDrawer = () => {
	currentBatchRow.value = {}
	showBatchRoomDrawer.value = false
}

const closeInstoreDrawer = (msg?: string) => {
	// 更新
	if (msg === "save") {
		fetchTableData()
		emits("update")
		getDetail()

		for (let i = 0; i < selectedTableList.value.length; i++) {
			//  更新 tableCache
			const rowIdx = findIndex(
				tableCache,
				(v) => v.id === selectedTableList.value[i].id
			)
			if (rowIdx !== -1) {
				tableCache.splice(rowIdx, 1, { ...selectedTableList.value[i] })
			}

			const delRowIdx = findIndex(
				editedTableRowStack.value,
				(v) => v.id === selectedTableList.value[i].id
			)
			if (rowIdx !== -1) {
				editedTableRowStack.value.splice(delRowIdx, 1)
			}
		}
	}
	showReturnDrawer.value = false
}
/**
 * 输入框失焦 handler
 *
 * 失焦后，记录编辑过的数据，用于保存任务数据
 */
function handleInputBlur(e: any) {
	if (!editedTableRowStack.value.length) {
		editedTableRowStack.value.push({ ...e })
		return
	}

	/**
	 * 队列存在数据，验证重复
	 *
	 * - 不重复->push
	 * - 重复->更新该条数据
	 */
	const rowIdx = findIndex(editedTableRowStack.value, (v) => v.id === e.id)
	if (rowIdx !== -1) {
		editedTableRowStack.value.splice(rowIdx, 1, { ...e })
		return
	}

	editedTableRowStack.value.push({ ...e })
}

/**
 * 翻页 回显已编辑的数据
 */
watch(
	() => tableData.value,
	() => {
		if (editedTableRowStack.value.length) {
			editedTableRowStack.value.map((e) => {
				const rowIdx = findIndex(tableData.value, (v) => v.id === e.id)
				if (rowIdx !== -1) {
					tableData.value.splice(rowIdx, 1, { ...e })
				}
			})
		}
	}
)

function validateInStoreNum(e: any) {
	const thisInStoreNum = toNumber(e.thisInStoreNum)

	// 最大本次入库数量
	const toInStoreNum = toNumber(e.toInStoreNum)

	e.thisInStoreNum = thisInStoreNum
	if (toInStoreNum < thisInStoreNum) {
		ElMessage.warning("本次入库数量不能大于待入库数量")
		e.thisInStoreNum = round(toInStoreNum, 4)
	} else if (thisInStoreNum <= 0) {
		const oldRow = tableCache.find((v) => v.id == e.id)
		e.thisInStoreNum = oldRow.thisInStoreNum

		ElMessage.warning("本次入库数量不能小于等于0！")
	}

	handleInputBlur(e)
}
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<!-- 左侧表单区域 -->
		<div class="drawer-column left">
			<div class="rows">
				<Title :title="drawerLeftTitle" />
				<el-descriptions size="small" :column="1" border class="content">
					<template>
						<el-descriptions-item
							v-for="(el, index) in formEl"
							label-align="center"
							:label="el.label"
							:key="index"
						>
							<span>
								{{ formModal[el.name] ? formModal[el.name] : "---" }}
							</span>
						</el-descriptions-item>
					</template>
				</el-descriptions>
			</div>
		</div>

		<!-- 右侧table区域 -->
		<div class="drawer-column right">
			<Title :title="drawerRightTitle" />
			<div class="rows">
				<el-scrollbar class="tab-mat editor-table-wrapper">
					<Query
						:queryArrList="queryArrList"
						@getQueryData="getTableData"
						class="custom-q"
					/>

					<PitayaTable
						ref="tableRef"
						:columns="(tbInit.tableProp as any)"
						:table-data="tableData"
						:need-index="true"
						:need-pagination="true"
						:single-select="props.model === IModalType.edit ? false : false"
						:need-selection="props.model === IModalType.view ? false : true"
						:total="pageTotal"
						@onSelectionChange="selectedTableList = $event"
						@on-current-page-change="onCurrentPageChange"
						:table-loading="tableLoading"
						:cell-class-name="tbCellClassName"
					>
						<!-- 物资性质 -->
						<template #attribute="{ rowData }">
							<dict-tag
								:options="DictApi.getMatAttr()"
								:value="rowData.attribute"
							/>
						</template>

						<!-- 库存状态 -->
						<template #useUnit="{ rowData }">
							{{
								dictFilter("INVENTORY_UNIT", rowData.useUnit)?.subitemName ||
								"---"
							}}
						</template>
						<!-- 入库状态 -->
						<template #status="{ rowData }">
							<dict-tag
								:options="getReturnMatStatus()"
								:value="rowData.status"
							/>
						</template>

						<!-- 入库货位 -->
						<template #inStoreRoomTimes="{ rowData }">
							<link-tag
								:value="rowData.inStoreRoomTimes"
								@click.stop="onRowRoom(rowData)"
							/>
						</template>
						<!-- 入库批次 -->
						<template #batchNo="{ rowData }">
							<link-tag
								:value="rowData.batchNo"
								@click.stop="onBatchRoom(rowData)"
							/>
						</template>

						<template #thisInStoreNum="{ rowData }">
							<el-input
								class="no-arrows"
								v-model="rowData.thisInStoreNum"
								@click.stop
								@input="
									rowData.thisInStoreNum = validateAndCorrectInput($event)
								"
								@change="validateInStoreNum(rowData)"
								v-if="rowData.status != MatReturnStatus.inStore"
							/>
							<span v-else>{{ rowData.thisInStoreNum_view }}</span>
						</template>

						<template #footerOperateLeft v-if="props.model == IModalType.edit">
							<ButtonList
								class="btn-list"
								:is-not-radius="true"
								:button="tableFooterBtns"
								@on-btn-click="handleTabFooterAction"
							/>
						</template>
					</PitayaTable>
				</el-scrollbar>
			</div>

			<ButtonList
				class="footer"
				:button="formBtnList"
				:loading="loading"
				@on-btn-click="onFormBtnList"
			/>

			<!-- 接收入库 -->
			<Drawer
				:size="modalSize.sm"
				v-model:drawer="showReturnDrawer"
				:destroyOnClose="true"
			>
				<import-drawer
					:applyId="props.id"
					:inStoreRow="selectedTableList"
					@on-save-or-close="closeInstoreDrawer"
				/>
			</Drawer>

			<!-- 查看货位 -->
			<Drawer
				:size="modalSize.lg"
				v-model:drawer="showRoomDrawer"
				:destroyOnClose="true"
			>
				<room-drawer
					:id="currentRow.id!"
					:row="currentRow"
					@on-save-or-close="showRoomDrawer = false"
				/>
			</Drawer>

			<!-- 查看货位 -->
			<Drawer
				:size="modalSize.lg"
				v-model:drawer="showBatchRoomDrawer"
				:destroyOnClose="true"
			>
				<batchNoDrawer
					:id="currentBatchRow.id!"
					:row="currentBatchRow"
					@on-save-or-close="closeBatchRoomDrawer"
				/>
			</Drawer>

			<!-- 选择入库货位 -->
			<Drawer
				:size="modalSize.lg"
				v-model:drawer="showStoreAddressVisible"
				:destroyOnClose="true"
			>
				<area-storage
					:storeId="formModal.storeId!"
					:storeName="formModal.storeName!"
					:storeCode="formModal?.storeCode"
					:regionId="first(selectedTableList)?.regionId"
					:room-id="[first(selectedTableList)?.roomId]"
					@onSaveOrClose="handleSaveRoom"
				/>
			</Drawer>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.custom-q {
	margin: 10px 0px -10px 10px !important;
}

.drawer-container {
	.left {
		width: 310px;
	}

	.right {
		width: calc(100% - 310px);
	}

	.tab-mat {
		height: 100%;
	}
}

.editor-table-wrapper {
	&:deep(.pitaya-table) {
		.error {
			.column-inner-item,
			.cell,
			.el-input__inner {
				color: red;
			}
		}
	}
}
</style>
