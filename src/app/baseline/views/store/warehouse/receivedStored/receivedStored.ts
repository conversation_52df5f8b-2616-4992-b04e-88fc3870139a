/**
 * 入库管理 - 接收入库 - 入库状态
 * @returns
 */
export function getInStoreStatus() {
	return [
		{
			label: "部分入库",
			value: "2",
			raw: { class: "primary" }
		},
		{
			label: "未入库",
			value: "1",
			raw: { class: "warning" }
		},
		{
			label: "已入库",
			value: "3",
			raw: { class: "success" }
		}
	]
}

/**
 * 入库管理 - 接收入库 - 物资明细 入库状态
 */
export enum MatInStoreStatus {
	"halfInStore" = 2, // 已入库
	"noInStore" = 1, // 未入库
	"allInStore" = 3
}
export function getInStoreMatStatus() {
	return [
		{
			label: "部分入库",
			value: "2",
			raw: { class: "primary" }
		},
		{
			label: "未入库",
			value: "1",
			raw: { class: "warning" }
		},
		{
			label: "已入库",
			value: "3",
			raw: { class: "success" }
		}
	]
}
