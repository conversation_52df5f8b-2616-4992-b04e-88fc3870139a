<script setup lang="ts">
import { DictApi, fileBusinessType } from "@/app/baseline/api/dict"
import { FormElementType } from "@/app/baseline/views/components/define"
import { ref } from "vue"
import {
	getInStoreMatStatus,
	getInStoreStatus,
	MatInStoreStatus
} from "./receivedStored"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import CostTag from "@/app/baseline/views/components/costTag.vue"
import LinkTag from "@/app/baseline/views/components/linkTag.vue"
import {
	MatInStoreReceiveDetailRequestParams,
	MatInStoreReceiveDetailVO,
	MatInStoreReceiveVO
} from "../../../../utils/types/store-warehouse-receive"
import {
	getReceiveById,
	getReceiveDetail,
	updateReceiveInStoreBatch
} from "@/app/baseline/api/store/warehouse/receive"
import { useTbInit } from "../../../components/tableBase"
import { useDictInit } from "../../../components/dictBase"
import { modalSize } from "@/app/baseline/utils/layout-config"
import importDrawer from "./importDrawer.vue"
import batchDrawer from "./batchDrawer.vue"
import { batchFormatterNumView, tableColFilter } from "@/app/baseline/utils"
import { findIndex, includes, map, round, toNumber, first } from "lodash-es"
import { useMessageBoxInit } from "../../../components/messageBox"
import {
	MatStoreRegionDTO,
	MatStoreRegionTreeVo
} from "@/app/baseline/utils/types/store-manage"
import areaStorage from "./areaStorage.vue"
import {
	getIdempotentToken,
	validateAndCorrectInput
} from "@/app/baseline/utils/validate"
import {
	IIdempotentTokenType,
	IIdempotentTokenTypePre,
	IModalType
} from "@/app/baseline/utils/types/common"

import TableFile from "@/app/baseline/views/components/tableFile.vue"

export interface Props {
	id: string | number //
	model?: string //显示模式 view, edit
}
const { dictOptions, getDictByCodeList, dictFilter } = useDictInit()

const { showWarnConfirm } = useMessageBoxInit()

const props = defineProps<Props>()
const emits = defineEmits(["update", "close"])
const loading = ref(false)
const formBtnList = [{ name: "取消", icon: ["fas", "circle-minus"] }]

const formModal = ref<MatInStoreReceiveVO>({})
const drawerLoading = ref(false)

/**
 * 左侧 Title 配置
 */
const drawerLeftTitle = {
	name: ["入库单"],
	icon: ["fas", "square-share-nodes"]
}

/**
 * 左侧 表单 配置
 */
const formEl = computed<FormElementType[]>(() => {
	const ls = [
		{ label: "入库单号", name: "code" },
		{ label: "关联业务单号", name: "preBusinessCode" },
		{ label: "入库类型", name: "prePreBusinessType_view" },
		{ label: "入库仓库名称", name: "storeName" },
		{ label: "入库金额", name: "inStoreMoney" },
		{ label: "入库物资编码", name: "matCount" },
		{ label: "待入库物资编码", name: "notInspectedCount" },
		{ label: "已入库物资编码", name: "inspectedCount" },
		{ label: "入库申请人", name: "userName_view" },
		{ label: "申请时间", name: "createdDate" },
		{ label: "入库完成时间", name: "applyFinishDate" },
		{
			label: "入库状态",
			name: "status" //  枚举值 1:未入库 2：部分入库
		}
	]

	if (formModal.value.status == "3") {
		// 已入库
		return ls.filter(
			(i) =>
				!["createdDate", "notInspectedCount", "inspectedCount"].includes(i.name)
		)
	} else {
		return ls.filter((i) => i.name !== "applyFinishDate")
	}
})

/**
 * 右侧 title 配置
 */
const drawerRightTitle = {
	name: ["物资明细"],
	icon: ["fas", "square-share-nodes"]
}

/**
 * 扩展栏标签页切换
 */
const tabList = ["物资明细", "相关附件"]
const activeTab = ref<number>(0)
const handleTabChange = (index: number) => {
	activeTab.value = index

	if (index === 0) {
		fetchParam.value = {
			applyId: props.id,
			//inspectUsername: formModal.value.inspectUsername,
			sord: "desc",
			sidx: "createdDate"
		}
		pageSize.value = 20
		getTableData()
	}
}

/**
 * 取消 操作
 * @param btnName
 */
const onFormBtnList = (btnName: string | undefined) => {
	if (btnName === "取消") {
		emits("close")
		return
	}
}

/**
 * 详情
 */
const getDetail = () => {
	if (!props.id) {
		return false
	}
	drawerLoading.value = true
	getReceiveById(props.id as number)
		.then((res: any) => {
			formModal.value = res
		})
		.finally(() => {
			drawerLoading.value = false
		})
}

onMounted(() => {
	getDictByCodeList(["INVENTORY_UNIT", "MATERIAL_NATURE"])
	getDetail()
	getTableData()
})

/**
 * 查询条件 配置
 */
const queryArrList = computed(() => [
	{
		name: "入库状态",
		key: "status",
		placeholder: "请选择入库状态",
		type: "select",
		children: getInStoreMatStatus()
	},
	{
		name: "物资编码",
		key: "materialCode",
		placeholder: "请输入物资编码",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资名称",
		key: "materialLabel",
		placeholder: "请输入物资名称",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "规格型号",
		key: "version",
		placeholder: "请输入规格型号",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资性质",
		key: "attribute",
		type: "select",
		children: dictOptions.value.MATERIAL_NATURE,
		placeholder: "请选择物资性质"
	}
])

// 入库抽屉显隐
const showQualityVisible = ref(false)

const showStoreAddressVisible = ref(false)

/**
 * 编辑后的table row 数据
 */
const editedTableRowStack = ref<any[]>([])

const tbInit = useTbInit<
	MatInStoreReceiveDetailVO,
	MatInStoreReceiveDetailRequestParams
>()

const {
	pageSize,
	tableCache,
	tableData,
	tableRef,
	currentPage,
	pageTotal,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange,
	tableLoading
} = tbInit

/**
 * table 列配置
 */
tbInit.tableProp = computed(() => {
	const defColumn: TableColumnType[] = [
		{ label: "物资编码", prop: "materialCode", width: 130, fixed: "left" },
		{ label: "物资名称", prop: "materialName", minWidth: 150 },
		{ label: "规格型号", prop: "version", width: 130 },
		{
			label: "技术参数",
			prop: "technicalParameter",
			minWidth: 150
		},
		{ label: "物资性质", prop: "attribute", needSlot: true, width: 120 },
		{ label: "库存单位", prop: "buyUnit", needSlot: true, width: 80 },
		{
			label: "采购单价",
			prop: "price",
			needSlot: true,
			align: "right",
			width: 120
		},
		{
			label: "入库金额",
			prop: "amount",
			needSlot: true,
			align: "right",
			minWidth: 120
		},
		{
			label: "入库状态",
			prop: "status",
			needSlot: true,
			width: 100,
			fixed: "right"
		}, // 1:未入库 2:已入库

		{
			label: "可入库数量",
			prop: "inStoreNum_view",
			width: 100,
			align: "right",
			fixed: "right"
		},
		{
			label: "待入库数量",
			prop: "toInStoreNum_view",
			width: 100,
			align: "right",
			fixed: "right"
		},
		{
			label: "已入库数量",
			prop: "inStoredNum_view",
			width: 100,
			align: "right",
			fixed: "right"
		},
		{
			label: "本次入库数量",
			prop: "thisInStoreNum",
			width: 120,
			needSlot: true,
			align: "right",
			fixed: "right"
		},
		{
			label: "已入库批次",
			prop: "inStoreBatchTimes",
			needSlot: true,
			width: 90,
			fixed: "right"
		}
	]

	if (props.model === IModalType.edit) {
		return defColumn
	} else {
		return tableColFilter(defColumn, ["本次入库数量"])
	}
})

/**
 * 格式化数量
 */
watch(tableData, () => {
	batchFormatterNumView(tableData.value as any[])
})

/**
 * 入库按钮 配置
 */
const tableFooterBtns = computed(() => {
	const isStatusClick = selectedTableList.value.every((e) => {
		return e.status != MatInStoreStatus.allInStore
	})

	return [
		{
			name: "入库",
			icon: ["fas", "arrow-down"],
			disabled:
				selectedTableList.value.length == 1 &&
				first(selectedTableList.value)?.status != MatInStoreStatus.allInStore
					? false
					: true
		},
		{
			name: "批量入库",
			icon: ["fas", "arrow-down"],
			disabled:
				selectedTableList.value.length > 0 && isStatusClick ? false : true
		}
	]
})

/**
 * 入库 操作
 */

function handleTabFooterAction(btnName?: string) {
	if (btnName === "入库") {
		showQualityVisible.value = true
	} else if (btnName === "批量入库") {
		/* const canNum = selectedTableList.value.every((e) => {
			return e.thisInStoreNum > 0
		}) */

		showStoreAddressVisible.value = true
	}
}

/**
 * 物资明细 table 行样式
 *
 * 库存不足物资，用红色标记该行
 */
const errorGoodsIdList = ref<number[]>([])
function tbCellClassName({ row }: any) {
	return includes(errorGoodsIdList.value, row.id) ? "error" : ""
}

async function handleSaveRoom(
	btnName: string,
	treeRow?: MatStoreRegionTreeVo,
	row?: MatStoreRegionDTO,
	callback?: any
) {
	if (btnName == "保存") {
		try {
			await showWarnConfirm(
				"对于批量入库的物资，系统将自动计算并更新其质保有效日期，确定将所选物资接收入库吗？"
			)
			const itemList = map(selectedTableList.value, (v) => ({
				id: v.id,
				completeNum: v.thisInStoreNum
			}))

			const idempotentToken = getIdempotentToken(
				IIdempotentTokenTypePre.other,
				IIdempotentTokenType.receiveToWarehouse,
				formModal.value.id
			)
			const { code, msg, data } = await updateReceiveInStoreBatch(
				{
					roomId: row?.id,
					itemList
				},
				idempotentToken
			)

			if (data && code != 200) {
				errorGoodsIdList.value = data || []
				ElMessage.error(msg)
				getTableData()
			} else {
				ElMessage.success("操作成功")
				getTableData()
				getDetail()
				emits("update")

				for (let i = 0; i < selectedTableList.value.length; i++) {
					//  更新 tableCache
					const rowIdx = findIndex(
						tableCache,
						(v) => v.id === selectedTableList.value[i].id
					)
					if (rowIdx !== -1) {
						tableCache.splice(rowIdx, 1, { ...selectedTableList.value[i] })
					}

					const delRowIdx = findIndex(
						editedTableRowStack.value,
						(v) => v.id === selectedTableList.value[i].id
					)
					if (rowIdx !== -1) {
						editedTableRowStack.value.splice(delRowIdx, 1)
					}
				}
			}
		} finally {
			callback?.()
		}
	}
	showStoreAddressVisible.value = false
}

function handleInStoreAction() {
	fetchTableData()
	getDetail()
	emits("update")

	for (let i = 0; i < selectedTableList.value.length; i++) {
		//  更新 tableCache
		const rowIdx = findIndex(
			tableCache,
			(v) => v.id === selectedTableList.value[i].id
		)
		if (rowIdx !== -1) {
			tableCache.splice(rowIdx, 1, { ...selectedTableList.value[i] })
		}

		const delRowIdx = findIndex(
			editedTableRowStack.value,
			(v) => v.id === selectedTableList.value[i].id
		)
		if (rowIdx !== -1) {
			editedTableRowStack.value.splice(delRowIdx, 1)
		}
	}
}

/**
 * 输入框失焦 handler
 *
 * 失焦后，记录编辑过的数据，用于保存任务数据
 */
function handleInputBlur(e: any) {
	if (!editedTableRowStack.value.length) {
		editedTableRowStack.value.push({ ...e })
		return
	}

	/**
	 * 队列存在数据，验证重复
	 *
	 * - 不重复->push
	 * - 重复->更新该条数据
	 */
	const rowIdx = findIndex(editedTableRowStack.value, (v) => v.id === e.id)
	if (rowIdx !== -1) {
		editedTableRowStack.value.splice(rowIdx, 1, { ...e })
		return
	}

	editedTableRowStack.value.push({ ...e })
}

/**
 * 翻页 回显已编辑的数据
 */
watch(
	() => tableData.value,
	() => {
		if (editedTableRowStack.value.length) {
			editedTableRowStack.value.map((e) => {
				const rowIdx = findIndex(tableData.value, (v) => v.id === e.id)
				if (rowIdx !== -1) {
					tableData.value.splice(rowIdx, 1, { ...e })
				}
			})
		}
	}
)

function validateInStoreNum(e: any) {
	const thisInStoreNum = toNumber(e.thisInStoreNum)

	// 最大本次入库数量
	const toInStoreNum = toNumber(e.toInStoreNum)

	e.thisInStoreNum = thisInStoreNum
	if (toInStoreNum < thisInStoreNum) {
		ElMessage.warning("本次入库数量不能大于待入库数量")
		e.thisInStoreNum = round(toInStoreNum, 4)
	} else if (thisInStoreNum <= 0) {
		const oldRow = tableCache.find((v) => v.id == e.id)
		e.thisInStoreNum = oldRow.thisInStoreNum

		ElMessage.warning("本次入库数量不能小于等于0！")
	}

	handleInputBlur(e)
}

/**
 * tab切换 table数据源
 * @param data
 */
const getTableData = (data?: MatInStoreReceiveDetailRequestParams) => {
	if (props.id) {
		fetchFunc.value = getReceiveDetail

		currentPage.value = 1
		tableRef.value?.resetCurrentPage()

		fetchParam.value = {
			applyId: props.id,
			sord: "desc",
			sidx: "createdDate",
			...data
		}
		fetchTableData()
	}
}

/**
 * 关联批次 操作
 */
const showBatchVisible = ref(false)
const currentRow = ref<MatInStoreReceiveDetailVO>({})
const onRowBatch = (row: MatInStoreReceiveDetailVO) => {
	if (!row.inStoreBatchTimes) {
		return false
	}
	currentRow.value = row
	showBatchVisible.value = true
}
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<!-- 左侧表单区域 -->
		<div class="drawer-column left">
			<div class="rows">
				<Title :title="drawerLeftTitle" />
				<el-descriptions size="small" :column="1" border class="content">
					<template>
						<el-descriptions-item
							v-for="(el, index) in formEl"
							label-align="center"
							:label="el.label"
							:key="index"
						>
							<!-- 入库状态 -->
							<span v-if="el.name == 'status'">
								<dict-tag
									:value="formModal[el.name]!"
									:options="getInStoreStatus()"
								/>
							</span>
							<!-- 入库金额 -->
							<span v-else-if="el.name == 'inStoreMoney'">
								<cost-tag :value="formModal[el.name]" />
							</span>
							<span v-else>
								{{
									formModal[el.name] || formModal[el.name] == 0
										? formModal[el.name]
										: "---"
								}}
							</span>
						</el-descriptions-item>
					</template>
				</el-descriptions>
			</div>
		</div>

		<!-- 右侧table区域 -->
		<div class="drawer-column right">
			<div class="rows">
				<Title :title="drawerRightTitle">
					<Tabs class="tabs" :tabs="tabList" @on-tab-change="handleTabChange" />
				</Title>
				<div v-if="activeTab === 0">
					<el-scrollbar class="tab-mat editor-table-wrapper">
						<Query
							:queryArrList="queryArrList"
							@getQueryData="getTableData"
							class="custom-q"
						/>

						<PitayaTable
							ref="tableRef"
							:columns="(tbInit.tableProp as any)"
							:table-data="tableData"
							:need-index="true"
							:need-pagination="true"
							:single-select="props.model === 'edit' ? false : false"
							:need-selection="props.model === 'edit' ? true : false"
							:total="pageTotal"
							@onSelectionChange="selectedTableList = $event"
							@on-current-page-change="onCurrentPageChange"
							:table-loading="tableLoading"
							:cell-class-name="tbCellClassName"
						>
							<!-- 物资性质 -->
							<template #attribute="{ rowData }">
								<dict-tag
									:options="DictApi.getMatAttr()"
									:value="rowData.attribute"
								/>
							</template>

							<template #buyUnit="{ rowData }">
								{{
									dictFilter("INVENTORY_UNIT", rowData.buyUnit)?.subitemName ||
									"---"
								}}
							</template>
							<!-- 采购单价 -->
							<template #price="{ rowData }">
								<cost-tag :value="rowData.price" />
							</template>
							<!-- 入库金额 -->
							<template #amount="{ rowData }">
								<cost-tag :value="rowData.amount" />
							</template>
							<!-- 入库状态 -->
							<template #status="{ rowData }">
								<dict-tag
									:options="getInStoreMatStatus()"
									:value="rowData.status"
								/>
							</template>

							<template #thisInStoreNum="{ rowData }">
								<el-input
									class="no-arrows"
									v-model="rowData.thisInStoreNum"
									@click.stop
									@input="
										rowData.thisInStoreNum = validateAndCorrectInput($event)
									"
									@change="validateInStoreNum(rowData)"
									v-if="rowData.status != MatInStoreStatus.allInStore"
								/>
								<span v-else>{{ rowData.thisInStoreNum_view }}</span>
							</template>

							<!-- 已入库批次 -->
							<template #inStoreBatchTimes="{ rowData }">
								<link-tag
									:value="rowData.inStoreBatchTimes"
									@click.stop="onRowBatch(rowData)"
								/>
							</template>
							<template #footerOperateLeft v-if="props.model == 'edit'">
								<ButtonList
									class="btn-list"
									:is-not-radius="true"
									:button="tableFooterBtns"
									@on-btn-click="handleTabFooterAction"
								/>
							</template>
						</PitayaTable>
					</el-scrollbar>
				</div>
				<div v-if="activeTab === 1">
					<TableFile
						:business-type="fileBusinessType.receivedStored"
						:business-id="props.id"
						:mod="
							formModal.status as any == MatInStoreStatus.allInStore ? IModalType.view: IModalType.edit
						"
					/>
				</div>
			</div>

			<ButtonList
				class="footer"
				:button="formBtnList"
				:loading="loading"
				@on-btn-click="onFormBtnList"
			/>
		</div>

		<!-- 接收入库 -->
		<Drawer
			:size="modalSize.sm"
			v-model:drawer="showQualityVisible"
			:destroyOnClose="true"
		>
			<import-drawer
				:desc-detail="formModal"
				:inStoreRow="selectedTableList"
				@update="handleInStoreAction"
				@close="showQualityVisible = false"
			/>
		</Drawer>

		<!-- 查看批次 -->
		<Drawer
			:size="modalSize.lg"
			v-model:drawer="showBatchVisible"
			:destroyOnClose="true"
		>
			<batch-drawer
				:id="currentRow.id!"
				:row="currentRow"
				@close="showBatchVisible = false"
			/>
		</Drawer>

		<!-- 选择入库货位 -->
		<Drawer
			:size="modalSize.lg"
			v-model:drawer="showStoreAddressVisible"
			:destroyOnClose="true"
		>
			<area-storage
				:storeId="formModal.storeId!"
				:storeName="formModal.storeName!"
				:storeCode="formModal?.storeCode"
				:regionId="first(selectedTableList)?.regionId"
				:room-id="[first(selectedTableList)?.roomId]"
				@onSaveOrClose="handleSaveRoom"
			/>
		</Drawer>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.custom-q {
	margin: 10px 0px -10px 10px !important;
}
.drawer-container {
	.left {
		width: 340px;
	}

	.right {
		width: calc(100% - 340px);
	}

	.tab-mat {
		height: 100%;
	}
}

.editor-table-wrapper {
	&:deep(.pitaya-table) {
		.error {
			.column-inner-item,
			.cell,
			.el-input__inner {
				color: red;
			}
		}
	}
}
</style>
