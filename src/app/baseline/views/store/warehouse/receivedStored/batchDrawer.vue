<script setup lang="ts">
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import { DictApi } from "@/app/baseline/api/dict"
import { reactive, ref } from "vue"
import CostTag from "@/app/baseline/views/components/costTag.vue"
import {
	MatInStoreReceiveBatchRequestParams,
	MatInStoreReceiveBatchVO,
	MatInStoreReceiveDetailVO
} from "../../../../utils/types/store-warehouse-receive"
import { useTbInit } from "../../../components/tableBase"
import { getReceiveBatch } from "@/app/baseline/api/store/warehouse/receive"
import { batchFormatterNumView } from "@/app/baseline/utils"
import { getRealLength, setString } from "@/app/baseline/utils/validate"

export interface Props {
	id: string | number //
	model?: string //显示模式 view, viewPlan
	row: MatInStoreReceiveDetailVO
}

const props = defineProps<Props>()
const emits = defineEmits(["close"])

const loading = ref(false)
const formBtnList = [{ name: "取消", icon: ["fas", "circle-minus"] }]

const formModal = reactive<MatInStoreReceiveDetailVO>({
	...props.row
})

const drawerLoading = ref(false)

/**
 * 左侧 title 配置
 */
const drawerLeftTitle = {
	name: ["物资信息"],
	icon: ["fas", "square-share-nodes"]
}

/**
 * 左侧 表单 配置
 */
const formEl = reactive([
	{ label: "物资编码", name: "materialCode" },
	{ label: "物资名称", name: "materialName" },
	{ label: "规格型号", name: "version" },
	{ label: "技术参数", name: "technicalParameter", needTooltip: true },
	{ label: "物资性质", name: "attribute" },
	{ label: "关联采购订单号", name: "purchaseCode" },
	{ label: "入库单号", name: "code" },
	{ label: "可入库数量", name: "inStoreNum_view" },
	{ label: "已入库数量", name: "inStoredNum_view" },
	{ label: "待入库数量", name: "toInStoreNum_view" },
	{ label: "批次号数量", name: "inStoreBatchTimes" }
])

/**
 * 左侧 title 配置
 */
const drawerRightTitle = {
	name: ["批次信息"],
	icon: ["fas", "square-share-nodes"]
}

const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	currentPage,
	pageTotal,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange,
	onDataSelected
} = useTbInit<MatInStoreReceiveBatchVO, MatInStoreReceiveBatchRequestParams>()

/**
 * table 列 配置
 */
tableProp.value = [
	{
		label: "入库批次号",
		prop: "batchNo",
		width: 160,
		fixed: "left"
	},
	{
		label: "入库单价",
		prop: "price",
		needSlot: true,
		align: "right",
		width: 120
	},
	{ label: "入库数量", prop: "inStoredNum_view", align: "right", width: 130 },
	{
		label: "入库金额",
		prop: "amount",
		needSlot: true,
		align: "right",
		minWidth: 130
	},
	{ label: "入库仓库名称", prop: "storeName", minWidth: 130 },
	{ label: "入库区域名称", prop: "regionLabel", minWidth: 100 },
	{ label: "入库货位编码", prop: "roomCode", minWidth: 100 },
	{
		label: "质保有效日期",
		prop: "validityPeriod",
		width: 130
	},
	{ label: "操作人", prop: "inspectionPersonId_view", width: 130 },
	{ label: "入库时间", prop: "inStoreUpdateTime", width: 160 }
]

/**
 * 格式化数量
 */
watch(tableData, () => {
	batchFormatterNumView(tableData.value as any[])
})

/* 初始化列表数据 */
const getTableData = (data?: any) => {
	if (props.id) {
		fetchFunc.value = getReceiveBatch

		currentPage.value = 1
		tableRef.value?.resetCurrentPage()

		fetchParam.value = {
			itemId: props.id,
			sord: "desc",
			sidx: "createdDate",
			...data
		}
		fetchTableData()
	}
}
onMounted(() => {
	getTableData()
})
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<!-- 左侧表单区域 -->
		<div class="drawer-column left">
			<div class="rows">
				<Title :title="drawerLeftTitle" />
				<el-descriptions size="small" :column="1" border class="content">
					<template>
						<el-descriptions-item
							v-for="(el, index) in formEl"
							label-align="center"
							:label="el.label"
							:key="index"
						>
							<dict-tag
								v-if="el.name === 'attribute'"
								:options="DictApi.getMatAttr()"
								:value="formModal.attribute"
							/>
							<span v-else-if="el.needTooltip">
								<el-tooltip
									effect="dark"
									:content="formModal?.[el.name]"
									:disabled="
										getRealLength(formModal?.[el.name]) <= 100 ? true : false
									"
								>
									{{
										getRealLength(formModal?.[el.name]) > 100
											? setString(formModal?.[el.name], 100)
											: formModal?.[el.name] || "---"
									}}
								</el-tooltip>
							</span>
							<span v-else>
								{{
									formModal[el.name]
										? formModal[el.name]
										: formModal[el.name] === 0
										? 0
										: "----"
								}}
							</span>
						</el-descriptions-item>
					</template>
				</el-descriptions>
			</div>
		</div>

		<!-- 右侧table区域 -->
		<div class="drawer-column right">
			<div class="rows">
				<Title :title="drawerRightTitle" />
				<el-scrollbar>
					<PitayaTable
						ref="tableRef"
						:columns="tableProp"
						:table-data="tableData"
						:need-index="true"
						:need-pagination="true"
						:single-select="false"
						:need-selection="false"
						:total="pageTotal"
						@onSelectionChange="onDataSelected"
						@on-current-page-change="onCurrentPageChange"
						:table-loading="tableLoading"
					>
						<template #amount="{ rowData }">
							<cost-tag :value="rowData.amount" />
						</template>
						<template #price="{ rowData }">
							<cost-tag :value="rowData.price" />
						</template>
					</PitayaTable>
				</el-scrollbar>
			</div>

			<ButtonList
				class="footer"
				:button="formBtnList"
				:loading="loading"
				@on-btn-click="emits('close')"
			/>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.drawer-container {
	.left {
		width: 310px;
	}

	.right {
		width: calc(100% - 310px);
	}

	.tab-mat {
		height: 100%;
	}
}
</style>
