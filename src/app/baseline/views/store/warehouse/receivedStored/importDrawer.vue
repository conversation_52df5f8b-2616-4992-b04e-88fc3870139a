<script setup lang="ts">
import { computed, reactive, ref, toValue } from "vue"
import { FormElementType } from "../../../components/define"
import FormElement from "@/app/baseline/views/components/formElement.vue"
import { ElMessage, type FormInstance, type FormRules } from "element-plus"
import areaStorage from "./areaStorage.vue"
import storeTable from "../../components/storeTable.vue"
import {
	MatInStoreReceiveDetailVO,
	MatInStoreReceiveVO
} from "../../../../utils/types/store-warehouse-receive"
import {
	IWarehouseType,
	MatStoreRegionDTO,
	MatStoreRegionTreeVo
} from "../../../../utils/types/store-manage"
import { editReceiveInStore } from "../../../../api/store/warehouse/receive"
import { pick, toNumber } from "lodash-es"
import { modalSize } from "@/app/baseline/utils/layout-config"
import { useDictInit } from "../../../components/dictBase"
import { first } from "lodash-es"
import {
	getIdempotentToken,
	validateAndCorrectInput
} from "@/app/baseline/utils/validate"
import { IInventoryBusinessType } from "@/app/baseline/utils/types/store-inventory"
import { useMessageBoxInit } from "../../../components/messageBox"
import {
	IIdempotentTokenType,
	IIdempotentTokenTypePre
} from "@/app/baseline/utils/types/common"

const { getDictByCodeList, dictFilter } = useDictInit()
const { showWarnConfirm } = useMessageBoxInit()

export interface Props {
	inStoreRow?: MatInStoreReceiveDetailVO[]
	descDetail: MatInStoreReceiveVO
}

const props = defineProps<Props>()
const qualityDrawerLeftTitle = {
	name: ["接收入库"],
	icon: ["fas", "square-share-nodes"]
}
const formBtnList = ref([
	{ name: "取消", icon: ["fas", "circle-minus"] },
	{ name: "确定", icon: ["fas", "check-circle"], disabled: false }
])
const emits = defineEmits(["close", "update"])
const ruleFormRef = ref<FormInstance>()

const formModelData = reactive<MatInStoreReceiveDetailVO>({
	...first(props.inStoreRow),
	storeId: props.descDetail.storeId,
	storeName: props.descDetail.storeName,
	storeCode: props.descDetail.storeCode,
	validityPeriod: first(props.inStoreRow)?.validityPeriod
		? first(props.inStoreRow)?.validityPeriod + " 00:00:00"
		: "",
	completeNum: first(props.inStoreRow)?.thisInStoreNum
})

/**
 * 根据prePreBusinessType 类型限制仓库类型
 * 采购入库 => 标准库
 * 随车配件、备品备件入库 => 备品备件库
 * 交旧完修入库 => 周转件正常库
 */
const storeSlectorType = computed(() => {
	switch (props.descDetail.prePreBusinessType) {
		case IInventoryBusinessType.purchaseToWarehouse: // 采购入库时
			return `${IWarehouseType.default},${IWarehouseType.dangerousWarehouse}`
		case IInventoryBusinessType.sparePartsToWarehouse: // 备品备件入库
			return `${IWarehouseType.spareParts},${IWarehouseType.sparePartsWarehouse}`
		case IInventoryBusinessType.partsToWarehouse: // 随车配件
			return `${IWarehouseType.spareParts},${IWarehouseType.sparePartsWarehouse}`
		case IInventoryBusinessType.repairToWarehouse: // 交旧完修入库
			return IWarehouseType.rotablesNormal
		case IInventoryBusinessType.physicalObject: // 实物物资入库
			return IWarehouseType.physicalObject
		default:
			return ""
	}
})

const showStoreAddressDrawer = ref(false) // 入库货位 drawer
const showMatStoreDrawer = ref(false) // 入库仓库

/**
 * 表单 配置
 */
const formEl = computed<FormElementType[][]>(() => {
	const ls: FormElementType[][] = [
		[{ label: "物资编码", name: "materialCode", disabled: true }],
		[{ label: "物资名称", name: "materialName", disabled: true }],
		[{ label: "规格型号", name: "version", disabled: true }],
		[
			{
				label: "技术参数",
				name: "technicalParameter",
				rows: 5,
				type: "textarea",
				disabled: true
			}
		],
		[{ label: "物资性质", name: "attribute_view", disabled: true }],
		[
			{
				label: "已入库数量",
				name: "inStoredNum",
				disabled: true,
				type: "number",
				append:
					dictFilter("INVENTORY_UNIT", formModelData.buyUnit!)?.subitemName ||
					"---"
			}
		],
		[
			{
				label: "待入库数量",
				name: "toInStoreNum",
				type: "number",
				disabled: true,
				append:
					dictFilter("INVENTORY_UNIT", formModelData.buyUnit!)?.subitemName ||
					"---"
			}
		],
		[
			{
				label: "本次入库数量",
				name: "completeNum",
				type: "number",
				input: (value: any) => {
					formModelData.completeNum = validateAndCorrectInput(value)
				},
				blur: (event: any) => {
					formModelData.completeNum = toNumber(event.target.value)
				},
				append:
					dictFilter("INVENTORY_UNIT", formModelData.buyUnit!)?.subitemName ||
					"---"
			}
		],
		[
			{
				label: "入库仓库",
				name: "storeName",
				type: "drawer",
				disabled: true
				/* clickApi: () => {
					showMatStoreDrawer.value = true
				} */
			}
		],
		[
			{ label: "入库区域", name: "regionLabel", disabled: true, width: 10 },
			{
				label: "入库货位",
				name: "roomCode",
				type: "drawer",
				disabled: !formModelData.storeId,
				width: 14,
				clickApi: () => {
					showStoreAddressDrawer.value = true
				}
			}
		],
		[
			{
				label: "质保期",
				name: "warrantyPeriod_view",
				disabled: true,
				placeholder: "请选择质保期"
			}
		],
		[
			{
				label: "质保有效日期",
				name: "validityPeriod",
				type: "date",
				placeholder: "请选择质保有效日期"
			}
		],
		[
			{
				label: "备注说明",
				name: "remark",
				maxlength: 200,
				rows: 5,
				type: "textarea"
			}
		]
	]

	switch (props.descDetail.prePreBusinessType) {
		case IInventoryBusinessType.repairToWarehouse:
			return ls.filter(
				(v) => v[0].label !== "质保有效日期" && v[0].label !== "warrantyPeriod"
			)

		default:
			return ls
	}
})

/**
 * 校验：本次入库数量不能大于待入库数量
 * @param rule
 * @param value
 * @param callback
 */
const validateStoreNum = (rule: any, value: any, callback: any) => {
	//if (formModelData.toInStoreNum! > 0) {
	if (formModelData.completeNum > formModelData.toInStoreNum!) {
		return callback(new Error("本次入库数量不能大于待入库数量"))
	} else if (formModelData.completeNum <= 0) {
		return callback(new Error("本次入库数量不能小于等于0"))
	}
	//}
	callback()
}

/**
 * 表单校验 配置
 */
const rules = reactive<FormRules<typeof formModelData>>({
	completeNum: [
		{ required: true, message: "本次入库数量不能为空", trigger: "change" },
		{ validator: validateStoreNum, required: true, trigger: "change" }
	],
	roomCode: [
		{ required: true, message: "入库货位不能为空", trigger: "change" }
	],
	storeName: [
		{ required: true, message: "入库仓库不能为空", trigger: "change" }
	]
})

/**
 * 接收入库 保存 操作
 */
const drawerBtnLoading = ref(false)
const onFormBtnList = (btnName: string | undefined) => {
	if (btnName === "确定") {
		if (ruleFormRef.value) {
			ruleFormRef.value.validate(async (valid) => {
				if (valid) {
					await showWarnConfirm("请确认是否接收入库？")

					drawerBtnLoading.value = true
					const params: { [propName: string]: any } = toValue(formModelData)
					//params.validityPeriod

					const idempotentToken = getIdempotentToken(
						IIdempotentTokenTypePre.other,
						IIdempotentTokenType.receiveToWarehouse,
						props.descDetail.id
					)
					editReceiveInStore(
						{
							...pick(
								params,
								"id",
								"completeNum",
								"storeId",
								"roomId",
								"regionId",
								"remark",
								"validityPeriod"
							)
						},
						idempotentToken
					)
						.then(() => {
							ElMessage.success("操作成功")
							emits("update")
							emits("close")
						})
						.finally(() => (drawerBtnLoading.value = false))
				} else {
					return
				}
			})
		} else {
			return
		}
	} else if (btnName === "取消") {
		formModelData.value = {}
		ruleFormRef.value?.clearValidate?.()
		emits("close")
	}
}
/**
 * 选择仓库 操作
 * @param btnName
 * @param row
 */
const saveSelectedMatStore = (btnName: string, row?: Record<string, any>) => {
	showMatStoreDrawer.value = false

	if (btnName === "保存") {
		formModelData.storeId = row?.id
		formModelData.storeName = row?.label
		formModelData.storeCode = row?.code

		// 清空货位&区域
		formModelData.regionId = ""
		formModelData.regionId = ""
		formModelData.roomCode = ""
		formModelData.roomId = null

		// 改变入库仓库后 清一下校验信息
		setTimeout(() => {
			ruleFormRef.value?.clearValidate()
		}, 0)
	}
}

/**
 * 选择入库货位 确定
 * @param btnName
 * @param treeRow
 * @param row
 */
const saveSelectedArea = (
	btnName: string,
	treeRow?: MatStoreRegionTreeVo,
	row?: MatStoreRegionDTO
) => {
	showStoreAddressDrawer.value = false
	if (btnName == "保存") {
		// 区域
		formModelData.regionLabel = treeRow?.label
		formModelData.regionId = treeRow?.id

		// 货位
		formModelData.roomCode = row?.code // 货位label
		formModelData.roomId = row?.id // 货位ID
	}
}

onMounted(async () => {
	await getDictByCodeList(["INVENTORY_UNIT", "MATERIAL_NATURE"])
	formModelData.attribute_view =
		dictFilter("MATERIAL_NATURE", formModelData?.attribute)?.subitemName ||
		"---"
})
</script>
<template>
	<div class="drawer-container">
		<div class="drawer-column">
			<div class="rows">
				<Title :title="qualityDrawerLeftTitle" />
				<el-scrollbar>
					<el-form
						style="padding-bottom: 30px"
						class="content form-base"
						ref="ruleFormRef"
						:model="formModelData"
						:rules="rules"
						label-position="top"
					>
						<FormElement :form-element="formEl" :form-data="formModelData" />
					</el-form>
				</el-scrollbar>
			</div>
			<ButtonList
				class="footer"
				:button="formBtnList"
				:loading="drawerBtnLoading"
				@onBtnClick="onFormBtnList"
			/>

			<!-- 选择入库货位 -->
			<Drawer
				:size="modalSize.lg"
				v-model:drawer="showStoreAddressDrawer"
				:destroyOnClose="true"
			>
				<area-storage
					:storeId="formModelData.storeId!"
					:storeName="formModelData.storeName!"
					:storeCode="formModelData.storeCode"
					:regionId="formModelData.regionId"
					:room-id="[formModelData.roomId]"
					@onSaveOrClose="saveSelectedArea"
				/>
			</Drawer>

			<!-- 选择入库仓库 -->
			<Drawer
				:size="modalSize.lg"
				v-model:drawer="showMatStoreDrawer"
				:destroyOnClose="true"
			>
				<store-table
					:selected-ids="[formModelData.storeId]"
					@onSave="saveSelectedMatStore"
					:table-api-params="{
						type: storeSlectorType,
						storekeeperFalg: 'true'
					}"
				/>
			</Drawer>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.drawer-column {
	width: 100%;
}
</style>
