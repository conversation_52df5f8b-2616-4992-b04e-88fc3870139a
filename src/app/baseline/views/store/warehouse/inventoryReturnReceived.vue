<script lang="ts" setup>
import { onMounted, ref, computed } from "vue"
import {
	checkPermission,
	isCheckPermission
} from "@/app/platform/utils/permission"
import { powerList } from "../../components/define.d"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import LinkTag from "@/app/baseline/views/components/linkTag.vue"
import CostTag from "@/app/baseline/views/components/costTag.vue"
import ViewDrawer from "./inventoryReturnReceived/viewDrawer.vue"
import warehouseReturnApplyDetail from "../business/warehouseReturnApply/warehouseReturnApplyDetail.vue"
import { getReturnStatus } from "./inventoryReturnReceived/inventoryReturnReceived"
import { listMatStoragePaged } from "@/app/baseline/api/store/manage-api"
import { omit } from "lodash-es"
import {
	MatReturnStoreRequestParams,
	MatReturnStoreVO
} from "../../../utils/types/store-warehouse-return"
import {
	getInStoreReturnBpmStatusCnt,
	getReturnList
} from "../../../api/store/warehouse/return"

import { modalSize } from "@/app/baseline/utils/layout-config"
import { useTbInit } from "../../components/tableBase"
import { InStoreStatus } from "./warehouse"

/**
 * 查询条件 配置
 */
const queryArrList = computed<querySetting[]>(() => {
	return [
		{
			name: "入库单号",
			key: "inStoreNo",
			placeholder: "请输入入库单号",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "退库单号",
			key: "preBusinessCode",
			placeholder: "请输入退库单号",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "申请人",
			key: "userName",
			placeholder: "请输入申请人",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "入库仓库",
			key: "storeId", // TODO
			placeholder: "请选择入库仓库",
			type: "tableSelect",
			tableInfo: [
				{
					title: "请选择入库仓库",
					tableApi: listMatStoragePaged, //表格接口
					tableColumns: [
						{ label: "仓库编码", prop: "code", width: 120 },
						{ label: "仓库名称", prop: "label" },
						{
							label: "仓库级别",
							prop: "level_view",
							width: 100
						},
						{
							label: "仓库类型",
							prop: "type_view",
							width: 120
						},
						{
							label: "所属段区",
							prop: "depotId_view",
							width: 150
						}
					],
					queryArrList: [
						{
							name: "仓库编码",
							key: "code",
							type: "input",
							placeholder: "请输入仓库编码"
						},
						{
							name: "仓库名称",
							key: "label",
							type: "input",
							placeholder: "请输入仓库名称"
						}
					],
					params: {
						currentPage: 1,
						pageSize: 20,
						storekeeperFalg: "true"
					}, //表格接口参数

					labelName: {
						key: "id", //回显标识
						label: "label", //input框要展示的字段
						value: "id" //要给后台传的字段
					}
				}
			]
		}
	]
})

/**
 * tab 切换 table数据源
 * @param data
 */
const getTableData = (data?: Record<string, any>) => {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()
	fetchParam.value = {
		...fetchParam.value,
		tableType: tabStatus.value,
		sord: "desc",
		sidx: "createdDate",
		...data
	}
	tableProp.value = [
		...tablePropGroup["base"],
		...tablePropGroup[`status${tabStatus.value}`]
	]
	handleUpdate()
}

/**
 * title 配置
 */
const rightTitle = {
	name: ["退库入库"],
	icon: ["fas", "square-share-nodes"]
}

/**
 * tab 配置
 */
const statusCnt = ref<number[]>([])
const tabList = [
	{
		name: "待入库",
		value: InStoreStatus.noIn
	},
	{
		name: "已入库",
		value: InStoreStatus.in
	}
]

/**
 * tab 操作
 */
const tabStatus = ref(tabList[0].value)
const activeName = ref(tabList[0].name)
const handleTabsClick = (tab: any) => {
	activeName.value = tab.paneName
	tabStatus.value = tabList[tab.index].value
	getTableData()
}

const {
	tableData,
	tableProp,
	tableRef,
	tableLoading,
	currentPage,
	pageTotal,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = useTbInit<MatReturnStoreVO, MatReturnStoreRequestParams>()

/**
 * table 列 配置
 */
const tablePropGroup: Record<string, any> = {
	base: [
		{
			label: "入库单号",
			prop: "code",
			minWidth: 180,
			sortable: true
		},
		{
			label: "关联退库单号",
			prop: "preBusinessCode",
			needSlot: true,
			minWidth: 200
		},
		{ label: "领料用途", prop: "purpose_view", width: 120 },
		{ label: "入库仓库名称", prop: "storeName", minWidth: 150 },
		{
			label: "退库金额",
			prop: "amount",
			align: "right",
			needSlot: true,
			minWidth: 120
		}
	],
	status1: [
		{
			label: "入库状态",
			prop: "status", //  入库状态 1：未入库、2：部分入库、 3：已入库
			minWidth: 150,
			needSlot: true
		},
		{ label: "入库物资编码", prop: "matCount", minWidth: 120 },
		{ label: "待入库物资编码", prop: "notInspectedCount", minWidth: 120 },
		{ label: "已入库物资编码", prop: "inspectedCount", minWidth: 120 },
		{ label: "操作人", prop: "userName_view", minWidth: 100 },
		{ label: "申请部门", prop: "sysOrgName_view", minWidth: 150 },
		{ label: "申请时间", prop: "createdDate", minWidth: 150, sortable: true },
		{
			label: "操作",
			width: 150,
			prop: "operations",
			needSlot: true,
			fixed: "right"
		}
	],
	status2: [
		{ label: "入库物资编码", prop: "matCount", minWidth: 120 },
		{ label: "待入库物资编码", prop: "notInspectedCount", minWidth: 120 },
		{ label: "已入库物资编码", prop: "inspectedCount", minWidth: 120 },
		{ label: "操作人", prop: "userName_view", minWidth: 100 },
		{ label: "申请部门", prop: "sysOrgName_view", minWidth: 150 },
		{ label: "申请时间", prop: "createdDate", minWidth: 150, sortable: true },
		{
			label: "操作",
			width: 150,
			prop: "operations",
			needSlot: true,
			fixed: "right"
		}
	]
}
fetchFunc.value = getReturnList

const curRowId = ref<any>("")
const curRowData = ref<any>("")
const showViewDrawer = ref<boolean>(false)
const model = ref<string>("view")

/**
 * 查看 操作
 * @param row
 */
const onRowView = (row: MatReturnStoreVO) => {
	curRowId.value = row.id
	curRowData.value = row
	showViewDrawer.value = true
	model.value = "view"
}

/**
 * 编辑 操作
 * @param row
 */
const onRowEdit = (row: MatReturnStoreVO) => {
	curRowId.value = row.id
	curRowData.value = row
	showViewDrawer.value = true
	model.value = "edit"
}

/**
 * 关联退库单号 操作
 */
const viewReturnApplyVisible = ref(false)
const onRowBusinessOrder = (row: MatReturnStoreVO) => {
	curRowId.value = row.id
	curRowData.value = row
	viewReturnApplyVisible.value = true
}

/**
 * 关闭 操作
 * @param msg
 */
/* const closeDrawer = (msg: string) => {
	if (msg === "pub") {
		handleUpdate()
	} else if (msg === "save") {
		handleUpdate()
		showViewDrawer.value = false
	} else {
		showViewDrawer.value = false
		viewReturnApplyVisible.value = false
	}
} */

/**
 * 更新主列表/tab 状态统计
 */
const handleUpdate = async () => {
	statusCnt.value = await getInStoreReturnBpmStatusCnt(
		omit(
			{
				...fetchParam.value
			},
			"tableType",
			"currentPage",
			"pageSize"
		) as any
	)
	fetchTableData()
}

onMounted(() => {
	getTableData()
})

/**
 * 排序
 * @param column
 * @param prop 字段名称
 * @param order ascending 升序 descending 降序
 */
function handleSortChange(column: any, prop: any, order: any) {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		sord:
			order === "descending" ? "desc" : order === "ascending" ? "asc" : "desc",
		sidx: order ? prop : "createdDate" // 排序字段
	}

	fetchTableData()
}
</script>
<template>
	<div class="app-container">
		<div class="whole-frame">
			<ModelFrame class="top-frame">
				<Query
					:queryArrList="queryArrList"
					@getQueryData="getTableData"
					class="ml10"
				/>
			</ModelFrame>
			<ModelFrame class="bottom-frame">
				<Title :title="rightTitle">
					<div class="app-tabs-wrapper">
						<el-tabs v-model="activeName" @tab-click="handleTabsClick">
							<el-tab-pane
								v-for="(tab, index) in tabList"
								:key="index"
								:label="`${tab.name}（${statusCnt[index] ?? 0}）`"
								:name="tab.name"
								:index="tab.name"
							/>
						</el-tabs>
					</div>
				</Title>

				<PitayaTable
					ref="tableRef"
					:columns="tableProp"
					:tableData="tableData"
					:total="pageTotal"
					:need-index="true"
					@on-current-page-change="onCurrentPageChange"
					:table-loading="tableLoading"
					@on-table-sort-change="handleSortChange"
				>
					<!-- 关联业务单号 -->
					<template #preBusinessCode="{ rowData }">
						<link-tag
							:value="rowData.preBusinessCode"
							@on-click="onRowBusinessOrder(rowData)"
						/>
					</template>

					<!-- 入库金额 -->
					<template #amount="{ rowData }">
						<cost-tag :value="rowData.amount" />
					</template>

					<!-- 入库状态 -->
					<template #status="{ rowData }">
						<dict-tag :options="getReturnStatus()" :value="rowData.status" />
					</template>
					<template #operations="{ rowData }">
						<slot
							v-if="
								(tabStatus == InStoreStatus.noIn &&
									isCheckPermission(
										powerList.storeWarehouseInventoryReturnReceivedBtnEdit
									)) ||
								isCheckPermission(
									powerList.storeWarehouseInventoryReturnReceivedBtnPreview
								)
							"
						>
							<el-button
								v-btn
								link
								@click="onRowEdit(rowData)"
								v-if="
									tabStatus == InStoreStatus.noIn &&
									isCheckPermission(
										powerList.storeWarehouseInventoryReturnReceivedBtnEdit
									)
								"
								:disabled="
									checkPermission(
										powerList.storeWarehouseInventoryReturnReceivedBtnEdit
									)
								"
							>
								<font-awesome-icon :icon="['fas', 'pen-to-square']" />
								<span class="table-inner-btn">编辑</span>
							</el-button>
							<el-button
								v-btn
								link
								@click="onRowView(rowData)"
								v-if="
									isCheckPermission(
										powerList.storeWarehouseInventoryReturnReceivedBtnPreview
									)
								"
								:disabled="
									checkPermission(
										powerList.storeWarehouseInventoryReturnReceivedBtnPreview
									)
								"
							>
								<font-awesome-icon :icon="['fas', 'eye']" />
								<span class="table-inner-btn">查看</span>
							</el-button>
						</slot>
						<slot v-else>---</slot>
					</template>
				</PitayaTable>

				<!-- 编辑/查看弹窗 -->
				<Drawer
					:size="modalSize.xl"
					v-model:drawer="showViewDrawer"
					:destroyOnClose="true"
				>
					<view-drawer
						:id="curRowId"
						:model="model"
						@close="showViewDrawer = false"
						@update="handleUpdate"
					/>
				</Drawer>

				<!-- 关联退库单号 -->
				<Drawer
					v-model:drawer="viewReturnApplyVisible"
					:size="modalSize.xl"
					destroy-on-close
				>
					<warehouse-return-apply-detail
						:id="curRowData.preBusinessId"
						@close="viewReturnApplyVisible = false"
					/>
				</Drawer>
			</ModelFrame>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
