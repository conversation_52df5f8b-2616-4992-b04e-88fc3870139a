<script lang="ts" setup>
import { computed, onMounted, ref } from "vue"
import {
	checkPermission,
	isCheckPermission
} from "@/app/platform/utils/permission"
import { powerList } from "../../components/define.d"
import {
	getInStoreInspectBpmStatusCnt,
	getInspectById,
	getInspectList,
	getInspectTransferPage,
	pushInspectStore
} from "@/app/baseline/api/store/warehouse/inspect"
import { listCompanyWithFormat } from "@/app/baseline/api/system"
import ViewDrawer from "./qualityInspection/viewDrawer.vue"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import LinkTag from "@/app/baseline/views/components/linkTag.vue"
import {
	MatInspectionMainStatus,
	MatInspectionStatus,
	getInspectionMainStatus,
	getInspectionMainToStoreStatus
} from "@/app/baseline/views/store/warehouse/qualityInspection/qualityInspection"
import {
	MatInStoreInspectRequestParams,
	MatInStoreInspectVO
} from "../../../utils/types/store-warehouse-inspect"
import { listMatStoragePaged } from "@/app/baseline/api/store/manage-api"
import { omit, first } from "lodash-es"
import { modalSize } from "@/app/baseline/utils/layout-config"
import businessComponentMap from "../../store/business/inventory/business-component-map"
import { IInventoryBusinessType } from "@/app/baseline/utils/types/store-inventory"
import { useTbInit } from "../../components/tableBase"
import { batchFormatterNumView, tableColFilter } from "@/app/baseline/utils"
import { useMessageBoxInit } from "../../components/messageBox"
import {
	IIdempotentTokenType,
	IIdempotentTokenTypePre,
	IModalType
} from "@/app/baseline/utils/types/common"
import shareEditor from "./qualityInspection/shareEditor.vue"
import { getIdempotentToken } from "@/app/baseline/utils/validate"

const { showWarnConfirm } = useMessageBoxInit()

/**
 * 公司列表
 */
const companyOptions = ref<any>([])

/**
 * 查询条件 配置
 */
const queryArrList = computed(() => {
	return [
		{
			name: "质检单号",
			key: "code",
			placeholder: "请输入质检单号",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "公司",
			key: "companyCode",
			type: "select",
			children: companyOptions.value,
			placeholder: "请选择公司"
		},
		{
			name: "入库仓库",
			key: "storeId",
			placeholder: "请选择入库仓库",
			type: "tableSelect",
			tableInfo: [
				{
					title: "请选择入库仓库",
					tableApi: listMatStoragePaged, //表格接口
					tableColumns: [
						{ label: "仓库编码", prop: "code", width: 120 },
						{ label: "仓库名称", prop: "label" },
						{
							label: "仓库级别",
							prop: "level_view",
							width: 100
						},
						{
							label: "仓库类型",
							prop: "type_view",
							width: 120
						},
						{
							label: "所属段区",
							prop: "depotId_view",
							width: 150
						}
					],
					queryArrList: [
						{
							name: "仓库编码",
							key: "code",
							type: "input",
							placeholder: "请输入仓库编码"
						},
						{
							name: "仓库名称",
							key: "label",
							type: "input",
							placeholder: "请输入仓库名称"
						}
					],
					params: {
						currentPage: 1,
						pageSize: 20
					}, //表格接口参数

					labelName: {
						key: "id", //回显标识
						label: "label", //input框要展示的字段
						value: "id" //要给后台传的字段
					}
				}
			]
		}
	]
})

/**
 * Title 配置
 */
const rightTitle = {
	name: ["质量检验"],
	icon: ["fas", "square-share-nodes"]
}

/**
 * table tab 配置项
 */
const statusCnt = ref<number[]>([])
const tabList = [
	{
		name: "待处理",
		value: MatInspectionMainStatus.noFinish
	},
	{
		name: "已完成",
		value: MatInspectionMainStatus.finish
	},
	{
		name: "转派记录",
		value: MatInspectionMainStatus.transfer
	}
]
const tabStatus = ref(MatInspectionMainStatus.noFinish) // tab status 待处理列表：1，已完成列表：2
const activeName = ref(tabList[0].name)
const handleTabsClick = (tab: any) => {
	activeName.value = tab.paneName
	tabStatus.value = tabList[tab.index].value

	if (tabStatus.value == MatInspectionMainStatus.transfer) {
		fetchFunc.value = getInspectTransferPage
	} else {
		fetchFunc.value = getInspectList
	}
	getTableData()
}

const tbInit = useTbInit<MatInStoreInspectVO, MatInStoreInspectRequestParams>()
const {
	tableData,
	tableRef,
	tableLoading,
	currentPage,
	pageTotal,
	fetchParam,
	selectedTableList,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = tbInit

/* table 列 配置 */
tbInit.tableProp = computed(() => {
	const defCols: TableColumnType[] = [
		{
			label: "质检单号",
			prop: "code",
			minWidth: 180
		},
		{
			label: "关联业务单号",
			prop: "preBusinessCode",
			needSlot: true,
			minWidth: 200
		},
		{ label: "入库仓库名称", prop: "storeName", minWidth: 150 },
		{ label: "质检状态", prop: "status", needSlot: true, minWidth: 100 }, // todo 枚举值
		{
			label: "推送入库状态",
			prop: "toStoreStatus",
			needSlot: true,
			minWidth: 100
		},
		{
			label: "质检物资数量",
			prop: "inspectNum_view",
			minWidth: 150,
			align: "right"
		},
		{
			label: "待质检物资数量",
			prop: "toInspectNum_view",
			minWidth: 150,
			align: "right"
		},
		{
			label: "已质检物资数量",
			prop: "inspectedNum_view",
			minWidth: 150,
			align: "right"
		},
		{
			label: "质检完成时间",
			prop: "inspectFinishTime",
			minWidth: 150
		},
		{
			label: "推送质检时间",
			prop: "createdDate",
			minWidth: 150,
			sortable: true
		},
		{ label: "质检员", prop: "inspectUsername_view", minWidth: 150 },
		{
			label: "操作",
			width: 150,
			prop: "operations",
			needSlot: true,
			fixed: "right"
		}
	]

	if (tabStatus.value == MatInspectionMainStatus.transfer) {
		return [
			{
				label: "转派时间",
				prop: "createdDate",
				minWidth: 160,
				sortable: true
			},
			{
				label: "质检单号",
				prop: "code",
				minWidth: 180
			},
			{
				label: "转派人",
				prop: "transferUsername_view"
			},
			{
				label: "接收人",
				prop: "receiveUsername_view"
			},
			{ label: "转派原因", prop: "reason" }
		]
	}
	return tableColFilter(
		defCols,
		tabStatus.value == MatInspectionMainStatus.noFinish
			? ["质检完成时间"]
			: ["质检状态", "推送入库状态", "待质检物资数量", "已质检物资数量"]
	)
})

const tbBtnLoading = ref(false)
const shareEditorVisible = ref(false)
/**
 * 按钮 配置
 */
const tableFooterBtns = computed(() => {
	return [
		{
			name: "转派",
			roles: powerList.storeWarehouseQualityInspectionBtnShare,
			icon: ["fas", "fa-share"],
			disabled:
				selectedTableList.value.length > 0 &&
				first(selectedTableList.value)?.status == MatInspectionStatus.noQuality
					? false
					: true
		}
	]
})

/**
 * 格式化数量
 */
watch(tableData, () => {
	batchFormatterNumView(tableData.value as any[])
})

/* table 数据源 */
fetchFunc.value = getInspectList

const getTableData = (data?: any) => {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()
	fetchParam.value = {
		...fetchParam.value,
		tableType: tabStatus.value,
		...data
	}

	handleUpdate()
}

const curRowId = ref<any>("")
const curRowData = ref<MatInStoreInspectVO>({})
const showViewDrawer = ref<boolean>(false)
const model = ref<string>("view") // view | edit

/**
 * 业务组件
 *
 * 根据不同事务类型，展示事务的前置业务
 */
const businessComponent = computed(() => {
	return businessComponentMap[
		curRowData.value?.preBusinessType as IInventoryBusinessType
	]
})

/**
 * 查看 操作
 * @param row
 */
const onRowView = (row: MatInStoreInspectVO) => {
	curRowId.value = row.id
	curRowData.value = row
	showViewDrawer.value = true
	model.value = IModalType.view
}

/**
 * 编辑 操作
 * @param row
 */
const onRowEdit = (row: MatInStoreInspectVO) => {
	curRowId.value = row.id
	curRowData.value = row
	showViewDrawer.value = true
	model.value = IModalType.edit
}

/**
 * 关联业务单号 操作
 */
const businessComponentVisible = ref(false)
const onRowBusinessOrder = (row: MatInStoreInspectVO) => {
	curRowId.value = row.id
	curRowData.value = row
	businessComponentVisible.value = true
}

/**
 * 更新主列表/tab 状态统计
 */
const handleUpdate = async () => {
	statusCnt.value = await getInStoreInspectBpmStatusCnt(
		omit(
			{
				...fetchParam.value
			},
			"tableType",
			"currentPage",
			"pageSize"
		) as any
	)
	fetchTableData()
}

/**
 * 关闭弹窗前置判断 是否有未推送数据
 */
const formBtnLoading = ref(false)

async function handleBeforeClose() {
	formBtnLoading.value = true
	try {
		const r: any = await getInspectById(curRowId.value as number)
		if (r.toStoreNum > 0) {
			await showWarnConfirm("您有已质检的物资还未推送入库，是否推送入库？")
			const idempotentToken = getIdempotentToken(
				IIdempotentTokenTypePre.other,
				IIdempotentTokenType.qualityInspection,
				curRowId.value
			)
			await pushInspectStore(curRowId.value, idempotentToken)
			ElMessage.success("已将已质检物资全部推送至接收入库！")
			handleUpdate()
		}
	} finally {
		formBtnLoading.value = false
		showViewDrawer.value = false
	}
}

onMounted(() => {
	fetchParam.value.sord = "desc"
	fetchParam.value.sidx = "createdDate"
	listCompanyWithFormat().then((r) => (companyOptions.value = r))
	getTableData()
})

/**
 * 排序
 * @param column
 * @param prop 字段名称
 * @param order ascending 升序 descending 降序
 */
function handleSortChange(column: any, prop: any, order: any) {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		sord:
			order === "descending" ? "desc" : order === "ascending" ? "asc" : "desc",
		sidx: order ? prop : "createdDate" // 排序字段
	}

	fetchTableData()
}
</script>
<template>
	<div class="app-container">
		<div class="whole-frame">
			<ModelFrame class="top-frame">
				<Query
					:queryArrList="queryArrList"
					@getQueryData="getTableData"
					class="ml10"
				/>
			</ModelFrame>

			<ModelFrame class="bottom-frame">
				<Title :title="rightTitle">
					<div class="app-tabs-wrapper">
						<el-tabs v-model="activeName" @tab-click="handleTabsClick">
							<el-tab-pane
								v-for="(tab, index) in tabList"
								:key="index"
								:label="`${tab.name}（${statusCnt[index] ?? 0}）`"
								:name="tab.name"
								:index="tab.name"
							/>
						</el-tabs>
					</div>
				</Title>

				<PitayaTable
					ref="tableRef"
					:columns="(tbInit.tableProp as any)"
					:tableData="tableData"
					:total="pageTotal"
					:need-index="true"
					:single-select="tabStatus == tabList[0].value ? true : false"
					:need-selection="tabStatus == tabList[0].value ? true : false"
					@on-current-page-change="onCurrentPageChange"
					:table-loading="tableLoading"
					@on-table-sort-change="handleSortChange"
					@onSelectionChange="selectedTableList = $event"
				>
					<!-- 关联业务单号 -->
					<template #preBusinessCode="{ rowData }">
						<link-tag
							:value="rowData.preBusinessCode"
							@on-click="onRowBusinessOrder(rowData)"
						/>
					</template>

					<!-- 质检状态 -->
					<template #status="{ rowData }">
						<dict-tag
							:options="getInspectionMainStatus()"
							:value="rowData.status"
						/>
					</template>

					<!-- 推送入库状态 -->
					<template #toStoreStatus="{ rowData }">
						<dict-tag
							:options="getInspectionMainToStoreStatus()"
							:value="rowData.toStoreStatus"
						/>
					</template>

					<template #operations="{ rowData }">
						<slot
							v-if="
								(tabStatus == tabList[0].value &&
									isCheckPermission(
										powerList.storeWarehouseQualityInspectionBtnEdit
									)) ||
								isCheckPermission(
									powerList.storeWarehouseQualityInspectionBtnPreview
								)
							"
						>
							<el-button
								v-btn
								link
								@click="onRowEdit(rowData)"
								v-if="
									tabStatus == tabList[0].value &&
									isCheckPermission(
										powerList.storeWarehouseQualityInspectionBtnEdit
									)
								"
								:disabled="
									checkPermission(
										powerList.storeWarehouseQualityInspectionBtnEdit
									)
								"
							>
								<font-awesome-icon :icon="['fas', 'pen-to-square']" />
								<span class="table-inner-btn">编辑</span>
							</el-button>
							<el-button
								v-btn
								link
								@click="onRowView(rowData)"
								v-if="
									isCheckPermission(
										powerList.storeWarehouseQualityInspectionBtnPreview
									)
								"
								:disabled="
									checkPermission(
										powerList.storeWarehouseQualityInspectionBtnPreview
									)
								"
							>
								<font-awesome-icon :icon="['fas', 'eye']" />
								<span class="table-inner-btn">查看</span>
							</el-button>
						</slot>
						<slot v-else>---</slot>
					</template>

					<template #footerOperateLeft v-if="tabStatus === tabList[0].value">
						<ButtonList
							class="btn-list"
							:is-not-radius="true"
							:button="tableFooterBtns"
							:loading="tbBtnLoading"
							@on-btn-click="shareEditorVisible = true"
						/>
					</template>
				</PitayaTable>

				<!-- 编辑/查看弹窗 -->
				<Drawer
					:size="modalSize.xl"
					v-model:drawer="showViewDrawer"
					destroyOnClose
					:isBeforeClose="model == IModalType.edit ? true : false"
					@before-close="handleBeforeClose"
				>
					<view-drawer
						:id="curRowId"
						:row="curRowData"
						:model="model"
						v-model:formBtnLoading="formBtnLoading"
						@update="handleUpdate"
						@close="showViewDrawer = false"
					/>
				</Drawer>

				<!-- 关联业务单号 -->
				<Drawer
					:size="modalSize.xl"
					v-model:drawer="businessComponentVisible"
					destroyOnClose
				>
					<component
						:is="businessComponent"
						:id="curRowData?.preBusinessId"
						@close="businessComponentVisible = false"
					/>
				</Drawer>
				<Drawer
					:size="modalSize.sm"
					v-model:drawer="shareEditorVisible"
					destroyOnClose
				>
					<shareEditor
						:id="first(selectedTableList)?.id"
						@update="handleUpdate"
						@close="shareEditorVisible = false"
					/>
				</Drawer>
			</ModelFrame>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
