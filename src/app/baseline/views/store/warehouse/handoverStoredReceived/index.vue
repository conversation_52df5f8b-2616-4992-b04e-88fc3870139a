<!-- 交旧入库 列表 -->
<script lang="ts" setup>
import { onMounted, ref, computed } from "vue"
import {
	checkPermission,
	isCheckPermission
} from "@/app/platform/utils/permission"
import { powerList } from "../../../components/define.d"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import LinkTag from "@/app/baseline/views/components/linkTag.vue"
import handoverStoredReceivedDetail from "./handoverStoredReceivedDetail.vue"
import { getInStoreStatus } from "@/app/baseline/views/store/warehouse/receivedStored/receivedStored"

import { modalSize } from "@/app/baseline/utils/layout-config"
import businessComponentMap from "../../../store/business/inventory/business-component-map"
import { IInventoryBusinessType } from "@/app/baseline/utils/types/store-inventory"
import { IModalType } from "@/app/baseline/utils/types/common"
import { listMatStoragePaged } from "@/app/baseline/api/store/manage-api"
import { useDictInit } from "../../../components/dictBase"
import { useTbInit } from "../../../components/tableBase"
import {
	getInStoreWasteOldBpmStatusCnt,
	listMatInStoreWasteOldApplyPage
} from "@/app/baseline/api/store/warehouse/handover"
import {
	MatInStoreWasteOldApplyPageVo,
	MatInStoreWasteOldApplyPageVoRequest
} from "@/app/baseline/utils/types/warehouse-handover"
import { InStoreStatus } from "../warehouse"
import { tableColFilter } from "@/app/baseline/utils"
import { omit } from "lodash-es"

const { dictOptions, getDictByCodeList } = useDictInit()

/**
 * 查询条件 配置
 */
const queryArrList = computed(() => {
	return [
		{
			name: "入库单号",
			key: "code",
			placeholder: "请输入入库单号",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "交旧申请单号",
			key: "wasteOldApplyCode",
			placeholder: "请输入入库单号",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "交旧类型",
			key: "preBusinessType",
			placeholder: "请选择交旧类型",
			type: "select",
			children: dictOptions.value["EXCHANGE_TPYE"]
		},
		{
			name: "入库类型",
			key: "type",
			placeholder: "请选择入库类型",
			type: "select", // TRANSACTION_BUSINESS
			children: [
				{ label: "交旧废旧入库", value: "CK-JJ-FJ-RK" },
				{ label: "交旧返修入库", value: "CK-JJ-FX-RK" }
				/* { label: "交旧备用入库", value: "CK-JJ-BY-RK" } */
			]
		},
		{
			name: "入库仓库",
			key: "storeId",
			type: "tableSelect",
			placeholder: "请选择入库仓库",
			tableInfo: [
				{
					title: "请选择入库仓库",
					tableApi: listMatStoragePaged, //表格接口
					tableColumns: [
						{ label: "仓库编码", prop: "code", width: 120 },
						{ label: "仓库名称", prop: "label" },
						{
							label: "仓库级别",
							prop: "level_view",
							width: 100
						},
						{
							label: "仓库类型",
							prop: "type_view",
							width: 120
						},
						{
							label: "所属段区",
							prop: "depotId_view",
							width: 150
						}
					],
					queryArrList: [
						{
							name: "仓库编码",
							key: "code",
							type: "input",
							placeholder: "请输入仓库编码"
						},
						{
							name: "仓库名称",
							key: "label",
							type: "input",
							placeholder: "请输入仓库名称"
						}
					],
					params: {
						currentPage: 1,
						pageSize: 20,
						storekeeperFalg: "true"
					}, //表格接口参数

					labelName: {
						key: "id", //回显标识
						label: "label", //input框要展示的字段
						value: "id" //要给后台传的字段
					}
				}
			]
		}
	]
})

/**
 * tab 切换 table数据源
 * @param data
 */
const getTableData = (data?: Record<string, any>) => {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		tableType: tabStatus.value,
		sord: "desc",
		sidx: "createdDate",
		...data
	}

	handleUpdate()
}

/**
 * title 配置
 */
const rightTitle = {
	name: ["交旧入库"],
	icon: ["fas", "square-share-nodes"]
}

/**
 * tab 配置
 */
const statusCnt = ref<number[]>([])
const tabList = [
	{
		name: "待入库",
		value: InStoreStatus.noIn
	},
	{
		name: "已入库",
		value: InStoreStatus.in
	}
]

/**
 * tab 操作
 */
const tabStatus = ref(tabList[0].value)
const activeName = ref(tabList[0].name)
const handleTabsClick = (tab: any) => {
	activeName.value = tab.paneName
	tabStatus.value = tabList[tab.index].value
	getTableData()
}

const tabInit = useTbInit<
	MatInStoreWasteOldApplyPageVo,
	MatInStoreWasteOldApplyPageVoRequest
>()
const {
	tableData,
	tableRef,
	tableLoading,
	currentPage,
	pageTotal,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = tabInit

/**
 * table 列 配置
 */
tabInit.tableProp = computed(() => {
	const defConfColumns: TableColumnType[] = [
		{
			label: "入库单号",
			prop: "code",
			minWidth: 200,
			fixed: "left",
			sortable: true
		},
		{
			label: "关联交旧申请单号",
			prop: "preBusinessCode",
			needSlot: true,
			minWidth: 200
		},
		{ label: "交旧类型", prop: "wasteOldType_view", width: 130 }, // 字典
		{ label: "入库类型", prop: "type_view", minWidth: 130 }, // 字典
		{ label: "入库仓库名称", prop: "storeName", minWidth: 150 },
		{ label: "入库物资编码数量", prop: "materialCodeNum", minWidth: 150 },
		{ label: "待入库物资编码数量", prop: "toMaterialCodeNum", minWidth: 150 },
		{
			label: "已入库物资编码数量",
			prop: "completedMaterialCodeNum",
			minWidth: 150
		},
		{
			label: "入库状态", // todo
			prop: "status", //  枚举值 1:未入库 2:部分入库
			minWidth: 150,
			needSlot: true
		},
		{ label: "申请部门", prop: "sysOrgId_view", minWidth: 150 },
		{ label: "申请人", prop: "createdBy_view", minWidth: 150 },
		{ label: "申请时间", prop: "createdDate", minWidth: 150, sortable: true },
		{
			label: "操作",
			width: 150,
			prop: "operations",
			needSlot: true,
			fixed: "right"
		}
	]
	if (tabStatus.value == InStoreStatus.in) {
		return tableColFilter(defConfColumns, [
			"待入库物资编码数量",
			"已入库物资编码数量"
		])
	} else {
		return defConfColumns
	}
})

fetchFunc.value = listMatInStoreWasteOldApplyPage

const curRowId = ref<any>("")
const curRowData = ref<any>("")
const showViewDrawer = ref<boolean>(false)
const model = ref<string>("view")

/**
 * 业务组件
 *
 * 根据不同事务类型，展示事务的前置业务
 */
const businessComponent = computed(() => {
	return businessComponentMap[
		curRowData.value?.preBusinessType as IInventoryBusinessType
	]
})

/**
 * 查看 操作
 * @param row
 */
const onRowView = (row: MatInStoreWasteOldApplyPageVo) => {
	curRowId.value = row.id
	curRowData.value = row
	showViewDrawer.value = true
	model.value = IModalType.view
}

/**
 * 编辑 操作
 * @param row
 */
const onRowEdit = (row: MatInStoreWasteOldApplyPageVo) => {
	curRowId.value = row.id
	curRowData.value = row
	showViewDrawer.value = true
	model.value = IModalType.edit
}

/**
 * 关联业务单号 操作
 */
const businessComponentVisible = ref(false)
const onRowBusinessOrder = (row: MatInStoreWasteOldApplyPageVo) => {
	curRowId.value = row.id
	curRowData.value = row
	businessComponentVisible.value = true
}

/**
 * 更新主列表/tab 状态统计
 */
const handleUpdate = async () => {
	statusCnt.value = await getInStoreWasteOldBpmStatusCnt(
		omit(
			{
				...fetchParam.value
			},
			"tableType",
			"currentPage",
			"pageSize"
		) as any
	)
	fetchTableData()
}

onMounted(() => {
	getDictByCodeList(["EXCHANGE_TPYE"])
	getTableData()
})

/**
 * 排序
 * @param column
 * @param prop 字段名称
 * @param order ascending 升序 descending 降序
 */
function handleSortChange(column: any, prop: any, order: any) {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		sord:
			order === "descending" ? "desc" : order === "ascending" ? "asc" : "desc",
		sidx: order ? prop : "createdDate" // 排序字段
	}

	fetchTableData()
}
</script>
<template>
	<div class="app-container">
		<div class="whole-frame">
			<ModelFrame class="top-frame">
				<Query
					:queryArrList="queryArrList"
					@getQueryData="getTableData"
					class="ml10"
				/>
			</ModelFrame>
			<ModelFrame class="bottom-frame">
				<Title :title="rightTitle">
					<div class="app-tabs-wrapper">
						<el-tabs v-model="activeName" @tab-click="handleTabsClick">
							<el-tab-pane
								v-for="(tab, index) in tabList"
								:key="index"
								:label="`${tab.name}（${statusCnt[index] ?? 0}）`"
								:name="tab.name"
								:index="tab.name"
							/>
						</el-tabs>
					</div>
				</Title>

				<PitayaTable
					ref="tableRef"
					:columns="(tabInit.tableProp as any)"
					:tableData="tableData"
					:total="pageTotal"
					:need-index="true"
					@on-current-page-change="onCurrentPageChange"
					:table-loading="tableLoading"
					@on-table-sort-change="handleSortChange"
				>
					<!-- 关联业务单号 -->
					<template #preBusinessCode="{ rowData }">
						<link-tag
							:value="rowData.preBusinessCode"
							@on-click="onRowBusinessOrder(rowData)"
						/>
					</template>

					<!-- 入库状态 -->
					<template #status="{ rowData }">
						<dict-tag :options="getInStoreStatus()" :value="rowData.status" />
					</template>
					<template #operations="{ rowData }">
						<slot
							v-if="
								(tabStatus == InStoreStatus.noIn &&
									isCheckPermission(
										powerList.storeWarehouseHandoverStoredReceivedBtnEdit
									)) ||
								isCheckPermission(
									powerList.storeWarehouseHandoverStoredReceivedBtnPreview
								)
							"
						>
							<el-button
								v-btn
								link
								@click="onRowEdit(rowData)"
								v-if="
									tabStatus == InStoreStatus.noIn &&
									isCheckPermission(
										powerList.storeWarehouseHandoverStoredReceivedBtnEdit
									)
								"
								:disabled="
									checkPermission(
										powerList.storeWarehouseHandoverStoredReceivedBtnEdit
									)
								"
							>
								<font-awesome-icon :icon="['fas', 'pen-to-square']" />
								<span class="table-inner-btn">编辑</span>
							</el-button>
							<el-button
								v-btn
								link
								@click="onRowView(rowData)"
								v-if="
									isCheckPermission(
										powerList.storeWarehouseHandoverStoredReceivedBtnPreview
									)
								"
								:disabled="
									checkPermission(
										powerList.storeWarehouseHandoverStoredReceivedBtnPreview
									)
								"
							>
								<font-awesome-icon :icon="['fas', 'eye']" />
								<span class="table-inner-btn">查看</span>
							</el-button>
						</slot>
						<slot v-else>---</slot>
					</template>
				</PitayaTable>

				<!-- 编辑/查看弹窗 -->
				<Drawer
					:size="modalSize.xl"
					v-model:drawer="showViewDrawer"
					:destroyOnClose="true"
				>
					<handover-stored-received-detail
						:id="curRowId"
						:model="model"
						@close="showViewDrawer = false"
						@update="handleUpdate"
					/>
				</Drawer>

				<!-- 关联业务单号 -->
				<Drawer
					:size="modalSize.xl"
					v-model:drawer="businessComponentVisible"
					:destroyOnClose="true"
				>
					<component
						:is="businessComponent"
						:id="curRowData?.preBusinessId"
						@close="businessComponentVisible = false"
					/>
				</Drawer>
			</ModelFrame>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
