<script setup lang="ts">
import { computed, reactive, ref, toRef, toValue } from "vue"
import { FormElementType } from "../../../components/define"
import FormElement from "@/app/baseline/views/components/formElement.vue"
import { ElMessage, type FormInstance, type FormRules } from "element-plus"
import areaStorage from "../receivedStored/areaStorage.vue"
import storeTable from "../../components/storeTable.vue"
import {
	MatStoreRegionDTO,
	MatStoreRegionTreeVo
} from "../../../../utils/types/store-manage"
import { pick, toNumber } from "lodash-es"
import { modalSize } from "@/app/baseline/utils/layout-config"
import { useDictInit } from "../../../components/dictBase"
import {
	MatInStoreWasteOldApplyDetailVo,
	MatInStoreWasteOldApplyItemPageVo
} from "@/app/baseline/utils/types/warehouse-handover"
import { updateMatInStoreWasteOldApplyInStore } from "@/app/baseline/api/store/warehouse/handover"
import {
	getIdempotentToken,
	validateAndCorrectInput
} from "@/app/baseline/utils/validate"
import { useMessageBoxInit } from "../../../components/messageBox"

import {
	IIdempotentTokenType,
	IIdempotentTokenTypePre
} from "@/app/baseline/utils/types/common"

const { getDictByCodeList, dictFilter } = useDictInit()

const { showWarnConfirm } = useMessageBoxInit()

export interface Props {
	detailInfo?: MatInStoreWasteOldApplyDetailVo
	inStoreRow?: MatInStoreWasteOldApplyItemPageVo[]
}

const props = defineProps<Props>()
const curInstoreRowData = toRef(props, "inStoreRow")
const qualityDrawerLeftTitle = {
	name: ["交旧入库"],
	icon: ["fas", "square-share-nodes"]
}
const formBtnList = ref([
	{ name: "取消", icon: ["fas", "circle-minus"] },
	{ name: "确定", icon: ["fas", "check-circle"], disabled: false }
])
const emits = defineEmits(["onSaveOrClose"])
const ruleFormRef = ref<FormInstance>()

const formModelData = ref<MatInStoreWasteOldApplyItemPageVo>({
	...curInstoreRowData.value![0],
	storeId: props.detailInfo?.storeId,
	storeName: props.detailInfo?.storeName,
	storeCode: props.detailInfo?.storeCode,
	completeNum: curInstoreRowData.value![0].thisInStoreNum
})

const showStoreAddressDrawer = ref(false) // 入库货位 drawer
const showMatStoreDrawer = ref(false) // 入库仓库

/**
 * 表单 配置
 */
const formEl = computed<FormElementType[][]>(() => {
	return [
		[{ label: "物资编码", name: "materialCode", disabled: true }],
		[{ label: "物资名称", name: "materialName", disabled: true }],
		[{ label: "规格型号", name: "version", disabled: true }],
		[
			{
				label: "技术参数",
				name: "technicalParameter",
				disabled: true,
				rows: 5,
				type: "textarea"
			}
		],
		[{ label: "物资性质", name: "attribute_view", disabled: true }],
		[{ label: "库存单位", name: "useUnit_view", disabled: true }],
		[{ label: "主要材质", name: "quality_view", disabled: true }],
		[{ label: "废旧物资分类", name: "wasteMaterialType_view", disabled: true }],
		[
			{
				label: "待入库数量",
				name: "waitingInStoreNum",
				type: "number",
				disabled: true,
				append:
					dictFilter(
						"INVENTORY_UNIT",
						formModelData.value.useUnit as unknown as string
					)?.subitemName || "---"
			}
		],
		[
			{
				label: "本次入库数量",
				name: "completeNum",
				type: "number",
				input: (value: any) => {
					formModelData.value.completeNum = validateAndCorrectInput(value)
				},
				blur: (event: any) => {
					formModelData.value.completeNum = toNumber(event.target.value)
				},
				append:
					dictFilter(
						"INVENTORY_UNIT",
						formModelData.value.useUnit as unknown as string
					)?.subitemName || "---"
			}
		],
		[
			{
				label: "入库仓库",
				name: "storeName",
				type: "input",
				disabled: true
				/* type: "drawer",
				clickApi: () => {
					showMatStoreDrawer.value = true
				} */
			}
		],
		[
			{ label: "入库区域", name: "regionLabel", disabled: true, width: 10 },
			{
				label: "入库货位",
				name: "roomCode",
				type: "drawer",
				disabled: !formModelData.value.storeId,
				width: 14,
				clickApi: () => {
					showStoreAddressDrawer.value = true
				}
			}
		],
		[
			{
				label: "备注说明",
				name: "remark",
				maxlength: 200,
				rows: 5,
				type: "textarea"
			}
		]
	]
})

/**
 * 校验：本次入库数量不能大于待入库数量
 * @param rule
 * @param value
 * @param callback
 */
const validateStoreNum = (rule: any, value: any, callback: any) => {
	if (
		formModelData.value.completeNum > formModelData.value.waitingInStoreNum!
	) {
		return callback(new Error("本次入库数量不能大于待入库数量"))
	} else if (formModelData.value.completeNum <= 0) {
		return callback(new Error("本次入库数量不能小于等于0"))
	}
	callback()
}

/**
 * 表单校验 配置
 */
const rules = reactive<FormRules<typeof formModelData>>({
	completeNum: [
		{ required: true, message: "本次入库数量不能为空", trigger: "change" },
		{ validator: validateStoreNum, required: true, trigger: "change" }
	],
	roomCode: [
		{ required: true, message: "入库货位不能为空", trigger: "change" }
	],
	storeName: [
		{ required: true, message: "入库仓库不能为空", trigger: "change" }
	]
})

/**
 * 交旧入库 保存 操作
 */
const drawerBtnLoading = ref(false)
const onFormBtnList = (btnName: string | undefined) => {
	if (btnName === "确定") {
		if (ruleFormRef.value) {
			ruleFormRef.value.validate(async (valid) => {
				if (valid) {
					await showWarnConfirm("请确认是否交旧入库？")

					drawerBtnLoading.value = true
					const params: { [propName: string]: any } = toValue(
						formModelData.value
					)

					const idempotentToken = getIdempotentToken(
						IIdempotentTokenTypePre.other,
						IIdempotentTokenType.handoverStoredReceived,
						props.detailInfo?.id
					)

					updateMatInStoreWasteOldApplyInStore(
						pick(
							params,
							"id",
							"completeNum",
							"storeId",
							"roomId",
							"regionId",
							"remark"
						),
						idempotentToken
					)
						.then(() => {
							ElMessage.success("操作成功")
							emits("onSaveOrClose", "save")
						})
						.finally(() => (drawerBtnLoading.value = false))
				} else {
					return
				}
			})
		} else {
			return
		}
	} else if (btnName === "取消") {
		formModelData.value = {}
		ruleFormRef.value?.clearValidate?.()
		emits("onSaveOrClose")
	}
}
/**
 * 选择仓库 操作
 * @param btnName
 * @param row
 */
const saveSelectedMatStore = (btnName: string, row?: Record<string, any>) => {
	showMatStoreDrawer.value = false

	if (btnName === "保存") {
		formModelData.value.storeId = row?.id
		formModelData.value.storeName = row?.label
		formModelData.value.storeCode = row?.code

		// 清空货位&区域
		formModelData.value.regionId = ""
		formModelData.value.regionId = ""
		formModelData.value.roomCode = ""
		formModelData.value.roomId = null

		// 改变入库仓库后 清一下校验信息
		setTimeout(() => {
			ruleFormRef.value?.clearValidate()
		}, 0)
	}
}

/**
 * 选择入库货位 确定
 * @param btnName
 * @param treeRow
 * @param row
 */
const saveSelectedArea = (
	btnName: string,
	treeRow?: MatStoreRegionTreeVo,
	row?: MatStoreRegionDTO
) => {
	showStoreAddressDrawer.value = false
	if (btnName == "保存") {
		// 区域
		formModelData.value.regionLabel = treeRow?.label
		formModelData.value.regionId = treeRow?.id

		// 货位
		formModelData.value.roomCode = row?.code // 货位label
		formModelData.value.roomId = row?.id // 货位ID
	}
}

onMounted(async () => {
	await getDictByCodeList([
		"INVENTORY_UNIT",
		"MAIN_MATERIALS",
		"MATERIAL_NATURE"
	])
	formModelData.value.useUnit_view =
		dictFilter(
			"INVENTORY_UNIT",
			formModelData.value.useUnit as unknown as string
		)?.subitemName || "---"
	formModelData.value.quality_view =
		dictFilter(
			"MAIN_MATERIALS",
			formModelData.value.quality as unknown as string
		)?.subitemName || "---"

	formModelData.value.attribute_view =
		dictFilter("MATERIAL_NATURE", formModelData.value.attribute)?.subitemName ||
		"---"
})
</script>
<template>
	<div class="drawer-container">
		<div class="drawer-column">
			<div class="rows">
				<Title :title="qualityDrawerLeftTitle" />
				<el-scrollbar>
					<el-form
						style="padding-bottom: 30px"
						class="content form-base"
						ref="ruleFormRef"
						:model="formModelData"
						:rules="rules"
						label-position="top"
					>
						<FormElement :form-element="formEl" :form-data="formModelData" />
					</el-form>
				</el-scrollbar>
			</div>
			<ButtonList
				class="footer"
				:button="formBtnList"
				:loading="drawerBtnLoading"
				@onBtnClick="onFormBtnList"
			/>

			<!-- 选择入库货位 -->
			<Drawer
				:size="modalSize.lg"
				v-model:drawer="showStoreAddressDrawer"
				:destroyOnClose="true"
			>
				<area-storage
					:storeId="formModelData.storeId!"
					:storeName="formModelData.storeName!"
					:storeCode="formModelData.storeCode"
					:regionId="formModelData.regionId"
					:room-id="[formModelData.roomId]"
					@onSaveOrClose="saveSelectedArea"
				/>
			</Drawer>

			<!-- 选择入库仓库 -->
			<Drawer
				:size="modalSize.lg"
				v-model:drawer="showMatStoreDrawer"
				:destroyOnClose="true"
			>
				<store-table @onSave="saveSelectedMatStore" />
			</Drawer>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.drawer-column {
	width: 100%;
}
</style>
