<!-- 交旧入库 详情 -->
<script setup lang="ts">
import { reactive, ref } from "vue"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import LinkTag from "@/app/baseline/views/components/linkTag.vue"
import importDrawer from "./importDrawer.vue"
import roomDrawer from "./roomDrawer.vue"
import { modalSize } from "@/app/baseline/utils/layout-config"
import {
	getInStoreMatStatus,
	MatInStoreStatus
} from "../receivedStored/receivedStored"

import { useTbInit } from "../../../components/tableBase"
import { useDictInit } from "../../../components/dictBase"
import {
	IIdempotentTokenType,
	IIdempotentTokenTypePre,
	IModalType
} from "@/app/baseline/utils/types/common"
import {
	matInStoreWasteOldApplyGetById,
	matInStoreWasteOldApplyPageDetail,
	updateMatInStoreWasteOldApplyInStoreBatch
} from "@/app/baseline/api/store/warehouse/handover"
import {
	MatInStoreWasteOldApplyDetailVo,
	MatInStoreWasteOldApplyItemPageVo,
	MatInStoreWasteOldApplyItemPageVoRequest
} from "@/app/baseline/utils/types/warehouse-handover"
import { DictApi } from "@/app/baseline/api/dict"
import { batchFormatterNumView, toFixedTwo } from "@/app/baseline/utils"
import { IWasteOldType } from "@/app/baseline/utils/types/waste-handover-apply"
import {
	getIdempotentToken,
	getRealLength,
	setString,
	validateAndCorrectInput
} from "@/app/baseline/utils/validate"
import areaStorage from "../receivedStored/areaStorage.vue"
import { findIndex, first, includes, map, round, toNumber } from "lodash-es"
import {
	MatStoreRegionDTO,
	MatStoreRegionTreeVo
} from "@/app/baseline/utils/types/store-manage"
import { useMessageBoxInit } from "../../../components/messageBox"

export interface Props {
	id: string | number //
	model?: string //显示模式 view, edit
}

const props = defineProps<Props>()
const emits = defineEmits(["close", "update"])
const loading = ref(false)
const formBtnList = [{ name: "取消", icon: ["fas", "circle-minus"] }]

const formModal = ref<MatInStoreWasteOldApplyDetailVo>({})
const drawerLoading = ref(false)
const { dictOptions, getDictByCodeList, dictFilter } = useDictInit()
const { showWarnConfirm } = useMessageBoxInit()

/**
 * 左侧 Title 配置
 */
const drawerLeftTitle = {
	name: ["入库单"],
	icon: ["fas", "square-share-nodes"]
}

/**
 * 左侧 表单 配置
 */
const formEl = reactive([
	{ label: "入库单号", name: "code" },
	{ label: "关联交旧申请单", name: "wasteOldApplyCode" },
	{ label: "交旧类型", name: "preBusinessType_view" },
	{ label: "入库类型", name: "type_view" },
	{ label: "入库仓库名称", name: "storeName" },
	{ label: "物资编码", name: "materialCodeNum" },
	{ label: "申请部门", name: "sysOrgId_view" },
	{ label: "申请人", name: "createdBy_view" },
	{ label: "申请时间", name: "createdDate" },
	{ label: "备注说明", name: "remark", needTooltip: true }
])

/**
 * 右侧 title 配置
 */
const drawerRightTitle = {
	name: ["物资明细"],
	icon: ["fas", "square-share-nodes"]
}

/**
 * 详情
 */
const getDetail = () => {
	if (!props.id) {
		return false
	}
	drawerLoading.value = true
	matInStoreWasteOldApplyGetById(props.id as number)
		.then((res: any) => {
			formModal.value = res
		})
		.finally(() => {
			drawerLoading.value = false
		})
}

/**
 * 查询条件 配置
 */
const queryArrList = computed(() => [
	{
		name: "入库状态",
		key: "status",
		placeholder: "请选择入库状态",
		type: "select",
		children: getInStoreMatStatus()
	},
	{
		name: "物资编码",
		key: "materialCode",
		placeholder: "请输入物资编码",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资名称",
		key: "materialLabel",
		placeholder: "请输入物资名称",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "规格型号",
		key: "version",
		placeholder: "请输入规格型号",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资性质",
		key: "attribute",
		type: "select",
		children: dictOptions.value.MATERIAL_NATURE,
		placeholder: "请选择物资性质"
	}
])

// 入库抽屉显隐
const inStoreVisible = ref(false)

const showStoreAddressVisible = ref(false)

/**
 * 编辑后的table row 数据
 */
const editedTableRowStack = ref<any[]>([])

const tbInit = useTbInit<
	MatInStoreWasteOldApplyItemPageVo,
	MatInStoreWasteOldApplyItemPageVoRequest
>()
const {
	tableCache,
	tableData,
	tableRef,
	currentPage,
	pageTotal,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange,
	tableLoading
} = tbInit

/**
 * table 列配置
 */
tbInit.tableProp = computed(() => {
	const defColumn: TableColumnType[] = [
		{ label: "物资编码", prop: "materialCode", width: 130, fixed: "left" },
		{ label: "物资名称", prop: "materialName", minWidth: 150 },
		{ label: "规格型号", prop: "version", width: 130 },
		{
			label: "技术参数",
			prop: "technicalParameter",
			minWidth: 150
		},
		{ label: "物资性质", prop: "attribute", needSlot: true, width: 120 },
		{ label: "库存单位", prop: "useUnit", needSlot: true, width: 80 },
		{
			label: "废旧物资分类",
			prop: "wasteMaterialType", // 字典
			needSlot: true,
			width: 100
		},
		{ label: "主要材质", prop: "quality", needSlot: true }, // 字典
		{
			label: "预估回收重量(KG)",
			prop: "recoveryWeight",
			width: 150,
			align: "right",
			needSlot: true
		},
		{
			label: "批次号",
			prop: "batchNo",
			width: 160
		},
		{
			label: "入库状态",
			prop: "status",
			needSlot: true,
			width: 100,
			fixed: "right"
		},

		{
			label: "可入库数量",
			prop: "canInStoreNum_view",
			width: 90,
			align: "right",
			fixed: "right"
		},
		{
			label: "已入库数量",
			prop: "completeInStoreNum_view",
			width: 90,
			align: "right",
			fixed: "right"
		},
		{
			label: "待入库数量",
			prop: "waitingInStoreNum_view",
			width: 90,
			align: "right",
			fixed: "right"
		},
		{
			label: "本次入库数量",
			prop: "thisInStoreNum",
			width: 120,
			needSlot: true,
			align: "right",
			fixed: "right"
		},
		{
			label: "已入库货位",
			prop: "roomCount",
			needSlot: true,
			width: 90,
			fixed: "right"
		}
	]

	if (formModal.value.preBusinessType === IWasteOldType.matInStore) {
		if (
			formModal.value.status == MatInStoreStatus.allInStore ||
			props.model == IModalType.view
		) {
			return defColumn.filter((v) => v.label !== "本次入库数量")
		} else {
			return defColumn
		}
	} else {
		if (
			formModal.value.status == MatInStoreStatus.allInStore ||
			props.model == IModalType.view
		) {
			return defColumn.filter(
				(v) => !["批次号", "本次入库数量"].includes(v.label)
			)
		} else {
			return defColumn.filter((v) => v.label !== "批次号")
		}
	}
})

/**
 * 格式化数量
 */
watch(tableData, () => {
	batchFormatterNumView(tableData.value)
})

/**
 * 入库按钮 配置
 */
const tableFooterBtns = computed(() => {
	const isStatusClick = selectedTableList.value.every((e) => {
		return e.status != MatInStoreStatus.allInStore
	})
	return [
		{
			name: "入库",
			icon: ["fas", "arrow-down"],
			disabled:
				selectedTableList.value.length == 1 &&
				first(selectedTableList.value)?.status != MatInStoreStatus.allInStore
					? false
					: true
		},
		{
			name: "批量入库",
			icon: ["fas", "arrow-down"],
			disabled:
				selectedTableList.value.length > 0 && isStatusClick ? false : true
		}
	]
})

function handleTabFooterAction(btnName?: string) {
	if (btnName === "入库") {
		inStoreVisible.value = true
	} else if (btnName === "批量入库") {
		/* const canNum = selectedTableList.value.every((e) => {
			return e.thisInStoreNum > 0
		}) */

		showStoreAddressVisible.value = true
	}
}

/**
 * 物资明细 table 行样式
 *
 * 库存不足物资，用红色标记该行
 */
const errorGoodsIdList = ref<number[]>([])
function tbCellClassName({ row }: any) {
	return includes(errorGoodsIdList.value, row.id) ? "error" : ""
}

async function handleSaveRoom(
	btnName: string,
	treeRow?: MatStoreRegionTreeVo,
	row?: MatStoreRegionDTO,
	callback?: any
) {
	if (btnName == "保存") {
		try {
			await showWarnConfirm("确定将所选物资交旧入库吗？")
			const itemList = map(selectedTableList.value, (v) => ({
				id: v.id,
				completeNum: v.thisInStoreNum
			}))

			const idempotentToken = getIdempotentToken(
				IIdempotentTokenTypePre.other,
				IIdempotentTokenType.handoverStoredReceived,
				formModal.value.id
			)

			const { code, msg, data } =
				await updateMatInStoreWasteOldApplyInStoreBatch(
					{
						roomId: row?.id,
						itemList
					},
					idempotentToken
				)

			if (data && code != 200) {
				errorGoodsIdList.value = data || []
				ElMessage.error(msg)
				getTableData()
			} else {
				ElMessage.success("操作成功")
				getTableData()
				getDetail()
				emits("update")

				for (let i = 0; i < selectedTableList.value.length; i++) {
					//  更新 tableCache
					const rowIdx = findIndex(
						tableCache,
						(v) => v.id === selectedTableList.value[i].id
					)
					if (rowIdx !== -1) {
						tableCache.splice(rowIdx, 1, { ...selectedTableList.value[i] })
					}

					const delRowIdx = findIndex(
						editedTableRowStack.value,
						(v) => v.id === selectedTableList.value[i].id
					)
					if (rowIdx !== -1) {
						editedTableRowStack.value.splice(delRowIdx, 1)
					}
				}
			}
		} finally {
			callback?.()
		}
	}

	showStoreAddressVisible.value = false
}

/**
 * tab切换 table数据源
 * @param data
 */
const getTableData = (data?: Record<string, any>) => {
	if (props.id) {
		fetchFunc.value = matInStoreWasteOldApplyPageDetail

		currentPage.value = 1
		tableRef.value?.resetCurrentPage()

		fetchParam.value = {
			applyId: props.id,
			sord: "desc",
			sidx: "createdDate",
			...data
		}
		fetchTableData()
	}
}

/**
 * 关联货位 操作
 */
const roomDetailVisible = ref(false)
const currentRow = ref<MatInStoreWasteOldApplyItemPageVo>({})
const onRowRoom = (row: MatInStoreWasteOldApplyItemPageVo) => {
	if (!row.roomCount) {
		return false
	}
	currentRow.value = row
	roomDetailVisible.value = true
}

/**
 * 入库 handler
 * @param msg
 */
const closeInstoreDrawer = (msg?: string) => {
	// 更新
	if (msg === "save") {
		fetchTableData()
		emits("update")
		getDetail()

		for (let i = 0; i < selectedTableList.value.length; i++) {
			//  更新 tableCache
			const rowIdx = findIndex(
				tableCache,
				(v) => v.id === selectedTableList.value[i].id
			)
			if (rowIdx !== -1) {
				tableCache.splice(rowIdx, 1, { ...selectedTableList.value[i] })
			}

			const delRowIdx = findIndex(
				editedTableRowStack.value,
				(v) => v.id === selectedTableList.value[i].id
			)
			if (rowIdx !== -1) {
				editedTableRowStack.value.splice(delRowIdx, 1)
			}
		}
	}
	inStoreVisible.value = false
}

/**
 * 输入框失焦 handler
 *
 * 失焦后，记录编辑过的数据，用于保存任务数据
 */
function handleInputBlur(e: any) {
	if (!editedTableRowStack.value.length) {
		editedTableRowStack.value.push({ ...e })
		return
	}

	/**
	 * 队列存在数据，验证重复
	 *
	 * - 不重复->push
	 * - 重复->更新该条数据
	 */
	const rowIdx = findIndex(editedTableRowStack.value, (v) => v.id === e.id)
	if (rowIdx !== -1) {
		editedTableRowStack.value.splice(rowIdx, 1, { ...e })
		return
	}

	editedTableRowStack.value.push({ ...e })
}

/**
 * 翻页 回显已编辑的数据
 */
watch(
	() => tableData.value,
	() => {
		if (editedTableRowStack.value.length) {
			editedTableRowStack.value.map((e) => {
				const rowIdx = findIndex(tableData.value, (v) => v.id === e.id)
				if (rowIdx !== -1) {
					tableData.value.splice(rowIdx, 1, { ...e })
				}
			})
		}
	}
)

function validateInStoreNum(e: any) {
	const thisInStoreNum = toNumber(e.thisInStoreNum)

	// 最大本次入库数量
	const waitingInStoreNum = toNumber(e.waitingInStoreNum)

	e.thisInStoreNum = thisInStoreNum
	if (waitingInStoreNum < thisInStoreNum) {
		ElMessage.warning("本次入库数量不能大于待入库数量")
		e.thisInStoreNum = round(waitingInStoreNum, 4)
	} else if (thisInStoreNum <= 0) {
		const oldRow = tableCache.find((v) => v.id == e.id)
		e.thisInStoreNum = oldRow.thisInStoreNum

		ElMessage.warning("本次入库数量不能小于等于0！")
	}

	handleInputBlur(e)
}

onMounted(() => {
	getDictByCodeList(["INVENTORY_UNIT", "MAIN_MATERIALS", "MATERIAL_NATURE"])
	getDetail()
	getTableData()
})
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<!-- 左侧表单区域 -->
		<div class="drawer-column left">
			<div class="rows">
				<Title :title="drawerLeftTitle" />
				<el-descriptions size="small" :column="1" border class="content">
					<template>
						<el-descriptions-item
							v-for="(el, index) in formEl"
							label-align="center"
							:label="el.label"
							:key="index"
						>
							<span v-if="el.needTooltip">
								<el-tooltip
									effect="dark"
									:content="formModal?.[el.name]"
									:disabled="
										getRealLength(formModal?.[el.name]) <= 100 ? true : false
									"
								>
									{{
										getRealLength(formModal?.[el.name]) > 100
											? setString(formModal?.[el.name], 100)
											: formModal?.[el.name] || "---"
									}}
								</el-tooltip>
							</span>
							<span v-else>
								{{
									formModal[el.name] || formModal[el.name] == 0
										? formModal[el.name]
										: "---"
								}}
							</span>
						</el-descriptions-item>
					</template>
				</el-descriptions>
			</div>
		</div>

		<!-- 右侧table区域 -->
		<div class="drawer-column right">
			<Title :title="drawerRightTitle" />
			<div class="rows">
				<el-scrollbar class="tab-mat editor-table-wrapper">
					<Query
						:queryArrList="queryArrList"
						@getQueryData="getTableData"
						class="custom-q"
					/>
					<PitayaTable
						ref="tableRef"
						:columns="(tbInit.tableProp as any)"
						:table-data="tableData"
						:need-index="true"
						:need-pagination="true"
						:single-select="props.model === 'edit' ? false : false"
						:need-selection="props.model === 'edit' ? true : false"
						:total="pageTotal"
						@onSelectionChange="selectedTableList = $event"
						@on-current-page-change="onCurrentPageChange"
						:table-loading="tableLoading"
						:cell-class-name="tbCellClassName"
					>
						<!-- 预估回收重量 -->
						<template #recoveryWeight="{ rowData }">
							{{ toFixedTwo(rowData.recoveryWeight) }}
						</template>

						<!-- 物资性质 -->
						<template #attribute="{ rowData }">
							<dict-tag
								:options="DictApi.getMatAttr()"
								:value="rowData.attribute"
							/>
						</template>

						<!-- 库存单位 -->
						<template #useUnit="{ rowData }">
							{{
								dictFilter("INVENTORY_UNIT", rowData.useUnit)?.subitemName ||
								"---"
							}}
						</template>

						<!-- 废旧物资分类 -->
						<template #wasteMaterialType="{ rowData }">
							<dict-tag
								:options="DictApi.getWasteMaterialType()"
								:value="rowData.wasteMaterialType"
							/>
						</template>

						<!-- 主要材质 -->
						<template #quality="{ rowData }">
							{{
								dictFilter("MAIN_MATERIALS", rowData.quality)?.subitemName ||
								"---"
							}}
						</template>

						<!-- 入库状态 -->
						<template #status="{ rowData }">
							<dict-tag
								:options="getInStoreMatStatus()"
								:value="rowData.status"
							/>
						</template>

						<template #thisInStoreNum="{ rowData }">
							<el-input
								class="no-arrows"
								v-model="rowData.thisInStoreNum"
								@click.stop
								@input="
									rowData.thisInStoreNum = validateAndCorrectInput($event)
								"
								@change="validateInStoreNum(rowData)"
								v-if="rowData.status != MatInStoreStatus.allInStore"
							/>
							<span v-else>{{ rowData.thisInStoreNum_view }}</span>
						</template>

						<!-- 入库货位 -->
						<template #roomCount="{ rowData }">
							<link-tag
								:value="rowData.roomCount"
								@click.stop="onRowRoom(rowData)"
							/>
						</template>
						<template #footerOperateLeft v-if="props.model == IModalType.edit">
							<ButtonList
								class="btn-list"
								:is-not-radius="true"
								:button="tableFooterBtns"
								@on-btn-click="handleTabFooterAction"
							/>
						</template>
					</PitayaTable>
				</el-scrollbar>
			</div>

			<ButtonList
				class="footer"
				:button="formBtnList"
				:loading="loading"
				@on-btn-click="emits('close')"
			/>

			<!-- 交旧入库 -->
			<Drawer
				:size="modalSize.sm"
				v-model:drawer="inStoreVisible"
				:destroyOnClose="true"
			>
				<import-drawer
					:inStoreRow="selectedTableList"
					:detailInfo="formModal"
					@on-save-or-close="closeInstoreDrawer"
				/>
			</Drawer>

			<!-- 查看货位 -->
			<Drawer
				:size="modalSize.lg"
				v-model:drawer="roomDetailVisible"
				:destroyOnClose="true"
			>
				<room-drawer
					:id="currentRow.id!"
					:row="currentRow"
					:detailInfo="formModal"
					@close="roomDetailVisible = false"
				/>
			</Drawer>

			<!-- 选择入库货位 -->
			<Drawer
				:size="modalSize.lg"
				v-model:drawer="showStoreAddressVisible"
				:destroyOnClose="true"
			>
				<area-storage
					:storeId="formModal.storeId!"
					:storeName="formModal.storeName!"
					:storeCode="formModal?.storeCode"
					:regionId="first(selectedTableList)?.regionId"
					:room-id="[first(selectedTableList)?.roomId]"
					@onSaveOrClose="handleSaveRoom"
				/>
			</Drawer>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.custom-q {
	margin: 10px 0px -10px 10px !important;
}
.drawer-container {
	.left {
		width: 330px;
	}

	.right {
		width: calc(100% - 330px);
	}

	.tab-mat {
		height: calc(100% - 43px);
	}
}

.editor-table-wrapper {
	&:deep(.pitaya-table) {
		.error {
			.column-inner-item,
			.cell,
			.el-input__inner {
				color: red;
			}
		}
	}
}
</style>
