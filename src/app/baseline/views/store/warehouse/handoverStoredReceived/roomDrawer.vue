<script setup lang="ts">
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import { DictApi } from "@/app/baseline/api/dict"
import { reactive, ref } from "vue"

import { useTbInit } from "../../../components/tableBase"
import { getWasteOldItemRoomInfoPage } from "@/app/baseline/api/store/warehouse/handover"
import {
	MatInStoreWasteOldApplyDetailVo,
	MatInStoreWasteOldApplyItemPageVo,
	MatInStoreWasteOldItemRoomPageVo,
	MatInStoreWasteOldItemRoomPageVoRequest
} from "@/app/baseline/utils/types/warehouse-handover"
import { useDictInit } from "../../../components/dictBase"
import { batchFormatterNumView } from "@/app/baseline/utils"
import { getRealLength, setString } from "@/app/baseline/utils/validate"

export interface Props {
	id: string | number //
	row: MatInStoreWasteOldApplyItemPageVo
	detailInfo: MatInStoreWasteOldApplyDetailVo
}

const props = defineProps<Props>()
const emits = defineEmits(["close"])

const { dictFilter, getDictByCodeList } = useDictInit()

const loading = ref(false)
const formBtnList = [{ name: "取消", icon: ["fas", "circle-minus"] }]

const formModal = ref<
	MatInStoreWasteOldApplyItemPageVo & MatInStoreWasteOldApplyDetailVo
>({
	...props.detailInfo,
	...props.row
})

/**
 * 左侧 title 配置
 */
const drawerLeftTitle = {
	name: ["物资信息"],
	icon: ["fas", "square-share-nodes"]
}

/**
 * 左侧 表单 配置
 */
const formEl = reactive([
	{ label: "物资编码", name: "materialCode" },
	{ label: "物资名称", name: "materialName" },
	{ label: "品牌型号", name: "version" },
	{ label: "技术参数", name: "technicalParameter", needTooltip: true },
	{ label: "物资性质", name: "attribute" },
	{ label: "交旧申请单号", name: "wasteOldApplyCode" },
	{ label: "入库单号", name: "code" },
	{ label: "入库仓库名称", name: "storeName" },
	{ label: "入库数量", name: "completeInStoreNum_view" }
])

/**
 * 左侧 title 配置
 */
const drawerRightTitle = {
	name: ["入货货位"],
	icon: ["fas", "square-share-nodes"]
}

const {
	tableData,
	tableProp,
	tableRef,
	tableLoading,
	currentPage,
	pageTotal,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange,
	onDataSelected
} = useTbInit<
	MatInStoreWasteOldItemRoomPageVo,
	MatInStoreWasteOldItemRoomPageVoRequest
>()

/**
 * table 列 配置
 */
tableProp.value = [
	{ label: "入库区域编码", prop: "regionCode" },
	{ label: "入库货位编码", prop: "roomCode" },
	{ label: "库存单位", prop: "useUnit", needSlot: true },
	{ label: "入库数量", prop: "num_view", align: "right" },
	{ label: "操作人", prop: "createdBy_view" },
	{ label: "入库时间", prop: "inStoreTime" }
]

/**
 * 格式化数量
 */
watch(tableData, () => {
	batchFormatterNumView(tableData.value)
})

/**
 * table 数据源
 * @param data
 */
const getTableData = (data?: any) => {
	if (props.id) {
		fetchFunc.value = getWasteOldItemRoomInfoPage

		currentPage.value = 1
		tableRef.value?.resetCurrentPage()

		fetchParam.value = {
			itemId: props.id,
			sord: "desc",
			sidx: "createdDate",
			...data
		} // applyItemId:领料出库明细 ID

		fetchTableData()
	}
}

const drawerLoading = ref(false)

onMounted(() => {
	getTableData()
	getDictByCodeList(["INVENTORY_UNIT"])
	console.log(props.row, props.detailInfo)
})
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<!-- 左侧表单区域 -->
		<div class="drawer-column left">
			<div class="rows">
				<Title :title="drawerLeftTitle" />
				<el-descriptions size="small" :column="1" border class="content">
					<template>
						<el-descriptions-item
							v-for="(el, index) in formEl"
							label-align="center"
							:label="el.label"
							:key="index"
						>
							<dict-tag
								v-if="el.name === 'attribute'"
								:options="DictApi.getMatAttr()"
								:value="formModal.attribute"
							/>
							<span v-else-if="el.needTooltip">
								<el-tooltip
									effect="dark"
									:content="formModal?.[el.name]"
									:disabled="
										getRealLength(formModal?.[el.name]) <= 100 ? true : false
									"
								>
									{{
										getRealLength(formModal?.[el.name]) > 100
											? setString(formModal?.[el.name], 100)
											: formModal?.[el.name] || "---"
									}}
								</el-tooltip>
							</span>
							<span v-else>
								{{
									formModal[el.name] || formModal[el.name] == 0
										? formModal[el.name]
										: "---"
								}}
							</span>
						</el-descriptions-item>
					</template>
				</el-descriptions>
			</div>
		</div>

		<!-- 右侧table区域 -->
		<div class="drawer-column right">
			<div class="rows">
				<Title :title="drawerRightTitle" />
				<el-scrollbar class="tab-mat">
					<PitayaTable
						ref="tableRef"
						:columns="tableProp"
						:table-data="tableData"
						:need-index="true"
						:need-pagination="true"
						:single-select="false"
						:need-selection="false"
						:total="pageTotal"
						@onSelectionChange="onDataSelected"
						@on-current-page-change="onCurrentPageChange"
						:table-loading="tableLoading"
					>
						<template #useUnit>
							{{
								dictFilter(
									"INVENTORY_UNIT",
									props.row.useUnit as unknown as string
								)?.subitemName || "---"
							}}
						</template>
					</PitayaTable>
				</el-scrollbar>
			</div>

			<ButtonList
				class="footer"
				:button="formBtnList"
				:loading="loading"
				@on-btn-click="emits('close')"
			/>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.drawer-container {
	.left {
		width: 340px;
	}

	.right {
		width: calc(100% - 340px);
	}

	.tab-mat {
		height: calc(100% - 43px);
	}
}
</style>
