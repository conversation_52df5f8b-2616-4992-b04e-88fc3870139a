<script setup lang="ts">
import { FormElementType } from "@/app/baseline/views/components/define"
import { onMounted, reactive, ref, toRef } from "vue"

import TableFile from "@/app/baseline/views/components/tableFile.vue"
import { fileBusinessType } from "@/app/baseline/api/dict"
import { useTbInit } from "../../../components/tableBase"
import { useDictInit } from "../../../components/dictBase"

import {
	getCheckPlanTask,
	listCheckPlanTaskGoodsPaged
} from "@/app/baseline/api/store/inventory-in-store-api"

import {
	CheckTaskProfitInStoreMaterialPageVoQuery,
	CheckTaskProfitInStoreMaterialPageVo,
	CheckTaskProfitInStoreDetailVo
} from "@/app/baseline/utils/types/store-inventory-in-store"
import { IModalType } from "../../../../utils/types/common"
import { batchFormatterNumView } from "@/app/baseline/utils"
import { DictApi } from "@/app/baseline/api/dict"
import DictTag from "@/app/baseline/views/components/dictTag.vue"

export interface Props {
	id: string | number //
	model?: string //显示模式 view
}
const props = defineProps<Props>()
const emits = defineEmits(["close"])
const currentId = toRef(props, "id")
const loading = ref(false)
const childTableLoading = ref(false)

/**
 * 按钮 配置
 */
const formBtnListView = [{ name: "取消", icon: ["fas", "circle-minus"] }]

/**
 * 表单数据源
 */
const formModal = ref<CheckTaskProfitInStoreDetailVo>({})

/**
 * 左侧title 配置
 */
const drawerLeftTitle = {
	name: ["盘盈入库"],
	icon: ["fas", "square-share-nodes"]
}

/**
 * 左侧表单 配置
 */
const formEl = reactive<FormElementType[]>([
	{ label: "盘盈入库单号", name: "inStoreCode" },
	{ label: "关联盘点计划", name: "planCode" },
	{ label: "盘点计划名称", name: "planName" },
	{ label: "关联盘点任务", name: "taskCode" },
	{ label: "入库仓库名称", name: "storeName" },
	{ label: "入库物资编码", name: "materialCodeCount" },
	{ label: "入库时间", name: "inStoreDate" }
])

/**
 * 右侧title 配置
 */
const drawerRightTitle = {
	name: ["扩展信息"],
	icon: ["fas", "square-share-nodes"]
}

/**
 * 扩展栏标签页切换
 */
const tabList = ["物资明细", "相关附件"]
const activeTab = ref<number>(0)
const handleTabChange = (index: number) => {
	activeTab.value = index

	if (index === 0) {
		fetchParam.value = {
			taskId: formModal.value.taskId,
			applyId: formModal.value.applyId,
			sord: "desc",
			sidx: "createdDate"
		}

		pageSize.value = 20

		getTableData()
	}
}
/**
 *表 单按钮 操作
 * @param btnName
 */
const onFormBtnList = async (btnName: string | undefined) => {
	if (btnName === "取消") {
		emits("close")
	}
}

const { dictOptions, getDictByCodeList, dictFilter } = useDictInit()

const {
	tableData,
	tableProp,
	tableRef,
	tableLoading,
	currentPage,
	pageTotal,
	pageSize,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange,
	onDataSelected
} = useTbInit<
	CheckTaskProfitInStoreMaterialPageVo,
	CheckTaskProfitInStoreMaterialPageVoQuery
>()

/**
 * table列 配置
 */
tableProp.value = [
	{ label: "物资编码", prop: "materialCode", width: 130 },
	{ label: "物资名称", prop: "materialLabel" },
	{ label: "规格型号", prop: "version" },
	{ label: "技术参数", prop: "technicalParameter" },
	{ label: "物资性质", prop: "attribute", needSlot: true, width: 120 },
	{ label: "库存单位", prop: "useUnit", needSlot: true, width: 90 },
	{ label: "入库数量", prop: "inStoreNum_view", align: "right" },
	{ label: "入库区域名称", prop: "regionLabel" },
	{ label: "入库货位编码", prop: "roomCode", width: 100 }
]

/**
 * 格式化数量
 */
watch(tableData, () => {
	batchFormatterNumView(tableData.value as any[])
})

/**
 * 查询条件 配置
 */
const queryArrList = computed(() => [
	{
		name: "物资编码",
		key: "materialCode",
		placeholder: "请输入物资编码",
		type: "input",
		enableFuzzy: true
	},
	{
		name: "物资名称",
		key: "materialLabel",
		placeholder: "请输入物资名称",
		type: "input",
		enableFuzzy: true
	},
	{
		name: "规格型号",
		key: "version",
		placeholder: "请输入规格型号",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资性质",
		key: "attribute",
		type: "select",
		children: dictOptions.value.MATERIAL_NATURE,
		placeholder: "请选择物资性质"
	}
])

/**
 * 数据源
 * @param data
 */
const getTableData = (data?: Record<string, any>) => {
	if (formModal.value.taskId) {
		fetchFunc.value = listCheckPlanTaskGoodsPaged

		currentPage.value = 1
		tableRef.value?.resetCurrentPage()

		fetchParam.value = {
			...fetchParam.value,
			taskId: formModal.value.taskId,
			applyId: formModal.value.applyId,
			sord: "desc",
			sidx: "createdDate",
			...data
		}
		fetchTableData()
	}
}
const drawerLoading = ref(false)
async function getDetail() {
	if (!props.id) {
		return
	}
	drawerLoading.value = true
	try {
		formModal.value = await getCheckPlanTask(props.id)
	} finally {
		drawerLoading.value = false
	}
}

onMounted(async () => {
	getDictByCodeList(["INVENTORY_UNIT", "MATERIAL_NATURE"])
	await getDetail()
	getTableData()
})
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<!-- 左侧表单区域 -->
		<div class="drawer-column left">
			<div class="rows">
				<Title :title="drawerLeftTitle" />
				<el-descriptions size="small" :column="1" border class="content">
					<template>
						<el-descriptions-item
							v-for="(el, index) in formEl"
							label-align="center"
							:label="el.label"
							:key="index"
						>
							<span>
								{{
									formModal[el.name] || formModal[el.name] == 0
										? formModal[el.name]
										: "---"
								}}
							</span>
						</el-descriptions-item>
					</template>
				</el-descriptions>
			</div>
		</div>

		<!-- 右侧table区域 -->
		<div class="drawer-column right">
			<div class="rows">
				<Title :title="drawerRightTitle">
					<Tabs class="tabs" :tabs="tabList" @on-tab-change="handleTabChange" />
				</Title>

				<el-scrollbar class="tab-mat" v-if="activeTab === 0">
					<Query
						:queryArrList="queryArrList"
						@getQueryData="getTableData"
						class="custom-q"
					/>
					<PitayaTable
						ref="tableRef"
						:columns="tableProp"
						:table-data="tableData"
						:need-index="true"
						:need-pagination="true"
						:single-select="false"
						:need-selection="false"
						:total="pageTotal"
						@onSelectionChange="onDataSelected"
						@on-current-page-change="onCurrentPageChange"
						:table-loading="tableLoading"
					>
						<!-- 物资性质 -->
						<template #attribute="{ rowData }">
							<dict-tag
								:options="DictApi.getMatAttr()"
								:value="rowData.attribute"
							/>
						</template>

						<template #useUnit="{ rowData }">
							{{
								dictFilter("INVENTORY_UNIT", rowData.useUnit)?.subitemName ||
								"---"
							}}
						</template>
					</PitayaTable>
				</el-scrollbar>

				<div v-if="activeTab === 1">
					<TableFile
						:business-type="fileBusinessType.inventoryJob"
						:business-id="currentId"
						:table-loading="childTableLoading"
						:mod="IModalType.view"
					/>
				</div>
			</div>
			<ButtonList
				class="footer"
				:button="formBtnListView"
				:loading="loading"
				@on-btn-click="onFormBtnList"
			/>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.drawer-container {
	.left {
		width: 340px;
	}

	.right {
		width: calc(100% - 340px);
	}

	.tab-mat {
		height: calc(100% - 43px);
	}
}
.custom-q {
	margin: 10px 0px -10px 10px !important;
}

.el-input-number {
	width: 95%;
}
</style>
