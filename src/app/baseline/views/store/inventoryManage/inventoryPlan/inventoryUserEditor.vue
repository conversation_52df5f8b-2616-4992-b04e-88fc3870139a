<!-- 盘点计划-指定盘点人员 -->
<template>
	<div class="drawer-container">
		<div class="drawer-column" style="width: 310px">
			<Title :title="titleConf" />

			<el-form
				class="content"
				:model="formData"
				:rules="formRules"
				ref="formRef"
				label-position="top"
				label-width="100px"
			>
				<form-element :form-element="formEls" :form-data="formData" />
			</el-form>

			<button-list
				class="footer"
				:button="btnConf"
				:loading="btnLoading"
				@on-btn-click="handleBtnClick"
			/>
		</div>

		<Drawer
			v-model:drawer="userSelectorVisible"
			:size="modalSize.md"
			destroy-on-close
		>
			<user-selector
				:table-fetch-params="{ selectedUserNames }"
				:table-api="listInventoryJobUserPaged"
				:selected-ids="[
					editingFormItemProp == 'firstCheckRealName'
						? formData.firstCheckUserName
						: formData.secondCheckUserName
				]"
				@close="userSelectorVisible = false"
				@save="handleSelectUser"
			/>
		</Drawer>
	</div>
</template>

<script setup lang="ts">
import { requiredValidator } from "@/app/baseline/utils/validate"
import FormElement from "../../../components/formElement.vue"
import { FormElementType } from "../../../components/define"
import UserSelector from "../../components/userSelector.vue"
import { modalSize } from "@/app/baseline/utils/layout-config"
import {
	MatStoreCheckPlanTaskPageVo,
	MatStoreCheckPlanTaskUserUpdateDTO
} from "@/app/baseline/utils/types/inventory-manage"
import { first } from "lodash-es"
import { SystemUserVo } from "@/app/baseline/utils/types/system"
import { FormInstance } from "element-plus"
import { updateInventoryPlanWarehouseUsers } from "@/app/baseline/api/store/inventory-plan-api"
import { listInventoryJobUserPaged } from "@/app/baseline/api/system"
import { map } from "lodash-es"

const props = defineProps<{
	/**
	 * 盘点计划仓库数据
	 */
	planWarehouseData: MatStoreCheckPlanTaskPageVo[]

	isSecondCheck?: number // 是否复盘 1 是，0 否
}>()

const emit = defineEmits<{
	(e: "close"): void

	/**
	 * 保存人员后
	 */
	(e: "saved"): void
}>()

const titleConf = computed(() => ({
	name: ["指定盘点人员"],
	icon: ["fas", "square-share-nodes"]
}))

const userSelectorVisible = ref(false)

const formRef = ref<FormInstance>()

const formData = ref<MatStoreCheckPlanTaskUserUpdateDTO>({})

const formRules = computed(() => {
	if (props.isSecondCheck == 1) {
		return {
			firstCheckRealName: requiredValidator("盘点人员"),
			secondCheckRealName: requiredValidator("复核人员")
		}
	} else {
		return { firstCheckRealName: requiredValidator("盘点人员") }
	}
})

/**
 * 获取选中的用户 username
 */
const selectedUserNames = computed(() => {
	const { firstCheckUserName, secondCheckUserName } = formData.value ?? {}
	const ls = []

	if (editingFormItemProp.value === "firstCheckRealName") {
		// 过滤复盘人员
		ls.push(secondCheckUserName)
	} else {
		// 过滤初盘人员
		ls.push(firstCheckUserName)
	}

	return ls.toString()
})

/**
 * 正在编辑中的表单项目 prop
 */
const editingFormItemProp = ref<"firstCheckRealName" | "secondCheckRealName">()

const formEls: FormElementType[][] = [
	[
		{
			label: "盘点人员",
			name: "firstCheckRealName",
			type: "drawer",
			clickApi: () => {
				userSelectorVisible.value = true
				editingFormItemProp.value = "firstCheckRealName"
			}
		},
		{
			label: "复核人员",
			name: "secondCheckRealName",
			type: "drawer",
			hidden: props.isSecondCheck == 1 ? false : true,
			clickApi: () => {
				userSelectorVisible.value = true
				editingFormItemProp.value = "secondCheckRealName"
			}
		}
	]
]

const btnLoading = ref(false)

const btnConf = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "保存",
		icon: ["fas", "floppy-disk"]
	}
]

onBeforeMount(() => {
	/**
	 * 1. 指定盘点人员时，盘点人员默认带出盘点仓库的库管员，可进行编辑
	 * 2. 当盘点仓库有多个库管员时，默认填写第一个库管员
	 * 3.当选多个仓库指定盘点人员时，无默认值
	 */

	if (props.planWarehouseData.length == 1) {
		const {
			firstCheckRealName,
			firstCheckUserName,
			secondCheckRealName,
			secondCheckUserName,
			storeManageUserName,
			storeManage
		} = props.planWarehouseData[0]

		const storeManageUserNameFirst = first(storeManageUserName?.split(","))
		const storeManageRealNameFirst = first(storeManage?.split(","))
		formData.value.firstCheckRealName = (
			firstCheckRealName ? firstCheckRealName : storeManageRealNameFirst
		) as any
		formData.value.firstCheckUserName = (
			firstCheckUserName ? firstCheckUserName : storeManageUserNameFirst
		) as any
		formData.value.secondCheckRealName = secondCheckRealName
		formData.value.secondCheckUserName = secondCheckUserName
	}
})

onMounted(() => {
	formRef.value?.clearValidate()
})

function handleBtnClick(name?: string) {
	if (name === "取消") {
		emit("close")
		return
	}

	// 保存人员编辑
	if (!formRef.value) return

	formRef.value.validate(async (valid) => {
		if (!valid) return

		btnLoading.value = true

		const taskIdList = map(props.planWarehouseData, ({ id }) => id) || []

		try {
			await updateInventoryPlanWarehouseUsers({
				...formData.value,
				taskIdList
			} as any)

			ElMessage.success("操作成功")
			emit("saved")
		} finally {
			btnLoading.value = false
		}
	})
}

/**
 * 人员选择 handler
 */
function handleSelectUser(e?: SystemUserVo[]) {
	const { realname, username } = first(e) ?? {}

	if (editingFormItemProp.value === "firstCheckRealName") {
		// 初盘人指定
		formData.value.firstCheckUserName = username
		formData.value.firstCheckRealName = realname
	} else {
		// 复盘人指定
		formData.value.secondCheckUserName = username
		formData.value.secondCheckRealName = realname
	}
	userSelectorVisible.value = false
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
