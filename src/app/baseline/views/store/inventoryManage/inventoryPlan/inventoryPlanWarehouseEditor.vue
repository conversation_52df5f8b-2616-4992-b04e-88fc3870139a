<template>
	<Query
		:query-arr-list="(queryArrList as any)"
		@get-query-data="getTableData"
		style="margin: 10px 10px -10px"
	/>

	<pitaya-table
		ref="tableRef"
		:columns="(tbInit.tableProp as any)"
		:table-data="tableData"
		:need-selection="true"
		:single-select="false"
		:need-index="true"
		:need-pagination="true"
		:total="pageTotal"
		:table-loading="tableLoading"
		:max-height="maxTableHeight"
		:cell-class-name="tbCellClassName"
		@on-selection-change="selectedTableList = $event"
		@on-current-page-change="onCurrentPageChange"
	>
		<!-- 仓库级别 -->
		<template #storeLevel="{ rowData }">
			<color-tag
				:bg-color="warehouseTypeTagColorMap[rowData.storeLevel as IWarehouseType]"
			>
				{{ dictFilter("STORE_LEVEL", rowData.storeLevel)?.label }}
			</color-tag>
		</template>

		<!-- 仓库类型 -->
		<template #storeType="{ rowData }">
			<color-tag
				:bg-color="warehouseTypeTagColorMap[rowData.storeType as IWarehouseType]"
			>
				{{ dictFilter("STORE_TYPE", rowData.storeType)?.label }}
			</color-tag>
		</template>

		<!-- 线路 -->
		<template #storeLineId="{ rowData }">
			<line-tag :options="lineList" :value="rowData.storeLineId" />
		</template>

		<!-- 盘点状态 -->
		<template #status="{ rowData }">
			<color-tag
				border
				v-bind="inventoryCheckStatusColorConf[rowData.status as IInventoryStatus]"
			>
				{{ dictFilter("CHECK_TASK_STATUS", rowData.status)?.label }}
			</color-tag>
		</template>

		<!-- 操作 -->
		<template #actions="{ rowData }">
			<!-- 编辑：编辑仓库下已选择的物资数据 -->
			<el-button
				v-if="tableActionBtnStatus.editVisible"
				v-btn
				link
				@click.stop="showGoodsEditor(rowData)"
			>
				<font-awesome-icon
					:icon="['fas', 'pen-to-square']"
					style="color: var(--pitaya-btn-background)"
				/>
				<span class="table-inner-btn">编辑</span>
			</el-button>

			<el-button
				v-if="tableActionBtnStatus.viewVisible"
				v-btn
				link
				@click.stop="showGoodsEditor(rowData, IModalType.view)"
			>
				<font-awesome-icon
					:icon="['fas', 'eye']"
					style="color: var(--pitaya-btn-background)"
				/>
				<span class="table-inner-btn">查看</span>
			</el-button>

			<el-button
				v-if="tableActionBtnStatus.removeVisible"
				v-btn
				link
				@click.stop="delWarehouse(rowData)"
			>
				<font-awesome-icon
					:icon="['fas', 'trash-can']"
					style="color: var(--pitaya-btn-background)"
				/>
				<span class="table-inner-btn">移除</span>
			</el-button>
		</template>

		<template #footerOperateLeft>
			<button-list-fixed
				:is-not-radius="true"
				:button="tbBtnConf"
				:loading="tbBtnLoading"
				@on-btn-click="handleTbBtnClick"
			/>
		</template>
	</pitaya-table>

	<!-- 仓库选择器 -->
	<Drawer
		v-model:drawer="warehouseSelectorVisible"
		:size="modalSize.lg"
		destroy-on-close
	>
		<!-- prettier-ignore -->
		<store-table
			:plan-id="props.planId"
			:plan-type="props.planType"
			:table-api="listInventoryPlanStoreSelectorOptions"
			:table-api-params="{ checkPlanId: planId }"
			multiple
			@on-save="(handleStoreSelect as any)"
		/>
	</Drawer>

	<!-- 盘点仓库物资编辑器 -->
	<Drawer
		v-model:drawer="goodsEditorVisible"
		:size="modalSize.lg"
		destroy-on-close
	>
		<inventory-goods-editor
			:plan-warehouse-data="first(selectedTableList)"
			:mode="goodsEditorMode"
			:plan-type="props.planType"
			:plan-task-id="editingTableRowId"
			:detail-id-list="props.detailIdList"
			@close="goodsEditorVisible = false"
			@saved="handleGoodsChanged"
		/>
	</Drawer>

	<!-- 人员编辑器 -->
	<Drawer
		v-model:drawer="userEditorVisible"
		:size="modalSize.sm"
		destroy-on-close
	>
		<inventory-user-editor
			:isSecondCheck="props.isSecondCheck"
			:plan-warehouse-data="(selectedTableList as MatStoreCheckPlanTaskPageVo[])"
			@close="userEditorVisible = false"
			@saved="handleUserEditorSaved"
		/>
	</Drawer>
</template>

<script setup lang="ts">
import { BaseLineSysApi } from "@/app/baseline/api/system"
import { useDictInit } from "../../../components/dictBase"
import { useMessageBoxInit } from "../../../components/messageBox"
import {
	IInventoryStatus,
	IInventoryType,
	MatStoreCheckPlanTaskAddStoreListParams,
	MatStoreCheckPlanTaskPageReqParams,
	MatStoreCheckPlanTaskPageVo
} from "@/app/baseline/utils/types/inventory-manage"
import {
	MatStoreVo,
	IWarehouseType
} from "@/app/baseline/utils/types/store-manage"
import { useTbInit } from "../../../components/tableBase"
import {
	inventoryCheckStatusColorConf,
	warehouseTypeTagColorMap
} from "@/app/baseline/utils/colors"
import { modalSize } from "@/app/baseline/utils/layout-config"
import ColorTag from "../../components/colorTag.vue"
import LineTag from "../../../components/lineTag.vue"
import StoreTable from "./storeTable.vue"
import inventoryGoodsEditor from "./inventoryGoodsEditor.vue"
import InventoryUserEditor from "./inventoryUserEditor.vue"
import {
	IIdempotentTokenType,
	IIdempotentTokenTypePre,
	IModalType,
	LineVo
} from "@/app/baseline/utils/types/common"
import { first, map } from "lodash-es"
import {
	addInventoryPlanWarehouses,
	deleteInventoryPlanWarehousesBatch,
	listInventoryPlanWarehousePaged
} from "@/app/baseline/api/store/inventory-plan-api"
import { listInventoryPlanStoreSelectorOptions } from "@/app/baseline/api/store/manage-api"
import ButtonListFixed from "../../components/buttonListFixed.vue"
import { maxTableHeight } from "@/app/baseline/utils"
import { SystemDepotVo } from "@/app/baseline/utils/types/system-depot"
import { SystemCostCenterVo } from "@/app/baseline/utils/types/system-cost-center"
import { getIdempotentToken } from "@/app/baseline/utils/validate"

const props = defineProps<{
	/**
	 * 盘点计划id
	 */
	planId: any

	/**
	 * 盘点计划类型
	 */
	planType: IInventoryType

	/**
	 * 抽样比例
	 */
	ratio?: any

	/**
	 * 盘点计划状态
	 */
	planStatus: IInventoryStatus

	tbCellClassName?: (params: any) => string

	detailIdList?: any[]

	isSecondCheck?: number
}>()

const emit = defineEmits<{
	/**
	 * 仓库明细数据 change event
	 */
	(e: "change"): void
}>()

// 线路列表
const lineList = ref<LineVo[]>([])

// 段区列表
const depotList = ref<SystemDepotVo[]>([])

// 成本中心
const costCenterList = ref<SystemCostCenterVo>([])

// query 配置
const queryArrList = computed<querySetting[]>(() => {
	return [
		{
			name: "线路",
			key: "lineId",
			type: "select",
			children: lineList.value,
			placeholder: "请选择线路"
		},
		{
			name: "段区",
			key: "depotId",
			type: "select",
			children: depotList.value,
			placeholder: "请选择段区"
		},
		{
			name: "成本中心",
			key: "costCenterId",
			type: "select",
			children: costCenterList.value,
			placeholder: "请选择成本中心"
		}
	] as querySetting[]
})

/**
 * 物资编辑器mode
 */
const goodsEditorMode = ref(IModalType.create)

const tbInit = useTbInit<
	MatStoreCheckPlanTaskPageVo,
	Ref<MatStoreCheckPlanTaskPageReqParams>
>()

const {
	currentPage,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = tbInit

fetchFunc.value = listInventoryPlanWarehousePaged

tbInit.tableProp = computed(() => {
	const defConf: TableColumnType[] = [
		{
			label: "仓库编码",
			prop: "storeCode",
			width: 100,
			fixed: "left"
		},
		{
			label: "仓库名称",
			prop: "storeName",
			width: 180
		},
		{
			label: "仓库级别",
			prop: "storeLevel",
			needSlot: true
		},
		{
			label: "仓库类型",
			prop: "storeType",
			needSlot: true
		},
		{
			label: "线路",
			prop: "storeLineId",
			needSlot: true
		},
		{
			label: "所属段区",
			prop: "storeDepotId_view"
		},
		{
			label: "成本中心",
			prop: "storeCostCenterId_view"
		},
		{ label: "库管员", prop: "storeManage" },
		{ label: "盘点人员", prop: "firstCheckRealName" },
		{ label: "复核人员", prop: "secondCheckRealName" },
		{
			label: "盘点状态",
			prop: "status",
			needSlot: true
		},
		{
			label: "盘盈数量",
			prop: "profitNum"
		},
		{
			label: "盘亏数量",
			prop: "lossesNum"
		},
		{
			label: "操作",
			prop: "actions",
			needSlot: true,
			width: 200,
			fixed: "right"
		}
	]

	if (props.planStatus === IInventoryStatus.notStart) {
		// 未开始不展示状态
		return defConf.filter(
			(v) => !["盘点状态", "盘盈数量", "盘亏数量"].includes(v.label)
		)
	}

	return defConf
})

/**
 * 仓库明细操作按钮状态（与盘点类型关联）
 *
 * - 编辑：专项盘/动态盘启用
 * - 查看：都启用
 * - 移除：都启用
 */
const tableActionBtnStatus = computed(() => {
	return {
		editVisible:
			props.planType === IInventoryType.special ||
			props.planType === IInventoryType.trends,
		viewVisible: true,
		removeVisible: true
	}
})

/**
 * 仓库选择器visible
 */
const warehouseSelectorVisible = ref(false)

const userEditorVisible = ref(false)

const goodsEditorVisible = ref(false)

/**
 * 编辑中的 table row id
 */
const editingTableRowId = ref()

/**
 * 编辑中的 table row 中的 store id
 */
const editingTableRowStoreId = ref()

const { showDelConfirm } = useMessageBoxInit()

const { dictFilter, getDictByCodeList } = useDictInit()

const tbBtnLoading = ref(false)

/**
 * table footer 按钮组配置
 *
 * [选择物资] 专项盘可用
 * [自动抽取] 抽盘可用
 */
const tbBtnConf = computed(() => {
	/**
	 * 未选择 table row
	 */
	const notSelRow = selectedTableList.value?.length < 1

	const defConf = [
		{
			name: "添加仓库",
			icon: ["fas", "circle-plus"]
		},
		{
			name: "指定盘点人员",
			icon: ["fas", "circle-plus"],
			disabled: notSelRow
		},
		{
			name: "选择物资",
			icon: ["fas", "circle-plus"],
			disabled: selectedTableList.value?.length != 1
		},
		{
			name: "批量移除",
			icon: ["fas", "trash-can"],
			disabled: notSelRow
		}
	]

	/**
	 * 专项盘: 添加仓库/指定盘点人员/选择物资
	 * 其它：添加仓库/指定盘点人员
	 */
	switch (props.planType) {
		case IInventoryType.special:
			return defConf

		default:
			return defConf.filter((v) => v.name !== "选择物资")
	}
})

/**
 * 获取段区配置
 */
function getDepotList() {
	BaseLineSysApi.getDepotList().then((res) => {
		depotList.value = res
	})
}

/**
 * 获取成本中心配置
 */
function getCostCenterList() {
	BaseLineSysApi.getCostCenterList().then((res) => {
		costCenterList.value = res
	})
}

onMounted(() => {
	fetchParam.value.checkPlanId = props.planId
	fetchParam.value.sord = "desc"
	fetchParam.value.sidx = "createdDate"

	BaseLineSysApi.getLineOptions().then((res) => {
		lineList.value = res
	})

	getDepotList()
	getCostCenterList()

	getDictByCodeList([
		"STORE_LEVEL",
		"CHECK_TASK_STATUS",
		"STORE_TYPE",
		"CHECK_TASK_STATUS"
	])

	fetchTableData()
})

/**
 * 筛选 handler
 */
const getTableData = (params?: any) => {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		...params
	}
	fetchTableData()
}

defineExpose({ getTableData: getTableData })

watch(
	() => props.planId,
	(v) => {
		fetchParam.value.planId = v
	}
)

/**
 * 批量移除
 */
async function handleDelStore() {
	const idList = map(selectedTableList.value, ({ id }) => id)
	await showDelConfirm()

	tbBtnLoading.value = true
	try {
		await deleteInventoryPlanWarehousesBatch(idList)
		ElMessage.success("操作成功")
		handleGoodsChanged()
	} finally {
		tbBtnLoading.value = false
	}
}

/**
 * 仓库选择 Handler
 */
async function handleStoreSelect(btnName: string, e?: MatStoreVo[]) {
	if (btnName === "保存") {
		let storeList: MatStoreCheckPlanTaskAddStoreListParams[] = []
		if (props.planType === IInventoryType.circulate) {
			storeList =
				map(e, (v) => {
					return {
						storeId: v.storeId,
						materialCodeNum: v.materialCodeNum
					}
				}) || []
		} else {
			storeList =
				map(e, (v) => {
					return {
						storeId: v.id as number
					}
				}) || []
		}

		tbBtnLoading.value = true

		try {
			const idempotentToken = getIdempotentToken(
				IIdempotentTokenTypePre.other,
				IIdempotentTokenType.inventoryManagePlan,
				props.planId
			)

			await addInventoryPlanWarehouses(
				{
					checkPlanId: props.planId,
					storeList
				},
				idempotentToken
			)

			ElMessage.success("操作成功")

			getTableData()

			emit("change")
		} finally {
			tbBtnLoading.value = false
			warehouseSelectorVisible.value = false
		}

		return
	} else if (btnName == "取消") {
		// 取消
		warehouseSelectorVisible.value = false
	}
}

/**
 * 表格下操作按钮 点击 handler
 */
function handleTbBtnClick(name?: string) {
	editingTableRowId.value = first(selectedTableList.value)?.id

	switch (name) {
		case "添加仓库":
			warehouseSelectorVisible.value = true
			break
		case "指定盘点人员":
			userEditorVisible.value = true
			break
		case "选择物资":
			goodsEditorVisible.value = true
			goodsEditorMode.value = IModalType.create
			break
		case "批量移除":
			handleDelStore()
			break
		default:
			break
	}
}

/**
 * 人员编辑后 handler
 */
function handleUserEditorSaved() {
	fetchTableData()
	userEditorVisible.value = false
}

/**
 * 展示物资编辑器
 *
 * @param [mode=IModalType.edit] 物资编辑模式
 */
function showGoodsEditor(e: any, mode: IModalType = IModalType.edit) {
	editingTableRowId.value = e.id
	editingTableRowStoreId.value = e.storeId
	goodsEditorVisible.value = true
	goodsEditorMode.value = mode
}

/**
 * 删除盘点仓库
 */
async function delWarehouse(e: any) {
	await showDelConfirm()
	await deleteInventoryPlanWarehousesBatch([e.id])
	ElMessage.success("操作成功")
	handleGoodsChanged()
}

/**
 * 物资编辑后 handler
 */
function handleGoodsChanged() {
	fetchTableData()
	emit("change")
	goodsEditorVisible.value = false
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
