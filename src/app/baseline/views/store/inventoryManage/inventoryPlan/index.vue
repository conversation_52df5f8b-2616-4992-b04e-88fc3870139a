<!-- 库存管理-盘点管理-盘点计划 -->
<template>
	<div class="app-container">
		<div class="whole-frame">
			<model-frame class="top-frame">
				<!-- 筛选 -->
				<Query
					:query-arr-list="(queryConf as any)"
					@get-query-data="handleQuery"
					class="ml10"
				/>
			</model-frame>

			<model-frame class="bottom-frame">
				<Title
					:title="titleConf"
					:button="(titleBtnConf as any)"
					@on-btn-click="showCreatorDrawer"
				>
					<!-- <Tabs
						style="margin-right: auto; margin-left: 30px"
						:tabs="tabsConf"
						@on-tab-change="handleTabChanged"
					/> -->
					<div class="app-tabs-wrapper">
						<el-tabs v-model="activeName" @tab-click="handleTabChanged">
							<el-tab-pane
								v-for="(tab, index) in tabsConf"
								:key="index"
								:label="`${tab.name}（${statusCnt[tab.value] ?? 0}）`"
								:name="tab.name"
								:index="tab.name"
							/>
						</el-tabs>
					</div>
				</Title>

				<!-- 已完成状态 添加 selection, 用于盘点报告功能 -->
				<el-scrollbar class="rows">
					<pitaya-table
						ref="tableRef"
						:columns="(tbInit.tableProp as any)"
						:table-data="tableData"
						:need-selection="activatedTab === IInventoryPlanStatus.completed"
						:single-select="true"
						:need-index="true"
						:need-pagination="true"
						:total="pageTotal"
						:table-loading="tableLoading"
						@on-selection-change="selectedTableList = $event"
						@on-current-page-change="onCurrentPageChange"
						@on-table-sort-change="handleSortChange"
					>
						<template #type="{ rowData }">
							<color-tag
								border
								v-bind="inventoryCheckTypeColorConf[rowData.type as IInventoryType]"
							>
								{{ dictFilter("CHECK_PLAN_TYPE", rowData.type)?.label }}
							</color-tag>
						</template>

						<!-- 盘点进度 50%以下-蓝色 50%以上-绿色（参考原型） -->
						<template #checkProcessRate="{ rowData }">
							<el-progress
								:show-text="false"
								:stroke-width="14"
								:percentage="rowData.checkProcessRate"
								:color="progressColor"
							/>
						</template>

						<!-- 审批状态 -->
						<template #bpmStatus="{ rowData }">
							<dict-tag
								:options="DictApi.getBpmStatus()"
								:value="rowData.bpmStatus"
							/>
						</template>

						<!-- 差异处理审批状态 -->
						<template #bpmDiffStatus="{ rowData }">
							<dict-tag
								:options="DictApi.getBpmDiffStatus()"
								:value="rowData.bpmDiffStatus"
							/>
						</template>

						<template #resultStatus="{ rowData }">
							<color-tag
								border
								v-bind="IInventoryPlanResultStatusColorConf[rowData.resultStatus as IInventoryPlanResultStatus]"
							>
								{{
									rowData.resultStatus == IInventoryPlanResultStatus.normal
										? "正常"
										: rowData.resultStatus == IInventoryPlanResultStatus.danger
										? "异常"
										: "---"
								}}
							</color-tag>
						</template>

						<template #actions="{ rowData }">
							<slot
								v-if="
									(canShowTableEditAction(rowData) &&
										isCheckPermission(powerList.storeInventoryPlanBtnEdit)) ||
									isCheckPermission(powerList.storeInventoryPlanBtnPreview) ||
									(canShowTableEditAction(rowData) &&
										isCheckPermission(powerList.storeInventoryPlanBtnDrop))
								"
							>
								<el-button
									v-if="
										canShowTableEditAction(rowData) &&
										isCheckPermission(powerList.storeInventoryPlanBtnEdit)
									"
									v-btn
									link
									@click.stop="showEditorDrawer(rowData)"
									:disabled="
										checkPermission(powerList.storeInventoryPlanBtnEdit)
									"
								>
									<font-awesome-icon
										:icon="['fas', 'pen-to-square']"
										style="color: var(--pitaya-btn-background)"
									/>
									<span class="table-inner-btn">编辑</span>
								</el-button>

								<el-button
									v-btn
									link
									@click.stop="showDetailDrawer(rowData)"
									v-if="
										isCheckPermission(powerList.storeInventoryPlanBtnPreview)
									"
									:disabled="
										checkPermission(powerList.storeInventoryPlanBtnPreview)
									"
								>
									<font-awesome-icon
										:icon="['fas', 'eye']"
										style="color: var(--pitaya-btn-background)"
									/>
									<span class="table-inner-btn">查看</span>
								</el-button>

								<el-button
									v-if="
										canShowTableEditAction(rowData) &&
										isCheckPermission(powerList.storeInventoryPlanBtnDrop)
									"
									v-btn
									link
									@click.stop="handleDelRow(rowData)"
									:disabled="
										checkPermission(powerList.storeInventoryPlanBtnDrop)
									"
								>
									<font-awesome-icon
										:icon="['fas', 'trash-can']"
										style="color: var(--pitaya-btn-background)"
									/>
									<span class="table-inner-btn">移除</span>
								</el-button>
							</slot>
							<slot v-else>---</slot>
						</template>

						<!-- 已完成才能显示盘点报告 -->
						<template
							v-if="activatedTab === IInventoryPlanStatus.completed"
							#footerOperateLeft
						>
							<button-list
								class="footer"
								:is-not-radius="true"
								:button="tbBtnConf"
								:loading="tbBtnLoading"
								@on-btn-click="handleTbBtnClick"
							/>
						</template>
					</pitaya-table>
				</el-scrollbar>
			</model-frame>
		</div>

		<!-- 查看 drawer -->
		<Drawer
			v-model:drawer="detailVisible"
			:size="modalSize.xl"
			destroy-on-close
		>
			<inventory-plan-detail
				:id="editingTableRowId"
				:mode="editorMode"
				@save="handleUpdate"
				@close="detailVisible = false"
			/>
		</Drawer>

		<!-- 编辑 drawer -->
		<Drawer
			v-model:drawer="editorVisible"
			:size="modalSize.xl"
			destroy-on-close
		>
			<inventory-plan-editor
				:id="editingTableRowId"
				:mode="editorMode"
				@save="handleDrawerSave"
				@close="editorVisible = false"
			/>
		</Drawer>

		<!-- 差异处理 drawer -->
		<Drawer
			v-model:drawer="bpmDiffStatusVisible"
			:size="modalSize.xl"
			destroy-on-close
		>
			<inventory-plan-diff-editor
				:id="editingTableRowId"
				:mode="editorMode"
				@save="handleDrawerSave"
				@close="bpmDiffStatusVisible = false"
			/>
		</Drawer>

		<!-- 审批、查看弹窗 -->
		<Drawer
			:size="modalSize.xxl"
			v-model:drawer="approvedVisible"
			:destroyOnClose="true"
		>
			<approved-drawer
				:mod="editorMode"
				:task-value="editTask"
				@on-save-or-close="approvedVisible = false"
			/>
		</Drawer>
	</div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from "vue"
import {
	checkPermission,
	isCheckPermission
} from "@/app/platform/utils/permission"
import { powerList } from "../../../components/define.d"
import { IModalType } from "@/app/baseline/utils/types/common"
import { useMessageBoxInit } from "../../../components/messageBox"
import { useTbInit } from "../../../components/tableBase"
import { modalSize } from "@/app/baseline/utils/layout-config"
import { DictApi, appStatus } from "@/app/baseline/api/dict"
import ColorTag from "../../components/colorTag.vue"
import {
	inventoryCheckTypeColorConf,
	IInventoryPlanResultStatusColorConf
} from "@/app/baseline/utils/colors"
import { useDictInit } from "../../../components/dictBase"
import {
	IInventoryPlanResultStatus,
	IInventoryPlanStatus,
	IInventoryType,
	MatStoreCheckPlanVo,
	MatStoreCheckPlanVoQueryParams
} from "@/app/baseline/utils/types/inventory-manage"
import DictTag from "../../../components/dictTag.vue"
import InventoryPlanDetail from "./inventoryPlanDetail.vue"
import InventoryPlanDiffEditor from "./inventoryPlanDiffEditor.vue"
import { progressColor } from "./utils"
import {
	deleteInventoryPlanBatch,
	getStoreCheckPlanBmpStatusCnt,
	listInventoryPlanPaged
} from "@/app/baseline/api/store/inventory-plan-api"
import { first, isNil, omit, toString } from "lodash-es"
import inventoryPlanEditor from "./inventoryPlanEditor.vue"
import { getTaskByBusinessIds } from "@/app/baseline/api/system"
import ApprovedDrawer from "@/app/baseline/views/components/approvedDrawer.vue"

const { showDelConfirm } = useMessageBoxInit()

/**
 * 编辑模式：编辑/新建
 */
const editorMode = ref(IModalType.create)

const detailVisible = ref(false)

const editorVisible = ref(false)

const approvedVisible = ref(false)
const editTask = ref<Record<string, any>>()

const { dictFilter, dictOptions, getDictByCodeList } = useDictInit()

const tbBtnLoading = ref(false)

/**
 * 按钮配置：盘点报告
 */
const titleBtnConf = [
	{
		name: "新建盘点计划",
		roles: powerList.storeInventoryPlanBtnCreate,
		icon: ["fas", "circle-plus"]
	}
]

const tbBtnConf = computed(() => {
	switch (activatedTab.value) {
		case IInventoryPlanStatus.completed:
			return [
				{
					name: "盘点报告",
					roles: powerList.storeInventoryPlanBtnReport,
					icon: ["fas", ""],
					disabled: selectedTableList.value?.length < 1
				},
				{
					name: "差异处理",
					roles: powerList.storeInventoryPlanBtnDiff,
					icon: ["fas", "pen-to-square"],
					disabled:
						selectedTableList.value?.length < 1 ||
						!canShowErrorTableEditAction(first(selectedTableList.value))
				}
			]
		default:
			return []
	}
})

/**
 * 标题配置
 */
const titleConf = {
	name: ["盘点计划"],
	icon: ["fas", "square-share-nodes"]
}

/**
 * 筛选配置
 */
const queryConf = computed<querySetting[]>(() => {
	return [
		{
			name: "盘点计划编号",
			key: "code",
			type: "input",
			placeholder: "请输入盘点计划编号"
		},
		{
			name: "盘点计划名称",
			key: "name",
			type: "input",
			placeholder: "请输入盘点计划名称"
		},
		{
			name: "盘点类型",
			key: "type",
			type: "select",
			placeholder: "请选择",
			children: dictOptions.value.CHECK_PLAN_TYPE
		},
		{
			name: "审批状态",
			key: "bpmStatus",
			type: "select",
			children: DictApi.getBpmStatus(),
			placeholder: "请选择"
		}
	]
})

/**
 * 标签页配置
 */
const statusCnt = ref<number[]>([])
const tabsConf = [
	{
		name: "未启动",
		value: IInventoryPlanStatus.notStart
	},
	{
		name: "盘点中",
		value: IInventoryPlanStatus.progress
	},
	{
		name: "已完成",
		value: IInventoryPlanStatus.completed
	}
]

/**
 * 当前激活的标签页位置
 */
const activatedTab = ref(tabsConf[0].value)
const activeName = ref<string>(tabsConf[0].name)

const tbInit = useTbInit<MatStoreCheckPlanVo, MatStoreCheckPlanVoQueryParams>()

const {
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	selectedTableList,
	fetchParam,
	currentPage,
	fetchTableData,
	onCurrentPageChange,
	fetchFunc
} = tbInit

// 盘点计划分页 fetchParam 初始化 tab status - 未启动
fetchParam.value.status = activatedTab.value
fetchParam.value.sord = "desc"
fetchParam.value.sidx = "createdDate"

/**
 * table api function conf
 */
fetchFunc.value = listInventoryPlanPaged

/**
 * table 的 columns 配置
 */
tbInit.tableProp = computed(() => {
	const defCols: TableColumnType[] = [
		{
			prop: "code",
			label: "盘点计划编号",
			width: 180,
			fixed: "left"
		},
		{
			prop: "name",
			label: "盘点计划名称",
			width: 150
		},
		{
			prop: "type",
			label: "盘点类型",
			needSlot: true,
			width: 120
		},
		{
			prop: "bpmStatus",
			label: "审批状态",
			needSlot: true,
			width: 100
		},
		{
			prop: "storeNum",
			label: "盘点仓库"
		},
		{
			prop: "checkTaskStr",
			label: "盘点任务"
		},
		{
			prop: "materialCodeNum",
			label: "盘点物资编码",
			width: 120
		},
		{
			prop: "checkProcessRate",
			label: "盘点进度",
			needSlot: true
		},
		{
			prop: "resultStatus",
			label: "盘点结果",
			needSlot: true,
			width: 120
		},
		{
			prop: "bpmDiffStatus",
			label: "差异处理状态",
			needSlot: true,
			width: 140
		},
		{
			prop: "beginDate",
			label: "盘点开始日期",
			width: 150,
			sortable: true
		},
		{
			prop: "endDate",
			label: "盘点结束日期",
			width: 150,
			sortable: true
		},
		{
			prop: "createdBy",
			label: "申请人",
			minWidth: 120
		},
		{
			prop: "createdDate",
			label: "创建时间",
			width: 150,
			sortable: true
		},
		{
			prop: "actions",
			label: "操作",
			width: 200,
			needSlot: true,
			fixed: "right"
		}
	]

	switch (activatedTab.value) {
		case IInventoryPlanStatus.notStart:
			// 未启动
			return tableColFilter(defCols, ["盘点进度", "盘点结果", "差异处理状态"])

		case IInventoryPlanStatus.progress:
			// 盘点中
			return tableColFilter(defCols, ["审批状态", "盘点结果", "差异处理状态"])

		default:
			// 已完成
			return tableColFilter(defCols, ["审批状态", "盘点进度"])
	}
}) as any

/**
 * table col 过滤器
 */
function tableColFilter(cols: TableColumnType[], excludeCols: string[]) {
	return cols.filter((c) => !excludeCols.includes(c.label))
}

/**
 * 是否可见 - table 操作按钮 编辑/移除
 *
 * 可见规则：盘点未启动 & 符合条件的审核状态（待提交 & 驳回）
 */
function canShowTableEditAction({ bpmStatus }: any) {
	const isValidBpmStatus = [
		appStatus.pendingApproval,
		appStatus.rejected
	].includes(toString(bpmStatus) as any)

	return activatedTab.value === 0 && isValidBpmStatus
}

/**
 * 是否可见 - table 操作按钮 编辑
 *
 * 可见规则：盘点差异处理 & 符合条件的审核状态（待提交 & 驳回）
 */
function canShowErrorTableEditAction({ bpmDiffStatus }: any) {
	const isValidBpmDiffStatus = [
		appStatus.pendingApproval,
		appStatus.rejected
	].includes(toString(bpmDiffStatus) as any)

	return isValidBpmDiffStatus
}

/**
 * 当前编辑/查看的 table row id
 */
const editingTableRowId = ref()

onMounted(() => {
	getDictByCodeList(["CHECK_PLAN_TYPE"])
	handleQuery()
})

/**
 * 筛选 handler
 */
function handleQuery(e?: any) {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	// 聚合筛选条件
	fetchParam.value = {
		...fetchParam.value,
		status: activatedTab.value,
		...e
	}

	handleUpdate()
}

/**
 * 展示详情 drawer
 */
async function showDetailDrawer(e: any) {
	editorMode.value = IModalType.view
	editingTableRowId.value = e.id
	/* detailVisible.value = true */

	const isValidBpmDiffStatus =
		!isNil(e.bpmDiffStatus) &&
		e.bpmDiffStatus != appStatus.pendingApproval &&
		e.status == IInventoryPlanStatus.completed &&
		e.resultStatus == IInventoryPlanResultStatus.danger

	if (
		(e?.bpmStatus == appStatus.pendingApproval ||
			[IInventoryPlanStatus.progress, IInventoryPlanStatus.completed].includes(
				e.status
			)) &&
		!isValidBpmDiffStatus
	) {
		detailVisible.value = true
	} else {
		const res = await getTaskByBusinessIds({
			businessIds: [e?.id],
			camundaKey: isValidBpmDiffStatus ? "check_plan_upload" : "check_plan"
		})

		if (Array.isArray(res) && res.length > 0) {
			editTask.value = { ...res[res.length - 1] }
			approvedVisible.value = true
		} else {
			detailVisible.value = true
		}
	}
}

/**
 * 展示编辑器 drawer
 */
function showEditorDrawer(e: any) {
	editingTableRowId.value = e.id
	editorMode.value = IModalType.edit

	editorVisible.value = true
}

/**
 * 展示新建 drawer
 */
function showCreatorDrawer(e: any) {
	editingTableRowId.value = e.id
	editorMode.value = IModalType.create
	editorVisible.value = true
}

/**
 * 删除 table row
 */
async function handleDelRow(e: any) {
	await showDelConfirm()
	await deleteInventoryPlanBatch([e.id])
	ElMessage.success("操作成功")
	handleUpdate()
}

/**
 * drawer save handler
 *
 * 处理 drawer 保存之后的逻辑
 *
 * @param id - table row id
 * @param [visible=false] 编辑器可见性
 */
function handleDrawerSave(id: any, visible = false) {
	editorMode.value = IModalType.edit
	editorVisible.value = visible
	editingTableRowId.value = id
	handleUpdate()
}

function handleTabChanged(tab: any) {
	activeName.value = tabsConf[tab.index].name
	activatedTab.value = tabsConf[tab.index].value
	handleQuery()

	/* fetchParam.value = {
		...fetchParam.value,
		status: activatedTab.value
	}

	handleUpdate() */
}

// TODO: 盘点计划-盘点报告逻辑-本期不做
/**
 * 盘点报告
 */

/**
 * 更新主表/statusCnt
 */
const handleUpdate = async () => {
	fetchTableData()
	statusCnt.value = await getStoreCheckPlanBmpStatusCnt(
		omit(
			{
				...fetchParam.value
			},
			"status",
			"currentPage",
			"pageSize"
		) as any
	)
}

const bpmDiffStatusVisible = ref(false)
async function handleTbBtnClick(name?: string) {
	if (name === "盘点报告") {
		await inventoryReport()
	} else if (name === "差异处理") {
		editingTableRowId.value = first(selectedTableList.value)?.id
		bpmDiffStatusVisible.value = true
		/* tbBtnLoading.value = true
		 try {
			await diffHandlingInventoryPlan(first(selectedTableList.value)?.id)
			ElMessage.success("操作成功")
			handleUpdate()
		} finally {
			tbBtnLoading.value = false
		} */
	}
}
function inventoryReport() {
	ElMessage.success("该功能建设中")
}

/**
 * 排序
 * @param column
 * @param prop 字段名称
 * @param order ascending 升序 descending 降序
 */
function handleSortChange(column: any, prop: any, order: any) {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		sord:
			order === "descending" ? "desc" : order === "ascending" ? "asc" : "desc",
		sidx: order ? prop : "createdDate" // 排序字段
	}

	fetchTableData()
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
