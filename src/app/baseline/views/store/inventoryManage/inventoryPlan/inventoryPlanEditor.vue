<!-- 盘点计划编辑 -->
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<div class="drawer-column left" style="width: 310px">
			<Title :title="titleConf" />

			<div class="rows">
				<grid-panel :options="gridPanelOptions" />

				<el-form
					ref="formRef"
					class="content"
					:model="formData"
					:rules="formRules"
					label-position="top"
					label-width="100px"
				>
					<form-element :form-element="formEls" :form-data="formData" />
				</el-form>
			</div>

			<button-list
				class="footer"
				:button="btnConf"
				:loading="btnLoading"
				@on-btn-click="handleDrawerLeftBtnClick"
			/>
		</div>

		<!-- 扩展信息 -->
		<div
			class="drawer-column right"
			:class="mode === IModalType.create ? 'disabled' : ''"
			style="width: calc(100% - 310px)"
		>
			<Title :title="extraTitleConf">
				<Tabs
					:tabs="tabsConf"
					style="margin-right: auto; margin-left: 30px"
					@on-tab-change="activatedTab = $event"
				/>
			</Title>
			<el-scrollbar
				v-if="mode === IModalType.edit"
				class="rows plan-editor-table-wrapper"
			>
				<!-- 仓库明细编辑器 -->
				<inventory-plan-warehouse-editor
					v-if="activatedTab === 0"
					ref="warehouseEditorRef"
					:plan-type="(formData.type as any)"
					:plan-id="id"
					:ratio="formData.drawRatio"
					:plan-status="(formData.status as any)"
					:is-second-check="formData.isSecondCheck"
					@change="getDetail"
					:tb-cell-class-name="tbCellClassName"
					:detail-id-list="errorGoodsIdList?.detailIdList"
				/>

				<table-file
					v-else
					:business-type="fileBusinessType.inventoryPlan"
					:business-id="id"
					:mod="mode === IModalType.edit ? IModalType.edit : IModalType.view"
				/>
			</el-scrollbar>

			<button-list
				v-if="mode === IModalType.edit"
				class="footer"
				:button="submitBtnConf"
				:loading="submitBtnLoading"
				@on-btn-click="handleSubmit"
			/>
		</div>
	</div>
</template>

<script setup lang="ts">
import { getModalTypeLabel } from "@/app/baseline/utils"
import {
	IIdempotentTokenType,
	IIdempotentTokenTypePre,
	IModalType
} from "@/app/baseline/utils/types/common"
import { FormItemRule, FormInstance } from "element-plus"
import GridPanel from "../../components/gridPanel.vue"
import { FormElementType } from "../../../components/define"
import { inputMaxLength } from "@/app/baseline/utils/layout-config"
import { useDictInit } from "../../../components/dictBase"
import FormElement from "../../../components/formElement.vue"
import {
	IInventoryType,
	MatStoreCheckPlanDTO,
	MatStoreCheckPlanVo
} from "@/app/baseline/utils/types/inventory-manage"
import InventoryPlanWarehouseEditor from "./inventoryPlanWarehouseEditor.vue"
import {
	addInventoryPlan,
	getInventoryPlan,
	submitInventoryPlan,
	updateInventoryPlan
} from "@/app/baseline/api/store/inventory-plan-api"
import {
	getIdempotentToken,
	requiredValidator,
	validateNumber
} from "@/app/baseline/utils/validate"
import { fileBusinessType } from "@/app/baseline/api/dict"
import TableFile from "../../../components/tableFile.vue"
import { includes } from "lodash-es"
import { useMessageBoxInit } from "../../../components/messageBox"

const props = defineProps<{
	/**
	 * 业务id
	 */
	id: any

	/**
	 * 编辑模式：编辑/新建
	 */
	mode: IModalType
}>()

const emit = defineEmits<{
	(e: "close"): void
	/**
	 * 保存事件
	 *
	 * @param id 主业务id
	 * @param visible 编辑 drawer visible
	 */
	(e: "save", id: any, visible?: boolean): void
}>()

const { showWarnConfirm } = useMessageBoxInit()

const warehouseEditorRef = ref()

const { dictOptions, getDictByCodeList } = useDictInit()

const titleConf = computed(() => ({
	name: [getModalTypeLabel(props.mode, "盘点计划")],
	icon: ["fas", "square-share-nodes"]
}))

const extraTitleConf = {
	name: ["扩展信息"],
	icon: ["fas", "square-share-nodes"]
}

const btnConf = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "保存草稿",
		icon: ["fas", "floppy-disk"]
	}
]

const submitBtnConf = computed(() => [
	{
		name: "提交审核",
		icon: ["fas", "circle-check"],
		disabled: props.mode === IModalType.create
	}
])

/**
 * 保存旧的表单数据
 */
const oldFormData = ref<string>()

const btnLoading = ref(false)

const submitBtnLoading = ref(false)

/**
 * 表单数据
 */
const formData = ref<MatStoreCheckPlanVo & MatStoreCheckPlanDTO>({
	isSecondCheck: 1
})

/**
 * 表单实例
 */
const formRef = ref<FormInstance>()

/**
 * 表单 validator 规则
 */
const formRules = computed<Record<string, FormItemRule | FormItemRule[]>>(
	() => {
		return {
			name: requiredValidator("盘点计划名称"),
			type: requiredValidator("盘点类型"),
			//mode: requiredValidator("盘点方式"),
			dateArray: requiredValidator("盘点日期"),
			dynamicDateArray: requiredValidator("动态日期"),
			storeNumZero: requiredValidator("是否盘点库存为0的物资"),
			isSecondCheck: requiredValidator("是否复核"),
			remark: requiredValidator("盘点计划说明"),
			drawRatio: [
				{
					required: (formData.value.type as any) == IInventoryType.part,
					message: "抽盘比例不能为空",
					trigger: "change"
				},
				{
					validator: validateNumber,
					required: (formData.value.type as any) == IInventoryType.part,
					trigger: "change"
				},
				{
					validator: (rule: any, value: any, callback: any) => {
						if (!rule.required && !value) {
							callback()
						} else {
							if (value > 100) {
								return callback(new Error("盘点比例不能大于100%"))
							}
							callback()
						}
					},
					required: (formData.value.type as any) == IInventoryType.part,
					trigger: "change"
				}
			],
			secondCheckScope: [
				{
					required: formData.value.isSecondCheck == 1,
					message: "复核范围不能为空",
					trigger: "change"
				}
			]
			/* beginDate: requiredValidator("盘点开始日期"),
	endDate: requiredValidator("盘点结束日期") */
		}
	}
)

/**
 * 盘点计划编辑 - panel数据
 */
const gridPanelOptions = computed(() => [
	{
		label: "盘点仓库",
		value: formData.value.storeNum ?? 0
	},
	{
		label: "盘点物资编码",
		value: formData.value.materialCodeNum ?? 0
	}
])

/**
 * 表单项配置
 */
const formEls = computed<FormElementType[][]>(() => {
	const ls: FormElementType[] = [
		{
			label: "盘点计划名称",
			name: "name",
			maxlength: inputMaxLength.input
		},
		{
			label: "盘点类型",
			name: "type",
			type: "select",
			data: dictOptions.value.CHECK_PLAN_TYPE,
			disabled: props.mode === IModalType.edit,
			change: () => {
				setTimeout(() => {
					formRef.value?.clearValidate()
				}, 0)
			}
		},
		{
			label: "抽盘比例",
			name: "drawRatio",
			type: "number",
			disabled: props.mode === IModalType.edit,
			append: "%"
		},
		/* {
			label: "盘点开始日期",
			name: "beginDate",
			type: "date",
			valueFormat: "YYYY-MM-DD"
		},
		{
			label: "盘点结束日期",
			name: "endDate",
			type: "date",
			valueFormat: "YYYY-MM-DD"
		}, */
		{
			label: "盘点日期",
			name: "dateArray",
			type: "daterange",
			startPlaceholder: "开始日期",
			endPlaceholder: "结束日期",
			valueFormat: "YYYY-MM-DD",
			disabledDate: (time: any) => {
				if ((formData.value.type as any) == IInventoryType.circulate) {
					const now = new Date()
					const nextMonth = now.getMonth() + 1
					const lastDay = new Date(now.getFullYear(), nextMonth, 0)
					return (
						time.getTime() < Date.now() - 8.64e7 ||
						time.getTime() > new Date(lastDay).getTime()
					)
				} else {
					return time.getTime() < Date.now() - 8.64e7
				}
			}
		},
		{
			label: "动态日期",
			name: "dynamicDateArray",
			type: "daterange",
			startPlaceholder: "开始日期",
			endPlaceholder: "结束日期",
			valueFormat: "YYYY-MM-DD",
			disabled: props.mode === IModalType.edit,
			disabledDate: (time: any) => {
				return time.getTime() > Date.now() - 8.64e7
			},
			slotLabelContent: `开始日期：盘点某一时间段进行出入库的物资<br />结束日期：盘点某一时间段进行出入库的物资`
		},
		{
			label: "是否盘点库存为0的物资",
			name: "storeNumZero",
			type: "select",
			disabled: props.mode === IModalType.edit,
			data: [
				{
					label: "是",
					value: 1
				},
				{
					label: "否",
					value: 0
				}
			]
		},
		{
			label: "是否复核",
			name: "isSecondCheck",
			type: "select",
			disabled: props.mode === IModalType.edit,
			data: [
				{
					label: "是",
					value: 1
				},
				{
					label: "否",
					value: 0
				}
			],
			change: () => {
				setTimeout(() => {
					formRef.value?.clearValidate()
				})
			}
		},
		{
			label: "复核范围",
			name: "secondCheckScope",
			type: "select",
			disabled: props.mode === IModalType.edit,
			data: [
				{
					label: "全部物资",
					value: 1
				},
				{
					label: "有差异物资",
					value: 0
				}
			]
		},
		{
			label: "盘点计划说明",
			name: "remark",
			type: "textarea",
			maxlength: inputMaxLength.textarea
		}
	]

	switch (formData.value.type as any) {
		case IInventoryType.all:
			return formData.value.isSecondCheck == 1
				? [ls.filter((v) => v.label !== "抽盘比例" && v.label !== "动态日期")]
				: [
						ls.filter(
							(v) =>
								v.label !== "抽盘比例" &&
								v.label !== "动态日期" &&
								v.label !== "复核范围"
						)
				  ]
		case IInventoryType.part:
			return formData.value.isSecondCheck == 1
				? [ls.filter((v) => v.label !== "动态日期")]
				: [ls.filter((v) => v.label !== "动态日期" && v.label !== "复核范围")]
		case IInventoryType.special:
			return formData.value.isSecondCheck == 1
				? [ls.filter((v) => v.label !== "抽盘比例" && v.label !== "动态日期")]
				: [
						ls.filter(
							(v) =>
								v.label !== "抽盘比例" &&
								v.label !== "动态日期" &&
								v.label !== "复核范围"
						)
				  ]
		case IInventoryType.trends:
			return formData.value.isSecondCheck == 1
				? [
						ls.filter((v) => {
							return (
								v.label !== "抽盘比例" && v.label !== "是否盘点库存为0的物资"
							)
						})
				  ]
				: [
						ls.filter((v) => {
							return (
								v.label !== "抽盘比例" &&
								v.label !== "是否盘点库存为0的物资" &&
								v.label !== "复核范围"
							)
						})
				  ]

		default:
			return formData.value.isSecondCheck == 1
				? [
						ls.filter(
							(v) =>
								v.label !== "抽盘比例" &&
								v.label !== "动态日期" &&
								v.label !== "是否盘点库存为0的物资"
						)
				  ]
				: [
						ls.filter(
							(v) =>
								v.label !== "抽盘比例" &&
								v.label !== "动态日期" &&
								v.label !== "是否盘点库存为0的物资" &&
								v.label !== "复核范围"
						)
				  ]
	}
})

const tabsConf = ["仓库明细", "相关附件"]

const activatedTab = ref(0)

const drawerLoading = ref(false)

onMounted(() => {
	getDictByCodeList(["CHECK_PLAN_TYPE"])

	if (props.mode === IModalType.edit) {
		getDetail()
	}
})

/**
 * 物资明细 table 行样式
 *
 * 库存物资异常，用红色标记该行
 */
const errorGoodsIdList = ref()

function tbCellClassName({ row }: any) {
	return includes(errorGoodsIdList.value?.taskIdList || [], row.id)
		? "error"
		: ""
}

watch(
	() => formData.value.type,
	() => {
		formRef.value?.clearValidate()
	}
)

/**
 * 获取详情数据
 */
async function getDetail() {
	drawerLoading.value = true
	try {
		const r = await getInventoryPlan(props.id)
		r.type = r.type?.toString() as any
		r.mode = r.mode?.toString() as any
		formData.value = r as any

		formData.value.dateArray =
			r.beginDate && r.endDate ? [r.beginDate, r.endDate] : []

		formData.value.dynamicDateArray =
			r.dynamicBeginDate && r.dynamicEndDate
				? [r.dynamicBeginDate, r.dynamicEndDate]
				: []

		oldFormData.value = JSON.stringify(formData.value)
	} finally {
		drawerLoading.value = false
	}
}

/**
 * 处理保存草稿（包含新增和编辑）
 *
 * 保存草稿不需要表单校验
 */
async function handleSaveDraft() {
	if (!formRef.value) {
		return
	}

	formRef.value.validate(async (valid) => {
		if (!valid) {
			return
		}

		btnLoading.value = true

		// 表单校验通过
		try {
			formData.value.beginDate = formData.value.dateArray[0]
			formData.value.endDate = formData.value.dateArray[1]

			formData.value.dynamicBeginDate =
				formData.value.dynamicDateArray && formData.value.dynamicDateArray[0]
			formData.value.dynamicEndDate =
				formData.value.dynamicDateArray && formData.value.dynamicDateArray[1]

			const api =
				props.mode === IModalType.create
					? addInventoryPlan
					: updateInventoryPlan

			let idempotentToken = ""
			if (props.mode === IModalType.create) {
				idempotentToken = getIdempotentToken(
					IIdempotentTokenTypePre.apply,
					IIdempotentTokenType.inventoryManagePlan
				)
			}

			const r = await api(formData.value, idempotentToken)
			formData.value.id = r.id
			formData.value.code = r.code

			oldFormData.value = JSON.stringify(formData.value)

			ElMessage.success("操作成功")
			emit("save", r.id, true)

			warehouseEditorRef.value?.getTableData()
		} finally {
			btnLoading.value = false
		}
	})
}

/**
 * 处理提交审核
 *
 * 需要表单校验
 */
function handleSubmit() {
	if (!formRef.value) {
		return
	}

	formRef.value.validate(async (valid) => {
		if (!valid) {
			return
		}
		await showWarnConfirm("请确认是否提交本次数据？")

		submitBtnLoading.value = true
		drawerLoading.value = true

		try {
			// 如果主表有修改，则先更新主表数据
			if (oldFormData.value != JSON.stringify(formData.value)) {
				await updateInventoryPlan(formData.value)
			}

			const idempotentToken = getIdempotentToken(
				IIdempotentTokenTypePre.other,
				IIdempotentTokenType.inventoryManagePlan,
				formData.value.id
			)

			const { code, msg, data } = await submitInventoryPlan(
				formData.value.id,
				idempotentToken
			)

			if (data && code != 200) {
				errorGoodsIdList.value = data || []
				ElMessage.error(msg)
				warehouseEditorRef.value?.getTableData()
			} else {
				ElMessage.success("操作成功")
				emit("save", undefined)
			}
		} finally {
			submitBtnLoading.value = false
			drawerLoading.value = false
		}
	})
}

function handleDrawerLeftBtnClick(name?: string) {
	if (name === "保存草稿") {
		return handleSaveDraft()
	}

	emit("close")
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.plan-editor-table-wrapper {
	&:deep(.pitaya-table) {
		.error {
			.column-inner-item,
			.cell,
			.el-input__inner {
				color: red;
			}
		}
	}
}
</style>
