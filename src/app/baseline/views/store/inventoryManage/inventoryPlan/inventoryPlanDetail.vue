<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<div class="drawer-column left" style="width: 310px">
			<Title :title="titleConf" />

			<el-scrollbar class="rows">
				<grid-panel :options="gridPanelOptions" />
				<!-- 明细信息 -->
				<el-descriptions size="small" :column="1" border class="content">
					<el-descriptions-item
						v-for="desc in descOptions"
						:key="desc.label"
						:label="desc.label"
					>
						<!-- 盘点类型 -->
						<color-tag
							v-if="desc.key === 'type'"
							border
							v-bind="inventoryCheckTypeColorConf[toString(detailData.type) as IInventoryType]"
						>
							{{ dictFilter("CHECK_PLAN_TYPE", detailData.type as any)?.label }}
						</color-tag>

						<!-- 盘点进度 -->
						<el-progress
							v-else-if="desc.key === 'checkProcessRate'"
							:show-text="false"
							:stroke-width="14"
							:percentage="detailData.checkProcessRate ?? 0"
							:color="progressColor"
						/>

						<!-- 盘点状态 -->
						<color-tag
							v-else-if="desc.key === 'status'"
							border
							v-bind="inventoryPlanStatusColorConf[detailData.status as IInventoryPlanStatus ]"
						>
							{{
								dictFilter("CHECK_PLAN_STATUS", detailData.status as any)?.label
							}}
						</color-tag>

						<!-- 盘点结果 -->
						<color-tag
							v-else-if="desc.key === 'resultStatus'"
							border
							v-bind="IInventoryPlanResultStatusColorConf[detailData.resultStatus as IInventoryPlanResultStatus]"
						>
							{{
								detailData.resultStatus == IInventoryPlanResultStatus.normal
									? "正常"
									: detailData.resultStatus == IInventoryPlanResultStatus.danger
									? "异常"
									: "---"
							}}
						</color-tag>

						<dict-tag
							v-else-if="desc.key === 'bpmDiffStatus'"
							:options="DictApi.getBpmStatus()"
							:value="detailData.bpmDiffStatus"
						/>

						<span
							v-else-if="desc.key === 'profitAmount'"
							style="color: var(--el-color-success)"
						>
							{{ toMoney(detailData.profitAmount) }}
						</span>

						<span
							v-else-if="desc.key === 'lossesAmount'"
							style="color: var(--el-color-danger)"
						>
							{{ toMoney(detailData.lossesAmount) }}
						</span>

						<span
							v-else-if="desc.key === 'diffAmount'"
							:style="{
								color:
									detailData.diffAmount >= 0
										? `var(--el-color-success)`
										: `var(--el-color-danger)`
							}"
						>
							{{ toMoney(Math.abs(detailData.diffAmount)) }}
						</span>
						<span v-else-if="desc.key === 'storeNumZero'">
							{{ detailData.storeNumZero == 1 ? "是" : "否" }}
						</span>
						<span v-else-if="desc.key === 'isSecondCheck'">
							{{ detailData.isSecondCheck == 1 ? "是" : "否" }}
						</span>
						<span v-else-if="desc.key === 'secondCheckScope'">
							{{
								isNil(detailData.secondCheckScope)
									? "---"
									: detailData.secondCheckScope == 1
									? "全部物资"
									: "有差异物资"
							}}
						</span>
						<span v-else>{{
							getNumDefByNumKey(desc.key, detailData[desc.key])
						}}</span>
					</el-descriptions-item>
				</el-descriptions>
			</el-scrollbar>
		</div>

		<!-- 扩展信息 -->
		<div
			class="drawer-column right"
			style="width: calc(100% - 310px)"
			:class="{ pdr10: !footerBtnVisible }"
		>
			<Title :title="extraTitleConf">
				<Tabs
					:tabs="tabsConf"
					:activeIndex="activatedTab"
					style="margin-right: auto; margin-left: 30px"
					@on-tab-change="handleTabChange"
				/>
			</Title>
			<el-scrollbar class="rows">
				<Query
					:query-arr-list="(queryArrList as any)"
					@get-query-data="getTableData"
					style="margin: 10px 10px -10px"
					v-if="activatedTab === 0"
				/>
				<pitaya-table
					v-if="activatedTab === 0"
					ref="tableRef"
					:columns="(tbInit.tableProp as any)"
					:table-data="tableData"
					:need-selection="false"
					:single-select="false"
					:need-index="true"
					:need-pagination="true"
					:total="pageTotal"
					:table-loading="tableLoading"
					:max-height="maxTableHeight"
					@on-selection-change="selectedTableList = $event"
					@on-current-page-change="onCurrentPageChange"
				>
					<!-- 仓库级别 -->
					<template #storeLevel="{ rowData }">
						<!-- <color-tag
							:bg-color="warehouseTypeTagColorMap[rowData.storeLeve as IWarehouseType]"
						> -->
						{{ dictFilter("STORE_LEVEL", rowData.storeLevel)?.label }}
						<!-- </color-tag> -->
					</template>

					<!-- 仓库类型 -->
					<template #storeType="{ rowData }">
						<!-- <color-tag
							:bg-color="warehouseTypeTagColorMap[rowData.storeType as  IWarehouseType]"
						> -->
						{{ dictFilter("STORE_TYPE", rowData.storeType)?.label }}
						<!-- </color-tag> -->
					</template>

					<!-- 盘点阶段 -->
					<template #status="{ rowData }">
						<color-tag
							v-if="!isNil(rowData.status)"
							border
							v-bind="inventoryPlanStatusColorConf[rowData.status as IInventoryTaskStatus]"
						>
							{{ dictFilter("CHECK_TASK_STATUS", rowData.status)?.label }}
						</color-tag>
						<span v-else>---</span>
					</template>

					<!-- 线路 -->
					<template #storeLineId="{ rowData }">
						<line-tag :options="lineList" :value="rowData.storeLineId" />
					</template>

					<template #diffMaterialCodeNum="{ rowData }">
						<link-tag
							:value="rowData.diffMaterialCodeNum"
							@click="handleViewDiffDetail(rowData)"
						/>
					</template>

					<template #resultStatus="{ rowData }">
						<color-tag
							border
							class="ml10"
							v-bind="
								lowValueInventoryCheckStatusColorConf[rowData.resultStatus as ILowValueInventoryCheckStatus]
							"
						>
							{{
								!isNil(rowData.resultStatus)
									? rowData.resultStatus == "0"
										? "正常"
										: "异常"
									: "---"
							}}
						</color-tag>
					</template>

					<template #actions="{ rowData }">
						<el-button v-btn link @click="showInventoryJob(rowData)">
							<font-awesome-icon
								:icon="['fas', 'eye']"
								style="color: var(--pitaya-btn-background)"
							/>
							<span class="table-inner-btn">查看</span>
						</el-button>
					</template>
				</pitaya-table>
				<inventory-plan-diff-detail
					v-else-if="
						activatedTab === 1 &&
						detailData.status === IInventoryPlanStatus.completed
					"
					:plan-id="props.id"
					:check-result="`${IInventoryResultType.profitable},${IInventoryResultType.unprofitable}`"
				/>
				<table-file
					v-else-if="
						activatedTab === 3 &&
						detailData.status === IInventoryPlanStatus.completed
					"
					ref="tableFileRef"
					:business-type="fileBusinessType.inventoryPlanDiff"
					:business-id="id"
					:mod="IModalType.view"
				/>
				<table-file
					v-else
					ref="tableFileRef"
					:business-type="fileBusinessType.inventoryPlan"
					:business-id="id"
					:mod="IModalType.view"
				/>
			</el-scrollbar>

			<button-list
				class="footer"
				v-if="footerBtnVisible"
				:loading="drawerBtnLoading"
				:button="props.mode == IModalType.edit ? btnEditConf : btnConf"
				@on-btn-click="emit('close')"
			/>
		</div>

		<!-- 盘点任务详情 -->
		<Drawer
			v-model:drawer="inventoryJobVisible"
			:size="modalSize.lg"
			destroy-on-close
		>
			<inventory-job-detail
				:id="editingTableRowId"
				@close="inventoryJobVisible = false"
			/>
		</Drawer>

		<Drawer
			v-model:drawer="diffMatCodeNumVisible"
			:size="modalSize.lg"
			destroy-on-close
		>
			<inventory-task-diff-detail
				:check-plan-task-id="editTaskRow.id"
				@close="diffMatCodeNumVisible = false"
			/>
		</Drawer>
	</div>
</template>

<script setup lang="ts">
import { IModalType, LineVo } from "@/app/baseline/utils/types/common"
import { fileBusinessType } from "@/app/baseline/api/dict"
import TableFile from "../../../components/tableFile.vue"
import { getNumDefByNumKey, toMoney } from "@/app/baseline/utils"
import GridPanel from "../../components/gridPanel.vue"
import { useDictInit } from "../../../components/dictBase"
import ColorTag from "../../components/colorTag.vue"
import {
	inventoryCheckTypeColorConf,
	inventoryPlanStatusColorConf,
	IInventoryPlanResultStatusColorConf,
	warehouseTypeTagColorMap
} from "@/app/baseline/utils/colors"
import {
	IInventoryType,
	IInventoryPlanStatus,
	MatStoreCheckPlanVo,
	MatStoreCheckPlanTaskPageVo,
	MatStoreCheckPlanTaskPageReqParams,
	IInventoryPlanResultStatus,
	IInventoryResultType,
	IInventoryTaskStatus
} from "@/app/baseline/utils/types/inventory-manage"
import { useTbInit } from "../../../components/tableBase"
import { IWarehouseType } from "@/app/baseline/utils/types/store-manage"
import { BaseLineSysApi } from "@/app/baseline/api/system"
import LineTag from "../../../components/lineTag.vue"
import DictTag from "../../../components/dictTag.vue"
import LinkTag from "../../../components/linkTag.vue"
import {
	getInventoryPlan,
	listInventoryPlanWarehousePaged
} from "@/app/baseline/api/store/inventory-plan-api"
import { includes, isNil, result, toString } from "lodash-es"
import { progressColor } from "./utils"
import InventoryJobDetail from "../inventoryJob/inventoryJobDetail.vue"
import { modalSize } from "@/app/baseline/utils/layout-config"
import { maxTableHeight } from "@/app/baseline/utils"
import { DictApi } from "@/app/baseline/api/dict"
import inventoryPlanDiffDetail from "./inventoryPlanDiffDetail.vue"
import inventoryTaskDiffDetail from "./inventoryTaskDiffDetail.vue"
import { SystemDepotVo } from "@/app/baseline/utils/types/system-depot"
import { SystemCostCenterVo } from "@/app/baseline/utils/types/system-cost-center"
import { lowValueInventoryCheckStatusColorConf } from "@/app/baseline/utils/colors"
import { ILowValueInventoryCheckStatus } from "@/app/baseline/utils/types/lowValue-inventory-manage"

const props = withDefaults(
	defineProps<{
		/**
		 * 业务id
		 */
		id: any
		mode?: string | undefined
		footerBtnVisible?: boolean
	}>(),
	{ footerBtnVisible: true }
)

const emit = defineEmits<{
	(e: "close"): void
	(e: "save"): void
}>()

const { dictFilter, getDictByCodeList } = useDictInit()

const drawerBtnLoading = ref(false)

const editingTableRowId = ref()

const inventoryJobVisible = ref(false)

const btnConf = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	}
]
const btnEditConf = computed(() => {
	return [
		{
			name: "取消",
			icon: ["fas", "circle-minus"]
		},
		{
			name: "提交审核",
			icon: ["fas", "circle-check"],
			disabled: activatedTab.value === 0
		}
	]
})

// 线路列表
const lineList = ref<LineVo[]>([])

// 段区列表
const depotList = ref<SystemDepotVo[]>([])

// 成本中心
const costCenterList = ref<SystemCostCenterVo>([])

// query 配置
const queryArrList = computed<querySetting[]>(() => {
	return [
		{
			name: "线路",
			key: "lineId",
			type: "select",
			children: lineList.value,
			placeholder: "请选择线路"
		},
		{
			name: "段区",
			key: "depotId",
			type: "select",
			children: depotList.value,
			placeholder: "请选择段区"
		},
		{
			name: "成本中心",
			key: "costCenterId",
			type: "select",
			children: costCenterList.value,
			placeholder: "请选择成本中心"
		}
	]
})

const tbInit = useTbInit<
	MatStoreCheckPlanTaskPageVo,
	MatStoreCheckPlanTaskPageReqParams
>()

const {
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	pageSize,
	currentPage,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = tbInit

fetchFunc.value = listInventoryPlanWarehousePaged

tbInit.tableProp = computed(() => {
	const defCols: TableColumnType[] = [
		{
			label: "仓库编码",
			prop: "storeCode",
			width: 100,
			fixed: "left"
		},
		{
			label: "仓库名称",
			prop: "storeName",
			width: 180
		},
		{
			label: "仓库级别",
			prop: "storeLevel",
			needSlot: true
		},
		{
			label: "仓库类型",
			prop: "storeType",
			needSlot: true
		},
		{
			label: "线路",
			prop: "storeLineId",
			needSlot: true
		},
		{
			label: "所属段区",
			prop: "storeDepotId_view"
		},
		{
			label: "成本中心",
			prop: "storeCostCenterId_view"
		},
		{
			label: "库管员",
			prop: "storeManage"
		},
		{
			label: "盘点人员",
			prop: "firstCheckRealName"
		},
		{
			label: "复核人员",
			prop: "secondCheckRealName"
		},
		{
			label: "盘点阶段",
			prop: "status",
			needSlot: true
		},
		{
			label: "差异物资编码",
			prop: "diffMaterialCodeNum",
			needSlot: true,
			width: 100
		},
		{
			label: "盘点结果",
			prop: "resultStatus",
			needSlot: true,
			width: 120
		},
		{
			label: "操作",
			prop: "actions",
			needSlot: true,
			width: 100,
			fixed: "right"
		}
	]

	if ([IInventoryPlanStatus.notStart].includes(detailData.value.status!)) {
		// 盘点状态: 未启动 过滤字段
		return defCols.filter(
			(v) => !["盘点阶段", "差异物资编码", "盘点结果"].includes(v.label)
		)
	} else if (
		[IInventoryPlanStatus.progress].includes(detailData.value.status!)
	) {
		// 盘点状态: 盘点中 过滤字段
		return defCols.filter((v) => !["差异物资编码"].includes(v.label))
	} else {
		// 盘点状态: 已完成 过滤字段
		return defCols.filter((v) => !["盘点阶段"].includes(v.label))
	}
	/* 	if (
		[IInventoryPlanStatus.notStart, IInventoryPlanStatus.progress].includes(
			detailData.value.status!
		)
	) {
		// 盘点状态: 未启动和盘点中 过滤字段
		return defCols.filter(
			(v) => !["盘点结果", "差异物资编码"].includes(v.label)
		)
	}

	return defCols*/
})

const titleConf = computed(() => ({
	name: ["盘点计划信息"],
	icon: ["fas", "square-share-nodes"]
}))

const extraTitleConf = {
	name: ["扩展信息"],
	icon: ["fas", "square-share-nodes"]
}

const handleTabChange = (index: number) => {
	activatedTab.value = index

	if (index === 0) {
		fetchParam.value = {
			checkPlanId: props.id || detailData.value.id,
			sord: "desc",
			sidx: "createdDate"
		}
		pageSize.value = 20
		getTableData()
	}
}

/**
 * 明细配置
 */
const descOptions = computed(() => {
	const defConf = [
		{
			label: "盘点计划号",
			key: "code"
		},
		{
			label: "盘点计划名称",
			key: "name"
		},
		{
			label: "盘点类型",
			key: "type"
		},
		{
			label: "盘点仓库",
			key: "storeNum"
		},
		{
			label: "盘点物资编码",
			key: "materialCodeNum"
		},
		{
			label: "盘点任务",
			key: "checkTaskStr"
		},
		{
			label: "盘点状态",
			key: "status"
		},
		{
			label: "盘点进度",
			key: "checkProcessRate"
		},
		{
			label: "盘点结果",
			key: "resultStatus"
		},
		{
			label: "盘盈金额",
			key: "profitAmount"
		},
		{
			label: "盘亏金额",
			key: "lossesAmount"
		},
		{
			label: "差异总金额",
			key: "diffAmount"
		},
		{
			label: "差异处理状态",
			key: "bpmDiffStatus"
		},
		{
			label: "盘点开始时间",
			key: "beginDate"
		},
		{
			label: "盘点结束时间",
			key: "endDate"
		},
		{
			label: "是否盘点库存为0的物资",
			key: "storeNumZero"
		},
		{
			label: "是否复核",
			key: "isSecondCheck"
		},
		{
			label: "复核范围",
			key: "secondCheckScope"
		},
		{
			label: "申请人",
			key: "createdBy"
		},
		{
			label: "更新时间",
			key: "lastModifiedDate"
		}
	]

	if (detailData.value.status === IInventoryPlanStatus.completed) {
		// 盘点结束，展示盘盈盘亏结果
		if (
			[IInventoryType.trends, IInventoryType.circulate].includes(
				String(detailData.value?.type) as any
			)
		) {
			return defConf.filter(
				(v) => !includes(["盘点进度", "是否盘点库存为0的物资"], v.label)
			)
		} else {
			return defConf.filter((v) => !includes(["盘点进度"], v.label))
		}
	} else {
		if (
			[IInventoryType.trends, IInventoryType.circulate].includes(
				String(detailData.value?.type) as any
			)
		) {
			return defConf.filter(
				(v) =>
					!includes(
						[
							"盘点结果",
							"差异处理状态",
							"盘盈金额",
							"盘亏金额",
							"差异总金额",
							"是否盘点库存为0的物资"
						],
						v.label
					)
			)
		} else {
			return defConf.filter(
				(v) =>
					!includes(
						["盘点结果", "差异处理状态", "盘盈金额", "盘亏金额", "差异总金额"],
						v.label
					)
			)
		}
	}
})

const tabsConf = computed(() => {
	switch (detailData.value.status) {
		case IInventoryPlanStatus.completed:
			return ["仓库明细", "差异物资", "相关附件", "盘点相关文件"]
		default:
			return ["仓库明细", "相关附件"]
	}
})

const activatedTab = ref(0)

/**
 * 详情数据
 */
const detailData = ref<MatStoreCheckPlanVo>({})

// 盘点计划详情-panel数据
const gridPanelOptions = computed(() => [
	{
		label: "盘点仓库",
		value: detailData.value.storeNum ?? 0
	},
	{
		label: "盘点物资编码",
		value: detailData.value.materialCodeNum ?? 0
	}
])

const drawerLoading = ref(false)

/**
 * 获取段区配置
 */
function getDepotList() {
	BaseLineSysApi.getDepotList().then((res) => {
		depotList.value = res
	})
}

/**
 * 获取成本中心配置
 */
function getCostCenterList() {
	BaseLineSysApi.getCostCenterList().then((res) => {
		costCenterList.value = res
	})
}

/**
 * 筛选 handler
 */
const getTableData = (params?: any) => {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		...params
	}
	fetchTableData()
}

onMounted(() => {
	fetchParam.value.checkPlanId = props.id
	fetchParam.value.sord = "desc"
	fetchParam.value.sidx = "createdDate"

	BaseLineSysApi.getLineOptions().then((res) => {
		lineList.value = res
	})

	getDepotList()
	getCostCenterList()

	getDictByCodeList([
		"CHECK_PLAN_TYPE",
		"STORE_LEVEL",
		"CHECK_PLAN_STATUS",
		"CHECK_TASK_STATUS",
		"STORE_TYPE"
	])
	getDetail()
	fetchTableData()
})

/**
 * 获取详情数据
 */
async function getDetail() {
	drawerLoading.value = true
	try {
		detailData.value = await getInventoryPlan(props.id)
	} finally {
		drawerLoading.value = false
	}
}

function showInventoryJob(e: any) {
	editingTableRowId.value = e.id
	inventoryJobVisible.value = true
}

const diffMatCodeNumVisible = ref(false)
const editTaskRow = ref<MatStoreCheckPlanTaskPageVo>({})

/**
 * 差异物资编码 下钻
 */
function handleViewDiffDetail(e: MatStoreCheckPlanTaskPageVo) {
	diffMatCodeNumVisible.value = true
	editTaskRow.value = { ...e }
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
