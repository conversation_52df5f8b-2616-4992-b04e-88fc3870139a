<!-- 任务差异物资 下钻 -->
<template>
	<div class="drawer-container">
		<div class="drawer-column" style="width: 100%">
			<Title :title="titleConf" />

			<div class="rows">
				<inventory-plan-diff-detail
					:task-id="props.checkPlanTaskId"
					:check-result="`${IInventoryResultType.profitable},${IInventoryResultType.unprofitable}`"
				/>
			</div>

			<button-list
				class="footer"
				:button="drawerBtnConf"
				@on-btn-click="emit('close')"
			/>
		</div>
	</div>
</template>
<script lang="ts" setup>
import inventoryPlanDiffDetail from "./inventoryPlanDiffDetail.vue"
import { IInventoryResultType } from "@/app/baseline/utils/types/inventory-manage"

const props = defineProps<{
	/**
	 * 盘点任务id
	 */
	checkPlanTaskId: any
}>()

const titleConf = computed(() => ({
	name: ["差异物资信息"],
	icon: ["fas", "square-share-nodes"]
}))
const drawerBtnConf = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	}
]

const emit = defineEmits<{
	(e: "close"): void
}>()
</script>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
