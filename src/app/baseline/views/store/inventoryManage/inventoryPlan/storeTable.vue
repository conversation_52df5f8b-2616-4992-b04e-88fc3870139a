<script setup lang="ts">
import { onMounted, computed } from "vue"
import { useTbInit } from "@/app/baseline/views/components/tableBase"
import { listMatStoragePaged } from "@/app/baseline/api/store/manage-api"
import {
	IWarehouseStatus,
	IWarehouseType,
	MatStoreDTO,
	MatStoreVo
} from "@/app/baseline/utils/types/store-manage"
import { warehouseTypeTagColorMap } from "@/app/baseline/utils/colors"
import colorTag from "../../components/colorTag.vue"
import { first, isNil, map } from "lodash-es"
import { useDictInit } from "../../../components/dictBase"
import { getCirculateMaterialNum } from "@/app/baseline/api/store/inventory-plan-api"
import {
	IInventoryType,
	MatStoreCheckPlanDetaiAddlVo
} from "@/app/baseline/utils/types/inventory-manage"
import { BaseLineSysApi } from "@/app/baseline/api/system"
import { SystemDepotVo } from "@/app/baseline/utils/types/system-depot"

const props = withDefaults(
	defineProps<{
		planId: number
		planType: IInventoryType
		/**
		 * 是否支持多选，默认单选
		 */
		multiple?: boolean
		filterStoreTypes?: any[]

		tableApi?: (arg?: any) => any

		tableApiParams?: any
		queryArrList?: any
	}>(),
	{
		filterStoreTypes: () => []
	}
)

const { dictOptions, getDictByCodeList } = useDictInit()
const emits = defineEmits<{
	(e: "onSave", btnName: string, v?: MatStoreVo | MatStoreVo[]): void
}>()

/**
 * title 配置
 */
const matStoreDrawerTitle = {
	name: ["选择仓库"],
	icon: ["fas", "square-share-nodes"]
}

/**
 * 根据 filterStoreTypes 过滤 storeType；
 */
const newStoreTypes = computed<Record<string, any>[]>(() => {
	if (props.filterStoreTypes && props.filterStoreTypes?.length > 0) {
		return (dictOptions.value["STORE_TYPE"] || []).filter(
			(c) => !props.filterStoreTypes.includes(c.value)
		)
	} else {
		return dictOptions.value["STORE_TYPE"] || []
	}
})

/**
 * query查询条件 配置
 */
const queryArrMatStoreList = computed(() => {
	const ls = [
		{
			name: "段区",
			key: "depotId",
			type: "select",
			children: depotList.value,
			placeholder: "请选择段区"
		},
		{
			name: "仓库级别",
			key: "level",
			type: "select",
			children: dictOptions.value["STORE_LEVEL"]
		},
		{
			name: "仓库类型",
			key: "type",
			//children: fileStoreType.value
			needSingleSelect: false,
			type: "select",
			children: newStoreTypes.value
		}
	]

	if (props.queryArrList) {
		return props.queryArrList
	} else {
		if (isNil(props.tableApiParams?.type)) {
			return ls
		} else {
			return ls.filter((c) => !["仓库类型"].includes(c.name))
		}
	}
})

const btnLoading = ref(false)
const {
	tableProp,
	tableRef,
	tableData,
	tableLoading,
	tbBtnLoading,
	pageTotal,
	selectedTableList,
	tbBtns,
	fetchParam,
	currentPage,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange,
	onBtnClick
} = useTbInit<MatStoreVo, MatStoreDTO>()

/**
 * table 列 配置
 */
tableProp.value = [
	{
		label: "仓库编码",
		prop: "code",
		width: 120
	},
	{
		label: "仓库名称",
		prop: "label",
		minWidth: 200
	},
	{
		label: "仓库级别",
		prop: "level_view",
		width: 100
	},
	{
		label: "仓库类型",
		prop: "type",
		needSlot: true,
		width: 120
	},
	{
		label: "所属公司",
		prop: "sysCommunityId_view",
		width: 150
	},
	{
		label: "所属段区",
		prop: "depotId_view",
		minWidth: 120
	},
	{
		label: "成本中心",
		prop: "costCenterId_view",
		width: 120
	},
	{
		label: "仓库位置",
		prop: "positionId",
		minWidth: 120
	},
	{
		label: "库管员",
		prop: "storeManage",
		width: 200
	}
]

fetchFunc.value = props.tableApi ?? listMatStoragePaged

/**
 * Table 按钮配置
 */
const matStoreBtnList = computed(() => {
	return [
		{ name: "取消", icon: ["fas", "circle-minus"] },
		{
			name: "保存",
			icon: ["fas", "floppy-disk"],
			disabled: selectedTableList.value.length > 0 ? false : true
		}
	]
})

const onMatStoreBtnList = (btnName: string | undefined) => {
	if (props.planType === IInventoryType.circulate) {
		circleStoreMatNumVisible.value = true
		const storeList =
			map(selectedTableList.value, (v) => {
				return {
					storeId: v.id as number
				}
			}) || []
		handleStoreSelectCircle(storeList)
	} else {
		btnLoading.value = true
		emits(
			"onSave",
			btnName as any,
			props.multiple ? selectedTableList.value : first(selectedTableList.value)
		)
	}
}

const depotList = ref<SystemDepotVo[]>([])

/**
 * 获取段区配置
 */
function getDepotList() {
	BaseLineSysApi.getDepotList().then((res) => {
		depotList.value = res
	})
}

const handleQuery = (e?: any) => {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		...e
	}
	if (props.filterStoreTypes && props.filterStoreTypes?.length > 0) {
		const types = map(newStoreTypes.value, ({ value }) => value).toString()
		fetchParam.value.type = e?.type || types
	}

	fetchTableData()
}
onMounted(async () => {
	fetchParam.value.status = IWarehouseStatus.activated
	fetchParam.value.sord = "desc"
	fetchParam.value.sidx = "createdDate"

	if (props.tableApiParams) {
		fetchParam.value = {
			...fetchParam.value,
			...props.tableApiParams
		}
	}

	getDepotList()

	await getDictByCodeList(["STORE_LEVEL", "STORE_TYPE", "STORE_STATUS"])
	handleQuery()
})

const circleStoreMatNumVisible = ref(false)
const storeTableData = ref<MatStoreCheckPlanDetaiAddlVo[]>([])
const storeTableLoading = ref<boolean>(false)
const storeTableProp = computed<TableColumnType[]>(() => {
	return [
		{
			label: "盘点仓库名称",
			prop: "storeName"
		},
		{
			label: "物资种类",
			prop: "materialCodeNum",
			needSlot: true,
			width: 150
		}
	]
})

/**
 *循环盘点 编辑物资种类
 * @param storeList
 */
async function handleStoreSelectCircle(storeList: any) {
	circleStoreMatNumVisible.value = true
	storeTableLoading.value = true
	try {
		const res = await getCirculateMaterialNum({
			checkPlanId: props.planId,
			storeList
		})

		storeTableData.value = res as MatStoreCheckPlanDetaiAddlVo[]
	} finally {
		storeTableLoading.value = false
	}
}

function checkStoreMatNum(e: MatStoreCheckPlanDetaiAddlVo) {
	if (e.materialCodeNum! > e.maxMaterialCodeNum) {
		ElMessage.warning("物资种类不能大于最大值")
		e.materialCodeNum = e.maxMaterialCodeNum
		return false
	}
}

function handleCircleStoreSave(btnName?: string) {
	if (btnName === "保存") {
		btnLoading.value = true
		emits("onSave", "保存", storeTableData.value)

		circleStoreMatNumVisible.value = false
	} else {
		circleStoreMatNumVisible.value = false
		btnLoading.value = false
	}
}
</script>
<template>
	<div class="drawer-container">
		<div class="drawer-column">
			<div class="rows">
				<Title :title="matStoreDrawerTitle" />
				<div style="margin: 10px 10px -10px">
					<Query
						:queryArrList="queryArrMatStoreList"
						@getQueryData="handleQuery"
						class="custom-q"
					/>
				</div>
				<div class="common-from-group" style="padding: 0px">
					<el-scrollbar>
						<PitayaTable
							ref="tableRef"
							:columns="tableProp"
							:table-data="tableData"
							:need-index="true"
							:need-pagination="true"
							:single-select="!multiple"
							:need-selection="true"
							:total="pageTotal"
							@on-current-page-change="onCurrentPageChange"
							@onSelectionChange="selectedTableList = $event"
							:table-loading="tableLoading"
						>
							<!-- 仓库类型 -->
							<template #type="{ rowData }">
								<color-tag
									:bg-color="warehouseTypeTagColorMap[rowData.type as IWarehouseType]"
								>
									{{ rowData.type_view }}
								</color-tag>
							</template>

							<template #footerOperateLeft>
								<ButtonList
									class="btn-list"
									:is-not-radius="true"
									:button="tbBtns"
									v-loading="tbBtnLoading"
									@on-btn-click="onBtnClick"
								/>
							</template>
						</PitayaTable>
					</el-scrollbar>
				</div>
			</div>
			<ButtonList
				class="footer"
				:button="matStoreBtnList"
				:loading="btnLoading"
				@on-btn-click="onMatStoreBtnList"
			/>
			<!-- :loading="loading" -->
		</div>
		<div class="dialog-box">
			<el-dialog
				v-model="circleStoreMatNumVisible"
				title="系统消息"
				width="40%"
			>
				<div style="padding-left: 10px; padding-bottom: 10px">
					本月循环盘点物资种类如下，您可以进行调整，请确认是否按以下数据进行盘点？
				</div>
				<el-scrollbar class="rows">
					<pitaya-table
						ref="storeTableRef"
						:columns="(storeTableProp as any)"
						:table-data="storeTableData"
						:need-selection="false"
						:single-select="false"
						:need-index="true"
						:need-pagination="false"
						:max-height="300"
						:table-loading="storeTableLoading"
					>
						<template #materialCodeNum="{ rowData }">
							<el-input-number
								:controls="false"
								v-model="rowData.materialCodeNum"
								:min="1"
								@change="checkStoreMatNum(rowData)"
								style="width: 100%"
							/>
						</template>
					</pitaya-table>
				</el-scrollbar>
				<template #footer>
					<span class="dialog-footer">
						<ButtonList
							class="footer"
							:button="[
								{ name: '取消', icon: ['fas', 'circle-minus'] },
								{ name: '保存', icon: ['fas', 'circle-check'] }
							]"
							:loading="btnLoading"
							@on-btn-click="handleCircleStoreSave"
							style="
								width: 100%;
								align-items: center;
								justify-content: flex-end;
							"
						/>
					</span>
				</template>
			</el-dialog>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.drawer-column {
	width: 100%;
}
.dialog-box {
	:deep(.el-dialog__body) {
		margin: 10px;
		padding: 20px 0 10px !important;
		border-top: 1px solid #ddd;
		border-bottom: 1px solid #ddd;
	}
}
</style>
