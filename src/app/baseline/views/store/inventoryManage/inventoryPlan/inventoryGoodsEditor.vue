<!-- 盘点物资编辑器 -->
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<div class="drawer-column" style="width: 100%">
			<Title :title="titleConf" />

			<Query
				:queryArrList="queryArrList"
				@getQueryData="getTableData"
				class="custom-q"
			/>

			<div class="rows plan-editor-table-wrapper">
				<pitaya-table
					ref="tableRef"
					:columns="(tbInit.tableProp as any)"
					:table-data="tableData"
					:need-selection="mode !== IModalType.view"
					:single-select="false"
					:need-index="true"
					:need-pagination="true"
					:total="pageTotal"
					:table-loading="tableLoading"
					@on-selection-change="selectedTableList = $event"
					@on-current-page-change="onCurrentPageChange"
					:cell-class-name="tbCellClassName"
				>
					<!-- 物资性质 -->
					<template #attribute="{ rowData }">
						<dict-tag
							:options="DictApi.getMatAttr()"
							:value="rowData.attribute"
						/>
					</template>

					<!-- 库存单位 -->
					<template #useUnit="{ rowData }">
						{{
							dictFilter("INVENTORY_UNIT", rowData.useUnit)?.subitemName ||
							"---"
						}}
					</template>

					<!-- 库存金额 -->
					<template #storeAmount="{ rowData }">
						<cost-tag :value="rowData.storeAmount" />
					</template>
					<!-- 移除按钮 -->
					<template v-if="mode === IModalType.edit" #footerOperateLeft>
						<button-list
							:button="btnConf"
							:is-not-radius="true"
							@on-btn-click="removeGoods"
						/>
					</template>
				</pitaya-table>
			</div>

			<div
				class="footer"
				style="display: flex; justify-content: space-between"
				v-if="
					props.planType === IInventoryType.special &&
					props.mode != IModalType.view
				"
			>
				<div class="blue-text">
					{{
						`所选物资金额已达${selectedSumAmount}，占库存物资总金额的${
							isNil(selectedSumAmountRate) ? 0 : selectedSumAmountRate
						}%`
					}}
				</div>
				<button-list
					:button="drawerBtnConf"
					:loading="btnLoading"
					@on-btn-click="handleDrawerBtnClick"
				/>
			</div>

			<button-list
				class="footer"
				v-else
				:button="drawerBtnConf"
				:loading="btnLoading"
				@on-btn-click="handleDrawerBtnClick"
			/>
		</div>
	</div>
</template>

<script setup lang="ts">
import { toMoney } from "@/app/baseline/utils"
import {
	IIdempotentTokenType,
	IIdempotentTokenTypePre,
	IModalType
} from "@/app/baseline/utils/types/common"
import { useDictInit } from "../../../components/dictBase"
import { useMessageBoxInit } from "../../../components/messageBox"
import { useTbInit } from "../../../components/tableBase"
import {
	deleteInventoryPlanWarehouseGoodsBatch,
	listInventoryPlanWarehouseAllMaterialPaged,
	listInventoryPlanWarehouseGoodsPaged,
	addInventoryPlanWarehouseGoods,
	listInventoryPlanEditWarehouseGoodsPaged
} from "@/app/baseline/api/store/inventory-plan-api"
import { map, includes, first, isNil } from "lodash-es"
import {
	IInventoryType,
	MatStoreCheckPlanTaskPageVo
} from "@/app/baseline/utils/types/inventory-manage"
import { tableColFilter } from "@/app/baseline/utils"
import CostTag from "../../../components/costTag.vue"
import { DictApi } from "@/app/baseline/api/dict"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import { getIdempotentToken } from "@/app/baseline/utils/validate"

const props = defineProps<{
	/**
	 * 盘点计划仓库数据
	 */
	planWarehouseData?: MatStoreCheckPlanTaskPageVo

	/**
	 * 盘点任务id
	 */
	planTaskId: any

	/**
	 * 编辑模式 编辑/新建/查看
	 */
	mode: IModalType

	detailIdList?: any[]

	planType: IInventoryType
}>()

const emit = defineEmits<{
	(e: "close"): void

	/**
	 * 保存后
	 */
	(e: "saved"): void
}>()

function tbCellClassName({ row }: any) {
	return includes(props?.detailIdList || [], row.id) ? "error" : ""
}

const titleConf = computed(() => ({
	name: ["物资库存信息"],
	icon: ["fas", "square-share-nodes"]
}))

const selectedSumAmount = computed(() => {
	return toMoney(
		selectedTableList.value?.reduce((pre, cur) => pre + cur.storeAmount, 0)
	)
})

const selectedSumAmountRate = computed(() => {
	const totalAmount = selectedTableList.value?.reduce(
		(pre, cur) => pre + (cur.storeAmount ?? 0),
		0
	)

	if (totalAmount && selectedTableList.value.length > 0) {
		return (
			(totalAmount / (first(selectedTableList.value)?.sumAmount ?? 0)) *
			100
		).toFixed(2)
	} else {
		return 0
	}
})
const btnLoading = ref(false)

const { dictOptions, dictFilter, getDictByCodeList } = useDictInit()

const { showDelConfirm } = useMessageBoxInit()

/**
 * 查询条件 配置
 */
const queryArrList = computed(() => {
	return [
		{
			name: "物资编码",
			key: "materialCode",
			placeholder: "请输入物资编码",
			type: "input",
			enableFuzzy: true
		},
		{
			name: "物资名称",
			key: "materialLabel",
			placeholder: "请输入物资名称",
			type: "input",
			enableFuzzy: true
		},
		{
			name: "规格型号",
			key: "version",
			placeholder: "请输入规格型号",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "物资性质",
			key: "attribute",
			type: "select",
			children: dictOptions.value.MATERIAL_NATURE,
			placeholder: "请选择物资性质"
		}
	]
})

const tbInit = useTbInit()
const {
	currentPage,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	selectedTableList,
	fetchParam,
	fetchTableData,
	onCurrentPageChange,
	fetchFunc
} = tbInit

tbInit.tableProp = computed(() => {
	const defConfCols: TableColumnType[] = [
		{ prop: "regionCode", label: "区域编码", minWidth: 80 },
		{ prop: "roomCode", label: "货位编码", minWidth: 100 },
		{ prop: "materialCode", label: "物资编码", width: 130 },
		{ prop: "materialLabel", label: "物资名称" },
		{ prop: "version", label: "规格型号" },
		{ prop: "technicalParameter", label: "技术参数", minWidth: 100 },
		{ prop: "attribute", label: "物资性质", width: 120, needSlot: true },
		{ prop: "useUnit", label: "库存单位", width: 80, needSlot: true },
		{
			prop: "storeAmount",
			label: "库存金额",
			width: 120,
			needSlot: true,
			align: "right"
		}
	]

	switch (props.mode) {
		case IModalType.view:
			return props.planType === IInventoryType.special
				? defConfCols
				: tableColFilter(defConfCols, ["库存金额"])
		default:
			return props.planType === IInventoryType.special
				? tableColFilter(defConfCols, ["区域编码", "货位编码"])
				: tableColFilter(defConfCols, ["区域编码", "货位编码", "库存金额"])
	}
})

const getTableData = (data?: any) => {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		...data
	}
	fetchTableData()
}

const drawerLoading = ref(false)

const btnConf = computed(() => [
	{
		name: "移除",
		icon: ["fas", "trash-alt"],
		disabled: selectedTableList.value?.length < 1
	}
])

const drawerBtnConf = computed(() => {
	if (props.mode !== IModalType.create) {
		return [
			{
				name: "取消",
				icon: ["fas", "circle-minus"]
			}
		]
	}

	return [
		{
			name: "取消",
			icon: ["fas", "circle-minus"]
		},
		{
			name: "保存",
			icon: ["fas", "floppy-disk"]
		}
	]
})

onMounted(() => {
	/**
	 * 查看 仓库任务下所有物资 - 货位维度
	 * 编辑 仓库任务下所有物资
	 * 新建 仓库下所有物资 过滤已选择物资
	 */
	if (props.mode === IModalType.create) {
		fetchFunc.value = listInventoryPlanWarehouseAllMaterialPaged
		fetchParam.value.checkPlanTaskId = props.planWarehouseData?.id
		fetchParam.value.sord = "asc"
		fetchParam.value.sidx = "code"
	} else if (props.mode === IModalType.edit) {
		fetchParam.value.sord = "asc"
		fetchParam.value.sidx = "code"
		fetchParam.value.checkPlanTaskId = props.planTaskId
		fetchFunc.value = listInventoryPlanEditWarehouseGoodsPaged
	} else {
		fetchParam.value.checkPlanTaskId = props.planTaskId
		fetchFunc.value = listInventoryPlanWarehouseGoodsPaged
	}

	getDictByCodeList(["INVENTORY_UNIT", "MATERIAL_NATURE"])
	fetchTableData()
})

/**
 * 移除物资
 */
async function removeGoods() {
	await showDelConfirm()
	const materialIdList = map(selectedTableList.value, (v) => v.materialId)
	await deleteInventoryPlanWarehouseGoodsBatch({
		planTaskId: props.planTaskId,
		materialIdList
	})
	ElMessage.success("操作成功")
	fetchTableData()
}

async function handleDrawerBtnClick(name?: string) {
	if (name === "取消") {
		emit("close")
		return
	}

	if (props.mode === IModalType.create) {
		// 新增物资到盘点仓库

		if (selectedTableList.value?.length < 1) {
			return ElMessage.warning("请选择物资！")
		}
		btnLoading.value = true

		const materialIdList = map(
			selectedTableList.value,
			({ materialId }) => materialId
		)

		try {
			const idempotentToken = getIdempotentToken(
				IIdempotentTokenTypePre.other,
				IIdempotentTokenType.inventoryManageTask,
				props.planTaskId
			)

			await addInventoryPlanWarehouseGoods(
				{
					planTaskId: props.planTaskId,
					materialIdList
				},
				idempotentToken
			)
			ElMessage.success("操作成功")
			emit("saved")
		} finally {
			btnLoading.value = false
		}
	}
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.custom-q {
	margin: 10px 0px -10px 10px !important;
}
.blue-text {
	color: $---color-info2;
}
.plan-editor-table-wrapper {
	&:deep(.pitaya-table) {
		.error {
			.column-inner-item,
			.cell,
			.el-input__inner {
				color: red;
			}
		}
	}
}
</style>
