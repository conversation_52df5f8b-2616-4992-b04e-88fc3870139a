<!-- 差异物资明细 -->
<template>
	<Query
		:queryArrList="queryArrList"
		@getQueryData="getTableData"
		class="custom-q"
	/>

	<pitaya-table
		ref="tableRef"
		:columns="(tbInit.tableProp as any)"
		:table-data="tableData"
		:need-selection="false"
		:single-select="true"
		:need-index="true"
		:need-pagination="true"
		:total="pageTotal"
		:table-loading="tableLoading"
		@on-selection-change="selectedTableList = $event"
		@on-current-page-change="onCurrentPageChange"
	>
		<!-- 物资性质 -->
		<template #attribute="{ rowData }">
			<dict-tag :options="DictApi.getMatAttr()" :value="rowData.attribute" />
		</template>

		<!-- 库存单位 -->
		<template #useUnit="{ rowData }">
			{{ dictFilter("INVENTORY_UNIT", rowData.useUnit)?.subitemName || "---" }}
		</template>

		<!-- 标准成本 -->
		<template #standardCost="{ rowData }">
			<cost-tag :value="rowData.standardCost" />
		</template>

		<!-- 仓库类型 -->
		<template #storeType="{ rowData }">
			<color-tag
				:bg-color="warehouseTypeTagColorMap[rowData.storeType as IWarehouseType]"
			>
				{{ dictFilter("STORE_TYPE", rowData.storeType)?.label }}
			</color-tag>
		</template>

		<template #checkDiffNum_view="{ rowData }">
			<span :style="fontColorFromInventoryResultType(rowData.checkResult)">
				{{ isNumber(rowData.checkDiffNum_view) }}
			</span>
		</template>

		<!-- 标准差异金额 -->
		<!-- <template #standardAmount="{ rowData }">
			<span :style="fontColorFromInventoryResultType(rowData.checkResult)">
				<cost-tag :value="Math.abs(rowData.checkDiffNum * rowData.amount)" />
			</span>
		</template> -->
		<template #standardAmount="{ rowData }">
			<span :style="fontColorFromInventoryResultType(rowData.checkResult)">
				<cost-tag :value="Math.abs(rowData.standardAmount)" />
			</span>
		</template>

		<!-- 实际差异金额 -->
		<template #practicalAmount="{ rowData }">
			<span :style="fontColorFromInventoryResultType(rowData.checkResult)">
				<!-- 盘盈的时候显示 --- -->
				<span v-if="rowData.checkResult == IInventoryResultType.profitable">
					---
				</span>
				<cost-tag :value="Math.abs(rowData.practicalAmount)" v-else />
			</span>
		</template>

		<!-- 盘点结果 -->
		<template #checkResult="{ rowData }">
			<color-tag
				v-if="!isNil(rowData.checkResult)"
				border
				v-bind="inventoryResultColorConf[rowData.checkResult as IInventoryResultType]"
			>
				{{ dictFilter("CHECK_RESULT", rowData.checkResult)?.label }}
			</color-tag>
			<span v-else>---</span>
		</template>

		<!-- 盘点状态 -->
		<template #status="{ rowData }">
			<color-tag
				v-if="!isNil(rowData.status)"
				border
				v-bind="inventoryCheckStatusColorConf[rowData.status as IInventoryStatus]"
			>
				{{ dictFilter("CHECK_TASK_STATUS", rowData.status)?.label }}
			</color-tag>
			<span v-else>---</span>
		</template>

		<!-- 情况说明 -->
		<template #remark="{ rowData }">
			<span :style="fontColorFromInventoryResultType(rowData)">
				{{ rowData.remark ?? "---" }}
			</span>
		</template>
	</pitaya-table>
</template>

<script setup lang="ts">
import { inventoryResultColorConf } from "@/app/baseline/utils/colors"
import { diffLabel, fontColorFromInventoryResultType } from "../utils"
import {
	IInventoryResultType,
	IInventoryStatus,
	MatStoreCheckPlanTaskPageReqParams,
	MatStoreCheckPlanTaskPageVo
} from "@/app/baseline/utils/types/inventory-manage"
import { IWarehouseType } from "@/app/baseline/utils/types/store-manage"
import { useTbInit } from "../../../components/tableBase"
import {
	inventoryCheckStatusColorConf,
	warehouseTypeTagColorMap
} from "@/app/baseline/utils/colors"
import ColorTag from "../../components/colorTag.vue"
import { isNil, map } from "lodash-es"
import CostTag from "../../../components/costTag.vue"
import { toFixedTwo } from "@/app/baseline/utils"
import { useDictInit } from "../../../components/dictBase"
import { listInventoryJobGoodsPaged } from "@/app/baseline/api/store/inventory-job-api"
import { DictApi } from "@/app/baseline/api/dict"
import DictTag from "@/app/baseline/views/components/dictTag.vue"

const { dictOptions, dictFilter, getDictByCodeList } = useDictInit()
const props = defineProps<{
	/**
	 * 盘点计划id
	 */
	planId?: any

	/**
	 * 任务Id
	 */
	taskId?: any

	/**
	 * 盘点结果(0: 正常, 1: 盘亏, 2: 盘盈)（多个用逗号隔开）
	 */
	checkResult?: string
}>()

/**
 * 查询条件 配置
 */
const queryArrList = computed(() => {
	return [
		{
			name: "物资编码",
			key: "materialCode",
			placeholder: "请输入物资编码",
			type: "input",
			enableFuzzy: true
		},
		{
			name: "物资名称",
			key: "materialLabel",
			placeholder: "请输入物资名称",
			type: "input",
			enableFuzzy: true
		},
		{
			name: "规格型号",
			key: "version",
			placeholder: "请输入规格型号",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "物资性质",
			key: "attribute",
			type: "select",
			children: dictOptions.value.MATERIAL_NATURE,
			placeholder: "请选择物资性质"
		}
	]
})

const tbInit = useTbInit<
	MatStoreCheckPlanTaskPageVo,
	Ref<MatStoreCheckPlanTaskPageReqParams>
>()

const {
	currentPage,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = tbInit

fetchFunc.value = listInventoryJobGoodsPaged

tbInit.tableProp = computed(() => {
	const defCols: TableColumnType[] = [
		{
			prop: "materialCode",
			label: "物资编码",
			width: 130,
			fixed: "left"
		},
		{
			prop: "materialLabel",
			label: "物资名称",
			width: 120
		},
		{
			prop: "version",
			label: "规格型号",
			width: 120
		},
		{
			prop: "technicalParameter",
			label: "技术参数",
			width: "120"
		},
		{
			prop: "attribute",
			label: "物资性质",
			needSlot: true,
			width: 120
		},
		{
			prop: "useUnit",
			label: "库存单位",
			needSlot: true,
			width: 80
		},
		{
			prop: "standardCost",
			label: "标准成本",
			needSlot: true,
			align: "right",
			width: 120
		},
		{
			prop: "storeNum_view",
			label: "库存数量",
			align: "right",
			width: 120
		},
		{
			prop: "firstCheckNum_view",
			label: "盘点数量",
			align: "right",
			width: 120
		},
		{
			prop: "secondCheckNum_view",
			label: "复核数量",
			align: "right"
		},
		{
			prop: "checkDiffNum_view",
			label: "差异数量",
			align: "right",
			needSlot: true
		},
		{
			prop: "standardAmount",
			label: "标准差异金额",
			align: "right",
			needSlot: true,
			width: 120
		},
		{
			prop: "practicalAmount",
			label: "实际差异金额",
			align: "right",
			needSlot: true,
			width: 120
		},
		{
			prop: "checkResult",
			label: "差异结果",
			needSlot: true
		},
		{
			prop: "remark",
			label: "情况说明",
			minWidth: 100,
			needSlot: true
		},
		{
			prop: "roomCode",
			label: "货位编码",
			width: 100
		},
		{
			prop: "task_storeName",
			label: "盘点仓库名称",
			width: 120
		},
		{
			prop: "task_code",
			label: "盘点任务编号",
			width: 180
		},
		{
			prop: "task_firstCheckRealName",
			label: "盘点人员",
			width: 120
		},
		{
			prop: "task_secondCheckRealName",
			label: "复核人员"
		}
	]

	return defCols
})

const getTableData = (data?: any) => {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		...data
	}
	fetchTableData()
}

onMounted(() => {
	fetchParam.value.checkPlanId = props.planId
	fetchParam.value.checkResult = props.checkResult
	fetchParam.value.checkPlanTaskId = props.taskId
	fetchParam.value.sord = "desc"
	fetchParam.value.sidx = "createdDate"

	getDictByCodeList([
		"CHECK_PLAN_TYPE",
		"INVENTORY_UNIT",
		"STORE_TYPE",
		"CHECK_RESULT",
		"MATERIAL_NATURE"
	])

	fetchTableData()
})

/* watch(
	() => props.planId,
	(v) => {
		fetchParam.value.checkPlanId = v
	}
) */

/**
 * 格式化数量
 */
watch(tableData, () => {
	map(tableData.value, (item: Record<string, any>) => {
		if (item.task) {
			Array.from(Object.keys(item?.task))?.map(
				(v) => (item[`task_${v}`] = item.task[v])
			)
		}

		Object.keys(item).forEach((key) => {
			if (
				[
					"storeNum",
					"firstCheckNum",
					"secondCheckNum",
					"checkDiffNum"
				].includes(key)
			) {
				if (!isNil(item[key])) {
					item[`${key + "_view"}`] = toFixedTwo(item[key])
				}
			}
		})
	})
})

const isNumber = (data: string) => {
	// 移除逗号并尝试转换为浮点数
	const cleanedValue = data.replace(/,/g, "")
	const numberValue = parseFloat(cleanedValue)
	if (numberValue > 0) {
		return "+" + data
	} else {
		return data
	}
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.custom-q {
	margin: 10px 0px -10px 10px !important;
}
</style>
