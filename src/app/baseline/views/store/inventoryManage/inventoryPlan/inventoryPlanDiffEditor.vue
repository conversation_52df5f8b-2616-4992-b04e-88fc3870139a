<!-- 差异处理编辑 -->
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<div class="drawer-column left" style="width: 310px">
			<Title :title="titleConf" />

			<el-scrollbar class="rows">
				<grid-panel :options="gridPanelOptions" />
				<!-- 明细信息 -->
				<el-form
					ref="formRef"
					class="content"
					:rules="formRules"
					:model="formData"
					label-position="top"
					label-width="100px"
				>
					<form-element :form-element="formEls" :form-data="formData" />

					<el-form-item label="上传附件" prop="fileId">
						<UploadFile
							:action="uploadUrl"
							@onSuccess="handleFileSave"
							:accept="accept"
							:allExtensions="allExtensions"
							listType="text"
							:view-file="false"
						/>
						<!-- <ButtonList
							class="btn-list spec-btn-list"
							:is-not-radius="true"
							:button="tableEndBtn"
							style="display: block; width: 100%"
							@onBtnClick="handleAddFileSelector"
						/> -->
					</el-form-item>
				</el-form>
			</el-scrollbar>
			<button-list
				class="footer"
				:button="btnConf"
				:loading="drawerBtnLoading"
				@on-btn-click="handleDrawerLeftBtnClick"
			/>
		</div>

		<!-- 扩展信息 -->
		<div class="drawer-column right" style="width: calc(100% - 310px)">
			<Title :title="extraTitleConf">
				<Tabs
					:tabs="tabsConf"
					:activeIndex="activatedTab"
					style="margin-right: auto; margin-left: 30px"
					@on-tab-change="activatedTab = $event"
				/>
			</Title>
			<el-scrollbar class="rows" v-show="activatedTab === 0">
				<inventory-plan-diff-detail
					:plan-id="props.id"
					:check-result="`${IInventoryResultType.profitable},${IInventoryResultType.unprofitable}`"
				/>
			</el-scrollbar>

			<el-scrollbar class="rows" v-show="activatedTab === 1">
				<table-file
					v-show="activatedTab === 1"
					ref="tableFileRef"
					:business-type="fileBusinessType.inventoryPlanDiff"
					:business-id="id"
					:mod="IModalType.edit"
					:is-button="false"
				/>
			</el-scrollbar>

			<button-list
				class="footer"
				v-if="footerBtnVisible"
				:loading="drawerBtnLoading"
				:button="btnEditConf"
				@on-btn-click="handleTbBtnClick"
			/>
		</div>

		<Drawer
			:size="310"
			v-model:drawer="fileDrawerVisible"
			:destroyOnClose="true"
		>
			<file-drawer
				:businessType="fileBusinessType.inventoryPlanDiff"
				:businessId="props.id"
				:file-txt-conf="{
					btnName: '添加附件',
					fileName: '附件名称',
					fileLabel: '上传附件'
				}"
				@onUpdateList="handleFileSave"
				@onSaveOrClose="fileDrawerVisible = false"
			/>
		</Drawer>
	</div>
</template>

<script setup lang="ts">
import { toMoney } from "@/app/baseline/utils"
import {
	IIdempotentTokenType,
	IIdempotentTokenTypePre,
	IModalType
} from "@/app/baseline/utils/types/common"
import { fileBusinessType } from "@/app/baseline/api/dict"
import TableFile from "../../../components/tableFile.vue"
import GridPanel from "../../components/gridPanel.vue"
import { useDictInit } from "../../../components/dictBase"
import {
	MatStoreCheckPlanVo,
	IInventoryResultType
} from "@/app/baseline/utils/types/inventory-manage"
import {
	getInventoryPlan,
	submitUploadInventoryPlan,
	updateInventoryPlan
} from "@/app/baseline/api/store/inventory-plan-api"
import { inputMaxLength } from "@/app/baseline/utils/layout-config"
import { useMessageBoxInit } from "../../../components/messageBox"
import { FormElementType } from "../../../components/define"
import FormElement from "../../../components/formElement.vue"
import fileDrawer from "../../../components/fileDrawer.vue"
import { FormInstance, FormItemRule } from "element-plus"
import {
	getIdempotentToken,
	requiredValidator
} from "@/app/baseline/utils/validate"
import inventoryPlanDiffDetail from "./inventoryPlanDiffDetail.vue"

const { showErrorConfirm, showWarnConfirm } = useMessageBoxInit()

const props = withDefaults(
	defineProps<{
		/**
		 * 业务id
		 */
		id: any
		mode?: string | undefined
		footerBtnVisible?: boolean
	}>(),
	{ footerBtnVisible: true }
)

const emit = defineEmits<{
	(e: "close"): void
	(e: "save"): void
}>()

const { dictFilter, getDictByCodeList } = useDictInit()

const drawerBtnLoading = ref(false)

const btnEditConf = computed(() => {
	return [
		{
			name: "提交审核",
			icon: ["fas", "circle-check"]
		}
	]
})

const btnConf = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "保存草稿",
		icon: ["fas", "floppy-disk"]
	}
]

/* const tableEndBtn = [
	{
		name: "附件上传",
		icon: ["fas", "upload"]
	}
] */

/**
 * 表单实例
 */
const formRef = ref<FormInstance>()
/**
 * 表单项配置
 */
const formEls = computed<FormElementType[][]>(() => {
	formData.value.type_view = dictFilter(
		"CHECK_PLAN_TYPE",
		formData.value.type as any
	)?.label

	const ls: FormElementType[] = [
		{
			label: "差异处理名称",
			name: "diffName",
			maxlength: inputMaxLength.input
		},
		{
			label: "盘点计划号",
			name: "code",
			disabled: true
		},
		{
			label: "盘点计划名称",
			name: "name",
			disabled: true
		},
		{
			label: "盘点类型",
			name: "type_view",
			disabled: true
		},
		{
			label: "盘盈金额",
			name: "profitAmount_view",
			disabled: true,
			className: "success-input-color"
		},
		{
			label: "盘亏金额",
			name: "lossesAmount_view",
			disabled: true,
			className: "danger-input-color"
		},
		{
			label: "差异总金额",
			name: "diffAmount_view",
			disabled: true,
			className:
				formData.value.diffAmount >= 0
					? "success-input-color"
					: "danger-input-color"
		}
	]

	return [ls]
})

/**
 * 表单 validator 规则
 */
const formRules = computed<Record<string, FormItemRule | FormItemRule[]>>(
	() => {
		return {
			diffName: requiredValidator("差异处理名称")
		}
	}
)
const allExtensions = [
	".pdf",
	".doc",
	".docx",
	".xlsx",
	".png",
	".jpg",
	".zip",
	".jpeg",
	".mp4",
	".mov",
	".rmvb"
]
const accept = ".png,.jpeg,.jpg,.pdf,.doc,.docx,.xlsx,.zip,.mp4,.mov,.rmvb"
const uploadUrl = `/pitaya/system/common/upload?businessType=${fileBusinessType.inventoryPlanDiff}&businessId=${props.id}`
const fileDrawerVisible = ref(false)
/* function handleAddFileSelector() {
	fileDrawerVisible.value = true
} */

function handleFileSave() {
	tableFileRef.value?.getTableData()
	activatedTab.value = 1
	//fileDrawerVisible.value = false
}
/**
 * 处理保存草稿（包含新增和编辑）
 *
 * 保存草稿不需要表单校验
 */
async function handleSaveDraft() {
	if (!formRef.value) {
		return
	}

	formRef.value.validate(async (valid: any) => {
		if (!valid) {
			return
		}

		drawerBtnLoading.value = true

		// 表单校验通过
		try {
			await updateInventoryPlan(formData.value as any)

			oldFormData.value = JSON.stringify(formData.value)

			activatedTab.value = 1
			ElMessage.success("操作成功")
		} finally {
			drawerBtnLoading.value = false
		}
	})
}

function handleDrawerLeftBtnClick(name?: string) {
	if (name === "保存草稿") {
		return handleSaveDraft()
	}

	emit("close")
}

const titleConf = computed(() => ({
	name: ["差异处理申请"],
	icon: ["fas", "square-share-nodes"]
}))

const extraTitleConf = {
	name: ["扩展信息"],
	icon: ["fas", "square-share-nodes"]
}

const tabsConf = ["差异物资明细", "盘点相关文件"]

const activatedTab = ref(0)

/**
 * 详情数据
 */

const formData = ref<MatStoreCheckPlanVo>({
	profitAmount_view: "￥0.00" as any,
	lossesAmount_view: "￥0.00" as any
})
const oldFormData = ref("")

// 盘点计划详情-panel数据
const gridPanelOptions = computed(() => [
	{
		label: "盘点仓库",
		value: formData.value.storeNum ?? 0
	},
	{
		label: "盘点物资编码",
		value: formData.value.materialCodeNum ?? 0
	},
	{
		label: "差异物资编码",
		value: formData.value.diffMaterialCodeNum ?? 0
	}
])

const drawerLoading = ref(false)

onMounted(async () => {
	getDictByCodeList([
		"CHECK_PLAN_TYPE",
		"INVENTORY_UNIT",
		"STORE_TYPE",
		"CHECK_RESULT"
	])
	await getDetail()

	setTimeout(() => {
		formRef.value?.clearValidate()
	}, 0)
})

/**
 * 获取详情数据
 */
async function getDetail() {
	drawerLoading.value = true
	try {
		const r = await getInventoryPlan(props.id)
		formData.value = { ...r }
		formData.value.profitAmount_view = toMoney(r.profitAmount)
		formData.value.lossesAmount_view = toMoney(r.lossesAmount)
		formData.value.diffAmount_view = toMoney(Math.abs(r.diffAmount))
	} finally {
		drawerLoading.value = false
	}
}

const tableFileRef = ref()
async function handleTbBtnClick(name: string | undefined) {
	if (name === "提交审核") {
		formRef.value?.validate(async (valid: any) => {
			if (!valid) {
				return
			}

			if (!tableFileRef.value) {
				return showErrorConfirm("请上传盘点文件!")
			}
			if (tableFileRef.value?.["tableFileList"].length < 1) {
				return showErrorConfirm("请上传盘点文件!")
			}

			await showWarnConfirm("请确认是否提交本次数据？")
			drawerBtnLoading.value = true
			drawerLoading.value = true
			try {
				// 如果主表有修改，则先更新主表数据
				if (oldFormData.value != JSON.stringify(formData.value)) {
					await updateInventoryPlan(formData.value as any)
				}

				const idempotentToken = getIdempotentToken(
					IIdempotentTokenTypePre.other,
					IIdempotentTokenType.inventoryManageTask,
					props.id
				)
				await submitUploadInventoryPlan(props.id, idempotentToken)
				ElMessage.success("操作成功")
				emit("save")
				emit("close")
			} finally {
				drawerBtnLoading.value = false
				drawerLoading.value = false
			}
		})
	}
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.spec-btn-list {
	:deep(button) {
		width: 100%;
	}
}
</style>
