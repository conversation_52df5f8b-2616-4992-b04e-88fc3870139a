import { tagColorTheme } from "@/app/baseline/utils/colors"
import { IInventoryResultType } from "@/app/baseline/utils/types/inventory-manage"
import { StyleValue } from "vue"

/**
 * 根据盘点结果类型返回文本样式
 */
export function fontColorFromInventoryResultType(
	checkResult: IInventoryResultType
) {
	const defStyle: StyleValue = {}

	if (checkResult === IInventoryResultType.normal) {
		return defStyle
	}

	switch (checkResult) {
		case IInventoryResultType.profitable:
			// 盘盈
			return {
				...defStyle,
				color: tagColorTheme.success
			}

		case IInventoryResultType.unprofitable:
			// 盘亏
			return {
				...defStyle,
				color: tagColorTheme.danger
			}

		default:
			return defStyle
	}
}

/**
 * 差异 label
 */
export function diffLabel(num?: number | null) {
	if (num === null || num === undefined) {
		return "---"
	}

	return num > 0 ? `+${num}` : num
}
