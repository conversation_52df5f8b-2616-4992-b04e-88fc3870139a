<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<div class="drawer-column left" style="width: 310px">
			<Title :title="titleConf" />

			<el-scrollbar class="rows">
				<!-- 盘点任务明细数据 -->
				<inventory-desc :desc-data="descData" />
			</el-scrollbar>
		</div>

		<!-- 扩展信息 -->
		<div
			class="drawer-column right"
			:class="mode === IModalType.create ? 'disabled' : ''"
			style="width: calc(100% - 310px)"
		>
			<Title :title="extraTitleConf">
				<Tabs
					:tabs="tabsConf"
					style="margin-right: auto; margin-left: 30px"
					@on-tab-change="handleTabChange"
				/>
			</Title>

			<el-scrollbar
				v-if="mode === IModalType.edit"
				class="rows inventory-job-editor-table"
			>
				<Query
					:queryArrList="queryArrList"
					@getQueryData="getTableData"
					class="custom-q"
					v-if="activatedTab === 0"
				/>
				<pitaya-table
					v-if="activatedTab === 0"
					ref="tableRef"
					:columns="(tbInit.tableProp as any)"
					:table-data="tableData"
					:need-selection="false"
					:single-select="false"
					:need-index="true"
					:need-pagination="true"
					:total="pageTotal"
					:table-loading="tableLoading"
					@on-selection-change="selectedTableList = $event"
					@on-current-page-change="onCurrentPageChange"
					:cell-class-name="tbCellClassName"
				>
					<!-- 物资性质 -->
					<template #attribute="{ rowData }">
						<dict-tag
							:options="DictApi.getMatAttr()"
							:value="rowData.attribute"
						/>
					</template>

					<!-- 库存数量 -->
					<template #storeNum="{ rowData }">
						{{ toFixedTwo(rowData.storeNum) }}
					</template>

					<!-- 初盘 -->
					<template #firstCheckNum="{ rowData }">
						<el-input
							v-if="isFirstJob"
							class="no-arrows"
							v-model="rowData.firstCheckNum"
							key="firstCheckNum"
							@click.stop
							@input="rowData.firstCheckNum = validateAndCorrectInput($event)"
							@change="validateNum(rowData, 'firstCheckNum')"
						/>
						<span v-else>{{ toFixedTwo(rowData.firstCheckNum) }}</span>
					</template>

					<!-- 复盘 -->
					<template #secondCheckNum="{ rowData }">
						<el-input
							v-if="isSecondJob"
							class="no-arrows"
							v-model="rowData.secondCheckNum"
							key="secondCheckNum"
							@click.stop
							@input="rowData.secondCheckNum = validateAndCorrectInput($event)"
							@change="validateNum(rowData, 'secondCheckNum')"
						/>
						<span v-else>{{ toFixedTwo(rowData.secondCheckNum) }}</span>
					</template>

					<!-- 库存单位 -->
					<template #useUnit="{ rowData }">
						{{
							dictFilter("INVENTORY_UNIT", rowData.useUnit)?.subitemName ||
							"---"
						}}
					</template>

					<!-- 盘点差异 -->
					<template #checkDiffNum="{ rowData }">
						<span
							:style="fontColorFromInventoryResultType(rowData.checkResult)"
						>
							{{ diffLabel(rowData.checkDiffNum) }}
						</span>
					</template>

					<!-- 盘点结果 -->
					<template #checkResult="{ rowData }">
						<color-tag
							v-if="!isNil(rowData.checkResult)"
							border
							v-bind="inventoryResultColorConf[rowData.checkResult as IInventoryResultType]"
						>
							{{ dictFilter("CHECK_RESULT", rowData.checkResult)?.label }}
						</color-tag>
						<span v-else>---</span>
					</template>

					<!-- 情况说明 -->
					<template #remark="{ rowData }">
						<el-input
							v-if="canEditRemark"
							v-model="rowData.remark"
							:input-style="fontColorFromInventoryResultType(rowData)"
							placeholder="请输入"
							@click.stop
							@change="handleInputBlur(rowData)"
							maxlength="50"
							show-word-limit
						/>
						<span v-else>{{ rowData.remark ?? "---" }}</span>
					</template>

					<!-- <template #footerOperateLeft>
						<button-list
							:button="tbBtnConf"
							@on-btn-click="handleClickDiffReCheckBtn"
						/>
					</template> -->
				</pitaya-table>

				<table-file
					v-else
					:business-type="fileBusinessType.inventoryJob"
					:business-id="id"
					:mod="IModalType.edit"
				/>
			</el-scrollbar>

			<button-list
				class="footer"
				:button="drawerBtnConf"
				:loading="drawerBtnLoading"
				@on-btn-click="handleClickDrawerBtn"
			/>
		</div>
	</div>
</template>

<script setup lang="ts">
import { fileBusinessType } from "@/app/baseline/api/dict"
import {
	IIdempotentTokenType,
	IIdempotentTokenTypePre,
	IModalType
} from "@/app/baseline/utils/types/common"
import TableFile from "../../../components/tableFile.vue"
import { useTbInit } from "../../../components/tableBase"
import { useDictInit } from "../../../components/dictBase"
import { findIndex, isNil, map, includes, toNumber } from "lodash-es"
import InventoryDesc from "./inventoryDesc.vue"
import { diffLabel, fontColorFromInventoryResultType } from "../utils"
import {
	IInventoryResultType,
	IInventoryTaskStatus,
	IInventoryUserType,
	MatStoreCheckPlanTaskDetailVo,
	MatStoreCheckPlanTaskMaterialDetailPageQueryParams,
	MatStoreCheckPlanTaskMaterialDetailPageVo
} from "@/app/baseline/utils/types/inventory-manage"
import ColorTag from "../../components/colorTag.vue"
import { inventoryResultColorConf } from "@/app/baseline/utils/colors"
import { useUserStore } from "@/app/platform/store/modules/user"
import {
	getInventoryJob,
	listInventoryJobGoodsPaged,
	submitInventoryJobGoods,
	updateInventoryJobGoods
} from "@/app/baseline/api/store/inventory-job-api"
import { useMessageBoxInit } from "../../../components/messageBox"
import {
	getIdempotentToken,
	validateAndCorrectInput
} from "@/app/baseline/utils/validate"
import { tableColFilter, toFixedTwo } from "@/app/baseline/utils"
import { DictApi } from "@/app/baseline/api/dict"
import DictTag from "@/app/baseline/views/components/dictTag.vue"

const props = defineProps<{
	/**
	 * 业务id
	 */
	id: any

	/**
	 * 编辑模式：编辑/新建
	 */
	mode: IModalType
}>()

const emit = defineEmits<{
	(e: "close"): void
	(e: "update"): void
	/**
	 * 保存事件
	 *
	 * @param id 主业务id
	 * @param visible 编辑 drawer visible
	 */
	(e: "save", id: any, visible?: boolean): void
}>()

const { showWarnConfirm } = useMessageBoxInit()

const { dictOptions, dictFilter, getDictByCodeList } = useDictInit()

const { userInfo } = storeToRefs(useUserStore())

const tabsConf = ["物资明细", "相关附件"]

const activatedTab = ref(0)

const drawerLoading = ref(false)

/**
 * 编辑后的table row 数据
 */
const editedTableRowStack = ref<any[]>([])

/**
 * 明细数据
 *
 * 包含盘点人类型-初盘/复盘
 */
const descData = ref<MatStoreCheckPlanTaskDetailVo>({})

const titleConf = computed(() => ({
	name: ["盘点任务信息"],
	icon: ["fas", "square-share-nodes"]
}))

const extraTitleConf = {
	name: ["扩展信息"],
	icon: ["fas", "square-share-nodes"]
}

const handleTabChange = (index: number) => {
	activatedTab.value = index

	if (index === 0) {
		fetchParam.value = {
			checkPlanTaskId: props.id || descData.value.id,
			sord: "desc",
			sidx: "createdDate"
		}

		pageSize.value = 20

		getTableData()
	}
}

const drawerBtnConf = computed(() => {
	return [
		{
			name: "取消",
			icon: ["fas", "circle-minus"]
		},
		{
			name: "保存",
			icon: ["fas", "file-signature"],
			disabled: !(isFirstJob.value || isSecondJob.value)
		},
		{
			name: "确认提交",
			icon: ["fas", "circle-check"],
			disabled: !(isFirstJob.value || isSecondJob.value)
		}
	]
})

const drawerBtnLoading = ref(false)

/**
 * 查询条件 配置
 */
const queryArrList = computed(() => {
	return [
		{
			name: "物资编码",
			key: "materialCode",
			placeholder: "请输入物资编码",
			type: "input",
			enableFuzzy: true
		},
		{
			name: "物资名称",
			key: "materialLabel",
			placeholder: "请输入物资名称",
			type: "input",
			enableFuzzy: true
		},
		{
			name: "规格型号",
			key: "version",
			placeholder: "请输入规格型号",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "物资性质",
			key: "attribute",
			type: "select",
			children: dictOptions.value.MATERIAL_NATURE,
			placeholder: "请选择物资性质"
		}
	]
})

const tbInit = useTbInit<
	MatStoreCheckPlanTaskMaterialDetailPageVo,
	Ref<MatStoreCheckPlanTaskMaterialDetailPageQueryParams>
>()
const {
	currentPage,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	pageSize,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = tbInit

fetchFunc.value = listInventoryJobGoodsPaged

tbInit.tableProp = computed(() => {
	const defCols: TableColumnType[] = [
		{
			prop: "regionCode",
			label: "区域编码",
			width: 80,
			fixed: "left"
		},

		{
			prop: "roomCode",
			label: "货位编码",
			width: 100,
			fixed: "left"
		},
		{
			prop: "materialCode",
			label: "物资编码",
			width: 130
		},
		{
			prop: "materialLabel",
			label: "物资名称"
		},
		{
			prop: "version",
			label: "规格型号"
		},
		{
			prop: "technicalParameter",
			label: "技术参数"
		},
		{
			prop: "attribute",
			label: "物资性质",
			needSlot: true,
			width: 120
		},
		{
			prop: "useUnit",
			label: "库存单位",
			needSlot: true,
			width: 80
		},
		{
			prop: "storeNum",
			label: "库存数量",
			align: "right"
		},
		{
			prop: "firstCheckNum",
			label: "盘点数量",
			align: "right",
			needSlot: true
		},
		{
			prop: "checkDiffNum",
			label: "差异数量",
			align: "right",
			needSlot: true
		},
		{
			prop: "secondCheckNum",
			label: "复核数量",
			align: "right",
			needSlot: true
		},
		{
			prop: "checkResult",
			label: "盘点结果",
			needSlot: true
		},
		{
			prop: "remark",
			label: "情况说明",
			minWidth: 100,
			needSlot: true
		}
	]

	if (isFirstJob.value) {
		return tableColFilter(defCols, [
			"库存数量",
			"复核数量",
			"差异数量",
			"盘点结果"
		])
	} else if (isSecondJob.value) {
		return tableColFilter(defCols, ["盘点结果"])
	} else {
		return tableColFilter(defCols, [
			"库存数量",
			"盘点数量",
			"差异数量",
			"盘点结果"
		])
	}
})

/**
 * 是否初盘
 */
const isFirstJob = computed(() => {
	return descData.value.status === IInventoryTaskStatus.firstTask
})

/**
 * 是否复盘
 */
const isSecondJob = computed(() => {
	return (
		!isFirstJob.value &&
		descData.value.status === IInventoryTaskStatus.secondTask
	)
})

/**
 * 当前用户类型 初盘/复盘/其他
 */
const curInventoryUserType = computed(() => {
	if (descData.value.firstCheckUserName === userInfo.value.userName) {
		return IInventoryUserType.first
	}

	if (descData.value.secondCheckUserName === userInfo.value.userName) {
		return IInventoryUserType.second
	}

	return null
})

/**
 * 能否编辑初盘数量
 *
 * 1. 当前任务是初盘状态
 * 2. 当前登录用户是该任务的初盘人
 * 3. 差异编辑中
 */
const canEditFirstNum = computed(() => {
	return (
		isFirstJob.value && curInventoryUserType.value === IInventoryUserType.first
	)
})

/**
 * 能否编辑复盘数量
 *
 * 差异盘点状态中
 * 1. 当前任务是复盘状态
 * 2. 当前登录用户是该任务的复盘人
 */
const canEditSecondNum = computed(() => {
	return (
		curInventoryUserType.value === IInventoryUserType.second &&
		!isFirstJob.value &&
		isSecondJob.value
	)
})

/**
 * 能否编辑备注
 *
 * 复盘结束后，不可编辑
 */
const canEditRemark = computed(() => {
	return canEditFirstNum.value || canEditSecondNum.value
})

/**
 * 物资明细 table 行样式
 *
 * 库存异常物资，用红色标记该行
 */
const errorGoodsIdList = ref<number[]>([])
function tbCellClassName({ row }: any) {
	return includes(errorGoodsIdList.value, row.id) ? "error" : ""
}

const getTableData = (data?: any) => {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		...data
	}
	fetchTableData()
}

onMounted(async () => {
	fetchParam.value.checkPlanTaskId = props.id
	fetchParam.value.sord = "desc"
	fetchParam.value.sidx = "createdDate"

	getDictByCodeList(["INVENTORY_UNIT", "CHECK_RESULT", "MATERIAL_NATURE"])
	await getDetail()
	fetchTableData()
})

/**
 * 获取详情数据
 */
async function getDetail() {
	drawerLoading.value = true
	try {
		descData.value = await getInventoryJob(props.id)

		if (descData.value.status == IInventoryTaskStatus.secondTask) {
			fetchParam.value.secondCheckScope = descData.value.secondCheckScope
		}
	} finally {
		drawerLoading.value = false
	}
}

function handleClickDrawerBtn(name?: string) {
	if (name === "取消") {
		return emit("close")
	}

	if (name === "保存") {
		// 保存逻辑
		handleSave()
	} else {
		// 确认提交
		handleSubmit()
	}
}

/**
 * 保存逻辑
 *
 * 提交所有物资数据（所有分页的）
 */
async function handleSave() {
	// await showWarnConfirm("是否保存本次盘点数据？")

	drawerBtnLoading.value = true

	try {
		const items = map(editedTableRowStack.value, (v) => ({
			id: v.id,
			remark: v.remark,
			firstCheckNum: v.firstCheckNum,
			secondCheckNum: v.secondCheckNum
		}))

		if (items.length < 1) {
			ElMessage.warning("当前物资列表未修改")
			return false
		}

		await updateInventoryJobGoods({ checkPlanTaskId: props.id, items })

		// 如果已启用差异复盘，则关闭差异复盘
		/* if (isDiffReEditEnabled.value) {
			isDiffReEditEnabled.value = false
		} */

		ElMessage.success("操作成功")
		await getDetail()
		fetchTableData()
		emit("update")

		editedTableRowStack.value = []
		/* const hasDiffWarn = await haveDiffWarningMsg()

		if (!hasDiffWarn) {
			ElMessage.success("操作成功")
		}

		if (descData.value.secondCheckIs === IInventoryJobIsSecond.already) {
			// 如果已复盘，则更新盘点任务分页
			emit("save", null, true)
		} */
	} finally {
		drawerBtnLoading.value = false
	}
}

/**
 * 翻页 回显已编辑的数据
 */
watch(
	() => tableData.value,
	() => {
		if (editedTableRowStack.value.length) {
			editedTableRowStack.value.map((e) => {
				const rowIdx = findIndex(tableData.value, (v) => v.id === e.id)
				if (rowIdx !== -1) {
					tableData.value.splice(rowIdx, 1, { ...e })
				}
			})
		}
	}
)

/**
 * 确认提交
 *
 * 如果有修改数据 需先调保存接口 再调 提交接口
 */
async function handleSubmit() {
	const infoTxt = canEditFirstNum.value
		? "请确认是否提交本次盘点数据？"
		: "请确认是否提交本次复核数据？"
	await showWarnConfirm(infoTxt)

	drawerBtnLoading.value = true
	drawerLoading.value = true

	try {
		let isError = false

		const items = map(editedTableRowStack.value, (v) => {
			if (isFirstJob.value) {
				if (
					v.firstCheckNum === null ||
					v.firstCheckNum === undefined ||
					v.firstCheckNum === ""
				) {
					isError = true
				}
			}

			if (isSecondJob.value) {
				if (
					v.secondCheckNum === null ||
					v.secondCheckNum === undefined ||
					v.secondCheckNum === ""
				) {
					isError = true
				}
			}

			return {
				id: v.id,
				remark: v.remark,
				firstCheckNum: v.firstCheckNum,
				secondCheckNum: v.secondCheckNum
			}
		})

		if (items.length > 0) {
			await updateInventoryJobGoods({ checkPlanTaskId: props.id, items })
		}

		if (isError) {
			return ElMessage.warning(
				`您有物资还未填写${isFirstJob.value ? "盘点数量" : ""}${
					isSecondJob.value ? "复核数量" : ""
				}，请先填写正确数据！`
			)
		}

		const idempotentToken = getIdempotentToken(
			IIdempotentTokenTypePre.other,
			IIdempotentTokenType.inventoryManageTask,
			props.id
		)
		const { code, msg, data } = await submitInventoryJobGoods(
			props.id,
			idempotentToken
		)

		if (data && code != 200) {
			errorGoodsIdList.value = data || []
			ElMessage.error(msg)
			getTableData()

			editedTableRowStack.value = []
		} else {
			ElMessage.success("操作成功")

			/* if (descData.value.status === IInventoryTaskStatus.secondTask) {
				// 如果已复盘，则更新盘点任务分页
				await getDetail()
				getTableData()
				emit("save", null, true)
			} else {
				emit("save", null, false)
			} */
			emit("save", null, false)
			editedTableRowStack.value = []
		}

		// emit("save", null, false)
	} finally {
		drawerBtnLoading.value = false
		drawerLoading.value = false
	}
}

/**
 *
 * @param e
 */
function validateNum(e: any, key: string) {
	const formatVal = toNumber(e[key])
	if (e[key] === "" || e[key] === null || e[key] === undefined) {
		e[key] = ""
	} else if (formatVal < 0) {
		ElMessage.warning(
			`${key === "firstCheckNum" ? "初盘数量" : "复盘数量"}不能小于0！`
		)
		e[key] = 0
	} else {
		e[key] = formatVal
	}

	handleInputBlur(e)
}

/**
 * 差异复盘操作
 *
 * 1. 消费api，消费掉差异复盘机会
 * 2. 差异行数据再次可编辑
 * 3. 调用详情api，更新相关条件（差异复盘使用机会）
 */
/* async function handleClickDiffReCheckBtn() {
	await inventoryJobDiffRecheck(props.id)
	isDiffReEditEnabled.value = true
	getDetail()
} */

/**
 * 输入框失焦 handler
 *
 * 失焦后，记录编辑过的数据，用于保存任务数据
 */
function handleInputBlur(e: any) {
	if (!editedTableRowStack.value.length) {
		editedTableRowStack.value.push({ ...e })
		return
	}

	/**
	 * 队列存在数据，验证重复
	 *
	 * - 不重复->push
	 * - 重复->更新该条数据
	 */
	const rowIdx = findIndex(editedTableRowStack.value, (v) => v.id === e.id)
	if (rowIdx !== -1) {
		editedTableRowStack.value.splice(rowIdx, 1, { ...e })
		return
	}

	editedTableRowStack.value.push({ ...e })
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.custom-q {
	margin: 10px 0px -10px 10px !important;
}

.inventory-job-editor-table {
	&:deep(.pitaya-table) {
		.error {
			.column-inner-item,
			.cell,
			.el-input__inner {
				color: red;
			}
		}
	}
}
</style>
