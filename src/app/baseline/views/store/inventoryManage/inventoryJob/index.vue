<template>
	<div class="app-container">
		<div class="whole-frame">
			<model-frame class="top-frame">
				<!-- 筛选 -->
				<Query
					:query-arr-list="(queryConf as any)"
					@get-query-data="handleQuery"
					class="ml10"
				/>
			</model-frame>

			<model-frame class="bottom-frame">
				<Title :title="titleConf">
					<!-- <Tabs
						style="margin-right: auto; margin-left: 30px"
						:tabs="tabsConf"
						@on-tab-change="handleTabChanged"
					/> -->
					<div class="app-tabs-wrapper">
						<el-tabs v-model="activeName" @tab-click="handleTabChanged">
							<el-tab-pane
								v-for="(tab, index) in tabsConf"
								:key="index"
								:label="`${tab.name}（${statusCnt[tab.value] ?? 0}）`"
								:name="tab.name"
								:index="tab.name"
							/>
						</el-tabs>
					</div>
				</Title>

				<pitaya-table
					ref="tableRef"
					:columns="(tbInit.tableProp as any)"
					:table-data="tableData"
					:need-selection="activatedTab != IInventoryTaskStatus.completed"
					:single-select="true"
					:need-index="true"
					:need-pagination="true"
					:total="pageTotal"
					:table-loading="tableLoading"
					@on-selection-change="selectedTableList = $event"
					@on-current-page-change="onCurrentPageChange"
					@on-table-sort-change="handleSortChange"
				>
					<!-- 差异处理审批状态 -->
					<template #bpmDiffStatus="{ rowData }">
						<dict-tag
							:options="DictApi.getBpmDiffStatus()"
							:value="rowData.bpmDiffStatus"
						/>
					</template>

					<!-- 盘点结果 -->
					<template #resultStatus="{ rowData }">
						<color-tag
							border
							v-bind="IInventoryPlanResultStatusColorConf[rowData.resultStatus as IInventoryPlanResultStatus]"
						>
							{{
								rowData.resultStatus == IInventoryPlanResultStatus.normal
									? "正常"
									: rowData.resultStatus == IInventoryPlanResultStatus.danger
									? "异常"
									: "---"
							}}
						</color-tag>
					</template>

					<!-- 盘点进度 -->
					<template #taskProcessRate="{ rowData }">
						<el-progress
							:show-text="false"
							:stroke-width="14"
							:percentage="rowData.taskProcessRate"
							:color="progressColor"
						/>
					</template>

					<!-- 盘点计划号 -->
					<template #checkPlanCode="{ rowData }">
						<link-tag
							:value="rowData.checkPlanCode"
							@click.stop="showPlanDetail(rowData)"
						/>
					</template>

					<!-- 盘点类型 -->
					<template #planType="{ rowData }">
						<color-tag
							border
							v-bind="inventoryCheckTypeColorConf[rowData.planType as IInventoryType]"
						>
							{{ dictFilter("CHECK_PLAN_TYPE", rowData.planType)?.label }}
						</color-tag>
					</template>

					<template #checkStatus="{ rowData }">
						<color-tag
							v-if="!isNil(rowData.checkStatus)"
							border
							v-bind="inventoryTaskCheckStatusColorConf[rowData.checkStatus as IInventoryTaskCheckStatus]"
						>
							{{
								rowData.checkStatus == IInventoryTaskCheckStatus.notStart
									? "未开始"
									: rowData.checkStatus == IInventoryTaskCheckStatus.progress
									? rowData.status == IInventoryTaskStatus.firstTask
										? "盘点中"
										: "复核中"
									: rowData.checkStatus == IInventoryTaskCheckStatus.completed
									? "盘点完成"
									: "---"
							}}
						</color-tag>
						<span v-else>---</span>
					</template>

					<template #secondCheckScope="{ rowData }">
						{{
							isNil(rowData.secondCheckScope)
								? "---"
								: rowData.secondCheckScope == "1"
								? "全部物资"
								: "有差异物资"
						}}
					</template>

					<template #actions="{ rowData }">
						<!-- 盘点中才能编辑盘点任务 -->
						<slot
							v-if="
								(activatedTab === 1 &&
									isCheckPermission(powerList.storeInventoryTaskBtnEdit)) ||
								isCheckPermission(powerList.storeInventoryTaskBtnPreview)
							"
						>
							<el-button
								v-if="
									[0, 1].includes(activatedTab) &&
									isCheckPermission(powerList.storeInventoryTaskBtnEdit)
								"
								v-btn
								link
								:disabled="
									!isEdit(rowData) ||
									checkPermission(powerList.storeInventoryTaskBtnEdit)
								"
								@click.stop="showEditorDrawer(rowData)"
							>
								<font-awesome-icon
									:icon="['fas', 'pen-to-square']"
									style="color: var(--pitaya-btn-background)"
								/>
								<span class="table-inner-btn">编辑</span>
							</el-button>

							<el-button
								v-btn
								link
								@click.stop="showDetailDrawer(rowData)"
								:disabled="
									checkPermission(powerList.storeInventoryTaskBtnPreview)
								"
								v-if="isCheckPermission(powerList.storeInventoryTaskBtnPreview)"
							>
								<font-awesome-icon
									:icon="['fas', 'eye']"
									style="color: var(--pitaya-btn-background)"
								/>
								<span class="table-inner-btn">查看</span>
							</el-button>
						</slot>
						<slot v-else>---</slot>
					</template>

					<template #footerOperateLeft>
						<button-list-fixed
							v-if="tbBtnConf.length > 0"
							class="footer"
							:is-not-radius="true"
							:button="tbBtnConf"
							:loading="tbBtnLoading"
							@on-btn-click="handleTbBtnClick"
						/>
					</template>
				</pitaya-table>
			</model-frame>
		</div>

		<!-- 查看 drawer -->
		<Drawer
			v-model:drawer="detailVisible"
			:size="modalSize.xl"
			destroy-on-close
		>
			<inventory-job-detail
				:id="editingTableRowId"
				@close="detailVisible = false"
			/>
		</Drawer>

		<!-- 编辑 drawer -->
		<Drawer
			v-model:drawer="editorVisible"
			:size="modalSize.xl"
			destroy-on-close
		>
			<inventory-job-editor
				:mode="IModalType.edit"
				:id="editingTableRowId"
				@close="editorVisible = false"
				@update="handleUpdate"
				@save="handleChangeInventoryJob"
			/>
		</Drawer>

		<!-- 盘点计划详情 -->
		<Drawer v-model:drawer="planVisible" :size="modalSize.xl" destroy-on-close>
			<inventory-plan-detail :id="viewingPlanId" @close="planVisible = false" />
		</Drawer>
	</div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from "vue"
import {
	checkPermission,
	isCheckPermission
} from "@/app/platform/utils/permission"
import { powerList } from "../../../components/define.d"
import {
	IIdempotentTokenType,
	IIdempotentTokenTypePre,
	IModalType
} from "@/app/baseline/utils/types/common"
import { useTbInit } from "../../../components/tableBase"
import { modalSize } from "@/app/baseline/utils/layout-config"
import { useDictInit } from "../../../components/dictBase"
import { listMatStoragePaged } from "@/app/baseline/api/store/manage-api"
import { first, omit, isNil } from "lodash-es"
import {
	IInventoryPlanResultStatus,
	IInventoryTaskCheckStatus,
	IInventoryTaskStatus,
	IInventoryType,
	MatStoreCheckPlanTaskPageSearchResultVo
} from "@/app/baseline/utils/types/inventory-manage"

import {
	inventoryTaskCheckStatusColorConf,
	inventoryCheckTypeColorConf,
	IInventoryPlanResultStatusColorConf
} from "@/app/baseline/utils/colors"
import LinkTag from "../../../components/linkTag.vue"
import DictTag from "../../../components/dictTag.vue"
import { progressColor } from "../inventoryPlan/utils"
import ColorTag from "../../components/colorTag.vue"
import InventoryJobDetail from "./inventoryJobDetail.vue"
import InventoryJobEditor from "./inventoryJobEditor.vue"
import {
	batchEndInventoryJob,
	batchInventoryJobFinancialPosting,
	batchStartInventoryJob,
	batchStartSecondCheckInventoryJob,
	getStoreCheckPlanTaskBmpStatusCnt,
	listInventoryJobPaged
} from "@/app/baseline/api/store/inventory-job-api"
import InventoryPlanDetail from "../inventoryPlan/inventoryPlanDetail.vue"
import dayjs from "dayjs"
import { useMessageBoxInit } from "../../../components/messageBox"
import { useUserStore } from "@/app/platform/store/modules/user"
import ButtonListFixed from "../../components/buttonListFixed.vue"
import { DictApi } from "@/app/baseline/api/dict"
import { getIdempotentToken } from "@/app/baseline/utils/validate"
const { showWarnConfirm } = useMessageBoxInit()

/**
 * 编辑模式：编辑/新建
 */
const editorMode = ref(IModalType.create)

const detailVisible = ref(false)

const editorVisible = ref(false)

const planVisible = ref(false)

const tbBtnLoading = ref(false)

const { userInfo } = storeToRefs(useUserStore())

/**
 * 正在查看的计划id
 */
const viewingPlanId = ref()

const { dictOptions, dictFilter, getDictByCodeList } = useDictInit()

const { showErrorConfirm } = useMessageBoxInit()

/**
 * 标题配置
 */
const titleConf = {
	name: ["盘点任务"],
	icon: ["fas", "square-share-nodes"]
}

/**
 * 筛选配置
 */
const queryConf = computed<querySetting[]>(() => {
	return [
		{
			name: "盘点计划号",
			key: "planCode",
			type: "input",
			placeholder: "请输入盘点计划号"
		},
		{
			name: "盘点计划名",
			key: "planName",
			type: "input",
			placeholder: "请输入盘点计划名"
		},
		{
			name: "盘点任务编号",
			key: "taskCode",
			type: "input",
			placeholder: "请输入盘点任务编号"
		},
		{
			name: "仓库编码",
			key: "storeCode",
			type: "input",
			placeholder: "请输入仓库编码"
		},
		{
			name: "盘点人员",
			key: "firstCheckPersonName",
			type: "input",
			placeholder: "请输入盘点人员"
		},
		{
			name: "复核人员",
			key: "secondCheckPersonName",
			type: "input",
			placeholder: "请输入复核人员"
		},
		{
			name: "盘点类型",
			key: "checkType",
			type: "select",
			placeholder: "请选择",
			children: dictOptions.value.CHECK_PLAN_TYPE
		},
		{
			name: "盘点仓库",
			key: "storeId",
			placeholder: "请选择盘点仓库",
			type: "tableSelect",
			tableInfo: [
				{
					title: "请选择盘点仓库",
					tableApi: listMatStoragePaged, //表格接口
					tableColumns: [
						{ label: "仓库编码", prop: "code", width: 120 },
						{ label: "仓库名称", prop: "label" },
						{
							label: "仓库级别",
							prop: "level_view",
							width: 100
						},
						{
							label: "仓库类型",
							prop: "type_view",
							width: 120
						},
						{
							label: "所属段区",
							prop: "depotId_view",
							width: 150
						}
					],
					queryArrList: [
						{
							name: "仓库编码",
							key: "code",
							type: "input",
							placeholder: "请输入仓库编码"
						},
						{
							name: "仓库名称",
							key: "label",
							type: "input",
							placeholder: "请输入仓库名称"
						}
					],
					params: {
						currentPage: 1,
						pageSize: 20
					}, //表格接口参数

					labelName: {
						key: "id", //回显标识
						label: "label", //input框要展示的字段
						value: "id" //要给后台传的字段
					}
				}
			]
		},
		{
			name: "盘点结果",
			key: "resultStatus",
			type: "select",
			placeholder: "请选择",
			children: [
				{ label: "正常", value: IInventoryPlanResultStatus.normal },
				{ label: "异常", value: IInventoryPlanResultStatus.danger }
			]
		},
		{
			name: "差异处理状态",
			key: "bpmDiffStatus",
			type: "select",
			placeholder: "请选择",
			children: DictApi.getBpmDiffStatus()
		}
	]
})

/**
 * 标签页配置
 */
const statusCnt = ref<number[]>([])
const tabsConf = [
	{
		name: "初盘",
		value: IInventoryTaskStatus.firstTask
	},
	{
		name: "复核",
		value: IInventoryTaskStatus.secondTask
	},
	{
		name: "完成",
		value: IInventoryTaskStatus.completed
	}
]

/**
 * 当前激活的标签页位置
 */
const activatedTab = ref(tabsConf[0].value)
const activeName = ref<string>(tabsConf[0].name)
const tbInit = useTbInit<MatStoreCheckPlanTaskPageSearchResultVo, Ref<any>>()

const {
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	selectedTableList,
	fetchParam,
	currentPage,
	fetchTableData,
	onCurrentPageChange,
	fetchFunc
} = tbInit

fetchParam.value.taskStatus = activatedTab.value
fetchParam.value.sord = "desc"
fetchParam.value.sidx = "createdDate"

/**
 * table api function conf
 */
fetchFunc.value = listInventoryJobPaged

/**
 * table 的 columns 配置
 */
tbInit.tableProp = computed(() => {
	const defCols: TableColumnType[] = [
		{
			prop: "code",
			label: "盘点任务编号",
			width: 180
		},
		{
			prop: "checkPlanCode",
			label: "盘点计划号",
			needSlot: true,
			width: 180,
			fixed: "left"
		},
		{
			prop: "checkPlanName",
			label: "盘点计划名称",
			width: 150
		},
		{
			prop: "storeName",
			label: "盘点仓库名称",
			minWidth: 150
		},
		{
			prop: "storeCode",
			label: "盘点仓库编码",
			width: 120
		},
		{
			prop: "planType",
			label: "盘点类型",
			needSlot: true,
			width: 100
		},
		{
			prop: "checkStatus",
			label: "盘点状态",
			needSlot: true,
			width: 100
		},
		{
			prop: "secondCheckScope",
			label: "复核范围",
			needSlot: true,
			width: 100
		},
		{
			prop: "materialCodeNum",
			label: "盘点物资编码",
			width: 120
		},
		{
			prop: "resultStatus",
			label: "盘点结果",
			needSlot: true,
			width: 100
		},
		{
			prop: "diffMaterialCodeNum",
			label: "差异物资编码",
			width: 120
		},
		{
			prop: "bpmDiffStatus",
			label: "差异处理状态",
			needSlot: true,
			width: 100
		},
		{
			prop: "taskProcessRate",
			label: "盘点进度",
			needSlot: true
		},
		{
			prop: "beginDate",
			label: "任务开始时间",
			width: 150,
			sortable: true
		},
		{
			prop: "endDate",
			label: "任务结束时间",
			width: 150,
			sortable: true
		},
		{
			prop: "planBeginDate",
			label: "盘点开始时间",
			width: 150,
			sortable: true
		},
		{
			prop: "planEndDate",
			label: "盘点结束时间",
			width: 150,
			sortable: true
		},
		{
			prop: "firstCheckRealName",
			label: "盘点人员",
			minWidth: 120
		},
		{
			prop: "secondCheckRealName",
			label: "复核人员",
			minWidth: 120
		},
		{
			prop: "lastModifiedDate",
			label: "更新时间",
			width: 150,
			sortable: true
		},
		{
			prop: "actions",
			label: "操作",
			width: 150,
			needSlot: true,
			fixed: "right"
		}
	]

	switch (activatedTab.value) {
		case IInventoryTaskStatus.firstTask:
			// 初盘
			return tableColFilter(defCols, [
				/* "盘点进度",
				"盘点结果", */
				"差异物资编码",
				"差异处理状态",
				"复核范围",
				/* "任务开始时间" */
				"任务结束时间"
			])

		case IInventoryTaskStatus.secondTask:
			// 复核
			return tableColFilter(defCols, [
				"盘点开始时间",
				"盘点结束时间",
				"差异处理状态",
				"任务结束时间"
			])
		default:
			// 已完成
			return tableColFilter(defCols, [
				"盘点进度",
				"盘点开始时间",
				"盘点结束时间",
				"盘点状态"
			])
	}
})

/**
 * 当前编辑/查看的 table row id
 */
const editingTableRowId = ref()

const tbBtnConf = computed(() => {
	const notSelected = selectedTableList.value?.length < 1

	const { firstCheckUserName, checkStatus, status, secondCheckUserName } =
		(first(selectedTableList.value) ??
			{}) as MatStoreCheckPlanTaskPageSearchResultVo

	const curUserName = userInfo.value.userName

	const isCanEnd = () => {
		if (status == IInventoryTaskStatus.firstTask) {
			return (
				checkStatus === IInventoryTaskCheckStatus.completed &&
				!secondCheckUserName &&
				firstCheckUserName === curUserName
			)
		}

		if (status == IInventoryTaskStatus.secondTask) {
			return (
				checkStatus === IInventoryTaskCheckStatus.completed &&
				secondCheckUserName &&
				secondCheckUserName === curUserName
			)
		}

		return false
	}

	// 开始盘点/复核
	const isCanStart = () => {
		if (status == IInventoryTaskStatus.firstTask) {
			return (
				checkStatus == IInventoryTaskCheckStatus.notStart &&
				firstCheckUserName === curUserName
			)
		}

		if (status == IInventoryTaskStatus.secondTask) {
			return (
				checkStatus == IInventoryTaskCheckStatus.notStart &&
				secondCheckUserName === curUserName
			)
		}
	}

	const defConf = [
		{
			name:
				activatedTab.value === IInventoryTaskStatus.firstTask
					? "开始盘点"
					: "开始复核",
			roles: powerList.storeInventoryTaskBtnStart,
			icon: ["fas", "circle-check"],
			disabled: notSelected || !isCanStart()
		},
		{
			name: "结束盘点",
			roles: powerList.storeInventoryTaskBtnFinish,
			icon: ["fas", "circle-check"],
			disabled: notSelected || !isCanEnd()
		}
	]

	return activatedTab.value === IInventoryTaskStatus.completed ? [] : defConf
})

/**
 * 盘点中  是否显示编辑按钮
 * 1.初盘人 未确认提交前 可编辑
 * 1.复盘人 未确认提交前 可编辑
 * @param row
 */
const isEdit = function (row: MatStoreCheckPlanTaskPageSearchResultVo) {
	if (row.status === IInventoryTaskStatus.firstTask) {
		if (row.checkStatus === IInventoryTaskCheckStatus.progress) {
			return row.firstCheckUserName === userInfo.value.userName
		}
	} else if (row.status === IInventoryTaskStatus.secondTask) {
		if (row.checkStatus === IInventoryTaskCheckStatus.progress) {
			return row.secondCheckUserName === userInfo.value.userName
		}
	}

	return false
}

/**
 * 更新主表/statusCnt
 */
const handleUpdate = async () => {
	fetchTableData()
	statusCnt.value = await getStoreCheckPlanTaskBmpStatusCnt(
		omit(
			{
				...fetchParam.value
			},
			"taskStatus",
			"currentPage",
			"pageSize"
		) as any
	)
}

onMounted(() => {
	getDictByCodeList(["CHECK_PLAN_TYPE", "CHECK_TASK_FINANCIAL_POSTING_STATUS"])
	handleQuery()
})

async function handleTbBtnClick(name?: string) {
	const jobData = first(selectedTableList.value)
	tbBtnLoading.value = true
	try {
		switch (name) {
			case "开始盘点":
				await startJob(jobData)
				break
			case "开始复核":
				await startSecondCheckJob(jobData)
				break
			case "结束盘点":
				await endJob(jobData)
				break
			default:
				// 盘点任务：财务过账
				await batchInventoryJobFinancialPosting([jobData?.id])
				break
		}
	} finally {
		tbBtnLoading.value = false
		handleUpdate()
	}
}

/**
 * 开始盘点任务
 *
 * 需要校验任务开始时间
 */
async function startJob(jobData?: any) {
	if (dayjs().isBefore(dayjs(jobData.planBeginDate))) {
		tbBtnLoading.value = false
		return showErrorConfirm("未到盘点开始日期！")
	}

	await showWarnConfirm("请确认是否开始盘点？")

	const idempotentToken = getIdempotentToken(
		IIdempotentTokenTypePre.other,
		IIdempotentTokenType.inventoryManageTask,
		jobData.id
	)
	await batchStartInventoryJob(jobData?.id, idempotentToken)

	editingTableRowId.value = jobData.id
	editorMode.value = IModalType.edit
	editorVisible.value = true
}

/**
 * 开始复核
 * @param jobData
 */
async function startSecondCheckJob(jobData?: any) {
	if (dayjs().isBefore(dayjs(jobData.planBeginDate))) {
		tbBtnLoading.value = false
		return showErrorConfirm("未到盘点开始日期！")
	}

	/**
	 * 复核范围：secondCheckScope = 0 有差异物资; 1:全部物资
	 * 有异常物资: firstResultStatus == "1"; 无差异 0
	 */
	if (
		!isNil(jobData.secondCheckScope) &&
		jobData.secondCheckScope == "0" &&
		jobData.firstResultStatus == "0"
	) {
		await showWarnConfirm("此盘点任务无差异物资，无需复核")

		const idempotentToken = getIdempotentToken(
			IIdempotentTokenTypePre.other,
			IIdempotentTokenType.inventoryManageTask,
			jobData.id
		)
		await batchStartSecondCheckInventoryJob(jobData?.id, idempotentToken)
		handleQuery()
	} else {
		await showWarnConfirm("请确认是否开始复核？")

		const idempotentToken = getIdempotentToken(
			IIdempotentTokenTypePre.other,
			IIdempotentTokenType.inventoryManageTask,
			jobData.id
		)
		await batchStartSecondCheckInventoryJob(jobData?.id, idempotentToken)

		editingTableRowId.value = jobData.id
		editorMode.value = IModalType.edit
		editorVisible.value = true
	}
}

/**
 * 结束盘点
 *
 * 校验规则：
 * 1. 复盘人限定（btn conf 已经限制，此处略）
 * 2. 已复盘
 */
async function endJob(jobData?: any) {
	// 盘点任务分页-结束盘点-已复盘校验
	if (
		![
			IInventoryPlanResultStatus.normal,
			IInventoryPlanResultStatus.danger
		].includes(jobData?.resultStatus)
	) {
		return showErrorConfirm("盘点任务中存在待盘点的物资！")
	}

	await showWarnConfirm("请确认是否结束盘点？")

	const idempotentToken = getIdempotentToken(
		IIdempotentTokenTypePre.other,
		IIdempotentTokenType.inventoryManageTask,
		jobData.id
	)

	await batchEndInventoryJob(jobData.id, idempotentToken)

	ElMessage.success("操作成功")
}

/**
 * table col 过滤器
 */
function tableColFilter(cols: TableColumnType[], excludeCols: string[]) {
	return cols.filter((c) => !excludeCols.includes(c.label))
}

/**
 * 筛选 handler
 */
function handleQuery(e?: any) {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	// 聚合筛选条件
	fetchParam.value = {
		...fetchParam.value,
		...e,
		taskStatus: activatedTab.value
	}

	handleUpdate()
}

/**
 * 展示详情 drawer
 */
function showDetailDrawer(e: any) {
	editingTableRowId.value = e.id
	detailVisible.value = true
}

/**
 * 展示编辑器 drawer
 */
function showEditorDrawer(e: any) {
	editingTableRowId.value = e.id
	editorMode.value = IModalType.edit
	editorVisible.value = true
}

/**
 * 更新盘点任务 handler
 */
function handleChangeInventoryJob(_: any, visible = false) {
	editorVisible.value = visible
	handleUpdate()
}

function handleTabChanged(tab: any) {
	activeName.value = tabsConf[tab.index].name
	activatedTab.value = tabsConf[tab.index].value

	handleQuery()

	/* fetchParam.value = {
		...fetchParam.value,
		taskStatus: activatedTab.value
	}

	handleUpdate() */
}

/**
 * 展示盘点计划详情
 */
function showPlanDetail(e: any) {
	if (!e.checkPlanId) {
		return false
	}
	planVisible.value = true
	viewingPlanId.value = e.checkPlanId
}

/**
 * 排序
 * @param column
 * @param prop 字段名称
 * @param order ascending 升序 descending 降序
 */
function handleSortChange(column: any, prop: any, order: any) {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		sord:
			order === "descending" ? "desc" : order === "ascending" ? "asc" : "desc",
		sidx: order
			? prop == "planBeginDate"
				? " checkPlan.beginDate"
				: prop == "planEndDate"
				? "checkPlan.endDate"
				: prop
			: "createdDate" // 排序字段
	}
	// planEndDate

	fetchTableData()
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
