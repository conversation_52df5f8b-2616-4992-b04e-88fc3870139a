<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<div class="drawer-column left" style="width: 310px">
			<Title :title="titleConf" />

			<el-scrollbar class="rows">
				<!-- 明细信息 -->
				<inventory-desc :desc-data="detailData" />
			</el-scrollbar>
		</div>

		<!-- 扩展信息 -->
		<div class="drawer-column right" style="width: calc(100% - 310px)">
			<Title :title="extraTitleConf">
				<Tabs
					:tabs="tabsConf"
					style="margin-right: auto; margin-left: 30px"
					@on-tab-change="handleTabChange"
				/>
			</Title>
			<el-scrollbar class="rows">
				<Query
					:queryArrList="queryArrList"
					@getQueryData="getTableData"
					class="custom-q"
					v-if="activatedTab === 0"
				/>
				<pitaya-table
					v-if="activatedTab === 0"
					ref="tableRef"
					:columns="(tbInit.tableProp as any)"
					:table-data="tableData"
					:need-selection="false"
					:single-select="false"
					:need-index="true"
					:need-pagination="true"
					:total="pageTotal"
					:table-loading="tableLoading"
					@on-selection-change="selectedTableList = $event"
					@on-current-page-change="onCurrentPageChange"
				>
					<!-- 物资性质 -->
					<template #attribute="{ rowData }">
						<dict-tag
							:options="DictApi.getMatAttr()"
							:value="rowData.attribute"
						/>
					</template>

					<!-- 库存单位 -->
					<template #useUnit="{ rowData }">
						{{
							dictFilter("INVENTORY_UNIT", rowData.useUnit)?.subitemName ||
							"---"
						}}
					</template>

					<template #checkDiffNum_view="{ rowData }">
						<span
							:style="fontColorFromInventoryResultType(rowData.checkResult)"
						>
							{{ diffLabel(rowData.checkDiffNum_view) }}
						</span>
					</template>

					<!-- 盘点结果 -->
					<template #checkResult="{ rowData }">
						<color-tag
							v-if="!isNil(rowData.checkResult)"
							border
							v-bind="inventoryResultColorConf[rowData.checkResult as IInventoryResultType]"
						>
							{{ dictFilter("CHECK_RESULT", rowData.checkResult)?.label }}
						</color-tag>
						<span v-else>---</span>
					</template>

					<!-- 情况说明 -->
					<template #remark="{ rowData }">
						<span :style="fontColorFromInventoryResultType(rowData)">
							{{ rowData.remark ?? "---" }}
						</span>
					</template>
				</pitaya-table>

				<table-file
					v-else
					:business-type="fileBusinessType.inventoryJob"
					:business-id="id"
					:mod="
						detailData.checkStatus === IInventoryTaskCheckStatus.completed
							? IModalType.edit
							: IModalType.view
					"
				/>
			</el-scrollbar>
			<button-list
				class="footer"
				:button="btnConf"
				@on-btn-click="emit('close')"
			/>
		</div>
	</div>
</template>

<script setup lang="ts">
import { inventoryResultColorConf } from "@/app/baseline/utils/colors"
import { useDictInit } from "../../../components/dictBase"
import { useTbInit } from "../../../components/tableBase"
import ColorTag from "../../components/colorTag.vue"
import InventoryDesc from "./inventoryDesc.vue"
import {
	IInventoryResultType,
	IInventoryTaskCheckStatus,
	IInventoryTaskStatus,
	MatStoreCheckPlanTaskMaterialDetailPageVo
} from "@/app/baseline/utils/types/inventory-manage"
import { diffLabel, fontColorFromInventoryResultType } from "../utils"
import { IModalType } from "@/app/baseline/utils/types/common"
import { fileBusinessType } from "@/app/baseline/api/dict"
import TableFile from "../../../components/tableFile.vue"
import {
	getInventoryJob,
	listInventoryJobGoodsPaged
} from "@/app/baseline/api/store/inventory-job-api"
import { isNil, map } from "lodash-es"
import { tableColFilter, toFixedTwo } from "@/app/baseline/utils"
import { DictApi } from "@/app/baseline/api/dict"
import DictTag from "@/app/baseline/views/components/dictTag.vue"

const props = defineProps<{
	/**
	 * 业务id
	 */
	id: any
}>()

const emit = defineEmits<{
	(e: "close"): void
}>()

const titleConf = computed(() => ({
	name: ["盘点任务信息"],
	icon: ["fas", "square-share-nodes"]
}))

const extraTitleConf = {
	name: ["扩展信息"],
	icon: ["fas", "square-share-nodes"]
}

const handleTabChange = (index: number) => {
	activatedTab.value = index

	if (index === 0) {
		fetchParam.value = {
			checkPlanTaskId: props.id,
			sord: "desc",
			sidx: "createdDate"
		}

		pageSize.value = 20

		getTableData()
	}
}

const btnConf = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	}
]

const tabsConf = ["物资明细", "相关附件"]

const activatedTab = ref(0)

const { dictOptions, dictFilter, getDictByCodeList } = useDictInit()

/**
 * 详情数据
 */
const detailData = ref<any>({})

const drawerLoading = ref(false)

/**
 * 查询条件 配置
 */
const queryArrList = computed(() => {
	const ls = [
		{
			name: "盘点结果",
			key: "checkResult",
			type: "select",
			placeholder: "请选择",
			children: [
				{ label: "正常物资", value: IInventoryResultType.normal },
				{ label: "盘亏物资", value: IInventoryResultType.unprofitable },
				{ label: "盘盈物资", value: IInventoryResultType.profitable }
			]
		},
		{
			name: "物资编码",
			key: "materialCode",
			placeholder: "请输入物资编码",
			type: "input",
			enableFuzzy: true
		},
		{
			name: "物资名称",
			key: "materialLabel",
			placeholder: "请输入物资名称",
			type: "input",
			enableFuzzy: true
		},
		{
			name: "规格型号",
			key: "version",
			placeholder: "请输入规格型号",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "物资性质",
			key: "attribute",
			type: "select",
			children: dictOptions.value.MATERIAL_NATURE,
			placeholder: "请选择物资性质"
		}
	]

	if (detailData.value.checkStatus == IInventoryTaskCheckStatus.completed) {
		return ls
	} else {
		return ls.filter((v) => v.name !== "盘点结果")
	}
})

const tbInit = useTbInit<MatStoreCheckPlanTaskMaterialDetailPageVo, any>()
const {
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	pageSize,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	currentPage,
	onCurrentPageChange
} = tbInit

tbInit.tableProp = computed(() => {
	const defCols: TableColumnType[] = [
		{
			prop: "regionCode",
			label: "区域编码",
			width: 80,
			fixed: "left"
		},

		{
			prop: "roomCode",
			label: "货位编码",
			width: 100,
			fixed: "left"
		},
		{
			prop: "materialCode",
			label: "物资编码",
			width: 130
		},
		{
			prop: "materialLabel",
			label: "物资名称"
		},
		{
			prop: "version",
			label: "规格型号"
		},
		{
			prop: "technicalParameter",
			label: "技术参数"
		},
		{
			prop: "attribute",
			label: "物资性质",
			needSlot: true,
			width: 120
		},
		{
			prop: "useUnit",
			label: "库存单位",
			needSlot: true,
			width: 80
		},
		{
			prop: "storeNum_view",
			label: "库存数量",
			align: "right"
		},
		{
			prop: "firstCheckNum_view",
			label: "盘点数量",
			align: "right"
		},
		{
			prop: "secondCheckNum_view",
			label: "复核数量",
			align: "right"
		},
		{
			prop: "checkDiffNum_view",
			label: "差异数量",
			align: "right",
			needSlot: true
		},
		{
			prop: "checkResult",
			label: "盘点结果",
			needSlot: true
		},
		{
			prop: "remark",
			label: "情况说明",
			minWidth: 100,
			needSlot: true
		}
	]

	if (detailData.value.status == IInventoryTaskStatus.firstTask) {
		switch (detailData.value.checkStatus) {
			case IInventoryTaskCheckStatus.notStart: // 未启动
				return tableColFilter(defCols, [
					"盘点数量",
					"复核数量",
					"差异数量",
					"盘点结果",
					"情况说明"
				])
			case IInventoryTaskCheckStatus.progress: // 盘点中
				return tableColFilter(defCols, ["复核数量", "差异数量", "盘点结果"])
			default:
				return tableColFilter(defCols, ["复核数量"])
		}
	}

	if (detailData.value.status == IInventoryTaskStatus.secondTask) {
		switch (detailData.value.checkStatus) {
			case IInventoryTaskCheckStatus.notStart: // 未启动
				return tableColFilter(defCols, ["复核数量", "盘点结果"])
			case IInventoryTaskCheckStatus.progress: // 盘点中
				return tableColFilter(defCols, ["盘点结果"])
			default:
				return defCols
		}
	}

	if (detailData.value.status == IInventoryTaskStatus.completed) {
		return tableColFilter(defCols, ["库存数量"])
	} else {
		return defCols
	}
})

/**
 * 格式化数量
 */
watch(tableData, () => {
	map(tableData.value, (item: Record<string, any>) => {
		Object.keys(item).forEach((key) => {
			if (
				[
					"storeNum",
					"firstCheckNum",
					"secondCheckNum",
					"checkDiffNum"
				].includes(key)
			) {
				if (!isNil(item[key])) {
					item[`${key + "_view"}`] = toFixedTwo(item[key])
				}
			}
		})
	})
})

/**
 * 数据源
 * @param data
 */
const getTableData = (data?: Record<string, any>) => {
	if (props.id) {
		fetchParam.value.checkPlanTaskId = props.id
		fetchParam.value.sord = "desc"
		fetchParam.value.sidx = "createdDate"

		fetchFunc.value = listInventoryJobGoodsPaged

		currentPage.value = 1
		tableRef.value?.resetCurrentPage()

		fetchParam.value = {
			...fetchParam.value,
			...data
		}
		fetchTableData()
	}
}
onMounted(async () => {
	getDictByCodeList(["INVENTORY_UNIT", "CHECK_RESULT", "MATERIAL_NATURE"])

	await getDetail()
	getTableData()
})

/**
 * 获取详情数据
 */
async function getDetail() {
	drawerLoading.value = true
	try {
		detailData.value = await getInventoryJob(props.id)

		if (detailData.value.status == IInventoryTaskStatus.secondTask) {
			fetchParam.value.secondCheckScope = detailData.value.secondCheckScope
		}
	} finally {
		drawerLoading.value = false
	}
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.custom-q {
	margin: 10px 0px -10px 10px !important;
}
</style>
