<template>
	<el-descriptions size="small" :column="1" border class="content">
		<el-descriptions-item
			v-for="desc in defDescConf"
			:key="desc.label"
			:label="desc.label"
		>
			<!-- 盘点类型 -->
			<color-tag
				v-if="desc.key === 'planType'"
				border
				v-bind="inventoryCheckTypeColorConf[descData.planType as IInventoryType]"
			>
				{{ dictFilter("CHECK_PLAN_TYPE", descData.planType)?.label }}
			</color-tag>
			<!-- 仓库级别 -->
			<color-tag
				v-else-if="desc.key === 'storeLevel' && !isNil(descData.storeLevel)"
				:bg-color="warehouseTypeTagColorMap[descData.storeLevel as IWarehouseType]"
			>
				{{ dictFilter("STORE_LEVEL", descData.storeLevel)?.label }}
			</color-tag>

			<!-- 盘点阶段 -->
			<color-tag
				v-else-if="desc.key === 'status'"
				border
				v-bind="inventoryTaskStatusColorConf[descData.status as IInventoryTaskStatus]"
			>
				{{ dictFilter("CHECK_TASK_STATUS", descData.status)?.label }}
			</color-tag>

			<!-- 盘点人员 盘点状态 -->
			<color-tag
				v-else-if="desc.key === 'checkStatus'"
				border
				v-bind="inventoryTaskCheckStatusColorConf[descData.checkStatus as IInventoryTaskCheckStatus]"
			>
				{{
					descData.checkStatus == IInventoryTaskCheckStatus.notStart
						? "未开始"
						: descData.checkStatus == IInventoryTaskCheckStatus.progress
						? descData.status == IInventoryTaskStatus.firstTask
							? "盘点中"
							: "复核中"
						: descData.checkStatus == IInventoryTaskCheckStatus.completed
						? "盘点完成"
						: "---"
				}}
			</color-tag>

			<span v-else-if="desc.key === 'secondCheckScope'">
				{{
					isNil(descData.secondCheckScope)
						? "---"
						: descData.secondCheckScope == 1
						? "全部物资"
						: "有差异物资"
				}}
			</span>

			<!-- 盘点进度 -->
			<el-progress
				v-else-if="desc.key === 'taskProcessRate'"
				:show-text="false"
				:stroke-width="14"
				:percentage="descData.taskProcessRate"
				:color="progressColor"
			/>

			<!-- 盘点结果 -->
			<color-tag
				v-else-if="desc.key === 'resultStatus'"
				border
				v-bind="IInventoryPlanResultStatusColorConf[descData.resultStatus as IInventoryPlanResultStatus]"
			>
				{{
					descData.resultStatus == IInventoryPlanResultStatus.normal
						? "正常"
						: descData.resultStatus == IInventoryPlanResultStatus.danger
						? "异常"
						: "---"
				}}
			</color-tag>

			<dict-tag
				v-else-if="desc.key === 'bpmDiffStatus'"
				:options="DictApi.getBpmStatus()"
				:value="descData.bpmDiffStatus"
			/>

			<span v-else>{{ getNumDefByNumKey(desc.key, descData[desc.key]) }}</span>
		</el-descriptions-item>
	</el-descriptions>
</template>

<script setup lang="ts">
import {
	inventoryTaskCheckStatusColorConf,
	inventoryTaskStatusColorConf,
	inventoryCheckStatusColorConf,
	inventoryCheckTypeColorConf,
	warehouseTypeTagColorMap,
	IInventoryPlanResultStatusColorConf
} from "@/app/baseline/utils/colors"
import ColorTag from "../../components/colorTag.vue"
import {
	IInventoryPlanResultStatus,
	IInventoryType,
	IInventoryTaskCheckStatus,
	IInventoryTaskStatus
} from "@/app/baseline/utils/types/inventory-manage"
import { progressColor } from "../inventoryPlan/utils"
import { useDictInit } from "../../../components/dictBase"
import { getNumDefByNumKey } from "@/app/baseline/utils"
import { IWarehouseType } from "@/app/baseline/utils/types/store-manage"
import { isNil, includes } from "lodash-es"
import { DictApi } from "@/app/baseline/api/dict"
import DictTag from "../../../components/dictTag.vue"

const props = defineProps<{
	/**
	 * 明细配置
	 */
	descOptions?: { label: string; key: string }[]

	/**
	 * 明细数据
	 */
	descData?: any
}>()

const { dictFilter, getDictByCodeList } = useDictInit()

/**
 * 默认明细配置
 */
const defDescConf = computed(() => {
	const conf = [
		{
			label: "盘点任务编号",
			key: "code"
		},
		{
			label: "盘点计划号",
			key: "checkPlanCode"
		},
		{
			label: "盘点计划名称",
			key: "checkPlanName"
		},
		{
			label: "盘点仓库名称",
			key: "storeName"
		},
		{
			label: "仓库级别",
			key: "storeLevel"
		},
		{
			label: "仓库编码",
			key: "storeCode"
		},
		{
			label: "盘点类型",
			key: "planType"
		},
		{
			label: "盘点物资编码",
			key: "materialCodeNum"
		},
		{
			label: "盘点阶段",
			key: "status"
		},
		{
			label: "盘点状态",
			key: "checkStatus"
		},
		{ label: "复核范围", key: "secondCheckScope" },
		{
			label: "盘点进度",
			key: "taskProcessRate"
		},
		{
			label: "盘点结果",
			key: "resultStatus"
		},
		{
			label: "差异物资编码",
			key: "diffMaterialCodeNum"
		},
		{
			label: "差异处理状态",
			key: "bpmDiffStatus"
		},
		{
			label: "任务开始时间",
			key: "beginDate"
		},
		{
			label: "任务结束时间",
			key: "endDate"
		},
		{
			label: "盘点人员",
			key: "firstCheckRealName"
		},
		{
			label: "复核人员",
			key: "secondCheckRealName"
		},
		{
			label: "更新时间",
			key: "lastModifiedDate"
		}
	]
	if (props.descOptions) {
		return props.descOptions
	} else {
		if (props.descData.status === IInventoryTaskStatus.completed) {
			return conf.filter(
				(v) => !includes(["盘点进度", "盘点状态", "复核范围"], v.label)
			)
		} else if (props.descData.status === IInventoryTaskStatus.secondTask) {
			return conf.filter(
				(v) => !includes(["盘点结果", "差异处理状态"], v.label)
			)
		} else {
			return conf.filter(
				(v) => !includes(["盘点结果", "差异物资编码", "差异处理状态"], v.label)
			)
		}
	}
})

onMounted(() => {
	getDictByCodeList([
		"CHECK_TASK_STATUS",
		"CHECK_PLAN_TYPE",
		"CHECK_RESULT",
		"STORE_LEVEL",
		"CHECK_TASK_FINANCIAL_POSTING_STATUS"
	])
})
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
