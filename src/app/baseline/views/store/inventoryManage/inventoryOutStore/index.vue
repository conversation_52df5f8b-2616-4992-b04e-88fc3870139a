<script lang="ts" setup>
import { computed, onMounted, ref } from "vue"
import {
	checkPermission,
	isCheckPermission
} from "@/app/platform/utils/permission"
import { powerList } from "../../../components/define.d"
import LinkTag from "@/app/baseline/views/components/linkTag.vue"
import { modalSize } from "@/app/baseline/utils/layout-config"
import { useTbInit } from "../../../components/tableBase"
import { listMatStoragePaged } from "@/app/baseline/api/store/manage-api"
import inventoryOutStoreDetail from "./inventoryOutStoreDetail.vue"
import { listCheckPlanTaskOutStorePaged } from "@/app/baseline/api/store/inventory-out-store-api"
import {
	CheckLossesOutStorePageVo,
	CheckLossesOutStorePageVoQuery
} from "@/app/baseline/utils/types/store-inventory-out-store"

import inventoryPlanDetail from "../inventoryPlan/inventoryPlanDetail.vue"
import inventoryJobDetail from "../inventoryJob/inventoryJobDetail.vue"

/**
 * 查询条件 配置
 */
const queryArrList = computed(() => {
	return [
		{
			name: "盘亏出库单号",
			key: "outStoreCode",
			placeholder: "请输入盘亏出库单号",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "关联盘点计划",
			key: "planCode",
			placeholder: "请输入关联盘点计划",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "盘点计划名称",
			key: "planName",
			placeholder: "请输入盘点计划名称",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "关联盘点任务",
			key: "taskCode",
			placeholder: "请输入关联盘点任务",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "盘点人员",
			key: "firstCheckRealname",
			placeholder: "请输入盘点人员",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "复核人员",
			key: "secondCheckRealname",
			placeholder: "请输入复核人员",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "仓库编码",
			key: "storeCode",
			placeholder: "请输入仓库编码",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "出库仓库",
			key: "storeId",
			placeholder: "请选择出库仓库",
			type: "tableSelect",
			tableInfo: [
				{
					title: "请选择出库仓库",
					tableApi: listMatStoragePaged, //表格接口
					tableColumns: [
						{ label: "仓库编码", prop: "code", width: 120 },
						{ label: "仓库名称", prop: "label" },
						{
							label: "仓库级别",
							prop: "level_view",
							width: 100
						},
						{
							label: "仓库类型",
							prop: "type_view",
							width: 120
						},
						{
							label: "所属段区",
							prop: "depotId_view",
							width: 150
						}
					],
					queryArrList: [
						{
							name: "仓库编码",
							key: "code",
							type: "input",
							placeholder: "请输入仓库编码"
						},
						{
							name: "仓库名称",
							key: "label",
							type: "input",
							placeholder: "请输入仓库名称"
						}
					],
					params: {
						currentPage: 1,
						pageSize: 20
					}, //表格接口参数

					labelName: {
						key: "id", //回显标识
						label: "label", //input框要展示的字段
						value: "id" //要给后台传的字段
					}
				}
			]
		}
	]
})

/**
 * Title 配置
 */
const rightTitle = {
	name: ["盘亏出库"],
	icon: ["fas", "square-share-nodes"]
}

const {
	tableData,
	tableProp,
	tableRef,
	tableLoading,
	currentPage,
	pageTotal,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange,
	selectedTableList
} = useTbInit<CheckLossesOutStorePageVo, CheckLossesOutStorePageVoQuery>()

/**
 * table 列配置
 */
tableProp.value = [
	{ label: "盘亏出库单号", prop: "outStoreCode", width: 200 },
	{
		label: "关联盘点计划",
		prop: "planCode", // TODO 缺关联计划ID
		needSlot: true,
		width: 200
	},
	{
		label: "盘点计划名称",
		prop: "planName"
	},
	{
		label: "关联盘点任务",
		prop: "taskCode", // // TODO 缺关联任务ID
		needSlot: true,
		width: 200
	},
	{
		label: "盘点人员",
		prop: "firstCheckRealname"
	},
	{
		label: "复核人员",
		prop: "secondCheckRealname"
	},
	{
		label: "出库仓库名称",
		prop: "storeName"
	},
	{
		label: "仓库编码",
		prop: "storeCode"
	},
	{
		label: "出库物资编码",
		prop: "materialCodeCount"
	},
	{
		label: "出库时间",
		prop: "outStoreDate",
		width: 150,
		sortable: true
	},
	{
		label: "操作",
		width: 100,
		prop: "operations",
		needSlot: true
	}
]

/**
 * table 数据源
 */
fetchFunc.value = listCheckPlanTaskOutStorePaged
const getTableData = (data?: any) => {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		sidx: "createdDate",
		sord: "desc",
		...data
	}

	fetchTableData()
}

const curRowId = ref<any>("")
const businessPlanId = ref()
const viewDrawerVisible = ref<boolean>(false)

/**
 * 查看 操作
 * @param row
 */
const onRowView = (row: CheckLossesOutStorePageVo) => {
	curRowId.value = row.taskId
	viewDrawerVisible.value = true
}

/**
 * 关联盘点计划 操作
 */
const businessPlanVisible = ref(false)
const onRowRelativePlan = (row: CheckLossesOutStorePageVo) => {
	businessPlanId.value = row.planId
	businessPlanVisible.value = true
}

/**
 * 关联盘点任务 操作
 */
const businessJobVisible = ref(false)
const onRowRelativeJob = (row: CheckLossesOutStorePageVo) => {
	curRowId.value = row.taskId
	businessJobVisible.value = true
}

/**
 * 抽屉关闭 回调操作
 * @param msg
 */
const closeDrawer = () => {
	viewDrawerVisible.value = false
	businessPlanVisible.value = false
	businessJobVisible.value = false
}

/**
 * 排序
 * @param column
 * @param prop 字段名称
 * @param order ascending 升序 descending 降序
 */
function handleSortChange(column: any, prop: any, order: any) {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		sord:
			order === "descending" ? "desc" : order === "ascending" ? "asc" : "desc",
		sidx: order
			? prop === "outStoreDate"
				? "createdDate"
				: prop
			: "createdDate" // 排序字段
	}

	fetchTableData()
}

onMounted(() => {
	getTableData()
})
</script>
<template>
	<div class="app-container">
		<div class="whole-frame">
			<ModelFrame class="top-frame">
				<Query
					:queryArrList="queryArrList"
					@getQueryData="getTableData"
					class="ml10"
				/>
			</ModelFrame>

			<ModelFrame class="bottom-frame">
				<Title :title="rightTitle" />

				<PitayaTable
					ref="tableRef"
					:columns="tableProp"
					:tableData="tableData"
					:total="pageTotal"
					:need-index="true"
					:single-select="true"
					:need-selection="false"
					@onSelectionChange="selectedTableList = $event"
					@on-current-page-change="onCurrentPageChange"
					:table-loading="tableLoading"
					@on-table-sort-change="handleSortChange"
				>
					<!-- 关联盘点计划 -->
					<template #planCode="{ rowData }">
						<link-tag
							:value="rowData.planCode"
							@on-click="onRowRelativePlan(rowData)"
						/>
					</template>

					<!-- 关联盘点任务 -->
					<template #taskCode="{ rowData }">
						<link-tag
							:value="rowData.taskCode"
							@on-click="onRowRelativeJob(rowData)"
						/>
					</template>

					<template #operations="{ rowData }">
						<slot
							v-if="
								isCheckPermission(powerList.storeInventoryOutStoreBtnPreview)
							"
						>
							<el-button
								v-btn
								link
								@click="onRowView(rowData)"
								:disabled="
									checkPermission(powerList.storeInventoryOutStoreBtnPreview)
								"
								v-if="
									isCheckPermission(powerList.storeInventoryOutStoreBtnPreview)
								"
							>
								<font-awesome-icon :icon="['fas', 'eye']" />
								<span class="table-inner-btn">查看</span>
							</el-button>
						</slot>
						<slot v-else>---</slot>
					</template>
				</PitayaTable>

				<!-- 编辑/查看弹窗 -->
				<Drawer
					:size="modalSize.xl"
					v-model:drawer="viewDrawerVisible"
					:destroyOnClose="true"
				>
					<inventory-out-store-detail :id="curRowId" @close="closeDrawer" />
				</Drawer>

				<!-- 关联盘点计划 -->
				<Drawer
					:size="modalSize.xl"
					v-model:drawer="businessPlanVisible"
					:destroyOnClose="true"
				>
					<inventory-plan-detail
						:id="businessPlanId"
						@close="businessPlanVisible = false"
					/>
				</Drawer>

				<!-- 关联盘点任务 -->
				<Drawer
					:size="modalSize.xl"
					v-model:drawer="businessJobVisible"
					:destroyOnClose="true"
				>
					<inventory-job-detail
						:id="curRowId"
						@close="businessJobVisible = false"
					/>
				</Drawer>
			</ModelFrame>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
