<!-- 物资选择器 -->
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<div class="drawer-column" style="width: 100%">
			<Title :title="titleConf" />

			<div class="rows mat-selector-table">
				<Query
					v-if="queryVisible"
					:query-arr-list="(queryConf as any)"
					style="margin: 10px 10px -10px"
					@get-query-data="getQueryData"
				/>
				<el-scrollbar>
					<pitaya-table
						ref="tableRef"
						:columns="tableProp"
						:table-data="tableData"
						:need-selection="true"
						:single-select="!multiple"
						:need-index="true"
						:need-pagination="true"
						:total="pageTotal"
						:table-loading="tableLoading"
						:cell-class-name="tbCellClassName"
						@on-selection-change="selectedTableList = $event"
						@on-current-page-change="fetchTableDataWithSetRowsCheck"
					>
						<!-- 预估回收重量 -->
						<template #recoveryWeight="{ rowData }">
							{{ toFixedTwo(rowData.recoveryWeight) }}
						</template>
						<!-- 物资性质 -->
						<template #attribute="{ rowData }">
							<dict-tag
								:options="DictApi.getMatAttr()"
								:value="rowData.attribute"
							/>
						</template>

						<template #amount="{ rowData }">
							<cost-tag :value="rowData.amount" />
						</template>

						<template #useUnit="{ rowData }">
							{{
								dictFilter("INVENTORY_UNIT", rowData.useUnit)?.subitemName ||
								"---"
							}}
						</template>

						<template #quality="{ rowData }">
							{{
								dictFilter("MAIN_MATERIALS", rowData.quality)?.subitemName ||
								"---"
							}}
						</template>

						<!-- 低值类型 -->
						<template #lowValueType="{ rowData }">
							{{
								dictFilter("LOW_VALUE_TYPE", rowData.lowValueType)
									?.subitemName || "---"
							}}
						</template>

						<!-- 辅助材质 -->
						<template #auxiliaryQuality="{ rowData }">
							{{
								dictFilter("MAIN_MATERIALS", rowData.auxiliaryQuality)
									?.subitemName || "---"
							}}
						</template>

						<!-- 废旧物资分类 -->
						<template #wasteMaterialType="{ rowData }">
							<dict-tag
								:options="DictApi.getWasteMaterialType()"
								:value="rowData.wasteMaterialType"
							/>
						</template>

						<!-- 冻结量下钻 -->
						<template #frozenNum_view="{ rowData }">
							<link-tag
								:value="rowData.frozenNum_view"
								@on-click="showFreezeNumDetail(rowData)"
							/>
						</template>
					</pitaya-table>
				</el-scrollbar>
			</div>

			<button-list
				class="footer"
				:button="btnConf"
				:loading="btnLoading"
				@on-btn-click="handleBtnClick"
			/>
		</div>

		<!-- 冻结量 查看下钻 -->
		<Drawer
			v-model:drawer="transferfreezeNumTableVisible"
			:size="modalSize.mmd"
			destroy-on-close
		>
			<transfer-freeze-table
				:material-id="editorRow.materialId"
				:sourceRoomId="editorRow.sourceRoomId"
				:batchNo="editorRow.batchNo"
				@close="transferfreezeNumTableVisible = false"
			/>
		</Drawer>
	</div>
</template>

<script setup lang="ts">
import { useTbInit } from "../../components/tableBase"
import { useDictInit } from "../../components/dictBase"
import CostTag from "../../components/costTag.vue"
import dictTag from "../../components/dictTag.vue"
import LinkTag from "../../components/linkTag.vue"
import { useTableSelectorUtils } from "../hooks/table-selector-utils"
import { includes } from "lodash-es"
import { DictApi } from "@/app/baseline/api/dict"
import { batchFormatterNumView, toFixedTwo } from "@/app/baseline/utils"
import { IInventoryBusinessType } from "@/app/baseline/utils/types/store-inventory"
import TransferFreezeTable from "@/app/baseline/views/store/transfer/transferApplication/freezeTable.vue"
import { modalSize } from "@/app/baseline/utils/layout-config"

const emit = defineEmits<{
	(e: "close"): void
	(e: "save", v: any[]): void
}>()

const props = withDefaults(
	defineProps<{
		title?: string
		/**
		 * 当前选中的id 集合
		 */
		selectedIds?: any[]
		/**
		 * table 数据源
		 */
		tableApi: (params: any) => Promise<any>

		/**
		 * table 的列配置
		 */
		columns?: TableColumnType[]

		/**
		 * 是否支持多选
		 */
		multiple?: boolean

		/**
		 * table 请求参数
		 */
		tableReqParams?: any

		/**
		 * 筛选是否可见
		 */
		queryVisible?: boolean
		/**
		 * 标红的ID
		 */
		errorIdList?: any[]
		queryArrList?: any
		businessType?: IInventoryBusinessType
	}>(),
	{
		multiple: false,
		tableReqParams: () => ({}),
		columns: () => [],
		queryVisible: true,
		title: "选择物资"
	}
)
const { dictOptions, dictFilter, getDictByCodeList } = useDictInit()

const titleConf = computed(() => ({
	name: [props.title],
	icon: ["fas", "square-share-nodes"]
}))

const queryConf = computed(() => {
	const ls = [
		{
			name: "物资编码",
			key: "code",
			type: "input",
			placeholder: "请输入物资编码"
		},
		{
			name: "物资名称",
			key: "label",
			type: "input",
			placeholder: "请输入物资名称"
		},
		{
			name: "规格型号",
			key: "version",
			placeholder: "请输入规格型号",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "物资性质",
			key: "attribute",
			type: "select",
			children: dictOptions.value.MATERIAL_NATURE,
			placeholder: "请选择物资性质"
		}
	]

	return props.queryArrList || ls
})

const drawerLoading = ref(false)

const btnLoading = ref(false)

const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	selectedTableList,
	fetchParam,
	currentPage,
	fetchTableData,
	fetchFunc
} = useTbInit()

// eslint-disable-next-line vue/no-setup-props-destructure
fetchFunc.value = props.tableApi

tableProp.value = props.columns?.length
	? props.columns
	: [
			{
				prop: "materialNo",
				label: "物资编码"
			},
			{
				prop: "materialName",
				label: "物资名称"
			},
			{
				prop: "typeCode",
				label: "物资分类编码"
			},
			{
				prop: "typeLabel",
				label: "物资分类名称"
			},
			{
				prop: "version",
				label: "规格型号"
			},
			{
				prop: "technicalParameter",
				label: "技术参数"
			},
			{
				prop: "attribute",
				label: "物资性质",
				needSlot: true,
				width: 120
			},
			{
				prop: "useUnit",
				label: "库存单位",
				needSlot: true,
				width: 80
			},
			{
				prop: "storeNum_view",
				label: "库存数量",
				align: "right"
			},
			{
				prop: "freezeNum_view",
				label: "冻结量",
				align: "right"
			}
	  ]

/**
 * 格式化数量
 */
watch(tableData, () => {
	batchFormatterNumView(tableData.value as any[])
})

const { fetchTableDataWithSetRowsCheck } = useTableSelectorUtils({
	tableData,
	tableRef,
	selectedIds: props.selectedIds,
	fetchTableData
})

const btnConf = computed(() => {
	return [
		{
			name: "取消",
			icon: ["fas", "circle-minus"]
		},
		{
			name: "保存",
			icon: ["fas", "floppy-disk"],
			disabled: selectedTableList.value.length > 0 ? false : true
		}
	]
})

function tbCellClassName(data: {
	row: any
	column: any
	rowIndex: number
	columnIndex: number
}): string {
	return includes(props.errorIdList ?? [], data.row.id) ? "error" : ""
}

onMounted(() => {
	getDictByCodeList([
		"INVENTORY_UNIT",
		"MAIN_MATERIALS",
		"LOW_VALUE_TYPE",
		"MATERIAL_NATURE"
	])
	fetchParam.value = { ...fetchParam.value, ...props.tableReqParams }

	fetchTableDataWithSetRowsCheck()
})

function getQueryData(e: any) {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = { ...fetchParam.value, ...e }
	fetchTableDataWithSetRowsCheck()
}

async function handleBtnClick(name?: string) {
	if (name === "取消") {
		return emit("close")
	}

	btnLoading.value = true
	try {
		await new Promise((resolve, reject) => {
			try {
				emit("save", selectedTableList.value)
				//resolve("success")
			} catch (error) {
				// 如果事件处理出错，手动拒绝Promise
				reject(error)
			}
		})
		/* emit("save", selectedTableList.value) */
	} finally {
		btnLoading.value = false
	}
}

/**
 * 查看 冻结数量
 */
const transferfreezeNumTableVisible = ref(false)
const editorRow = ref<Record<string, any>>({})
function showFreezeNumDetail(e: Record<string, any>) {
	if (
		props?.businessType == IInventoryBusinessType.transferApplyKW ||
		props?.businessType == IInventoryBusinessType.transferApplyKN
	) {
		editorRow.value = { ...e }
		transferfreezeNumTableVisible.value = true
	}
	console.log("e", e)
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.mat-selector-table {
	&:deep(.pitaya-table) {
		.error {
			.column-inner-item,
			.cell,
			.el-input__inner {
				color: red;
			}
		}
	}
}
</style>
