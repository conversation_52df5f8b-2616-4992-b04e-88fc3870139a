<!-- 入库申请-详情-物资明细 table -->

<template>
	<Query
		:query-arr-list="(queryArrList as any)"
		style="margin: 10px 10px -10px"
		@get-query-data="handleQuery"
	/>
	<div class="editor-table-wrapper">
		<pitaya-table
			ref="tableRef"
			:columns="tableProp"
			:table-data="tableData"
			:need-selection="mode !== IModalType.view"
			:single-select="false"
			:need-index="true"
			:need-pagination="true"
			:total="pageTotal"
			:table-loading="tableLoading"
			:cell-class-name="tbCellClassName"
			@on-selection-change="selectedTableList = $event"
			@on-current-page-change="onCurrentPageChange"
		>
			<!-- 物资性质 -->
			<template #attribute="{ rowData }">
				<dict-tag :options="DictApi.getMatAttr()" :value="rowData.attribute" />
			</template>
			<template #useUnit="{ rowData }">
				{{
					dictFilter("INVENTORY_UNIT", rowData.useUnit)?.subitemName || "---"
				}}
			</template>
			<template #amount="{ rowData }">
				<cost-tag :value="rowData.amount" />
			</template>
			<template #price="{ rowData }">
				<cost-tag :value="rowData.price" />
			</template>

			<template v-if="mode === IModalType.edit" #footerOperateLeft>
				<button-list
					:button="btnConf"
					:is-not-radius="true"
					@on-btn-click="handleBtnClick"
				/>
			</template>
		</pitaya-table>
	</div>
	<Drawer
		v-model:drawer="userSelectorVisible"
		:size="modalSize.md"
		destroy-on-close
	>
		<user-selector
			:table-api="userOptionsApi"
			:table-props="userSelectorTableProps"
			@close="userSelectorVisible = false"
			@save="handleUserSelected"
		/>
	</Drawer>
</template>

<script setup lang="ts">
import { DictApi } from "@/app/baseline/api/dict"
import {
	MatInStoreApplyItemListQueryParams,
	MatInStoreApplyItemVo
} from "@/app/baseline/utils/types/warehousing-apply"
import { IModalType } from "@/app/baseline/utils/types/common"
import Query from "@/compontents/Query.vue"
import { modalSize } from "@/app/baseline/utils/layout-config"
import userSelector from "./userSelector.vue"
import { first, map } from "lodash-es"
import { useTbInit } from "../../components/tableBase"
import { SystemUserVo } from "@/app/baseline/utils/types/system"
import CostTag from "../../components/costTag.vue"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import { useDictInit } from "../../components/dictBase"
import { MatInStoreApplyInspectDTO } from "@/app/baseline/utils/types/other-warehousing"
import { batchFormatterNumView } from "@/app/baseline/utils"

const props = withDefaults(
	defineProps<{
		/**
		 * 入库申请id
		 */
		applyId: any
		/**
		 * 详情编辑模式 查看/编辑
		 */
		mode?: IModalType

		/**
		 * 添加质检员 api
		 */
		addInspectorApi?: (
			e: MatInStoreApplyInspectDTO
		) => Promise<boolean | undefined>

		/**
		 * 申请下的物资明细列表 api
		 */
		listGoodsApi: (params: any) => Promise<any>

		/**
		 * 用户列表api
		 */
		userOptionsApi?: (params?: any) => Promise<any>

		/**
		 * 隐藏的筛选项目key列表
		 */
		hiddenQueryKey?: string[]

		/**
		 * table columns 配置
		 */
		columns?: TableColumnType[]

		maxHeight?: number
		tbCellClassName?: (params: any) => string
	}>(),
	{
		mode: IModalType.view
	}
)

const userSelectorVisible = ref(false)

const queryData = ref({})
const { dictOptions, dictFilter, getDictByCodeList } = useDictInit()

const btnConf = computed(() => [
	{
		name: "分配质检员",
		icon: ["fas", "user-check"],
		disabled: selectedTableList.value.length < 1,
		confirm: true
	}
])

const userSelectorTableProps = [
	{
		prop: "realname",
		label: "用户姓名"
	},
	{
		prop: "username",
		label: "用户账号"
	},
	{
		prop: "sex",
		label: "性别",
		needSlot: true
	},
	{
		prop: "phone",
		label: "手机号"
	},
	{
		prop: "team",
		label: "质检组"
	},
	{
		prop: "sysOrgId_view",
		label: "部门"
	}
]

const queryArrList = computed<querySetting[]>(() => {
	const list = [
		{
			name: "是否分配质检员",
			key: "existFlag",
			type: "select",
			children: [
				{ label: "待分配质检员", value: "0" },
				{ label: "已分配质检员", value: "1" }
			]
		},
		{
			name: "物资编码",
			key: "materialCode",
			type: "input",
			placeholder: "请输入物资编码"
		},
		{
			name: "物资名称",
			key: "materialLabel",
			type: "input",
			placeholder: "请输入物资名称"
		},
		{
			name: "规格型号",
			key: "version",
			placeholder: "请输入规格型号",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "物资性质",
			key: "attribute",
			type: "select",
			children: dictOptions.value.MATERIAL_NATURE,
			placeholder: "请选择物资性质"
		}
	]

	if (props.hiddenQueryKey?.length) {
		return list.filter((v) => !props.hiddenQueryKey?.includes(v.key))
	}

	return list
})

const {
	tableProp,
	tableData,
	tableRef,
	currentPage,
	tableLoading,
	pageTotal,
	fetchParam,
	selectedTableList,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = useTbInit<MatInStoreApplyItemVo, MatInStoreApplyItemListQueryParams>()

// eslint-disable-next-line vue/no-setup-props-destructure
fetchFunc.value = props.listGoodsApi

tableProp.value = props.columns ?? [
	{
		prop: "materialCode",
		label: "物资编码"
	},
	{
		prop: "materialName",
		label: "物资名称"
	},
	{
		prop: "version",
		label: "规格型号"
	},
	{
		prop: "technicalParameter",
		label: "技术参数"
	},
	{
		prop: "attribute",
		label: "物资性质",
		needSlot: true,
		width: 120
	},
	{
		prop: "useUnit",
		label: "库存单位",
		needSlot: true,
		width: 80
	},
	{
		prop: "price",
		label: "采购单价（元）",
		needSlot: true,
		align: "right"
	},
	{
		prop: "num_view",
		label: "到货数量",
		align: "right"
	},
	{
		prop: "amount",
		label: "金额（元）",
		needSlot: true,
		align: "right"
	},
	{
		prop: "inspectionPersonId_view",
		label: "质检员",
		width: 100
	}
]

/**
 * 格式化数量
 */
watch(tableData, () => {
	batchFormatterNumView(tableData.value as any[])
})

onMounted(() => {
	getDictByCodeList(["INVENTORY_UNIT", "MATERIAL_NATURE"])
	fetchParam.value = {
		...fetchParam.value,
		applyId: props.applyId,
		sord: "desc",
		sidx: "createdDate"
	}
	fetchTableData()
})

function handleBtnClick(name?: any) {
	if (name === "分配质检员") {
		userSelectorVisible.value = true
	}
}

/**
 * 选择用户 handler
 */
function handleUserSelected(e?: SystemUserVo[], callback?: any) {
	const username = first(e)?.username
	if (!username) {
		callback()
		return ElMessage.warning("请至少选择一个人员！")
	}

	// 选中的物资id list
	const itemIds = map(selectedTableList.value, ({ id }) => id) ?? []

	// 添加质检员
	props.addInspectorApi?.({ itemIds, userId: username }).then(() => {
		ElMessage.success("操作成功")
		fetchTableData()
		userSelectorVisible.value = false
	})
}

function handleQuery(e?: any) {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	queryData.value = e
	fetchParam.value = { ...fetchParam.value, ...queryData.value }
	fetchTableData()
}

defineExpose({ getTableData: handleQuery })
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.editor-table-wrapper {
	&:deep(.pitaya-table) {
		.error {
			.column-inner-item,
			.cell,
			.el-input__inner {
				color: red;
			}
		}
	}
}
</style>
