<script setup lang="ts">
import { onMounted, computed } from "vue"
import { useTbInit } from "@/app/baseline/views/components/tableBase"
import { listMatStoragePaged } from "@/app/baseline/api/store/manage-api"
import {
	IWarehouseStatus,
	IWarehouseType,
	MatStoreDTO,
	MatStoreVo
} from "@/app/baseline/utils/types/store-manage"
import { warehouseTypeTagColorMap } from "@/app/baseline/utils/colors"
import { useDictInit } from "../../components/dictBase"
import colorTag from "./colorTag.vue"
import { first, isNil, map } from "lodash-es"
import { useTableSelectorUtils } from "../hooks/table-selector-utils"
import { BaseLineSysApi } from "@/app/baseline/api/system"
import { SystemDepotVo } from "@/app/baseline/utils/types/system-depot"

const props = withDefaults(
	defineProps<{
		/**
		 * 是否支持多选，默认单选
		 */
		multiple?: boolean
		filterStoreTypes?: any[]

		tableApi?: (arg?: any) => any

		tableApiParams?: any
		queryArrList?: any

		/**
		 * 当前选中的id 集合
		 */
		selectedIds?: any[]
	}>(),
	{
		filterStoreTypes: () => []
	}
)

const { dictOptions, getDictByCodeList } = useDictInit()

const emits = defineEmits<{
	(e: "onSave", btnName: string, v?: MatStoreVo | MatStoreVo[]): void
}>()

/**
 * title 配置
 */
const matStoreDrawerTitle = {
	name: ["选择仓库"],
	icon: ["fas", "square-share-nodes"]
}

/**
 * 根据 filterStoreTypes 过滤 storeType；
 */
const newStoreTypes = computed<Record<string, any>[]>(() => {
	if (props.filterStoreTypes && props.filterStoreTypes?.length > 0) {
		return (dictOptions.value["STORE_TYPE"] || []).filter(
			(c) => !props.filterStoreTypes.includes(c.value)
		)
	} else if (
		props.tableApiParams &&
		props.tableApiParams?.type?.indexOf(",") > -1
	) {
		return (dictOptions.value["STORE_TYPE"] || []).filter((c) =>
			props.tableApiParams?.type?.split(",").includes(c.value)
		)
	} else {
		return dictOptions.value["STORE_TYPE"] || []
	}
})

const depotList = ref<SystemDepotVo[]>([])

/**
 * 获取段区配置
 */
function getDepotList() {
	BaseLineSysApi.getDepotList().then((res) => {
		depotList.value = res
	})
}

/**
 * query查询条件 配置
 */
const queryArrMatStoreList = computed(() => {
	const ls = [
		{
			name: "仓库编码",
			key: "code",
			type: "input",
			placeholder: "请输入仓库编码"
		},
		{
			name: "仓库名称",
			key: "label",
			type: "input",
			placeholder: "请输入仓库名称"
		},
		{
			name: "段区",
			key: "depotId",
			type: "select",
			children: depotList.value,
			placeholder: "请选择段区"
		},
		{
			name: "仓库级别",
			key: "level",
			type: "select",
			children: dictOptions.value["STORE_LEVEL"]
		},
		{
			name: "仓库类型",
			key: "type",
			type: "select",
			children: newStoreTypes.value
		}
	]

	if (props.queryArrList) {
		return props.queryArrList
	} else {
		if (
			isNil(props.tableApiParams?.type) ||
			props.tableApiParams?.type.indexOf(",") > -1
		) {
			if (props.tableApiParams?.depotId) {
				return ls.filter((c) => !["段区"].includes(c.name))
			} else {
				return ls
			}
		} else {
			return ls.filter((c) => !["仓库类型"].includes(c.name))
		}
	}
})

const btnLoading = ref(false)
const {
	tableProp,
	tableRef,
	tableData,
	tableLoading,
	tbBtnLoading,
	pageTotal,
	selectedTableList,
	tbBtns,
	fetchParam,
	currentPage,
	fetchTableData,
	fetchFunc,
	onBtnClick
} = useTbInit<MatStoreVo, MatStoreDTO>()

/**
 * table 列 配置
 */
tableProp.value = [
	{
		label: "仓库编码",
		prop: "code",
		width: 120
	},
	{
		label: "仓库名称",
		prop: "label",
		width: 200
	},
	{
		label: "仓库级别",
		prop: "level_view",
		width: 100
	},
	{
		label: "仓库类型",
		prop: "type",
		needSlot: true,
		width: 120
	},
	{
		label: "所属公司",
		prop: "sysCommunityId_view",
		width: 150
	},
	{
		label: "所属段区",
		prop: "depotId_view",
		width: 100
	},
	{
		label: "成本中心",
		prop: "costCenterId_view",
		width: 120
	},
	{
		label: "仓库位置",
		prop: "positionId",
		minWidth: 120
	},
	{
		label: "库管员",
		prop: "storeManage",
		minWidth: 120
	}
]

fetchFunc.value = props.tableApi ?? listMatStoragePaged

const { fetchTableDataWithSetRowsCheck } = useTableSelectorUtils({
	tableData,
	tableRef,
	selectedIds: props.selectedIds,
	fetchTableData
})

/**
 * Table 按钮配置
 */
const matStoreBtnList = computed(() => {
	return [
		{ name: "取消", icon: ["fas", "circle-minus"] },
		{
			name: "保存",
			icon: ["fas", "floppy-disk"],
			disabled: false //selectedTableList.value.length > 0 ? false : true
		}
	]
})

const onMatStoreBtnList = async (btnName: string | undefined) => {
	const selectRows = tableRef.value?.pitayaTableRef?.getSelectionRows()

	if (btnName === "保存") {
		if (selectRows.length === 0) {
			return ElMessage.warning("请选择仓库！")
		}
	}

	btnLoading.value = true
	try {
		await new Promise((resolve, reject) => {
			try {
				emits(
					"onSave",
					btnName as any,
					props.multiple ? selectRows : first(selectRows)
				)
				//resolve("success")
			} catch (error) {
				// 如果事件处理出错，手动拒绝Promise
				reject(error)
			}
		})
	} finally {
		btnLoading.value = false
	}
}

const handleQuery = (e?: any) => {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		...e
	}
	if (props.filterStoreTypes && props.filterStoreTypes?.length > 0) {
		const types = map(newStoreTypes.value, ({ value }) => value).toString()
		fetchParam.value.type = e?.type || types
	}

	if (props.tableApiParams && props.tableApiParams?.type) {
		fetchParam.value.type = e?.type || props.tableApiParams?.type
	}

	fetchTableDataWithSetRowsCheck()
}
onMounted(async () => {
	fetchParam.value.status = IWarehouseStatus.activated
	fetchParam.value.sord = "desc"
	fetchParam.value.sidx = "createdDate"

	if (props.tableApiParams) {
		fetchParam.value = {
			...fetchParam.value,
			...props.tableApiParams
		}
	}
	getDepotList()
	await getDictByCodeList(["STORE_LEVEL", "STORE_TYPE", "STORE_STATUS"])
	handleQuery()
})
</script>
<template>
	<div class="drawer-container">
		<div class="drawer-column">
			<div class="rows">
				<Title :title="matStoreDrawerTitle" />
				<div style="margin: 10px 10px -10px">
					<Query
						:queryArrList="queryArrMatStoreList"
						@getQueryData="handleQuery"
						class="custom-q"
					/>
				</div>
				<div class="common-from-group" style="padding: 0px">
					<el-scrollbar>
						<PitayaTable
							ref="tableRef"
							:columns="tableProp"
							:table-data="tableData"
							:need-index="true"
							:need-pagination="true"
							:single-select="!multiple"
							:need-selection="true"
							:total="pageTotal"
							@onSelectionChange="selectedTableList = $event"
							@on-current-page-change="fetchTableDataWithSetRowsCheck"
							:table-loading="tableLoading"
						>
							<!-- 仓库类型 -->
							<template #type="{ rowData }">
								<color-tag
									:bg-color="warehouseTypeTagColorMap[rowData.type as IWarehouseType]"
								>
									{{ rowData.type_view }}
								</color-tag>
							</template>
							<template #footerOperateLeft>
								<ButtonList
									class="btn-list"
									:is-not-radius="true"
									:button="tbBtns"
									v-loading="tbBtnLoading"
									@on-btn-click="onBtnClick"
								/>
							</template>
						</PitayaTable>
					</el-scrollbar>
				</div>
			</div>
			<ButtonList
				class="footer"
				:button="matStoreBtnList"
				:loading="btnLoading"
				@on-btn-click="onMatStoreBtnList"
			/>
			<!-- :loading="loading" -->
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.drawer-column {
	width: 100%;
}
</style>
