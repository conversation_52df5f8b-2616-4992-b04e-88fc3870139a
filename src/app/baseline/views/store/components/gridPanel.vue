<template>
	<el-row class="grid-panel-wrapper">
		<div
			v-for="v in props.isFormat ? formateOptions : props.options"
			:key="v.label"
			class="grid-item"
		>
			<div class="content">
				<div class="value">{{ v.value }}</div>
				<div class="label">{{ v.label }}</div>
			</div>
		</div>
	</el-row>
</template>

<script setup lang="ts">
import { convertToUnit } from "@/app/baseline/utils"
import { map } from "xe-utils"

const props = withDefaults(
	defineProps<{
		options: { value: any; label: any }[]
		isFormat?: boolean
	}>(),
	{ isFormat: true }
)

const formateOptions = computed(() => {
	return map(props.options, (v) => {
		const res = convertToUnit(v.value)
		return {
			...v,
			value: res.num,
			label: res.unit ? v.label + `(${res.unit})` : v.label
		}
	})
})
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.grid-panel-wrapper {
	border-bottom: 1px solid #ccc;
}

.grid-item {
	flex: 1;
	padding: 15px 0;

	&:not(:first-child) {
		.content {
			border-left: 1px solid #ccc;
		}
	}

	.content {
		height: auto !important;
		text-align: center;

		.value {
			line-height: 1;
			font-size: 40px;
			font-weight: bolder;
			color: $---color-info;
		}

		.label {
			margin-top: 8px;
			line-height: 1;
			font-size: var(--pitaya-fs-12) !important;
			color: var(--pitaya-place-font-color) !important;
		}
	}
}
</style>
