<!-- 采购订单详情 -->
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<div class="drawer-column left" style="width: 310px">
			<Title :title="titleConf" />

			<el-descriptions size="small" :column="1" border class="content">
				<el-descriptions-item
					v-for="desc in descOptions"
					:key="desc.label"
					:label="desc.label"
				>
					<dict-tag
						v-if="desc.key === 'source'"
						:options="DictApi.getPurchaseOrderSource()"
						:value="descData.source"
					/>

					<dict-tag
						v-else-if="desc.key === 'projectPurchaseType'"
						:value="descData.projectPurchaseType"
						:options="dictOptions.CONTRACT_TYPE"
					/>

					<cost-tag
						v-else-if="desc.key === 'orderingAmount'"
						:value="descData.orderingAmount"
					/>
					<span v-else-if="desc.type == 'date'">
						{{ getOnlyDate(descData[desc.key]) || "---" }}
					</span>

					<span v-else>{{ descData[desc.key] || "----" }}</span>
				</el-descriptions-item>
			</el-descriptions>
		</div>
		<div class="drawer-column right" style="width: calc(100% - 310px)">
			<Title :title="extraTitleConf">
				<Tabs
					style="margin-right: auto; margin-left: 30px"
					:tabs="tabsConf"
					@on-tab-change="handleTabChange"
				/>
			</Title>

			<el-scrollbar class="rows">
				<!-- 物资明细 table -->
				<Query
					:query-arr-list="(queryArrList as any)"
					style="margin: 10px 10px -10px"
					@get-query-data="handleQuery"
					v-if="activatedTab === 0"
				/>
				<pitaya-table
					v-if="activatedTab === 0"
					ref="tableRef"
					:columns="tableProp"
					:table-data="fmtTableData"
					:need-selection="false"
					:single-select="false"
					:need-index="true"
					:need-pagination="true"
					:total="pageTotal"
					:table-loading="tableLoading"
					@on-selection-change="(e) => (selectedTableList = e)"
					@on-current-page-change="onCurrentPageChange"
				>
					<!-- 物资性质 -->
					<template #attribute="{ rowData }">
						<dict-tag
							:options="DictApi.getMatAttr()"
							:value="rowData.attribute"
						/>
					</template>

					<!-- 库存单位 -->
					<template #content_buyUnit="{ rowData }">
						{{
							dictFilter("INVENTORY_UNIT", rowData.content_buyUnit)?.label ||
							"---"
						}}
					</template>
					<template #purchaseUnitPrice="{ rowData }">
						<cost-tag :value="rowData.purchaseUnitPrice" />
					</template>
					<template #orderingAmount="{ rowData }">
						<cost-tag :value="rowData.orderingAmount" />
					</template>

					<!-- 在途数量  在途数量=订货数量-已到货数量 -->
					<!-- <template #completedComputedNum="{ rowData }">
						{{
							(rowData.orderingNum || 0) - (rowData.completedArrivedNum || 0)
						}}
					</template> -->
				</pitaya-table>
				<!-- 相关附件 table -->
				<table-file
					v-else
					:business-type="fileBusinessType.purchaseOrder"
					:business-id="purchaseOrderId"
					:mod="IModalType.view"
				/>
			</el-scrollbar>

			<button-list
				class="footer"
				:button="btnConf"
				@on-btn-click="emit('close')"
			/>
		</div>
	</div>
</template>

<script setup lang="ts">
import { GetPurchaseOrderItemListRequest } from "@/app/baseline/api/defines"
import { PurchaseOrderApi } from "@/app/baseline/api/purchase/purchaseOrder"
import { PurchaseOrderItemApi } from "@/app/baseline/api/purchase/purchaseOrderItem"
import { forEach } from "lodash-es"
import { map, keys } from "xe-utils"
import { useDictInit } from "../../components/dictBase"
import { useTbInit } from "../../components/tableBase"
import { IModalType } from "@/app/baseline/utils/types/common"
import { DictApi, fileBusinessType } from "@/app/baseline/api/dict"
import CostTag from "../../components/costTag.vue"
import DictTag from "../../components/dictTag.vue"
import TableFile from "../../components/tableFile.vue"
import { isValidateNumber } from "@/app/baseline/utils/validate"
import { getOnlyDate } from "@/app/baseline/utils"
const props = defineProps<{
	/**
	 * 采购订单id
	 */
	purchaseOrderId: any

	/**
	 * table columns 配置
	 */
	columns?: TableColumnType[]
}>()

const emit = defineEmits<{
	(e: "close"): void
}>()

const btnConf = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	}
]

const drawerLoading = ref(false)

const { dictOptions, dictFilter, getDictByCodeList } = useDictInit()

const activatedTab = ref(0)

const titleConf: any = {
	name: ["采购订单"],
	icon: ["fas", "square-share-nodes"]
}

const extraTitleConf: any = {
	name: ["扩展信息"],
	icon: ["fas", "square-share-nodes"]
}

const handleTabChange = (index: number) => {
	activatedTab.value = index

	if (index === 0) {
		fetchParam.value = {
			orderId: props.purchaseOrderId,
			sord: "desc",
			sidx: "createdDate"
		}

		pageSize.value = 20

		handleQuery()
	}
}

const tabsConf = ["物资明细", "相关附件"]

const descOptions = [
	{
		label: "年度",
		key: "year"
	},
	{
		label: "月份",
		key: "month"
	},
	{
		label: "采购订单号",
		key: "code"
	},
	{
		label: "采购订单名称",
		key: "label"
	},
	{
		label: "采购计划号",
		key: "planPurchaseCode"
	},
	{
		label: "采购项目号",
		key: "projectCode"
	},
	{
		label: "采购项目名称",
		key: "projectLabel"
	},
	{
		label: "供应商名称",
		key: "supplierLabel"
	},
	{
		label: "合同编号",
		key: "contractCode"
	},
	{
		label: "采购方式",
		key: "projectPurchaseType"
	},
	{
		label: "订货金额",
		key: "orderingAmount"
	},
	{
		label: "最晚送货日期",
		key: "latestDeliveryDate",
		type: "date"
	},
	{
		label: "采购员",
		key: "purchaseUserName"
	},
	{
		label: "订单来源",
		key: "source"
	},
	{
		label: "操作人",
		key: "lastModifiedBy_view"
	},
	{
		label: "操作时间",
		key: "lastModifiedDate"
	}
]

/**
 * 采购订单数据
 */
const descData = ref<any>({})

const queryArrList = computed<querySetting[]>(() => {
	return [
		{
			name: "物资编码",
			key: "materialCode",
			type: "input",
			placeholder: "请输入物资编码"
		},
		{
			name: "物资名称",
			key: "materialLabel",
			type: "input",
			placeholder: "请输入物资名称"
		},
		{
			name: "规格型号",
			key: "version",
			placeholder: "请输入规格型号",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "物资性质",
			key: "attribute",
			type: "select",
			children: dictOptions.value.MATERIAL_NATURE,
			placeholder: "请选择物资性质"
		}
	]
})

const {
	pageSize,
	currentPage,
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	selectedTableList,
	fetchTableData,
	fetchParam,
	fetchFunc,
	onCurrentPageChange
} = useTbInit<any[], GetPurchaseOrderItemListRequest>()

fetchFunc.value = PurchaseOrderItemApi.getPurchaseOrderItemList

/**
 * pitaya table 无法读取 嵌套对象属性，所以将嵌套对象拍平处理
 */
const fmtTableData = computed(() => {
	return map(tableData.value, (v: any) => {
		forEach(
			keys(v.content),
			(contentKey) => (v["content_" + contentKey] = v.content[contentKey])
		)

		/* 计算在途数量 */
		const completedComputedNum =
			(v.orderingNum || 0) - (v.completedArrivedNum || 0)

		v.completedComputedNum = isValidateNumber(
			completedComputedNum as unknown as string
		)
			? completedComputedNum
			: Number(
					parseInt((completedComputedNum! * 10000) as unknown as string) / 10000
			  )
		return v
	})
})

tableProp.value = props.columns ?? [
	{
		prop: "content_code",
		label: "物资编码",
		fixed: "left",
		width: 130
	},
	{
		prop: "content_label",
		label: "物资名称",
		minWidth: 100
	},
	{
		prop: "content_version",
		label: "规格型号",
		minWidth: 100
	},
	{
		prop: "content_technicalParameter",
		label: "技术参数",
		minWidth: 100
	},
	{
		prop: "attribute",
		label: "物资性质",
		needSlot: true,
		width: 120
	},
	{
		prop: "content_buyUnit",
		label: "采购单位",
		needSlot: true,
		width: 90
	},
	{
		prop: "purchaseUnitPrice",
		label: "采购单价",
		needSlot: true,
		width: 120
	},
	{ label: "采购数量", prop: "purchaseNum", width: 90 },
	{ label: "订货数量", prop: "orderingNum", width: 90 },
	{ label: "订货金额", prop: "orderingAmount", needSlot: true, width: 120 },
	{
		label: "已到货",
		prop: "completedArrivedNum",
		width: 90
	},
	{
		label: "在途数量",
		prop: "completedComputedNum",
		width: 90
	},
	{ label: "质检合格", prop: "qualityPassNum", width: 90 },
	{
		label: "质检不合格",
		prop: "qualityFailNum",
		width: 90
	},
	{
		label: "已入库量",
		prop: "completedStoredNum",
		width: 90,
		fixed: "right"
	},
	{
		label: "退货数量",
		prop: "completedReturnedNum",
		width: 90,
		fixed: "right"
	},
	{
		label: "已开票",
		prop: "completedInvoicedNum",
		width: 90,
		fixed: "right"
	},
	{ label: "配送仓库名称", prop: "storeLabel", width: 150, fixed: "right" }
]

onMounted(() => {
	getDictByCodeList(["CONTRACT_TYPE", "INVENTORY_UNIT", "MATERIAL_NATURE"])
	getPurchaseOrder()
	fetchParam.value.orderId = props.purchaseOrderId
	fetchParam.value.sord = "desc"
	fetchParam.value.sidx = "createdDate"

	fetchTableData()
})

function handleQuery(e?: any) {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = { ...fetchParam.value, ...e }
	fetchTableData()
}

/**
 * 获取采购订单
 */
function getPurchaseOrder() {
	PurchaseOrderApi.getPurchaseOrder(props.purchaseOrderId).then(
		(r) => (descData.value = r)
	)
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
