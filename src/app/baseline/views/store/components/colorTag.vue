<!-- 颜色标签 tag -->

<template>
	<el-tag
		size="small"
		:style="style"
		v-bind="$attrs"
		v-if="props.borderColor && props.bgColor"
	>
		<slot />
	</el-tag>
	<span v-else>
		<slot />
	</span>
</template>

<script setup lang="ts">
import { CSSProperties } from "vue"

const props = withDefaults(
	defineProps<{
		/**
		 * 背景颜色
		 */
		bgColor?: string
		/**
		 * 边框颜色
		 */
		borderColor?: string
		/**
		 * 文字颜色
		 */
		color?: string

		/**
		 * 是否启用边框
		 */
		border?: boolean
	}>(),
	{
		color: "white",
		border: false
	}
)

const style = computed<CSSProperties>(() => {
	return {
		color: props.color,
		borderWidth: props.border ? 1 : 0,
		borderColor: props.borderColor,
		backgroundColor: props.bgColor
	}
})
</script>
