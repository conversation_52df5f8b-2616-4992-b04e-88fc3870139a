<script lang="ts" setup>
import {
	checkPermission,
	isCheckPermission
} from "@/app/platform/utils/permission"

interface btnObj {
	name?: string
	icon?: string[]
	class?: string
	disabled?: boolean
	roles?: any
}

interface Props {
	button: btnObj[]
	isNotRadius?: boolean
	loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
	loading: false
})
const button = toRef(props, "button")
const isNotRadius = toRef(props, "isNotRadius")
const loading = toRef(props, "loading")
const emit = defineEmits<{
	(e: "onBtnClick", btnName: string | undefined): void
}>()

const onClick = (btn: string | undefined) => {
	emit("onBtnClick", btn)
}
</script>

<template>
	<div class="common-btn-list-wrapper">
		<template v-for="btn in button" :key="btn.name">
			<template v-if="btn.roles">
				<el-button
					v-if="isCheckPermission(btn.roles)"
					:disabled="checkPermission(btn.roles) || btn.disabled"
					v-btn
					:loading="loading"
					:key="btn.name"
					type="primary"
					:class="[isNotRadius ? 'button-not-radius' : '', btn.class]"
					v-debounce="{ cb: onClick, extArgs: [btn.name] }"
				>
					<!-- @click="onClick(btn.name)" -->
					<font-awesome-icon
						v-if="btn.icon && btn.icon.length"
						:icon="btn.icon"
					/>
					<span class="btn-text">{{ btn.name }}</span>
				</el-button>
			</template>
			<el-button
				:disabled="btn.disabled"
				v-else
				v-btn
				:loading="loading"
				type="primary"
				:color="btn.class"
				:class="[isNotRadius ? 'button-not-radius' : '', btn.class]"
				v-debounce="{ cb: onClick, extArgs: [btn.name] }"
			>
				<font-awesome-icon
					v-if="btn.icon && btn.icon.length"
					:icon="btn.icon"
				/>
				<span class="btn-text">{{ btn.name }}</span>
			</el-button>
		</template>
	</div>
</template>

<style lang="scss" scoped>
.common-btn-list-wrapper {
	display: flex;
	flex-wrap: wrap;
	:deep(.el-button.button-not-radius) {
		border-radius: 0;
	}
	.btn-text {
		margin-left: 5px;
		padding-top: 1px;
	}
}

:deep(.el-button.is-disabled) {
	background-color: #f6f6f6;
	color: #999 !important;
	border: 1px solid #dcdfe6 !important;
}
</style>
