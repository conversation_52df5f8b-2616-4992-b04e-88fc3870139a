<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<div class="drawer-column" style="width: 100%">
			<Title :title="titleConf" />

			<div class="rows">
				<Query
					:query-arr-list="(queryConf as any)"
					style="margin: 10px 10px -10px"
					@get-query-data="getQueryData"
				/>
				<pitaya-table
					ref="tableRef"
					:columns="tableProp"
					:table-data="tableData"
					:need-selection="true"
					:single-select="!multiple"
					:need-index="true"
					:need-pagination="true"
					:total="pageTotal"
					:table-loading="tableLoading"
					@on-selection-change="(e) => (selectedTableList = e)"
					@on-current-page-change="handleCurrentPageChange"
				>
					<template #sex="{ rowData }">
						{{ rowData.sex === 1 ? "男" : "女" }}
					</template>
				</pitaya-table>
			</div>

			<button-list
				class="footer"
				:button="btnConf"
				:loading="btnLoading"
				@on-btn-click="handleBtnClick"
			/>
		</div>
	</div>
</template>

<script setup lang="ts">
import { BaseLineSysApi, listSysUserPaged } from "@/app/baseline/api/system"
import { useTbInit } from "../../components/tableBase"
import { SystemUserDTO, SystemUserVo } from "@/app/baseline/utils/types/system"
import { useTableSelectorUtils } from "../hooks/table-selector-utils"
import { useUserStore } from "@/app/platform/store/modules/user"

const props = defineProps<{
	/**
	 * 当前选中的 table row id
	 */
	selectedIds?: any[]

	/**
	 * 是否多选
	 */
	multiple?: boolean

	/**
	 * 自定义 table columns
	 */
	tableProps?: TableColumnType[]

	/**
	 * 自定义 table api
	 */
	tableApi?: (params?: any) => Promise<any>

	/**
	 * 自定义 table fetch 参数
	 */
	tableFetchParams?: any

	/**
	 * 判断部门是否disable
	 */
	sysOrgId?: any
	sysOrgIdView?: any
}>()

const emit = defineEmits<{
	(e: "close"): void
	(e: "save", v?: SystemUserVo[], fn?: any): void
}>()

const { userInfo } = storeToRefs(useUserStore())

const drawerLoading = ref(false)

const titleConf = {
	name: ["选择用户"],
	icon: ["fas", "square-share-nodes"]
}

const queryConf = computed(() => {
	return [
		{
			name: "部门",
			key: "sysOrgId",
			type: "treeSelect",
			placeholder: props.sysOrgId ? props.sysOrgIdView : "请选择",
			disabled: props.sysOrgId ? true : false,
			treeApi: () =>
				BaseLineSysApi.getDepartmentTreeWithCompanyDisabled(
					userInfo.value.companyId
				)
		},
		{
			name: "用户姓名",
			key: "realname",
			type: "input",
			placeholder: "请输入用户名姓名"
		}
	]
})

const btnConf = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "保存",
		icon: ["fas", "floppy-disk"]
	}
]

const btnLoading = ref(false)

const {
	fetchParam,
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	selectedTableList,
	currentPage,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = useTbInit<SystemUserVo, SystemUserDTO>()

fetchParam.value = {
	...fetchParam.value,
	...props.tableFetchParams,
	sord: "asc",
	sidx: "realname",
	sysOrgId:
		props.tableFetchParams?.addOrgAuthorityFalg ||
		props.tableFetchParams?.parentOrgId
			? ""
			: props.sysOrgId
}

fetchFunc.value = props.tableApi ?? listSysUserPaged

tableProp.value = props.tableProps ?? [
	{
		prop: "realname",
		label: "姓名"
	},
	{
		prop: "username",
		label: "用户账号"
	},
	{
		prop: "sex",
		label: "性别",
		needSlot: true
	},
	{
		prop: "phone",
		label: "手机号"
	},
	{
		prop: "sysOrgId_view",
		label: "部门"
	},
	{
		prop: "station",
		label: "职务"
	}
]

const { fetchTableDataWithSetRowsCheck, setTableSelect } =
	useTableSelectorUtils({
		tableData,
		tableRef,
		selectedIds: props.selectedIds,
		fetchTableData,
		selectedKey: "username"
	})

onMounted(fetchTableDataWithSetRowsCheck)

function getQueryData(e?: any) {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		...e
	}
	if (
		props.sysOrgId &&
		!props.tableFetchParams?.addOrgAuthorityFalg &&
		!props.tableFetchParams?.parentOrgId
	) {
		fetchParam.value.sysOrgId = props.sysOrgId
	}

	fetchTableDataWithSetRowsCheck()
}

async function handleBtnClick(name?: string) {
	if (name === "取消") return emit("close")

	const selectRows = tableRef.value?.pitayaTableRef?.getSelectionRows()

	btnLoading.value = true

	emit("save", selectRows, () => {
		btnLoading.value = false
	})
}

function handleCurrentPageChange(data: any) {
	onCurrentPageChange(data).then(setTableSelect)
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
