<template>
	<div class="dialog-box">
		<el-dialog v-model="dialog" title="系统消息" width="30%">
			<!-- :before-close="handleClose" -->
			<div style="padding-left: 10px; padding-bottom: 10px">
				<span>请确认是关闭此出库单？</span>
			</div>

			<el-form
				ref="formRef"
				class="content"
				:model="formData"
				:rules="formRules"
				label-position="left"
				label-width="80px"
			>
				<form-element :form-element="formEls" :form-data="formData" />
			</el-form>

			<template #footer>
				<span class="dialog-footer">
					<el-button @click="emit('close')" :loading="dialogBtnLoading">
						取消
					</el-button>
					<el-button
						type="primary"
						@click="handleSaveClose"
						:loading="dialogBtnLoading"
					>
						确认
					</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>
<script setup lang="ts">
import { requiredValidator } from "@/app/baseline/utils/validate"
import { FormInstance, FormItemRule } from "element-plus"
import { FormElementType } from "../../components/define"
import FormElement from "../../components/formElement.vue"

const props = defineProps<{
	id?: any // id
	dialog: boolean
}>()

const emit = defineEmits<{
	(e: "save", reason: any): void
	(e: "close"): void
	(e: "update:dialog", val: boolean): void
}>()

const dialog = computed({
	get() {
		return props.dialog
	},
	set(value) {
		formData.value.reason = ""
		setTimeout(() => {
			formRef.value?.clearValidate()
			dialogBtnLoading.value = false
		}, 0)
		emit("update:dialog", value)
	}
})

/**
 * 表单实例
 */
const formRef = ref<FormInstance>()
const formRules: Record<string, FormItemRule> = {
	reason: requiredValidator("关闭原因")
}

const formData = ref({ reason: "" })
const formEls = computed<FormElementType[][]>(() => {
	const ls: FormElementType[] = [
		{
			label: "关闭原因",
			name: "reason",
			type: "textarea",
			maxlength: "200",
			rows: 5
		}
	]
	return [ls]
})
const dialogBtnLoading = ref(false)
/**
 * 出库关闭 保存
 */
function handleSaveClose() {
	if (!formRef.value) {
		return
	}

	formRef.value.validate(async (valid) => {
		if (!valid) {
			return
		}
		dialogBtnLoading.value = true

		try {
			emit("save", formData.value.reason)
		} finally {
			dialogBtnLoading.value = false
		}
	})
}
</script>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.dialog-box {
	:deep(.el-dialog__body) {
		margin: 10px;
		padding: 20px 0 10px !important;
		border-top: 1px solid #ddd;
		border-bottom: 1px solid #ddd;
	}
}
</style>
