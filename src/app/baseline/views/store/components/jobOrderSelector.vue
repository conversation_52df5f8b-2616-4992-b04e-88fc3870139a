<!-- 工单选择器 -->
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<div class="drawer-column" style="width: 100%">
			<Title :title="titleConf" />

			<div class="rows">
				<Query
					:query-arr-list="(queryConf as any)"
					style="margin: 10px 10px -10px"
					@get-query-data="handleQuery"
				/>
				<pitaya-table
					ref="tableRef"
					:columns="tableProp"
					:table-data="tableData"
					:need-selection="true"
					:single-select="true"
					:need-index="true"
					:need-pagination="true"
					:total="pageTotal"
					:table-loading="tableLoading"
					@on-selection-change="(e) => (selectedTableList = e)"
					@on-current-page-change="handleCurrentPageChange"
				/>
			</div>

			<button-list
				class="footer"
				:button="btnConf"
				@on-btn-click="handleDrawerBtnClick"
			/>
		</div>
	</div>
</template>

<script setup lang="ts">
import { first } from "lodash-es"
import { useTbInit } from "../../components/tableBase"
import { BaseLineSysApi, listJobOrderPaged } from "@/app/baseline/api/system"
import {
	MatStoreJobOrderDTO,
	MatStoreJobOrderVo
} from "@/app/baseline/utils/types/system"
import { LineVo } from "@/app/baseline/utils/types/common"
import { useTableSelectorUtils } from "../hooks/table-selector-utils"

const props = defineProps<{
	/**
	 * 当前选中的 table row id
	 */
	selectedIds?: any[]
}>()

// TODO: 工单选择器：数据回显处理

const emit = defineEmits<{
	(e: "close"): void
	(e: "save", v?: any): void
}>()
// 线路列表
const lineList = ref<LineVo[]>([])

const titleConf = {
	name: ["选择工单"],
	icon: ["fas", "square-share-nodes"]
}

const queryConf = computed<querySetting[]>(() => [
	{
		name: "工单编号",
		key: "jobNo",
		type: "input",
		placeholder: "请输入工单编号"
	},
	{
		name: "工单名称",
		key: "jobName",
		type: "input",
		placeholder: "请输入工单名称"
	},
	{
		name: "工单来源",
		key: "jobSource",
		type: "input",
		placeholder: "请输入工单来源"
	},
	// TODO: 工单选择器：工单类型-后续再做
	// {
	// 	name: "工单类型",
	// 	key: "jobType",
	// 	type: "select",
	// 	placeholder: "请选择",
	// 	children: []
	// },
	{
		name: "线路",
		key: "lineNoId",
		type: "select",
		placeholder: "请选择",
		children: lineList.value
	}
	// TODO: 工单选择器：班组筛选-后续再做
	// {
	// 	name: "工单执行班组",
	// 	key: "a",
	// 	type: "treeSelect",
	// 	placeholder: "请选择"
	// },
])

const btnConf = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "保存",
		icon: ["fas", "floppy-disk"]
	}
]

const drawerLoading = ref(false)

const {
	tableProp,
	fetchParam,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	selectedTableList,
	currentPage,
	fetchTableData,
	onCurrentPageChange,
	fetchFunc
} = useTbInit<MatStoreJobOrderVo, MatStoreJobOrderDTO>()

fetchFunc.value = listJobOrderPaged

fetchParam.value = {
	...fetchParam.value
}

const { fetchTableDataWithSetRowsCheck, setTableSelect } =
	useTableSelectorUtils({
		tableData,
		tableRef,
		selectedIds: props.selectedIds,
		fetchTableData
	})

tableProp.value = [
	{
		prop: "jobNo",
		label: "工单编号"
	},
	{
		prop: "jobName",
		label: "工单名称"
	},
	{
		prop: "jobType",
		label: "工单类型"
	},
	{
		prop: "jobSource",
		label: "工单来源"
	},
	{
		prop: "workGroupName",
		label: "工单执行班组"
	},
	{
		prop: "lineNoId_view",
		label: "线路"
	}
]

onMounted(() => {
	getLineList()

	fetchTableDataWithSetRowsCheck()
})

function handleQuery(e: any) {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		...e
	}
	fetchTableDataWithSetRowsCheck()
}

function handleCurrentPageChange(data: any) {
	onCurrentPageChange(data).then(setTableSelect)
}

function handleDrawerBtnClick(name?: string) {
	const selectRows = tableRef.value?.pitayaTableRef?.getSelectionRows()

	if (name !== "取消") {
		const v = first(selectRows)
		emit("save", v)
	}

	emit("close")
}

function getLineList() {
	BaseLineSysApi.getLineOptions().then((res) => {
		lineList.value = res
	})
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
