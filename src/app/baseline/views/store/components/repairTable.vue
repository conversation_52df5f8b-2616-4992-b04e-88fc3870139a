<script setup lang="ts">
import { onMounted, computed } from "vue"
import { useTbInit } from "@/app/baseline/views/components/tableBase"
import { listWasteRepairApplySelectPaged } from "@/app/baseline/api/waste/repairApply"
import {
	MatWasteRepairApplyPageQuery,
	MatWasteRepairApplyVo
} from "@/app/baseline/utils/types/waste-repair-apply"
import { first } from "lodash-es"
import { toFixedTwo } from "@/app/baseline/utils"
import { useTableSelectorUtils } from "../hooks/table-selector-utils"

const props = withDefaults(
	defineProps<{
		/**
		 * 是否支持多选，默认单选
		 */
		multiple?: boolean
		isFilterStoreType?: boolean

		tableApi?: (arg?: any) => any

		tableApiParams?: any
		queryArrList?: any

		/**
		 * 当前选中的id 集合
		 */
		selectedIds?: any[]
	}>(),
	{
		isFilterStoreType: false
	}
)

const emits = defineEmits<{
	(e: "onSave", btnName: string, v?: MatWasteRepairApplyVo[]): void
}>()

/* title 配置 */
const matStoreDrawerTitle = {
	name: ["选择返修申请单"],
	icon: ["fas", "square-share-nodes"]
}

/* query 查询条件 配置 */
const queryArrMatStoreList = computed(() => {
	const ls = [
		{
			name: "返修单号",
			key: "code",
			placeholder: "请输入返修单号",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "返修单名称",
			key: "label",
			placeholder: "请输入返修单号",
			enableFuzzy: true,
			type: "input"
		}
	]

	if (props.queryArrList) {
		return props.queryArrList
	} else {
		return ls
	}
})

const {
	tableProp,
	tableRef,
	tableData,
	tableLoading,
	tbBtnLoading,
	pageTotal,
	selectedTableList,
	tbBtns,
	fetchParam,
	currentPage,
	fetchTableData,
	fetchFunc,
	onBtnClick
} = useTbInit<MatWasteRepairApplyVo, MatWasteRepairApplyPageQuery>()

const { fetchTableDataWithSetRowsCheck } = useTableSelectorUtils({
	tableData,
	tableRef,
	selectedIds: props.selectedIds,
	fetchTableData
})

/* table 列 配置 */
tableProp.value = [
	{ label: "返修单号", prop: "code", width: 180 },
	{ label: "返修单名称", prop: "label" },
	{ label: "物资编码", prop: "materialCodeNum" },
	{ label: "返修数量", prop: "repairNum", needSlot: true, align: "right" },
	{ label: "返修备注说明", prop: "reason", width: 180 },
	{ label: "申请部门", prop: "sysOrgId_view" },
	{ label: "申请人", prop: "createdBy_view" },
	{ label: "申请时间", prop: "createdDate", width: 150 }
]

fetchFunc.value = props.tableApi ?? listWasteRepairApplySelectPaged

/* Table 按钮配置 */
const matStoreBtnList = computed(() => {
	return [
		{ name: "取消", icon: ["fas", "circle-minus"] },
		{
			name: "保存",
			icon: ["fas", "file-alt"],
			disabled: selectedTableList.value.length > 0 ? false : true
		}
	]
})

const onMatStoreBtnList = (btnName: string | undefined) => {
	emits(
		"onSave",
		btnName as any,
		props.multiple
			? selectedTableList.value
			: (first(selectedTableList.value) as any)
	)
}

const handleQuery = (e?: any) => {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		...e
	}

	fetchTableDataWithSetRowsCheck()
}
onMounted(() => {
	fetchParam.value.sord = "desc"
	fetchParam.value.sidx = "createdDate"
	if (props.tableApiParams) {
		fetchParam.value = {
			...fetchParam.value,
			...props.tableApiParams
		}
	}

	handleQuery()
})
</script>
<template>
	<div class="drawer-container">
		<div class="drawer-column">
			<div class="rows">
				<Title :title="matStoreDrawerTitle" />
				<div style="margin: 10px 10px -10px">
					<Query
						:queryArrList="queryArrMatStoreList"
						@getQueryData="handleQuery"
						class="custom-q"
					/>
				</div>
				<div class="common-from-group" style="padding: 0px">
					<el-scrollbar>
						<PitayaTable
							ref="tableRef"
							:columns="tableProp"
							:table-data="tableData"
							:need-index="true"
							:need-pagination="true"
							:single-select="!multiple"
							:need-selection="true"
							:total="pageTotal"
							@on-current-page-change="fetchTableDataWithSetRowsCheck"
							@onSelectionChange="selectedTableList = $event"
							:table-loading="tableLoading"
						>
							<template #repairNum="{ rowData }">
								{{ toFixedTwo(rowData.repairNum) }}
							</template>
							<template #footerOperateLeft>
								<ButtonList
									class="btn-list"
									:is-not-radius="true"
									:button="tbBtns"
									v-loading="tbBtnLoading"
									@on-btn-click="onBtnClick"
								/>
							</template>
						</PitayaTable>
					</el-scrollbar>
				</div>
			</div>
			<ButtonList
				class="footer"
				:button="matStoreBtnList"
				@on-btn-click="onMatStoreBtnList"
			/>
			<!-- :loading="loading" -->
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.drawer-column {
	width: 100%;
}
</style>
