<!-- 采购项目编号 详情 -->
<script lang="ts" setup>
import projectDetail from "@/app/baseline/views/purchase/project/projectDetail.vue"
import { IModalType } from "@/app/baseline/utils/types/common"

const props = defineProps<{
	/**
	 * 业务id
	 */
	id?: any
}>()

const emit = defineEmits<{
	(e: "close"): void
}>()
</script>
<template>
	<project-detail
		:id="props.id"
		:model="IModalType.view"
		@close="emit('close')"
	/>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
:deep(.el-descriptions__label) {
	width: 260px !important;
}
.drawer-container {
	.left {
		width: 310px;
	}

	.right {
		width: calc(100% - 310px);
	}

	.tab-mat {
		height: 100%;
	}
}
</style>
