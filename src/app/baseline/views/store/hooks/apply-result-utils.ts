import { MatGetApplyCode } from "@/app/baseline/utils/types/common"
import { useMessageBoxInit } from "../../components/messageBox"

export const useApplyResultUtils = ({
	fetchSubmitApi,
	fetchTableData,
	id,
	successCb
}: any) => {
	const { showInfoConfirm } = useMessageBoxInit()

	/**
	 * 申请提交等待时长
	 */
	const waitSeconds = 10 * 1000

	let timer: NodeJS.Timeout | null = null

	/**
	 * 轮询 timer
	 */
	let rollPollingTimer: NodeJS.Timeout | null = null

	/**
	 * 轮询间隔
	 */
	const rollPollingTimerStep = 3 * 1000

	/**
	 * Error 物资列表
	 */
	const errorGoodsList = ref<any[]>([])

	/**
	 * 初始化轮询
	 */
	function initRollPolling(cb: () => void) {
		cancelRollPolling()
		rollPollingTimer = setInterval(cb, rollPollingTimerStep)
	}

	/**
	 * 取消轮询
	 */
	function cancelRollPolling() {
		if (rollPollingTimer) {
			clearInterval(rollPollingTimer)
			rollPollingTimer = null
		}
	}

	/**
	 * 获取申请结果
	 */
	async function getApplyResult() {
		const { code, msg, data } = (await fetchSubmitApi(id.value)) as any
		handleApplyResultByCode(code, msg, data)
	}

	/**
	 * 处理申请等待逻辑
	 */
	async function handleNeedWait() {
		try {
			showInfoConfirm("当前提交申请的用户较多，请稍后！", {
				showCancelButton: false,
				showConfirmButton: false,
				closeOnClickModal: false
			})
			initRollPolling(getApplyResult)
			onTimeout(handleApplyError)
		} catch (error) {
			clearTimer()
			cancelRollPolling()
		}
	}

	/**
	 * 处理申请成功逻辑
	 */
	function handleApplySuccess() {
		clearTimer()
		cancelRollPolling()

		ElMessageBox.close()
		ElMessage.success("操作成功")
		// emit("save", undefined)
		successCb()
	}

	/**
	 * 处理申请失败逻辑
	 */
	function handleApplyError(msg = "系统正忙，请稍后再试！", idList?: any[]) {
		clearTimer()
		cancelRollPolling()
		ElMessageBox.close()

		errorGoodsList.value = idList || []
		fetchTableData()
		ElMessage.error(msg)
		//showErrorConfirm(msg)
	}

	/**
	 * 处理申请结果
	 */
	async function handleApplyResultByCode(
		code: MatGetApplyCode,
		msg?: string,
		idList?: any[]
	) {
		switch (code) {
			case MatGetApplyCode.needWait:
				handleNeedWait()
				break
			case MatGetApplyCode.success:
				handleApplySuccess()
				break
			default:
				handleApplyError(msg, idList)
				break
		}
	}

	function onTimeout(cb?: () => void) {
		clearTimer()

		timer = setTimeout(() => {
			cb?.()
		}, waitSeconds)
	}

	function clearTimer() {
		if (timer) {
			clearTimeout(timer)
			timer = null
		}
	}

	return {
		handleApplyResultByCode,
		errorGoodsList,
		clearTimer,
		cancelRollPolling
	}
}
