import { debounce, filter, includes, forEach } from "lodash-es"
import { MaybeRef } from "vue"

interface TableSelectorUtilsParams {
	/**
	 * element table 实例
	 */
	tableRef: Ref<PitayaTableRef | undefined>
	/**
	 * 列表数据
	 */
	tableData: Ref<any[]>
	/**
	 * 当前选中的
	 */
	selectedIds: MaybeRef<any>

	selectedKey?: string

	/**
	 * table 数据 fetcher
	 */
	fetchTableData: (args?: any) => Promise<any>
}

/**
 * el-table 多选回显工具 hook
 */
export const useTableSelectorUtils = ({
	tableRef,
	tableData,
	selectedIds,
	fetchTableData,
	selectedKey
}: TableSelectorUtilsParams) => {
	/**
	 * 根据 id 集合，过滤出 选中的 table rows data
	 */
	function getSelectedRowsByIds() {
		return (
			filter(tableData.value, (v: any) =>
				includes(toValue(selectedIds), selectedKey ? v[selectedKey] : v.id)
			) ?? []
		)
	}

	/**
	 * 设置表格行选中
	 */
	const setTableSelect = debounce(() => {
		if (tableData.value?.length) {
			const tbRef = tableRef.value?.pitayaTableRef
			const selRows = getSelectedRowsByIds()
			tbRef?.clearSelectedTableData?.()

			if (!selRows.length) {
				return
			}
			forEach(selRows, (row) => tbRef?.toggleRowSelection(row, true))
		}
	}, 100)

	/**
	 * 获取 table 数据，并设置行选中
	 */
	function fetchTableDataWithSetRowsCheck(args?: any) {
		fetchTableData(args).then(setTableSelect)
	}

	return {
		setTableSelect,
		fetchTableDataWithSetRowsCheck
	}
}
