<!-- 返修申请新增、编辑 -->
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<div class="drawer-column left" style="width: 310px">
			<Title :title="titleConf" />

			<el-scrollbar>
				<grid-panel :options="gridPanelOptions" />

				<el-form
					class="content"
					:model="formData"
					:rules="formRules"
					:validate-on-rule-change="false"
					ref="formRef"
					label-position="top"
					label-width="100px"
				>
					<form-element :form-element="formEls" :form-data="formData" />
				</el-form>
			</el-scrollbar>

			<button-list
				class="footer"
				:button="drawerBtnConf"
				:loading="drawerBtnLoading"
				@on-btn-click="handleDrawerBtnClick"
			/>
		</div>
		<!-- 物资明细 -->
		<div
			class="drawer-column right"
			:class="!canEditExtra ? 'disabled' : ''"
			style="width: calc(100% - 310px)"
		>
			<Title :title="extraTitleConf">
				<Tabs
					style="margin-right: auto; margin-left: 30px"
					:tabs="tabsConf"
					@on-tab-change="handleTabChange"
				/>
			</Title>

			<el-scrollbar class="rows repair-apply-editor-table-wrapper">
				<Query
					:queryArrList="queryArrList"
					@getQueryData="getTableData"
					class="custom-q"
					v-if="activatedTab === 0"
				/>
				<pitaya-table
					v-if="activatedTab === 0"
					ref="tableRef"
					:columns="(tbInitConf.tableProp as any)"
					:table-data="tableData"
					:need-selection="true"
					:single-select="false"
					:need-index="true"
					:need-pagination="true"
					:total="pageTotal"
					:table-loading="tableLoading"
					:cell-class-name="tbCellClassName"
					@on-selection-change="selectedTableList = $event"
					@on-current-page-change="onCurrentPageChange"
				>
					<!-- 物资性质 -->
					<template #attribute="{ rowData }">
						<dict-tag
							:options="DictApi.getMatAttr()"
							:value="rowData.attribute"
						/>
					</template>

					<template #num="{ rowData }">
						<el-input
							class="no-arrows"
							v-model="rowData.num"
							@click.stop
							@input="rowData.num = validateAndCorrectInput($event)"
							@change="updateWarehousingGoodsNum(rowData)"
						/>
					</template>
					<template #actions="{ rowData }">
						<el-button v-btn link @click.stop="delRow(rowData)">
							<font-awesome-icon
								:icon="['fas', 'trash-can']"
								style="color: var(--pitaya-btn-background)"
							/>
							<span class="table-inner-btn">移除</span>
						</el-button>
					</template>

					<template #footerOperateLeft>
						<button-list
							:is-not-radius="true"
							:button="tbBtnConf"
							:loading="drawerBtnLoading"
							@on-btn-click="handleFootClick"
						/>
					</template>
				</pitaya-table>
				<!-- 相关附件 table -->
				<table-file
					v-else
					:business-type="fileBusinessType.repairApply"
					:business-id="props.id || formData.id"
					:mod="canEditExtra ? IModalType.edit : IModalType.view"
				/>
			</el-scrollbar>

			<button-list
				class="footer"
				:button="submitBtnConf"
				:loading="drawerBtnLoading"
				@on-btn-click="handleSubmit"
			/>
		</div>
	</div>

	<!-- 物资选择 -->
	<Drawer
		v-model:drawer="goodsSelectorVisible"
		:size="modalSize.lg"
		destroy-on-close
	>
		<mat-selector
			:table-req-params="{ id: formData.id }"
			:table-api="listRepairAddPageItem"
			:columns="matSelectColumns"
			:multiple="true"
			@save="handleAddGoods"
			@close="goodsSelectorVisible = false"
		/>
	</Drawer>

	<!-- 出库仓库 -->
	<Drawer
		v-model:drawer="storeSelectorVisible"
		:size="modalSize.lg"
		destroy-on-close
	>
		<store-table
			:selected-ids="[formData.storeId]"
			:table-api-params="{
				type: IWarehouseType.rotablesWaitingRepair
			}"
			@on-save="handleStoreSave"
		/>
	</Drawer>

	<!-- 维修公司选择器 维修方式：委外维修时 调用-->
	<Drawer
		v-model:drawer="supplierSelectorVisible"
		:size="modalSize.lg"
		destroy-on-close
	>
		<supplier-selector
			:selected-ids="[formData.repairOrgId]"
			@on-save="handleSupplierSave"
		/>
	</Drawer>
</template>

<script setup lang="ts">
import {
	addWasteRepairApply,
	updateWasteRepairApply,
	getRepairApplyDetail,
	submitWasteRepairApply,
	repairApplyPageItem,
	addRepairApplyBatchItem,
	updateRepairApplyItem,
	deleteRepiarApplyBatchItem,
	listRepairAddPageItem
} from "@/app/baseline/api/waste/repairApply"
import {
	MatWasteRepairApplyAddVo,
	repairApplyItemVo
} from "@/app/baseline/utils/types/waste-repair-apply"
import { IWarehouseType } from "@/app/baseline/utils/types/store-manage"
import tableFile from "@/app/baseline/views/components/tableFile.vue"
import { DictApi, fileBusinessType } from "@/app/baseline/api/dict"
import {
	IIdempotentTokenType,
	IIdempotentTokenTypePre,
	IModalType
} from "@/app/baseline/utils/types/common"
import { useTbInit } from "@/app/baseline/views/components/tableBase"
import { useMessageBoxInit } from "@/app/baseline/views/components/messageBox"
import formElement from "@/app/baseline/views/components/formElement.vue"
import { FormElementType } from "@/app/baseline/views/components/define"
import { useDictInit } from "@/app/baseline/views/components/dictBase"
import gridPanel from "@/app/baseline/views/store/components/gridPanel.vue"
import { inputMaxLength, modalSize } from "@/app/baseline/utils/layout-config"
import { ElMessage, FormInstance, FormItemRule } from "element-plus"
import { MatStoreVo } from "@/app/baseline/utils/types/store-manage"
import { debounce, findIndex, map, includes, toNumber } from "lodash-es"
import {
	getIdempotentToken,
	requiredValidator,
	validateAndCorrectInput
} from "@/app/baseline/utils/validate"

import storeTable from "@/app/baseline/views/store/components/storeTable.vue"
import supplierSelector from "./supplierSelector.vue"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import matSelector from "@/app/baseline/views/store/components/matSelector.vue"
import { BaseLineSysApi } from "@/app/baseline/api/system"
import { batchFormatterNumView, getModalTypeLabel } from "@/app/baseline/utils"
import { useApplyResultUtils } from "../../store/hooks/apply-result-utils"
import { useUserStore } from "@/app/platform/store/modules/user"

enum IRepairWayType {
	out = 1,
	self = 2
}

const { userInfo } = storeToRefs(useUserStore())

const { showDelConfirm, showWarnConfirm } = useMessageBoxInit()

const props = defineProps<{
	/**
	 * 返修申请id
	 */
	id: any
	/**
	 * 编辑类型 新建/编辑
	 */
	mode: IModalType
}>()

const emit = defineEmits<{
	(e: "update"): void
	(e: "close"): void
	/**
	 * 保存
	 *
	 * @param id
	 * @param visible - 是否隐藏本编辑 drawer
	 */
	(e: "save", close?: boolean): void
}>()

/**
 * 保存旧的表单数据
 */
const oldFormData = ref<string>()

const activatedTab = ref(0)

const goodsSelectorVisible = ref(false)

const drawerBtnLoading = ref(false)

const matSelectColumns = ref<any[]>([
	{ label: "物资编码", prop: "materialCode", fixed: "left", width: 130 },
	{ label: "物资名称", prop: "materialLabel" },
	{ label: "规格型号", prop: "version" },
	{ label: "技术参数", prop: "technicalParameter" },
	{ label: "物资性质", prop: "attribute", needSlot: true, width: 120 },
	{ label: "库存单位", prop: "useUnit_view", width: 90 },
	{ label: "区域编码", prop: "regionCode" },
	{ label: "货位编码", prop: "roomCode" },
	{ label: "批次号", prop: "batchNo", width: 180 },
	{ label: "库存数量", prop: "storeNum_view", align: "right" },
	{ label: "冻结量", prop: "freezeNum_view", align: "right" }
])
/**
 * 仓库选择器 visible
 */
const storeSelectorVisible = ref(false)

const supplierSelectorVisible = ref(false)

const drawerBtnConf = computed(() => [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "保存草稿",
		icon: ["fas", "floppy-disk"]
	}
])

/**
 * 提交审核按钮配置
 */
const submitBtnConf = computed(() => {
	return [
		{
			name: "提交审核",
			icon: ["fas", "circle-check"],
			disabled: tableData.value.length === 0
		}
	]
})

const { dictOptions, getDictByCodeList } = useDictInit()

const formData = ref<MatWasteRepairApplyAddVo>({})

const formRules = computed(() => {
	const obj: Record<string, FormItemRule> = {
		label: requiredValidator("返修单名称"),
		storeLabel: requiredValidator("出库仓库"),
		repairWay: requiredValidator("维修方式"),
		repairOrgId_view: requiredValidator("维修部门"),
		repairCommunityLabel: requiredValidator("维修公司")
	}
	return obj
})

/**
 * 能否编辑扩展信息
 */
const canEditExtra = computed(() => Boolean(formData.value?.id || props.id))

const formEls = computed<FormElementType[][]>(() => {
	const ls = [
		{
			label: "返修单名称",
			name: "label",
			maxlength: inputMaxLength.input
		},
		{
			label: "出库仓库",
			name: "storeLabel",
			type: "drawer",
			disabled: canEditExtra.value,
			clear: false,
			clickApi: () => (storeSelectorVisible.value = true)
		},
		{
			label: "维修方式",
			name: "repairWay",
			type: "select",
			disabled: canEditExtra.value,
			data: [
				{ label: "委外维修", value: IRepairWayType.out },
				{ label: "自主维修", value: IRepairWayType.self }
			]
		},
		{
			label: "维修公司",
			name: "repairCommunityLabel",
			vname: "repairCommunityId",
			type: "drawer",
			placeholder: "请选择",
			disabled: !formData.value.repairWay,
			clickApi: () => (supplierSelectorVisible.value = true)
		},
		{
			label: "维修部门",
			name: "repairOrgId_view",
			vname: "repairOrgId",
			type: "treeSelect",
			treeApi: () =>
				BaseLineSysApi.getDepartmentTreeWithCompanyDisabled(
					userInfo.value.companyId
				),
			placeholder: "请选择",
			disabled: !formData.value.repairWay
		},
		{
			label: "预估维修时间（天）",
			name: "repairDay",
			type: "number",
			input: (value: any) => {
				formData.value.repairDay = validateAndCorrectInput(value)
			},
			blur: (event: any) => {
				formData.value.repairDay = toNumber(event.target.value)
			}
		},
		{
			label: "维修预估费用（元）",
			name: "amount",
			type: "number",
			input: (value: any) => {
				formData.value.amount = validateAndCorrectInput(value, 5)
			},
			blur: (event: any) => {
				formData.value.amount = toNumber(event.target.value)
			}
		},
		{
			label: "维修备注说明",
			name: "reason",
			type: "textarea",
			disabled: false,
			clear: true,
			maxlength: inputMaxLength.textarea
		}
	]

	if (formData.value.repairWay == IRepairWayType.self) {
		return [ls.filter((v) => v.label !== "维修公司")]
	} else {
		return [ls.filter((v) => v.label !== "维修部门")]
	}
})

const tbBtnConf = computed(() => {
	return [
		{
			name: "添加物资",
			icon: ["fas", "circle-plus"]
		},
		{
			name: "批量移除",
			icon: ["fas", "trash-can"],
			disabled: selectedTableList.value.length > 0 ? false : true
		}
	]
})

const handleFootClick = async (btnName?: string) => {
	if (btnName === "添加物资") {
		goodsSelectorVisible.value = true
	} else if (btnName === "批量移除") {
		const ids = map(selectedTableList.value, ({ id }) => id)
		await showDelConfirm()
		drawerBtnLoading.value = true
		try {
			await deleteRepiarApplyBatchItem(ids)
			ElMessage.success("操作成功")
			fetchTableData()
			getDetail()
			emit("update")
		} finally {
			drawerBtnLoading.value = false
		}
	}
}

const formRef = ref<FormInstance>()

const tabsConf = ["物资明细", "相关附件"]

const drawerLoading = ref(false)

const titleConf = computed(() => ({
	name: [
		getModalTypeLabel(
			canEditExtra.value ? IModalType.edit : IModalType.create,
			"返修申请"
		)
	],
	icon: ["fas", "square-share-nodes"]
}))

const extraTitleConf = {
	name: ["扩展信息"],
	icon: ["fas", "square-share-nodes"]
}

const handleTabChange = (index: number) => {
	activatedTab.value = index

	if (index === 0) {
		fetchParam.value = {
			applyId: props.id || formData.value.id,
			sord: "desc",
			sidx: "createdDate"
		}

		pageSize.value = 20

		getTableData()
	}
}

/**
 * 查询条件 配置
 */
const queryArrList = computed(() => [
	{
		name: "物资编码",
		key: "materialCode",
		placeholder: "请输入物资编码",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资名称",
		key: "materialLabel",
		placeholder: "请输入物资名称",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "规格型号",
		key: "version",
		placeholder: "请输入规格型号",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资性质",
		key: "attribute",
		type: "select",
		children: dictOptions.value.MATERIAL_NATURE,
		placeholder: "请选择物资性质"
	}
])

const tbInitConf = useTbInit<repairApplyItemVo, Record<string, any>>()

const getTableData = (data?: Record<string, any>) => {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		...data
	}
	fetchTableData()
}

const {
	currentPage,
	tableData,
	tableRef,
	tableCache,
	tableLoading,
	pageTotal,
	pageSize,
	selectedTableList,
	fetchTableData,
	fetchParam,
	fetchFunc,
	onCurrentPageChange
} = tbInitConf

const formDataId = computed(() => {
	return formData.value.id
})
const {
	handleApplyResultByCode,
	errorGoodsList,
	clearTimer,
	cancelRollPolling
} = useApplyResultUtils({
	fetchSubmitApi: submitWasteRepairApply,
	fetchTableData: getTableData,
	id: formDataId,
	successCb: () => {
		emit("save", undefined)
	}
})

tbInitConf.tableProp = computed(() => {
	const ls: TableColumnType[] = [
		{
			prop: "materialCode",
			label: "物资编码",
			width: 130,
			fixed: "left"
		},
		{
			prop: "materialLabel",
			label: "物资名称",
			minWidth: 120
		},
		{ prop: "version", label: "规格型号" },
		{
			prop: "technicalParameter",
			label: "技术参数",
			minWidth: 120
		},
		{ prop: "attribute", label: "物资性质", needSlot: true, width: 120 },
		{ prop: "useUnit_view", label: "库存单位" },
		{ prop: "regionCode", label: "区域编码" },
		{ prop: "roomCode", label: "货位编码", width: 120 },
		{ prop: "batchNo", label: "批次号", width: 180 },
		{
			prop: "storeNum_view",
			label: "库存数量",
			align: "right",
			width: 120
		},
		{
			prop: "freezeNum_view",
			label: "冻结量",
			align: "right",
			width: 120
		},
		{
			prop: "num",
			label: "返修数量",
			needSlot: true,
			width: 120,
			fixed: "right"
		},
		{
			prop: "actions",
			label: "操作",
			needSlot: true,
			width: 100,
			fixed: "right"
		}
	]
	return ls
})

fetchFunc.value = repairApplyPageItem

watch(tableData, () => {
	batchFormatterNumView(tableData.value)
})

const gridPanelOptions = computed(() => {
	return [
		{
			label: "物资编码",
			value: formData.value.materialCodeNum ?? 0
		},
		{
			label: "返修数量",
			value: parseInt(formData.value.repairNum) || 0
		}
	]
})

/**
 * 物资明细 table 行样式
 *
 * 库存异常物资，用红色标记该行
 */
function tbCellClassName({ row }: any) {
	return includes(errorGoodsList.value, row.id) ? "error" : ""
}

onMounted(() => {
	getDictByCodeList([
		"IN_STORE_TYPE",
		"STORE_LEVEL",
		`${userInfo.value.companyCode}_DEPOT`,
		"MATERIAL_NATURE"
	])

	fetchParam.value.sord = "desc"
	fetchParam.value.sidx = "createdDate"

	if (props.mode === IModalType.edit) {
		fetchParam.value = { ...fetchParam.value, applyId: props.id }
		fetchTableData()
		getDetail()
	}
})

onUnmounted(() => {
	clearTimer()
	cancelRollPolling()
})

/**
 * 删除物资
 */
async function delRow(e: any) {
	await showDelConfirm()
	await deleteRepiarApplyBatchItem([e.id])
	ElMessage.success("操作成功")
	fetchTableData()
	getDetail()
	emit("update")
}

/**
 * 保存草稿 handler
 */
async function handleSaveDraft() {
	if (!formRef.value) {
		return
	}

	formRef.value?.validate(async (valid) => {
		if (!valid) {
			return
		}

		drawerBtnLoading.value = true

		try {
			const api = canEditExtra.value
				? updateWasteRepairApply
				: addWasteRepairApply

			let idempotentToken = ""
			if (!canEditExtra.value) {
				idempotentToken = getIdempotentToken(
					IIdempotentTokenTypePre.apply,
					IIdempotentTokenType.wasteRepairApply
				)
			}
			const r = await api(
				formData.value as MatWasteRepairApplyAddVo,
				idempotentToken
			)
			ElMessage.success("操作成功")
			formData.value.id = r.id

			oldFormData.value = JSON.stringify(formData.value)

			fetchParam.value = { ...fetchParam.value, applyId: formData.value.id }
			emit("save", false)

			getTableData()
		} finally {
			drawerBtnLoading.value = false
		}
	})
}

// 提交审核逻辑
async function handleSubmit() {
	if (!formRef.value) {
		return
	}

	formRef.value?.validate(async (valid) => {
		if (!valid) {
			return
		}
		await showWarnConfirm("请确认是否提交本次数据？")
		drawerBtnLoading.value = true
		drawerLoading.value = true
		try {
			// 如果主表有修改，则先更新主表数据
			if (oldFormData.value != JSON.stringify(formData.value)) {
				await updateWasteRepairApply(formData.value as MatWasteRepairApplyAddVo)
			}

			const idempotentToken = getIdempotentToken(
				IIdempotentTokenTypePre.other,
				IIdempotentTokenType.wasteRepairApply,
				formData.value.id
			)
			const { code, msg, data } = await submitWasteRepairApply(
				formData.value.id!,
				idempotentToken
			)

			if (data && code != 200) {
				handleApplyResultByCode(code, msg, data)
			} else {
				ElMessage.success("操作成功")
				emit("save", true)
			}
		} finally {
			drawerBtnLoading.value = false
			drawerLoading.value = false
		}
	})
}

function handleDrawerBtnClick(name?: string) {
	if (name === "保存草稿") {
		handleSaveDraft()
		return
	}

	emit("close")
}

/**
 * 添加物资
 */
async function handleAddGoods(params: any[]) {
	if (!params || params.length === 0) {
		ElMessage.warning("请选择物资")
		return
	}
	const idempotentToken = getIdempotentToken(
		IIdempotentTokenTypePre.other,
		IIdempotentTokenType.wasteRepairApply,
		formData.value.id
	)
	await addRepairApplyBatchItem(
		{
			applyId: props.id || formData.value.id,
			itemIdList: params.map((item: any) => {
				return {
					storeBatchId: item.id,
					materialCode: item.materialCode,
					materialLabel: item.materialLabel,
					version: item.version,
					attribute: item.attribute
				}
			})
		},
		idempotentToken
	)
	goodsSelectorVisible.value = false
	ElMessage.success("操作成功")

	getTableData()
	getDetail()
	emit("update")
}

/**
 * 获取返修申请详情
 */
function getDetail() {
	drawerLoading.value = true
	getRepairApplyDetail(props.id || formData.value.id)
		.then((r) => {
			formData.value = r as any

			oldFormData.value = JSON.stringify(formData.value)
		})
		.finally(() => (drawerLoading.value = false))
}

/**
 * 仓库选择 handler
 * @param btnName 按钮名称
 * @param store 仓库列表
 */
function handleStoreSave(btnName: string, store?: MatStoreVo) {
	if (btnName === "取消") {
		storeSelectorVisible.value = false
		return
	}
	formData.value.storeId = store?.id
	formData.value.storeLabel = store?.label
	storeSelectorVisible.value = false
}

/**
 * 维修公司选择 handler
 * @param btnName 按钮名称
 * @param row 仓库列表
 */
function handleSupplierSave(btnName: string, row?: any) {
	if (btnName === "取消") {
		supplierSelectorVisible.value = false
		return
	}
	formData.value.repairCommunityId = row?.id
	formData.value.repairCommunityLabel = row?.label
	supplierSelectorVisible.value = false
}

/**
 * 更新入库物资数量
 */

const updateWarehousingGoodsNum = debounce(async (e: repairApplyItemVo) => {
	const canNum = (e.storeNum ?? 0) - (e.freezeNum ?? 0)
	const num = toNumber(e.num)

	e.num = num
	if (num > canNum) {
		ElMessage.warning("返修数量不能大于库存数量减冻结量！")
		e.num = canNum
	} else if (num! <= 0) {
		const oldRow = tableCache.find((r) => r.id == e.id)

		e.num = oldRow.num
		ElMessage.warning("返修数量不能小于等于0！")
		return
	}

	await updateRepairApplyItem({ id: e.id, num: e.num })

	//  更新 tableCache
	const rowIdx = findIndex(tableCache, (v) => v.id === e.id)
	if (rowIdx !== -1) {
		tableCache.splice(rowIdx, 1, { ...e })
	}

	ElMessage.success("操作成功")
	getDetail()
	emit("update")
}, 300)
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.custom-q {
	margin: 10px 0px -10px 10px !important;
}
.repair-apply-editor-table-wrapper {
	&:deep(.pitaya-table) {
		.error {
			.column-inner-item,
			.cell,
			.el-input__inner {
				color: red;
			}
		}
	}
}
</style>
