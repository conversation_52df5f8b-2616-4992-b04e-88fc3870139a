<script setup lang="ts">
import { computed, onMounted, ref } from "vue"
import { IModalType } from "@/app/baseline/utils/types/common"
import {
	batchFormatterNumView,
	getNumDefByNumKey,
	toFixedTwo
} from "@/app/baseline/utils"
import gridPanel from "@/app/baseline/views/store/components/gridPanel.vue"
import { useTbInit } from "@/app/baseline/views/components/tableBase"
import { DictApi, fileBusinessType } from "@/app/baseline/api/dict"
import TableFile from "@/app/baseline/views/components/tableFile.vue"
import { repairApplyItemVo } from "@/app/baseline/utils/types/waste-repair-apply"
import {
	getRepairApplyDetail,
	repairApplyPageItem
} from "@/app/baseline/api/waste/repairApply"
import CostTag from "@/app/baseline/views/components/costTag.vue"
import { appStatus } from "@/app/baseline/api/dict"
import { getRealLength, setString } from "@/app/baseline/utils/validate"
import { useDictInit } from "../../components/dictBase"
import DictTag from "@/app/baseline/views/components/dictTag.vue"

const { dictOptions, getDictByCodeList } = useDictInit()

const props = withDefaults(
	defineProps<{
		id: any // 申请id
		footerBtnVisible?: boolean
	}>(),
	{ footerBtnVisible: true }
)

const emits = defineEmits<{
	(e: "onSaveOrClose", msg?: string): void
	(e: "close"): void
}>()

const drawerLoading = ref(false)

/**
 * 左侧title 配置
 */
const leftTitleConf = computed(() => ({
	name: ["返修信息"],
	icon: ["fas", "square-share-nodes"]
}))

const drawerBtnRightConf = computed(() => [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	}
])

/**
 * 右侧 title 配置
 */
const extraTitleConf = {
	name: ["相关信息"],
	icon: ["fas", "square-share-nodes"]
}
const tabsConf = ["物资明细", "相关附件"]
const activatedTab = ref(0)

const handleTabChange = (index: number) => {
	activatedTab.value = index

	if (index === 0) {
		fetchParam.value = {
			applyId: props.id || formData.value.id,
			sord: "desc",
			sidx: "createdDate"
		}

		pageSize.value = 20

		getTableData()
	}
}

/**
 * 左侧表单 配置
 */
const formData = ref<Record<string, any>>({})

/**
 * 表单按钮 操作
 * @param btnName 保存草稿 | 取消
 */
const handleFormBtnClick = (btnName?: string) => {
	if (btnName == "取消") {
		emits("close")
	}
}

/**
 * 关联业务单数据
 */
const descList = computed(() => {
	const ls = [
		{ label: "返修单号", key: "code" },
		{ label: "返修单名称", key: "label" },
		{ label: "维修方式", key: "repairWay" },
		{ label: "维修部门", key: "repairOrgId_view" },
		{ label: "维修公司", key: "repairCommunityLabel" },
		{ label: "出库仓库名称", key: "storeLabel" },
		{ label: "物资编码", key: "materialCodeNum" },
		{ label: "返修数量", key: "repairNum_view" },
		{ label: "返修备注说明", key: "reason", needTooltip: true },
		/* { label: "预估维修时间（天）", key: "repairDay" },
		{ label: "维修预估费用（元）", key: "amount" }, */
		{ label: "申请部门", key: "sysOrgId_view" },
		{ label: "申请人", key: "createdBy_view" },
		{ label: "申请时间", key: "createdDate" }
	]
	if (formData.value.repairWay == 1) {
		return ls.filter((item) => item.label != "维修部门")
	} else {
		return ls.filter((item) => item.label != "维修公司")
	}
})

/**
 * 查询条件 配置
 */
const queryArrList = computed(() => [
	{
		name: "物资编码",
		key: "materialCode",
		placeholder: "请输入物资编码",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资名称",
		key: "materialLabel",
		placeholder: "请输入物资名称",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "规格型号",
		key: "version",
		placeholder: "请输入规格型号",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资性质",
		key: "attribute",
		type: "select",
		children: dictOptions.value.MATERIAL_NATURE,
		placeholder: "请选择物资性质"
	}
])

const tbInit = useTbInit<repairApplyItemVo, Record<string, any>>()
const {
	currentPage,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	pageSize,
	fetchParam,
	fetchTableData,
	fetchFunc
} = tbInit

tbInit.tableProp = computed(() => {
	const defTableProps: TableColumnType[] = [
		{ prop: "materialCode", label: "物资编码", fixed: "left", width: 130 },
		{ prop: "materialLabel", label: "物资名称", minWidth: 120 },
		{ prop: "version", label: "规格型号", minWidth: 120 },
		{ prop: "technicalParameter", label: "技术参数", minWidth: 120 },
		{ prop: "attribute", label: "物资性质", needSlot: true, width: 120 },
		{ prop: "useUnit_view", label: "库存单位", width: 80 },
		{ prop: "regionCode", label: "区域编码", width: 100 },
		{ prop: "roomCode", label: "货位编码", width: 120 },
		{ prop: "batchNo", label: "批次号", width: 180 },
		{ prop: "num_view", label: "返修数量", align: "right" },
		{
			prop: "outStoreNum_view",
			label: "返修出库数量",
			align: "right",
			width: 120
		},
		{
			prop: "repairingNum_view",
			label: "返修中数量",
			align: "right",
			width: 120
		},
		{ prop: "finishNum_view", label: "已完修数量", align: "right", width: 120 },
		{
			prop: "wasteOldNum_view",
			label: "已交旧数量",
			align: "right",
			width: 120
		}
		/* {
			prop: "inStoreNum_view",
			label: "返修入库数量",
			align: "right",
			width: 120
		} */
	]

	switch (formData.value.bpmStatus) {
		case appStatus.approved:
			return defTableProps
		// return defTableProps.filter((item) => item.label !== "批次号")

		default:
			return defTableProps.filter(
				(item) =>
					item.label !== "返修出库数量" &&
					item.label !== "返修中数量" &&
					item.label !== "已完修数量" &&
					item.label !== "已交旧数量"
			)
	}
})

fetchFunc.value = repairApplyPageItem

const getTableData = (data?: Record<string, any>) => {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		...data
	}
	fetchTableData()
}

watch(tableData, () => {
	batchFormatterNumView(tableData.value)
})

const gridPanelOptions = computed(() => {
	return [
		{
			label: "物资编码",
			value: formData.value.materialCodeNum ?? 0
		},
		{
			label: "返修数量",
			value: parseInt(formData.value.repairNum) || 0
		}
	]
})

/**
 * 获取详情数据
 */
async function getDetail() {
	drawerLoading.value = true
	try {
		const r = await getRepairApplyDetail(props.id)
		formData.value = r
		formData.value.repairNum_view = toFixedTwo(r.repairNum)
	} finally {
		drawerLoading.value = false
	}
}

onMounted(async () => {
	getDictByCodeList(["MATERIAL_NATURE"])

	fetchParam.value.sord = "desc"
	fetchParam.value.sidx = "createdDate"

	if (props.id) {
		fetchParam.value.applyId = props.id

		fetchTableData()
		getDetail()
	}
})
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<div class="drawer-column left" style="width: 310px">
			<Title :title="leftTitleConf" />

			<el-scrollbar>
				<grid-panel :options="gridPanelOptions" />
				<el-descriptions
					size="small"
					:column="1"
					border
					class="content long_text"
				>
					<template>
						<el-descriptions-item
							v-for="desc in descList"
							:key="desc.key"
							:label="desc.label"
						>
							<span v-if="desc.key === 'amount'">
								<cost-tag
									v-if="formData?.[desc.key]"
									:value="formData?.[desc.key]"
								/>
								<span v-else>---</span>
							</span>
							<span v-else-if="desc.key === 'repairWay'">
								{{ formData.repairWay == 1 ? "委外维修" : "自主维修" }}
							</span>
							<span v-else-if="desc.key === 'repairNum_view'">
								{{ toFixedTwo(formData?.[desc.key]) }}
							</span>
							<span v-else-if="desc.needTooltip">
								<el-tooltip
									effect="dark"
									:content="formData?.[desc.key]"
									:disabled="
										getRealLength(formData?.[desc.key]) <= 100 ? true : false
									"
								>
									{{
										getRealLength(formData?.[desc.key]) > 100
											? setString(formData?.[desc.key], 100)
											: formData?.[desc.key] || "---"
									}}
								</el-tooltip>
							</span>
							<span v-else>
								{{ getNumDefByNumKey(desc.key, formData?.[desc.key]) }}
							</span>
						</el-descriptions-item>
					</template>
				</el-descriptions>
			</el-scrollbar>
		</div>
		<div
			class="drawer-column right"
			style="width: calc(100% - 310px)"
			:class="{ pdr10: !footerBtnVisible }"
		>
			<Title :title="extraTitleConf">
				<Tabs
					style="margin-right: auto; margin-left: 30px"
					:tabs="tabsConf"
					@on-tab-change="handleTabChange"
				/>
			</Title>

			<el-scrollbar class="rows">
				<Query
					:queryArrList="queryArrList"
					@getQueryData="getTableData"
					class="custom-q"
					v-if="activatedTab === 0"
				/>
				<pitaya-table
					v-if="activatedTab === 0"
					ref="tableRef"
					:columns="(tbInit.tableProp as any)"
					:table-data="tableData"
					:need-selection="false"
					:single-select="false"
					:need-index="true"
					:need-pagination="true"
					:total="pageTotal"
					:table-loading="tableLoading"
					@on-current-page-change="fetchTableData"
				>
					<!-- 物资性质 -->
					<template #attribute="{ rowData }">
						<dict-tag
							:options="DictApi.getMatAttr()"
							:value="rowData.attribute"
						/>
					</template>
				</pitaya-table>

				<table-file
					v-else
					:business-type="fileBusinessType.repairApply"
					:business-id="props.id"
					:mod="
						formData.bpmStatus === appStatus.approved
							? IModalType.edit
							: IModalType.view
					"
				/>
			</el-scrollbar>
			<button-list
				v-if="footerBtnVisible"
				class="footer"
				:button="drawerBtnRightConf"
				@on-btn-click="handleFormBtnClick"
			/>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.custom-q {
	margin: 10px 0px -10px 10px !important;
}
.long_text {
	:deep(.el-descriptions__label) {
		width: 230px;
	}
}
</style>
