<!-- 返修申请 -->
<script lang="ts" setup>
import {
	checkPermission,
	isCheckPermission
} from "@/app/platform/utils/permission"
import { powerList } from "../../components/define.d"
import { computed, onMounted, ref } from "vue"
import { useMessageBoxInit } from "../../components/messageBox"
import { useTbInit } from "../../components/tableBase"
import { BaseLineSysApi, getTaskByBusinessIds } from "@/app/baseline/api/system"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import { modalSize } from "@/app/baseline/utils/layout-config"
import { IModalType } from "../../../utils/types/common"
import { DictApi } from "@/app/baseline/api/dict"

import repairEditor from "./repairEditor.vue"
import repairDetail from "./repairDetail.vue"
import {
	MatWasteRepairApplyPageQuery,
	MatWasteRepairApplyVo
} from "@/app/baseline/utils/types/waste-repair-apply"
import {
	delWasteRepairApply,
	getWasteRepairApplyBmpStatusCnt,
	listWasteRepairApplyPaged
} from "@/app/baseline/api/waste/repairApply"
import { appStatus } from "@/app/baseline/api/dict"
import { tableColFilter, toFixedTwo } from "@/app/baseline/utils"
import { omit } from "lodash-es"
import { useUserStore } from "@/app/platform/store/modules/user"
import { hasPermi } from "@/app/baseline/utils"
import ApprovedDrawer from "@/app/baseline/views/components/approvedDrawer.vue"

const { userInfo } = storeToRefs(useUserStore())

const { showDelConfirm } = useMessageBoxInit()

/**
 * 查询条件 配置
 */
const queryArrList = computed(() => {
	return [
		{
			name: "返修单号",
			key: "code",
			placeholder: "请输入返修单号",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "返修单名称",
			key: "label",
			placeholder: "请输入返修单号",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "申请人",
			key: "createdByName",
			placeholder: "请输入申请人",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "申请部门",
			key: "sysOrgId",
			type: "treeSelect",
			placeholder: "请选择",
			treeApi: () =>
				BaseLineSysApi.getDepartmentTreeWithCompanyDisabled(
					userInfo.value.companyId
				)
		},
		{
			name: "维修方式",
			key: "repairWay",
			type: "select",
			placeholder: "请选择",
			children: [
				{ label: "委外维修", value: 1 },
				{ label: "自主维修", value: 2 }
			]
		}
	]
})

/**
 * Title 配置
 */
const rightTitle = {
	name: ["返修申请"],
	icon: ["fas", "square-share-nodes"]
}
const titleBtnConf = [
	{
		name: "新建返修申请",
		roles: powerList.wasteRepairApplyBtnCreate,
		icon: ["fas", "circle-plus"]
	}
]

/**
 * tab 配置项
 */
const statusCnt = ref<number[]>([])
const tabList = [
	{
		name: "草稿箱",
		value: appStatus.pendingApproval + "," + appStatus.rejected
	},
	{ name: "审批中", value: appStatus.underApproval },
	{ name: "已审批", value: appStatus.approved }
]
const tabStatus = ref(appStatus.pendingApproval + "," + appStatus.rejected)
const activeName = ref(tabList[0].name)
const handleTabsClick = (tab: any) => {
	activeName.value = tab.paneName
	tabStatus.value = tabList[tab.index].value
	getTableData()
}

const tbInit = useTbInit<MatWasteRepairApplyVo, MatWasteRepairApplyPageQuery>()
const {
	tableData,
	tableRef,
	tableLoading,
	currentPage,
	pageTotal,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = tbInit

/**
 * table 列配置
 */
tbInit.tableProp = computed(() => {
	const defCols: TableColumnType[] = [
		{ label: "返修单号", prop: "code", width: 180 },
		{ label: "返修单名称", prop: "label" },
		{ label: "维修方式", prop: "repairWay", needSlot: true, width: 120 },
		{ label: "物资编码", prop: "materialCodeNum" },
		{ label: "返修数量", prop: "repairNum", needSlot: true, align: "right" },
		{ label: "返修备注说明", prop: "reason", width: 180 },
		{ label: "审批状态", prop: "bpmStatus", needSlot: true },
		{ label: "申请部门", prop: "sysOrgId_view" },
		{ label: "申请人", prop: "createdBy_view" },
		{ label: "申请时间", prop: "createdDate", width: 150, sortable: true },
		{ label: "返修状态", prop: "status", needSlot: true },
		{
			label: "操作",
			width: 200,
			prop: "operations",
			needSlot: true,
			fixed: "right"
		}
	]

	switch (tabStatus.value) {
		case appStatus.approved:
			// 未启动
			return defCols
		default:
			return tableColFilter(defCols, ["返修状态"])
	}
})

fetchFunc.value = listWasteRepairApplyPaged

/**
 * tab 切换数据源
 */
const getTableData = (data?: any) => {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		bpmStatus: tabStatus.value,
		sord: "desc",
		sidx: "createdDate",
		...data
	}
	handleUpdate()
}

const curRowId = ref<any>("")
const curRowData = ref<MatWasteRepairApplyVo>({})
const viewVisible = ref<boolean>(false)
const editorVisible = ref(false)
const editorMode = ref(IModalType.edit)

const approvedVisible = ref(false)
const editTask = ref<Record<string, any>>()

/**
 * 新建 操作
 */
const handleClickAddBtn = () => {
	editorMode.value = IModalType.create
	editorVisible.value = true
	curRowId.value = ""
	curRowData.value = {}
}

/**
 * 编辑 操作
 * @param row
 */
const onRowEdit = (row: MatWasteRepairApplyVo) => {
	curRowId.value = row.id
	curRowData.value = row
	editorVisible.value = true
	editorMode.value = IModalType.edit
}

/**
 * 查看
 * @param row
 */
const onRowView = async (row: MatWasteRepairApplyVo) => {
	curRowId.value = row.id
	curRowData.value = row
	/* viewVisible.value = true */
	editorMode.value = IModalType.view

	if (row?.bpmStatus == appStatus.pendingApproval) {
		viewVisible.value = true
	} else {
		const res = await getTaskByBusinessIds({
			businessIds: [row?.id],
			camundaKey: "waste_repair_apply"
		})

		if (Array.isArray(res) && res.length > 0) {
			editTask.value = { ...res[res.length - 1] }
			approvedVisible.value = true
		} else {
			viewVisible.value = true
		}
	}
}

/**
 * 删除 操作
 * @param row
 */
const onRowDel = async (row: MatWasteRepairApplyVo) => {
	await showDelConfirm()
	await delWasteRepairApply(row.id)
	ElMessage.success("操作成功")
	handleUpdate()
}

/**
 * 查看/新建/编辑  关闭 抽屉回调
 * @param msg save | false
 */
const handleCloseDrawer = (close?: boolean) => {
	handleUpdate()
	if (close) {
		editorVisible.value = false
	}
}

/**
 * 更新主表/ tab 状态统计
 */
async function handleUpdate() {
	statusCnt.value = await getWasteRepairApplyBmpStatusCnt(
		omit(
			{
				...fetchParam.value
			},
			"bpmStatus",
			"currentPage",
			"pageSize"
		) as any
	)
	fetchTableData()
}

onMounted(() => {
	getTableData()
})

/**
 * 排序
 * @param column
 * @param prop 字段名称
 * @param order ascending 升序 descending 降序
 */
function handleSortChange(column: any, prop: any, order: any) {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		sord:
			order === "descending" ? "desc" : order === "ascending" ? "asc" : "desc",
		sidx: order ? prop : "createdDate" // 排序字段
	}

	fetchTableData()
}
</script>
<template>
	<div class="app-container">
		<div class="whole-frame">
			<ModelFrame class="top-frame">
				<Query
					:queryArrList="queryArrList"
					@getQueryData="getTableData"
					class="ml10"
				/>
			</ModelFrame>

			<ModelFrame class="bottom-frame">
				<Title
					:title="rightTitle"
					:button="(titleBtnConf as any)"
					@on-btn-click="handleClickAddBtn"
				>
					<div class="app-tabs-wrapper">
						<el-tabs v-model="activeName" @tab-click="handleTabsClick">
							<el-tab-pane
								v-for="(tab, index) in tabList"
								:key="index"
								:label="`${tab.name}（${statusCnt[index] ?? 0}）`"
								:name="tab.name"
								:index="tab.name"
							/>
						</el-tabs>
					</div>
				</Title>

				<pitaya-table
					ref="tableRef"
					:columns="(tbInit.tableProp as any)"
					:tableData="tableData"
					:total="pageTotal"
					:need-index="true"
					@on-current-page-change="onCurrentPageChange"
					:table-loading="tableLoading"
					@on-table-sort-change="handleSortChange"
				>
					<!-- 审批状态 -->
					<template #bpmStatus="{ rowData }">
						<dict-tag
							:options="DictApi.getBpmStatus()"
							:value="rowData.bpmStatus"
						/>
					</template>

					<!-- 返修状态 -->
					<template #status="{ rowData }">
						<dict-tag
							:options="DictApi.getRepairStatus()"
							:value="rowData.status"
						/>
					</template>

					<!-- 维修方式 -->
					<template #repairWay="{ rowData }">
						{{ rowData.repairWay == 1 ? "委外维修" : "自主维修" }}
					</template>

					<!-- 返修数量 -->
					<template #repairNum="{ rowData }">
						{{ toFixedTwo(rowData.repairNum) }}
					</template>

					<template #operations="{ rowData }">
						<slot
							v-if="
								(tabStatus == tabList[0].value &&
									isCheckPermission(powerList.wasteRepairApplyBtnEdit) &&
									hasPermi(rowData.createdBy)) ||
								isCheckPermission(powerList.wasteRepairApplyBtnPreview) ||
								(tabStatus == tabList[0].value &&
									isCheckPermission(powerList.wasteRepairApplyBtnDrop) &&
									hasPermi(rowData.createdBy))
							"
						>
							<el-button
								v-btn
								link
								@click="onRowEdit(rowData)"
								v-if="
									tabStatus == tabList[0].value &&
									isCheckPermission(powerList.wasteRepairApplyBtnEdit) &&
									hasPermi(rowData.createdBy)
								"
								:disabled="checkPermission(powerList.wasteRepairApplyBtnEdit)"
							>
								<font-awesome-icon :icon="['fas', 'pen-to-square']" />
								<span class="table-inner-btn">编辑</span>
							</el-button>
							<el-button
								v-btn
								link
								@click="onRowView(rowData)"
								v-if="isCheckPermission(powerList.wasteRepairApplyBtnPreview)"
								:disabled="
									checkPermission(powerList.wasteRepairApplyBtnPreview)
								"
							>
								<font-awesome-icon :icon="['fas', 'eye']" />
								<span class="table-inner-btn">查看</span>
							</el-button>
							<el-button
								v-btn
								link
								@click="onRowDel(rowData)"
								v-if="
									tabStatus == tabList[0].value &&
									isCheckPermission(powerList.wasteRepairApplyBtnDrop) &&
									hasPermi(rowData.createdBy)
								"
								:disabled="checkPermission(powerList.wasteRepairApplyBtnDrop)"
							>
								<font-awesome-icon :icon="['fas', 'trash-can']" />
								<span class="table-inner-btn">移除</span>
							</el-button>
						</slot>
						<slot v-else>---</slot>
					</template>
				</pitaya-table>

				<!-- 编辑/新建  -->
				<Drawer
					:size="modalSize.xl"
					v-model:drawer="editorVisible"
					:destroyOnClose="true"
				>
					<repairEditor
						:id="curRowId"
						:mode="editorMode"
						@save="handleCloseDrawer"
						@close="editorVisible = false"
						@update="fetchTableData"
					/>
				</Drawer>

				<!-- 查看  -->
				<Drawer
					:size="modalSize.xl"
					v-model:drawer="viewVisible"
					:destroyOnClose="true"
				>
					<repairDetail
						:id="curRowId"
						:mode="editorMode"
						@close="viewVisible = false"
					/>
				</Drawer>

				<!-- 审批、查看弹窗 -->
				<Drawer
					:size="modalSize.xxl"
					v-model:drawer="approvedVisible"
					:destroyOnClose="true"
				>
					<approved-drawer
						:mod="editorMode"
						:task-value="editTask"
						@on-save-or-close="approvedVisible = false"
					/>
				</Drawer>
			</ModelFrame>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
