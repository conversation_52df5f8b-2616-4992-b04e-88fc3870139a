<script setup lang="ts">
import { onMounted, computed } from "vue"
import { useTbInit } from "@/app/baseline/views/components/tableBase"
import { listWasteRepairCompanyPaged } from "@/app/baseline/api/waste/supplier"
import {
	MatWasteRepairCompanyVo,
	MatWasteRepairCompanyRequest
} from "@/app/baseline/utils/types/waste-supplier"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import { DictApi } from "@/app/baseline/api/dict"

import { first } from "lodash-es"
import { useTableSelectorUtils } from "../../store/hooks/table-selector-utils"

const props = defineProps<{
	/**
	 * 是否支持多选，默认单选
	 */
	multiple?: boolean

	tableApi?: (arg?: any) => any

	tableApiParams?: any
	queryArrList?: any

	/**
	 * 当前选中的id 集合
	 */
	selectedIds?: any[]
}>()

const emits = defineEmits<{
	(e: "onSave", btnName: string, v?: any | any[]): void
}>()

/**
 * title 配置
 */
const drawerTitle = {
	name: ["选择维修公司"],
	icon: ["fas", "square-share-nodes"]
}

/**
 * query 查询条件 配置
 */
const queryArrayList = props.queryArrList ?? [
	{
		name: "维修公司名称",
		key: "label",
		type: "input",
		placeholder: "请输入维修公司名称"
	},
	{
		name: "联系人",
		key: "contact",
		type: "input",
		placeholder: "请输入联系人"
	}
]

const {
	tableProp,
	tableRef,
	tableData,
	tableLoading,
	pageTotal,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc
} = useTbInit<MatWasteRepairCompanyVo, MatWasteRepairCompanyRequest>()

const { fetchTableDataWithSetRowsCheck } = useTableSelectorUtils({
	tableData,
	tableRef,
	selectedIds: props.selectedIds,
	fetchTableData
})

/**
 * table 列 配置
 */
tableProp.value = [
	{ label: "维修公司名称", prop: "label", minWidth: 130, fixed: "left" },
	{ label: "法定代表人", prop: "legalRepresentative" },
	{ label: "联系人", prop: "contact" },
	{ label: "联系人手机号", prop: "contactPhoneNumber" },
	{ label: "联系地址", prop: "contactAddress" },
	{ label: "电子邮箱", prop: "contactEmail", minWidth: 120 },
	{ label: "审批状态", prop: "bpmStatus", needSlot: true, width: 120 },
	{ label: "创建人", prop: "createdBy_view" },
	{ label: "创建时间", prop: "createdDate" }
]

fetchFunc.value = props.tableApi ?? listWasteRepairCompanyPaged

/**
 * 按钮配置
 */
const drawerBtnList = computed(() => {
	return [
		{ name: "取消", icon: ["fas", "circle-minus"] },
		{
			name: "保存",
			icon: ["fas", "floppy-disk"],
			disabled: selectedTableList.value.length > 0 ? false : true
		}
	]
})

const handleDrawerBtnAction = (btnName: string | undefined) => {
	emits(
		"onSave",
		btnName as any,
		props.multiple ? selectedTableList.value : first(selectedTableList.value)
	)
}

const handleQuery = (e?: any) => {
	fetchParam.value = {
		...fetchParam.value,
		...e
	}

	fetchTableDataWithSetRowsCheck()
}
onMounted(() => {
	fetchParam.value.bpmStatus = 2
	fetchParam.value.sord = "desc"
	fetchParam.value.sidx = "createdDate"

	if (props.tableApiParams) {
		fetchParam.value = {
			...fetchParam.value,
			...props.tableApiParams
		}
	}

	handleQuery()
})
</script>
<template>
	<div class="drawer-container">
		<div class="drawer-column">
			<div class="rows">
				<Title :title="drawerTitle" />
				<div style="margin: 10px 10px -10px">
					<Query
						:queryArrList="queryArrayList"
						@getQueryData="handleQuery"
						class="custom-q"
					/>
				</div>
				<div class="common-from-group" style="padding: 0px">
					<el-scrollbar>
						<PitayaTable
							ref="tableRef"
							:columns="tableProp"
							:table-data="tableData"
							:need-index="true"
							:need-pagination="true"
							:single-select="!multiple"
							:need-selection="true"
							:total="pageTotal"
							@on-current-page-change="fetchTableDataWithSetRowsCheck"
							@onSelectionChange="selectedTableList = $event"
							:table-loading="tableLoading"
						>
							<template #bpmStatus="{ rowData }">
								<dict-tag
									:options="DictApi.getBpmStatus()"
									:value="rowData.bpmStatus"
								/>
							</template>
						</PitayaTable>
					</el-scrollbar>
				</div>
			</div>
			<ButtonList
				class="footer"
				:button="drawerBtnList"
				@on-btn-click="handleDrawerBtnAction"
			/>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.drawer-column {
	width: 100%;
}
</style>
