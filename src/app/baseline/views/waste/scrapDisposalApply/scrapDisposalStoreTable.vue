<!-- 仓库明细 table -->
<template>
	<pitaya-table
		ref="tableRef"
		:columns="tableProp"
		:table-data="tableData"
		:need-index="true"
		:single-select="false"
		:need-pagination="true"
		:total="pageTotal"
		:table-loading="tableLoading"
		@on-selection-change="onDataSelected"
		@on-current-page-change="onCurrentPageChange"
	>
		<!-- 预估回收总重量 -->
		<template #recoveryWeight="{ rowData }">
			{{ toFixedTwo(rowData.recoveryWeight) }}
		</template>

		<!-- 报废总数量 -->
		<template #completeNum="{ rowData }">
			{{ toFixedTwo(rowData.completeNum) }}
		</template>

		<!-- 操作 -->
		<template #actions="{ rowData }">
			<slot>
				<el-button v-btn link @click="showStoreMatDetail(rowData)">
					<font-awesome-icon :icon="['fas', 'eye']" />
					<span class="table-inner-btn">查看物资明细</span>
				</el-button>
			</slot>
		</template>
	</pitaya-table>

	<!-- 仓库明细 - 查看物资明细 -->
	<Drawer v-model:drawer="detailVisible" :size="modalSize.lg" destroy-on-close>
		<scrap-disposal-store-table-detail
			:row="editingTableRow"
			:id="id"
			@close="detailVisible = false"
		/>
	</Drawer>
</template>

<script setup lang="ts">
import { useTbInit } from "../../components/tableBase"
import { modalSize } from "@/app/baseline/utils/layout-config"
import scrapDisposalStoreTableDetail from "./scrapDisposalStoreTableDetail.vue"
import { WasteStoreVo } from "@/app/baseline/utils/types/waste-scrap-apply"
import { listWasteStoreByDisposalApplyIdPaged } from "@/app/baseline/api/waste/scrap-disposal-apply"
import { MatWasteScrapDisposalApplyItemVoQuery } from "@/app/baseline/utils/types/waste-scrap-disposal-apply"
import { toFixedTwo } from "@/app/baseline/utils"

const props = defineProps<{ id: any }>()

const editingTableRow = ref()

const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange,
	onDataSelected
} = useTbInit<WasteStoreVo, MatWasteScrapDisposalApplyItemVoQuery>()

fetchFunc.value = listWasteStoreByDisposalApplyIdPaged

tableProp.value = [
	{ prop: "storeCode", label: "仓库编码", width: 100, fixed: "left" },
	{ prop: "storeLabel", label: "仓库名称" },
	{ prop: "depotId_view", label: "所属段区" },
	{ prop: "positionId", label: "仓库位置" },
	{ prop: "storeManage", label: "库管员" },
	{ prop: "materialTypeCnt", label: "物资分类" },
	{ prop: "materialIdCnt", label: "物资编码" },
	{ prop: "completeNum", label: "报废总数量", needSlot: true, align: "right" },
	{
		prop: "recoveryWeight",
		label: "预估回收总重量(kg)",
		width: 150,
		needSlot: true,
		align: "right"
	},
	{ prop: "actions", label: "操作", needSlot: true, fixed: "right", width: 130 }
]

const detailVisible = ref(false)

onMounted(() => {
	fetchParam.value = {
		id: props.id,
		sord: "asc",
		sidx: "code",
		...fetchParam.value
	}
	fetchTableData()
})

function showStoreMatDetail(e: any) {
	editingTableRow.value = { ...e }
	detailVisible.value = true
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
