<!-- 处置报告 -->
<script setup lang="ts">
import { onMounted, reactive, ref } from "vue"
import FormElement from "@/app/baseline/views/components/formElement.vue"
import { ElMessage, type FormInstance, type FormRules } from "element-plus"
import { FormElementType } from "../../components/define"
import {
	addScrapDisposalResult,
	listDisposalApplyDetail
} from "@/app/baseline/api/waste/scrap-disposal-apply"
import { fileBusinessType } from "@/app/baseline/api/dict"
import {
	getIdempotentToken,
	validateAndCorrectInput
} from "@/app/baseline/utils/validate"
import { toNumber } from "lodash-es"
import {
	IIdempotentTokenType,
	IIdempotentTokenTypePre
} from "@/app/baseline/utils/types/common"

export interface Props {
	id: any
}

const props = defineProps<Props>()

/**
 * Title 配置
 */
const qualityDrawerTitle = {
	name: ["处置报告"],
	icon: ["fas", "square-share-nodes"]
}

/**
 * 按钮 配置
 */
const formBtnList = ref([
	{ name: "取消", icon: ["fas", "circle-minus"] },
	{ name: "保存", icon: ["fas", "check-circle"], disabled: false }
])

const emits = defineEmits(["close", "update"])
const ruleFormRef = ref<FormInstance>()

const formModelData = ref<{ [propName: string]: any }>({})

/**
 * 表单 配置
 */
const formEl = reactive<FormElementType[][]>([
	[{ label: "报废处置单号", name: "code", disabled: true }],
	[{ label: "报废处置单名称", name: "label", disabled: true }],
	[{ label: "处置方式", name: "disposalType_view", disabled: true }],
	[{ label: "处置协议合同编号", name: "contractCode", maxlength: 50 }],
	[{ label: "受让方名称", name: "assigneeName", maxlength: 50 }],
	[{ label: "代理机构", name: "agency", maxlength: 50 }],
	[
		{
			label: "处置成本",
			name: "disposalcost",
			type: "number",
			input: (value: any) => {
				formModelData.value.disposalcost = validateAndCorrectInput(value, 5)
			},
			blur: (event: any) => {
				formModelData.value.disposalcost = toNumber(event.target.value)
			},
			append: "元"
		}
	],
	[
		{
			label: "处置收益",
			name: "dispoalIncome",
			type: "number",
			input: (value: any) => {
				formModelData.value.dispoalIncome = validateAndCorrectInput(value, 5)
			},
			blur: (event: any) => {
				formModelData.value.dispoalIncome = toNumber(event.target.value)
			},
			append: "元"
		}
	]
])

/**
 * 表单校验 配置
 */
const rules = reactive<FormRules<typeof formModelData>>({
	assigneeName: {
		required: true,
		message: "受让方名称不能为空",
		trigger: "change"
	},
	disposalcost: [
		{
			required: true,
			message: "处置成本不能为空",
			trigger: "change"
		}
	],
	dispoalIncome: [
		{
			required: true,
			message: "处置收益不能为空",
			trigger: "change"
		}
	],
	fileId: {
		required: true,
		message: "上传附件不能为空"
	}
})

/* 表单 操作 */
const drawerBtnLoading = ref(false)
const onFormBtnList = (btnName: string | undefined) => {
	if (btnName === "保存") {
		if (!ruleFormRef.value) {
			return
		}
		ruleFormRef.value?.validate(async (valid) => {
			if (!valid) {
				return
			}
			drawerBtnLoading.value = true
			try {
				const idempotentToken = getIdempotentToken(
					IIdempotentTokenTypePre.other,
					IIdempotentTokenType.wasteBFCZApply,
					formModelData.value.id
				)

				await addScrapDisposalResult(
					{
						applyId: formModelData.value.id,
						contractCode: formModelData.value.contractCode,
						assigneeName: formModelData.value.assigneeName,
						agency: formModelData.value.agency,
						disposalcost: formModelData.value.disposalcost,
						dispoalIncome: formModelData.value.dispoalIncome,
						attachmentId: [{ id: formModelData.value.fileId }]
					},
					idempotentToken
				)

				ElMessage.success("操作成功")

				emits("update")
				emits("close")
			} finally {
				drawerBtnLoading.value = false
			}
		})
	} else if (btnName === "取消") {
		emits("close")
	}
}

const uploadUrl = `/pitaya/system/common/upload?businessType=${fileBusinessType.disposalReport}&businessId=${props.id}`

/**
 * 附件上传成功 回调
 * @param response
 */
const handleSuccess = (response: Record<string, any>) => {
	formModelData.value.fileId = response.data?.id
}
/**
 * 手动删除附件 回调
 * @param uploadFile
 * @param fileList
 */
const handleRemove = () => {
	formModelData.value.fileId = ""
}
/**
 * 获取报废处置详情
 */
const drawerLoading = ref(false)
function getDetail() {
	drawerLoading.value = true

	// 获取详情
	listDisposalApplyDetail(props.id)
		.then((r = {}) => {
			formModelData.value = { ...r }
		})
		.finally(() => (drawerLoading.value = false))
}

onMounted(() => {
	getDetail()
})
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<div class="drawer-column">
			<div class="rows">
				<Title :title="qualityDrawerTitle" />
				<el-scrollbar>
					<el-form
						style="padding-bottom: 30px"
						class="content form-base"
						ref="ruleFormRef"
						:model="formModelData"
						:rules="rules"
						label-position="top"
					>
						<FormElement :form-element="formEl" :form-data="formModelData" />

						<el-form-item label="上传附件" prop="fileId">
							<el-input type="hidden" v-model="formModelData.fileId" />
							<UploadFile
								:action="uploadUrl"
								:viewDesc="true"
								:maxCount="1"
								@onSuccess="handleSuccess"
								@handle-remove="handleRemove"
								listType="text"
							/>
						</el-form-item>
					</el-form>
				</el-scrollbar>
			</div>
			<ButtonList
				class="footer"
				:button="formBtnList"
				:loading="drawerBtnLoading"
				@onBtnClick="onFormBtnList"
			/>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.drawer-column {
	width: 100%;
}
:deep(.el-form-item__error) {
	top: 34px !important;
}

:deep(.el-upload__tip) {
	margin-top: 12px !important;
}
</style>
