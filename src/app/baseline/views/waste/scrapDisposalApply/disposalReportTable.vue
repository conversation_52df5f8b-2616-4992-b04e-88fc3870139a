<!-- 处置结果  -->
<template>
	<pitaya-table
		ref="tableRef"
		:columns="tableProp"
		:table-data="tableData"
		:need-index="true"
		:need-pagination="true"
		:total="pageTotal"
		:table-loading="tableLoading"
		@on-selection-change="selectedTableList = $event"
		@on-current-page-change="onCurrentPageChange"
	>
		<!-- 处置成本 -->
		<template #disposalcost="{ rowData }">
			<cost-tag :value="rowData.disposalcost" />
		</template>

		<!-- 处置收益 -->
		<template #dispoalIncome="{ rowData }">
			<cost-tag :value="rowData.dispoalIncome" />
		</template>

		<template #operations="{ rowData }">
			<slot>
				<el-button v-btn link @click="downloadFile(rowData)">
					<font-awesome-icon :icon="['fas', 'file-arrow-down']" />
					<span class="table-inner-btn">下载</span>
				</el-button>
				<el-button v-btn link @click="deldisposalReport(rowData)" v-if="isEdit">
					<font-awesome-icon :icon="['fas', 'trash-can']" />
					<span class="table-inner-btn">移除</span>
				</el-button>
			</slot>
		</template>

		<template #footerOperateLeft>
			<button-list
				v-if="isEdit"
				:button="tbBtnConf"
				:is-not-radius="true"
				:loading="tbBtnLoading"
				@on-btn-click="handleTbBtnClick"
			/>
		</template>
	</pitaya-table>

	<Drawer
		:size="modalSize.sm"
		v-model:drawer="disposalReportVisible"
		:destroyOnClose="true"
	>
		<disposal-report
			:id="props.id"
			@close="disposalReportVisible = false"
			@update="
				() => {
					emit('updateMain')
					fetchTableData()
				}
			"
		/>
	</Drawer>
</template>

<script setup lang="ts">
import { useTbInit } from "../../components/tableBase"
import { modalSize } from "@/app/baseline/utils/layout-config"
import {
	delScrapDisposalResult,
	listScrapDisposalResultPaged
} from "@/app/baseline/api/waste/scrap-disposal-apply"
import {
	MatWasteScrapDisposalApplyItemVoQuery,
	MatWasteScrapDisposalResultVo
} from "@/app/baseline/utils/types/waste-scrap-disposal-apply"
import costTag from "../../components/costTag.vue"
import disposalReport from "./disposalReport.vue"
import { FileApi } from "@/app/baseline/api/file"
import { first } from "lodash-es"
import { scrapDisposalApplyStatus } from "./scrapDisposalApply"
import { IModalType } from "@/app/baseline/utils/types/common"

const props = withDefaults(
	defineProps<{
		/**
		 * 报废处置申请id
		 */
		id: any
		status: any
		mode?: IModalType
	}>(),
	{
		mode: IModalType.edit
	}
)
const emit = defineEmits<{
	(e: "updateMain"): void
}>()

const disposalReportVisible = ref(false)
const tbBtnLoading = ref(false)
const tbBtnConf = [
	{
		name: "添加处置报告",
		icon: ["fas", "circle-plus"]
	}
]

const isEdit = computed(() => {
	return props.status == scrapDisposalApplyStatus.unDisposed &&
		IModalType.edit == props.mode
		? true
		: false
})
function handleTbBtnClick() {
	disposalReportVisible.value = true
}
const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	fetchParam,
	selectedTableList,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = useTbInit<
	MatWasteScrapDisposalResultVo,
	MatWasteScrapDisposalApplyItemVoQuery
>()

fetchFunc.value = listScrapDisposalResultPaged

tableProp.value = [
	{
		prop: "sysOrgId_view",
		label: "处置部门",
		fixed: "left"
	},
	{
		prop: "assigneeName",
		label: "受让方名称"
	},
	{
		prop: "contractCode",
		label: "处置协议合同编号"
	},
	{
		prop: "agency",
		label: "代理机构"
	},
	{
		prop: "disposalcost",
		label: "处置成本",
		needSlot: true,
		align: "right"
	},
	{
		prop: "dispoalIncome",
		label: "处置收益",
		needSlot: true,
		align: "right"
	},
	{
		prop: "createdBy_view",
		label: "更新人"
	},
	{
		prop: "createdDate",
		label: "更新时间"
	},
	{
		label: "操作",
		width: 150,
		prop: "operations",
		needSlot: true
	}
]

/**
 * 删除 操作
 * @param e
 */
async function deldisposalReport(e: MatWasteScrapDisposalResultVo) {
	await delScrapDisposalResult(e.id as number)
	ElMessage.success("操作成功")
	emit("updateMain")
	fetchTableData()
}

/**
 * 下载附件 操作
 * @param e
 */
function downloadFile(e: MatWasteScrapDisposalResultVo) {
	FileApi.downloadFile(first(e.attachmentList)?.filePath as any)
}
onMounted(() => {
	fetchParam.value.id = props.id
	fetchParam.value.sord = "desc"
	fetchParam.value.sidx = "createdDate"
	fetchTableData()
})

const tableFileList = computed(() => {
	return tableData.value || []
})
defineExpose({ tableFileList })
</script>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
