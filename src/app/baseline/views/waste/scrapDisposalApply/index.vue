<!-- 报废处置 主列表 -->
<script lang="ts" setup>
import {
	checkPermission,
	isCheckPermission
} from "@/app/platform/utils/permission"
import { powerList } from "../../components/define.d"
import { computed, onMounted, ref } from "vue"
import { useDictInit } from "../../components/dictBase"
import { useMessageBoxInit } from "../../components/messageBox"
import { useTbInit } from "../../components/tableBase"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import { modalSize } from "@/app/baseline/utils/layout-config"
import { IModalType } from "../../../utils/types/common"
import { appStatus, DictApi } from "@/app/baseline/api/dict"
import scrapDisposalApplyDetail from "./scrapDisposalApplyDetail.vue"
import scrapDisposalApplyEditor from "./scrapDisposalApplyEditor.vue"
import assessmentReport from "./assessmentReport.vue"
import disposalReport from "./disposalReport.vue"
import {
	delDisposalApply,
	getStatusCnt,
	listDisposalApplyPaged
} from "@/app/baseline/api/waste/scrap-disposal-apply"
import {
	MatWasteScrapDisposalApplyStatusVo,
	MatWasteScrapDisposalApplyVo,
	MatWasteScrapDisposalApplyVoRequest
} from "@/app/baseline/utils/types/waste-scrap-disposal-apply"
import { first, omit } from "lodash-es"
import {
	scrapDisposalApplyStatus,
	getScrapDisposalStatus
} from "./scrapDisposalApply"
import { hasPermi, toFixedTwo } from "@/app/baseline/utils"
import { getTaskByBusinessIds } from "@/app/baseline/api/system"
import ApprovedDrawer from "@/app/baseline/views/components/approvedDrawer.vue"

const { dictOptions, getDictByCodeList, dictFilter } = useDictInit()
const { showDelConfirm } = useMessageBoxInit()

/**
 * 查询条件 配置
 */
const queryArrList = computed(() => {
	return [
		{
			name: "处置单号",
			key: "code",
			placeholder: "请输入处置单号",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "处置名称",
			key: "label",
			placeholder: "请输入处置名称",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "申请人",
			key: "realname",
			placeholder: "请输入申请人",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "处置方式",
			key: "disposalType",
			placeholder: "请选择处置方式",
			type: "select",
			children: dictOptions.value["DISPOSAL_TYPE"]
		},
		{
			name: "废旧物资分类",
			key: "materialType",
			placeholder: "请选择废旧物资分类",
			type: "select",
			children: dictOptions.value["WASTE_MATERIALS_TYPE"]
		}
		/* {
			name: "报废状态", // TODO
			key: "bfStatus",
			placeholder: "请选择报废状态",
			type: "select",
			children: [
				{ label: "待询价", value: 1 },
				{ label: "已报废询价", value: 2 },
				{ label: "已处置", value: 3 }
			]
		} */
	]
})

/**
 * Title 配置
 */
const rightTitle = {
	name: ["报废处置"],
	icon: ["fas", "square-share-nodes"]
}
const titleBtnConf = [
	{
		name: "新建处置申请",
		roles: powerList.wasteScrapDisposalBtnCreate,
		icon: ["fas", "circle-plus"]
	}
]

/**
 * tab 配置项
 */
const statusCnt = ref<MatWasteScrapDisposalApplyStatusVo>({})
const tabList = computed(() => {
	return [
		{
			name: "草稿箱",
			value: scrapDisposalApplyStatus.pendingApproval,
			count: statusCnt.value[0] ?? 0
		},
		{
			name: "审批中",
			value: scrapDisposalApplyStatus.underApproval,
			count: statusCnt.value[1] ?? 0
		},
		{
			name: "待询价",
			value: scrapDisposalApplyStatus.unEvaluated,
			count: statusCnt.value[2] ?? 0
		},
		{
			name: "待处置",
			value: scrapDisposalApplyStatus.unDisposed,
			count: statusCnt.value[3] ?? 0
		},
		{
			name: "已完成",
			value: scrapDisposalApplyStatus.isFinish,
			count: statusCnt.value[4] ?? 0
		}
	]
})
const tabStatus = ref(scrapDisposalApplyStatus.pendingApproval)
const activeName = ref(tabList.value[0].name)
const handleTabsClick = async (tab: any) => {
	activeName.value = tab.paneName
	tabStatus.value = tabList.value[tab.index].value as any

	getTableData()
}

const {
	tableData,
	tableProp,
	tableRef,
	tableLoading,
	currentPage,
	pageTotal,
	fetchParam,
	selectedTableList,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = useTbInit<
	MatWasteScrapDisposalApplyVo,
	MatWasteScrapDisposalApplyVoRequest
>()

/**
 * table 列配置
 */
tableProp.value = [
	{ label: "处置单号", prop: "code", width: 180, fixed: "left" },
	{ label: "处置名称", prop: "label" },
	{
		label: "处置方式",
		prop: "disposalType", // 字典 DISPOSAL_TYPE
		needSlot: true,
		width: 180
	},
	{ label: "备注说明", prop: "remark" },
	{ label: "审批状态", prop: "bpmStatus", needSlot: true },
	{ label: "报废状态", prop: "status", needSlot: true },
	{
		label: "废旧物资分类",
		prop: "materialType",
		needSlot: true,
		width: 180
	},
	{
		label: "预估回收总重量(kg)",
		prop: "recoveryWeight",
		needSlot: true,
		align: "right",
		width: 150
	},
	{ label: "申请人", prop: "createdBy_view" },
	{ label: "申请时间", prop: "createdDate", width: 150, sortable: true },
	{
		label: "操作",
		width: 200,
		prop: "operations",
		needSlot: true,
		fixed: "right"
	}
]
fetchFunc.value = listDisposalApplyPaged

/**
 * tab 切换数据源
 */
const getTableData = async (data?: any) => {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		status: tabStatus.value,
		...data
	}
	handleUpdate()
}

const curRowId = ref<any>("")
const curRowData = ref<MatWasteScrapDisposalApplyVo>({})
const viewVisible = ref<boolean>(false)
const editorVisible = ref(false)
const editorMode = ref(IModalType.edit)
const approvedVisible = ref(false)
const editTask = ref<Record<string, any>>()

/**
 * 新建 操作
 */
const handleClickAddBtn = () => {
	editorMode.value = IModalType.create
	editorVisible.value = true
	curRowId.value = ""
	curRowData.value = {}
}

/**
 * 编辑 操作
 * @param row
 */
const onRowEdit = (row: MatWasteScrapDisposalApplyVo) => {
	curRowId.value = row.id
	curRowData.value = row
	editorVisible.value = true
	editorMode.value = IModalType.edit
}

/**
 * 查看
 * @param row
 */
const onRowView = async (row: MatWasteScrapDisposalApplyVo) => {
	curRowId.value = row.id
	curRowData.value = row
	/* viewVisible.value = true */
	editorMode.value = IModalType.view

	if (row?.bpmStatus == appStatus.pendingApproval) {
		viewVisible.value = true
	} else {
		const res = await getTaskByBusinessIds({
			businessIds: [row?.id],
			camundaKey: "waste_scrap_disposal_apply"
		})

		if (Array.isArray(res) && res.length > 0) {
			editTask.value = { ...res[res.length - 1] }
			approvedVisible.value = true
		} else {
			viewVisible.value = true
		}
	}
}

/**
 * 删除 操作
 * @param row
 */
const onRowDel = async (row: MatWasteScrapDisposalApplyVo) => {
	await showDelConfirm()
	await delDisposalApply(row.id as number)
	ElMessage.success("操作成功")
	handleUpdate()
}

/**
 * 待询价/待处置 操作 暂时去掉
 */
const assessmentReportVisible = ref(false)
const disposalReportVisible = ref(false)
const tbBtnLoading = ref(false)

const tbBtnConf = computed(() => {
	const notSelected = selectedTableList.value?.length < 1
	switch (tabStatus.value) {
		case scrapDisposalApplyStatus.unEvaluated:
			return [
				{
					name: "报废询价",
					icon: ["fas", "calendar-alt"],
					disabled: notSelected
				}
			]
		case scrapDisposalApplyStatus.unDisposed:
			return [
				{
					name: "处置",
					icon: ["fas", "comment-dots"],
					disabled: notSelected
				}
			]
		default:
			return []
	}
})
async function handleTbBtnClick(name?: string) {
	tbBtnLoading.value = true
	try {
		switch (name) {
			case "报废询价":
				assessmentReportVisible.value = true
				break
			case "处置":
				disposalReportVisible.value = true
				break
			default:
				break
		}
	} finally {
		tbBtnLoading.value = false
	}
}

const handleUpdate = async () => {
	fetchTableData()
	statusCnt.value = await getStatusCnt(
		omit(
			{
				...fetchParam.value
			},
			"status",
			"currentPage",
			"pageSize"
		) as any
	)
}
onMounted(() => {
	fetchParam.value.sord = "desc"
	fetchParam.value.sidx = "createdDate"

	getDictByCodeList(["WASTE_MATERIALS_TYPE", "DISPOSAL_TYPE"])
	getTableData()
})

/**
 * 排序
 * @param column
 * @param prop 字段名称
 * @param order ascending 升序 descending 降序
 */
function handleSortChange(column: any, prop: any, order: any) {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		sord:
			order === "descending" ? "desc" : order === "ascending" ? "asc" : "desc",
		sidx: order ? prop : "createdDate" // 排序字段
	}

	fetchTableData()
}
</script>
<template>
	<div class="app-container">
		<div class="whole-frame">
			<ModelFrame class="top-frame">
				<Query
					:queryArrList="queryArrList"
					@getQueryData="getTableData"
					class="ml10"
				/>
			</ModelFrame>

			<ModelFrame class="bottom-frame">
				<Title
					:title="rightTitle"
					:button="(titleBtnConf as any)"
					@on-btn-click="handleClickAddBtn"
				>
					<div class="app-tabs-wrapper">
						<el-tabs v-model="activeName" @tab-click="handleTabsClick">
							<el-tab-pane
								v-for="(tab, index) in tabList"
								:key="index"
								:label="`${tab.name}（${tab.count}）`"
								:name="tab.name"
								:index="tab.name"
							/>
						</el-tabs>
					</div>
				</Title>

				<pitaya-table
					ref="tableRef"
					:columns="tableProp"
					:tableData="tableData"
					:total="pageTotal"
					:need-index="true"
					:need-selection="false"
					:single-select="false"
					@on-selection-change="selectedTableList = $event"
					@on-current-page-change="onCurrentPageChange"
					:table-loading="tableLoading"
					@on-table-sort-change="handleSortChange"
				>
					<!-- 预估回收总重量 recoveryWeight -->
					<template #recoveryWeight="{ rowData }">
						{{ toFixedTwo(rowData.recoveryWeight) }}
					</template>

					<!-- disposalType 处置方式 -->
					<template #disposalType="{ rowData }">
						{{
							dictFilter("DISPOSAL_TYPE", rowData.disposalType)?.subitemName ||
							"---"
						}}
					</template>

					<!-- 废旧物资分类 -->
					<template #materialType="{ rowData }">
						<dict-tag
							:options="DictApi.getWasteMaterialType()"
							:value="rowData.materialType"
						/>
					</template>

					<!-- 审批状态 -->
					<template #bpmStatus="{ rowData }">
						<dict-tag
							:options="DictApi.getBpmStatus()"
							:value="rowData.bpmStatus"
						/>
					</template>

					<!-- 处置状态 -->
					<template #status="{ rowData }">
						<dict-tag
							:options="getScrapDisposalStatus()"
							:value="rowData.status"
						/>
					</template>

					<template #operations="{ rowData }">
						<slot
							v-if="
								(tabStatus != scrapDisposalApplyStatus.underApproval &&
									tabStatus != scrapDisposalApplyStatus.isFinish &&
									isCheckPermission(powerList.wasteScrapDisposalBtnEdit) &&
									hasPermi(rowData.createdBy)) ||
								isCheckPermission(powerList.wasteScrapDisposalBtnPreview) ||
								(tabStatus == tabList[0].value &&
									isCheckPermission(powerList.wasteScrapDisposalBtnDrop) &&
									hasPermi(rowData.createdBy))
							"
						>
							<el-button
								v-btn
								link
								@click.stop="onRowEdit(rowData)"
								v-if="
									tabStatus != scrapDisposalApplyStatus.underApproval &&
									tabStatus != scrapDisposalApplyStatus.isFinish &&
									isCheckPermission(powerList.wasteScrapDisposalBtnEdit) &&
									hasPermi(rowData.createdBy)
								"
								:disabled="checkPermission(powerList.wasteScrapDisposalBtnEdit)"
							>
								<font-awesome-icon :icon="['fas', 'pen-to-square']" />
								<span class="table-inner-btn">编辑</span>
							</el-button>
							<el-button
								v-btn
								link
								@click.stop="onRowView(rowData)"
								v-if="isCheckPermission(powerList.wasteScrapDisposalBtnPreview)"
								:disabled="
									checkPermission(powerList.wasteScrapDisposalBtnPreview)
								"
							>
								<font-awesome-icon :icon="['fas', 'eye']" />
								<span class="table-inner-btn">查看</span>
							</el-button>
							<el-button
								v-btn
								link
								@click.stop="onRowDel(rowData)"
								v-if="
									tabStatus == tabList[0].value &&
									isCheckPermission(powerList.wasteScrapDisposalBtnDrop) &&
									hasPermi(rowData.createdBy)
								"
								:disabled="checkPermission(powerList.wasteScrapDisposalBtnDrop)"
							>
								<font-awesome-icon :icon="['fas', 'trash-can']" />
								<span class="table-inner-btn">移除</span>
							</el-button>
						</slot>
						<slot v-else>---</slot>
					</template>

					<!-- <template #footerOperateLeft>
						<button-list
							v-if="tbBtnConf.length > 0"
							class="footer"
							:is-not-radius="true"
							:button="tbBtnConf"
							:loading="tbBtnLoading"
							@on-btn-click="handleTbBtnClick"
						/>
					</template> -->
				</pitaya-table>

				<!-- 编辑/新建  -->
				<Drawer
					:size="modalSize.xxl"
					v-model:drawer="editorVisible"
					:destroyOnClose="true"
				>
					<scrap-disposal-apply-editor
						:id="curRowId"
						:mode="editorMode"
						:status="tabStatus"
						@close="editorVisible = false"
						@save="handleUpdate"
						@update="fetchTableData"
					/>
				</Drawer>

				<!-- 查看  -->
				<Drawer
					:size="modalSize.xxl"
					v-model:drawer="viewVisible"
					:destroyOnClose="true"
				>
					<scrap-disposal-apply-detail
						:id="curRowId"
						:mode="editorMode"
						:status="tabStatus"
						@close="viewVisible = false"
					/>
				</Drawer>

				<!-- 待询价报告 -->
				<Drawer
					:size="modalSize.sm"
					v-model:drawer="assessmentReportVisible"
					:destroyOnClose="true"
				>
					<assessment-report
						:id="first(selectedTableList)?.id"
						@close="assessmentReportVisible = false"
						@update="handleUpdate"
					/>
				</Drawer>

				<!-- 待处置报告 -->
				<Drawer
					:size="modalSize.sm"
					v-model:drawer="disposalReportVisible"
					:destroyOnClose="true"
				>
					<disposal-report
						:id="first(selectedTableList)?.id"
						@close="disposalReportVisible = false"
						@update="handleUpdate"
					/>
				</Drawer>

				<!-- 审批、查看弹窗 -->
				<Drawer
					:size="modalSize.xxl"
					v-model:drawer="approvedVisible"
					:destroyOnClose="true"
				>
					<approved-drawer
						:mod="editorMode"
						:task-value="editTask"
						@on-save-or-close="approvedVisible = false"
					/>
				</Drawer>
			</ModelFrame>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
