<!-- 废旧-报废处置申请 详情 -->
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<div class="drawer-column left" style="width: 310px">
			<Title :title="titleConf" />
			<el-scrollbar class="rows">
				<el-descriptions size="small" :column="1" border class="content">
					<template>
						<el-descriptions-item
							v-for="desc in descList"
							:key="desc.key"
							:label="desc.label"
						>
							<!-- 报废状态 -->
							<span v-if="desc.key === 'status'">
								<dict-tag
									:options="getScrapDisposalStatus()"
									:value="formData.status"
								/>
							</span>

							<span v-else-if="desc.key === 'materialType'">
								<dict-tag
									:options="DictApi.getWasteMaterialType()"
									:value="formData.materialType"
								/>
							</span>
							<span v-else-if="desc.key === 'recoveryWeight'">
								{{ toFixedTwo(formData.recoveryWeight) }}
							</span>

							<span v-else-if="desc.needTooltip">
								<el-tooltip
									effect="dark"
									:content="formData?.[desc.key]"
									:disabled="
										getRealLength(formData?.[desc.key]) <= 100 ? true : false
									"
								>
									{{
										getRealLength(formData?.[desc.key]) > 100
											? setString(formData?.[desc.key], 100)
											: formData?.[desc.key] || "---"
									}}
								</el-tooltip>
							</span>

							<span v-else>
								{{ getNumDefByNumKey(desc.key, formData?.[desc.key]) }}
							</span>
						</el-descriptions-item>
					</template>
				</el-descriptions>
			</el-scrollbar>
		</div>

		<div
			class="drawer-column right"
			style="width: calc(100% - 310px)"
			:class="{ pdr10: !footerBtnVisible }"
		>
			<Title :title="extTitleConf">
				<div class="app-tabs-wrapper" style="left: 110px">
					<el-tabs v-model="activeName" @tab-click="handleTabChanged">
						<el-tab-pane
							v-for="(tab, index) in tabsConf"
							:key="index"
							:label="tab.name"
							:name="tab.name"
							:index="tab.name"
						/>
					</el-tabs>
				</div>
			</Title>

			<el-scrollbar class="rows">
				<pitaya-table
					v-if="activatedTab === 0"
					ref="tableRef"
					:columns="tableProp"
					:table-data="tableData"
					:need-selection="false"
					:single-select="false"
					:need-index="true"
					:need-pagination="true"
					:total="pageTotal"
					:table-loading="tableLoading"
					@on-selection-change="selectedTableList = $event"
					@on-current-page-change="onCurrentPageChange"
				>
					<!-- 预估回收重量 -->
					<template #recoveryWeight="{ rowData }">
						{{ toFixedTwo(rowData.recoveryWeight) }}
					</template>

					<!-- 废旧物资分类 -->
					<template #wasteMaterialType="{ rowData }">
						<dict-tag
							:options="DictApi.getWasteMaterialType()"
							:value="rowData.wasteMaterialType"
						/>
					</template>

					<template #actions="{ rowData }">
						<el-button v-btn link @click="viewScrapApply(rowData)">
							<font-awesome-icon
								:icon="['fas', 'eye']"
								style="color: var(--pitaya-btn-background)"
							/>
							<span class="table-inner-btn">查看</span>
						</el-button>
					</template>
				</pitaya-table>
				<scrapDisposalMatTable
					v-else-if="activatedTab === 1"
					:id="formData.id!"
				/>
				<scrap-disposal-store-table
					v-else-if="activatedTab === 2"
					:id="formData.id!"
				/>
				<assessment-report-table
					v-else-if="activatedTab === 3"
					:id="props.id || formData.id!"
					:status="formData.status"
					:mode="props.mode"
					:info="formData"
				/>
				<disposal-report-table
					v-else-if="activatedTab === 4"
					:id="props.id || formData.id!"
					:status="formData.status"
					:mode="props.mode"
				/>
				<table-file
					v-else
					:business-type="fileBusinessType.wasteScrapDisposalApply"
					:business-id="props.id || formData.id!"
					:mod="IModalType.view"
				/>
			</el-scrollbar>
			<button-list
				v-if="footerBtnVisible"
				class="footer"
				:button="submitBtnConf"
				:loading="drawerBtnLoading"
				@on-btn-click="emit('close')"
			/>
		</div>
	</div>

	<!-- 查看报废申请  -->
	<Drawer
		v-model:drawer="scrapApplyDetailVisble"
		:size="modalSize.xl"
		destroy-on-close
	>
		<scrap-apply-detail
			:id="editScrapApplyRow.id"
			@close="scrapApplyDetailVisble = false"
		/>
	</Drawer>
</template>

<script setup lang="ts">
import { IModalType } from "@/app/baseline/utils/types/common"
import ButtonList from "@/compontents/ButtonList.vue"
import dictTag from "../../components/dictTag.vue"

import { modalSize } from "@/app/baseline/utils/layout-config"
import { fileBusinessType } from "@/app/baseline/api/dict"
import { getModalTypeLabel } from "@/app/baseline/utils"

import { useDictInit } from "../../components/dictBase"

import TableFile from "../../components/tableFile.vue"
import { useTbInit } from "../../components/tableBase"
import { MatWasteScrapApplyVo } from "@/app/baseline/utils/types/waste-scrap-apply"
import { DictApi } from "@/app/baseline/api/dict"

import scrapDisposalStoreTable from "./scrapDisposalStoreTable.vue"
import scrapDisposalMatTable from "./scrapDisposalMatTable.vue"
import scrapApplyDetail from "../scrapApply/scrapApplyDetail.vue"
import assessmentReportTable from "./assessmentReportTable.vue"
import disposalReportTable from "./disposalReportTable.vue"
import {
	listDisposalApplyDetail,
	listScrapApplyByDisposalApplyIdPaged
} from "@/app/baseline/api/waste/scrap-disposal-apply"
import { MatWasteScrapDisposalApplyItemVoQuery } from "@/app/baseline/utils/types/waste-scrap-disposal-apply"
import { getNumDefByNumKey, toFixedTwo } from "@/app/baseline/utils"
import {
	getScrapDisposalStatus,
	scrapDisposalApplyStatus
} from "./scrapDisposalApply"
import { getRealLength, setString } from "@/app/baseline/utils/validate"

const props = withDefaults(
	defineProps<{
		/**
		 * 报废申请id
		 */
		id: any
		/**
		 * 详情类型 编辑/查看/新建
		 */
		mode?: IModalType
		footerBtnVisible?: boolean

		/**
		 * 报废 处置状态
		 */
		status: any
	}>(),
	{
		mode: IModalType.view,
		footerBtnVisible: true
	}
)
const emit = defineEmits<{
	(e: "close", msg?: string): void
	(e: "update"): void
}>()

const drawerBtnLoading = ref(false)

/**
 * 关联业务单数据
 */
const descList = ref([
	{ label: "处置单号", key: "code" },
	{ label: "处置名称", key: "label" },
	{ label: "处置方式", key: "disposalType_view" },
	{ label: "处置决策依据", key: "decisionBasis", needTooltip: true },
	{ label: "备注说明", key: "remark", needTooltip: true },
	{ label: "报废状态", key: "status" },
	{ label: "废旧物资分类", key: "materialType" },
	{ label: "预估回收总重量(kg)", key: "recoveryWeight" },
	{ label: "申请人", key: "createdBy_view" },
	{ label: "申请时间", key: "createdDate" }
])

/**
 * 标签页配置
 */
const tabsConf = computed(() => {
	const ls = [
		{
			name: "报废申请",
			value: 0
		},
		{
			name: "物资明细",
			value: 1
		},
		{
			name: "仓库信息",
			value: 2
		},
		{
			name: "报废询价",
			value: 3
		},
		{
			name: "处置结果",
			value: 4
		},
		{
			name: "相关附件",
			value: 5
		}
	]

	if (
		[
			scrapDisposalApplyStatus.underApproval,
			scrapDisposalApplyStatus.pendingApproval
		].includes(props.status)
	) {
		return ls.filter((item) => item.value !== 4 && item.value !== 3)
	} else if (props.status === scrapDisposalApplyStatus.unEvaluated) {
		return ls.filter((item) => item.value !== 4)
	} else {
		return ls
	}
})

/**
 * 当前激活的标签页位置
 */
const activatedTab = ref(tabsConf.value[0].value)
const activeName = ref<string>(tabsConf.value[0].name)
function handleTabChanged(tab: any) {
	activeName.value = tabsConf.value[tab.index].name
	activatedTab.value = tabsConf.value[tab.index].value

	if (tab.index == 0) {
		pageSize.value = 20
		currentPage.value = 1
		fetchTableData()
	}
}

const { getDictByCodeList } = useDictInit()

/**
 * 提交审核按钮配置
 */
const submitBtnConf = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"],
		disabled: false
	}
]

const drawerLoading = ref(false)

const titleConf = computed(() => ({
	name: [getModalTypeLabel(props.mode, "处置申请")],
	icon: ["fas", "square-share-nodes"]
}))

const extTitleConf = {
	name: ["扩展信息"],
	icon: ["fas", "square-share-nodes"]
}

const formData = ref<MatWasteScrapApplyVo>({})

const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	pageSize,
	currentPage,
	fetchParam,
	selectedTableList,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = useTbInit<MatWasteScrapApplyVo, MatWasteScrapDisposalApplyItemVoQuery>()

fetchFunc.value = listScrapApplyByDisposalApplyIdPaged

tableProp.value = [
	{
		prop: "code",
		label: "报废申请单号",
		width: 180,
		fixed: "left"
	},
	{ prop: "label", label: "报废申请名称", minWidth: 120 },
	{
		prop: "wasteMaterialType",
		label: "废旧物资分类",
		needSlot: true,
		width: 120
	},
	{
		prop: "recoveryWeight",
		label: "预估回收总重量(KG)",
		width: 150,
		needSlot: true,
		align: "right"
	},
	{ prop: "materialTypeCnt", label: "物资分类" },
	{ prop: "materialCodeCnt", label: "物资编码" },
	{ prop: "qualityCnt", label: "主要材质" },
	{ prop: "sysCommunityId_view", label: "所属公司" },
	{ prop: "sysOrgId_view", label: "申请部门" },
	{
		prop: "actions",
		label: "操作",
		width: 150,
		needSlot: true,
		fixed: "right"
	}
]

onMounted(async () => {
	getDictByCodeList([
		"INVENTORY_UNIT",
		"WASTE_MATERIALS_TYPE",
		"MAIN_MATERIALS",
		"DISPOSAL_TYPE"
	])

	fetchParam.value.sord = "desc"
	fetchParam.value.sidx = "createdDate"

	if (props.id) {
		fetchParam.value.id = props.id

		getDetail()
		fetchTableData()

		if (props.status === scrapDisposalApplyStatus.unEvaluated) {
			activatedTab.value = tabsConf.value[3].value
			activeName.value = tabsConf.value[3].name
		} else if (props.status === scrapDisposalApplyStatus.unDisposed) {
			activatedTab.value = tabsConf.value[4].value
			activeName.value = tabsConf.value[4].name
		}
	}
})

/**
 * 获取报废处置详情
 */
function getDetail() {
	drawerLoading.value = true

	// 获取详情
	listDisposalApplyDetail(props.id)
		.then((r = {}) => {
			formData.value = { ...r }
		})
		.finally(() => (drawerLoading.value = false))
}

/**
 * 查看报废申请 操作
 */
const scrapApplyDetailVisble = ref(false)
const editScrapApplyRow = ref<MatWasteScrapApplyVo>({})
function viewScrapApply(e: MatWasteScrapApplyVo) {
	editScrapApplyRow.value = e
	scrapApplyDetailVisble.value = true
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
