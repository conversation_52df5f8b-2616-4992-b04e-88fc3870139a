<!-- 物资选择器 -->
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<div class="drawer-column" style="width: 100%">
			<Title :title="titleConf" />

			<div class="rows mat-selector-table">
				<el-scrollbar>
					<pitaya-table
						ref="tableRef"
						:columns="tableProp"
						:table-data="tableData"
						:need-selection="true"
						:single-select="!multiple"
						:need-index="true"
						:need-pagination="true"
						:total="pageTotal"
						:table-loading="tableLoading"
						@on-selection-change="selectedTableList = $event"
						@on-current-page-change="onCurrentPageChange"
					>
						<!-- 预估回收重量 -->
						<template #recoveryWeight="{ rowData }">
							{{ toFixedTwo(rowData.recoveryWeight) }}
						</template>

						<!-- 废旧物资分类 -->
						<template #wasteMaterialType="{ rowData }">
							<dict-tag
								:options="DictApi.getWasteMaterialType()"
								:value="rowData.wasteMaterialType"
							/>
						</template>
					</pitaya-table>
				</el-scrollbar>
			</div>

			<button-list
				class="footer"
				:button="btnConf"
				@on-btn-click="handleBtnClick"
			/>
		</div>
	</div>
</template>

<script setup lang="ts">
import { useTbInit } from "../../components/tableBase"
import dictTag from "../../components/dictTag.vue"
import { DictApi } from "@/app/baseline/api/dict"
import { toFixedTwo } from "@/app/baseline/utils"

const emit = defineEmits<{
	(e: "close"): void
	(e: "save", v: any[]): void
}>()

const props = withDefaults(
	defineProps<{
		/**
		 * table 数据源
		 */
		tableApi: (params: any) => Promise<any>

		/**
		 * table 的列配置
		 */
		columns?: TableColumnType[]
		/**
		 * 是否支持多选
		 */
		multiple?: boolean

		/**
		 * table 请求参数
		 */
		tableReqParams?: any
	}>(),
	{
		multiple: false,
		tableReqParams: () => ({}),
		columns: () => []
	}
)

const titleConf = {
	name: ["关联报废申请"],
	icon: ["fas", "square-share-nodes"]
}

const drawerLoading = ref(false)

const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = useTbInit()

tableProp.value = props.columns?.length
	? props.columns
	: [
			{
				prop: "code",
				label: "报废申请单号",
				width: 180,
				fixed: "left"
			},
			{
				prop: "label",
				label: "报废申请名称"
			},
			{
				prop: "wasteMaterialType",
				label: "废旧物资分类",
				needSlot: true
			},
			{
				prop: "recoveryWeight",
				label: "预估回收总重量（Kg)",
				width: 150,
				align: "right",
				needSlot: true
			},
			{
				prop: "materialTypeCnt",
				label: "物资分类"
			},
			{
				prop: "materialCodeCnt",
				label: "物资编码"
			},
			{
				prop: "qualityCnt",
				label: "主要材质"
			},
			{
				prop: "sysCommunityId_view",
				label: "所属公司"
			},
			{
				prop: "sysOrgId_view",
				label: "申请部门"
			}
	  ]

const btnConf = computed(() => {
	return [
		{
			name: "取消",
			icon: ["fas", "circle-minus"]
		},
		{
			name: "保存",
			icon: ["fas", "floppy-disk"],
			disabled: selectedTableList.value.length > 0 ? false : true
		}
	]
})

onMounted(async () => {
	fetchParam.value = { ...fetchParam.value, ...props.tableReqParams }
	fetchFunc.value = props.tableApi
	fetchTableData()
})

function handleBtnClick(name?: string) {
	if (name === "取消") {
		return emit("close")
	}

	emit("save", selectedTableList.value)
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.mat-selector-table {
	&:deep(.pitaya-table) {
		.error {
			.column-inner-item,
			.cell,
			.el-input__inner {
				color: red;
			}
		}
	}
}
</style>
