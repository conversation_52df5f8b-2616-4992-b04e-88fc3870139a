<!-- 报废询价报告  -->
<template>
	<pitaya-table
		ref="tableRef"
		:columns="tableProp"
		:table-data="tableData"
		:need-index="true"
		:need-pagination="true"
		:total="pageTotal"
		:table-loading="tableLoading"
		@on-selection-change="selectedTableList = $event"
		@on-current-page-change="onCurrentPageChange"
	>
		<!-- 询价金额 -->
		<template #amount="{ rowData }">
			<cost-tag :value="rowData.amount" />
		</template>

		<template #decisionBasis>
			{{ props.info?.decisionBasis }}
		</template>

		<template #operations="{ rowData }">
			<slot>
				<el-button v-btn link @click="downloadFile(rowData)">
					<font-awesome-icon :icon="['fas', 'file-arrow-down']" />
					<span class="table-inner-btn">下载</span>
				</el-button>
				<el-button
					v-btn
					link
					@click="delAssessmentReport(rowData)"
					v-if="isEdit"
				>
					<font-awesome-icon :icon="['fas', 'trash-can']" />
					<span class="table-inner-btn">移除</span>
				</el-button>
			</slot>
		</template>

		<template #footerOperateLeft>
			<button-list
				v-if="isEdit"
				:button="tbBtnConf"
				:is-not-radius="true"
				@on-btn-click="handleTbBtnClick"
			/>
		</template>
	</pitaya-table>

	<Drawer
		:size="modalSize.sm"
		v-model:drawer="assessmentReportVisible"
		:destroyOnClose="true"
	>
		<assessment-report
			:id="props.id"
			@close="assessmentReportVisible = false"
			@update="
				() => {
					emit('updateMain')
					fetchTableData()
				}
			"
		/>
	</Drawer>
</template>

<script setup lang="ts">
import { useTbInit } from "../../components/tableBase"
import { modalSize } from "@/app/baseline/utils/layout-config"
import {
	delScrapDisposalReport,
	listScrapDisposalReportPaged
} from "@/app/baseline/api/waste/scrap-disposal-apply"
import {
	MatWasteScrapDisposalApplyItemVoQuery,
	MatWasteScrapDisposalReportVo
} from "@/app/baseline/utils/types/waste-scrap-disposal-apply"
import costTag from "../../components/costTag.vue"
import assessmentReport from "./assessmentReport.vue"
import { FileApi } from "@/app/baseline/api/file"
import { first } from "lodash-es"
import { scrapDisposalApplyStatus } from "./scrapDisposalApply"
import { IModalType } from "@/app/baseline/utils/types/common"
import { MatWasteScrapApplyVo } from "@/app/baseline/utils/types/waste-scrap-apply"

const props = withDefaults(
	defineProps<{
		/**
		 * 报废处置申请id
		 */
		id: any
		status: any
		mode?: IModalType
		info?: MatWasteScrapApplyVo
	}>(),
	{
		mode: IModalType.edit
	}
)
const emit = defineEmits<{
	(e: "updateMain"): void
}>()

const assessmentReportVisible = ref(false)
const tbBtnConf = [
	{
		name: "添加报废询价报告",
		icon: ["fas", "circle-plus"]
	}
]

const isEdit = computed(() => {
	return props.status === scrapDisposalApplyStatus.unEvaluated &&
		props.mode == IModalType.edit
		? true
		: false
})
function handleTbBtnClick() {
	assessmentReportVisible.value = true
}
const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	fetchParam,
	selectedTableList,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = useTbInit<
	MatWasteScrapDisposalReportVo,
	MatWasteScrapDisposalApplyItemVoQuery
>()

fetchFunc.value = listScrapDisposalReportPaged

tableProp.value = [
	{
		prop: "orgLabel",
		label: "报废询价机构"
	},
	{
		prop: "reportCode",
		label: "报废询价报告书编号"
	},
	{
		prop: "amount",
		label: "询价金额(元)",
		needSlot: true,
		align: "right"
	},
	{
		prop: "decisionBasis",
		label: "处置决策依据",
		needSlot: true
	},
	{
		prop: "createdBy_view",
		label: "更新人"
	},
	{
		prop: "createdDate",
		label: "更新时间"
	},
	{
		label: "操作",
		width: 150,
		prop: "operations",
		needSlot: true,
		fixed: "right"
	}
]

/**
 * 删除 操作
 * @param e
 */
async function delAssessmentReport(e: MatWasteScrapDisposalReportVo) {
	await delScrapDisposalReport(e.id as number)
	ElMessage.success("操作成功")
	fetchTableData()
	emit("updateMain")
}

/**
 * 下载附件 操作
 * @param e
 */
function downloadFile(e: MatWasteScrapDisposalReportVo) {
	FileApi.downloadFile(first(e.attachmentList)?.filePath as any)
}
onMounted(() => {
	fetchParam.value.id = props.id
	fetchParam.value.sord = "desc"
	fetchParam.value.sidx = "createdDate"
	fetchTableData()
})

const tableFileList = computed(() => {
	return tableData.value || []
})
defineExpose({ tableFileList })
</script>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
