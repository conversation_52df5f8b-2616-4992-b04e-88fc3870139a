/**
 * 报废处置 主页 状态
 * 报废处置状态:0草稿 1审批中 2待询价 3待处置 4已完成
 */
export enum scrapDisposalApplyStatus {
	/**
	 * 待提交
	 */
	"pendingApproval" = "0",

	/**
	 * 审批中
	 */
	"underApproval" = "1",

	/**
	 * 待询价
	 */
	"unEvaluated" = "2",

	/**
	 * 待处置
	 */
	"unDisposed" = "3",

	/**
	 * 已完成
	 */
	"isFinish" = "4"
}

export function getScrapDisposalStatus() {
	return [
		{
			label: "待询价",
			value: scrapDisposalApplyStatus.pendingApproval,
			raw: { class: "warning" }
		},
		{
			label: "待询价",
			value: scrapDisposalApplyStatus.underApproval,
			raw: { class: "warning" }
		},
		{
			label: "待询价",
			value: scrapDisposalApplyStatus.unEvaluated,
			raw: { class: "warning" }
		},
		{
			label: "待处置",
			value: scrapDisposalApplyStatus.unDisposed,
			raw: { class: "danger" }
		},
		{
			label: "已完成",
			value: scrapDisposalApplyStatus.isFinish,
			raw: { class: "primary" }
		}
	]
}
