<!-- 废旧-报废处置申请 编辑 -->
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<div class="drawer-column left" style="width: 310px">
			<Title :title="titleConf" />
			<el-scrollbar class="rows" v-if="isSubmitCheck">
				<el-form
					class="content"
					:model="formData"
					:rules="formRules"
					ref="formRef"
					label-position="top"
					label-width="100px"
				>
					<form-element :form-element="formEls" :form-data="formData" />
				</el-form>
			</el-scrollbar>

			<button-list
				class="footer"
				:loading="drawerBtnLoading"
				:button="footerBtnConf"
				@on-btn-click="handleDrawerBtnClick"
				v-if="isSubmitCheck"
			/>
			<el-scrollbar class="rows" v-else>
				<el-descriptions size="small" :column="1" border class="content">
					<template>
						<el-descriptions-item
							v-for="desc in descList"
							:key="desc.key"
							:label="desc.label"
						>
							<span v-if="desc.needTooltip">
								<el-tooltip
									effect="dark"
									:content="formData?.[desc.key]"
									:disabled="
										getRealLength(formData?.[desc.key]) <= 100 ? true : false
									"
								>
									{{
										getRealLength(formData?.[desc.key]) > 100
											? setString(formData?.[desc.key], 100)
											: formData?.[desc.key] || "---"
									}}
								</el-tooltip>
							</span>

							<span v-else>
								{{ getNumDefByNumKey(desc.key, formData?.[desc.key]) }}
							</span>
						</el-descriptions-item>
					</template>
				</el-descriptions>
			</el-scrollbar>
		</div>

		<div
			class="drawer-column right"
			style="width: calc(100% - 310px)"
			:class="!canEditExtra ? 'disabled' : ''"
		>
			<Title :title="extTitleConf">
				<div class="app-tabs-wrapper" style="left: 110px">
					<el-tabs v-model="activeName" @tab-click="handleTabChanged">
						<el-tab-pane
							v-for="(tab, index) in tabsConf"
							:key="index"
							:label="tab.name"
							:name="tab.name"
							:index="tab.name"
						/>
					</el-tabs>
				</div>
			</Title>

			<el-scrollbar class="rows scrap-disposal-apply-editor-table-wrapper">
				<pitaya-table
					v-if="activatedTab === 0"
					ref="tableRef"
					:columns="tableProp"
					:table-data="tableData"
					:need-selection="false"
					:single-select="false"
					:need-index="true"
					:need-pagination="true"
					:total="pageTotal"
					:table-loading="tableLoading"
					:cell-class-name="tbCellClassName"
					@on-selection-change="selectedTableList = $event"
					@on-current-page-change="onCurrentPageChange"
				>
					<!-- 预估回收总重量 -->
					<template #recoveryWeight="{ rowData }">
						{{ toFixedTwo(rowData.recoveryWeight) }}
					</template>

					<!-- 废旧物资分类 -->
					<template #wasteMaterialType="{ rowData }">
						<dict-tag
							:options="DictApi.getWasteMaterialType()"
							:value="rowData.wasteMaterialType"
						/>
					</template>

					<template #actions="{ rowData }">
						<el-button v-btn link @click="viewScrapApply(rowData)">
							<font-awesome-icon
								:icon="['fas', 'eye']"
								style="color: var(--pitaya-btn-background)"
							/>
							<span class="table-inner-btn">查看</span>
						</el-button>

						<el-button
							v-btn
							link
							@click="delScrapApply(rowData)"
							v-if="props.status == scrapDisposalApplyStatus.pendingApproval"
						>
							<font-awesome-icon
								:icon="['fas', 'trash-can']"
								style="color: var(--pitaya-btn-background)"
							/>
							<span class="table-inner-btn">移除</span>
						</el-button>
					</template>

					<template #footerOperateLeft>
						<button-list
							:button="tbBtnConf"
							:is-not-radius="true"
							v-if="isSubmitCheck"
							@on-btn-click="scrapApplySelectorVisible = true"
						/>
					</template>
				</pitaya-table>
				<scrapDisposalMatTable
					v-else-if="activatedTab === 1"
					:id="formData.id!"
				/>
				<scrap-disposal-store-table
					v-else-if="activatedTab === 2"
					:id="formData.id!"
				/>
				<assessment-report-table
					v-else-if="activatedTab === 3"
					ref="assessmentReportTableRef"
					:id="props.id || formData.id!"
					:status="formData.status"
					:info="formData"
					@updateMain="emit('update')"
				/>
				<disposal-report-table
					v-else-if="activatedTab === 4"
					ref="disposalReportTableRef"
					:id="props.id || formData.id!"
					:status="formData.status"
					@updateMain="emit('update')"
				/>
				<table-file
					v-else
					:business-type="fileBusinessType.wasteScrapDisposalApply"
					:business-id="props.id || formData.id!"
					:mod="IModalType.edit"
				/>
			</el-scrollbar>
			<button-list
				class="footer"
				:button="submitBtnConf"
				:loading="drawerBtnLoading"
				@on-btn-click="handleSubmit"
			/>
		</div>
	</div>

	<!-- 物资选择器 -->
	<Drawer
		v-model:drawer="scrapApplySelectorVisible"
		:size="modalSize.lg"
		destroy-on-close
	>
		<scrap-apply-selector
			:table-req-params="{
				id: formData.id,
				sord: 'desc',
				sidx: 'createdDate'
			}"
			:table-api="listScrapApplyByDisposalApplyIdOthersPaged"
			:multiple="true"
			@save="handleAddScrapApply"
			@close="scrapApplySelectorVisible = false"
		/>
	</Drawer>

	<!-- 查看报废申请  -->
	<Drawer
		v-model:drawer="scrapApplyDetailVisble"
		:size="modalSize.xl"
		destroy-on-close
	>
		<scrap-apply-detail
			:id="editScrapApplyRow.id"
			@close="scrapApplyDetailVisble = false"
		/>
	</Drawer>
</template>

<script setup lang="ts">
import {
	IIdempotentTokenType,
	IIdempotentTokenTypePre,
	IModalType
} from "@/app/baseline/utils/types/common"
import formElement from "../../components/formElement.vue"
import { useMessageBoxInit } from "../../components/messageBox"
import ButtonList from "@/compontents/ButtonList.vue"
import dictTag from "../../components/dictTag.vue"

import { inputMaxLength, modalSize } from "@/app/baseline/utils/layout-config"
import { fileBusinessType } from "@/app/baseline/api/dict"
import { getModalTypeLabel } from "@/app/baseline/utils"
import { FormElementType } from "../../components/define"

import { map } from "lodash-es"
import { FormInstance, FormItemRule } from "element-plus"
import { useDictInit } from "../../components/dictBase"

import scrapApplySelector from "./scrapApplySelector.vue"
import TableFile from "../../components/tableFile.vue"
import {
	getIdempotentToken,
	requiredValidator
} from "@/app/baseline/utils/validate"
import { useTbInit } from "../../components/tableBase"
import { MatWasteScrapApplyVo } from "@/app/baseline/utils/types/waste-scrap-apply"
import { DictApi } from "@/app/baseline/api/dict"

import scrapDisposalStoreTable from "./scrapDisposalStoreTable.vue"
import scrapDisposalMatTable from "./scrapDisposalMatTable.vue"
import scrapApplyDetail from "../scrapApply/scrapApplyDetail.vue"
import assessmentReportTable from "./assessmentReportTable.vue"
import disposalReportTable from "./disposalReportTable.vue"
import {
	addDisposalApply,
	delScrapApplyByDisposalApplyIdPaged,
	listDisposalApplyDetail,
	listScrapApplyByDisposalApplyIdPaged,
	updateDisposalApply,
	listScrapApplyByDisposalApplyIdOthersPaged,
	addBatchAddDisposalApplyItem,
	pubishDisposalApply,
	submitScrapDisposalReport,
	submitScrapDisposalResult
} from "@/app/baseline/api/waste/scrap-disposal-apply"
import { MatWasteScrapDisposalApplyItemVoQuery } from "@/app/baseline/utils/types/waste-scrap-disposal-apply"
import { scrapDisposalApplyStatus } from "./scrapDisposalApply"
import { appStatus } from "@/app/baseline/api/dict"
import { getNumDefByNumKey, toFixedTwo } from "@/app/baseline/utils"
import { includes } from "lodash-es"
import { getRealLength, setString } from "@/app/baseline/utils/validate"

const props = withDefaults(
	defineProps<{
		/**
		 * 报废申请id
		 */
		id: any
		/**
		 * 详情类型 编辑/查看/新建
		 */
		mode?: IModalType
		/**
		 * 报废 处置状态
		 */
		status: any
	}>(),
	{
		mode: IModalType.create
	}
)
const emit = defineEmits<{
	(e: "close", msg?: string): void
	(e: "update"): void
	(e: "save"): void
}>()

const { showDelConfirm, showWarnConfirm, showErrorConfirm } =
	useMessageBoxInit()

const assessmentReportTableRef = ref()
const disposalReportTableRef = ref()

/**
 * 保存旧的表单数据
 */
const oldFormData = ref<string>()

const formRef = ref<FormInstance>()

const canEditExtra = computed(() => Boolean(formData.value?.id || props.id))

const drawerBtnLoading = ref(false)

const scrapApplySelectorVisible = ref(false)
/**
 * 关联业务单数据
 */
const descList = ref([
	{ label: "处置单号", key: "code" },
	{ label: "处置名称", key: "label" },
	{ label: "处置方式", key: "disposalType_view" },
	{ label: "处置决策依据", key: "decisionBasis", needTooltip: true },
	{ label: "备注说明", key: "remark", needTooltip: true }
])

/**
 * 标签页配置
 */
const tabsConf = computed(() => {
	const ls = [
		{
			name: "报废申请",
			value: 0
		},
		{
			name: "物资明细",
			value: 1
		},
		{
			name: "仓库信息",
			value: 2
		},
		{
			name: "报废询价",
			value: 3
		},
		{
			name: "处置结果",
			value: 4
		},
		{
			name: "相关附件",
			value: 5
		}
	]

	if (
		[
			scrapDisposalApplyStatus.underApproval,
			scrapDisposalApplyStatus.pendingApproval
		].includes(props.status)
	) {
		return ls.filter((item) => item.value !== 4 && item.value !== 3)
	} else if (props.status === scrapDisposalApplyStatus.unEvaluated) {
		return ls.filter((item) => item.value !== 4)
	} else {
		return ls
	}
})

/**
 * 当前激活的标签页位置
 */
const activatedTab = ref(tabsConf.value[0].value)
const activeName = ref<string>(tabsConf.value[0].name)
function handleTabChanged(tab: any) {
	activeName.value = tabsConf.value[tab.index].name
	activatedTab.value = tabsConf.value[tab.index].value

	if (tab.index == 0) {
		pageSize.value = 20
		getTableData()
	}
}

const { dictOptions, getDictByCodeList } = useDictInit()

/**
 *  btmStatus 待审批前
 */
const isSubmitCheck = computed(() => {
	return formData.value.bpmStatus == appStatus.pendingApproval ||
		formData.value.bpmStatus == appStatus.rejected ||
		props.mode === IModalType.create
		? true
		: false
})

/**
 * 待询价
 */
const isScrapDisposalReport = computed(() => {
	return formData.value.bpmStatus == appStatus.approved &&
		formData.value.status == scrapDisposalApplyStatus.unEvaluated
		? true
		: false
})

/**
 * 待处置
 */
const isScrapDisposalResult = computed(() => {
	return formData.value.bpmStatus == appStatus.approved &&
		formData.value.status == scrapDisposalApplyStatus.unDisposed
		? true
		: false
})

/**
 * 提交审核按钮配置
 */
const submitBtnConf = computed(() => {
	if (isSubmitCheck.value) {
		return [
			{
				name: "提交审核",
				icon: ["fas", "circle-check"],
				disabled: tableData.value.length < 1
			}
		]
	} else if (isScrapDisposalReport.value || isScrapDisposalResult.value) {
		return [
			{
				name: "取消",
				icon: ["fas", "circle-minus"],
				disabled: false
			},
			{
				name: "确认提交",
				icon: ["fas", "circle-check"],
				disabled: false
			}
		]
	} else {
		return [
			{
				name: "取消",
				icon: ["fas", "circle-minus"],
				disabled: false
			}
		]
	}
})

const drawerLoading = ref(false)

const titleConf = computed(() => ({
	name: [
		getModalTypeLabel(
			canEditExtra.value ? IModalType.edit : IModalType.create,
			"处置申请"
		)
	],
	icon: ["fas", "square-share-nodes"]
}))

const extTitleConf = {
	name: ["扩展信息"],
	icon: ["fas", "square-share-nodes"]
}

const formData = ref<MatWasteScrapApplyVo>({})

const formRules: Record<string, FormItemRule> = {
	label: requiredValidator("处置申请名称"),
	disposalType: requiredValidator("处置方式"),
	materialType: requiredValidator("废旧物资分类"),
	decisionBasis: requiredValidator("处置决策依据")
}

const {
	currentPage,
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	pageSize,
	fetchParam,
	selectedTableList,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = useTbInit<MatWasteScrapApplyVo, MatWasteScrapDisposalApplyItemVoQuery>()

fetchFunc.value = listScrapApplyByDisposalApplyIdPaged

tableProp.value = [
	{
		prop: "code",
		label: "报废申请单号",
		width: 180,
		fixed: "left"
	},
	{ prop: "label", label: "报废申请名称", minWidth: 120 },
	{
		prop: "wasteMaterialType",
		label: "废旧物资分类",
		needSlot: true,
		width: 120
	},
	{
		prop: "recoveryWeight",
		label: "预估回收总重量(KG)",
		width: 150,
		needSlot: true,
		align: "right"
	},
	{ prop: "materialTypeCnt", label: "物资分类" },
	{ prop: "materialCodeCnt", label: "物资编码" },
	{ prop: "qualityCnt", label: "主要材质" },
	{ prop: "sysCommunityId_view", label: "所属公司" },
	{ prop: "sysOrgId_view", label: "申请部门" },
	{
		prop: "actions",
		label: "操作",
		width: 150,
		needSlot: true,
		fixed: "right"
	}
]

const formEls = computed<FormElementType[][]>(() => [
	[
		{
			label: "处置申请名称",
			name: "label",
			maxlength: inputMaxLength.input
		},
		{
			label: "处置方式",
			name: "disposalType",
			type: "select",
			data: dictOptions.value.DISPOSAL_TYPE,
			disabled: canEditExtra.value,
			clear: false
		},
		{
			label: "废旧物资分类",
			name: "materialType",
			type: "select",
			data: dictOptions.value.WASTE_MATERIALS_TYPE,
			disabled: canEditExtra.value,
			clear: false
		},
		{
			label: "处置决策依据",
			name: "decisionBasis",
			type: "textarea",
			disabled: false,
			clear: false,
			maxlength: inputMaxLength.textarea
		},
		{
			label: "备注说明",
			name: "remark",
			type: "textarea",
			disabled: false,
			clear: false,
			maxlength: inputMaxLength.textarea
		}
	]
])

const tbBtnConf = [
	{
		name: "关联报废申请",
		icon: ["fas", "circle-plus"]
	}
]
const footerBtnConf = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "保存草稿",
		icon: ["fas", "floppy-disk"]
	}
]

/**
 * 提交审核
 *
 * 物资明细 table 行样式
 *
 * 库存异常物资，用红色标记该行
 */
const errorGoodsIdList = ref<number[]>([])
function tbCellClassName({ row }: any) {
	return includes(errorGoodsIdList.value, row.id) ? "error" : ""
}

onMounted(async () => {
	getDictByCodeList([
		"INVENTORY_UNIT",
		"WASTE_MATERIALS_TYPE",
		"MAIN_MATERIALS",
		"DISPOSAL_TYPE"
	])

	fetchParam.value.sord = "desc"
	fetchParam.value.sidx = "createdDate"

	if (props.id) {
		fetchParam.value.id = props.id

		/**
		 * 根据报废处置状态 显示不同的 tab明细
		 */
		getDetail()

		await fetchTableData()

		if (props.status === scrapDisposalApplyStatus.unEvaluated) {
			activatedTab.value = tabsConf.value[3].value
			activeName.value = tabsConf.value[3].name
		} else if (props.status === scrapDisposalApplyStatus.unDisposed) {
			activatedTab.value = tabsConf.value[4].value
			activeName.value = tabsConf.value[4].name
		}
		/* else {
			activatedTab.value = tabsConf.value[0].value
			activeName.value = tabsConf.value[0].name
			fetchTableData()
		} */
	}
})

const getTableData = () => {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value
	}
	fetchTableData()
}

/**
 * 保存草稿 handler
 */
async function handleSaveDraft() {
	if (!formRef.value) {
		return
	}

	formRef.value?.validate(async (valid) => {
		if (!valid) {
			return
		}
		drawerBtnLoading.value = true

		const api = canEditExtra.value ? updateDisposalApply : addDisposalApply
		try {
			let idempotentToken = ""
			if (!canEditExtra.value) {
				idempotentToken = getIdempotentToken(
					IIdempotentTokenTypePre.apply,
					IIdempotentTokenType.wasteBFCZApply
				)
			}

			const r = await api(formData.value as any, idempotentToken)

			ElMessage.success("操作成功")
			formData.value.id = r?.id
			formData.value.code = r?.code

			oldFormData.value = JSON.stringify(formData.value)

			fetchParam.value.id = r?.id as any

			emit("save")

			fetchTableData()
		} finally {
			drawerBtnLoading.value = false
		}
	})
}

// 提交审核逻辑
async function handleSubmit(btnName?: string) {
	if (btnName === "提交审核" && isSubmitCheck.value) {
		if (!formRef.value) {
			return
		}

		formRef.value?.validate(async (valid) => {
			if (!valid) {
				return
			}

			await showWarnConfirm("请确认是否提交本次数据？")
			drawerBtnLoading.value = true
			drawerLoading.value = true
			try {
				// 如果主表有修改，则先更新主表数据
				if (oldFormData.value != JSON.stringify(formData.value)) {
					await updateDisposalApply(formData.value as any)
				}

				const idempotentToken = getIdempotentToken(
					IIdempotentTokenTypePre.other,
					IIdempotentTokenType.wasteBFCZApply,
					formData.value.id
				)

				const { code, msg, data } = await pubishDisposalApply(
					props.id || formData.value.id,
					idempotentToken
				)

				if (data && code != 200) {
					errorGoodsIdList.value = data || []
					ElMessage.error(msg)
					fetchTableData()
				} else {
					ElMessage.success("操作成功")
					emit("save")
					emit("close")
				}
			} finally {
				drawerBtnLoading.value = false
				drawerLoading.value = false
			}
		})
	} else if (btnName === "确认提交") {
		if (isScrapDisposalReport.value) {
			if (assessmentReportTableRef.value?.["tableFileList"].length < 1) {
				return ElMessage.warning("请添加报废询价报告!")
			}
		} else if (isScrapDisposalResult.value) {
			if (disposalReportTableRef.value?.["tableFileList"].length < 1) {
				return ElMessage.warning("请添加处置报告!")
			}
		}

		await showWarnConfirm("是否确认提交？")
		drawerBtnLoading.value = true
		drawerLoading.value = true

		try {
			const api = isScrapDisposalReport.value
				? submitScrapDisposalReport
				: submitScrapDisposalResult

			const idempotentToken = getIdempotentToken(
				IIdempotentTokenTypePre.other,
				IIdempotentTokenType.wasteBFCZApply,
				formData.value.id
			)
			const res = await api(props.id || formData.value.id, idempotentToken)
			if (res) {
				ElMessage.success("操作成功")
				emit("save")
				emit("close")
			} else {
				ElMessage.error("操作失败")
			}
		} finally {
			drawerBtnLoading.value = false
			drawerLoading.value = false
		}
	} else {
		emit("close")
	}
}

/**
 * 点击 drawer 按钮 handler
 */
function handleDrawerBtnClick(name?: string) {
	if (name === "保存草稿") {
		handleSaveDraft()
		//emit("update")
		return
	}

	emit("close")
}

/**
 * 获取报废处置详情
 */
function getDetail() {
	drawerLoading.value = true

	// 获取详情
	listDisposalApplyDetail(props.id)
		.then((r = {}) => {
			formData.value = { ...r }

			oldFormData.value = JSON.stringify(formData.value)
		})
		.finally(() => (drawerLoading.value = false))
}

/**
 * 添加物资 handler
 */
async function handleAddScrapApply(e?: any) {
	const idList = map(e, ({ id }) => id)

	const idempotentToken = getIdempotentToken(
		IIdempotentTokenTypePre.other,
		IIdempotentTokenType.wasteBFCZApply,
		formData.value.id
	)
	await addBatchAddDisposalApplyItem(
		{
			disposalApplyId: props.id || formData.value.id,
			scrapIdList: idList || []
		},
		idempotentToken
	)

	ElMessage.success("操作成功")

	getTableData()
	scrapApplySelectorVisible.value = false
	emit("update")
}

/**
 * 扩展明细 - 报废申请 删除报废申请 操作
 */
async function delScrapApply(e: MatWasteScrapApplyVo) {
	await showDelConfirm()
	await delScrapApplyByDisposalApplyIdPaged({
		disposalApplyId: props.id,
		scrapIdList: [e.id!]
	})

	ElMessage.success("操作成功")
	fetchTableData()
	emit("update")
}

/**
 * 查看报废申请 操作
 */
const scrapApplyDetailVisble = ref(false)
const editScrapApplyRow = ref<MatWasteScrapApplyVo>({})
function viewScrapApply(e: MatWasteScrapApplyVo) {
	editScrapApplyRow.value = e
	scrapApplyDetailVisble.value = true
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.scrap-disposal-apply-editor-table-wrapper {
	&:deep(.pitaya-table) {
		.error {
			.column-inner-item,
			.cell,
			.el-input__inner {
				color: red;
			}
		}
	}
}
</style>
