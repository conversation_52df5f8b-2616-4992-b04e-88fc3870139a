<!-- 物资明细 -->
<template>
	<Query
		:query-arr-list="(queryConf as any)"
		style="margin: 10px 10px -10px"
		@get-query-data="getTableData"
	/>

	<pitaya-table
		ref="tableRef"
		:columns="tableProp"
		:table-data="tableData"
		:need-selection="false"
		:single-select="false"
		:need-index="true"
		:need-pagination="true"
		:total="pageTotal"
		:table-loading="tableLoading"
		@on-selection-change="selectedTableList = $event"
		@on-current-page-change="onCurrentPageChange"
	>
		<!-- 物资性质 -->
		<template #attribute="{ rowData }">
			<dict-tag :options="DictApi.getMatAttr()" :value="rowData.attribute" />
		</template>

		<!-- 预估回收总重量 -->
		<template #recoveryWeight="{ rowData }">
			{{ toFixedTwo(rowData.recoveryWeight) }}
		</template>

		<!-- 库存单位 -->
		<template #useUnit="{ rowData }">
			{{ dictFilter("INVENTORY_UNIT", rowData.useUnit)?.subitemName || "---" }}
		</template>
		<!-- 材质 -->
		<template #quality="{ rowData }">
			{{ dictFilter("MAIN_MATERIALS", rowData.quality)?.subitemName || "---" }}
		</template>

		<!-- 废旧物资分类 -->
		<template #wasteMaterialType="{ rowData }">
			<dict-tag
				:options="DictApi.getWasteMaterialType()"
				:value="rowData.wasteMaterialType"
			/>
		</template>

		<!-- 存放仓库 storeIdCnt -->
		<template #storeIdCnt="{ rowData }">
			<link-tag
				:value="rowData.storeIdCnt"
				@on-click="onRowStoreView(rowData)"
			/>
		</template>
	</pitaya-table>

	<Drawer
		v-model:drawer="matStoreVisible"
		:size="modalSize.lg"
		destroy-on-close
	>
		<mat-store-detail
			:id="props.id"
			:row="editMatRow"
			type="scrapDisposal"
			@close="matStoreVisible = false"
		/>
	</Drawer>
</template>

<script setup lang="ts">
import { DictApi } from "@/app/baseline/api/dict"
import { MatWasteScrapApplyItemVo } from "@/app/baseline/utils/types/waste-scrap-apply"
import { useDictInit } from "../../components/dictBase"
import { useTbInit } from "../../components/tableBase"
import { IModalType } from "@/app/baseline/utils/types/common"
import matStoreDetail from "../scrapApply/matStoreDetail.vue"
import { modalSize } from "@/app/baseline/utils/layout-config"
import { listWasteMatByDisposalApplyIdPaged } from "@/app/baseline/api/waste/scrap-disposal-apply"
import { MatWasteScrapDisposalApplyItemVoQuery } from "@/app/baseline/utils/types/waste-scrap-disposal-apply"
import linkTag from "../../components/linkTag.vue"
import dictTag from "../../components/dictTag.vue"
import { batchFormatterNumView, toFixedTwo } from "@/app/baseline/utils"

const props = withDefaults(
	defineProps<{
		/**
		 * 报废申请id
		 */
		id: any
		/**
		 * 详情类型 编辑/查看/新建
		 */
		mode?: IModalType
	}>(),
	{
		mode: IModalType.create
	}
)

const editMatRow = ref()
const matStoreVisible = ref(false)

const { dictOptions, dictFilter, getDictByCodeList } = useDictInit()

const queryConf = computed(() => {
	return [
		{
			name: "物资编码",
			key: "materialCode",
			type: "input",
			placeholder: "请输入物资编码"
		},
		{
			name: "物资名称",
			key: "materialLabel",
			type: "input",
			placeholder: "请输入物资名称"
		},
		{
			name: "规格型号",
			key: "version",
			placeholder: "请输入规格型号",
			type: "input"
		},
		{
			name: "物资性质",
			key: "attribute",
			type: "select",
			children: dictOptions.value.MATERIAL_NATURE,
			placeholder: "请选择物资性质"
		}
	]
})

const {
	currentPage,
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	fetchParam,
	selectedTableList,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = useTbInit<MatWasteScrapApplyItemVo, MatWasteScrapDisposalApplyItemVoQuery>()

fetchFunc.value = listWasteMatByDisposalApplyIdPaged

tableProp.value = [
	{
		prop: "materialCode",
		label: "物资编码",
		width: 130,
		fixed: "left"
	},
	{
		prop: "materialLabel",
		label: "物资名称"
	},
	{
		prop: "materialTypeCode",
		label: "分类编码",
		width: 120
	},
	{
		prop: "materialTypeLabel",
		label: "物资分类名称",
		width: 120
	},
	{
		prop: "version",
		label: "规格型号"
	},
	{
		prop: "attribute",
		label: "物资性质",
		needSlot: true,
		width: 120
	},
	{
		prop: "useUnit",
		label: "库存单位",
		needSlot: true,
		width: 90
	},
	{
		prop: "quality",
		label: "主要材质",
		needSlot: true,
		minWidth: 120
	},
	{
		prop: "wasteMaterialType",
		label: "废旧物资分类",
		needSlot: true,
		width: 120
	},
	{
		prop: "recoveryWeight",
		label: "预估回收总重量(KG)",
		width: 150,
		needSlot: true,
		align: "right"
	},
	{
		prop: "num_view",
		label: "库存数量",
		align: "right"
	},
	{
		prop: "completeNum_view",
		label: "报废数量",
		align: "right"
	},
	{
		prop: "storeIdCnt",
		label: "存放仓库",
		needSlot: true,
		fixed: "right"
	}
]

watch(tableData, () => {
	batchFormatterNumView(tableData.value)
})

function onRowStoreView(e: MatWasteScrapApplyItemVo) {
	editMatRow.value = { ...e }
	matStoreVisible.value = true
}

/**
 * table 数据源
 * @param data
 */
const getTableData = (data?: any) => {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()
	fetchParam.value = {
		...fetchParam.value,
		...data
	}

	fetchTableData()
}
onMounted(() => {
	getDictByCodeList([
		"INVENTORY_UNIT",
		"WASTE_MATERIALS_TYPE",
		"MAIN_MATERIALS",
		"MATERIAL_NATURE"
	])

	fetchParam.value.scrapDisposalId = props.id
	fetchParam.value.sord = "asc"
	fetchParam.value.sidx = "code"
	fetchTableData()
})
</script>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
