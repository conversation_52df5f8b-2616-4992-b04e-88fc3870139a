<!-- 在库时长 table -->
<template>
	<div>
		<pitaya-table
			ref="tableRef"
			:columns="tableProp"
			:table-data="tableData"
			:need-index="true"
			:single-select="false"
			:need-pagination="true"
			:total="pageTotal"
			:table-loading="tableLoading"
			:max-height="maxTableHeight"
			@on-selection-change="onDataSelected"
			@on-table-sort-change="handleSortChange"
			@on-current-page-change="onCurrentPageChange"
		>
			<template #age="{ rowData }">
				<span v-if="rowData.age < 1">{{ rowData.age }}</span>

				<span style="color: #f00 !important" v-else>
					{{ rowData.age }}
				</span>
			</template>
		</pitaya-table>
	</div>
</template>

<script setup lang="ts">
import { useTbInit } from "../../components/tableBase"
import {
	ScrapBookItemRequest,
	ScrapBookInStoreAgePageVo
} from "@/app/baseline/utils/types/waste-scrap-ledger"
import { maxTableHeight } from "@/app/baseline/utils"
import { listWasteRepairBookInStorePaged } from "@/app/baseline/api/waste/repair-ledger"

const props = defineProps<{ id: any }>()

const {
	currentPage,
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange,
	onDataSelected
} = useTbInit<ScrapBookInStoreAgePageVo, ScrapBookItemRequest>()

fetchFunc.value = listWasteRepairBookInStorePaged

tableProp.value = [
	{
		prop: "storeCode",
		label: "仓库编码",
		width: 120,
		fixed: "left"
	},
	{
		prop: "storeLabel",
		label: "仓库名称"
	},
	{
		prop: "storeType_view",
		label: "仓库类型"
	},
	{
		prop: "roomCode",
		label: "货位编码"
	},
	{
		prop: "batchNo",
		label: "批次号",
		width: 180
	},
	{
		prop: "inStoreTime",
		label: "入库时间",
		width: 160
	},
	{
		prop: "age",
		label: "在库时长（年）",
		width: 150,
		needSlot: true,
		sortable: true,
		fixed: "right"
	}
]

onMounted(() => {
	fetchParam.value = {
		sord: "desc",
		sidx: "age",
		bookId: props.id,
		...fetchParam.value
	}
	fetchTableData()
})

/**
 * 按库龄排序
 * @param column
 * @param prop 字段名称
 * @param order ascending 升序 descending 降序
 */
function handleSortChange(column: any, prop: any, order: any) {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		id: props.id,
		...fetchParam.value,
		sord: order === "ascending" ? "asc" : "desc",
		sidx: order ? prop : "age" // 排序字段
	}

	fetchTableData()
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
