<!-- 交旧记录 table -->
<template>
	<div>
		<pitaya-table
			ref="tableRef"
			:columns="tableProp"
			:table-data="tableData"
			:need-index="true"
			:single-select="false"
			:need-pagination="true"
			:total="pageTotal"
			:table-loading="tableLoading"
			:max-height="maxTableHeight"
			@on-selection-change="onDataSelected"
			@on-current-page-change="onCurrentPageChange"
			@on-table-sort-change="handleSortChange"
		>
			<!-- 交旧类型 -->
			<template #type="{ rowData }">
				{{ dictFilter("EXCHANGE_TPYE", rowData.type)?.subitemName || "---" }}
			</template>

			<!-- 关联业务单号展示 -->
			<template #preBusinessCode="{ rowData }">
				<link-tag
					:value="rowData.preBusinessCode"
					@on-click="showBusinessComponent(rowData)"
				/>
			</template>
		</pitaya-table>

		<!-- 业务组件详情 -->
		<Drawer
			v-model:drawer="businessComponentVisible"
			:size="modalSize.lg"
			destroy-on-close
		>
			<component :is="businessComponent" :id="editingTableRow?.preBusinessId" />
		</Drawer>
	</div>
</template>

<script setup lang="ts">
import { useTbInit } from "../../components/tableBase"
import LinkTag from "../../components/linkTag.vue"
import { IInventoryBusinessType } from "@/app/baseline/utils/types/store-inventory"
import { modalSize } from "@/app/baseline/utils/layout-config"
import businessComponentMap from "../../store/business/inventory/business-component-map"
import { listRepairBookWasteOldRecordPaged } from "@/app/baseline/api/waste/repair-ledger"
import {
	RepairBookItemRequest,
	RepairBookWasteOldRecordPageVo
} from "@/app/baseline/utils/types/warehouse-repair-ledger"
import { batchFormatterNumView, maxTableHeight } from "@/app/baseline/utils"
import { useDictInit } from "../../components/dictBase"

const props = defineProps<{ id: any }>()

const {
	currentPage,
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange,
	onDataSelected
} = useTbInit<RepairBookWasteOldRecordPageVo, RepairBookItemRequest>()

const { dictFilter, getDictByCodeList } = useDictInit()

const businessComponentVisible = ref(false)

/**
 * 要查看的领料单Id
 */
const editingTableRow = ref<RepairBookWasteOldRecordPageVo>()

fetchFunc.value = listRepairBookWasteOldRecordPaged

tableProp.value = [
	{ prop: "code", label: "申请单号", width: 200, fixed: "left" },
	{ prop: "label", label: "交旧业务名称" },
	{
		prop: "type",
		label: "交旧类型", // 字典
		needSlot: true
	},
	{
		prop: "preBusinessCode",
		label: "关联业务单号",
		needSlot: true,
		width: 170
	},
	{ prop: "wasteOldNum_view", label: "维修数量", align: "right" },
	{ prop: "sysOrgId_view", label: "申请部门" },
	{ prop: "createdBy_view", label: "申请人" },
	{ prop: "createdDate", label: "申请时间", sortable: true }
]

/**
 * 格式化数量
 */
watch(tableData, () => {
	batchFormatterNumView(tableData.value)
})

/**
 * 业务组件
 *
 * 根据不同事务类型，展示事务的前置业务
 */
const businessComponent = computed(() => {
	return businessComponentMap[
		editingTableRow.value?.preBusinessType as IInventoryBusinessType
	]
})

onMounted(() => {
	getDictByCodeList(["EXCHANGE_TPYE"])

	fetchParam.value = {
		bookId: props.id,
		sord: "desc",
		sidx: "createdDate",
		...fetchParam.value
	}
	fetchTableData()
})

/**
 * 排序
 * @param column
 * @param prop 字段名称
 * @param order ascending 升序 descending 降序
 */
function handleSortChange(column: any, prop: any, order: any) {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		sord:
			order === "descending" ? "desc" : order === "ascending" ? "asc" : "desc",
		sidx: order ? prop : "createdDate" // 排序字段
	}

	fetchTableData()
}

/**
 * 下钻 操作
 */
function showBusinessComponent(e?: RepairBookWasteOldRecordPageVo) {
	businessComponentVisible.value = true
	editingTableRow.value = e
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
