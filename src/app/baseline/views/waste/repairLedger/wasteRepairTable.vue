<!-- 维修记录 table -->
<template>
	<div>
		<pitaya-table
			ref="tableRef"
			:columns="tableProp"
			:table-data="tableData"
			:need-index="true"
			:single-select="false"
			:need-pagination="true"
			:total="pageTotal"
			:table-loading="tableLoading"
			:max-height="maxTableHeight"
			@on-selection-change="onDataSelected"
			@on-current-page-change="onCurrentPageChange"
			@on-table-sort-change="handleSortChange"
		>
			<!-- 维修费用 -->
			<template #repairFee="{ rowData }">
				<cost-tag :value="rowData.repairFee" />
			</template>

			<template #repairWay="{ rowData }">
				{{ rowData.repairWay == 1 ? "委外维修" : "自主维修" }}
			</template>
		</pitaya-table>
	</div>
</template>

<script setup lang="ts">
import { useTbInit } from "../../components/tableBase"
import costTag from "../../components/costTag.vue"
import { listRepairBookRepairRecordePaged } from "@/app/baseline/api/waste/repair-ledger"
import {
	RepairBookItemRequest,
	RepairBookRepairRecordPageVo
} from "@/app/baseline/utils/types/warehouse-repair-ledger"
import { batchFormatterNumView, maxTableHeight } from "@/app/baseline/utils"

const props = defineProps<{ id: any }>()

const {
	currentPage,
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange,
	onDataSelected
} = useTbInit<RepairBookRepairRecordPageVo, RepairBookItemRequest>()

fetchFunc.value = listRepairBookRepairRecordePaged

tableProp.value = [
	{
		prop: "businessApplyCode",
		label: "返修单号",
		width: 200,
		fixed: "left"
	},
	{ prop: "businessApplyLabel", label: "返修业务名称" },
	{ prop: "repairNum_view", label: "返修数量", align: "right" },
	{ prop: "repairWay", label: "维修方式", needSlot: true },
	{ prop: "repairDayCount", label: "维修时间（天）" },
	{ prop: "repairFee", label: "维修费用", needSlot: true, align: "right" },
	{ prop: "sysOrgId_view", label: "申请部门" },
	{ prop: "userName_view", label: "申请人" },
	{ prop: "applyDate", label: "申请时间", width: 160, sortable: true }
]

/**
 * 格式化数量
 */
watch(tableData, () => {
	batchFormatterNumView(tableData.value)
})

onMounted(() => {
	fetchParam.value = {
		bookId: props.id,
		sord: "desc",
		sidx: "createdDate",
		...fetchParam.value
	}
	fetchTableData()
})

/**
 * 排序
 * @param column
 * @param prop 字段名称
 * @param order ascending 升序 descending 降序
 */
function handleSortChange(column: any, prop: any, order: any) {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		sord:
			order === "descending" ? "desc" : order === "ascending" ? "asc" : "desc",
		sidx: order ? (prop == "applyDate" ? "createdDate" : prop) : "createdDate" // 排序字段
	}

	fetchTableData()
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
