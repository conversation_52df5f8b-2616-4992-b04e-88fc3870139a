<!-- 库存查询 table -->
<template>
	<pitaya-table
		ref="tableRef"
		:columns="tableProp"
		:table-data="tableData"
		:need-index="true"
		:single-select="false"
		:need-pagination="true"
		:total="pageTotal"
		:table-loading="tableLoading"
		:max-height="maxTableHeight"
		@on-selection-change="selectedTableList = $event"
		@on-current-page-change="onCurrentPageChange"
	/>
</template>

<script setup lang="ts">
import { listRepairBookStorePaged } from "@/app/baseline/api/waste/repair-ledger"
import { useTbInit } from "../../components/tableBase"
import {
	RepairBookItemRequest,
	RepairBookStorePageVo
} from "@/app/baseline/utils/types/warehouse-repair-ledger"
import { batchFormatterNumView, maxTableHeight } from "@/app/baseline/utils"

const props = defineProps<{ id: any }>()

const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = useTbInit<RepairBookStorePageVo, RepairBookItemRequest>()

fetchFunc.value = listRepairBookStorePaged

tableProp.value = [
	{ prop: "storeCode", label: "仓库编码", fixed: "left" },
	{ prop: "storeName", label: "仓库名称" },
	{ prop: "num_view", label: "库存数量", align: "right" },
	{ prop: "sysCommunityId_view", label: "所属公司" }, // TODO
	{ prop: "depotId_view", label: "所属段区" },
	{ prop: "costCenterId_view", label: "成本中心" },
	{ prop: "positionId", label: "仓库位置" },
	{ prop: "storeManage", label: "库管员", fixed: "right" }
]

/**
 * 格式化数量
 */
watch(tableData, () => {
	batchFormatterNumView(tableData.value)
})

onMounted(async () => {
	fetchParam.value = {
		bookId: props.id,
		...fetchParam.value
	}
	fetchTableData()
})
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
