<!-- 废旧-报废申请编辑 -->
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<div class="drawer-column left" style="width: 310px">
			<Title :title="titleConf" />
			<el-scrollbar class="rows">
				<el-form
					class="content"
					:model="formData"
					:rules="formRules"
					ref="formRef"
					label-position="top"
					label-width="100px"
				>
					<form-element :form-element="formEls" :form-data="formData" />
				</el-form>
			</el-scrollbar>

			<button-list
				class="footer"
				:loading="drawerBtnLoading"
				:button="footerBtnConf"
				@on-btn-click="handleDrawerBtnClick"
			/>
		</div>

		<div
			class="drawer-column right"
			:class="!canEditExtra ? 'disabled' : ''"
			style="width: calc(100% - 310px)"
		>
			<Title :title="extTitleConf">
				<Tabs
					style="margin-right: auto; margin-left: 30px"
					:tabs="['物资明细', '仓库明细', '相关附件']"
					@on-tab-change="handleTabChange"
				/>
			</Title>

			<div class="rows scrap-apply-editor-table-wrapper">
				<el-scrollbar v-if="activatedTab === 0">
					<Query
						:query-arr-list="(queryConf as any)"
						style="margin: 10px 10px -10px"
						@get-query-data="getQueryData"
					/>

					<pitaya-table
						ref="tableRef"
						:columns="tableProp"
						:table-data="tableData"
						:need-selection="true"
						:single-select="false"
						:need-index="true"
						:need-pagination="true"
						:total="pageTotal"
						:table-loading="tableLoading"
						:cell-class-name="tbCellClassName"
						@on-selection-change="selectedTableList = $event"
						@on-current-page-change="onCurrentPageChange"
					>
						<!-- 物资性质 -->
						<template #attribute="{ rowData }">
							<dict-tag
								:options="DictApi.getMatAttr()"
								:value="rowData.attribute"
							/>
						</template>

						<!-- 预估回收总重量 recoveryWeight -->
						<template #recoveryWeight="{ rowData }">
							{{ toFixedTwo(rowData.recoveryWeight) }}
						</template>

						<!-- 库存单位 -->
						<template #useUnit="{ rowData }">
							{{
								dictFilter("INVENTORY_UNIT", rowData.useUnit)?.subitemName ||
								"---"
							}}
						</template>
						<!-- 材质 -->
						<template #quality="{ rowData }">
							{{
								dictFilter("MAIN_MATERIALS", rowData.quality)?.subitemName ||
								"---"
							}}
						</template>

						<!-- 废旧物资分类 -->
						<template #wasteMaterialType="{ rowData }">
							<dict-tag
								:options="DictApi.getWasteMaterialType()"
								:value="rowData.wasteMaterialType"
							/>
						</template>

						<!-- 存放仓库 storeIdCnt -->
						<template #storeIdCnt="{ rowData }">
							<link-tag
								:value="rowData.storeIdCnt"
								@click.stop="onRowStoreView(rowData)"
							/>
						</template>
						<template #actions="{ rowData }">
							<el-button v-btn link @click.stop="delMat(rowData)">
								<font-awesome-icon
									:icon="['fas', 'trash-can']"
									style="color: var(--pitaya-btn-background)"
								/>
								<span class="table-inner-btn">移除</span>
							</el-button>
						</template>

						<template #footerOperateLeft>
							<button-list
								:button="tbBtnConf"
								:is-not-radius="true"
								:loading="drawerBtnLoading"
								@on-btn-click="handleTabsClick"
							/>
						</template>
					</pitaya-table>
				</el-scrollbar>

				<el-scrollbar v-else-if="activatedTab === 1">
					<scrap-store-table :id="formData.id!" />
				</el-scrollbar>

				<table-file
					v-else
					:business-type="fileBusinessType.wasteScrapApply"
					:business-id="formData.id!"
					:mod="IModalType.edit"
				/>
			</div>
			<button-list
				class="footer"
				:button="submitBtnConf"
				:loading="drawerBtnLoading"
				@on-btn-click="handleSubmit"
			/>
		</div>
	</div>

	<!-- 物资选择器 -->
	<Drawer
		v-model:drawer="goodsSelectorVisible"
		:size="modalSize.lg"
		destroy-on-close
	>
		<mat-selector
			:table-req-params="{
				wasteMaterialType: formData.wasteMaterialType,
				id: props.id || formData.id,
				sord: 'asc',
				sidx: 'code'
			}"
			:table-api="listWasteMaterialStorePage"
			:multiple="true"
			:columns="matSelectColumns"
			:query-arr-list="matSelectQueryConf"
			@save="handleAddGoods"
			@close="goodsSelectorVisible = false"
		/>
	</Drawer>

	<Drawer
		v-model:drawer="matStoreVisible"
		:size="modalSize.lg"
		destroy-on-close
	>
		<mat-store-detail
			:id="props.id || formData.id"
			:row="editMatRow"
			@close="matStoreVisible = false"
		/>
	</Drawer>
</template>

<script setup lang="ts">
import {
	IIdempotentTokenType,
	IIdempotentTokenTypePre,
	IModalType
} from "@/app/baseline/utils/types/common"
import formElement from "../../components/formElement.vue"
import { useMessageBoxInit } from "../../components/messageBox"
import ButtonList from "@/compontents/ButtonList.vue"
import dictTag from "../../components/dictTag.vue"
import linkTag from "../../components/linkTag.vue"
import { inputMaxLength, modalSize } from "@/app/baseline/utils/layout-config"
import { fileBusinessType } from "@/app/baseline/api/dict"
import {
	batchFormatterNumView,
	getModalTypeLabel,
	toFixedTwo
} from "@/app/baseline/utils"
import { FormElementType } from "../../components/define"

import { map, includes, findIndex } from "lodash-es"
import { FormInstance, FormItemRule } from "element-plus"
import { useDictInit } from "../../components/dictBase"

import matSelector from "../../store/components/matSelector.vue"
import TableFile from "../../components/tableFile.vue"
import {
	getIdempotentToken,
	requiredValidator
} from "@/app/baseline/utils/validate"
import { useTbInit } from "../../components/tableBase"
import {
	addWasteScrapApply,
	listWasteScrapApplyDetail,
	listWasteScrapApplyItemPaged,
	updateWasteScrapApply,
	listWasteMaterialStorePage,
	addWasteScrapApplyBatchItem,
	delWasteScrapApplyBatchItem,
	submitWasteScrapApplyPublish
} from "@/app/baseline/api/waste/scrap-apply"
import {
	MatWasteScrapApplyItemVo,
	MatWasteScrapApplyItemVoQuery,
	MatWasteScrapApplyVo
} from "@/app/baseline/utils/types/waste-scrap-apply"
import { DictApi } from "@/app/baseline/api/dict"
import matStoreDetail from "./matStoreDetail.vue"
import scrapStoreTable from "./scrapStoreTable.vue"
import { listMatStoragePaged } from "@/app/baseline/api/store/manage-api"

const props = withDefaults(
	defineProps<{
		/**
		 * 报废申请id
		 */
		id: any
		/**
		 * 详情类型 编辑/查看/新建
		 */
		mode?: IModalType
	}>(),
	{
		mode: IModalType.create
	}
)
const emit = defineEmits<{
	(e: "close", msg?: string): void
	(e: "update"): void
	(e: "save"): void
}>()

const { showDelConfirm, showWarnConfirm } = useMessageBoxInit()

/**
 * 保存旧的表单数据
 */
const oldFormData = ref<string>()

const formRef = ref<FormInstance>()

const canEditExtra = computed(() => Boolean(formData.value?.id || props.id))

const drawerBtnLoading = ref(false)

const goodsSelectorVisible = ref(false)

const { dictOptions, dictFilter, getDictByCodeList } = useDictInit()

/**
 * 提交审核按钮配置
 */
const submitBtnConf = computed(() => [
	{
		name: "提交审核",
		icon: ["fas", "circle-check"],
		disabled: tableData.value.length < 1
	}
])

const activatedTab = ref(0)

const drawerLoading = ref(false)

const titleConf = computed(() => ({
	name: [
		getModalTypeLabel(
			canEditExtra.value ? IModalType.edit : IModalType.create,
			"报废申请"
		)
	],
	icon: ["fas", "square-share-nodes"]
}))

const extTitleConf = {
	name: ["扩展信息"],
	icon: ["fas", "square-share-nodes"]
}

const handleTabChange = (index: number) => {
	activatedTab.value = index

	if (index === 0) {
		fetchParam.value = {
			scrapId: props.id || formData.value.id,
			sord: "asc",
			sidx: "code"
		}

		pageSize.value = 20

		getQueryData()
	}
}

const formData = ref<MatWasteScrapApplyVo>({})

const formRules: Record<string, FormItemRule> = {
	label: requiredValidator("申请名称"),
	wasteMaterialType: requiredValidator("废旧物资分类"),
	decisionBasis: requiredValidator("废旧认定决策依据"),
	reason: requiredValidator("报废原因")
}

const queryConf = computed(() => {
	return [
		{
			name: "物资编码",
			key: "materialCode",
			type: "input",
			placeholder: "请输入物资编码"
		},
		{
			name: "物资名称",
			key: "materialLabel",
			type: "input",
			placeholder: "请输入物资名称"
		},
		{
			name: "规格型号",
			key: "version",
			placeholder: "请输入规格型号",
			type: "input"
		},
		{
			name: "物资性质",
			key: "attribute",
			type: "select",
			children: dictOptions.value.MATERIAL_NATURE,
			placeholder: "请选择物资性质"
		}
	]
})

const {
	currentPage,
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	pageSize,
	fetchParam,
	selectedTableList,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = useTbInit<MatWasteScrapApplyItemVo, MatWasteScrapApplyItemVoQuery>()

fetchFunc.value = listWasteScrapApplyItemPaged

tableProp.value = [
	{
		prop: "materialCode",
		label: "物资编码",
		width: 130,
		fixed: "left"
	},
	{
		prop: "materialLabel",
		label: "物资名称"
	},
	{
		prop: "materialTypeCode",
		label: "分类编码",
		width: 120
	},
	{
		prop: "materialTypeLabel",
		label: "物资分类名称",
		width: 120
	},
	{
		prop: "version",
		label: "规格型号"
	},
	{
		prop: "attribute",
		label: "物资性质",
		needSlot: true,
		width: 120
	},
	{
		prop: "useUnit",
		label: "库存单位",
		needSlot: true,
		width: 90
	},
	{
		prop: "quality",
		label: "主要材质",
		needSlot: true
	},
	{
		prop: "wasteMaterialType",
		label: "废旧物资分类",
		needSlot: true,
		width: 120
	},
	{
		prop: "recoveryWeight",
		label: "预估回收重量(KG)",
		width: 130,
		needSlot: true,
		align: "right"
	},
	{
		prop: "num_view",
		label: "库存数量",
		align: "right"
	},
	{
		prop: "completeNum_view",
		label: "报废数量",
		align: "right"
	},
	{
		prop: "storeIdCnt",
		label: "存放仓库",
		needSlot: true
	},
	{
		prop: "actions",
		label: "操作",
		width: 100,
		needSlot: true,
		fixed: "right"
	}
]

const formEls = computed<FormElementType[][]>(() => [
	[
		{
			label: "申请名称",
			name: "label",
			maxlength: inputMaxLength.input
		},
		{
			label: "废旧物资分类",
			name: "wasteMaterialType",
			type: "select",
			data: dictOptions.value.WASTE_MATERIALS_TYPE,
			disabled: canEditExtra.value,
			clear: false
		},
		{
			label: "废旧认定决策依据",
			name: "decisionBasis",
			type: "textarea",
			disabled: false,
			clear: false,
			maxlength: inputMaxLength.textarea
		},
		{
			label: "报废原因",
			name: "reason",
			type: "textarea",
			disabled: false,
			clear: false,
			maxlength: inputMaxLength.textarea
		},
		{
			label: "备注说明",
			name: "remark",
			type: "textarea",
			disabled: false,
			clear: false,
			maxlength: inputMaxLength.textarea
		}
	]
])

watch(tableData, () => {
	batchFormatterNumView(tableData.value)
})

const tbBtnConf = computed(() => {
	return [
		{
			name: "添加物资",
			icon: ["fas", "circle-plus"]
		},
		{
			name: "批量移除",
			icon: ["fas", "trash-can"],
			disabled: selectedTableList.value.length > 0 ? false : true
		}
	]
})
const footerBtnConf = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "保存草稿",
		icon: ["fas", "floppy-disk"]
	}
]

/**
 * 物资明细 table 行样式
 *
 * 库存异常物资，用红色标记该行
 */
const errorGoodsIdList = ref<number[]>([])
function tbCellClassName({ row }: any) {
	return includes(errorGoodsIdList.value, row.id) ? "error" : ""
}

function getQueryData(e?: any) {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = { ...fetchParam.value, ...e }
	fetchTableData()
}

onMounted(() => {
	getDictByCodeList([
		"INVENTORY_UNIT",
		"WASTE_MATERIALS_TYPE",
		"MAIN_MATERIALS",
		"MATERIAL_NATURE"
	])

	fetchParam.value.sord = "asc"
	fetchParam.value.sidx = "code"

	if (props.id) {
		fetchParam.value.scrapId = props.id

		getDetail()
		fetchTableData()
	}
})

/**
 * 查看存放仓库
 * @param e
 */
const editMatRow = ref()
const matStoreVisible = ref(false)
function onRowStoreView(e: MatWasteScrapApplyItemVo) {
	if (!e.storeIdCnt) {
		return false
	}
	editMatRow.value = { ...e }
	matStoreVisible.value = true
}

/**
 * 保存草稿 handler
 */
async function handleSaveDraft() {
	if (!formRef.value) {
		return
	}

	formRef.value?.validate(async (valid) => {
		if (!valid) {
			return
		}
		drawerBtnLoading.value = true

		const api = canEditExtra.value ? updateWasteScrapApply : addWasteScrapApply
		try {
			let idempotentToken = ""
			if (!canEditExtra.value) {
				idempotentToken = getIdempotentToken(
					IIdempotentTokenTypePre.apply,
					IIdempotentTokenType.wasteBFApply
				)
			}

			const r = await api(formData.value as any, idempotentToken)

			ElMessage.success("操作成功")
			formData.value.id = r?.id
			formData.value.code = r?.code

			oldFormData.value = JSON.stringify(formData.value)

			fetchParam.value.scrapId = r?.id as any

			emit("save")
			getQueryData()
		} finally {
			drawerBtnLoading.value = false
		}
	})
}

// 提交审核逻辑
async function handleSubmit() {
	if (!formRef.value) {
		return
	}

	formRef.value?.validate(async (valid) => {
		if (!valid) {
			return
		}

		await showWarnConfirm("请确认是否提交本次数据？")

		drawerBtnLoading.value = true
		drawerLoading.value = true

		try {
			// 如果主表有修改，则先更新主表数据
			if (oldFormData.value != JSON.stringify(formData.value)) {
				await updateWasteScrapApply(formData.value as any)
			}

			const idempotentToken = getIdempotentToken(
				IIdempotentTokenTypePre.other,
				IIdempotentTokenType.wasteBFApply,
				formData.value.id
			)

			const { code, msg, data } = await submitWasteScrapApplyPublish(
				props.id || formData.value.id,
				idempotentToken
			)

			if (data && code != 200) {
				errorGoodsIdList.value = data || []
				ElMessage.error(msg)
				getQueryData()
			} else {
				ElMessage.success("操作成功")
				emit("save")
				emit("close")
			}
		} finally {
			drawerBtnLoading.value = false
			drawerLoading.value = false
		}
	})
}

/**
 * 点击 drawer 按钮 handler
 */
function handleDrawerBtnClick(name?: string) {
	if (name === "保存草稿") {
		handleSaveDraft()
		return
	}

	emit("close")
}

async function handleTabsClick(btnName?: string) {
	if (btnName === "添加物资") {
		goodsSelectorVisible.value = true
	} else if (btnName === "批量移除") {
		const ids = map(selectedTableList.value, ({ id }) => id)
		await showDelConfirm()

		drawerBtnLoading.value = true
		try {
			await delWasteScrapApplyBatchItem({
				scrapId: props.id || formData.value.id,
				materialIdList: ids
			})

			ids.forEach((id) => {
				const idx = findIndex(errorGoodsIdList.value, (v) => v == id)
				if (idx > -1) {
					errorGoodsIdList.value.splice(idx, 1)
				}
			})
		} finally {
			drawerBtnLoading.value = false
		}

		ElMessage.success("操作成功")
		fetchTableData()
		emit("update")
	}
}

/**
 * 获取报废申请详情
 */
function getDetail() {
	drawerLoading.value = true

	// 获取详情
	listWasteScrapApplyDetail(props.id)
		.then((r = {}) => {
			formData.value = { ...r }
			oldFormData.value = JSON.stringify(formData.value)
		})
		.finally(() => (drawerLoading.value = false))
}

const matSelectColumns: TableColumnType[] = [
	{ prop: "materialCode", label: "物资编码", width: 130, fixed: "left" },
	{ prop: "materialLabel", label: "物资名称" },
	{ prop: "materialTypeLabel", label: "物资分类名称" },
	{ prop: "version", label: "规格型号" },
	{ prop: "attribute", label: "物资性质", needSlot: true, width: 120 },
	{ prop: "wasteMaterialType", label: "废旧物资分类", needSlot: true },
	{ prop: "quality_view", label: "主要材质" },
	{ prop: "useUnit_view", label: "库存单位" },
	{
		prop: "recoveryWeight",
		label: "预估回收重量(KG)",
		width: 140,
		needSlot: true,
		align: "right"
	},
	{ prop: "storeLabel", label: "存放仓库名称" },
	{ prop: "num_view", label: "库存数量", align: "right" },
	{ prop: "completeNum_view", label: "可报废数量", align: "right" }
]

const matSelectQueryConf = computed(() => {
	return [
		{
			name: "物资编码",
			key: "code",
			type: "input",
			placeholder: "请输入物资编码"
		},
		{
			name: "物资名称",
			key: "label",
			type: "input",
			placeholder: "请输入物资名称"
		},
		{
			name: "规格型号",
			key: "version",
			placeholder: "请输入规格型号",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "物资性质",
			key: "attribute",
			type: "select",
			children: dictOptions.value.MATERIAL_NATURE,
			placeholder: "请选择物资性质"
		},
		{
			name: "仓库名称",
			key: "storeId",
			placeholder: "请选择仓库名称",
			type: "tableSelect",
			tableInfo: [
				{
					title: "请选择仓库",
					tableApi: listMatStoragePaged, //表格接口
					tableColumns: [
						{ label: "仓库编码", prop: "code", width: 120 },
						{ label: "仓库名称", prop: "label" },
						{
							label: "仓库级别",
							prop: "level_view",
							width: 100
						},
						{
							label: "仓库类型",
							prop: "type_view",
							width: 120
						},
						{
							label: "所属段区",
							prop: "depotId_view",
							width: 150
						}
					],
					queryArrList: [
						{
							name: "仓库编码",
							key: "code",
							type: "input",
							placeholder: "请输入仓库编码"
						},
						{
							name: "仓库名称",
							key: "label",
							type: "input",
							placeholder: "请输入仓库名称"
						}
					],
					params: {
						currentPage: 1,
						pageSize: 20
					}, //表格接口参数

					labelName: {
						key: "id", //回显标识
						label: "label", //input框要展示的字段
						value: "id" //要给后台传的字段
					}
				}
			]
		}
	]
})

/**
 * 添加物资 handler
 */
async function handleAddGoods(e?: any) {
	const idempotentToken = getIdempotentToken(
		IIdempotentTokenTypePre.other,
		IIdempotentTokenType.wasteBFApply,
		formData.value.id
	)

	await addWasteScrapApplyBatchItem(
		{
			scrapId: props.id || formData.value.id,
			dtoList: e || []
		},
		idempotentToken
	)

	ElMessage.success("操作成功")
	getQueryData()
	goodsSelectorVisible.value = false
	emit("update")
}

/**
 * 删除物资
 */
async function delMat(e: any) {
	await showDelConfirm()
	await delWasteScrapApplyBatchItem({
		scrapId: props.id || formData.value.id,
		materialIdList: [e.id]
	})

	const idx = findIndex(errorGoodsIdList.value, (v) => v == e.id)
	if (idx > -1) {
		errorGoodsIdList.value.splice(idx, 1)
	}

	ElMessage.success("操作成功")
	fetchTableData()
	emit("update")
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.scrap-apply-editor-table-wrapper {
	&:deep(.pitaya-table) {
		.error {
			.column-inner-item,
			.cell,
			.el-input__inner {
				color: red;
			}
		}
	}
}
</style>
