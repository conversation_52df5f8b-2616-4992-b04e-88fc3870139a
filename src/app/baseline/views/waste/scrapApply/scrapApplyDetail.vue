<!-- 废旧-报废申请详情 -->
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<div class="drawer-column left" style="width: 330px">
			<Title :title="titleConf" />
			<el-scrollbar>
				<el-descriptions size="small" :column="1" border class="content">
					<template>
						<el-descriptions-item
							v-for="desc in descList"
							:key="desc.key"
							:label="desc.label"
						>
							<span v-if="desc.needTooltip">
								<el-tooltip
									effect="dark"
									:content="formData?.[desc.key]"
									:disabled="
										getRealLength(formData?.[desc.key]) <= 100 ? true : false
									"
								>
									{{
										getRealLength(formData?.[desc.key]) > 100
											? setString(formData?.[desc.key], 100)
											: formData?.[desc.key] || "---"
									}}
								</el-tooltip>
							</span>
							<span v-else>
								{{ getNumDefByNumKey(desc.key, formData?.[desc.key], /Cnt$/) }}
							</span>
						</el-descriptions-item>
					</template>
				</el-descriptions>
			</el-scrollbar>
		</div>

		<div
			class="drawer-column right"
			style="width: calc(100% - 330px)"
			:class="{ pdr10: !footerBtnVisible }"
		>
			<Title :title="extTitleConf">
				<Tabs
					style="margin-right: auto; margin-left: 30px"
					:tabs="['物资明细', '仓库明细', '相关附件']"
					@on-tab-change="handleTabChange"
				/>
			</Title>

			<div class="rows">
				<el-scrollbar v-if="activatedTab === 0">
					<Query
						:query-arr-list="(queryConf as any)"
						style="margin: 10px 10px -10px"
						@get-query-data="getQueryData"
					/>
					<pitaya-table
						ref="tableRef"
						:columns="tableProp"
						:table-data="tableData"
						:need-selection="false"
						:single-select="false"
						:need-index="true"
						:need-pagination="true"
						:total="pageTotal"
						:table-loading="tableLoading"
						@on-selection-change="selectedTableList = $event"
						@on-current-page-change="onCurrentPageChange"
					>
						<!-- 物资性质 -->
						<template #attribute="{ rowData }">
							<dict-tag
								:options="DictApi.getMatAttr()"
								:value="rowData.attribute"
							/>
						</template>
						<!-- 预估回收总重量 recoveryWeight -->
						<template #recoveryWeight="{ rowData }">
							{{ toFixedTwo(rowData.recoveryWeight) }}
						</template>

						<!-- 库存单位 -->
						<template #useUnit="{ rowData }">
							{{
								dictFilter("INVENTORY_UNIT", rowData.useUnit)?.subitemName ||
								"---"
							}}
						</template>
						<!-- 材质 -->
						<template #quality="{ rowData }">
							{{
								dictFilter("MAIN_MATERIALS", rowData.quality)?.subitemName ||
								"---"
							}}
						</template>

						<!-- 废旧物资分类 -->
						<template #wasteMaterialType="{ rowData }">
							<dict-tag
								:options="DictApi.getWasteMaterialType()"
								:value="rowData.wasteMaterialType"
							/>
						</template>

						<!-- 存放仓库 storeIdCnt -->
						<template #storeIdCnt="{ rowData }">
							<link-tag
								:value="rowData.storeIdCnt"
								@on-click="onRowStoreView(rowData)"
							/>
						</template>
					</pitaya-table>
				</el-scrollbar>

				<el-scrollbar v-else-if="activatedTab === 1">
					<scrap-store-table :id="formData.id!" />
				</el-scrollbar>
				<table-file
					v-else
					:business-type="fileBusinessType.wasteScrapApply"
					:business-id="formData.id!"
					:mod="IModalType.view"
				/>
			</div>
			<button-list
				v-if="footerBtnVisible"
				class="footer"
				:button="submitBtnConf"
				:loading="drawerBtnLoading"
				@on-btn-click="emit('close')"
			/>
		</div>
	</div>

	<Drawer
		v-model:drawer="matStoreVisible"
		:size="modalSize.lg"
		destroy-on-close
	>
		<mat-store-detail
			:id="id"
			:row="editMatRow"
			@close="matStoreVisible = false"
		/>
	</Drawer>
</template>

<script setup lang="ts">
import { IModalType } from "@/app/baseline/utils/types/common"
import ButtonList from "@/compontents/ButtonList.vue"
import dictTag from "../../components/dictTag.vue"
import linkTag from "../../components/linkTag.vue"
import { modalSize } from "@/app/baseline/utils/layout-config"
import { fileBusinessType } from "@/app/baseline/api/dict"

import { useDictInit } from "../../components/dictBase"

import TableFile from "../../components/tableFile.vue"
import { useTbInit } from "../../components/tableBase"
import {
	listWasteScrapApplyDetail,
	listWasteScrapApplyItemPaged
} from "@/app/baseline/api/waste/scrap-apply"
import {
	MatWasteScrapApplyItemVo,
	MatWasteScrapApplyItemVoQuery,
	MatWasteScrapApplyVo
} from "@/app/baseline/utils/types/waste-scrap-apply"
import { DictApi } from "@/app/baseline/api/dict"
import matStoreDetail from "./matStoreDetail.vue"
import scrapStoreTable from "./scrapStoreTable.vue"
import {
	batchFormatterNumView,
	getNumDefByNumKey,
	toFixedTwo
} from "@/app/baseline/utils"
import { getRealLength, setString } from "@/app/baseline/utils/validate"

const props = withDefaults(
	defineProps<{
		/**
		 * 报废申请id
		 */
		id: any
		/**
		 * 详情类型 编辑/查看/新建
		 */
		mode?: IModalType
		footerBtnVisible?: boolean
	}>(),
	{
		mode: IModalType.create,
		footerBtnVisible: true
	}
)
const emit = defineEmits<{
	(e: "close"): void
}>()

const drawerBtnLoading = ref(false)

const { dictOptions, dictFilter, getDictByCodeList } = useDictInit()

/**
 * 提交审核按钮配置
 */
const submitBtnConf = computed(() => [
	{
		name: "取消",
		icon: ["fas", "circle-minus"],
		disabled: false
	}
])

const activatedTab = ref(0)

const drawerLoading = ref(false)

const titleConf = computed(() => ({
	name: ["报废单信息"],
	icon: ["fas", "square-share-nodes"]
}))

const extTitleConf = {
	name: ["扩展信息"],
	icon: ["fas", "square-share-nodes"]
}

const handleTabChange = (index: number) => {
	activatedTab.value = index

	if (index === 0) {
		fetchParam.value = {
			scrapId: props.id || formData.value.id,
			sord: "asc",
			sidx: "code"
		}

		pageSize.value = 20

		getQueryData()
	}
}

const formData = ref<MatWasteScrapApplyVo>({})

const queryConf = computed(() => {
	return [
		{
			name: "物资编码",
			key: "materialCode",
			type: "input",
			placeholder: "请输入物资编码"
		},
		{
			name: "物资名称",
			key: "materialLabel",
			type: "input",
			placeholder: "请输入物资名称"
		},
		{
			name: "规格型号",
			key: "version",
			placeholder: "请输入规格型号",
			type: "input"
		},
		{
			name: "物资性质",
			key: "attribute",
			type: "select",
			children: dictOptions.value.MATERIAL_NATURE,
			placeholder: "请选择物资性质"
		}
	]
})

const {
	currentPage,
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	pageSize,
	fetchParam,
	selectedTableList,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = useTbInit<MatWasteScrapApplyItemVo, MatWasteScrapApplyItemVoQuery>()

fetchFunc.value = listWasteScrapApplyItemPaged

tableProp.value = [
	{
		prop: "materialCode",
		label: "物资编码",
		width: 130,
		fixed: "left"
	},
	{
		prop: "materialLabel",
		label: "物资名称"
	},
	{
		prop: "materialTypeCode",
		label: "分类编码",
		width: 120
	},
	{
		prop: "materialTypeLabel",
		label: "物资分类名称",
		width: 120
	},
	{
		prop: "version",
		label: "规格型号"
	},
	{
		prop: "attribute",
		label: "物资性质",
		needSlot: true,
		width: 120
	},
	{
		prop: "useUnit",
		label: "库存单位",
		needSlot: true,
		width: 90
	},
	{
		prop: "quality",
		label: "主要材质",
		needSlot: true
	},
	{
		prop: "wasteMaterialType",
		label: "废旧物资分类",
		needSlot: true,
		width: 120
	},
	{
		prop: "recoveryWeight",
		label: "预估回收重量(KG)",
		width: 130,
		needSlot: true,
		align: "right"
	},
	{
		prop: "num_view",
		label: "库存数量",
		align: "right"
	},
	{
		prop: "completeNum_view",
		label: "报废数量",
		align: "right"
	},
	{
		prop: "storeIdCnt",
		label: "存放仓库",
		needSlot: true,
		fixed: "right"
	}
]

watch(tableData, () => {
	batchFormatterNumView(tableData.value)
})

/**
 * 关联业务单数据
 */
const descList = ref([
	{ label: "申请单号", key: "code" },
	{ label: "申请名称", key: "label" },
	{ label: "申请部门", key: "sysOrgId_view" },
	{ label: "申请人", key: "createdBy_view" },
	{ label: "申请时间", key: "createdDate" },
	{ label: "决策依据", key: "decisionBasis", needTooltip: true },
	{ label: "申请原因", key: "reason", needTooltip: true },

	{ label: "物资分类(项)", key: "materialTypeCnt" },
	{ label: "物资编码(个)", key: "materialCodeCnt" },
	{ label: "主要材质(类)", key: "qualityCnt" },
	{ label: "预估回收总重量(kg)", key: "recoveryWeight_view" }
])

function getQueryData(e?: any) {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = { ...fetchParam.value, ...e }
	fetchTableData()
}

onMounted(() => {
	getDictByCodeList([
		"INVENTORY_UNIT",
		"WASTE_MATERIALS_TYPE",
		"MAIN_MATERIALS",
		"MATERIAL_NATURE"
	])

	fetchParam.value.sord = "asc"
	fetchParam.value.sidx = "code"

	if (props.id) {
		fetchParam.value.scrapId = props.id

		getDetail()
		fetchTableData()
	}
})

/**
 * 查看存放仓库
 * @param e
 */
const editMatRow = ref()
const matStoreVisible = ref(false)
function onRowStoreView(e: MatWasteScrapApplyItemVo) {
	editMatRow.value = { ...e }
	matStoreVisible.value = true
}

/**
 * 获取报废申请详情
 */
function getDetail() {
	drawerLoading.value = true

	// 获取详情
	listWasteScrapApplyDetail(props.id)
		.then((r = {}) => {
			formData.value = { ...r }
			formData.value.recoveryWeight_view = toFixedTwo(r?.recoveryWeight)
		})
		.finally(() => (drawerLoading.value = false))
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
