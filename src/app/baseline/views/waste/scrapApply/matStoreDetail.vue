<!-- 查看存放仓库 -->
<script setup lang="ts">
import { FormElementType } from "@/app/baseline/views/components/define"
import { reactive, ref } from "vue"

import CostTag from "@/app/baseline/views/components/costTag.vue"
import dictTag from "@/app/baseline/views/components/dictTag.vue"
import { useDictInit } from "../../components/dictBase"
import { useTbInit } from "../../components/tableBase"
import {
	CkWasteStoreMaterialVo,
	CkWasteStoreMaterialVoQuery,
	MatWasteScrapApplyItemVo
} from "@/app/baseline/utils/types/waste-scrap-apply"
import { DictApi } from "@/app/baseline/api/dict"
import { listShowStoresByStoreIdsAndMaterialIdPaged } from "@/app/baseline/api/waste/scrap-apply"
import { listWasteStoresByDisposalApplyIdAndMaterialIdPaged } from "@/app/baseline/api/waste/scrap-disposal-apply"
import { CkWasteDisposalStoreMaterialVoQuery } from "@/app/baseline/utils/types/waste-scrap-disposal-apply"
import { toFixedTwo } from "@/app/baseline/utils"
export interface Props {
	id: any
	row: MatWasteScrapApplyItemVo
	type?: string
}

const { getDictByCodeList, dictFilter } = useDictInit()

const props = defineProps<Props>()
const emits = defineEmits(["close"])

const loading = ref(false)
const formBtnList = [{ name: "取消", icon: ["fas", "circle-minus"] }]

const formModal = computed<MatWasteScrapApplyItemVo>(() => {
	return {
		...props.row,
		recoveryWeight_view: toFixedTwo(props.row.recoveryWeight)
	}
})

/**
 * 左侧 title 配置
 */
const drawerLeftTitle = {
	name: ["物资信息"],
	icon: ["fas", "square-share-nodes"]
}

/**
 * 左侧 表单 配置
 */
const formEl = reactive<FormElementType[]>([
	{ label: "物资编码", name: "materialCode" },
	{ label: "物资名称", name: "materialLabel" },
	{ label: "分类编码", name: "materialTypeCode" },
	{ label: "分类名称", name: "materialTypeLabel" },
	{ label: "规格型号", name: "version" },
	{ label: "物资性质", name: "attribute" },
	{ label: "库存单位", name: "useUnit" },
	{ label: "主要材质", name: "quality" },
	{ label: "废旧物资分类", name: "wasteMaterialType" },
	{ label: "预估回收重量(KG)", name: "recoveryWeight_view" },
	{ label: "报废数量", name: "completeNum" }
])

/**
 * 右侧 title 配置
 */
const drawerRightTitle = {
	name: ["仓库信息"],
	icon: ["fas", "square-share-nodes"]
}

const {
	tableData,
	tableProp,
	tableRef,
	tableLoading,
	currentPage,
	pageTotal,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange,
	onDataSelected
} = useTbInit<
	CkWasteStoreMaterialVo,
	CkWasteStoreMaterialVoQuery | CkWasteDisposalStoreMaterialVoQuery
>()

/**
 * table 列 配置
 */
tableProp.value = [
	{ label: "仓库编码", prop: "storeCode", fixed: "left" },
	{ label: "仓库名称", prop: "storeLabel" },
	{ label: "所属段区", prop: "depotId_view" },
	{ label: "仓库位置", prop: "positionId" },
	{ label: "库管员", prop: "storeManage" },
	{ label: "报废数量", prop: "completeNum", needSlot: true, align: "right" }
]

/**
 * table 数据源
 * @param data
 */
const getTableData = () => {
	fetchFunc.value =
		props.type == "scrapDisposal"
			? listWasteStoresByDisposalApplyIdAndMaterialIdPaged
			: listShowStoresByStoreIdsAndMaterialIdPaged

	currentPage.value = 1
	tableRef.value?.resetCurrentPage()
	if (props.type == "scrapDisposal") {
		fetchParam.value = {
			id: props.id,
			sord: "desc",
			sidx: "createdDate",
			materialId: Number(formModal.value.materialId)
		}
	} else {
		fetchParam.value = {
			scrapId: props.id,
			sord: "desc",
			sidx: "createdDate",
			materialId: Number(formModal.value.materialId)
		}
	}

	fetchTableData()
}

onMounted(() => {
	getDictByCodeList(["INVENTORY_UNIT", "MAIN_MATERIALS"])
	getTableData()
})
</script>
<template>
	<div class="drawer-container">
		<!-- 左侧表单区域 -->
		<div class="drawer-column left">
			<div class="rows">
				<Title :title="drawerLeftTitle" />
				<el-descriptions size="small" :column="1" border class="content">
					<template>
						<el-descriptions-item
							v-for="(el, index) in formEl"
							label-align="center"
							:label="el.label"
							:key="index"
						>
							<span v-if="el.name == 'wasteMaterialType'">
								<dict-tag
									:options="DictApi.getWasteMaterialType()"
									:value="formModal[el.name]!"
								/>
							</span>

							<dict-tag
								v-else-if="el.name === 'attribute'"
								:options="DictApi.getMatAttr()"
								:value="formModal.attribute"
							/>

							<span v-else-if="el.name == 'useUnit'">
								{{
									dictFilter("INVENTORY_UNIT", formModal[el.name]!)
										?.subitemName || "---"
								}}
							</span>
							<span v-else-if="el.name == 'quality'">
								{{
									dictFilter("MAIN_MATERIALS", formModal[el.name]!)
										?.subitemName || "---"
								}}
							</span>
							<span v-else>
								{{
									formModal[el.name] || formModal[el.name] == 0
										? formModal[el.name]
										: "---"
								}}
							</span>
						</el-descriptions-item>
					</template>
				</el-descriptions>
			</div>
		</div>

		<!-- 右侧table区域 -->
		<div class="drawer-column right">
			<div class="rows">
				<Title :title="drawerRightTitle" />
				<el-scrollbar class="tab-mat">
					<PitayaTable
						ref="tableRef"
						:columns="tableProp"
						:table-data="tableData"
						:need-index="true"
						:need-pagination="true"
						:single-select="false"
						:need-selection="false"
						:total="pageTotal"
						@onSelectionChange="onDataSelected"
						@on-current-page-change="onCurrentPageChange"
						:table-loading="tableLoading"
					>
						<template #completeNum="{ rowData }">
							{{ toFixedTwo(rowData.completeNum) }}
						</template>

						<template #useUnit="{ rowData }">
							{{
								dictFilter("INVENTORY_UNIT", rowData.useUnit)?.subitemName ||
								"---"
							}}
						</template>
						<template #inStorePrice="{ rowData }">
							<cost-tag :value="rowData.inStorePrice" />
						</template>
					</PitayaTable>
				</el-scrollbar>
			</div>

			<ButtonList
				class="footer"
				:button="formBtnList"
				:loading="loading"
				@on-btn-click="emits('close')"
			/>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.drawer-container {
	.left {
		width: 340px;
	}

	.right {
		width: calc(100% - 340px);
	}

	.tab-mat {
		height: calc(100% - 43px);
	}
}
</style>
