<!-- 报废申请 主列表 -->
<script lang="ts" setup>
import {
	checkPermission,
	isCheckPermission
} from "@/app/platform/utils/permission"
import { powerList } from "../../components/define.d"
import { computed, onMounted, ref } from "vue"
import { useDictInit } from "../../components/dictBase"
import { useMessageBoxInit } from "../../components/messageBox"
import { useTbInit } from "../../components/tableBase"
import {
	BaseLineSysApi,
	getTaskByBusinessIds,
	listCompanyWithFormat
} from "@/app/baseline/api/system"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import { modalSize } from "@/app/baseline/utils/layout-config"
import { IModalType } from "../../../utils/types/common"
import { DictApi } from "@/app/baseline/api/dict"
import { appStatus } from "@/app/baseline/api/dict"
import {
	delWasteScrapApply,
	getWasteScrapApplyStatusCnt,
	listWasteScrapApplyPaged
} from "@/app/baseline/api/waste/scrap-apply"
import {
	MatWasteScrapApplyPageVo,
	MatWasteScrapApplyPageVoQuery
} from "@/app/baseline/utils/types/waste-scrap-apply"
import scrapApplyEditor from "./scrapApplyEditor.vue"
import scrapApplyDetail from "./scrapApplyDetail.vue"
import { LowValueApplyBpmStatusVo } from "@/app/baseline/utils/types/lowValue-requisition-apply"
import { toFixedTwo, hasPermi } from "@/app/baseline/utils"
import { omit } from "lodash-es"
import { useUserStore } from "@/app/platform/store/modules/user"
import ApprovedDrawer from "@/app/baseline/views/components/approvedDrawer.vue"

const { userInfo } = storeToRefs(useUserStore())

const { dictOptions, getDictByCodeList } = useDictInit()
const { showDelConfirm } = useMessageBoxInit()

/**
 * 公司列表
 */
const companyOptions = ref<any>([])

/**
 * 查询条件 配置
 */
const queryArrList = computed(() => {
	return [
		{
			name: "申请单号",
			key: "code",
			placeholder: "请输入申请单号",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "申请名称",
			key: "label",
			placeholder: "请输入申请名称",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "申请人",
			key: "createdBy",
			placeholder: "请输入申请人",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "废旧物资分类",
			key: "wasteMaterialType",
			placeholder: "请选择废旧物资分类",
			type: "select",
			children: dictOptions.value["WASTE_MATERIALS_TYPE"]
		},
		{
			name: "所属公司",
			key: "sysCommunityId",
			type: "select",
			children: companyOptions.value,
			placeholder: "请选择公司"
		},
		{
			name: "申请部门",
			key: "sysOrgId",
			type: "treeSelect",
			placeholder: "请选择",
			treeApi: () =>
				BaseLineSysApi.getDepartmentTreeWithCompanyDisabled(
					userInfo.value.companyId
				)
		}
	]
})

/**
 * Title 配置
 */
const rightTitle = {
	name: ["报废申请"],
	icon: ["fas", "square-share-nodes"]
}
const titleBtnConf = [
	{
		name: "新建报废申请",
		roles: powerList.wasteScrapApplyBtnCreate,
		icon: ["fas", "circle-plus"]
	}
]

/**
 * tab 配置项
 */
const statusCnt = ref<LowValueApplyBpmStatusVo>({})
const tabList = computed(() => [
	{
		name: "草稿箱",
		value: `${appStatus.pendingApproval},${appStatus.rejected}`,
		count: statusCnt.value[0] ?? 0
	},
	{
		name: "审批中",
		value: appStatus.underApproval,
		count: statusCnt.value[1] ?? 0
	},
	{
		name: "已审批",
		value: appStatus.approved,
		count: statusCnt.value[2] ?? 0
	}
])
const tabStatus = ref(tabList.value[0].value)
const activeName = ref(tabList.value[0].name)
const handleTabsClick = (tab: any) => {
	activeName.value = tab.paneName
	tabStatus.value = tabList.value[tab.index].value
	getTableData()
}

const {
	tableData,
	tableProp,
	tableRef,
	tableLoading,
	currentPage,
	pageTotal,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = useTbInit<MatWasteScrapApplyPageVo, MatWasteScrapApplyPageVoQuery>()

/**
 * table 列配置
 */
tableProp.value = [
	{ label: "申请单号", prop: "code", width: 180, fixed: "left" },
	{ label: "申请名称", prop: "label" },
	{
		label: "废旧物资分类",
		prop: "wasteMaterialType",
		needSlot: true,
		width: 120
	},
	{
		label: "预估回收总重量(kg)",
		prop: "recoveryWeight",
		width: 150,
		needSlot: true,
		align: "right"
	},
	{ label: "物资分类", prop: "materialTypeCnt" },
	{ label: "物资编码", prop: "materialCodeCnt" },
	{ label: "主要材质", prop: "qualityCnt" },
	{ label: "所属公司", prop: "sysCommunityId_view" },
	{ label: "申请部门", prop: "sysOrgId_view" },
	{ label: "审批状态", prop: "bpmStatus", needSlot: true },
	{ label: "申请人", prop: "createdBy_view" },
	{ label: "申请时间", prop: "createdDate", width: 150, sortable: true },
	{
		label: "操作",
		width: 200,
		prop: "operations",
		needSlot: true,
		fixed: "right"
	}
]
fetchFunc.value = listWasteScrapApplyPaged

/**
 * tab 切换数据源
 */
const getTableData = (data?: any) => {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		sidx: "createdDate",
		sord: "desc",
		...fetchParam.value,
		bpmStatus: tabStatus.value,
		...data
	}
	handleUpdate()
}

const curRowId = ref<any>("")
const curRowData = ref<MatWasteScrapApplyPageVo>({})
const viewVisible = ref<boolean>(false)
const editorVisible = ref(false)
const editorMode = ref(IModalType.edit)
const approvedVisible = ref(false)
const editTask = ref<Record<string, any>>()

/**
 * 新建 操作
 */
const handleClickAddBtn = () => {
	editorMode.value = IModalType.create
	editorVisible.value = true
	curRowId.value = ""
	curRowData.value = {}
}

/**
 * 编辑 操作
 * @param row
 */
const onRowEdit = (row: MatWasteScrapApplyPageVo) => {
	curRowId.value = row.id
	curRowData.value = row
	editorVisible.value = true
	editorMode.value = IModalType.edit
}

/**
 * 查看
 * @param row
 */
const onRowView = async (row: MatWasteScrapApplyPageVo) => {
	curRowId.value = row.id
	curRowData.value = row
	/* viewVisible.value = true */
	editorMode.value = IModalType.view

	if (row?.bpmStatus == appStatus.pendingApproval) {
		viewVisible.value = true
	} else {
		const res = await getTaskByBusinessIds({
			businessIds: [row?.id],
			camundaKey: "waste_scrap_apply"
		})

		if (Array.isArray(res) && res.length > 0) {
			editTask.value = { ...res[res.length - 1] }
			approvedVisible.value = true
		} else {
			viewVisible.value = true
		}
	}
}

/**
 * 删除 操作
 * @param row
 */
const onRowDel = async (row: MatWasteScrapApplyPageVo) => {
	await showDelConfirm()
	await delWasteScrapApply(row.id as number)
	ElMessage.success("操作成功")
	handleUpdate()
}

/**
 * 查看/新建/编辑  关闭 抽屉回调
 * @param msg save | false
 */
/* const handleCloseDrawer = (msg?: string) => {
	if (msg === "save") {
		fetchTableData()
		editorVisible.value = false
	} else {
		editorVisible.value = false
		viewVisible.value = false
	}
} */
// getWasteScrapApplyStatusCnt
/**
 * 更新主表/statusCnt
 */
const handleUpdate = async () => {
	fetchTableData()
	statusCnt.value = await getWasteScrapApplyStatusCnt(
		omit(
			{
				...fetchParam.value
			},
			"sidx",
			"sord",
			"bpmStatus",
			"currentPage",
			"pageSize"
		) as any
	)
}
onMounted(() => {
	listCompanyWithFormat().then((r) => (companyOptions.value = r))

	getDictByCodeList(["WASTE_MATERIALS_TYPE"])
	getTableData()
})

/**
 * 排序
 * @param column
 * @param prop 字段名称
 * @param order ascending 升序 descending 降序
 */
function handleSortChange(column: any, prop: any, order: any) {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		sord:
			order === "descending" ? "desc" : order === "ascending" ? "asc" : "desc",
		sidx: order ? prop : "createdDate" // 排序字段
	}

	fetchTableData()
}
</script>
<template>
	<div class="app-container">
		<div class="whole-frame">
			<ModelFrame class="top-frame">
				<Query
					:queryArrList="queryArrList"
					@getQueryData="getTableData"
					class="ml10"
				/>
			</ModelFrame>

			<ModelFrame class="bottom-frame">
				<Title
					:title="rightTitle"
					:button="(titleBtnConf as any)"
					@on-btn-click="handleClickAddBtn"
				>
					<div class="app-tabs-wrapper">
						<el-tabs v-model="activeName" @tab-click="handleTabsClick">
							<el-tab-pane
								v-for="(tab, index) in tabList"
								:key="index"
								:label="`${tab.name}（${tab.count ?? 0}）`"
								:name="tab.name"
								:index="tab.name"
							/>
						</el-tabs>
					</div>
				</Title>

				<pitaya-table
					ref="tableRef"
					:columns="tableProp"
					:tableData="tableData"
					:total="pageTotal"
					:need-index="true"
					@on-current-page-change="onCurrentPageChange"
					:table-loading="tableLoading"
					@on-table-sort-change="handleSortChange"
				>
					<!-- 预估回收总重量 recoveryWeight -->
					<template #recoveryWeight="{ rowData }">
						{{ toFixedTwo(rowData.recoveryWeight) }}
					</template>

					<!-- 废旧物资分类 -->
					<template #wasteMaterialType="{ rowData }">
						<dict-tag
							:options="DictApi.getWasteMaterialType()"
							:value="rowData.wasteMaterialType"
						/>
					</template>

					<!-- 审批状态 -->
					<template #bpmStatus="{ rowData }">
						<dict-tag
							:options="DictApi.getBpmStatus()"
							:value="rowData.bpmStatus"
						/>
					</template>

					<template #operations="{ rowData }">
						<slot
							v-if="
								(tabStatus == tabList[0].value &&
									isCheckPermission(powerList.wasteScrapApplyBtnEdit) &&
									hasPermi(rowData.createdBy)) ||
								isCheckPermission(powerList.wasteScrapApplyBtnPreview) ||
								(tabStatus == tabList[0].value &&
									isCheckPermission(powerList.wasteScrapApplyBtnDrop) &&
									hasPermi(rowData.createdBy))
							"
						>
							<el-button
								v-btn
								link
								@click="onRowEdit(rowData)"
								v-if="
									tabStatus == tabList[0].value &&
									isCheckPermission(powerList.wasteScrapApplyBtnEdit) &&
									hasPermi(rowData.createdBy)
								"
								:disabled="checkPermission(powerList.wasteScrapApplyBtnEdit)"
							>
								<font-awesome-icon :icon="['fas', 'pen-to-square']" />
								<span class="table-inner-btn">编辑</span>
							</el-button>
							<el-button
								v-btn
								link
								@click="onRowView(rowData)"
								v-if="isCheckPermission(powerList.wasteScrapApplyBtnPreview)"
								:disabled="checkPermission(powerList.wasteScrapApplyBtnPreview)"
							>
								<font-awesome-icon :icon="['fas', 'eye']" />
								<span class="table-inner-btn">查看</span>
							</el-button>
							<el-button
								v-btn
								link
								@click="onRowDel(rowData)"
								v-if="
									tabStatus == tabList[0].value &&
									isCheckPermission(powerList.wasteScrapApplyBtnDrop) &&
									hasPermi(rowData.createdBy)
								"
								:disabled="checkPermission(powerList.wasteScrapApplyBtnDrop)"
							>
								<font-awesome-icon :icon="['fas', 'trash-can']" />
								<span class="table-inner-btn">移除</span>
							</el-button>
						</slot>
						<slot v-else>---</slot>
					</template>
				</pitaya-table>

				<!-- 编辑/新建  -->
				<Drawer
					:size="modalSize.xl"
					v-model:drawer="editorVisible"
					:destroyOnClose="true"
				>
					<scrap-apply-editor
						:id="curRowId"
						:mode="editorMode"
						@close="editorVisible = false"
						@save="handleUpdate"
						@update="fetchTableData"
					/>
				</Drawer>

				<!-- 查看  -->
				<Drawer
					:size="modalSize.xl"
					v-model:drawer="viewVisible"
					:destroyOnClose="true"
				>
					<scrap-apply-detail :id="curRowId" @close="viewVisible = false" />
				</Drawer>

				<!-- 审批、查看弹窗 -->
				<Drawer
					:size="modalSize.xxl"
					v-model:drawer="approvedVisible"
					:destroyOnClose="true"
				>
					<approved-drawer
						:mod="editorMode"
						:task-value="editTask"
						@on-save-or-close="approvedVisible = false"
					/>
				</Drawer>
			</ModelFrame>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
