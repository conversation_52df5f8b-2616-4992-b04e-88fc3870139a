<!-- 仓库明细 - 查看物资明细 -->
<script setup lang="ts">
import { FormElementType } from "@/app/baseline/views/components/define"
import { reactive, ref } from "vue"
import { getNumDefByNumKey, toFixedTwo } from "@/app/baseline/utils"

import { useTbInit } from "../../components/tableBase"
import {
	WasteMaterialStoreVo,
	WasteStoreVo
} from "@/app/baseline/utils/types/waste-scrap-apply"
import { listShowMaterialByStoreIdAndMaterialIds } from "@/app/baseline/api/waste/scrap-apply"
import { useDictInit } from "../../components/dictBase"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import { DictApi } from "@/app/baseline/api/dict"

export interface Props {
	id: any
	row: WasteStoreVo
}

const { dictOptions, getDictByCodeList } = useDictInit()

const props = defineProps<Props>()
const emits = defineEmits(["close"])

const loading = ref(false)
const formBtnList = [{ name: "取消", icon: ["fas", "circle-minus"] }]

const formModal = computed<WasteStoreVo>(() => {
	return {
		...props.row,
		recoveryWeight_view: toFixedTwo(props.row.recoveryWeight)
	}
})

/**
 * 左侧 title 配置
 */
const drawerLeftTitle = {
	name: ["仓库信息"],
	icon: ["fas", "square-share-nodes"]
}

/**
 * 左侧 表单 配置
 */
const formEl = reactive<FormElementType[]>([
	{ label: "仓库编码", name: "storeCode" },
	{ label: "仓库名称", name: "storeLabel" },
	{ label: "所属段区", name: "depotId_view" },
	{ label: "仓库位置", name: "positionId" },
	{ label: "库管员", name: "storeManage" },
	{ label: "物资分类(项)", name: "materialTypeCnt" },
	{ label: "物资编码(个)", name: "materialIdCnt" },
	{ label: "报废总数量", name: "completeNum_view" },
	{ label: "预估回收总重量(kg)", name: "recoveryWeight_view" }
])

/**
 * 右侧 title 配置
 */
const drawerRightTitle = {
	name: ["物资明细"],
	icon: ["fas", "square-share-nodes"]
}

const queryConf = computed(() => {
	return [
		{
			name: "物资编码",
			key: "materialCode",
			type: "input",
			placeholder: "请输入物资编码"
		},
		{
			name: "物资名称",
			key: "materialLabel",
			type: "input",
			placeholder: "请输入物资名称"
		},
		{
			name: "规格型号",
			key: "version",
			placeholder: "请输入规格型号",
			type: "input"
		},
		{
			name: "物资性质",
			key: "attribute",
			type: "select",
			children: dictOptions.value.MATERIAL_NATURE,
			placeholder: "请选择物资性质"
		}
	]
})

const {
	tableData,
	tableProp,
	tableRef,
	tableLoading,
	currentPage,
	pageTotal,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange,
	onDataSelected
} = useTbInit<WasteMaterialStoreVo, Record<string, any>>()

/**
 * table 列 配置
 */
tableProp.value = [
	{ label: "物资编码", prop: "materialCode", fixed: "left", width: 130 },
	{ label: "物资名称", prop: "materialLabel" },
	{ label: "物资分类名称", prop: "materialTypeLabel" },
	{ label: "规格型号", prop: "version" },
	{
		prop: "attribute",
		label: "物资性质",
		needSlot: true,
		width: 120
	},
	{ label: "库存单位", prop: "useUnit_view", width: 90 },
	{ label: "主要材质", prop: "quality_view" },
	{
		label: "预估回收重量(KG)",
		prop: "recoveryWeight",
		width: 140,
		align: "right",
		needSlot: true
	},
	{ label: "报废数量", prop: "completeNum", needSlot: true, align: "right" }
]

/**
 * table 数据源
 * @param data
 */
const getTableData = (data?: any) => {
	fetchFunc.value = listShowMaterialByStoreIdAndMaterialIds

	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		storeId: formModal.value.storeId,
		scrapId: props.id,
		sord: "desc",
		sidx: "createdDate",
		...data
	}

	fetchTableData()
}

onMounted(async () => {
	await getDictByCodeList(["MATERIAL_NATURE"])
	getTableData()
})
</script>
<template>
	<div class="drawer-container">
		<!-- 左侧表单区域 -->
		<div class="drawer-column left">
			<div class="rows">
				<Title :title="drawerLeftTitle" />
				<el-descriptions size="small" :column="1" border class="content">
					<template>
						<el-descriptions-item
							v-for="el in formEl"
							label-align="center"
							:label="el.label"
							:key="el.name"
						>
							<span>
								{{ getNumDefByNumKey(el.name, formModal?.[el.name], /Cnt$/) }}
								<!-- {{ getNumDefByNumKey(desc.key, formData?.[desc.key]) }} -->
							</span>
						</el-descriptions-item>
					</template>
				</el-descriptions>
			</div>
		</div>

		<!-- 右侧table区域 -->
		<div class="drawer-column right">
			<div class="rows">
				<Title :title="drawerRightTitle" />
				<Query
					:query-arr-list="(queryConf as any)"
					style="margin: 10px 10px -10px"
					@get-query-data="getTableData"
				/>
				<el-scrollbar class="tab-mat">
					<PitayaTable
						ref="tableRef"
						:columns="tableProp"
						:table-data="tableData"
						:need-index="true"
						:need-pagination="true"
						:single-select="false"
						:need-selection="false"
						:total="pageTotal"
						@onSelectionChange="onDataSelected"
						@on-current-page-change="onCurrentPageChange"
						:table-loading="tableLoading"
					>
						<!-- 物资性质 -->
						<template #attribute="{ rowData }">
							<dict-tag
								:options="DictApi.getMatAttr()"
								:value="rowData.attribute"
							/>
						</template>

						<template #recoveryWeight="{ rowData }">
							{{ toFixedTwo(rowData.recoveryWeight) }}
						</template>

						<template #completeNum="{ rowData }">
							{{ toFixedTwo(rowData.completeNum) }}
						</template>
					</PitayaTable>
				</el-scrollbar>
			</div>

			<ButtonList
				class="footer"
				:button="formBtnList"
				:loading="loading"
				@on-btn-click="emits('close')"
			/>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.drawer-container {
	.left {
		width: 360px;
	}

	.right {
		width: calc(100% - 360px);
	}

	.tab-mat {
		height: calc(100% - 80px);
	}
}
</style>
