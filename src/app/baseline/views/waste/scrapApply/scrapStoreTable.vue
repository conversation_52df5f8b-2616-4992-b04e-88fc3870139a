<!-- 仓库明细 table -->
<template>
	<pitaya-table
		ref="tableRef"
		:columns="tableProp"
		:table-data="tableData"
		:need-index="true"
		:single-select="false"
		:need-pagination="true"
		:total="pageTotal"
		:table-loading="tableLoading"
		@on-selection-change="onDataSelected"
		@on-current-page-change="onCurrentPageChange"
	>
		<!-- 预估回收总重量 recoveryWeight -->
		<template #recoveryWeight="{ rowData }">
			{{ toFixedTwo(rowData.recoveryWeight) }}
		</template>
		<template #actions="{ rowData }">
			<slot>
				<el-button v-btn link @click="showStoreMatDetail(rowData)">
					<font-awesome-icon :icon="['fas', 'eye']" />
					<span class="table-inner-btn">查看物资明细</span>
				</el-button>
			</slot>
		</template>
	</pitaya-table>

	<!-- 仓库明细 - 查看物资明细 -->
	<Drawer v-model:drawer="detailVisible" :size="modalSize.lg" destroy-on-close>
		<scrap-store-table-detail
			:row="editingTableRow"
			:id="id"
			@close="detailVisible = false"
		/>
	</Drawer>
</template>

<script setup lang="ts">
import { useTbInit } from "../../components/tableBase"
import { modalSize } from "@/app/baseline/utils/layout-config"
import scrapStoreTableDetail from "./scrapStoreTableDetail.vue"
import { listShowStoreByMaterialIds } from "@/app/baseline/api/waste/scrap-apply"
import { WasteStoreVo } from "@/app/baseline/utils/types/waste-scrap-apply"
import { batchFormatterNumView, toFixedTwo } from "@/app/baseline/utils"

const props = defineProps<{ id: any }>()

const editingTableRow = ref()

const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange,
	onDataSelected
} = useTbInit<WasteStoreVo, Record<string, any>>()

fetchFunc.value = listShowStoreByMaterialIds

tableProp.value = [
	{ prop: "storeCode", label: "仓库编码", width: 100, fixed: "left" },
	{ prop: "storeLabel", label: "仓库名称" },
	{ prop: "depotId_view", label: "所属段区" },
	{ prop: "positionId", label: "仓库位置" },
	{ prop: "storeManage", label: "库管员" },
	{ prop: "materialTypeCnt", label: "物资分类" },
	{ prop: "materialIdCnt", label: "物资编码" },
	{ prop: "completeNum_view", label: "报废总数量", align: "right" },
	{
		prop: "recoveryWeight",
		label: "预估回收总重量(kg)",
		needSlot: true,
		align: "right",
		width: 140
	},
	{ prop: "actions", label: "操作", needSlot: true, fixed: "right", width: 130 }
]

watch(tableData, () => {
	batchFormatterNumView(tableData.value)
})

const detailVisible = ref(false)

onMounted(() => {
	fetchParam.value = {
		scrapId: props.id,
		sord: "asc",
		sidx: "code",
		...fetchParam.value
	}
	fetchTableData()
})

function showStoreMatDetail(e: any) {
	editingTableRow.value = { ...e }
	detailVisible.value = true
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
