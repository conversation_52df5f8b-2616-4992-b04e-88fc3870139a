<!-- 废旧 - 厂商管理 -->
<script lang="ts" setup>
import {
	checkPermission,
	isCheckPermission
} from "@/app/platform/utils/permission"
import { powerList } from "../../components/define.d"
import { computed, onMounted, ref } from "vue"
import { useTbInit } from "../../components/tableBase"
import { modalSize } from "@/app/baseline/utils/layout-config"
import { IModalType } from "../../../utils/types/common"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import {
	delWasteRepairCompany,
	listWasteRepairCompanyPaged
} from "@/app/baseline/api/waste/supplier"
import {
	MatWasteRepairCompanyVo,
	MatWasteRepairCompanyRequest
} from "@/app/baseline/utils/types/waste-supplier"
import { DictApi, appStatus } from "@/app/baseline/api/dict"
import supplierDetail from "./supplierDetail.vue"
import supplierEditor from "./supplierEditor.vue"
import { useMessageBoxInit } from "../../components/messageBox"
import { hasPermi } from "@/app/baseline/utils"

const { showDelConfirm } = useMessageBoxInit()
/**
 * 查询条件 配置
 */
const queryArrList = computed(() => [
	{
		name: "维修公司名称",
		key: "label",
		type: "input",
		placeholder: "请输入维修公司名称"
	},
	{
		name: "联系人",
		key: "contact",
		type: "input",
		placeholder: "请输入联系人"
	},
	{
		name: "审批状态",
		key: "bpmStatus",
		type: "select",
		placeholder: "请选择",
		children: DictApi.getQueryBpmStatus()
	}
])

/**
 * Title 配置
 */
const rightTitle = {
	name: ["厂商管理"],
	icon: ["fas", "square-share-nodes"]
}

const titleBtnConf = [
	{
		name: "新建维修公司",
		roles: powerList.wasteSupplierBtnCreate,
		icon: ["fas", "circle-plus"]
	}
]

const {
	tableData,
	tableProp,
	tableRef,
	tableLoading,
	currentPage,
	pageTotal,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = useTbInit<MatWasteRepairCompanyVo, MatWasteRepairCompanyRequest>()

/**
 * table 列配置
 */
tableProp.value = [
	{ label: "维修公司名称", prop: "label", minWidth: 130, fixed: "left" },
	{ label: "法定代表人", prop: "legalRepresentative" },
	{ label: "联系人", prop: "contact" },
	{ label: "联系人手机号", prop: "contactPhoneNumber" },
	{ label: "联系地址", prop: "contactAddress" },
	{ label: "电子邮箱", prop: "contactEmail", minWidth: 120 },
	{ label: "审批状态", prop: "bpmStatus", needSlot: true, width: 120 },
	{ label: "创建人", prop: "createdBy_view" },
	{ label: "创建时间", prop: "createdDate", sortable: true },
	{
		label: "操作",
		width: 200,
		prop: "operations",
		needSlot: true,
		fixed: "right"
	}
]

fetchFunc.value = listWasteRepairCompanyPaged

const getTableData = (data?: any) => {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		...data
	}
	fetchTableData()
}

const curRowId = ref<any>("")
const curRowData = ref<MatWasteRepairCompanyVo>({})
const editorVisible = ref<boolean>(false)
const viewVisible = ref<boolean>(false)
const editorMode = ref(IModalType.edit)

/**
 * 新建 操作
 */
const handleClickAddBtn = () => {
	editorMode.value = IModalType.create
	editorVisible.value = true
	curRowId.value = ""
	curRowData.value = {}
}

const onRowEdit = (row: MatWasteRepairCompanyVo) => {
	curRowId.value = row.id
	curRowData.value = row
	editorVisible.value = true
	editorMode.value = IModalType.edit
}
/**
 * 查看
 * @param row
 */
const onRowView = (row: MatWasteRepairCompanyVo) => {
	curRowId.value = row.id
	curRowData.value = row
	viewVisible.value = true
	editorMode.value = IModalType.view
}

const onRowDel = async (row: MatWasteRepairCompanyVo) => {
	await showDelConfirm()
	await delWasteRepairCompany(row.id!)
	ElMessage.success("操作成功")
	fetchTableData()
}

/**
 * 排序
 * @param column
 * @param prop 字段名称
 * @param order ascending 升序 descending 降序
 */
function handleSortChange(column: any, prop: any, order: any) {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		sord:
			order === "descending" ? "desc" : order === "ascending" ? "asc" : "desc",
		sidx: order ? prop : "createdDate" // 排序字段
	}

	fetchTableData()
}

onMounted(() => {
	fetchParam.value.sord = "desc"
	fetchParam.value.sidx = "createdDate"

	getTableData()
})
</script>
<template>
	<div class="app-container">
		<div class="whole-frame">
			<ModelFrame class="top-frame">
				<Query
					:queryArrList="queryArrList"
					@getQueryData="getTableData"
					class="ml10"
				/>
			</ModelFrame>

			<ModelFrame class="bottom-frame">
				<Title
					:title="rightTitle"
					:button="(titleBtnConf as any)"
					@on-btn-click="handleClickAddBtn"
				/>

				<pitaya-table
					ref="tableRef"
					:columns="tableProp"
					:tableData="tableData"
					:total="pageTotal"
					:need-index="true"
					@on-current-page-change="onCurrentPageChange"
					:table-loading="tableLoading"
					@on-table-sort-change="handleSortChange"
				>
					<!-- 审批状态 -->
					<template #bpmStatus="{ rowData }">
						<dict-tag
							:options="DictApi.getBpmStatus()"
							:value="rowData.bpmStatus"
						/>
					</template>
					<template #operations="{ rowData }">
						<slot
							v-if="
								([appStatus.pendingApproval, appStatus.rejected].includes(
									rowData.bpmStatus
								) &&
									isCheckPermission(powerList.wasteSupplierBtnEdit) &&
									hasPermi(rowData.createdBy)) ||
								isCheckPermission(powerList.wasteSupplierBtnPreview) ||
								([appStatus.pendingApproval, appStatus.rejected].includes(
									rowData.bpmStatus
								) &&
									isCheckPermission(powerList.wasteSupplierBtnDrop) &&
									hasPermi(rowData.createdBy))
							"
						>
							<el-button
								v-btn
								link
								@click="onRowEdit(rowData)"
								v-if="
									[appStatus.pendingApproval, appStatus.rejected].includes(
										rowData.bpmStatus
									) &&
									isCheckPermission(powerList.wasteSupplierBtnEdit) &&
									hasPermi(rowData.createdBy)
								"
								:disabled="checkPermission(powerList.wasteSupplierBtnEdit)"
							>
								<font-awesome-icon :icon="['fas', 'pen-to-square']" />
								<span class="table-inner-btn">编辑</span>
							</el-button>
							<el-button
								v-btn
								link
								@click="onRowView(rowData)"
								v-if="isCheckPermission(powerList.wasteSupplierBtnPreview)"
								:disabled="checkPermission(powerList.wasteSupplierBtnPreview)"
							>
								<font-awesome-icon :icon="['fas', 'eye']" />
								<span class="table-inner-btn">查看</span>
							</el-button>
							<el-button
								v-btn
								link
								@click="onRowDel(rowData)"
								v-if="
									[appStatus.pendingApproval, appStatus.rejected].includes(
										rowData.bpmStatus
									) &&
									isCheckPermission(powerList.wasteSupplierBtnDrop) &&
									hasPermi(rowData.createdBy)
								"
								:disabled="checkPermission(powerList.wasteSupplierBtnDrop)"
							>
								<font-awesome-icon :icon="['fas', 'trash-can']" />
								<span class="table-inner-btn">移除</span>
							</el-button>
						</slot>
						<slot v-else>---</slot>
					</template>
				</pitaya-table>

				<!-- 新建/编辑  -->
				<Drawer
					:size="modalSize.sm"
					v-model:drawer="editorVisible"
					:destroyOnClose="true"
				>
					<supplier-editor
						:mode="editorMode"
						:id="curRowId"
						@close="editorVisible = false"
						@update="fetchTableData"
					/>
				</Drawer>

				<!-- 查看  -->
				<Drawer
					:size="modalSize.sm"
					v-model:drawer="viewVisible"
					:destroyOnClose="true"
				>
					<supplier-detail
						:row="curRowData"
						:id="curRowId"
						@close="viewVisible = false"
					/>
				</Drawer>
			</ModelFrame>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
