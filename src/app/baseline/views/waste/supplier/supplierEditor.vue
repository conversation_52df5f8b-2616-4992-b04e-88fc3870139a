<!-- 废旧 - 厂商管理 新建/编辑 -->
<script setup lang="ts">
import { ElMessage, FormInstance, FormRules } from "element-plus"
import {
	IIdempotentTokenType,
	IIdempotentTokenTypePre,
	IModalType
} from "../../../utils/types/common"
import { onMounted, reactive, ref } from "vue"
import { getModalTypeLabel } from "@/app/baseline/utils"

import formElement from "../../components/formElement.vue"
import { inputMaxLength } from "@/app/baseline/utils/layout-config"
import {
	getIdempotentToken,
	validateEmail,
	validatePhone
} from "../../../utils/validate"
import {
	addWasteRepairCompany,
	getWasteRepairCompanyDetail,
	submitWasteRepairCompany,
	updateWasteRepairCompany
} from "@/app/baseline/api/waste/supplier"
import { MatWasteRepairCompanyVo } from "@/app/baseline/utils/types/waste-supplier"
import { useMessageBoxInit } from "../../components/messageBox"

const props = defineProps<{
	id?: any // id
	mode: IModalType // create || edit || create
}>()

const emits = defineEmits<{
	(e: "update"): void
	(e: "close"): void
}>()

const { showWarnConfirm } = useMessageBoxInit()

const loading = ref(false)

const drawerLoading = ref(false)

const canEditExtra = computed(() => Boolean(formData.value?.id || props.id))

const formRef = ref<FormInstance>()

/**
 * 按钮 配置
 */
const formBtnListEdit = computed(() => [
	/* { name: "取消", icon: ["fas", "circle-minus"] }, */
	{ name: "保存草稿", icon: ["fas", "floppy-disk"] },
	{
		name: "提交审核",
		icon: ["fas", "circle-check"],
		disabled: !canEditExtra.value
	}
])

/**
 * 表单数据源
 */
const formData = ref<MatWasteRepairCompanyVo>({})

/**
 * 保存旧的表单数据
 */
const oldFormData = ref<string>()

/**
 * 左侧title 配置
 */
const titleConf = computed(() => ({
	name: [
		getModalTypeLabel(
			canEditExtra.value ? IModalType.edit : IModalType.create,
			"维修公司"
		)
	],
	icon: ["fas", "square-share-nodes"]
}))

/**
 *表 单按钮 操作
 * @param btnName
 */
const onFormBtnList = async (btnName: string | undefined) => {
	if (btnName === "提交审核") {
		if (!formRef.value) {
			return
		}
		formRef.value?.validate(async (valid) => {
			if (!valid) {
				return
			}
			await showWarnConfirm("请确认是否提交本次数据？")
			loading.value = true
			drawerLoading.value = true
			try {
				// 如果主表有修改，则先更新主表数据
				if (oldFormData.value != JSON.stringify(formData.value)) {
					const res = await updateWasteRepairCompany(formData.value)
					formData.value.id = res.id
				}

				const idempotentToken = getIdempotentToken(
					IIdempotentTokenTypePre.other,
					IIdempotentTokenType.wasteRepairApply,
					formData.value.id
				)

				await submitWasteRepairCompany(
					props.id || formData.value.id,
					idempotentToken
				)
				ElMessage.success("操作成功")
				emits("update")
				emits("close")
			} finally {
				loading.value = false
				drawerLoading.value = false
			}
		})
	} else if (btnName === "保存草稿") {
		if (!formRef.value) {
			return
		}
		formRef.value?.validate(async (valid) => {
			if (!valid) {
				return
			}
			loading.value = true
			try {
				const api = canEditExtra.value
					? updateWasteRepairCompany
					: addWasteRepairCompany

				let idempotentToken = ""
				if (!canEditExtra.value) {
					idempotentToken = getIdempotentToken(
						IIdempotentTokenTypePre.apply,
						IIdempotentTokenType.wasteSupplier
					)
				}

				const res = await api(formData.value, idempotentToken)
				formData.value.id = res.id

				oldFormData.value = JSON.stringify(formData.value)

				ElMessage.success("操作成功")
				emits("update")
			} finally {
				loading.value = false
			}
		})
	}
}

/**
 * 表单配置
 */
const formElBase = computed(() => {
	return [
		[{ label: "维修公司名称", name: "label", maxlength: inputMaxLength.input }],
		[
			{
				label: "法定代表人",
				name: "legalRepresentative",
				maxlength: inputMaxLength.input
			}
		],
		[{ label: "联系人", name: "contact", maxlength: inputMaxLength.input }],
		[
			{
				label: "联系人手机号",
				name: "contactPhoneNumber",
				maxlength: 11
			}
		],
		[
			{
				label: "联系地址",
				name: "contactAddress",
				maxlength: inputMaxLength.input
			}
		],
		[
			{
				label: "电子邮箱",
				name: "contactEmail",
				maxlength: inputMaxLength.input
			}
		],
		[
			{
				label: "备注说明",
				name: "remark",
				type: "textarea",
				rows: "5",
				maxlength: inputMaxLength.textarea
			}
		]
	]
})

/**
 * 表单校验
 */
const formRules = reactive<FormRules<typeof formData>>({
	label: [
		{ required: true, message: "维修公司名称不能为空", trigger: "change" }
	],
	legalRepresentative: [
		{ required: true, message: "法定代表人不能为空", trigger: "change" }
	],
	contact: [{ required: true, message: "联系人不能为空", trigger: "change" }],
	contactPhoneNumber: [
		{ required: true, message: "联系人手机号不能为空", trigger: "change" },
		{ validator: validatePhone, required: true, trigger: "change" }
	],
	contactEmail: [
		{ required: true, message: "电子邮箱不能为空", trigger: "change" },
		{ validator: validateEmail, required: true, trigger: "change" }
	]
})

/**
 * 获取详情
 */
const getDetail = () => {
	if (props.id) {
		drawerLoading.value = true
		getWasteRepairCompanyDetail(props.id as number)
			.then((res: any) => {
				formData.value = { ...res }
				oldFormData.value = JSON.stringify(formData.value)
			})
			.finally(() => {
				drawerLoading.value = false
			})
	}
}

onMounted(async () => {
	getDetail()
})
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<!-- 左侧表单区域 -->
		<div class="drawer-column" style="width: 100%">
			<Title :title="titleConf" />
			<el-scrollbar class="rows">
				<el-form
					class="content"
					:model="formData"
					ref="formRef"
					label-position="top"
					label-width="100px"
					:rules="formRules"
				>
					<form-element :form-element="formElBase" :form-data="formData" />
				</el-form>
			</el-scrollbar>
			<ButtonList
				class="footer"
				:button="formBtnListEdit"
				:loading="loading"
				@on-btn-click="onFormBtnList"
			/>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
