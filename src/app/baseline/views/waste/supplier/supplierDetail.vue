<!-- 废旧 - 厂商管理 查看详情 -->
<script setup lang="ts">
import { onMounted, reactive, ref } from "vue"

import { MatWasteRepairCompanyVo } from "@/app/baseline/utils/types/waste-supplier"
import { getWasteRepairCompanyDetail } from "@/app/baseline/api/waste/supplier"
import { getRealLength, setString } from "@/app/baseline/utils/validate"

export interface Props {
	id: string | number //
	footerBtnVisible?: boolean
}

const props = withDefaults(defineProps<Props>(), { footerBtnVisible: true })
const emits = defineEmits<{
	(e: "update"): void
	(e: "close"): void
}>()
const loading = ref(false)

const drawerLoading = ref(false)

/**
 * 按钮 配置
 */
const formBtnListEdit = [{ name: "取消", icon: ["fas", "circle-minus"] }]

/**
 * 表单数据源
 */
const formModal = ref<MatWasteRepairCompanyVo>({})

/**
 * 左侧title 配置
 */
const titleConf = {
	name: ["查看维修公司"],
	icon: ["fas", "square-share-nodes"]
}

/**
 * 左侧表单 配置
 */
const formEl = reactive([
	{ label: "维修公司名称", name: "label" },
	{ label: "法定代表人", name: "legalRepresentative" },
	{ label: "联系人", name: "contact" },
	{ label: "联系人手机号", name: "contactPhoneNumber" },
	{ label: "联系地址", name: "contactAddress" },
	{ label: "电子邮箱", name: "contactEmail" },
	{ label: "备注说明", name: "remark", needTooltip: true }
])

const getDetail = () => {
	if (props.id) {
		drawerLoading.value = true
		getWasteRepairCompanyDetail(props.id as number)
			.then((res: any) => {
				formModal.value = { ...res }
			})
			.finally(() => {
				drawerLoading.value = false
			})
	}
}

onMounted(async () => {
	getDetail()
})
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<!-- 左侧表单区域 -->
		<div
			class="drawer-column"
			style="width: 100%"
			:class="{ pdr10: !footerBtnVisible }"
		>
			<Title :title="titleConf" />
			<el-scrollbar class="rows">
				<el-descriptions size="small" :column="1" border class="content">
					<template>
						<el-descriptions-item
							v-for="(el, index) in formEl"
							label-align="center"
							:label="el.label"
							:key="index"
						>
							<span v-if="el.needTooltip">
								<el-tooltip
									effect="dark"
									:content="formModal?.[el.name]"
									:disabled="
										getRealLength(formModal?.[el.name]) <= 100 ? true : false
									"
								>
									{{
										getRealLength(formModal?.[el.name]) > 100
											? setString(formModal?.[el.name], 100)
											: formModal?.[el.name] || "---"
									}}
								</el-tooltip>
							</span>
							<span v-else>
								{{
									formModal[el.name] || formModal[el.name] == 0
										? formModal[el.name]
										: "---"
								}}
							</span>
						</el-descriptions-item>
					</template>
				</el-descriptions>
			</el-scrollbar>
			<ButtonList
				v-if="footerBtnVisible"
				class="footer"
				:button="formBtnListEdit"
				:loading="loading"
				@on-btn-click="emits('close')"
			/>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
