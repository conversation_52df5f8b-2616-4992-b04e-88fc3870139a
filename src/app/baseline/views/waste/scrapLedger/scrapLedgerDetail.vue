<!-- 低值台账 详情 -->
<script setup lang="ts">
import { computed, onMounted, ref } from "vue"
import {
	batchFormatterNumView,
	getNumDefByNumKey,
	toFixedTwo
} from "@/app/baseline/utils"
import { useDictInit } from "../../components/dictBase"
import { DictApi } from "@/app/baseline/api/dict"
import GridPanel from "../../store/components/gridPanel.vue"
import { listWasteScrapBookDetail } from "@/app/baseline/api/waste/scrap-ledger"
import { ScrapBookDetailVo } from "@/app/baseline/utils/types/waste-scrap-ledger"
import dictTag from "@/app/baseline/views/components/dictTag.vue"
import { getRealLength, setString } from "@/app/baseline/utils/validate"

const { dictFilter, getDictByCodeList } = useDictInit()

const props = withDefaults(
	defineProps<{
		id: any // 申请id
		footerBtnVisible?: boolean
	}>(),
	{ footerBtnVisible: true }
)

const emits = defineEmits<{
	(e: "close"): void
}>()

const drawerLoading = ref(false)

const selectedTabIdx = ref(0)

/**
 * 左侧title 配置
 */
const leftTitleConf = computed(() => ({
	name: ["废旧物资信息"],
	icon: ["fas", "square-share-nodes"]
}))

const drawerBtnRightConf = computed(() => [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	}
])

/**
 * 右侧 title 配置
 */
const extraTitleConf = {
	name: ["扩展信息"],
	icon: ["fas", "square-share-nodes"]
}
const tabsConf = ["库存查询", "交旧记录", "报废记录", "在库时长"]

const formData = ref<ScrapBookDetailVo>({})

/**
 * 当前库存 & 已交旧数量 & 已处置数量
 */
const gridPanelOptions = computed(() => {
	return [
		{
			label: "当前库存",
			value: parseInt(formData.value.storeNum as any) || 0
		},
		{
			label: "已交旧数量",
			value: parseInt(formData.value.completeWasteOldNum as any) || 0
		},
		{
			label: "已处置数量",
			value: parseInt(formData.value.completeScrapNum as any) || 0
		}
	]
})

/**
 * 关联业务单数据
 */
const descList = ref<Record<string, any>[]>([
	{ label: "物资编码", key: "materialCode" },
	{ label: "物资名称", key: "materialLabel" },
	{ label: "分类编码", key: "materialTypeCode" },
	{ label: "分类名称", key: "materialTypeName" },
	{ label: "规格型号", key: "version" },
	{ label: "技术参数", key: "technicalParameter", needTooltip: true },
	{ label: "物资性质", key: "attribute" }, // 字典
	{ label: "库存单位", key: "useUnit" }, // 字典

	{ label: "主要材质", key: "quality" }, // 字典
	{ label: "辅助材质", key: "auxiliaryQuality_view" },
	{ label: "废旧物资分类", key: "wasteMaterialType" }, // 字典
	{ label: "预估回收重量(kg)", key: "recoveryWeight_view" },
	{ label: "当前库存", key: "storeNum_view" },
	{ label: "已交旧数量", key: "completeWasteOldNum_view" },
	{ label: "已处置数量", key: "completeScrapNum_view" }
])

/**
 * 表格组件
 */
const tableComponent = computed(() => {
	const els = [
		// 库存查询
		defineAsyncComponent(() => import("./inventoryTable.vue")),
		// 交旧记录
		defineAsyncComponent(() => import("./wasteHandoverTable.vue")),
		// 报废记录
		defineAsyncComponent(() => import("./wasteScrapTable.vue")),
		// 在库时长
		defineAsyncComponent(() => import("./inventoryAge.vue"))
	]

	return els[selectedTabIdx.value]
})

onMounted(async () => {
	getDictByCodeList(["INVENTORY_UNIT", "MAIN_MATERIALS"])
	if (props.id) {
		drawerLoading.value = true
		try {
			const r = await listWasteScrapBookDetail(props.id)
			formData.value = { ...r }
			/**
			 * 格式化数量
			 */
			batchFormatterNumView([formData.value])

			formData.value.recoveryWeight_view = toFixedTwo(r.recoveryWeight)
		} finally {
			drawerLoading.value = false
		}
	}
})
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<div class="drawer-column left" style="width: 460px">
			<Title :title="leftTitleConf" />

			<el-scrollbar>
				<grid-panel :options="gridPanelOptions" />
				<el-descriptions size="small" :column="1" border class="content">
					<template>
						<el-descriptions-item
							v-for="desc in descList"
							:key="desc.key"
							:label="desc.label"
						>
							<span v-if="desc.key === 'useUnit'">
								{{
									dictFilter("INVENTORY_UNIT", formData?.[desc.key!])
										?.subitemName || "---"
								}}
							</span>
							<span v-else-if="desc.key === 'quality'">
								{{
									dictFilter("MAIN_MATERIALS", formData?.[desc.key])
										?.subitemName || "---"
								}}
							</span>
							<span v-else-if="desc.key === 'wasteMaterialType'">
								<dict-tag
									:options="DictApi.getWasteMaterialType()"
									:value="formData?.[desc.key]"
								/>
							</span>
							<dict-tag
								v-else-if="desc.key === 'attribute'"
								:options="DictApi.getMatAttr()"
								:value="formData.attribute"
							/>
							<span v-else-if="desc.needTooltip">
								<el-tooltip
									effect="dark"
									:content="formData?.[desc.key]"
									:disabled="
										getRealLength(formData?.[desc.key]) <= 100 ? true : false
									"
								>
									{{
										getRealLength(formData?.[desc.key]) > 100
											? setString(formData?.[desc.key], 100)
											: formData?.[desc.key] || "---"
									}}
								</el-tooltip>
							</span>
							<span v-else>
								{{ getNumDefByNumKey(desc.key, formData?.[desc.key]) }}
							</span>
						</el-descriptions-item>
					</template>
				</el-descriptions>
			</el-scrollbar>
		</div>
		<div class="drawer-column right" style="width: calc(100% - 460px)">
			<Title :title="extraTitleConf">
				<Tabs
					style="margin-right: auto; margin-left: 30px"
					:tabs="tabsConf"
					@on-tab-change="selectedTabIdx = $event"
				/>
			</Title>
			<el-scrollbar class="rows">
				<component :is="tableComponent" :id="id" />
			</el-scrollbar>
			<button-list
				v-if="footerBtnVisible"
				class="footer"
				:button="drawerBtnRightConf"
				@on-btn-click="emits('close')"
			/>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
