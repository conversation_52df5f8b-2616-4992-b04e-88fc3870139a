<!-- 报废 table -->
<template>
	<div>
		<pitaya-table
			ref="tableRef"
			:columns="tableProp"
			:table-data="tableData"
			:need-index="true"
			:single-select="false"
			:need-pagination="true"
			:total="pageTotal"
			:table-loading="tableLoading"
			:max-height="maxTableHeight"
			@on-selection-change="onDataSelected"
			@on-current-page-change="onCurrentPageChange"
			@on-table-sort-change="handleSortChange"
		/>
	</div>
</template>

<script setup lang="ts">
import { useTbInit } from "../../components/tableBase"
import { listWasteScrapBookScrapPaged } from "@/app/baseline/api/waste/scrap-ledger"
import {
	ScrapBookItemRequest,
	ScrapBookScrapRecordPageVo
} from "@/app/baseline/utils/types/waste-scrap-ledger"
import { batchFormatterNumView, maxTableHeight } from "@/app/baseline/utils"

const props = defineProps<{ id: any }>()

const {
	currentPage,
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange,
	onDataSelected
} = useTbInit<ScrapBookScrapRecordPageVo, ScrapBookItemRequest>()

fetchFunc.value = listWasteScrapBookScrapPaged

tableProp.value = [
	{
		prop: "code",
		label: "申请单号",
		width: 180,
		fixed: "left"
	},
	{ prop: "label", label: "报废业务名称" },
	{ prop: "scrapNum_view", align: "right", label: "报废数量" },
	{ prop: "sysOrgId_view", label: "申请部门" },
	{ prop: "createdBy_view", label: "申请人" },
	{ prop: "createdDate", label: "申请时间", width: 160, sortable: true }
]
/**
 * 格式化数量
 */
watch(tableData, () => {
	batchFormatterNumView(tableData.value)
})

/**
 * 排序
 * @param column
 * @param prop 字段名称
 * @param order ascending 升序 descending 降序
 */
function handleSortChange(column: any, prop: any, order: any) {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		sord:
			order === "descending" ? "desc" : order === "ascending" ? "asc" : "desc",
		sidx: order ? prop : "createdDate" // 排序字段
	}

	fetchTableData()
}

onMounted(() => {
	fetchParam.value = {
		sord: "desc",
		sidx: "createdDate",
		bookId: props.id,
		...fetchParam.value
	}
	fetchTableData()
})
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
