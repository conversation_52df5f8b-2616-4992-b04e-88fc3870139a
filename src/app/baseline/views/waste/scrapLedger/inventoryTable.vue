<!-- 库存查询 table -->
<template>
	<pitaya-table
		ref="tableRef"
		:columns="tableProp"
		:table-data="tableData"
		:need-index="true"
		:single-select="false"
		:need-pagination="true"
		:total="pageTotal"
		:table-loading="tableLoading"
		:max-height="maxTableHeight"
		@on-selection-change="selectedTableList = $event"
		@on-current-page-change="onCurrentPageChange"
	/>
</template>

<script setup lang="ts">
import { useTbInit } from "../../components/tableBase"
import { listWasteBookStorePaged } from "@/app/baseline/api/waste/scrap-ledger"
import {
	ScrapBookItemRequest,
	ScrapBookStorePageVo
} from "@/app/baseline/utils/types/waste-scrap-ledger"
import { batchFormatterNumView, maxTableHeight } from "@/app/baseline/utils"

const props = defineProps<{ id: any }>()

const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = useTbInit<ScrapBookStorePageVo, ScrapBookItemRequest>()

fetchFunc.value = listWasteBookStorePaged

tableProp.value = [
	{ prop: "storeCode", label: "仓库编码", fixed: "left" },
	{ prop: "storeName", label: "仓库名称" },
	{ prop: "num_view", align: "right", label: "库存数量" },
	{ prop: "sysCommunityId_view", label: "所属公司" }, // TODO
	{ prop: "depotId_view", label: "所属段区" },
	{ prop: "costCenterId_view", label: "成本中心" },
	{ prop: "positionId", label: "仓库位置" },
	{ prop: "storeManage", label: "库管员" }
]

/**
 * 格式化数量
 */
watch(tableData, () => {
	batchFormatterNumView(tableData.value)
})

onMounted(async () => {
	fetchParam.value = {
		bookId: props.id,
		...fetchParam.value
	}
	fetchTableData()
})
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
