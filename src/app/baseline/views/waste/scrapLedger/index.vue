<!-- 废旧-废旧物资台账 查询 -->
<template>
	<div class="app-container">
		<!-- 左侧 tree 筛选 -->
		<model-frame class="left-frame">
			<div class="drawer-container">
				<div class="drawer-column" style="width: 100%">
					<div class="rows" style="padding-bottom: 20px">
						<Title :title="titleConf" />
						<pitaya-lazy-tree-new
							ref="treeRef"
							v-model:tree-biz-id="treeSelectId"
							v-loading="treeLoading"
							:tree-data="treeData"
							:tree-props="{
								label: 'fullLabel',
								children: 'children',
								isLeaf: 'isLeaf'
							}"
							:default-expanded-keys="defaultExpandedKeys"
							:lazy="changeLazy"
							:need-search="true"
							:expand-on-click-node="false"
							node-key="id"
							:loadTreeChildren="loadTreeChildren"
							@on-tree-change="handleTreeCheck"
							:on-tree-search="onLocTreeSearch"
						/>
					</div>
				</div>
			</div>
		</model-frame>

		<div class="right-frame">
			<model-frame class="top-frame">
				<Query
					:query-arr-list="queryConf"
					@get-query-data="handleQuery"
					class="ml10"
				/>
			</model-frame>
			<model-frame class="bottom-frame">
				<Title :title="tableTitleConf" />
				<pitaya-table
					ref="tableRef"
					:columns="tableProp"
					:table-data="tableData"
					:need-selection="false"
					:single-select="false"
					:need-index="true"
					:need-pagination="true"
					:total="pageTotal"
					:table-loading="tableLoading"
					@on-selection-change="selectedTableList = $event"
					@on-current-page-change="onCurrentPageChange"
					@on-table-sort-change="handleSortChange"
				>
					<!-- 物资性质 -->
					<template #attribute="{ rowData }">
						<dict-tag
							:options="DictApi.getMatAttr()"
							:value="rowData.attribute"
						/>
					</template>

					<!-- 库存单位 -->
					<template #useUnit="{ rowData }">
						{{
							dictFilter("INVENTORY_UNIT", rowData.useUnit)?.subitemName ||
							"---"
						}}
					</template>

					<!-- 主要材质 -->
					<template #quality="{ rowData }">
						{{
							dictFilter("MAIN_MATERIALS", rowData.quality)?.subitemName ||
							"---"
						}}
					</template>

					<!-- 废旧物资分类  -->
					<template #wasteMaterialType="{ rowData }">
						<dict-tag
							:options="DictApi.getWasteMaterialType()"
							:value="rowData.wasteMaterialType"
						/>
					</template>

					<!-- 物资性质 -->
					<template #actions="{ rowData }">
						<slot
							v-if="isCheckPermission(powerList.wasteScrapLedgerBtnPreview)"
						>
							<el-button
								v-btn
								link
								@click="onRowView(rowData)"
								v-if="isCheckPermission(powerList.wasteScrapLedgerBtnPreview)"
								:disabled="
									checkPermission(powerList.wasteScrapLedgerBtnPreview)
								"
							>
								<font-awesome-icon
									:icon="['fas', 'eye']"
									style="color: var(--pitaya-btn-background)"
								/>
								<span class="table-inner-btn">查看</span>
							</el-button>
						</slot>
						<slot v-else>---</slot>
					</template>
				</pitaya-table>
			</model-frame>
		</div>

		<Drawer
			v-model:drawer="detailVisible"
			:size="modalSize.xl"
			destroy-on-close
		>
			<scrap-ledger-detail
				:id="editingTableRowId"
				@close="detailVisible = false"
			/>
			<!-- <inventory-detail
				:id="editingTableRowId"
				:fetch-param="fetchParam"
			/> -->
		</Drawer>
	</div>
</template>

<script setup lang="ts">
import {
	checkPermission,
	isCheckPermission
} from "@/app/platform/utils/permission"
import { powerList } from "../../components/define.d"
import { modalSize } from "@/app/baseline/utils/layout-config"
import { useTbInit } from "../../components/tableBase"
import { DictApi, matStatus } from "@/app/baseline/api/dict"
import DictTag from "../../components/dictTag.vue"
import { useDictInit } from "../../components/dictBase"
import { MatTypeApi } from "@/app/baseline/api/material/matType"
import { debounce, first, map } from "lodash-es"
import PitayaTree from "@/compontents/PitayaTree.vue"
import scrapLedgerDetail from "./scrapLedgerDetail.vue"
import {
	ScrapBookPageVo,
	ScrapBookPageVoQuery
} from "@/app/baseline/utils/types/waste-scrap-ledger"
import { listWasteScrapBookPaged } from "@/app/baseline/api/waste/scrap-ledger"
import { batchFormatterNumView } from "@/app/baseline/utils"
import XEUtils from "xe-utils"

const titleConf = {
	name: ["废旧物资查询"],
	icon: ["fas", "square-share-nodes"]
}

const { dictFilter, dictOptions, getDictByCodeList } = useDictInit()

/**
 * 物资id
 */
const editingTableRowId = ref()
const detailVisible = ref()

const changeLazy = ref(true)

const defaultExpandedKeys = ref([0])

const treeLoading = ref(false)

const treeSelectId = ref()

const treeRef = ref<InstanceType<typeof PitayaTree>>()

const treeData = ref<Record<string, any>[]>([
	{
		id: 0,
		code: "",
		fullLabel: "物资全部分类",
		label: "物资全部分类",
		children: [],
		isLeaf: false
	}
])

/**
 * 筛选配置
 */
const queryConf = computed<querySetting[]>(() => [
	{
		name: "物资编码",
		key: "materialCode",
		type: "input",
		placeholder: "请输入物资编码"
	},
	{
		name: "物资名称",
		key: "materialLabel",
		type: "input",
		placeholder: "请输入物资名称"
	},
	{
		name: "规格型号",
		key: "version",
		placeholder: "请输入规格型号",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资性质",
		key: "attribute",
		type: "select",
		children: dictOptions.value.MATERIAL_NATURE,
		placeholder: "请选择物资性质"
	},
	{
		name: "主要材质",
		key: "qualities",
		placeholder: "请选择主要材质",
		type: "select",
		children: dictOptions.value.MAIN_MATERIALS
	},
	{
		name: "废旧物资分类",
		key: "wasteMaterialType",
		placeholder: "请选择废旧物资分类",
		type: "select",
		children: dictOptions.value["WASTE_MATERIALS_TYPE"]
	}
])

const tableTitleConf = {
	name: ["废旧物资台账"],
	icon: ["fas", "square-share-nodes"]
}

const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	currentPage,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = useTbInit<ScrapBookPageVo, Ref<ScrapBookPageVoQuery>>()

fetchFunc.value = listWasteScrapBookPaged

tableProp.value = [
	{
		prop: "materialCode",
		label: "物资编码",
		width: 130,
		fixed: "left",
		sortable: true
	},
	{
		prop: "materialLabel",
		label: "物资名称",
		minWidth: 130
	},
	{
		prop: "materialTypeName",
		label: "物资分类名称",
		minWidth: 100
	},
	{
		prop: "version",
		label: "规格型号",
		minWidth: 100
	},
	{ label: "物资性质", prop: "attribute", needSlot: true, width: 120 },
	{
		prop: "quality", // 字典
		label: "主要材质",
		needSlot: true,
		width: 100
	},
	{
		prop: "auxiliaryQuality_view",
		label: "辅助材质",
		width: 120
	},
	{
		prop: "wasteMaterialType", // 字典
		label: "废旧物资分类",
		needSlot: true,
		width: 120
	},
	{
		prop: "useUnit",
		label: "库存单位",
		needSlot: true,
		width: 80
	},
	{
		prop: "storeNum_view",
		label: "当前库存",
		width: 120,
		align: "right"
	},
	{
		prop: "completeWasteOldNum_view",
		label: "已交旧数量",
		width: 120,
		align: "right"
	},
	{
		prop: "completeScrapNum_view",
		label: "已处置数量",
		align: "right",
		width: 120
	},
	{
		prop: "actions",
		label: "操作",
		needSlot: true,
		width: 100,
		fixed: "right"
	}
]

/**
 * 格式化数量
 */
watch(tableData, () => {
	batchFormatterNumView(tableData.value)
})

onMounted(() => {
	fetchParam.value.sord = "asc"
	fetchParam.value.sidx = "code"

	getDictByCodeList([
		"INVENTORY_UNIT",
		"MAIN_MATERIALS",
		"WASTE_MATERIALS_TYPE",
		"MATERIAL_NATURE"
	])
	fetchTableData()
})

/**
 * 排序
 * @param column
 * @param prop 字段名称
 * @param order ascending 升序 descending 降序
 */
function handleSortChange(column: any, prop: any, order: any) {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		sord:
			order === "descending" ? "desc" : order === "ascending" ? "asc" : "asc",
		sidx: order ? (prop === "materialCode" ? "code" : prop) : "code" // 排序字段
	}

	fetchTableData()
}

/**
 * 子类加载
 * @param treeNode
 */
async function loadTreeChildren(treeNode: any) {
	if (treeNode.level == 0) {
		return MatTypeApi.getMatTypeChild({
			//...matParams.value,
			status: `${matStatus.normal},${matStatus.canceled},${matStatus.freeze}`,
			fid: treeNode.data[0].id
		}).then((res: any) => {
			treeData.value[0].children = map(res, (v) => ({
				...v,
				fullLabel:
					v.lowValue == "1"
						? `${v.code} ${
								v.label
						  } <i style='border:1px solid #409eff;color:#409eff;font-size:10px;padding:1px;'>低值</i>${
								v.status === matStatus.freeze
									? `<i style='color:#f59b22;font-size:12px;'>（冻结）</i>`
									: v.status === matStatus.canceled
									? `<i style='color:#e25e59;font-size:12px;'>（作废）</i>`
									: ""
						  }`
						: v.status === matStatus.freeze
						? `${v.code} ${v.label}<i style='color:#f59b22;font-size:12px;'>（冻结）</i>`
						: v.status === matStatus.canceled
						? `${v.code} ${v.label}<i style='color:#e25e59;font-size:12px;'>（作废）</i>`
						: `${v.code} ${v.label}`,
				isLeaf: v.childCount == 0 ? true : false
			}))

			return treeData.value
		})
	} else {
		return MatTypeApi.getMatTypeChild({
			status: `${matStatus.normal},${matStatus.canceled},${matStatus.freeze}`,
			fid: treeNode.data.id
		}).then((res: any) => {
			treeNode.children = map(res, (v) => ({
				...v,
				fullLabel:
					v.lowValue == "1"
						? `${v.code} ${
								v.label
						  } <i style='border:1px solid #409eff;color:#409eff;font-size:10px;padding:1px;'>低值</i>${
								v.status === matStatus.freeze
									? `<i style='color:#f59b22;font-size:12px;'>（冻结）</i>`
									: v.status === matStatus.canceled
									? `<i style='color:#e25e59;font-size:12px;'>（作废）</i>`
									: ""
						  }`
						: v.status === matStatus.freeze
						? `${v.code} ${v.label}<i style='color:#f59b22;font-size:12px;'>（冻结）</i>`
						: v.status === matStatus.canceled
						? `${v.code} ${v.label}<i style='color:#e25e59;font-size:12px;'>（作废）</i>`
						: `${v.code} ${v.label}`,
				isLeaf: v.childCount == 0 ? true : false
			}))

			return treeNode.children
		})
	}
}

/**
 * 初始化 treeData数据
 */
/* function initTreeData(res: any) {
	treeData.value[0].children = map(res, (v) => ({
		...v,
		fullLabel:
			v.lowValue == "1"
				? `${v.code} ${v.label} <span style='border:1px solid #409eff;color:#409eff;font-size:10px;padding:1px;'>低值</span>`
				: `${v.code} ${v.label}`
	}))

	treeData.value[0].children.forEach((item: any) => {
		loadLocChildren(item)
	})
} */

/**
 * 树形筛选 check handler
 */
const selectedMatIds = ref("")
const handleTreeCheck = debounce(() => {
	const checkedNodes = treeRef.value?.getCheckedNodes()

	fetchParam.value.qualities = undefined
	fetchParam.value.typeIds = undefined

	const e = first(checkedNodes) as any | undefined
	if (!e) {
		handleQuery()
		return
	}

	const ids = map(checkedNodes, ({ id }) => id).toString()
	selectedMatIds.value = ids

	// 按物资分类
	fetchParam.value.typeIds = selectedMatIds.value

	handleQuery()
}, 300)

/* const onLocTreeExpand = (data: any) => {
	if (data.children && data.children.length > 0) {
		data.children.forEach((item: any) => {
			!filterText.value && loadLocChildren(item)
		})
	}
} */

const filterText = ref("")
const getFilterTreeData = () => {
	treeLoading.value = true
	MatTypeApi.getMatTypeTree({
		key: filterText.value ? `*${filterText.value}*` : "",
		//fid: 0,
		status: `${matStatus.normal},${matStatus.canceled},${matStatus.freeze}`
	} as any)
		.then((res: any) => {
			if (res.length == 0) {
				return (treeData.value = [])
			}

			defaultExpandedKeys.value = [0]
			XEUtils.eachTree(res, (item: any) => {
				defaultExpandedKeys.value.push(item.id)

				item.fullLabel =
					item.lowValue == "1"
						? `${item.code} ${
								item.label
						  } <i style='border:1px solid #409eff;color:#409eff;font-size:10px;padding:1px;'>低值</i>${
								item.status === matStatus.freeze
									? `<i style='color:#f59b22;font-size:12px;'>（冻结）</i>`
									: item.status === matStatus.canceled
									? `<i style='color:#e25e59;font-size:12px;'>（作废）</i>`
									: ""
						  }`
						: item.status === matStatus.freeze
						? `${item.code} ${item.label}<i style='color:#f59b22;font-size:12px;'>（冻结）</i>`
						: item.status === matStatus.canceled
						? `${item.code} ${item.label}<i style='color:#e25e59;font-size:12px;'>（作废）</i>`
						: `${item.code} ${item.label}`
			})

			treeData.value[0].children = res

			//initTreeData(res)
		})
		.finally(() => {
			treeLoading.value = false
		})
}

const onLocTreeSearch = debounce(async (infilterText: any) => {
	treeRef.value?.setCheckedKeys([])

	if (infilterText && infilterText.length >= 1) {
		filterText.value = infilterText
		changeLazy.value = false
		getFilterTreeData()
	} else if (!infilterText || infilterText.length < 1) {
		filterText.value = ""
		defaultExpandedKeys.value = [0]

		fetchParam.value.quality = undefined
		fetchParam.value.typeIds = undefined
		handleQuery()

		treeData.value = [
			{
				id: 0,
				code: "",
				fullLabel: "物资全部分类",
				label: "物资全部分类",
				children: [],
				isLeaf: false
			}
		]

		changeLazy.value = true
	}
}, 500)

function handleQuery(e?: any) {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		...e
	}

	fetchTableData()
}

function onRowView(e: any) {
	editingTableRowId.value = e.id
	detailVisible.value = true
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.inventory-radio-group :deep(.el-radio-button__original-radio) {
	&:checked + .el-radio-button__inner {
		color: white !important;
		background-color: $---color-info2;
		border-color: $---color-info2;
		box-shadow: -1px 0 0 0 $---color-info2;
	}

	& + .el-radio-button__inner:hover {
		color: $---color-info2;
	}
}
</style>
