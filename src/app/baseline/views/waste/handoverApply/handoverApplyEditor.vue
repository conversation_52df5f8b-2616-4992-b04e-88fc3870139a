<script setup lang="ts">
import { computed, onMounted, reactive, ref } from "vue"
import {
	IIdempotentTokenType,
	IIdempotentTokenTypePre,
	IModalType
} from "../../../utils/types/common"
import { ElMessage, FormInstance, FormRules } from "element-plus"
import { inputMaxLength, modalSize } from "@/app/baseline/utils/layout-config"
import { getModalTypeLabel } from "@/app/baseline/utils"
import { FormElementType } from "../../components/define"
import { useDictInit } from "../../components/dictBase"
import formElement from "../../components/formElement.vue"
import matGetTicketSelector from "./matGetTicketSelector.vue"

import { first, map } from "lodash-es"
import { useMessageBoxInit } from "../../components/messageBox"
import { fileBusinessType, MaterialPurpose } from "@/app/baseline/api/dict"
import TableFile from "../../components/tableFile.vue"
import matGetUseApplySelector from "./matGetUseApplySelector.vue"
import {
	IWasteOldType,
	MatWasteOldApplyAddDTO
} from "@/app/baseline/utils/types/waste-handover-apply"
import {
	addWasteOldApply,
	listWasteOldApplyDetail,
	submitWasteOldApply,
	updateWasteOldApply,
	updateWasteOldApplyBatchItem
} from "@/app/baseline/api/waste/handover-apply"
import { listSelectLowValueUseApplyPage } from "@/app/baseline/api/lowValue/requisitionApply"
import { listCanPickApplyPaged } from "@/app/baseline/api/store/mat-get-apply-api"
import handoverApplyItemEditor from "./handoverApplyItemEditor.vue"
import userSelector from "../../store/components/userSelector.vue"
import storeTable from "../../store/components/storeTable.vue"
import roomSelector from "./roomSelector.vue"
import {
	IWarehouseType,
	MatStoreRegionDTO,
	MatStoreRegionTreeVo
} from "@/app/baseline/utils/types/store-manage"
import repairSelector from "./repairSelector.vue"
import { useUserStore } from "@/app/platform/store/modules/user"

import { getIdempotentToken } from "@/app/baseline/utils/validate"

const { dictOptions, getDictByCodeList } = useDictInit()
const { showWarnConfirm } = useMessageBoxInit()

const { userInfo } = storeToRefs(useUserStore())

const itemEditorTableRef = ref()

const props = defineProps<{
	id?: any // id
	mode: IModalType // create || edit || create
	formValue?: Record<string, any>
}>()

const emits = defineEmits<{
	(e: "onSaveOrClose", msg?: string): void
	(e: "update"): void
}>()

/**
 * 物资明细 table 行样式
 *
 * 库存物资异常，用红色标记该行
 */
const errorGoodsIdList = ref()

/**
 * 保存旧的表单数据
 */
const oldFormData = ref<string>()

/* table-req-params */
const drawerLoading = ref(false)

/**
 * 左侧title 配置
 */
const leftTitleConf = computed(() => ({
	name: [
		getModalTypeLabel(
			canEditExtra.value ? IModalType.edit : IModalType.create,
			"交旧申请"
		)
	],
	icon: ["fas", "square-share-nodes"]
}))

const leftBtnConf = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "保存草稿",
		icon: ["fas", "floppy-disk"]
	}
]
const drawerBtnRightConf = computed(() => [
	{
		name: "保存",
		icon: ["fas", "file-signature"],
		disabled: itemEditorTableRef.value?.editedTableRowStack.length < 1
	},
	{
		name: "提交审核",
		icon: ["fas", "circle-check"],
		disabled: itemEditorTableRef.value?.tableData.length < 1
	}
])
const formBtnLoading = ref(false) // 表单按钮loading

/**
 * 右侧 title 配置
 */
const extraTitleConf = {
	name: ["相关信息"],
	icon: ["fas", "square-share-nodes"]
}
const tabsConf = ["物资明细", "相关附件"]
const activatedTab = ref(0)

const canEditExtra = computed(() => Boolean(formData.value?.id || props.id))

/**
 * 左侧表单 配置
 */
const formData = ref<Record<string, any>>({})
const formRef = ref<FormInstance>()
const formElBase = computed<FormElementType[][]>(() => {
	const ls = [
		{
			label: "交旧业务名称",
			name: "label",
			maxlength: inputMaxLength.input
		},
		{
			label: "交旧类型",
			name: "type",
			type: "select",
			disabled: canEditExtra.value,
			data: dictOptions.value["EXCHANGE_TPYE"],
			change: () => {
				formRef.value?.clearValidate()
				formData.value.applyId = ""
				formData.value.applyCode = ""
				formData.value.applyLabel = ""
			}
		},
		{
			label: "领料单号",
			name: "applyCode",
			type: "drawer",
			disabled: canEditExtra.value,
			placeholder: "请选择",
			clear: false,
			clickApi: () => (matGetTicketSelectorVisible.value = true)
		},
		{
			label: "领用单号",
			name: "applyCode",
			type: "drawer",
			disabled: canEditExtra.value,
			placeholder: "请选择",
			clear: false,
			clickApi: () => (matGetUseApplySelectorVisible.value = true)
		},
		{
			label: "关联返修单",
			name: "applyCode",
			type: "drawer",
			disabled: canEditExtra.value,
			placeholder: "请选择",
			clear: false,
			clickApi: () => (matRepairApplySelectorVisible.value = true)
		},
		{
			label: "选择仓库",
			name: "storeLabel",
			type: "drawer",
			disabled: canEditExtra.value,
			placeholder: "请选择",
			clear: false,
			clickApi: () => (storeSelectorVisible.value = true)
		},
		{
			label: "选择货位",
			name: "roomCode",
			type: "drawer",
			disabled: canEditExtra.value || !formData.value.storeId,
			placeholder: "请选择",
			clear: false,
			clickApi: () => (roomSelectorVisible.value = true)
		},
		{
			label: "备注说明",
			name: "remark",
			type: "textarea",
			maxlength: inputMaxLength.textarea
		},
		{
			label: "技术鉴定人",
			name: "technicalEvaluatorUsername_view",
			type: "drawer",
			disabled: canEditExtra.value,
			placeholder: "请选择",
			clear: false,
			clickApi: () => (evaluatorUsernameUserVisible.value = true)
		},
		{
			label: "申请人",
			name: "applyBy_view",
			type: "input",
			disabled: true
		}
	]

	switch (formData.value.type) {
		case IWasteOldType.matPick:
			return [
				ls.filter(
					(v) =>
						!["领用单号", "关联返修单", "选择仓库", "选择货位"].includes(
							v.label
						)
				)
			]
		case IWasteOldType.matLow:
			return [
				ls.filter(
					(v) =>
						!["领料单号", "关联返修单", "选择仓库", "选择货位"].includes(
							v.label
						)
				)
			]
		case IWasteOldType.matRepair:
			return [
				ls.filter(
					(v) =>
						!["领料单号", "领用单号", "选择仓库", "选择货位"].includes(v.label)
				)
			]
		case IWasteOldType.matInStore: // 废旧库/危险废旧库
			return [
				ls.filter(
					(v) => !["领料单号", "领用单号", "关联返修单"].includes(v.label)
				)
			]
		default:
			return [
				ls.filter(
					(v) =>
						![
							"领料单号",
							"领用单号",
							"关联返修单",
							"选择仓库",
							"选择货位"
						].includes(v.label)
				)
			] // v.label !== "领料单号" && v.label !== "领用单号" && v.label !== "关联返修单"
	}
})
// 左侧表单校验
const formRules = reactive<FormRules<typeof formData>>({
	label: {
		required: true,
		message: "交旧业务名称不能为空",
		trigger: "change"
	},
	type: {
		required: true,
		message: "交旧类型不能为空",
		trigger: "change"
	},
	applyCode: {
		required: true,
		message: "领料单号不能为空",
		trigger: "change"
	},
	technicalEvaluatorUsername_view: {
		required: true,
		message: "技术鉴定人不能为空",
		trigger: "change"
	},
	storeLabel: {
		required: true,
		message: "仓库不能为空",
		trigger: "change"
	},
	roomCode: {
		required: true,
		message: "货位不能为空",
		trigger: "change"
	}
})

/**
 * 领料单选择器 visible
 */
const matGetTicketSelectorVisible = ref(false)

/**
 * 选择领料单 handler
 */
const handleSelectedMatGetTicket = (e: any) => {
	matGetTicketSelectorVisible.value = false
	formData.value.applyId = e.id
	formData.value.applyCode = e.code
	formData.value.applyLabel = e.label
}

/**
 * 领用单选择器 visible
 */
const matGetUseApplySelectorVisible = ref(false)

/**
 * 选择领用单 handler
 */
const handleSelectedMatGetUseApply = (e: any) => {
	matGetUseApplySelectorVisible.value = false
	formData.value.applyId = e.id
	formData.value.applyCode = e.code
	formData.value.applyLabel = e.label
}

const matRepairApplySelectorVisible = ref(false)

function handleRepairSelect(btnName: string, row?: any) {
	if (btnName === "取消") {
		matRepairApplySelectorVisible.value = false
		return
	}
	formData.value.applyId = row?.id
	formData.value.applyCode = row?.code
	formData.value.applyLabel = row?.label
	matRepairApplySelectorVisible.value = false
}
/**
 *  技术鉴定人员 选择器
 */
const evaluatorUsernameUserVisible = ref(false)

const handleEvaluatorUsername = (e?: Record<string, any>[]) => {
	formData.value.technicalEvaluatorUsername = first(e)?.username
	formData.value.technicalEvaluatorUsername_view = first(e)?.realname
	evaluatorUsernameUserVisible.value = false
}

/**
 *  选择仓库
 */
const storeSelectorVisible = ref(false)

function handleSaveStore(btnName?: string, e?: any) {
	if (btnName === "保存") {
		formData.value.storeId = e?.id
		formData.value.storeLabel = e?.label
		formData.value.storeCode = e?.code
	}

	storeSelectorVisible.value = false
}

/**
 * 选择货位
 */
const roomSelectorVisible = ref(false)

/**
 * 选择货位 handler
 */
function handleSelectoreRoom(
	btnName: string,
	treeRow?: MatStoreRegionTreeVo,
	row?: MatStoreRegionDTO
) {
	if (btnName == "保存") {
		// 区域
		formData.value.regionLabel = treeRow?.label
		formData.value.regionId = treeRow?.id

		// 货位
		formData.value.roomCode = row?.code // 货位label
		formData.value.roomId = row?.id // 货位ID
	}

	roomSelectorVisible.value = false
}

/**
 * 表单按钮 操作
 * @param btnName 保存草稿 | 取消
 */
const handleFormBtnClick = (btnName?: string) => {
	if (btnName == "取消") {
		emits("onSaveOrClose", "")
	} else if (btnName == "保存草稿") {
		handleSaveDraft()
	} else if (btnName == "保存") {
		handleSave()
	} else if (btnName == "提交审核") {
		handleSubmit()
	}
}

/**
 * 保存草稿 handler
 */
async function handleSaveDraft() {
	if (!formRef.value) {
		return
	}

	formRef.value.validate(async (valid) => {
		if (!valid) {
			return
		}
		formBtnLoading.value = true
		// 表单校验通过
		try {
			const api = canEditExtra.value ? updateWasteOldApply : addWasteOldApply

			let idempotentToken = ""
			if (!canEditExtra.value) {
				idempotentToken = getIdempotentToken(
					IIdempotentTokenTypePre.apply,
					IIdempotentTokenType.storeWasteHandoverApply
				)
			}

			const r = await api(
				formData.value as MatWasteOldApplyAddDTO,
				idempotentToken
			)
			formData.value.id = r.id

			oldFormData.value = JSON.stringify(formData.value)

			ElMessage.success("操作成功")
			itemEditorTableRef.value?.getTableData()
			emits("update")
		} finally {
			formBtnLoading.value = false
		}
	})
}

/**
 * 保存 handler
 */
async function handleSave() {
	// await showWarnConfirm("是否保存本次数据？")

	formBtnLoading.value = true

	try {
		// 如果主表有修改，则先更新主表数据
		if (oldFormData.value != JSON.stringify(formData.value)) {
			await updateWasteOldApply(formData.value as MatWasteOldApplyAddDTO)

			oldFormData.value = JSON.stringify(formData.value)
		}

		const items = map(itemEditorTableRef.value.editedTableRowStack, (v) => {
			return {
				applyItemId: v.id,
				id: v.id,
				recoveryWeight: v.recoveryWeight,
				materialId: v.materialId,
				repairNum: v.repairNum,
				repairStoreId: v.repairStoreId,
				scrapNum: v.scrapNum,
				scrapStoreId: v.scrapStoreId,
				standbyNum: v.standbyNum,
				standbyStoreId: v.standbyStoreId,
				repairDamageDescribe: v.repairDamageDescribe,
				scrapReason: v.scrapReason,
				standbyDamageDescribe: v.standbyDamageDescribe
			}
		})

		if (items.length < 1) {
			ElMessage.warning("当前物资列表未修改")
			return false
		}

		await updateWasteOldApplyBatchItem({
			applyId: props.id || formData.value.id,
			items
		})

		itemEditorTableRef.value.editedTableRowStack = []
		ElMessage.success("操作成功")
	} finally {
		formBtnLoading.value = false
	}
}
/**
 * 提交 handler
 */
async function handleSubmit() {
	if (!formRef.value) {
		return
	}

	formRef.value.validate(async (valid) => {
		if (!valid) {
			return
		}

		await showWarnConfirm("请确认是否提交本次数据？")

		formBtnLoading.value = true
		drawerLoading.value = true

		try {
			// 如果主表有修改，则先更新主表数据
			if (oldFormData.value != JSON.stringify(formData.value)) {
				await updateWasteOldApply(formData.value as MatWasteOldApplyAddDTO)
			}

			const items = map(itemEditorTableRef.value.editedTableRowStack, (v) => {
				return {
					applyItemId: v.id,
					id: v.id,
					recoveryWeight: v.recoveryWeight,
					materialId: v.materialId,
					repairNum: v.repairNum,
					repairStoreId: v.repairStoreId,
					scrapNum: v.scrapNum,
					scrapStoreId: v.scrapStoreId,
					standbyNum: v.standbyNum,
					standbyStoreId: v.standbyStoreId,
					repairDamageDescribe: v.repairDamageDescribe,
					scrapReason: v.scrapReason,
					standbyDamageDescribe: v.standbyDamageDescribe
				}
			})

			if (items.length > 0) {
				await updateWasteOldApplyBatchItem({
					applyId: props.id || formData.value.id,
					items
				})
			}

			const idempotentToken = getIdempotentToken(
				IIdempotentTokenTypePre.other,
				IIdempotentTokenType.storeWasteHandoverApply,
				formData.value.id
			)

			const { code, msg, data } = await submitWasteOldApply(
				props.id || formData.value.id,
				idempotentToken
			)

			if (data && code != 200) {
				errorGoodsIdList.value = data || []
				ElMessage.error(msg)
				itemEditorTableRef.value?.getTableData()
			} else {
				ElMessage.success("操作成功")

				// fetchTableData()
				emits("onSaveOrClose", "save")
			}
		} finally {
			formBtnLoading.value = false
			drawerLoading.value = false
		}
	})
}

/**
 * 获取详情数据
 */
async function getDetail() {
	drawerLoading.value = true
	try {
		const r = await listWasteOldApplyDetail(props.id || formData.value.id)
		formData.value = r

		oldFormData.value = JSON.stringify(formData.value)
	} finally {
		drawerLoading.value = false
	}
}

onMounted(async () => {
	await getDictByCodeList([
		"EXCHANGE_TPYE",
		"MAIN_MATERIALS",
		"INVENTORY_UNIT",
		"STORE_LEVEL",
		"LOW_VALUE_TYPE"
	])
	if (props.id) {
		await getDetail()
	} else {
		formData.value.applyBy_view = userInfo.value.realName
		formData.value.applyBy = userInfo.value.userName
	}

	if (props.formValue) {
		formData.value.type = IWasteOldType.matPick
		formData.value.applyCode = props.formValue.code
		formData.value.applyId = props.formValue.id
		formData.value.applyLabel = props.formValue.label
	}
})
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<div class="drawer-column left" style="width: 310px">
			<Title :title="leftTitleConf" />

			<el-scrollbar>
				<el-form
					class="content"
					:model="formData"
					ref="formRef"
					label-position="top"
					label-width="100px"
					:rules="formRules"
				>
					<form-element :form-element="formElBase" :form-data="formData" />
				</el-form>
			</el-scrollbar>
			<button-list
				class="footer"
				:button="leftBtnConf"
				:loading="formBtnLoading"
				@on-btn-click="handleFormBtnClick"
			/>
		</div>

		<div
			class="drawer-column right"
			:class="!canEditExtra ? 'disabled' : ''"
			style="width: calc(100% - 310px)"
		>
			<Title :title="extraTitleConf">
				<Tabs
					style="margin-right: auto; margin-left: 30px"
					:tabs="tabsConf"
					@on-tab-change="activatedTab = $event"
				/>
			</Title>
			<div class="rows">
				<handover-apply-item-editor
					v-if="activatedTab === 0 && formData.id"
					:id="props.id || formData.id"
					:type="formData.type"
					:apply-id="formData.applyId"
					:apply-code="formData.applyCode"
					:store-code="formData.storeCode"
					:room-code="formData.roomCode"
					ref="itemEditorTableRef"
					:error-id-list="errorGoodsIdList"
				/>

				<table-file
					v-else-if="formData.id"
					:business-type="fileBusinessType.wasteHandoverApply"
					:business-id="props.id || formData.id"
					:mod="canEditExtra ? IModalType.edit : IModalType.view"
				/>
			</div>
			<button-list
				class="footer"
				:button="drawerBtnRightConf"
				:loading="formBtnLoading"
				@on-btn-click="handleFormBtnClick"
			/>
		</div>

		<!-- 领料单选择器 -->
		<Drawer
			v-model:drawer="matGetTicketSelectorVisible"
			:size="modalSize.lg"
			destroy-on-close
		>
			<mat-get-ticket-selector
				:selected="(formData.applyId as number)"
				:table-api-params="{ purpose: MaterialPurpose.lowvalueMaterial }"
				:table-api="listCanPickApplyPaged"
				@close="matGetTicketSelectorVisible = false"
				@save="handleSelectedMatGetTicket"
			/>
		</Drawer>

		<!-- 领用单选择器 -->
		<Drawer
			v-model:drawer="matGetUseApplySelectorVisible"
			:size="modalSize.lg"
			destroy-on-close
		>
			<mat-get-use-apply-selector
				:selected="formData.applyId"
				:table-api="listSelectLowValueUseApplyPage"
				@close="matGetUseApplySelectorVisible = false"
				@save="handleSelectedMatGetUseApply"
			/>
		</Drawer>

		<Drawer
			v-model:drawer="matRepairApplySelectorVisible"
			:size="modalSize.lg"
			destroy-on-close
		>
			<repair-selector
				:selected-ids="[formData.applyId]"
				@on-save="handleRepairSelect"
			/>
		</Drawer>

		<!-- 技术鉴定人选择器 -->
		<Drawer
			v-model:drawer="evaluatorUsernameUserVisible"
			:size="modalSize.lg"
			destroy-on-close
		>
			<user-selector
				:selected-ids="[formData.technicalEvaluatorUsername]"
				:table-fetch-params="{ sysCommunityId: userInfo.companyId }"
				@save="handleEvaluatorUsername"
				@close="evaluatorUsernameUserVisible = false"
			/>
		</Drawer>

		<Drawer
			v-model:drawer="storeSelectorVisible"
			:size="modalSize.lg"
			destroy-on-close
		>
			<!-- 过滤废旧库和危险废旧库 -->
			<store-table
				:filterStoreTypes="[
					IWarehouseType.waste,
					IWarehouseType.dangerousWaste
				]"
				:selected-ids="[formData.storeId]"
				@on-save="handleSaveStore"
			/>
		</Drawer>

		<Drawer
			v-model:drawer="roomSelectorVisible"
			:size="modalSize.lg"
			destroy-on-close
		>
			<room-selector
				:store-id="formData.storeId!"
				:store-name="formData.storeLable!"
				:store-code="formData.storeCode"
				:region-id="formData.regionId"
				:room-id="[formData.roomId]"
				@onSaveOrClose="handleSelectoreRoom"
			/>
		</Drawer>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.content {
	.btn-group {
		width: 100%;
		.el-button--warning {
			background-color: #e6a23c !important;
		}
		.el-button--success {
			background-color: #67c23a !important;
		}
		.el-button {
			width: 50% !important;
		}
	}
}
</style>
