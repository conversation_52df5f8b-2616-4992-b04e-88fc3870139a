<!-- 领用单选择器 -->
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<div class="drawer-column" style="width: 100%">
			<Title :title="titleConf" />
			<div class="rows">
				<Query
					:query-arr-list="(queryConf as any)"
					style="margin: 10px 10px -10px"
					@get-query-data="handleQuery"
				/>
				<pitaya-table
					ref="tableRef"
					:columns="tableProp"
					:table-data="tableData"
					:need-selection="true"
					:single-select="!multiple"
					:need-index="true"
					:need-pagination="true"
					:total="pageTotal"
					:table-loading="tableLoading"
					@on-selection-change="selectedTableList = $event"
					@on-current-page-change="fetchTableDataWithSetSelected"
				/>
			</div>
			<button-list
				class="footer"
				:button="btnConf"
				@on-btn-click="handleBtnClick"
			/>
		</div>
	</div>
</template>

<script setup lang="ts">
import { filter, first, forEach, includes } from "lodash-es"
import { ElTable } from "element-plus"
import { useTbInit } from "../../components/tableBase"
import { listSelectLowValueUseApplyPage } from "@/app/baseline/api/lowValue/requisitionApply"
import {
	MatLowValueApplyVO,
	MatLowValueApplyVORequest
} from "@/app/baseline/utils/types/lowValue-requisition-apply"

const props = withDefaults(
	defineProps<{
		/**
		 * 当前选中行的id
		 */
		selected?: number | number[]
		/**
		 * 是否支持多选
		 */
		multiple?: boolean

		/**
		 * table api 请求query参数
		 */
		tableApiParams?: any

		/**
		 * table api 请求
		 */
		tableApi?: (arg?: any) => any
		/**
		 * title
		 */
		title?: string
	}>(),
	{ title: "选择领用单" }
)

const emit = defineEmits<{
	(e: "close"): void
	(e: "save", v: any): void
}>()

const titleConf = computed(() => ({
	name: [`${props.title}`],
	icon: ["fas", "square-share-nodes"]
}))

const drawerLoading = ref(false)

const queryConf = [
	{
		name: "领用单号",
		key: "code",
		type: "input",
		placeholder: "请输入领用单号"
	},
	{
		name: "领用单名称",
		key: "label",
		type: "input",
		placeholder: "请输入领用单名称"
	}
]

const btnConf = computed(() => [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "保存",
		icon: ["fas", "floppy-disk"],
		disabled: selectedTableList.value.length < 1
	}
])

const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	currentPage,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc
} = useTbInit<MatLowValueApplyVO, MatLowValueApplyVORequest>()

/* fetchParam.value = {
	bpmStatus: appStatus.approved
} */

fetchFunc.value = props.tableApi ?? listSelectLowValueUseApplyPage // listLowValueUseApplyPageWasteOld

tableProp.value = [
	{
		prop: "code",
		label: "领用单号"
	},
	{
		prop: "label",
		label: "领用单名称"
	},
	{
		prop: "sysOrgId_view",
		label: "领用部门"
	},
	{
		prop: "createdBy_view",
		label: "领用人"
	},
	{
		prop: "createdDate",
		label: "申请时间"
	}
]

onMounted(() => {
	fetchParam.value = {
		//...fetchParam.value,
		...props.tableApiParams,
		sord: "desc",
		sidx: "createdDate"
	}

	fetchTableDataWithSetSelected()
})

/**
 * 根据选中的id返回选中的表格行数据
 */
function getTableRowsBySelectedId() {
	return (
		filter(tableData.value, (v: any) => {
			if (props.multiple) {
				// 如果是多选的情况
				return includes(props.selected as any, v.id)
			}

			return v.id === props.selected
		}) ?? []
	)
}

/**
 * 设置表格选中
 */
function setTableSelect() {
	const selectedRows = getTableRowsBySelectedId()
	const tbRef = tableRef.value?.pitayaTableRef as InstanceType<typeof ElTable>
	tbRef.clearSelection()
	forEach(selectedRows, (row) => tbRef.toggleRowSelection(row, true))
}

/**
 * 筛选 handler
 */
function handleQuery(e: any) {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		...e
	}

	fetchTableDataWithSetSelected()
}

/**
 * 获取 table data 以及 设置 table row 选中
 */
function fetchTableDataWithSetSelected() {
	fetchTableData().then(setTableSelect)
}

function handleBtnClick(name?: string) {
	if (name === "取消") {
		return emit("close")
	}

	// 保存逻辑
	emit(
		"save",
		props.multiple ? selectedTableList.value : first(selectedTableList.value)
	)
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
