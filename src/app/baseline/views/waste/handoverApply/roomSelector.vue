<script setup lang="ts">
import { ref, onMounted, computed } from "vue"
import {
	listMatStoreRoomPaged,
	listMatStoreRegion
} from "@/app/baseline/api/store/manage-api"
import {
	MatStoreRegionVo,
	MatStoreRoomQueryParams,
	MatStoreRoomVo
} from "../../../utils/types/store-manage"
import { find, first } from "lodash-es"
import { useTbInit } from "../../components/tableBase"
import { Folder } from "@element-plus/icons-vue"
import { useTableSelectorUtils } from "../../store/hooks/table-selector-utils"

export interface Props {
	storeId: string | number // 仓库ID
	storeName: string // 仓库名称
	storeCode?: string // 仓库编码
	regionId?: string | number // 选中的区域Id
	roomId?: any[]
	// params?: Record<string, any>
	tableApi?: (params: any) => Promise<any> // table 数据源
	tableReqParams?: any // table 请求参数
}

const props = defineProps<Props>()
const emits = defineEmits(["onSaveOrClose"])
const drawerLoading = ref(false)

/**
 * title 配置
 */
const drawerLeftTitle = computed(() => {
	return {
		name: [`选择货位【 仓库编码：${props.storeCode ?? "---"}】`],
		icon: ["fas", "square-share-nodes"]
	}
})

/**
 * 查询条件 配置
 */
const queryArrRef = ref<any>()
const queryArrList = [
	{
		name: "货位编码",
		key: "code",
		placeholder: "请输入货位编码",
		enableFuzzy: true,
		type: "input"
	}
]

/**
 * table 数据源
 * @param data
 */
const getTableData = (data?: Record<string, any>) => {
	currentPage.value = 1
	treeTableRef.value?.resetCurrentPage()
	fetchParam.value = {
		...fetchParam.value,
		//...props.params,
		...props.tableReqParams,
		regionId: checkedAreaData.value?.id,
		sord: "desc",
		sidx: "createdDate",
		...data
	}
	fetchTableDataWithSetRowsCheck()
}
const {
	tableRef: treeTableRef,
	currentPage,
	tableProp,
	tableData,
	tableLoading,
	pageTotal,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc
} = useTbInit<MatStoreRoomVo, MatStoreRoomQueryParams>()

fetchFunc.value = props.tableApi || listMatStoreRoomPaged

const { fetchTableDataWithSetRowsCheck } = useTableSelectorUtils({
	tableData,
	tableRef: treeTableRef,
	selectedIds: props.roomId,
	fetchTableData
})

/**
 * table 列配置
 */
tableProp.value = [
	{
		label: "区域名称",
		prop: "regionLabel"
	},
	{
		label: "货位编码",
		prop: "code"
	},
	{
		label: "货位类型",
		prop: "type_view"
	},
	{
		label: "备注说明",
		prop: "remark"
	}
]

/**
 * 按钮 配置
 */
const formBtnList = computed(() => {
	return [
		{ name: "取消", icon: ["fas", "circle-minus"] },
		{
			name: "保存",
			icon: ["fas", "floppy-disk"]
		}
	]
})

/**
 * 按钮 操作
 * @param btnName
 */
const onFormBtnList = (btnName: string | undefined) => {
	const selectRows = treeTableRef.value?.pitayaTableRef?.getSelectionRows()

	if (btnName === "取消") {
		emits("onSaveOrClose", btnName)
	} else if (btnName === "保存") {
		if (selectRows.length === 0) {
			return ElMessage.warning("请选择货位！")
		}
		emits("onSaveOrClose", btnName, checkedAreaData.value, first(selectRows))
	}
}

const warehouseAreaTreeData = ref<any[]>([])
const checkedAreaData = ref<MatStoreRegionVo>()
/**
 * 获取仓库区域 tree data
 */
async function getWarehouseAreaTree() {
	drawerLoading.value = true
	try {
		listMatStoreRegion({
			storeId: props.storeId as number
		}).then((r) => {
			warehouseAreaTreeData.value = r ?? []
			checkedAreaData.value = find(
				warehouseAreaTreeData.value,
				(o) => o.id == props.regionId
			)

			handleAreaTreeClick(checkedAreaData.value || r[0])
		})
	} finally {
		drawerLoading.value = false
	}
}

/**
 * 区域 tree node 点击 handler
 */
function handleAreaTreeClick(data?: any) {
	checkedAreaData.value = data
	fetchParam.value.regionId = data?.id

	if (data?.id) {
		fetchTableDataWithSetRowsCheck()
	} else {
		tableData.value = []
	}
}

/**
 * 初始化 区域树
 */
onMounted(() => {
	getWarehouseAreaTree()
})
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<div class="drawer-column" style="width: 100%">
			<div class="">
				<Title :title="drawerLeftTitle" />
			</div>
			<div class="bottom">
				<div class="left">
					<el-scrollbar class="area-list-wrap mt10">
						<ul class="drawer-left">
							<template v-if="warehouseAreaTreeData.length > 0">
								<li
									class="flex-dr li"
									:class="[checkedAreaData?.id == item.id ? 'hover' : '']"
									v-for="item in warehouseAreaTreeData"
									@click="handleAreaTreeClick(item)"
									:key="item.id"
								>
									<div class="f12 flex-dr c-3">
										<slot name="headerIcon" :item="item">
											<!-- 文字前按钮 -->
										</slot>
										<div class="text-wrap" style="vertical-align: middle">
											<el-icon :size="12" style="vertical-align: -15%">
												<Folder />
											</el-icon>

											<span class="text">{{ item.code }}</span>
											<span class="text-label">
												<el-tooltip
													class="box-item"
													effect="dark"
													:content="item.label"
													placement="top"
													:disabled="item.label.length < 20"
												>
													<span>{{ item.label }}</span>
												</el-tooltip>
											</span>
											<span class="text">({{ item.roomNum || 0 }})</span>
										</div>
									</div>
								</li>
							</template>
							<li v-else class="tc">
								<EmptySearch class="empty_img" />
								<p>未查询到相关信息</p>
							</li>
						</ul>
					</el-scrollbar>
				</div>
				<div class="right">
					<div>
						<Query
							:queryArrList="queryArrList"
							@getQueryData="getTableData"
							class="custom-q"
							ref="queryArrRef"
							style="margin: 10px 10px -10px"
						/>
						<PitayaTable
							ref="treeTableRef"
							:columns="tableProp"
							:table-data="tableData"
							:need-index="true"
							:need-pagination="true"
							:single-select="true"
							:need-selection="true"
							:total="pageTotal"
							:table-loading="tableLoading"
							@onSelectionChange="selectedTableList = $event"
							@on-current-page-change="fetchTableDataWithSetRowsCheck"
						/>
					</div>
					<ButtonList
						class="footer"
						:button="formBtnList"
						@on-btn-click="onFormBtnList"
					/>
				</div>
			</div>
		</div>
	</div>
</template>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.drawer-container {
	.bottom {
		display: flex;
		height: calc(100% - 30px);
	}
	.left {
		width: 310px;
	}

	.left::after {
		content: "";
		position: absolute;
		right: 0;
		top: 0px;
		width: 1px;
		height: calc(100% + 10px);
		background-color: #ccc;
	}

	.right {
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		width: calc(100% - 310px);
	}
}

.area-list-wrap {
	max-height: calc(100% - 10px);
}

.drawer-left {
	position: relative;
	box-sizing: border-box;

	.tree-search {
		margin-top: var(--pitaya-fs-12);
		margin-bottom: var(--pitaya-fs-12);
	}

	.li {
		line-height: 20px;
		padding: 15px;
		border-bottom: 1px dashed var(--el-border-color);
		.text {
			display: inline-block;
			vertical-align: middle;
			margin-left: 5px;
		}
		.text-label {
			display: inline-block;
			max-width: 170px; // 宽度170px
			white-space: nowrap; /* 不换行 */
			overflow: hidden; /* 超出部分隐藏 */
			text-overflow: ellipsis; /* 溢出部分显示省略号 */
			vertical-align: middle;
			margin-left: 5px;
		}
	}

	.flex-dc {
		display: flex;
		flex-direction: column;
	}

	.li:hover {
		color: #666666;
		background: var(--el-fill-color-light);
		cursor: pointer;
	}

	.hover {
		color: #666666;
		background: var(--el-fill-color-light);
	}
}

.flex-dr {
	display: flex;
	flex-direction: row;
	justify-content: space-between;
	align-items: center;
}
.tc {
	color: #666666;
	font-size: 12px;
	text-align: center;
}

.f12 {
	font-size: 12px;
}

.c-9 {
	color: #999;
}
.c-3 {
	color: #333;
}

.mt20 {
	margin-top: 20px;
}

.cp {
	cursor: pointer;
}
</style>
