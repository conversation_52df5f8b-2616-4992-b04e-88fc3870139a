<!-- 交旧申请 -->
<script lang="ts" setup>
import {
	checkPermission,
	isCheckPermission
} from "@/app/platform/utils/permission"
import { powerList } from "../../components/define.d"
import { computed, onMounted, ref } from "vue"
import { useDictInit } from "../../components/dictBase"
import { useMessageBoxInit } from "../../components/messageBox"
import { useTbInit } from "../../components/tableBase"
import {
	BaseLineSysApi,
	getTaskByBusinessIds,
	listCompanyWithFormat
} from "@/app/baseline/api/system"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import LinkTag from "@/app/baseline/views/components/linkTag.vue"
import { modalSize } from "@/app/baseline/utils/layout-config"
import { IModalType } from "../../../utils/types/common"
import { DictApi } from "@/app/baseline/api/dict"

import handoverApplyEditor from "./handoverApplyEditor.vue"
import handoverApplyDetail from "./handoverApplyDetail.vue"
import {
	MatWasteOldApplyPageQuery,
	MatWasteOldApplyPageVo
} from "@/app/baseline/utils/types/waste-handover-apply"
import {
	delWasteOldApply,
	getWasteOldApplyBmpStatusCnt,
	listWasteOldApplyPaged
} from "@/app/baseline/api/waste/handover-apply"
import { appStatus } from "@/app/baseline/api/dict"
import matGetApplyDetail from "../../store/business/matGetApply/matGetApplyDetail.vue"
import requisitionApplyDetail from "../../lowValue/requisitionApply/requisitionApplyDetail.vue"
import repairDetail from "../repairApply/repairDetail.vue"
import { omit } from "lodash-es"
import { useUserStore } from "@/app/platform/store/modules/user"
import { hasPermi } from "@/app/baseline/utils"
import ApprovedDrawer from "@/app/baseline/views/components/approvedDrawer.vue"

const { userInfo } = storeToRefs(useUserStore())

const { dictOptions, getDictByCodeList, dictFilter } = useDictInit()
const { showDelConfirm } = useMessageBoxInit()

/**
 * 公司列表
 */
const companyOptions = ref<any>([])

/**
 * 查询条件 配置
 */
const queryArrList = computed(() => {
	return [
		{
			name: "申请单号",
			key: "code",
			placeholder: "请输入申请单号",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "交旧业务名称",
			key: "label",
			placeholder: "请输入交旧业务名称",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "关联业务单号",
			key: "applyCode",
			placeholder: "请输入关联业务单号",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "申请人",
			key: "applyRealname",
			placeholder: "请输入申请人",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "交旧类型",
			key: "type",
			placeholder: "请选择交旧类型",
			type: "select",
			children: dictOptions.value["EXCHANGE_TPYE"]
		},
		{
			name: "公司",
			key: "companyId",
			type: "select",
			children: companyOptions.value,
			placeholder: "请选择公司"
		},
		{
			name: "申请部门",
			key: "deptId",
			type: "treeSelect",
			placeholder: "请选择",
			treeApi: () =>
				BaseLineSysApi.getDepartmentTreeWithCompanyDisabled(
					userInfo.value.companyId
				)
		},
		{
			name: "申请时间",
			key: "createdDate",
			type: "startAndEndTime"
		}
	]
})

/**
 * Title 配置
 */
const rightTitle = {
	name: ["交旧申请"],
	icon: ["fas", "square-share-nodes"]
}
const titleBtnConf = [
	{
		name: "新建交旧申请",
		roles: powerList.wasteHandoverApplyBtnCreate,
		icon: ["fas", "circle-plus"]
	}
]

/**
 * tab 配置项
 */
const statusCnt = ref<number[]>([])
const tabList = [
	{
		name: "草稿箱",
		value: appStatus.pendingApproval + "," + appStatus.rejected
	},
	{ name: "审批中", value: appStatus.underApproval },
	{ name: "已审批", value: appStatus.approved }
]
const tabStatus = ref(appStatus.pendingApproval + "," + appStatus.rejected)
const activeName = ref(tabList[0].name)
const handleTabsClick = (tab: any) => {
	activeName.value = tab.paneName
	tabStatus.value = tabList[tab.index].value
	getTableData()
}

const {
	tableData,
	tableProp,
	tableRef,
	tableLoading,
	currentPage,
	pageTotal,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = useTbInit<MatWasteOldApplyPageVo, MatWasteOldApplyPageQuery>()

/**
 * table 列配置
 */
tableProp.value = [
	{ label: "申请单号", prop: "code", width: 180, fixed: "left" },
	{ label: "交旧业务名称", prop: "label" },
	{ label: "关联业务单号", prop: "applyCode", needSlot: true, width: 180 },
	{ label: "交旧类型", prop: "type", needSlot: true },
	{ label: "所属公司", prop: "sysCommunityId_view" },
	{ label: "审批状态", prop: "bpmStatus", needSlot: true },
	{ label: "申请部门", prop: "sysDepartmentId_view" },
	{ label: "申请人", prop: "applyRealname" },
	{ label: "技术鉴定人", prop: "technicalEvaluatorUsername_view" },
	{ label: "申请时间", prop: "createdDate", width: 160, sortable: true },
	{
		label: "操作",
		width: 200,
		prop: "operations",
		needSlot: true,
		fixed: "right"
	}
]
fetchFunc.value = listWasteOldApplyPaged

/**
 * tab 切换数据源
 */
const getTableData = (data: any = {}) => {
	const { createdDate } = data

	data.createdDate_start = createdDate && createdDate[0]
	data.createdDate_end = createdDate && createdDate[1]

	delete data.createdDate

	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		bpmStatus: tabStatus.value,
		sord: "desc",
		sidx: "createdDate",
		...data
	}
	handleUpdate()
}

const curRowId = ref<any>("")
const curRowData = ref<MatWasteOldApplyPageVo>({})
const viewVisible = ref<boolean>(false)
const editorVisible = ref(false)
const editorMode = ref(IModalType.edit)

const approvedVisible = ref(false)
const editTask = ref<Record<string, any>>()

/**
 * 新建 操作
 */
const handleClickAddBtn = () => {
	editorMode.value = IModalType.create
	editorVisible.value = true
	curRowId.value = ""
	curRowData.value = {}
}

/**
 * 编辑 操作
 * @param row
 */
const onRowEdit = (row: MatWasteOldApplyPageVo) => {
	curRowId.value = row.id
	curRowData.value = row
	editorVisible.value = true
	editorMode.value = IModalType.edit
}

/**
 * 查看
 * @param row
 */
const onRowView = async (row: MatWasteOldApplyPageVo) => {
	curRowId.value = row.id
	curRowData.value = row
	/* viewVisible.value = true */
	editorMode.value = IModalType.view

	if (row?.bpmStatus == appStatus.pendingApproval) {
		viewVisible.value = true
	} else {
		const res = await getTaskByBusinessIds({
			businessIds: [row?.id],
			camundaKey: "waste_old_apply"
		})

		if (Array.isArray(res) && res.length > 0) {
			editTask.value = { ...res[res.length - 1] }
			approvedVisible.value = true
		} else {
			viewVisible.value = true
		}
	}
}

/**
 * 删除 操作
 * @param row
 */
const onRowDel = async (row: MatWasteOldApplyPageVo) => {
	await showDelConfirm()
	await delWasteOldApply(row.id)
	ElMessage.success("操作成功")
	handleUpdate()
}

/**
 * 查看/新建/编辑  关闭 抽屉回调
 * @param msg save | false
 */
const handleCloseDrawer = (msg?: string) => {
	if (msg === "save") {
		handleUpdate()
		editorVisible.value = false
	} else {
		editorVisible.value = false
		viewVisible.value = false
	}
}

/**
 * 关联领料单号
 * @param row
 */
const businessVisible = ref(false)
const onRowBusinessPick = (e: MatWasteOldApplyPageVo) => {
	curRowData.value = e
	businessVisible.value = true
}

/**
 * 更新主表/ tab 状态统计
 */
async function handleUpdate() {
	fetchTableData()
	statusCnt.value = await getWasteOldApplyBmpStatusCnt(
		omit(
			{
				...fetchParam.value
			},
			"bpmStatus",
			"currentPage",
			"pageSize"
		) as any
	)
}

onMounted(() => {
	getDictByCodeList(["EXCHANGE_TPYE"])
	listCompanyWithFormat().then((r) => (companyOptions.value = r))
	getTableData()
})

/**
 * 排序
 * @param column
 * @param prop 字段名称
 * @param order ascending 升序 descending 降序
 */
function handleSortChange(column: any, prop: any, order: any) {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		sord:
			order === "descending" ? "desc" : order === "ascending" ? "asc" : "desc",
		sidx: order ? prop : "createdDate" // 排序字段
	}

	fetchTableData()
}
</script>
<template>
	<div class="app-container">
		<div class="whole-frame">
			<ModelFrame class="top-frame">
				<Query
					:queryArrList="queryArrList"
					@getQueryData="getTableData"
					class="ml10"
				/>
			</ModelFrame>

			<ModelFrame class="bottom-frame">
				<Title
					:title="rightTitle"
					:button="(titleBtnConf as any)"
					@on-btn-click="handleClickAddBtn"
				>
					<div class="app-tabs-wrapper">
						<el-tabs v-model="activeName" @tab-click="handleTabsClick">
							<el-tab-pane
								v-for="(tab, index) in tabList"
								:key="index"
								:label="`${tab.name}（${statusCnt[index] ?? 0}）`"
								:name="tab.name"
								:index="tab.name"
							/>
						</el-tabs>
					</div>
				</Title>

				<pitaya-table
					ref="tableRef"
					:columns="tableProp"
					:tableData="tableData"
					:total="pageTotal"
					:need-index="true"
					@on-current-page-change="onCurrentPageChange"
					:table-loading="tableLoading"
					@on-table-sort-change="handleSortChange"
				>
					<!-- 审批状态 -->
					<template #bpmStatus="{ rowData }">
						<dict-tag
							:options="DictApi.getBpmStatus()"
							:value="rowData.bpmStatus"
						/>
					</template>

					<!-- 关联业务单号 -->
					<template #applyCode="{ rowData }">
						<link-tag
							:value="rowData.applyCode"
							@on-click="onRowBusinessPick(rowData)"
						/>
					</template>

					<!-- 交旧类型 -->
					<template #type="{ rowData }">
						{{
							dictFilter("EXCHANGE_TPYE", rowData.type)?.subitemName || "---"
						}}
					</template>

					<template #operations="{ rowData }">
						<slot
							v-if="
								(tabStatus == tabList[0].value &&
									isCheckPermission(powerList.wasteHandoverApplyBtnEdit) &&
									hasPermi(rowData.createdBy)) ||
								isCheckPermission(powerList.wasteHandoverApplyBtnPreview) ||
								(tabStatus == tabList[0].value &&
									isCheckPermission(powerList.wasteHandoverApplyBtnDrop) &&
									hasPermi(rowData.createdBy))
							"
						>
							<el-button
								v-btn
								link
								@click="onRowEdit(rowData)"
								v-if="
									tabStatus == tabList[0].value &&
									isCheckPermission(powerList.wasteHandoverApplyBtnEdit) &&
									hasPermi(rowData.createdBy)
								"
								:disabled="checkPermission(powerList.wasteHandoverApplyBtnEdit)"
							>
								<font-awesome-icon :icon="['fas', 'pen-to-square']" />
								<span class="table-inner-btn">编辑</span>
							</el-button>
							<el-button
								v-btn
								link
								@click="onRowView(rowData)"
								v-if="isCheckPermission(powerList.wasteHandoverApplyBtnPreview)"
								:disabled="
									checkPermission(powerList.wasteHandoverApplyBtnPreview)
								"
							>
								<font-awesome-icon :icon="['fas', 'eye']" />
								<span class="table-inner-btn">查看</span>
							</el-button>
							<el-button
								v-btn
								link
								@click="onRowDel(rowData)"
								v-if="
									tabStatus == tabList[0].value &&
									isCheckPermission(powerList.wasteHandoverApplyBtnDrop) &&
									hasPermi(rowData.createdBy)
								"
								:disabled="checkPermission(powerList.wasteHandoverApplyBtnDrop)"
							>
								<font-awesome-icon :icon="['fas', 'trash-can']" />
								<span class="table-inner-btn">移除</span>
							</el-button>
						</slot>
						<slot v-else>---</slot>
					</template>
				</pitaya-table>

				<!-- 编辑/新建  -->
				<Drawer
					:size="modalSize.xxl"
					v-model:drawer="editorVisible"
					:destroyOnClose="true"
				>
					<handover-apply-editor
						:id="curRowId"
						:mode="editorMode"
						@on-save-or-close="handleCloseDrawer"
						@update="handleUpdate"
					/>
				</Drawer>

				<!-- 查看  -->
				<Drawer
					:size="modalSize.xxl"
					v-model:drawer="viewVisible"
					:destroyOnClose="true"
				>
					<handover-apply-detail
						:id="curRowId"
						:mode="editorMode"
						@close="viewVisible = false"
					/>
				</Drawer>

				<!-- 关联业务单 -->
				<Drawer
					v-model:drawer="businessVisible"
					:size="modalSize.xl"
					destroy-on-close
				>
					<!-- 领料单申请详情 -->
					<mat-get-apply-detail
						v-if="curRowData.type === '0'"
						:id="curRowData.applyId!"
						@close="businessVisible = false"
					/>

					<!-- 领用单申请详情 -->
					<requisition-apply-detail
						v-else-if="curRowData.type === '2'"
						:id="curRowData.applyId!"
						@close="businessVisible = false"
					/>

					<!-- 返修单申请详情 -->
					<repair-detail
						v-else-if="curRowData.type === '3'"
						:id="curRowData.applyId!"
						@close="businessVisible = false"
					/>
				</Drawer>

				<!-- 审批、查看弹窗 -->
				<Drawer
					:size="modalSize.xxl"
					v-model:drawer="approvedVisible"
					:destroyOnClose="true"
				>
					<approved-drawer
						:mod="editorMode"
						:task-value="editTask"
						@on-save-or-close="approvedVisible = false"
					/>
				</Drawer>
			</ModelFrame>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
