<script setup lang="ts">
import { computed, onMounted, ref } from "vue"
import { IModalType } from "../../../utils/types/common"
import {
	batchFormatterNumView,
	tableColFilter,
	toFixedTwo
} from "@/app/baseline/utils"
import { useDictInit } from "../../components/dictBase"

import { useTbInit } from "../../components/tableBase"
import dictTag from "../../components/dictTag.vue"
import { fileBusinessType } from "@/app/baseline/api/dict"
import TableFile from "../../components/tableFile.vue"
import {
	IWasteOldType,
	MatWasteOldApplyItemPageVo
} from "@/app/baseline/utils/types/waste-handover-apply"
import {
	listWasteOldApplyDetail,
	listWasteOldApplyPageItem
} from "@/app/baseline/api/waste/handover-apply"
import { DictApi } from "@/app/baseline/api/dict"
import { getRealLength, setString } from "@/app/baseline/utils/validate"

const { dictFilter, getDictByCodeList } = useDictInit()

const props = withDefaults(
	defineProps<{
		id: any // 申请id
		footerBtnVisible?: boolean
	}>(),
	{ footerBtnVisible: true }
)

const emits = defineEmits<{
	(e: "onSaveOrClose", msg?: string): void
	(e: "close"): void
}>()

const drawerLoading = ref(false)

/**
 * 左侧title 配置
 */
const leftTitleConf = computed(() => ({
	name: ["交旧信息"],
	icon: ["fas", "square-share-nodes"]
}))

const drawerBtnRightConf = computed(() => [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	}
])

/**
 * 右侧 title 配置
 */
const extraTitleConf = {
	name: ["相关信息"],
	icon: ["fas", "square-share-nodes"]
}
const tabsConf = ["物资明细", "相关附件"]
const activatedTab = ref(0)

const handleTabChange = (index: number) => {
	activatedTab.value = index

	if (index === 0) {
		fetchParam.value = {
			wasteApplyId: props.id,
			sord: "desc",
			sidx: "createdDate"
		}

		pageSize.value = 20

		getTableData()
	}
}

/**
 * 左侧表单 配置
 */
const formData = ref<Record<string, any>>({})

/**
 * 表单按钮 操作
 * @param btnName 保存草稿 | 取消
 */
const handleFormBtnClick = (btnName?: string) => {
	if (btnName == "取消") {
		emits("close")
	}
}

/**
 * 关联业务单数据
 */
const descList = computed(() => {
	const ls = [
		{ label: "申请单号", key: "code" },
		{ label: "申请名称", key: "label" },
		{ label: "交旧类型", key: "type" },
		{ label: "申请部门", key: "sysDepartmentId_view" },
		{ label: "关联业务单号", key: "applyCode" },
		{ label: "关联业务单名称", key: "applyLabel" },
		{ label: "仓库名称", key: "storeLabel" },
		{ label: "货位编码", key: "roomCode" },
		{ label: "技术鉴定人", key: "technicalEvaluatorUsername_view" },
		{ label: "申请人", key: "applyBy_view" },
		{ label: "申请时间", key: "createdDate" },
		{ label: "备注说明", key: "remark", needTooltip: true }
	]

	if (formData.value.type === IWasteOldType.matHistory) {
		return ls.filter(
			(v) =>
				!["关联业务单号", "关联业务单名称", "仓库名称", "货位编码"].includes(
					v.label
				)
		)
	} else if (formData.value.type === IWasteOldType.matInStore) {
		return ls.filter(
			(v) => !["关联业务单号", "关联业务单名称"].includes(v.label)
		)
	} else {
		return ls.filter((v) => !["仓库名称", "货位编码"].includes(v.label))
	}
})

/**
 * 查询条件 配置
 */
const queryArrList = computed(() => [
	{
		name: "物资编码",
		key: "materialCode",
		placeholder: "请输入物资编码",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资名称",
		key: "materialLabel",
		placeholder: "请输入物资名称",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "规格型号",
		key: "version",
		placeholder: "请输入规格型号",
		enableFuzzy: true,
		type: "input"
	}
])

const tbInit = useTbInit<MatWasteOldApplyItemPageVo, Record<string, any>>()
const {
	currentPage,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	pageSize,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = tbInit

tbInit.tableProp = computed(() => {
	const ls: TableColumnType[] = [
		{
			prop: "materialCode",
			label: "物资编码",
			width: 130,
			fixed: "left"
		},
		{
			prop: "materialLabel",
			label: "物资名称",
			width: 120
		},
		{
			prop: "version",
			label: "规格型号",
			width: 120
		},
		{
			prop: "technicalParameter",
			label: "技术参数",
			minWidth: 120
		},
		{
			prop: "useUnit",
			label: "库存单位",
			needSlot: true
		},
		{
			prop: "wasteMaterialType",
			label: "废旧物资分类",
			width: 100,
			needSlot: true
		},
		{
			prop: "quality",
			label: "主要材质",
			needSlot: true,
			width: 80
		},
		{
			prop: "auxiliaryQuality",
			label: "辅助材质",
			needSlot: true,
			width: 80
		},
		{
			prop: "recoveryWeight",
			label: "预估回收重量(KG)",
			width: 130,
			align: "right",
			needSlot: true
		},
		{
			prop: "batchNo",
			label: "批次号",
			width: 160
		},
		{
			prop: "lowValueType_view",
			label: "低值类型",
			width: 100
		},
		{
			prop: "allocationUsername_view",
			label: "使用人",
			width: 100
		},
		{
			prop: "allocationNum_view",
			label: "分配数量",
			width: 100,
			align: "right"
		},
		/* {
			prop: "outNum_view",
			label: "领料出库数量",
			width: 100,
			align: "right"
		}, */
		/* {
			prop: "completeOldNum_view",
			label: "已交旧数量",
			width: 100,
			align: "right"
		},
		{
			prop: "canOldNum_view",
			label: "可交旧数量",
			width: 100,
			align: "right",
			fixed: "right"
		}, */
		/* {
			prop: "standbyNum_view",
			label: "备用数量",
			width: 100,
			align: "right"
		},
		{
			prop: "standbyStoreName",
			label: "周转件仓库名称",
			width: 120
		},
		{
			prop: "standbyDamageDescribe",
			label: "备用损坏描述",
			width: 100,
			align: "right"
		}, */
		{
			prop: "repairNum_view",
			label: "返修数量",
			width: 100,
			align: "right"
		},
		{
			prop: "repairStoreName",
			label: "维修仓库名称",
			width: 120
		},
		{
			prop: "repairDamageDescribe",
			label: "返修损坏描述",
			width: 100,
			align: "right"
		},
		{
			prop: "scrapNum_view",
			label: "报废数量",
			width: 100,
			align: "right"
		},
		{
			prop: "scrapStoreName",
			label: "废旧仓库名称",
			width: 120
		},
		{
			prop: "scrapReason",
			label: "报废原因",
			width: 100,
			align: "right"
		}
	]

	switch (formData.value.type) {
		/* case IWasteOldType.matPick:
			return tableColFilter(ls, ["低值类型", "使用人", "分配数量"])
		case IWasteOldType.matHistory:
			return tableColFilter(ls, ["低值类型", "使用人", "分配数量"]) */
		case IWasteOldType.matLow:
			return tableColFilter(ls, [
				//"备用数量",
				//"周转件仓库名称",
				"返修数量",
				"维修仓库名称",
				//"备用损坏描述",
				"返修损坏描述",
				"批次号"
			])
		case IWasteOldType.matRepair:
			return tableColFilter(ls, [
				//"备用数量",
				//"周转件仓库名称",
				"返修数量",
				"维修仓库名称",
				//"备用损坏描述",
				"返修损坏描述",
				"低值类型",
				"使用人",
				"分配数量",
				"批次号"
			])
		case IWasteOldType.matInStore:
			return tableColFilter(ls, [
				//"备用数量",
				//"周转件仓库名称",
				//"备用损坏描述",
				"低值类型",
				"使用人",
				"分配数量"
			])
		default:
			return tableColFilter(ls, ["低值类型", "使用人", "分配数量", "批次号"])
	}
})

fetchFunc.value = listWasteOldApplyPageItem

watch(tableData, () => {
	batchFormatterNumView(tableData.value)
})

const getTableData = (data?: any) => {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		...data
	}
	fetchTableData()
}

/**
 * 获取详情数据
 */
async function getDetail() {
	drawerLoading.value = true
	try {
		const r = await listWasteOldApplyDetail(props.id)
		formData.value = r
	} finally {
		drawerLoading.value = false
	}
}

onMounted(() => {
	getDictByCodeList(["EXCHANGE_TPYE", "MAIN_MATERIALS", "INVENTORY_UNIT"])

	fetchParam.value.sord = "desc"
	fetchParam.value.sidx = "createdDate"

	if (props.id) {
		fetchParam.value.wasteApplyId = props.id

		fetchTableData()
		getDetail()
	}
})
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<div class="drawer-column left" style="width: 310px">
			<Title :title="leftTitleConf" />

			<el-scrollbar>
				<el-descriptions size="small" :column="1" border class="content">
					<template>
						<el-descriptions-item
							v-for="desc in descList"
							:key="desc.key"
							:label="desc.label"
						>
							<span v-if="desc.key === 'type'">
								{{
									dictFilter("EXCHANGE_TPYE", formData?.[desc.key])
										?.subitemName || "---"
								}}
							</span>
							<span v-else-if="desc.needTooltip">
								<el-tooltip
									effect="dark"
									:content="formData?.[desc.key]"
									:disabled="
										getRealLength(formData?.[desc.key]) <= 100 ? true : false
									"
								>
									{{
										getRealLength(formData?.[desc.key]) > 100
											? setString(formData?.[desc.key], 100)
											: formData?.[desc.key] || "---"
									}}
								</el-tooltip>
							</span>
							<span v-else>
								{{ formData?.[desc.key] || "---" }}
							</span>
						</el-descriptions-item>
					</template>
				</el-descriptions>
			</el-scrollbar>
		</div>
		<div
			class="drawer-column right"
			style="width: calc(100% - 310px)"
			:class="{ pdr10: !footerBtnVisible }"
		>
			<Title :title="extraTitleConf">
				<Tabs
					style="margin-right: auto; margin-left: 30px"
					:tabs="tabsConf"
					@on-tab-change="handleTabChange"
				/>
			</Title>

			<el-scrollbar class="rows">
				<Query
					:queryArrList="queryArrList"
					@getQueryData="getTableData"
					class="custom-q"
					v-if="activatedTab === 0"
				/>
				<pitaya-table
					v-if="activatedTab === 0"
					ref="tableRef"
					:columns="(tbInit.tableProp as any)"
					:table-data="tableData"
					:need-selection="false"
					:single-select="false"
					:need-index="true"
					:need-pagination="true"
					:total="pageTotal"
					:table-loading="tableLoading"
					@on-selection-change="selectedTableList = $event"
					@on-current-page-change="onCurrentPageChange"
				>
					<!-- 预估回收重量 -->
					<template #recoveryWeight="{ rowData }">
						{{ toFixedTwo(rowData.recoveryWeight) }}
					</template>
					<!-- 库存单位 -->
					<template #useUnit="{ rowData }">
						{{
							dictFilter("INVENTORY_UNIT", rowData.useUnit)?.subitemName ||
							"---"
						}}
					</template>

					<!-- 废旧物资分类 -->
					<template #wasteMaterialType="{ rowData }">
						<dict-tag
							:options="DictApi.getWasteMaterialType()"
							:value="rowData.wasteMaterialType"
						/>
					</template>

					<!-- 主要材质 -->
					<template #quality="{ rowData }">
						{{
							dictFilter("MAIN_MATERIALS", rowData.quality)?.subitemName ||
							"---"
						}}
					</template>

					<!-- 辅助材质 -->
					<template #auxiliaryQuality="{ rowData }">
						{{
							dictFilter("MAIN_MATERIALS", rowData.auxiliaryQuality as any)
								?.subitemName || "---"
						}}
					</template>
				</pitaya-table>

				<table-file
					v-else
					:business-type="fileBusinessType.wasteHandoverApply"
					:business-id="id"
					:mod="IModalType.view"
				/>
			</el-scrollbar>
			<button-list
				v-if="footerBtnVisible"
				class="footer"
				:button="drawerBtnRightConf"
				@on-btn-click="handleFormBtnClick"
			/>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.custom-q {
	margin: 10px 0px -10px 10px !important;
}
</style>
