<!-- 交旧申请 - 物资明细 编辑 Table -->
<template>
	<div class="mat-table-wrap" v-loading="tableLoading">
		<Query
			:queryArrList="queryArrList"
			@getQueryData="getTableData"
			class="custom-q"
		/>

		<div class="mat-table-title" v-if="tableData && tableData.length > 0">
			共添加 <span style="font-weight: bold">{{ total }}</span> 项物资
		</div>
		<el-scrollbar
			:max-height="`${maxTableHeight - 100}px`"
			style="height: auto"
		>
			<div class="mat-table-box" v-if="tableData && tableData.length > 0">
				<div
					class="mat-table-item"
					v-for="(item, index) in tableData"
					:key="index"
					:style="{ color: includes(props.errorIdList, item.id) ? 'red' : '' }"
				>
					<div class="check-box">
						<input
							type="checkbox"
							:value="item.id"
							v-model="selectedTableList"
							@change="updateSelectAll"
						/>
					</div>
					<div class="base-info">
						<el-row>
							<el-col :span="12" class="base-info-content">
								<span>物资编码：</span>
								{{ item.materialCode }}
							</el-col>
							<el-col :span="12" class="base-info-content">
								<el-tooltip
									effect="dark"
									:content="item.materialLabel"
									:disabled="
										getRealLength(item.materialLabel!) <= 12 ? true : false
									"
								>
									<span>
										物资名称：{{
											getRealLength(item.materialLabel!) > 12
												? setString(item.materialLabel!, 12)
												: item.materialLabel || "---"
										}}
									</span>
								</el-tooltip>
							</el-col>
						</el-row>
						<el-row>
							<el-col :span="12" class="base-info-content">
								<el-tooltip
									effect="dark"
									:content="item.version"
									:disabled="
										getRealLength(item.version!) <= 12 ? true : false
									"
								>
									<span>
										规格型号：{{
											getRealLength(item.version!) > 12
												? setString(item.version!, 12)
												: item.version || "---"
										}}
									</span>
								</el-tooltip>
							</el-col>
							<el-col :span="12" class="base-info-content">
								<span>库存单位：</span>
								{{
									dictFilter("INVENTORY_UNIT", item.useUnit as any)
										?.subitemName || "---"
								}}
							</el-col>
						</el-row>
						<el-row>
							<el-col :span="12" class="base-info-content">
								<el-tooltip
									effect="dark"
									:content="item.technicalParameter"
									:disabled="
										getRealLength(item.technicalParameter!) <= 12 ? true : false
									"
								>
									<span>
										技术参数：{{
											getRealLength(item.technicalParameter!) > 12
												? setString(item.technicalParameter!, 12)
												: item.technicalParameter || "---"
										}}
									</span>
								</el-tooltip>
							</el-col>
							<el-col
								:span="12"
								class="base-info-content"
								style="display: flex; align-items: center"
							>
								<span>废旧物资分类：</span>
								<span>
									<dict-tag
										:options="DictApi.getWasteMaterialType()"
										:value="(item.wasteMaterialType as any)"
									/>
								</span>
							</el-col>
							<!-- <el-col
								:span="12"
								class="base-info-content"
								:title="item.lowValueType_view"
								v-if="props.type == IWasteOldType.matLow"
							>
								<span>低值类型：</span>{{ item.lowValueType_view }}
							</el-col> -->
						</el-row>
						<el-row>
							<el-col :span="12" class="base-info-content">
								<span>主要材质：</span>
								{{
									dictFilter("MAIN_MATERIALS", item.quality as any)
										?.subitemName || "---"
								}}
							</el-col>
							<el-col
								:span="12"
								class="base-info-content"
								:title="item.auxiliaryQuality"
							>
								<span>辅助材质</span>
								{{
									dictFilter("MAIN_MATERIALS", item.auxiliaryQuality as any)
										?.subitemName || "---"
								}}
							</el-col>
						</el-row>
					</div>
					<div class="base-info-center">
						<div class="top">
							<el-row>
								<el-col :span="10" style="height: 35px; line-height: 35px">
									<span> <i class="red">*</i> 预估回收重量（KG）： </span>
								</el-col>
								<el-col
									:span="12"
									class="base-info-content"
									style="height: 35px"
								>
									<el-input
										class="no-arrows"
										v-model="item.recoveryWeight"
										type="number"
										:min="0"
										@click.stop
										@input="
											item.recoveryWeight = validateAndCorrectInput($event)
										"
										@change="validateNum(item, 'recoveryWeight')"
									/>
								</el-col>
							</el-row>
							<el-row
								v-if="props.type == IWasteOldType.matLow"
								style="margin-top: 10px"
							>
								<el-col
									:span="8"
									class="base-info-content"
									:title="item.lowValueType_view"
								>
									<span>低值类型：</span>{{ item.lowValueType_view }}
								</el-col>
								<el-col :span="16" class="base-info-content">
									<span style="padding-left: 20px">使用人：</span>
									{{ item.allocationUsername_view || "--" }}
								</el-col>
							</el-row>
							<el-row
								v-else-if="props.type == IWasteOldType.matInStore"
								style="margin-top: 10px"
							>
								<el-col
									:span="24"
									class="base-info-content"
									:title="item.batchNo"
								>
									<span>批次号：</span>{{ item.batchNo || "--" }}
								</el-col>
							</el-row>
						</div>

						<div class="bottom" v-if="props.type == IWasteOldType.matInStore">
							<div class="bottom-content">
								<div class="value">
									{{ convertToUnit(parseInt((item.storeNum ?? 0) as any)).num }}
									<!-- {{ parseInt(item.storeNum ?? 0) }} -->
								</div>
								<div class="label">
									<!-- 库存数量 -->
									{{
										convertToUnit(parseInt((item.storeNum ?? 0) as any)).unit
											? `库存数量(${
													convertToUnit(parseInt((item.storeNum ?? 0) as any))
														.unit
											  })`
											: "库存数量"
									}}
								</div>
							</div>
							<div class="bottom-content">
								<div class="value">
									{{
										convertToUnit(parseInt((item.canOldNum ?? 0) as any)).num
									}}
									<!-- {{ parseInt((item.canOldNum ?? 0) as any) }} -->
								</div>
								<div class="label">
									<!-- 可交旧数量 -->
									{{
										convertToUnit(parseInt((item.canOldNum ?? 0) as any)).unit
											? `可交旧数量(${
													convertToUnit(parseInt((item.canOldNum ?? 0) as any))
														.unit
											  })`
											: "可交旧数量"
									}}
								</div>
							</div>
							<div class="bottom-content">
								<div class="value">
									{{
										convertToUnit(parseInt((item.freezeNum ?? 0) as any)).num
									}}
									<!-- {{ parseInt(item.freezeNum ?? 0) }} -->
								</div>
								<div class="label">
									<!-- 冻结数量 -->
									{{
										convertToUnit(parseInt((item.freezeNum ?? 0) as any)).unit
											? `冻结量(${
													convertToUnit(parseInt((item.freezeNum ?? 0) as any))
														.unit
											  })`
											: "冻结量"
									}}
								</div>
							</div>
						</div>

						<div class="bottom" v-else>
							<div
								class="bottom-content"
								v-if="props.type == IWasteOldType.matLow"
							>
								<div class="value">
									<!-- {{ parseInt(item.allocationNum ?? 0) }} -->
									{{
										convertToUnit(parseInt((item.allocationNum ?? 0) as any))
											.num
									}}
								</div>
								<div class="label">
									<!-- 分配数量 -->
									{{
										convertToUnit(parseInt((item.allocationNum ?? 0) as any))
											.unit
											? `分配数量(${
													convertToUnit(
														parseInt((item.allocationNum ?? 0) as any)
													).unit
											  })`
											: "分配数量"
									}}
								</div>
							</div>
							<div
								class="bottom-content"
								v-if="props.type != IWasteOldType.matLow"
							>
								<!-- {{ parseInt(item.outNum as any) || 0 }} -->
								<div class="value">
									{{ convertToUnit(parseInt((item.outNum ?? 0) as any)).num }}
								</div>
								<div class="label">
									{{
										convertToUnit(parseInt((item.outNum ?? 0) as any)).unit
											? `已出库数量(${
													convertToUnit(parseInt((item.outNum ?? 0) as any))
														.unit
											  })`
											: "已出库数量"
									}}
								</div>
							</div>
							<div class="bottom-content">
								<div class="value">
									{{
										convertToUnit(parseInt((item.canOldNum ?? 0) as any)).num
									}}
								</div>
								<div class="label">
									{{
										convertToUnit(parseInt((item.canOldNum ?? 0) as any)).unit
											? `可交旧数量(${
													convertToUnit(parseInt((item.canOldNum ?? 0) as any))
														.unit
											  })`
											: "可交旧数量"
									}}
								</div>
								<!-- <div class="value">
									{{ parseInt(item.canOldNum as any) || 0 }}
								</div>
								<div class="label">可交旧数量</div> -->
							</div>
							<div class="bottom-content">
								<div class="value">
									{{
										convertToUnit(parseInt((item.completeOldNum ?? 0) as any))
											.num
									}}
								</div>
								<div class="label">
									{{
										convertToUnit(parseInt((item.completeOldNum ?? 0) as any))
											.unit
											? `已交旧数量(${
													convertToUnit(
														parseInt((item.completeOldNum ?? 0) as any)
													).unit
											  })`
											: "已交旧数量"
									}}
								</div>
								<!-- <div class="value">
									{{ parseInt(item.completeOldNum as any) || 0 }}
								</div>
								<div class="label">已交旧数量</div> -->
							</div>
						</div>
					</div>
					<div class="base-info-edit">
						<el-row :gutter="10" style="display: none">
							<el-col :span="2" class="label">备用数量</el-col>
							<el-col :span="5">
								<el-input
									class="no-arrows"
									v-model="item.standbyNum"
									:disabled="disabledEditorStandbyInfo ? true : false"
									@click.stop
									@input="item.standbyNum = validateAndCorrectInput($event)"
									@change="validateNum(item, IWasteRepairAndScrap.standby)"
								/>
							</el-col>
							<el-col :span="2" class="label">周转件库</el-col>
							<!--  周转件正常库 -->
							<el-col :span="8">
								<el-input
									v-model.trim="item.standbyStoreName"
									placeholder="请选择仓库"
									:disabled="disabledEditorStandbyInfo ? true : false"
									@click="selectedStore(item, IWasteRepairAndScrap.standby)"
									readonly
								>
									<template #suffix>
										<CircleClose
											v-if="item.standbyStoreName"
											@click.stop="
												handleClearStore(item, IWasteRepairAndScrap.standby)
											"
											style="width: 15px"
										/>
									</template>

									<template #append>
										<font-awesome-icon
											:icon="['fas', 'layer-group']"
											style="color: #ccc"
											@click="
												disabledEditorStandbyInfo
													? ''
													: selectedStore(item, IWasteRepairAndScrap.standby)
											"
										/>
									</template>
								</el-input>
							</el-col>
							<el-col :span="2" class="label">损坏描述</el-col>
							<el-col :span="5">
								<el-input
									class="no-arrows"
									maxlength="50"
									show-word-limit
									v-model="item.standbyDamageDescribe"
									:disabled="disabledEditorStandbyInfo ? true : false"
									@change="validateReason(item)"
								/>
							</el-col>
						</el-row>
						<el-row :gutter="10">
							<el-col :span="2" class="label">返修数量 </el-col>
							<el-col :span="5">
								<!-- 本次返修数量 fixFlag: 0，不可填写返修数量和待维修仓库 -->

								<el-input
									class="no-arrows"
									v-model="item.repairNum"
									@click.stop
									@input="item.repairNum = validateAndCorrectInput($event)"
									:disabled="
										item.fixFlag == '1' &&
										props.type != IWasteOldType.matLow &&
										props.type != IWasteOldType.matRepair &&
										!(props.type == IWasteOldType.matInStore && item.isLow == 1)
											? false
											: true
									"
									@change="validateNum(item, IWasteRepairAndScrap.repair)"
								/>
							</el-col>
							<el-col :span="2" class="label">维修仓库</el-col>
							<el-col :span="8">
								<el-input
									v-model.trim="item.repairStoreName"
									placeholder="请选择仓库"
									:disabled="
										item.fixFlag == '1' &&
										props.type != IWasteOldType.matLow &&
										props.type != IWasteOldType.matRepair &&
										!(props.type == IWasteOldType.matInStore && item.isLow == 1)
											? false
											: true
									"
									@click="selectedStore(item, IWasteRepairAndScrap.repair)"
									readonly
								>
									<template #suffix>
										<CircleClose
											v-if="item.repairStoreName"
											@click.stop="
												handleClearStore(item, IWasteRepairAndScrap.repair)
											"
											style="width: 15px"
										/>
									</template>

									<template #append>
										<font-awesome-icon
											:icon="['fas', 'layer-group']"
											style="color: #ccc"
											@click="
												item.fixFlag == '1' &&
												props.type != IWasteOldType.matLow &&
												props.type != IWasteOldType.matRepair &&
												!(
													props.type == IWasteOldType.matInStore &&
													item.isLow == 1
												)
													? selectedStore(item, IWasteRepairAndScrap.repair)
													: ''
											"
										/>
									</template>
								</el-input>
							</el-col>

							<el-col :span="2" class="label">损坏描述</el-col>
							<el-col :span="5">
								<el-input
									class="no-arrows"
									maxlength="50"
									show-word-limit
									v-model="item.repairDamageDescribe"
									:disabled="
										item.fixFlag == '1' &&
										props.type != IWasteOldType.matLow &&
										props.type != IWasteOldType.matRepair &&
										!(props.type == IWasteOldType.matInStore && item.isLow == 1)
											? false
											: true
									"
									@change="validateReason(item)"
								/>
							</el-col>
						</el-row>
						<!-- 低值只有报废数量 -->
						<el-row :gutter="10">
							<el-col :span="2" class="label">报废数量</el-col>
							<el-col :span="5">
								<el-input
									class="no-arrows"
									v-model="item.scrapNum"
									@click.stop
									@input="item.scrapNum = validateAndCorrectInput($event)"
									@change="validateNum(item, IWasteRepairAndScrap.scrap)"
								/>
							</el-col>
							<el-col :span="2" class="label">废旧仓库</el-col>
							<el-col :span="8">
								<el-input
									v-model.trim="item.scrapStoreName"
									placeholder="请选择仓库"
									@click="selectedStore(item, IWasteRepairAndScrap.scrap)"
									readonly
								>
									<template #suffix>
										<CircleClose
											v-if="item.scrapStoreName"
											@click.stop="
												handleClearStore(item, IWasteRepairAndScrap.scrap)
											"
											style="width: 15px"
										/>
									</template>

									<template #append>
										<font-awesome-icon
											:icon="['fas', 'layer-group']"
											style="color: #ccc"
											@click="selectedStore(item, IWasteRepairAndScrap.scrap)"
										/>
									</template>
								</el-input>
							</el-col>
							<el-col :span="2" class="label">报废原因</el-col>
							<el-col :span="5">
								<el-input
									class="no-arrows"
									maxlength="50"
									show-word-limit
									v-model="item.scrapReason"
									@change="validateReason(item)"
								/>
							</el-col>
						</el-row>
					</div>
				</div>
			</div>

			<div class="empty-table" v-else>
				<EmptyData class="empty_img" />
				<p>未查询到相关数据</p>
			</div>
		</el-scrollbar>
		<div class="table-footer-operate">
			<div class="footer-operate-left">
				<button-list
					:button="tbBtnConf"
					:is-not-radius="true"
					:loading="formBtnLoading"
					@on-btn-click="handleTabsClick"
				/>
			</div>
			<div class="footer-operate-pagination">
				<el-pagination
					@current-change="currentPagechange"
					@size-change="handleSizeChange1"
					v-model:current-page="paginationData.currentPage"
					v-model:page-size="paginationData.pageSize"
					:total="total"
					:page-sizes="[10, 20, 30, 40, 50]"
					:layout="`prev, pager, next, sizes, total, jumper, slot`"
					:pager-count="5"
					prev-text="上一页"
					next-text="下一页"
				>
					<button class="jumper-slot-btn">GO</button>
				</el-pagination>
			</div>
		</div>
	</div>

	<!-- 物资编码手册选择器 -->
	<Drawer
		v-model:drawer="goodsSelectorVisible"
		:size="modalSize.lg"
		destroy-on-close
	>
		<mat-selector
			v-if="props.type != IWasteOldType.matHistory"
			:table-req-params="matSelectorApiParams"
			:table-api="matSelectorApi"
			:columns="matSelectColumns"
			:multiple="true"
			:title="matGetTicketTitle"
			:queryArrList="
				props.type == IWasteOldType.matLow
					? matUseApplyQueryArrList
					: props.type == IWasteOldType.matRepair
					? matRepairApplyQueryArrList
					: null
			"
			@save="handleAddGoods"
			@close="goodsSelectorVisible = false"
		/>
		<mat-manual-list
			v-else
			:table-req-params="{
				sord: 'asc',
				sidx: 'code',
				businessId: props.id,
				status: `${matStatus.normal},${matStatus.freeze}` // 正常,冻结
			}"
			:table-api="listOldMaterialCodePaged"
			@on-selected="handleAddGoods"
			@on-closed="goodsSelectorVisible = false"
		/>
	</Drawer>

	<!-- 仓库选择 -->
	<Drawer
		v-model:drawer="handoverSelectorVisible"
		:size="modalSize.lg"
		destroy-on-close
	>
		<!-- 返修库: 周转件待修库; 废旧：废旧库； 周转件正常库 -->
		<store-table
			@on-save="handleStoreSelect"
			:selected-ids="[selectedStoreIds]"
			:tableApiParams="{
				type: selectedStoreType
			}"
		/>
	</Drawer>
</template>
<script setup lang="ts">
import {
	deleteWasteOldApplyBatchItem,
	listWasteOldApplyPageItem,
	listOldMaterialCodePaged,
	addWasteOldApplyBatchItem,
	listMatInStoreSelectorPaged,
	batchUpdateStore
} from "@/app/baseline/api/waste/handover-apply"
import { usePagination } from "@/app/platform/hooks/usePagination"
import { useDictInit } from "../../components/dictBase"
import { DictApi, matStatus } from "@/app/baseline/api/dict"
import dictTag from "../../components/dictTag.vue"
import {
	IWasteOldType,
	MatWasteOldApplyItemPageVo,
	IWasteRepairAndScrap
} from "@/app/baseline/utils/types/waste-handover-apply"
import {
	getIdempotentToken,
	validateAndCorrectInput
} from "@/app/baseline/utils/validate"
import { modalSize } from "@/app/baseline/utils/layout-config"
import storeTable from "../../store/components/storeTable.vue"
import { findIndex, map, includes, toNumber, round } from "lodash-es"
import { useMessageBoxInit } from "../../components/messageBox"
import { IInventoryBusinessType } from "@/app/baseline/utils/types/store-inventory"
import { IWarehouseType } from "@/app/baseline/utils/types/store-manage"
import { listCanPickApplyItemPaged } from "@/app/baseline/api/store/mat-get-apply-api"
import { listSelectLowValueUseApplyItemPage } from "@/app/baseline/api/lowValue/requisitionApply"
import matSelector from "../../store/components/matSelector.vue"
import matManualList from "../../plan/components/matManualList.vue"
import { maxTableHeight, convertToUnit } from "@/app/baseline/utils"
import EmptyData from "@/assets/images/empty/empty_data.svg?component"
import { listRepairAddPageItem } from "@/app/baseline/api/store/other-warehousing-api"
import { setString, getRealLength } from "@/app/baseline/utils/validate"
import { useUserStore } from "@/app/platform/store/modules/user"
import { CircleClose } from "@element-plus/icons-vue"
import {
	IIdempotentTokenType,
	IIdempotentTokenTypePre
} from "@/app/baseline/utils/types/common"

const props = defineProps<{
	id?: any // id
	type: IWasteOldType
	applyId?: any
	applyCode?: any
	errorIdList?: any[]
	storeCode?: string
	roomCode?: string
}>()

const { userInfo } = storeToRefs(useUserStore())

const formBtnLoading = ref(false) // 表单按钮loading
const selectedTableList = ref<any[]>([])

const disabledEditorStandbyInfo = computed(() => {
	return (
		props.type == IWasteOldType.matLow ||
		props.type == IWasteOldType.matRepair ||
		props.type == IWasteOldType.matInStore
	)
})

const { dictOptions, dictFilter, getDictByCodeList } = useDictInit()

const { showDelConfirm, showWarnConfirm } = useMessageBoxInit()

const { paginationData, handleSizeChange, handleCurrentChange } =
	usePagination()
const pageSize = computed(() => {
	return paginationData.pageSize
})
const currentPage = computed(() => {
	return paginationData.currentPage
})

const tableLoading = ref(true)

const total = ref<any>(0)
const tableData = ref<MatWasteOldApplyItemPageVo[]>([])
const tableCache = ref<MatWasteOldApplyItemPageVo[]>([])
const currentPagechange = (value: number) => {
	handleCurrentChange(value)

	getList(fetchParam.value)
}

const handleSizeChange1 = (value: number) => {
	handleSizeChange(value)
	handleCurrentChange(1)
	getList(fetchParam.value)
}
async function getList(data?: Record<string, any>) {
	tableLoading.value = true
	try {
		const res = await listWasteOldApplyPageItem({
			wasteApplyId: props.id,
			pageSize: pageSize.value,
			currentPage: currentPage.value,
			sord: "desc",
			sidx: "createdDate",
			...data
		})

		total.value = res.records
		tableData.value = res.rows || []

		tableCache.value.length = 0
		tableCache.value.push(...JSON.parse(JSON.stringify(res.rows)))
	} finally {
		tableLoading.value = false
	}
}

/**
 * 查询条件 配置
 */
const queryArrList = computed(() => [
	{
		name: "物资编码",
		key: "materialCode",
		placeholder: "请输入物资编码",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资名称",
		key: "materialLabel",
		placeholder: "请输入物资名称",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "规格型号",
		key: "version",
		placeholder: "请输入规格型号",
		enableFuzzy: true,
		type: "input"
	}
])

const fetchParam = ref({})
const getTableData = (data?: Record<string, any>) => {
	if (props.id) {
		//tableRef.value?.resetCurrentPage()

		paginationData.currentPage = 1
		//paginationData.pageSize = 20

		fetchParam.value = {
			...fetchParam.value,
			...data
		}

		getList(fetchParam.value)
	}
}

onMounted(() => {
	getDictByCodeList([
		"INVENTORY_UNIT",
		"MAIN_MATERIALS",
		"LOW_VALUE_TYPE",
		`${userInfo.value.companyCode}_DEPOT`,
		"STORE_LEVEL",
		//"AUXILIARY_MATERIALS",
		"MATERIAL_NATURE"
	])
	getList()
})

/**
 * 翻页 回显已编辑的数据
 */
watch(
	() => tableData.value,
	() => {
		if (editedTableRowStack.value.length) {
			editedTableRowStack.value.map((e) => {
				const rowIdx = findIndex(tableData.value, (v) => v.id === e.id)
				if (rowIdx !== -1) {
					tableData.value.splice(rowIdx, 1, { ...e })
				}
			})
		}
	}
)

function updateSelectAll(event: any) {
	event.stopPropagation()

	if (event.target.checked) {
		console.log(selectedTableList.value)
	} else {
		const index = selectedTableList.value.findIndex(
			(item) => item == event.target.value
		)
		if (index != -1) {
			selectedTableList.value.splice(index, 1)
		}
	}
}

/**
 * 编辑后的table row 数据
 */
const editedTableRowStack = ref<any[]>([])

/**
 * 当前编辑的行
 */
const editRow = ref<any>([])
const editStoreType = ref() // repair 返修；scrap 报修; standby 备用

const handoverSelectorVisible = ref(false)

/**
 * 仓库选择器选中的仓库id；用于回显
 */
const selectedStoreIds = computed(() => {
	switch (editStoreType.value) {
		case IWasteRepairAndScrap.repair:
			return editRow.value.repairStoreId
		case IWasteRepairAndScrap.scrap:
			return editRow.value.scrapStoreId
		case IWasteRepairAndScrap.standby:
			return editRow.value.standbyStoreId
		default:
			return []
	}
})

/**
 * 仓库选择器选 仓库类型
 */
const selectedStoreType = computed(() => {
	switch (editStoreType.value) {
		case IWasteRepairAndScrap.repair:
			return IWarehouseType.rotablesWaitingRepair
		case IWasteRepairAndScrap.scrap:
			return IWarehouseType.waste + "," + IWarehouseType.dangerousWaste
		case IWasteRepairAndScrap.standby:
			return (
				IWarehouseType.rotablesNormal + "," + IWarehouseType.sparePartsWarehouse
			)
		default:
			return []
	}
})

/**
 * 较验：可交旧数量与 本次报废数量
 *
 * 领料单交旧：本次返修数量(repairNum) +本次报废数量(scrapNum) + 本次备用数量(standbyNum) <= 可交旧数量(canOldNum)
 * 历史物资交旧：不做数量较验
 * 低值易耗交旧：本次报废数量<=可交旧数量
 */
function validateNum(e: any, type: string) {
	if (type === "recoveryWeight") {
		const num = toNumber(e.recoveryWeight)
		if (num <= 0) {
			const oldRow = tableCache.value.find((r) => r.id == e.id)
			e.recoveryWeight = oldRow?.recoveryWeight
			ElMessage.warning("预估回收重量不能小于等于0！")
			return false
		} else {
			e.recoveryWeight = num
		}
	} else if (props.type != IWasteOldType.matHistory) {
		if (type === IWasteRepairAndScrap.repair) {
			const canGetNum = round(
				(e.canOldNum ?? 0) - (e.scrapNum ?? 0) - (e.standbyNum ?? 0),
				4
			)

			const num = toNumber(e.repairNum)

			if (
				e.repairNum === "" ||
				e.repairNum === null ||
				e.repairNum === undefined
			) {
				e.repairNum === ""
			} else if (num > canGetNum) {
				ElMessage.warning("本次返修数量+本次报废数量 不能大于可交旧数量")

				e.repairNum = canGetNum === 0 ? "" : canGetNum
			} else if (num <= 0) {
				const oldRow = tableCache.value.find((r) => r.id == e.id)
				e.repairNum = oldRow?.repairNum
				ElMessage.warning("返修数量不能小于等于0！")
				return false
			} else {
				e.repairNum = num
			}
		} else if (type === IWasteRepairAndScrap.scrap) {
			const canGetNum = round(
				(e.canOldNum ?? 0) - (e.repairNum ?? 0) - (e.standbyNum ?? 0),
				4
			)
			const num = toNumber(e.scrapNum)

			if (
				e.scrapNum === "" ||
				e.scrapNum === null ||
				e.scrapNum === undefined
			) {
				e.scrapNum === ""
			} else if (num > canGetNum) {
				ElMessage.warning("本次返修数量+本次报废数量 不能大于可交旧数量")
				e.scrapNum = canGetNum === 0 ? "" : canGetNum
			} else if (num <= 0) {
				const oldRow = tableCache.value.find((r) => r.id == e.id)
				e.scrapNum = oldRow?.scrapNum
				ElMessage.warning("报废数量不能小于等于0！")
				return false
			} else {
				e.scrapNum = num
			}
		} else if (type === IWasteRepairAndScrap.standby) {
			const canGetNum =
				(e.canOldNum ?? 0) - (e.repairNum ?? 0) - (e.scrapNum ?? 0)
			const num = toNumber(e.standbyNum)

			if (
				e.standbyNum === "" ||
				e.standbyNum === null ||
				e.standbyNum === undefined
			) {
				e.standbyNum === ""
			} else if (num > canGetNum) {
				ElMessage.warning(
					"本次备用数量+本次返修数量+本次报废数量 不能大于可交旧数量"
				)
				e.standbyNum = canGetNum === 0 ? "" : canGetNum
			} else if (num <= 0) {
				const oldRow = tableCache.value.find((r) => r.id == e.id)
				e.standbyNum = oldRow?.standbyNum
				ElMessage.warning("备用数量不能小于等于0！")
				return false
			} else {
				e.standbyNum = num
			}
		}
	} else {
		if (type === IWasteRepairAndScrap.repair) {
			const num = toNumber(e.repairNum)

			e.repairNum = num
			if (
				e.repairNum === "" ||
				e.repairNum === null ||
				e.repairNum === undefined
			) {
				e.repairNum === ""
			} else if (num <= 0) {
				const oldRow = tableCache.value.find((r) => r.id == e.id)
				e.repairNum = oldRow?.repairNum
				ElMessage.warning("返修数量不能小于等于0！")
				return false
			}
		} else if (type === IWasteRepairAndScrap.scrap) {
			const num = toNumber(e.scrapNum)
			e.scrapNum = num
			if (
				e.scrapNum === "" ||
				e.scrapNum === null ||
				e.scrapNum === undefined
			) {
				e.scrapNum === ""
			} else if (num <= 0) {
				const oldRow = tableCache.value.find((r) => r.id == e.id)
				e.scrapNum = oldRow?.scrapNum
				ElMessage.warning("报废数量不能小于等于0！")
				return false
			}
		} else if (type === IWasteRepairAndScrap.standby) {
			const num = toNumber(e.standbyNum)

			e.standbyNum = num
			if (
				e.standbyNum === "" ||
				e.standbyNum === null ||
				e.standbyNum === undefined
			) {
				e.standbyNum === ""
			} else if (num <= 0) {
				const oldRow = tableCache.value.find((r) => r.id == e.id)
				e.standbyNum = oldRow?.standbyNum
				ElMessage.warning("备用数量不能小于等于0！")
				return false
			}
		}
	}

	handleInputBlur(e)
}

/**
 * 保存 文本域内容 原因
 */
function validateReason(e: any) {
	handleInputBlur(e)
}

/**
 * 输入框失焦 handler
 *
 * 失焦后，记录编辑过的数据，用于保存任务数据
 */
function handleInputBlur(e: any) {
	if (!editedTableRowStack.value.length) {
		editedTableRowStack.value.push({ ...e })
		return
	}

	/**
	 * 队列存在数据，验证重复
	 *
	 * - 不重复->push
	 * - 重复->更新该条数据
	 */
	const rowIdx = findIndex(editedTableRowStack.value, (v) => v.id === e.id)
	if (rowIdx !== -1) {
		editedTableRowStack.value.splice(rowIdx, 1, { ...e })
		return
	}

	editedTableRowStack.value.push({ ...e })
}

function selectedStore(e: MatWasteOldApplyItemPageVo, type: string) {
	editRow.value = e
	editStoreType.value = type
	handoverSelectorVisible.value = true
	isAllChangeScrapStore.value = false
	isAllChangeRepairStore.value = false
}

/**
 * 清空仓库信息
 * @param e
 * @param type
 */
function handleClearStore(e: MatWasteOldApplyItemPageVo, type: string) {
	editStoreType.value = type
	if (editStoreType.value == IWasteRepairAndScrap.repair) {
		editRow.value = {
			...e,
			repairStoreId: null,
			repairStoreName: ""
		}
	} else if (editStoreType.value == IWasteRepairAndScrap.scrap) {
		editRow.value = {
			...e,
			scrapStoreId: null,
			scrapStoreName: ""
		}
	} else if (editStoreType.value == IWasteRepairAndScrap.standby) {
		editRow.value = {
			...e,
			standbyStoreId: null,
			standbyStoreName: ""
		}
	}

	// 双向绑定 table仓库
	const rowIdx = findIndex(tableData.value, (v) => v.id === editRow.value.id)
	if (rowIdx !== -1) {
		tableData.value.splice(rowIdx, 1, { ...editRow.value })
	}

	handleInputBlur(editRow.value)
}

/**
 *	仓库保存
 * @param btnName
 * @param e
 */
async function handleStoreSelect(btnName: string, e?: any) {
	if (btnName === "保存") {
		if (
			editStoreType.value == IWasteRepairAndScrap.repair &&
			!isAllChangeRepairStore.value
		) {
			editRow.value = {
				...editRow.value,
				repairStoreId: e?.id,
				repairStoreName: e?.label
			}
		} else if (
			editStoreType.value == IWasteRepairAndScrap.scrap &&
			!isAllChangeScrapStore.value
		) {
			editRow.value = {
				...editRow.value,
				scrapStoreId: e?.id,
				scrapStoreName: e?.label
			}
		} else if (editStoreType.value == IWasteRepairAndScrap.standby) {
			editRow.value = {
				...editRow.value,
				standbyStoreId: e?.id,
				standbyStoreName: e?.label
			}
		}

		if (
			(isAllChangeScrapStore.value || isAllChangeRepairStore.value) &&
			(editStoreType.value == IWasteRepairAndScrap.scrap ||
				editStoreType.value == IWasteRepairAndScrap.repair)
		) {
			await showWarnConfirm("将所选物资放入所选仓库吗？")

			/**
			 * 批量更新废旧/维修仓库
			 * type: 更新仓库类型 1维修 2废旧
			 */
			await batchUpdateStore({
				itemIdList: selectedTableList.value,
				type: editStoreType.value == IWasteRepairAndScrap.scrap ? "2" : "1",
				storeId: e?.id
			})
			getList()
			for (let i = 0; i < selectedTableList.value.length; i++) {
				const rowIdx = findIndex(
					editedTableRowStack.value,
					(v) => v.id === selectedTableList.value[i]
				)

				if (rowIdx !== -1) {
					if (editStoreType.value == IWasteRepairAndScrap.scrap) {
						editedTableRowStack.value.splice(rowIdx, 1, {
							...editedTableRowStack.value[rowIdx],
							scrapStoreId: e?.id,
							scrapStoreName: e?.label
						})
					}

					if (editStoreType.value == IWasteRepairAndScrap.repair) {
						editedTableRowStack.value.splice(rowIdx, 1, {
							...editedTableRowStack.value[rowIdx],
							repairStoreId: e?.id,
							repairStoreName: e?.label
						})
					}
				} else {
					const row = tableData.value.find(
						(r) => r.id === selectedTableList.value[i]
					)
					if (editStoreType.value == IWasteRepairAndScrap.scrap) {
						editedTableRowStack.value.push({
							...row,
							scrapStoreId: e?.id,
							scrapStoreName: e?.label
						})
					}

					if (editStoreType.value == IWasteRepairAndScrap.repair) {
						editedTableRowStack.value.push({
							...row,
							repairStoreId: e?.id,
							repairStoreName: e?.label
						})
					}
				}
			}

			selectedTableList.value = []
		} else {
			// 双向绑定 table仓库
			const rowIdx = findIndex(
				tableData.value,
				(v) => v.id === editRow.value.id
			)
			if (rowIdx !== -1) {
				tableData.value.splice(rowIdx, 1, { ...editRow.value })
			}

			handleInputBlur(editRow.value)
		}
	} else {
		editRow.value = null
	}
	handoverSelectorVisible.value = false
}

const tbBtnConf = computed(() => {
	const rows = []
	for (let i = 0; i < selectedTableList.value.length; i++) {
		const row = tableData.value.find((r) => r.id === selectedTableList.value[i])
		rows.push(row)
	}

	const isFixFlag = rows.every((item) => {
		return item?.fixFlag == "1"
	})

	// 判断 是否非低值 ： 1：低值 0：非低值
	const isNotLow = rows.every((item) => {
		return item?.isLow != "1"
	})

	return [
		{
			name: "添加交旧物资",
			icon: ["fas", "circle-plus"]
		},
		{
			name: "批量移除",
			icon: ["fas", "trash-can"],
			disabled: selectedTableList.value.length > 0 ? false : true
		},
		{
			name: "批量选择废旧仓库",
			icon: ["fas", "trash-restore"],
			disabled: selectedTableList.value.length > 0 ? false : true
		},
		{
			name: "批量选择待修仓库",
			icon: ["fas", "trash-restore"],
			disabled:
				selectedTableList.value.length > 0 &&
				isFixFlag &&
				props.type != IWasteOldType.matLow &&
				props.type != IWasteOldType.matRepair &&
				(props.type == IWasteOldType.matInStore ||
					props.type == IWasteOldType.matHistory ||
					(props.type == IWasteOldType.matPick && isNotLow))
					? false
					: true
		}
	]
})

const goodsSelectorVisible = ref(false)
const isAllChangeScrapStore = ref(false) // 是否批量更新废旧仓库
const isAllChangeRepairStore = ref(false) // 是否批量更新返修仓库
const handleTabsClick = async (btnName?: string) => {
	if (btnName === "添加交旧物资") {
		goodsSelectorVisible.value = true
	} else if (btnName === "批量选择废旧仓库") {
		isAllChangeScrapStore.value = true
		isAllChangeRepairStore.value = false
		editStoreType.value = IWasteRepairAndScrap.scrap
		handoverSelectorVisible.value = true
	} else if (btnName === "批量选择待修仓库") {
		isAllChangeScrapStore.value = false
		isAllChangeRepairStore.value = true

		editStoreType.value = IWasteRepairAndScrap.repair
		handoverSelectorVisible.value = true
	} else if (btnName === "批量移除") {
		const ids = selectedTableList.value.toString()
		await showDelConfirm()

		formBtnLoading.value = true
		try {
			for (let i = 0; i < selectedTableList.value.length; i++) {
				const rowIdx = findIndex(
					editedTableRowStack.value,
					(v) => v.id === selectedTableList.value[i]
				)
				if (rowIdx !== -1) {
					editedTableRowStack.value.splice(rowIdx, 1)
				}
			}

			await deleteWasteOldApplyBatchItem(ids)
			ElMessage.success("操作成功")
			getList()

			selectedTableList.value = []
		} finally {
			formBtnLoading.value = false
		}
	}
}

/**
 * 选择交旧物资 根据交旧类型 区分 api 参数
 */
const matSelectorApiParams = computed(() => {
	switch (props.type) {
		case IWasteOldType.matPick: // 领料
			return {
				pickApplyId: props.applyId,
				applyId: props.id,
				sord: "asc",
				sidx: "materialCode",
				type: IInventoryBusinessType.storeWasteHandoverApply // CK-JJ-SQ
			}
		case IWasteOldType.matInStore:
			return { businessId: props.id }
		case IWasteOldType.matRepair:
			return {
				applyId: props.id,
				repairApplyId: props.applyId,
				type: IInventoryBusinessType.storeWasteHandoverApply,
				sord: "asc",
				sidx: "code"
			}
		default: // 低值
			return {
				applyId: props.id,
				useApplyId: props.applyId,
				type: IInventoryBusinessType.storeWasteHandoverApply
			}
	}
})

/**
 * 选择交旧物资 根据交旧类型 区分api
 */
const matSelectorApi = computed(() => {
	switch (props.type) {
		case IWasteOldType.matPick: // 领料
			return listCanPickApplyItemPaged
		case IWasteOldType.matInStore: // 在库物资交旧
			return listMatInStoreSelectorPaged
		case IWasteOldType.matRepair:
			return listRepairAddPageItem
		default: // 低值
			return listSelectLowValueUseApplyItemPage
	}
})

/**
 * 低值易耗 选择物资筛选条件 配置
 */
const matUseApplyQueryArrList = computed(() => [
	{
		name: "物资编码",
		key: "code",
		type: "input",
		placeholder: "请输入物资编码"
	},
	{
		name: "物资名称",
		key: "label",
		type: "input",
		placeholder: "请输入物资名称"
	},
	{
		name: "规格型号",
		key: "version",
		placeholder: "请输入规格型号",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资性质",
		key: "attribute",
		type: "select",
		children: dictOptions.value.MATERIAL_NATURE,
		placeholder: "请选择物资性质"
	},
	{
		name: "使用人",
		key: "realname",
		type: "input",
		placeholder: "请输入使用人"
	}
])

/**
 * 返修物资交旧 选择物资筛选条件 配置
 */
const matRepairApplyQueryArrList = computed(() => [
	{
		name: "物资编码",
		key: "materialCode",
		type: "input",
		placeholder: "请输入物资编码"
	},
	{
		name: "物资名称",
		key: "materialLabel",
		type: "input",
		placeholder: "请输入物资名称"
	},
	{
		name: "规格型号",
		key: "version",
		placeholder: "请输入规格型号",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资性质",
		key: "attribute",
		type: "select",
		children: dictOptions.value.MATERIAL_NATURE,
		placeholder: "请选择物资性质"
	}
])

/**
 * 选择交旧物资 根据交旧类型 区分 title
 */
const matGetTicketTitle = computed(() => {
	switch (props.type) {
		case IWasteOldType.matPick:
			return `选择可交旧物资【领料单号: ${props.applyCode}】`
		case IWasteOldType.matLow:
			return `选择可交旧物资【领用单号: ${props.applyCode}】`
		case IWasteOldType.matInStore:
			return `选择可交旧物资【仓库编码: ${props.storeCode}, 货位编码：${props.roomCode}】`
		default:
			return `选择可交旧物资`
	}
})

/**
 * 选择交旧物资 列配置
 */
const matSelectColumns = computed<TableColumnType[]>(() => {
	const ls: TableColumnType[] = [
		{ prop: "materialCode", label: "物资编码", width: 130, fixed: "left" },
		{ prop: "materialName", label: "物资名称", minWidth: 100 },
		{ prop: "version", label: "规格型号" },
		{ prop: "technicalParameter", label: "技术参数" },
		{ prop: "attribute", label: "物资性质", needSlot: true, width: 120 },
		{ prop: "useUnit", label: "库存单位", needSlot: true, width: 90 },
		{
			prop: "wasteMaterialType",
			label: "废旧物资分类",
			needSlot: true,
			width: 120
		},
		{ prop: "quality", label: "主要材质", needSlot: true },
		{ prop: "auxiliaryQuality", label: "辅助材质", needSlot: true },
		{
			prop: "recoveryWeight",
			label: "预估回收重量(KG)",
			width: 140,
			align: "right",
			needSlot: true
		},

		{ prop: "num_view", label: "领料数量", align: "right", minWidth: 100 },
		{
			prop: "outStoreNum_view",
			label: "出库数量",
			align: "right",
			minWidth: 100
		},
		{
			prop: "completeReturnNum_view",
			label: "已退库数量",
			align: "right",
			minWidth: 100
		},
		{
			prop: "completeWasteOldNum_view",
			label: "已交旧数量",
			align: "right",
			minWidth: 100
		},
		{ prop: "canNum_view", label: "可交旧数量", align: "right", minWidth: 100 }
	]

	if (props.type === IWasteOldType.matPick) {
		return ls
	} else if (props.type === IWasteOldType.matRepair) {
		return [
			{ label: "物资编码", prop: "materialCode", width: 130, fixed: "left" },
			{ label: "物资名称", prop: "materialLabel", minWidth: 120 },
			{
				label: "规格型号",
				prop: "version",
				minWidth: 120
			},
			{
				label: "技术参数",
				prop: "technicalParameter",
				minWidth: 120
			},
			{ prop: "attribute", label: "物资性质", needSlot: true, width: 120 },
			{
				prop: "useUnit_view",
				label: "库存单位",
				width: 100
			},
			{
				prop: "repairNum_view",
				label: "返修数量",
				align: "right",
				width: 100
			},
			{
				prop: "canNum_view",
				label: "可交旧数量",
				align: "right",
				width: 100
			},
			{
				prop: "storeLabel",
				label: "出库仓库名称",
				minWidth: 120
			},
			{
				prop: "outDate",
				label: "出库时间",
				width: 150
			}
		]
	} else if (props.type === IWasteOldType.matInStore) {
		return [
			{ prop: "code", label: "物资编码", width: 130, fixed: "left" },
			{ prop: "label", label: "物资名称", minWidth: 120 },
			{ prop: "version", label: "规格型号", minWidth: 120 },
			{ prop: "attribute", label: "物资性质", needSlot: true, width: 120 },
			{ prop: "technicalParameter", label: "技术参数", minWidth: 120 },
			{ prop: "useUnit", label: "库存单位", needSlot: true, width: 90 },
			{
				prop: "wasteMaterialType",
				label: "废旧物资分类",
				needSlot: true,
				width: 120
			},
			{
				prop: "auxiliaryQuality",
				label: "辅助材质",
				needSlot: true,
				width: 120
			},
			{ prop: "quality", label: "主要材质", needSlot: true, width: 120 },
			{
				prop: "recoveryWeight",
				label: "预估回收重量(KG)",
				width: 140,
				align: "right",
				needSlot: true
			},
			{ prop: "storeNum_view", label: "库存数量", align: "right", width: 120 },
			{ prop: "freezeNum_view", label: "冻结量", align: "right", width: 120 },
			{ prop: "batchNo", label: "批次号", width: 160, fixed: "right" }
		]
	} else {
		// 低值易耗领用
		return [
			{ prop: "materialCode", label: "物资编码", width: 130, fixed: "left" },
			{ prop: "materialLabel", label: "物资名称", minWidth: 120 },
			{ prop: "materialTypeCode", label: "物资分类编码", minWidth: 120 },
			{ prop: "materialTypeLabel", label: "物资分类名称", minWidth: 120 },
			{ prop: "version", label: "规格型号", minWidth: 120 },
			{ prop: "attribute", label: "物资性质", needSlot: true, width: 120 },
			{ prop: "auxiliaryQuality", label: "辅助材质", needSlot: true },
			{ prop: "quality", label: "主要材质", needSlot: true, width: 120 },
			{ prop: "lowValueType", label: "低值类型", needSlot: true, width: 120 },
			{ prop: "useUnit", label: "库存单位", needSlot: true, width: 100 },
			{ prop: "username_view", label: "使用人" },
			{ prop: "num_view", label: "分配数量", align: "right", width: 120 },
			{
				prop: "wasteOldNum_view",
				label: "已交旧数量",
				align: "right",
				width: 120
			},
			{ prop: "canNum_view", label: "可交旧数量", align: "right", width: 120 }
		]
	}
})

/**
 * 保存交旧物资
 * @param params
 */
const handleAddGoods = async (params: any[]) => {
	let matList: any[] = []
	if (props.type == IWasteOldType.matHistory) {
		matList =
			map(params, (v) => ({
				...v,
				materialId: v.id,
				materialCode: v.code,
				materialLabel: v.label,
				version: v.version,
				attribute: v.attribute
			})) || []
	} else if (props.type == IWasteOldType.matLow) {
		matList = map(params, (v) => ({
			materialId: v.materialId,
			allocationId: v.id,
			allocationUsername: v.username,
			allocationNum: v.num,
			materialCode: v.materialCode,
			materialLabel: v.materialLabel,
			version: v.version,
			attribute: v.attribute
		}))
	} else if (props.type == IWasteOldType.matInStore) {
		matList = map(params, (v) => ({
			materialId: v.materialId,
			batchNo: v.batchNo,
			materialCode: v.code,
			materialLabel: v.label,
			version: v.version,
			attribute: v.attribute
		}))
	} else {
		matList = map(params, (v) => ({
			materialId: v.materialId,
			materialCode: v.materialCode,
			materialLabel: v.materialLabel,
			version: v.version,
			attribute: v.attribute
			//allocationId: v.id
		}))
	}
	const idempotentToken = getIdempotentToken(
		IIdempotentTokenTypePre.other,
		IIdempotentTokenType.storeWasteHandoverApply,
		props.id
	)
	await addWasteOldApplyBatchItem(
		{
			applyId: props.id,
			items: matList
		},
		idempotentToken
	)
	goodsSelectorVisible.value = false
	ElMessage.success("操作成功")

	paginationData.currentPage = 1
	getList()
}

defineExpose({ tableData, editedTableRowStack, getTableData: getTableData })
</script>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.custom-q {
	margin: 10px 0px -10px 10px !important;
}
.empty-table {
	text-align: center;
	line-height: 0rem;
	padding-bottom: 40px;
	.empty_img {
		width: 150px;
	}
	p {
		color: #666666;
		font-size: 12px;
		text-align: center;
	}
}
:deep(.el-input-group__append) {
	padding: 0 10px !important;
	background-color: #f5f7fa !important;
}

.mat-table-wrap {
	font-size: 12px;
	.mat-table-title {
		height: 40px;
		line-height: 40px;
		margin: 10px 10px 0;
	}
	.mat-table-box {
		margin: 10px 10px 0;
		.mat-table-item {
			width: 100%;
			box-sizing: border-box;
			border: 1px solid #ddd;
			height: 150px;
			display: flex;
			margin-bottom: 10px;
			.check-box {
				display: flex;
				min-width: 30px;
				max-width: 360px;
				flex-direction: column;
				justify-content: center;
				align-items: center;
			}
			.base-info {
				display: flex;
				flex-direction: column;
				justify-content: space-between;
				min-width: 360px;
				max-width: 360px;
				padding: 5px 10px;
				margin-right: 10px;
				background: $---color-background;
				&-content {
					width: 100%;
					height: 34px;
					line-height: 34px;
					text-overflow: ellipsis;
					overflow: hidden;
					white-space: nowrap;
				}
			}

			.base-info-center {
				padding: 10px 10px;
				display: flex;
				flex-direction: column;
				justify-content: space-between;
				min-width: 350px;
				max-width: 350px;
				margin-right: 10px;
				background: $---color-background;
				.red {
					color: #f00 !important;
				}
				.bottom {
					display: flex;
					flex-direction: row;
					justify-content: space-between;
					&-content {
						text-align: center;
						.value {
							line-height: 1;
							font-size: 20px;
							color: $---font-color-3;
							font-weight: bolder;
						}
						.label {
							margin-top: 10px;
							font-size: 12px;
							line-height: 1;
						}
					}
				}
			}

			.base-info-edit {
				display: flex;
				flex-direction: column;
				justify-content: space-between;
				width: calc(100% - 770px);
				min-width: 720px;
				background: $---color-background;
				padding: 10px;
				.label {
					text-align: right;
					height: 30px;
					line-height: 30px;
				}
			}
		}
	}
}
.table-footer-operate {
	display: flex;
	justify-content: space-between;
	margin-top: 10px;
	padding-right: 10px;
	.footer-operate-pagination {
		padding-top: 10px;
		padding-bottom: 10px;
		display: flex;
		align-items: center;
		justify-content: flex-end;
		:deep(.el-pagination__total) {
			height: 32px;
			padding: 1px 10px;
			margin-left: 0px;
			border: 1px solid #dcdfe6;
			border-left: none;
			font-size: var(--pitaya-fs-12);
			display: flex;
			align-items: center;
		}

		.jumper-slot-btn {
			display: flex;
			align-items: center;
			justify-content: center;
			border: 1px solid var(--pitaya-border-color);
			border-left: none;
			width: 32px;
			height: 32px;
			font-size: var(--pitaya-fs-12);
			color: #fff;
			background-color: var(--pitaya-btn-background);
			border-radius: 0;
			cursor: pointer;
			&:hover {
				background-color: var(--pitaya-hover-btn-background);
			}
			&:active {
				background-color: var(--pitaya-active-btn-background);
			}
		}
		:deep(.el-input__wrapper) {
			padding: 1px 10px;
		}
		:deep(.el-pager) {
			.number,
			.more {
				border: 1px solid var(--pitaya-border-color);
				//border-left: none;
				border-radius: 0;
			}
			.is-active {
				background-color: #0a4e9a;
				color: #fff;
				border-color: #0a4e9a;
			}
		}
		:deep(.el-pager) {
			li {
				font-size: var(--pitaya-fs-12);
			}
		}
		:deep(.btn-prev),
		:deep(.btn-next) {
			height: 32px;
			width: 70px;
			text-align: center;
			line-height: 32px;
			border: 1px solid var(--pitaya-btn-background) !important;
			background-color: var(--pitaya-btn-background);
			color: #fff;
			border-radius: 0;
			span {
				font-size: var(--pitaya-fs-12);
			}
			&:hover {
				background-color: var(--pitaya-hover-btn-background);
			}
			&:active {
				background-color: var(--pitaya-active-btn-background);
			}
			&:disabled {
				border: 1px solid var(--pitaya-border-color) !important;
			}
		}
		:deep(.btn-next) {
			border-left: none !important;
		}
		:deep(.btn-prev) {
			border-right: none !important;
		}
		:deep(.el-pagination__sizes) {
			margin-left: 10px;
			.el-input {
				width: 100px;
				font-size: var(--pitaya-fs-12);
			}
			.el-input__wrapper {
				border-radius: 0px;
			}
		}
		:deep(.el-pagination__jump) {
			margin-left: 10px;
			.el-pagination__goto,
			.el-pagination__classifier {
				display: none;
			}
			.el-input__wrapper {
				padding: 0 10px;
				border-radius: 0px;
				box-shadow: none;
				border: 1px solid var(--pitaya-border-color);
				//border-right: none;
			}
		}
	}
}
</style>
