<!-- 领料单选择器 -->
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<div class="drawer-column" style="width: 100%">
			<Title :title="titleConf" />
			<div class="rows">
				<Query
					:query-arr-list="(queryConf as any)"
					style="margin: 10px 10px -10px"
					@get-query-data="handleQuery"
				/>
				<pitaya-table
					ref="tableRef"
					:columns="tableProp"
					:table-data="tableData"
					:need-selection="true"
					:single-select="!multiple"
					:need-index="true"
					:need-pagination="true"
					:total="pageTotal"
					:table-loading="tableLoading"
					@on-selection-change="selectedTableList = $event"
					@on-current-page-change="fetchTableDataWithSetRowsCheck"
				/>
			</div>
			<button-list
				class="footer"
				:button="btnConf"
				@on-btn-click="handleBtnClick"
			/>
		</div>
	</div>
</template>

<script setup lang="ts">
import { first } from "lodash-es"
import { listMatPickApplyPaged } from "@/app/baseline/api/store/mat-get-apply-api"
import {
	MatOutStorePickApplyDTO,
	MatOutStorePickApplyVo
} from "@/app/baseline/utils/types/mat-get-apply"
import { appStatus } from "@/app/baseline/api/dict"
import { useTbInit } from "../../components/tableBase"
import { listMatStoragePaged } from "@/app/baseline/api/store/manage-api"
import { ICostCategoryStatus } from "@/app/baseline/utils/types/system-cost-category"
import { BaseLineSysApi } from "@/app/baseline/api/system"
import { useUserStore } from "@/app/platform/store/modules/user"
import { useTableSelectorUtils } from "../../store/hooks/table-selector-utils"

const props = withDefaults(
	defineProps<{
		/**
		 * 当前选中行的id
		 */
		selected?: number
		/**
		 * 是否支持多选
		 */
		multiple?: boolean

		/**
		 * table api 请求query参数
		 */
		tableApiParams?: any

		/**
		 * table api 请求
		 */
		tableApi?: (arg?: any) => any

		/**
		 * title
		 */
		title?: string
	}>(),
	{
		title: "选择领料单"
	}
)

const emit = defineEmits<{
	(e: "close"): void
	(e: "save", v: any): void
}>()

const { userInfo } = storeToRefs(useUserStore())

const titleConf = computed(() => ({
	name: [`${props.title}`],
	icon: ["fas", "square-share-nodes"]
}))

const drawerLoading = ref(false)

const queryConf = computed<querySetting[]>(() => {
	return [
		{
			name: "领料单号",
			key: "code",
			type: "input",
			placeholder: "请输入领料单号"
		},
		{
			name: "领料单名称",
			key: "label",
			type: "input",
			placeholder: "请输入领料单名称"
		},
		{
			name: "出库仓库",
			key: "storeId",
			placeholder: "请选择出库仓库",
			type: "tableSelect",
			tableInfo: [
				{
					title: "请选择出库仓库",
					tableApi: listMatStoragePaged, //表格接口
					tableColumns: [
						{ label: "仓库编码", prop: "code", width: 120 },
						{ label: "仓库名称", prop: "label" },
						{
							label: "仓库级别",
							prop: "level_view",
							width: 100
						},
						{
							label: "仓库类型",
							prop: "type_view",
							width: 120
						},
						{
							label: "所属段区",
							prop: "depotId_view",
							width: 150
						}
					],
					queryArrList: [
						{
							name: "仓库编码",
							key: "code",
							type: "input",
							placeholder: "请输入仓库编码"
						},
						{
							name: "仓库名称",
							key: "label",
							type: "input",
							placeholder: "请输入仓库名称"
						}
					],
					params: {
						currentPage: 1,
						pageSize: 20
					}, //表格接口参数

					labelName: {
						key: "id", //回显标识
						label: "label", //input框要展示的字段
						value: "id" //要给后台传的字段
					}
				}
			]
		},
		{
			name: "费用类别",
			key: "expenseCategory",
			type: "treeSelect",
			placeholder: "请选择费用类别",
			treeApi: () =>
				BaseLineSysApi.getCostCategoryTree({
					status: ICostCategoryStatus.Started
				})
		},
		{
			name: "领料人",
			key: "pickUserName",
			type: "input",
			placeholder: "请输入领料人"
		},
		{
			name: "领料部门",
			key: "pickSysOrgId",
			type: "treeSelect",
			placeholder: "请选择领料部门",
			treeApi: () =>
				BaseLineSysApi.getDepartmentTreeWithCompanyDisabled(
					userInfo.value.companyId
				)
		},
		{
			name: "申请人",
			key: "createdBy",
			type: "input",
			placeholder: "请输入申请人"
		},
		{
			name: "申请部门",
			key: "sysOrgId",
			type: "treeSelect",
			placeholder: "请选择申请部门",
			treeApi: () =>
				BaseLineSysApi.getDepartmentTreeWithCompanyDisabled(
					userInfo.value.companyId
				)
		}
	]
})

const btnConf = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "保存",
		icon: ["fas", "floppy-disk"]
	}
]

const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	currentPage,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc
} = useTbInit<MatOutStorePickApplyVo, MatOutStorePickApplyDTO>()

fetchParam.value = {
	sidx: "createdDate",
	sord: "desc",
	bpmStatus: appStatus.approved,
	...props.tableApiParams
}

fetchFunc.value = props.tableApi ?? listMatPickApplyPaged

tableProp.value = [
	{
		prop: "code",
		label: "领料单号",
		fixed: "left"
	},
	{
		prop: "label",
		label: "领料单名称"
	},
	{
		prop: "pickUserId_view",
		label: "领料人"
	},
	{
		prop: "pickSysOrgId_view",
		label: "领料部门"
	},
	{
		prop: "expenseCategory_view",
		label: "费用类别"
	},
	{
		prop: "storeLabel",
		label: "出库仓库名称"
	},
	/**
	 * 一期：领料人=申请人
	 * 二期：领料人与申请人分开字段保存
	 */
	{
		prop: "createdBy_view",
		label: "申请人"
	},
	{
		prop: "sysOrgId_view",
		label: "申请部门"
	},
	{
		prop: "createdDate",
		label: "申请时间"
	}
]

const { fetchTableDataWithSetRowsCheck } = useTableSelectorUtils({
	tableData,
	tableRef,
	selectedIds: [props.selected],
	fetchTableData
})

onMounted(() => {
	fetchParam.value = {
		sidx: "createdDate",
		sord: "desc",
		...fetchParam.value,
		...props.tableApiParams
	}

	fetchTableDataWithSetRowsCheck()
})

/**
 * 筛选 handler
 */
function handleQuery(e: any) {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		...e
	}

	fetchTableDataWithSetRowsCheck()
}

function handleBtnClick(name?: string) {
	if (name === "取消") {
		return emit("close")
	}

	// 保存逻辑
	emit(
		"save",
		props.multiple ? selectedTableList.value : first(selectedTableList.value)
	)
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
