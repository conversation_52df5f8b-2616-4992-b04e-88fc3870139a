<!-- @description 24.07.11 物资树分类 组件V2.0 -->

<template>
	<div class="drawer-container">
		<div class="drawer-column" style="padding-bottom: 0">
			<div class="rows">
				<Title :title="props.title">
					<div class="tree-switch" v-if="props.needSwitch">
						<span>
							<el-switch
								size="small"
								v-model="hasCancelMatType"
								:active-value="true"
								:inactive-value="false"
								active-color="#0a4e9a"
								:disabled="treeLoading"
								@change="handleHasCancelMatTypeChange"
						/></span>
						<span class="tip">包含作废分类编码</span>
					</div>
				</Title>
				<pitaya-lazy-tree-new
					ref="treeRef"
					:lazy="changeLazy"
					v-model:tree-biz-id="treeSelectId"
					v-loading="treeLoading"
					:tree-data="treeData"
					:tree-props="treeProp"
					:check-strictly="props.needSingleSelect"
					:expand-on-click-node="props.needCheckBox"
					:need-check-box="props.needCheckBox"
					:need-single-select="props.needSingleSelect"
					:loadTreeChildren="loadTreeChildren"
					:default-expanded-keys="defaultExpandedKeys"
					@on-tree-click="handleTreeClick"
					@on-tree-change="handleTreeCheck"
					:on-tree-search="handleTreeSearch"
					style="height: calc(100% - 35px)"
				/>

				<!--
				:default-expanded-keys="defaultExpandedKeys"
					:check-strictly="props.needSingleSelect"
					:expand-on-click-node="props.needCheckBox"
					:need-check-box="props.needCheckBox"
					:need-single-select="props.needSingleSelect"
					node-key="id"
					:loadTreeChildren="loadTreeChildren"
					@on-tree-change="handleTreeCheck"
					@on-tree-click="handleTreeClick"
					:on-tree-search="handleTreeSearch" -->
			</div>
			<ButtonList
				:button="formBtnList"
				@onBtnClick="handleFormBtn"
				class="footer"
				v-if="footBtnVisible"
			/>
		</div>
	</div>
</template>
<script setup lang="ts">
import { ElTree as ElementTree } from "element-plus"
import { matStatus } from "@/app/baseline/api/dict"
import PitayaLazyTree from "@/compontents/v3/PitayaLazyTree.vue"
import { MatTypeApi } from "@/app/baseline/api/material/matType"
import { debounce, map } from "lodash-es"
import XEUtils from "xe-utils"
interface Props {
	lazy?: boolean
	defId?: string
	title?: Object
	needSwitch?: boolean // 是否显示包含作废分类编码
	needCheckBox?: boolean // 是否需要复选框
	defaultExpandedKeys?: any[] // 默认展开节点
	defaultCheckedKeys?: any[] // 默认选中节点
	currentNodeKey?: number //当前选中节点
	checkStrictly?: boolean // 是否严格遵守父子不关联
	needSingleSelect?: boolean // 是否需要单选
	status?: string // mat分类状态
	needLabelCount?: boolean
	footBtnVisible?: boolean // 是否显示 保存/取消按钮
}

const props = withDefaults(defineProps<Props>(), {
	lazy: true,
	needCheckBox: true,
	needSwitch: true,
	defaultExpandedKeys: () => [0],
	defaultCheckedKeys: () => [],
	checkStrictly: false,
	needSingleSelect: false,
	readOnly: true,
	currentNodeKey: -1,
	footBtnVisible: false,
	title: () => ({
		name: ["物资库存查询"],
		icon: ["fas", "square-share-nodes"]
	})
})

const changeLazy = ref(props.lazy)

const defaultExpandedKeys = ref(props.defaultExpandedKeys)
const emits = defineEmits([
	"onTreeClick",
	"onTreeCheck",
	"update:treeBizId",
	"onScrollLoad",
	"onBtnClick"
])

const treeProp = {
	children: "children",
	label: "fullLabel", // label
	isLeaf: "isLeaf"
}

const treeLoading = ref(false)

const treeSelectId = ref()

const treeRef = ref<InstanceType<typeof ElementTree>>()

const hasCancelMatType = ref(false)

const checkedId = ref<number[]>([]) //保存选中的数据

const filterText = ref("") // 搜索txt

const treeData = ref<Record<string, any>[]>([
	{
		id: props.defId != undefined ? props.defId : 0,
		code: "",
		fullLabel: "物资全部分类",
		label: "物资全部分类",
		children: [],
		isLeaf: false
	}
])

/**
 *	默认分类 状态
 */
const defaultStatus: matStatus[] = [
	matStatus.normal,
	matStatus.freeze
	/* matStatus.drafted */
]

/**
 * 物资分类参数
 */
const matParams = ref({
	fid: 0,
	status: props.status || defaultStatus.join(",")
})

const formBtnList = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "保存",
		icon: ["fas", "floppy-disk"]
	}
]

/**
 * 按钮点击
 * @param btnName
 */
const handleFormBtn = (btnName: string | undefined) => {
	if (btnName === "保存") {
		const treeList = treeRef.value?.getCheckedNodes().pop()

		emits("onBtnClick", treeList)
		return
	}
	if (btnName === "取消") {
		checkedId.value = []
		emits("onBtnClick", "", "cancel")
		return
	}
}

/**
 * 重置 默认值
 */
async function resetDefault() {
	checkedId.value = [props.currentNodeKey]
	treeRef.value?.setCheckedKeys(checkedId.value)
	treeRef.value?.setCurrentKey(checkedId.value[0])

	//treeLoading.value = true
	filterText.value = ""

	const pitayaTreeRef = treeRef.value?.PitayaTreeRef
	pitayaTreeRef?.setCurrentKey(0)

	defaultExpandedKeys.value = [0]
	const selectedData = pitayaTreeRef?.getCurrentNode()
	const selectedNode = pitayaTreeRef?.getNode(0)
	handleTreeClick(selectedData, selectedNode)
	changeTreeNodeStatus(selectedNode)
}

/**
 * 是否包含作废物资分类 change
 */
async function handleHasCancelMatTypeChange() {
	treeLoading.value = true

	if (hasCancelMatType.value) {
		// 包含作废的
		matParams.value.status += "," + matStatus.canceled
	} else {
		// 不包含作废
		matParams.value.status = props.status
			? props.status
			: defaultStatus.join(",")
	}

	try {
		if (filterText.value) {
			changeLazy.value = false

			getFilterTreeData()
		} else {
			// 设置 tree 选中祖先节点
			const pitayaTreeRef = treeRef.value?.PitayaTreeRef
			pitayaTreeRef?.setCurrentKey(0)

			defaultExpandedKeys.value = [0]
			const selectedData = pitayaTreeRef?.getCurrentNode()
			const selectedNode = pitayaTreeRef?.getNode(0)
			treeSelectId.value = ""

			selectedNode.loaded = false
			selectedNode.expand()
			handleTreeClick(selectedData, selectedNode)
			changeTreeNodeStatus(selectedNode)
		}
	} finally {
		treeLoading.value = false
	}
}
function changeTreeNodeStatus(node: any) {
	node.expanded = false
	for (let i = 0; i < node.childNodes.length; i++) {
		node.childNodes[i].expanded = false

		if (node.childNodes[i].childNodes.length > 0) {
			changeTreeNodeStatus(node.childNodes[i])
		}
	}
}

/**
 * 子类加载
 * @param treeNode
 */
async function loadTreeChildren(treeNode: any) {
	if (treeNode.level == 0) {
		return MatTypeApi.getMatTypeChild({
			...matParams.value,
			fid: treeNode.data[0].id
		})
			.then((res: any) => {
				treeData.value[0].children = map(res, (v) => ({
					...v,
					fullLabel:
						v.lowValue == "1"
							? `${v.code} ${
									v.label
							  } <i style='border:1px solid #409eff;color:#409eff;font-size:10px;padding:1px;'>低值</i>${
									v.status === matStatus.freeze
										? `<i style='color:#f59b22;font-size:12px;'>（冻结）</i>`
										: v.status === matStatus.canceled
										? `<i style='color:#e25e59;font-size:12px;'>（作废）</i>`
										: `${v.code} ${v.label}`
							  }`
							: v.status === matStatus.freeze
							? `${v.code} ${v.label}<i style='color:#f59b22;font-size:12px;'>（冻结）</i>`
							: v.status === matStatus.canceled
							? `${v.code} ${v.label}<i style='color:#e25e59;font-size:12px;'>（作废）</i>`
							: `${v.code} ${v.label}`,
					isLeaf: v.childCount == 0 ? true : false
				}))

				return treeData.value
			})
			.then(() => {
				const pitayaTreeRef = treeRef.value?.PitayaTreeRef
				pitayaTreeRef?.setCurrentKey(0)
				const selectedData = pitayaTreeRef?.getCurrentNode()
				const selectedNode = pitayaTreeRef?.getNode(0)
				handleTreeClick(selectedData, selectedNode)
			})
	} else {
		return MatTypeApi.getMatTypeChild({
			...matParams.value,
			fid: treeNode.data.id
		}).then((res: any) => {
			treeNode.children = map(res, (v) => ({
				...v,
				fullLabel:
					v.lowValue == "1"
						? `${v.code} ${
								v.label
						  } <i style='border:1px solid #409eff;color:#409eff;font-size:10px;padding:1px;'>低值</i>${
								v.status === matStatus.freeze
									? `<i style='color:#f59b22;font-size:12px;'>（冻结）</i>`
									: v.status === matStatus.canceled
									? `<i style='color:#e25e59;font-size:12px;'>（作废）</i>`
									: ""
						  }`
						: v.status === matStatus.freeze
						? `${v.code} ${v.label}<i style='color:#f59b22;font-size:12px;'>（冻结）</i>`
						: v.status === matStatus.canceled
						? `${v.code} ${v.label}<i style='color:#e25e59;font-size:12px;'>（作废）</i>`
						: `${v.code} ${v.label}`,
				isLeaf: v.childCount == 0 ? true : false
			}))

			return treeNode.children
		})
	}
}

/**
 * 树形筛选 check handler
 */
const handleTreeCheck = debounce(() => {
	const checkedNodes = treeRef.value?.getCheckedNodes()

	const ids = map(checkedNodes, ({ id }) => id)
	emits("onTreeCheck", ids, matParams.value.status)
}, 300)

/**
 * 树形 单击
 */
function handleTreeClick(data: any, treeData: any) {
	// treeSelectId.value = data.id
	emits("onTreeClick", data, treeData, matParams.value.status)
}

const getFilterTreeData = () => {
	treeLoading.value = true
	MatTypeApi.getMatTypeTree({
		key: filterText.value ? `*${filterText.value}*` : "",
		//fid: 0,
		status: matParams.value.status
	} as any)
		.then((res: any) => {
			defaultExpandedKeys.value = [0]

			XEUtils.eachTree(res, (item: any) => {
				defaultExpandedKeys.value.push(item.id)

				item.fullLabel =
					item.lowValue == "1"
						? `${item.code} ${
								item.label
						  } <i style='border:1px solid #409eff;color:#409eff;font-size:10px;padding:1px;'>低值</i>${
								item.status === matStatus.freeze
									? `<i style='color:#f59b22;font-size:12px;'>（冻结）</i>`
									: item.status === matStatus.canceled
									? `<i style='color:#e25e59;font-size:12px;'>（作废）</i>`
									: ""
						  }`
						: item.status === matStatus.freeze
						? `${item.code} ${item.label}<i style='color:#f59b22;font-size:12px;'>（冻结）</i>`
						: item.status === matStatus.canceled
						? `${item.code} ${item.label}<i style='color:#e25e59;font-size:12px;'>（作废）</i>`
						: `${item.code} ${item.label}`
			})

			treeData.value[0].children = res
			return []
			//initTreeData(res)
		})
		.finally(() => {
			treeLoading.value = false
		})
}

/**
 * 搜索
 */
const handleTreeSearch = debounce(async (txt?: string) => {
	checkedId.value = [props.currentNodeKey]
	treeRef.value?.setCheckedKeys(checkedId.value)
	treeRef.value?.setCurrentKey(checkedId.value[0])

	if (txt && txt.length >= 1) {
		filterText.value = txt
		changeLazy.value = false

		getFilterTreeData()
	} else if (!txt || txt.length < 1) {
		filterText.value = ""
		defaultExpandedKeys.value = [0]
		changeLazy.value = true
	}
}, 500)

defineExpose({
	treeRef,
	resetTreeData: resetDefault,

	handleTreeClick,
	loadTreeChildren
})
onMounted(() => {
	//resetDefault()
	// handleHasCancelMatTypeChange()
})
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.drawer-column {
	width: 100%;
	padding-bottom: 10px;
}

.tree-switch {
	color: #666;
	font-size: 12px;
	padding-right: 10px;

	.tip {
		padding-left: 5px;
	}
	position: absolute;
	right: 0;
}
</style>
