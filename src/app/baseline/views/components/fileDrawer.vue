<script setup lang="ts">
import { reactive, ref } from "vue"
import UploadFile from "@/compontents/UploadFile.vue"
import { FormInstance, FormRules, UploadInstance } from "element-plus"
import { attachmentUpdate } from "../../api/system"
interface Props {
	businessId?: string | number //业务ID
	businessType: string | number //业务类型
	mod: string //显示模式  view : 查看, edit : 编辑
	fileTxtConf: Record<string, any>
	allExtensions?: string[]
	accept?: string // 上传文件类型
	allowsize?: number
}

const props = withDefaults(defineProps<Props>(), {
	businessId: undefined,
	businessType: "",
	mod: "edit",
	allowsize: 100,
	accept: ".png,.jpeg,.jpg,.pdf,.doc,.docx,.xlsx,.zip,.mp4,.mov,.rmvb",
	allExtensions: () => [
		".pdf",
		".doc",
		".docx",
		".xlsx",
		".png",
		".jpg",
		".zip",
		".jpeg",
		".mp4",
		".mov",
		".rmvb"
	],
	fileTxtConf: () => {
		return {
			title: "",
			fileName: "",
			fileLable: "上传附件"
		}
	}
})
const emits = defineEmits(["onUpdateList", "onSaveOrClose"])
const ruleFormRef = ref<FormInstance>()

const qualityDrawerLeftTitle = computed(() => {
	return {
		name: [props.fileTxtConf.title],
		icon: ["fas", "square-share-nodes"]
	}
})
const formBtnList = ref([
	{ name: "取消", icon: ["fas", "circle-minus"] },
	{ name: "确定", icon: ["fas", "check-circle"], disabled: false }
])
const drawerBtnLoading = ref(false)
const onFormBtnList = (btnName: string | undefined) => {
	if (btnName === "确定") {
		if (ruleFormRef.value) {
			ruleFormRef.value.validate((valid) => {
				if (valid) {
					drawerBtnLoading.value = true
					attachmentUpdate({
						businessId: props.businessId,
						fileIds: [formModelData.value.id],
						customFileName: formModelData.value.fileName
					})
						.then(() => {
							emits("onUpdateList")
							emits("onSaveOrClose")
						})
						.finally(() => {
							drawerBtnLoading.value = false
						})
				}
			})
		}
	} else if (btnName === "取消") {
		emits("onSaveOrClose")
	}
}
const formModelData = ref({
	fileName: "",
	file: "",
	id: ""
})
const rules = reactive<FormRules<typeof formModelData>>({
	id: [
		{
			required: true,
			message: `${props.fileTxtConf?.fileLable || "上传附件"}不能为空`
		}
	],
	fileName: [
		{
			required: true,
			message: `${props.fileTxtConf?.fileName}不能为空`
		}
	]
})

const uploadUrl = `/pitaya/system/common/upload?businessType=${props.businessType}`
// &businessId=${props.businessId}
/**
 * 附件上传成功 回调
 * @param response
 */
const handleSuccess = (response: Record<string, any>) => {
	formModelData.value.fileName =
		formModelData.value.fileName || response.data?.fileName.slice(0, 50)
	formModelData.value.id = response.data?.id
	//emits("onUpdateList")
}
/**
 * 手动删除附件 回调
 * @param uploadFile
 * @param fileList
 */
const handleRemove = (uploadFile: any, fileList: any) => {
	formModelData.value.fileName = ""
	formModelData.value.id = ""
	fileList.value = []

	setTimeout(() => {
		ruleFormRef.value?.clearValidate()
	})
}
</script>
<template>
	<div class="drawer-container">
		<div class="drawer-column">
			<div class="rows">
				<Title :title="qualityDrawerLeftTitle" />
				<el-scrollbar>
					<el-form
						style="padding-bottom: 30px"
						class="content form-base"
						ref="ruleFormRef"
						:model="formModelData"
						:rules="rules"
						label-position="top"
					>
						<el-form-item :label="props.fileTxtConf.fileName" prop="fileName">
							<el-input
								v-model.trim="formModelData.fileName"
								maxlength="50"
								show-word-limit
								:placeholder="`请输入${props.fileTxtConf.fileName}`"
							/>
						</el-form-item>
						<el-form-item
							:label="props.fileTxtConf.label"
							prop="id"
							style="margin-top: 18px"
						>
							<el-input type="hidden" v-model="formModelData.id" />
							<UploadFile
								:action="uploadUrl"
								:viewDesc="true"
								:maxCount="1"
								:accept="props.accept"
								:allExtensions="props.allExtensions"
								:allowsize="props.allowsize"
								@onSuccess="handleSuccess"
								@handle-remove="handleRemove"
								listType="text"
							/>
						</el-form-item>
					</el-form>
				</el-scrollbar>
			</div>
			<ButtonList
				class="footer"
				:button="formBtnList"
				:loading="drawerBtnLoading"
				@onBtnClick="onFormBtnList"
			/>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.drawer-column {
	width: 100%;
}
:deep(.el-form-item__error) {
	top: 34px !important;
}
:deep(.el-upload__tip) {
	margin-top: 12px !important;
}
</style>
