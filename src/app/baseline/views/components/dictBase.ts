import {ref} from "vue";
import XEUtils from "xe-utils";
import {DictApi} from "../../api/dict";

export function useDictInit(){
	//获取字典
	const dictOptions = ref<Record<string, any[]>>({
		PLAN_CATEGORY: []
	})

	function getDictByCodeList( keys : string[] ): Promise<null> {
		dictOptions.value	= {};
		keys.map( _key => dictOptions.value[_key] = [] );

		return new Promise((resolve) => {
			DictApi.getDictByCodeList(dictOptions)
				.then((res) => {
					Object.keys( res ).map( _k => {
						XEUtils.eachTree( res[_k], (item: any) => {
							item.id 			= item.subitemValue;
							item.value 		= item.subitemValue;
							item.name 		= item.subitemName;
							item.allName 	= item.subitemName;
						})
					})
					dictOptions.value = res
				})
				.finally(() => {
					resolve( null )
				})
		})
	}

	function dictFilter( key : string, value : string ){
		if( dictOptions.value[key] ){
				return dictOptions.value[key].find( _o => _o.value == value );
		}else
			return undefined;
	}

	return{
		dictOptions,
		dictFilter,
		getDictByCodeList
	}
}

/**
 * 使用说明
 *
import { useDictInit } from "@/app/baseline/views/components/dictBase.ts";
const { dictOptions, getDictByCodeList } = useDictInit();
getDictByCodeList( ['INVOICE_TYPE', 'INVOICE_STATUS'] )
 */
