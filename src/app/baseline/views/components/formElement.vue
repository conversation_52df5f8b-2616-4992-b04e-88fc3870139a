<script setup lang="ts">
import { toRef, ref, watch, nextTick, computed } from "vue"
import { FormElementType } from "@/app/baseline/views/components/define"
import InputNumber from "@/app/baseline/views/components/inputNumber.vue"
import {
	inputLimit,
	validateAndCorrectInput
} from "@/app/baseline/utils/validate"
import LinkTag from "@/app/baseline/views/components/linkTag.vue"
import { number } from "echarts"

interface Props {
	formElement: any
	formData?: any
	drawerTreeDestroyOnClose?: boolean
}

const props = defineProps<Props>()
const formElementModal = toRef(props, "formElement")
const formDataModal = toRef(props, "formData")
if (typeof formDataModal.value.year == "number") {
	formDataModal.value.year = formDataModal.value.year + ""
	console.log(" number")
}

watch(
	() => props.formData,
	(val: any) => {
		if (val) {
			// const tmpForm = JSON.parse(JSON.stringify(val))
			// //处理一下
			// formElementModal.value.forEach((elArr) => {
			// 	elArr.forEach((el) => {
			// 		if (el.disabled && !tmpForm[el.name]) {
			// 			tmpForm[el.name] = "---"
			// 		}
			// 	})
			// })
			Object.assign(formDataModal, val)
		}
	},
	{ immediate: true }
)

const showDrawer = ref<boolean>(false)
const drawerSize = 310

/*************树形控件 *****************************/
const drawerTree = ref()
// 树选中id值
const selectIds = ref<string[]>([])
const selectIdsObj = ref<{ [propName: string]: any }>({})
const treeApi = ref<Function | undefined>(() => {})
const checkStrictly = ref(false)
const needSingleSelect = ref(false)
const currentEl = ref<FormElementType>()
const drawerTreeTitle = ref({
	name: ["请选择"],
	icon: ["fas", "square-share-nodes"]
})
const checkedKeys = computed(() => {
	return selectIdsObj.value[currentEl.value?.name] || []
})
const openDrawerTree = (el: FormElementType) => {
	currentEl.value = el
	treeApi.value = el.treeApi
	drawerTreeTitle.value.name = [`${el.label}`]
	checkStrictly.value = true
	needSingleSelect.value = el.needSingleSelect === false ? false : true
	if (needSingleSelect.value) {
		selectIdsObj.value[currentEl.value.name] = [
			formDataModal.value[`${currentEl.value.vname}`]
		]
	} else {
		selectIdsObj.value[currentEl.value.name] =
			formDataModal.value[`${currentEl.value.vname}`]?.split(",")
	}

	showDrawer.value = true
}
const onTreeBtnClick = (data: string[] | undefined, selectNodes: any[]) => {
	if (selectNodes) {
		formDataModal.value[`${currentEl.value.name}`] = selectNodes
			.map((item) => item.allName)
			.join(",")
		formDataModal.value[`${currentEl.value.vname}`] = selectNodes
			.map((item) => item.id)
			.join(",")
		// if (currentEl.value.replaceIdTo) {
		// 	selectIds.value = selectNodes
		// 		.filter((item) => item.id !== 0)
		// 		.map((item) => item[currentEl.value.replaceIdTo])
		// } else {
		selectIds.value = selectNodes
			.filter((item) => item.id !== 0)
			.map((item) => item.id)
		//	}
		selectIdsObj.value[currentEl.value!.name] = selectIds.value
	} else {
		if (selectIdsObj.value[currentEl.value!.name]) {
			drawerTree.value?.resetChecked()
			drawerTree.value?.pitayaTreeRef.setCheckedKeys(
				selectIdsObj.value[currentEl.value!.name]
			)
		} else {
			selectIds.value = []
			selectIdsObj.value[currentEl.value!.name] = []
			drawerTree.value?.resetChecked()
		}
	}
	showDrawer.value = false
}

function handleBeforeClose() {
	if (selectIdsObj.value[currentEl.value!.name]) {
		drawerTree.value?.resetChecked()
		drawerTree.value?.pitayaTreeRef.setCheckedKeys(
			selectIdsObj.value[currentEl.value!.name]
		)
	} else {
		selectIds.value = []
		selectIdsObj.value[currentEl.value!.name] = []
		drawerTree.value?.resetChecked()
	}
	showDrawer.value = false
}
const openDrawer = (el: FormElementType) => {
	if (el.clickApi) {
		el.clickApi()
	}
}
</script>

<template>
	<div class="form-base">
		<template v-for="(arrEl, index) in formElementModal" :key="index">
			<el-row :gutter="10">
				<el-col
					v-for="el in arrEl"
					:span="el.width ? el.width : 24"
					:key="el.name"
				>
					<el-form-item
						:label="el.label"
						:prop="el.name"
						v-show="el.type != 'hidden' && el.hidden != true"
						:style="{ flexBasis: el.width ? el.width : 'auto' }"
					>
						<template #label v-if="el.slotLabelContent">
							<span>
								{{ el.label }}
								<el-tooltip
									placement="left"
									effect="light"
									:content="el.slotLabelContent"
									:raw-content="true"
								>
									<template #content>
										<span
											style="color: #999 !important"
											v-html="el.slotLabelContent"
										/>
									</template>
									<font-awesome-icon
										:icon="['fas', 'question-circle']"
										style="color: #aaa; cursor: pointer"
									/>
								</el-tooltip>
							</span>
						</template>
						<el-select
							v-if="el.type == 'select'"
							v-model="formDataModal[el.name]"
							style="width: 100%"
							:clearable="el.clear == false ? el.clear : true"
							:disabled="el.disabled"
							@change="el.change"
							:placeholder="
								el.placeholder ? el.placeholder : `请选择${el.label}`
							"
						>
							<el-option
								v-for="(opt, index) in el.data"
								:key="index"
								:label="opt.label"
								:value="opt.value"
								:disabled="opt.disabled ?? false"
							/>
						</el-select>
						<el-input
							v-else-if="el.type == 'treeSelect'"
							v-model.trim="formDataModal[el.name]"
							:rows="3"
							:type="el.type"
							:maxlength="el.maxlength"
							:show-word-limit="el.showLimit == undefined ? true : el.showLimit"
							:placeholder="
								el.placeholder ? el.placeholder : `请选择${el.label}`
							"
							:disabled="el.disabled"
							@click="openDrawerTree(el)"
							readonly
						>
							<template #append>
								<font-awesome-icon
									:icon="['fas', 'layer-group']"
									style="color: #ccc"
									@click="el.disabled ? '' : openDrawerTree(el)"
								/>
							</template>
						</el-input>
						<el-input
							v-else-if="el.type == 'drawer'"
							v-model.trim="formDataModal[el.name]"
							:placeholder="
								el.placeholder ? el.placeholder : `请选择${el.label}`
							"
							:disabled="el.disabled"
							@click="openDrawer(el)"
							readonly
						>
							<template #append>
								<font-awesome-icon
									:icon="['fas', 'layer-group']"
									style="color: #ccc"
									@click="el.disabled ? '' : openDrawer(el)"
								/>
							</template>
						</el-input>
						<!-- 	<span

						> -->
						<el-text
							type="primary"
							v-else-if="el.type === 'link'"
							style="
								border: 1px solid #dcdfe6;
								padding: 0 10px;
								width: 100%;
								cursor: pointer;
							"
							@click="el.clickApi"
						>
							{{ formDataModal[el.name] || "---" }}
						</el-text>
						<!-- <link-tag
								:value="formDataModal[el.name]"
								@on-click="el.clickApi"
							/> -->
						<!-- </span> -->
						<el-date-picker
							v-else-if="
								['year', 'month', 'date', 'week', 'daterange'].includes(el.type)
							"
							v-model="formDataModal[el.name]"
							:type="el.type"
							:placeholder="
								el.placeholder ? el.placeholder : `请选择${el.label}`
							"
							:disabled="el.disabled"
							:disabled-date="el.disabledDate"
							:start-placeholder="`请选择${el.startPlaceholder}`"
							:end-placeholder="`请选择${el.endPlaceholder}`"
							:format="el.format ?? 'YYYY-MM-DD'"
							:value-format="el.valueFormat ?? 'YYYY-MM-DD HH:mm:ss'"
							clearable
						/>

						<el-date-picker
							v-else-if="['datetime'].includes(el.type)"
							v-model.trim="formDataModal[el.name]"
							:type="el.type"
							:placeholder="
								el.placeholder ? el.placeholder : `请选择${el.label}`
							"
							:disabled="el.disabled"
							:disabled-date="el.disabledDate"
							format="YYYY-MM-DD HH:mm:ss"
							value-format="YYYY-MM-DD HH:mm:ss"
							clearable
						/>
						<el-input
							v-else-if="el.type == 'number'"
							v-model="formDataModal[el.name]"
							:placeholder="
								el.placeholder ? el.placeholder : `请输入${el.label}`
							"
							:disabled="el.disabled"
							:clearable="true"
							@input="el.input"
							@blur="el.blur"
							@change="el.change"
							@keydown="inputLimit"
						>
							<template v-if="el.append" #append>{{ el.append }}</template>
						</el-input>
						<template v-else>
							<el-input
								v-if="!formDataModal[el.name] && el.disabled"
								value="---"
								:rows="el.rows ? el.rows : 5"
								:type="el.type"
								:maxlength="el.maxlength"
								:show-word-limit="
									el.showLimit == undefined ? true : el.showLimit
								"
								:placeholder="el.placeholder"
								:disabled="el.disabled"
								clearable
							/>
							<el-input
								:class="el.className"
								v-else
								v-model.trim="formDataModal[el.name]"
								:rows="el.rows ? el.rows : 5"
								:type="el.type"
								:maxlength="el.maxlength"
								:show-word-limit="
									el.showLimit == undefined ? true : el.showLimit
								"
								:placeholder="
									el.placeholder ? el.placeholder : `请输入${el.label}`
								"
								:disabled="el.disabled"
								clearable
							>
								<template v-if="el.prepend" #prepend>{{ el.prepend }}</template>
								<template v-if="el.append" #append>{{ el.append }}</template>
							</el-input>
						</template>
					</el-form-item>
				</el-col>
			</el-row>
		</template>
		<Drawer
			class="drawer-hidden-box"
			:size="drawerSize"
			v-model:drawer="showDrawer"
			:destroy-on-close="drawerTreeDestroyOnClose"
			:isBeforeClose="true"
			@before-close="handleBeforeClose"
		>
			<Title :title="drawerTreeTitle" />
			<DrawerTree
				ref="drawerTree"
				:title="drawerTreeTitle.name[0]"
				:drawerState="showDrawer"
				@onBtnClick="onTreeBtnClick"
				:multi-select="true"
				:checked-keys="checkedKeys"
				:apifunc="treeApi"
				:checkStrictly="checkStrictly"
				:needSingleSelect="needSingleSelect"
			/>
			<!--			<TreeMatType-->
			<!--				:title="drawerTitle"-->
			<!--				:multiSelect="false"-->
			<!--				:read-only="false"-->
			<!--				:drawerState="showDrawer"-->
			<!--				@onBtnClick="onMajorBtnClick"-->
			<!--			/>-->
		</Drawer>
	</div>
</template>

<style scoped lang="scss">
@import "@/app/baseline/assets/css/index.scss";

:deep(.el-input.is-disabled.success-input-color) {
	.el-input__inner {
		color: var(--el-color-success) !important;
		-webkit-text-fill-color: var(--el-color-success) !important;
	}
}

:deep(.el-textarea__inner) {
	/* 针对 WebKit 浏览器隐藏滚动条 */
	-webkit-scrollbar: none;
	/* Firefox */
	scrollbar-width: none; /* Firefox 64+ */
}
:deep(.el-input.is-disabled.danger-input-color) {
	.el-input__inner {
		color: var(--el-color-danger) !important;
		-webkit-text-fill-color: var(--el-color-danger) !important;
	}
}
:deep(.el-date-editor) {
	width: 100%;
	.el-input__wrapper {
		flex-direction: row-reverse;
		padding-right: 1px !important;
	}
	.el-input__prefix {
		padding: 0px 4px 0px 10px !important;
		/* box-shadow: 0 1px 0 0 transparent inset, 0 -1px 0 0 transparent inset,
			-1px 0 0 0 var(--el-input-border-color) inset; */
		border-left: solid 1px var(--el-input-border-color);
		background-color: #f5f7fa !important;
		border-radius: 0 var(--el-border-radius-base) var(--el-border-radius-base) 0;
	}
	.el-input__suffix {
		position: relative;
		left: calc(100% - 65px);
		width: 0px;
	}
	.el-range-input {
		font-size: $---font-size-m;
	}
}
:deep(.date-picker-right-icon .el-input__suffix) {
	display: none; /* 隐藏原来的图标 */
}

:deep(.date-picker-right-icon .el-input__wrapper) {
	padding-right: 30px; /* 添加右侧内边距以便于放置图标 */
	position: relative; /* 设置定位 */
}

:deep(.date-picker-right-icon .el-input__icon) {
	display: none; /* 隐藏原来的图标 */
}

:deep(.date-picker-right-icon .right-icon) {
	position: absolute;
	right: 5px;
	top: 50%;
	transform: translateY(-50%);
}
</style>
