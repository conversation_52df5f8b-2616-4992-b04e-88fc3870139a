import { ref } from "vue"
import { usePagination } from "@/app/platform/hooks/usePagination"

import { useMessageBoxInit } from "@/app/baseline/views/components/messageBox"

const { showWarnConfirm } = useMessageBoxInit()
interface btnObj {
	name: string
	roles?: any
	icon?: string[]
	class?: string
	disabled?: boolean
	confirm?: boolean //确认信息（用于判断是否显示确认提示）
	click: Function //点击回调处理
}

interface BaseLineApiResponseData {
	rows: any[]
	records: number
	[propName: string]: any
}

export function useTbInit<T, P extends Record<string, any>>() {
	const tableCache: any[] = [] //表格数据
	const tableData = ref<T[]>([]) //表格数据
	const tableRef = ref<any>() //表格EL 引用
	const tableProp = ref<TableColumnType[]>([]) //表格列配置
	const tableLoading = ref<boolean>(false) //表格loading锁
	const selectedTableList = ref<anyKey[]>([]) //表格选中行
	const pageSize = ref<number>(usePagination().paginationData.pageSize) //分页参数
	const pageTotal = ref<number>(0) //总数据量
	const currentPage = ref<number>(usePagination().paginationData.currentPage) //当前页码
	const tbBtns = ref<btnObj[]>([]) //底部按钮
	const tbBtnLoading = ref<boolean>(false)
	const fetchParam = ref<Record<string, any>>({}) //查询参数
	const fetchFunc = ref<Function>() //数据查询函数
	const onDataSelected = ref<(e: T) => void>() //数据选中回调
	const onCurrentPageChange = (pageData: any) => {
		pageSize.value = pageData.pageSize
		currentPage.value = pageData.currentPage
		return fetchTableData()
	}
	const fetchTableData = (param?: P): Promise<Ref> => {
		return new Promise((resolve) => {
			if (fetchFunc.value) {
				tableLoading.value = true
				tableRef.value?.clearSelectedTableData()
				fetchParam.value.currentPage! = currentPage.value
				fetchParam.value.pageSize! = pageSize.value
				const _param = {
					...fetchParam.value,
					...param
				}
				fetchFunc
					.value(_param)
					.then((_r: BaseLineApiResponseData) => {
						pageTotal.value = _r.records || 0
						tableData.value = _r.rows || _r
						tableCache.length = 0
						tableCache.push(...JSON.parse(JSON.stringify(_r.rows || _r)))
					})
					.finally(() => {
						tableLoading.value = false
						resolve(tableData)
					})
			} else {
				resolve(tableData)
			}
		})
	}

	const onBtnClick = (btnName?: string) => {
		const _btnConf = tbBtns.value?.find((_b) => _b.name == btnName)
		if (_btnConf != undefined) {
			new Promise<void>((resolve, reject) => {
				tbBtnLoading.value = true
				if (_btnConf?.confirm) {
					showWarnConfirm(`确认要${btnName}吗?`)
						.then(() => resolve())
						.catch(() => reject())
				} else {
					// @ts-ignore
					resolve()
				}
			})
				// @ts-ignore
				.then(() => _btnConf.click(selectedTableList))
				.then(() => {
					fetchTableData()
				})
				.finally(() => {
					tbBtnLoading.value = false
				})
		}
	}

	return {
		tableData,
		tableCache,
		tableRef,
		tableProp,
		tableLoading,
		selectedTableList,
		pageSize,
		pageTotal,
		currentPage,
		fetchParam,
		fetchFunc,
		onDataSelected,
		tbBtns,
		tbBtnLoading,
		onCurrentPageChange,
		fetchTableData,
		onBtnClick
	}
}

/*-------------------初始化表格-start-------------------
import { useTbInit } from "@/app/baseline/views/components/tableBase.ts";

const {
	tableProp, tableData, tableRef, tableLoading, tbBtnLoading,
	pageSize, currentPage, pageTotal, selectedTableList, fetchParam, tbBtns,
	fetchTableData, fetchFunc, onCurrentPageChange, onDataSelected, onBtnClick
} = useTbInit();
tableProp.value					= [
	{ label: "物资分类编码", prop: "code", width: 100 },
];
onDataSelected.value		= (rowList: anyKey[]) => {

}
fetchFunc.value					= MatApplyApi.getMatApplyList;
tbBtns.value	= [
];
*/
/*-------------------------------------Table-tpl-------------------------------------
<PitayaTable
				ref="tableRef"
				:columns="tableProp"
				:table-data="tableData"
				:total="pageTotal"
				@onSelectionChange="onDataSelected"
				@on-current-page-change="onCurrentPageChange"
				:table-loading="tableLoading"
			>
				<template #footerOperateLeft>
					<ButtonList
						class="btn-list"
						:is-not-radius="true"
						:button="tbBtns"
						v-loading="tbBtnLoading"
						@on-btn-click="onBtnClick"
					/>
				</template>
			</PitayaTable>
 */
