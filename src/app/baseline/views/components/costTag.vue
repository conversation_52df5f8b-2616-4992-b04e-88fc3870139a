<script setup lang="ts">
import { defineProps, withDefaults } from "vue"
import XEUtils from "xe-utils"
interface Props {
	value?: string | number | null | undefined
}
const props = withDefaults(defineProps<Props>(), {
	value: null
})
</script>
<template>
	<span
		v-if="props.value != undefined && props?.value?.toString()?.length > 0"
		>{{ XEUtils.commafy(props.value, { digits: 5 }) }}</span
	>
	<template v-else>¥0.00000</template>
</template>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

span {
	&:before {
		content: "￥";
	}
}
</style>
