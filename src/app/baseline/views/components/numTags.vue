<script lang="ts" setup>
import {defineProps,} from 'vue'
export interface Props {
	dataList : { value : number, title : string }[]
}

const props = withDefaults(defineProps<Props>(), {
	dataList	: undefined
})

</script>
<template>
	<div class="mum-tag">
		<div class="tag-item" v-for="data in props.dataList">
			<span class="tag-item-value">{{ data.value }}</span>
			<label class="tag-item-title">{{ data.title }}</label>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.mum-tag{
	display: flex;
	padding:$---spacing-m;
	.tag-item{
		display: flex;
		flex-direction: column;
		width: 50%;
		align-items: center;
		border-right:solid 1px $---border-color;
		&:last-child{
			border-right:none;
		}
	}

	.tag-item-value{
		color:$---color-info;
		font-size:30px;
		font-weight: bold;
	}
}
</style>
