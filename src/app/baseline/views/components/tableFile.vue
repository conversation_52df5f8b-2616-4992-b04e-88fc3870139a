<script lang="ts" setup>
import { ElMessage } from "element-plus"
import { usePagination } from "@/app/platform/hooks/usePagination"
import { ref, onMounted } from "vue"
import type {
	UploadInstance,
	UploadProps,
	UploadRawFile,
	UploadProgressEvent,
	UploadRequestOptions
} from "element-plus"
import { getToken } from "@/app/platform/utils/cache/cookies"
import { FileApi } from "@/app/baseline/api/file"
import { useUserStore } from "@/app/platform/store/modules/user"
import { convertBytesToMegabytes, hasPermi } from "@/app/baseline/utils"
import { useMessageBoxInit } from "@/app/baseline/views/components/messageBox"
import UploadFile from "@/compontents/UploadFile.vue"
import fileDrawer from "./fileDrawer.vue"
const { showDelConfirm, showErrorConfirm } = useMessageBoxInit()
interface Props {
	businessId?: string | number //业务ID
	businessIds?: string | number //业务ID列表
	businessType: string | number //业务类型
	needPage: boolean // 是否需要分页
	mod: string //显示模式  view : 查看, edit : 编辑
	multiple?: boolean
	btnName?: string
	fileName?: string
	fileLabel?: string
	accept?: string // 上传文件类型
	allExtensions?: string[]
	allowsize?: number
	isButton?: boolean
}

const props = withDefaults(defineProps<Props>(), {
	businessId: undefined,
	businessIds: undefined,
	businessType: "",
	needPage: true,
	mod: "edit",
	multiple: true,
	btnName: "添加附件",
	fileName: "附件名称",
	fileLabel: "上传附件",
	isButton: true
})

const tableEndBtn = computed(() => {
	return [
		{
			name: props.btnName,
			icon: ["fas", "circle-plus"]
		}
	]
})

const fileTxtConf = computed(() => {
	return {
		title: props.btnName,
		fileName: props.fileName,
		fileLabel: props.fileLabel
	}
})
const tableProp: TableColumnType[] = [
	{ label: "附件编号", prop: "code", needSlot: true, width: 150 },
	{ label: "附件名称", prop: "customFileName", minWidth: 150 },
	{ label: "附件格式", prop: "fileType", width: 85 },
	{ label: "附件大小", prop: "fileSize", needSlot: true, width: 120 },
	{ label: "上传人", prop: "createdBy_view", width: 120 },
	{ label: "上传时间", prop: "createDate", needSlot: true, width: 160 },
	{
		label: "操作",
		prop: "operations",
		fixed: "right",
		width: props.mod == "view" ? 130 : 180,
		needSlot: true
	}
]

const usePaginationStore = usePagination()
const usePaginationStoreForData = usePaginationStore.paginationData
const pageTotal = ref<number>(0)
const currentPage = ref<number>(usePaginationStoreForData.currentPage)
const pageSize = ref<number>(usePaginationStoreForData.pageSize)
const refFileTable = ref()
const tableLoading = ref<boolean>(false)
const tableData = ref<any[]>([])

const fetchFunc = ref<Function>()
const getTableData = () => {
	tableLoading.value = true
	const params = {
		businessType: props.businessType,
		businessId: props.businessId,
		businessIds: props.businessIds,
		currentPage: currentPage.value,
		pageSize: pageSize.value
	}
	let _fetchFunc = props.businessIds
		? FileApi.getFileListByBusinessIds
		: FileApi.getFileList
	if (props.needPage) {
		_fetchFunc(params)
			.then((res: any) => {
				pageTotal.value = res.records
				tableData.value = res.rows
			})
			.finally(() => {
				tableLoading.value = false
			})
	} else {
		FileApi.getAllFileList(params)
			.then((res: any) => {
				tableData.value = res
			})
			.finally(() => {
				tableLoading.value = false
			})
	}
}
// 分页回调
const onCurrentPageChange = (pageData: any) => {
	pageSize.value = pageData.pageSize
	currentPage.value = pageData.currentPage
	getTableData()
}

// 编辑
const onRowDownLoad = (row: any) => {
	FileApi.downloadFile(row.filePath)
}

// 移除
const onRowDelete = (row: any) => {
	showDelConfirm().then(() => {
		FileApi.deleteFile({ id: row.id }).then(() => {
			ElMessage.success("移除成功")
			getTableData()
		})
	})
}
//*******************文件上传*********************************/
const uploadUrl = `/pitaya/system/common/upload?businessType=${props.businessType}&businessId=${props.businessId}`
const filePercents = ref<any>({})
const upload = ref<UploadInstance>()
const handleBeforeUpload: UploadProps["beforeUpload"] = (
	file: UploadRawFile
) => {
	const str = file.name
	const lastIndex = str.lastIndexOf(".")
	const name = str.substring(0, lastIndex)
	const fmt = str.substring(lastIndex + 1)

	const formats = ["pdf", "doc", "docx", "xlsx", "png", "jpg", "jpeg"]
	if (!formats.includes(fmt.toLowerCase())) {
		ElMessage.warning("只能上传pdf、doc、docx、xlsx、png、jpg格式的文件")
		return false
	}
	if (file.size > 100 * 1024 * 1024) {
		// 文件大于 100MB 时取消上传
		ElMessage.warning("文件必须小于100MB")
		return false
	}

	const data = {
		id: -1,
		code: "---",
		fileName: name,
		fileType: fmt,
		fileSize: convertBytesToMegabytes(file.size),
		uid: file.uid
	}
	filePercents.value[file.uid] = 0
	tableData.value.unshift(data)
	return true
}

const handleProgress: UploadProps["onProgress"] = (
	evt: UploadProgressEvent,
	file
) => {
	filePercents.value[file.uid] = evt.percent.toFixed(0)
}
const handleSuccess: UploadProps["onSuccess"] = (response, uploadFile) => {
	//imageUrl.value = URL.createObjectURL(uploadFile.raw!)
}
const handleUploadFile = (params: UploadRequestOptions) => {
	const formData = new FormData()
	formData.append("file", params.file)
	request({
		url: uploadUrl,
		method: "POST",
		headers: {
			Authorization: "Bearer " + getToken(),
			"Content-Type": "multipart/form-data"
		},
		data: formData,
		onUploadProgress: function (evt) {
			filePercents.value[params.file.uid] = Number(
				((evt.loaded / evt.total) * 95).toFixed(0)
			)
		}
	})
		.then((res) => {
			//	imageUrl.value = previewUrl + res.filePath
			const targetObj = tableData.value.find(
				(obj) => obj.uid === params.file.uid
			)
			if (targetObj) {
				// 替换对象的内容
				const userStore = useUserStore()
				const { userInfo } = storeToRefs(userStore)
				Object.assign(targetObj, res)
				targetObj.createdBy_view = userInfo.value.realName
				pageTotal.value += 1
			}
		})
		.catch(() => {})
}
function getCode(row) {
	const paddedNum1 = String(row.businessType).padEnd(3, "0")
	const paddedNum2 = String(row.businessId).padEnd(6, "0")
	const paddedNum3 = String(row.id).padStart(7, "0")
	return paddedNum1 + paddedNum2 + paddedNum3
}

/**
 * 附件抽屉
 */
const fileDrawerVisible = ref(false)
const onFormBtnList = (btnName: string | undefined) => {
	if (btnName === props.btnName) {
		if (!props.multiple && tableData.value.length > 0) {
			return showErrorConfirm("文件已上传,如需重新上传请先移除已上传文件!")
		}
		fileDrawerVisible.value = true
	}
}
const closeDrawer = () => {
	fileDrawerVisible.value = false
}
onMounted(() => {
	if ((props.businessId || props.businessIds) && props.businessType) {
		getTableData()
	}
})
watch(
	() => props.businessId,
	() => {
		props.businessId && getTableData()
	}
)
defineOptions({
	name: "tableFile"
})
const tableFileList = computed(() => {
	return tableData.value || []
})
defineExpose({ tableFileList, getTableData })
</script>

<template>
	<div>
		<PitayaTable
			ref="refFileTable"
			:columns="tableProp"
			:table-data="tableData"
			:needSelection="false"
			:single-select="false"
			:need-index="true"
			:need-pagination="props.needPage"
			:total="pageTotal"
			@on-current-page-change="onCurrentPageChange"
			:table-loading="tableLoading"
			:maxHeight="props.needPage ? 0 : 400"
		>
			<template #createDate="{ rowData }">
				<div v-if="rowData.id !== -1">{{ rowData.createdDate }}</div>
				<div v-else>
					<el-progress
						:text-inside="true"
						:stroke-width="14"
						:percentage="filePercents[rowData.uid]"
						status="success"
					/>
				</div>
			</template>
			<template #code="{ rowData }">
				{{ getCode(rowData) }}
			</template>
			<template #fileSize="{ rowData }">
				{{ convertBytesToMegabytes(rowData.fileSize) }}
			</template>
			<template #operations="{ rowData }">
				<div v-if="rowData.id !== -1">
					<PreviewButton :fileUrl="rowData.filePath">
						<el-button v-btn link>
							<font-awesome-icon :icon="['fas', 'eye']" />
							<span class="table-inner-btn">查看</span>
						</el-button>
					</PreviewButton>
					<el-button
						v-btn
						link
						@click="onRowDownLoad(rowData)"
						style="margin-left: 10px !important"
					>
						<font-awesome-icon :icon="['fas', 'file-arrow-down']" />
						<span class="table-inner-btn">下载</span>
					</el-button>
					<el-button
						v-btn
						link
						:disabled="hasPermi(rowData.createdBy) ? false : true"
						@click="onRowDelete(rowData)"
						v-if="props.mod == 'edit' || props.mod == 'create'"
					>
						<font-awesome-icon :icon="['fas', 'trash-can']" />
						<span class="table-inner-btn">移除</span>
					</el-button>
				</div>
			</template>
			<template #footerOperateLeft>
				<ButtonList
					v-if="mod !== 'view' && isButton"
					class="btn-list"
					:is-not-radius="true"
					:button="tableEndBtn"
					@onBtnClick="onFormBtnList"
				/>
				<!-- <UploadFile
					v-if="mod !== 'view'"
					:action="uploadUrl"
					:accept="fmt"
					@onSuccess="getTableData"
					listType="text"
				></UploadFile> -->
				<!--				<el-upload
					v-if="mod !== 'view'"
					ref="upload"
					:before-upload="handleBeforeUpload"
					:on-progress="handleProgress"
					:on-success="handleSuccess"
					:show-file-list="false"
					:http-request="handleUploadFile"
				>
					<div>
						<ButtonList
							class="btn-list"
							:is-not-radius="true"
							:button="tableEndBtn"
						/>
					</div>
				</el-upload>-->
			</template>
		</PitayaTable>

		<Drawer
			:size="310"
			v-model:drawer="fileDrawerVisible"
			:destroyOnClose="true"
		>
			<file-drawer
				:businessType="props.businessType"
				:businessId="props.businessId"
				:fileTxtConf="fileTxtConf"
				:accept="props.accept"
				:allExtensions="props.allExtensions"
				:allowsize="props.allowsize"
				@onUpdateList="getTableData"
				@onSaveOrClose="closeDrawer"
			/>
		</Drawer>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.one-query-rows {
	height: auto !important;
}
</style>
