<template>
	<div class="line-container">
		<template v-for="(item, index) in options">
			<template v-if="values.includes(item.id)">
				<el-tag
					:disable-transitions="true"
					:key="item.id"
					:index="index"
					class="line-item"
					:style="{
						background: item.colour
					}"
				>
					{{ item.name }}
				</el-tag>
			</template>
		</template>
		<template v-if="hasNoMatchingItems">---</template>
	</div>
</template>

<script lang="ts" setup>
import { ref, computed,watch } from "vue"

const props = defineProps({
	options: {
		type: Array as () => Array<any>,
		default: null
	},
	value: {
		type: [Number, String, Array] as unknown as unknown as unknown as () => Number | String | Array<any>,
		default: null
	}
})

const values = ref<Array<string>>([])
const hasNoMatchingItems = ref(false)

watch(
	() => props.options,
	(newOptions) => {
		if (newOptions && Array.isArray(newOptions)) {
			values.value =
				props.value !== null && typeof props.value !== "undefined"
					? Array.isArray(props.value)
						? props.value.map(String)
						: String(props.value)
								.split(",")
								.map((item) => item.trim())
					: []
			hasNoMatchingItems.value = !newOptions.some((item) =>
				values.value.includes(item.id)
			)
		}
	},
	{ immediate: true }
)
watch(
	() => props.value,
	(newValue) => {
		if (Array.isArray(newValue)) {
			values.value = newValue.map(String)
		} else {
			values.value = [String(newValue)]
		}
		hasNoMatchingItems.value = !props.options.some((item) =>
			values.value.includes(item.id)
		)
	}
)
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.line-container {
	display: inline-flex;
	align-items: center;
	justify-content: center;
	overflow: hidden;
	.line-item {
		margin-right: 10px;
		height: 22px;
		display: inline-block;
		align-items: center;
		justify-content: center;
		padding: 0 10px;
		border-radius: 3px;
		font-size: var(--pitaya-fs-12);
		color: #fff;
		&:last-child {
			margin-right: 0;
		}
	}
}
</style>
