interface FormElementType {
	name: string
	label: string
	type?: string
	maxlength?: string | number
	valueFormat?: string
	blur?: (arg?: any) => any
	input?: (arg?: any) => any
	change?: (arg?: any) => any
	placeholder?: string
	showLimit?: true
	rows?: string | number
	disabled?: boolean
	data?: ref<any>[]
	width?: number
	bizType?: "major" | "lines"
	treeApi?: Function
	needSingleSelect?: boolean
	vname?: string
	hidden?: boolean
	clear?: boolean
	clickApi?: Function
	max?: number
	min?: number
	precision?: number
	append?: string
	startPlaceholder?: string
	endPlaceholder?: string
	storeLabel?: string
	disabledDate?: (arg?: any) => any
	slotLabelContent?: string
	className?: string
	format?: string
}

/**
 * 模块相关权限定义
 */
export const powerList = {
	//物资分类
	matCodeBtnCreate: "material:matCode:matType:btn:add", //新建
	matCodeBtnPreview: "material:matCode:matType:btn:preview", //查看权限
	matCodeBtnEdit: "material:matCode:matType:btn:edit", //编辑权限
	matCodeBtnDrop: "material:matCode:matType:btn:drop", //删除权限
	matCodeBtnStart: "material:matCode:matType:btn:start", //启用权限
	matCodeBtnFreeze: "material:matCode:matType:btn:freeze", //冻结权限
	matCodeBtnVoid: "material:matCode:matType:btn:void", //作废权限
	matCodeBtnThawing: "material:matCode:matType:btn:thawing", //解冻权限

	//编码申请
	matApplyBtnCreate: "material:matCode:matApply:btn:add", //新建
	matApplyBtnPreview: "material:matCode:matApply:btn:preview", //查看权限
	matApplyBtnEdit: "material:matCode:matApply:btn:edit", //编辑权限
	matApplyBtnDrop: "material:matCode:matApply:btn:drop", //删除权限
	matApplyBtnApproval: "material:matCode:matApply:btn:approval", //审批权限

	//编码手册
	matManualBtnPreview: "material:matCode:matManual:btn:preview", //查看
	matManualBtnFreeze: "material:matCode:matManual:btn:freeze", //冻结权限
	matManualBtnVoid: "material:matCode:matManual:btn:void", //作废权限
	matManualBtnThawing: "material:matCode:matManual:btn:thawing", //解冻权限
	matManualBtnProcurement: "material:matCode:matManual:btn:procurement", //采购信息
	matManualBtnMaterialProperties:
		"material:matCode:matManual:btn:materialProperties", //更新物资性质
	matManualBtnMaterialQuality: "material:matCode:matManual:btn:materialQuality", //更新物资材质
	matManualBtnMaterialRecipientConf:
		"material:matCode:matManual:btn:recipientConf", // 接收人配置
	matManualBtnMaterialUpdatePrice: "material:matCode:matManual:btn:updatePrice",

	// 阈值管理
	matThresholdBtnCreate: "material:matCode:matThreshold:btn:add", //新建
	matThresholdBtnPreview: "material:matCode:matThreshold:btn:preview", //查看权限
	matThresholdBtnEdit: "material:matCode:matThreshold:btn:edit", //编辑权限
	matThresholdBtnDrop: "material:matCode:matThreshold:btn:drop", //删除权限
	matThresholdBtnStart: "material:matCode:matThreshold:btn:start", //启用权限
	matThresholdBtnClose: "material:matCode:matThreshold:btn:close", //关闭权限

	// 计划 - 需求清单
	needListBtnCreate: "plan:needList:btn:create", //新建
	needListBtnPreview: "plan:needList:btn:preview", //查看权限
	needListBtnEdit: "plan:needList:btn:edit", //编辑权限
	needListBtnDrop: "plan:needList:btn:drop", //删除权限
	needListBtnStart: "plan:needList:btn:start", //启用权限
	needListBtnStop: "plan:needList:btn:stop", //停用权限
	needListBtnCopy: "plan:needList:btn:copy", //停用权限

	// 计划 - 需求计划
	needPlanBtnCreate: "plan:needPlan:btn:add", //新建
	needPlanBtnPreview: "plan:needPlan:btn:preview", //查看权限
	needPlanBtnEdit: "plan:needPlan:btn:edit", //编辑权限
	needPlanBtnDrop: "plan:needPlan:btn:drop", //删除权限

	// 计划 - 合并计划
	mergePlanBtnCreate: "plan:merge:mergePlan:btn:add", //新建
	mergePlanBtnPreview: "plan:merge:mergePlan:btn:preview", //查看权限
	mergePlanBtnEdit: "plan:merge:mergePlan:btn:edit", //编辑权限
	mergePlanBtnDrop: "plan:merge:mergePlan:btn:drop", //删除权限

	// 计划 - 采购计划
	purchasePlanBtnCreate: "plan:merge:purchasePlan:btn:add", //新建
	purchasePlanBtnPreview: "plan:merge:purchasePlan:btn:preview", //查看权限
	purchasePlanBtnEdit: "plan:merge:purchasePlan:btn:edit", //编辑权限
	purchasePlanBtnDrop: "plan:merge:purchasePlan:btn:drop", //删除权限
	purchasePlanBtnSend: "plan:merge:purchasePlan:btn:send", //发送电商

	// 计划 - 费用类别
	systemCostCategoryBtnCreate: "system:costCategory:btn:add", //新建
	systemCostCategoryBtnPreview: "system:costCategory:btn:preview", //查看权限
	systemCostCategoryBtnEdit: "system:costCategory:btn:edit", //编辑权限
	systemCostCategoryBtnDrop: "system:costCategory:btn:drop", //删除权限
	systemCostCategoryBtnStart: "system:costCategory:btn:start", //启用权限
	systemCostCategoryBtnFreeze: "system:costCategory:btn:freeze", //冻结权限
	systemCostCategoryBtnThawing: "system:costCategory:btn:thawing", //解冻权限

	// 计划 - 段区
	systemDepotBtnCreate: "system:depot:btn:add", //新建
	systemDepotBtnPreview: "system:depot:btn:preview", //查看权限
	systemDepotBtnEdit: "system:depot:btn:edit", //编辑权限
	systemDepotBtnDrop: "system:depot:btn:drop", //删除权限
	systemDepotBtnStart: "system:depot:btn:start", //启用权限
	systemDepotBtnStop: "system:depot:btn:stop", //停用权限

	//采购-供应商信息
	purchaseSupplierBtnCreate: "purchase:supplier:btn:add", //新建
	purchaseSupplierBtnPreview: "purchase:supplier:btn:preview", //查看
	purchaseSupplierBtnEdit: "purchase:supplier:btn:edit", //编辑
	purchaseSupplierBtnDrop: "purchase:supplier:btn:drop", //删除

	//采购-采购分包
	purchaseDistributionBtnCreate: "purchase:distribution:btn:add", //新建
	purchaseDistributionBtnPreview: "purchase:distribution:btn:preview", //查看
	purchaseDistributionBtnEdit: "purchase:distribution:btn:edit", //编辑
	purchaseDistributionBtnDrop: "purchase:distribution:btn:drop", //删除

	//订货计划
	purchaseOrderPlanBtnCreate: "purchase:plan:btn:add", //新建
	purchaseOrderPlanBtnManagerPreview: "purchase:plan:btn:managerpreview", //管理查看权限
	purchaseOrderPlanBtnEditPreview: "purchase:plan:btn:editpreview", //编辑查看权限
	purchaseOrderPlanBtnEdit: "purchase:plan:btn:edit", //编辑权限
	purchaseOrderPlanBtnDrop: "purchase:plan:btn:drop", //删除权限
	purchaseOrderPlanBtnOrder: "purchase:plan:btn:generateOrder", //生成订单

	// 采购 - 采购订单
	purchaseOrderBtnCreate: "purchase:order:btn:add", //新建
	purchaseOrderBtnPreview: "purchase:order:btn:preview", //查看
	purchaseOrderBtnEdit: "purchase:order:btn:edit", //编辑
	purchaseOrderBtnDrop: "purchase:order:btn:drop", //删除
	purchaseOrderBtnDesignatedPersonnel: "purchase:order:btn:designatedPersonnel", //指定采购
	purchaseOrderBtnConfirmOrder: "purchase:order:btn:confirmOrder", //确认订单
	purchaseOrderBtnNotifySuppliers: "purchase:order:btn:notifySuppliers", //通知供应商
	purchaseOrderBtnChange: "purchase:order:btn:change", //变更采购单价
	purchaseOrderBtnArrival: "purchase:order:btn:confirmArrival", //确认到货
	purchaseOrderBtnClose: "purchase:order:btn:close", //关闭

	//采购-采购合同
	purchaseContractBtnCreate: "purchase:contract:btn:add", //新建
	purchaseContractBtnPreview: "purchase:contract:btn:preview", //查看
	purchaseContractBtnEdit: "purchase:contract:btn:edit", //编辑
	purchaseContractBtnDrop: "purchase:contract:btn:drop", //删除

	//采购项目
	purchaseProjectBtnCreate: "purchase:project:btn:add", //新建
	purchaseProjectBtnPreview: "purchase:project:btn:preview", //查看
	purchaseProjectBtnEdit: "purchase:project:btn:edit", //编辑
	purchaseProjectBtnDrop: "purchase:project:btn:drop", //删除
	purchaseProjectBtnGeneratePlan: "purchase:project:btn:generatePlan", //生成月度订货计划
	purchaseProjectBtnClose: "purchase:project:btn:close", //关闭

	//发票管理
	purchaseInvoiceBtnCreate: "purchase:invoice:btn:add", //新建
	purchaseInvoiceBtnPreview: "purchase:invoice:btn:preview", //查看
	purchaseInvoiceBtnEdit: "purchase:invoice:btn:edit", //编辑
	purchaseInvoiceBtnDrop: "purchase:invoice:btn:drop", //删除

	//年度计划
	yearPlanAdd: "plan:yearPlan:btn:add", // 新增
	yearPlanEdit: "plan:yearPlan:btn:edit", //编辑
	yearPlanDrop: "plan:yearPlan:btn:drop", // 移除
	yearPlanPreview: "plan:yearPlan:btn:preview", //查看
	yearPlanStart: "plan:yearPlan:btn:start", // 启动
	yearPlanStop: "plan:yearPlan:btn:stop", // 结束

	// 库存 - 仓库
	storeManagelBtnCreate: "store:managel:btn:add", // 新建
	storeManagelBtnPreview: "store:managel:btn:preview", // 查看权限
	storeManagelBtnEdit: "store:managel:btn:edit", // 编辑权限
	storeManagelBtnDrop: "store:managel:btn:drop", // 删除权限
	storeManagelBtnFreeze: "store:managel:btn:freeze", // 冻结
	storeManagelBtnStop: "store:managel:btn:stop", // 停用
	storeManagelBtnStart: "store:managel:btn:start", //启用权限
	storeManagelBtnQrCode: "store:managel:btn:qrCode", // 生成货位码

	// 库存 - 成本中心
	systemCostCenterBtnCreate: "system:costCenter:btn:add", //新建
	systemCostCenterBtnPreview: "system:costCenter:btn:preview", //查看权限
	systemCostCenterBtnEdit: "system:costCenter:btn:edit", //编辑权限
	systemCostCenterBtnDrop: "system:costCenter:btn:drop", //删除权限
	systemCostCenterBtnStart: "system:costCenter:btn:start", //启用权限
	systemCostCenterBtnStop: "system:costCenter:btn:stop", //停用权限

	// 库存 - 库存查询
	storeBusinessInventoryBtnPreview: "store:business:inventory:btn:preview", // 查看权限
	storeBusinessInventoryBtnQrCode: "store:business:inventory:btn:qrCode", // 生成物资码

	// 库存 - 业务管理 - 入库申请
	storeBusinessWarehousingApplyBtnPreview:
		"store:business:warehousingApplication:btn:preview", // 查看权限
	storeBusinessWarehousingApplyBtnQuality:
		"store:business:warehousingApplication:btn:quality", // 推送质检
	storeBusinessWarehousingApplyBtnBatchQuality:
		"store:business:warehousingApplication:btn:batchQuality", // 批量推送质检
	storeBusinessWarehousingApplyBtnClose:
		"store:business:warehousingApplication:btn:close", // 关闭

	// 库存 - 业务管理 - 其他入库申请
	storeBusinessOtherWarehouseApplyBtnCreate:
		"store:business:otherWarehouse:btn:add", // 新增权限
	storeBusinessOtherWarehouseApplyBtnPreview:
		"store:business:otherWarehouse:btn:preview", // 查看权限
	storeBusinessOtherWarehouseApplyBtnEdit:
		"store:business:otherWarehouse:btn:edit", // 编辑权限
	storeBusinessOtherWarehouseApplyBtnDrop:
		"store:business:otherWarehouse:btn:drop", // 删除权限
	storeBusinessOtherWarehouseApplyBtnQuality:
		"store:business:otherWarehouse:btn:quality", // 推送质检

	// 库存 - 业务管理 - 领料申请
	storeBusinessMatPickApplyBtnCreate:
		"store:business:materialRequisition:btn:add", // 新增权限
	storeBusinessMatPickApplyBtnPreview:
		"store:business:materialRequisition:btn:preview", // 查看权限
	storeBusinessMatPickApplyBtnEdit:
		"store:business:materialRequisition:btn:edit", // 编辑权限
	storeBusinessMatPickApplyBtnDrop:
		"store:business:materialRequisition:btn:drop", // 删除权限
	storeBusinessMatPickApplyBtnHandover:
		"store:business:materialRequisition:btn:handover", // 物资领料交旧

	// 库存 - 业务管理 - 退库申请
	storeBusinessInventoryReturnApplyBtnCreate:
		"store:business:inventoryReturn:btn:add", // 新增权限
	storeBusinessInventoryReturnApplyBtnPreview:
		"store:business:inventoryReturn:btn:preview", // 查看权限
	storeBusinessInventoryReturnApplyBtnEdit:
		"store:business:inventoryReturn:btn:edit", // 编辑权限
	storeBusinessInventoryReturnApplyBtnDrop:
		"store:business:inventoryReturn:btn:drop", // 删除权限
	storeBusinessInventoryReturnApplyBtnQuality:
		"store:business:inventoryReturn:btn:quality", // 推送质检

	// 库存 - 业务管理 - 退货申请
	storeBusinessGoodsReturnApplyBtnCreate: "store:business:goodsReturn:btn:add", // 新增权限
	storeBusinessGoodsReturnApplyBtnPreview:
		"store:business:goodsReturn:btn:preview", // 查看权限
	storeBusinessGoodsReturnApplyBtnEdit: "store:business:goodsReturn:btn:edit", // 编辑权限
	storeBusinessGoodsReturnApplyBtnDrop: "store:business:goodsReturn:btn:drop", // 删除权限

	// 库存 - 入库管理 - 质量检验
	storeWarehouseQualityInspectionBtnEdit:
		"store:warehouse:qualityInspection:btn:edit", // 编辑权限
	storeWarehouseQualityInspectionBtnPreview:
		"store:warehouse:qualityInspection:btn:preview", // 查看权限
	storeWarehouseQualityInspectionBtnShare:
		"store:warehouse:qualityInspection:btn:transfer", // 转派权限

	// 库存 - 入库管理 - 接收入库
	storeWarehouseReceivedStoredBtnEdit:
		"store:warehouse:receivedStored:btn:edit", // 编辑权限
	storeWarehouseReceivedStoredBtnPreview:
		"store:warehouse:receivedStored:btn:preview", // 查看权限

	// 库存 - 入库管理 - 交旧入库
	storeWarehouseHandoverStoredReceivedBtnPreview:
		"store:warehouse:handoverStoredReceived:btn:preview", // 查看权限
	storeWarehouseHandoverStoredReceivedBtnEdit:
		"store:warehouse:handoverStoredReceived:btn:edit", // 编辑权限

	// 库存 - 入库管理 - 退库入库
	storeWarehouseInventoryReturnReceivedBtnPreview:
		"store:warehouse:inventoryReturnReceived:btn:preview", // 查看权限
	storeWarehouseInventoryReturnReceivedBtnEdit:
		"store:warehouse:inventoryReturnReceived:btn:edit", // 编辑权限

	// 库存 - 出库管理 - 领料出库
	storeOutboundMaterialOutboundBtnPreview:
		"store:outbound:materialOutbound:btn:preview", // 查看权限
	storeOutboundMaterialOutboundBtnEdit:
		"store:outbound:materialOutbound:btn:edit", // 编辑权限
	storeOutboundMaterialOutboundBtnClose:
		"store:outbound:materialOutbound:btn:close", // 关闭权限

	// 库存 - 出库管理 - 退货出库
	storeOutboundReturnOutboundBtnPreview:
		"store:outbound:returnOutbound:btn:preview", // 查看权限
	storeOutboundReturnOutboundBtnEdit: "store:outbound:returnOutbound:btn:edit", // 编辑权限
	storeOutboundReturnOutboundBtnClose:
		"store:outbound:returnOutbound:btn:close", // 关闭权限

	// 库存 - 出库管理 - 返修出库
	storeOutboundRepairOutboundBtnPreview:
		"store:outbound:repairOutbound:btn:preview", // 查看权限
	storeOutboundRepairOutboundBtnEdit: "store:outbound:repairOutbound:btn:edit", // 编辑权限
	storeOutboundRepairOutboundBtnClose:
		"store:outbound:repairOutbound:btn:close", // 关闭权限

	// 库存 - 出库管理 - 其他出库
	storeOutboundOtherOutboundBtnPreview:
		"store:outbound:otherOutbound:btn:preview",

	// 库存 - 调拨管理 - 调拨申请
	storeTransferApplyBtnCreate: "store:transfer:transferApplication:btn:add", // 新增权限
	storeTransferApplyBtnPreview:
		"store:transfer:transferApplication:btn:preview", // 查看权限
	storeTransferApplyBtnEdit: "store:transfer:transferApplication:btn:edit", // 编辑权限
	storeTransferApplyBtnDrop: "store:transfer:transferApplication:btn:drop", // 删除权限

	// 库存 - 调拨管理 - 调拨出库
	storeTransferOutboundBtnPreview:
		"store:transfer:transferOutbound:btn:preview", // 查看权限

	// 库存 - 调拨管理 - 调拨入库
	storeTransferReceiptBtnPreview: "store:transfer:transferReceipt:btn:preview", // 查看权限
	storeTransferReceiptBtnEdit: "store:transfer:transferReceipt:btn:edit", // 编辑权限
	storeTransferReceiptBtnClose: "store:transfer:transferReceipt:btn:close", // 关闭权限

	// 库存 - 盘点管理 - 盘点计划
	storeInventoryPlanBtnCreate: "store:inventoryManage:inventoryPlant:btn:add", // 新增权限
	storeInventoryPlanBtnPreview:
		"store:inventoryManage:inventoryPlan:btn:preview", // 查看权限
	storeInventoryPlanBtnEdit: "store:inventoryManage:inventoryPlan:btn:edit", // 编辑权限
	storeInventoryPlanBtnDrop: "store:inventoryManage:inventoryPlan:btn:drop", // 删除权限
	storeInventoryPlanBtnDiff: "store:inventoryManage:inventoryPlan:btn:diff", // 差异处理
	storeInventoryPlanBtnReport: "store:inventoryManage:inventoryPlan:btn:report", // 盘点报告

	// 库存 - 盘点管理 - 盘点任务
	storeInventoryTaskBtnPreview:
		"store:inventoryManage:inventoryTask:btn:preview", // 查看权限
	storeInventoryTaskBtnEdit: "store:inventoryManage:inventoryTask:btn:edit", // 编辑权限
	storeInventoryTaskBtnStart: "store:inventoryManage:inventoryTask:btn:start", // 开始盘点
	storeInventoryTaskBtnFinish: "store:inventoryManage:inventoryTask:btn:finish", // 结束盘点

	// 库存 - 盘点管理 - 盘盈入库
	storeInventoryInStoreBtnPreview:
		"store:inventoryManage:inventoryInStore:btn:preview", // 查看权限

	// 库存 - 盘点管理 - 盘亏出库
	storeInventoryOutStoreBtnPreview:
		"store:inventoryManage:inventoryOutStore:btn:preview", // 查看权限

	// 废旧 - 交旧申请
	wasteHandoverApplyBtnPreview: "waste:handoverApplication:btn:preview", // 查看权限
	wasteHandoverApplyBtnCreate: "waste:handoverApplication:btn:add", // 新建权限
	wasteHandoverApplyBtnEdit: "waste:handoverApplication:btn:edit", // 编辑权限
	wasteHandoverApplyBtnDrop: "waste:handoverApplication:btn:drop", // 删除权限

	// 废旧 - 返修台账
	wasteRepairLedgerBtnPreview: "waste:repairLedger:btn:preview", // 查看权限

	// 废旧 - 返修申请
	wasteRepairApplyBtnPreview: "waste:repairApplication:btn:preview", // 查看权限
	wasteRepairApplyBtnCreate: "waste:repairApplication:btn:add", // 新建权限
	wasteRepairApplyBtnEdit: "waste:repairApplication:btn:edit", // 编辑权限
	wasteRepairApplyBtnDrop: "waste:repairApplication:btn:drop", // 删除权限

	// 废旧 - 废旧台账
	wasteScrapLedgerBtnPreview: "waste:scrapLedger:btn:preview", // 查看权限

	// 废旧 - 厂商管理
	wasteSupplierBtnPreview: "waste:supplier:btn:preview", // 查看权限
	wasteSupplierBtnCreate: "waste:supplier:btn:add", // 新建权限
	wasteSupplierBtnEdit: "waste:supplier:btn:edit", // 编辑权限
	wasteSupplierBtnDrop: "waste:supplier:btn:drop", // 删除权限

	// 废旧 - 报废申请
	wasteScrapApplyBtnPreview: "waste:scrapApplication:btn:preview", // 查看权限
	wasteScrapApplyBtnCreate: "waste:scrapApplication:btn:add", // 新建权限
	wasteScrapApplyBtnEdit: "waste:scrapApplication:btn:edit", // 编辑权限
	wasteScrapApplyBtnDrop: "waste:scrapApplication:btn:drop", // 删除权限

	// 废旧 - 报废处置
	wasteScrapDisposalBtnPreview: "waste:scrapDisposal:btn:preview", // 查看权限
	wasteScrapDisposalBtnCreate: "waste:scrapDisposal:btn:add", // 新建权限
	wasteScrapDisposalBtnEdit: "waste:scrapDisposal:btn:edit", // 编辑权限
	wasteScrapDisposalBtnDrop: "waste:scrapDisposal:btn:drop", // 删除权限

	// 低值 - 台账查询
	lowValueLedgerBtnPreview: "lowValue:ledger:btn:preview", // 查看权限

	// 低值 - 领用申请
	lowValueRequisitionApplyBtnPreview:
		"lowValue:requisitionApplication:btn:preview", //查看权限
	lowValueRequisitionApplyBtnCreate: "lowValue:requisitionApplication:btn:add", //新建权限
	lowValueRequisitionApplyBtnEdit: "lowValue:requisitionApplication:btn:edit", //编辑权限
	lowValueRequisitionApplyBtnDistribution:
		"lowValue:requisitionApplication:btn:distribution", //分配权限
	lowValueRequisitionApplyBtnDrop: "lowValue:requisitionApplication:btn:drop", //删除权限

	// 低值 - 归还申请
	lowValueReturnApplyBtnPreview: "lowValue:returnApplication:btn:preview", //查看权限
	lowValueReturnApplyBtnCreate: "lowValue:returnApplication:btn:add", //新建权限
	lowValueReturnApplyBtnEdit: "lowValue:returnApplication:btn:edit", //编辑权限
	lowValueReturnApplyBtnDrop: "lowValue:returnApplication:btn:drop", //删除权限

	// 低值 - 盘点管理 - 盘点计划
	lowValueInventoryPlanBtnCreate:
		"lowValue:inventoryManage:inventoryPlan:btn:add", // 新增权限
	lowValueInventoryPlanBtnPreview:
		"lowValue:inventoryManage:inventoryPlan:btn:preview", // 查看权限
	lowValueInventoryPlanBtnEdit:
		"lowValue:inventoryManage:inventoryPlan:btn:edit", // 编辑权限
	lowValueInventoryPlanBtnDrop:
		"lowValue:inventoryManage:inventoryPlan:btn:drop", // 删除权限

	// 低值 - 盘点管理 - 盘点任务
	lowValueInventoryTaskBtnPreview:
		"lowValue:inventoryManage:inventoryTask:btn:preview", // 查看权限
	lowValueInventoryTaskBtnEdit:
		"lowValue:inventoryManage:inventoryTask:btn:edit", // 编辑权限
	lowValueInventoryTaskBtnStart:
		"lowValue:inventoryManage:inventoryTask:btn:start", // 开始盘点
	lowValueInventoryTaskBtnFinish:
		"lowValue:inventoryManage:inventoryTask:btn:finish", // 结束盘点
	lowValueInventoryTaskBtnUser:
		"lowValue:inventoryManage:inventoryTask:btn:user", // 指定盘点人员
	lowValueInventoryTaskBtnRelease:
		"lowValue:inventoryManage:inventoryTask:btn:release", // 任务下发
	lowValueInventoryTaskBtnReport:
		"lowValue:inventoryManage:inventoryTask:btn:report", // 结束盘点

	// 低值 - 盘点管理 - 盘点报告
	lowValueInventoryReportBtnPreview:
		"lowValue:inventoryManage:inventoryReport:btn:preview", // 查看权限
	lowValueInventoryReportBtnEdit:
		"lowValue:inventoryManage:inventoryReport:btn:edit", // 编辑权限

	//配置 - 专家管理
	expertManagementBtnImport: "expert:management:btn:import", // 导入权限
	expertManagementBtnPreview: "expert:management:btn:preview", // 预览权限

	//配置 - 图纸资料

	//配置 - 图纸资料
	//配置 - GIS信息
	gisInformationBtnImport: "gis:information:btn:import", // 导入权限
	gisInformationBtnPreview: "gis:information:btn:preview", // 预览权限
	//配置 - 应急物资
	//配置 - 抢险车辆

	//流程 - 流程编制
	processCompilationBtnImport: "process:compilation:btn:import", // 导入权限
	processCompilationBtnCreate: "process:compilation:btn:add", // 新增权限
	processCompilationBtnEdit: "process:compilation:btn:edit", // 编辑权限
	processCompilationBtnDrop: "process:compilation:btn:drop", // 删除权限
}
