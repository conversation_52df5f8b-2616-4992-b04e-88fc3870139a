<template>
	<div class="drawer-container">
		<div class="drawer-left">
			<div class="drawer-left-form">
				<PitayaTree
					:treeData="TreeData"
					:treeProps="TreeProp"
					:needCheckBox="true"
					:checkStrictly="true"
					:needSingleSelect="!multiSelect"
					v-model:treeBizId="treeBizId"
					:defaultExpandedKeys="defaultExpandedKeys"
					ref="pitayaTreeRef"
					nodeKey="value"
					@onTreeClick="treeNodeClick"
					@onTreeChange="treeChangeClick"
				/>
			</div>
		</div>
	</div>
</template>
<script lang="ts" setup>
import { DictApi } from "@/app/baseline/api/dict"
import { ref, toRef, nextTick, watch } from "vue"
import { useDictInit } from "./dictBase"
/**
 * props*/
interface Props {
	multiSelect?: boolean
	drawerState?: boolean
	type?: string
	checkedKeys?: string[]
	selectId?: number
}
const props = defineProps<Props>()
const treeBizId = ref()
const drawerState = toRef(props, "drawerState")
const multiSelect = toRef(props, "multiSelect")
const emits = defineEmits(["onTreeClick"])

const { dictOptions, getDictByCodeList } = useDictInit()

/**
 * 基本数据列表
 * */
const TreeData = ref<any[]>([])
const pitayaTreeRef = ref<any>()

const treeList = ref<any[]>([]) //保存用户当前点击树信息
const treeListData = ref("") //保存用户当前点击树信息
const checkedId = ref<any[]>([]) //保存选中的数据
// 默认展开
const defaultExpandedKeys = ref<any[]>([])
/**
 * 对象列表
 * */
// 部门树ref
const TreeProp = {
	children: "children",
	label: "label"
}

/**
 *  函数
 */
const getTreeData = async () => {
	TreeData.value = []

	await getDictByCodeList(["MATERIAL_NATURE"])
	TreeData.value = dictOptions.value["MATERIAL_NATURE"]

	nextTick(() => {
		if (checkedId.value) {
			pitayaTreeRef!.value.setCheckedKeys(checkedId.value)
			pitayaTreeRef!.value.PitayaTreeRef.setCurrentKey(checkedId.value[0])
		}
	})

	/* DictApi.getMaterialAttribute().then((res: any) => {
		const allData = [{ id: 31, value: "0", label: "物资性质", children: res }]
		TreeData.value = allData
		// 取消设置默认展开节点
		defaultExpandedKeys.value = ["0","1","2"]
		nextTick(() => {
			if (checkedId.value) {
				pitayaTreeRef!.value.setCheckedKeys(checkedId.value)
				pitayaTreeRef!.value.PitayaTreeRef.setCurrentKey(checkedId.value[0])
			}
		})
	}) */
}
const treeChangeClick = (selected: any, checked: boolean) => {
	emits("onTreeClick", pitayaTreeRef!.value.PitayaTreeRef.getCheckedNodes()[0])
}
// 树点击
const treeNodeClick = (data: any, treeData: any) => {
	treeList.value = data
	treeListData.value = treeData

	emits("onTreeClick", pitayaTreeRef!.value.PitayaTreeRef.getCheckedNodes()[0])
}
/**
 * Watch
 * */
watch(
	[() => drawerState.value],
	(newVal: any[]) => {
		if (newVal) {
			getTreeData()
		}
	},
	{
		immediate: true
	}
)
</script>

<style lang="scss" scoped>
.drawer-container {
	display: flex;
	align-items: center;
	position: relative;
	height: calc(100vh - 20px);
	padding: 0;
	overflow: hidden;

	.drawer-left {
		position: relative;
		box-sizing: border-box;
		width: 100%;
		height: 100%;
	}
}
</style>
