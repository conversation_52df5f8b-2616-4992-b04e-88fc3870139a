<template>
	<div>
		<template v-for="(item, index) in options">
			<template v-if="values.includes(item.value)">
				<span
					v-if="
						!item.hasOwnProperty('raw') ||
						'default' == item.raw.class ||
						item.raw.class == ''
					"
					:key="item.value"
					:index="index"
					>{{ item.label }}</span
				>
				<el-tag
					v-else
					:disable-transitions="true"
					:key="item?.value"
					:index="index"
					:type="item.raw.class == 'primary' ? '' : item.raw.class"
					:class="item.raw.cssClass"
				>
					{{ item.label }}
				</el-tag>
			</template>
		</template>
		<template v-if="hasNoMatchingItems">---</template>
	</div>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from "vue"

const props = defineProps({
	options: {
		type: Array as () => Array<any>,
		default: null
	},
	value: {
		type: [Number, String, Array] as unknown as unknown as unknown as () =>
			| Number
			| String
			| Array<any>,
		default: null
	}
})

const values = ref<Array<string>>([])
const hasNoMatchingItems = ref(false)

watch(
	() => props.options,
	(newOptions) => {
		if (newOptions && Array.isArray(newOptions)) {
			values.value =
				props.value !== null && typeof props.value !== "undefined"
					? Array.isArray(props.value)
						? props.value.map(String)
						: [String(props.value)]
					: []
			hasNoMatchingItems.value = !newOptions.some((item) =>
				values.value.includes(item.value)
			)
		}
	},
	{ immediate: true }
)
watch(
	() => props.value,
	(newValue) => {
		if (Array.isArray(newValue)) {
			values.value = newValue.map(String)
		} else {
			values.value = [String(newValue)]
		}
		hasNoMatchingItems.value = !props.options.some((item) =>
			values.value.includes(item.value)
		)
	}
)
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.el-tag + .el-tag {
	margin-left: 10px;
}
.el-tag {
	color: $---color-info;
	border-color: $---color-info;
	background-color: $---color-info-light;
}
.el-tag--primary-deep {
	color: $---color-info2;
	border-color: $---color-info2;
	background-color: $---color-info2-light;
}
.el-tag--success {
	color: $---color-success;
	border-color: $---color-success;
	background-color: $---color-success-light;
}
.el-tag--success-less {
	color: $---color-success2;
	border-color: $---color-success2;
	background-color: $---color-success2-light;
}
.el-tag--info {
	color: $---font-color-3;
	border-color: $---color-useless;
	background-color: $---color-useless-light;
}
.el-tag--warning {
	color: $---color-warn;
	border-color: $---color-warn;
	background-color: $---color-warn-light;
}
.el-tag--danger {
	color: $---color-error;
	border-color: $---color-error;
	background-color: $---color-error-light;
}
.el-tag--danger-less {
	color: $---color-error2;
	border-color: $---color-error2;
	background-color: $---color-error2-light;
}

.el-tag--purple {
	color: $---color-purple;
	border-color: $---color-purple;
	background-color: $---color-purple-light;
}

.warning_bg {
	color: $---font-color-0;
	border-color: $---color-warn;
	background-color: $---color-warn;
}
.success_bg {
	color: $---font-color-0;
	border-color: $---color-success4;
	background-color: $---color-success4;
}
.primary_bg {
	color: $---font-color-0;
	border-color: $---color-info3;
	background-color: $---color-info3;
}
.successLess_bg {
	color: $---font-color-0;
	border-color: $---font-color-5;
	background-color: $---font-color-5;
}
</style>
