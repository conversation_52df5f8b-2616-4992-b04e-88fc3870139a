<template>
	<div class="el-input el-input-group el-input-group--append">
		<el-input-number
			class="el-input__wrapper"
			v-bind="$attrs"
			v-model="innerValue"
			@input="handleInput"
			:controls="false"
			:precision="precision"
		/>
		<div v-if="hasAppendSlot" class="el-input-group__append">
			<slot name="append" />
		</div>
	</div>
</template>

<script lang="ts">
import { defineComponent, ref, Ref, watch } from "vue"

export default defineComponent({
	name: "CustomInputNumber",
	props: {
		modelValue: {
			type: Number,
			default: null
		},
		precision: {
      type: Number as () => number, // 添加类型注解
			default: 0,
      requeired:false
		}
	},
	data(){
		return {
			hasAppendSlot	 : false
		}
	},
	emits: ["update:modelValue"],
	setup(props, { emit }) {
		const innerValue: Ref<number> = ref(props.modelValue)

		watch(
			() => props.modelValue,
			(value) => {
				innerValue.value = value
			}
		)

		const handleInput = (value: number) => {
			innerValue.value =  value ;
      emit( "update:modelValue",  value  )
      // innerValue.value = parseFloat( value );
			// emit( "update:modelValue", parseFloat( value ) )
		}

		return {
			innerValue,
			handleInput
		}
	},
	mounted() {
		this.hasAppendSlot	= this.$slots?.append;
	}
})
</script>
<style lang="scss" scoped>
:deep(.el-input-number) {
	width: 100% !important;
	padding: 0px;
}
:deep(.el-input-number.is-without-controls .el-input__wrapper) {
	padding-left: 10px;
	padding-right: 10px;
}
</style>
