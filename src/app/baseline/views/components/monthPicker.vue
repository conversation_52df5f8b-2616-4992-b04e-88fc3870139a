<template>
	<div class="month-picker">
		<div class="month-picker-panel">
			<div class="month-picker-header">
				<button class="month-picker-btn disabled" @click="decreaseYear">
					<font-awesome-icon icon="fa-angles-left" />
				</button>
				<span class="month-picker-year">{{ currentYear }}年</span>
				<button class="month-picker-btn disabled" @click="increaseYear">
					<font-awesome-icon icon="fa-angles-right" />
				</button>
			</div>
			<div class="month-picker-body">
				<div
					v-for="(month, index) in months"
					:key="index"
					:class="{
						disabled: !isSelectedMonth(index),
						'month-picker-month': true
					}"
					@click="selectMonth(index)"
				>
					<span :class="{ selected: isSelectedMonth(index) }">{{ month }}</span>
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts">
import { defineComponent, ref, computed } from "vue"

export default defineComponent({
	name: "MonthPicker",
	props: {
		value: {
			type: Object,
			required: true
		}
	},
	emits: ["update:value"],
	setup(props, { emit }) {
		const currentYear = ref(props.value.year ? props.value.year : new Date().getFullYear())
		const currentMonth = ref(props.value.month ? props.value.month : new Date().getMonth() + 1)

		const months = [
			"一月",
			"二月",
			"三月",
			"四月",
			"五月",
			"六月",
			"七月",
			"八月",
			"九月",
			"十月",
			"十一月",
			"十二月"
		]

		const increaseYear = () => {
			currentYear.value++
			emit("update:value", {
				year: currentYear.value,
				month: currentMonth.value
			})
		}

		const decreaseYear = () => {
			currentYear.value--
			emit("update:value", {
				year: currentYear.value,
				month: currentMonth.value
			})
		}

		const selectMonth = (index: number) => {
			currentMonth.value = index + 1
			emit("update:value", {
				year: currentYear.value,
				month: currentMonth.value
			})
		}

		const isSelectedMonth = (index: number) => {
			return index === currentMonth.value - 1
		}

		return {
			currentYear,
			currentMonth,
			months,
			increaseYear,
			decreaseYear,
			selectMonth,
			isSelectedMonth
		}
	}
})
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.month-picker {
	display: inline-block;
	width: 100%;
}

.month-picker-panel {
	width: 100%;
	padding: 10px;
	border: 1px solid $---border-color2;
	border-radius: 4px;
	//box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
	font-size: $---font-size-m;
	color: #606266;
}

.month-picker-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: $---spacing-m;
	padding-bottom: $---spacing-m;
	border-bottom: solid 1px $---border-color2;
	font-size: $---font-size-l;
}

.month-picker-btn {
	background-color: transparent;
	border: none;
	color: $---font-color-2;
	cursor: pointer;
}

.month-picker-year {
	color: $---font-color-1;
	font-weight: bold;
}

.month-picker-body {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: $---spacing-m;
}

.month-picker-month {
	padding: 6px;
	text-align: center;

	span {
		color: $---font-color-2;
		cursor: pointer;
		border-radius: $---border-radius-l;
		padding: $---spacing-s $---spacing-l;
		&:hover {
			color: $---color-info2;
		}

		&.selected {
			font-weight: bold;
			background: $---color-info;
			color: #ffffff;
		}
	}
}

@media (max-width: 600px) {
	.month-picker-body {
		grid-template-columns: repeat(2, 1fr);
	}
}
</style>
