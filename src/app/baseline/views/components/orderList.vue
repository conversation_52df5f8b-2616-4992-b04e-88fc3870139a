<script lang="ts" setup>
import { defineProps, toValue, withDefaults, onMounted } from "vue"
import { commonBoolan, IInvoiceColor } from "@/app/baseline/api/dict"
import { useTbInit } from "@/app/baseline/views/components/tableBase"
import { PurchaseInvoiceOrderApi } from "@/app/baseline/api/purchase/purchaseInvoiceOrder"
import CostTag from "@/app/baseline/views/components/costTag.vue"
import { maxTableHeight } from "@/app/baseline/utils/index"

interface Props {
	invoiceId: string | number //当前发票ID
	needSelected: boolean
	singleSelect: boolean
	invoiceColor: string
	blueInvoiceId: number
}

const props = withDefaults(defineProps<Props>(), {
	needSelected: true,
	singleSelect: false
})
const emits = defineEmits(["onClosed", "onSelected"])

/**********************初始化table *********************************/
const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	currentPage,
	pageTotal,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = useTbInit()

tableProp.value = [
	{ label: "采购订单号", prop: "code", width: 180 },
	{ label: "采购订单名称", prop: "label", minWidth: 200 },
	{ label: "采购计划号", prop: "planPurchaseCode", width: 200 },
	{ label: "采购项目编号", prop: "projectCode", width: 130 },
	{ label: "采购项目名称", prop: "contractLabel", width: 130 },
	{
		label: "订货金额",
		prop: "orderingAmount",
		needSlot: true,
		width: 130,
		align: "right"
	}
]
fetchFunc.value = () => {
	if (props.invoiceColor === IInvoiceColor.red) {
		return PurchaseInvoiceOrderApi.getPurchaseInvoiceOrderList({
			redInvoiceId: props.invoiceId,
			blueInvoiceId: props.blueInvoiceId
		} as any)
	} else {
		return PurchaseInvoiceOrderApi.getPurchaseInvoiceOrderListCanAdd({
			id: props.invoiceId as any,
			confirmStatus: commonBoolan.true
		} as any)
	}
}

const getTableData = (data?: any) => {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		...data
	}
	fetchTableData()
}
/**********************初始化table *********************************/

const formBtnList = computed(() => [
	{ name: "取消", icon: ["fas", "circle-minus"] },
	{
		name: "保存",
		icon: ["fas", "file-signature"],
		disabled: selectedTableList.value.length > 0 ? false : true
	}
])

function onFormBtnList(btnName?: string) {
	if (btnName == "保存") {
		emits("onSelected", toValue(selectedTableList))
	} else emits("onClosed")
}

onMounted(() => {
	getTableData()
})

defineOptions({
	name: "Order"
})
</script>
<template>
	<div class="common-order-list">
		<PitayaTable
			ref="tableRef"
			:columns="tableProp"
			:tableData="tableData"
			:total="pageTotal"
			:single-select="props.singleSelect"
			:need-selection="props.needSelected"
			:need-index="true"
			:max-height="maxTableHeight"
			@onSelectionChange="selectedTableList = $event"
			@on-current-page-change="onCurrentPageChange"
			:table-loading="tableLoading"
		>
			<template #orderingAmount="{ rowData }">
				<cost-tag :value="rowData.orderingAmount" />
			</template>
		</PitayaTable>
		<ButtonList
			v-if="formBtnList.length > 0"
			class="footer"
			:button="formBtnList"
			@on-btn-click="onFormBtnList"
		/>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.common-order-list {
	height: calc(100% - 30px);
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}
.pitaya-table {
}
.footer {
	padding-top: $---spacing-m;
	border-top: solid 1px $---border-color;
	justify-content: flex-end;
}
</style>
