<script setup lang="ts">
import {ref,onMounted,defineProps} from "vue";
import { usePagination } from "@/app/platform/hooks/usePagination"
import { DictApi } from "@/app/baseline/api/dict"
import { ElMessage } from "element-plus"

import { useTbInit } from "@/app/baseline/views/components/tableBase.ts";
import { NeedApi } from "@/app/baseline/api/plan/need";
import MatList from "@/app/baseline/views/plan/components/matManualList.vue"
import costTag from "@/app/baseline/views/components/costTag.vue"


export interface tableBtn {
	label							?: string,
	icon							?: string,
	confirm						?: boolean,		//操作提示，如果该参数不为空则自动弹出确认对话框
	disabled					?: Function,	//根据函数的返回结果确认是否禁用按钮
	[propName:string]  : any
}

export interface Props {
	needIndex				: boolean,
	needPagination	: boolean,
	needSelect			: boolean,
	singleSelect		: boolean,
	tableProp				: any[],
	fetchFunction		: Function,
	optionBtns		?: tableBtn[]
}
const props 						= withDefaults(defineProps<Props>(), {
	fetchFunction	: () => {},
	optionBtns		: undefined,
	needIndex		: true,
	needPagination: true,
	needSelect		: true,
	singleSelect	: false,
})
const emits 						= defineEmits(["onDataSelectChange", "onDataChanged", "onBtnClick"])

/*-------------------初始化表格-start-------------------*/
const {
	tableProp, tableData, tableRef, tableLoading, tbBtnLoading,
	pageSize, currentPage, pageTotal, selectedTableList, fetchParam, tbBtns,
	fetchTableData, fetchFunc, onCurrentPageChange, onDataSelected, onBtnClick
} = useTbInit();
tableProp.value					= props.tableProp;
onDataSelected.value		= (rowList: {[propName:string]:any}[]) => {
	selectedTableList.value = rowList;
	emits( 'onDataSelectChange', selectedTableList );
}

fetchFunc.value					= props.fetchFunction;
tbBtns.value						= props.optionBtns;
function onTablePageChange( pageData: any ) : Promise<any>{
	return new Promise( resolve => {
		onCurrentPageChange( pageData )
			.then( _tableData => {
				emits( 'onDataChanged', _tableData );
			})
	} )
}
/*-------------------初始化表格-end-------------------*/


/*-------------------加载配置-start-------------------*/
const dictOptions = ref<Record<string, any[]>>({
	INVENTORY_UNIT: []
})
function getDictByCodeList(): Promise<null> {
	return new Promise((resolve) => {
		DictApi.getDictByCodeList(dictOptions)
			.then((res) => {
				dictOptions.value = res;
			})
			.finally(() => {
				resolve( null )
			})
	})
}
/*-------------------加载配置-end-------------------*/

const queryArrList			= [
	{
		name: "",
		key: "label",
		placeholder: "请输入物资编码、物资名称",
		enableFuzzy: false,
		type: "input"
	}
]

function getTableData( data : {[propName:string]:any} ){
	currentPage.value	= 1;
	fetchParam.value	= data;
	fetchTableData()
		.then( tableData => {
			emits( 'onDataChanged', tableData );
		})
}

function clearSelected(){
	tableRef.value?.clearSelectedTableData();
}

function onRowBtnClick( btnCfg : tableBtn, rowData ){
	if( btnCfg.confirm ){
		ElMessageBox.confirm( btnCfg.confirm, "提示", {
			confirmButtonText: "确认",
			cancelButtonText: "取消",
			autofocus: false
		}).then(() => {
			emits( 'onBtnClick', rowData, btnCfg );
		})
	}else
		emits( 'onBtnClick', rowData, btnCfg );
}

onMounted(() => {
	getDictByCodeList().then( () => {
		fetchTableData();
	})
})

defineExpose({
	clearSelected,
	fetchTableData
})

</script>
<template>
	<div class="mat-list-table">
		<PitayaTable
			ref="tableRef"
			:columns="tableProp"
			:table-data="tableData"
			:need-index="true"
			:need-pagination="props.needPagination"
			:single-select="props.singleSelect"
			:need-selection="props.needSelect"
			:total="pageTotal"
			@onSelectionChange="onDataSelected"
			@on-current-page-change="onCurrentPageChange"
			:table-loading="tableLoading"
		>
			<template #buyUnit="{ rowData }">
				{{dictOptions.INVENTORY_UNIT.find( _c => _c.value == rowData.buyUnit )?.label || '---'}}
			</template>
			<template #evaluation="{ rowData }">
				<cost-tag :value="rowData.evaluation" />
			</template>
			<template #operations="{ rowData }">
				<el-button v-btn link v-for="btnCfg in optionBtns"
									 :disabled="btnCfg.disabled ? btnCfg.disabled( rowData, selectedTableList ) : false"
									 @click="onRowBtnClick( btnCfg, rowData )"
				>
					<font-awesome-icon v-if="btnCfg?.icon" :icon="['fas', btnCfg.icon]"/>
					<span class="table-inner-btn">{{ btnCfg?.label }}</span>
				</el-button>
			</template>
			<template #footerOperateLeft>
				<slot name="footerOperateLeft" />
			</template>
		</PitayaTable>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
