<script setup lang="ts">
const props = defineProps({
	value: {
		type: [Number, String] as unknown as unknown as unknown as () =>
			| Number
			| String,
		default: null
	}
})
const emits = defineEmits(["onClick"])

const handleClick = () => {
	emits("onClick")
}
</script>

<template>
	<div v-if="props.value && props.value != '0.0000'">
		<el-link
			type="primary"
			class="underline-link"
			href="javascript:;"
			@click.prevent="handleClick()"
		>
			{{ props.value }}
		</el-link>
	</div>
	<div v-else-if="props.value == '0' || props.value == '0.0000'">
		{{ props.value }}
	</div>
	<div v-else>---</div>
</template>

<style scoped lang="scss">
.underline-link {
	text-decoration: none;
	border-bottom: 1px solid;
	display: inline-block;
	transition: border-bottom-color 0.2s;
	font-size: 12px;
}

.underline-link:hover {
	border-bottom-color: transparent;
}
</style>
