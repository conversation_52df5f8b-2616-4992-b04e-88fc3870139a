<!-- 我审批的 -->
<script setup lang="ts">
import { ref, onMounted, defineProps, reactive, nextTick } from "vue"
import ApprovedExtend from "@/app/platform/views/examineApprove/ApprovedExtend.vue"
import DynamicCompont from "@/app/platform/views/examineApprove/dynamicCompont.vue"
import { dealwithComplete } from "@/app/platform/api/examineApproved"
import { modalSize } from "../../utils/layout-config"

interface props {
	taskValue: { [propName: string]: any }
	mod: string
	passCallBack: PromiseFulfilledResult<any> | undefined
}
const props = withDefaults(defineProps<props>(), {
	taskValue: undefined,
	id: undefined,
	mod: "view",
	passCallBack: undefined
})

const row = reactive(props.taskValue)
const emits = defineEmits(["onSaveOrClose"])
const refApprovedExtend = ref()
const formComponent = ref<any>()
const drawerApproved = ref<any>({
	size: modalSize.xxl,
	title: {
		name: ["审批表单"],
		icon: ["fas", "square-share-nodes"]
	},
	state: false,
	formType: "myApproved",
	// 审批扩展信息展示否
	showExpend: false,
	// 被审批信息的id
	id: undefined,
	taskId: undefined,
	processInstanceId: undefined,
	processInstanceKey: undefined,
	processDefinitionId: undefined
})
// 流程编码对应的组件
const codeContrast = {
	//material_code: "materialCodeApproved",
	material_code: `/src/app/baseline/views/processForm/materialCode.vue`,
	need_plan: `/src/app/baseline/views/processForm/needPlan.vue`,
	temp_plan: `/src/app/baseline/views/processForm/needPlan.vue`,
	purchase_plan: `/src/app/baseline/views/processForm/purchasePlan.vue`,
	merge_plan: `/src/app/baseline/views/processForm/mergePlan.vue`,
	/* allocation_unspanned_apply:
		"/src/app/baseline/views/processForm/transferApplyDetail.vue",
	allocation_spanned_apply:
		"/src/app/baseline/views/processForm/transferApplyDetail.vue" */
	distribution: "/src/app/baseline/views/processForm/distribution.vue", // 采购 - 分包
	order_verify: "/src/app/baseline/views/processForm/orderVerify.vue", // 采购- 订货计划
	purchase_order: "/src/app/baseline/views/processForm/purchaseOrder.vue", // 采购 - 采购订单
	purchase_contract: "/src/app/baseline/views/processForm/purchaseContract.vue", // 采购- 采购合同
	invoice: "/src/app/baseline/views/processForm/invoice.vue", // 采购 - 发标管理
	other: "/src/app/baseline/views/processForm/otherApplyDetail.vue", // 库存 - 其他入库
	pick_apply: "/src/app/baseline/views/processForm/matGetApplyDetail.vue", // 库存 - 领料申请
	return_apply:
		"/src/app/baseline/views/processForm/warehouseReturnApplyDetail.vue", // 库存 - 退库申请
	remove_apply: "/src/app/baseline/views/processForm/goodsReturnDetail.vue", // 库存 - 退货申请
	allocation_unspanned_apply:
		"/src/app/baseline/views/processForm/transferApplyDetail.vue", // 库存 - 调拨申请 - 不跨成本中心
	allocation_spanned_apply:
		"/src/app/baseline/views/processForm/transferApplyDetail.vue", // 库存 - 调拨申请 - 跨成本中心
	check_plan: "/src/app/baseline/views/processForm/inventoryPlanDetail.vue", // 库存 - 盘点计划
	check_plan_upload:
		"/src/app/baseline/views/processForm/inventoryPlanDetail.vue", // 库存 - 盘点计划差异处理
	waste_old_apply:
		"/src/app/baseline/views/processForm/handoverApplyDetail.vue", // 报废 - 交旧申请
	waste_repair_apply:
		"/src/app/baseline/views/processForm/wasteRepairApply.vue", // 报废 - 返修申请
	waste_scrap_apply: "/src/app/baseline/views/processForm/scrapApplyDetail.vue", // 报废 - 报废申请
	waste_scrap_disposal_apply:
		"/src/app/baseline/views/processForm/scrapDisposalApplyDetail.vue", // 报废 - 报废处置
	low_value_apply:
		"/src/app/baseline/views/processForm/requisitionApplyDetail.vue", // 低值 - 领用申请
	low_value_return_apply:
		"/src/app/baseline/views/processForm/lowValueReturnApplyDetail.vue", // 低值 - 归还申请
	low_value_check:
		"/src/app/baseline/views/processForm/lowValueInventoryPlanDetail.vue" // 低值 - 盘点计划
}
onMounted(() => {
	//	drawerApproved.value.id = row.id
	drawerApproved.value.taskId = row.taskId
	drawerApproved.value.processInstanceId = row.processInstanceId
	drawerApproved.value.processInstanceKey = row.taskDefinitionKey
	drawerApproved.value.processDefinitionId = row.processDefinitionId
	drawerApproved.value.businessId = row.businessId
	drawerApproved.value.formData = {}
	const keys =
		row.processDefinitionId.split(":").length > 1
			? row.processDefinitionId.split(":")[0]
			: row.camundaKey
	//获取最后一段
	const lastIndex = keys.lastIndexOf("_")
	const key = keys.substring(0, lastIndex)
	drawerApproved.value.formData.formType = codeContrast[key]
	console.log(key)
	drawerApproved.value.state = true
	drawerApproved.value.showExpend = true
	drawerApproved.value.formData.businessId = row.businessId
	drawerApproved.value.formData.isMySatrt = props.mod === "view"
	//row.formData.formType：组建名称
	nextTick(() => {
		refApprovedExtend.value.getTaskList()
		console.log(drawerApproved.value)
		formComponent.value.loadComponent(drawerApproved.value.formData.formType)
	})
})
const handleComplete = (inputForm: { input: any; result: any }) => {
	drawerApproved.value.state = false
	dealwithComplete({
		taskId: drawerApproved.value.taskId,
		comment: inputForm.input,
		agree: inputForm.result
	}).then((res: any) => {
		if (res) {
			ElMessage({
				message: "操作成功",
				type: "success"
			})
			emits("onSaveOrClose", "save")
		}
	})
}
const handlePass = (inputForm: { input: any; result: any }) => {
	handleComplete(inputForm)
	// alert(props.passCallBack)
	//  props.passCallBack().then((res: any) => {
	//    alert(res)
	//    //handleComplete(inputForm)
	//  })
}
const greet = ref()
const updateGreet = (fun: any) => {
	greet.value = fun //fun:Promise
}
provide("greet", { greet, updateGreet })
</script>
<template>
	<div class="drawer-box">
		<div class="drawer-box-left" style="padding-right: 0">
			<!-- <Title :title="drawerApproved.title" /> -->
			<!-- <el-scrollbar class="drawer-content"> -->
			<DynamicCompont
				:form-value="drawerApproved"
				:drawerApproved="drawerApproved"
				ref="formComponent"
			/>
			<!-- </el-scrollbar> -->
		</div>
		<div class="drawer-box-right">
			<ApprovedExtend
				:isMySatrt="props.mod === 'view'"
				:taskId="drawerApproved.taskId"
				@on-pass="handlePass"
				@on-reject="handleComplete"
				v-if="drawerApproved.showExpend"
				ref="refApprovedExtend"
				:processInstanceId="drawerApproved.processInstanceId"
				:processDefinitionId="drawerApproved.processDefinitionId"
				:type="drawerApproved.formType"
			/>
		</div>
	</div>
</template>

<style scoped lang="scss">
@import url("../../../../styles/common-from-wrapper.scss");

.drawer-box {
	height: 100%;
	display: flex;

	.drawer-box-left {
		display: flex;
		flex-direction: column;
		height: 100%;
		width: calc(100% - 400px);
		padding-right: 10px;
		.drawer-content {
			flex: 1;
		}
	}
	.drawer-box-right {
		width: 400px;
		margin: -10px 0;
		padding: 10px 0 10px 10px;
		border-left: 1px solid #ccc;
	}
}
</style>
