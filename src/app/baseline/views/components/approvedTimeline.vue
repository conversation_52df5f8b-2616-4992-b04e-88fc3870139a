<script lang="ts" setup>
import {defineProps} from 'vue';
import { Clock, MoreFilled, Timer  } from "@element-plus/icons-vue"
import {ApprovedTasks} from "../material/components/define";

/*通用变量定义*/
interface props{
	datalist : ApprovedTasks[]
}

const props = withDefaults(defineProps<props>(), {
	datalist : undefined
})


defineExpose({} );
</script>

<template>
	<div class="content approved-timeline fix" >
		<el-scrollbar>
			<el-timeline class="customer">
				<el-timeline-item
					v-for="step in props.datalist"
					:icon="Clock"
					class="status"
				>
					<div class="title">{{step.time}}</div>
					<div class="title">{{step.label}}</div>
					<div class="name">{{step.name}}:</div>
					<div class="content">{{step.content}}</div>
				</el-timeline-item>
			</el-timeline>
		</el-scrollbar>
	</div>
</template>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.title{
	color:$---color-warn;
	font-size:$---font-size-m;
}
.name{
	color:$---color-error;
	font-size:$---font-size-m;
}
.content{
	color:$---font-color-2;
	font-size:$---font-size-m;
}
:deep( .el-timeline-item__node ){
	background:$---color-warn;
}
</style>
