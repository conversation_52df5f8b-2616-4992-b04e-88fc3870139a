import { CustomMessageBox } from "@/app/platform/utils/message"
import { ElMessageBoxOptions } from "element-plus"
export function useMessageBoxInit() {
	function showDelConfirm(): Promise<null> {
		return new Promise((resolve, reject) => {
			CustomMessageBox({ message: "确定要移除吗？" }, (res: boolean) => {
				if (res) resolve(null)
				else reject()
			})
		})
	}

	function showAddConfirm(funcCallBack: Function) {
		CustomMessageBox(
			{ type: "warning", message: "您确认要保存本次选择的数据吗？" },
			(res: boolean) => {
				if (res) return funcCallBack()
			}
		)
	}

	function showInfoConfirm(message: string, opts?: ElMessageBoxOptions) {
		return new Promise((resolve, reject) => {
			CustomMessageBox(
				{
					type: "warning",
					message: message,
					...opts
				},
				(res: boolean) => {
					if (res) resolve(null)
					else reject()
				}
			)
		})
	}

	function showWarnConfirm(message: string, showCancel = true): Promise<null> {
		return new Promise((resolve, reject) => {
			CustomMessageBox(
				{ type: "warning", message: message, showCancelButton: showCancel },
				(res: boolean) => {
					if (res) resolve(null)
					else reject()
				}
			)
		})
	}

	function showErrorConfirm(message: string): Promise<null> {
		return new Promise((resolve, reject) => {
			CustomMessageBox(
				{ type: "error", message: message, showCancelButton: false },
				(res: boolean) => {
					if (res) resolve(null)
					else reject()
				}
			)
		})
	}

	return {
		showInfoConfirm,
		showDelConfirm,
		showAddConfirm,
		showWarnConfirm,
		showErrorConfirm
	}
}
