<template>
	<div class="drawer-container">
		<div class="drawer-column">
			<div class="rows">
				<Title :title="props.title ? props.title : treeTitle">
					<div class="tree-switch" v-if="props.needSwitch">
						<span>
							<el-switch
								size="small"
								v-model="hasCancel"
								:active-value="true"
								:inactive-value="false"
								active-color="#0a4e9a"
								:disabled="loading"
								@change="handleSwitchChange"
						/></span>
						<span class="tip">包含作废分类编码</span>
					</div>
				</Title>

				<PitayaLazyTree
					class="dep-tree"
					ref="pitayaTreeRef"
					:treeData="TreeData"
					:treeProps="TreeProp"
					:needCheckBox="props.needCheckBox"
					:expandOnClickNode="props.needCheckBox"
					:needSingleSelect="props.needSingleSelect"
					:treeBizId="treeBizId"
					:treeLoading="loading"
					:checkStrictly="props.needSingleSelect"
					:tree-props="{
								label: 'label',
								children: 'children'
							}"
					@nodeExpand="onLocTreeExpand"
					@onTreeClick="treeNodeClick"
					@onTreeChange="treeCheck"
					:onTreeSearch="FetchTreeData"
					:defaultExpandedKeys="defaultExpandedKeys"
				/>
			</div>
			<ButtonList
				:button="formBtnList"
				@onBtnClick="onBtnClick"
				class="footer"
				v-if="!readOnly"
			/>
		</div>
	</div>
</template>
<script lang="ts" setup>
import { MatTypeApi } from "@/app/baseline/api/material/matType"
import { nextTick, ref, toRef,onMounted } from "vue"
import XEUtils from "xe-utils"
import { matStatus } from "@/app/baseline/api/dict"

/**
 * props*/
interface Props {
	ifClick?:boolean // 判断是否初始化 click单击；控制第156行
	defId?: string
	title?: Object
	needSwitch?: boolean // 是否显示包含作废分类编码
	needCheckBox?: boolean
	defaultExpandedKeys?: any[]
	defaultCheckedKeys?: any[]
	currentNodeKey?: number | string //当前选中节点
	checkStrictly?: boolean // 是否严格遵守父子不关联
	needSingleSelect?: boolean // 是否需要单选
	readOnly?: boolean
	status?: string | string[]
	needLabelCount?: boolean
}

const treeTitle = {
	name: ["物资编码分类"],
	icon: ["fas", "square-share-nodes"]
}
const props = withDefaults(defineProps<Props>(), {
	ifClick:true,
	needCheckBox: true,
	needSwitch: true,
	defaultExpandedKeys: () => [0],
	defaultCheckedKeys: () => [],
	checkStrictly: false,
	needSingleSelect: false,
	readOnly: true,
	currentNodeKey: -1
})
const emits = defineEmits([
	"onTreeClick",
	"onTreeCheck",
	"update:treeBizId",
	"onScrollLoad",
	"onBtnClick"
])

const treeBizId = ref( "0" )
const readOnly = toRef(() => props.readOnly)
const defaultExpandedKeys = toRef(props, "defaultExpandedKeys")

/**
 * 基本数据列表
 * */
const TreeData = ref<any[]>([])
const pitayaTreeRef = ref<any>()

const treeList = ref<{[propName:string]:any}>([]) //保存用户当前点击树信息
const treeListData = ref("") //保存用户当前点击树信息
const checkedId = ref<any[]>([]) //保存选中的数据

const TreeProp = {
	children: "children",
	label: "fullLabel",
	expanded: "expanded"
}
const formBtnList = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "保存",
		icon: ["fas", "floppy-disk"]
	}
]
const hasCancel = ref(false)
const defaultStatus = [matStatus.normal, matStatus.freeze, matStatus.drafted]
const formModal = ref({
	fid: 0,
	status: props.status ? props.status : defaultStatus.join(",")
})
const loading = ref(false)
/**
 *  函数
 */
const getTreeData = () => {
	//root
	TreeData.value = [
		{ id: props.defId != undefined ? props.defId : 0, code: "", fullLabel: "物资全部分类", label: "物资全部分类", children: [] }
	]

	loading.value = true
	FetchChildData( TreeData.value[0], treeBizId.value ).then( _r => {
		_r.children.map( _subObj => {
			FetchChildData( _subObj, _subObj.id )
		})

		//选中节点并展开
		checkedId.value = [props.currentNodeKey]
		nextTick(() => {
			if (checkedId.value) {
				pitayaTreeRef!.value.setCheckedKeys(checkedId.value)
				pitayaTreeRef!.value.PitayaTreeRef.setCurrentKey(checkedId.value[0])
				if (props.ifClick){
					document.querySelector(".el-tree-node__content")?.click()
				}
				loading.value = false
			}
		})
	})
}

function FetchChildData( targetData, parentId : string ){
	return new Promise( (resolve, reject) => {
		MatTypeApi.getMatTypeChild( { ...formModal.value, fid : parentId } )
			.then( (res: any) => {
				XEUtils.eachTree(res, (item: any) => {
					item.fullLabel = item.code + " " + item.label
					if (props.needLabelCount && item.materialCount > 0) {
						item.fullLabel += "（" + item.materialCount + "）"
					}
				})

				const rootCount = res.reduce((count, item) => count + item.materialCount, 0)
				targetData.children = res
				if (props.needLabelCount && rootCount > 0) {
					targetData.fullLabel += "（" + rootCount + "）"
				}
				resolve( targetData );
			})
			.catch( () => reject() )
	})
}


const FetchTreeData = ( infilterText : string ) => {
	loading.value = true

	// 重置所选信息 
	checkedId.value = []
	formModal.value.fid = 0
	pitayaTreeRef!.value.setCheckedKeys(checkedId.value)


	if( infilterText && infilterText.length >= 0 ){
		MatTypeApi.getMatTypeTree( { label : `*${infilterText}*`, ...formModal.value } ).then((res: any) => {
			XEUtils.eachTree(res, (item: any) => {
				item.fullLabel = item.code + " " + item.label
				if (props.needLabelCount && item.materialCount > 0) {
					item.fullLabel += "（" + item.materialCount + "）"
				}
			})

			const rootCount = res.reduce((count, item) => count + item.materialCount, 0)
			TreeData.value[0].children = res
			if (props.needLabelCount && rootCount > 0) {
				TreeData.value[0].fullLabel += "（" + rootCount + "）"
			}
			loading.value = false;
		})
	}else{
		getTreeData();
	}
}

const resetTreeData = () => {
	let chidrenData = []
	MatTypeApi.getMatTypeTree(formModal.value).then((res: any) => {
		if (treeList.value.id == 0) {
			chidrenData = res
		} else {
			XEUtils.eachTree(res, (item: any) => {
				if (item.id == treeList.value.id) {
					chidrenData = item.children
				}
			})
		}
		XEUtils.eachTree(chidrenData, (item: any) => {
			item.fullLabel = item.code + " " + item.label
		})
		nextTick(() => {
			pitayaTreeRef!.value.PitayaTreeRef.updateKeyChildren(
				treeList.value.id,
				chidrenData
			)
			const node = pitayaTreeRef!.value.PitayaTreeRef.getNode(treeList.value.id)
			if (node) {
				node.expanded = true
			}
		})
	})
}

function onLocTreeExpand( data: any, node: any ){
	treeNodeClick( data, node );
	data?.children?.forEach( _subObj => {
		FetchChildData( _subObj, _subObj.id )
	})
}

// 树点击
const treeNodeClick = (data: any, treeData: any) => {
	treeBizId.value 		= data.id
	treeList.value 			= data
	treeListData.value 	= treeData
	if (props.needSingleSelect) {
		pitayaTreeRef!.value.setCheckedKeys([data.id])
	} else {
		// checkedId.value.push(data.id)
		// pitayaTreeRef!.value.setCheckedKeys(checkedId.value)
	}
	emits("onTreeClick", data, treeData, formModal.value.status)
}
const treeCheck = () => {
	if( checkedId.value.join() != pitayaTreeRef!.value.PitayaTreeRef.getCheckedKeys().join() ){
		checkedId.value = pitayaTreeRef!.value.PitayaTreeRef.getCheckedKeys()
		emits("onTreeCheck", checkedId.value, formModal.value.status)
	}
}
// 按钮点击
const onBtnClick = (btnName: string | undefined) => {
	if (btnName === "保存") {
		treeList.value = pitayaTreeRef.value.getCheckedNodes().pop()
		checkedId.value = pitayaTreeRef.value.getCheckedKeys()
		emits("onBtnClick", treeList.value)
		return
	}
	if (btnName === "取消") {
		checkedId.value = []
		emits("onBtnClick", "", "cancel")
		return
	}
}

const handleSwitchChange = () => {
	pitayaTreeRef.value.filterText = ''
	if (hasCancel.value) {
		//看作废的
		formModal.value.status += "," + matStatus.canceled
	} else {
		//取消作废的
		formModal.value.status = props.status
			? props.status
			: defaultStatus.join(",")
	}
	getTreeData()
}
onMounted(() => {
	getTreeData()
})
defineExpose({
	resetTreeData
})
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.drawer-column {
	width: 100%;
	padding-bottom: 10px;
}

.tree-switch {
	color: #666;
	font-size: 12px;
	padding-right: 10px;

	.tip {
		padding-left: 5px;
	}
  position: absolute;
  right: 0;
}
</style>
