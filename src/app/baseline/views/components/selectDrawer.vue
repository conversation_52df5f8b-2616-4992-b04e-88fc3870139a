<script lang="ts" setup>
import { useTbInit } from "./tableBase"
import { nextTick, toRef, ref, onMounted } from "vue"

interface Props {
	title: string //标题
	queryArrList?: querySetting[]
	tableProp: any[]
	tableApi: Function
	fetchParam?: any
	loading: boolean
	selected: any[]
	selectedKey: string
	needSingleSelect?: boolean
}
const props = withDefaults(defineProps<Props>(), {
	loading: false,
	title: "选择",
	selectedKey: "id",
	selected: undefined,
	needSingleSelect: false
})

const emits = defineEmits(["onClose", "onSave"])
const drawerLoading = toRef(props.loading)

const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	tbBtnLoading,
	pageSize,
	currentPage,
	pageTotal,
	selectedTableList,
	fetchParam,
	tbBtns,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange,
	onDataSelected,
	onBtnClick
} = useTbInit()
tableProp.value = props.tableProp
fetchFunc.value = props.tableApi
fetchParam.value = props.fetchParam
//table
onDataSelected.value = (rowList: anyKey[]) => {
	selectedTableList.value = rowList
}

const drawerTitle = {
	name: [props.title],
	icon: ["fas", "square-share-nodes"]
}
const formBtnList = [
	{ name: "取消", icon: ["fas", "circle-minus"] },
	{ name: "保存", icon: ["fas", "floppy-disk"] }
]

// 左侧按钮点击
const onFormBtnList = (btnName: string | undefined) => {
	if (btnName === "保存") {
		const filteredData = tableData.value.filter((data) => {
			return selectedTableList.value.some((selected) => selected.id === data.id)
		})
		emits("onSave", filteredData)
	}
	if (btnName === "取消") {
		emits("onClose")
	}
}
const selectedData = ref<anyKey>([])
selectedData.value =
	Array.isArray(props.selected) && props.selected.length > 0
		? props.selected.map((item) => ({ [props.selectedKey]: item }))
		: []
const loadResult = ref(false)
const getTableData = (data?: any) => {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		...data
	}
	loadResult.value = false
	fetchTableData()
	/* .then((res) => {
		selectedData.value = res.value.filter((obj) =>
			props.selected.includes(obj[props.selectedKey])
		)
		loadResult.value = true
	}) */
}

/**
 * 补丁
 * 监听tableData, 重组lastTableData
 */
watch([tableData], () => {
	selectedData.value = tableData.value.filter((obj: any) =>
		props.selected.includes(obj[props.selectedKey])
	)
	loadResult.value = true

	drawerLoading.value = false
})

onMounted(() => {
	getTableData()
})
defineOptions({
	name: "SelectTableDrawer"
})
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<!-- 右侧table区域 -->
		<div class="drawer-column">
			<div class="rows">
				<Title :title="drawerTitle" />
				<Query
					:queryArrList="queryArrList"
					@getQueryData="getTableData"
					class="custom-q"
					v-if="queryArrList && queryArrList.length > 0"
				/>
				<PitayaTable
					v-if="loadResult"
					ref="tableRef"
					:columns="tableProp"
					:tableData="tableData"
					:total="pageTotal"
					:single-select="needSingleSelect"
					:need-selection="true"
					:need-index="true"
					@onSelectionChange="onDataSelected"
					@on-current-page-change="onCurrentPageChange"
					:table-loading="drawerLoading"
					:selectedTableData="selectedData"
				>
					<template v-for="t in tableProp" #[t.prop]="{ rowData }">
						<slot :name="t.prop" :rowData="rowData" />
					</template>
				</PitayaTable>
			</div>
			<ButtonList
				class="footer"
				:button="formBtnList"
				:loading="drawerLoading"
				@on-btn-click="onFormBtnList"
			/>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index";
.drawer-container {
	.drawer-column {
		width: 100%;
	}
	.custom-q {
		margin: 10px 0px -10px 10px !important;
	}
}
</style>
