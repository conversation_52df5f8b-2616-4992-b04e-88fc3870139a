<!-- 计划 - 需求清单 主表 V1.0-->
<script lang="ts" setup>
import { ref, onMounted, defineProps } from "vue"
import { ElMessage } from "element-plus"
import {
	checkPermission,
	isCheckPermission
} from "@/app/platform/utils/permission"
import { DictApi, needListStatus } from "@/app/baseline/api/dict"
import { useTbInit } from "@/app/baseline/views/components/tableBase"
import { powerList } from "@/app/baseline/views/components/define.d"
import { BaseLineSysApi } from "@/app/baseline/api/system"
import { NeedApi } from "@/app/baseline/api/plan/need"
import { hasPermi } from "../../utils"
import { useDictInit } from "@/app/baseline/views/components/dictBase"

import NeedListDrawer from "./components/needListDrawer.vue"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import { useMessageBoxInit } from "@/app/baseline/views/components/messageBox"

const { showDelConfirm } = useMessageBoxInit()
const { dictOptions, getDictByCodeList } = useDictInit()

/*-------------------初始化表格-start-------------------*/
const tbBtnLoading = ref(false)
const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	currentPage,
	pageTotal,
	selectedTableList,
	fetchParam,
	tbBtns,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange,
	onDataSelected,
	onBtnClick
} = useTbInit()

tableProp.value = [
	{ label: "需求清单编号", prop: "code", width: 160 },
	{ label: "需求清单名称", prop: "label", minWidth: 150 },
	{ label: "物资编码数量", prop: "matCodeNum", width: 110 },
	{ label: "专业", prop: "majorId_view", width: 160 },
	{ label: "费用类别", prop: "expenseCategory_view", width: 100 },
	{ label: "公司", prop: "sysCommunityId_view", needSlot: false, width: 120 },
	{ label: "清单状态", prop: "status", needSlot: true, width: 85 },
	{ label: "创建人", prop: "createdBy_view", needSlot: false, width: 85 },
	{ label: "创建时间", prop: "createdDate", width: 160 },
	{
		label: "操作",
		width: 150,
		prop: "operations",
		fixed: "right",
		needSlot: true
	}
]
onDataSelected.value = (rowList: any) => {
	selectedTableList.value = rowList
	const isSel = rowList.length <= 0
	if (props.model == "edit") {
		tbBtns.value[0].disabled =
			rowList.some(
				(item: any) => item.status === needListStatus.started.toString()
			) || isSel
		tbBtns.value[1].disabled =
			rowList.some(
				(item: any) => item.status !== needListStatus.started.toString()
			) || isSel
	}
}
fetchFunc.value = NeedApi.getPlanNeedList

tbBtns.value = [
	{
		name: "启用",
		roles: powerList.needListBtnStart,
		icon: ["fas", "power-off"],
		disabled: true,
		click: () => handleStatus(needListStatus.started.toString())
	},
	{
		name: "停用",
		roles: powerList.needListBtnStop,
		icon: ["fas", "circle-stop"],
		disabled: true,
		click: () => handleStatus(needListStatus.stopped.toString())
	}
]
/*-------------------初始化表格-end-------------------*/

export interface Props {
	filters?: string //查询条件
	model?: string //显示模式  view : 查看, edit : 编辑
}
const props = withDefaults(defineProps<Props>(), {
	model: "edit"
})

const rightTitle = {
	name: ["需求清单"],
	icon: ["fas", "square-share-nodes"]
}
const addBtn = [
	{
		name: "新建需求清单",
		roles: powerList.needListBtnCreate,
		icon: ["fas", "square-plus"]
	}
]

/**
 * 查询条件 配置
 */
const queryArrList = ref([
	{
		name: "所属公司",
		key: "sysCommunityId",
		type: "treeSelect",
		treeApi: BaseLineSysApi.getCompanyAllList,
		placeholder: "请选择所属公司"
	},
	{
		name: "需求清单名称",
		key: "label",
		placeholder: "请输入需求清单名称",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "需求清单编号",
		key: "code",
		placeholder: "请输入需求清单编号",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "所属专业",
		key: "parentMajorId",
		type: "treeSelect",
		treeApi: BaseLineSysApi.getProfessionTree,
		placeholder: "请选择所属专业"
	},
	{
		name: "费用类别",
		key: "expenseCategory",
		type: "treeSelect",
		treeApi: () => Promise.resolve(dictOptions.value.COST_CATEGORY),
		replaceIdTo: "subitemValue",
		placeholder: "请选择费用类别"
	},
	{
		name: "清单状态",
		key: "status",
		placeholder: "请选择清单状态",
		enableFuzzy: false,
		type: "elTreeSelect",
		children: DictApi.getStpStatus()
	}
])
const currentRow = ref<{ [propName: string]: any }>({})
const detailModel = ref("view")
//drawer
const showDrawer = ref<boolean>(false)
const drawerSize = 1700

//搜索条件确认回调
function getQueryData(data: { [propName: string]: any }) {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = data
	fetchTableData()
}

/**
 * 启用/停用 操作
 * @param status
 */
function handleStatus(status: string) {
	if (selectedTableList.value?.length > 0) {
		const _id: string[] = []
		selectedTableList.value.map((_d) => _id.push(_d.id))

		tbBtnLoading.value = true
		NeedApi.updateNeedStatus(_id.join(","), status)
			.then(() => {
				fetchTableData()
			})
			.finally(() => {
				tbBtnLoading.value = false
			})
	}
}

function onRowEdit(row: any, model = "view") {
	detailModel.value = model
	currentRow.value = row
	showDrawer.value = true
}

const onRowDelete = (row: any) => {
	showDelConfirm().then(() => {
		NeedApi.deleteNeed(row.id).then(() => {
			ElMessage.success("移除成功")
			fetchTableData()
		})
	})
}

const onAddBtn = () => {
	currentRow.value = {}
	detailModel.value = "edit"
	showDrawer.value = true
}

// 弹窗关闭
const onCloseDrawer = () => {
	fetchTableData()
}

function onMatChange() {
	fetchTableData()
}

onMounted(() => {
	if (props.filters) {
		queryArrList.value = queryArrList.value.filter(
			(_q) => props.filters && props.filters.split(",").includes(_q.key)
		)
	}
	if (props.model == "view") tableProp.value.pop()
	Promise.all([getDictByCodeList(["COST_CATEGORY"])]).then(() => {
		fetchTableData()
	})
})

defineOptions({
	name: "MatApplyManagement"
})
</script>
<template>
	<div class="app-container">
		<div class="whole-frame">
			<ModelFrame class="top-frame">
				<Query :queryArrList="queryArrList" @getQueryData="getQueryData" />
			</ModelFrame>
			<ModelFrame class="bottom-frame">
				<Title :title="rightTitle" :button="addBtn" @onBtnClick="onAddBtn" />
				<PitayaTable
					ref="tableRef"
					:columns="tableProp"
					:table-data="tableData"
					:needSelection="true"
					:single-select="false"
					:need-index="true"
					:need-pagination="true"
					:total="pageTotal"
					@onSelectionChange="onDataSelected"
					@on-current-page-change="onCurrentPageChange"
					:table-loading="tableLoading"
				>
					<template #expenseCategory="{ rowData }">
						<dict-tag
							:options="DictApi.getFeeType()"
							:value="rowData.expenseCategory"
						/>
					</template>
					<template #status="{ rowData }">
						<dict-tag
							:options="DictApi.getStpStatus()"
							:value="rowData.status"
						/>
					</template>

					<template #operations="{ rowData }">
						<teamplate
							v-if="
								(rowData.status != needListStatus.drafted &&
									isCheckPermission(powerList.needListBtnPreview)) ||
								(rowData.status == needListStatus.drafted &&
									isCheckPermission(powerList.needListBtnEdit) &&
									hasPermi(rowData.createdBy)) ||
								(rowData.status == needListStatus.drafted &&
									isCheckPermission(powerList.needListBtnDrop) &&
									hasPermi(rowData.createdBy))
							"
						>
							<el-button
								v-btn
								link
								@click="onRowEdit(rowData)"
								:disabled="checkPermission(powerList.needListBtnPreview)"
								v-if="
									rowData.status != needListStatus.drafted &&
									isCheckPermission(powerList.needListBtnPreview)
								"
							>
								<font-awesome-icon :icon="['fas', 'eye']" />
								<span class="table-inner-btn">查看</span>
							</el-button>
							<el-button
								v-btn
								link
								@click="onRowEdit(rowData, 'edit')"
								:disabled="checkPermission(powerList.needListBtnEdit)"
								v-if="
									rowData.status == needListStatus.drafted &&
									isCheckPermission(powerList.needListBtnEdit) &&
									hasPermi(rowData.createdBy)
								"
							>
								<font-awesome-icon :icon="['fas', 'pen-to-square']" />
								<span class="table-inner-btn">编辑</span>
							</el-button>
							<el-button
								v-btn
								link
								@click="onRowDelete(rowData)"
								:disabled="checkPermission('system:company:btn:delete')"
								v-if="
									rowData.status == needListStatus.drafted &&
									isCheckPermission(powerList.needListBtnDrop) &&
									hasPermi(rowData.createdBy)
								"
							>
								<font-awesome-icon :icon="['fas', 'trash-can']" />
								<span class="table-inner-btn">移除</span>
							</el-button>
						</teamplate>
						<teamplate v-else>---</teamplate>
					</template>
					<template #footerOperateLeft>
						<ButtonList
							class="btn-list"
							:is-not-radius="true"
							:button="tbBtns"
							:loading="tbBtnLoading"
							v-if="props.model == 'edit'"
							@on-btn-click="onBtnClick"
						/>
					</template>
				</PitayaTable>
				<Drawer
					:size="drawerSize"
					v-model:drawer="showDrawer"
					:destroyOnClose="true"
				>
					<NeedListDrawer
						:id="currentRow.id"
						:model="detailModel"
						@onSuccess="onCloseDrawer"
						@onDataChange="onMatChange"
						@onClosed="showDrawer = false"
					/>
				</Drawer>
			</ModelFrame>
			<slot name="options" :data="selectedTableList" />
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
