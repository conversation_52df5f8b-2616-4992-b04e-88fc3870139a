<!-- 需求清单 新建/编辑页 V2.0-->
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<!-- 左侧表单区域 -->
		<div class="drawer-column left">
			<div class="rows">
				<Title :title="drawerLeftTitle" />
				<el-scrollbar>
					<el-form
						class="content"
						:model="formData"
						:rules="formRules"
						ref="formRef"
						label-position="top"
						label-width="100px"
					>
						<FormElement :form-element="formEl" :form-data="formData" />
					</el-form>
				</el-scrollbar>
				<ButtonList
					class="footer"
					:button="btnConf"
					:loading="formBtnLoading"
					@on-btn-click="handleFormAction"
				/>
			</div>
		</div>
		<!-- 右侧table区域 -->
		<div class="drawer-column right" :class="!canEditExtra ? 'disabled' : ''">
			<div class="rows">
				<Title :title="drawerRightTitle" />
				<Query
					:queryArrList="queryArrList"
					@getQueryData="getTableData"
					class="custom-q"
				/>
				<el-scrollbar>
					<pitaya-table
						ref="tableRef"
						:columns="tableProp"
						:table-data="tableData"
						:need-selection="true"
						:single-select="false"
						:need-index="true"
						:need-pagination="true"
						:total="pageTotal"
						:table-loading="tableLoading"
						@on-selection-change="selectedTableList = $event"
						@on-current-page-change="onCurrentPageChange"
						@on-table-sort-change="handleSortChange"
					>
						<!-- 审批状态 -->
						<template #content_operateStatus="{ rowData }">
							<dict-tag
								v-if="rowData.content_operateStatus > 0"
								:options="DictApi.getMatOperateTypeStatus()"
								:value="rowData.content_operateStatus"
							/>
							<span v-else>---</span>
						</template>

						<!-- 物资状态 -->
						<template #content_status="{ rowData }">
							<dict-tag
								:options="DictApi.getMatStatus()"
								:value="rowData.content_status"
							/>
						</template>

						<!-- 采购单位 -->
						<template #content_buyUnit="{ rowData }">
							{{
								dictFilter("INVENTORY_UNIT", rowData.content_buyUnit)
									?.subitemName || "---"
							}}
						</template>

						<!-- 预估采购单价 -->
						<template #evaluation="{ rowData }">
							<cost-tag :value="rowData.evaluation" />
						</template>

						<!-- 需求数量 -->
						<template #num="{ rowData }">
							<el-input
								class="no-arrows"
								v-model="rowData.num"
								@click.stop
								@input="
									(e:any) => {
										handleInputMin(e, rowData)
									}
								"
								@change="validateNeedNum(rowData)"
							/>
						</template>

						<template #attribute="{ rowData }">
							<dict-tag
								:options="DictApi.getMatAttr()"
								:value="rowData.attribute"
							/>
						</template>

						<template #operations="{ rowData }">
							<el-button v-btn link @click.stop="handleRowDel(rowData)">
								<font-awesome-icon :icon="['fas', 'trash-can']" />
								<span class="table-inner-btn">移除</span>
							</el-button>
						</template>

						<template #footerOperateLeft>
							<button-list
								:button="tbBtnConf"
								:is-not-radius="true"
								:loading="submitBtnLoading"
								@on-btn-click="handleTableAction"
							/>
						</template>
					</pitaya-table>
				</el-scrollbar>
			</div>
			<button-list
				class="footer"
				:button="btnRightConf"
				@on-btn-click="handleFormAction"
			/>

			<Drawer
				:size="modalSize.lg"
				v-model:drawer="matSelectorVisible"
				:destroyOnClose="true"
			>
				<MatList
					:table-data="tableData"
					:query-arr-list="matQueryArrList"
					@onSelected="handleAddMat"
					:business-id="formData?.id"
					@onClosed="matSelectorVisible = false"
				/>
			</Drawer>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref } from "vue"
import { FormItemRule, FormInstance } from "element-plus"
import { FormElementType } from "@/app/baseline/views/components/define"
import { BaseLineSysApi } from "@/app/baseline/api/system"
import { NeedApi } from "@/app/baseline/api/plan/need"
import {
	getIdempotentToken,
	maxValidateErrorInfo,
	maxValidateNum,
	requiredValidator,
	validateAndCorrectInput
} from "@/app/baseline/utils/validate"
import { getModalTypeLabel } from "@/app/baseline/utils"
import {
	IIdempotentTokenType,
	IIdempotentTokenTypePre,
	IModalType
} from "@/app/baseline/utils/types/common"
import FormElement from "../../components/formElement.vue"
import { useTbInit } from "../../components/tableBase"
import CostTag from "../../components/costTag.vue"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import { useDictInit } from "../../components/dictBase"
import { modalSize } from "@/app/baseline/utils/layout-config"
import MatList from "@/app/baseline/views/plan/components/matManualList.vue"
import { toNumber, debounce, map, omit, findIndex } from "lodash-es"
import { useMessageBoxInit } from "../../components/messageBox"
import { ICostCategoryStatus } from "@/app/baseline/utils/types/system-cost-category"
import { DictApi, needListStatus } from "@/app/baseline/api/dict"

const props = defineProps<{
	/**
	 * 业务id
	 */
	id: any

	/**
	 * 编辑模式：编辑/新建
	 */
	mode: IModalType
}>()

const emit = defineEmits<{
	(e: "update"): void
	(e: "close"): void
}>()

const { dictOptions, dictFilter, getDictByCodeList } = useDictInit()
const { showDelConfirm, showWarnConfirm } = useMessageBoxInit()

const drawerLeftTitle = computed(() => ({
	name: [
		getModalTypeLabel(
			canEditExtra.value ? IModalType.edit : IModalType.create,
			"需求清单"
		)
	],
	icon: ["fas", "square-share-nodes"]
}))

const drawerRightTitle = {
	name: ["物资明细"],
	icon: ["fas", "square-share-nodes"]
}

const drawerLoading = ref(false)
const formBtnLoading = ref(false)
const submitBtnLoading = ref(false)

/**
 * 能否编辑扩展信息
 */
const canEditExtra = computed(() => Boolean(formData.value?.id || props.id))

const btnConf = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "保存草稿",
		icon: ["fas", "floppy-disk"]
	}
]

const btnRightConf = [
	{
		name: "保存",
		icon: ["fas", "circle-check"]
	}
]

/**
 * 保存旧的表单数据
 */
const oldFormData = ref<string>()

const formData = ref<Record<string, any>>({})

/**
 * 表单项配置
 */
const formEl: FormElementType[][] = [
	[{ label: "需求清单名称", name: "label", maxlength: 50 }],
	[
		{
			label: "所属专业",
			name: "majorId_view",
			vname: "majorId",
			type: "treeSelect",
			treeApi: BaseLineSysApi.getProfessionTree
		}
	],
	[
		{
			label: "费用类别",
			name: "expenseCategory_view",
			vname: "expenseCategory",
			type: "treeSelect",
			treeApi: () =>
				BaseLineSysApi.getCostCategoryTree({
					status: ICostCategoryStatus.Started
				})
		}
	],
	[
		{
			label: "备注说明",
			name: "remark",
			maxlength: 200,
			rows: "3",
			type: "textarea"
		}
	]
]

/**
 * 表单实例
 */
const formRef = ref<FormInstance>()

/**
 * 表单 validator 规则
 */
const formRules: Record<string, FormItemRule> = {
	label: requiredValidator("需求清单名称"),
	expenseCategory: requiredValidator("费用类别"),
	majorId_view: requiredValidator("所属专业")
}

function handleFormAction(btnName?: string) {
	if (btnName === "保存草稿") {
		return handleSaveDraft()
	} else if (btnName === "保存") {
		if (!formRef.value) {
			return
		}

		formRef.value.validate(async (valid) => {
			if (!valid) {
				return
			}

			await showWarnConfirm("确认更新需求清单数据！")
			formBtnLoading.value = true
			// 表单校验通过
			try {
				// 如果主表有修改，则先更新主表数据
				if (oldFormData.value != JSON.stringify(formData.value)) {
					await NeedApi.updateNeed(omit(formData.value, "code"))
				}

				// 保存 更新状态为启用
				await NeedApi.updateNeedStatus(
					formData.value.id,
					needListStatus.started
				)

				ElMessage.success("操作成功")
				emit("update")
				emit("close")
			} finally {
				formBtnLoading.value = false
			}
		})
	} else {
		emit("close")
	}
}

function handleSaveDraft() {
	if (!formRef.value) {
		return
	}

	formRef.value.validate(async (valid) => {
		if (!valid) {
			return
		}
		formBtnLoading.value = true
		// 表单校验通过
		try {
			const api = canEditExtra.value ? NeedApi.updateNeed : NeedApi.addNeed

			let idempotentToken = ""
			if (!canEditExtra.value) {
				idempotentToken = getIdempotentToken(
					IIdempotentTokenTypePre.apply,
					IIdempotentTokenType.planNeedList
				)
			}

			const r = await api(omit(formData.value, "code"), idempotentToken)

			formData.value.id = r.id

			oldFormData.value = JSON.stringify(formData.value)

			ElMessage.success("操作成功")
			emit("update")
			getTableData()
		} finally {
			formBtnLoading.value = false
		}
	})
}

const {
	tableCache,
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	selectedTableList,
	fetchParam,
	currentPage,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = useTbInit()

fetchFunc.value = (param: Record<string, any>) => {
	return new Promise((resolve) => {
		NeedApi.getMatListByAppId({
			planNeedId: (formData.value.id || props.id) as number,
			...param
		}).then((res) => {
			map(res.rows, (v: Record<string, any>) => {
				if (v.content)
					Array.from(Object.keys(v?.content))?.map(
						(c) => (v[`content_${c}`] = v.content[c])
					)
			})
			resolve(res)
		})
	})
}
tableProp.value = [
	{ label: "物资编码", prop: "content_code", width: 130, sortable: true },
	{ label: "物资名称", prop: "content_label", width: 150 },
	{
		label: "物资分类编码",
		prop: "content_materialTypeCode",
		width: 140
	},
	{ label: "物资分类名称", prop: "content_materialTypeLabel", width: 200 },
	{ label: "规格型号", prop: "content_version", width: 150 },
	{
		label: "技术参数",
		prop: "content_technicalParameter",
		minWidth: 100
	},
	{
		label: "物资性质",
		prop: "attribute",
		needSlot: true,
		width: 120
	},
	{ label: "物资状态", prop: "content_status", needSlot: true, width: 90 },
	{
		label: "操作状态",
		prop: "content_operateStatus",
		needSlot: true,
		width: 120
	},
	{
		label: "采购单位",
		prop: "content_buyUnit",
		needSlot: true,
		width: 90
	},
	{
		label: "预估采购单价",
		prop: "evaluation",
		needSlot: true,
		width: 120,
		align: "right"
	},
	{
		label: "需求数量",
		prop: "num",
		needSlot: true,
		width: 120,
		fixed: "right"
	},
	{
		label: "操作",
		width: 100,
		prop: "operations",
		fixed: "right",
		needSlot: true
	}
]

const queryArrList = computed(() => [
	{
		name: "物资编码",
		key: "materialCode",
		placeholder: "请输入物资编码",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资名称",
		key: "materialLabel",
		placeholder: "请输入物资名称",
		enableFuzzy: true,
		type: "input"
	}
	/* {
		name: "规格型号",
		key: "version",
		placeholder: "请输入规格型号",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资性质",
		key: "attribute",
		type: "select",
		children: dictOptions.value.MATERIAL_NATURE,
		placeholder: "请选择物资性质"
	} */
])

const matQueryArrList = computed(() => [
	{
		name: "物资编码",
		key: "code",
		placeholder: "请输入物资编码",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资名称",
		key: "label",
		placeholder: "请输入物资名称",
		enableFuzzy: true,
		type: "input"
	}
])
function getTableData(data?: { [propName: string]: any }) {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		...data
	}
	fetchTableData()
}
const tbBtnConf = computed(() => {
	return [
		{
			name: "添加物资",
			icon: ["fas", "circle-plus"]
		},
		{
			name: "批量移除",
			icon: ["fas", "trash-alt"],
			disabled: selectedTableList.value.length < 1
		}
	]
})

function handleInputMin(e: any, row: any) {
	row.num = validateAndCorrectInput(e, 0)
}
/**
 * 较验需求数量
 * @param e
 */
function validateNeedNum(e: any) {
	const num = toNumber(e.num)
	const evaluation = toNumber(e.evaluation) // 预估采购单价
	e.num = num

	const oldRow = tableCache.find((v) => v.id == e.id)

	if (num < 1) {
		e.num = oldRow.num
		ElMessage.warning("需求数量不能小于1！")
		return false
	} else {
		const price = toNumber(num * evaluation)
		if (price > maxValidateNum) {
			e.num = oldRow.num
			ElMessage.warning(
				`您的总金额已超过${maxValidateErrorInfo}元，请重新核对金额！`
			)
			return false
		}
	}

	handleUpdateNum(e)
}

/**
 * 更新需求数量
 */
const handleUpdateNum = debounce(async (e: any) => {
	NeedApi.updatePlanNeedItem({
		id: e.id,
		num: e.num
	})

	ElMessage.success("操作成功")

	//  更新 tableCache
	const rowIdx = findIndex(tableCache, (v) => v.id === e.id)
	if (rowIdx !== -1) {
		tableCache.splice(rowIdx, 1, { ...e })
	}

	emit("update")
}, 300)

/**
 * 删除
 * @param e
 */
async function handleRowDel(e: any) {
	await showDelConfirm()
	await NeedApi.deletePlanNeedItem(e.id)
	ElMessage.success("操作成功")
	fetchTableData()
	emit("update")
}

/**
 * 添加物资 | 批量移除
 * @param btnName
 */
const matSelectorVisible = ref(false)
async function handleTableAction(btnName?: string) {
	if (btnName === "添加物资") {
		matSelectorVisible.value = true
	} else {
		const ids = map(selectedTableList.value, ({ id }) => id).toString()
		await showDelConfirm()

		submitBtnLoading.value = true
		try {
			await NeedApi.deletePlanNeedItem(ids)
			ElMessage.success("操作成功")
			fetchTableData()
			emit("update")
		} finally {
			submitBtnLoading.value = false
		}
	}
}

/**
 * 添加物资
 * @param e
 */
async function handleAddMat(e: any) {
	const params = map(e, ({ id, code, label, version, attribute }) => ({
		planNeedId: formData.value.id,
		materialId: id,
		num: 0,
		materialCode: code,
		materialLabel: label,
		version: version,
		attribute: attribute
	}))

	const idempotentToken = getIdempotentToken(
		IIdempotentTokenTypePre.other,
		IIdempotentTokenType.planNeedList,
		formData.value.id
	)
	await NeedApi.addPlanNeedItemList(params, idempotentToken)
	getTableData()
	emit("update")

	matSelectorVisible.value = false
}

/**
 * 左侧详情
 */
function getDetail() {
	if (props.id) {
		drawerLoading.value = true
		NeedApi.getInfoById(props.id)
			.then((res: any) => {
				formData.value = { ...res }

				oldFormData.value = JSON.stringify(formData.value)
			})
			.finally(() => {
				drawerLoading.value = false
			})
	}
}
onMounted(async () => {
	getDictByCodeList(["INVENTORY_UNIT"])
	getDetail()

	fetchParam.value.sord = "desc"
	fetchParam.value.sidx = "createdDate"

	if (props.id) {
		fetchTableData()
	}
})

/**
 * 排序
 * @param column
 * @param prop 字段名称
 * @param order ascending 升序 descending 降序
 */
function handleSortChange(column: any, prop: any, order: any) {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	const computedProp = () => {
		if (prop === "content_code") {
			return "materialCode"
		} else {
			return prop
		}
	}
	fetchParam.value = {
		...fetchParam.value,
		sord:
			order === "descending" ? "desc" : order === "ascending" ? "asc" : "desc",
		sidx: order ? computedProp() : "createdDate" // 排序字段
	}

	fetchTableData()
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.custom-q {
	margin: 10px 0px -10px 10px !important;
}
.drawer-container {
	.left {
		width: 310px;
	}

	.right {
		width: calc(100% - 310px);
	}

	.el-scrollbar {
		height: calc(100% - 73px);
	}
}
</style>
