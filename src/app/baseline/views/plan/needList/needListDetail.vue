<!-- 需求清单 详情页 V2.0 -->
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<!-- 左侧表单区域 -->
		<div class="drawer-column left">
			<div class="rows">
				<Title :title="drawerLeftTitle" />
				<el-scrollbar>
					<el-descriptions size="small" :column="1" border class="content">
						<el-descriptions-item
							v-for="desc in descOptions"
							:key="desc.label"
							:label="desc.label"
						>
							<span v-if="desc.needTooltip">
								<el-tooltip
									effect="dark"
									:content="formData?.[desc.key]"
									:disabled="
										getRealLength(formData?.[desc.key]) <= 100 ? true : false
									"
								>
									{{
										getRealLength(formData?.[desc.key]) > 100
											? setString(formData?.[desc.key], 100) //formData?.[desc.key].substring(0, 100) + "..."
											: formData?.[desc.key] || "---"
									}}
								</el-tooltip>
							</span>
							<span v-else>
								{{ getNumDefByNumKey(desc.key, formData?.[desc.key]) }}
							</span>
						</el-descriptions-item>
					</el-descriptions>
				</el-scrollbar>
			</div>
		</div>
		<!-- 右侧table区域 -->
		<div class="drawer-column right">
			<!-- <div class="rows"> -->
			<Title :title="drawerRightTitle" />
			<Query
				:queryArrList="queryArrList"
				@getQueryData="getTableData"
				class="custom-q"
			/>
			<el-scrollbar clas="row">
				<pitaya-table
					ref="tableRef"
					:columns="tableProp"
					:table-data="tableData"
					:need-selection="false"
					:single-select="false"
					:need-index="true"
					:need-pagination="true"
					:total="pageTotal"
					:table-loading="tableLoading"
					@on-selection-change="selectedTableList = $event"
					@on-current-page-change="onCurrentPageChange"
					@on-table-sort-change="handleSortChange"
				>
					<!-- 审批状态 -->
					<template #content_operateStatus="{ rowData }">
						<dict-tag
							v-if="rowData.content_operateStatus > 0"
							:options="DictApi.getMatOperateTypeStatus()"
							:value="rowData.content_operateStatus"
						/>
						<span v-else>---</span>
					</template>

					<!-- 物资状态 -->
					<template #content_status="{ rowData }">
						<dict-tag
							:options="DictApi.getMatStatus()"
							:value="rowData.content_status"
						/>
					</template>

					<!-- 采购单位 -->
					<template #content_buyUnit="{ rowData }">
						{{
							dictFilter("INVENTORY_UNIT", rowData.content_buyUnit)
								?.subitemName || "---"
						}}
					</template>

					<!-- 预估采购单价 -->
					<template #evaluation="{ rowData }">
						<cost-tag :value="rowData.evaluation" />
					</template>

					<!-- 物资性质 -->
					<template #attribute="{ rowData }">
						<dict-tag
							:options="DictApi.getMatAttr()"
							:value="rowData.attribute"
						/>
					</template>
				</pitaya-table>
			</el-scrollbar>
			<button-list
				class="footer"
				:button="btnConf"
				@on-btn-click="emit('close')"
			/>
			<!-- </div> -->
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref } from "vue"
import { NeedApi } from "@/app/baseline/api/plan/need"
import {
	batchFormatterNumView,
	getModalTypeLabel,
	getNumDefByNumKey
} from "@/app/baseline/utils"
import { IModalType } from "@/app/baseline/utils/types/common"
import { useTbInit } from "../../components/tableBase"
import CostTag from "../../components/costTag.vue"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import { useDictInit } from "../../components/dictBase"
import { map } from "lodash-es"
import { getRealLength, setString } from "@/app/baseline/utils/validate"
import { DictApi } from "@/app/baseline/api/dict"

const props = defineProps<{
	/**
	 * 业务id
	 */
	id: any

	/**
	 * 编辑模式：查看
	 */
	mode: IModalType
}>()

const emit = defineEmits<{
	(e: "close"): void
}>()

const { dictOptions, dictFilter, getDictByCodeList } = useDictInit()

const drawerLeftTitle = computed(() => ({
	name: [getModalTypeLabel(props.mode, "需求清单")],
	icon: ["fas", "square-share-nodes"]
}))

const drawerRightTitle = {
	name: ["物资明细"],
	icon: ["fas", "square-share-nodes"]
}

/**
 * 明细配置
 */
const descOptions = [
	{ label: "需求清单号", key: "code" },
	{ label: "需求清单名称", key: "label" },
	{ label: "物资编码数量", key: "matCodeNum" },
	{ label: "所属专业", key: "majorId_view" },
	{ label: "费用类别", key: "expenseCategory_view" },
	{ label: "备注说明", key: "remark", needTooltip: true },
	{ label: "创建人", key: "createdBy_view" },
	{ label: "创建时间", key: "createdDate" }
]

const drawerLoading = ref(false)

const btnConf = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	}
]

const formData = ref<Record<string, any>>({})

const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	selectedTableList,
	fetchParam,
	currentPage,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = useTbInit()

fetchFunc.value = (param: Record<string, any>) => {
	return new Promise((resolve) => {
		NeedApi.getMatListByAppId({
			planNeedId: (formData.value.id || props.id) as number,
			...param
		}).then((res) => {
			map(res.rows, (v: Record<string, any>) => {
				if (v.content)
					Array.from(Object.keys(v?.content))?.map(
						(c) => (v[`content_${c}`] = v.content[c])
					)
			})
			resolve(res)
		})
	})
}
tableProp.value = [
	{ label: "物资编码", prop: "content_code", width: 130, sortable: true },
	{ label: "物资名称", prop: "content_label", width: 150 },
	{
		label: "物资分类编码",
		prop: "content_materialTypeCode",
		width: 140
	},
	{ label: "物资分类名称", prop: "content_materialTypeLabel", width: 200 },
	{ label: "规格型号", prop: "content_version", width: 150 },
	{
		label: "技术参数",
		prop: "content_technicalParameter",
		minWidth: 100
	},
	{
		label: "物资性质",
		prop: "attribute",
		needSlot: true,
		width: 120
	},
	{ label: "物资状态", prop: "content_status", needSlot: true, width: 90 },
	{
		label: "操作状态",
		prop: "content_operateStatus",
		needSlot: true,
		width: 120
	},
	{
		label: "采购单位",
		prop: "content_buyUnit",
		needSlot: true,
		width: 90
	},
	{
		label: "预估采购单价",
		prop: "evaluation",
		needSlot: true,
		width: 120,
		align: "right"
	},
	{
		label: "需求数量",
		prop: "num_view",
		width: 100,
		align: "right",
		fixed: "right"
	}
]

watch(tableData, () => {
	batchFormatterNumView(tableData.value as any[], undefined, 0)
})
const queryArrList = computed(() => [
	{
		name: "物资编码",
		key: "materialCode",
		placeholder: "请输入物资编码",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资名称",
		key: "materialLabel",
		placeholder: "请输入物资名称",
		enableFuzzy: true,
		type: "input"
	}
	/* {
		name: "规格型号",
		key: "version",
		placeholder: "请输入规格型号",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资性质",
		key: "attribute",
		type: "select",
		children: dictOptions.value.MATERIAL_NATURE,
		placeholder: "请选择物资性质"
	} */
])
function getTableData(data: { [propName: string]: any }) {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = { ...fetchParam.value, ...data }
	fetchTableData()
}
/**
 * 左侧详情
 */
function getDetail() {
	if (props.id) {
		drawerLoading.value = true
		NeedApi.getInfoById(props.id)
			.then((res: any) => {
				formData.value = { ...res }
			})
			.finally(() => {
				drawerLoading.value = false
			})
	}
}
onMounted(() => {
	getDictByCodeList(["INVENTORY_UNIT"])
	getDetail()

	fetchParam.value.sord = "desc"
	fetchParam.value.sidx = "createdDate"

	if (props.id) {
		fetchTableData()
	}
})

/**
 * 排序
 * @param column
 * @param prop 字段名称
 * @param order ascending 升序 descending 降序
 */
function handleSortChange(column: any, prop: any, order: any) {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	const computedProp = () => {
		if (prop === "content_code") {
			return "materialCode"
		} else {
			return prop
		}
	}
	fetchParam.value = {
		...fetchParam.value,
		sord:
			order === "descending" ? "desc" : order === "ascending" ? "asc" : "desc",
		sidx: order ? computedProp() : "createdDate" // 排序字段
	}

	fetchTableData()
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.custom-q {
	margin: 10px 0px -10px 10px !important;
}
.drawer-container {
	.left {
		width: 310px;
	}

	.right {
		width: calc(100% - 310px);
	}

	/* .el-scrollbar {
		height: calc(100% - 123px);
	} */
}
</style>
