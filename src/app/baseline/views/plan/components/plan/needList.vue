<script lang="ts" setup>
import { ref, onMounted, defineProps } from "vue"
import { ElMessage } from "element-plus"
import {
	checkPermission,
	isCheckPermission
} from "@/app/platform/utils/permission"
import { DictApi } from "@/app/baseline/api/dict"
import { useTbInit } from "@/app/baseline/views/components/tableBase.ts"
import { powerList } from "@/app/baseline/views/components/define.d.ts"
import { needListStatus } from "@/app/baseline/api/dict"
import { BaseLineSysApi } from "@/app/baseline/api/system"
import { NeedApi } from "@/app/baseline/api/plan/need"

import DictTag from "@/app/baseline/views/components/dictTag.vue"

/*-------------------初始化表格-start-------------------*/
const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	tbBtnLoading,
	pageSize,
	currentPage,
	pageTotal,
	selectedTableList,
	fetchParam,
	tbBtns,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange,
	onDataSelected,
	onBtnClick
} = useTbInit()
tableProp.value = [
	{ label: "需求清单编号", prop: "code", width: 160 },
	{ label: "需求清单名称", prop: "label", minWidth: 150 },
	{ label: "物资编码数量", prop: "matCodeNum", width: 120 },
	{ label: "专业", prop: "majorId_view", width: 150 },
	{ label: "费用类别", prop: "expenseCategory_view", width: 100 },
	{ label: "公司", prop: "sysCommunityId_view", needSlot: false, width: 120 },
	{ label: "清单状态", prop: "status", needSlot: true, width: 85 },
	{ label: "创建人", prop: "createdBy_view", needSlot: false, width: 120 },
	{ label: "创建时间", prop: "createdDate", width: 160 },
	{
		label: "操作",
		width: 150,
		prop: "operations",
		fixed: "right",
		needSlot: true
	}
]
onDataSelected.value = (rowList: { [propName: string]: any }[]) => {
	selectedTableList.value = rowList
	emits("onDataChange", rowList)
	const isSel = rowList.length <= 0
	if (props.model == "edit") {
		tbBtns.value[0].disabled =
			!rowList.some(
				(item) => item.status !== needListStatus.started.toString()
			) || isSel
		tbBtns.value[1].disabled =
			!rowList.some(
				(item) => item.status === needListStatus.started.toString()
			) || isSel
	}
}
fetchFunc.value = (param) => {
	if (props.majorId)
		return NeedApi.getPlanNeedList({ parentMajorId: props.majorId, ...param })
	else return NeedApi.getPlanNeedList(param)
}
tbBtns.value = [
	{
		name: "启用",
		roles: powerList.needListBtnStart,
		icon: ["fas", "power-off"],
		disabled: true,
		click: () => handleStatus(needListStatus.started.toString())
	},
	{
		name: "停用",
		roles: powerList.needListBtnStop,
		icon: ["fas", "circle-stop"],
		disabled: true,
		click: () => handleStatus(needListStatus.stopped.toString())
	}
]
/*-------------------初始化表格-end-------------------*/

export interface Props {
	majorId?: string //专业筛选条件
	filters?: string //查询条件
	model?: string //显示模式  view : 查看, edit : 编辑
	status?: string //显示模式  view : 查看, edit : 编辑
	singleSelect?: boolean //显示模式  view : 查看, edit : 编辑
}
const props = withDefaults(defineProps<Props>(), {
	model: "edit",
	majorId: undefined,
	singleSelect: true
})
const emits = defineEmits(["onDataChange"])

const rightTitle = {
	name: ["需求清单"],
	icon: ["fas", "square-share-nodes"]
}
const addBtn = [
	{
		name: "新建需求清单",
		roles: powerList.needListBtnCreate,
		icon: ["fas", "square-plus"]
	}
]
const queryArrList = ref([
	{
		name: "所属专业",
		key: "parentMajorId",
		type: "treeSelect",
		treeApi: BaseLineSysApi.getProfessionTree,
		placeholder: "请选择所属专业"
	},
	{
		name: "费用类别",
		key: "expenseCategory",
		type: "treeSelect",
		treeApi: getCostSubject,
		placeholder: "请选择费用类别"
	},
	{
		name: "清单状态",
		key: "status",
		placeholder: "请选择清单状态",
		enableFuzzy: false,
		type: "elTreeSelect",
		children: DictApi.getStpStatus()
	},
	{
		name: "需求清单编号",
		key: "code",
		placeholder: "请输入需求清单编号",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "需求清单名称",
		key: "label",
		placeholder: "请输入需求清单名称",
		enableFuzzy: true,
		type: "input"
	}
])
const costSubject = ref<{ [propName: string]: any }>({})
const currentRow = ref(false)
const detailModel = ref("view")
//drawer
const showDrawer = ref<boolean>(false)
const drawerSize = 1700

//读取费用类别
function getCostSubject(): Promise<any> {
	return new Promise((resolve) => {
		if (costSubject.value) resolve(costSubject.value)
		else {
			BaseLineSysApi.getCostSubject()
				.then((_r) => {
					costSubject.value = _r
				})
				.finally(() => {
					resolve(costSubject.value)
				})
		}
	})
}

//搜索条件确认回调
function getQueryData(data: { [propName: string]: any }) {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = data

	if (props.status) fetchParam.value.status = props.status
	fetchTableData()
}

function handleStatus(status: string) {
	if (selectedTableList.value?.length > 0) {
		let _id = selectedTableList.value[0].id
		NeedApi.updateNeed({ id: _id, status }).then(() => {
			fetchTableData()
		})
	}
}

function onRowEdit(row: any, model = "view") {
	detailModel.value = model
	currentRow.value = row
	showDrawer.value = true
}

// 弹窗关闭
const onCloseDrawer = (key: string) => {
	if (key === "success") {
		fetchTableData()
	}
	showDrawer.value = false
}
onMounted(() => {
	if (props.filters) {
		queryArrList.value = queryArrList.value.filter(
			(_q) => props.filters && props.filters.split(",").includes(_q.key)
		)
	}
	if (props.model == "view") {
		if (props.status) fetchParam.value.status = props.status
		tableProp.value.pop()
	}
	Promise.all([getCostSubject()]).then(() => {
		fetchTableData()
	})
})
</script>
<template>
	<div class="app-container need-list">
		<div class="whole-frame">
			<Query
				class="need-item-table"
				:queryArrList="queryArrList"
				@getQueryData="getQueryData"
			/>
			<ModelFrame class="bottom-frame">
				<PitayaTable
					ref="tableRef"
					:columns="tableProp"
					:table-data="tableData"
					:needSelection="true"
					:single-select="props.singleSelect"
					:need-index="true"
					:need-pagination="true"
					:total="pageTotal"
					@onSelectionChange="onDataSelected"
					@on-current-page-change="onCurrentPageChange"
					:table-loading="tableLoading"
				>
					<template #status="{ rowData }">
						<dict-tag
							:options="DictApi.getStpStatus()"
							:value="rowData.status"
						/>
					</template>
					<template #expenseCategory="{ rowData }">
						{{
							costSubject.find(
								(_c) => _c.subitemValue == rowData.expenseCategory
							)?.subitemName || "---"
						}}
					</template>
					<template #footerOperateLeft>
						<ButtonList
							class="btn-list"
							:is-not-radius="true"
							:button="tbBtns"
							v-loading="tbBtnLoading"
							v-if="props.model == 'edit'"
							@on-btn-click="onBtnClick"
						/>
					</template>
				</PitayaTable>
			</ModelFrame>

			<slot name="options" :data="selectedTableList"></slot>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.app-container {
	height: calc(100% - 30px) !important;
	padding-bottom: 0px;
}
.need-item-table,
.model-frame-wrapper {
	margin: 0px $---spacing-m;
}
.model-frame-wrapper {
	width: calc(100% - $---spacing-m * 2) !important;
}
:deep(.common-query-wrapper) {
	padding-top: $---spacing-m;
}
.bottom-frame {
	border: none;
	padding: 0px;
	.pitaya-table {
		padding: 0px;
		margin: 0px;
	}
}
</style>
