<script setup lang="ts">
import { watch, ref, onMounted, toValue } from "vue"
import { usePagination } from "@/app/platform/hooks/usePagination"
import { DictApi, needListStatus } from "@/app/baseline/api/dict"
import {
	checkPermission,
	isCheckPermission
} from "@/app/platform/utils/permission"
import { ElMessage } from "element-plus"
import { defineProps } from "vue"

import { useTbInit } from "@/app/baseline/views/components/tableBase.ts"
import { PlanApi } from "@/app/baseline/api/plan/plan"
import { NeedApi } from "@/app/baseline/api/plan/need"
import MatList from "@/app/baseline/views/plan/components/matManualList.vue"
import CostTag from "@/app/baseline/views/components/costTag.vue"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import MatDetail from "@/app/baseline/views/plan/components/plan/matDetail.vue"
import NeedList from "@/app/baseline/views/plan/components/plan/needList.vue"
import InputNumber from "@/app/baseline/views/components/inputNumber.vue"
import XEUtils from "xe-utils"
import { useMessageBoxInit } from "@/app/baseline/views/components/messageBox.ts"
import { first, map } from "lodash-es"
import { batchFormatterNumView } from "@/app/baseline/utils"

const { showDelConfirm, showAddConfirm } = useMessageBoxInit()

export interface Props {
	id?: string | number //需求清单ID
	model?: string //显示模式  view : 查看, edit : 编辑
	detailModel?: string //  view : 查看, edit : 编辑
	currentPlanType?: string //计划类型
	majorId?: string
	withFilter?: boolean
	withNumber?: boolean
	matId?: string
}
const props = withDefaults(defineProps<Props>(), {
	id: "",
	model: "view",
	detailModel: "draw",
	majorId: undefined,
	currentPlanType: undefined,
	withFilter: true,
	withNumber: true
})

/*-------------------初始化表格-start-------------------*/
const {
	tableProp,
	tableCache,
	tableData,
	tableRef,
	tableLoading,
	tbBtnLoading,
	pageSize,
	currentPage,
	pageTotal,
	selectedTableList,
	fetchParam,
	tbBtns,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange,
	onDataSelected,
	onBtnClick
} = useTbInit()
const tablePropPreview = [
	{ label: "物资编码", prop: "content_code", width: 130, fixed: "left" },
	{ label: "物资名称", prop: "content_label", width: 150 },
	{ label: "物资分类编码", prop: "content_materialTypeCode", width: 100 },
	{ label: "物资分类名称", prop: "content_materialTypeLabel", width: 120 },
	{ label: "规格型号", prop: "content_version", needSlot: false, width: 120 },
	{
		label: "技术参数",
		prop: "content_technicalParameter",
		needSlot: false,
		minWidth: 100
	},
	{ label: "采购单位", prop: "content_buyUnit", needSlot: true, width: 80 },
	{
		label: "预估采购单价",
		prop: "content_evaluation",
		needSlot: true,
		width: 150,
		align: "right"
	},
	{
		label: "需求数量",
		prop: "num_view",
		width: 120,
		fixed: "right",
		align: "right"
	},
	{
		label: "预估采购金额",
		prop: "cost",
		width: 120,
		needSlot: true,
		align: "right",
		fixed: "right"
	}
]
const tablePropEdit: Record<string, any>[] = [
	{ label: "物资编码", prop: "content_code", width: 130, fixed: "left" },
	{ label: "物资名称", prop: "content_label", width: 150 },
	{ label: "物资分类编码", prop: "content_materialTypeCode", width: 100 },
	{ label: "物资分类名称", prop: "content_materialTypeLabel", width: 200 },
	{ label: "规格型号", prop: "content_version", needSlot: false, width: 120 },
	{
		label: "技术参数",
		prop: "content_technicalParameter",
		needSlot: false,
		minWidth: 100
	},
	{ label: "采购单位", prop: "content_buyUnit", needSlot: true, width: 80 },
	{
		label: "预估采购单价",
		prop: "content_evaluation",
		needSlot: true,
		width: 150,
		align: "right"
	},
	{ label: "需求数量", prop: "num", needSlot: true, width: 120, fixed: "right" }
]

if (props.currentPlanType == "1") {
	tablePropEdit.push(
		...[
			{
				label: "月度需求量",
				prop: "allocationStatus",
				needSlot: true,
				width: 90,
				fixed: "right"
			},
			{
				label: "预估采购金额",
				prop: "cost",
				width: 120,
				needSlot: true,
				fixed: "right",
				align: "right"
			},
			{
				label: "操作",
				width: 120,
				prop: "operations",
				fixed: "right",
				needSlot: true
			}
		]
	)
}
tableProp.value = []
onDataSelected.value = (rowList: { [propName: string]: any }[]) => {
	selectedTableList.value = rowList
	if (tbBtns.value.length > 1) {
		if (rowList.length > 0) tbBtns.value[0].disabled = false
		else tbBtns.value[0].disabled = true
	}
	if (selectedTableList.value.length > 0) {
		curMatId.value = selectedTableList.value[0].id
	}
}
fetchFunc.value = (param) => {
	return new Promise((resolve) => {
		PlanApi.getMatListByPlanId(param).then((_r) => {
			XEUtils.map(_r.rows, (_d: { [propName: string]: any }) => {
				if (_d.content)
					Array.from(Object.keys(_d?.content))?.map(
						(_k) => (_d[`content_${_k}`] = _d.content[_k])
					)
			})
			resolve(_r)
		})
	})
}

/**
 * 格式化数量
 */
watch(tableData, () => {
	batchFormatterNumView(tableData.value as any[], undefined, 0)
})

tbBtns.value = [
	{
		name: "批量移除",
		icon: ["fas", "trash-alt"],
		disabled: true,
		click: onBtnDelete
	},
	{
		name: "引入需求清单",
		icon: ["fas", "square-plus"],
		click: showNeedList
	},
	{
		name: "添加物资",
		icon: ["fas", "square-plus"],
		click: showMatList
	}
	/*{
		name: "导入",
		icon: ["fas", "square-plus"],
		click : showImport
	},
	{
		name: "模版下载",
		icon: ["fas", "square-plus"],
		click : downloadTpl
	}*/
]
/*-------------------初始化表格-end-------------------*/
const emits = defineEmits(["onDataChange"])

const dictOptions = ref<Record<string, any[]>>({
	INVENTORY_UNIT: []
})
function getDictByCodeList(): Promise<null> {
	return new Promise((resolve) => {
		DictApi.getDictByCodeList(dictOptions)
			.then((res) => {
				dictOptions.value = res
			})
			.finally(() => {
				resolve(null)
			})
	})
}

const drawerSize = 1500
const drawerSizeSmall = 800
const queryArrList = [
	{
		name: "物资编码",
		key: "materialCode",
		placeholder: "请输入物资编码",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资名称",
		key: "materialLabel",
		placeholder: "请输入物资名称",
		enableFuzzy: true,
		type: "input"
	}
]
const showAddNeedDrawer = ref(false) //引入需求清单抽屉
const needDrawerBtn = ref([
	{ name: "取消", icon: ["fas", "delete-left"] },
	{ name: "保存", icon: ["fas", "square-plus"], disabled: true }
])
const showAddMatDrawer = ref(false) //添加物资抽屉
const showMatDetailDrawer = ref(false) //月度需求量抽屉
const curData = ref<{ [propName: string]: any }>({})
const curMatId = ref()
const curPlanId = ref<string | number>("")

const reqListTitle = {
	name: ["需求清单"],
	icon: ["fas", "square-share-nodes"]
}
function getTableData(data: { [propName: string]: any }) {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = data
	fetchParam.value.planId = curPlanId.value
	queryTableData()
}

/**
 * 编辑|查看阅读需求量
 * @param row
 */
function onDetailEdit(row: any) {}

/**
 * 批量删除
 */
function onBtnDelete() {
	return new Promise((resolve, reject) => {
		if (selectedTableList.value?.length > 0) {
			showDelConfirm().then(() => {
				let _ids: string[] = []
				selectedTableList.value.map((_d) => {
					_ids.push(_d.id)
				})

				PlanApi.deletePlanItem(_ids.join(",")).then(() => {
					queryTableData()
					emits("onDataChange", toValue(tableData))
				})
			})
		}
		reject()
	})
}

//打开需求清单列表
function showNeedList() {
	showAddNeedDrawer.value = true
	return Promise.reject()
}

//根据选中的需求计划补充当前物资清单
function mergeMatsByAppId(id): Promise<null> {
	return new Promise((resolve) => {
		NeedApi.getMatListByAppId({ planNeedId: id, pageSize: 999999 })
			.then((_r) => {
				resolve(_r.rows)
			})
			.finally(() => resolve(null))
	})
}

function addPlanItem(materialId: string, num: number = 0) {
	return PlanApi.addPlanItem({
		planId: curPlanId.value,
		materialId,
		num
	})
}

function onNeedListChange(data) {
	needDrawerBtn.value[1].disabled = data?.length <= 0
}

function onNeedListBtnClick(key, data) {
	if (key == "保存") {
		showAddConfirm(() => {
			tbBtnLoading.value = true
			let _ids = data.map((_d) => _d.id)
			PlanApi.addPlanItemFromNeedList(curPlanId.value, _ids).finally(() => {
				queryTableData()
				emits("onDataChange")
				tbBtnLoading.value = false
				showAddNeedDrawer.value = false
			})
		})
	} else {
		tbBtnLoading.value = false
		showAddNeedDrawer.value = false
	}
}

//从物资列表导入
async function onMatListBtnClick(data: any) {
	tableLoading.value = true
	tbBtnLoading.value = true
	try {
		const params = data.map((v: any) => {
			return { planId: curPlanId.value, materialId: v.id, num: 0 }
		})

		await PlanApi.addPlanItemBatch(params)
		queryTableData()
		emits("onDataChange")
		showAddMatDrawer.value = false
	} finally {
		tableLoading.value = false
		tbBtnLoading.value = false
	}
}

//打开物资列表
function showMatList() {
	showAddMatDrawer.value = true
	return Promise.reject()
}
//导入
function showImport() {
	return new Promise<void>((reject) => {
		reject()
	})
}
//下载模板
function downloadTpl() {
	return new Promise<void>((reject) => {
		reject()
	})
}

//重新计算 物资数量及价格
function resetCounter() {}

function showMatDetail(data) {
	curData.value = data
	curMatId.value = data.id
	showMatDetailDrawer.value = true
}

function changeMatDetail(data: { [propName: string]: any }) {
	if (curData.value) {
		curData.value.detail = data
		showMatDetailDrawer.value = false
		queryTableData()
	}
}

const _lock = {}
function checkItemNum(e, data) {
	data.num = isNaN(parseInt(data.num)) ? 0 : parseInt(data.num)
	let _data = tableData.value.find((_d) => _d.id == data.id)
	if (_data) _data.num = data.num
	if (_lock[data.id]) window.clearTimeout(_lock[data.id])
	_lock[data.id] = window.setTimeout(() => updateMat(e.target, _data), 500)
}

function updateMat(el, data) {
	PlanApi.updatePlanItem(data)
		.then((_r) => {
			tableData.value.map((_d) => {
				if (_d.id == data.id) Object.assign(_d, _r)
			})

			tableCache.map((_d) => {
				if (_d.id == data.id) Object.assign(_d, _r)
			})
			ElMessage.success("操作成功")
			emits("onDataChange")
		})
		.catch(() => {
			const _data = tableCache.find((_d: any) => _d.id == data.id)

			data.num = _data.num
			el.value = _data.num
		})
}

function queryTableData() {
	fetchTableData()
	/* .then((_r) => {
		if (
			_r.value?.length > 0 &&
			props.model == "view" &&
			props.currentPlanType == "1"
		) {
			curMatId.value = _r.value[0].id
			tableRef.value.pitayaTableRef!.toggleRowSelection(_r.value[0], true)
		}
	}) */
}

/**
 * 补丁
 * 监听 tableData 变化；设置物资Id为第一个
 */
watch([tableData], () => {
	if (
		tableData.value?.length > 0 &&
		props.model == "view" &&
		props.currentPlanType == "1"
	) {
		curMatId.value = (first(tableData.value) as any)?.id
		nextTick(() => {
			tableRef.value.pitayaTableRef!.toggleRowSelection(
				first(tableData.value),
				true
			)
		})
	}
})

onMounted(() => {
	getDictByCodeList().then(() => {
		if (props.id) {
			curPlanId.value = props.id
			fetchParam.value.planId = props.id
			fetchParam.value.materialId = props.matId
			queryTableData()
		}
	})
	if (props.model == "view") {
		tableProp.value = tablePropPreview as any
	} else tableProp.value = tablePropEdit as any
})

watch(
	() => props.id,
	() => {
		if (props.id) {
			curPlanId.value = props.id
			fetchParam.value.planId = props.id

			currentPage.value = 1
			tableRef.value?.resetCurrentPage()
			queryTableData()
		}
	}
)
</script>
<template>
	<div
		class="data-list"
		:class="{
			view: props.model == 'view' && props.currentPlanType == '1',
			withNum: props.withNumber
		}"
	>
		<div class="data-table">
			<Query
				v-if="withFilter"
				:queryArrList="queryArrList"
				@getQueryData="getTableData"
				class="custom-q"
			/>
			<PitayaTable
				ref="tableRef"
				:columns="tableProp"
				:tableData="tableData"
				:need-index="true"
				:need-pagination="true"
				:single-select="props.model == 'view'"
				:need-selection="true"
				:total="pageTotal"
				@onSelectionChange="onDataSelected"
				@on-current-page-change="onCurrentPageChange"
				:table-loading="tableLoading"
			>
				<template #num="{ rowData }">
					<el-input-number
						:controls="false"
						v-if="props.model == 'edit'"
						class="number-input"
						v-model="rowData.num"
						@change="(e) => checkItemNum(e, rowData)"
						:min="0"
					/>
					<!--					<InputNumber :controls="false" :precision="2" v-if="props.model == 'edit'" class="number-input" v-model="rowData.num" @keyup="e => checkItemNum( e, rowData )" min="0" />-->
					<template v-else>{{ rowData.num || 0 }}</template>
				</template>
				<template #content_buyUnit="{ rowData }">
					{{
						dictOptions.INVENTORY_UNIT.find(
							(_c) => _c.value == rowData.content_buyUnit
						)?.label || "---"
					}}
				</template>
				<template #content_evaluation="{ rowData }">
					<cost-tag :value="rowData.evaluation" />
				</template>
				<template #allocationStatus="{ rowData }">
					<el-tag v-if="rowData.allocationStatus == 1" type="success"
						>已分配</el-tag
					>
					<el-tag v-else type="danger">待分配</el-tag>
				</template>
				<template #attributeId="{ rowData }">
					<cost-tag :value="rowData.attributeId" />
				</template>
				<template #status="{ rowData }">
					<dict-tag :options="DictApi.getMatStatus()" :value="rowData.status" />
				</template>
				<template #cost="{ rowData }">
					<cost-tag :value="rowData.evaluation * rowData.num" />
				</template>
				<template #operations="{ rowData }">
					<el-button
						v-if="rowData.num > 0"
						v-btn
						link
						@click="showMatDetail(rowData)"
					>
						<font-awesome-icon :icon="['fas', 'eye']" />
						<span class="table-inner-btn">月度需求量</span>
					</el-button>
					<template v-else>---</template>
				</template>
				<template #footerOperateLeft>
					<ButtonList
						class="btn-list"
						:is-not-radius="true"
						:button="tbBtns"
						v-if="props.model == 'edit'"
						:loading="tbBtnLoading"
						@on-btn-click="onBtnClick"
					/>
				</template>
			</PitayaTable>
		</div>
		<MatDetail
			v-if="props.model == 'view' && props.currentPlanType == '1'"
			filters="code"
			model="view"
			:id="curMatId"
			:withInfo="false"
		/>
	</div>

	<!--	月度需求量-->
	<Drawer
		class="drawer-hidden-box"
		:size="600"
		v-model:drawer="showMatDetailDrawer"
		:model="props.model"
		:destroyOnClose="true"
	>
		<MatDetail
			filters="code"
			model="edit"
			:id="curMatId"
			@onSuccess="changeMatDetail"
			@onClose="showMatDetailDrawer = false"
		/>
	</Drawer>

	<!--	需求清单-->
	<Drawer
		class="drawer-hidden-box"
		:size="drawerSize"
		v-model:drawer="showAddNeedDrawer"
		:destroyOnClose="true"
	>
		<Title :title="reqListTitle" />
		<NeedList
			filters="code"
			model="view"
			:majorId="props.majorId"
			:singleSelect="false"
			:status="needListStatus.started"
			@onDataChange="(_data) => onNeedListChange(_data)"
		>
			<template #options="{ data }">
				<ButtonList
					class="btn-list needlist-opt-btns"
					:is-not-radius="true"
					:button="needDrawerBtn"
					@on-btn-click="(btnKey) => onNeedListBtnClick(btnKey, data)"
				/>
			</template>
		</NeedList>
	</Drawer>

	<!--	物资清单-->
	<Drawer
		class="drawer-hidden-box"
		:size="drawerSize"
		v-model:drawer="showAddMatDrawer"
		:destroyOnClose="true"
	>
		<MatList
			model="view"
			typeTitle="物资分类编码"
			listTitle="选择物资"
			:table-data="tableData"
			:planId="curPlanId"
			@onSelected="onMatListBtnClick"
			@onClosed="showAddMatDrawer = false"
		/>
	</Drawer>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
$--detail--width: 220px;
.custom-q {
	margin: 10px 0px -10px 10px !important;
}
.data-list {
	display: flex;
	flex-direction: row;
	width: 100%;
	height: 100%;
	.data-table {
		width: 100%;
	}
	&.view.withNum {
		.data-table {
			width: calc(100% - $--detail--width);
		}
		.mat-detail {
			padding-top: 10px;
			border-left: solid 1px $---border-color;
			width: $--detail--width;
		}
	}
}
.needlist-opt-btns {
	border-top: solid 1px $---border-color;
	padding-top: 10px;
	justify-content: flex-end;
}
</style>
