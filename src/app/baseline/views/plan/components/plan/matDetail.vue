<!-- 计划 - 需求计划 - 物资明细/月度需求量 -->
<script lang="ts" setup>
import { watch, ref, reactive, toValue, onBeforeMount } from "vue"
import { MatApplyApi } from "@/app/baseline/api/material/matApply"
import { PlanApi } from "@/app/baseline/api/plan/plan"
import { useDictInit } from "@/app/baseline/views/components/dictBase"
import EmptyData from "@/assets/images/empty/empty_data.svg?component"
import CostTag from "@/app/baseline/views/components/costTag.vue"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import { chain, sumBy } from "lodash-es"
import { toFixedTwo } from "@/app/baseline/utils"
import {
	getIdempotentToken,
	getRealLength,
	setString
} from "@/app/baseline/utils/validate"
import { DictApi } from "@/app/baseline/api/dict"
import {
	IIdempotentTokenType,
	IIdempotentTokenTypePre
} from "@/app/baseline/utils/types/common"

const { dictFilter, getDictByCodeList } = useDictInit()

export interface Props {
	id?: string | number
	model?: string
	withInfo?: boolean
}

const props = withDefaults(defineProps<Props>(), {
	id: "",
	model: "view",
	withInfo: true
})

const emits = defineEmits(["onSuccess", "onClose"])

/**
 * 左侧 title 配置
 */
const drawerLeftTitle = {
	name: ["物资信息"],
	icon: ["fas", "square-share-nodes"]
}

/**
 * 右侧 title 配置
 */
const drawerRightTitle = {
	name: ["月度需求量"],
	icon: ["fas", "square-share-nodes"]
}

/**
 * 按钮配置
 */
const formBtnLoading = ref(false)
const formBtnList = ref([
	{ name: "取消", icon: ["fas", "circle-minus"] },
	{ name: "保存", icon: ["fas", "circle-check"] }
])
const loading = ref(true)
const empty = ref(false)
const planInfo = ref<Record<string, any>>({
	year: ""
})
const needInfo = ref<Record<string, any>>({
	code: "",
	label: "",
	planId: "",
	materialTypeCode: "",
	materialTypeLabel: "",
	version: "",
	technicalParameter: "",
	buyUnit: "",
	evaluation: 0,
	num: 0
})

/**
 * 首次进来 获取已分配数量
 */
const firstNumSum = ref(0)

/**
 * 首次进来 获取待分配数量
 */
const firstNeedNum = ref<number>(0)

const numSum = ref(0)
const formData = reactive(new Array(12).fill(0))
const formDataIds = reactive<Record<string, any>>({})
const materialId = ref()

function getMatId(): Promise<string> {
	return new Promise<string>((resolve, reject) => {
		PlanApi.getPlanItem(props.id as string)
			.then((_r: any) => {
				needInfo.value.planId = _r.planId
				needInfo.value.num = _r.num

				firstNeedNum.value = _r.num
				resolve(_r.materialId)
			})
			.catch(() => reject)
	})
}

function getInfoById(id: any) {
	return PlanApi.getInfoById(id)
}

function getNeedInfo(): Promise<any> {
	return MatApplyApi.getMatApplyById(materialId.value)
}
function getPlanItemMonthlyList(): Promise<any> {
	if (!props.id) return Promise.resolve()
	else
		return PlanApi.getPlanItemMonthlyList({
			planItemId: props.id as number,
			pageSize: 99
		})
}

function updateItemNum(month: string, num: number): Promise<any> {
	if (formDataIds[month]) {
		// 修改需求计划物资明细-月度
		return PlanApi.updatePlanItemMonthly({
			id: formDataIds[month],
			planItemId: props.id as number,
			year: planInfo.value.year as any,
			month: parseInt(month) + 1,
			num
		})
	} else {
		// 新增需求计划物资明细-月度
		const idempotentToken = getIdempotentToken(
			IIdempotentTokenTypePre.other,
			IIdempotentTokenType.planNeedPlanItem,
			`${props.id}-${planInfo.value.year}-${parseInt(month) + 1}`
		)
		return PlanApi.addPlanItemMonthly(
			{
				planItemId: props.id as number,
				year: planInfo.value.year as any,
				month: parseInt(month) + 1,
				num
			},
			idempotentToken
		)
	}
}

function onFormBtnList(key?: string) {
	if (key == "保存") {
		if (sumBy(formData) != needInfo.value.num) {
			ElMessage.error("您有待分配的物资，请分配后再进行保存！")
			return false
		}
		formBtnLoading.value = true
		const _funcs: Promise<any>[] = []
		Object.keys(formData).map((_idx: any) => {
			_funcs.push(updateItemNum(_idx, formData[_idx]))
		})
		Promise.all(_funcs)
			.then(() => {
				ElMessage.success("操作成功")
				emits("onSuccess", formData)
			})
			.finally(() => {
				formBtnLoading.value = false
			})
	} else {
		emits("onClose")
	}
}

/**
 * 初始加载
 */
async function loadData() {
	loading.value = true
	try {
		// 查询需求计划物资明细详细
		const planItemRes = await PlanApi.getPlanItem(props.id as string)

		needInfo.value = {
			...planItemRes,
			planId: planItemRes.planId,
			num: planItemRes.num,
			num_view: toFixedTwo(planItemRes.num as any, 0)
		}

		firstNeedNum.value = planItemRes.num as unknown as number
		materialId.value = planItemRes.materialId

		// 物资详情
		const mayApplyByIdRes = await MatApplyApi.getMatApplyById(materialId.value)

		needInfo.value = { ...mayApplyByIdRes, ...needInfo.value }

		const planItemMonthlyListRes = await PlanApi.getPlanItemMonthlyList({
			planItemId: props.id as number,
			pageSize: 99
		})

		Object.keys(formData).map((_k: any) => (formData[_k] = 0))
		planItemMonthlyListRes.rows.map((_d: any) => {
			const _monthNum = parseInt(_d.month) - 1
			if (formData[_monthNum] != undefined) {
				formData[_monthNum] = _d.num
				formDataIds[_monthNum] = _d.id
			}
		})

		/**
		 * 初次请求 待分配数量为0时
		 * 系统需要支持将需求数量平均分配至每月的月度需求量
		 * 余数 倒叙增加
		 */
		firstNumSum.value = formData.reduce((sum, cur) => sum + cur)

		if (firstNeedNum.value != 0) {
			// 均分配至每月的月度需求量
			const averageMonthly = Math.floor(firstNeedNum.value / 12)

			Object.keys(formData).map((_k: any) => {
				if (sumBy(formData) < firstNeedNum.value) {
					formData[_k] = averageMonthly || 1
				}
			})

			if (sumBy(formData) < firstNeedNum.value) {
				for (let a = Object.keys(formData).length - 1; a > 0; a--) {
					if (sumBy(formData) < firstNeedNum.value) {
						formData[a] += 1
					}
				}
			}
		}

		const planInfoRes = await PlanApi.getInfoById(needInfo.value.planId)
		planInfo.value = { ...planInfoRes }
	} finally {
		loading.value = false
	}
}

/**
 * 废弃方法
 */
function loadData1() {
	loading.value = true
	getMatId()
		.then((_mid) => {
			materialId.value = _mid
			return getNeedInfo()
		})
		.then((_r) => {
			needInfo.value = Object.assign(toValue(needInfo), _r)
			return getPlanItemMonthlyList()
		})
		.then((_r) => {
			Object.keys(formData).map((_k: any) => (formData[_k] = 0))
			_r.rows.map((_d: any) => {
				const _monthNum = parseInt(_d.month) - 1
				if (formData[_monthNum] != undefined) {
					formData[_monthNum] = _d.num
					formDataIds[_monthNum] = _d.id
				}
			})

			/**
			 * 初次请求 待分配数量为0时
			 * 系统需要支持将需求数量平均分配至每月的月度需求量
			 * 余数 倒叙增加
			 */
			firstNumSum.value = formData.reduce((sum, cur) => sum + cur)

			if (firstNeedNum.value != 0) {
				// 均分配至每月的月度需求量
				const averageMonthly = chain(firstNeedNum.value)
					.divide(12)
					.floor()
					.value()

				Object.keys(formData).map((_k: any) => {
					if (sumBy(formData) < firstNeedNum.value) {
						formData[_k] = averageMonthly || 1
					}
				})

				if (sumBy(formData) < firstNeedNum.value) {
					for (let a = Object.keys(formData).length - 1; a > 0; a--) {
						if (sumBy(formData) < firstNeedNum.value) {
							formData[a] += 1
						}
					}
				}
			}

			return getInfoById(needInfo.value.planId)
		})
		.then((_r: any) => {
			planInfo.value = _r
		})
		.catch((e) => {
			console.log("empty.value-----", e)
			empty.value = true
		})
		.finally(() => {
			loading.value = false
		})
}

onMounted(() => {
	if (props.model == "view") {
		formBtnList.value.pop()
	}
	getDictByCodeList(["INVENTORY_UNIT"])

	if (props.id) {
		loadData()
	} else {
		empty.value = true
	}
})

watch(
	() => formData,
	(newVal) => {
		numSum.value = newVal.reduce((sum, cur) => sum + cur)
		/* const _btn = formBtnList.value.find((_btn) => _btn.name == "保存")
		if (_btn) {
			if (needInfo.value.num - numSum.value == 0) _btn.disabled = false
			else _btn.disabled = true
		} */
	},
	{ deep: true }
)

watch(
	() => props.id,
	() => {
		empty.value = false
		loadData()
	},
	{ deep: true }
)
</script>
<template>
	<div class="mat-detail drawer-container" v-loading="loading && !empty">
		<!-- 左侧表单区域 -->
		<div class="drawer-column left" v-if="withInfo">
			<div class="rows">
				<Title :title="drawerLeftTitle" v-if="!loading" />
				<el-empty v-if="empty" description="暂无数据" />
				<el-descriptions
					v-else
					size="small"
					:column="1"
					:border="true"
					class="content"
				>
					<el-descriptions-item label="物资编码">{{
						needInfo.code || "---"
					}}</el-descriptions-item>
					<el-descriptions-item label="物资名称">{{
						needInfo.label || "---"
					}}</el-descriptions-item>
					<el-descriptions-item label="物资分类编码">{{
						needInfo.materialTypeCode || "---"
					}}</el-descriptions-item>
					<el-descriptions-item label="物资分类名称">{{
						needInfo.materialTypeLabel || "---"
					}}</el-descriptions-item>
					<el-descriptions-item label="规格型号">{{
						needInfo.version || "---"
					}}</el-descriptions-item>
					<el-descriptions-item label="技术参数">
						<el-tooltip
							effect="dark"
							:content="needInfo.technicalParameter"
							:disabled="
								getRealLength(needInfo.technicalParameter) <= 100 ? true : false
							"
						>
							{{
								getRealLength(needInfo.technicalParameter) > 100
									? setString(needInfo.technicalParameter, 100) //needInfo.technicalParameter.substring(0, 100) + "..."
									: needInfo.technicalParameter || "---"
							}}
						</el-tooltip>
					</el-descriptions-item>
					<el-descriptions-item label="物资性质">
						<dict-tag
							:options="DictApi.getMatAttr()"
							:value="needInfo.attribute"
						/>
					</el-descriptions-item>
					<el-descriptions-item label="采购单位">{{
						dictFilter("INVENTORY_UNIT", needInfo.buyUnit)?.label || "---"
					}}</el-descriptions-item>
					<el-descriptions-item label="预估采购单价"
						><CostTag :value="needInfo.evaluation"
					/></el-descriptions-item>
					<el-descriptions-item label="需求数量">{{
						needInfo.num_view || "---"
					}}</el-descriptions-item>
					<el-descriptions-item label="预估采购金额"
						><CostTag :value="needInfo.num * needInfo.evaluation"
					/></el-descriptions-item>

					<el-descriptions-item label="需求清单编号">
						{{ planInfo.planNeedCode }}
					</el-descriptions-item>
				</el-descriptions>
			</div>
		</div>
		<!-- 右侧table区域 -->
		<div class="drawer-column right" :class="props.model">
			<div class="rows">
				<Title :title="drawerRightTitle" />
				<template v-if="empty">
					<EmptyData class="empty_img" />
					<p>未查询到相关数据</p>
				</template>
				<el-scrollbar v-else class="request-form">
					<div class="request-title">
						<div>
							<span>{{ needInfo.num - numSum }}</span>
							<label>待分配</label>
						</div>
						<div>
							<span>{{ numSum }}</span>
							<label>已分配</label>
						</div>
					</div>
					<div class="request-list">
						<div
							class="list-item"
							v-for="(value, index) in formData"
							:key="index"
						>
							<span>{{ (index + 1).toString().padStart(2, "0") }}</span>
							<label v-if="props.model == 'view'">{{ formData[index] }}</label>
							<el-input-number
								v-else
								v-model="formData[index]"
								:max="needInfo.num - numSum + formData[index]"
								:min="0"
								:disabled="props.model == 'view'"
							/>
						</div>
					</div>
				</el-scrollbar>
			</div>

			<ButtonList
				v-if="props.model == 'edit'"
				class="footer"
				:loading="formBtnLoading"
				:button="formBtnList"
				@on-btn-click="onFormBtnList"
			/>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
$--input-font-size: 20px;
/* .el-descriptions {
	:deep(.el-descriptions__label) {
		width: 200px;
	}
} */
.right {
	width: 250px;
	&.view {
		padding: 0px;
		.request-title {
			display: none;
		}
		:deep(.common-title-wrapper) {
			border: none;
			padding: $---spacing-s 0px $---spacing-m 0px;
		}
		.list-item {
			width: 100% !important;
			padding: 0px $---spacing-m;
		}
		:deep(.el-input__wrapper) {
			background: transparent;
			.el-input__inner {
				color: red !important;
			}
		}
		:deep(.el-input-number__decrease),
		:deep(.el-input-number__increase) {
			display: none;
		}
	}
}
.request-form {
	width: 100%;
	height: calc(100% - 20px);
	span {
		color: $---color-info;
		font-size: 30px;
		font-weight: bold;
	}
	.request-title {
		display: flex;
		padding: $---spacing-m;
		div {
			display: flex;
			flex-direction: column;
			width: 50%;
			align-items: center;
			&:last-child {
				border-left: solid 1px $---border-color;
			}
		}
	}
	.request-list {
		display: flex;
		flex-direction: column;
		align-items: center;
		.list-item {
			display: flex;
			justify-content: center;
			width: 280px;
			padding-top: $---spacing-m;
			> * {
				height: 50px;
				line-height: 50px;
			}
			span {
				border: solid 1px $---border-color;
				border-radius: $---border-radius-m 0px 0px $---border-radius-m;
				text-align: center;
				width: 80px;
				background: $---color-background;
				&:after {
					content: "月";
					pading-left: $---spacing-s;
					color: $---color-info;
					font-size: $---font-size-m;
				}
			}
			label {
				width: 115px;
				border: solid 1px $---border-color;
				border-radius: 0px $---border-radius-m $---border-radius-m 0px;
				border-left: none;
				font-size: calc($--input-font-size + 10px);
				text-align: center;
				color: $---color-info;
			}
			.el-input-number {
				border: solid 1px $---border-color;
				border-radius: 0px $---border-radius-m $---border-radius-m 0px;
				border-left: none;
				:deep(.el-input-number__decrease) {
					background: transparent !important;
				}
				:deep(.el-input-number__increase) {
					background: transparent !important;
				}
				:deep(.el-input__wrapper) {
					box-shadow: none;
				}
				.el-input {
					width: 200px;
				}
				:deep(.el-input__inner) {
					font-size: $--input-font-size !important;
					text-align: center !important;
				}
			}
		}
	}
}
.empty_img {
	margin: auto;
	width: 150px;
	display: block;
	margin-top: calc(100% - 80px);
	+ p {
		color: $---font-color-2;
		font-size: $---font-size-m;
		text-align: center;
	}
}
</style>
