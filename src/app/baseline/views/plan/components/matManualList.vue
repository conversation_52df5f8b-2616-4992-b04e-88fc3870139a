<script lang="ts" setup>
import { MatApplyApi } from "@/app/baseline/api/material/matApply"
import { appStatus, DictApi } from "@/app/baseline/api/dict"

import { useTbInit } from "@/app/baseline/views/components/tableBase"
import { matStatus } from "@/app/baseline/api/dict"

import TreeMatType from "@/app/baseline/views/components/v2/treeMatType.vue"
import costTag from "@/app/baseline/views/components/costTag.vue"
import { defineProps, ref, withDefaults, onMounted } from "vue"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import { useMessageBoxInit } from "@/app/baseline/views/components/messageBox"
import { NeedPlanApi } from "@/app/baseline/api/material/needPlan"
import { IMateOperateStatus } from "@/app/baseline/utils/types/material"

interface Props {
	planId?: string | number
	businessId?: string | number // 业务Id  如：交旧申请主表Id

	/**
	 * table 的列配置
	 */
	columns?: TableColumnType[]

	defaultExpandedKeys?: any
	ifClick?: boolean
	/**
	 * table 数据源
	 */
	tableApi?: (params: any) => Promise<any>

	/**
	 * table 请求参数
	 */
	tableReqParams?: any

	queryArrList?: any
}
const props = withDefaults(defineProps<Props>(), {
	planId: undefined,
	businessId: undefined,
	columns: () => [],
	defaultExpandedKeys: () => [0]
})

const btnLoading = ref(false)

const { showWarnConfirm } = useMessageBoxInit()

const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	currentPage,
	pageTotal,
	selectedTableList,
	fetchParam,
	tbBtns,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange,
	onDataSelected
} = useTbInit()
tableProp.value = props.columns?.length
	? props.columns
	: [
			{ label: "物资编码", prop: "code", width: 130, fixed: "left" },
			{ label: "物资名称", prop: "label", width: 150 },
			{
				label: "物资分类编码",
				prop: "materialTypeCode",
				width: 100
			},
			{
				label: "物资分类名称",
				prop: "materialTypeLabel",
				width: 200
			},
			{
				label: "规格型号",
				prop: "version",
				needSlot: false,
				width: 150
			},
			{
				label: "技术参数",
				prop: "technicalParameter",
				needSlot: false,
				minWidth: 100,
				align: "left"
			},
			{
				label: "物资性质",
				prop: "attribute",
				needSlot: true,
				width: 120
			},
			{ label: "采购单位", prop: "buyUnit", needSlot: true, width: 90 },
			{
				label: "预估采购单价",
				prop: "evaluation",
				needSlot: true,
				width: 120,
				align: "right",
				fixed: "right"
			}
	  ]

const emits = defineEmits(["onSelected", "onClosed"])

onDataSelected.value = (rowList: { [propName: string]: any }[]) => {
	selectedTableList.value = rowList
	if (selectedTableList.value?.length > 0) {
		formBtnList.value[1].disabled = false
	} else formBtnList.value[1].disabled = true
}
fetchFunc.value = (param) => {
	if (props.planId) {
		return NeedPlanApi.getMatApplyList({
			planId: props.planId,
			...param,
			status: matStatus.normal,
			operateStatus: `${IMateOperateStatus.normal},${IMateOperateStatus.update}`,
			sord: "asc",
			sidx: "code"
		})
	} else if (props.tableApi) {
		return props.tableApi({
			status: matStatus.normal,
			...param
		})
	} else if (props.businessId) {
		return MatApplyApi.getMatApplyListV2({
			...param,
			businessId: props.businessId,
			status: matStatus.normal,
			operateStatus: `${IMateOperateStatus.normal},${IMateOperateStatus.update}`,
			sord: "asc",
			sidx: "code"
		})
	} else {
		return MatApplyApi.getMatApplyList({
			...param,
			status: matStatus.normal
			//operateStatus: `${IMateOperateStatus.normal},${IMateOperateStatus.update}`,
		})
	}
}
tbBtns.value = []

const leftTitle = {
	name: ["物资编码分类"],
	icon: ["fas", "square-share-nodes"]
}
const rightTitle = ref<any>({
	name: ["选择物资"],
	icon: ["fas", "square-share-nodes"]
})
//查询

const queryConf = computed(() => {
	const ls = [
		{
			name: "物资编码",
			key: "code",
			placeholder: "请输入物资编码",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "物资名称",
			key: "label",
			placeholder: "请输入物资名称",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "规格型号",
			key: "version",
			placeholder: "请输入规格型号",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "物资性质",
			key: "attribute",
			type: "select",
			children: dictOptions.value.MATERIAL_NATURE,
			placeholder: "请选择物资性质"
		}
	]

	return props.queryArrList || ls
})

const formBtnList = ref([
	{ name: "取消", icon: ["fas", "circle-minus"] },
	{ name: "保存", icon: ["fas", "floppy-disk"], disabled: true }
])

const treeCheck = (selectedId: any, status: string) => {
	getQueryData({
		materialTypeIds: selectedId.join(","),
		materialTypeStatus: status
	})
}
const getQueryData = (data: { [propName: string]: any }) => {
	const { startAndEndTime } = data
	data.lastModifiedDate_start = startAndEndTime && startAndEndTime[0]
	data.lastModifiedDate_end = startAndEndTime && startAndEndTime[1]
	delete data.startAndEndTime

	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		...{ bpmStatus: appStatus.approved },
		...data,
		...props.tableReqParams
	}
	fetchTableData()
}
const editId = ref<any>("")

async function onFormBtnList(btnName?: string) {
	if (btnName == "保存") {
		await showWarnConfirm("您确认要保存本次选择的数据吗？")
		btnLoading.value = true
		try {
			await new Promise((resolve, reject) => {
				try {
					emits("onSelected", selectedTableList.value)
					//resolve("success")
				} catch (error) {
					// 如果事件处理出错，手动拒绝Promise
					reject(error)
				}
			})
		} finally {
			btnLoading.value = false
		}
	} else {
		emits("onClosed")
	}
}
//获取数据字典
const dictOptions = ref<Record<string, any[]>>({
	INVENTORY_UNIT: [],
	MATERIAL_NATURE: []
})
function getDictByCodeList(): Promise<null> {
	return new Promise((resolve) => {
		DictApi.getDictByCodeList(dictOptions)
			.then((res) => {
				dictOptions.value = res
			})
			.finally(() => {
				resolve(null)
			})
	})
}

const treeLoading = ref(false)
const defaultStatus = matStatus.freeze + "," + matStatus.normal
onMounted(() => {
	treeLoading.value = true
	Promise.all([getDictByCodeList()])
		.then(() => {
			treeCheck([], defaultStatus)
		})
		.finally(() => (treeLoading.value = false))
})

// 分页
defineOptions({
	name: "MatManualManagement"
})
</script>
<template>
	<div class="app-container">
		<ModelFrame class="left-frame">
			<TreeMatType
				v-loading="treeLoading"
				:title="leftTitle"
				:status="defaultStatus"
				:needSwitch="false"
				:defaultExpandedKeys="props.defaultExpandedKeys"
				@onTreeCheck="treeCheck"
			/>
		</ModelFrame>
		<div class="right-frame">
			<Title :title="rightTitle" />
			<ModelFrame class="whole-frame">
				<Query
					:queryArrList="queryConf"
					@getQueryData="getQueryData"
					style="margin: 10px 10px 0"
				/>
				<el-scrollbar>
					<PitayaTable
						ref="tableRef"
						:columns="tableProp"
						:table-data="tableData"
						:total="pageTotal"
						@onSelectionChange="onDataSelected"
						@on-current-page-change="onCurrentPageChange"
						:table-loading="tableLoading"
						:need-index="true"
						:need-pagination="true"
						:single-select="false"
						:need-selection="true"
					>
						<template #buyUnit="{ rowData }">
							<dict-tag
								:options="dictOptions.INVENTORY_UNIT"
								:value="rowData.buyUnit"
							/>
						</template>
						<template #evaluation="{ rowData }">
							<cost-tag :value="rowData.evaluation" />
						</template>

						<template #attribute="{ rowData }">
							<dict-tag
								:options="DictApi.getMatAttr()"
								:value="rowData.attribute"
							/>
						</template>
					</PitayaTable>
				</el-scrollbar>
				<!--维护采购信息-->
			</ModelFrame>

			<ButtonList
				style="padding-right: 10px"
				class="footer end"
				:button="formBtnList"
				:loading="btnLoading"
				@on-btn-click="onFormBtnList"
			/>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.app-container {
	padding: 0px $---spacing-m;
}
.left-frame,
.whole-frame {
	padding: 0px;
	border: none;
}
.left-frame {
	padding-right: $---spacing-m;
}
.right-frame {
	padding-left: $---spacing-m;
	border-left: solid 1px $---border-color;
}
.whole-frame {
}
/* :deep(.common-query-wrapper) {
	padding-top: 10px;
	padding-left: 10px;
} */
:deep(.pitaya-table) {
	padding-top: 0px;
	margin-top: 0px;
}
.common-btn-list-wrapper {
	border-top: solid 1px $---border-color;
	padding-top: $---spacing-m;
}
</style>
