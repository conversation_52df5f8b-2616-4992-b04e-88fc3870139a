<!-- 计划 - 合并计划 新建/编辑 页 -->
<script lang="ts" setup>
import { ElMessage, type FormInstance, type FormRules } from "element-plus"
import TableFile from "../../components/tableFile.vue"
import { FormElementType } from "@/app/baseline/views/components/define"
import PlanMatItem from "@/app/baseline/views/plan/components/merge/tableMatItem.vue"
import FormElement from "@/app/baseline/views/components/formElement.vue"
import { DictApi, fileBusinessType } from "@/app/baseline/api/dict"
import { PlanMerge } from "@/app/baseline/views/plan/components/define"
import TableNeedPlan from "@/app/baseline/views/plan/components/merge/tableNeedPlan.vue"
import { BaseLineSysApi } from "@/app/baseline/api/system"
import { PlanMergeApi } from "@/app/baseline/api/plan/planMerge"
import { useUserStore } from "@/app/platform/store/modules/user"
import { watch, ref, onMounted, reactive } from "vue"
import XEUtils from "xe-utils"
import { useDictInit } from "@/app/baseline/views/components/dictBase.ts"

const { dictOptions, getDictByCodeList } = useDictInit()

const props = defineProps({
	id: {
		type: [String, Number],
		required: false
	}
})
const emits = defineEmits(["onSaveOrClose", "onSelectChange"])

/**
 * 保存旧的表单数据
 */
const oldFormData = ref<string>()

const currentId = ref(props.id)
const loading = ref(false)
const drawerLeftTitle = {
	name: props.id ? ["编辑合并计划"] : ["新建合并计划"],
	icon: ["fas", "square-share-nodes"]
}
const drawerRightTitle = {
	name: ["扩展信息"],
	icon: ["fas", "square-share-nodes"]
}

const refForm = ref<FormInstance>()
const formBtnList = [
	{ name: "取消", icon: ["fas", "circle-minus"] },
	{ name: "保存", icon: ["fas", "floppy-disk"] }
]
const formBtnListRight = computed(() => [
	{
		name: "提交审核",
		icon: ["fas", "circle-check"],
		disabled: formModal.value.planNum > 0 ? false : true
	}
])
//表单默认值
const futureYears = DictApi.getFutureYears(-1, 1)
const userStore = useUserStore()
const { userInfo } = storeToRefs(userStore)
const formModal = ref<Record<string, any>>({
	id: currentId.value,
	year: futureYears[1].value,
	sysOrgId: userInfo.value.orgId,
	sysOrgId_view: userInfo.value.orgName,
	bpmStatus: "0"
})
//表单定义
const formEl = computed<FormElementType[][]>(() => [
	[
		{
			label: "合并计划ID",
			name: "id",
			maxlength: 50,
			disabled: currentId.value ? true : false,
			type: "hidden"
		}
	],
	[
		{
			label: "计划年度",
			name: "year",
			type: "year",
			format: "YYYY",
			valueFormat: "YYYY",
			disabledDate: (time) => {
				const year = time.getFullYear()
				return (
					year < new Date().getFullYear()
				)
			},
			data: futureYears,
			disabled: !!currentId.value
		}
	],
	[
		{
			label: "合并计划名称",
			name: "label",
			maxlength: 50
		}
	],
	[
		{
			label: "线路",
			name: "lineNoId_view",
			vname: "lineNoId",
			type: "treeSelect",
			disabled: currentId.value ? true : false,
			treeApi: BaseLineSysApi.getLineList
		}
	],
	[
		{
			label: "所属专业",
			name: "majorId_view",
			vname: "majorId",
			type: "treeSelect",
			disabled: currentId.value ? true : false,
			treeApi: BaseLineSysApi.getProfessionTree
		}
	],
	[
		{
			label: "段区",
			name: "depotId_view",
			vname: "depotId",
			type: "treeSelect",
			disabled: currentId.value ? true : false,
			treeApi: BaseLineSysApi.getSegmentList
		}
	],
	[
		{
			label: "费用类别",
			name: "expenseCategory_view",
			vname: "expenseCategory",
			type: "treeSelect",
			disabled: currentId.value ? true : false,
			treeApi: () => Promise.resolve(dictOptions.value.COST_CATEGORY)
		}
	],
	[
		{
			label: "合并部门",
			name: "sysOrgId_view",
			vname: "sysOrgId",
			disabled: true
		}
	],
	[
		{
			label: "需求计划数量",
			name: "planNum",
			disabled: true
		}
	],
	[
		{
			label: "物资数量",
			name: "num",
			disabled: true
		}
	],
	[
		{
			label: "预估采购金额",
			name: "purchaseAmount_view",
			disabled: true
		}
	],
	[
		{
			label: "备注说明",
			name: "remark",
			maxlength: 200,
			rows: "3",
			type: "textarea"
		}
	]
])
const formRules = reactive<FormRules<typeof formModal.value>>({
	year: [{ required: true, message: "计划年度不能为空", trigger: "change" }],
	label: [
		{ required: true, message: "合并计划名称不能为空", trigger: "change" }
	],
	expenseCategory_view: [
		{ required: true, message: "费用类别不能为空", trigger: "change" }
	]
})
const childTableLoading = ref(false)
const drawerLoading = ref(false)
const segmentList = ref<{ [propName: string]: any }[]>([])

const getInfoById = () => {
	drawerLoading.value = true
	PlanMergeApi.getPlanMerge(currentId.value as any)
		.then((res: any) => {
			Object.assign(formModal.value, res)
			//处理字段
			const found = segmentList.value.find(
				(item) => item.id == formModal.value["depotId"]
			)
			if (found) {
				formModal.value["depotId_view"] = found.label
			}
			const tmpAmount = `${XEUtils.commafy(res.purchaseAmount, { digits: 2 })}`
			formModal.value["purchaseAmount_view"] = tmpAmount
				? `￥${tmpAmount}`
				: "---"
			formModal.value.year = res.year + ""
			oldFormData.value = JSON.stringify(formModal.value)
		})
		.finally(() => {
			drawerLoading.value = false
		})
}
//通知主列表刷新
const onSelectChange = () => {
	PlanMergeApi.getPlanMerge(currentId.value as any).then((res: any) => {
		formModal.value.planNum = res.planNum
		formModal.value.num = res.num
		const tmpAmount = `${XEUtils.commafy(res.purchaseAmount, { digits: 2 })}`
		formModal.value["purchaseAmount_view"] = tmpAmount
			? `￥${tmpAmount}`
			: "---"
	})
	emits("onSelectChange")
}
// 左侧按钮点击
const onSaveDraft = () => {
	return new Promise<void>((resolve, reject) => {
		if (refForm.value) {
			refForm.value.validate((valid) => {
				if (valid) {
					drawerLoading.value = true

					let savePromise
					if (currentId.value) {
						//保存完之后 要重新赋值，原来为空
						savePromise = PlanMergeApi.updatePlanMerge({
							...formModal.value,
							id: currentId.value as unknown as number
						} as any).then((res: Record<string, any>) => {
							formModal.value.label = res.label

							oldFormData.value = JSON.stringify(formModal.value)
						})
					} else {
						savePromise = PlanMergeApi.addPlanMerge({
							...formModal.value
						}).then((res: Record<string, any>) => {
							currentId.value = res.id
							formModal.value.label = res.label
							formModal.value.id = res.id

							oldFormData.value = JSON.stringify(formModal.value)
						})
					}
					savePromise
						.then(() => {
							resolve()
						})
						.catch((error) => {
							reject(error)
						})
						.finally(() => {
							drawerLoading.value = false
						})
				} else {
					reject("Validation failed")
				}
			})
		}
	})
}
const refTableNeedPlan = ref("")
const onFormBtnList = async (btnName: string | undefined) => {
	if (!refForm.value) return
	if (btnName === "保存") {
		loading.value = true
		onSaveDraft()
			.then(() => {
				ElMessage.success("保存成功，您可以继续其他操作")
				// 触发 onSaveOrClose 事件
				emits("onSaveOrClose", "save")
			})
			.catch((error) => {
				console.error("Save draft failed:", error)
			})
			.finally(() => {
				loading.value = false
			})
	}
	if (btnName === "提交审核") {
		//先查询是否已有需求计划
		if (
			!formModal.value["planNum"] ||
			parseInt(formModal.value["planNum"]) <= 0
		) {
			ElMessage.error("需选择需求计划才可以提交审核")
			return false
		}

		drawerLoading.value = true
		loading.value = true
		try {
			// 如果主表有修改，则先更新主表数据
			if (oldFormData.value != JSON.stringify(formModal.value)) {
				await PlanMergeApi.updatePlanMerge({
					...formModal.value,
					id: currentId.value as unknown as number
				} as any)
			}

			await PlanMergeApi.publishApply(formModal.value as any)
			ElMessage.success("提交审核成功")
			emits("onSaveOrClose", "pub")
		} finally {
			drawerLoading.value = false
			loading.value = false
		}
	}
	if (btnName === "取消") {
		refForm.value?.clearValidate()
		emits("onSaveOrClose", "cancel")
		return
	}
}

//扩展栏标签页切换
const tabList = ["需求计划", "物资明细", "相关附件"]
const activeTab = ref<number>(0)
const handleTabChange = (index: number) => {
	activeTab.value = index
}

function getSegmentList() {
	BaseLineSysApi.getSegmentList().then((res) => {
		segmentList.value = res
	})
}
onMounted(async () => {
	Promise.all([getDictByCodeList(["COST_CATEGORY"]), getSegmentList()]).then(
		() => {
			if (props.id) {
				getInfoById()
			}
		}
	)
})
/* watch(
	[() => formModal.planNum],
	([planNum], [oldPlanNum]) => {
		formEl.value[1][0].disabled = planNum > 0
		formBtnListRight[0].disabled = !(planNum > 0)
	},
	{ immediate: true }
) */
defineOptions({
	name: "MergePlanDrawer"
})
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<!-- 左侧表单区域 -->
		<div class="drawer-column left">
			<div class="rows">
				<Title :title="drawerLeftTitle" />
				<el-scrollbar>
					<el-form
						style="padding-bottom: 30px"
						class="content"
						:model="formModal"
						:rules="formRules"
						ref="refForm"
						label-position="top"
						label-width="100px"
					>
						<FormElement :form-element="formEl" :form-data="formModal" />
					</el-form>
				</el-scrollbar>
			</div>
			<ButtonList
				class="footer"
				:button="formBtnList"
				:loading="loading"
				@on-btn-click="onFormBtnList"
			/>
		</div>
		<!-- 右侧table区域 -->
		<div
			class="drawer-column right"
			:class="
				currentId ? 'drawer-column right' : 'drawer-column right disabled'
			"
		>
			<div class="rows">
				<Title :title="drawerRightTitle">
					<Tabs class="tabs" :tabs="tabList" @on-tab-change="handleTabChange" />
				</Title>
				<div v-if="activeTab === 0">
					<TableNeedPlan
						:need-query="false"
						:need-opt="['view', 'drop']"
						:table-loading="childTableLoading"
						:id="currentId"
						:merge-plan-info="formModal"
						ref="refTableNeedPlan"
						@on-select-change="onSelectChange"
					/>
				</div>
				<div v-if="activeTab === 1">
					<PlanMatItem
						:table-loading="childTableLoading"
						model="m-edit"
						:id="currentId"
						type="merge"
					/>
				</div>
				<div v-if="activeTab === 2">
					<TableFile
						:table-loading="childTableLoading"
						:business-type="fileBusinessType.mergePlan"
						:business-id="currentId"
					/>
				</div>
			</div>
			<ButtonList
				class="footer"
				:button="formBtnListRight"
				:loading="loading"
				@on-btn-click="onFormBtnList"
			/>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.drawer-container {
	.left {
		width: 310px;
	}

	.right {
		width: calc(100% - 310px);
	}
}
</style>
