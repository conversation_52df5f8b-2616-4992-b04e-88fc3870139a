<script lang="ts" setup>
import { ref, reactive, toValue, onBeforeMount } from "vue"
import { ElMessage, type FormInstance, type FormRules } from "element-plus"
import { FormElementType } from "@/app/baseline/views/components/define"
import { BaseLineSysApi } from "@/app/baseline/api/system"
import { NeedApi } from "@/app/baseline/api/plan/need"
import { useDictInit } from "@/app/baseline/views/components/dictBase"

import FormElement from "@/app/baseline/views/components/formElement.vue"
import TableMatItem from "@/app/baseline/views/plan/components/need/tableMatItem.vue"
export interface Props {
	id?: string
	model?: string
}

const { dictOptions, getDictByCodeList } = useDictInit()
const props = withDefaults(defineProps<Props>(), {
	id: "",
	model: "view"
})

const emits = defineEmits(["onSuccess", "onClosed", "onDataChange"])
const currentId = ref("")

const loading = ref(false)
const drawerLeftTitle = ref({
	name: ["新建需求清单"],
	icon: ["fas", "square-share-nodes"]
})
const drawerRightTitle = {
	name: ["扩展信息"],
	icon: ["fas", "square-share-nodes"]
}

const formBtnList = ref([{ name: "取消", icon: ["fas", "circle-minus"] }])
const tabList = ["清单明细"]

const formModal = reactive({
	id: props.id,
	code: "",
	label: "",
	matCodeNum: 0,
	majorId: "",
	majorId_view: "",
	costCategoryId: "",
	costCategoryId_view: "",
	remark: "",
	createUser: "",
	createTime: "",
	expenseCategory: "",
	expenseCategory_view: "",
	createdBy_view: "",
	createdDate: ""
})
const formEl: FormElementType[][] = [
	[{ label: "需求清单名称", name: "label", maxlength: 50 }],
	[
		{
			label: "所属专业",
			name: "majorId_view",
			vname: "majorId",
			type: "treeSelect",
			treeApi: BaseLineSysApi.getProfessionTree
		}
	],
	[
		{
			label: "费用类别",
			name: "expenseCategory_view",
			vname: "expenseCategory",
			type: "treeSelect",
			treeApi: () => Promise.resolve(dictOptions.value.COST_CATEGORY)
		}
	],
	[
		{
			label: "备注说明",
			name: "remark",
			maxlength: 200,
			rows: "3",
			type: "textarea"
		}
	]
]
const ruleFormRef = ref<FormInstance>()
const formRules = reactive<FormRules<typeof formModal>>({
	label: [
		{ required: true, message: "需求清单名称不能为空", trigger: "change" }
	],
	expenseCategory: [
		{ required: true, message: "费用类别不能为空", trigger: "change" }
	],
	majorId_view: [
		{ required: true, message: "所属专业不能为空", trigger: "change" }
	]
})

const drawerLoading = ref(false)
const getNeedInfo = () => {
	drawerLoading.value = true
	NeedApi.getInfoById(currentId.value)
		.then((res: any) => {
			Object.assign(formModal, res)
		})
		.finally(() => {
			drawerLoading.value = false
		})
}
// 左侧按钮点击
const onFormBtnList = (btnName: string | undefined) => {
	if (btnName === "保存") {
		if (ruleFormRef.value) {
			ruleFormRef.value.validate((valid) => {
				if (valid) {
					loading.value = true
					drawerLoading.value = true
					const params: { [propName: string]: any } = toValue(formModal)
					if (currentId.value) {
						params.id = currentId.value
						delete params.code
						NeedApi.updateNeed(params)
							.then((_r) => {
								Object.assign(formModal, _r)
								ElMessage.success("修改成功")
								emits("onSuccess")
							})
							.finally(() => {
								loading.value = false
								drawerLoading.value = false
							})
					} else {
						NeedApi.addNeed(params)
							.then((_r: any) => {
								Object.assign(formModal, _r)
								currentId.value = _r.id
								ElMessage.success("请继续添加清单明细")
								emits("onSuccess")
							})
							.finally(() => {
								loading.value = false
								drawerLoading.value = false
							})
					}
				} else {
					return false
				}
			})
		}
	} else emits("onClosed")
}

//扩展栏标签页切换
const activeTab = ref<number>(0)
const handleTabChange = (index: number) => {
	activeTab.value = index
}

function onMatChange() {
	// getNeedInfo();
	emits("onDataChange")
}
onBeforeMount(() => {
	Promise.all([getDictByCodeList(["COST_CATEGORY"])]).then(() => {
		if (props.model == "edit") {
			formBtnList.value.push({ name: "保存", icon: ["fas", "floppy-disk"] })
		}
		if (props.id) {
			currentId.value = props.id
			getNeedInfo()
			drawerLeftTitle.value.name = ["需求清单信息"]
		}
	})
})

defineOptions({
	name: "CompanyDrawer"
})
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<!-- 左侧表单区域 -->
		<div class="drawer-column left">
			<div class="rows">
				<Title :title="drawerLeftTitle" />
				<el-scrollbar>
					<el-form
						class="content"
						:model="formModal"
						:rules="formRules"
						ref="ruleFormRef"
						label-position="top"
						label-width="100px"
						v-if="props.model == 'edit'"
					>
						<FormElement :form-element="formEl" :form-data="formModal" />
					</el-form>

					<el-descriptions
						v-else
						size="small"
						:column="1"
						:border="true"
						class="content"
					>
						<el-descriptions-item label="需求清单号">{{
							formModal.code || "---"
						}}</el-descriptions-item>
						<el-descriptions-item label="需求清单名称">{{
							formModal.label || "---"
						}}</el-descriptions-item>
						<el-descriptions-item label="物资编码数量">{{
							formModal.matCodeNum || "---"
						}}</el-descriptions-item>
						<el-descriptions-item label="所属专业">{{
							formModal.majorId_view || "---"
						}}</el-descriptions-item>
						<el-descriptions-item label="费用类别">{{
							formModal.expenseCategory_view || "---"
						}}</el-descriptions-item>
						<el-descriptions-item label="备注说明">{{
							formModal.remark || "---"
						}}</el-descriptions-item>
						<el-descriptions-item label="创建人">{{
							formModal.createdBy_view || "---"
						}}</el-descriptions-item>
						<el-descriptions-item label="创建时间">{{
							formModal.createdDate || "---"
						}}</el-descriptions-item>
					</el-descriptions>
				</el-scrollbar>
				<ButtonList
					class="footer"
					:button="formBtnList"
					:loading="loading"
					@on-btn-click="onFormBtnList"
				/>
			</div>
		</div>
		<!-- 右侧table区域 -->
		<div class="drawer-column right" :class="currentId ? '' : 'disabled'">
			<div class="rows">
				<Title :title="drawerRightTitle">
					<Tabs class="tabs" :tabs="tabList" @on-tab-change="handleTabChange" />
				</Title>
				<div>
					<TableMatItem
						:id="currentId"
						:model="props.model"
						@onDataChange="onMatChange"
					/>
				</div>
			</div>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.drawer-container {
	.left {
		width: 310px;
	}

	.right {
		width: calc(100% - 310px);
	}

	.el-scrollbar {
		height: calc(100% - 73px);
	}
}
</style>
