<script lang="ts" setup>
import { ref, onMounted, reactive, toValue, onBeforeMount } from "vue"
import { ElMessage, type FormInstance, type FormRules } from "element-plus"
import { MatApply<PERSON>pi } from "@/app/baseline/api/material/matApply"
import { FormElementType } from "@/app/baseline/views/components/define"
import { DictApi, fileBusinessType } from "@/app/baseline/api/dict"
import { BaseLineSysApi } from "@/app/baseline/api/system"
import { PlanApi } from "@/app/baseline/api/plan/plan"
import { useDictInit } from "@/app/baseline/views/components/dictBase.ts"
import XEUtils from "xe-utils"

import TableMatItem from "@/app/baseline/views/plan/components/plan/tableMatItem.vue"
import FormElement from "@/app/baseline/views/components/formElement.vue"
import TableFile from "../../components/tableFile.vue"
import CostTag from "@/app/baseline/views/components/costTag.vue"
import { useUserStore } from "@/app/platform/store/modules/user"
import { omit } from "lodash-es"
import { IModalType } from "@/app/baseline/utils/types/common"

const { dictOptions, getDictByCodeList } = useDictInit()
//当前用户信息
const userStore = useUserStore()
const { userInfo } = storeToRefs(userStore)
const currentId = ref<string>("")

interface Props {
	id: string
	model: string
	type: string
	withNumber?: boolean
	viewButton?: boolean
	year?: any
	matId?: string
}
const props = withDefaults(defineProps<Props>(), {
	id: "",
	model: "view",
	type: "",
	withNumber: true,
	viewButton: true,
	year: {}
})
const emits = defineEmits(["onSuccess", "onClosed", "onDataChange"])

const loading = ref(true)
const drawerLeftTitle = ref({
	name: [""],
	icon: ["fas", "square-share-nodes"]
})
const drawerRightTitle = {
	name: ["扩展信息"],
	icon: ["fas", "square-share-nodes"]
}
const formBtnList = ref([
	// { name: "取消", icon: ["fas", "circle-minus"] },
	// { name: "保存草稿", icon: ["fas", "file-signature"] },
	{ name: "提交审核", icon: ["fas", "circle-check"], disabled: true }
])
const tabList = ref(["物资明细", "相关附件"])

const baseFormBtnList = ref([{ name: "取消", icon: ["fas", "circle-minus"] }])

//编辑表单
const formModal = reactive({
	id: props.id,
	planYearId: "",
	code: "",
	year: DictApi.getFutureYears(-1, 1)[2].value,
	planType: props.type,
	label: "",
	lineNoId: "",
	majorId: "",
	depotId: "",
	expenseCategory: "",
	matCodeNum: "0",
	purchaseAmount: "0",
	sysOrgId: "",
	sysOrgId_view: "",
	remark: "",
	playType: "",
	depotId_view: "",
	lineNoId_view: "",
	majorId_view: "",
	expenseCategory_view: "",
	createdBy_view: "",
	createdDate: ""
})

const formElNormal: FormElementType[][] = reactive([
	[
		{
			label: "计划年度",
			name: "year",
			type: "year",
			format: "YYYY",
			valueFormat: "YYYY",
			disabledDate: (time) => {
				const year = time.getFullYear()
				return year < new Date().getFullYear()
			},
			data: DictApi.getFutureYears(-1, 1),
			disabled:
				currentId.value?.length > 0 ||
				props.type == "1" ||
				(props.type == "2" && props.id!=='' && props.model == "edit")
		}
	],
	[
		{
			label: "计划类型",
			name: "planType",
			type: "select",
			data: [],
			disabled: true
		}
	],
	[
		{
			label: "需求计划名称",
			name: "label",
			maxlength: 50
		}
	],
	[
		{
			label: "线路",
			name: "lineNoId_view",
			vname: "lineNoId",
			maxlength: 50,
			type: "treeSelect",
			needSingleSelect: true,
			treeApi: BaseLineSysApi.getLineList
		}
	],
	[
		{
			label: "所属专业",
			name: "majorId_view",
			vname: "majorId",
			type: "treeSelect",
			treeApi: BaseLineSysApi.getProfessionTree
		}
	],
	[
		{
			label: "段区",
			name: "depotId_view",
			vname: "depotId",
			type: "treeSelect",
			treeApi: BaseLineSysApi.getSegmentList,
			placeholder: "请选择段区"
		}
	],
	[
		{
			label: "费用类别",
			name: "expenseCategory_view",
			vname: "expenseCategory",
			type: "treeSelect",
			treeApi: () => Promise.resolve(dictOptions.value.COST_CATEGORY)
		}
	],
	[
		{
			label: "物资编码数量",
			name: "matCodeNum",
			maxlength: 200,
			disabled: true
		}
	],
	[
		{
			label: "预估采购金额",
			name: "purchaseAmount_view",
			type: "money",
			disabled: true
		}
	],
	[
		{
			label: "需求部门",
			name: "sysOrgId_view",
			vname: "sysOrgId",
			maxlength: 50,
			type: "treeSelect",
			treeApi: BaseLineSysApi.getDepartmentTree,
			disabled: true
		}
	],

	[
		{
			label: "需求原因",
			name: "remark",
			maxlength: 200,
			rows: "3",
			type: "textarea"
		}
	]
])

const formElLess: FormElementType[][] = reactive([
	[
		{
			label: "计划年度",
			name: "year",
			type: "year",
			disabledDate: (date) => {
				return date < new Date(XEUtils.toDateString(new Date(), "yyyy"))
			},
			disabled: currentId.value
		}
	],
	[
		{
			label: "计划类型",
			name: "planType",
			type: "select",
			data: [],
			disabled: true
		}
	],
	[
		{
			label: "需求计划名称",
			name: "label",
			maxlength: 50
		}
	]
])
const formEl: FormElementType[][] = reactive([])
const formRules = reactive<FormRules<typeof formModal>>({
	label: [
		{ required: true, message: "需求计划名称不能为空", trigger: "change" }
	],
	lineNoId_view: [
		{ required: true, message: "线路不能为空", trigger: "change" }
	],
	majorId_view: [
		{ required: true, message: "所属专业不能为空", trigger: "change" }
	],
	expenseCategory_view: [
		{ required: true, message: "费用类别不能为空", trigger: "change" }
	],
	remark: [{ required: true, message: "需求原因不能为空", trigger: "change" }]
})

const drawerLoading = ref(false)
const ruleFormRef = ref<FormInstance>()
const subjectTableData = ref<any[]>([])
const refFileTable = ref<any>()
const costSubject = ref(false)
const playTypes = ref<{ [propName: string]: any }[]>([])
const currentPlanType = ref("")
const getSegmentList = ref<{ [propName: string]: any }[]>([])

/**
 * 保存旧的表单数据
 */
const oldFormData = ref<string>()

const getMatApplyInfo = (updParams?: string[]) => {
	drawerLoading.value = true
	PlanApi.getInfoById(currentId.value)
		.then((res: any) => {
			res.year = res.year.toString()
			res.purchaseAmount = `${XEUtils.commafy(res.purchaseAmount, {
				digits: 2
			})}`
			res.purchaseAmount_view = `￥${res.purchaseAmount}`
			if (updParams && updParams?.length > 0) {
				Object.keys(res)
					.filter((_k) => updParams.includes(_k))
					.map((_k) => {
						formModal[_k] = res[_k]
					})
			} else {
				Object.assign(formModal, res)
				currentPlanType.value = res.planType
				if (props.type?.length < 1) {
					formModal.playType = currentPlanType.value
					let _typeName =
						playTypes.value.find(
							(_d) => _d.subitemValue == currentPlanType.value
						)?.subitemName || ""
					drawerLeftTitle.value.name = [`${_typeName}信息`]
				}
			}

			let _btnPublish = formBtnList.value?.find((_b) => _b.name == "提交审核")
			if (_btnPublish) {
				if (res.matCodeNum > 0) {
					_btnPublish.disabled = false
				} else _btnPublish.disabled = true
			}

			formModal.depotId_view = getSegmentList.value.find(
				(_d) => _d.value == formModal.depotId
			)?.label

			oldFormData.value = JSON.stringify(formModal)
		})
		.finally(() => {
			drawerLoading.value = false
		})
}

//读取费用类别
function getCostSubject(): Promise<any> {
	return new Promise((resolve) => {
		if (costSubject.value) resolve(costSubject.value)
		else {
			BaseLineSysApi.getCostSubject()
				.then((_r) => {
					costSubject.value = _r
				})
				.finally(() => {
					resolve(costSubject.value)
				})
		}
	})
}

// 左侧按钮点击
const onFormBtnList = async (btnName: string | undefined) => {
	if (btnName === "保存") {
		if (ruleFormRef.value) {
			ruleFormRef.value.validate(async (valid) => {
				loading.value = true
				if (valid) {
					drawerLoading.value = true

					if (currentId.value) {
						try {
							const r = await PlanApi.updatePlan(
								omit(
									{ ...formModal, id: currentId.value },
									"purchaseAmount",
									"code"
								) as any
							)
							Object.assign(formModal, r)
							ElMessage.success("修改成功")

							oldFormData.value = JSON.stringify(formModal)

							emits("onSuccess")
						} finally {
							loading.value = false
							drawerLoading.value = false
						}
					} else {
						try {
							const r = await PlanApi.addPlan(
								omit({ ...formModal }, "purchaseAmount", "code") as any
							)

							Object.assign(formModal, r)

							currentId.value = formModal.id

							ElMessage.success("保存成功")

							oldFormData.value = JSON.stringify(formModal)

							emits("onSuccess")
						} finally {
							loading.value = false
							drawerLoading.value = false
						}
					}
				} else {
					loading.value = false
					return false
				}
			})
		}
	} else if (btnName == "提交审核") {
		drawerLoading.value = true
		try {
			// 如果主表有修改，则先更新主表数据
			if (oldFormData.value != JSON.stringify(formModal)) {
				await PlanApi.updatePlan(
					omit(
						{ ...formModal, id: currentId.value },
						"purchaseAmount",
						"code"
					) as any
				)
			}

			await PlanApi.publishPlan(currentId.value)
			ElMessage.success("提交成功")
			emits("onSuccess", true)
		} finally {
			drawerLoading.value = false
		}
	} else {
		emits("onClosed")
	}
}

//扩展栏标签页切换
const activeTab = ref<number>(0)
const handleTabChange = (index: number) => {
	activeTab.value = index
}

//计划类别
function initPlanType(): Promise<void> {
	return new Promise((resolve) => {
		DictApi.getDictByCode("PLAN_CATEGORY")
			.then((res) => {
				formEl[1][0].data = res
				playTypes.value = res
				formModal.playType = currentPlanType.value
				let _typeName =
					res.find((_d) => _d.subitemValue == currentPlanType.value)
						?.subitemName || ""
				if (props.model == "edit") {
					if (props.id) {
						drawerLeftTitle.value.name = [`编辑${_typeName}`]
					} else {
						drawerLeftTitle.value.name = [`新建${_typeName}`]
					}
				} else {
					drawerLeftTitle.value.name = [`${_typeName}信息`]
				}
			})
			.finally(() => {
				resolve()
			})
	})
}

//费用类型
function initCostSubject(): Promise<void> {
	return new Promise((resolve) => {
		BaseLineSysApi.getCostSubject()
			.then((_r) => {
				formEl[6][0].data = _r
			})
			.finally(() => resolve())
	})
}

//段区
function initSegment(): Promise<void> {
	return new Promise((resolve) => {
		BaseLineSysApi.getSegmentList().then((_r) => {
			getSegmentList.value = _r
		})
		resolve()
	})
}

function onMatListChange() {
	getMatApplyInfo(["matCodeNum", "purchaseAmount_view"])
	emits("onDataChange")
}

onBeforeMount(() => {
	if (props.id) {
		currentId.value = props.id
	} else {
		formModal.sysOrgId = userInfo.value.orgId
		formModal.sysOrgId_view = userInfo.value.orgName
	}
	currentPlanType.value = props.type
	if (currentPlanType.value == "3") {
		tabList.value = ["采购项目", "物资明细"]
		formEl.push(...formElLess)
	} else formEl.push(...formElNormal)
	if (props.model == "edit") {
		baseFormBtnList.value.push({ name: "保存", icon: ["fas", "floppy-disk"] })
	} else {
		formBtnList.value = []
	}
	if (props.type === "1" && !props.id) {
		console.log(props.year)
		formModal.planYearId = props.year.id
		formModal.year = props.year.year
	}
	Promise.all([initPlanType(), initCostSubject(), initSegment()]).then(() => {
		loading.value = false
	})
})
onMounted(() => {
	Promise.all([getDictByCodeList(["COST_CATEGORY"])]).then(() => {
		if (props.id) {
			currentId.value = props.id
			getMatApplyInfo()
		}
	})
})
</script>
<template>
	<div
		class="needplan-draw drawer-container"
		v-loading="drawerLoading"
		:class="{ withNum: props.withNumber }"
	>
		<!-- 左侧表单区域 -->
		<div class="drawer-column left" v-loading="loading">
			<div class="rows">
				<Title :title="drawerLeftTitle" />
				<el-scrollbar>
					<el-form
						class="content"
						:model="formModal"
						:rules="formRules"
						ref="ruleFormRef"
						label-position="top"
						label-width="100px"
						v-if="!loading && model == 'edit'"
					>
						<FormElement :form-element="formEl" :form-data="formModal" />
					</el-form>
					<el-descriptions
						v-if="!loading && model == 'view'"
						size="small"
						:column="1"
						border
						class="content"
					>
						<el-descriptions-item label="需求计划号">{{
							formModal.code || "---"
						}}</el-descriptions-item>
						<el-descriptions-item label="需求计划名称">{{
							formModal.label || "---"
						}}</el-descriptions-item>
						<template v-if="currentPlanType != '3'">
							<el-descriptions-item label="计划年度">{{
								formModal.year || "---"
							}}</el-descriptions-item>
							<el-descriptions-item label="计划类型">{{
								playTypes.find((_d) => _d.subitemValue == formModal.playType)
									?.subitemName || "---"
							}}</el-descriptions-item>
							<el-descriptions-item label="物资编码数量">{{
								formModal.matCodeNum || "---"
							}}</el-descriptions-item>
							<el-descriptions-item label="预估采购金额"
								><CostTag :value="formModal.purchaseAmount"
							/></el-descriptions-item>
							<el-descriptions-item label="线路">{{
								formModal.lineNoId_view || "---"
							}}</el-descriptions-item>
							<el-descriptions-item label="专业">{{
								formModal.majorId_view || "---"
							}}</el-descriptions-item>
							<el-descriptions-item label="段区">{{
								getSegmentList.find((_d) => _d.value == formModal.depotId)
									?.label || "---"
							}}</el-descriptions-item>
							<el-descriptions-item label="费用类别">{{
								formModal.expenseCategory_view || "---"
							}}</el-descriptions-item>
							<el-descriptions-item label="需求原因">{{
								formModal.remark || "---"
							}}</el-descriptions-item>
							<el-descriptions-item label="需求部门">{{
								formModal.sysOrgId_view || "---"
							}}</el-descriptions-item>
						</template>
						<template v-else>
							<el-descriptions-item label="需求原因">{{
								formModal.remark || "---"
							}}</el-descriptions-item>
						</template>
						<el-descriptions-item label="创建人">{{
							formModal.createdBy_view || "---"
						}}</el-descriptions-item>
						<el-descriptions-item label="创建时间">{{
							formModal.createdDate || "---"
						}}</el-descriptions-item>
					</el-descriptions>
				</el-scrollbar>
				<!-- 非临时计划 查看详情 -->
				<ButtonList
					v-if="
						props.model != IModalType.view &&
						viewButton &&
						baseFormBtnList.length > 0
					"
					class="footer"
					:button="baseFormBtnList"
					:loading="loading"
					@on-btn-click="onFormBtnList"
				/>
			</div>
		</div>
		<!-- 右侧table区域 -->
		<div class="drawer-column right" :class="currentId ? '' : 'disabled'">
			<div class="rows">
				<Title :title="drawerRightTitle">
					<Tabs class="tabs" :tabs="tabList" @on-tab-change="handleTabChange" />
				</Title>
				<div
					class="detail-table"
					v-show="currentPlanType == '3' && activeTab === 0"
				>
					<el-empty description="功能建设中..." />
				</div>
				<div
					class="detail-table"
					v-show="
						(currentPlanType == '3' && activeTab === 1) ||
						(currentPlanType != '3' && activeTab === 0)
					"
				>
					<TableMatItem
						:id="currentId"
						:model="props.model"
						:withNumber="props.withNumber"
						:majorId="formModal.majorId"
						:currentPlanType="currentPlanType"
						:matId="props.matId"
						@onDataChange="onMatListChange"
						detailModel="view"
					/>
				</div>
				<div
					class="detail-table"
					v-show="currentPlanType != '3' && activeTab === 1"
				>
					<TableFile
						ref="refFileTable"
						v-if="currentId"
						:mod="props.model"
						:businessId="currentId"
						:businessType="fileBusinessType.needPlan"
						:needPage="true"
					/>
				</div>
			</div>
			<ButtonList
				v-if="formBtnList.length > 0"
				class="footer"
				:button="formBtnList"
				:loading="loading"
				@on-btn-click="onFormBtnList"
			/>

			<!-- 临时计划 查看详情 -->
			<ButtonList
				v-else-if="viewButton && baseFormBtnList.length > 0"
				class="footer"
				:button="baseFormBtnList"
				:loading="loading"
				@on-btn-click="onFormBtnList"
			/>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.drawer-container {
	width: 100%;
	&.withNum {
		width: 1600px;
	}
	.left {
		width: 300px;
		.el-scrollbar {
			height: calc(100% - 73px);
		}
		.content {
			width: 100%;
			.el-form {
				width: calc(100% - 13px);
			}
		}
	}
	.right {
		width: calc(100% - 300px);
		.detail-table {
			width: 100%;
			height: 100%;
		}
	}
}
</style>
