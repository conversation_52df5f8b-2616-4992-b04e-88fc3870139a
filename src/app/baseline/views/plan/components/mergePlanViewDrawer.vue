<script lang="ts" setup>
import {toRef,ref,onMounted,reactive} from 'vue'
import TableFile from "../../components/tableFile.vue"
import PlanMatItem from "@/app/baseline/views/plan/components/merge/tableMatItem.vue"
import { FormElementType } from "@/app/baseline/views/components/define"
import { DictApi, fileBusinessType } from "@/app/baseline/api/dict"
import { PlanMerge } from "@/app/baseline/views/plan/components/define"
import TableNeedPlan from "@/app/baseline/views/plan/components/merge/tableNeedPlan.vue"
import { PlanMergeApi } from "@/app/baseline/api/plan/planMerge"
import CostTag from "@/app/baseline/views/components/costTag.vue"
import { BaseLineSysApi } from "@/app/baseline/api/system"

const props = defineProps({
	id: {
		type: [String, Number],
		required: false
	},
	modal: {
		type: String,
		required: false,
		default: "" //"mat" 以物资视角浏览
	}
})
const emits = defineEmits(["onSaveOrClose"])
const currentId = toRef(props, "id")
const loading = ref(false)
const drawerLeftTitle = {
	name: ["合并计划信息"],
	icon: ["fas", "square-share-nodes"]
}
const drawerRightTitle = {
	name: ["扩展信息"],
	icon: ["fas", "square-share-nodes"]
}

const formBtnList = [{ name: "取消", icon: ["fas", "circle-minus"] }]
const formModal = reactive<PlanMerge>({
	id: props.id
})
const formEl: FormElementType[] = [
	{
		label: "合并计划号",
		name: "code"
	},
	{
		label: "合并计划名称",
		name: "label"
	},
	{
		label: "计划年度",
		name: "year",
		data: DictApi.getFutureYears(-1,1)
	},
	{
		label: "需求计划数量",
		name: "planNum"
	},
	{
		label: "物资数量",
		name: "num"
	},
	{
		label: "预估采购金额",
		name: "purchaseAmount",
		type: "money"
	},
	{ label: "线路", name: "lineNoId_view" },
	{ label: "专业", name: "majorId_view" },
	{ label: "段区", name: "depotId" },
	{
		label: "费用类别",
		name: "expenseCategory_view",
		type: "select",
		data: []
	},
	{
		label: "备注说明",
		name: "remark",
		type: "textarea"
	},
	{
		label: "合并部门",
		name: "sysOrgId_view"
	},
	{
		label: "创建人",
		name: "createdBy_view"
	},
	{
		label: "创建时间",
		name: "createdDate"
	}
]

const childTableLoading = ref(false)
const drawerLoading = ref(false)
const getInfoById = () => {
	return new Promise<void>((resolve) => {
		drawerLoading.value = true
		PlanMergeApi.getPlanMerge(currentId.value)
			.then((res: any) => {
				Object.assign(formModal, res)
			})
			.finally(() => {
				drawerLoading.value = false
				resolve()
			})
	})
}
// 左侧按钮点击
const onFormBtnList = (btnName: string | undefined) => {
	if (btnName === "取消") {
		emits("onSaveOrClose", false)
		return
	}
}

//扩展栏标签页切换
const tabList = ref<string[]>(["需求计划", "物资清单", "相关附件"])
const activeTab = ref<number>(0)
const handleTabChange = (index: number) => {
	activeTab.value = index
}
defineOptions({
	name: "viewDrawer"
})

//获取字典
const dictOptions = ref<Record<string, any[]>>({
	COST_CATEGORY: []
})
function getDictByCodeList(): Promise<void> {
	return new Promise((resolve) => {
		DictApi.getDictByCodeList(dictOptions)
			.then((res) => {
				dictOptions.value = res
			})
			.finally(() => {
				resolve()
			})
	})
}
const segmentList = ref<[]>([])
function getSegmentList() {
	BaseLineSysApi.getSegmentList().then((res) => {
		segmentList.value = res
	})
}

onMounted(async () => {
	if (props.modal === "mat") {
		tabList.value = ["物资明细", "相关附件"]
	}
	Promise.all([getSegmentList(), getInfoById()]).then(() => {
		const found : {[propName:string]:any} = segmentList.value.find(
			(item : {[propName:string]:any}) => item.id == formModal["depotId"]
		) || {}
		if (found) {
			formModal["depotId"] = found.label;
		}
	})
})
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<!-- 左侧表单区域 -->
		<div class="drawer-column left">
			<div class="rows">
				<Title :title="drawerLeftTitle" />
				<el-descriptions size="small" :column="1" :border="true" class="content">
					<el-descriptions-item
						v-for="(el, index) in formEl"
						label-align="center"
						:label="el.label"
						:key="index"
					>
						<span v-if="el.type == 'money'">
							<cost-tag :value="formModal[el.name]" />
						</span>
						<span v-else>
							{{ formModal[el.name] ? formModal[el.name] : "---" }}
						</span>
					</el-descriptions-item>
				</el-descriptions>
			</div>
		</div>
		<!-- 右侧table区域 -->
		<div class="drawer-column right">
			<div class="rows">
				<Title :title="drawerRightTitle">
					<Tabs class="tabs" :tabs="tabList" @on-tab-change="handleTabChange" />
				</Title>
				<!-- 仅显示 物资明细m-edit模式 和附件 -->
				<template v-if="props.modal === 'mat'">
					<div v-show="activeTab === 0">
						<PlanMatItem model="m-edit" :id="currentId" type="merge" />
					</div>
					<div v-show="activeTab === 1">
						<TableFile
							mod="view"
							:table-loading="childTableLoading"
							:business-type="fileBusinessType.mergePlan"
							:business-id="currentId"
						/>
					</div>
				</template>
				<!-- 显示需求清单、 物资明细mp-view模式 和附件 -->
				<template v-else>
					<div v-if="activeTab === 0">
						<TableNeedPlan
							:need-query="false"
							:need-opt="['view']"
							:table-loading="childTableLoading"
							:id="currentId"
						/>
					</div>
					<div v-if="activeTab === 1" class="tab-mat">
						<PlanMatItem
							model="mp-view"
							:id="currentId"
							type="merge"
							:need-query="true"
						/>
					</div>
					<div v-if="activeTab === 2">
						<TableFile
							mod="view"
							:table-loading="childTableLoading"
							:business-type="fileBusinessType.mergePlan"
							:business-id="currentId"
						/>
					</div>
				</template>
			</div>
			<ButtonList
          v-if="false"
				class="footer"
				:button="formBtnList"
				:loading="loading"
				@on-btn-click="onFormBtnList"
			/>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index";

.drawer-container {
	.left {
		width: 310px;
	}
	.right {
		width: calc(100% - 310px);
	}
	.tab-mat {
		height: 100%;
	}
}
</style>
