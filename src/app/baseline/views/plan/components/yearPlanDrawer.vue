<script lang="ts" setup>
import { ref, reactive, toValue, onBeforeMount, watchEffect } from "vue"
import { ElMessage, type FormInstance, type FormRules } from "element-plus"
import { FormElementType } from "@/app/baseline/views/components/define"
import { YearPlanApi } from "@/app/baseline/api/plan/yearPlan"
import FormElement from "@/app/baseline/views/components/formElement.vue"
import { IEndWindowPhase } from "@/app/baseline/api/defines"
import { getIdempotentToken } from "@/app/baseline/utils/validate"
import {
	IIdempotentTokenType,
	IIdempotentTokenTypePre
} from "@/app/baseline/utils/types/common"
export interface Props {
	id?: string
	model?: string
	editModel?: any
}
const props = withDefaults(defineProps<Props>(), {
	id: "",
	model: "view",
	editModel: null
})

const emits = defineEmits(["onSuccess", "onClosed"])
const currentId = ref("")

const loading = ref(false)
const drawerLeftTitle = computed(() => ({
	name: [
		props.model === "edit"
			? props.id
				? "编辑年度计划"
				: "新建年度计划"
			: "查看年度计划"
	],
	icon: ["fas", "square-share-nodes"]
}))

const formBtnList = ref([
	{ name: "取消", icon: ["fas", "circle-minus"] },
	{ name: "保存", icon: ["fas", "floppy-disk"] }
])

const formModal = reactive({
	id: props.id,
	year: "",
	label: "",
	beginDate: "",
	endDate: "",
	endWindowPhase: "",
	dateArray: []
})
const formEl: FormElementType[][] = [
	[
		{
			label: "计划年度",
			name: "year",
			type: "year",
			format: "YYYY",
			valueFormat: "YYYY",
			disabledDate: (time) => {
				const year = time.getFullYear()
				return year < new Date().getFullYear()
			}
		}
	],
	/* [
		{
			label: "计划填报开始日期",
			name: "beginDate",
			type: "date",
			disabledDate: (time) => {
				return time.getTime() < Date.now() - 8.64e7
			}
		}
	],
	[
		{
			label: "计划填报截止日期",
			name: "endDate",
			type: "date"
		}
	], */
	[
		{
			label: "计划填报日期",
			name: "dateArray",
			type: "daterange",
			startPlaceholder: "开始日期",
			endPlaceholder: "结束日期",
			valueFormat: "YYYY-MM-DD",
			disabledDate: (time: any) => {
				return time.getTime() < Date.now() - 8.64e7
			}
		}
	],
	[
		{
			label: "年度计划类型",
			name: "endWindowPhase",
			type: "select",
			data: [
				{ label: "需求计划", value: IEndWindowPhase.NeedPlan },
				{ label: "采购计划", value: IEndWindowPhase.PurchasePlan }
			]
		}
	],
	[{ label: "年度计划名称", name: "label", maxlength: 50 }]
]

const ruleFormRef = ref<FormInstance>()
const formRules = reactive<FormRules<typeof formModal>>({
	label: [{ required: true, message: "年度计划名称不能为空", trigger: "blur" }],
	year: [{ required: true, message: "计划年度不能为空", trigger: "change" }],
	dateArray: [
		{ required: true, message: "计划填报日期不能为空", trigger: "change" }
	],
	/* endDate: [
		{ required: true, message: "计划填报截止日期不能为空", trigger: "change" }
	], */
	endWindowPhase: [
		{ required: true, message: "年度计划类型不能为空", trigger: "change" }
	]
})

const drawerLoading = ref(false)
// 左侧按钮点击
const onFormBtnList = (btnName: string | undefined) => {
	if (btnName === "保存") {
		if (ruleFormRef.value) {
			ruleFormRef.value.validate((valid) => {
				if (valid) {
					loading.value = true
					drawerLoading.value = true

					formModal.beginDate = formModal.dateArray[0]
					formModal.endDate = formModal.dateArray[1]

					const params: { [propName: string]: any } = toValue(formModal)
					if (currentId.value) {
						params.id = currentId.value
						YearPlanApi.updatePlan(params)
							.then((_r) => {
								ElMessage.success("修改成功")
								emits("onSuccess")
							})
							.finally(() => {
								loading.value = false
								drawerLoading.value = false
							})
					} else {
						const idempotentToken = getIdempotentToken(
							IIdempotentTokenTypePre.apply,
							IIdempotentTokenType.planYearPlan
						)
						YearPlanApi.addPlan(params, idempotentToken)
							.then((_r) => {
								ElMessage.success("新增成功")
								emits("onSuccess")
							})
							.finally(() => {
								loading.value = false
								drawerLoading.value = false
							})
					}
				} else {
					return false
				}
			})
		}
	} else emits("onClosed")
}

watchEffect(() => {
	if (props.model != "edit") {
		formBtnList.value.pop()
	}
})

onBeforeMount(() => {
	if (props.id) {
		currentId.value = props.id
	}
	if (props.editModel) {
		formModal.year = props.editModel.year
		formModal.beginDate = props.editModel.beginDate
		formModal.endDate = props.editModel.endDate
		formModal.label = props.editModel.label
		formModal.endWindowPhase = props.editModel.endWindowPhase
		formModal.dateArray = (
			props.editModel.beginDate && props.editModel.endDate
				? [props.editModel.beginDate, props.editModel.endDate]
				: []
		) as any[]
	}
})

const formatData = (val: any) => {
	if (!val) return ""
	return val.split(" ")[0]
}

defineOptions({
	name: "YearPlanDrawer"
})
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<!-- 左侧表单区域 -->
		<div class="drawer-column year_left">
			<div class="rows">
				<Title :title="drawerLeftTitle" />
				<div class="year-content">
					<el-form
						class="content"
						:model="formModal"
						:rules="formRules"
						ref="ruleFormRef"
						label-position="top"
						label-width="100px"
						v-if="props.model == 'edit'"
					>
						<FormElement :form-element="formEl" :form-data="formModal" />
					</el-form>

					<el-descriptions
						v-else
						size="small"
						:column="1"
						:border="true"
						class="content long_label"
					>
						<el-descriptions-item label="计划年度">{{
							formModal.year || "---"
						}}</el-descriptions-item>
						<el-descriptions-item label="计划填报开始日期">{{
							formatData(formModal.beginDate) || "---"
						}}</el-descriptions-item>
						<el-descriptions-item label="计划填报截止日期">{{
							formatData(formModal.endDate) || "---"
						}}</el-descriptions-item>
						<el-descriptions-item label="年度计划类型">
							{{
								formModal.endWindowPhase === IEndWindowPhase.NeedPlan
									? "需求计划"
									: formModal.endWindowPhase === IEndWindowPhase.PurchasePlan
									? "采购计划"
									: "---"
							}}
						</el-descriptions-item>
						<el-descriptions-item label="年度计划名称">{{
							formModal.label || "---"
						}}</el-descriptions-item>
					</el-descriptions>
				</div>
				<ButtonList
					class="footer"
					:button="formBtnList"
					:loading="loading"
					@on-btn-click="onFormBtnList"
				/>
			</div>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.year_left {
	width: 310px;

	.year-content {
		height: calc(100% - 73px);
	}

	:deep(.long_label .el-descriptions__label) {
		width: 220px !important;
	}
}
</style>
