<!-- 计划 - 采购计划 新增/编辑 页 -->
<script lang="ts" setup>
import { ref, onMounted, reactive } from "vue"
import { ElMessage, type FormInstance, type FormRules } from "element-plus"
import TableFile from "../../components/tableFile.vue"
import { FormElementType } from "@/app/baseline/views/components/define"
import FormElement from "@/app/baseline/views/components/formElement.vue"
import { DictApi, fileBusinessType } from "@/app/baseline/api/dict"
import { PlanMerge } from "@/app/baseline/views/plan/components/define"
import TableMergePlan from "@/app/baseline/views/plan/components/purchase/tableMergePlan.vue"
import { useUserStore } from "@/app/platform/store/modules/user"
import { PlanPurchaseApi } from "@/app/baseline/api/plan/planPurchase"
import TableMatItem from "@/app/baseline/views/plan/components/purchase/tableMatItem.vue"
import XEUtils from "xe-utils"
import checkNeedNumDrawer from "./purchase/checkNeedNumDrawer.vue"

const props = defineProps({
	id: {
		type: [String, Number],
		required: false
	}
})
const emits = defineEmits(["onSaveOrClose", "onSelectChange"])

/**
 * 保存旧的表单数据
 */
const oldFormData = ref<string>()

const currentId = ref(props.id)
const loading = ref(false)
const drawerLeftTitle = {
	name: props.id ? ["编辑采购计划"] : ["新建采购计划"],
	icon: ["fas", "square-share-nodes"]
}
const drawerRightTitle = {
	name: ["扩展信息"],
	icon: ["fas", "square-share-nodes"]
}

const refForm = ref<FormInstance>()
const formBtnList = [
	{ name: "取消", icon: ["fas", "circle-minus"] },
	{ name: "保存", icon: ["fas", "floppy-disk"] }
]
const formBtnListRight = reactive([
	{ name: "提交审核", icon: ["fas", "circle-check"], disabled: true }
])
//表单默认值
const futureYears = DictApi.getFutureYears(-1, 1)
const userStore = useUserStore()
const { userInfo } = storeToRefs(userStore)
const formModal = reactive<Record<string, any>>({
	id: props.id,
	year: futureYears[1].value,
	planType: "1",
	bpmStatus: "0",
	pushEcommerceStatus: "0"
})
const formEl = reactive<FormElementType[][]>([
	[
		{
			label: "采购计划ID",
			name: "id",
			maxlength: 50,
			disabled: true,
			type: "hidden"
		}
	],
	[
		{
			label: "计划年度",
			name: "year",
			type: "year",
			format:'YYYY',
			valueFormat:'YYYY',
			disabledDate: (time) => {
				const year = time.getFullYear();  
				return year < new Date().getFullYear() ;  
			},
			disabled: props.id?true:false,
		}
	],
	[
		{
			label: "计划类型",
			name: "planType",
			type: "select",
			clear: true,
			disabled: true,
			data: []
		}
	],
	[
		{
			label: "采购计划名称",
			name: "label",
			maxlength: 50
		}
	],
	[
		{
			label: "预估采购金额（元）",
			name: "purchaseAmount_view",
			disabled: true
		}
	],
	[
		{
			label: "备注说明",
			name: "remark",
			maxlength: 200,
			rows: "5",
			type: "textarea"
		}
	]
])
const formRules = reactive<FormRules<typeof formModal>>({
	year: [{ required: true, message: "计划年度不能为空", trigger: "change" }],
	label: [
		{ required: true, message: "采购计划名称不能为空", trigger: "change" }
	]
})

const childTableLoading = ref(false)
const drawerLoading = ref(false)
const getInfoById = () => {
	if (currentId.value) {
		drawerLoading.value = true
		PlanPurchaseApi.getPlanPurchase(currentId.value as any)
			.then((res: any) => {
				Object.assign(formModal, res)
				const tmpAmount = `${XEUtils.commafy(res.purchaseAmount, {
					digits: 2
				})}`
				formModal["purchaseAmount_view"] = tmpAmount ? `￥${tmpAmount}` : "---"
				formModal.year=res.year+''
				oldFormData.value = JSON.stringify(formModal)
			})
			.finally(() => {
				drawerLoading.value = false
			})
	}
}
const curMergCntTotal = ref(0)
const onSetMergeCnt = (total: any) => {
	curMergCntTotal.value = total
	//formEl[1][0].disabled = total > 0
	formBtnListRight[0].disabled = !(total > 0)
}
//通知主列表刷新
const onSelectChange = () => {
	//发生变化，通知刷新
	PlanPurchaseApi.getPlanPurchase(currentId.value).then((res: any) => {
		const tmpAmount = `${XEUtils.commafy(res.purchaseAmount, { digits: 2 })}`
		formModal["purchaseAmount_view"] = tmpAmount ? `￥${tmpAmount}` : "---"
	})
	emits("onSelectChange") //通知主界面刷新
}
// 左侧按钮点击
const onSaveDraft = () => {
	return new Promise<void>((resolve, reject) => {
		if (refForm.value) {
			refForm.value.validate((valid) => {
				if (valid) {
					drawerLoading.value = true

					// 处理一些字段
					let savePromise
					if (currentId.value) {
						//params.id = currentId.value
						savePromise = PlanPurchaseApi.updatePlanPurchase({
							...formModal,
							id: currentId.value
						} as any).then((res) => {
							formModal.label = res.label

							oldFormData.value = JSON.stringify(formModal)
							//	formEl[1][0].disabled = true
						})
					} else {
						savePromise = PlanPurchaseApi.addPlanPurchase({
							...formModal
						}).then((res: Record<string, any>) => {
							currentId.value = res.id
							formModal.label = res.label
							formModal.id = res.id

							oldFormData.value = JSON.stringify(formModal)
							//	formEl[1][0].disabled = true
						})
					}
					savePromise
						.then(() => {
							resolve()
						})
						.catch((error) => {
							reject(error)
						})
						.finally(() => {
							drawerLoading.value = false
						})
				} else {
					reject("Validation failed")
				}
			})
		}
	})
}
const refTableMergePlan = ref("")
const onFormBtnList = async (btnName: string | undefined) => {
	if (btnName === "保存") {
		loading.value = true

		onSaveDraft()
			.then(() => {
				ElMessage.success("保存成功，您可以继续其他操作")
				// 触发 onSaveOrClose 事件
				emits("onSaveOrClose", "save")
			})
			.catch((error) => {
				console.error("Save draft failed:", error)
			})
			.finally(() => {
				loading.value = false
			})
	}
	if (btnName === "提交审核") {
		if (curMergCntTotal.value <= 0) {
			ElMessage.error("需选择合并计划计划才可以提交审核")
			return false
		}

		loading.value = true
		try {
			// 如果主表有修改，则先更新主表数据
			if (oldFormData.value != JSON.stringify(formModal)) {
				await PlanPurchaseApi.updatePlanPurchase({
					...formModal,
					id: currentId.value
				} as any)
			}

			const checkNum = await PlanPurchaseApi.updatePlanPurchaseCheckNum(
				formModal.id
			)

			/**
			 * 判断采购数量是否等于需求数量
			 * 存在不相等的则返回false；
			 * 都相等时true
			 */
			if (!checkNum) {
				// 不相等时 填原因
				checkNumDialogVisible.value = true
			} else {
				await PlanPurchaseApi.publishApply(formModal)
				ElMessage.success("提交审核成功")
				emits("onSaveOrClose", "pub")
			}
		} finally {
			loading.value = false
		}
	}
	if (btnName === "取消") {
		refForm.value?.clearValidate()
		emits("onSaveOrClose", "cancel")
		return
	}
}

//扩展栏标签页切换
const tabList = ["合并计划", "物资明细", "相关附件"]
const activeTab = ref<number>(0)
const handleTabChange = (index: number) => {
	activeTab.value = index
}
//获取字典
const dictOptions = ref<Record<string, any[]>>({
	PLAN_CATEGORY: []
})

function getDictByCodeList(): Promise<void> {
	return new Promise((resolve) => {
		DictApi.getDictByCodeList(dictOptions)
			.then((res) => {
				dictOptions.value = res
			})
			.finally(() => {
				resolve()
			})
	})
}

onMounted(async () => {
	Promise.all([getDictByCodeList()]).then(() => {
		formEl[2][0].data = dictOptions.value.PLAN_CATEGORY
		if (props.id) {
			getInfoById()
		}
	})
})

const checkNumDialogVisible = ref(false)

/**
 * 需求变更原因 保存
 * @param reason
 */
async function handleCheckNumReason(reason: any) {
	await PlanPurchaseApi.updatePlanPurchase({ id: formModal.id, reason: reason })

	await PlanPurchaseApi.publishApply(formModal)

	checkNumDialogVisible.value = false
	ElMessage.success("提交审核成功")
	emits("onSaveOrClose", "pub")
}

defineOptions({
	name: "purchasePlanDrawer"
})
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<!-- 左侧表单区域 -->
		<div class="drawer-column left">
			<div class="rows">
				<Title :title="drawerLeftTitle" />
				<el-scrollbar>
					<el-form
						class="content"
						style="padding-bottom: 30px"
						:model="formModal"
						:rules="formRules"
						ref="refForm"
						label-position="top"
						label-width="100px"
					>
						<FormElement :form-element="formEl" :form-data="formModal" />
					</el-form>
				</el-scrollbar>
			</div>
			<ButtonList
				class="footer"
				:button="formBtnList"
				:loading="loading"
				@on-btn-click="onFormBtnList"
			/>
		</div>
		<!-- 右侧table区域 -->
		<div
			class="drawer-column right"
			:class="
				currentId ? 'drawer-column right' : 'drawer-column right disabled'
			"
		>
			<div class="rows">
				<Title :title="drawerRightTitle">
					<Tabs class="tabs" :tabs="tabList" @on-tab-change="handleTabChange" />
				</Title>
				<div v-if="activeTab === 0">
					<TableMergePlan
						:need-opt="true"
						:purchase-plan-id="currentId"
						:purchase-plan-year="formModal['year']"
						ref="refTableMergePlan"
						@on-select-change="onSelectChange"
						@on-set-merge-cnt="onSetMergeCnt"
					/>
				</div>
				<div v-if="activeTab === 1">
					<TableMatItem
						:table-loading="childTableLoading"
						model="p-edit"
						type="purchase"
						:singleSelected="false"
						:id="currentId"
					/>
				</div>
				<div v-if="activeTab === 2">
					<TableFile
						:table-loading="childTableLoading"
						:business-type="fileBusinessType.purchasePlan"
						:business-id="currentId"
					/>
				</div>
			</div>
			<ButtonList
				class="footer"
				:button="formBtnListRight"
				:loading="loading"
				@on-btn-click="onFormBtnList"
			/>
		</div>

		<!-- 校验采购数量是否等于需求数量; 填写原因 -->
		<check-need-num-drawer
			v-model:dialog="checkNumDialogVisible"
			@close="checkNumDialogVisible = false"
			@save="handleCheckNumReason"
		/>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.drawer-container {
	.left {
		width: 310px;
	}

	.right {
		width: calc(100% - 310px);
	}
}
</style>
