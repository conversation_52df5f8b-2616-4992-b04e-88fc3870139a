<script setup lang="ts">
import { watch, onMounted } from "vue"
import { useTbInit } from "@/app/baseline/views/components/tableBase"
import CostTag from "@/app/baseline/views/components/costTag.vue"
// import { PlanMergeItemApi } from "@/app/baseline/api/plan/planMergeItem"
import { PlanPurchaseMaterialItemApi } from "@/app/baseline/api/plan/planPurchaseMaterialItem"
import { toFixedTwo } from "@/app/baseline/utils"

//根据物资ID查看关联的需求计划列表
interface Props {
	matId: number | string
	id: number | string //业务ID
	type: string //merge 、 purchase
}
const props = defineProps<Props>()
const {
	tableProp,
	tableData,
	tableLoading,
	currentPage,
	pageTotal,
	fetchParam,
	tbBtns,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = useTbInit()

//选中的table Row
tableProp.value = [
	{ label: "合并计划号", prop: "code", width: 160, fixed: "left" },
	{ label: "合并计划名称", prop: "label", width: 100 },
	{ label: "合并部门", prop: "sysOrgId_view" },
	{ label: "需求数量", prop: "num", align: "right", needSlot: true, width: 75 },
	{
		label: "预估采购金额",
		prop: "purchaseAmount",
		needSlot: true,
		width: 120,
		align: "right"
	}
]

tbBtns.value = []
function getTableData(data?: { [propName: string]: any }) {
	// 	//获取一个采购计划 物资对应得合并计划
	fetchParam.value = { purchasePlanId: props.id, materialId: props.matId }
	fetchFunc.value =
		PlanPurchaseMaterialItemApi.getPlanPurchaseMaterialItemMergePlanList
	currentPage.value = 1
	fetchParam.value = {
		...fetchParam.value,
		...data
	}
	fetchTableData()
}
watch(
	() => props.matId,
	(id: any) => {
		if (id) {
			getTableData()
		}
	},
	{ immediate: false }
)
onMounted(() => {})
</script>
<template>
	<div>
		<PitayaTable
			ref="refTable"
			:columns="tableProp"
			:table-data="tableData"
			:need-index="true"
			:need-pagination="false"
			:single-select="false"
			:need-selection="false"
			:total="pageTotal"
			@on-current-page-change="onCurrentPageChange"
			:table-loading="tableLoading"
		>
			<template #num="{ rowData }">
				{{ toFixedTwo(rowData.num, 0) }}
			</template>

			<template #purchaseAmount="{ rowData }">
				<cost-tag :value="rowData.purchaseAmount" />
			</template>
		</PitayaTable>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
