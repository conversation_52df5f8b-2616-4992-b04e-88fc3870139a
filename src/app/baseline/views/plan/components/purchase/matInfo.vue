<!-- 采购计划：下钻查看 只显示跟当前物资相关的信息 -->
<script setup lang="ts">
import { PlanApi } from "@/app/baseline/api/plan/plan"
import CostTag from "../../../components/costTag.vue"
import { useDictInit } from "../../../components/dictBase"
import { PlanPurchaseMaterialItemApi } from "@/app/baseline/api/plan/planPurchaseMaterialItem"
import { ref, onMounted } from "vue"
import { toFixedTwo } from "@/app/baseline/utils"

export interface Props {
	planId: string | number // 计划Id
	materialId: string | number // 物资Id
}
const props = defineProps<Props>()

const emit = defineEmits<{
	(e: "close"): void
}>()

const { dictFilter, getDictByCodeList } = useDictInit()

const drawerLoading = ref(false)
const descData = ref()

/**
 * 左侧title 配置
 */
const leftTitleConf = computed(() => ({
	name: ["物资信息"],
	icon: ["fas", "square-share-nodes"]
}))

/**
 * 右侧title 配置
 */
const rightTitleConf = {
	name: ["月度需求信息"],
	icon: ["fas", "square-share-nodes"]
}

/**
 * 关联物资数据
 */
const descList = ref([
	{ label: "物资编码", key: "content_code" },
	{ label: "物资名称", key: "content_label" },
	{ label: "物资分类编码", key: "content_materialTypeCode" },
	{ label: "物资分类名称", key: "content_materialTypeLabel" },
	{ label: "规格型号", key: "content_version" },
	{ label: "技术参数", key: "content_technicalParameter" },
	{ label: "采购单位", key: "content_buyUnit" },
	{ label: "预估采购单价", key: "evaluation" },
	{ label: "需求数量", key: "num_view" },
	{ label: "预估采购金额", key: "purchaseAmount" }
])

/**
 * 获取月度需求信息
 */
async function getMonthInfoList() {
	const res = await PlanApi.getPlanItemMonthlyList({
		planItemId: descData.value.id,
		pageSize: 99
	})

	res.rows.map((v: any) => {
		const _monthNum = parseInt(v.month) - 1
		formData.value[_monthNum] = v.num ?? 0
	})
}
/**
 * 获取物资详情信息
 */
async function getDetail() {
	drawerLoading.value = true
	try {
		const r = await PlanPurchaseMaterialItemApi.getByPlanIdAndMaterialId({
			planId: props.planId as number,
			materialId: props.materialId as number
		})

		if (r.content) {
			Array.from(Object.keys(r?.content))?.map(
				(v) => (r[`content_${v}`] = r.content[v])
			)
		}

		descData.value = r
		descData.value.num_view = toFixedTwo(r.num, 0)

		getMonthInfoList()
	} finally {
		drawerLoading.value = false
	}
}

const formData = ref(new Array(12).fill(0))

const btnConf = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	}
]
onMounted(() => {
	getDictByCodeList(["INVENTORY_UNIT"])
	getDetail()
})
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<!-- 左侧表单区域 -->
		<div class="drawer-column left" style="width: 310px">
			<Title :title="leftTitleConf" />
			<el-scrollbar>
				<el-descriptions size="small" :column="1" border class="content">
					<template>
						<el-descriptions-item
							v-for="desc in descList"
							:key="desc.key"
							:label="desc.label"
						>
							<!-- 预估采购单价 -->
							<cost-tag
								v-if="desc.key === 'evaluation'"
								:value="descData?.evaluation"
							/>
							<!-- 预估采购金额 -->
							<cost-tag
								v-else-if="desc.key === 'purchaseAmount'"
								:value="descData?.purchaseAmount"
							/>
							<span v-else-if="desc.key === 'content_buyUnit'">
								{{
									dictFilter("INVENTORY_UNIT", descData?.content_buyUnit)
										?.subitemName || "---"
								}}
							</span>
							<span v-else>
								{{ descData?.[desc.key] }}
							</span>
						</el-descriptions-item>
					</template>
				</el-descriptions>
			</el-scrollbar>
		</div>
		<!-- 右侧table区域 -->
		<div class="drawer-column right" style="width: calc(100% - 310px)">
			<div class="rows">
				<Title :title="rightTitleConf" />
				<el-scrollbar class="request-form">
					<div class="request-title">
						<div>
							<span>{{ descData?.num }}</span>
							<label>需求总量</label>
						</div>
					</div>
					<div class="request-list">
						<div
							class="list-item"
							v-for="(value, index) in formData"
							:key="index"
						>
							<span>{{ (index + 1).toString().padStart(2, "0") }}</span>
							<label>{{ formData[index] }}</label>
							<!-- <el-input-number
								v-else
								v-model="formData[index]"
								:max="needInfo.num - numSum + formData[index]"
								:min="0"
								:disabled="props.model == 'view'"
							/> -->
						</div>
					</div>
				</el-scrollbar>
			</div>

			<button-list
				class="footer"
				:button="btnConf"
				@on-btn-click="emit('close')"
			/>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
$--input-font-size: 20px;
.request-form {
	width: 100%;
	span {
		color: $---color-info;
		font-size: 30px;
		font-weight: bold;
	}
	.request-title {
		display: flex;
		padding: $---spacing-m;
		div {
			display: flex;
			flex-direction: column;
			width: 100%;
			align-items: center;
		}
		label {
			font-size: 12px;
		}
	}
	.request-list {
		display: flex;
		flex-direction: column;
		align-items: center;
		.list-item {
			display: flex;
			justify-content: center;
			width: 280px;
			padding-top: $---spacing-m;
			> * {
				height: 50px;
				line-height: 50px;
			}
			span {
				border: solid 1px $---border-color;
				border-radius: $---border-radius-m 0px 0px $---border-radius-m;
				text-align: center;
				width: 80px;
				background: $---color-background;
				&:after {
					content: "月";
					pading-left: $---spacing-s;
					color: $---color-info;
					font-size: $---font-size-m;
				}
			}
			label {
				width: 115px;
				border: solid 1px $---border-color;
				border-radius: 0px $---border-radius-m $---border-radius-m 0px;
				border-left: none;
				font-size: calc($--input-font-size + 10px);
				text-align: center;
				color: $---color-info;
			}
		}
	}
}
</style>
