<script setup lang="ts">
import { ref, onMounted, nextTick } from "vue"
import { DictApi } from "@/app/baseline/api/dict"
import { defineProps } from "vue"
import { useTbInit } from "@/app/baseline/views/components/tableBase.ts"
import CostTag from "@/app/baseline/views/components/costTag.vue"
import MatDetail from "@/app/baseline/views/plan/components/purchase/matDetail.vue"
import { PlanPurchaseMaterialItemApi } from "@/app/baseline/api/plan/planPurchaseMaterialItem"
import MatNeedPlan from "@/app/baseline/views/plan/components/merge/matNeedPlan.vue"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import {
	batchFormatterNumView,
	convertContentTableData,
	toFixedTwo
} from "@/app/baseline/utils"
import { PlanMergeItemApi } from "@/app/baseline/api/plan/planMergeItem"
import { map, debounce, findIndex, floor } from "lodash-es"
import { isValidateInt } from "@/app/baseline/utils/validate"

/**
 * 合并计划  新建和编辑时  物资明细(只有列表）
 *          查看时物资明细（左右结构，查询 + 列表 + 单物资的关联需求计划)
 * 采购计划  新建和编辑时  物资明细（列表 + 关联需求计划弹出层 + 一键平衡利库）
 *          查看时物资明细（同合并计划）
 */
export interface Props {
	id: string | number //合并计划ID，采购划ID
	type: string //merge 、 purchase
	model?: string //显示模式  m-edit 列表: , p-edit 列表+ 关联需求计划，mp-view:列表 + 右侧关联需求计划
	needQuery?: boolean
	needSelection?: boolean
	selectFirst?: boolean
	singleSelected?: boolean
	tableEndBtn?: object
}

const props = withDefaults(defineProps<Props>(), {
	id: "",
	type: "merge",
	model: "list",
	needQuery: false,
	needSelection: false,
	selectFirst: true,
	singleSelected: true
})
const needSelection = props.model == "mp-view" || props.needSelection
const emits = defineEmits(["onDataChange", "onTableEndBtn"])

/*-------------------初始化表格-start-------------------*/
const {
	tableCache,
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	tbBtnLoading,
	pageSize,
	currentPage,
	pageTotal,
	selectedTableList,
	fetchParam,
	tbBtns,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange,
	onDataSelected,
	onBtnClick
} = useTbInit()

const tableGroup = {
	base: [
		{ label: "物资编码", prop: "code", width: 130, fixed: "left" },
		{ label: "物资名称", prop: "label", width: 150 },
		{ label: "物资分类编码", prop: "materialTypeCode", width: 100 },
		{
			label: "物资分类名称",
			prop: "materialTypeLabel",
			width: 150
		},
		{ label: "规格型号", prop: "version", width: 120 },
		{
			label: "技术参数",
			prop: "technicalParameter",
			minWidth: 200,
			align: "left"
		},
		{ label: "采购单位", prop: "buyUnit", needSlot: true, width: 90 },
		{
			label: "预估采购单价",
			prop: "evaluation",
			needSlot: true,
			width: 150,
			align: "right"
		}
	],
	other: [
		{
			label: "需求数量",
			prop: "num_view",
			align: "right",
			width: 90,
			fixed: "right"
		},
		{
			label: "预估采购金额",
			prop: "purchaseAmount",
			width: 120,
			needSlot: true,
			fixed: "right",
			align: "right"
		}
	],
	pEdit: [
		{
			label: "需求数量",
			prop: "num_view",
			align: "right",
			needSlot: true,
			width: 90
		},
		{
			label: "预估金额",
			prop: "planAmount",
			needSlot: true,
			width: 120,
			align: "right"
		},
		{
			label: "库存数量",
			prop: "storeNum",
			needSlot: true,
			align: "right",
			width: 90
		},
		{ label: "安全库存", prop: "safeStock", width: 90, align: "right" },
		{
			label: "在途数量",
			prop: "inTransitNum",
			needSlot: true,
			width: 90,
			align: "right"
		},
		{
			label: "采购数量",
			prop: "purchaseNum_view",
			needSlot: true,
			width: 180,
			fixed: "right"
		},
		{
			label: "预估采购金额",
			prop: "purchaseAmount",
			needSlot: true,
			width: 120,
			fixed: "right",
			align: "right"
		}
	],
	pView: [
		{
			label: "需求数量",
			prop: "num_view",
			needSlot: true,
			width: 90,
			align: "right",
			fixed: "right"
		},
		{
			label: "采购数量",
			prop: "purchaseNum_view",
			needSlot: true,
			width: 90,
			fixed: "right"
		},
		{
			label: "预估采购金额",
			prop: "purchaseAmount",
			needSlot: true,
			width: 120,
			fixed: "right",
			align: "right"
		}
	]
}
tbBtns.value = props.tableEndBtn ? props.tableEndBtn : []

/*-------------------初始化表格-end-------------------*/

const queryArrList = [
	{
		name: "物资编码",
		key: "materialCode",
		placeholder: "请输入物资编码",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资名称",
		key: "materialLabel",
		placeholder: "请输入物资名称",
		enableFuzzy: true,
		type: "input"
	}
]
const needPlanTitle = {
	name: ["关联需求计划"],
	icon: ["fas", "square-share-nodes"]
}
const lastTableData = ref<any[]>([])
const curMatId = ref<any>()
const curMatData = ref<any>()

const tableEndBtnMatItem = [
	{
		name: "平衡利库",
		icon: ["fas", "sliders"],
		click: () => handleBtnBalance()
	}
]

const handleBtnBalance = () => {
	const params = objectToFormData({
		planPurchaseId: props.id
	})
	tableLoading.value = true

	const ids = map(selectedTableList.value, ({ id }) => id)
	if (ids.length < 1) {
		tableLoading.value = false
		ElMessage.error("请选择物资明细！")
		return false
	}
	PlanPurchaseMaterialItemApi.balancePurchaseMaterialItem(ids)
		.then((res) => {
			getTableData()
			ElMessage.success("平衡利库成功")
			tableLoading.value = false
		})
		.finally(() => {
			tableLoading.value = false
		})
}

function getTableData(data?: { [propName: string]: any }) {
	//采购计划的物资明细
	if (props.model == "p-edit") {
		tableProp.value = tableGroup["base"].concat(tableGroup["pEdit"])
		tableProp.value.push({
			label: "操作",
			width: 130,
			prop: "operations",
			fixed: "right",
			needSlot: true
		})
		tbBtns.value = tableEndBtnMatItem
	} else {
		tableProp.value = tableGroup["base"].concat(tableGroup["pView"])
		tbBtns.value = []
	}
	if (props.id) {
		fetchParam.value = { purchasePlanId: props.id }
		fetchFunc.value =
			PlanPurchaseMaterialItemApi.getPlanPurchaseMaterialItemList
	} else {
		ElMessage.error("缺少采购计划ID")
	}

	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		...data
	}
	fetchTableData()
}

/**
 * 补丁
 * 监听 tableData，重组 lastTableData
 */
watch([tableData], () => {
	lastTableData.value = convertContentTableData(tableData.value, [
		"num",
		"planAmount",
		"purchaseAmount",
		"purchaseNum",
		"storeNum",
		"inTransitNum"
	])

	batchFormatterNumView(lastTableData.value as any[], undefined, 0)

	if (lastTableData.value.length > 0 && needSelection && props.selectFirst) {
		nextTick(() => {
			tableRef.value.pitayaTableRef!.toggleRowSelection(
				lastTableData.value[0],
				true
			)
			curMatId.value = lastTableData.value[0].contentId
		})
	}
	if (tbBtns.value.length > 0) {
		tbBtns.value[0].disabled = lastTableData.value.length <= 0
	}
})

/**
 * 采购数量 > 0; 整数
 * @param e
 */
function validateNum(e: any) {
	if (e.purchaseNum < 0) {
		const oldRow = tableCache.find((r) => r.id == e.id)
		e.purchaseNum = oldRow.purchaseNum
		ElMessage.error("采购数量不能小于0！")
		return false
	} else if (!isValidateInt(e.purchaseNum)) {
		ElMessage.error("采购数量必须为整数！")
		e.purchaseNum = floor(e.purchaseNum, 0)
	}
	updatePurchaseNum(e)
}

/**
 * 更新采购数量 api
 */
const updatePurchaseNum = debounce(async (e: any) => {
	const res =
		await PlanPurchaseMaterialItemApi.updateBalancePurchaseMaterialItem({
			id: e.id,
			purchaseNum: e.purchaseNum
		})

	// 失焦保存 更新预估采购金额
	e.purchaseAmount = res.purchaseAmount

	//  更新 tableCache
	const rowIdx = findIndex(tableCache, (v) => v.id === e.id)
	if (rowIdx !== -1) {
		tableCache.splice(rowIdx, 1, { ...e })
	}

	ElMessage.success("操作成功")
}, 300)

//table 选择
onDataSelected.value = (rowList: { [propName: string]: any }[]) => {
	selectedTableList.value = rowList
	if (rowList.length > 0) {
		curMatId.value = selectedTableList.value[0].contentId
	}

	const _ids = rowList.map((_d) => _d.id)
	emits(
		"onDataChange",
		tableData.value.filter((_d) => _ids.includes(_d.id))
	)
}
const showMatDetailDrawer = ref(false)
function showMatDetail(row) {
	curMatData.value = row
	curMatId.value = row.contentId
	showMatDetailDrawer.value = true
}
const dictOptions = ref<Record<string, any[]>>({
	INVENTORY_UNIT: []
})

function getDictByCodeList(): Promise<null> {
	return new Promise((resolve) => {
		DictApi.getDictByCodeList(dictOptions)
			.then((res) => {
				dictOptions.value = res
			})
			.finally(() => {
				resolve(null)
			})
	})
}
onMounted(() => {
	if (props.model == "p-edit") {
		tableProp.value.push({
			label: "操作",
			width: 130,
			prop: "operations",
			fixed: "right",
			needSlot: true
		})
	}
	Promise.all([getDictByCodeList()]).then(() => {
		getTableData()
	})
})
</script>
<template>
	<div class="tab-mat">
		<div class="tab-left">
			<Query
				:queryArrList="queryArrList"
				@getQueryData="getTableData"
				class="custom-q"
				v-if="props.needQuery"
			/>
			<PitayaTable
				ref="tableRef"
				:columns="tableProp"
				:table-data="lastTableData"
				:need-index="true"
				:need-pagination="true"
				:single-select="props.singleSelected"
				:need-selection="needSelection || props.model == 'p-edit'"
				:total="pageTotal"
				@onSelectionChange="onDataSelected"
				@on-current-page-change="onCurrentPageChange"
				:table-loading="tableLoading"
			>
				<template #buyUnit="{ rowData }">
					<dict-tag
						:options="dictOptions.INVENTORY_UNIT"
						:value="rowData.buyUnit"
					/>
				</template>
				<template #evaluation="{ rowData }">
					<cost-tag :value="rowData.evaluation" />
				</template>
				<template #purchaseAmount="{ rowData }">
					<span :class="{ 'num-blue': props.model == 'p-edit' }">
						<cost-tag :value="rowData.purchaseAmount"
					/></span>
				</template>
				<template #planAmount="{ rowData }">
					<cost-tag :value="rowData.planAmount" />
				</template>
				<template #num_view="{ rowData }">
					<span class="num-blue"> {{ rowData.num_view || "0" }}</span>
				</template>

				<template #storeNum="{ rowData }">
					{{ toFixedTwo(rowData.storeNum) }}
				</template>

				<template #inTransitNum="{ rowData }">
					{{ toFixedTwo(rowData.inTransitNum) }}
				</template>

				<template #purchaseNum_view="{ rowData }">
					<!-- 采购数量 可编辑 -->
					<el-input-number
						:controls="false"
						v-if="props.model == 'p-edit'"
						v-model="rowData.purchaseNum"
						:min="0"
						@change="validateNum(rowData)"
					/>
					<span class="num-blue" v-else>
						{{ rowData.purchaseNum_view || "0" }}</span
					>
				</template>
				<template #operations="{ rowData }">
					<el-button v-btn link @click="showMatDetail(rowData)">
						<font-awesome-icon :icon="['fas', 'eye']" />
						<span class="table-inner-btn">关联需求计划</span>
					</el-button>
				</template>
				<template #footerOperateLeft>
					<ButtonList
						class="btn-list"
						:is-not-radius="true"
						:button="tbBtns"
						v-loading="tbBtnLoading"
						@on-btn-click="onBtnClick"
					/>
				</template>
			</PitayaTable>
		</div>
		<!--直接显示关联的需求计划 -->
		<div class="tab-right content tab-border" v-if="props.model == 'mp-view'">
			<Title :title="needPlanTitle" />
			<MatNeedPlan
				style="padding-top: 2px"
				:mat-id="curMatId"
				:id="props.id"
				:type="props.type"
			/>
		</div>
		<!--弹出层显示关联的需求计划 -->
		<Drawer
			:size="1710"
			v-model:drawer="showMatDetailDrawer"
			:destroyOnClose="true"
		>
			<MatDetail
				:mat-id="curMatId"
				:id="props.id"
				:mat-obj="curMatData"
				@onClose="showMatDetailDrawer = false"
			/>
		</Drawer>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.custom-q {
	margin: 10px 0px -10px 10px !important;
}

.tab-mat {
	display: flex;
	justify-content: space-between;
	flex-direction: row;
	height: 100%;
	width: 100%;

	.tab-left {
		width: calc(100% - 500px);
		flex: 1;
		height: 100%;
	}

	.tab-border {
		//	padding-right: 10px !important;
		border-left: 1px solid #ccc;
		height: 100%;
	}

	.tab-right {
		width: 500px;
	}
	.num-blue {
		color: $---color-info2;
	}
}
</style>
