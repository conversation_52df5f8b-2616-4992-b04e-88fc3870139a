<script lang="ts" setup>
import { ref, onMounted } from "vue"
import CostTag from "@/app/baseline/views/components/costTag.vue"
import { useTbInit } from "@/app/baseline/views/components/tableBase"
import LineTag from "@/app/baseline/views/components/lineTag.vue"
import { PlanPurchaseMaterialItemApi } from "@/app/baseline/api/plan/planPurchaseMaterialItem"
// import { PlanMergeItemApi } from "@/app/baseline/api/plan/planMergeItem"
import { BaseLineSysApi } from "@/app/baseline/api/system"
import { MatApplyObj } from "@/app/baseline/views/material/components/define"
import { DictApi } from "@/app/baseline/api/dict"
import NeedPlanDrawer from "@/app/baseline/views/plan/components/needPlanDrawer.vue"
import { convertContentTableData } from "@/app/baseline/utils"
import matInfo from "./matInfo.vue"
import { toFixedTwo } from "@/app/baseline/utils"

export interface Props {
	id: string | number
	matId: string | number
	mergePlanId: string | number
	matObj?: MatApplyObj
}
const props = defineProps<Props>()

const loading = ref(false)
const drawerLeftTitle = {
	name: ["物资信息"],
	icon: ["fas", "square-share-nodes"]
}
const drawerRightTitle = {
	name: ["关联需求计划"],
	icon: ["fas", "square-share-nodes"]
}

const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	currentPage,
	pageTotal,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = useTbInit()
tableProp.value = [
	{ label: "需求计划号", prop: "code", width: 160, fixed: "left" },
	{ label: "需求计划名称", prop: "label", minWidth: 120 },
	{ label: "需求部门", prop: "sysOrgId_view", minWidth: 120 },
	{ label: "专业", prop: "majorId_view", width: 120 },
	{ label: "线路", prop: "lineNoId", needSlot: true, width: 100 },
	{ label: "段区", prop: "depotId_view", width: 120 },
	{
		label: "预估采购单价",
		prop: "evaluation",
		needSlot: true,
		width: 120,
		align: "right"
	},
	{
		label: "需求数量",
		prop: "num",
		needSlot: true,
		align: "right",
		width: 120
	},
	{
		label: "预估金额",
		prop: "purchaseAmount",
		needSlot: true,
		width: 120,
		align: "right"
	},
	// { label: "需求原因", prop: "remark", minWidth: 200, align: "left" },
	{ label: "创建人", prop: "createdBy_view", width: 120 },
	{ label: "创建时间", prop: "createdDate", width: 150 },
	{
		label: "操作",
		width: 100,
		prop: "operations",
		fixed: "right",
		needSlot: true
	}
]
// const lastTableData = ref<any[]>([])
function getTableData() {
	//获取一个采购计划 物资对应得需求计划
	fetchParam.value = {
		purchasePlanId: props.id,
		materialId: props.matId,
		mergePlanId: props.mergePlanId
	}
	fetchFunc.value =
		PlanPurchaseMaterialItemApi.getPlanPurchaseMaterialItemNeedPlanList
	// fetchParam.value = { mergePlanId: props.id, materialId: props.matId }
	// fetchFunc.value = PlanMergeItemApi.getPlanMergeItemList
	currentPage.value = 1
	fetchTableData()
	// fetchTableData().then(() => {
	// 	lastTableData.value = convertContentTableData(tableData.value)
	// })
}
const dictOptions = ref<Record<string, any[]>>({
	INVENTORY_UNIT: []
})

function getDictByCodeList(): Promise<null> {
	return new Promise((resolve) => {
		DictApi.getDictByCodeList(dictOptions)
			.then((res) => {
				dictOptions.value = res
			})
			.finally(() => {
				resolve(null)
			})
	})
}
const lineList = ref<[]>([])
function getLineList() {
	BaseLineSysApi.getLineList().then((res) => {
		lineList.value = res
	})
}
const formatData = (data: any) => {
	return data ? data : "---"
}

const editId = ref("")
const showDrawer = ref()
const currentType = ref("")
const selectYearData = ref("")
const showMatDetail = (data: any) => {
	// editId.value = data.contentId
	editId.value = data.id
	currentType.value = data.planType
	selectYearData.value = data.year
	showDrawer.value = true
}

const buyUnit_view = ref("---")
onMounted(() => {
	Promise.all([getLineList(), getDictByCodeList()]).then(() => {
		const found = dictOptions.value.INVENTORY_UNIT.find(
			(item) => item.value == props.matObj!.buyUnit
		)
		if (found) {
			buyUnit_view.value = found.label
		}
		getTableData()
	})
})
</script>
<template>
	<div class="drawer-container" v-loading="loading">
		<!-- 左侧表单区域 -->
		<div class="drawer-column left">
			<div class="rows">
				<Title :title="drawerLeftTitle" v-if="!loading" />
				<el-descriptions size="small" :column="1" border class="content">
					<el-descriptions-item label="物资编码">
						{{ formatData(matObj?.code) }}
					</el-descriptions-item>
					<el-descriptions-item label="物资名称">
						{{ formatData(matObj?.label) }}
					</el-descriptions-item>
					<el-descriptions-item label="物资分类编码">
						{{ formatData(matObj?.materialTypeCode) }}
					</el-descriptions-item>
					<el-descriptions-item label="物资分类名称">
						{{ formatData(matObj?.materialTypeLabel) }}
					</el-descriptions-item>
					<el-descriptions-item label="规格型号">
						{{ formatData(matObj?.version) }}
					</el-descriptions-item>
					<el-descriptions-item label="技术参数">
						{{ formatData(matObj?.technicalParameter) }}
					</el-descriptions-item>
					<el-descriptions-item label="采购单位">
						{{ formatData(buyUnit_view) }}
					</el-descriptions-item>
					<el-descriptions-item label="预估采购单价">
						<CostTag :value="matObj?.evaluation" />
					</el-descriptions-item>
					<el-descriptions-item label="需求数量">
						{{ formatData(matObj?.num) }}
					</el-descriptions-item>
					<el-descriptions-item label="预估采购金额">
						<CostTag :value="matObj?.purchaseAmount" />
					</el-descriptions-item>
				</el-descriptions>
			</div>
		</div>
		<!-- 右侧table区域 -->
		<div class="drawer-column right">
			<div class="rows">
				<Title :title="drawerRightTitle" />
				<PitayaTable
					ref="tableRef"
					:columns="tableProp"
					:tableData="tableData"
					:total="pageTotal"
					:single-select="false"
					:need-selection="false"
					:need-index="true"
					@on-current-page-change="onCurrentPageChange"
					:table-loading="tableLoading"
				>
					<template #num="{ rowData }">
						{{ toFixedTwo(rowData.num, 0) }}
					</template>

					<template #evaluation="{ rowData }">
						<cost-tag :value="rowData.evaluation" />
					</template>
					<template #purchaseAmount="{ rowData }">
						<cost-tag :value="rowData.purchaseAmount" />
					</template>
					<template #lineNoId="{ rowData }">
						<line-tag :options="lineList" :value="rowData.lineNoId" />
					</template>
					<template #operations="{ rowData }">
						<el-button v-btn link @click="showMatDetail(rowData)">
							<font-awesome-icon :icon="['fas', 'eye']" />
							<span class="table-inner-btn">物资明细</span>
						</el-button>
					</template>
				</PitayaTable>
			</div>
		</div>
		<!-- :table-data="tableData" -->
		<Drawer :size="550" v-model:drawer="showDrawer" :destroyOnClose="true">
			<mat-info
				:plan-id="editId"
				:material-id="props.matId"
				@close="showDrawer = false"
			/>
			<!-- <NeedPlanDrawer
				:id="editId"
				:matId="props.matId"
				model="view"
				:type="currentType"
				:year="selectYearData"
				@onClosed="showDrawer = false"
			/> -->
		</Drawer>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index";
.el-descriptions {
	:deep(.el-descriptions__label) {
		width: 200px;
	}
}
.left {
	width: 310px;
}

.right {
	width: calc(100% - 310px);
}
</style>
