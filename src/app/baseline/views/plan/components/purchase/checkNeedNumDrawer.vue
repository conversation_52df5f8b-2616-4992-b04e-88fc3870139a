<!-- 采购计划：提交审核时校验，如果采购数量不等于需求数量，即弹出提示框，需要填写原因-->
<template>
	<div class="dialog-box">
		<el-dialog v-model="dialog" title="采购计划数量变更" width="30%">
			<!-- :before-close="handleClose" -->
			<div style="padding-left: 20px; padding-bottom: 10px">
				<p>需要变更采购计划中的物资采购数量！</p>
				<p>请及时跟需求计划的填报人沟通，并确认需求数量！</p>
			</div>

			<el-form
				ref="formRef"
				class="content"
				:model="formData"
				:rules="formRules"
				label-position="left"
				label-width="80px"
			>
				<form-element :form-element="formEls" :form-data="formData" />
			</el-form>

			<template #footer>
				<span class="dialog-footer">
					<el-button @click="emit('close')" :loading="dialogBtnLoading">
						取消
					</el-button>
					<el-button
						type="primary"
						@click="handleSaveClose"
						:loading="dialogBtnLoading"
					>
						确认
					</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>
<script setup lang="ts">
import { requiredValidator } from "@/app/baseline/utils/validate"
import { FormInstance, FormItemRule } from "element-plus"
import { FormElementType } from "../../../components/define"
import FormElement from "../../../components/formElement.vue"
import { inputMaxLength } from "@/app/baseline/utils/layout-config"

const props = defineProps<{
	id?: any // id
	dialog: boolean
}>()

const emit = defineEmits<{
	(e: "save", reason: any): void
	(e: "close"): void
	(e: "update:dialog", val: boolean): void
}>()

const dialog = computed({
	get() {
		return props.dialog
	},
	set(value) {
		formData.value.reason = ""
		setTimeout(() => {
			formRef.value?.clearValidate()
			dialogBtnLoading.value = false
		}, 0)
		emit("update:dialog", value)
	}
})

/**
 * 表单实例
 */
const formRef = ref<FormInstance>()
const formRules: Record<string, FormItemRule> = {
	reason: requiredValidator("变更原因")
}

const formData = ref({ reason: "" })
const formEls = computed<FormElementType[][]>(() => {
	const ls: FormElementType[] = [
		{
			label: "变更原因",
			name: "reason",
			type: "textarea",
			maxlength: inputMaxLength.textarea
		}
	]
	return [ls]
})
const dialogBtnLoading = ref(false)
/**
 * 出库关闭 保存
 */
function handleSaveClose() {
	if (!formRef.value) {
		return
	}

	formRef.value.validate((valid) => {
		if (!valid) {
			return
		}

		dialogBtnLoading.value = true
		emit("save", formData.value.reason)
	})
}
</script>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.dialog-box {
	:deep(.el-dialog__body) {
		margin: 10px;
		padding: 20px 0 10px !important;
		border-top: 1px solid #ddd;
		border-bottom: 1px solid #ddd;
	}
	:deep(.el-dialog__footer) {
		padding: 0 10px 10px;
	}
}
</style>
