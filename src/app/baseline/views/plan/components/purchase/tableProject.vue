<script lang="ts" setup>
import { ref, onMounted } from "vue"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import { useTbInit } from "@/app/baseline/views/components/tableBase.ts"
import CostTag from "@/app/baseline/views/components/costTag.vue"
/* import ViewDrawer from "@/app/baseline/views/purchase/project/viewDrawer.vue" */
import { projectPageApi } from "@/app/baseline/views/purchase/project/projectPage"
import { PurchaseProjectApi } from "@/app/baseline/api/purchase/purchaseProject"
import { useDictInit } from "@/app/baseline/views/components/dictBase"
import LinkTag from "@/app/baseline/views/components/linkTag.vue"
import { getOnlyDate } from "../../../../utils"
const { dictOptions, getDictByCodeList } = useDictInit()

interface Props {
	id: string | number // 采购计划ID
}
const props = defineProps<Props>()

/**-- 采购项目 ---*/
/**********************初始化table *********************************/
const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	tbBtnLoading,
	pageSize,
	currentPage,
	pageTotal,
	selectedTableList,
	fetchParam,
	tbBtns,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange,
	onDataSelected,
	onBtnClick
} = useTbInit()

const getTableCol = () => {
	const data = projectPageApi.getProjectTableProps([
		"year",
		"code",
		"label",
		"contractCode",
		"contractSigningDate",
		"contractEndDate",
		"contractType",
		"contractAmount",
		"supplierLabel",
		"expireDate",
		"purchaseUserName"
	])
	tableProp.value = data.filter((v) => v !== undefined)
}

// 获取表格默认参数
fetchParam.value = { planPurchaseId: props.id }
fetchFunc.value = PurchaseProjectApi.getPurchaseProjectList
tbBtns.value = []
onDataSelected.value = (rowList: { [propName: string]: any }[]) => {
	selectedTableList.value = rowList
}
const getTableData = (data?: any) => {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		...data
	}
	fetchTableData()
}
const queryArrList = []

/********************** drawer相关操作 *********************************/

//drawer
const curRowId = ref<any>("")
const curRowData = ref<any>("")

const showViewDrawer = ref<boolean>(false)
const onRowView = (row: any) => {
	curRowId.value = row.id
	curRowData.value = row
	showViewDrawer.value = true
}
// 弹窗关闭
const closeDrawer = (msg: string) => {
	if (msg === "pub") {
		getTableData()
	} else if (msg === "save") {
		getTableData()
	} else {
		showViewDrawer.value = false
	}
}

onMounted(() => {
	getTableCol()
	Promise.all([getDictByCodeList(["PURCHASE_TYPE", "CONTRACT_TYPE"])]).then(
		() => {
			getTableData({})
		}
	)
})

defineOptions({
	name: "Project"
})
</script>
<template>
	<div>
		<PitayaTable
			ref="tableRef"
			:columns="tableProp"
			:tableData="tableData"
			:total="pageTotal"
			:single-select="true"
			:need-selection="false"
			:need-index="true"
			@onSelectionChange="onDataSelected"
			@on-current-page-change="onCurrentPageChange"
			:table-loading="tableLoading"
		>
			<template #expireDate="{ rowData }">
				{{ getOnlyDate(rowData.expireDate) || "---" }}
			</template>
			<template #contractSigningDate="{ rowData }">
				{{ getOnlyDate(rowData.contractSigningDate) || "---" }}
			</template>
			<template #contractEndDate="{ rowData }">
				{{ getOnlyDate(rowData.contractEndDate) || "---" }}
			</template>
			<template #contractAmount="{ rowData }">
				<cost-tag :value="rowData.contractAmount" />
			</template>
			<template #purchasePlanNum="{ rowData }">
				<link-tag
					:value="rowData.purchasePlanNum"
					@on-click="onRowView(rowData)"
				/>
			</template>
			<template #purchaseType="{ rowData }">
				<dict-tag
					:options="dictOptions.PURCHASE_TYPE"
					:value="rowData.purchaseType"
				/>
			</template>
			<template #contractType="{ rowData }">
				<dict-tag
					:options="dictOptions.CONTRACT_TYPE"
					:value="rowData.contractType"
				/>
			</template>
			<template #operations="{ rowData }">
				<el-button v-btn link @click="onRowView(rowData)">
					<font-awesome-icon
						:icon="['fas', 'eye']"
						style="color: var(--pitaya-btn-background)"
					/>
					<span class="table-inner-btn">查看</span>
				</el-button>
			</template>
		</PitayaTable>

		<!-- 查看弹窗 -->
		<Drawer :size="1800" v-model:drawer="showViewDrawer" :destroyOnClose="true">
			<!-- <view-drawer
				:id="curRowId"
				:row="curRowData"
				model="view"
				@on-save-or-close="closeDrawer"
			/> -->
		</Drawer>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
