<script setup lang="ts">
import { ref, onMounted } from "vue"
import { DictApi } from "@/app/baseline/api/dict"
import { defineProps } from "vue"
import { useTbInit } from "@/app/baseline/views/components/tableBase"
import CostTag from "@/app/baseline/views/components/costTag.vue"
import MatDetail from "@/app/baseline/views/plan/components/purchase/matDetail.vue"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import { convertContentTableData } from "@/app/baseline/utils"
// import { PlanPurchaseMaterialItemApi } from "@/app/baseline/api/plan/planPurchaseMaterialItem"
import { PlanMergeItemApi } from "@/app/baseline/api/plan/planMergeItem"
import { toFixedTwo } from "@/app/baseline/utils"

/**
 * 合并计划  新建和编辑时  物资明细(只有列表）
 *          查看时物资明细（左右结构，查询 + 列表 + 单物资的关联需求计划)
 * 采购计划  新建和编辑时  物资明细（列表 + 关联需求计划弹出层 + 一键平衡利库）
 *          查看时物资明细（同合并计划）
 */
export interface Props {
	id: string | number
	mergePlanId: string | number
}

const props = withDefaults(defineProps<Props>(), {
	id: "",
	mergePlanId: ""
})

/*-------------------初始化表格-start-------------------*/
const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	currentPage,
	pageTotal,
	fetchParam,
	fetchFunc,
	fetchTableData,
	onCurrentPageChange
} = useTbInit()

tableProp.value = [
	{ label: "物资编码", prop: "code", width: 130 },
	{ label: "物资名称", prop: "label", width: 150 },
	{ label: "物资分类编码", prop: "materialTypeCode", width: 100 },
	{
		label: "物资分类名称",
		prop: "materialTypeLabel",
		width: 150
	},
	{ label: "规格型号", prop: "version", width: 150 },
	{
		label: "技术参数",
		prop: "technicalParameter",
		minWidth: 150,
		align: "left"
	},
	{ label: "采购单位", prop: "buyUnit", needSlot: true, width: 90 },
	{
		label: "预估采购单价",
		prop: "evaluation",
		needSlot: true,
		width: 150,
		align: "right"
	},
	{ label: "需求数量", prop: "num", needSlot: true, align: "right", width: 90 },
	// { label: "预估金额", prop: "planAmount", needSlot: true, width: 120 },
	// { label: "库存数量", prop: "storeNum", width: 90 },
	// { label: "安全库存", prop: "safeStock", width: 90 },
	// { label: "采购数量", prop: "purchaseNum", needSlot: true, width: 90 },
	{
		label: "预估采购金额",
		prop: "purchaseAmount",
		needSlot: true,
		width: 120,
		align: "right"
	},
	{
		label: "操作",
		width: 130,
		prop: "operations",
		fixed: "right",
		needSlot: true
	}
]

const lastTableData = ref<any[]>([])
const curMatId = ref<any>()
const curMatData = ref<any>()
// fetchFunc.value = PlanPurchaseMaterialItemApi.getPlanPurchaseMaterialItemList
// fetchParam.value = { purchasePlanId: props.id, mergePlanId: props.mergePlanId }
fetchFunc.value = PlanMergeItemApi.getPlanMergeItemMaterialList
fetchParam.value = { mergePlanId: props.mergePlanId }
function getTableData(data?: { [propName: string]: any }) {
	//采购计划的物资明细
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		...data
	}
	fetchTableData()
}

/**
 * 补丁
 * 监听tableData 重组lastTableData
 */
watch([tableData], () => {
	lastTableData.value = convertContentTableData(tableData.value, [
		"num",
		"planAmount",
		"purchaseAmount",
		"purchaseNum",
		"storeNum",
		"evaluation"
	])
})

const showMatDetailDrawer = ref(false)
function showMatDetail(row: any) {
	curMatData.value = row
	curMatId.value = row.contentId
	showMatDetailDrawer.value = true
}
const dictOptions = ref<Record<string, any[]>>({
	INVENTORY_UNIT: []
})

function getDictByCodeList(): Promise<null> {
	return new Promise((resolve) => {
		DictApi.getDictByCodeList(dictOptions)
			.then((res) => {
				dictOptions.value = res
			})
			.finally(() => {
				resolve(null)
			})
	})
}
onMounted(() => {
	Promise.all([getDictByCodeList()]).then(() => {
		getTableData()
	})
})
</script>
<template>
	<div class="tab-mat">
		<div class="tab-left">
			<PitayaTable
				ref="tableRef"
				:columns="tableProp"
				:table-data="lastTableData"
				:need-index="true"
				:need-pagination="true"
				:single-select="false"
				:need-selection="false"
				:total="pageTotal"
				@on-current-page-change="onCurrentPageChange"
				:table-loading="tableLoading"
			>
				<template #buyUnit="{ rowData }">
					<dict-tag
						:options="dictOptions.INVENTORY_UNIT"
						:value="rowData.buyUnit"
					/>
				</template>
				<template #evaluation="{ rowData }">
					<cost-tag :value="rowData.evaluation" />
				</template>
				<template #purchaseAmount="{ rowData }">
					<span class="num-blue">
						<cost-tag :value="rowData.purchaseAmount"
					/></span>
				</template>
				<template #planAmount="{ rowData }">
					<cost-tag :value="rowData.planAmount" />
				</template>
				<template #num="{ rowData }">
					<span class="num-blue"> {{ toFixedTwo(rowData.num, 0) || "0" }}</span>
				</template>
				<template #purchaseNum="{ rowData }">
					<span class="num-blue"> {{ rowData.purchaseNum || "0" }}</span>
				</template>
				<template #operations="{ rowData }">
					<el-button v-btn link @click="showMatDetail(rowData)">
						<font-awesome-icon :icon="['fas', 'eye']" />
						<span class="table-inner-btn">关联需求计划</span>
					</el-button>
				</template>
			</PitayaTable>
		</div>
		<!--弹出层显示关联的需求计划 -->
		<Drawer
			:size="1680"
			v-model:drawer="showMatDetailDrawer"
			:destroyOnClose="true"
		>
			<MatDetail
				:mat-id="curMatId"
				:id="props.id"
				:mergePlanId="props.mergePlanId"
				:mat-obj="curMatData"
				@onClose="showMatDetailDrawer = false"
			/>
		</Drawer>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.custom-q {
	margin: 10px 0px -10px 10px !important;
}

.tab-mat {
	display: flex;
	justify-content: space-between;
	flex-direction: row;
	height: 100%;
	width: 100%;

	.tab-left {
		width: calc(100% - 500px);
		flex: 1;
		height: 100%;
	}

	.tab-border {
		//	padding-right: 10px !important;
		border-left: 1px solid #ccc;
		height: 100%;
	}

	.tab-right {
		width: 500px;
	}
	.num-blue {
		color: $---color-info2;
	}
}
</style>
