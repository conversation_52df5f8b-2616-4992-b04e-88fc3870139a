<script lang="ts" setup>
import { watch, ref, onMounted } from "vue"
import { ElMessage } from "element-plus"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import { DictApi } from "@/app/baseline/api/dict"
import { useTbInit } from "@/app/baseline/views/components/tableBase"
import MergePlanViewDrawer from "@/app/baseline/views/plan/components/purchase/MergePlanViewDrawer.vue"
import { BaseLineSysApi } from "@/app/baseline/api/system"
import { PlanPurchaseMergeItemAPi } from "@/app/baseline/api/plan/planPurchaseMergeItem"
import CostTag from "@/app/baseline/views/components/costTag.vue"
import LineTag from "@/app/baseline/views/components/lineTag.vue"
import { convertContentTableData } from "../../../../utils"
import { useMessageBoxInit } from "@/app/baseline/views/components/messageBox"
const { showDelConfirm } = useMessageBoxInit()
interface Props {
	purchasePlanId: string | number
	needOpt?: boolean
	needQuery?: boolean
}

const props = defineProps<Props>()
const emits = defineEmits(["onDataSelected", "onSelectChange", "onSetMergeCnt"])
//查询
const queryArrList = [
	{
		name: "部门",
		key: "sysOrgId",
		type: "treeSelect",
		treeApi: BaseLineSysApi.getDepartmentTree,
		placeholder: "请选择所属部门"
	},
	{
		name: "专业",
		key: "majorId",
		type: "treeSelect",
		treeApi: BaseLineSysApi.getProfessionTree,
		placeholder: "请选择所属专业"
	},
	{
		name: "合并计划号",
		key: "code",
		placeholder: "请输入合并计划号",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "合并计划名称",
		key: "label",
		placeholder: "请输入合并计划名称",
		enableFuzzy: true,
		type: "input"
	}
]

const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	tbBtnLoading,
	currentPage,
	pageTotal,
	selectedTableList,
	fetchParam,
	tbBtns,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange,
	onDataSelected,
	onBtnClick
} = useTbInit()

tableProp.value = [
	{ label: "年度", prop: "year", width: 85, fixed: "left" },
	{ label: "合并计划号", prop: "code", width: 160, fixed: "left" },
	{ label: "合并计划名称", prop: "label", minWidth: 200 },
	{ label: "专业", prop: "majorId_view", width: 150 },
	{ label: "线路", prop: "lineNoId", needSlot: true, width: 100 },
	{
		label: "段区",
		prop: "depotId_view",
		width: 100
	},
	{
		label: "费用类别",
		prop: "expenseCategory_view",
		width: 150
	},
	{
		label: "预估采购金额",
		prop: "purchaseAmount",
		needSlot: true,
		width: 150,
		align: "right"
	},
	{ label: "需求原因", prop: "remark", width: 160, align: "left" },
	{ label: "合并部门", prop: "sysOrgId_view", needSlot: false, width: 130 },
	{
		label: "操作",
		width: 160,
		prop: "operations",
		fixed: "right",
		needSlot: true
	}
]
tbBtns.value = [
	{
		name: "选择合并计划",
		icon: ["fas", "square-plus"],
		click: () => openSelectDrawer()
	},
	{
		name: "批量移除",
		icon: ["fas", "trash-alt"],
		disabled: true,
		click: () => batchDelete()
	}
]
//table 选择
onDataSelected.value = () => {
	//selectedTableList.value = rowList
	selectedTableList.value = tableRef.value!.pitayaTableRef!.getSelectionRows()
	tbBtns.value[1].disabled = selectedTableList.value.length <= 0
	emits("onDataSelected", selectedTableList.value)
}
//删除一组采购计划的 合并计划
const batchDelete = (id?: string) => {
	let arrIds = [id]
	if (!id) {
		arrIds = selectedTableList.value.map((item: { id: any }) => item.id)
	}
	showDelConfirm().then(() => {
		PlanPurchaseMergeItemAPi.deletePlanPurchaseMergeItem(arrIds.join(",")).then(
			() => {
				ElMessage.success("移除成功")
				getTableData()
				emits("onSelectChange")
			}
		)
	})
}

//table opt
const editMergePlanId = ref<any>("")
const mergePlanId = ref<any>("")
const curId = ref<any>("")
//合并计划查看
const showDetailsDrawer = ref<boolean>(false)
const onCloseDrawer = () => {
	showDetailsDrawer.value = false
}
//查看合并计划详情
const onRowViewDetail = (row: any) => {
	curId.value = row.contentId
	const data = tableData.value.find((item) => item.id === row.id)
	editMergePlanId.value = data.purchasePlanId
	mergePlanId.value = data.mergePlanId
	showDetailsDrawer.value = true
}
const onRowDelete = (row: any) => {
	batchDelete(row.id)
}

//选择合并计划
const showSelectDrawer = ref<boolean>(false)
const openSelectDrawer = () => {
	showSelectDrawer.value = true
	return Promise.reject()
}
const lastTableData = ref<any[]>([])

const getTableData = (data?: any) => {
	//根据一个purchasePlanId 获取合并计划明细
	if (props.purchasePlanId) {
		fetchParam.value = { purchasePlanId: props.purchasePlanId }
		fetchFunc.value = PlanPurchaseMergeItemAPi.getPlanPurchaseMergeItemList
	}
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		...data
	}
	fetchTableData()
}

/**
 * 补丁
 * 监听tableData, 重组lastTableData
 */
watch([tableData], () => {
	lastTableData.value = convertContentTableData(tableData.value)
	emits("onSetMergeCnt", lastTableData.value.length)
})

//获取字典,多个字典可同时获取
const dictOptions = ref<Record<string, any[]>>({
	COST_CATEGORY: []
})
function getDictByCodeList(): Promise<null> {
	return new Promise((resolve) => {
		DictApi.getDictByCodeList(dictOptions)
			.then((res: Record<string, any[]>) => {
				dictOptions.value = res
			})
			.finally(() => {
				resolve(null)
			})
	})
}
const lineList = ref<[]>([])
function getLineList() {
	BaseLineSysApi.getLineList().then((res) => {
		lineList.value = res
	})
}
onMounted(() => {
	Promise.all([getLineList(), getDictByCodeList()]).then(() => {
		getTableData({})
	})
})
watch(
	[() => pageTotal.value],
	([nT]) => {
		if (tbBtns.value.length > 1) {
			if (nT <= 0) {
				tbBtns.value[1].disabled = true
			}
		}
	},
	{ immediate: true }
)
defineOptions({
	name: "TableMergePlan"
})
const getMergeCount = () => {
	return pageTotal.value
}
defineExpose({
	getMergeCount
})
</script>
<template>
	<div>
		<Query
			v-if="props.needQuery"
			:queryArrList="queryArrList"
			@getQueryData="getTableData"
			class="custom-q"
		/>
		<PitayaTable
			ref="tableRef"
			:columns="tableProp"
			:tableData="lastTableData"
			:total="pageTotal"
			:single-select="false"
			:need-selection="props.needOpt ? true : false"
			:need-index="true"
			@onSelectionChange="onDataSelected"
			@on-current-page-change="onCurrentPageChange"
			:table-loading="tableLoading"
		>
			<template #purchaseAmount="{ rowData }">
				<cost-tag :value="rowData.purchaseAmount" />
			</template>
			<template #expenseCategory="{ rowData }">
				<dict-tag
					:options="dictOptions.COST_CATEGORY"
					:value="rowData.expenseCategory"
				/>
			</template>
			<template #lineNoId="{ rowData }">
				<line-tag :options="lineList" :value="rowData.lineNoId" />
			</template>
			<template #operations="{ rowData }">
				<el-button v-btn link @click="onRowViewDetail(rowData)">
					<font-awesome-icon :icon="['fas', 'eye']" />
					<span class="table-inner-btn">查看</span>
				</el-button>
				<el-button
					v-if="props.needOpt"
					v-btn
					link
					@click="onRowDelete(rowData)"
				>
					<font-awesome-icon :icon="['fas', 'trash-can']" />
					<span class="table-inner-btn">移除</span>
				</el-button>
			</template>
			<template #footerOperateLeft>
				<ButtonList
					v-if="props.needOpt"
					class="btn-list"
					:is-not-radius="true"
					:button="tbBtns"
					v-loading="tbBtnLoading"
					@on-btn-click="onBtnClick"
				/>
			</template>
		</PitayaTable>
		<!-- 物资明细窗口.合并计划详情 -->
		<Drawer
			:size="1740"
			v-model:drawer="showDetailsDrawer"
			:destroyOnClose="true"
		>
			<MergePlanViewDrawer
				:id="curId"
				:purchasePlanId="editMergePlanId"
				:mergePlanId="mergePlanId"
				modal="purchase"
				@on-save-or-close="onCloseDrawer"
			/>
		</Drawer>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index";
.custom-q {
	margin: 10px 0px -10px 10px !important;
}
</style>
