<script setup lang="ts">
import { DictApi } from "@/app/baseline/api/dict"
import { ref, onMounted, nextTick } from "vue"
import { useTbInit } from "@/app/baseline/views/components/tableBase"
import CostTag from "@/app/baseline/views/components/costTag.vue"
import { PlanPurchaseMaterialItemApi } from "@/app/baseline/api/plan/planPurchaseMaterialItem"
import matPurchasePlan from "@/app/baseline/views/plan/components/purchase/matPurchasePlan.vue"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import {
	batchFormatterNumView,
	convertContentTableData
} from "@/app/baseline/utils"
import { PlanMergeItemApi } from "@/app/baseline/api/plan/planMergeItem"

/**
 * 合并计划  新建和编辑时  物资明细(只有列表）
 *          查看时物资明细（左右结构，查询 + 列表 + 单物资的关联需求计划)
 * 采购计划  新建和编辑时  物资明细（列表 + 关联需求计划弹出层 + 一键平衡利库）
 *          查看时物资明细（同合并计划）
 */
export interface Props {
	id: string | number //合并计划ID，采购划ID
	type: string //merge 、 purchase
	model?: string //显示模式  m-edit 列表: , p-edit 列表+ 关联需求计划，mp-view:列表 + 右侧关联需求计划
	needQuery?: boolean
	needSelection?: boolean
	selectFirst?: boolean
	singleSelected?: boolean
	tableEndBtn?: object
}

const props = withDefaults(defineProps<Props>(), {
	id: "",
	type: "merge",
	model: "list",
	needQuery: false,
	needSelection: false,
	selectFirst: true,
	singleSelected: true
})
const emits = defineEmits(["onDataChange", "onTableEndBtn"])
const needSelection = props.model == "mp-view" || props.needSelection

/*-------------------初始化表格-start-------------------*/
const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	currentPage,
	pageTotal,
	selectedTableList,
	fetchParam,
	tbBtns,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange,
	onDataSelected
} = useTbInit()

const tableGroup = {
	base: [
		{ label: "物资编码", prop: "code", width: 130, fixed: "left" },
		{ label: "物资名称", prop: "label", width: 150 },
		{ label: "物资分类编码", prop: "materialTypeCode", width: 100 },
		{
			label: "物资分类名称",
			prop: "materialTypeLabel",
			width: 150
		},
		{ label: "规格型号", prop: "version", width: 120 },
		{
			label: "技术参数",
			prop: "technicalParameter",
			minWidth: 200,
			align: "left"
		},
		{ label: "采购单位", prop: "buyUnit", needSlot: true, width: 90 },
		{
			label: "预估采购单价",
			prop: "evaluation",
			needSlot: true,
			width: 150,
			align: "right"
		}
	],
	other: [
		{
			label: "需求数量",
			prop: "num_view",
			align: "right",
			width: 90,
			fixed: "right"
		},
		{
			label: "预估采购金额",
			prop: "purchaseAmount",
			width: 120,
			needSlot: true,
			align: "right",
			fixed: "right"
		}
	],
	pEdit: [
		{
			label: "预估金额",
			prop: "planAmount",
			needSlot: true,
			width: 120,
			align: "right"
		},
		{ label: "库存数量", prop: "storeNum_view", align: "right", width: 90 },
		{ label: "安全库存", prop: "safeStock", width: 90, align: "right" },
		{
			label: "采购数量",
			prop: "purchaseNum_view",
			align: "right",
			needSlot: true,
			width: 90,
			fixed: "right"
		},
		{
			label: "预估采购金额",
			prop: "purchaseAmount",
			needSlot: true,
			width: 120,
			fixed: "right",
			align: "right"
		}
	],
	pView: [
		{
			label: "需求数量",
			prop: "num_view",
			align: "right",
			needSlot: true,
			width: 90,
			fixed: "right"
		},
		{
			label: "采购数量",
			prop: "purchaseNum_view",
			align: "right",
			needSlot: true,
			width: 90,
			fixed: "right"
		},
		{
			label: "预估采购金额",
			prop: "purchaseAmount",
			needSlot: true,
			width: 120,
			fixed: "right",
			align: "right"
		}
	]
}
tbBtns.value = props.tableEndBtn ? props.tableEndBtn : []

/*-------------------初始化表格-end-------------------*/

const queryArrList = [
	{
		name: "物资编码",
		key: "materialCode",
		placeholder: "请输入物资编码",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资名称",
		key: "materialLabel",
		placeholder: "请输入物资名称",
		enableFuzzy: true,
		type: "input"
	}
]
const needPlanTitle = {
	name: ["关联合并计划"],
	icon: ["fas", "square-share-nodes"]
}
const lastTableData = ref<any[]>([])
const curMatId = ref<any>()
function getTableData(data?: { [propName: string]: any }) {
	if (props.type == "merge") {
		tableProp.value = tableGroup["base"].concat(tableGroup["other"])
		//合并计划的物资明细
		if (props.id) {
			fetchParam.value = { mergePlanId: props.id }
			fetchFunc.value = PlanMergeItemApi.getPlanMergeItemMaterialList
		} else {
			ElMessage.error("缺少需求计划ID")
		}
	} else {
		//采购计划的物资明细
		if (props.model == "p-edit") {
			tableProp.value = tableGroup["base"].concat(tableGroup["pEdit"])
		} else {
			tableProp.value = tableGroup["base"].concat(tableGroup["pView"])
		}
		if (props.id) {
			fetchParam.value = { purchasePlanId: props.id }
			fetchFunc.value =
				PlanPurchaseMaterialItemApi.getPlanPurchaseMaterialItemList
		} else {
			ElMessage.error("缺少采购计划ID")
		}
	}

	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		...data
	}
	fetchTableData()
}

/**
 * 补丁
 * 监听tableData,重组lastTableData
 */
watch([tableData], () => {
	lastTableData.value = convertContentTableData(tableData.value, [
		"num",
		"planAmount",
		"purchaseAmount",
		"purchaseNum",
		"storeNum",
		"evaluation"
	])

	batchFormatterNumView(lastTableData.value as any[], undefined, 0)

	if (lastTableData.value.length > 0 && needSelection) {
		nextTick(() => {
			if (tableRef.value) {
				tableRef.value!.pitayaTableRef!.toggleRowSelection(
					lastTableData.value[0],
					true
				)
				curMatId.value = lastTableData.value[0].contentId
			}
		})
	}
})

//table 选择
onDataSelected.value = (rowList: { [propName: string]: any }[]) => {
	selectedTableList.value = rowList
	if (rowList.length > 0) {
		curMatId.value = selectedTableList.value[0].contentId
	}

	const _ids = rowList.map((_d) => _d.id)
	emits(
		"onDataChange",
		tableData.value.filter((_d: any) => _ids.includes(_d.id))
	)
}
const dictOptions = ref<Record<string, any[]>>({
	INVENTORY_UNIT: []
})

function getDictByCodeList(): Promise<null> {
	return new Promise((resolve) => {
		DictApi.getDictByCodeList(dictOptions)
			.then((res) => {
				dictOptions.value = res
			})
			.finally(() => {
				resolve(null)
			})
	})
}
onMounted(() => {
	if (props.model == "p-edit") {
		tableProp.value.push({
			label: "操作",
			width: 130,
			prop: "operations",
			fixed: "right",
			needSlot: true
		})
	}
	Promise.all([getDictByCodeList()]).then(() => {
		getTableData()
	})
})
</script>
<template>
	<div class="tab-mat">
		<div class="tab-left">
			<Query
				:queryArrList="queryArrList"
				@getQueryData="getTableData"
				class="custom-q"
				v-if="props.needQuery"
			/>
			<PitayaTable
				ref="tableRef"
				:columns="tableProp"
				:table-data="lastTableData"
				:need-index="true"
				:need-pagination="true"
				:single-select="props.singleSelected"
				:need-selection="true"
				:total="pageTotal"
				@onSelectionChange="onDataSelected"
				@on-current-page-change="onCurrentPageChange"
				:table-loading="tableLoading"
			>
				<template #buyUnit="{ rowData }">
					<dict-tag
						:options="dictOptions.INVENTORY_UNIT"
						:value="rowData.buyUnit"
					/>
				</template>
				<template #evaluation="{ rowData }">
					<cost-tag :value="rowData.evaluation" />
				</template>
				<template #purchaseAmount="{ rowData }">
					<span :class="{ 'num-blue': props.model == 'p-edit' }">
						<cost-tag :value="rowData.purchaseAmount"
					/></span>
				</template>
				<template #planAmount="{ rowData }">
					<cost-tag :value="rowData.planAmount" />
				</template>
				<template #num_view="{ rowData }">
					<span class="num-blue" v-if="props.type == 'merge'">
						{{ rowData.num_view || "0" }}</span
					>
					<span
						:class="
							rowData.num == rowData.purchaseNum ? 'num-blue' : 'red-blue'
						"
						v-else
					>
						{{ rowData.num_view || "0" }}</span
					>
				</template>
				<template #purchaseNum_view="{ rowData }">
					<span class="num-blue" v-if="props.type == 'merge'">
						{{ rowData.purchaseNum_view || "0" }}</span
					>
					<span
						:class="
							rowData.num == rowData.purchaseNum ? 'num-blue' : 'red-blue'
						"
						v-else
					>
						{{ rowData.purchaseNum_view || "0" }}</span
					>
				</template>
			</PitayaTable>
		</div>
		<!--直接显示关联的需求计划 -->
		<div class="tab-right content tab-border" v-if="props.model == 'mp-view'">
			<Title :title="needPlanTitle" />
			<matPurchasePlan
				style="padding-top: 2px"
				:mat-id="curMatId"
				:id="props.id"
				:type="props.type"
			/>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.custom-q {
	margin: 10px 0px -10px 10px !important;
}

.tab-mat {
	display: flex;
	justify-content: space-between;
	flex-direction: row;
	height: 100%;
	width: 100%;

	.tab-left {
		width: calc(100% - 500px);
		flex: 1;
		height: 100%;
	}

	.tab-border {
		//	padding-right: 10px !important;
		border-left: 1px solid #ccc;
		height: 100%;
	}

	.tab-right {
		width: 500px;
	}
	.num-blue {
		color: $---color-info2;
	}
	.red-blue {
		color: $---color-error;
	}
}
</style>
