<script lang="ts" setup>
import { toRef, ref, onMounted, reactive } from "vue"
import materialDetails from "@/app/baseline/views/plan/components/purchase/materialDetails.vue"
import { FormElementType } from "@/app/baseline/views/components/define"
import { Dict<PERSON>pi } from "@/app/baseline/api/dict"
import { PlanMerge } from "@/app/baseline/views/plan/components/define"
import { PlanMergeApi } from "@/app/baseline/api/plan/planMerge"
import CostTag from "@/app/baseline/views/components/costTag.vue"
import { BaseLineSysApi } from "@/app/baseline/api/system"

const props = defineProps({
	id: {
		type: [String, Number],
		required: false
	},
	mergePlanId: {
		type: [String, Number],
		required: false
	},
	purchasePlanId: {
		type: [String, Number],
		required: false
	},
	modal: {
		type: String,
		required: false,
		default: "" //"mat" 以物资视角浏览
	}
})
const currentId = toRef(props, "id")
const currentMergePlanId = toRef(props, "mergePlanId")
const purchasePlanId = toRef(props, "purchasePlanId")
console.log(currentMergePlanId.value)
const drawerLeftTitle = {
	name: ["合并计划信息"],
	icon: ["fas", "square-share-nodes"]
}
const drawerRightTitle = {
	name: ["合并计划明细"],
	icon: ["fas", "square-share-nodes"]
}

const formModal = reactive<PlanMerge>({
	id: props.id
})
const formEl: FormElementType[] = [
	{
		label: "合并计划号",
		name: "code"
	},
	{
		label: "合并计划名称",
		name: "label"
	},
	// {
	// 	label: "需求类型",
	// 	name: "planType"
	// },
	{
		label: "计划年度",
		name: "year",
		data: DictApi.getFutureYears(-1, 1)
	},
	{
		label: "需求计划数量",
		name: "planNum"
	},
	{
		label: "物资数量",
		name: "num"
	},
	{
		label: "预估采购金额",
		name: "purchaseAmount",
		type: "money"
	},
	{ label: "线路", name: "lineNoId_view" },
	{ label: "专业", name: "majorId_view" },
	{ label: "段区", name: "depotId" },
	{
		label: "费用类别",
		name: "expenseCategory_view",
		type: "select",
		data: []
	},
	{
		label: "备注说明",
		name: "remark",
		type: "textarea"
	},
	{
		label: "合并部门",
		name: "sysOrgId_view"
	},
	{
		label: "创建人",
		name: "createdBy_view"
	},
	{
		label: "创建时间",
		name: "createdDate"
	}
]

const drawerLoading = ref(false)
const getInfoById = () => {
	return new Promise<void>((resolve) => {
		drawerLoading.value = true
		PlanMergeApi.getPlanMerge(currentId.value)
			.then((res: any) => {
				console.log(res)
				Object.assign(formModal, res)
			})
			.finally(() => {
				drawerLoading.value = false
				resolve()
			})
	})
}

defineOptions({
	name: "viewDrawer"
})

const segmentList = ref<[]>([])
function getSegmentList() {
	BaseLineSysApi.getSegmentList().then((res) => {
		segmentList.value = res
	})
}

onMounted(async () => {
	Promise.all([getSegmentList(), getInfoById()]).then(() => {
		const found: { [propName: string]: any } =
			segmentList.value.find(
				(item: { [propName: string]: any }) => item.id == formModal["depotId"]
			) || {}
		if (found) {
			formModal["depotId"] = found.label
		}
	})
})
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<!-- 左侧表单区域 -->
		<div class="drawer-column left">
			<div class="rows">
				<Title :title="drawerLeftTitle" />
				<el-descriptions
					size="small"
					:column="1"
					:border="true"
					class="content"
				>
					<el-descriptions-item
						v-for="(el, index) in formEl"
						label-align="center"
						:label="el.label"
						:key="index"
					>
						<span v-if="el.type == 'money'">
							<cost-tag :value="formModal[el.name]" />
						</span>
						<span v-else>
							{{ formModal[el.name] ? formModal[el.name] : "---" }}
						</span>
					</el-descriptions-item>
				</el-descriptions>
			</div>
		</div>
		<!-- 右侧table区域 -->
		<div class="drawer-column right">
			<div class="rows">
				<Title :title="drawerRightTitle" />
				<!-- 仅显示 物资明细 -->
				<materialDetails
					:id="purchasePlanId"
					:mergePlanId="currentMergePlanId"
				/>
			</div>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index";

.drawer-container {
	.left {
		width: 310px;
	}
	.right {
		width: calc(100% - 310px);
	}
	.tab-mat {
		height: 100%;
	}
}
</style>
