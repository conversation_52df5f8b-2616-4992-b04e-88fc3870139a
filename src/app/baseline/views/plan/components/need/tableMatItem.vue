<script setup lang="ts">
import { watch, ref, onMounted } from "vue"
import { DictApi } from "@/app/baseline/api/dict"

import { defineProps } from "vue"
import { useMessageBoxInit } from "@/app/baseline/views/components/messageBox"

import { useTbInit } from "@/app/baseline/views/components/tableBase"
import { NeedApi } from "@/app/baseline/api/plan/need"
import MatList from "@/app/baseline/views/plan/components/matManualList.vue"
import costTag from "@/app/baseline/views/components/costTag.vue"
import { toFixedTwo } from "@/app/baseline/utils"
import XEUtils from "xe-utils"

const { showDelConfirm } = useMessageBoxInit()
/*-------------------初始化表格-start-------------------*/
const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	tbBtnLoading,
	pageSize,
	currentPage,
	pageTotal,
	selectedTableList,
	fetchParam,
	tbBtns,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange,
	onDataSelected,
	onBtnClick
} = useTbInit()
tableProp.value = [
	{ label: "物资编码", prop: "content_code", width: 150 },
	{ label: "物资名称", prop: "content_label", width: 150 },
	{ label: "物资分类编码", prop: "content_materialTypeCode", width: 100 },
	{ label: "物资分类名称", prop: "content_materialTypeLabel", width: 200 },
	{ label: "规格型号", prop: "content_version", needSlot: false, width: 150 },
	{
		label: "技术参数",
		prop: "content_technicalParameter",
		needSlot: false,
		minWidth: 100
	},
	{ label: "采购单位", prop: "content_buyUnit", needSlot: true, width: 90 },
	{
		label: "预估采购单价",
		prop: "evaluation",
		needSlot: true,
		width: 120,
		align: "right"
	},
	{
		label: "需求数量",
		prop: "num",
		needSlot: true,
		width: 120,
		fixed: "right",
		align: "right"
	}
]
onDataSelected.value = (rowList: any) => {
	selectedTableList.value = rowList
	if (tbBtns.value.length > 1) {
		if (rowList.length > 0) tbBtns.value[1].disabled = false
		else tbBtns.value[1].disabled = true
	}
}
fetchFunc.value = (param: Record<string, any>) => {
	return new Promise((resolve) => {
		NeedApi.getMatListByAppId({
			planNeedId: props.id as number,
			...param
		}).then((_r) => {
			XEUtils.map(_r.rows, (_d: { [propName: string]: any }) => {
				if (_d.content)
					Array.from(Object.keys(_d?.content))?.map(
						(_k) => (_d[`content_${_k}`] = _d.content[_k])
					)
			})
			resolve(_r)
		})
	})
}

tbBtns.value = []
/*-------------------初始化表格-end-------------------*/

export interface Props {
	id?: string | number //需求清单ID
	model?: string //显示模式  view : 查看, edit : 编辑
}
const props = withDefaults(defineProps<Props>(), {
	id: "",
	model: "view"
})
const emits = defineEmits(["onTableEndBtn", "onDataChange"])

const dictOptions = ref<Record<string, any[]>>({
	INVENTORY_UNIT: []
})
function getDictByCodeList(): Promise<null> {
	return new Promise((resolve) => {
		DictApi.getDictByCodeList(dictOptions)
			.then((res) => {
				dictOptions.value = res
			})
			.finally(() => {
				resolve(null)
			})
	})
}

const drawerSize = 1500
const queryArrList = [
	{
		name: "物资编码",
		key: "materialCode",
		placeholder: "请输入物资编码",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资名称",
		key: "materialLabel",
		placeholder: "请输入物资名称",
		enableFuzzy: true,
		type: "input"
	}
]
const showAddMatDrawer = ref(false) //添加物資抽屜

function onTableEndBtn(btnName: any) {
	emits("onTableEndBtn", btnName)
}

function getTableData(data: { [propName: string]: any }) {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = data
	fetchTableData()
}

function addPalnNeedItem(datas: any) {
	return NeedApi.addPlanNeedItemList(datas)
}

function deletePlanNeedItem(id: any) {
	return NeedApi.deletePlanNeedItem(id)
}

function showMatList() {
	showAddMatDrawer.value = true
	return Promise.reject()
}

function onRowDelete(row: any) {
	showDelConfirm().then(() => {
		deletePlanNeedItem(row.id).then(() => {
			emits("onDataChange")
			fetchTableData()
		})
	})
}

function onBtnDelete() {
	return new Promise((resolve, reject) => {
		if (selectedTableList.value?.length > 0) {
			showDelConfirm().then(() => {
				const _ids: string[] = []
				selectedTableList.value.map((_d) => {
					_ids.push(_d.id)
				})
				deletePlanNeedItem(_ids.join(",")).then(() => {
					emits("onDataChange")
					fetchTableData()
				})
			})
		}
		reject()
	})
}

function onMatDataSelected(data: any) {
	const _datas: { [propName: string]: any } = []
	data.map((_d: any) => {
		_datas.push({
			planNeedId: props.id,
			materialId: _d.id,
			num: 0
		})
	})
	addPalnNeedItem(_datas).then((_r) => {
		showAddMatDrawer.value = false
		emits("onDataChange")
		fetchTableData()
	})
}

const _lock: { [propName: string]: number } = {}
function checkItemNum(data: any) {
	data.num = isNaN(parseInt(data.num)) ? 0 : parseInt(data.num)
	const _data = tableData.value.find((_d: any) => _d.id == data.id)
	if (_data) _data.num = data.num
	if (_lock[data.id]) window.clearTimeout(_lock[data.id])
	_lock[data.id] = window.setTimeout(() => updateItemNum(data), 500)
}

function updateItemNum(data: any) {
	NeedApi.updatePlanNeedItem({
		id: data.id,
		num: data.num
	}).then(() => {
		ElMessage.success("操作成功")
		if (_lock[data.id]) window.clearTimeout(_lock[data.id])
	})
}

onMounted(() => {
	getDictByCodeList().then(() => {
		if (props.id) {
			fetchTableData()
		}
		if (props.model == "edit") {
			tableProp.value.push({
				label: "操作",
				width: 100,
				prop: "operations",
				fixed: "right",
				needSlot: true
			})
			tbBtns.value.push({
				name: "添加物资",
				icon: ["fas", "square-plus"],
				click: showMatList
			})
			tbBtns.value.push({
				name: "批量移除",
				icon: ["fas", "trash-alt"],
				disabled: true,
				click: onBtnDelete
			})
		}
	})
})

watch(
	() => props.id,
	(_value) => {
		fetchTableData()
	}
)
</script>
<template>
	<div>
		<Query
			:queryArrList="queryArrList"
			@getQueryData="getTableData"
			class="custom-q"
		/>
		<PitayaTable
			ref="tableRef"
			:columns="tableProp"
			:table-data="tableData"
			:need-index="true"
			:need-pagination="true"
			:single-select="false"
			:need-selection="props.model == 'edit'"
			:total="pageTotal"
			@onSelectionChange="onDataSelected"
			@on-current-page-change="onCurrentPageChange"
			:table-loading="tableLoading"
		>
			<template #num="{ rowData }">
				<el-input-number
					:controls="false"
					v-if="props.model == 'edit'"
					class="number-input"
					v-model="rowData.num"
					@change="checkItemNum(rowData)"
					:min="0"
				/>
				<template v-else>{{ toFixedTwo(rowData.num, 0) }}</template>
			</template>
			<template #content_buyUnit="{ rowData }">
				{{
					dictOptions.INVENTORY_UNIT.find(
						(_c) => _c.value == rowData.content_buyUnit
					)?.label || "---"
				}}
			</template>
			<template #evaluation="{ rowData }">
				<cost-tag :value="rowData.evaluation" />
			</template>
			<template #operations="{ rowData }">
				<el-button v-btn link @click="onRowDelete(rowData)">
					<font-awesome-icon :icon="['fas', 'trash-can']" />
					<span class="table-inner-btn">移除</span>
				</el-button>
			</template>
			<template #footerOperateLeft>
				<ButtonList
					class="btn-list"
					:is-not-radius="true"
					:button="tbBtns"
					v-loading="tbBtnLoading"
					@on-btn-click="onBtnClick"
				/>
			</template>
		</PitayaTable>

		<Drawer
			:size="drawerSize"
			v-model:drawer="showAddMatDrawer"
			:destroyOnClose="true"
		>
			<MatList
				:table-data="tableData"
				@onSelected="onMatDataSelected"
				@onClosed="showAddMatDrawer = false"
			/>
		</Drawer>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.custom-q {
	margin: 10px 0px -10px 10px !important;
}
.number-input {
	height: 26px;
	line-height: 26px;
	width: 90px;
	:deep(.el-input__inner) {
		height: 100%;
	}
}
</style>
