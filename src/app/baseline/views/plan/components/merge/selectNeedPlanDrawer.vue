<script lang="ts" setup>
import { ref } from "vue"
import { ElMessage } from "element-plus"
import TableNeedPlan from "@/app/baseline/views/plan/components/merge/tableNeedPlan.vue"
import { PlanMergeItemApi } from "@/app/baseline/api/plan/planMergeItem"
import { useMessageBoxInit } from "@/app/baseline/views/components/messageBox.ts"
import { PlanPurchaseMergeItemAPi } from "@/app/baseline/api/plan/planPurchaseMergeItem"
const { showAddConfirm } = useMessageBoxInit()
interface Props {
	mergePlanId: string | number
	mergePlanInfo: object
}
const props = defineProps<Props>()
const emits = defineEmits(["onSaveOrClose"])

const drawerRightTitle = {
	name: ["选择需求计划"],
	icon: ["fas", "square-share-nodes"]
}

const formBtnList = [
	{ name: "取消", icon: ["fas", "circle-minus"] },
	{ name: "保存", icon: ["fas", "file-signature"] }
]
const drawerLoading = ref(false)
const selectedTableList = ref<{[propName:string]:any}[]>([]) //
const onDataSelected = (rowList: {[propName:string]:any}[]) => {
	selectedTableList.value = rowList
}
// 左侧按钮点击
const onFormBtnList = (btnName: string | undefined) => {
	if (btnName === "保存") {
		if (selectedTableList.value.length <= 0) {
			ElMessage.warning("请选择需求计划")
			return
		}
		showAddConfirm(() => {
			drawerLoading.value = true
			const arrParams:{[propName:string]:any} = []
			selectedTableList.value.forEach((needPlan) => {
				arrParams.push({
					mergePlanId: props.mergePlanId,
					planId: needPlan.id
				})
			})
			PlanMergeItemApi.addBatchPlanMergeItem(arrParams)
				.then(() => {
					ElMessage.success("保存成功")
					emits("onSaveOrClose", true)
				})
				.finally(() => {
					drawerLoading.value = false
				})
		})
	}
	if (btnName === "取消") {
		emits("onSaveOrClose", false)
		return
	}
}
defineOptions({
	name: "SelectNeedPlanDrawer"
})
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<!-- 右侧table区域 -->
		<div class="drawer-column right">
			<div class="rows">
				<Title :title="drawerRightTitle" />
				<TableNeedPlan
					modal="all"
					:id="props.mergePlanId"
					:merge-plan-info="props.mergePlanInfo"
					:need-query="true"
					@on-data-selected="onDataSelected"
				/>
			</div>
			<ButtonList
				class="footer"
				:button="formBtnList"
				@on-btn-click="onFormBtnList"
			/>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index";

.drawer-container {
	.right {
		width: 100%;
	}
}
</style>
