<script lang="ts" setup>
import { ref, onMounted } from "vue"
import CostTag from "@/app/baseline/views/components/costTag.vue"
import { useTbInit } from "@/app/baseline/views/components/tableBase"
import LineTag from "@/app/baseline/views/components/lineTag.vue"
import { PlanPurchaseMaterialItemApi } from "@/app/baseline/api/plan/planPurchaseMaterialItem"
import { BaseLineSysApi } from "@/app/baseline/api/system"
import { MatApplyObj } from "@/app/baseline/views/material/components/define"
import { DictApi } from "@/app/baseline/api/dict"
import { toFixedTwo } from "@/app/baseline/utils"

export interface Props {
	id: string | number
	matId: string | number
	matObj?: MatApplyObj
}
const props = defineProps<Props>()

const emits = defineEmits(["onSuccess", "onClose"])

const loading = ref(false)
const drawerLeftTitle = {
	name: ["物资信息"],
	icon: ["fas", "square-share-nodes"]
}
const drawerRightTitle = {
	name: ["关联需求计划"],
	icon: ["fas", "square-share-nodes"]
}
const formBtnList = [{ name: "取消", icon: ["fas", "circle-minus"] }]

function onFormBtnList() {
	emits("onClose")
}

const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	tbBtnLoading,
	pageSize,
	currentPage,
	pageTotal,
	selectedTableList,
	fetchParam,
	tbBtns,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange,
	onDataSelected,
	onBtnClick
} = useTbInit()
tableProp.value = [
	{ label: "需求计划号", prop: "code", width: 160 },
	{ label: "需求计划名称", prop: "label", width: 200 },
	{ label: "需求部门", prop: "sysOrgId_view", width: 160 },
	{ label: "专业", prop: "majorId_view", width: 120 },
	{ label: "线路", prop: "lineNoId", needSlot: true, width: 100 },
	{ label: "段区", prop: "depotId_view", width: 120 },
	{
		label: "预估采购单价",
		prop: "evaluation",
		needSlot: true,
		width: 120,
		align: "right"
	},
	{
		label: "需求数量",
		prop: "num",
		align: "right",
		needSlot: true,
		width: 120
	},
	{
		label: "预估金额",
		prop: "purchaseAmount",
		needSlot: true,
		width: 100,
		align: "right"
	},
	{ label: "需求原因", prop: "remark", minWidth: 200, align: "left" },
	{ label: "创建人", prop: "createdBy_view", width: 90 },
	{ label: "创建时间", prop: "createdDate", width: 160 }
]
tbBtns.value = []
function getTableData() {
	//获取一个采购计划 物资对应得需求计划
	fetchParam.value = { purchasePlanId: props.id, materialId: props.matId }
	fetchFunc.value =
		PlanPurchaseMaterialItemApi.getPlanPurchaseMaterialItemNeedPlanList
	currentPage.value = 1
	fetchTableData()
}
const dictOptions = ref<Record<string, any[]>>({
	INVENTORY_UNIT: []
})

function getDictByCodeList(): Promise<null> {
	return new Promise((resolve) => {
		DictApi.getDictByCodeList(dictOptions)
			.then((res) => {
				dictOptions.value = res
			})
			.finally(() => {
				resolve(null)
			})
	})
}
const lineList = ref<[]>([])
function getLineList() {
	BaseLineSysApi.getLineList().then((res) => {
		lineList.value = res
	})
}
const formatData = (data: any) => {
	return data ? data : "---"
}
const buyUnit_view = ref("---")
onMounted(() => {
	Promise.all([getLineList(), getDictByCodeList()]).then(() => {
		const found = dictOptions.value.INVENTORY_UNIT.find(
			(item) => item.value == props.matObj.buyUnit
		)
		if (found) {
			buyUnit_view.value = found.label
		}
		getTableData()
	})
})
</script>
<template>
	<div class="drawer-container" v-loading="loading">
		<!-- 左侧表单区域 -->
		<div class="drawer-column left">
			<div class="rows">
				<Title :title="drawerLeftTitle" v-if="!loading" />
				<el-descriptions size="small" :column="1" border class="content">
					<el-descriptions-item label="物资编码">
						{{ formatData(matObj.code) }}
					</el-descriptions-item>
					<el-descriptions-item label="物资名称">
						{{ formatData(matObj.label) }}
					</el-descriptions-item>
					<el-descriptions-item label="物资分类编码">
						{{ formatData(matObj.materialCode) }}
					</el-descriptions-item>
					<el-descriptions-item label="物资分类名称">
						{{ formatData(matObj.materialTypeLabel) }}
					</el-descriptions-item>
					<el-descriptions-item label="规格型号">
						{{ formatData(matObj.version) }}
					</el-descriptions-item>
					<el-descriptions-item label="技术参数">
						{{ formatData(matObj.technicalParameter) }}
					</el-descriptions-item>
					<el-descriptions-item label="采购单位">
						{{ formatData(buyUnit_view) }}
					</el-descriptions-item>
					<el-descriptions-item label="预估采购单价">
						<CostTag :value="matObj.evaluation" />
					</el-descriptions-item>
					<el-descriptions-item label="需求数量">
						{{ formatData(matObj.matCodeNum) }}
					</el-descriptions-item>
					<el-descriptions-item label="预估采购金额">
						<CostTag :value="matObj.purchaseAmount" />
					</el-descriptions-item>
				</el-descriptions>
			</div>
		</div>
		<!-- 右侧table区域 -->
		<div class="drawer-column right">
			<div class="rows">
				<Title :title="drawerRightTitle" />
				<PitayaTable
					:ref="tableRef"
					:columns="tableProp"
					:tableData="tableData"
					:total="pageTotal"
					:single-select="false"
					:need-selection="true"
					:need-index="true"
					@onSelectionChange="onDataSelected"
					@on-current-page-change="onCurrentPageChange"
					:table-loading="tableLoading"
				>
					<template #num="{ rowData }">
						{{ toFixedTwo(rowData.num, 0) }}
					</template>

					<template #evaluation="{ rowData }">
						<cost-tag :value="rowData.evaluation" />
					</template>
					<template #purchaseAmount="{ rowData }">
						<cost-tag :value="rowData.purchaseAmount" />
					</template>
					<template #lineNoId="{ rowData }">
						<line-tag :options="lineList" :value="rowData.lineNoId" />
					</template>
					<template #footerOperateLeft>
						<ButtonList
							class="btn-list"
							:is-not-radius="true"
							:button="tbBtns"
							:loading="tbBtnLoading"
							@on-btn-click="onBtnClick"
						/>
					</template>
				</PitayaTable>
			</div>
			<ButtonList
				class="footer"
				:button="formBtnList"
				@on-btn-click="onFormBtnList"
			/>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index";
.el-descriptions {
	:deep(.el-descriptions__label) {
		width: 200px;
	}
}
.left {
	width: 310px;
}

.right {
	width: calc(100% - 310px);
}
</style>
