<script setup lang="ts">
import { watch, onMounted } from "vue"
import { useTbInit } from "@/app/baseline/views/components/tableBase"
import CostTag from "@/app/baseline/views/components/costTag.vue"
import { PlanMergeItemApi } from "@/app/baseline/api/plan/planMergeItem"
import { PlanPurchaseMaterialItemApi } from "@/app/baseline/api/plan/planPurchaseMaterialItem"
import { toFixedTwo } from "@/app/baseline/utils"

//根据物资ID查看关联的需求计划列表
interface Props {
	matId: number | string
	id: number | string //业务ID
	type: string //merge 、 purchase
}
const props = defineProps<Props>()
const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	tbBtnLoading,
	pageSize,
	currentPage,
	pageTotal,
	selectedTableList,
	fetchParam,
	tbBtns,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange,
	onDataSelected,
	onBtnClick
} = useTbInit()

//选中的table Row
tableProp.value = [
	{ label: "需求计划号", prop: "code", width: 160, fixed: "left" },
	{ label: "需求计划名称", prop: "label", minWidth: 150 },
	{ label: "需求部门", prop: "sysOrgId_view", width: 150 },
	{ label: "需求数量", prop: "num", needSlot: true, align: "right", width: 85 },
	{
		label: "预估采购金额",
		prop: "purchaseAmount",
		align: "right",
		needSlot: true,
		width: 150
	}
]

tbBtns.value = []
function getTableData(data?: { [propName: string]: any }) {
	if (props.type == "merge") {
		//获取一个合并计划 物资对应得需求计划
		fetchParam.value = { mergePlanId: props.id, materialId: props.matId }
		fetchFunc.value = PlanMergeItemApi.getPlanMergeItemMaterialNeedPlanList
	} else {
		//获取一个采购计划 物资对应得需求计划
		fetchParam.value = { purchasePlanId: props.id, materialId: props.matId }
		fetchFunc.value =
			PlanPurchaseMaterialItemApi.getPlanPurchaseMaterialItemNeedPlanList
	}
	currentPage.value = 1
	fetchParam.value = {
		...fetchParam.value,
		...data
	}
	fetchTableData()
}
watch(
	() => props.matId,
	(id: any) => {
		if (id) {
			getTableData()
		}
	},
	{ immediate: false }
)
onMounted(() => {})
</script>
<template>
	<div>
		<PitayaTable
			ref="refTable"
			:columns="tableProp"
			:table-data="tableData"
			:need-index="true"
			:need-pagination="false"
			:single-select="false"
			:need-selection="false"
			:total="pageTotal"
			@on-current-page-change="onCurrentPageChange"
			:table-loading="tableLoading"
		>
			<template #purchaseAmount="{ rowData }">
				<cost-tag :value="rowData.purchaseAmount" />
			</template>

			<template #num="{ rowData }">
				{{ toFixedTwo(rowData.num, 0) }}
			</template>
		</PitayaTable>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
