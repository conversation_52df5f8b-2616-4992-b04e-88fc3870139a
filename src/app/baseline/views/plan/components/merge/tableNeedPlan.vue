<script lang="ts" setup>
import { ref, onMounted } from "vue"
import { ElMessage } from "element-plus"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import { appStatus, DictApi } from "@/app/baseline/api/dict"
import { useTbInit } from "@/app/baseline/views/components/tableBase"
import NeedPlanDrawer from "@/app/baseline/views/plan/components/needPlanDrawer.vue"
import { BaseLineSysApi } from "@/app/baseline/api/system"
import CostTag from "@/app/baseline/views/components/costTag.vue"
import { PlanMergeItemApi } from "@/app/baseline/api/plan/planMergeItem"
import LineTag from "@/app/baseline/views/components/lineTag.vue"
import { convertContentTableData } from "@/app/baseline/utils"
import { PlanApi } from "@/app/baseline/api/plan/plan"
import { PlanPurchaseMaterialItemApi } from "@/app/baseline/api/plan/planPurchaseMaterialItem"
import { useUserStore } from "@/app/platform/store/modules/user"
import { useMessageBoxInit } from "@/app/baseline/views/components/messageBox"
import needPlanSelector from "./needPlanSelector.vue"
import { map } from "lodash-es"
import { AddPlanMergeItemRequest } from "@/app/baseline/api/defines"
import { modalSize } from "@/app/baseline/utils/layout-config"

const { showDelConfirm } = useMessageBoxInit()
interface Props {
	id: string | number
	needOpt?: string[]
	needQuery?: boolean
	modal: string // ”all" 全部 | "purchase" 采购计划 | “merge" 合并计划
	mergePlanInfo?: { [propName: string]: any } // all 时 当前合并计划信息的年度信息
}

const props = defineProps<Props>()
const emits = defineEmits(["onDataSelected", "onSelectChange"])
const isContent = props.modal != "all" && props.modal != "purchase"
const userStore = useUserStore()
const { userInfo } = storeToRefs(userStore)
//查询
const queryArrList = [
	{
		name: "部门",
		key: "sysOrgId",
		type: "treeSelect",
		treeApi: BaseLineSysApi.getDepartmentTree,
		placeholder: "请选择所属部门"
	},
	{
		name: "需求计划号",
		key: "code",
		placeholder: "请输入需求计划号",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "需求计划名称",
		key: "label",
		placeholder: "请输入需求计划名称",
		enableFuzzy: true,
		type: "input"
	}
]

const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	tbBtnLoading,
	pageSize,
	currentPage,
	pageTotal,
	selectedTableList,
	fetchParam,
	tbBtns,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange,
	onDataSelected,
	onBtnClick
} = useTbInit()
tableProp.value = [
	// { label: "计划年度", prop: "year", width: 85 },
	{ label: "需求部门", prop: "sysOrgId_view", width: 100, fixed: "left" },
	{ label: "需求计划号", prop: "code", width: 160, fixed: "left" },
	{ label: "需求计划名称", prop: "label", minWidth: 150 },
	{ label: "专业", prop: "majorId_view", width: 120 },
	{
		label: "线路",
		prop: "lineNoId",
		needSlot: true,
		width: 120
	},
	{ label: "段区", prop: "depotId_view", needSlot: true, width: 120 },
	{ label: "费用类别", prop: "expenseCategory_view", width: 120 },
	{
		label: "预估金额",
		prop: "purchaseAmount",
		needSlot: true,
		width: 150,
		align: "right"
	},
	{ label: "需求原因", prop: "remark", minWidth: 150, align: "left" },
	{ label: "创建人", prop: "createdBy_view", width: 120 },
	{ label: "创建时间", prop: "createdDate", width: 150 }
]
tbBtns.value = [
	{
		name: "选择需求计划",
		icon: ["fas", "square-plus"],
		click: () => openSelectDrawer()
	},
	{
		name: "批量移除",
		icon: ["fas", "trash-alt"],
		disabled: true,
		click: () => batchDelete()
	}
]

onDataSelected.value = (rowList: { [propName: string]: any }[]) => {
	//rowList有bug
	selectedTableList.value = tableRef.value.pitayaTableRef!.getSelectionRows()
	tbBtns.value[1].disabled = selectedTableList.value <= 0
	emits("onDataSelected", selectedTableList.value)
}
const batchDelete = (id?: any) => {
	return new Promise<void>((resolve, reject) => {
		let arrIds = [id]
		if (!id) {
			arrIds = selectedTableList.value.map((item) => item.id)
		}
		showDelConfirm()
			.then(() => {
				PlanMergeItemApi.deletePlanMergeItem(arrIds.join(","))
					.then(() => {
						ElMessage.success("移除成功")
						emits("onSelectChange")
						getTableData()
						resolve()
					})
					.catch(() => reject())
			})
			.catch(() => reject())
	})
}

//tabel row操作按钮
//查看需求计划详情
const editNeedPlanId = ref<any>("")
const showNeedPlanItemDrawer = ref<boolean>(false)
const onCloseDrawer = (refresh: boolean) => {
	if (refresh) {
		getTableData()
	}
	showNeedPlanItemDrawer.value = false
}
const onRowView = (row: any) => {
	editNeedPlanId.value = isContent ? row.contentId : row.id
	showNeedPlanItemDrawer.value = true
}
const onRowDelete = (row: any) => {
	batchDelete(row.id)
}
const lastTableData = ref<any[]>([])
const getTableData = (data?: any) => {
	if (props.modal === "all") {
		//查看没有被选择过的所有的需求计划列表
		fetchParam.value = {
			inMergeItem: false,
			bpmStatus: appStatus.approved,
			planType: 1,
			sysOrgId: props?.mergePlanInfo?.sysOrgId,
			year: props?.mergePlanInfo?.year,
			lineNoId: props.mergePlanInfo?.lineNoId
				? `*${props.mergePlanInfo?.lineNoId}*`
				: null,
			majorId: props.mergePlanInfo?.majorId
		}
		fetchFunc.value = PlanApi.getList
	} else if (props.modal === "purchase") {
		//根据一个purchasePlanId 获取需求计划明细
		fetchParam.value = { purchasePlanId: props.id }
		fetchFunc.value = PlanPurchaseMaterialItemApi.getPlanPurchaseNeedPlanList
	} else {
		//根据一个mergePlanId 获取需求计划明细
		fetchParam.value = { mergePlanId: props.id }
		fetchFunc.value = PlanMergeItemApi.getPlanMergeItemList
	}
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		...data
	}
	fetchTableData()
}

/**
 * 补丁
 * 监听tableData 重组lastTableData
 */
watch([tableData], () => {
	if (isContent) {
		lastTableData.value = convertContentTableData(tableData.value)
	} else {
		lastTableData.value = tableData.value
	}
})
const showSelectDrawer = ref<boolean>(false)
const drawerSizeNeedPlan = 1200
const openSelectDrawer = () => {
	showSelectDrawer.value = true
	return Promise.reject()
}

/**
 * 添加需求计划操作
 * @param needList
 */
async function handleSavePlanList(needList: any[]) {
	const matList =
		map(needList, (v) => ({ planId: v.id, mergePlanId: props.id })) || []
	await PlanMergeItemApi.addBatchPlanMergeItem(
		matList as AddPlanMergeItemRequest[]
	)

	ElMessage.success("保存成功")

	getTableData()
	showSelectDrawer.value = false

	emits("onSelectChange")
}

//获取字典,多个字典可同时获取
const dictOptions = ref<Record<string, any[]>>({
	COST_CATEGORY: []
})
function getDictByCodeList(): Promise<null> {
	return new Promise((resolve) => {
		DictApi.getDictByCodeList(dictOptions)
			.then((res) => {
				dictOptions.value = res
			})
			.finally(() => {
				resolve(null)
			})
	})
}
const lineList = ref<[]>([])
function getLineList() {
	BaseLineSysApi.getLineList().then((res) => {
		lineList.value = res
	})
}
const segmentList = ref<[]>([])
function getSegmentList() {
	BaseLineSysApi.getSegmentList().then((res) => {
		segmentList.value = res
	})
}
onMounted(() => {
	if (props?.needOpt) {
		if (props?.needOpt?.length > 0) {
			tableProp.value.push({
				label: "操作",
				width: 80 * props?.needOpt?.length,
				prop: "operations",
				fixed: "right",
				needSlot: true
			})
		}
	}
	Promise.all([getLineList(), getDictByCodeList(), getSegmentList()]).then(
		() => {
			if (props.id) {
				getTableData()
			}
		}
	)
})

defineOptions({
	name: "TableNeedPlan"
})
const getNeedCount = () => {
	return pageTotal.value
}
defineExpose({
	getNeedCount
})
</script>
<template>
	<div>
		<Query
			v-if="props.needQuery"
			:queryArrList="queryArrList"
			@getQueryData="getTableData"
			class="custom-q"
		/>
		<PitayaTable
			ref="tableRef"
			:columns="tableProp"
			:tableData="lastTableData"
			:total="pageTotal"
			:single-select="false"
			:need-selection="true"
			:need-index="true"
			@onSelectionChange="onDataSelected"
			@on-current-page-change="onCurrentPageChange"
			:table-loading="tableLoading"
		>
			<template #purchaseAmount="{ rowData }">
				<cost-tag :value="rowData.purchaseAmount" />
			</template>
			<template #depotId_view="{ rowData }">
				<dict-tag :options="segmentList" :value="rowData.depotId" />
			</template>
			<template #expenseCategory="{ rowData }">
				<dict-tag
					:options="dictOptions.COST_CATEGORY"
					:value="rowData.expenseCategory"
				/>
			</template>
			<template #lineNoId="{ rowData }">
				<line-tag :options="lineList" :value="rowData.lineNoId" />
			</template>
			<template #operations="{ rowData }">
				<el-button
					v-btn
					link
					@click="onRowView(rowData)"
					v-if="props.needOpt?.includes('view')"
				>
					<font-awesome-icon :icon="['fas', 'eye']" />
					<span class="table-inner-btn">查看</span>
				</el-button>
				<el-button
					v-btn
					link
					@click="onRowDelete(rowData)"
					v-if="props.needOpt?.includes('drop')"
				>
					<font-awesome-icon :icon="['fas', 'trash-can']" />
					<span class="table-inner-btn">移除</span>
				</el-button>
			</template>
			<template #footerOperateLeft>
				<ButtonList
					v-if="props.needOpt?.includes('drop')"
					class="btn-list"
					:is-not-radius="true"
					:button="tbBtns"
					v-loading="tbBtnLoading"
					@on-btn-click="onBtnClick"
				/>
			</template>
		</PitayaTable>
		<!-- 需求计划详情窗口 -->
		<Drawer
			size="auto"
			v-model:drawer="showNeedPlanItemDrawer"
			:destroyOnClose="true"
		>
			<NeedPlanDrawer
				:id="editNeedPlanId"
				model="view"
				@on-closed="onCloseDrawer"
			/>
		</Drawer>
		<!-- 选择需求计划弹窗 -->
		<Drawer
			:size="modalSize.lg"
			v-model:drawer="showSelectDrawer"
			:destroyOnClose="true"
		>
			<need-plan-selector
				:multiple="true"
				title="选择需求计划"
				:table-req-params="{
					inMergeItem: false,
					bpmStatus: appStatus.approved,
					planType: 1,
					sysOrgId: props?.mergePlanInfo?.sysOrgId,
					year: props?.mergePlanInfo?.year,
					depotId: props.mergePlanInfo?.depotId,
					lineNoId: props.mergePlanInfo?.lineNoId
						? `*${props.mergePlanInfo?.lineNoId}*`
						: null,
					parentMajorId: props.mergePlanInfo?.majorId
				}"
				@close="showSelectDrawer = false"
				@save="handleSavePlanList"
			/>
		</Drawer>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.custom-q {
	margin: 10px 0px -10px 10px !important;
}
</style>
