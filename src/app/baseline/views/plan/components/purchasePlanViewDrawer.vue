<script lang="ts" setup>
import { watch, toRef, ref, reactive } from "vue"
import { type FormInstance } from "element-plus"
import { fileBusinessType } from "@/app/baseline/api/dict"
import { FormElementType } from "@/app/baseline/views/components/define"
import { PlanMerge } from "@/app/baseline/views/plan/components/define"
import { PlanPurchaseApi } from "@/app/baseline/api/plan/planPurchase"
import TableFile from "../../components/tableFile.vue"
import TableMatItemView from "@/app/baseline/views/plan/components/purchase/tableMatItemView.vue"
import tableMergePlanHasSelect from "@/app/baseline/views/plan/components/purchase/tableMergePlanHasSelect.vue"
import TableNeedPlan from "@/app/baseline/views/plan/components/merge/tableNeedPlan.vue"
import CostTag from "@/app/baseline/views/components/costTag.vue"
import TableProject from "@/app/baseline/views/plan/components/purchase/tableProject.vue"

const props = defineProps({
	id: {
		type: [String, Number],
		required: false
	},
	matMode: {
		type: Boolean,
		required: false,
		default: true
	}
})
const emits = defineEmits(["onSaveOrClose"])
const currentId = toRef(props, "id")
const loading = ref(false)
const drawerLeftTitle = {
	name: ["采购计划信息"],
	icon: ["fas", "square-share-nodes"]
}
const drawerRightTitle = {
	name: ["扩展信息"],
	icon: ["fas", "square-share-nodes"]
}

const refForm = ref<FormInstance>()
const formBtnList = [{ name: "取消", icon: ["fas", "circle-minus"] }]
const formModal = reactive<PlanMerge>({
	id: props.id
})
const formEl: FormElementType[] = [
	{
		label: "采购计划号",
		name: "code"
	},
	{
		label: "采购计划名称",
		name: "label"
	},
	{
		label: "计划年度",
		name: "year"
	},
	{
		label: "预估总金额",
		name: "purchaseAmount",
		type: "money"
	},

	{
		label: "备注说明",
		name: "remark",
		rows: "3"
	},
	{
		label: "创建人",
		name: "createdBy_view"
	},
	{
		label: "创建时间",
		name: "createdDate"
	}
]
const showDrawerNeedPlan = ref<boolean>(false)
const drawerSizeNeedPlan = 1200

const drawerLoading = ref(false)
const getInfoById = () => {
	drawerLoading.value = true
	PlanPurchaseApi.getPlanPurchase(currentId.value)
		.then((res: any) => {
			Object.assign(formModal, res)
		})
		.finally(() => {
			drawerLoading.value = false
		})
}
// 表单按钮点击
const onFormBtnList = (btnName: string | undefined) => {
	if (btnName === "取消") {
		refForm.value?.clearValidate()
		emits("onSaveOrClose", false)
		return
	}
}

//扩展栏标签页切换
const tabList = ["合并计划", "物资明细", "相关附件", "采购项目"]
const activeTab = ref<number>(0)
const handleTabChange = (index: number) => {
	activeTab.value = index
}
watch(
	() => props.id,
	(id: any) => {
		if (id) {
			getInfoById()
		}
	},
	{ immediate: true }
)

defineOptions({
	name: "PurchasePlanDrawer"
})
</script>
<template>
	<div class="drawer-container purchase-plan" v-loading="drawerLoading">
		<!-- 左侧表单区域 -->
		<div class="drawer-column left">
			<div class="rows">
				<Title :title="drawerLeftTitle" />
				<el-descriptions
					size="small"
					:column="1"
					:border="true"
					class="content"
				>
					<el-descriptions-item
						v-for="(el, index) in formEl"
						label-align="center"
						:label="el.label"
						:key="index"
					>
						<span v-if="el.type == 'money'"
							><cost-tag :value="formModal[el.name]"
						/></span>
						<span v-else>
							{{ formModal[el.name] ? formModal[el.name] : "---" }}
						</span>
					</el-descriptions-item>
				</el-descriptions>
			</div>
		</div>
		<!-- 右侧table区域 -->
		<div class="drawer-column right">
			<div class="rows">
				<Title :title="drawerRightTitle">
					<Tabs class="tabs" :tabs="tabList" @on-tab-change="handleTabChange" />
				</Title>
				<!-- model="mp-view" -->
				<div v-if="activeTab === 1" class="tab-mat">
					<TableMatItemView
						:need-query="true"
						:id="currentId"
						:model="props.matMode ? 'mp-view' : ''"
						type="purchase"
					/>
				</div>
				<div v-if="activeTab === 0">
					<tableMergePlanHasSelect
						:need-query="false"
						:purchase-plan-id="currentId"
						:need-opt="false"
					/>
				</div>

				<div v-if="activeTab === 2">
					<TableFile
						mod="view"
						:business-type="fileBusinessType.purchasePlan"
						:business-id="currentId"
					/>
				</div>
				<div v-if="activeTab === 3">
					<TableProject :id="currentId" />
				</div>
			</div>
			<ButtonList
				v-if="false"
				class="footer"
				:button="formBtnList"
				:loading="loading"
				@on-btn-click="onFormBtnList"
			/>
		</div>
		<!-- 选择需求计划弹窗 -->
		<Drawer
			:size="drawerSizeNeedPlan"
			v-model:drawer="showDrawerNeedPlan"
			:destroyOnClose="true"
		>
			<TableNeedPlan :need-opt="['view-ext']" />
		</Drawer>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index";

.drawer-container {
	.left {
		width: 310px;
	}

	.right {
		width: calc(100% - 310px);
	}

	.tab-mat {
		height: 100%;
	}

	//临时
	.tmp {
		height: 50%;
		display: flex;
		justify-content: center;
		align-items: center;
		flex-direction: column;
	}
}
</style>
