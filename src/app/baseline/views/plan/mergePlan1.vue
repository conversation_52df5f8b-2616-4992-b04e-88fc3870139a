<!-- 合并计划 - 废弃 -->
<script lang="ts" setup>
import {
	watch,
	toRef,
	ref,
	onMounted,
	reactive,
	nextTick,
	toValue,
	onBeforeMount
} from "vue"
import MergePlanDrawer from "./components/mergePlanDrawer.vue"
import { ElMessage } from "element-plus"
import {
	checkPermission,
	isCheckPermission
} from "@/app/platform/utils/permission"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import { appStatus, DictApi } from "@/app/baseline/api/dict"
import { useTbInit } from "@/app/baseline/views/components/tableBase.ts"
import MergePlanViewDrawer from "@/app/baseline/views/plan/components/mergePlanViewDrawer.vue"
import { BaseLineSysApi } from "@/app/baseline/api/system"
import { powerList } from "@/app/baseline/views/components/define.d.ts"
import LineTag from "@/app/baseline/views/components/lineTag.vue"
import { PlanMergeApi } from "@/app/baseline/api/plan/planMerge"
import CostTag from "@/app/baseline/views/components/costTag.vue"
import { hasEditByBpm, hasViewByBpm } from "../../utils"
import { useMessageBoxInit } from "@/app/baseline/views/components/messageBox.ts"
import { modalSize } from "@/app/baseline/utils/layout-config"

const { showDelConfirm } = useMessageBoxInit()
import { useDictInit } from "@/app/baseline/views/components/dictBase"
const { dictOptions, getDictByCodeList } = useDictInit()
/**********************初始化table *********************************/
const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	tbBtnLoading,
	pageSize,
	currentPage,
	pageTotal,
	selectedTableList,
	fetchParam,
	tbBtns,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange,
	onDataSelected,
	onBtnClick
} = useTbInit()
tableProp.value = [
	{ label: "计划年度", prop: "year", width: 85, fixed: "left" },
	{ label: "合并计划号", prop: "code", width: 160, fixed: "left" },
	{ label: "合并计划名称", prop: "label", minWidth: 200 },
	{ label: "专业", prop: "majorId_view", width: 200 },
	{ label: "线路", prop: "lineNoId", needSlot: true, width: 120 },
	{
		label: "费用类别",
		prop: "expenseCategory_view",
		width: 150
	},
	{
		label: "物资数量",
		prop: "num",
		needSlot: false,
		width: 100
	},
	{
		label: "需求计划数量",
		prop: "planNum",
		needSlot: false,
		width: 100
	},
	{
		label: "预估采购金额",
		prop: "purchaseAmount",
		needSlot: true,
		width: 150,
		align: "right"
	},
	{ label: "审批状态", prop: "bpmStatus", needSlot: true, width: 90 },
	{ label: "合并部门", prop: "sysOrgId_view", needSlot: false, width: 120 },
	{ label: "创建人", prop: "createdBy_view", needSlot: false, width: 120 },
	{ label: "创建时间", prop: "createdDate", width: 160 },
	{
		label: "操作",
		width: 130,
		prop: "operations",
		fixed: "right",
		needSlot: true
	}
]
onDataSelected.value = (rowList: { [propName: string]: any }[]) => {
	selectedTableList.value = rowList
}
fetchFunc.value = PlanMergeApi.getPlanMergeList
tbBtns.value = []
/**********************初始化table *********************************/

const rightTitle = {
	name: ["合并计划"],
	icon: ["fas", "square-share-nodes"]
}
const addBtn = [
	{
		name: "新建合并计划",
		roles: powerList.mergePlanBtnCreate,
		icon: ["fas", "square-plus"]
	}
]
//查询
const curSelCompany = ref("")
const singleQueryData = (
	key: string,
	queryData: { [propName: string]: any }
) => {
	if (key === "sysCommunityId") {
		if (Array.isArray(queryData) && queryData.length > 0) {
			curSelCompany.value = queryData[0].id
		} else {
			curSelCompany.value = ""
		}
	}
}
const queryArrList = [
	{
		name: "合并计划号",
		key: "code",
		placeholder: "请输入合并计划号",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "合并计划名称",
		key: "label",
		placeholder: "请输入合并计划名称",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "线路",
		key: "lineNoId",
		type: "treeSelect",
		treeApi: BaseLineSysApi.getLineList,
		placeholder: "请选择线路"
	},
	{
		name: "专业",
		key: "parentMajorId",
		type: "treeSelect",
		treeApi: BaseLineSysApi.getProfessionTree,
		placeholder: "请选择专业"
	},
	{
		name: "费用类别",
		key: "expenseCategory",
		type: "treeSelect",
		treeApi: () => Promise.resolve(dictOptions.value.COST_CATEGORY),
		replaceIdTo: "subitemValue",
		placeholder: "请选择费用类别"
	},
	{
		name: "公司",
		key: "sysCommunityId",
		type: "treeSelect",
		treeApi: BaseLineSysApi.getCompanyAllList,
		placeholder: "请选择公司"
	},
	{
		name: "合并部门",
		key: "sysOrgId",
		type: "treeSelect",
		treeApi: () => BaseLineSysApi.getDepartmentTree(curSelCompany.value),
		placeholder: "请选择合并部门"
	}
	// {
	// 	name: "审批状态",
	// 	key: "bpmStatus",
	// 	type: "elTreeSelect",
	// 	placeholder: "请选择",
	// 	children: DictApi.getBpmStatus()
	// },
]
////tabs
const tabList = [
	{
		name: "草稿箱",
		icon: ["fas", "square-plus"]
	},
	{
		name: "审批中",
		icon: ["fas", "square-plus"]
	},
	{
		name: "已审批",
		icon: ["fas", "square-plus"]
	}
]
//定义tabs相关变量
const tabNum = ref([0, 0, 0])
const tabStatus = reactive([
	[appStatus.pendingApproval, appStatus.rejected],
	[appStatus.underApproval],
	[appStatus.approved]
])
const activeName = ref(tabList[0].name)
const handleTabsClick = (tab: any) => {
	activeName.value = tab.name
	fetchParam.value.bpmStatus = tabStatus[tab.index].join(",")
	getTableData()
}
//drawer
const editId = ref<any>("")
const showDrawer = ref<boolean>(false)
const showViewDrawer = ref<boolean>(false)

const onAddBtn = () => {
	editId.value = ""
	showDrawer.value = true
}
const onRowView = (row: any) => {
	editId.value = row.id
	showViewDrawer.value = true
}
const onRowEdit = (row: any) => {
	editId.value = row.id
	showDrawer.value = true
}
const onRowDelete = (row: any) => {
	showDelConfirm().then(() => {
		PlanMergeApi.deletePlanMerge(row.id).then(() => {
			ElMessage.success("移除成功")
			getTableData()
		})
	})
}
// 获取表格默认参数
fetchParam.value = {
	bpmStatus: tabStatus[0].join(",")
}
const getTableData = (data?: any) => {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		...data
	}
	fetchTableData()
	getBmpStatusStatistics()
}
const getBmpStatusStatistics = () => {
	const params = JSON.parse(JSON.stringify(fetchParam.value))
	delete params.currentPage
	delete params.pageSize
	delete params.bpmStatus
	PlanMergeApi.getBmpStatusStatistics(params)
		.then((res) => {
			tabNum.value[0] =
				(res[appStatus.rejected] ? res[appStatus.rejected] : 0) +
				(res[appStatus.pendingApproval] ? res[appStatus.pendingApproval] : 0)
			tabNum.value[1] = res[appStatus.underApproval]
				? res[appStatus.underApproval]
				: 0
			tabNum.value[2] = res[appStatus.approved] ? res[appStatus.approved] : 0
		})
		.finally(() => {})
}

// 弹窗关闭
const onCloseDrawer = (msg: string) => {
	if (msg === "pub") {
		getTableData()
		showDrawer.value = false
	} else if (msg === "save") {
		getTableData()
	} else {
		showDrawer.value = false
		showViewDrawer.value = false
	}
}
const lineList = ref<[]>([])
function getLineList() {
	BaseLineSysApi.getLineList().then((res) => {
		lineList.value = res
	})
}
onMounted(() => {
	Promise.all([getLineList(), getDictByCodeList(["COST_CATEGORY"])]).then(
		() => {
			getTableData({})
		}
	)
})

defineOptions({
	name: "MatApplyManagement"
})
</script>
<template>
	<div class="app-container">
		<div class="whole-frame">
			<ModelFrame class="top-frame">
				<Query
					:queryArrList="queryArrList"
					@getQueryData="getTableData"
					@single-query-data="singleQueryData"
				/>
			</ModelFrame>
			<ModelFrame class="bottom-frame">
				<Title :title="rightTitle" :button="addBtn" @onBtnClick="onAddBtn">
					<div class="app-tabs-wrapper">
						<el-tabs v-model="activeName" @tab-click="handleTabsClick">
							<el-tab-pane
								v-for="(tab, index) in tabList"
								:key="index"
								:label="`${tab.name}（${tabNum[index]}）`"
								:name="tab.name"
								:index="tab.name"
							/>
						</el-tabs>
					</div>
				</Title>
				<PitayaTable
					ref="tableRef"
					:columns="tableProp"
					:tableData="tableData"
					:total="pageTotal"
					:single-select="true"
					:need-selection="true"
					:need-index="false"
					@onSelectionChange="onDataSelected"
					@on-current-page-change="onCurrentPageChange"
					:table-loading="tableLoading"
				>
					<template #purchaseAmount="{ rowData }">
						<cost-tag :value="rowData.purchaseAmount" />
					</template>
					<template #expenseCategory="{ rowData }">
						<dict-tag
							:options="dictOptions.COST_CATEGORY"
							:value="rowData.expenseCategory"
						/>
					</template>
					<template #lineNoId="{ rowData }">
						<line-tag :options="lineList" :value="rowData.lineNoId" />
					</template>
					<template #bpmStatus="{ rowData }">
						<dict-tag
							:options="DictApi.getBpmStatus()"
							:value="rowData.bpmStatus"
						/>
					</template>
					<template #operations="{ rowData }">
						<slot
							v-if="
								(isCheckPermission(powerList.mergePlanBtnPreview) &&
									hasViewByBpm(rowData.bpmStatus)) ||
								(isCheckPermission(powerList.mergePlanBtnEdit) &&
									hasEditByBpm(rowData.bpmStatus, rowData.createdBy))
							"
						>
							<el-button
								v-btn
								link
								@click="onRowView(rowData)"
								:disabled="checkPermission(powerList.mergePlanBtnPreview)"
								v-if="
									isCheckPermission(powerList.mergePlanBtnPreview) &&
									hasViewByBpm(rowData.bpmStatus)
								"
							>
								<font-awesome-icon
									:icon="['fas', 'eye']"
									style="color: var(--pitaya-btn-background)"
								/>
								<span class="table-inner-btn">查看</span>
							</el-button>
							<el-button
								v-btn
								link
								@click="onRowEdit(rowData)"
								:disabled="checkPermission(powerList.mergePlanBtnEdit)"
								v-if="
									isCheckPermission(powerList.mergePlanBtnEdit) &&
									hasEditByBpm(rowData.bpmStatus, rowData.createdBy)
								"
							>
								<font-awesome-icon :icon="['fas', 'pen-to-square']" />
								<span class="table-inner-btn">编辑</span>
							</el-button>
							<el-button
								v-btn
								link
								@click="onRowDelete(rowData)"
								:disabled="checkPermission(powerList.mergePlanBtnDrop)"
								v-if="
									isCheckPermission(powerList.mergePlanBtnDrop) &&
									hasEditByBpm(rowData.bpmStatus, rowData.createdBy)
								"
							>
								<font-awesome-icon :icon="['fas', 'trash-can']" />
								<span class="table-inner-btn">移除</span>
							</el-button>
						</slot>
						<slot v-else>---</slot>
					</template>
					<template #footerOperateLeft>
						<ButtonList
							class="btn-list"
							:is-not-radius="true"
							:button="tbBtns"
							:loading="tbBtnLoading"
							@on-btn-click="onBtnClick"
						/>
					</template>
				</PitayaTable>
				<!-- 新增弹窗 -->
				<Drawer
					:size="modalSize.xl"
					v-model:drawer="showDrawer"
					:destroyOnClose="true"
				>
					<MergePlanDrawer
						:id="editId"
						@on-save-or-close="onCloseDrawer"
						@on-select-change="fetchTableData"
					/>
				</Drawer>
				<!-- 查看弹窗 -->
				<Drawer
					:size="modalSize.xl"
					v-model:drawer="showViewDrawer"
					:destroyOnClose="true"
				>
					<MergePlanViewDrawer :id="editId" @on-save-or-close="onCloseDrawer" />
				</Drawer>
			</ModelFrame>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
