<!-- 费用类别 查看 -->
<script lang="ts" setup>
import { ref, onMounted, reactive } from "vue"
import { type FormInstance } from "element-plus"
import { FormElementType } from "@/app/baseline/views/components/define"
import FormElement from "@/app/baseline/views/components/formElement.vue"
import { getSystemCostCategoryById } from "@/app/baseline/api/system-cost-category"
import { IModalType } from "@/app/baseline/utils/types/common"

const props = defineProps<{ currentNode?: any; id?: any; model?: IModalType }>()
const emit = defineEmits<{
	(e: "close"): void
}>()

const leftTitle = computed(() => ({
	name: ["查看费用类别"],
	icon: ["fas", "square-share-nodes"]
}))

const formBtnList = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	}
]

const drawerLoading = ref(false)
const refForm = ref<FormInstance>()

const formData = ref<{ [propName: string]: any }>({
	fid: props.currentNode.isCompany ? 0 : props.currentNode?.id,
	fCode: props.currentNode.isCompany ? "" : props.currentNode?.code,
	fLabel: props.currentNode.isCompany ? "" : props.currentNode?.label
})

const formEl = reactive<FormElementType[][]>([
	[
		{
			label: "上级费用类别编码",
			name: "fcode",
			disabled: true
		}
	],
	[
		{
			label: "上级费用类别名称",
			name: "flabel",
			maxlength: 50,
			disabled: true
		}
	],
	[{ label: "费用类别编码", name: "code", maxlength: 50, disabled: true }],
	[{ label: "费用类别名称", name: "label", maxlength: 50, disabled: true }],
	[
		{
			label: "备注信息",
			name: "remark",
			maxlength: 1000,
			rows: 5,
			type: "textarea",
			disabled: true
		}
	]
])

async function getInfo() {
	if (!props.id) {
		return false
	}

	try {
		drawerLoading.value = true

		formData.value = await getSystemCostCategoryById(props.id)
	} finally {
		drawerLoading.value = false
	}
}

onMounted(async () => {
	getInfo()
})
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<div class="drawer-column">
			<div class="rows">
				<Title :title="leftTitle" />
				<el-scrollbar>
					<el-form
						style="padding-bottom: 30px"
						class="content form-base"
						ref="refForm"
						:model="formData"
						label-position="top"
					>
						<FormElement :form-element="formEl" :form-data="formData" />
					</el-form>
				</el-scrollbar>
			</div>
			<ButtonList
				class="footer"
				:button="formBtnList"
				@onBtnClick="emit('close')"
			/>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.drawer-column {
	width: 100%;
}
</style>
