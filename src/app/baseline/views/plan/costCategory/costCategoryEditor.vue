<!-- 费用类别 编辑 -->
<script lang="ts" setup>
import { ref, onMounted, reactive } from "vue"
import { ElMessage, type FormInstance, type FormRules } from "element-plus"
import { FormElementType } from "@/app/baseline/views/components/define"
import FormElement from "@/app/baseline/views/components/formElement.vue"
import {
	addSystemCostCategory,
	getSystemCostCategoryById,
	updateSystemCostCategory
} from "@/app/baseline/api/system-cost-category"
import {
	IIdempotentTokenType,
	IIdempotentTokenTypePre,
	IModalType
} from "@/app/baseline/utils/types/common"
import { getModalTypeLabel } from "@/app/baseline/utils"
import { omit } from "lodash-es"
import { getIdempotentToken } from "@/app/baseline/utils/validate"

const props = defineProps<{ currentNode?: any; id?: any; model?: IModalType }>()
const emit = defineEmits<{
	(e: "update"): void
	(e: "close"): void
}>()

/**
 * 能否编辑扩展信息
 */
const canEditExtra = computed(() => Boolean(formData.value?.id || props.id))

const leftTitle = computed(() => ({
	name: [
		getModalTypeLabel(
			canEditExtra.value ? IModalType.edit : IModalType.create,
			"费用类别"
		)
	],
	icon: ["fas", "square-share-nodes"]
}))

const formBtnList = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "保存",
		icon: ["fas", "floppy-disk"]
	}
]

const drawerLoading = ref(false)
const formBtnLoading = ref(false)
const refForm = ref<FormInstance>()

const formData = ref<{ [propName: string]: any }>({
	fid: props.currentNode.isCompany ? 0 : props.currentNode?.id,
	fcode: props.currentNode.isCompany ? "" : props.currentNode?.code,
	flabel: props.currentNode.isCompany ? "" : props.currentNode?.label
})

const formEl = reactive<FormElementType[][]>([
	[
		{
			label: "上级费用类别编码",
			name: "fcode",
			disabled: true
		}
	],
	[
		{
			label: "上级费用类别名称",
			name: "flabel",
			maxlength: 50,
			disabled: true
		}
	],
	[{ label: "费用类别编码", name: "code", maxlength: 50 }],
	[{ label: "费用类别名称", name: "label", maxlength: 50 }],
	[
		{
			label: "备注信息",
			name: "remark",
			maxlength: 1000,
			rows: 5,
			type: "textarea"
		}
	]
])

const rules = reactive<FormRules<typeof formData.value>>({
	code: [
		{ required: true, message: "费用类别编码不能为空", trigger: "change" }
	],
	label: [
		{ required: true, message: "费用类别名称不能为空", trigger: "change" }
	]
})

// 按钮点击
const onFormBtnList = (btnName: string | undefined) => {
	if (btnName === "保存") {
		if (!refForm.value) {
			return
		}
		refForm.value?.validate(async (valid) => {
			if (!valid) {
				return
			}

			formBtnLoading.value = true

			try {
				const api = canEditExtra.value
					? updateSystemCostCategory
					: addSystemCostCategory

				let idempotentToken = ""
				if (!canEditExtra.value) {
					idempotentToken = getIdempotentToken(
						IIdempotentTokenTypePre.apply,
						IIdempotentTokenType.costCategory
					)
				}

				await api(
					{
						...omit(formData.value),
						sysCommunityId: props.currentNode?.sysCommunityId
					},
					idempotentToken
				)

				ElMessage.success("操作成功")
				emit("update")
				emit("close")
			} finally {
				formBtnLoading.value = false
			}
		})
	}
	if (btnName === "取消") {
		emit("close")
	}
}

async function getInfo() {
	if (!props.id) {
		return false
	}

	try {
		drawerLoading.value = true

		formData.value = await getSystemCostCategoryById(props.id)
	} finally {
		drawerLoading.value = false
	}
}

onMounted(async () => {
	console.log(props.currentNode)
	getInfo()
})
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<div class="drawer-column">
			<div class="rows">
				<Title :title="leftTitle" />
				<el-scrollbar>
					<el-form
						style="padding-bottom: 30px"
						class="content form-base"
						ref="refForm"
						:model="formData"
						:rules="rules"
						label-position="top"
					>
						<FormElement :form-element="formEl" :form-data="formData" />
					</el-form>
				</el-scrollbar>
			</div>
			<ButtonList
				class="footer"
				:button="formBtnList"
				:loading="formBtnLoading"
				@onBtnClick="onFormBtnList"
			/>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.drawer-column {
	width: 100%;
}
</style>
