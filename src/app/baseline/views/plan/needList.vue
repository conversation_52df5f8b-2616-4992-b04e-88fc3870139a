<!-- 计划 - 需求清单 主表 V2.0-->
<script lang="ts" setup>
import { ref, onMounted } from "vue"
import { ElMessage } from "element-plus"
import {
	checkPermission,
	isCheckPermission
} from "@/app/platform/utils/permission"
import { DictApi, needListStatus } from "@/app/baseline/api/dict"
import { useTbInit } from "@/app/baseline/views/components/tableBase"
import { powerList } from "@/app/baseline/views/components/define.d"
import {
	BaseLineSysApi,
	listCompanyWithFormat
} from "@/app/baseline/api/system"
import { NeedApi } from "@/app/baseline/api/plan/need"
import { hasPermi } from "../../utils"

import DictTag from "@/app/baseline/views/components/dictTag.vue"
import { useMessageBoxInit } from "@/app/baseline/views/components/messageBox"
import { first, map } from "lodash-es"
import {
	IIdempotentTokenType,
	IIdempotentTokenTypePre,
	IModalType
} from "../../utils/types/common"
import { modalSize } from "@/app/baseline/utils/layout-config"
import needListEdit from "./needList/needListEdit.vue"
import needListDetail from "./needList/needListDetail.vue"
import { ICostCategoryStatus } from "../../utils/types/system-cost-category"
import { getIdempotentToken } from "../../utils/validate"

const { showDelConfirm, showWarnConfirm } = useMessageBoxInit()

const tbBtnLoading = ref(false)
const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	currentPage,
	pageTotal,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = useTbInit()

tableProp.value = [
	{ label: "需求清单编号", prop: "code", width: 160, sortable: true },
	{ label: "需求清单名称", prop: "label", minWidth: 120 },
	{ label: "物资编码数量", prop: "matCodeNum", width: 110 },
	{ label: "专业", prop: "majorId_view", width: 160 },
	{ label: "费用类别", prop: "expenseCategory_view", width: 120 },
	{ label: "公司", prop: "sysCommunityId_view", width: 120 },
	{ label: "清单状态", prop: "status", needSlot: true, width: 85 },
	{ label: "创建人", prop: "createdBy_view", width: 120 },
	{ label: "创建时间", prop: "createdDate", width: 160, sortable: true },
	{ label: "更新时间", prop: "lastModifiedDate", width: 160, sortable: true },
	{
		label: "操作",
		width: 200,
		prop: "operations",
		fixed: "right",
		needSlot: true
	}
]

/**
 * table 底部 按钮 配置
 */
const tableFooterBtns = computed(() => {
	const noCanStart = selectedTableList.value.some(
		(item: Record<string, any>) =>
			item.status === needListStatus.started.toString()
	)

	const noCanStop = selectedTableList.value.some(
		(item: any) => item.status !== needListStatus.started.toString()
	)

	const noCanCopy = selectedTableList.value.some(
		(item: any) =>
			item.status !== needListStatus.started.toString() &&
			item.status !== needListStatus.stopped.toString()
	)
	return [
		{
			name: "启用",
			roles: powerList.needListBtnStart,
			icon: ["fas", "power-off"],
			disabled: selectedTableList.value.length < 1 || noCanStart
		},
		{
			name: "停用",
			roles: powerList.needListBtnStop,
			icon: ["fas", "circle-stop"],
			disabled: selectedTableList.value.length < 1 || noCanStop
		},
		{
			name: "复制",
			roles: powerList.needListBtnCopy,
			icon: ["fas", "fa-copy"],
			disabled: selectedTableList.value.length != 1 || noCanCopy
		}
	]
})

fetchFunc.value = NeedApi.getPlanNeedList

const rightTitle = {
	name: ["需求清单"],
	icon: ["fas", "square-share-nodes"]
}
const addBtn = [
	{
		name: "新建需求清单",
		roles: powerList.needListBtnCreate,
		icon: ["fas", "square-plus"]
	}
]
/**
 * 公司列表
 */
const companyOptions = ref<any>([])

/**
 * 查询条件 配置
 */
const queryArrList = computed(() => [
	{
		name: "所属公司",
		key: "sysCommunityId",
		type: "select",
		children: companyOptions.value,
		placeholder: "请选择所属公司"
	},
	{
		name: "需求清单名称",
		key: "label",
		placeholder: "请输入需求清单名称",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "需求清单编号",
		key: "code",
		placeholder: "请输入需求清单编号",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "所属专业",
		key: "parentMajorId",
		type: "treeSelect",
		treeApi: () => BaseLineSysApi.getProfessionTree(),
		placeholder: "请选择所属专业"
	},
	{
		name: "费用类别",
		key: "parentExpenseCategory",
		type: "treeSelect",
		treeApi: () =>
			BaseLineSysApi.getCostCategoryTree({
				status: ICostCategoryStatus.Started
			}),
		placeholder: "请选择费用类别"
	},
	{
		name: "清单状态",
		key: "status",
		placeholder: "请选择清单状态",
		enableFuzzy: false,
		type: "elTreeSelect",
		children: DictApi.getStpStatus()
	}
])

//搜索条件确认回调
function getQueryData(data?: { [propName: string]: any }) {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = { ...fetchParam.value, ...data }
	fetchTableData()
}

/**
 * 启用/停用 操作
 * @param status
 */
async function handleChangeStatus(btnName?: string) {
	if (btnName === "复制") {
		const labelLen = first(selectedTableList.value)?.label.trim().length
		if (labelLen > 47) {
			return ElMessage.warning("此需求清单名称超长，不可复制！")
		}

		await showWarnConfirm(`请确认是否${btnName}？`)

		tbBtnLoading.value = true

		try {
			const idempotentToken = getIdempotentToken(
				IIdempotentTokenTypePre.other,
				IIdempotentTokenType.planNeedList,
				first(selectedTableList.value)?.id
			)

			await NeedApi.copyPlanNeed(
				first(selectedTableList.value)?.id,
				idempotentToken
			)
			fetchTableData()
			ElMessage.success("操作成功")
		} finally {
			tbBtnLoading.value = false
		}
	} else {
		const status =
			btnName === "启用" ? needListStatus.started : needListStatus.stopped

		await showWarnConfirm(`请确认是否${btnName}？`)
		const ids = map(selectedTableList.value, ({ id }) => id).toString()

		tbBtnLoading.value = true

		try {
			await NeedApi.updateNeedStatus(ids, status)
			fetchTableData()
			ElMessage.success("操作成功")
		} finally {
			tbBtnLoading.value = false
		}
	}
}

/**
 * 当前编辑行 id
 */
const editId = ref("")

const editorVisible = ref(false)
const detailVisible = ref(false)
const editorMode = ref(IModalType.create)

/**
 * 添加
 */
function handleAdd() {
	editId.value = ""
	editorVisible.value = true
	editorMode.value = IModalType.create
}

/**
 * 编辑
 * @param row
 */
async function handleRowEdit(row: any) {
	if (row.status === needListStatus.started) {
		try {
			tableLoading.value = true
			await NeedApi.updateNeedStatus(row.id, needListStatus.drafted)
			fetchTableData()
		} finally {
			tableLoading.value = false
		}
	}

	editorVisible.value = true
	editorMode.value = IModalType.edit
	editId.value = row.id
}

/**
 * 查看
 * @param row
 */
function handleRowView(row: any) {
	detailVisible.value = true
	editorMode.value = IModalType.view
	editId.value = row.id
}

/**
 * 删除
 * @param row
 */
async function handleRowDel(row: any) {
	await showDelConfirm()
	await NeedApi.deleteNeed(row.id)

	ElMessage.success("操作成功")
	fetchTableData()
}

onMounted(() => {
	fetchParam.value.sord = "desc"
	fetchParam.value.sidx = "createdDate"
	listCompanyWithFormat().then((r) => (companyOptions.value = r))
	fetchTableData()
})

/**
 * 排序
 * @param column
 * @param prop 字段名称
 * @param order ascending 升序 descending 降序
 */
function handleSortChange(column: any, prop: any, order: any) {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		sord:
			order === "descending" ? "desc" : order === "ascending" ? "asc" : "desc",
		sidx: order ? prop : "createdDate" // 排序字段
	}

	fetchTableData()
}
</script>
<template>
	<div class="app-container">
		<div class="whole-frame">
			<ModelFrame class="top-frame">
				<Query
					:queryArrList="queryArrList"
					class="ml10"
					@getQueryData="getQueryData"
				/>
			</ModelFrame>
			<ModelFrame class="bottom-frame">
				<Title :title="rightTitle" :button="addBtn" @onBtnClick="handleAdd" />
				<PitayaTable
					ref="tableRef"
					:columns="tableProp"
					:table-data="tableData"
					:needSelection="true"
					:single-select="false"
					:need-index="true"
					:need-pagination="true"
					:total="pageTotal"
					@onSelectionChange="selectedTableList = $event"
					@on-current-page-change="onCurrentPageChange"
					:table-loading="tableLoading"
					@on-table-sort-change="handleSortChange"
				>
					<template #status="{ rowData }">
						<dict-tag
							:options="DictApi.getStpStatus()"
							:value="rowData.status"
						/>
					</template>

					<template #operations="{ rowData }">
						<teamplate
							v-if="
								isCheckPermission(powerList.needListBtnPreview) ||
								(rowData.status == needListStatus.drafted &&
									isCheckPermission(powerList.needListBtnPreview)) ||
								(rowData.status == needListStatus.drafted &&
									isCheckPermission(powerList.needListBtnEdit) &&
									hasPermi(rowData.createdBy)) ||
								(rowData.status == needListStatus.drafted &&
									isCheckPermission(powerList.needListBtnDrop) &&
									hasPermi(rowData.createdBy))
							"
						>
							<el-button
								v-btn
								link
								@click.stop="handleRowEdit(rowData)"
								:disabled="checkPermission(powerList.needListBtnEdit)"
								v-if="
									(rowData.status == needListStatus.drafted ||
										rowData.status == needListStatus.started) &&
									isCheckPermission(powerList.needListBtnEdit) &&
									hasPermi(rowData.createdBy)
								"
							>
								<font-awesome-icon :icon="['fas', 'pen-to-square']" />
								<span class="table-inner-btn">编辑</span>
							</el-button>

							<el-button
								v-btn
								link
								@click.stop="handleRowView(rowData)"
								:disabled="checkPermission(powerList.needListBtnPreview)"
								v-if="isCheckPermission(powerList.needListBtnPreview)"
							>
								<font-awesome-icon :icon="['fas', 'eye']" />
								<span class="table-inner-btn">查看</span>
							</el-button>

							<el-button
								v-btn
								link
								@click.stop="handleRowDel(rowData)"
								:disabled="checkPermission('system:company:btn:delete')"
								v-if="
									rowData.status == needListStatus.drafted &&
									isCheckPermission(powerList.needListBtnDrop) &&
									hasPermi(rowData.createdBy)
								"
							>
								<font-awesome-icon :icon="['fas', 'trash-can']" />
								<span class="table-inner-btn">移除</span>
							</el-button>
						</teamplate>
						<teamplate v-else>---</teamplate>
					</template>
					<template #footerOperateLeft>
						<ButtonList
							class="btn-list"
							:is-not-radius="true"
							:button="tableFooterBtns"
							:loading="tbBtnLoading"
							@on-btn-click="handleChangeStatus"
						/>
					</template>
				</PitayaTable>

				<!-- 新建/编辑 -->
				<Drawer
					:size="modalSize.xl"
					v-model:drawer="editorVisible"
					:destroyOnClose="true"
				>
					<need-list-edit
						:id="editId"
						:mode="editorMode"
						@update="fetchTableData"
						@close="editorVisible = false"
					/>
				</Drawer>

				<!-- 查看 -->
				<Drawer
					:size="modalSize.xl"
					v-model:drawer="detailVisible"
					:destroyOnClose="true"
				>
					<need-list-detail
						:id="editId"
						:mode="editorMode"
						@close="detailVisible = false"
					/>
				</Drawer>
			</ModelFrame>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
