<!-- 计划  - 采购计划 - 合并计划 - 关联需求计划 查看 页 -->
<script lang="ts" setup>
import { ref, onMounted } from "vue"
import { DictApi } from "@/app/baseline/api/dict"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import CostTag from "../../components/costTag.vue"
import LineTag from "../../components/lineTag.vue"
import { useTbInit } from "../../components/tableBase"
import { useDictInit } from "../../components/dictBase"
import { BaseLineSysApi } from "@/app/baseline/api/system"
import { PlanPurchaseMaterialItemApi } from "@/app/baseline/api/plan/planPurchaseMaterialItem"
import needPlanMonthDetail from "./needPlanMonthDetail.vue"
import { toFixedTwo } from "@/app/baseline/utils"
import { getRealLength, setString } from "@/app/baseline/utils/validate"

const props = withDefaults(
	defineProps<{
		purchasePlanId: any // 采购计划 id
		detailInfo?: Record<string, any>
		/**
		 * table 数据源
		 */
		tableApi?: (params: any) => Promise<any>
	}>(),
	{}
)

const emit = defineEmits<{
	(e: "close"): void
}>()

const { dictFilter, getDictByCodeList } = useDictInit()

/**
 * 能否编辑扩展信息
 */
const canEditExtra = computed(() => Boolean(formData.value?.id))

const titleConf = computed(() => ({
	name: ["物资信息"],
	icon: ["fas", "square-share-nodes"]
}))

const btnLoading = ref(false)

const drawerRightTitle = {
	name: ["关联需求计划"],
	icon: ["fas", "square-share-nodes"]
}

const formBtnListRight = [{ name: "取消", icon: ["fas", "circle-minus"] }]
const formData = ref<Record<string, any>>({
	...props.detailInfo,
	num_view: toFixedTwo(props.detailInfo?.num, 0)
})

const descOptions = [
	{ label: "物资编码", name: "code" },
	{ label: "物资名称", name: "label" },
	{ label: "物资分类编码", name: "materialTypeCode" },
	{ label: "物资分类名称", name: "materialTypeLabel" },
	{ label: "规格型号", name: "version" },
	{ label: "技术参数", name: "technicalParameter", needTooltip: true },
	{ label: "物资性质", name: "attribute" },
	{ label: "采购单位", name: "buyUnit" },
	{ label: "预估采购单价", name: "evaluation", type: "money" },
	{ label: "需求数量", name: "num_view" },
	{ label: "预估采购金额", name: "purchaseAmount", type: "money" }
]

const drawerLoading = ref(false)

onMounted(async () => {
	fetchParam.value.sord = "desc"
	fetchParam.value.sidx = "createdDate"

	getDictByCodeList(["INVENTORY_UNIT"])
	getLineList()
	getTableData()
})

const tbInit = useTbInit()
const {
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	fetchParam,
	currentPage,
	selectedTableList,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = tbInit

fetchFunc.value =
	props.tableApi ||
	PlanPurchaseMaterialItemApi.getPlanPurchaseMaterialItemNeedPlanList

tbInit.tableProp = computed(() => {
	const defColumns: TableColumnType[] = [
		{ label: "需求计划号", prop: "code", width: 160, fixed: "left" },
		{ label: "需求计划名称", prop: "label", minWidth: 120 },
		{ label: "需求部门", prop: "sysOrgId_view", minWidth: 120 },
		{ label: "专业", prop: "majorId_view", width: 120 },
		{ label: "线路", prop: "lineNoId", needSlot: true, width: 100 },
		{ label: "段区", prop: "depotId_view", width: 120 },
		{
			label: "预估采购单价",
			prop: "evaluation",
			needSlot: true,
			width: 120,
			align: "right"
		},
		{
			label: "需求数量",
			prop: "num",
			needSlot: true,
			align: "right",
			width: 120
		},
		{
			label: "预估金额",
			prop: "purchaseAmount",
			needSlot: true,
			width: 120,
			align: "right"
		},
		{ label: "需求清单编号", prop: "planNeedCode", width: 150 },
		{ label: "创建人", prop: "createdBy_view", width: 120 },
		{ label: "创建时间", prop: "createdDate", width: 150 },
		{
			label: "操作",
			width: 100,
			prop: "operations",
			fixed: "right",
			needSlot: true
		}
	]

	return defColumns
})

const getTableData = () => {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	if (props.detailInfo) {
		fetchParam.value = {
			purchasePlanId: props.purchasePlanId,
			materialId: props.detailInfo.materialId,
			mergePlanId: props.detailInfo.mergePlanId,
			...fetchParam.value
		}
		fetchTableData()
	}
}

const lineList = ref<[]>([])
function getLineList() {
	BaseLineSysApi.getLineList().then((res) => {
		lineList.value = res
	})
}

/**
 * 要查看的需求计划id
 */
const editingTableRow = ref<Record<string, any>>()
const needPlanMonthDetailVisible = ref(false)
function handleRowViewNeedPlan(e: any) {
	editingTableRow.value = { ...e }
	needPlanMonthDetailVisible.value = true
}
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<!-- 左侧表单区域 -->
		<div class="drawer-column left">
			<div class="rows">
				<Title :title="titleConf" />
				<el-scrollbar>
					<el-descriptions
						size="small"
						:column="1"
						:border="true"
						class="content"
					>
						<el-descriptions-item
							v-for="(el, index) in descOptions"
							label-align="center"
							:label="el.label"
							:key="index"
						>
							<span v-if="el.type == 'money'">
								<cost-tag :value="formData[el.name]" />
							</span>

							<span v-else-if="el.name === 'attribute'">
								<dict-tag
									:options="DictApi.getMatAttr()"
									:value="formData?.attribute"
								/>
							</span>
							<span v-else-if="el.name === 'buyUnit'">
								{{
									dictFilter("INVENTORY_UNIT", formData.buyUnit)?.subitemName ||
									"---"
								}}
							</span>
							<span v-else-if="el.needTooltip">
								<el-tooltip
									effect="dark"
									:content="formData?.[el.name]"
									:disabled="
										getRealLength(formData?.[el.name]) <= 100 ? true : false
									"
								>
									{{
										getRealLength(formData?.[el.name]) > 100
											? setString(formData?.[el.name], 100)
											: formData?.[el.name] || "---"
									}}
								</el-tooltip>
							</span>
							<span v-else>
								{{ formData[el.name] ? formData[el.name] : "---" }}
							</span>
						</el-descriptions-item>
					</el-descriptions>
				</el-scrollbar>
			</div>
		</div>
		<!-- 右侧table区域 -->
		<div class="drawer-column right" :class="canEditExtra ? '' : ' disabled'">
			<div class="rows">
				<Title :title="drawerRightTitle" />

				<el-scrollbar style="height: 100%">
					<pitaya-table
						ref="tableRef"
						:columns="(tbInit.tableProp as any)"
						:table-data="tableData"
						:need-index="true"
						:single-select="false"
						:need-selection="false"
						:need-pagination="true"
						:total="pageTotal"
						:table-loading="tableLoading"
						@on-selection-change="selectedTableList = $event"
						@on-current-page-change="onCurrentPageChange"
					>
						<template #evaluation="{ rowData }">
							<cost-tag :value="rowData.evaluation" />
						</template>
						<template #purchaseAmount="{ rowData }">
							<cost-tag :value="rowData.purchaseAmount" />
						</template>

						<template #lineNoId="{ rowData }">
							<line-tag :options="lineList" :value="rowData.lineNoId" />
						</template>

						<template #num="{ rowData }">
							{{ toFixedTwo(rowData.num, 0) }}
						</template>

						<template #operations="{ rowData }">
							<el-button v-btn link @click="handleRowViewNeedPlan(rowData)">
								<font-awesome-icon :icon="['fas', 'eye']" />
								<span class="table-inner-btn">物资明细</span>
							</el-button>
						</template>
					</pitaya-table>
				</el-scrollbar>
			</div>
			<ButtonList
				class="footer"
				:button="formBtnListRight"
				:loading="btnLoading"
				@on-btn-click="emit('close')"
			/>
		</div>

		<!-- 查看合并计划详情弹窗 -->
		<Drawer
			v-model:drawer="needPlanMonthDetailVisible"
			:size="550"
			destroy-on-close
		>
			<need-plan-month-detail
				:plan-id="editingTableRow?.id"
				:material-id="props.detailInfo?.materialId"
				@close="needPlanMonthDetailVisible = false"
			/>
		</Drawer>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.drawer-container {
	.left {
		width: 310px;
	}

	.right {
		width: calc(100% - 310px);
	}
}
</style>
