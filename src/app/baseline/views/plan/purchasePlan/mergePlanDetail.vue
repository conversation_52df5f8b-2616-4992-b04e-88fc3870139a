<!-- 计划  - 采购计划 - 合并计划 查看 页 -->
<script lang="ts" setup>
import { DictApi } from "@/app/baseline/api/dict"
import { PlanMergeApi } from "@/app/baseline/api/plan/planMerge"
import { useUserStore } from "@/app/platform/store/modules/user"
import { ref, onMounted } from "vue"
import XEUtils from "xe-utils"
import { IModalType } from "../../../utils/types/common"
import CostTag from "../../components/costTag.vue"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import { useTbInit } from "../../components/tableBase"
import { PlanMergeItemApi } from "@/app/baseline/api/plan/planMergeItem"
import { convertContentTableData } from "@/app/baseline/utils"
import { useDictInit } from "../../components/dictBase"
import { modalSize } from "@/app/baseline/utils/layout-config"
import needPlanDetail from "./needPlanDetail.vue"
import { toFixedTwo } from "@/app/baseline/utils"

const props = withDefaults(
	defineProps<{
		/**
		 * 采购计划Id
		 */
		purchasePlanId: any
		/**
		 * 合并计划id
		 */
		id: any

		/**
		 * 编辑模式：查看
		 */
		mode?: IModalType

		footerBtnVisible?: boolean
	}>(),
	{ footerBtnVisible: true, model: IModalType.view }
)

const emit = defineEmits<{
	(e: "close"): void
}>()

const { dictOptions, dictFilter, getDictByCodeList } = useDictInit()

const { userInfo } = storeToRefs(useUserStore())

/**
 * 能否编辑扩展信息
 */
const canEditExtra = computed(() => Boolean(formData.value?.id || props.id))

const titleConf = computed(() => ({
	name: ["合并计划信息"],
	icon: ["fas", "square-share-nodes"]
}))

const btnLoading = ref(false)

const drawerRightTitle = {
	name: ["合并计划明细"],
	icon: ["fas", "square-share-nodes"]
}

const formBtnListRight = [{ name: "取消", icon: ["fas", "circle-minus"] }]
//表单默认值
const futureYears = DictApi.getFutureYears(-1, 1)

const formData = ref<Record<string, any>>({
	year: futureYears[1].value,
	sysOrgId: userInfo.value.orgId,
	sysOrgId_view: userInfo.value.orgName,
	bpmStatus: "0"
})
const descOptions = [
	{ label: "合并计划号", name: "code" },
	{ label: "合并计划名称", name: "label" },
	{ label: "计划年度", name: "year", data: DictApi.getFutureYears(-1, 1) },
	{ label: "需求计划数量", name: "planNum" },
	{ label: "物资数量", name: "num_view" },
	{ label: "预估采购金额", name: "purchaseAmount", type: "money" },
	{ label: "线路", name: "lineNoId_view" },
	{ label: "专业", name: "majorId_view" },
	{ label: "段区", name: "depotId_view" },
	{ label: "费用类别", name: "expenseCategory_view" },
	{ label: "备注说明", name: "remark", type: "textarea" },
	{ label: "合并部门", name: "sysOrgId_view" },
	{ label: "创建人", name: "createdBy_view" },
	{ label: "创建时间", name: "createdDate" }
]

const drawerLoading = ref(false)

async function getDetail() {
	if (!props.id) {
		return false
	}

	try {
		drawerLoading.value = true
		const res = await PlanMergeApi.getPlanMerge(props.id as any)
		formData.value = { ...res }
		const tmpAmount = `${XEUtils.commafy(res.purchaseAmount as any, {
			digits: 5
		})}`
		formData.value["purchaseAmount_view"] = tmpAmount ? `￥${tmpAmount}` : "---"
		formData.value.num_view = toFixedTwo(res.num as any, 0)
		formData.value.year = res.year + ""
	} finally {
		drawerLoading.value = false
	}
}

onMounted(async () => {
	fetchParam.value.sord = "asc"
	fetchParam.value.sidx = "code"

	getDictByCodeList(["INVENTORY_UNIT", "MATERIAL_NATURE"])
	getDetail()
	getTableData()
})

const queryArrList = computed(() => [
	{
		name: "物资编码",
		key: "materialCode",
		placeholder: "请输入物资编码",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资名称",
		key: "materialLabel",
		placeholder: "请输入物资名称",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "规格型号",
		key: "version",
		placeholder: "请输入规格型号",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资性质",
		key: "attribute",
		type: "select",
		children: dictOptions.value.MATERIAL_NATURE,
		placeholder: "请选择物资性质"
	}
])

const tbInit = useTbInit()
const {
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	fetchParam,
	currentPage,
	selectedTableList,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = tbInit

fetchFunc.value = PlanMergeItemApi.getPlanMergeItemMaterialList

tbInit.tableProp = computed(() => {
	const defColumns: TableColumnType[] = [
		{ label: "物资编码", prop: "code", width: 130, fixed: "left" },
		{ label: "物资名称", prop: "label", width: 150 },
		{ label: "物资分类编码", prop: "materialTypeCode", width: 100 },
		{
			label: "物资分类名称",
			prop: "materialTypeLabel",
			width: 150
		},
		{ label: "规格型号", prop: "version", width: 120 },
		{
			label: "技术参数",
			prop: "technicalParameter",
			minWidth: 200,
			align: "left"
		},
		{ label: "物资性质", prop: "attribute", needSlot: true, width: 120 },
		{ label: "采购单位", prop: "buyUnit", needSlot: true, width: 90 },
		{
			label: "预估采购单价",
			prop: "evaluation",
			align: "right",
			needSlot: true,
			width: 150
		},
		{
			label: "需求数量",
			prop: "num",
			needSlot: true,
			width: 120,
			fixed: "right",
			align: "right"
		},
		{
			label: "预估采购金额",
			prop: "purchaseAmount",
			width: 120,
			needSlot: true,
			align: "right",
			fixed: "right"
		},
		{
			label: "操作",
			width: props.mode == IModalType.view ? 100 : 160,
			prop: "operations",
			fixed: "right",
			needSlot: true
		}
	]

	return defColumns
})

const getTableData = (e?: any) => {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	if (props.id) {
		fetchParam.value = {
			mergePlanId: props.id,
			...fetchParam.value,
			...e
		}
		fetchTableData()
	}
}

const lastTableData = ref<any[]>([])

/**
 * 补丁
 * 监听tableData 重组lastTableData
 */
watch([tableData], () => {
	lastTableData.value = convertContentTableData(tableData.value, [
		"num",
		"planAmount",
		"purchaseAmount",
		"purchaseNum",
		"storeNum",
		"evaluation",
		"materialId",
		"mergePlanId",
		"attribute"
	])
})

/**
 * 要查看的需求计划id
 */
const editingTableRow = ref<Record<string, any>>()
const needPlanDetailVisible = ref(false)
function handleRowViewNeedPlan(e: any) {
	editingTableRow.value = { ...e }
	needPlanDetailVisible.value = true
}
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<!-- 左侧表单区域 -->
		<div class="drawer-column left">
			<div class="rows">
				<Title :title="titleConf" />
				<el-scrollbar>
					<el-descriptions
						size="small"
						:column="1"
						:border="true"
						class="content"
					>
						<el-descriptions-item
							v-for="(el, index) in descOptions"
							label-align="center"
							:label="el.label"
							:key="index"
						>
							<span v-if="el.type == 'money'">
								<cost-tag :value="formData[el.name]" />
							</span>
							<span v-else>
								{{ formData[el.name] ? formData[el.name] : "---" }}
							</span>
						</el-descriptions-item>
					</el-descriptions>
				</el-scrollbar>
			</div>
		</div>
		<!-- 右侧table区域 -->
		<div class="drawer-column right" :class="canEditExtra ? '' : ' disabled'">
			<div class="rows">
				<Title :title="drawerRightTitle" />

				<Query
					:queryArrList="queryArrList"
					@getQueryData="getTableData"
					style="margin: 10px 0px -10px 10px !important"
				/>

				<el-scrollbar style="height: 100%">
					<pitaya-table
						ref="tableRef"
						:columns="(tbInit.tableProp as any)"
						:table-data="lastTableData"
						:need-index="true"
						:single-select="false"
						:need-selection="false"
						:need-pagination="true"
						:total="pageTotal"
						:table-loading="tableLoading"
						@on-selection-change="selectedTableList = $event"
						@on-current-page-change="onCurrentPageChange"
					>
						<!-- 物资性质 -->
						<template #attribute="{ rowData }">
							<dict-tag
								:options="DictApi.getMatAttr()"
								:value="rowData.attribute"
							/>
						</template>

						<!-- 预估采购金额 -->
						<template #purchaseAmount="{ rowData }">
							<span class="num-blue">
								<cost-tag :value="rowData.purchaseAmount" />
							</span>
						</template>

						<!-- 预估采购单价 -->
						<template #evaluation="{ rowData }">
							<cost-tag :value="rowData.evaluation" />
						</template>

						<!-- 需求数量 -->
						<template #num="{ rowData }">
							<span class="num-blue">
								{{ toFixedTwo(rowData.num, 0) }}
							</span>
						</template>

						<!-- 采购单位 -->
						<template #buyUnit="{ rowData }">
							{{
								dictFilter("INVENTORY_UNIT", rowData.buyUnit)?.subitemName ||
								"---"
							}}
						</template>

						<template #operations="{ rowData }">
							<el-button v-btn link @click="handleRowViewNeedPlan(rowData)">
								<font-awesome-icon :icon="['fas', 'eye']" />
								<span class="table-inner-btn">关联需求计划</span>
							</el-button>
						</template>
					</pitaya-table>
				</el-scrollbar>
			</div>
			<ButtonList
				v-if="props.footerBtnVisible"
				class="footer"
				:button="formBtnListRight"
				:loading="btnLoading"
				@on-btn-click="emit('close')"
			/>
		</div>

		<!-- 查看合并计划详情弹窗 -->
		<Drawer
			v-model:drawer="needPlanDetailVisible"
			:size="modalSize.lg"
			destroy-on-close
		>
			<need-plan-detail
				:detail-info="editingTableRow"
				:purchase-plan-id="props.purchasePlanId"
				:plan-type="'plan-purchase'"
				@close="needPlanDetailVisible = false"
			/>
		</Drawer>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.num-blue {
	color: $---color-info2;
}

.drawer-container {
	.left {
		width: 310px;
	}

	.right {
		width: calc(100% - 310px);
	}
}
</style>
