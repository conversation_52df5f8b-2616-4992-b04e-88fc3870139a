<!-- 合并计划 - 添加需求计划 选择器 -->
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<div class="drawer-column" style="width: 100%">
			<Title :title="titleConf" />

			<div class="rows mat-selector-table">
				<Query
					v-if="queryVisible"
					:query-arr-list="(queryConf as any)"
					style="margin: 10px 10px -10px"
					@get-query-data="getQueryData"
				/>
				<el-scrollbar>
					<pitaya-table
						ref="tableRef"
						:columns="tableProp"
						:table-data="tableData"
						:need-selection="true"
						:single-select="!multiple"
						:need-index="true"
						:need-pagination="true"
						:total="pageTotal"
						:table-loading="tableLoading"
						:cell-class-name="tbCellClassName"
						@on-selection-change="selectedTableList = $event"
						@on-current-page-change="fetchTableDataWithSetRowsCheck"
					>
						<template #purchaseAmount="{ rowData }">
							<cost-tag :value="rowData.purchaseAmount" />
						</template>

						<template #lineNoId="{ rowData }">
							<line-tag :options="lineList" :value="rowData.lineNoId" />
						</template>
					</pitaya-table>
				</el-scrollbar>
			</div>

			<button-list
				class="footer"
				:button="btnConf"
				:loading="btnLoading"
				@on-btn-click="handleBtnClick"
			/>
		</div>
	</div>
</template>

<script setup lang="ts">
import CostTag from "../../components/costTag.vue"
import lineTag from "../../components/lineTag.vue"

import { includes } from "lodash-es"
import { useTbInit } from "../../components/tableBase"
import { useTableSelectorUtils } from "../../store/hooks/table-selector-utils"
import { PlanMergeApi } from "@/app/baseline/api/plan/planMerge"
import { BaseLineSysApi } from "@/app/baseline/api/system"
import { useUserStore } from "@/app/platform/store/modules/user"

const { userInfo } = storeToRefs(useUserStore())

const emit = defineEmits<{
	(e: "close"): void
	(e: "save", v: any[]): void
}>()

const props = withDefaults(
	defineProps<{
		title?: string
		/**
		 * 当前选中的id 集合
		 */
		selectedIds?: any[]
		/**
		 * table 数据源
		 */
		tableApi?: (params: any) => Promise<any>

		/**
		 * table 的列配置
		 */
		columns?: TableColumnType[]

		/**
		 * 是否支持多选
		 */
		multiple?: boolean

		/**
		 * table 请求参数
		 */
		tableReqParams?: any

		/**
		 * 筛选是否可见
		 */
		queryVisible?: boolean
		/**
		 * 标红的ID
		 */
		errorIdList?: any[]
		queryArrList?: any
	}>(),
	{
		multiple: false,
		tableReqParams: () => ({}),
		columns: () => [],
		queryVisible: true,
		title: "选择"
	}
)

const titleConf = computed(() => ({
	name: [props.title],
	icon: ["fas", "square-share-nodes"]
}))

const queryConf = computed(() => {
	//查询
	const ls = [
		{
			name: "部门",
			key: "sysOrgId",
			type: "treeSelect",
			treeApi: () =>
				BaseLineSysApi.getDepartmentTreeWithCompanyDisabled(
					userInfo.value.companyId
				),
			placeholder: "请选择所属部门"
		},
		{
			name: "专业",
			key: "parentMajorId",
			type: "treeSelect",
			treeApi: () => BaseLineSysApi.getProfessionTree(),
			placeholder: "请选择所属专业"
		},
		{
			name: "合并计划号",
			key: "code",
			placeholder: "请输入合并计划号",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "合并计划名称",
			key: "label",
			placeholder: "请输入合并计划名称",
			enableFuzzy: true,
			type: "input"
		}
	]

	if (props.queryArrList) {
		return props.queryArrList
	}
	{
		return ls
	}
})

const drawerLoading = ref(false)

const btnLoading = ref(false)

const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	selectedTableList,
	fetchParam,
	currentPage,
	fetchTableData,
	fetchFunc
} = useTbInit()

// eslint-disable-next-line vue/no-setup-props-destructure
fetchFunc.value = props.tableApi || PlanMergeApi.getListCanMergePlan

tableProp.value = props.columns?.length
	? props.columns
	: [
			{ label: "年度", prop: "year", width: 85, fixed: "left" },
			{ label: "合并计划号", prop: "code", width: 160, fixed: "left" },
			{
				label: "合并部门",
				prop: "sysOrgId_view",
				needSlot: false,
				width: 140,
				fixed: "left"
			},

			{ label: "合并计划名称", prop: "label", minWidth: 150 },
			{ label: "专业", prop: "majorId_view", width: 150 },
			{ label: "线路", prop: "lineNoId", needSlot: true, width: 100 },
			{
				label: "费用类别",
				prop: "expenseCategory_view",
				width: 150
			},
			{
				label: "需求计划数量",
				prop: "planNum",
				width: 100
			},
			{
				label: "预估采购金额",
				prop: "purchaseAmount",
				needSlot: true,
				width: 150,
				align: "right"
			},
			{ label: "创建人", prop: "createdBy_view", width: 120 },
			{ label: "备注说明", prop: "remark", width: 160, align: "left" }
	  ]

const { fetchTableDataWithSetRowsCheck } = useTableSelectorUtils({
	tableData,
	tableRef,
	selectedIds: props.selectedIds,
	fetchTableData
})

const btnConf = computed(() => {
	return [
		{
			name: "取消",
			icon: ["fas", "circle-minus"]
		},
		{
			name: "保存",
			icon: ["fas", "floppy-disk"],
			disabled: selectedTableList.value.length > 0 ? false : true
		}
	]
})

function tbCellClassName(data: {
	row: any
	column: any
	rowIndex: number
	columnIndex: number
}): string {
	return includes(props.errorIdList ?? [], data.row.id) ? "error" : ""
}

const segmentList = ref<[]>([])

const lineList = ref<[]>([])

onMounted(async () => {
	BaseLineSysApi.getSegmentList().then((res) => {
		segmentList.value = res
	})
	BaseLineSysApi.getLineList().then((res) => {
		lineList.value = res
	})

	fetchParam.value = {
		...fetchParam.value,
		...props.tableReqParams,
		sord: "desc",
		sidx: "createdDate"
	}

	fetchTableDataWithSetRowsCheck()
})

function getQueryData(e: any) {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = { ...fetchParam.value, ...e }
	fetchTableDataWithSetRowsCheck()
}

function handleBtnClick(name?: string) {
	if (name === "取消") {
		return emit("close")
	}

	btnLoading.value = true
	emit("save", selectedTableList.value)
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.mat-selector-table {
	&:deep(.pitaya-table) {
		.error {
			.column-inner-item,
			.cell,
			.el-input__inner {
				color: red;
			}
		}
	}
}
</style>
