<!-- 采购计划 - 物资明细  table -->
<template>
	<div style="display: flex; height: 100%">
		<el-scrollbar
			style="width: 100%"
			:class="{ computedWidth: props.mode === IModalType.view && !isApproval }"
		>
			<Query
				:queryArrList="queryArrList"
				@getQueryData="getTableData"
				style="margin: 10px 0px -10px 10px !important"
			/>

			<pitaya-table
				ref="tableRef"
				:columns="(tbInit.tableProp as any)"
				:table-data="lastTableData"
				:need-index="true"
				:single-select="props.mode !== IModalType.view ? false : true"
				:need-selection="isApproval ? false : true"
				:need-pagination="true"
				:total="pageTotal"
				:table-loading="tableLoading"
				@on-selection-change="selectedTableList = $event"
				@on-current-page-change="onCurrentPageChange"
				@on-table-sort-change="handleSortChange"
			>
				<!-- 物资性质 -->
				<template #attribute="{ rowData }">
					<dict-tag
						:options="DictApi.getMatAttr()"
						:value="rowData.attribute"
					/>
				</template>

				<template #buyUnit="{ rowData }">
					{{
						dictFilter("INVENTORY_UNIT", rowData.buyUnit)?.subitemName || "---"
					}}
				</template>
				<template #evaluation="{ rowData }">
					<cost-tag :value="rowData.evaluation" />
				</template>
				<template #purchaseAmount="{ rowData }">
					<span :class="{ 'num-blue': props.mode !== IModalType.view }">
						<cost-tag :value="rowData.purchaseAmount"
					/></span>
				</template>
				<template #planAmount="{ rowData }">
					<cost-tag :value="rowData.planAmount" />
				</template>
				<template #num_view="{ rowData }">
					<span class="num-blue" v-if="props.mode != IModalType.view">
						{{ rowData.num_view || "0" }}</span
					>

					<span
						:class="rowData.num == rowData.purchaseNum ? 'num-blue' : 'num-red'"
						v-else
					>
						{{ rowData.num_view || "0" }}</span
					>
				</template>

				<template #storeNum="{ rowData }">
					{{ toFixedTwo(rowData.storeNum) }}
				</template>

				<template #inTransitNum="{ rowData }">
					{{ toFixedTwo(rowData.inTransitNum) }}
				</template>

				<template #purchaseNum_view="{ rowData }">
					<!-- 采购数量 可编辑 -->
					<el-input
						class="no-arrows"
						v-if="props.mode !== IModalType.view"
						v-model="rowData.purchaseNum"
						@click.stop
						@input="
							(e:any) => {
								handleInputMin(e, rowData)
							}
						"
						@change="validateNum(rowData)"
					/>

					<span class="num-blue" v-else>
						<span
							:class="
								rowData.num == rowData.purchaseNum ? 'num-blue' : 'num-red'
							"
						>
							{{ rowData.purchaseNum_view || "0" }}</span
						>
					</span>
				</template>

				<template #operations="{ rowData }">
					<el-button
						v-btn
						link
						@click.stop="handleRowMergePlanView(rowData)"
						v-if="props.isApproval"
					>
						<!-- 线盒多少钱；水管多少钱一米；不用人家的线； -->
						<font-awesome-icon :icon="['fas', 'eye']" />
						<span class="table-inner-btn">查看</span>
					</el-button>

					<el-button v-btn link @click.stop="handleRowView(rowData)" v-else>
						<font-awesome-icon :icon="['fas', 'eye']" />
						<span class="table-inner-btn">关联需求计划</span>
					</el-button>
				</template>
				<template #footerOperateLeft v-if="props.mode !== IModalType.view">
					<ButtonList
						class="btn-list"
						:is-not-radius="true"
						:button="tbBtnConf"
						:loading="tbBtnLoading"
						@on-btn-click="tbBtnAction"
					/>
				</template>
			</pitaya-table>
		</el-scrollbar>

		<div
			style="
				width: 500px;
				display: flex;
				padding-left: 10px;
				flex-direction: column;
				border-left: 1px solid #ddd;
			"
			v-if="props.mode === IModalType.view && !isApproval"
		>
			<Title :title="titleConf" style="margin-top: 10px" />

			<div>
				<!-- 物资信息 -->
				<selected-merge-plan-table
					:purchase-plan-id="props.id"
					:mat-id="
						first(tableRef?.pitayaTableRef?.getSelectionRows())?.materialId
					"
				/>
			</div>
		</div>
	</div>

	<!-- 查看需求计划详情弹窗 -->
	<Drawer
		v-model:drawer="needPlanDetailVisible"
		:size="modalSize.xl"
		destroy-on-close
	>
		<need-plan-detail
			:detail-info="editingTableRow"
			:purchase-plan-id="props.id"
			:plan-type="'plan-purchase'"
			:table-api="
				PlanPurchaseMaterialItemApi.getPlanPurchaseMaterialItemNeedPlanList
			"
			@close="needPlanDetailVisible = false"
		/>
	</Drawer>

	<!-- 查看关联合并计划 - 弹窗 -->
	<Drawer
		v-model:drawer="selectMergeNeedPlanDetailVisible"
		:size="modalSize.md"
		destroy-on-close
	>
		<div class="drawer-container">
			<div class="drawer-column" style="width: 100%">
				<Title :title="titleConf" />

				<el-scrollbar class="rows">
					<!-- 物资信息 -->
					<selected-merge-plan-table
						:purchase-plan-id="props.id"
						:mat-id="editingTableRow?.materialId"
					/>
				</el-scrollbar>

				<button-list
					class="footer"
					:button="[
						{
							name: '取消',
							icon: ['fas', 'circle-minus']
						}
					]"
					@on-btn-click="selectMergeNeedPlanDetailVisible = false"
				/>
			</div>
		</div>
	</Drawer>
</template>

<script setup lang="ts">
import { DictApi } from "@/app/baseline/api/dict"
import { BaseLineSysApi } from "@/app/baseline/api/system"
import { useTbInit } from "../../components/tableBase"
import CostTag from "@/app/baseline/views/components/costTag.vue"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import { modalSize } from "@/app/baseline/utils/layout-config"

import { findIndex, floor, map, debounce, toNumber, first } from "lodash-es"
import needPlanDetail from "./needPlanDetail.vue"
import { IModalType } from "../../../utils/types/common"
import { useDictInit } from "../../components/dictBase"
import {
	maxValidateNum,
	maxValidateErrorInfo,
	validateAndCorrectInput
} from "@/app/baseline/utils/validate"
import {
	batchFormatterNumView,
	convertContentTableData,
	toFixedTwo
} from "@/app/baseline/utils"
import { PlanPurchaseMaterialItemApi } from "@/app/baseline/api/plan/planPurchaseMaterialItem"
import selectedMergePlanTable from "./selectedMergePlanTable.vue"

const { dictOptions, dictFilter, getDictByCodeList } = useDictInit()

const props = withDefaults(
	defineProps<{
		/**
		 * 采购计划 Id
		 */
		id: any
		mergePlanInfo: Record<string, any>
		mode: IModalType
		isApproval?: boolean // 审批页面不显示 关联合并计划
	}>(),
	{ mode: IModalType.view, isApproval: false }
)

const emit = defineEmits<{
	(e: "update"): void
}>()

/**
 * 关联合并计划 title配置
 */
const titleConf = {
	name: ["关联合并计划"],
	icon: ["fas", "square-share-nodes"]
}

const queryArrList = computed(() => [
	{
		name: "物资编码",
		key: "materialCode",
		placeholder: "请输入物资编码",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资名称",
		key: "materialLabel",
		placeholder: "请输入物资名称",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "规格型号",
		key: "version",
		placeholder: "请输入规格型号",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资性质",
		key: "attribute",
		type: "select",
		children: dictOptions.value.MATERIAL_NATURE,
		placeholder: "请选择物资性质"
	}
])

const tbInit = useTbInit()
const {
	tableCache,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	fetchParam,
	currentPage,
	selectedTableList,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = tbInit

/**
 * 要查看的采购订单id
 */
const editingTableRow = ref<Record<string, any>>()

fetchFunc.value = PlanPurchaseMaterialItemApi.getPlanPurchaseMaterialItemList

tbInit.tableProp = computed(() => {
	const defColumns: TableColumnType[] = [
		{
			label: "物资编码",
			prop: "code",
			width: 130,
			fixed: "left",
			sortable: true
		},
		{ label: "物资名称", prop: "label", width: 150 },
		{ label: "物资分类编码", prop: "materialTypeCode", width: 100 },
		{
			label: "物资分类名称",
			prop: "materialTypeLabel",
			width: 150
		},
		{ label: "规格型号", prop: "version", width: 120 },
		{
			label: "技术参数",
			prop: "technicalParameter",
			minWidth: 200,
			align: "left"
		},
		{ label: "物资性质", prop: "attribute", needSlot: true, width: 120 },
		{ label: "采购单位", prop: "buyUnit", needSlot: true, width: 90 },
		{
			label: "预估采购单价",
			prop: "evaluation",
			needSlot: true,
			width: 150,
			align: "right",
			sortable: true
		}
	]

	const editColumns: TableColumnType[] = [
		{
			label: "需求数量",
			prop: "num_view",
			align: "right",
			needSlot: true,
			width: 120,
			sortable: true
		},
		{
			label: "预估金额",
			prop: "planAmount",
			needSlot: true,
			width: 120,
			align: "right"
		},
		{
			label: "库存数量",
			prop: "storeNum",
			needSlot: true,
			align: "right",
			width: 90
		},
		{ label: "安全库存", prop: "safeStock", width: 90, align: "right" },
		{
			label: "在途数量",
			prop: "inTransitNum",
			needSlot: true,
			width: 120,
			align: "right"
		},
		{
			label: "采购数量",
			prop: "purchaseNum_view",
			needSlot: true,
			width: 120,
			align: "right",
			fixed: "right"
		},
		{
			label: "预估采购金额",
			prop: "purchaseAmount",
			needSlot: true,
			width: 100,
			fixed: "right",
			align: "right"
		},
		{
			label: "操作",
			width: 130,
			prop: "operations",
			fixed: "right",
			needSlot: true
		}
	]

	const viewColumns: TableColumnType[] = [
		{
			label: "需求数量",
			prop: "num_view",
			align: "right",
			needSlot: true,
			width: 100,
			fixed: "right",
			sortable: true
		},
		{
			label: "采购数量",
			prop: "purchaseNum_view",
			needSlot: true,
			width: 100,
			align: "right",
			fixed: "right"
		},
		{
			label: "预估采购金额",
			prop: "purchaseAmount",
			needSlot: true,
			width: 120,
			fixed: "right",
			align: "right"
		}
	]
	if (props.mode === IModalType.view) {
		if (props.isApproval) {
			viewColumns.push({
				label: "操作",
				width: 100,
				prop: "operations",
				fixed: "right",
				needSlot: true
			})
		}
		const newColumns = defColumns.concat(viewColumns)
		return newColumns
	} else {
		const newColumns = defColumns.concat(editColumns)
		return newColumns
	}
})

const lastTableData = ref<any[]>([])

/**
 * 补丁
 * 监听 tableData，重组 lastTableData
 */
watch([tableData], () => {
	lastTableData.value = convertContentTableData(tableData.value, [
		"num",
		"evaluation",
		"planAmount",
		"purchaseAmount",
		"purchaseNum",
		"storeNum",
		"inTransitNum",
		"materialId",
		"attribute"
	])

	batchFormatterNumView(lastTableData.value as any[], undefined, 0)

	if (props.mode === IModalType.view && !props.isApproval) {
		if (tableData.value?.length > 0) {
			nextTick(() => {
				tableRef.value.pitayaTableRef!.toggleRowSelection(
					first(lastTableData.value),
					true
				)
			})
		}
	}
})

const lineList = ref<[]>([])
function getLineList() {
	BaseLineSysApi.getLineList().then((res) => {
		lineList.value = res
	})
}

const getTableData = (data?: any) => {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	if (props.id) {
		fetchParam.value = {
			purchasePlanId: props.id,
			...fetchParam.value,
			...data
		}
		fetchTableData()
	}
}

onMounted(async () => {
	fetchParam.value.sord = "desc"
	fetchParam.value.sidx = "createdDate"

	getDictByCodeList(["INVENTORY_UNIT", "MATERIAL_NATURE"])
	getLineList()
	getTableData()
})

/**
 * 排序
 * @param column
 * @param prop 字段名称
 * @param order ascending 升序 descending 降序
 */
function handleSortChange(column: any, prop: any, order: any) {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	const computedProp = () => {
		if (prop === "code") {
			return "materialCode"
		} else if (prop === "num_view") {
			return "num"
		} else {
			return prop
		}
	}

	fetchParam.value = {
		...fetchParam.value,
		sord:
			order === "descending" ? "desc" : order === "ascending" ? "asc" : "desc",
		sidx: order ? computedProp() : "createdDate" // 排序字段
	}

	fetchTableData()
}

/**
 * 需求计划 详情
 */
const needPlanDetailVisible = ref(false)
function handleRowView(e?: any) {
	needPlanDetailVisible.value = true
	editingTableRow.value = e
}

/**
 * 查看关联合并计划
 */
const selectMergeNeedPlanDetailVisible = ref(false)
function handleRowMergePlanView(e?: any) {
	selectMergeNeedPlanDetailVisible.value = true
	editingTableRow.value = { ...e }
}

const tbBtnConf = computed(() => {
	return [
		{
			name: "平衡利库",
			icon: ["fas", "sliders"],
			disabled: selectedTableList.value.length > 0 ? false : true
		}
	]
})

const tbBtnLoading = ref(false)
async function tbBtnAction(btnName?: string) {
	if (btnName == "平衡利库") {
		tableLoading.value = true
		try {
			const ids = map(selectedTableList.value, ({ id }) => id)
			await PlanPurchaseMaterialItemApi.balancePurchaseMaterialItem(ids)
			getTableData()
			emit("update")
			ElMessage.success("平衡利库成功")
		} finally {
			tableLoading.value = false
		}
	}
}
function handleInputMin(e: any, row: any) {
	row.purchaseNum = validateAndCorrectInput(e, 0)
}
/**
 * 采购数量 >= 0; 整数
 * @param e
 */
function validateNum(e: any) {
	const purchaseNum = toNumber(e.purchaseNum)
	const evaluation = toNumber(e.evaluation)
	const oldRow = tableCache.find((v) => v.id == e.id)
	e.purchaseNum = purchaseNum

	if (purchaseNum < 0) {
		e.purchaseNum = oldRow.purchaseNum
		return ElMessage.warning("采购数量不能小于0！")
	} else {
		const price = toNumber(purchaseNum * evaluation)

		if (price > maxValidateNum) {
			e.purchaseNum = oldRow.purchaseNum
			ElMessage.warning(
				`您的预估采购金额已超过${maxValidateErrorInfo}元，请重新核对金额！`
			)
			return false
		}
	}

	updatePurchaseNum(e)
}

/**
 * 更新采购数量 api
 */
const updatePurchaseNum = debounce(async (e: any) => {
	const res =
		await PlanPurchaseMaterialItemApi.updateBalancePurchaseMaterialItem({
			id: e.id,
			purchaseNum: e.purchaseNum
		})

	// 失焦保存 更新预估采购金额
	e.purchaseAmount = res.purchaseAmount

	//  更新 tableCache
	const rowIdx = findIndex(tableCache, (v) => v.id === e.id)
	if (rowIdx !== -1) {
		tableCache.splice(rowIdx, 1, { ...e })
	}

	emit("update")
	ElMessage.success("操作成功")
}, 300)

defineExpose({ getTableData })
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.num-blue {
	color: $---color-info2;
}

.num-red {
	color: $---color-error;
}
.computedWidth {
	width: calc(100% - 500px);
}
</style>
