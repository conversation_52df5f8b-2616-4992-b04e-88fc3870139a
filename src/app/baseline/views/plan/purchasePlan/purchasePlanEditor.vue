<!-- 计划 - 采购计划 新增/编辑 页 -->
<script lang="ts" setup>
import { ref, onMounted, reactive } from "vue"
import { ElMessage, type FormInstance, type FormRules } from "element-plus"
import { useMessageBoxInit } from "@/app/baseline/views/components/messageBox"
import TableFile from "../../components/tableFile.vue"
import { FormElementType } from "@/app/baseline/views/components/define"
import FormElement from "@/app/baseline/views/components/formElement.vue"
import { DictApi, fileBusinessType } from "@/app/baseline/api/dict"
import { PlanPurchaseApi } from "@/app/baseline/api/plan/planPurchase"
import XEUtils from "xe-utils"
import checkNeedNumDrawer from "../components/purchase/checkNeedNumDrawer.vue"
import {
	IIdempotentTokenType,
	IIdempotentTokenTypePre,
	IModalType
} from "../../../utils/types/common"
import { getModalTypeLabel, toMoney } from "@/app/baseline/utils"
import {
	getIdempotentToken,
	requiredValidator
} from "@/app/baseline/utils/validate"
import { useDictInit } from "../../components/dictBase"
import { includes } from "lodash-es"

const props = defineProps<{
	/**
	 * 业务id
	 */
	id: any

	/**
	 * 编辑模式：编辑/新建
	 */
	mode: IModalType
}>()
const emit = defineEmits<{
	(e: "update"): void
	(e: "close"): void
}>()

const { showWarnConfirm } = useMessageBoxInit()

const { dictOptions, dictFilter, getDictByCodeList } = useDictInit()

/**
 * 保存旧的表单数据
 */
const oldFormData = ref<string>()
/**
 * 能否编辑扩展信息
 */
const canEditExtra = computed(() => Boolean(formData.value?.id || props.id))

const titleConf = computed(() => ({
	name: [
		getModalTypeLabel(
			canEditExtra.value ? IModalType.edit : IModalType.create,
			"采购计划"
		)
	],
	icon: ["fas", "square-share-nodes"]
}))

const formBtnLoading = ref(false)

const drawerRightTitle = {
	name: ["扩展信息"],
	icon: ["fas", "square-share-nodes"]
}

const formRef = ref<FormInstance>()
const formBtnList = [
	{ name: "取消", icon: ["fas", "circle-minus"] },
	{ name: "保存", icon: ["fas", "floppy-disk"] }
]
const formBtnListRight = computed(() => [
	{
		name: "提交审核",
		icon: ["fas", "circle-check"],
		disabled: formData.value["matCodeNum"] > 0 ? false : true
	}
])

/**
 * 表单默认值
 */
const futureYears = DictApi.getFutureYears(-1, 1)
const formData = ref<Record<string, any>>({
	id: props.id,
	year: futureYears[1].value,
	planType: "1"
})

/**
 * 表单 配置
 */
const formEl = computed<FormElementType[][]>(() => [
	[
		{
			label: "采购计划ID",
			name: "id",
			maxlength: 50,
			disabled: true,
			type: "hidden"
		}
	],
	[
		{
			label: "计划年度",
			name: "year",
			type: "year",
			format: "YYYY",
			valueFormat: "YYYY",
			disabledDate: (time) => {
				const year = time.getFullYear()
				return year < new Date().getFullYear()
			},
			disabled: canEditExtra.value
		}
	],
	[
		{
			label: "年度计划类型",
			name: "planType",
			type: "select",
			clear: true,
			disabled: true,
			data: dictOptions.value.PLAN_CATEGORY
		}
	],
	[{ label: "采购计划名称", name: "label", maxlength: 50 }],
	[
		{
			label: "物资编码数量",
			name: "matCodeNum",
			disabled: true
		}
	],
	[
		{ label: "预估采购金额（元）", name: "purchaseAmount_view", disabled: true }
	],
	[
		{
			label: "备注说明",
			name: "remark",
			maxlength: 200,
			rows: "5",
			type: "textarea"
		}
	]
])

/**
 * 表单 较验
 */
const formRules = reactive<FormRules<typeof formData.value>>({
	year: requiredValidator("计划年度"),
	label: requiredValidator("采购计划名称")
})

const drawerLoading = ref(false)

/**
 * 获取详情
 */
async function getDetail() {
	if (!props.id && !formData.value.id) {
		return false
	}

	drawerLoading.value = true
	try {
		const res = await PlanPurchaseApi.getPlanPurchase(
			props.id || formData.value.id
		)
		formData.value = { ...res }

		formData.value["purchaseAmount_view"] = toMoney(res.purchaseAmount as any)

		formData.value.year = res.year + ""
		oldFormData.value = JSON.stringify(formData.value)
	} finally {
		drawerLoading.value = false
	}
}

/**
 * 表单 按钮事件
 * @param btnName
 */
const handleFormAction = async (btnName: string | undefined) => {
	if (!formRef.value) {
		return false
	}
	if (btnName === "保存") {
		handleSaveDraft()
	} else if (btnName === "提交审核") {
		handleSubmit()
	} else if (btnName === "取消") {
		formRef.value?.clearValidate()
		emit("close")
	}
}

/**
 * 处理保存草稿（包含新增和编辑）
 */
async function handleSaveDraft() {
	if (!formRef.value) {
		return
	}

	formRef.value.validate(async (valid: any) => {
		if (!valid) {
			return
		}
		formBtnLoading.value = true
		// 表单校验通过
		try {
			const api = canEditExtra.value
				? PlanPurchaseApi.updatePlanPurchase
				: PlanPurchaseApi.addPlanPurchase

			let idempotentToken = ""
			if (!canEditExtra.value) {
				idempotentToken = getIdempotentToken(
					IIdempotentTokenTypePre.apply,
					IIdempotentTokenType.planPurchase
				)
			}

			const r = await api(formData.value as any, idempotentToken)

			formData.value.id = r.id

			formData.value["purchaseAmount_view"] = toMoney(r.purchaseAmount as any)

			oldFormData.value = JSON.stringify(formData.value)

			ElMessage.success("操作成功")
			emit("update")

			matTableRef.value?.getTableData()
			//fetchTableData()
		} finally {
			formBtnLoading.value = false
		}
	})
}

/**
 * 处理提交审核
 *
 * 需要表单校验
 */
function handleSubmit() {
	if (!formRef.value) {
		return
	}

	formRef.value.validate(async (valid: any) => {
		if (!valid) {
			return
		}

		//先查询是否已有合并计划 根据 matCodeNum
		if (
			!formData.value["matCodeNum"] ||
			parseInt(formData.value["matCodeNum"]) <= 0
		) {
			return ElMessage.warning("需选择合并计划才可以提交审核")
		}

		await showWarnConfirm("请确认是否提交本次数据？")
		formBtnLoading.value = true
		drawerLoading.value = true

		try {
			// 如果主表有修改，则先更新主表数据
			if (oldFormData.value != JSON.stringify(formData.value)) {
				await PlanPurchaseApi.updatePlanPurchase(formData.value as any)
			}

			const checkNum = await PlanPurchaseApi.updatePlanPurchaseCheckNum(
				formData.value.id
			)

			/**
			 * 判断采购数量是否等于需求数量
			 * 存在不相等的则返回false；
			 * 都相等时true
			 */
			if (!checkNum) {
				// 不相等时 填原因
				checkNumDialogVisible.value = true
			} else {
				const idempotentToken = getIdempotentToken(
					IIdempotentTokenTypePre.other,
					IIdempotentTokenType.planPurchase,
					formData.value.id
				)
				const { code, data, msg } = await PlanPurchaseApi.publishApply(
					{
						id: formData.value.id
					},
					idempotentToken
				)

				if (data && code != 200) {
					errorGoodsIdList.value = data || []
					ElMessage.error(msg)
					matTableRef.value?.getTableData()
				} else {
					ElMessage.success("操作成功")
					emit("update")
					emit("close")
				}
			}
		} finally {
			formBtnLoading.value = false
			drawerLoading.value = false
		}
	})
}

//扩展栏标签页切换
const tabList = ["合并计划", "物资明细", "相关附件"]
const activeTab = ref<number>(0)
const handleTabChange = (index: number) => {
	activeTab.value = index
}

onMounted(async () => {
	getDictByCodeList(["PLAN_CATEGORY"])

	getDetail()
})

const checkNumDialogVisible = ref(false)

/**
 * 需求变更原因 保存
 * @param reason
 */
async function handleCheckNumReason(reason: any) {
	await PlanPurchaseApi.updatePlanPurchase({
		id: formData.value.id,
		reason: reason
	})

	await PlanPurchaseApi.publishApply(formData.value)

	checkNumDialogVisible.value = false
	ElMessage.success("提交审核成功")
	emit("close")
	emit("update")
}

/**
 * 表格组件
 */
const tableComponent = computed(() => {
	const els = [
		// 合并计划
		defineAsyncComponent(() => import("./mergePlanTable.vue")),
		// 物资明细
		defineAsyncComponent(() => import("./mergePlanMatTable.vue"))
	]

	return els[activeTab.value]
})

function handleUpdateAll() {
	getDetail()
	emit("update")
}

const matTableRef = ref()
/**
 * 物资明细 table 行样式
 *
 * 库存物资异常，用红色标记该行
 */
const errorGoodsIdList = ref<number[]>([])
function tbCellClassName({ row }: any) {
	return includes(errorGoodsIdList.value, row.id) ? "error" : ""
}

defineOptions({
	name: "purchasePlanDrawer"
})
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<!-- 左侧表单区域 -->
		<div class="drawer-column left">
			<div class="rows">
				<Title :title="titleConf" />
				<el-scrollbar>
					<el-form
						class="content"
						style="padding-bottom: 30px"
						:model="formData"
						:rules="formRules"
						ref="formRef"
						label-position="top"
						label-width="100px"
					>
						<FormElement :form-element="formEl" :form-data="formData" />
					</el-form>
				</el-scrollbar>
			</div>
			<ButtonList
				class="footer"
				:button="formBtnList"
				:loading="formBtnLoading"
				@on-btn-click="handleFormAction"
			/>
		</div>
		<!-- 右侧table区域 -->
		<div class="drawer-column right" :class="canEditExtra ? '' : ' disabled'">
			<div class="rows">
				<Title :title="drawerRightTitle">
					<Tabs class="tabs" :tabs="tabList" @on-tab-change="handleTabChange" />
				</Title>

				<div v-if="activeTab === 2">
					<TableFile
						:business-type="fileBusinessType.purchasePlan"
						:business-id="props.id || formData.id"
					/>
				</div>

				<div v-else>
					<component
						:is="tableComponent"
						:id="props.id || formData.id"
						:mergePlanInfo="formData"
						:mode="props.mode"
						v-if="activeTab < 2"
						@update="handleUpdateAll"
						:tb-cell-class-name="tbCellClassName"
						ref="matTableRef"
					/>
				</div>
			</div>
			<ButtonList
				class="footer"
				:button="formBtnListRight"
				:loading="formBtnLoading"
				@on-btn-click="handleFormAction"
			/>
		</div>

		<!-- 校验采购数量是否等于需求数量; 填写原因 -->
		<check-need-num-drawer
			v-model:dialog="checkNumDialogVisible"
			@close="checkNumDialogVisible = false"
			@save="handleCheckNumReason"
		/>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.drawer-container {
	.left {
		width: 310px;
	}

	.right {
		width: calc(100% - 310px);
	}
}
</style>
