<!-- 合并计划 table -->
<template>
	<el-scrollbar style="height: 100%" class="editor-table-wrapper">
		<pitaya-table
			ref="tableRef"
			:columns="(tbInit.tableProp as any)"
			:table-data="lastTableData"
			:need-index="true"
			:single-select="false"
			:need-selection="props.mode !== IModalType.view ? true : false"
			:need-pagination="true"
			:total="pageTotal"
			:table-loading="tableLoading"
			:cell-class-name="tbCellClassName"
			@on-selection-change="selectedTableList = $event"
			@on-current-page-change="onCurrentPageChange"
		>
			<template #purchaseAmount="{ rowData }">
				<cost-tag :value="rowData.purchaseAmount" />
			</template>

			<template #lineNoId="{ rowData }">
				<line-tag :options="lineList" :value="rowData.lineNoId" />
			</template>
			<template #operations="{ rowData }">
				<el-button v-btn link @click.stop="handleRowView(rowData)">
					<font-awesome-icon :icon="['fas', 'eye']" />
					<span class="table-inner-btn">物资明细</span>
				</el-button>
				<el-button
					v-btn
					link
					@click.stop="handleRowDel(rowData)"
					v-if="props.mode !== IModalType.view"
				>
					<font-awesome-icon :icon="['fas', 'trash-can']" />
					<span class="table-inner-btn">移除</span>
				</el-button>
			</template>
			<template #footerOperateLeft v-if="props.mode !== IModalType.view">
				<ButtonList
					class="btn-list"
					:is-not-radius="true"
					:button="tbBtnConf"
					:loading="tbBtnLoading"
					@on-btn-click="tbBtnAction"
				/>
			</template>
		</pitaya-table>
	</el-scrollbar>

	<!-- 查看合并计划详情弹窗 -->
	<Drawer
		v-model:drawer="mergePlanDetailVisible"
		:size="modalSize.xl"
		destroy-on-close
	>
		<merge-plan-detail
			:id="editingTableRow?.mergePlanId"
			:purchasePlanId="props.id"
			:plan-type="'plan-purchase'"
			@close="mergePlanDetailVisible = false"
		/>
	</Drawer>

	<!-- 选择合并计划弹窗 -->
	<Drawer
		:size="modalSize.lg"
		v-model:drawer="mergePlanSelectorVisible"
		:destroyOnClose="true"
	>
		<merge-plan-selector
			:multiple="true"
			title="选择合并计划"
			:table-req-params="{
				purchasePlanId: props?.mergePlanInfo?.id,
				bpmStatus: appStatus.approved,
				year: props?.mergePlanInfo?.year
			}"
			@close="mergePlanSelectorVisible = false"
			@save="handleSaveMergePlanList"
		/>
	</Drawer>
</template>

<script setup lang="ts">
import mergePlanSelector from "./mergePlanSelector.vue"
import { BaseLineSysApi } from "@/app/baseline/api/system"
import { appStatus } from "@/app/baseline/api/dict"
import { useTbInit } from "../../components/tableBase"
import CostTag from "@/app/baseline/views/components/costTag.vue"
import LineTag from "@/app/baseline/views/components/lineTag.vue"
import { modalSize } from "@/app/baseline/utils/layout-config"
import { convertContentTableData } from "@/app/baseline/utils"
import { useMessageBoxInit } from "@/app/baseline/views/components/messageBox"

import { map } from "lodash-es"
import mergePlanDetail from "./mergePlanDetail.vue"
import {
	IIdempotentTokenType,
	IIdempotentTokenTypePre,
	IModalType
} from "../../../utils/types/common"
import { PlanPurchaseMergeItemAPi } from "@/app/baseline/api/plan/planPurchaseMergeItem"
import { getIdempotentToken } from "@/app/baseline/utils/validate"

const { showInfoConfirm } = useMessageBoxInit()

const props = withDefaults(
	defineProps<{
		/**
		 * 采购计划 Id
		 */
		id: any
		mergePlanInfo: Record<string, any>
		mode: IModalType
		tbCellClassName?: (params: any) => string
	}>(),
	{ mode: IModalType.view }
)

const emit = defineEmits<{
	(e: "update"): void
}>()
const tbInit = useTbInit()
const {
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	fetchParam,
	currentPage,
	selectedTableList,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = tbInit

/**
 * 要查看的采购订单id
 */
const editingTableRow = ref<Record<string, any>>()

fetchFunc.value = PlanPurchaseMergeItemAPi.getPlanPurchaseMergeItemList

tbInit.tableProp = computed(() => {
	const defColumns: TableColumnType[] = [
		{ label: "年度", prop: "year", width: 85, fixed: "left" },
		{ label: "合并计划号", prop: "code", width: 160, fixed: "left" },
		{
			label: "合并部门",
			prop: "sysOrgId_view",
			needSlot: false,
			width: 140,
			fixed: "left"
		},

		{ label: "合并计划名称", prop: "label", minWidth: 150 },
		{ label: "专业", prop: "majorId_view", width: 150 },
		{ label: "线路", prop: "lineNoId", needSlot: true, width: 100 },
		{
			label: "费用类别",
			prop: "expenseCategory_view",
			width: 120
		},
		{ label: "需求计划数量", prop: "planNum", width: 100 },
		{
			label: "预估采购金额",
			prop: "purchaseAmount",
			needSlot: true,
			width: 150,
			align: "right"
		},
		{ label: "创建人", prop: "createdBy_view", needSlot: false, width: 120 },
		{ label: "备注说明", prop: "remark", width: 160, align: "left" },
		{
			label: "操作",
			width: props.mode == IModalType.view ? 100 : 160,
			prop: "operations",
			fixed: "right",
			needSlot: true
		}
	]

	return defColumns
})

const lastTableData = ref<any[]>([])

/**
 * 补丁
 * 监听tableData 重组lastTableData
 */
watch([tableData], () => {
	lastTableData.value = convertContentTableData(tableData.value, [
		"mergePlanId"
	])
})

const lineList = ref<[]>([])
function getLineList() {
	BaseLineSysApi.getLineList().then((res) => {
		lineList.value = res
	})
}

const getTableData = () => {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	if (props.id) {
		fetchParam.value = {
			purchasePlanId: props.id,
			...fetchParam.value
		}
		fetchTableData()
	}
}

onMounted(async () => {
	fetchParam.value.sord = "desc"
	fetchParam.value.sidx = "createdDate"

	getLineList()
	getTableData()
})

/**
 * 需求计划 详情
 */
const mergePlanDetailVisible = ref(false)
function handleRowView(e?: any) {
	mergePlanDetailVisible.value = true
	editingTableRow.value = e
}

async function handleRowDel(e: any) {
	await showInfoConfirm("确定要移除吗？")

	await PlanPurchaseMergeItemAPi.deletePlanPurchaseMergeItem(e.id)
	// 更新左侧
	emit("update")
	fetchTableData()
}

const tbBtnConf = computed(() => {
	return [
		{
			name: "选择合并计划",
			icon: ["fas", "circle-plus"]
		},
		{
			name: "批量移除",
			icon: ["fas", "trash-alt"],
			disabled: selectedTableList.value?.length > 0 ? false : true
		}
	]
})

const tbBtnLoading = ref(false)
const mergePlanSelectorVisible = ref(false)

async function tbBtnAction(btnName?: string) {
	if (btnName == "批量移除") {
		await showInfoConfirm("确定要移除选中数据吗？")

		try {
			tbBtnLoading.value = true
			const ids = map(selectedTableList.value, ({ id }) => id).toString()

			await PlanPurchaseMergeItemAPi.deletePlanPurchaseMergeItem(ids)
			// 更新左侧
			emit("update")
			fetchTableData()
		} finally {
			tbBtnLoading.value = false
		}
	} else if (btnName == "选择合并计划") {
		mergePlanSelectorVisible.value = true
	}
}

async function handleSaveMergePlanList(needList?: any) {
	const matList =
		map(needList, (v) => ({ purchasePlanId: props.id, mergePlanId: v.id })) ||
		[]

	const idempotentToken = getIdempotentToken(
		IIdempotentTokenTypePre.other,
		IIdempotentTokenType.planPurchase,
		props.id
	)
	await PlanPurchaseMergeItemAPi.addBatchPlanPurchaseMergeItem(
		matList,
		idempotentToken
	)

	ElMessage.success("保存成功")

	getTableData()
	emit("update")
	mergePlanSelectorVisible.value = false
}

defineExpose({
	getTableData
})
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.editor-table-wrapper {
	&:deep(.pitaya-table) {
		.error {
			.column-inner-item,
			.cell,
			.el-input__inner {
				color: red;
			}
		}
	}
}
</style>
