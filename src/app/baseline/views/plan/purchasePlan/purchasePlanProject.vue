<!-- 采购计划 - 采购项目 -->
<script lang="ts" setup>
import { onMounted } from "vue"
import { useTbInit } from "@/app/baseline/views/components/tableBase"
import CostTag from "@/app/baseline/views/components/costTag.vue"
import { PurchaseProjectApi } from "@/app/baseline/api/purchase/purchaseProject"
import { useDictInit } from "@/app/baseline/views/components/dictBase"
import { getOnlyDate } from "../../../utils"
const { dictFilter, getDictByCodeList } = useDictInit()

interface Props {
	id: string | number // 采购计划ID
}
const props = defineProps<Props>()

const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	currentPage,
	pageTotal,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange,
	onDataSelected
} = useTbInit()

tableProp.value = [
	{ label: "年度", prop: "year", width: 85, fixed: "left" },
	{ label: "采购计划号", prop: "planPurchaseCode", width: 150, fixed: "left" },
	{ label: "采购项目编号", prop: "code", width: 180, fixed: "left" },
	{ label: "采购项目名称", prop: "label", minWidth: 150 },
	{ label: "合同编号", prop: "contractCode", width: 150 },
	{
		label: "合同签订日期",
		prop: "contractSigningDate",
		needSlot: true,
		width: 160
	},
	{
		label: "合同截止日期",
		prop: "contractEndDate",
		needSlot: true,
		width: 160
	},
	{ label: "合同类型", prop: "contractType", needSlot: true, width: 130 },
	{
		label: "合同金额",
		prop: "contractAmount",
		needSlot: true,
		width: 130,
		align: "right"
	},
	{ label: "供应商", prop: "supplierLabel", minWidth: 150 }
]

// 获取表格默认参数

fetchFunc.value = PurchaseProjectApi.getPurchaseProjectList

const getTableData = (data?: any) => {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		...data
	}
	fetchTableData()
}

onMounted(async () => {
	fetchParam.value = {
		planPurchaseId: props.id,
		sord: "desc",
		sidx: "createdDate"
	}
	getDictByCodeList(["CONTRACT_TYPE"])
	getTableData({})
})
</script>
<template>
	<el-scrollbar style="height: 100%">
		<PitayaTable
			ref="tableRef"
			:columns="tableProp"
			:tableData="tableData"
			:total="pageTotal"
			:single-select="true"
			:need-selection="false"
			:need-index="true"
			@onSelectionChange="onDataSelected"
			@on-current-page-change="onCurrentPageChange"
			:table-loading="tableLoading"
		>
			<!-- 合同签订日期 -->
			<template #contractSigningDate="{ rowData }">
				{{ getOnlyDate(rowData.contractSigningDate) || "---" }}
			</template>

			<!-- 合同截止日期 -->
			<template #contractEndDate="{ rowData }">
				{{ getOnlyDate(rowData.contractEndDate) || "---" }}
			</template>

			<!-- 合同金额 -->
			<template #contractAmount="{ rowData }">
				<cost-tag :value="rowData.contractAmount" />
			</template>

			<!-- 合同类型 -->
			<template #contractType="{ rowData }">
				{{
					dictFilter("CONTRACT_TYPE", rowData.contractType)?.subitemName ||
					"---"
				}}
			</template>
		</PitayaTable>
	</el-scrollbar>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
