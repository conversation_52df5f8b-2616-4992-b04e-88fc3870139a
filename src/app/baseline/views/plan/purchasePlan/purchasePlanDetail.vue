<!-- 计划 - 采购计划 查看 页 -->
<script lang="ts" setup>
import { ref, onMounted } from "vue"
import TableFile from "../../components/tableFile.vue"
import { fileBusinessType } from "@/app/baseline/api/dict"
import { PlanPurchaseApi } from "@/app/baseline/api/plan/planPurchase"
import XEUtils from "xe-utils"
import { IModalType } from "../../../utils/types/common"
import { getModalTypeLabel } from "@/app/baseline/utils"
import CostTag from "@/app/baseline/views/components/costTag.vue"
import purchasePlanProject from "./purchasePlanProject.vue"

const props = withDefaults(
	defineProps<{
		/**
		 * 业务id
		 */
		id: any

		/**
		 * 编辑模式：编辑/新建
		 */
		mode?: IModalType

		footerBtnVisible?: boolean
	}>(),
	{ mode: IModalType.view, footerBtnVisible: true }
)
const emit = defineEmits<{
	(e: "close"): void
}>()

/**
 * 能否编辑扩展信息
 */
const canEditExtra = computed(() => Boolean(formData.value?.id || props.id))

const titleConf = computed(() => ({
	name: [getModalTypeLabel(props.mode, "采购计划")],
	icon: ["fas", "square-share-nodes"]
}))

const formBtnLoading = ref(false)

const drawerRightTitle = {
	name: ["扩展信息"],
	icon: ["fas", "square-share-nodes"]
}

const formBtnListRight = [{ name: "取消", icon: ["fas", "circle-minus"] }]

/**
 * 表单默认值
 */
const formData = ref<Record<string, any>>({
	id: props.id
})

const drawerLoading = ref(false)

const defConfOptions = [
	{ label: "采购计划号", name: "code" },
	{ label: "采购计划名称", name: "label" },
	{ label: "计划年度", name: "year" },
	{ label: "物资编码数量", name: "matCodeNum" },
	{ label: "预估总金额", name: "purchaseAmount", type: "money" },
	{ label: "备注说明", name: "remark", rows: "3" },
	{ label: "创建人", name: "createdBy_view" },
	{ label: "创建时间", name: "createdDate" }
]

/**
 * 获取详情
 */
async function getDetail() {
	if (!props.id && !formData.value.id) {
		return false
	}

	drawerLoading.value = true
	try {
		const res = await PlanPurchaseApi.getPlanPurchase(
			props.id || formData.value.id
		)
		formData.value = { ...res }

		const tmpAmount = `${XEUtils.commafy(res.purchaseAmount as any, {
			digits: 5
		})}`
		formData.value["purchaseAmount_view"] = tmpAmount ? `￥${tmpAmount}` : "---"
		formData.value.year = res.year + ""
	} finally {
		drawerLoading.value = false
	}
}

//扩展栏标签页切换
const tabList = ["合并计划", "物资明细", "相关附件", "采购项目"]
const activeTab = ref<number>(0)
const handleTabChange = (index: number) => {
	activeTab.value = index
}

onMounted(async () => {
	getDetail()
})

/**
 * 表格组件
 */
const tableComponent = computed(() => {
	const els = [
		// 合并计划
		defineAsyncComponent(() => import("./mergePlanTable.vue")),
		// 物资明细
		defineAsyncComponent(() => import("./mergePlanMatTable.vue"))
	]

	return els[activeTab.value]
})
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<!-- 左侧表单区域 -->
		<div class="drawer-column left">
			<div class="rows">
				<Title :title="titleConf" />
				<el-scrollbar>
					<el-descriptions
						size="small"
						:column="1"
						:border="true"
						class="content"
					>
						<el-descriptions-item
							v-for="(el, index) in defConfOptions"
							label-align="center"
							:label="el.label"
							:key="index"
						>
							<span v-if="el.type == 'money'">
								<cost-tag :value="formData[el.name]" />
							</span>
							<span v-else>
								{{ formData[el.name] ? formData[el.name] : "---" }}
							</span>
						</el-descriptions-item>
					</el-descriptions>
				</el-scrollbar>
			</div>
		</div>
		<!-- 右侧table区域 -->
		<div
			class="drawer-column right"
			:class="{ pdr10: !props.footerBtnVisible }"
		>
			<div class="rows">
				<Title :title="drawerRightTitle">
					<Tabs class="tabs" :tabs="tabList" @on-tab-change="handleTabChange" />
				</Title>

				<TableFile
					v-if="activeTab === 2"
					:mod="props.mode"
					:business-type="fileBusinessType.purchasePlan"
					:business-id="props.id || formData.id"
				/>

				<component
					v-else-if="activeTab < 2"
					:is="tableComponent"
					:id="props.id || formData.id"
					:mergePlanInfo="formData"
					:mode="props.mode"
					:is-approval="!props.footerBtnVisible"
				/>

				<purchase-plan-project
					:id="props.id || formData.id"
					v-if="activeTab === 3"
				/>
			</div>
			<ButtonList
				v-if="props.footerBtnVisible"
				class="footer"
				:button="formBtnListRight"
				:loading="formBtnLoading"
				@on-btn-click="emit('close')"
			/>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.drawer-container {
	.left {
		width: 310px;
	}

	.right {
		width: calc(100% - 310px);
	}
}
</style>
