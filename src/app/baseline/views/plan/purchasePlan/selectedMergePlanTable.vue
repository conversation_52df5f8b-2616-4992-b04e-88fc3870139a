<!-- 物资明细 关联合并计划 table -->
<template>
	<el-scrollbar style="height: 100%">
		<pitaya-table
			ref="tableRef"
			:columns="(tbInit.tableProp as any)"
			:table-data="tableData"
			:need-index="true"
			:single-select="false"
			:need-selection="false"
			:need-pagination="true"
			:total="pageTotal"
			:table-loading="tableLoading"
			@on-selection-change="selectedTableList = $event"
			@on-current-page-change="onCurrentPageChange"
		>
			<template #purchaseAmount="{ rowData }">
				<cost-tag :value="rowData.purchaseAmount" />
			</template>
			<template #num="{ rowData }">
				{{ toFixedTwo(rowData.num, 0) }}
			</template>
		</pitaya-table>
	</el-scrollbar>
</template>

<script setup lang="ts">
import { BaseLineSysApi } from "@/app/baseline/api/system"
import { useTbInit } from "../../components/tableBase"
import CostTag from "@/app/baseline/views/components/costTag.vue"
import { toFixedTwo } from "@/app/baseline/utils"
import { PlanPurchaseMaterialItemApi } from "@/app/baseline/api/plan/planPurchaseMaterialItem"

const props = withDefaults(
	defineProps<{
		/**
		 * 采购计划id
		 */
		purchasePlanId: any
		matId?: any
	}>(),
	{}
)

const tbInit = useTbInit()
const {
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	fetchParam,
	currentPage,
	selectedTableList,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = tbInit

fetchFunc.value =
	PlanPurchaseMaterialItemApi.getPlanPurchaseMaterialItemMergePlanList

tbInit.tableProp = computed(() => {
	const defColumns: TableColumnType[] = [
		{ label: "合并计划号", prop: "code", width: 160, fixed: "left" },
		{ label: "合并计划名称", prop: "label", minWidth: 130 },
		{ label: "合并部门", prop: "sysOrgId_view", width: 130 },
		{
			label: "需求数量",
			prop: "num",
			align: "right",
			needSlot: true,
			width: 120
		},
		{
			label: "预估采购金额",
			prop: "purchaseAmount",
			needSlot: true,
			width: 150,
			align: "right"
		}
	]

	return defColumns
})

const lineList = ref<[]>([])
function getLineList() {
	BaseLineSysApi.getLineList().then((res) => {
		lineList.value = res
	})
}

watch(
	() => props.matId,
	() => {
		if (props.purchasePlanId && props.matId) {
			fetchParam.value = {
				...fetchParam.value,
				purchasePlanId: props.purchasePlanId,
				materialId: props.matId
			}
			fetchTableData()
		} else {
			tableData.value = []
		}
	}
)
const getTableData = () => {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	if (props.purchasePlanId && props.matId) {
		fetchParam.value = {
			purchasePlanId: props.purchasePlanId,
			materialId: props.matId,
			...fetchParam.value
		}
		fetchTableData()
	}
}

onMounted(async () => {
	getLineList()
	getTableData()
})
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
