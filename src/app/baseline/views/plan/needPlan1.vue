<!-- 需求计划 V1.0 废弃 -->
<script lang="ts" setup>
import { ElMessage } from "element-plus"
import {
	checkPermission,
	isCheckPermission
} from "@/app/platform/utils/permission"
import { DictApi, purchaseOrderSource } from "@/app/baseline/api/dict"
import { useTbInit } from "@/app/baseline/views/components/tableBase"
import { powerList } from "@/app/baseline/views/components/define.d"
import { appStatus } from "@/app/baseline/api/dict"
import { useUserStore } from "@/app/platform/store/modules/user"
import { BaseLineSysApi } from "@/app/baseline/api/system"
import { hasPermi, tableColFilter } from "../../utils"
import { ref, onMounted, reactive } from "vue"
import { PlanApi } from "@/app/baseline/api/plan/plan"
import { useMessageBoxInit } from "@/app/baseline/views/components/messageBox"
import { useDictInit } from "@/app/baseline/views/components/dictBase"

const { showDelConfirm } = useMessageBoxInit()
const { dictOptions, getDictByCodeList } = useDictInit()

import NeedPlanDrawer from "./components/needPlanDrawer.vue"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import CostTag from "@/app/baseline/views/components/costTag.vue"
import LineTag from "@/app/baseline/views/components/lineTag.vue"
import { YearPlanApi } from "@/app/baseline/api/plan/yearPlan"
import XEUtils from "xe-utils"

//当前用户信息
const userStore = useUserStore()
const { userInfo } = storeToRefs(userStore)

const queryArrRef = ref<any>()

/*-------------------初始化表格-start-------------------*/
const tbInit = useTbInit()
const {
	tableData,
	tableRef,
	tableLoading,
	tbBtnLoading,
	pageSize,
	currentPage,
	pageTotal,
	selectedTableList,
	fetchParam,
	tbBtns,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange,
	onDataSelected,
	onBtnClick
} = tbInit

tbInit.tableProp = computed(() => {
	const defCols: TableColumnType[] = [
		{ label: "计划年度", prop: "year", width: 80, fixed: "left" },
		{ label: "需求计划号", prop: "code", width: 160, fixed: "left" },
		{ label: "需求计划名称", prop: "label", minWidth: 200 },
		{ label: "物资编码数量", prop: "matCodeNum", width: 100 },
		{
			label: "预估采购金额",
			prop: "purchaseAmount",
			needSlot: true,
			width: 160,
			align: "right"
		},
		{ label: "线路", prop: "lineNoId", needSlot: true, width: 120 },
		{ label: "专业", prop: "majorId_view", width: 120 },
		{ label: "段区", prop: "depotId_view", needSlot: true, width: 100 },
		{ label: "费用类别", prop: "expenseCategory_view", width: 100 },
		{ label: "审批状态", prop: "bpmStatus", needSlot: true, width: 90 },
		/* { label: "到货状态", prop: "arrivalStatus", needSlot: true, width: 120 }, */
		{ label: "公司", prop: "sysCommunityId_view", width: 160 },
		{ label: "需求部门", prop: "sysOrgId_view", width: 160 },
		{ label: "创建人", prop: "createdBy_view", needSlot: false, width: 120 },
		{ label: "创建时间", prop: "createdDate", width: 160 },
		{
			label: "操作",
			width: 150,
			prop: "operations",
			fixed: "right",
			needSlot: true
		}
	]

	/* if (currentType.value == "1") {
		return tableColFilter(defCols, ["到货状态"])
	} else { */
	return defCols
	/* } */
})

onDataSelected.value = (rowList: any) => {
	selectedTableList.value = rowList
	const isSel = rowList.length != 1
	tbBtns.value[0].disabled =
		rowList.some((_p: any) => _p.status === appStatus.rejected.toString()) ||
		isSel
}
fetchFunc.value = PlanApi.getList
tbBtns.value = []
/*-------------------初始化表格-end-------------------*/

const rightTitle = {
	name: ["需求计划"],
	icon: ["fas", "square-share-nodes"]
}
const addBtn = ref([
	{
		name: "新建需求计划",
		roles: powerList.needPlanBtnCreate,
		icon: ["fas", "square-plus"]
	}
])

function descOptionFilter(cols: Record<string, any>[], excludeCols: string[]) {
	return cols.filter((c) => !excludeCols.includes(c.name))
}

const queryArrList = computed<Record<string, any>[]>(() => {
	const all = [
		{
			name: "所属公司",
			key: "sysCommunityId",
			type: "treeSelect",
			treeApi: BaseLineSysApi.getCompanyAllList,
			placeholder: "请选择所属公司"
		},
		{
			name: "年度",
			key: "year",
			type: "select",
			children: DictApi.getFutureYears(-2, 2),
			placeholder: "请选择年度"
		},
		{
			name: "需求计划号",
			key: "code",
			type: "input",
			placeholder: "请输入需求计划号",
			enableFuzzy: true
		},
		{
			name: "线路",
			key: "lineNoId",
			type: "treeSelect",
			treeApi: () =>
				BaseLineSysApi.getLineList({
					companyId: fetchParam.value.companyRange
				}),
			placeholder: "请选择线路"
		},
		{
			name: "段区",
			key: "depotId",
			type: "treeSelect",
			treeApi: () =>
				BaseLineSysApi.getSegmentList(fetchParam.value.companyRange),
			placeholder: "请选择段区"
		},
		{
			name: "专业",
			key: "parentMajorId",
			type: "treeSelect",
			treeApi: () =>
				BaseLineSysApi.getProfessionTree(fetchParam.value.companyRange),
			placeholder: "请选择专业"
		},
		{
			name: "费用类别",
			key: "expenseCategory",
			type: "treeSelect",
			treeApi: () => Promise.resolve(dictOptions.value.COST_CATEGORY),
			replaceIdTo: "subitemValue",
			placeholder: "请选择费用类别"
		},
		{
			name: "需求部门",
			key: "sysOrgId",
			type: "treeSelect",
			treeApi: () => BaseLineSysApi.getDepartmentTree(curSelCompany.value),
			placeholder: "请选择需求部门"
		},
		{
			name: "审批状态",
			key: "bpmStatus",
			type: "select",
			placeholder: "请选择审批状态",
			children: DictApi.getQueryBpmStatus()
		}
	]

	if (currentType.value == "1") {
		return descOptionFilter(all, ["年度"])
	} else {
		return all
	}
})

////tabs
const tabList = ref<{ [propName: string]: any }>([])

//需求计划类型
const tabNum = reactive<Record<string, any>>({})
const currentType = ref("")

const editId = ref<any>("")
const showDrawer = ref<boolean>(false)
const drawModel = ref("view")
const drawerSize = 1500
const lineList = ref<[]>([])
const segmentList = ref<{ [propName: string]: any }[]>([])
const costSubject = ref(false)

const curSelCompany = ref("")
const singleQueryData = (
	key: string,
	queryData: { [propName: string]: any }
) => {
	if (key === "sysCommunityId") {
		if (Array.isArray(queryData) && queryData.length > 0) {
			curSelCompany.value = queryData[0].id
		} else {
			curSelCompany.value = ""
		}
	}
}

function initCompSelector(): Promise<void> {
	return Promise.resolve()
}

//读取费用类别
function getCostSubject(): Promise<any> {
	return new Promise((resolve) => {
		if (costSubject.value) resolve(costSubject.value)
		else {
			BaseLineSysApi.getCostSubject()
				.then((_r) => {
					costSubject.value = _r
				})
				.finally(() => {
					resolve(costSubject.value)
				})
		}
	})
}

function initPlanType(): Promise<void> {
	return new Promise((resolve) => {
		DictApi.getDictByCode("PLAN_CATEGORY")
			.then((res: any) => {
				res = res.filter(
					(_d: any) =>
						[
							purchaseOrderSource.annualDemandPlan,
							purchaseOrderSource.emergencyPlan
						].indexOf(_d.value) >= 0
				)
				tabList.value = res
				res.map((_t: any) => {
					tabNum[_t.subitemValue.toString()] = 0
				})
				currentType.value = res[0].subitemValue
				fetchParam.value.planType = currentType.value
				addBtn.value[0].name = `新建${res[0].subitemName}`
			})
			.finally(() => {
				resolve()
			})
	})
}
function initSegmentList(): Promise<void> {
	return new Promise((resolve) => {
		BaseLineSysApi.getSegmentList()
			.then((res) => {
				segmentList.value = res
			})
			.finally(() => {
				resolve()
			})
	})
}
function getListList(): Promise<void> {
	return new Promise<void>((resolve) => {
		BaseLineSysApi.getLineList()
			.then((res) => {
				lineList.value = res
			})
			.finally(() => resolve())
	})
}

const showDetails = ref<any>({})

function getTypeNumbers(): Promise<void> {
	return new Promise<void>((resolve) => {
		const _param = JSON.parse(JSON.stringify(fetchParam.value))
		delete _param["planType"]
		delete _param["currentPage"]
		delete _param["pageSize"]
		_param.planYearId = selectYearData.value.id
		PlanApi.getPlanCnt(_param)
			.then((_r) => {
				Object.keys(tabNum).map((_k) => {
					if (_r[_k] != undefined) tabNum[_k] = _r[_k].needPlanNum || 0
					else tabNum[_k] = 0
				})
				showDetails.value = _r["1"]
			})
			.finally(() => {
				resolve()
			})
	})
}

function handleTabsClick(tab: any) {
	currentType.value = tab.props.name
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {}
	fetchParam.value.planType = currentType.value
	addBtn.value[0].name = `新建${
		tabList.value.find((_t: any) => _t.subitemValue == currentType.value)
			?.subitemName
	}`
	/*	if( currentType.value == '2' )
		tableProp.value	= tablePropProtocol;
	else
		tableProp.value	= tablePropBase;*/
	// fetchTableData();
	if (currentType.value == "1") {
		fetchParam.value.planYearId = selectYearData.value.id
	} else {
		fetchParam.value.planYearId = ""
	}
	queryArrRef.value.clearQueryData()
	getTypeNumbers().then(() => fetchTableData())
}

// 获取表格数据
const getTableData = (data: { [propName: string]: any }) => {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = data
	fetchParam.value.planType = currentType.value
	if (currentType.value == "1") {
		fetchParam.value.planYearId = selectYearData.value.id
	} else {
		fetchParam.value.planYearId = ""
	}
	getTypeNumbers().then(() => fetchTableData())
}

const onAddBtn = () => {
	if (currentType.value == "1") {
		if (yearData.value.length === 0) {
			ElMessage.error("未查询到年度计划！")
			return false
		}
		// 如果是年度需求计划，需要校验填报截止时间
		if (selectYearData.value.status == "3") {
			ElMessage.error("计划填报已截止！")
			return false
		}
		if (selectYearData.value.status == "1") {
			ElMessage.error("未到计划开始填报日期！")
			return false
		}
		const nowData = new Date()
		const startData = new Date(selectYearData.value.beginDate + " 00:00:00")
		const endData = new Date(selectYearData.value.endDate + " 23:59:59")
		if (nowData < startData) {
			ElMessage.error("未到计划开始填报日期！")
			return false
		}
		if (nowData > endData) {
			ElMessage.error("计划填报已截止！")
			return false
		}
	}
	editId.value = ""
	drawModel.value = "edit"
	showDrawer.value = true
}
const onRowView = (row: any) => {
	editId.value = row.id
	drawModel.value = "view"
	showDrawer.value = true
}
const onRowEdit = (row: any) => {
	editId.value = row.id
	drawModel.value = "edit"
	showDrawer.value = true
}
const onRowDelete = (row: any) => {
	showDelConfirm().then(() => {
		PlanApi.deletePlan(row.id).then(() => {
			ElMessage.success("移除成功")
			getTypeNumbers()
			fetchTableData(fetchParam.value)
		})
	})
}
// 弹窗关闭
const onCloseDrawer = (refresh: boolean, closeDraw?: boolean) => {
	if (refresh) {
		getTypeNumbers()
		fetchTableData(fetchParam.value)
	}
	if (closeDraw) showDrawer.value = false
}

const selectYear = ref<any>("")
const selectYearData = ref<any>({})
const yearData = ref<any[]>([]) // 下拉框年度计划
const beforeYear = ref(false)
const onYearChange = () => {
	const data = yearData.value.find(
		(item: any) => item.year === selectYear.value
	)
	selectYearData.value = data
	changeYearShow()
	getTableData({})
}

const changeYearShow = () => {
	// 选择的计划开始时间 转成 时间戳
	const selectDateTime = new Date(selectYearData.value.beginDate).getTime()
	// 当前时间 转成时间戳
	const curTime = new Date().getTime()

	if (+selectDateTime > curTime) {
		beforeYear.value = false
	} else {
		beforeYear.value = true
	}
}

const getYearList = () => {
	YearPlanApi.getYearPlanList({ pageSize: 100, status: "2,3" }).then((res) => {
		const result: any[] = res.rows
		if (result.length > 0) {
			yearData.value = result

			selectYear.value = new Date().getFullYear()
			const data = result.find((item: any) => item.year == selectYear.value)
			if (data) {
				selectYearData.value = data
			} else {
				// 默认当前年度不存在。则选择数据第一条
				selectYear.value = result[0].year
				selectYearData.value = result[0]
			}

			changeYearShow()
			getTableData({})
		}
	})
}

const getClass = (val: any) => {
	if (val === "" || val === null || val === undefined) {
		return ""
	}
	return "blue_color"
}

onMounted(() => {
	Promise.all([
		getDictByCodeList(["COST_CATEGORY"]),
		initCompSelector(),
		getListList(),
		initSegmentList(),
		initPlanType()
	]).then(() => {
		fetchParam.value.planType = currentType.value
		// getTypeNumbers();
		getYearList()
		// fetchTableData();
	})
})

defineOptions({
	name: "MatApplyManagement"
})
</script>
<template>
	<div class="app-container">
		<div class="whole-frame">
			<ModelFrame class="top-frame">
				<Query
					ref="queryArrRef"
					:queryArrList="(queryArrList as any)"
					@getQueryData="getTableData"
					@single-query-data="singleQueryData"
				/>
			</ModelFrame>
			<ModelFrame class="bottom-frame">
				<Title :title="rightTitle" :button="addBtn" @onBtnClick="onAddBtn">
					<div class="app-tabs-wrapper">
						<el-tabs v-model="currentType" @tab-click="handleTabsClick">
							<el-tab-pane
								v-for="(tab, index) in tabList"
								:key="tab.subitemValue"
								:label="`${tab.subitemName}（${
									tabNum[tab.subitemValue] || 0
								}）`"
								:name="tab.subitemValue"
								:index="tab.subitemValue"
							/>
						</el-tabs>
					</div>
				</Title>
				<div class="need_plan_year" v-if="currentType == '1'">
					<el-select
						class="select_year"
						v-model="selectYear"
						:clearable="false"
						@change="onYearChange"
						placeholder="请选择"
					>
						<el-option
							v-for="(opt, index) in yearData"
							:key="index"
							:label="opt.label"
							:value="opt.year"
						/>
					</el-select>
					<div class="year_show year_item">
						{{ selectYear ? `${selectYear}年` : "---" }}
					</div>
					<div class="other_show year_item" v-if="beforeYear">
						<span>填报截止日期</span>
						<label class="org_color">{{
							selectYearData.endDate || "---"
						}}</label>
					</div>
					<div class="other_show year_item" v-else>
						<span>填报开始日期</span>
						<label class="nomal_color">{{
							selectYearData.beginDate || "---"
						}}</label>
					</div>
					<div class="other_show year_item">
						<span>需求计划（条）</span>
						<label :class="getClass(showDetails?.needPlanNum)">{{
							showDetails?.needPlanNum || "---"
						}}</label>
					</div>
					<div class="other_show year_item">
						<span>物资编码（项）</span>
						<label :class="getClass(showDetails?.matCodeNum)">{{
							showDetails?.matCodeNum || "---"
						}}</label>
					</div>
					<div class="long_show year_item">
						<span>预估总金额（元）</span>
						<label :class="getClass(showDetails?.predictAmount)">{{
							showDetails?.predictAmount
								? XEUtils.commafy(showDetails?.predictAmount, { digits: 2 })
								: "---"
						}}</label>
					</div>
				</div>
				<PitayaTable
					ref="tableRef"
					:columns="(tbInit.tableProp as any)"
					:tableData="tableData"
					:total="pageTotal"
					:needIndex="false"
					@onSelectionChange="onDataSelected"
					@on-current-page-change="onCurrentPageChange"
					:table-loading="tableLoading"
				>
					<template #expenseCategory="{ rowData }">
						<dict-tag
							:options="DictApi.getFeeType()"
							:value="rowData.expenseCategory"
						/>
					</template>
					<template #purchaseAmount="{ rowData }">
						<cost-tag :value="rowData.purchaseAmount" />
					</template>
					<template #lineNoId="{ rowData }">
						<line-tag
							v-for="lineNoId in rowData.lineNoId.split(',')"
							:options="lineList"
							:key="lineNoId"
							:value="lineNoId"
						/>
					</template>
					<template #depotId_view="{ rowData }">
						{{
							segmentList.find((_d) => _d.id == rowData.depotId)?.label || "---"
						}}
					</template>
					<template #bpmStatus="{ rowData }">
						<dict-tag
							:options="DictApi.getBpmStatus()"
							:value="rowData.bpmStatus"
						/>
					</template>

					<!-- 到货状态 -->
					<template #arrivalStatus="{ rowData }">
						<dict-tag
							:options="DictApi.getArrivalStatus()"
							:value="rowData.arrivalStatus"
						/>
					</template>
					<template #operations="{ rowData }">
						<teampalte
							v-if="
								(![appStatus.pendingApproval, appStatus.rejected].includes(
									rowData.bpmStatus
								) &&
									isCheckPermission(powerList.needPlanBtnPreview)) ||
								([appStatus.pendingApproval, appStatus.rejected].includes(
									rowData.bpmStatus
								) &&
									isCheckPermission(powerList.needPlanBtnEdit) &&
									hasPermi(rowData.createdBy)) ||
								([appStatus.pendingApproval, appStatus.rejected].includes(
									rowData.bpmStatus
								) &&
									isCheckPermission(powerList.needPlanBtnDrop) &&
									hasPermi(rowData.createdBy))
							"
						>
							<el-button
								v-btn
								link
								@click="onRowView(rowData)"
								:disabled="checkPermission(powerList.needPlanBtnPreview)"
								v-if="
									![appStatus.pendingApproval, appStatus.rejected].includes(
										rowData.bpmStatus
									) && isCheckPermission(powerList.needPlanBtnPreview)
								"
							>
								<font-awesome-icon :icon="['fas', 'eye']" />
								<span class="table-inner-btn">查看</span>
							</el-button>
							<el-button
								v-btn
								link
								@click="onRowEdit(rowData)"
								:disabled="checkPermission(powerList.needPlanBtnEdit)"
								v-if="
									[appStatus.pendingApproval, appStatus.rejected].includes(
										rowData.bpmStatus
									) &&
									isCheckPermission(powerList.needPlanBtnEdit) &&
									hasPermi(rowData.createdBy)
								"
							>
								<font-awesome-icon :icon="['fas', 'pen-to-square']" />
								<span class="table-inner-btn">编辑</span>
							</el-button>
							<el-button
								v-btn
								link
								@click="onRowDelete(rowData)"
								:disabled="checkPermission(powerList.needPlanBtnDrop)"
								v-if="
									[appStatus.pendingApproval, appStatus.rejected].includes(
										rowData.bpmStatus
									) &&
									isCheckPermission(powerList.needPlanBtnDrop) &&
									hasPermi(rowData.createdBy)
								"
							>
								<font-awesome-icon :icon="['fas', 'trash-can']" />
								<span class="table-inner-btn">移除</span>
							</el-button>
						</teampalte>
						<teampalte v-else>---</teampalte>
					</template>
					<template #footerOperateLeft>
						<ButtonList
							class="btn-list"
							:is-not-radius="true"
							:button="tbBtns"
							:loading="tbBtnLoading"
							@on-btn-click="onBtnClick"
						/>
					</template>
				</PitayaTable>
				<!-- 新增弹窗 -->
				<Drawer
					:size="'auto'"
					v-model:drawer="showDrawer"
					:destroyOnClose="true"
				>
					<NeedPlanDrawer
						:id="editId"
						:model="drawModel"
						:type="currentType"
						:year="selectYearData"
						:table-data="tableData"
						@onDataChange="() => onCloseDrawer(true, false)"
						@onSuccess="(closeDraw) => onCloseDrawer(true, closeDraw)"
						@onClosed="showDrawer = false"
					/>
				</Drawer>
			</ModelFrame>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.need_plan_year {
	display: flex;
	justify-content: space-between;
	align-items: center;
	height: 42px;
	margin-top: 10px;
	padding: 0 10px;
	color: #333;

	.year_item {
		height: 42px;
		padding: 0 20px;
		background-color: #f6f6f6;
		border: 1px solid #ccc;
		font-size: 14px;
		font-weight: bold;
		border-radius: 3px;
		margin-right: 20px;

		span {
			font-size: 14px;
			font-weight: bold;
		}

		label {
			font-size: 14px;
			font-weight: bold;
		}

		.org_color {
			color: #ff8c00;
		}
		.blue_color {
			color: #009dff;
		}

		.nomal_color {
			color: #999 !important;
		}
	}
	.year_item:last-child {
		margin-right: 0 !important;
	}

	.select_year {
		width: 278px;
		height: 42px;
		margin-right: 20px;
		font-size: 14px;

		:deep(.el-input__wrapper) {
			height: 42px;
			.el-input__inner {
				font-size: 14px !important;
			}
		}
	}

	.year_show {
		width: 140px;
		text-align: center;
		line-height: 42px;
		color: #009dff;
	}

	.other_show {
		width: 250px;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.long_show {
		flex: 1;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}
}
</style>
