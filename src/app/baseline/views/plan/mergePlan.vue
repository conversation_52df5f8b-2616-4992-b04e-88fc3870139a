<!-- 计划 - 合并计划 重构V2.0 -->
<script lang="ts" setup>
import { ref, onMounted, reactive } from "vue"
import { ElMessage } from "element-plus"
import {
	checkPermission,
	isCheckPermission
} from "@/app/platform/utils/permission"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import { appStatus, DictApi } from "@/app/baseline/api/dict"
import { useTbInit } from "@/app/baseline/views/components/tableBase"
import {
	BaseLineSysApi,
	getTaskByBusinessIds,
	listCompanyWithFormat
} from "@/app/baseline/api/system"
import { powerList } from "@/app/baseline/views/components/define.d"
import LineTag from "@/app/baseline/views/components/lineTag.vue"
import { PlanMergeApi } from "@/app/baseline/api/plan/planMerge"
import CostTag from "@/app/baseline/views/components/costTag.vue"
import { hasEditByBpm, hasViewByBpm, toFixedTwo } from "../../utils"
import { useMessageBoxInit } from "@/app/baseline/views/components/messageBox"
import { modalSize } from "@/app/baseline/utils/layout-config"
import { omit } from "lodash-es"
import { IModalType } from "../../utils/types/common"
import mergePlanEditor from "./mergePlan/mergePlanEditor.vue"
import mergePlanDetail from "./mergePlan/mergePlanDetail.vue"
import { ICostCategoryStatus } from "../../utils/types/system-cost-category"
import { useUserStore } from "@/app/platform/store/modules/user"
import ApprovedDrawer from "@/app/baseline/views/components/approvedDrawer.vue"

const { showDelConfirm } = useMessageBoxInit()

const { userInfo } = storeToRefs(useUserStore())

const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	currentPage,
	pageTotal,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = useTbInit()

/**
 * table 列配置
 */
tableProp.value = [
	{
		label: "计划年度",
		prop: "year",
		width: 100,
		fixed: "left",
		sortable: true
	},
	{
		label: "合并计划号",
		prop: "code",
		width: 160,
		fixed: "left",
		sortable: true
	},
	{ label: "合并计划名称", prop: "label", minWidth: 200 },
	{ label: "专业", prop: "majorId_view", width: 200 },
	{ label: "线路", prop: "lineNoId", needSlot: true, width: 120 },
	{ label: "费用类别", prop: "expenseCategory_view", width: 150 },
	{
		label: "物资需求总量",
		prop: "num",
		width: 100,
		align: "right",
		needSlot: true
	},
	{
		label: "需求计划数量",
		prop: "planNum",
		width: 100
	},
	{
		label: "预估采购金额",
		prop: "purchaseAmount",
		needSlot: true,
		width: 150,
		align: "right",
		sortable: true
	},
	{ label: "审批状态", prop: "bpmStatus", needSlot: true, width: 90 },
	{ label: "合并部门", prop: "sysOrgId_view", width: 120 },
	{ label: "创建人", prop: "createdBy_view", width: 120 },
	{ label: "创建时间", prop: "createdDate", width: 160, sortable: true },
	{
		label: "操作",
		width: 200,
		prop: "operations",
		fixed: "right",
		needSlot: true
	}
]

fetchFunc.value = PlanMergeApi.getPlanMergeList

/**********************初始化table *********************************/

const rightTitle = {
	name: ["合并计划"],
	icon: ["fas", "square-share-nodes"]
}
const addBtn = [
	{
		name: "新建合并计划",
		roles: powerList.mergePlanBtnCreate,
		icon: ["fas", "square-plus"]
	}
]
//查询
const curSelCompany = ref("")
const singleQueryData = (
	key: string,
	queryData: { [propName: string]: any }
) => {
	if (key === "sysCommunityId") {
		if (Array.isArray(queryData) && queryData.length > 0) {
			curSelCompany.value = queryData[0].id
		} else {
			curSelCompany.value = ""
		}
	}
}

/**
 * 查询条件 配置
 */
const queryArrList = computed(() => [
	{
		name: "合并计划号",
		key: "code",
		placeholder: "请输入合并计划号",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "合并计划名称",
		key: "label",
		placeholder: "请输入合并计划名称",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "线路",
		key: "lineNoId",
		type: "select",
		children: lineList.value,
		placeholder: "请选择线路"
	},
	{
		name: "专业",
		key: "parentMajorId",
		type: "treeSelect",
		treeApi: () => BaseLineSysApi.getProfessionTree(),
		placeholder: "请选择专业"
	},
	{
		name: "费用类别",
		key: "parentExpenseCategory",
		type: "treeSelect",
		treeApi: () =>
			BaseLineSysApi.getCostCategoryTree({
				status: ICostCategoryStatus.Started
			}),
		placeholder: "请选择费用类别"
	},
	{
		name: "公司",
		key: "sysCommunityId",
		type: "select",
		children: companyOptions.value,
		placeholder: "请选择公司"
	},
	{
		name: "合并部门",
		key: "sysOrgId",
		type: "treeSelect",
		treeApi: () =>
			BaseLineSysApi.getDepartmentTreeWithCompanyDisabled(
				userInfo.value.companyId
			),
		placeholder: "请选择合并部门"
	}
])

/**
 * tab 列
 */
const tabList = [
	{
		name: "草稿箱",
		icon: ["fas", "square-plus"]
	},
	{
		name: "审批中",
		icon: ["fas", "square-plus"]
	},
	{
		name: "已审批",
		icon: ["fas", "square-plus"]
	}
]

/**
 * 定义tabs相关变量
 */
const tabNum = ref([0, 0, 0])
const tabStatus = reactive([
	[appStatus.pendingApproval, appStatus.rejected],
	[appStatus.underApproval],
	[appStatus.approved]
])
const activeName = ref(tabList[0].name)
const handleTabsClick = (tab: any) => {
	activeName.value = tab.name
	fetchParam.value.bpmStatus = tabStatus[tab.index].join(",")
	getTableData()
}

const editorId = ref("")
const editorVisible = ref(false)
const detailVisible = ref(false)
const approvedVisible = ref(false)
const editTask = ref<Record<string, any>>()

/**
 * 编辑模式：编辑/新建
 */
const editorMode = ref(IModalType.create)

/**
 * 新建 操作
 */
function onAddBtn() {
	editorMode.value = IModalType.create
	editorId.value = ""
	editorVisible.value = true
}

/**
 * 查看 操作
 * @param row
 */
async function onRowView(row: any) {
	editorId.value = row.id
	editorMode.value = IModalType.view

	if (row.bpmStatus == appStatus.pendingApproval) {
		detailVisible.value = true
	} else {
		const res = await getTaskByBusinessIds({
			businessIds: [row.id],
			camundaKey: "merge_plan"
		})

		if (Array.isArray(res) && res.length > 0) {
			editTask.value = { ...res[res.length - 1] }
			approvedVisible.value = true
		} else {
			detailVisible.value = true
		}
	}
}

/**
 * 编辑 操作
 * @param row
 */
function onRowEdit(row: any) {
	editorId.value = row.id
	editorVisible.value = true
	editorMode.value = IModalType.edit
}

/**
 * 删除 操作

 * @param row
 */
async function onRowDelete(row: any) {
	await showDelConfirm()

	await PlanMergeApi.deletePlanMerge(row.id)

	ElMessage.success("移除成功")
	fetchTableData()
	getBmpStatusStatistics()
}

/**
 * 查询 操作
 * @param data
 */
const getTableData = (data?: any) => {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		...data
	}
	fetchTableData()
	getBmpStatusStatistics()
}

/**
 * tab 状态统计
 */
async function getBmpStatusStatistics() {
	const res: Record<string, any> = await PlanMergeApi.getBmpStatusStatistics(
		omit(
			{
				...fetchParam.value
			},
			"bpmStatus",
			"currentPage",
			"pageSize"
		) as any
	)

	tabNum.value[0] =
		(res[appStatus.rejected] ?? 0) + (res[appStatus.pendingApproval] ?? 0)
	tabNum.value[1] = res[appStatus.underApproval] ?? 0

	tabNum.value[2] = res[appStatus.approved] ?? 0
}

const lineList = ref<[]>([])

/**
 * 公司列表
 */
const companyOptions = ref<any>([])

function getLineList() {
	BaseLineSysApi.getLineOptions().then((res) => {
		lineList.value = res
	})
}
onMounted(async () => {
	fetchParam.value = {
		bpmStatus: tabStatus[0].join(","),
		sord: "desc",
		sidx: "createdDate"
	}

	listCompanyWithFormat().then((r) => (companyOptions.value = r))

	getLineList()
	getTableData()
})

/**
 * 排序
 * @param column
 * @param prop 字段名称
 * @param order ascending 升序 descending 降序
 */
function handleSortChange(column: any, prop: any, order: any) {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		sord:
			order === "descending" ? "desc" : order === "ascending" ? "asc" : "desc",
		sidx: order
			? prop === "purchaseAmount"
				? "predictAmount"
				: prop
			: "createdDate" // 排序字段
	}

	fetchTableData()
}

defineOptions({
	name: "MergePlanManage"
})
</script>
<template>
	<div class="app-container">
		<div class="whole-frame">
			<ModelFrame class="top-frame">
				<Query
					:queryArrList="queryArrList"
					class="ml10"
					@getQueryData="getTableData"
					@single-query-data="singleQueryData"
				/>
			</ModelFrame>
			<ModelFrame class="bottom-frame">
				<Title :title="rightTitle" :button="addBtn" @onBtnClick="onAddBtn">
					<div class="app-tabs-wrapper">
						<el-tabs v-model="activeName" @tab-click="handleTabsClick">
							<el-tab-pane
								v-for="(tab, index) in tabList"
								:key="index"
								:label="`${tab.name}（${tabNum[index]}）`"
								:name="tab.name"
								:index="tab.name"
							/>
						</el-tabs>
					</div>
				</Title>
				<PitayaTable
					ref="tableRef"
					:need-index="true"
					:columns="tableProp"
					:tableData="tableData"
					:total="pageTotal"
					:needSelection="false"
					:single-select="false"
					:need-pagination="true"
					@onSelectionChange="selectedTableList = $event"
					@on-current-page-change="onCurrentPageChange"
					:table-loading="tableLoading"
					@on-table-sort-change="handleSortChange"
				>
					<template #num="{ rowData }">
						{{ toFixedTwo(rowData.num, 0) }}
					</template>

					<!-- 预估采购金额 -->
					<template #purchaseAmount="{ rowData }">
						<cost-tag :value="rowData.purchaseAmount" />
					</template>

					<template #lineNoId="{ rowData }">
						<line-tag :options="lineList" :value="rowData.lineNoId" />
					</template>
					<template #bpmStatus="{ rowData }">
						<dict-tag
							:options="DictApi.getBpmStatus()"
							:value="rowData.bpmStatus"
						/>
					</template>
					<template #operations="{ rowData }">
						<slot
							v-if="
								(isCheckPermission(powerList.mergePlanBtnPreview) &&
									hasViewByBpm(rowData.bpmStatus)) ||
								(isCheckPermission(powerList.mergePlanBtnEdit) &&
									hasEditByBpm(rowData.bpmStatus, rowData.createdBy)) ||
								(isCheckPermission(powerList.mergePlanBtnDrop) &&
									hasEditByBpm(rowData.bpmStatus, rowData.createdBy))
							"
						>
							<el-button
								v-btn
								link
								@click="onRowEdit(rowData)"
								:disabled="checkPermission(powerList.mergePlanBtnEdit)"
								v-if="
									isCheckPermission(powerList.mergePlanBtnEdit) &&
									hasEditByBpm(rowData.bpmStatus, rowData.createdBy)
								"
							>
								<font-awesome-icon :icon="['fas', 'pen-to-square']" />
								<span class="table-inner-btn">编辑</span>
							</el-button>

							<el-button
								v-btn
								link
								@click="onRowView(rowData)"
								:disabled="checkPermission(powerList.mergePlanBtnPreview)"
								v-if="isCheckPermission(powerList.mergePlanBtnPreview)"
							>
								<font-awesome-icon
									:icon="['fas', 'eye']"
									style="color: var(--pitaya-btn-background)"
								/>
								<span class="table-inner-btn">查看</span>
							</el-button>

							<el-button
								v-btn
								link
								@click="onRowDelete(rowData)"
								:disabled="checkPermission(powerList.mergePlanBtnDrop)"
								v-if="
									isCheckPermission(powerList.mergePlanBtnDrop) &&
									hasEditByBpm(rowData.bpmStatus, rowData.createdBy)
								"
							>
								<font-awesome-icon :icon="['fas', 'trash-can']" />
								<span class="table-inner-btn">移除</span>
							</el-button>
						</slot>
						<slot v-else>---</slot>
					</template>
				</PitayaTable>
			</ModelFrame>
		</div>

		<!-- 新建/编辑 弹窗 -->
		<Drawer
			:size="modalSize.xl"
			v-model:drawer="editorVisible"
			:destroyOnClose="true"
		>
			<merge-plan-editor
				:id="editorId"
				:mode="editorMode"
				@close="editorVisible = false"
				@save="
					() => {
						getBmpStatusStatistics()
						fetchTableData()
					}
				"
			/>
		</Drawer>

		<!-- 查看弹窗 -->
		<Drawer
			:size="modalSize.xl"
			v-model:drawer="detailVisible"
			:destroyOnClose="true"
		>
			<merge-plan-detail
				:id="editorId"
				:mode="editorMode"
				@close="detailVisible = false"
			/>
		</Drawer>

		<!-- 审批、查看弹窗 -->
		<Drawer
			:size="modalSize.xxl"
			v-model:drawer="approvedVisible"
			:destroyOnClose="true"
		>
			<approved-drawer
				:mod="editorMode"
				:task-value="editTask"
				@on-save-or-close="approvedVisible = false"
			/>
		</Drawer>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
