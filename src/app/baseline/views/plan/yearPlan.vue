<script lang="ts" setup>
import { ElMessage } from "element-plus"
import {
	checkPermission,
	isCheckPermission
} from "@/app/platform/utils/permission"
import { DictApi, yearPlanListStatus } from "@/app/baseline/api/dict"
import { useTbInit } from "@/app/baseline/views/components/tableBase"
import { powerList } from "@/app/baseline/views/components/define.d"
import { ref, onMounted, reactive } from "vue"
import { YearPlanApi } from "@/app/baseline/api/plan/yearPlan"
import { useMessageBoxInit } from "@/app/baseline/views/components/messageBox"
import { useDictInit } from "@/app/baseline/views/components/dictBase"
import { first } from "lodash-es"
import { hasPermi } from "../../utils"
import { IEndWindowPhase } from "@/app/baseline/api/defines"

const { showDelConfirm, showWarnConfirm } = useMessageBoxInit()
const { dictOptions } = useDictInit()

import DictTag from "@/app/baseline/views/components/dictTag.vue"
import YearPlanDrawer from "./components/yearPlanDrawer.vue"

/*-------------------初始化表格-start-------------------*/
const tbBtnLoading = ref(false)
const {
	tableRef,
	tableProp,
	tableData,
	tableLoading,
	currentPage,
	pageTotal,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = useTbInit()

tableProp.value = [
	{ label: "年度计划名称", prop: "label" },
	{ label: "计划年度", prop: "year", width: 120 },
	{ label: "计划填报开始日期", prop: "beginDate" },
	{ label: "计划填报截止日期", prop: "endDate" },
	{ label: "年度计划类型", prop: "endWindowPhase", needSlot: true },
	{ label: "状态", prop: "bpmStatus", needSlot: true, width: 120 },
	{ label: "创建人", prop: "createdBy_view", width: 120 },
	{ label: "创建时间", prop: "createdDate", sortable: true },
	{
		label: "操作",
		width: 200,
		prop: "operations",
		fixed: "right",
		needSlot: true
	}
]
fetchFunc.value = YearPlanApi.getYearPlanList

const tableFooterBtns = computed(() => {
	return [
		{
			name: "启用",
			roles: powerList.yearPlanStart,
			icon: ["fas", "power-off"],
			disabled:
				selectedTableList.value.length < 1 ||
				first(selectedTableList.value)?.status !=
					yearPlanListStatus.drafted.toString()
		},
		{
			name: "停用",
			roles: powerList.yearPlanStop,
			icon: ["fas", "circle-stop"],
			disabled:
				selectedTableList.value.length < 1 ||
				first(selectedTableList.value)?.status !=
					yearPlanListStatus.started.toString()
		}
	]
})

/**
 * 启用/停用 操作
 * @param status
 */
async function handleChangeStatus(btnName?: string) {
	await showWarnConfirm(`请确认是否${btnName}？`)

	if (btnName === "启用") {
		tbBtnLoading.value = true

		try {
			await YearPlanApi.startPlan(first(selectedTableList.value)?.id)

			fetchTableData()
			ElMessage.success("操作成功")
		} finally {
			tbBtnLoading.value = false
		}
	}

	if (btnName === "停用") {
		tbBtnLoading.value = true

		try {
			await YearPlanApi.endPlan(first(selectedTableList.value)?.id)

			fetchTableData()
			ElMessage.success("操作成功")
		} finally {
			tbBtnLoading.value = false
		}
	}
}

/*-------------------初始化表格-end-------------------*/

const rightTitle = {
	name: ["年度计划"],
	icon: ["fas", "square-share-nodes"]
}
const addBtn = ref([
	{
		name: "新增年度计划",
		roles: powerList.yearPlanAdd,
		icon: ["fas", "square-plus"]
	}
])
const queryArrList = reactive([
	{
		name: "计划年度",
		key: "year",
		type: "select",
		children: DictApi.getFutureYears(0, 2),
		placeholder: "请选择年度"
	},
	{
		name: "状态",
		key: "status",
		type: "select",
		placeholder: "请选择状态",
		children: []
	}
])

const editId = ref<any>("")
const showDrawer = ref<boolean>(false)
const drawModel = ref("view")

// 获取表格数据
const getTableData = (data: { [propName: string]: any }) => {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = { ...fetchParam.value, ...data }
	fetchTableData()
}
const editModel = ref(null)
const onAddBtn = () => {
	editModel.value = null
	editId.value = ""
	drawModel.value = "edit"
	showDrawer.value = true
}
const onRowView = (row: any) => {
	editModel.value = row
	editId.value = row.id
	drawModel.value = "view"
	showDrawer.value = true
}
const onRowEdit = (row: any) => {
	editModel.value = row
	editId.value = row.id
	drawModel.value = "edit"
	showDrawer.value = true
}
const onRowDelete = (row: any) => {
	showDelConfirm().then(() => {
		YearPlanApi.deletePlan(row.id).then(() => {
			ElMessage.success("移除成功")
			fetchTableData(fetchParam.value)
		})
	})
}
// 弹窗关闭
const onCloseDrawer = () => {
	fetchTableData(fetchParam.value)
	showDrawer.value = false
}
const dictState = ref([])
const getDict = () => {
	dictOptions.value = { YEAR_PLAN_STATUS: "YEAR_PLAN_STATUS" }
	DictApi.getDictByCodeList(dictOptions).then((res) => {
		dictState.value = res["YEAR_PLAN_STATUS"]
		queryArrList[1].children = res["YEAR_PLAN_STATUS"]
		dictState.value.forEach((item: any) => {
			if (item.subitemValue == "1") {
				item.raw = { class: "info" }
			} else if (item.subitemValue == "2") {
				item.raw = { class: "warning" }
			} else {
				item.raw = { class: "success" }
			}
		})
	})
}
onMounted(() => {
	fetchParam.value.sord = "desc"
	fetchParam.value.sidx = "year"

	getDict()
	fetchTableData()
})

/**
 * 排序
 * @param column
 * @param prop 字段名称
 * @param order ascending 升序 descending 降序
 */
function handleSortChange(column: any, prop: any, order: any) {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		sord:
			order === "descending" ? "desc" : order === "ascending" ? "asc" : "desc",
		sidx: order ? prop : "year" // 排序字段
	}

	fetchTableData()
}

defineOptions({
	name: "YearPlan"
})
</script>
<template>
	<div class="app-container">
		<div class="whole-frame">
			<ModelFrame class="top-frame">
				<Query
					:queryArrList="queryArrList"
					class="ml10"
					@getQueryData="getTableData"
				/>
			</ModelFrame>
			<ModelFrame class="bottom-frame">
				<Title :title="rightTitle" :button="addBtn" @onBtnClick="onAddBtn" />
				<PitayaTable
					ref="tableRef"
					:columns="tableProp"
					:tableData="tableData"
					:total="pageTotal"
					:singleSelect="true"
					:needSelection="true"
					:need-index="true"
					@onSelectionChange="selectedTableList = $event"
					@on-current-page-change="onCurrentPageChange"
					:table-loading="tableLoading"
					@on-table-sort-change="handleSortChange"
				>
					<template #endWindowPhase="{ rowData }">
						{{
							rowData.endWindowPhase === IEndWindowPhase.NeedPlan
								? "需求计划"
								: rowData.endWindowPhase === IEndWindowPhase.PurchasePlan
								? "采购计划"
								: "---"
						}}
					</template>
					<template #bpmStatus="{ rowData }">
						<dict-tag :options="dictState" :value="rowData.status" />
					</template>
					<template #operations="{ rowData }">
						<teampalte
							v-if="
								(rowData.status === '1' &&
									isCheckPermission(powerList.yearPlanEdit) &&
									hasPermi(rowData.createdBy)) ||
								(rowData.status === '1' &&
									isCheckPermission(powerList.yearPlanDrop) &&
									hasPermi(rowData.createdBy)) ||
								isCheckPermission(powerList.yearPlanPreview)
							"
						>
							<el-button
								v-btn
								link
								@click.stop="onRowEdit(rowData)"
								:disabled="checkPermission(powerList.yearPlanEdit)"
								v-if="
									rowData.status === '1' &&
									isCheckPermission(powerList.yearPlanEdit) &&
									hasPermi(rowData.createdBy)
								"
							>
								<font-awesome-icon :icon="['fas', 'pen-to-square']" />
								<span class="table-inner-btn">编辑</span>
							</el-button>

							<el-button
								v-btn
								link
								@click.stop="onRowView(rowData)"
								:disabled="checkPermission(powerList.yearPlanPreview)"
								v-if="isCheckPermission(powerList.yearPlanPreview)"
							>
								<font-awesome-icon :icon="['fas', 'eye']" />
								<span class="table-inner-btn">查看</span>
							</el-button>

							<el-button
								v-btn
								link
								@click.stop="onRowDelete(rowData)"
								:disabled="checkPermission(powerList.yearPlanDrop)"
								v-if="
									rowData.status === '1' &&
									isCheckPermission(powerList.yearPlanDrop) &&
									hasPermi(rowData.createdBy)
								"
							>
								<font-awesome-icon :icon="['fas', 'trash-can']" />
								<span class="table-inner-btn">移除</span>
							</el-button>
						</teampalte>
						<teampalte v-else>---</teampalte>
					</template>
					<template #footerOperateLeft>
						<ButtonList
							class="btn-list"
							:is-not-radius="true"
							:button="tableFooterBtns"
							:loading="tbBtnLoading"
							@on-btn-click="handleChangeStatus"
						/>
					</template>
				</PitayaTable>
				<!-- 新增弹窗 -->
				<Drawer :size="310" v-model:drawer="showDrawer" :destroyOnClose="true">
					<YearPlanDrawer
						:id="editId"
						:model="drawModel"
						:edit-model="editModel"
						@onSuccess="onCloseDrawer"
						@onClosed="showDrawer = false"
					/>
				</Drawer>
			</ModelFrame>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
