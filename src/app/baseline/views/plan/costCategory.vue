<template>
	<div class="app-container">
		<!-- 左侧 tree 筛选 -->
		<model-frame class="left-frame">
			<div class="drawer-container">
				<div class="drawer-column" style="width: 100%">
					<div class="rows" style="padding-bottom: 20px">
						<Title :title="containerLeftTitle" />

						<pitaya-tree
							ref="treeRef"
							v-model:treeBizId="treeBizId"
							v-loading="treeLoading"
							:tree-data="treeData"
							:tree-props="{
								label: 'fullLabel',
								children: 'children'
							}"
							:needCheckBox="false"
							check-strictly
							:expand-on-click-node="false"
							need-single-select
							:defaultExpandedKeys="defaultExpandedKeys"
							node-key="id"
							@onTreeClick="costCategoryTreeClick"
						/>
					</div>
				</div>
			</div>
		</model-frame>

		<div class="right-frame">
			<model-frame class="top-frame">
				<Query
					:query-arr-list="queryConf"
					class="ml10"
					@get-query-data="handleQuery"
				/>
			</model-frame>
			<model-frame class="bottom-frame">
				<Title
					:title="containerRightTitle"
					:button="addBtn"
					@onBtnClick="handleAdd"
				/>

				<PitayaTable
					ref="tableRef"
					:columns="tableProp"
					:table-data="tableData"
					:needSelection="true"
					:single-select="false"
					:need-index="true"
					:need-pagination="true"
					:total="pageTotal"
					@onSelectionChange="selectedTableList = $event"
					@on-current-page-change="onCurrentPageChange"
					:table-loading="tableLoading"
					@on-table-sort-change="handleSortChange"
				>
					<template #status="{ rowData }">
						<dict-tag
							:options="getCostCategoryStatus()"
							:value="rowData.status"
						/>
					</template>

					<template #operations="{ rowData }">
						<slot
							v-if="
								(rowData.status == ICostCategoryStatus.Drafted &&
									isCheckPermission(powerList.systemCostCategoryBtnEdit) &&
									hasPermi(rowData.createdBy)) ||
								isCheckPermission(powerList.systemCostCategoryBtnPreview) ||
								(rowData.status == ICostCategoryStatus.Drafted &&
									isCheckPermission(powerList.systemCostCategoryBtnDrop) &&
									hasPermi(rowData.createdBy))
							"
						>
							<el-button
								v-btn
								link
								@click.stop="handleRowEdit(rowData)"
								:disabled="checkPermission(powerList.systemCostCategoryBtnEdit)"
								v-if="
									rowData.status == ICostCategoryStatus.Drafted &&
									isCheckPermission(powerList.systemCostCategoryBtnEdit) &&
									hasPermi(rowData.createdBy)
								"
							>
								<font-awesome-icon :icon="['fas', 'pen-to-square']" />
								<span class="table-inner-btn">编辑</span>
							</el-button>

							<el-button
								v-btn
								link
								@click.stop="handleRowView(rowData)"
								:disabled="
									checkPermission(powerList.systemCostCategoryBtnPreview)
								"
								v-if="isCheckPermission(powerList.systemCostCategoryBtnPreview)"
							>
								<font-awesome-icon :icon="['fas', 'eye']" />
								<span class="table-inner-btn">查看</span>
							</el-button>

							<el-button
								v-btn
								link
								@click.stop="handleRowDel(rowData)"
								:disabled="checkPermission(powerList.systemCostCategoryBtnDrop)"
								v-if="
									rowData.status == ICostCategoryStatus.Drafted &&
									isCheckPermission(powerList.systemCostCategoryBtnDrop) &&
									hasPermi(rowData.createdBy)
								"
							>
								<font-awesome-icon :icon="['fas', 'trash-can']" />
								<span class="table-inner-btn">移除</span>
							</el-button>
						</slot>
						<slot v-else>---</slot>
					</template>
					<template #footerOperateLeft>
						<ButtonList
							class="btn-list"
							:is-not-radius="true"
							:button="tableFooterBtns"
							:loading="tbBtnLoading"
							@on-btn-click="handleChangeStatus"
						/>
					</template>
				</PitayaTable>
			</model-frame>
		</div>

		<!-- 新建/编辑 -->
		<Drawer
			:size="modalSize.sm"
			v-model:drawer="editorVisible"
			:destroyOnClose="true"
		>
			<cost-category-editor
				:id="editorRow.id"
				:mode="editorMode"
				:current-node="currentTreeNode"
				@update="
					() => {
						getTreeData()
						fetchTableData()
					}
				"
				@close="editorVisible = false"
			/>
		</Drawer>

		<!-- 查看 -->
		<Drawer
			:size="modalSize.sm"
			v-model:drawer="detailVisible"
			:destroyOnClose="true"
		>
			<cost-category-detail
				:id="editorRow.id"
				:mode="editorMode"
				:current-node="currentTreeNode"
				@close="detailVisible = false"
			/>
		</Drawer>
	</div>
</template>
<script setup lang="ts">
import PitayaTree from "@/compontents/PitayaTree.vue"
import { map } from "lodash-es"
import {
	checkPermission,
	isCheckPermission
} from "@/app/platform/utils/permission"

import {
	batchEnableCostCategory,
	batchFreezeCostCategory,
	batchThawCostCategory,
	delSystemCostCategory,
	getSystemCostCategoryPaged,
	getSystemCostCategoryTree
} from "../../api/system-cost-category"
import { useTbInit } from "../components/tableBase"
import { ICostCategoryStatus } from "../../utils/types/system-cost-category"
import { IModalType } from "../../utils/types/common"
import { useMessageBoxInit } from "../components/messageBox"
import costCategoryEditor from "./costCategory/costCategoryEditor.vue"
import costCategoryDetail from "./costCategory/costCategoryDetail.vue"
import { modalSize } from "@/app/baseline/utils/layout-config"
import { getCostCategoryStatus } from "../../api/dict"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import XEUtils from "xe-utils"
import { hasPermi } from "../../utils"
import { powerList } from "../components/define.d"
const { showDelConfirm, showWarnConfirm } = useMessageBoxInit()

const treeRef = ref<InstanceType<typeof PitayaTree>>()

const treeLoading = ref(false)

const containerLeftTitle = {
	name: ["费用类别"],
	icon: ["fas", "square-share-nodes"]
}

const containerRightTitle = ref<any>({
	name: ["费用类别"],
	icon: ["fas", "square-share-nodes"]
})

const addBtn = [
	{
		name: "新建费用类别",
		roles: powerList.systemCostCategoryBtnCreate,
		icon: ["fas", "square-plus"]
	}
]

const treeData = ref<Record<string, any>[]>([])

const queryConf = computed<querySetting[]>(() => [
	{
		name: "费用类别编码",
		key: "code",
		type: "input",
		placeholder: "请输入费用类别编码"
	},
	{
		name: "费用类别名称",
		key: "label",
		type: "input",
		placeholder: "请输入费用类别名称"
	},
	{
		name: "费用类别状态",
		key: "status",
		type: "select",
		children: getCostCategoryStatus(),
		placeholder: "请选择费用类别状态"
	}
])

// tree当前选中的节点
const currentTreeNode = ref<any>()

/**
 * 树形筛选 check handler
 */
function costCategoryTreeClick(data: any, treeData: any) {
	currentPage.value = 1
	tableRef.value.resetCurrentPage()
	// 更新面包屑
	containerRightTitle.value.name = JSON.parse(
		JSON.stringify(containerLeftTitle)
	).name.concat(getTreeTitle(treeData, "label"))
	currentTreeNode.value = data

	fetchParam.value.fid = data.isCompany ? 0 : data.id
	fetchParam.value.sysCommunityId = data.sysCommunityId
	fetchTableData()
}

function handleQuery(data?: any) {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		...data
	}

	fetchTableData()
}

const treeBizId = ref()
const defaultExpandedKeys = ref<any[]>([])

async function getTreeData() {
	treeLoading.value = true

	try {
		const res = await getSystemCostCategoryTree()

		XEUtils.eachTree(res, (item: any) => {
			defaultExpandedKeys.value.push(item.id)

			item.fullLabel = item.isCompany
				? item.label
				: `${item.code} ${item.label}`
		})

		treeData.value = res

		if (!currentTreeNode.value?.id) {
			currentTreeNode.value = res[0]
		}

		defaultExpandedKeys.value = treeBizId.value
			? [treeBizId.value]
			: [res[0].id]
		treeBizId.value = treeBizId.value || res[0].id

		// 更新面包屑
		const titleObj = JSON.parse(JSON.stringify(containerLeftTitle))
		titleObj.name.push(res[0].label)
		containerRightTitle.value = titleObj

		fetchParam.value.fid = currentTreeNode.value.isCompany ? 0 : treeBizId.value
		fetchParam.value.sysCommunityId = currentTreeNode.value.sysCommunityId
	} finally {
		treeLoading.value = false
	}
}

onMounted(async () => {
	fetchParam.value.sord = "desc"
	fetchParam.value.sidx = "createdDate"
	await getTreeData()
	handleQuery()
})

const tbBtnLoading = ref(false)
const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	currentPage,
	pageTotal,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = useTbInit()

tableProp.value = [
	{ label: "费用类别编码", prop: "code", width: 120 },
	{ label: "费用类别名称", prop: "label", minWidth: 120 },
	{ label: "备注", prop: "remark" },
	{
		label: "费用类别状态",
		prop: "status",
		needSlot: true,
		width: 120
	},
	{ label: "创建时间", prop: "createdDate", width: 160, sortable: true },
	{
		label: "操作",
		width: 200,
		prop: "operations",
		fixed: "right",
		needSlot: true
	}
]

/**
 * table 底部 按钮 配置
 */
const tableFooterBtns = computed(() => {
	const CanStart = selectedTableList.value.every(
		(item: Record<string, any>) => item.status === ICostCategoryStatus.Drafted
	)

	const canFreeze = selectedTableList.value.every(
		(item: any) => item.status === ICostCategoryStatus.Started
	)

	const canThaw = selectedTableList.value.every(
		(item: any) => item.status === ICostCategoryStatus.Freeze
	)

	return [
		{
			name: "启用",
			roles: powerList.systemCostCategoryBtnStart,
			icon: ["fas", "power-off"],
			disabled: selectedTableList.value.length < 1 || !CanStart
		},
		{
			name: "冻结",
			roles: powerList.systemCostCategoryBtnFreeze,
			icon: ["fas", "lock"],
			disabled: selectedTableList.value.length < 1 || !canFreeze
		},
		{
			name: "解冻",
			roles: powerList.systemCostCategoryBtnThawing,
			icon: ["fas", "unlock-alt"],
			disabled: selectedTableList.value.length < 1 || !canThaw
		}
	]
})

/**
 * 启用/停用 操作
 * @param status
 */
async function handleChangeStatus(btnName?: string) {
	await showWarnConfirm(`请确认是否${btnName}？`)

	const idList = map(selectedTableList.value, ({ id }) => id)

	tbBtnLoading.value = true

	try {
		if (btnName === "启用") {
			await batchEnableCostCategory(idList)
		}

		if (btnName === "冻结") {
			await batchFreezeCostCategory(idList)
		}

		if (btnName === "解冻") {
			await batchThawCostCategory(idList)
		}
		await getTreeData()
		fetchTableData()
		ElMessage.success("操作成功")
	} finally {
		tbBtnLoading.value = false
	}
}

fetchFunc.value = getSystemCostCategoryPaged

const editorVisible = ref(false)
const detailVisible = ref(false)
const editorMode = ref(IModalType.create)
const editorRow = ref()

/**
 * 添加
 */
function handleAdd() {
	if (!currentTreeNode.value) {
		return ElMessage.warning("请选择费用类别")
	}

	editorRow.value = {}
	editorVisible.value = true
	editorMode.value = IModalType.create
}

/**
 * 查看
 * @param rowData
 */
function handleRowView(rowData: any) {
	editorRow.value = { ...rowData }
	detailVisible.value = true
	editorMode.value = IModalType.view
}

/**
 * 编辑
 * @param rowData
 */
function handleRowEdit(rowData: any) {
	editorRow.value = { ...rowData }
	editorVisible.value = true
	editorMode.value = IModalType.edit
}

async function handleRowDel(rowData: any) {
	await showDelConfirm()
	await delSystemCostCategory(rowData.id)

	ElMessage.success("操作成功")
	await getTreeData()
	fetchTableData()
}

/**
 * 按时间排序
 * @param column
 * @param prop 字段名称
 * @param order ascending 升序 descending 降序
 */
function handleSortChange(column: any, prop: any, order: any) {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		sord:
			order === "descending" ? "desc" : order === "ascending" ? "asc" : "desc",
		sidx: order ? prop : "createdDate" // 排序字段
	}

	fetchTableData()
}
</script>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
