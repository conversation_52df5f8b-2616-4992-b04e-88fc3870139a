<template>
	<div class="app-container">
		<div class="whole-frame">
			<model-frame class="top-frame">
				<Query
					:query-arr-list="queryConf"
					class="ml10"
					@get-query-data="handleQuery"
				/>
			</model-frame>
			<model-frame class="bottom-frame">
				<Title :title="titleConf" :button="addBtn" @onBtnClick="handleAdd" />

				<PitayaTable
					ref="tableRef"
					:columns="tableProp"
					:table-data="tableData"
					:needSelection="true"
					:single-select="false"
					:need-index="true"
					:need-pagination="true"
					:total="pageTotal"
					@onSelectionChange="selectedTableList = $event"
					@on-current-page-change="onCurrentPageChange"
					:table-loading="tableLoading"
					@on-table-sort-change="handleSortChange"
				>
					<template #status="{ rowData }">
						<dict-tag :options="getDepotStatus()" :value="rowData.status" />
					</template>

					<template #operations="{ rowData }">
						<slot
							v-if="
								(rowData.status == IDepotStatus.Drafted &&
									isCheckPermission(powerList.systemDepotBtnEdit) &&
									hasPermi(rowData.createdBy)) ||
								isCheckPermission(powerList.systemDepotBtnPreview) ||
								(rowData.status == IDepotStatus.Drafted &&
									isCheckPermission(powerList.systemDepotBtnDrop) &&
									hasPermi(rowData.createdBy))
							"
						>
							<el-button
								v-btn
								link
								@click.stop="handleRowEdit(rowData)"
								:disabled="checkPermission(powerList.systemDepotBtnEdit)"
								v-if="
									rowData.status == IDepotStatus.Drafted &&
									isCheckPermission(powerList.systemDepotBtnEdit) &&
									hasPermi(rowData.createdBy)
								"
							>
								<font-awesome-icon :icon="['fas', 'pen-to-square']" />
								<span class="table-inner-btn">编辑</span>
							</el-button>

							<el-button
								v-btn
								link
								@click.stop="handleRowView(rowData)"
								:disabled="checkPermission(powerList.systemDepotBtnPreview)"
								v-if="isCheckPermission(powerList.systemDepotBtnPreview)"
							>
								<font-awesome-icon :icon="['fas', 'eye']" />
								<span class="table-inner-btn">查看</span>
							</el-button>

							<el-button
								v-btn
								link
								@click.stop="handleRowDel(rowData)"
								:disabled="checkPermission(powerList.systemDepotBtnDrop)"
								v-if="
									rowData.status == IDepotStatus.Drafted &&
									isCheckPermission(powerList.systemDepotBtnDrop) &&
									hasPermi(rowData.createdBy)
								"
							>
								<font-awesome-icon :icon="['fas', 'trash-can']" />
								<span class="table-inner-btn">移除</span>
							</el-button>
						</slot>
						<slot v-else>---</slot>
					</template>
					<template #footerOperateLeft>
						<ButtonList
							class="btn-list"
							:is-not-radius="true"
							:button="tableFooterBtns"
							:loading="tbBtnLoading"
							@on-btn-click="handleChangeStatus"
						/>
					</template>
				</PitayaTable>
			</model-frame>
		</div>

		<!-- 新建/编辑 -->
		<Drawer
			:size="modalSize.sm"
			v-model:drawer="editorVisible"
			:destroyOnClose="true"
		>
			<depot-editor
				:id="editorRow.id"
				:mode="editorMode"
				@update="
					() => {
						fetchTableData()
					}
				"
				@close="editorVisible = false"
			/>
		</Drawer>

		<!-- 查看 -->
		<Drawer
			:size="modalSize.sm"
			v-model:drawer="detailVisible"
			:destroyOnClose="true"
		>
			<depot-detail
				:id="editorRow.id"
				:mode="editorMode"
				@close="detailVisible = false"
			/>
		</Drawer>
	</div>
</template>
<script setup lang="ts">
import { map } from "lodash-es"
import {
	checkPermission,
	isCheckPermission
} from "@/app/platform/utils/permission"

import {
	batchDisableDepot,
	batchEnableDepot,
	delSystemDepot,
	getSystemDepotPaged
} from "../../api/system-depot"
import { useTbInit } from "../components/tableBase"
import { IModalType } from "../../utils/types/common"
import { useMessageBoxInit } from "../components/messageBox"
import { getDepotStatus } from "../../api/dict"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import { hasPermi } from "../../utils"
import { powerList } from "../components/define.d"
import { IDepotStatus } from "../../utils/types/system-depot"
import depotEditor from "./depot/depotEditor.vue"
import depotDetail from "./depot/depotDetail.vue"
import { modalSize } from "@/app/baseline/utils/layout-config"
import { listMatStorage } from "../../api/store/manage-api"
import { IWarehouseStatus } from "../../utils/types/store-manage"
const { showDelConfirm, showWarnConfirm } = useMessageBoxInit()

const titleConf = ref<any>({
	name: ["段区"],
	icon: ["fas", "square-share-nodes"]
})

const addBtn = [
	{
		name: "新建段区",
		roles: powerList.systemDepotBtnCreate,
		icon: ["fas", "square-plus"]
	}
]

const queryConf = computed<querySetting[]>(() => [
	{
		name: "段区名称",
		key: "label",
		type: "input",
		placeholder: "请输入段区名称"
	}
	/* {
		name: "段区状态",
		key: "status",
		type: "select",
		children: getDepotStatus(),
		placeholder: "请选择段区状态"
	} */
])

function handleQuery(data?: any) {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		...data
	}

	fetchTableData()
}

onMounted(async () => {
	fetchParam.value.sord = "desc"
	fetchParam.value.sidx = "createdDate"
	handleQuery()
})

const tbBtnLoading = ref(false)
const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	currentPage,
	pageTotal,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = useTbInit()

tableProp.value = [
	{ label: "段区名称", prop: "label", minWidth: 120 },
	{ label: "所属公司", prop: "sysCommunityId_view" },
	{ label: "创建人", prop: "createdBy_view" },
	{ label: "创建时间", prop: "createdDate", width: 160, sortable: true },
	{ label: "备注", prop: "remark" },
	{ label: "更新人", prop: "lastModifiedBy_view" },
	{ label: "更新时间", prop: "lastModifiedDate", width: 160, sortable: true },
	{
		label: "状态",
		prop: "status",
		needSlot: true,
		width: 120
	},
	{
		label: "操作",
		width: 200,
		prop: "operations",
		fixed: "right",
		needSlot: true
	}
]

/**
 * table 底部 按钮 配置
 */
const tableFooterBtns = computed(() => {
	const CanStart = selectedTableList.value.every(
		(item: Record<string, any>) => item.status === IDepotStatus.Drafted
	)

	const canDisabled = selectedTableList.value.every(
		(item: any) => item.status === IDepotStatus.Started
	)

	return [
		{
			name: "启用",
			roles: powerList.systemDepotBtnStart,
			icon: ["fas", "power-off"],
			disabled: selectedTableList.value.length < 1 || !CanStart
		},
		{
			name: "停用",
			roles: powerList.systemDepotBtnStop,
			icon: ["fas", "circle-stop"],
			disabled: selectedTableList.value.length < 1 || !canDisabled
		}
	]
})

/**
 * 启用/停用 操作
 * @param status
 */
async function handleChangeStatus(btnName?: string) {
	const idList = map(selectedTableList.value, ({ id }) => id)

	tbBtnLoading.value = true

	try {
		if (btnName === "启用") {
			await showWarnConfirm(`请确认是否${btnName}？`)
			await batchEnableDepot(idList)
		}

		if (btnName === "停用") {
			const data = await listMatStorage({
				depotIdList: idList,
				status: IWarehouseStatus.activated
			})

			if (data && data.length > 0) {
				await showWarnConfirm("段区已有仓库关联，要继续停用吗？")
			} else {
				await showWarnConfirm(`请确认是否${btnName}？`)
			}

			await batchDisableDepot(idList)
		}

		fetchTableData()
		ElMessage.success("操作成功")
	} finally {
		tbBtnLoading.value = false
	}
}

fetchFunc.value = getSystemDepotPaged

const editorVisible = ref(false)
const detailVisible = ref(false)
const editorMode = ref(IModalType.create)
const editorRow = ref()

/**
 * 添加
 */
function handleAdd() {
	editorRow.value = {}
	editorVisible.value = true
	editorMode.value = IModalType.create
}

/**
 * 查看
 * @param rowData
 */
function handleRowView(rowData: any) {
	editorRow.value = { ...rowData }
	detailVisible.value = true
	editorMode.value = IModalType.view
}

/**
 * 编辑
 * @param rowData
 */
function handleRowEdit(rowData: any) {
	editorRow.value = { ...rowData }
	editorVisible.value = true
	editorMode.value = IModalType.edit
}

async function handleRowDel(rowData: any) {
	await showDelConfirm()
	await delSystemDepot(rowData.id)

	ElMessage.success("操作成功")
	fetchTableData()
}

/**
 * 按时间排序
 * @param column
 * @param prop 字段名称
 * @param order ascending 升序 descending 降序
 */
function handleSortChange(column: any, prop: any, order: any) {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		sord:
			order === "descending" ? "desc" : order === "ascending" ? "asc" : "desc",
		sidx: order ? prop : "createdDate" // 排序字段
	}

	fetchTableData()
}
</script>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
