<!-- 需求计划 重构V2.0 -->
<script lang="ts" setup>
import { ElMessage } from "element-plus"
import {
	checkPermission,
	isCheckPermission
} from "@/app/platform/utils/permission"
import { DictApi, purchaseOrderSource } from "@/app/baseline/api/dict"
import { useTbInit } from "@/app/baseline/views/components/tableBase"
import { powerList } from "@/app/baseline/views/components/define.d"
import { appStatus } from "@/app/baseline/api/dict"
import {
	BaseLineSysApi,
	getTaskByBusinessIds,
	listCompanyWithFormat
} from "@/app/baseline/api/system"
import { hasPermi, tableColFilter } from "../../utils"
import { ref, onMounted, reactive } from "vue"
import { PlanApi } from "@/app/baseline/api/plan/plan"
import { useMessageBoxInit } from "@/app/baseline/views/components/messageBox"

const { showDelConfirm } = useMessageBoxInit()

import NeedPlanEditor from "./needPlan/needPlanEditor.vue"
import NeedPlanDetail from "./needPlan/needPlanDetail.vue"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import CostTag from "@/app/baseline/views/components/costTag.vue"
import LineTag from "@/app/baseline/views/components/lineTag.vue"
import { YearPlanApi } from "@/app/baseline/api/plan/yearPlan"
import XEUtils from "xe-utils"
import { filter, omit } from "lodash-es"
import { modalSize } from "@/app/baseline/utils/layout-config"
import { IModalType, LineVo } from "../../utils/types/common"
import { useUserStore } from "@/app/platform/store/modules/user"
import { ICostCategoryStatus } from "../../utils/types/system-cost-category"
import { SystemDepotVo } from "../../utils/types/system-depot"
import ApprovedDrawer from "@/app/baseline/views/components/approvedDrawer.vue"

const { userInfo } = storeToRefs(useUserStore())

/**
 * Title 配置项
 */
const titleConf = {
	name: ["需求计划"],
	icon: ["fas", "square-share-nodes"]
}
const addBtn = ref([
	{
		name: "新建需求计划",
		roles: powerList.needPlanBtnCreate,
		icon: ["fas", "square-plus"]
	}
])

const tbInit = useTbInit()
const {
	tableData,
	tableRef,
	tableLoading,
	currentPage,
	pageTotal,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = tbInit

/**
 * table 列 配置项
 */
tbInit.tableProp = computed(() => {
	const defCols: TableColumnType[] = [
		{
			label: "计划年度",
			prop: "year",
			width: 100,
			fixed: "left",
			sortable: true
		},
		{
			label: "需求计划号",
			prop: "code",
			width: 160,
			fixed: "left",
			sortable: true
		},
		{ label: "需求计划名称", prop: "label", minWidth: 200 },
		{ label: "物资编码数量", prop: "matCodeNum", width: 100 },
		{
			label: "预估采购金额",
			prop: "purchaseAmount",
			needSlot: true,
			width: 160,
			align: "right",
			sortable: true
		},
		{ label: "线路", prop: "lineNoId", needSlot: true, width: 120 },
		{ label: "专业", prop: "majorId_view", width: 120 },
		{ label: "段区", prop: "depotId_view", width: 100 },
		{ label: "费用类别", prop: "expenseCategory_view", width: 100 },
		{ label: "审批状态", prop: "bpmStatus", needSlot: true, width: 90 },
		/* { label: "到货状态", prop: "arrivalStatus", needSlot: true, width: 120 }, */
		{ label: "公司", prop: "sysCommunityId_view", width: 160 },
		{ label: "需求部门", prop: "sysOrgId_view", width: 160 },
		{ label: "需求清单编号", prop: "planNeedCode", width: 150 },
		{ label: "创建人", prop: "createdBy_view", needSlot: false, width: 120 },
		{ label: "创建时间", prop: "createdDate", width: 160, sortable: true },
		{
			label: "操作",
			width: 200,
			prop: "operations",
			fixed: "right",
			needSlot: true
		}
	]

	if (currentType.value == "1") {
		return defCols
	} else {
		return tableColFilter(defCols, ["需求清单编号"])
	}
})

fetchFunc.value = PlanApi.getList

function descOptionFilter(cols: Record<string, any>[], excludeCols: string[]) {
	return cols.filter((c) => !excludeCols.includes(c.name))
}

const queryArrRef = ref<any>()

/**
 * 查询条件配置
 */
const queryArrList = computed<Record<string, any>[]>(() => {
	const all = [
		{
			name: "所属公司",
			key: "sysCommunityId",
			type: "select",
			children: companyOptions.value,
			placeholder: "请选择所属公司"
		},
		{
			name: "年度",
			key: "year",
			type: "select",
			children: DictApi.getFutureYears(-2, 2),
			placeholder: "请选择年度"
		},
		{
			name: "需求计划号",
			key: "code",
			type: "input",
			placeholder: "请输入需求计划号",
			enableFuzzy: true
		},
		{
			name: "线路",
			key: "lineNoId",
			type: "select",
			children: lineList.value,
			placeholder: "请选择线路"
		},
		{
			name: "段区",
			key: "depotId",
			type: "select",
			children: depotList.value,
			placeholder: "请选择段区"
		},
		{
			name: "专业",
			key: "parentMajorId",
			type: "treeSelect",
			treeApi: () =>
				BaseLineSysApi.getProfessionTree(fetchParam.value.companyRange),
			placeholder: "请选择专业"
		},
		{
			name: "费用类别",
			key: "parentExpenseCategory",
			type: "treeSelect",
			treeApi: () =>
				BaseLineSysApi.getCostCategoryTree({
					status: ICostCategoryStatus.Started
				}),
			placeholder: "请选择费用类别"
		},
		{
			name: "需求部门",
			key: "sysOrgId",
			type: "treeSelect",
			treeApi: () =>
				BaseLineSysApi.getDepartmentTreeWithCompanyDisabled(
					userInfo.value.companyId
				),
			placeholder: "请选择需求部门"
		},
		{
			name: "审批状态",
			key: "bpmStatus",
			type: "select",
			placeholder: "请选择审批状态",
			children: DictApi.getQueryBpmStatus()
		}
	]

	return all
	/* if (currentType.value == "1") {
		return descOptionFilter(all, ["年度"])
	} else {
		return all
	} */
})

////tabs
const tabList = ref<{ [propName: string]: any }>([])

//需求计划类型
const tabNum = reactive<Record<string, any>>({})
const currentType = ref("")

const editId = ref<any>("")
const editorVisible = ref<boolean>(false)
const detailVisible = ref(false)
const approvedVisible = ref(false)
const editTask = ref<Record<string, any>>()
const drawModel = ref(IModalType.view)

// 线路列表
const lineList = ref<LineVo[]>([])

/**
 * 公司列表
 */
const companyOptions = ref<any>([])

const depotList = ref<SystemDepotVo[]>([])

const curSelCompany = ref("")
const singleQueryData = (
	key: string,
	queryData: { [propName: string]: any }
) => {
	if (key === "sysCommunityId") {
		if (Array.isArray(queryData) && queryData.length > 0) {
			curSelCompany.value = queryData[0].id
		} else {
			curSelCompany.value = ""
		}
	}
}

/**
 * 获取线路配置
 */
function getLineList() {
	BaseLineSysApi.getLineOptions().then((res) => {
		lineList.value = res
	})
}

/**
 * 获取段区配置
 */
function getDepotList() {
	BaseLineSysApi.getDepotList().then((res) => {
		depotList.value = res
	})
}

/**
 * 需求计划类型
 */
function getPlanType() {
	DictApi.getDictByCode("PLAN_CATEGORY").then((res: any) => {
		tabList.value = filter(res, (v: Record<string, any>) =>
			[
				purchaseOrderSource.annualDemandPlan,
				purchaseOrderSource.emergencyPlan
			].includes(v.value)
		)

		res.map((_t: any) => {
			tabNum[_t.subitemValue.toString()] = 0
		})

		currentType.value = res[0]?.subitemValue
		fetchParam.value.planType = currentType.value
		addBtn.value[0].name = `新建${res[0]?.subitemName}`
	})
}

const showDetails = ref<any>({})

/**
 * 获取各类别计划数量
 */
async function getTypeNumbers() {
	const res = await PlanApi.getPlanCnt(
		omit(
			{
				...fetchParam.value,
				planYearId: selectYearData.value.id
			},
			"planType",
			"currentPage",
			"pageSize"
		) as any
	)

	Object.keys(tabNum).map((_k) => {
		tabNum[_k] = res[_k]?.needPlanNum || 0
	})
	showDetails.value = res["1"]
}

/**
 * tab 切换事件
 * @param tab
 */
function handleTabsClick(tab: any) {
	currentType.value = tab.props.name
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value.sord = "desc"
	fetchParam.value.sidx = "createdDate"
	fetchParam.value.planType = currentType.value
	addBtn.value[0].name = `新建${
		tabList.value.find((_t: any) => _t.subitemValue == currentType.value)
			?.subitemName
	}`

	if (currentType.value == "1") {
		fetchParam.value.planYearId = selectYearData.value.id
	} else {
		fetchParam.value.planYearId = ""
	}

	//queryArrRef.value.clearQueryData()
	getTypeNumbers().then(() => fetchTableData())
}

// 获取表格数据
const getTableData = (data: { [propName: string]: any }) => {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = data

	fetchParam.value.planType = currentType.value
	fetchParam.value.sord = "desc"
	fetchParam.value.sidx = "createdDate"

	if (currentType.value == "1") {
		fetchParam.value.planYearId = selectYearData.value.id
	} else {
		fetchParam.value.planYearId = ""
	}
	getTypeNumbers().then(() => fetchTableData())
}

const onAddBtn = () => {
	if (currentType.value == "1") {
		if (yearData.value.length === 0) {
			ElMessage.warning("未查询到年度计划！")
			return false
		}
		// 如果是年度需求计划，需要校验填报截止时间
		if (selectYearData.value.status == "3") {
			ElMessage.warning("计划填报已截止！")
			return false
		}
		if (selectYearData.value.status == "1") {
			ElMessage.warning("未到计划开始填报日期！")
			return false
		}
		const nowData = new Date()
		const startData = new Date(selectYearData.value.beginDate + " 00:00:00")
		const endData = new Date(selectYearData.value.endDate + " 23:59:59")
		if (nowData < startData) {
			ElMessage.warning("未到计划开始填报日期！")
			return false
		}
		if (nowData > endData) {
			ElMessage.warning("计划填报已截止！")
			return false
		}
	}
	editId.value = ""
	drawModel.value = IModalType.create
	editorVisible.value = true
}
const onRowView = async (row: any) => {
	editId.value = row.id
	drawModel.value = IModalType.view
	if (row.bpmStatus == appStatus.pendingApproval) {
		detailVisible.value = true
	} else {
		const res = await getTaskByBusinessIds({
			businessIds: [row.id],
			camundaKey: currentType.value == "1" ? "need_plan" : "temp_plan"
		})

		if (Array.isArray(res) && res.length > 0) {
			editTask.value = { ...res[res.length - 1] }
			approvedVisible.value = true
		} else {
			detailVisible.value = true
		}
	}
}
const onRowEdit = (row: any) => {
	editId.value = row.id
	drawModel.value = IModalType.edit
	editorVisible.value = true
}
const onRowDelete = (row: any) => {
	showDelConfirm().then(() => {
		PlanApi.deletePlan(row.id).then(() => {
			ElMessage.success("移除成功")
			getTypeNumbers()
			fetchTableData(fetchParam.value)
		})
	})
}

const selectYearId = ref("")
const selectYear = ref<any>("")
const selectYearData = ref<any>({})
const yearData = ref<any[]>([]) // 下拉框年度计划
const beforeYear = ref(false)
const onYearChange = () => {
	const data = yearData.value.find(
		(item: any) => item.id === selectYearId.value
	)
	selectYear.value = data?.year
	selectYearData.value = data
	changeYearShow()
	getTableData({})
}

const changeYearShow = () => {
	// 选择的计划开始时间 转成 时间戳
	const selectDateTime = new Date(selectYearData.value.beginDate).getTime()
	// 当前时间 转成时间戳
	const curTime = new Date().getTime()

	if (+selectDateTime > curTime) {
		beforeYear.value = false
	} else {
		beforeYear.value = true
	}
}

const getYearList = () => {
	YearPlanApi.getYearPlanList({ pageSize: 100, status: "2,3" }).then((res) => {
		const result: any[] = res.rows
		if (result.length > 0) {
			yearData.value = result

			selectYear.value = new Date().getFullYear()

			// 获取当前年度的 进行中 年度计划
			const selectYearIng = result.find(
				(item: any) => item.status == "2" && item.year == selectYear.value
			)

			// 获取当前年度的 已完成 年度计划
			const selectYearEnd = result.find(
				(item: any) => item.status == "3" && item.year == selectYear.value
			)

			if (selectYearIng) {
				selectYearId.value = selectYearIng.id
				selectYear.value = selectYearIng.year
				selectYearData.value = selectYearIng
			} else if (selectYearEnd) {
				selectYearId.value = selectYearEnd.id
				selectYear.value = selectYearEnd.year
				selectYearData.value = selectYearEnd
			} else {
				// 默认当前年度不存在。则选择数据第一条
				selectYearId.value = result[0].id
				selectYear.value = result[0].year
				selectYearData.value = result[0]
			}

			changeYearShow()
			getTableData({})
		}
	})
}

/**
 * 获取 颜色类名
 * @param val
 */
const getClass = (val: any) => {
	if (val === "" || val === null || val === undefined) {
		return ""
	}
	return "blue_color"
}

onMounted(() => {
	listCompanyWithFormat().then((r) => (companyOptions.value = r))
	getLineList()
	getDepotList()
	getPlanType()

	fetchParam.value.planType = currentType.value

	getYearList()
})

/**
 * 排序
 * @param column
 * @param prop 字段名称
 * @param order ascending 升序 descending 降序
 */
function handleSortChange(column: any, prop: any, order: any) {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		sord:
			order === "descending" ? "desc" : order === "ascending" ? "asc" : "desc",
		sidx: order
			? prop === "purchaseAmount"
				? "predictAmount"
				: prop
			: "createdDate" // 排序字段
	}

	fetchTableData()
}

defineOptions({
	name: "PlanNeedPlanIndex"
})
</script>
<template>
	<div class="app-container">
		<div class="whole-frame">
			<ModelFrame class="top-frame">
				<Query
					ref="queryArrRef"
					:queryArrList="(queryArrList as any)"
					class="ml10"
					@getQueryData="getTableData"
					@single-query-data="singleQueryData"
				/>
			</ModelFrame>
			<ModelFrame class="bottom-frame">
				<Title :title="titleConf" :button="addBtn" @onBtnClick="onAddBtn">
					<div class="app-tabs-wrapper">
						<el-tabs v-model="currentType" @tab-click="handleTabsClick">
							<el-tab-pane
								v-for="tab in tabList"
								:key="tab.subitemValue"
								:label="`${tab.subitemName}（${
									tabNum[tab.subitemValue] || 0
								}）`"
								:name="tab.subitemValue"
								:index="tab.subitemValue"
							/>
						</el-tabs>
					</div>
				</Title>
				<div class="need_plan_year" v-if="currentType == '1'">
					<el-select
						class="select_year"
						v-model="selectYearId"
						:clearable="false"
						@change="onYearChange"
						placeholder="请选择"
					>
						<el-option
							v-for="(opt, index) in yearData"
							:key="index"
							:label="opt.label"
							:value="opt.id"
						/>
					</el-select>
					<div class="year_show year_item">
						{{ selectYear ? `${selectYear}年` : "---" }}
					</div>
					<div class="other_show year_item" v-if="beforeYear">
						<span>填报截止日期</span>
						<label class="org_color">
							{{ selectYearData.endDate || "---" }}
						</label>
					</div>
					<div class="other_show year_item" v-else>
						<span>填报开始日期</span>
						<label class="nomal_color">
							{{ selectYearData.beginDate || "---" }}
						</label>
					</div>
					<div class="other_show year_item">
						<span>需求计划（条）</span>
						<label :class="getClass(showDetails?.needPlanNum)">
							{{ showDetails?.needPlanNum || "---" }}
						</label>
					</div>
					<div class="other_show year_item">
						<span>物资编码（项）</span>
						<label :class="getClass(showDetails?.matCodeNum)">
							{{ showDetails?.matCodeNum || "---" }}
						</label>
					</div>
					<div class="long_show year_item">
						<span>预估总金额（元）</span>
						<label :class="getClass(showDetails?.predictAmount)">
							{{
								showDetails?.predictAmount
									? XEUtils.commafy(showDetails?.predictAmount, { digits: 5 })
									: "---"
							}}
						</label>
					</div>
				</div>
				<PitayaTable
					ref="tableRef"
					:columns="(tbInit.tableProp as any)"
					:tableData="tableData"
					:total="pageTotal"
					:need-index="true"
					@onSelectionChange="selectedTableList = $event"
					@on-current-page-change="onCurrentPageChange"
					:table-loading="tableLoading"
					@on-table-sort-change="handleSortChange"
					:customize-height-number="currentType == '1' ? -50 : 0"
				>
					<!-- 线路 -->
					<template #lineNoId="{ rowData }">
						<line-tag
							:options="lineList"
							:value="rowData.lineNoId.split(',')"
						/>
					</template>

					<!-- 预估采购金额 -->
					<template #purchaseAmount="{ rowData }">
						<cost-tag :value="rowData.purchaseAmount" />
					</template>

					<!-- 审批状态 -->
					<template #bpmStatus="{ rowData }">
						<dict-tag
							:options="DictApi.getBpmStatus()"
							:value="rowData.bpmStatus"
						/>
					</template>

					<!-- 到货状态 -->
					<template #arrivalStatus="{ rowData }">
						<dict-tag
							:options="DictApi.getArrivalStatus()"
							:value="rowData.arrivalStatus"
						/>
					</template>
					<template #operations="{ rowData }">
						<teampalte
							v-if="
								isCheckPermission(powerList.needPlanBtnPreview) ||
								([appStatus.pendingApproval, appStatus.rejected].includes(
									rowData.bpmStatus
								) &&
									isCheckPermission(powerList.needPlanBtnEdit) &&
									hasPermi(rowData.createdBy)) ||
								([appStatus.pendingApproval, appStatus.rejected].includes(
									rowData.bpmStatus
								) &&
									isCheckPermission(powerList.needPlanBtnDrop) &&
									hasPermi(rowData.createdBy))
							"
						>
							<el-button
								v-btn
								link
								@click="onRowEdit(rowData)"
								:disabled="checkPermission(powerList.needPlanBtnEdit)"
								v-if="
									[appStatus.pendingApproval, appStatus.rejected].includes(
										rowData.bpmStatus
									) &&
									isCheckPermission(powerList.needPlanBtnEdit) &&
									hasPermi(rowData.createdBy)
								"
							>
								<font-awesome-icon :icon="['fas', 'pen-to-square']" />
								<span class="table-inner-btn">编辑</span>
							</el-button>

							<el-button
								v-btn
								link
								@click="onRowView(rowData)"
								:disabled="checkPermission(powerList.needPlanBtnPreview)"
								v-if="isCheckPermission(powerList.needPlanBtnPreview)"
							>
								<font-awesome-icon :icon="['fas', 'eye']" />
								<span class="table-inner-btn">查看</span>
							</el-button>

							<el-button
								v-btn
								link
								@click="onRowDelete(rowData)"
								:disabled="checkPermission(powerList.needPlanBtnDrop)"
								v-if="
									[appStatus.pendingApproval, appStatus.rejected].includes(
										rowData.bpmStatus
									) &&
									isCheckPermission(powerList.needPlanBtnDrop) &&
									hasPermi(rowData.createdBy)
								"
							>
								<font-awesome-icon :icon="['fas', 'trash-can']" />
								<span class="table-inner-btn">移除</span>
							</el-button>
						</teampalte>
						<teampalte v-else>---</teampalte>
					</template>
				</PitayaTable>

				<!-- 新增/编辑 弹窗 -->
				<Drawer
					:size="modalSize.xl"
					v-model:drawer="editorVisible"
					:destroyOnClose="true"
				>
					<NeedPlanEditor
						:id="editId"
						:model="drawModel"
						:type="currentType"
						:year="selectYearData"
						@close="editorVisible = false"
						@save="
							() => {
								getTypeNumbers()
								fetchTableData()
							}
						"
					/>
				</Drawer>

				<!-- 查看 弹窗 -->
				<Drawer
					:size="modalSize.xl"
					v-model:drawer="detailVisible"
					:destroyOnClose="true"
				>
					<NeedPlanDetail
						:id="editId"
						:model="drawModel"
						:type="currentType"
						:year="selectYearData"
						@close="detailVisible = false"
					/>
				</Drawer>

				<!-- 审批、查看弹窗 -->
				<Drawer
					:size="modalSize.xxl"
					v-model:drawer="approvedVisible"
					:destroyOnClose="true"
				>
					<approved-drawer
						:mod="drawModel"
						:task-value="editTask"
						@on-save-or-close="approvedVisible = false"
					/>
				</Drawer>
			</ModelFrame>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.need_plan_year {
	display: flex;
	justify-content: space-between;
	align-items: center;
	height: 42px;
	margin-top: 10px;
	padding: 0 10px;
	color: #333;

	.year_item {
		height: 42px;
		padding: 0 20px;
		background-color: #f6f6f6;
		border: 1px solid #ccc;
		font-size: 14px;
		font-weight: bold;
		border-radius: 3px;
		margin-right: 20px;

		span {
			font-size: 14px;
			font-weight: bold;
		}

		label {
			font-size: 14px;
			font-weight: bold;
		}

		.org_color {
			color: #ff8c00;
		}
		.blue_color {
			color: #009dff;
		}

		.nomal_color {
			color: #999 !important;
		}
	}
	.year_item:last-child {
		margin-right: 0 !important;
	}

	.select_year {
		width: 278px;
		height: 42px;
		margin-right: 20px;
		font-size: 14px;
		:deep(.el-input__wrapper) {
			height: 42px;
			.el-input__inner {
				font-size: 14px !important;
				white-space: nowrap !important; /* 防止文本换行 */
				overflow: hidden !important; /* 隐藏超出部分 */
				text-overflow: ellipsis !important; /* 使用省略号表示超出部分 */
			}
		}
	}

	.year_show {
		width: 140px;
		text-align: center;
		line-height: 42px;
		color: #009dff;
	}

	.other_show {
		width: 250px;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.long_show {
		flex: 1;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}
}
</style>
