<!-- 计划 - 采购计划 主列表 废弃V1.0 -->
<script lang="ts" setup>
import { ref, onMounted, reactive } from "vue"
import PurchasePlanDrawer from "./components/purchasePlanDrawer.vue"
import PurchasePlanViewDrawer from "./components/purchasePlanViewDrawer.vue"
import { ElMessage } from "element-plus"
import {
	checkPermission,
	isCheckPermission
} from "@/app/platform/utils/permission"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import { appStatus, DictApi } from "@/app/baseline/api/dict"
import { useTbInit } from "@/app/baseline/views/components/tableBase"
import { BaseLineSysApi } from "@/app/baseline/api/system"
import { powerList } from "@/app/baseline/views/components/define.d"
import { PlanPurchaseApi } from "@/app/baseline/api/plan/planPurchase"
import CostTag from "@/app/baseline/views/components/costTag.vue"
import { hasEditByBpm, hasViewByBpm } from "../../utils"
import { useMessageBoxInit } from "../components/messageBox"
import { modalSize } from "@/app/baseline/utils/layout-config"

const { showDelConfirm } = useMessageBoxInit()
const rightTitle = {
	name: ["采购计划"],
	icon: ["fas", "square-share-nodes"]
}
const addBtn = [
	{
		name: "新建采购计划",
		roles: powerList.purchasePlanBtnCreate,
		icon: ["fas", "square-plus"]
	}
]
//查询
const queryArrList = [
	{
		name: "采购计划号",
		key: "code",
		placeholder: "请输入采购计划号",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "采购计划名称",
		key: "label",
		placeholder: "请输入采购计划名称",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "所属公司",
		key: "sysCommunityId",
		type: "treeSelect",
		treeApi: BaseLineSysApi.getCompanyAllList,
		placeholder: "请选择所属公司"
	},
	{
		name: "年度",
		key: "year",
		type: "select",
		children: DictApi.getFutureYears(-2, 2),
		placeholder: "请选择年度"
	}
	// {
	// 	name: "审批状态",
	// 	key: "bpmStatus",
	// 	type: "select",
	// 	placeholder: "请选择",
	// 	children: DictApi.getBpmStatus()
	// },
	/*	{
		name: "推送电商",
		key: "pushEcommerceStatus",
		type: "select",
		children: DictApi.getSendPlatStatus(),
		placeholder: "请选择推送电商"
	}*/
]
/*************************初始化table ******************************/
const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	tbBtnLoading,
	pageSize,
	currentPage,
	pageTotal,
	selectedTableList,
	fetchParam,
	tbBtns,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange,
	onDataSelected,
	onBtnClick
} = useTbInit()
tableProp.value = [
	{ label: "计划年度", prop: "year", width: 85 },
	{ label: "采购计划号", prop: "code", width: 150 },
	{ label: "采购计划名称", prop: "label", minWidth: 150 },
	{ label: "计划类型", prop: "planType", needSlot: true, width: 150 },
	{ label: "公司", prop: "sysCommunityId_view", needSlot: false, width: 150 },
	{
		label: "预估采购金额",
		prop: "purchaseAmount",
		needSlot: true,
		align: "right",
		width: 200
	},
	{ label: "审批状态", prop: "bpmStatus", needSlot: true, minWidth: 120 },
	{ label: "创建人", prop: "createdBy_view", minWidth: 120 },
	{ label: "创建时间", prop: "createdDate", width: 160 },
	{
		label: "操作",
		width: 130,
		prop: "operations",
		fixed: "right",
		needSlot: true
	}
]
onDataSelected.value = (rowList: { [propName: string]: any }[]) => {
	selectedTableList.value = rowList
	const isNoSel = selectedTableList.value.length <= 0
	tbBtns.value[0].disabled =
		isNoSel || selectedTableList.value[0].pushEcommerceStatus === "1"
}
fetchFunc.value = PlanPurchaseApi.getPlanPurchaseList
tbBtns.value = [
	/*	{
		name: "发送电商",
		roles: powerList.purchasePlanBtnSend,
		icon: ["fas", "user"],
		disabled: true,
		click: () => sendPlant()
	}*/
]
/*************************初始化table end ******************************/

//tabs
const tabList = [
	{
		name: "草稿箱",
		icon: ["fas", "square-plus"]
	},
	{
		name: "审批中",
		icon: ["fas", "square-plus"]
	},
	{
		name: "已审批",
		icon: ["fas", "square-plus"]
	}
]
const tabNum = ref([0, 0, 0])
const activeName = ref(tabList[0].name)
const tabStatus = reactive([
	[appStatus.pendingApproval, appStatus.rejected],
	[appStatus.underApproval],
	[appStatus.approved]
])
const handleTabsClick = (tab: any) => {
	activeName.value = tab.paneName
	fetchParam.value.bpmStatus = tabStatus[tab.index].join(",")
	getTableData()
}
const editId = ref<any>("")
//drawer
const showDrawer = ref<boolean>(false)
const showViewDrawer = ref<boolean>(false)

const onAddBtn = () => {
	editId.value = ""
	showDrawer.value = true
}
const onRowView = (row: any) => {
	editId.value = row.id
	showViewDrawer.value = true
}
const onRowEdit = (row: any) => {
	editId.value = row.id
	showDrawer.value = true
}
const onRowDelete = (row: any) => {
	showDelConfirm().then(() => {
		PlanPurchaseApi.deletePlanPurchase(row.id).then(() => {
			ElMessage.success("移除成功")
			fetchTableData()
		})
	})
}
// 获取表格
fetchParam.value = {
	bpmStatus: tabStatus[0].join(",")
}
const getTableData = (data?: { [propName: string]: any }) => {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		...data
	}
	//	ElMessage.info(JSON.stringify(fetchParam.value))
	fetchTableData()
	getBmpStatusStatistics()
}
const getBmpStatusStatistics = () => {
	const params = JSON.parse(JSON.stringify(fetchParam.value))
	delete params.currentPage
	delete params.pageSize
	delete params.bpmStatus
	PlanPurchaseApi.getBmpStatusStatistics(params)
		.then((res) => {
			tabNum.value[0] =
				(res[appStatus.rejected] ? res[appStatus.rejected] : 0) +
				(res[appStatus.pendingApproval] ? res[appStatus.pendingApproval] : 0)
			tabNum.value[1] = res[appStatus.underApproval]
				? res[appStatus.underApproval]
				: 0
			tabNum.value[2] = res[appStatus.approved] ? res[appStatus.approved] : 0
		})
		.finally(() => {})
}

// 弹窗关闭
const onCloseDrawer = (msg: string) => {
	if (msg === "pub") {
		getTableData()
		showDrawer.value = false
	} else if (msg === "save") {
		getTableData()
	} else {
		showDrawer.value = false
		showViewDrawer.value = false
	}
}

//获取字典
const dictOptions = ref<Record<string, any[]>>({
	PLAN_CATEGORY: []
})

function getDictByCodeList(): Promise<void> {
	return new Promise((resolve) => {
		DictApi.getDictByCodeList(dictOptions)
			.then((res) => {
				dictOptions.value = res
			})
			.finally(() => {
				resolve()
			})
	})
}

onMounted(() => {
	Promise.all([getDictByCodeList()]).then(() => {
		getTableData()
	})
})

defineOptions({
	name: "PlanPurchaseManagement"
})
</script>
<template>
	<div class="app-container">
		<div class="whole-frame">
			<ModelFrame class="top-frame">
				<Query :queryArrList="queryArrList" @getQueryData="getTableData" />
			</ModelFrame>
			<ModelFrame class="bottom-frame">
				<Title :title="rightTitle" :button="addBtn" @onBtnClick="onAddBtn">
					<div class="app-tabs-wrapper">
						<el-tabs v-model="activeName" @tab-click="handleTabsClick">
							<el-tab-pane
								v-for="(tab, index) in tabList"
								:key="index"
								:label="`${tab.name}（${tabNum[index]}）`"
								:name="tab.name"
								:index="tab.name"
							/>
						</el-tabs>
					</div>
				</Title>
				<PitayaTable
					ref="tableRef"
					:columns="tableProp"
					:tableData="tableData"
					:total="pageTotal"
					:need-index="true"
					:single-select="true"
					:need-selection="true"
					@onSelectionChange="onDataSelected"
					@on-current-page-change="onCurrentPageChange"
					:table-loading="tableLoading"
				>
					<template #purchaseAmount="{ rowData }">
						<cost-tag :value="rowData.purchaseAmount" />
					</template>
					<template #planType="{ rowData }">
						<dict-tag
							:options="dictOptions.PLAN_CATEGORY"
							:value="rowData.planType"
						/>
					</template>
					<template #bpmStatus="{ rowData }">
						<dict-tag
							:options="DictApi.getBpmStatus()"
							:value="rowData.bpmStatus"
						/>
					</template>
					<template #pushEcommerceStatus="{ rowData }">
						<dict-tag
							:options="DictApi.getSendPlatStatus()"
							:value="rowData.pushEcommerceStatus"
						/>
					</template>

					<template #operations="{ rowData }">
						<slot
							v-if="
								(isCheckPermission(powerList.mergePlanBtnPreview) &&
									hasViewByBpm(rowData.bpmStatus)) ||
								(isCheckPermission(powerList.purchasePlanBtnDrop) &&
									hasEditByBpm(rowData.bpmStatus, rowData.createdBy))
							"
						>
							<el-button
								v-btn
								link
								@click="onRowView(rowData)"
								:disabled="checkPermission(powerList.mergePlanBtnPreview)"
								v-if="
									isCheckPermission(powerList.mergePlanBtnPreview) &&
									hasViewByBpm(rowData.bpmStatus)
								"
							>
								<font-awesome-icon :icon="['fas', 'eye']" />
								<span class="table-inner-btn">查看</span>
							</el-button>
							<el-button
								v-btn
								link
								@click="onRowEdit(rowData)"
								:disabled="checkPermission(powerList.purchasePlanBtnEdit)"
								v-if="
									isCheckPermission(powerList.purchasePlanBtnEdit) &&
									hasEditByBpm(rowData.bpmStatus, rowData.createdBy)
								"
							>
								<font-awesome-icon :icon="['fas', 'pen-to-square']" />
								<span class="table-inner-btn">编辑</span>
							</el-button>
							<el-button
								v-btn
								link
								@click="onRowDelete(rowData)"
								:disabled="checkPermission(powerList.purchasePlanBtnDrop)"
								v-if="
									isCheckPermission(powerList.purchasePlanBtnDrop) &&
									hasEditByBpm(rowData.bpmStatus, rowData.createdBy)
								"
							>
								<font-awesome-icon :icon="['fas', 'trash-can']" />
								<span class="table-inner-btn">移除</span>
							</el-button>
						</slot>
						<slot v-else>---</slot>
					</template>
					<template #footerOperateLeft>
						<ButtonList
							v-if="activeName == '已审批'"
							class="btn-list"
							:is-not-radius="true"
							:button="tbBtns"
							:loading="tbBtnLoading"
							@on-btn-click="onBtnClick"
						/>
					</template>
				</PitayaTable>
				<!-- 新增弹窗 -->
				<Drawer
					:size="modalSize.xxl"
					v-model:drawer="showDrawer"
					:destroyOnClose="true"
				>
					<PurchasePlanDrawer
						:id="editId"
						:table-data="tableData"
						@on-save-or-close="onCloseDrawer"
						@on-select-change="fetchTableData"
					/>
				</Drawer>
				<!-- 查看弹窗 -->
				<Drawer
					:size="modalSize.xxl"
					v-model:drawer="showViewDrawer"
					:destroyOnClose="true"
				>
					<PurchasePlanViewDrawer
						:id="editId"
						:table-data="tableData"
						@on-save-or-close="onCloseDrawer"
					/>
				</Drawer>
			</ModelFrame>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
