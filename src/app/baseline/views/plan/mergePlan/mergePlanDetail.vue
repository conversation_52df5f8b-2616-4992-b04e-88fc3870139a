<!-- 计划 - 合并计划 查看 页 -->
<script lang="ts" setup>
import TableFile from "../../components/tableFile.vue"
import { DictApi, fileBusinessType } from "@/app/baseline/api/dict"
import { PlanMergeApi } from "@/app/baseline/api/plan/planMerge"
import { useUserStore } from "@/app/platform/store/modules/user"
import { ref, onMounted } from "vue"
import XEUtils from "xe-utils"
import { IModalType } from "../../../utils/types/common"
import CostTag from "../../components/costTag.vue"
import { toFixedTwo, getNumDefByNumKey } from "@/app/baseline/utils"

const props = withDefaults(
	defineProps<{
		/**
		 * 业务id
		 */
		id: any

		/**
		 * 编辑模式：查看
		 */
		mode?: IModalType

		footerBtnVisible?: boolean
	}>(),
	{ footerBtnVisible: true, model: IModalType.view }
)

const emit = defineEmits<{
	(e: "close"): void
}>()

/**
 * 能否编辑扩展信息
 */
//const canEditExtra = computed(() => Boolean(formData.value?.id || props.id))

const titleConf = computed(() => ({
	name: ["合并计划"],
	icon: ["fas", "square-share-nodes"]
}))

const btnLoading = ref(false)

const drawerRightTitle = {
	name: ["扩展信息"],
	icon: ["fas", "square-share-nodes"]
}

const formBtnListRight = [{ name: "取消", icon: ["fas", "circle-minus"] }]
//表单默认值
const futureYears = DictApi.getFutureYears(-1, 1)
const { userInfo } = storeToRefs(useUserStore())
const formData = ref<Record<string, any>>({
	year: futureYears[1].value,
	sysOrgId: userInfo.value.orgId,
	sysOrgId_view: userInfo.value.orgName,
	bpmStatus: "0"
})
const descOptions = [
	{ label: "合并计划号", name: "code" },
	{ label: "合并计划名称", name: "label" },
	{ label: "计划年度", name: "year", data: DictApi.getFutureYears(-1, 1) },
	{ label: "需求计划数量", name: "planNum" },
	{ label: "物资需求总量", name: "num_view" },
	{ label: "预估采购金额", name: "purchaseAmount", type: "money" },
	{ label: "线路", name: "lineNoId_view" },
	{ label: "专业", name: "majorId_view" },
	{ label: "段区", name: "depotId_view" },
	{ label: "费用类别", name: "expenseCategory_view" },
	{ label: "备注说明", name: "remark", type: "textarea" },
	{ label: "合并部门", name: "sysOrgId_view" },
	{ label: "创建人", name: "createdBy_view" },
	{ label: "创建时间", name: "createdDate" }
]

const drawerLoading = ref(false)

async function getDetail() {
	if (!props.id) {
		return false
	}

	try {
		drawerLoading.value = true
		const res = await PlanMergeApi.getPlanMerge(props.id as any)
		formData.value = { ...res }
		const tmpAmount = `${XEUtils.commafy(res.purchaseAmount as any, {
			digits: 5
		})}`
		formData.value["purchaseAmount_view"] = tmpAmount ? `￥${tmpAmount}` : "---"
		formData.value.num_view = toFixedTwo(res.num as any, 0)
		formData.value.year = res.year + ""
	} finally {
		drawerLoading.value = false
	}
}

const tabList = ["需求计划", "物资明细", "相关附件"]
const activeTab = ref<number>(0)
const handleTabChange = (index: number) => {
	activeTab.value = index
}

/**
 * 表格组件
 */
const tableComponent = computed(() => {
	const els = [
		// 需求计划
		defineAsyncComponent(() => import("./needPlanTable.vue")),
		// 物资明细
		defineAsyncComponent(() => import("./matTable.vue"))
	]

	return els[activeTab.value]
})

onMounted(async () => {
	getDetail()
})
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<!-- 左侧表单区域 -->
		<div class="drawer-column left">
			<div class="rows">
				<Title :title="titleConf" />
				<el-scrollbar>
					<el-descriptions
						size="small"
						:column="1"
						:border="true"
						class="content"
					>
						<el-descriptions-item
							v-for="(el, index) in descOptions"
							label-align="center"
							:label="el.label"
							:key="index"
						>
							<span v-if="el.type == 'money'">
								<cost-tag :value="formData[el.name]" />
							</span>
							<span v-else>
								{{ getNumDefByNumKey(el.name, formData?.[el.name]) }}
							</span>
						</el-descriptions-item>
					</el-descriptions>
				</el-scrollbar>
			</div>
		</div>
		<!-- 右侧table区域 -->
		<div
			class="drawer-column right"
			:class="{ pdr10: !props.footerBtnVisible }"
		>
			<div class="rows">
				<Title :title="drawerRightTitle">
					<Tabs class="tabs" :tabs="tabList" @on-tab-change="handleTabChange" />
				</Title>

				<component
					:is="tableComponent"
					:id="id"
					:mergePlanInfo="formData"
					:mode="props.mode"
					:is-approval="!props.footerBtnVisible"
					v-if="activeTab < 2"
				/>

				<TableFile
					v-else
					:business-type="fileBusinessType.mergePlan"
					:business-id="formData.id"
					:mod="props.mode"
				/>
			</div>
			<ButtonList
				v-if="props.footerBtnVisible"
				class="footer"
				:button="formBtnListRight"
				:loading="btnLoading"
				@on-btn-click="emit('close')"
			/>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.drawer-container {
	.left {
		width: 310px;
	}

	.right {
		width: calc(100% - 310px);
	}
}
</style>
