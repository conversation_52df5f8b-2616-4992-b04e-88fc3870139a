<!-- 物资清单 table -->
<template>
	<div style="display: flex; height: 100%">
		<el-scrollbar
			style="width: 100%"
			:class="{ computedWidth: props.mode === IModalType.view && !isApproval }"
		>
			<Query
				:queryArrList="queryArrList"
				@getQueryData="getTableData"
				style="margin: 10px 0px -10px 10px !important"
			/>
			<pitaya-table
				ref="tableRef"
				:columns="(tbInit.tableProp as any)"
				:table-data="lastTableData"
				:need-index="true"
				:single-select="true"
				:need-selection="
					props.mode !== IModalType.view ||
					(props.mode === IModalType.view && isApproval)
						? false
						: true
				"
				:need-pagination="true"
				:total="pageTotal"
				:table-loading="tableLoading"
				@on-selection-change="selectedTableList = $event"
				@on-current-page-change="onCurrentPageChange"
				@on-table-sort-change="handleSortChange"
			>
				<!-- 物资性质 -->
				<template #attribute="{ rowData }">
					<dict-tag
						:options="DictApi.getMatAttr()"
						:value="rowData.attribute"
					/>
				</template>

				<!-- 预估采购金额 -->
				<template #purchaseAmount="{ rowData }">
					<cost-tag :value="rowData.purchaseAmount" />
				</template>

				<!-- 预估采购单价 -->
				<template #evaluation="{ rowData }">
					<cost-tag :value="rowData.evaluation" />
				</template>

				<!-- 采购单位 -->
				<template #buyUnit="{ rowData }">
					{{
						dictFilter("INVENTORY_UNIT", rowData.buyUnit)?.subitemName || "---"
					}}
				</template>

				<!-- 需求数量 千分符格式化 -->
				<template #num="{ rowData }">
					{{ toFixedTwo(rowData.num, 0) }}
				</template>

				<template #operations="{ rowData }">
					<el-button v-btn link @click="handleRowView(rowData)">
						<font-awesome-icon :icon="['fas', 'eye']" />
						<span class="table-inner-btn">查看</span>
					</el-button>
				</template>
			</pitaya-table>
		</el-scrollbar>

		<div
			style="
				width: 500px;
				display: flex;
				padding-left: 10px;
				flex-direction: column;
				border-left: 1px solid #ddd;
			"
			v-if="props.mode === IModalType.view && !isApproval"
		>
			<Title :title="titleConf" style="margin-top: 10px" />

			<div>
				<!-- 物资信息 -->
				<selected-need-plan-table
					:id="props.id"
					:mat-id="
						((first(tableRef?.pitayaTableRef?.getSelectionRows())) as any)?.materialId
					"
				/>
			</div>
		</div>

		<!-- 查看需求计划详情弹窗 -->
		<Drawer
			v-model:drawer="selectedNeedPlanDetailVisible"
			:size="modalSize.md"
			destroy-on-close
		>
			<div class="drawer-container">
				<div class="drawer-column" style="width: 100%">
					<Title :title="titleConf" />

					<el-scrollbar class="rows">
						<selected-need-plan-table
							:id="props.id"
							:mat-id="editingTableRow.materialId"
						/>
					</el-scrollbar>

					<button-list
						class="footer"
						:button="[
							{
								name: '取消',
								icon: ['fas', 'circle-minus']
							}
						]"
						@on-btn-click="selectedNeedPlanDetailVisible = false"
					/>
				</div>
			</div>
		</Drawer>
	</div>
</template>

<script setup lang="ts">
import { DictApi } from "@/app/baseline/api/dict"
import { useTbInit } from "../../components/tableBase"
import CostTag from "@/app/baseline/views/components/costTag.vue"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import { convertContentTableData } from "@/app/baseline/utils"

import { PlanMergeItemApi } from "@/app/baseline/api/plan/planMergeItem"
import { useDictInit } from "../../components/dictBase"
import { IModalType } from "../../../utils/types/common"
import selectedNeedPlanTable from "./selectedNeedPlanTable.vue"
import { modalSize } from "@/app/baseline/utils/layout-config"
import { first } from "lodash-es"
import { toFixedTwo } from "@/app/baseline/utils"

const { dictOptions, dictFilter, getDictByCodeList } = useDictInit()

const props = withDefaults(
	defineProps<{
		id: any
		mode: IModalType
		isApproval?: boolean // 审批页面不显示 关联需求计划
	}>(),
	{ mode: IModalType.view, isApproval: false }
)

const queryArrList = computed(() => {
	const ls = [
		{
			name: "物资编码",
			key: "materialCode",
			placeholder: "请输入物资编码",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "物资名称",
			key: "materialLabel",
			placeholder: "请输入物资名称",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "规格型号",
			key: "version",
			placeholder: "请输入规格型号",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "物资性质",
			key: "attribute",
			type: "select",
			children: dictOptions.value.MATERIAL_NATURE,
			placeholder: "请选择物资性质"
		}
	]
	if (props.mode === IModalType.view && !props.isApproval) {
		return ls.filter((v) => !["物资性质", "物资名称"].includes(v.name))
	}
	return ls
})

const titleConf = computed(() => ({
	name: ["关联需求计划"],
	icon: ["fas", "square-share-nodes"]
}))
const tbInit = useTbInit()
const {
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	fetchParam,
	currentPage,
	selectedTableList,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = tbInit

fetchFunc.value = PlanMergeItemApi.getPlanMergeItemMaterialList

tbInit.tableProp = computed(() => {
	const defColumns: TableColumnType[] = [
		{
			label: "物资编码",
			prop: "code",
			width: 130,
			fixed: "left",
			sortable: true
		},
		{ label: "物资名称", prop: "label", width: 150 },
		{ label: "物资分类编码", prop: "materialTypeCode", width: 100 },
		{
			label: "物资分类名称",
			prop: "materialTypeLabel",
			width: 150
		},
		{ label: "规格型号", prop: "version", width: 120 },
		{
			label: "技术参数",
			prop: "technicalParameter",
			minWidth: 200,
			align: "left"
		},
		{ label: "物资性质", prop: "attribute", needSlot: true, width: 120 },
		{ label: "采购单位", prop: "buyUnit", needSlot: true, width: 90 },
		{
			label: "预估采购单价",
			prop: "evaluation",
			align: "right",
			needSlot: true,
			width: 150
		},
		{
			label: "需求数量",
			prop: "num",
			needSlot: true,
			width: 90,
			fixed: "right",
			align: "right"
		},
		{
			label: "预估采购金额",
			prop: "purchaseAmount",
			width: 120,
			needSlot: true,
			align: "right",
			fixed: "right"
		}
	]

	if (props.isApproval) {
		defColumns.push({
			label: "操作",
			width: 100,
			prop: "operations",
			fixed: "right",
			needSlot: true
		})
	}
	return defColumns
})

const lastTableData = ref<any[]>([])

/**
 * 补丁
 * 监听tableData 重组lastTableData
 */
watch([tableData], async () => {
	lastTableData.value = convertContentTableData(tableData.value, [
		"num",
		"planAmount",
		"purchaseAmount",
		"purchaseNum",
		"storeNum",
		"evaluation",
		"materialId",
		"attribute"
	])

	if (tableData.value?.length > 0) {
		nextTick(() => {
			tableRef.value.pitayaTableRef!.toggleRowSelection(
				first(lastTableData.value),
				true
			)
		})
	}
})

const getTableData = (params?: any) => {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	if (props.id) {
		fetchParam.value = {
			mergePlanId: props.id,
			...fetchParam.value,
			...params
		}
		fetchTableData()
	}
}

onMounted(async () => {
	fetchParam.value.sord = "asc"
	fetchParam.value.sidx = "code"

	getDictByCodeList(["INVENTORY_UNIT", "MATERIAL_NATURE"])
	getTableData()
})

/**
 * 排序
 * @param column
 * @param prop 字段名称
 * @param order ascending 升序 descending 降序
 */
function handleSortChange(column: any, prop: any, order: any) {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		sord:
			order === "descending" ? "desc" : order === "ascending" ? "asc" : "asc",
		sidx: order ? prop : "code" // 排序字段
	}

	fetchTableData()
}

const selectedNeedPlanDetailVisible = ref(false)
const editingTableRow = ref()
function handleRowView(e: any) {
	selectedNeedPlanDetailVisible.value = true
	editingTableRow.value = { ...e }
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.computedWidth {
	width: calc(100% - 500px);
}
</style>
