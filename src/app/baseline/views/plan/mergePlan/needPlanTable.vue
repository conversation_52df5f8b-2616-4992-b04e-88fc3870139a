<!-- 需求计划 table -->
<template>
	<el-scrollbar style="height: 100%" class="editor-table-wrapper">
		<pitaya-table
			ref="tableRef"
			:columns="(tbInit.tableProp as any)"
			:table-data="lastTableData"
			:need-index="true"
			:single-select="false"
			:need-selection="props.mode !== IModalType.view ? true : false"
			:need-pagination="true"
			:total="pageTotal"
			:table-loading="tableLoading"
			:cell-class-name="tbCellClassName"
			@on-selection-change="selectedTableList = $event"
			@on-current-page-change="onCurrentPageChange"
			@on-table-sort-change="handleSortChange"
		>
			<template #purchaseAmount="{ rowData }">
				<cost-tag :value="rowData.purchaseAmount" />
			</template>

			<template #lineNoId="{ rowData }">
				<line-tag :options="lineList" :value="rowData.lineNoId" />
			</template>
			<template #operations="{ rowData }">
				<el-button v-btn link @click.stop="handleRowView(rowData)">
					<font-awesome-icon :icon="['fas', 'eye']" />
					<span class="table-inner-btn">查看</span>
				</el-button>
				<el-button
					v-btn
					link
					@click.stop="handleRowDel(rowData)"
					v-if="props.mode !== IModalType.view"
				>
					<font-awesome-icon :icon="['fas', 'trash-can']" />
					<span class="table-inner-btn">移除</span>
				</el-button>
			</template>
			<template #footerOperateLeft v-if="props.mode !== IModalType.view">
				<ButtonList
					class="btn-list"
					:is-not-radius="true"
					:button="tbBtnConf"
					:loading="tbBtnLoading"
					@on-btn-click="tbBtnAction"
				/>
			</template>
		</pitaya-table>
	</el-scrollbar>

	<!-- 查看需求计划详情弹窗 -->
	<Drawer
		v-model:drawer="needPlanDetailVisible"
		:size="modalSize.lg"
		destroy-on-close
	>
		<need-plan-detail
			:id="editingTableRow?.planId"
			@close="needPlanDetailVisible = false"
		/>
	</Drawer>

	<!-- 选择需求计划弹窗 -->
	<Drawer
		:size="modalSize.lg"
		v-model:drawer="needPlanSelectorVisible"
		:destroyOnClose="true"
	>
		<need-plan-selector
			:multiple="true"
			title="选择需求计划"
			:table-req-params="{
				mergePlanId: props?.mergePlanInfo?.id,
				bpmStatus: appStatus.approved,
				planType: 1,
				//sysOrgId: props?.mergePlanInfo?.sysOrgId,
				year: props?.mergePlanInfo?.year,
				depotId: props.mergePlanInfo?.depotId,
				lineNoId: props.mergePlanInfo?.lineNoId
					? `*${props.mergePlanInfo?.lineNoId}*`
					: null,
				parentMajorId: props.mergePlanInfo?.majorId,
				parentExpenseCategory: props.mergePlanInfo?.expenseCategory
			}"
			@close="needPlanSelectorVisible = false"
			@save="handleSaveNeedPlanList"
		/>
	</Drawer>
</template>

<script setup lang="ts">
import needPlanSelector from "./needPlanSelector.vue"
import { BaseLineSysApi } from "@/app/baseline/api/system"
import { appStatus } from "@/app/baseline/api/dict"
import { useTbInit } from "../../components/tableBase"
import CostTag from "@/app/baseline/views/components/costTag.vue"
import LineTag from "@/app/baseline/views/components/lineTag.vue"
import { modalSize } from "@/app/baseline/utils/layout-config"
import { convertContentTableData } from "@/app/baseline/utils"
import { useMessageBoxInit } from "@/app/baseline/views/components/messageBox"

import { PlanMergeItemApi } from "@/app/baseline/api/plan/planMergeItem"
import { map } from "lodash-es"
import { AddPlanMergeItemRequest } from "@/app/baseline/api/defines"
import needPlanDetail from "../needPlan/needPlanDetail.vue"
import {
	IIdempotentTokenType,
	IIdempotentTokenTypePre,
	IModalType
} from "../../../utils/types/common"
import { getIdempotentToken } from "@/app/baseline/utils/validate"

const { showInfoConfirm } = useMessageBoxInit()

const props = withDefaults(
	defineProps<{
		id: any
		mergePlanInfo: Record<string, any>
		mode: IModalType
		tbCellClassName?: (params: any) => string
	}>(),
	{ mode: IModalType.view }
)

const emit = defineEmits<{
	(e: "update"): void
}>()
const tbInit = useTbInit()
const {
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	fetchParam,
	currentPage,
	selectedTableList,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = tbInit

/**
 * 要查看的采购订单id
 */
const editingTableRow = ref<Record<string, any>>()

fetchFunc.value = PlanMergeItemApi.getPlanMergeItemList

tbInit.tableProp = computed(() => {
	const defColumns: TableColumnType[] = [
		{ label: "需求部门", prop: "sysOrgId_view", width: 100, fixed: "left" },
		{ label: "需求计划号", prop: "code", width: 160, fixed: "left" },
		{ label: "需求计划名称", prop: "label", minWidth: 150 },
		{ label: "专业", prop: "majorId_view", width: 120 },
		{
			label: "线路",
			prop: "lineNoId",
			needSlot: true,
			width: 120
		},
		{ label: "段区", prop: "depotId_view", width: 120 },
		{ label: "费用类别", prop: "expenseCategory_view", width: 120 },
		{
			label: "预估金额",
			prop: "purchaseAmount",
			needSlot: true,
			width: 150,
			align: "right"
		},
		{ label: "需求清单编号", prop: "planNeedCode", width: 150 },
		{ label: "需求原因", prop: "remark", minWidth: 150, align: "left" },
		{ label: "创建人", prop: "createdBy_view", width: 120 },
		{ label: "创建时间", prop: "createdDate", width: 150, sortable: true },
		{
			label: "操作",
			width: props.mode == IModalType.view ? 100 : 160,
			prop: "operations",
			fixed: "right",
			needSlot: true
		}
	]

	return defColumns
})

const lastTableData = ref<any[]>([])

/**
 * 补丁
 * 监听tableData 重组lastTableData
 */
watch([tableData], () => {
	lastTableData.value = convertContentTableData(tableData.value, ["planId"])
})

const lineList = ref<[]>([])
function getLineList() {
	BaseLineSysApi.getLineList().then((res) => {
		lineList.value = res
	})
}

const getTableData = () => {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	if (props.id) {
		fetchParam.value = {
			mergePlanId: props.id,
			...fetchParam.value
		}
		fetchTableData()
	}
}

onMounted(async () => {
	fetchParam.value.sord = "desc"
	fetchParam.value.sidx = "createdDate"

	getLineList()
	getTableData()
})

/**
 * 排序
 * @param column
 * @param prop 字段名称
 * @param order ascending 升序 descending 降序
 */
function handleSortChange(column: any, prop: any, order: any) {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		sord:
			order === "descending" ? "desc" : order === "ascending" ? "asc" : "desc",
		sidx: order ? prop : "createdDate" // 排序字段
	}

	fetchTableData()
}

/**
 * 需求计划 详情
 */
const needPlanDetailVisible = ref(false)
function handleRowView(e?: any) {
	needPlanDetailVisible.value = true
	editingTableRow.value = e
}

async function handleRowDel(e: any) {
	await showInfoConfirm("确定要移除吗？")

	await PlanMergeItemApi.deletePlanMergeItem(e.id)
	// 更新左侧
	emit("update")
	fetchTableData()
}

const tbBtnConf = computed(() => {
	return [
		{
			name: "选择需求计划",
			icon: ["fas", "circle-plus"]
		},
		{
			name: "批量移除",
			icon: ["fas", "trash-alt"],
			disabled: selectedTableList.value?.length > 0 ? false : true
		}
	]
})

const tbBtnLoading = ref(false)
const needPlanSelectorVisible = ref(false)

async function tbBtnAction(btnName?: string) {
	if (btnName == "批量移除") {
		await showInfoConfirm("确定要移除选中数据吗？")

		try {
			tbBtnLoading.value = true
			const ids = map(selectedTableList.value, ({ id }) => id).toString()

			await PlanMergeItemApi.deletePlanMergeItem(ids)
			// 更新左侧
			emit("update")
			fetchTableData()
		} finally {
			tbBtnLoading.value = false
		}
	} else if (btnName == "选择需求计划") {
		needPlanSelectorVisible.value = true
	}
}

async function handleSaveNeedPlanList(needList?: any) {
	const matList =
		map(needList, (v) => ({ planId: v.id, mergePlanId: props.id })) || []

	const idempotentToken = getIdempotentToken(
		IIdempotentTokenTypePre.other,
		IIdempotentTokenType.planMerge,
		props.id
	)
	await PlanMergeItemApi.addBatchPlanMergeItem(
		matList as AddPlanMergeItemRequest[],
		idempotentToken
	)

	ElMessage.success("保存成功")

	getTableData()
	emit("update")
	needPlanSelectorVisible.value = false
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.editor-table-wrapper {
	&:deep(.pitaya-table) {
		.error {
			.column-inner-item,
			.cell,
			.el-input__inner {
				color: red;
			}
		}
	}
}
</style>
