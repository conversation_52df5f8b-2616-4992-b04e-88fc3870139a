<!-- 计划 - 合并计划 新建/编辑 页 -->
<script lang="ts" setup>
import { ElMessage, type FormInstance, type FormRules } from "element-plus"
import { useMessageBoxInit } from "@/app/baseline/views/components/messageBox"
import TableFile from "../../components/tableFile.vue"
import { FormElementType } from "@/app/baseline/views/components/define"
import FormElement from "@/app/baseline/views/components/formElement.vue"
import { DictApi, fileBusinessType } from "@/app/baseline/api/dict"
import { BaseLineSysApi } from "@/app/baseline/api/system"
import { PlanMergeApi } from "@/app/baseline/api/plan/planMerge"
import { useUserStore } from "@/app/platform/store/modules/user"
import { ref, onMounted, reactive } from "vue"
import { getModalTypeLabel, toFixedTwo, toMoney } from "@/app/baseline/utils"
import {
	IIdempotentTokenType,
	IIdempotentTokenTypePre,
	IModalType
} from "../../../utils/types/common"
import {
	getIdempotentToken,
	requiredValidator
} from "@/app/baseline/utils/validate"
import { includes } from "lodash-es"
import { ICostCategoryStatus } from "@/app/baseline/utils/types/system-cost-category"

const props = defineProps<{
	/**
	 * 业务id
	 */
	id: any

	/**
	 * 编辑模式：编辑/新建
	 */
	mode: IModalType
}>()

const emit = defineEmits<{
	(e: "save"): void
	(e: "close"): void
}>()

const { showWarnConfirm } = useMessageBoxInit()

/**
 * 能否编辑扩展信息
 */
const canEditExtra = computed(() => Boolean(formData.value?.id || props.id))

const titleConf = computed(() => ({
	name: [
		getModalTypeLabel(
			canEditExtra.value ? IModalType.edit : IModalType.create,
			"合并计划"
		)
	],
	icon: ["fas", "square-share-nodes"]
}))

/**
 * 保存旧的表单数据
 */
const oldFormData = ref<string>()

const btnLoading = ref(false)

const drawerRightTitle = {
	name: ["扩展信息"],
	icon: ["fas", "square-share-nodes"]
}

const formRef = ref<FormInstance>()
const formBtnList = [
	{ name: "取消", icon: ["fas", "circle-minus"] },
	{ name: "保存草稿", icon: ["fas", "floppy-disk"] }
]
const formBtnListRight = computed(() => [
	{
		name: "提交审核",
		icon: ["fas", "circle-check"],
		disabled: formData.value.planNum > 0 ? false : true
	}
])
//表单默认值
const futureYears = DictApi.getFutureYears(-1, 1)
const userStore = useUserStore()
const { userInfo } = storeToRefs(userStore)
const formData = ref<Record<string, any>>({
	year: futureYears[1].value,
	sysOrgId: userInfo.value.orgId,
	sysOrgId_view: userInfo.value.orgName,
	bpmStatus: "0"
})
//表单定义
const formEl = computed<FormElementType[][]>(() => [
	[
		{
			label: "合并计划ID",
			name: "id",
			maxlength: 50,
			disabled: canEditExtra.value,
			type: "hidden"
		}
	],
	[
		{
			label: "计划年度",
			name: "year",
			type: "year",
			format: "YYYY",
			valueFormat: "YYYY",
			disabledDate: (time) => {
				const year = time.getFullYear()
				return year < new Date().getFullYear()
			},
			data: futureYears,
			disabled: !!canEditExtra.value
		}
	],
	[
		{
			label: "合并计划名称",
			name: "label",
			maxlength: 50
		}
	],
	[
		{
			label: "线路",
			name: "lineNoId_view",
			vname: "lineNoId",
			type: "treeSelect",
			disabled: canEditExtra.value,
			treeApi: BaseLineSysApi.getLineList
		}
	],
	[
		{
			label: "所属专业",
			name: "majorId_view",
			vname: "majorId",
			type: "treeSelect",
			disabled: canEditExtra.value,
			treeApi: BaseLineSysApi.getProfessionTree
		}
	],
	[
		{
			label: "段区",
			name: "depotId_view",
			vname: "depotId",
			type: "treeSelect",
			disabled: canEditExtra.value,
			treeApi: () => BaseLineSysApi.getDepotList()
		}
	],
	[
		{
			label: "费用类别",
			name: "expenseCategory_view",
			vname: "expenseCategory",
			type: "treeSelect",
			disabled: canEditExtra.value,
			treeApi: () =>
				BaseLineSysApi.getCostCategoryTree({
					status: ICostCategoryStatus.Started
				})
		}
	],
	[
		{
			label: "合并部门",
			name: "sysOrgId_view",
			vname: "sysOrgId",
			disabled: true
		}
	],
	[
		{
			label: "需求计划数量",
			name: "planNum",
			disabled: true
			/* type: "number" */
		}
	],
	[
		{
			label: "物资需求总量",
			name: "num_view",
			disabled: true
		}
	],
	[
		{
			label: "预估采购金额",
			name: "purchaseAmount_view",
			disabled: true
		}
	],
	[
		{
			label: "备注说明",
			name: "remark",
			maxlength: 200,
			rows: "3",
			type: "textarea"
		}
	]
])
const formRules = reactive<FormRules<typeof formData.value>>({
	year: requiredValidator("计划年度"), //[{ required: true, message: "计划年度不能为空", trigger: "change" }],
	label: requiredValidator("合并计划名称"),
	expenseCategory_view: requiredValidator("费用类别")
})
const drawerLoading = ref(false)

async function getDetail() {
	if (!props.id && !formData.value.id) {
		return false
	}

	try {
		drawerLoading.value = true
		const res = await PlanMergeApi.getPlanMerge(
			(props.id || formData.value.id) as any
		)
		formData.value = { ...res }

		formData.value.year = res.year + ""
		formData.value.num_view = toFixedTwo(res?.num as any, 0)

		formData.value["purchaseAmount_view"] = toMoney(res.purchaseAmount as any)

		oldFormData.value = JSON.stringify(formData.value)
	} finally {
		drawerLoading.value = false
	}
}

/**
 * 处理保存草稿（包含新增和编辑）
 */
async function handleSaveDraft() {
	if (!formRef.value) {
		return
	}

	formRef.value.validate(async (valid: any) => {
		if (!valid) {
			return
		}
		btnLoading.value = true
		// 表单校验通过
		try {
			const api = canEditExtra.value
				? PlanMergeApi.updatePlanMerge
				: PlanMergeApi.addPlanMerge

			let idempotentToken = ""
			if (!canEditExtra.value) {
				idempotentToken = getIdempotentToken(
					IIdempotentTokenTypePre.apply,
					IIdempotentTokenType.planMerge
				)
			}
			const r = await api(formData.value as any, idempotentToken)

			formData.value.id = r.id

			formData.value.num_view = toFixedTwo(r?.num as any, 0)

			formData.value["purchaseAmount_view"] = toMoney(r.purchaseAmount as any)

			//fetchParam.value.applyId = r.id!

			oldFormData.value = JSON.stringify(formData.value)

			ElMessage.success("操作成功")
			emit("save")

			//fetchTableData()
		} finally {
			btnLoading.value = false
		}
	})
}

/**
 * 处理提交审核
 *
 * 需要表单校验
 */
function handleSubmit() {
	if (!formRef.value) {
		return
	}

	formRef.value.validate(async (valid: any) => {
		if (!valid) {
			return
		}

		//先查询是否已有需求计划
		if (
			!formData.value["planNum"] ||
			parseInt(formData.value["planNum"]) <= 0
		) {
			return ElMessage.warning("需选择需求计划才可以提交审核")
		}

		await showWarnConfirm("请确认是否提交本次数据？")

		btnLoading.value = true
		drawerLoading.value = true

		try {
			// 如果主表有修改，则先更新主表数据
			if (oldFormData.value != JSON.stringify(formData.value)) {
				await PlanMergeApi.updatePlanMerge(formData.value as any)
			}

			const idempotentToken = getIdempotentToken(
				IIdempotentTokenTypePre.other,
				IIdempotentTokenType.planMerge,
				formData.value.id
			)

			const { code, data, msg } = await PlanMergeApi.publishApply(
				formData.value as any,
				idempotentToken
			)

			if (data && code != 200) {
				errorGoodsIdList.value = data || []
				ElMessage.error(msg)
				matTableRef.value?.getTableData()
			} else {
				ElMessage.success("操作成功")
				emit("save")
				emit("close")
			}
		} finally {
			btnLoading.value = false
			drawerLoading.value = false
		}
	})
}
const onFormBtnList = async (btnName: string | undefined) => {
	if (!formRef.value) {
		return false
	}
	if (btnName === "保存草稿") {
		handleSaveDraft()
	} else if (btnName === "提交审核") {
		handleSubmit()
	} else if (btnName === "取消") {
		formRef.value?.clearValidate()
		emit("close")
	}
}

const tabList = ["需求计划", "物资明细", "相关附件"]
const activeTab = ref<number>(0)
const handleTabChange = (index: number) => {
	activeTab.value = index
}

/**
 * 表格组件
 */
const tableComponent = computed(() => {
	const els = [
		// 需求计划
		defineAsyncComponent(() => import("./needPlanTable.vue")),
		// 物资明细
		defineAsyncComponent(() => import("./matTable.vue"))
	]

	return els[activeTab.value]
})

/**
 * 更新 详情及主表
 */
async function handleUpdateAll() {
	await getDetail()
	emit("save")
}
onMounted(async () => {
	getDetail()
})

const matTableRef = ref()
/**
 * 物资明细 table 行样式
 *
 * 库存物资异常，用红色标记该行
 */
const errorGoodsIdList = ref<number[]>([])
function tbCellClassName({ row }: any) {
	return includes(errorGoodsIdList.value, row.id) ? "error" : ""
}
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<!-- 左侧表单区域 -->
		<div class="drawer-column left">
			<div class="rows">
				<Title :title="titleConf" />
				<el-scrollbar>
					<el-form
						style="padding-bottom: 30px"
						class="content"
						:model="formData"
						:rules="formRules"
						ref="formRef"
						label-position="top"
						label-width="100px"
					>
						<FormElement :form-element="formEl" :form-data="formData" />
					</el-form>
				</el-scrollbar>
			</div>
			<ButtonList
				class="footer"
				:button="formBtnList"
				:loading="btnLoading"
				@on-btn-click="onFormBtnList"
			/>
		</div>
		<!-- 右侧table区域 -->
		<div class="drawer-column right" :class="canEditExtra ? '' : ' disabled'">
			<div class="rows">
				<Title :title="drawerRightTitle">
					<Tabs class="tabs" :tabs="tabList" @on-tab-change="handleTabChange" />
				</Title>

				<component
					:is="tableComponent"
					:id="props.id || formData.id"
					:mergePlanInfo="formData"
					:mode="props.mode"
					v-if="activeTab < 2"
					:tb-cell-class-name="tbCellClassName"
					@update="handleUpdateAll"
					ref="matTableRef"
				/>

				<TableFile
					v-else
					:business-type="fileBusinessType.mergePlan"
					:business-id="formData.id"
				/>
			</div>
			<ButtonList
				class="footer"
				:button="formBtnListRight"
				:loading="btnLoading"
				@on-btn-click="onFormBtnList"
			/>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.drawer-container {
	.left {
		width: 310px;
	}

	.right {
		width: calc(100% - 310px);
	}
}
</style>
