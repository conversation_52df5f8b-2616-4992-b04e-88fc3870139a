<!-- 物资明细 关联需求计划 table -->
<template>
	<el-scrollbar style="height: 100%">
		<pitaya-table
			ref="tableRef"
			:columns="(tbInit.tableProp as any)"
			:table-data="tableData"
			:need-index="true"
			:single-select="false"
			:need-selection="false"
			:need-pagination="true"
			:total="pageTotal"
			:table-loading="tableLoading"
			@on-selection-change="selectedTableList = $event"
			@on-current-page-change="onCurrentPageChange"
		>
			<template #purchaseAmount="{ rowData }">
				<cost-tag :value="rowData.purchaseAmount" />
			</template>
		</pitaya-table>
	</el-scrollbar>
</template>

<script setup lang="ts">
import { BaseLineSysApi } from "@/app/baseline/api/system"
import { useTbInit } from "../../components/tableBase"
import CostTag from "@/app/baseline/views/components/costTag.vue"

import { PlanMergeItemApi } from "@/app/baseline/api/plan/planMergeItem"

const props = withDefaults(
	defineProps<{
		id: any
		matId?: any
		mergePlanInfo?: Record<string, any>
	}>(),
	{}
)

const tbInit = useTbInit()
const {
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	fetchParam,
	currentPage,
	selectedTableList,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = tbInit

fetchFunc.value = PlanMergeItemApi.getPlanMergeItemMaterialNeedPlanList

tbInit.tableProp = computed(() => {
	const defColumns: TableColumnType[] = [
		{ label: "需求计划号", prop: "code", width: 160, fixed: "left" },
		{ label: "需求计划名称", prop: "label", minWidth: 120 },
		{ label: "需求部门", prop: "sysOrgId_view", width: 100 },
		{ label: "需求数量", prop: "num", width: 120, align: "right" },
		{
			label: "预估采购金额",
			prop: "purchaseAmount",
			needSlot: true,
			width: 150,
			align: "right"
		}
	]

	return defColumns
})

const lineList = ref<[]>([])
function getLineList() {
	BaseLineSysApi.getLineList().then((res) => {
		lineList.value = res
	})
}

watch(
	() => props.matId,
	() => {
		if (props.id && props.matId) {
			fetchParam.value = {
				...fetchParam.value,
				mergePlanId: props.id,
				materialId: props.matId
			}
			fetchTableData()
		} else {
			tableData.value = []
		}
	}
)
const getTableData = () => {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	if (props.id && props.matId) {
		fetchParam.value = {
			...fetchParam.value,
			mergePlanId: props.id,
			materialId: props.matId
		}
		fetchTableData()
	}
}

onMounted(async () => {
	getLineList()
	getTableData()
})
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
