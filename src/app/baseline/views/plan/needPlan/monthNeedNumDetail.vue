<!-- 计划 - 需求计划 查看 - 物资明细/月度需求量 -->
<script lang="ts" setup>
import { watch, ref, reactive } from "vue"
import { PlanApi } from "@/app/baseline/api/plan/plan"
import EmptyData from "@/assets/images/empty/empty_data.svg?component"

export interface Props {
	itemId?: string | number
}

const props = withDefaults(defineProps<Props>(), {})

/**
 * 右侧 title 配置
 */
const drawerRightTitle = {
	name: ["月度需求量"],
	icon: ["fas", "square-share-nodes"]
}

const loading = ref(true)
const empty = ref(false)

const formData = reactive(new Array(12).fill(0))
const formDataIds = reactive<Record<string, any>>({})

/**
 * 初始加载
 */
async function loadData() {
	loading.value = true
	try {
		const planItemMonthlyListRes = await PlanApi.getPlanItemMonthlyList({
			planItemId: props.itemId as any,
			pageSize: 99
		})

		Object.keys(formData).map((_k: any) => (formData[_k] = 0))
		planItemMonthlyListRes.rows.map((_d: any) => {
			const _monthNum = parseInt(_d.month) - 1
			if (formData[_monthNum] != undefined) {
				formData[_monthNum] = _d.num
				formDataIds[_monthNum] = _d.id
			}
		})
	} finally {
		loading.value = false
	}
}

onMounted(() => {
	if (props.itemId) {
		loadData()
	} else {
		loading.value = false
		empty.value = true
	}
})

watch(
	() => props.itemId,
	() => {
		if (props.itemId) {
			loadData()
			empty.value = false
		} else {
			loading.value = false
			empty.value = true
		}
	}
)
</script>
<template>
	<div class="mat-detail drawer-container" v-loading="loading && !empty">
		<!-- 右侧table区域 -->
		<div class="drawer-column right">
			<div class="rows">
				<Title :title="drawerRightTitle" />
				<template v-if="empty">
					<EmptyData class="empty_img" />
					<p>未查询到相关数据</p>
				</template>
				<el-scrollbar v-else class="request-form">
					<div class="request-list">
						<div
							class="list-item"
							v-for="(value, index) in formData"
							:key="index"
						>
							<span>{{ (index + 1).toString().padStart(2, "0") }}</span>
							<label>{{ formData[index] }}</label>
						</div>
					</div>
				</el-scrollbar>
			</div>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
$--input-font-size: 20px;
$--detail--width: 220px;
/* .el-descriptions {
	:deep(.el-descriptions__label) {
		width: 200px;
	}
} */

.mat-detail {
	padding-top: 10px;
	border-left: solid 1px $---border-color;
	width: 220px !important;
}

.right {
	width: 100%;
	&.view {
		padding: 0px;

		:deep(.common-title-wrapper) {
			border: none;
			padding: $---spacing-s 0px $---spacing-m 0px;
		}
		.list-item {
			width: 100% !important;
			padding: 0px $---spacing-m;
		}
		:deep(.el-input__wrapper) {
			background: transparent;
			.el-input__inner {
				color: red !important;
			}
		}
		:deep(.el-input-number__decrease),
		:deep(.el-input-number__increase) {
			display: none;
		}
	}
}
.request-form {
	width: 100%;
	height: calc(100% - 60px);

	span {
		color: $---color-info;
		font-size: 30px;
		font-weight: bold;
	}
	.request-title {
		display: flex;
		padding: $---spacing-m;
		div {
			display: flex;
			flex-direction: column;
			width: 50%;
			align-items: center;
			&:last-child {
				border-left: solid 1px $---border-color;
			}
		}
	}
	.request-list {
		display: flex;
		flex-direction: column;
		align-items: center;
		.list-item {
			display: flex;
			justify-content: center;
			width: 100%;
			padding-top: $---spacing-m;
			> * {
				height: 50px;
				line-height: 50px;
			}
			span {
				border: solid 1px $---border-color;
				border-radius: $---border-radius-m 0px 0px $---border-radius-m;
				text-align: center;
				width: 80px;
				background: $---color-background;
				&:after {
					content: "月";
					padding-left: $---spacing-s;
					color: $---color-info;
					font-size: $---font-size-m;
				}
			}
			label {
				width: 115px;
				border: solid 1px $---border-color;
				border-radius: 0px $---border-radius-m $---border-radius-m 0px;
				border-left: none;
				font-size: calc($--input-font-size + 10px);
				text-align: center;
				color: $---color-info;
			}
			.el-input-number {
				border: solid 1px $---border-color;
				border-radius: 0px $---border-radius-m $---border-radius-m 0px;
				border-left: none;
				:deep(.el-input-number__decrease) {
					background: transparent !important;
				}
				:deep(.el-input-number__increase) {
					background: transparent !important;
				}
				:deep(.el-input__wrapper) {
					box-shadow: none;
				}
				.el-input {
					width: 200px;
				}
				:deep(.el-input__inner) {
					font-size: $--input-font-size !important;
					text-align: center !important;
				}
			}
		}
	}
}
.empty_img {
	margin: auto;
	width: 150px;
	display: block;
	margin-top: calc(100% - 80px);
	+ p {
		color: $---font-color-2;
		font-size: $---font-size-m;
		text-align: center;
	}
}
</style>
