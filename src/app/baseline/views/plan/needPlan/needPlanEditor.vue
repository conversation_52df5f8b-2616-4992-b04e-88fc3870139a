<script lang="ts" setup>
import { ref, onMounted, reactive, onBeforeMount } from "vue"
import { ElMessage, type FormInstance, type FormRules } from "element-plus"
import { FormElementType } from "@/app/baseline/views/components/define"
import {
	DictApi,
	fileBusinessType,
	needListStatus
} from "@/app/baseline/api/dict"
import { BaseLineSysApi } from "@/app/baseline/api/system"
import { PlanApi } from "@/app/baseline/api/plan/plan"
import { useDictInit } from "@/app/baseline/views/components/dictBase"
import XEUtils from "xe-utils"

import FormElement from "@/app/baseline/views/components/formElement.vue"
import TableFile from "../../components/tableFile.vue"
import CostTag from "@/app/baseline/views/components/costTag.vue"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import { useUserStore } from "@/app/platform/store/modules/user"
import { omit, includes, map, toNumber } from "lodash-es"
import {
	IIdempotentTokenType,
	IIdempotentTokenTypePre,
	IModalType
} from "@/app/baseline/utils/types/common"
import {
	requiredValidator,
	maxValidateNum,
	maxValidateErrorInfo,
	validateAndCorrectInput,
	getIdempotentToken
} from "@/app/baseline/utils/validate"
import { useTbInit } from "@/app/baseline/views/components/tableBase"
import { useMessageBoxInit } from "@/app/baseline/views/components/messageBox"
import { modalSize } from "@/app/baseline/utils/layout-config"
import needListSelector from "./needListSelector.vue"
import { NeedApi } from "@/app/baseline/api/plan/need"
import MatList from "@/app/baseline/views/plan/components/matManualList.vue"
import { findIndex } from "lodash-es"
import MatDetail from "@/app/baseline/views/plan/components/plan/matDetail.vue"
import {
	getModalTypeLabel,
	tableColFilter,
	toMoney,
	toFixedTwo
} from "@/app/baseline/utils"
import { ICostCategoryStatus } from "@/app/baseline/utils/types/system-cost-category"

const { dictOptions, getDictByCodeList, dictFilter } = useDictInit()
const { showWarnConfirm } = useMessageBoxInit()

//当前用户信息
const userStore = useUserStore()
const { userInfo } = storeToRefs(userStore)

interface Props {
	id: string
	model: string
	type: string
	year?: any
	matId?: string
}
const props = withDefaults(defineProps<Props>(), {
	id: "",
	model: IModalType.create,
	type: "",
	year: {}
})
const emit = defineEmits<{
	(e: "save"): void
	(e: "close"): void
}>()

/**
 * 能否编辑扩展信息
 */
const canEditExtra = computed(() => Boolean(formData.value?.id || props.id))
const typeName = ref("")

const formBtnLoading = ref(false)
const drawerLeftTitle = computed(() => ({
	name: [
		getModalTypeLabel(
			canEditExtra.value ? IModalType.edit : IModalType.create,
			typeName.value
		)
	],
	icon: ["fas", "square-share-nodes"]
}))

const drawerRightTitle = {
	name: ["扩展信息"],
	icon: ["fas", "square-share-nodes"]
}
const formBtnList = ref([
	{ name: "取消", icon: ["fas", "circle-minus"] },
	{ name: "保存草稿", icon: ["fas", "floppy-disk"] }
])
const tabList = ref(["物资明细", "相关附件"])

const baseFormBtnList = computed(() => [
	{
		name: "提交审核",
		icon: ["fas", "circle-check"],
		disabled: !canEditExtra.value || tableData.value.length < 1
	}
])

//编辑表单
const formData = ref<Record<string, any>>({
	id: props.id,
	year: DictApi.getFutureYears(-1, 1)[2].value,
	planType: props.type
})

const formElNormal = computed<FormElementType[][]>(() => [
	[
		{
			label: "计划年度",
			name: "year",
			type: "year",
			format: "YYYY",
			valueFormat: "YYYY",
			disabledDate: (time: any) => {
				const year = time.getFullYear()
				return year < new Date().getFullYear()
			},
			data: DictApi.getFutureYears(-1, 1),
			disabled: props.type == "1" || canEditExtra.value ? true : false
		}
	],
	[
		{
			label: "年度计划类型",
			name: "planType",
			type: "select",
			data: playTypes.value,
			disabled: true
		}
	],
	[
		{
			label: `${typeName.value}名称`,
			name: "label",
			maxlength: 50
		}
	],
	[
		{
			label: "线路",
			name: "lineNoId_view",
			vname: "lineNoId",
			maxlength: 50,
			type: "treeSelect",
			needSingleSelect: true,
			disabled: canEditExtra.value ? true : false,
			treeApi: BaseLineSysApi.getLineList
		}
	],
	[
		{
			label: "所属专业",
			name: "majorId_view",
			vname: "majorId",
			type: "treeSelect",
			disabled: canEditExtra.value ? true : false,
			treeApi: BaseLineSysApi.getProfessionTree
		}
	],
	[
		{
			label: "段区",
			name: "depotId_view",
			vname: "depotId",
			type: "treeSelect",
			treeApi: BaseLineSysApi.getDepotList,
			placeholder: "请选择段区"
		}
	],
	[
		{
			label: "费用类别",
			name: "expenseCategory_view",
			vname: "expenseCategory",
			type: "treeSelect",
			disabled: canEditExtra.value ? true : false,
			treeApi: () =>
				BaseLineSysApi.getCostCategoryTree({
					status: ICostCategoryStatus.Started
				})
		}
	],
	[
		{
			label: "物资编码数量",
			name: "matCodeNum",
			maxlength: 200,
			disabled: true
		}
	],
	[
		{
			label: "预估采购金额",
			name: "purchaseAmount_view",
			type: "money",
			disabled: true
		}
	],
	[
		{
			label: "需求部门",
			name: "sysOrgId_view",
			vname: "sysOrgId",
			maxlength: 50,
			type: "treeSelect",
			treeApi: BaseLineSysApi.getDepartmentTree,
			disabled: true
		}
	],

	[
		{
			label: "需求原因",
			name: "remark",
			maxlength: 200,
			rows: "3",
			type: "textarea"
		}
	]
])

const formRules = computed<FormRules<typeof formData.value>>(() => ({
	label: requiredValidator(`${typeName.value}名称`),
	lineNoId_view: requiredValidator("线路"),
	majorId_view: requiredValidator("所属专业"),
	expenseCategory_view: requiredValidator("费用类别"),
	remark: requiredValidator("需求原因")
}))

const drawerLoading = ref(false)
const ruleFormRef = ref<FormInstance>()
const refFileTable = ref<any>()
const playTypes = ref<Record<string, any>[]>([])

/**
 * 保存旧的表单数据
 */
const oldFormData = ref<string>()

/**
 * 获取详情
 * @param updParams
 */
const getMatApplyInfo = async () => {
	if (canEditExtra.value) {
		drawerLoading.value = true
		try {
			const res = await PlanApi.getInfoById(props.id || formData.value.id)

			formData.value = { ...formData.value, ...res }

			formData.value.year = res.year.toString()

			formData.value["purchaseAmount_view"] = toMoney(res.purchaseAmount as any)

			oldFormData.value = JSON.stringify(formData.value)
		} finally {
			drawerLoading.value = false
		}
	}
}

/**
 * 物资明细 table 行样式
 *
 * 库存物资异常，用红色标记该行
 */
const errorGoodsIdList = ref<number[]>([])
function tbCellClassName({ row }: any) {
	return includes(errorGoodsIdList.value, row.id) ? "error" : ""
}

// 左侧按钮点击
const onFormBtnList = async (btnName: string | undefined) => {
	if (btnName === "保存草稿") {
		if (ruleFormRef.value) {
			ruleFormRef.value.validate(async (valid: any) => {
				if (!valid) {
					return
				}

				formBtnLoading.value = true
				try {
					const api = canEditExtra.value ? PlanApi.updatePlan : PlanApi.addPlan

					let idempotentToken = ""
					if (!canEditExtra.value) {
						idempotentToken = getIdempotentToken(
							IIdempotentTokenTypePre.apply,
							IIdempotentTokenType.planNeedPlan
						)
					}

					const r = await api(
						omit(formData.value, "purchaseAmount", "code") as any,
						idempotentToken
					)

					formData.value.id = r.id

					formData.value["purchaseAmount_view"] = toMoney(
						r.purchaseAmount as any
					)

					fetchParam.value.planId = r.id
					ElMessage.success("操作成功")

					oldFormData.value = JSON.stringify(formData)

					emit("save")
					getTableData()
				} finally {
					formBtnLoading.value = false
				}
			})
		}
	} else if (btnName == "提交审核") {
		await showWarnConfirm("请确认是否提交本次数据？")
		formBtnLoading.value = true
		drawerLoading.value = true
		try {
			// 如果主表有修改，则先更新主表数据
			if (oldFormData.value != JSON.stringify(formData)) {
				await PlanApi.updatePlan(
					omit({ ...formData.value }, "purchaseAmount", "code") as any
				)
			}

			const idempotentToken = getIdempotentToken(
				IIdempotentTokenTypePre.other,
				IIdempotentTokenType.planNeedPlan,
				formData.value.id
			)

			const { code, data, msg } = await PlanApi.publishPlan(
				formData.value.id,
				idempotentToken
			)

			if (data && code != 200) {
				errorGoodsIdList.value = data || []
				ElMessage.error(msg)
				getTableData({})

				//matTableRef.value?.getTableData()
			} else {
				ElMessage.success("提交成功")
				emit("save")
				emit("close")
			}
		} finally {
			formBtnLoading.value = false
			drawerLoading.value = false
		}
	} else {
		emit("close")
	}
}

//扩展栏标签页切换
const activeTab = ref<number>(0)
const handleTabChange = (index: number) => {
	activeTab.value = index

	if (index === 0) {
		fetchParam.value = {
			planId: props.id || formData.value.id,
			sord: "desc",
			sidx: "createdDate"
		}
		pageSize.value = 20
		getTableData()
	}
}

/**
 * 初始化 计划类别
 */
function initPlanType() {
	DictApi.getDictByCode("PLAN_CATEGORY").then((res: any) => {
		playTypes.value = res as any

		typeName.value =
			res.find((_d: any) => _d.subitemValue == props.type)?.subitemName || ""

		formData.value.playType = res.planType

		setTimeout(() => {
			ruleFormRef.value?.clearValidate()
		}, 0)
	})
}

onBeforeMount(() => {
	if (!props.id) {
		formData.value.sysOrgId = userInfo.value.orgId
		formData.value.sysOrgId_view = userInfo.value.orgName
	}
	if (props.type === "1" && !props.id) {
		formData.value.planYearId = props.year.id
		formData.value.year = props.year.year
	}
})

onMounted(() => {
	getDictByCodeList(["INVENTORY_UNIT", "MATERIAL_NATURE"])
	initPlanType()
	getMatApplyInfo()

	fetchParam.value.planId = props.id || formData.value.id
	fetchParam.value.sord = "desc"
	fetchParam.value.sidx = "createdDate"

	if (canEditExtra.value) {
		fetchTableData()
	}
})

const queryArrList = computed(() => [
	{
		name: "物资编码",
		key: "materialCode",
		placeholder: "请输入物资编码",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资名称",
		key: "materialLabel",
		placeholder: "请输入物资名称",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "规格型号",
		key: "version",
		placeholder: "请输入规格型号",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资性质",
		key: "attribute",
		type: "select",
		children: dictOptions.value.MATERIAL_NATURE,
		placeholder: "请选择物资性质"
	}
])

const tbInit = useTbInit()
const {
	tableCache,
	tableData,
	tableRef,
	tableLoading,
	currentPage,
	pageSize,
	pageTotal,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = tbInit

const tbBtnLoading = ref(false)

const tbBtnConf = computed(() => {
	return [
		{
			name: "批量移除",
			icon: ["fas", "trash-alt"],
			disabled: selectedTableList.value?.length > 0 ? false : true
		},
		{
			name: "引入需求清单",
			icon: ["fas", "circle-plus"]
		},
		{
			name: "添加物资",
			icon: ["fas", "circle-plus"]
		}
	]
})

tbInit.tableProp = computed(() => {
	const defTableColumns: TableColumnType[] = [
		{
			label: "物资编码",
			prop: "content_code",
			width: 130,
			fixed: "left",
			sortable: true
		},
		{ label: "物资名称", prop: "content_label", width: 150 },
		{ label: "物资分类编码", prop: "content_materialTypeCode", width: 100 },
		{ label: "物资分类名称", prop: "content_materialTypeLabel", width: 200 },
		{ label: "规格型号", prop: "content_version", width: 120 },
		{
			label: "技术参数",
			prop: "content_technicalParameter",
			minWidth: 100
		},
		{
			label: "物资性质",
			prop: "attribute",
			needSlot: true,
			width: 120
		},
		{ label: "采购单位", prop: "content_buyUnit", needSlot: true, width: 80 },
		{
			label: "预估采购单价",
			prop: "content_evaluation",
			needSlot: true,
			width: 150,
			align: "right",
			sortable: true
		},
		{
			label: "需求清单编号",
			prop: "planNeedCode",
			width: 150
		},
		{
			label: "库存数量",
			prop: "storeNum",
			width: 120,
			needSlot: true,
			align: "right"
		},
		{
			label: "需求数量",
			prop: "num",
			needSlot: true,
			width: 120,
			fixed: "right",
			sortable: true
		},
		{
			label: "月度需求量",
			prop: "allocationStatus",
			needSlot: true,
			width: 90,
			fixed: "right"
		},
		{
			label: "预估采购金额",
			prop: "cost",
			width: 120,
			needSlot: true,
			fixed: "right",
			align: "right"
		},
		{
			label: "操作",
			width: 120,
			prop: "operations",
			fixed: "right",
			needSlot: true
		}
	]

	if (props.type == "1") {
		return defTableColumns
	} else {
		return tableColFilter(defTableColumns, [
			"需求清单编号",
			"月度需求量",
			"预估采购金额",
			"操作"
		])
	}
})

fetchFunc.value = (param: any) => {
	return new Promise((resolve) => {
		PlanApi.getMatListByPlanId(param).then((_r) => {
			XEUtils.map(_r.rows, (_d: { [propName: string]: any }) => {
				if (_d.content)
					Array.from(Object.keys(_d?.content))?.map(
						(_k) => (_d[`content_${_k}`] = _d.content[_k])
					)
			})
			resolve(_r)
		})
	})
}
function getTableData(data?: { [propName: string]: any }) {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = { ...fetchParam.value, ...data }
	fetchParam.value.planId = formData.value.id
	fetchTableData()
}

function handleInputMin(e: any, row: any) {
	row.num = validateAndCorrectInput(e, 0)
}

/**
 * 更新需求数量
 * @param row
 */
function validateNeedNum(row: any) {
	const num = toNumber(row.num)
	const evaluation = toNumber(row.content_evaluation)
	row.num = num

	const oldRow = tableCache.find((v) => v.id == row.id)
	if (num <= 0) {
		row.num = oldRow.num
		ElMessage.warning("需求数量不能小于等于0！")
		return
	} else {
		const price = toNumber(num * evaluation)
		if (price > maxValidateNum) {
			row.num = oldRow.num
			ElMessage.warning(
				`您的预估采购金额已超过${maxValidateErrorInfo}元，请重新核对金额！`
			)
			return false
		}
	}

	updateNeedNum(row)
}

async function updateNeedNum(e: any) {
	formBtnLoading.value = true

	try {
		const res = await PlanApi.updatePlanItem({
			id: e.id,
			planId: formData.value.id,
			num: e.num as number,
			materialId: e.materialId
		} as any)

		tableData.value.map((_d: any) => {
			if (_d.id == e.id) {
				Object.assign(_d, res)
			}
		})

		//  更新 tableCache
		const rowIdx = findIndex(tableCache, (v) => v.id === e.id)
		if (rowIdx !== -1) {
			tableCache.splice(rowIdx, 1, { ...e })
		}

		ElMessage.success("操作成功")
		getMatApplyInfo()
		emit("save")
	} finally {
		formBtnLoading.value = false
	}
}

/**
 * 查看月度需求量
 * @param row
 */
const showMonthDetailDrawer = ref(false)
const selectedRow = ref<any>()
function showMonthNeedDetail(row: any) {
	selectedRow.value = { ...row }
	showMonthDetailDrawer.value = true
}

function changeMatDetail(data: { [propName: string]: any }) {
	if (selectedRow.value) {
		selectedRow.value.detail = data
		showMonthDetailDrawer.value = false

		// 过滤标红的 id
		const errorRowIdx = findIndex(
			errorGoodsIdList.value,
			(v) => v === selectedRow.value.id
		)
		if (errorRowIdx !== -1) {
			errorGoodsIdList.value.splice(errorRowIdx, 1)
		}
		fetchTableData()
	}
}

const showAddNeedDrawer = ref(false)
const showAddMatDrawer = ref(false)
async function tbBtnAction(btnName?: string) {
	if (btnName == "批量移除") {
		await showWarnConfirm("确定要移除选中数据吗？")

		try {
			tbBtnLoading.value = true
			const ids = map(selectedTableList.value, ({ id }) => id).toString()

			await PlanApi.deletePlanItem(ids)
			getMatApplyInfo()
			fetchTableData()
			emit("save")
		} finally {
			tbBtnLoading.value = false
		}
	} else if (btnName == "引入需求清单") {
		showAddNeedDrawer.value = true
	} else if (btnName == "添加物资") {
		showAddMatDrawer.value = true
	}
}

/**
 * 需求清单  保存
 * @param rows
 */
async function handleSaveNeedList(rows: any[]) {
	const idempotentToken = getIdempotentToken(
		IIdempotentTokenTypePre.other,
		IIdempotentTokenType.planNeedPlan,
		formData.value.id
	)

	await PlanApi.addPlanItemFromNeedList(
		formData.value.id,
		map(rows, ({ id }) => id),
		idempotentToken
	)

	getTableData()
	getMatApplyInfo()
	emit("save")
	showAddNeedDrawer.value = false
}

/**
 * 添加物资 保存
 * @param data
 */
async function onMatListBtnClick(data: any) {
	tbBtnLoading.value = true
	try {
		const params = data.map((v: any) => {
			return {
				planId: formData.value.id,
				materialId: v.id,
				num: 0,
				materialCode: v.code,
				materialLabel: v.label,
				version: v.version,
				attribute: v.attribute
			}
		})

		const idempotentToken = getIdempotentToken(
			IIdempotentTokenTypePre.other,
			IIdempotentTokenType.planNeedPlan,
			formData.value.id
		)

		await PlanApi.addPlanItemBatch(params, idempotentToken)
		getTableData()
		getMatApplyInfo()
		emit("save")
		showAddMatDrawer.value = false
	} finally {
		tbBtnLoading.value = false
	}
}

/**
 * 排序
 * @param column
 * @param prop 字段名称
 * @param order ascending 升序 descending 降序
 */
function handleSortChange(column: any, prop: any, order: any) {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	const computedProp = () => {
		if (prop === "content_code") {
			return "materialCode"
		} else if (prop === "content_evaluation") {
			return "evaluation"
		} else {
			return prop
		}
	}

	fetchParam.value = {
		...fetchParam.value,
		sord:
			order === "descending" ? "desc" : order === "ascending" ? "asc" : "desc",
		sidx: order ? computedProp() : "createdDate" // 排序字段
	}

	fetchTableData()
}
</script>
<template>
	<div class="needplan-draw drawer-container" v-loading="drawerLoading">
		<!-- 左侧表单区域 -->
		<div class="drawer-column left">
			<div class="rows">
				<Title :title="drawerLeftTitle" />
				<el-scrollbar>
					<el-form
						class="content"
						:model="formData"
						:rules="formRules"
						ref="ruleFormRef"
						label-position="top"
						label-width="100px"
					>
						<FormElement :form-element="formElNormal" :form-data="formData" />
					</el-form>
				</el-scrollbar>

				<ButtonList
					class="footer"
					:button="formBtnList"
					:loading="formBtnLoading"
					@on-btn-click="onFormBtnList"
				/>
			</div>
		</div>
		<!-- 右侧table区域 -->
		<div class="drawer-column right" :class="canEditExtra ? '' : 'disabled'">
			<div class="rows">
				<Title :title="drawerRightTitle">
					<Tabs class="tabs" :tabs="tabList" @on-tab-change="handleTabChange" />
				</Title>

				<div class="detail-table" v-if="activeTab === 0">
					<div class="data-table editor-table-wrapper">
						<Query
							:queryArrList="queryArrList"
							@getQueryData="getTableData"
							class="custom-q"
						/>
						<PitayaTable
							ref="tableRef"
							:columns="(tbInit.tableProp as any)"
							:tableData="tableData"
							:need-index="true"
							:need-pagination="true"
							:single-select="false"
							:need-selection="true"
							:total="pageTotal"
							@onSelectionChange="selectedTableList = $event"
							:cell-class-name="tbCellClassName"
							@on-current-page-change="onCurrentPageChange"
							:table-loading="tableLoading"
							@on-table-sort-change="handleSortChange"
						>
							<!-- 物资性质 -->
							<template #attribute="{ rowData }">
								<dict-tag
									:options="DictApi.getMatAttr()"
									:value="rowData.attribute"
								/>
							</template>

							<!-- 库存数量 -->
							<template #storeNum="{ rowData }">
								{{ toFixedTwo(rowData.storeNum) }}
							</template>

							<!-- 需求数量 -->
							<template #num="{ rowData }">
								<el-input
									class="no-arrows"
									v-model="rowData.num"
									@click.stop
									@input="
										(e:any) => {
											handleInputMin(e, rowData)
										}
									"
									@change="validateNeedNum(rowData)"
								/>
							</template>

							<!-- 采购单位 -->
							<template #content_buyUnit="{ rowData }">
								{{
									dictFilter("INVENTORY_UNIT", rowData.content_buyUnit)
										?.subitemName || "---"
								}}
							</template>

							<!-- 预估采购单价 -->
							<template #content_evaluation="{ rowData }">
								<cost-tag :value="rowData.evaluation" />
							</template>

							<!-- 月度需求量 -->
							<template #allocationStatus="{ rowData }">
								<el-tag v-if="rowData.allocationStatus == 1" type="success">
									已分配
								</el-tag>
								<el-tag v-else type="danger">待分配</el-tag>
							</template>

							<!-- 预估采购金额 -->
							<template #cost="{ rowData }">
								<cost-tag :value="rowData.evaluation * rowData.num" />
							</template>

							<!-- 操作 -->
							<template #operations="{ rowData }">
								<el-button
									v-if="rowData.num > 0"
									v-btn
									link
									@click.stop="showMonthNeedDetail(rowData)"
								>
									<font-awesome-icon :icon="['fas', 'eye']" />
									<span class="table-inner-btn">月度需求量</span>
								</el-button>
								<template v-else>---</template>
							</template>
							<template #footerOperateLeft>
								<ButtonList
									class="btn-list"
									:is-not-radius="true"
									:button="tbBtnConf"
									:loading="tbBtnLoading"
									@on-btn-click="tbBtnAction"
								/>
							</template>
						</PitayaTable>
					</div>
				</div>
				<div class="detail-table" v-else>
					<TableFile
						ref="refFileTable"
						v-if="formData.id"
						:mod="props.model"
						:businessId="formData.id"
						:businessType="fileBusinessType.needPlan"
						:needPage="true"
					/>
				</div>
			</div>
			<ButtonList
				class="footer"
				:button="baseFormBtnList"
				:loading="formBtnLoading"
				@on-btn-click="onFormBtnList"
			/>
		</div>

		<!--	需求清单 选择器-->
		<Drawer
			:size="modalSize.lg"
			v-model:drawer="showAddNeedDrawer"
			:destroyOnClose="true"
		>
			<need-list-selector
				:multiple="true"
				@close="showAddNeedDrawer = false"
				@save="handleSaveNeedList"
				:table-api="NeedApi.getPlanNeedList"
				:table-req-params="{
					//parentMajorId: formData.majorId,
					status: needListStatus.started
				}"
			/>
		</Drawer>

		<!--	物资清单-->
		<Drawer
			:size="modalSize.lg"
			v-model:drawer="showAddMatDrawer"
			:destroyOnClose="true"
		>
			<MatList
				model="view"
				typeTitle="物资分类编码"
				listTitle="选择物资"
				:planId="formData.id"
				@onSelected="onMatListBtnClick"
				@onClosed="showAddMatDrawer = false"
			/>
		</Drawer>

		<!--	查看月度需求量-->
		<Drawer
			class="drawer-hidden-box"
			:size="600"
			v-model:drawer="showMonthDetailDrawer"
			:model="props.model"
			:destroyOnClose="true"
		>
			<MatDetail
				filters="code"
				model="edit"
				:id="selectedRow.id"
				@onSuccess="changeMatDetail"
				@onClose="showMonthDetailDrawer = false"
			/>
		</Drawer>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.custom-q {
	margin: 10px 0px -10px 10px !important;
}

.editor-table-wrapper {
	&:deep(.pitaya-table) {
		.error {
			.column-inner-item,
			.cell,
			.el-input__inner {
				color: red;
			}
		}
	}
}

.drawer-container {
	width: 100%;
	&.withNum {
		width: 1600px;
	}
	.left {
		width: 300px;
		.el-scrollbar {
			height: calc(100% - 73px);
		}
		.content {
			width: 100%;
			.el-form {
				width: calc(100% - 13px);
			}
		}
	}
	.right {
		width: calc(100% - 300px);
		.detail-table {
			width: 100%;
			height: 100%;
		}
	}
}
</style>
