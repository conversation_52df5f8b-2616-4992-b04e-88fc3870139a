<script lang="ts" setup>
import { ref, onMounted } from "vue"
import { DictApi, fileBusinessType } from "@/app/baseline/api/dict"
import { PlanApi } from "@/app/baseline/api/plan/plan"
import { useDictInit } from "@/app/baseline/views/components/dictBase"
import XEUtils from "xe-utils"

import TableFile from "../../components/tableFile.vue"
import CostTag from "@/app/baseline/views/components/costTag.vue"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import { first } from "lodash-es"
import { IModalType } from "@/app/baseline/utils/types/common"
import { useTbInit } from "@/app/baseline/views/components/tableBase"
import monthNeedNumDetail from "./monthNeedNumDetail.vue"
import { tableColFilter, toFixedTwo } from "@/app/baseline/utils"
import { getRealLength, setString } from "@/app/baseline/utils/validate"

const { dictOptions, getDictByCodeList, dictFilter } = useDictInit()

interface Props {
	id: string
	model?: string
	type?: string
	year?: any
	matId?: string
	footerBtnVisible?: boolean
}
const props = withDefaults(defineProps<Props>(), {
	model: IModalType.view,
	type: "",
	year: {},
	footerBtnVisible: true
})
const emit = defineEmits<{
	(e: "save"): void
	(e: "close"): void
}>()

/**
 * 能否编辑扩展信息
 */
const canEditExtra = computed(() => Boolean(formData.value?.id || props.id))

const formBtnLoading = ref(false)
const drawerLeftTitle = ref({
	name: [""],
	icon: ["fas", "square-share-nodes"]
})

const drawerRightTitle = {
	name: ["扩展信息"],
	icon: ["fas", "square-share-nodes"]
}

const tabList = ref(["物资明细", "相关附件"])

const baseFormBtnList = computed(() => [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	}
])

//编辑表单
const formData = ref<Record<string, any>>({
	id: props.id,
	year: DictApi.getFutureYears(-1, 1)[2].value,
	planType: props.type
})

const drawerLoading = ref(false)
const refFileTable = ref<any>()
const playTypes = ref<Record<string, any>[]>([])

/**
 * 保存旧的表单数据
 */
const oldFormData = ref<string>()

/**
 * 获取详情
 * @param updParams
 */
const getMatApplyInfo = async () => {
	if (props.id) {
		drawerLoading.value = true
		try {
			const res = await PlanApi.getInfoById(props.id)

			formData.value = { ...formData.value, ...res }

			const _typeName =
				playTypes.value.find((_d) => _d.subitemValue == formData.value.planType)
					?.subitemName || ""
			drawerLeftTitle.value.name = [`${_typeName}信息`]

			formData.value.year = res.year.toString()

			oldFormData.value = JSON.stringify(formData.value)
		} finally {
			drawerLoading.value = false
		}
	}
}

//扩展栏标签页切换
const activeTab = ref<number>(0)
const handleTabChange = (index: number) => {
	activeTab.value = index

	if (index === 0) {
		fetchParam.value = {
			planId: props.id || formData.value.id,
			sord: "desc",
			sidx: "createdDate"
		}
		pageSize.value = 20
		getTableData()
	}
}

onMounted(async () => {
	getDictByCodeList(["INVENTORY_UNIT", "PLAN_CATEGORY", "MATERIAL_NATURE"])
	DictApi.getDictByCode("PLAN_CATEGORY").then((res: any) => {
		playTypes.value = res as any
	})
	await getMatApplyInfo()

	fetchParam.value.planId = props.id || formData.value.id
	fetchParam.value.sord = "desc"
	fetchParam.value.sidx = "createdDate"

	if (canEditExtra.value) {
		fetchTableData()
	}
})

const queryArrList = computed(() => {
	const ls = [
		{
			name: "物资编码",
			key: "materialCode",
			placeholder: "请输入物资编码",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "物资名称",
			key: "materialLabel",
			placeholder: "请输入物资名称",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "规格型号",
			key: "version",
			placeholder: "请输入规格型号",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "物资性质",
			key: "attribute",
			type: "select",
			children: dictOptions.value.MATERIAL_NATURE,
			placeholder: "请选择物资性质"
		}
	]

	if (!props.footerBtnVisible) {
		return ls.filter((v) => !["物资性质", "物资名称"].includes(v.name))
	}

	return ls
})

const tbInit = useTbInit()
const {
	tableData,
	tableRef,
	tableLoading,
	currentPage,
	pageTotal,
	pageSize,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = tbInit

tbInit.tableProp = computed(() => {
	const defTableColumns: TableColumnType[] = [
		{
			label: "物资编码",
			prop: "content_code",
			width: 130,
			fixed: "left",
			sortable: true
		},
		{ label: "物资名称", prop: "content_label", width: 150 },
		{ label: "物资分类编码", prop: "content_materialTypeCode", width: 100 },
		{ label: "物资分类名称", prop: "content_materialTypeLabel", width: 200 },
		{ label: "规格型号", prop: "content_version", width: 120 },
		{
			label: "技术参数",
			prop: "content_technicalParameter",
			minWidth: 100
		},
		{
			label: "物资性质",
			prop: "attribute",
			needSlot: true,
			width: 120
		},
		{ label: "采购单位", prop: "content_buyUnit", needSlot: true, width: 80 },
		{
			label: "预估采购单价",
			prop: "content_evaluation",
			needSlot: true,
			width: 150,
			align: "right",
			sortable: true
		},
		{
			label: "需求清单编号",
			prop: "planNeedCode",
			width: 150
		},
		{
			label: "库存数量",
			prop: "storeNum",
			width: 120,
			needSlot: true,
			align: "right"
		},
		{
			label: "需求数量",
			prop: "num",
			needSlot: true,
			width: 120,
			align: "right",
			fixed: "right",
			sortable: true
		},
		{
			label: "预估采购金额",
			prop: "cost",
			width: 120,
			needSlot: true,
			fixed: "right",
			align: "right"
		}
	]

	if (formData.value?.planType == "1") {
		return defTableColumns
	} else {
		return tableColFilter(defTableColumns, ["需求清单编号"])
	}
	/* return defTableColumns */
})

fetchFunc.value = (param: any) => {
	return new Promise((resolve) => {
		PlanApi.getMatListByPlanId(param).then((_r) => {
			XEUtils.map(_r.rows, (_d: { [propName: string]: any }) => {
				if (_d.content)
					Array.from(Object.keys(_d?.content))?.map(
						(_k) => (_d[`content_${_k}`] = _d.content[_k])
					)
			})
			resolve(_r)
		})
	})
}
function getTableData(data?: { [propName: string]: any }) {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = { ...fetchParam.value, ...data }
	fetchParam.value.planId = formData.value.id
	fetchTableData()
}

/**
 * 补丁
 * 监听 tableData 变化；设置物资Id为第一个
 */
watch(
	() => tableData.value,
	() => {
		if (tableData.value?.length > 0 && formData.value.planType == "1") {
			nextTick(() => {
				tableRef.value.pitayaTableRef!.toggleRowSelection(
					first(tableData.value),
					true
				)
			})
		}
	}
)

/**
 * 排序
 * @param column
 * @param prop 字段名称
 * @param order ascending 升序 descending 降序
 */
function handleSortChange(column: any, prop: any, order: any) {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	const computedProp = () => {
		if (prop === "content_code") {
			return "materialCode"
		} else if (prop === "content_evaluation") {
			return "evaluation"
		} else {
			return prop
		}
	}

	fetchParam.value = {
		...fetchParam.value,
		sord:
			order === "descending" ? "desc" : order === "ascending" ? "asc" : "desc",
		sidx: order ? computedProp() : "createdDate" // 排序字段
	}

	fetchTableData()
}
</script>
<template>
	<div class="needplan-draw drawer-container" v-loading="drawerLoading">
		<!-- 左侧表单区域 -->
		<div class="drawer-column left">
			<div class="rows">
				<Title :title="drawerLeftTitle" />
				<el-scrollbar>
					<el-descriptions size="small" :column="1" border class="content">
						<el-descriptions-item label="需求计划号">
							{{ formData.code || "---" }}
						</el-descriptions-item>
						<el-descriptions-item label="需求计划名称">{{
							formData.label || "---"
						}}</el-descriptions-item>
						<el-descriptions-item label="计划年度">{{
							formData.year || "---"
						}}</el-descriptions-item>
						<el-descriptions-item label="年度计划类型">
							{{
								dictFilter("PLAN_CATEGORY", formData.planType)?.subitemName ||
								"---"
							}}
						</el-descriptions-item>
						<el-descriptions-item label="物资编码数量">{{
							formData.matCodeNum || "0"
						}}</el-descriptions-item>
						<el-descriptions-item label="预估采购金额"
							><CostTag :value="formData.purchaseAmount"
						/></el-descriptions-item>
						<el-descriptions-item label="线路">{{
							formData.lineNoId_view || "---"
						}}</el-descriptions-item>
						<el-descriptions-item label="专业">{{
							formData.majorId_view || "---"
						}}</el-descriptions-item>
						<el-descriptions-item label="段区">
							{{ formData.depotId_view || "---" }}
						</el-descriptions-item>
						<el-descriptions-item label="费用类别">
							{{ formData.expenseCategory_view || "---" }}
						</el-descriptions-item>
						<el-descriptions-item label="需求部门">{{
							formData.sysOrgId_view || "---"
						}}</el-descriptions-item>
						<el-descriptions-item
							label="需求清单编号"
							v-if="formData.planType == '1'"
						>
							{{ formData.planNeedCode || "---" }}
						</el-descriptions-item>
						<el-descriptions-item label="需求原因">
							<el-tooltip
								effect="dark"
								:content="formData?.remark"
								:disabled="
									getRealLength(formData?.remark) <= 100 ? true : false
								"
							>
								{{
									getRealLength(formData?.remark) > 100
										? setString(formData?.remark, 100)
										: formData?.remark || "---"
								}}
							</el-tooltip>
						</el-descriptions-item>
						<el-descriptions-item label="创建人">{{
							formData.createdBy_view || "---"
						}}</el-descriptions-item>
						<el-descriptions-item label="创建时间">{{
							formData.createdDate || "---"
						}}</el-descriptions-item>
					</el-descriptions>
				</el-scrollbar>
			</div>
		</div>
		<!-- 右侧table区域 -->
		<div class="drawer-column right" :class="{ pdr10: !footerBtnVisible }">
			<div class="rows" style="margin-bottom: -10px">
				<Title :title="drawerRightTitle">
					<Tabs class="tabs" :tabs="tabList" @on-tab-change="handleTabChange" />
				</Title>

				<div class="detail-table" v-if="activeTab === 0" style="display: flex">
					<div
						class="data-table editor-table-wrapper"
						:class="{ computedWidth: formData.planType == '1' }"
					>
						<Query
							:queryArrList="queryArrList"
							@getQueryData="getTableData"
							class="custom-q"
						/>
						<PitayaTable
							ref="tableRef"
							:columns="(tbInit.tableProp as any)"
							:tableData="tableData"
							:need-index="true"
							:need-pagination="true"
							:single-select="true"
							:need-selection="formData.planType == '1'"
							:total="pageTotal"
							@onSelectionChange="selectedTableList = $event"
							@on-current-page-change="onCurrentPageChange"
							:table-loading="tableLoading"
							@on-table-sort-change="handleSortChange"
						>
							<!-- 物资性质 -->
							<template #attribute="{ rowData }">
								<dict-tag
									:options="DictApi.getMatAttr()"
									:value="rowData.attribute"
								/>
							</template>

							<!-- 采购单位 -->
							<template #content_buyUnit="{ rowData }">
								{{
									dictFilter("INVENTORY_UNIT", rowData.content_buyUnit)
										?.subitemName || "---"
								}}
							</template>

							<!-- 库存数量 -->
							<template #storeNum="{ rowData }">
								{{ toFixedTwo(rowData.storeNum) }}
							</template>

							<!-- 预估采购单价 -->
							<template #content_evaluation="{ rowData }">
								<cost-tag :value="rowData.evaluation" />
							</template>

							<!-- 预估采购金额 -->
							<template #cost="{ rowData }">
								<cost-tag :value="rowData.evaluation * rowData.num" />
							</template>

							<template #num="{ rowData }">
								{{ toFixedTwo(rowData.num, 0) }}
							</template>
						</PitayaTable>
					</div>

					<month-need-num-detail
						v-if="formData.planType == '1'"
						:itemId="(first(tableRef?.pitayaTableRef?.getSelectionRows()) as any)?.id"
					/>
				</div>
				<div class="detail-table" v-else>
					<TableFile
						ref="refFileTable"
						v-if="formData.id"
						:mod="props.model"
						:businessId="formData.id"
						:businessType="fileBusinessType.needPlan"
						:needPage="true"
					/>
				</div>
			</div>
			<ButtonList
				v-if="props.footerBtnVisible"
				class="footer"
				:button="baseFormBtnList"
				:loading="formBtnLoading"
				@on-btn-click="emit('close')"
			/>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

$--detail--width: 220px;

.custom-q {
	margin: 10px 0px -10px 10px !important;
}

.editor-table-wrapper {
	&:deep(.pitaya-table) {
		.error {
			.column-inner-item,
			.cell,
			.el-input__inner {
				color: red;
			}
		}
	}
}
.data-table {
	width: 100%;
}
.computedWidth {
	width: calc(100% - 220px);
}

.drawer-container {
	width: 100%;

	.left {
		width: 300px;
		.el-scrollbar {
			height: calc(100% - 73px);
		}
		.content {
			width: 100%;
			.el-form {
				width: calc(100% - 13px);
			}
		}
	}
	.right {
		width: calc(100% - 300px);
		.detail-table {
			width: 100%;
			height: 100%;
		}
	}
}
</style>
