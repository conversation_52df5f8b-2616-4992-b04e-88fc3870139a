<!-- 需求清单 选择器 -->
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<div class="drawer-column" style="width: 100%">
			<Title :title="titleConf" />
			<Query
				:query-arr-list="(queryArrList as any)"
				style="margin: 10px 10px -10px"
				@get-query-data="getQueryData"
			/>
			<el-scrollbar class="rows">
				<pitaya-table
					ref="tableRef"
					:columns="tableProp"
					:table-data="tableData"
					:need-selection="true"
					:single-select="!multiple"
					:need-index="true"
					:need-pagination="true"
					:total="pageTotal"
					:table-loading="tableLoading"
					@on-selection-change="selectedTableList = $event"
					@on-current-page-change="onCurrentPageChange"
				>
					<template #status="{ rowData }">
						<dict-tag
							:options="DictApi.getStpStatus()"
							:value="rowData.status"
						/>
					</template>
				</pitaya-table>
			</el-scrollbar>

			<button-list
				class="footer"
				:button="btnConf"
				:loading="btnLoading"
				@on-btn-click="handleBtnClick"
			/>
		</div>
	</div>
</template>

<script setup lang="ts">
import { PurchaseOrderApi } from "@/app/baseline/api/purchase/purchaseOrder"

import DictTag from "../../components/dictTag.vue"
import { DictApi } from "@/app/baseline/api/dict"
import { BaseLineSysApi } from "@/app/baseline/api/system"
import { useTbInit } from "../../components/tableBase"
import { ICostCategoryStatus } from "@/app/baseline/utils/types/system-cost-category"

const props = withDefaults(
	defineProps<{
		/**
		 * 当前选中的数据
		 */
		selected?: any

		/**
		 * 是否多选
		 */
		multiple?: boolean
		/**
		 * table 请求参数
		 */
		tableReqParams?: any

		/**
		 * table 数据源
		 */
		tableApi: (params: any) => Promise<any>
	}>(),
	{
		tableReqParams: () => ({}),
		multiple: false
	}
)

const btnLoading = ref(false)

const emit = defineEmits<{
	(e: "close"): void
	(e: "save", v: any[]): void
}>()

const titleConf = computed(() => ({
	name: ["选择需求清单"],
	icon: ["fas", "square-share-nodes"]
}))

const queryArrList = computed(() => [
	{
		name: "所属专业",
		key: "parentMajorId",
		type: "treeSelect",
		treeApi: () => BaseLineSysApi.getProfessionTree(),
		placeholder: "请选择所属专业"
	},
	{
		name: "费用类别",
		key: "parentExpenseCategory",
		type: "treeSelect",
		treeApi: () =>
			BaseLineSysApi.getCostCategoryTree({
				status: ICostCategoryStatus.Started
			}),
		placeholder: "请选择费用类别"
	},
	/* {
		name: "清单状态",
		key: "status",
		placeholder: "请选择清单状态",
		enableFuzzy: false,
		type: "elTreeSelect",
		children: DictApi.getStpStatus()
	}, */
	{
		name: "需求清单编号",
		key: "code",
		placeholder: "请输入需求清单编号",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "需求清单名称",
		key: "label",
		placeholder: "请输入需求清单名称",
		enableFuzzy: true,
		type: "input"
	}
])

const drawerLoading = ref(false)

const btnConf = computed(() => [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "保存",
		icon: ["fas", "floppy-disk"],
		disabled: selectedTableList.value.length === 0 ? true : false
	}
])

const {
	currentPage,
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	selectedTableList,
	fetchTableData,
	fetchFunc,
	fetchParam,
	onCurrentPageChange
} = useTbInit()

fetchFunc.value = PurchaseOrderApi.getPurchaseOrderList

tableProp.value = [
	{ label: "需求清单编号", prop: "code", width: 160 },
	{ label: "需求清单名称", prop: "label", minWidth: 150 },
	{ label: "物资编码数量", prop: "matCodeNum", width: 120 },
	{ label: "专业", prop: "majorId_view", width: 150 },
	{
		label: "费用类别",
		prop: "expenseCategory_view",
		width: 100
	},
	{ label: "公司", prop: "sysCommunityId_view" },
	{ label: "清单状态", prop: "status", needSlot: true, width: 85 },
	{ label: "创建人", prop: "createdBy_view", width: 120 },
	{ label: "创建时间", prop: "createdDate", width: 160 }
]

function getQueryData(e?: any) {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = { ...fetchParam.value, ...e }
	fetchTableData()
}

onMounted(async () => {
	fetchParam.value = {
		...fetchParam.value,
		...props.tableReqParams,
		sord: "desc",
		sidx: "createdDate"
	}
	fetchFunc.value = props.tableApi
	getQueryData()
})

function handleBtnClick(name?: string) {
	if (name === "取消") {
		return emit("close")
	}

	btnLoading.value = true
	try {
		emit("save", selectedTableList.value)
	} finally {
		setTimeout(() => {
			btnLoading.value = false
		}, 500)
	}
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
