<!-- 计划 - 采购计划 重构 V2.0 -->
<script lang="ts" setup>
import { ref, onMounted, reactive } from "vue"
import { ElMessage } from "element-plus"
import {
	checkPermission,
	isCheckPermission
} from "@/app/platform/utils/permission"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import { appStatus, DictApi } from "@/app/baseline/api/dict"
import { useTbInit } from "@/app/baseline/views/components/tableBase"
import {
	getTaskByBusinessIds,
	listCompanyWithFormat
} from "@/app/baseline/api/system"
import { powerList } from "@/app/baseline/views/components/define.d"
import { PlanPurchaseApi } from "@/app/baseline/api/plan/planPurchase"
import CostTag from "@/app/baseline/views/components/costTag.vue"
import { hasEditByBpm, hasViewByBpm } from "../../utils"
import { useMessageBoxInit } from "../components/messageBox"
import { modalSize } from "@/app/baseline/utils/layout-config"
import { omit } from "lodash-es"
import { useDictInit } from "../components/dictBase"
import { IModalType } from "../../utils/types/common"
import purchasePlanEditor from "./purchasePlan/purchasePlanEditor.vue"
import purchasePlanDetail from "./purchasePlan/purchasePlanDetail.vue"
import ApprovedDrawer from "@/app/baseline/views/components/approvedDrawer.vue"

const { dictFilter, getDictByCodeList } = useDictInit()
const { showDelConfirm } = useMessageBoxInit()

/**
 * title 配置
 */
const titleConf = {
	name: ["采购计划"],
	icon: ["fas", "square-share-nodes"]
}
const addBtn = [
	{
		name: "新建采购计划",
		roles: powerList.purchasePlanBtnCreate,
		icon: ["fas", "square-plus"]
	}
]

/**
 * 公司列表
 */
const companyOptions = ref<any>([])

/**
 * 查询条件 配置
 */
const queryArrList = computed(() => [
	{
		name: "采购计划号",
		key: "code",
		placeholder: "请输入采购计划号",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "采购计划名称",
		key: "label",
		placeholder: "请输入采购计划名称",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "所属公司",
		key: "sysCommunityId",
		type: "select",
		children: companyOptions.value,
		placeholder: "请选择所属公司"
	},
	{
		name: "年度",
		key: "year",
		type: "select",
		children: DictApi.getFutureYears(-2, 2),
		placeholder: "请选择年度"
	}
])

const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	currentPage,
	pageTotal,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = useTbInit()

/**
 * table 列 配置
 */
tableProp.value = [
	{ label: "计划年度", prop: "year", width: 100, sortable: true },
	{ label: "采购计划号", prop: "code", width: 150, sortable: true },
	{ label: "采购计划名称", prop: "label", minWidth: 150 },
	{ label: "年度计划类型", prop: "planType", needSlot: true, width: 150 },
	{ label: "公司", prop: "sysCommunityId_view", width: 150 },
	{ label: "物资编码数量", prop: "matCodeNum", width: 120 },
	{
		label: "预估采购金额",
		prop: "purchaseAmount",
		needSlot: true,
		align: "right",
		width: 200,
		sortable: true
	},
	{ label: "审批状态", prop: "bpmStatus", needSlot: true, minWidth: 120 },
	{ label: "创建人", prop: "createdBy_view", minWidth: 120 },
	{ label: "创建时间", prop: "createdDate", width: 160, sortable: true },
	{
		label: "操作",
		width: 200,
		prop: "operations",
		fixed: "right",
		needSlot: true
	}
]

fetchFunc.value = PlanPurchaseApi.getPlanPurchaseList

/**
 * tab 配置
 */
const tabList = [
	{
		name: "草稿箱",
		icon: ["fas", "square-plus"]
	},
	{
		name: "审批中",
		icon: ["fas", "square-plus"]
	},
	{
		name: "已审批",
		icon: ["fas", "square-plus"]
	}
]
const tabNum = ref([0, 0, 0])
const activeName = ref(tabList[0].name)
const tabStatus = reactive([
	[appStatus.pendingApproval, appStatus.rejected],
	[appStatus.underApproval],
	[appStatus.approved]
])
const handleTabsClick = (tab: any) => {
	activeName.value = tab.paneName
	fetchParam.value.bpmStatus = tabStatus[tab.index].join(",")
	getTableData()
}

/**
 * 编辑模式：编辑/新建
 */
const editorMode = ref(IModalType.create)
const editorId = ref<any>("")
const purchasePlanEditorVisible = ref<boolean>(false)
const purchasePlanDetailVisible = ref<boolean>(false)
const approvedVisible = ref(false)
const editTask = ref<Record<string, any>>()

/**
 * 新增 操作
 */
function handleRowAdd() {
	editorMode.value = IModalType.create
	editorId.value = ""
	purchasePlanEditorVisible.value = true
}

/**
 * 查看 操作
 * @param row
 */
const handleRowView = async (row: any) => {
	editorMode.value = IModalType.view
	editorId.value = row.id

	if (row.bpmStatus == appStatus.pendingApproval) {
		purchasePlanDetailVisible.value = true
	} else {
		const res = await getTaskByBusinessIds({
			businessIds: [row.id],
			camundaKey: "purchase_plan"
		})

		if (Array.isArray(res) && res.length > 0) {
			editTask.value = { ...res[res.length - 1] }
			approvedVisible.value = true
		} else {
			purchasePlanDetailVisible.value = true
		}
	}
}

/**
 * 编辑 操作
 * @param row
 */
const handleRowEditor = (row: any) => {
	editorMode.value = IModalType.edit
	editorId.value = row.id
	purchasePlanEditorVisible.value = true
}

/**
 * 删除
 * @param row
 */
async function handleRowDel(row: any) {
	await showDelConfirm()
	await PlanPurchaseApi.deletePlanPurchase(row.id)

	ElMessage.success("移除成功")
	fetchTableData()
	getBmpStatusStatistics()
}

/**
 * 查询回调
 * @param data
 */
const getTableData = (data?: { [propName: string]: any }) => {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		...data
	}
	fetchTableData()
	getBmpStatusStatistics()
}

/**
 * 状态统计
 */
async function getBmpStatusStatistics() {
	const res: Record<string, any> = await PlanPurchaseApi.getBmpStatusStatistics(
		omit(fetchParam.value, "currentPage", "pageSize", "bpmStatus")
	)

	tabNum.value[0] =
		(res[appStatus.rejected] ?? 0) + (res[appStatus.pendingApproval] ?? 0)
	tabNum.value[1] = res[appStatus.underApproval] ?? 0

	tabNum.value[2] = res[appStatus.approved] ?? 0
}

onMounted(async () => {
	fetchParam.value.bpmStatus = tabStatus[0].join(",")
	fetchParam.value.sord = "desc"
	fetchParam.value.sidx = "createdDate"

	listCompanyWithFormat().then((r) => (companyOptions.value = r))
	getDictByCodeList(["PLAN_CATEGORY"])
	getTableData()
})

/**
 * 排序
 * @param column
 * @param prop 字段名称
 * @param order ascending 升序 descending 降序
 */
function handleSortChange(column: any, prop: any, order: any) {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		sord:
			order === "descending" ? "desc" : order === "ascending" ? "asc" : "desc",
		sidx: order
			? prop === "purchaseAmount"
				? "predictAmount"
				: prop
			: "createdDate" // 排序字段
	}

	fetchTableData()
}

defineOptions({
	name: "PlanPurchaseManagement"
})
</script>
<template>
	<div class="app-container">
		<div class="whole-frame">
			<ModelFrame class="top-frame">
				<Query
					:queryArrList="queryArrList"
					class="ml10"
					@getQueryData="getTableData"
				/>
			</ModelFrame>
			<ModelFrame class="bottom-frame">
				<Title :title="titleConf" :button="addBtn" @onBtnClick="handleRowAdd">
					<div class="app-tabs-wrapper">
						<el-tabs v-model="activeName" @tab-click="handleTabsClick">
							<el-tab-pane
								v-for="(tab, index) in tabList"
								:key="index"
								:label="`${tab.name}（${tabNum[index]}）`"
								:name="tab.name"
								:index="tab.name"
							/>
						</el-tabs>
					</div>
				</Title>
				<PitayaTable
					ref="tableRef"
					:columns="tableProp"
					:tableData="tableData"
					:total="pageTotal"
					:need-index="true"
					:single-select="false"
					:need-selection="false"
					@onSelectionChange="selectedTableList = $event"
					@on-current-page-change="onCurrentPageChange"
					:table-loading="tableLoading"
					@on-table-sort-change="handleSortChange"
				>
					<!-- 预估采购金额 -->
					<template #purchaseAmount="{ rowData }">
						<cost-tag :value="rowData.purchaseAmount" />
					</template>

					<!-- 计划类型 -->
					<template #planType="{ rowData }">
						{{
							dictFilter("PLAN_CATEGORY", rowData.planType)?.subitemName ||
							"---"
						}}
					</template>

					<!-- 审批状态 -->
					<template #bpmStatus="{ rowData }">
						<dict-tag
							:options="DictApi.getBpmStatus()"
							:value="rowData.bpmStatus"
						/>
					</template>
					<template #operations="{ rowData }">
						<slot
							v-if="
								isCheckPermission(powerList.mergePlanBtnPreview) ||
								(isCheckPermission(powerList.purchasePlanBtnDrop) &&
									hasEditByBpm(rowData.bpmStatus, rowData.createdBy)) ||
								(isCheckPermission(powerList.purchasePlanBtnEdit) &&
									hasEditByBpm(rowData.bpmStatus, rowData.createdBy))
							"
						>
							<el-button
								v-btn
								link
								@click="handleRowEditor(rowData)"
								:disabled="checkPermission(powerList.purchasePlanBtnEdit)"
								v-if="
									isCheckPermission(powerList.purchasePlanBtnEdit) &&
									hasEditByBpm(rowData.bpmStatus, rowData.createdBy)
								"
							>
								<font-awesome-icon :icon="['fas', 'pen-to-square']" />
								<span class="table-inner-btn">编辑</span>
							</el-button>

							<el-button
								v-btn
								link
								@click="handleRowView(rowData)"
								:disabled="checkPermission(powerList.mergePlanBtnPreview)"
								v-if="isCheckPermission(powerList.mergePlanBtnPreview)"
							>
								<font-awesome-icon :icon="['fas', 'eye']" />
								<span class="table-inner-btn">查看</span>
							</el-button>

							<el-button
								v-btn
								link
								@click="handleRowDel(rowData)"
								:disabled="checkPermission(powerList.purchasePlanBtnDrop)"
								v-if="
									isCheckPermission(powerList.purchasePlanBtnDrop) &&
									hasEditByBpm(rowData.bpmStatus, rowData.createdBy)
								"
							>
								<font-awesome-icon :icon="['fas', 'trash-can']" />
								<span class="table-inner-btn">移除</span>
							</el-button>
						</slot>
						<slot v-else>---</slot>
					</template>
				</PitayaTable>

				<!-- 新增/编辑 弹窗 -->
				<Drawer
					:size="modalSize.xxl"
					v-model:drawer="purchasePlanEditorVisible"
					:destroyOnClose="true"
				>
					<purchase-plan-editor
						:id="editorId"
						:mode="editorMode"
						@close="purchasePlanEditorVisible = false"
						@update="
							() => {
								fetchTableData()
								getBmpStatusStatistics()
							}
						"
					/>
				</Drawer>

				<!-- 查看弹窗 -->
				<Drawer
					:size="modalSize.xxl"
					v-model:drawer="purchasePlanDetailVisible"
					:destroyOnClose="true"
				>
					<purchase-plan-detail
						:id="editorId"
						:mode="editorMode"
						@close="purchasePlanDetailVisible = false"
					/>
				</Drawer>

				<!-- 审批、查看弹窗 -->
				<Drawer
					:size="modalSize.xxl"
					v-model:drawer="approvedVisible"
					:destroyOnClose="true"
				>
					<approved-drawer
						:mod="editorMode"
						:task-value="editTask"
						@on-save-or-close="approvedVisible = false"
					/>
				</Drawer>
			</ModelFrame>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
