<script lang="ts" setup>
import { ElMessage } from "element-plus"
import { ref, onMounted } from "vue"
import SelectDrawer from "@/app/baseline/views/components/selectDrawer.vue"
import { useUserStore } from "@/app/platform/store/modules/user"
import { PlanPurchaseApi } from "@/app/baseline/api/plan/planPurchase"
import { appStatus, DictApi } from "../../../api/dict"
import { useDictInit } from "../../components/dictBase"
import DictTag from "../../components/dictTag.vue"
import CostTag from "../../components/costTag.vue"
const userStore = useUserStore()
const { userInfo } = storeToRefs(userStore)
const { dictOptions, getDictByCodeList } = useDictInit()
interface Props {
	id: string
}

const props = withDefaults(defineProps<Props>(), {
	id: undefined
})
const emits = defineEmits(["onSuccess", "onClosed"])
const selectId = props.id ? [props.id] : []
const drawerLoading = ref(false)
const queryArrList = ref<any[]>([
	{
		name: "计划年度",
		key: "year",
		type: "select",
		children: DictApi.getFutureYears(-2, 2),
		placeholder: "请选择年度"
	},
	{
		name: "采购计划号",
		key: "code",
		placeholder: "请输入采购计划号",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "采购计划名称",
		key: "label",
		placeholder: "请输入采购计划名称",
		enableFuzzy: true,
		type: "input"
	}
])
const tableProp = [
	{ label: "计划年度", prop: "year", width: 85 },
	{ label: "采购计划号", prop: "code", width: 150 },
	{ label: "采购计划名称", prop: "label", minWidth: 200 },
	{ label: "年度计划类型", prop: "planType", needSlot: true, width: 150 },
	{ label: "公司", prop: "sysCommunityId_view", width: 150 },
	{
		label: "预估总金额",
		prop: "purchaseAmount",
		needSlot: true,
		align: "right",
		width: 150
	},
	{
		label: "物资编码（项）",
		prop: "matCodeNum",
		width: 130
	},
	{
		label: "已分包物资（项）",
		prop: "distributionNum",
		width: 130
	}
]
const tableApi = PlanPurchaseApi.getPlanPurchaseList
const fetchParam = {
	bpmStatus: appStatus.approved,
	filterDistribution: "1",
	sord: "desc",
	sidx: "createdDate"
}
// 左侧按钮点击
const onSave = (rowList: any[]) => {
	if (rowList.length <= 0) {
		ElMessage.warning("请选择采购计划！")
		return
	}
	emits("onSuccess", rowList[0])
	drawerLoading.value = false
}
const onClose = () => {
	emits("onClosed")
}
onMounted(async () => {
	Promise.all([getDictByCodeList(["PLAN_CATEGORY"])]).then(() => {})
})
defineOptions({
	name: "SelectDrawer"
})
</script>
<template>
	<SelectDrawer
		title="选择采购计划"
		:query-arr-list="queryArrList"
		:table-prop="tableProp"
		:table-api="tableApi"
		:fetch-param="fetchParam"
		:selected="selectId"
		:loading="drawerLoading"
		:needSingleSelect="true"
		@on-close="onClose"
		@on-save="onSave"
	>
		<template #planType="{ rowData }">
			<dict-tag
				:options="dictOptions.PLAN_CATEGORY"
				:value="rowData.planType"
			/>
		</template>
		<template #purchaseAmount="{ rowData }">
			<cost-tag :value="rowData.purchaseAmount" />
		</template>
	</SelectDrawer>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index";

.drawer-container {
	.right {
		width: 100%;
	}
}
</style>
