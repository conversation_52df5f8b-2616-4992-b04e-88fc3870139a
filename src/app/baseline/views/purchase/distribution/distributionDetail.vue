<!-- 采购分包 查看 -->
<script lang="ts" setup>
import { watch, ref } from "vue"
import { DictApi } from "@/app/baseline/api/dict"

import { fileBusinessType } from "../../../api/dict"
import { BaseLineSysApi } from "../../../api/system"
import { PurchaseDistributionApi } from "../../../api/purchase/purchaseDistribution"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import CostTag from "@/app/baseline/views/components/costTag.vue"
import lineTag from "@/app/baseline/views/components/lineTag.vue"

import TableFile from "../../components/tableFile.vue"
import XEUtils from "xe-utils"
import { IModalType } from "@/app/baseline/utils/types/common"
import {
	batchFormatterNumView,
	getModalTypeLabel,
	toFixedTwo
} from "@/app/baseline/utils"
import { useDictInit } from "../../components/dictBase"
import { useTbInit } from "../../components/tableBase"
import { PurchaseDistributionItemApi } from "@/app/baseline/api/purchase/purchaseDistributionItem"
import { getRealLength, setString } from "@/app/baseline/utils/validate"

interface Props {
	id?: string
	mode?: IModalType
	footerBtnVisible?: boolean
}

const props = withDefaults(defineProps<Props>(), {
	mode: IModalType.view,
	footerBtnVisible: true
})

const emit = defineEmits<{
	(e: "update"): void
	(e: "close"): void
}>()

const { dictOptions, getDictByCodeList } = useDictInit()

const drawerLoading = ref(false)

const formData = ref<{ [propName: string]: any }>({})

/**
 * 能否编辑扩展信息
 */
const canEditExtra = computed(() => Boolean(formData.value?.id || props.id))

const formBtnLoading = ref(false)
const lineList = ref<[]>([])

/**
 * 左侧 title 配置
 */
const drawerLeftTitle = computed(() => ({
	name: [getModalTypeLabel(props.mode, "采购分包")],
	icon: ["fas", "square-share-nodes"]
}))

/**
 * 右侧 title 配置
 */
const drawerRightTitle = {
	name: ["扩展信息"],
	icon: ["fas", "square-share-nodes"]
}

const formBtnListRight = [{ name: "取消", icon: ["fas", "circle-minus"] }]
const tabList = ref(["物资明细", "相关附件"])

const descOptions = [
	{ label: "分包编号", name: "code" },
	{ label: "分包名称", name: "label" },
	{ label: "采购计划号", name: "planPurchaseCode" },
	{ label: "采购计划名称", name: "planPurchaseLabel" },
	{ label: "计划年度", name: "planPurchaseYear" },
	{ label: "采购员", name: "purchaseUserName" },
	{ label: "线路", name: "lineId" },
	{ label: "分包金额", name: "sumAmount", type: "money" },
	{ label: "备注说明", name: "remark", needTooltip: true },
	{ label: "创建人", name: "createdBy_view" },
	{ label: "创建时间", name: "createdDate" }
]

/**
 * 获取详情
 */
async function getDetail() {
	if (!props.id) {
		return false
	}
	drawerLoading.value = true
	try {
		const res = await PurchaseDistributionApi.getPurchaseDistribution(
			props.id || formData.value.id
		)
		formData.value = { ...res }
	} finally {
		drawerLoading.value = false
	}
}

//扩展栏标签页切换
const activeTab = ref<number>(0)
const handleTabChange = (index: number) => {
	activeTab.value = index

	if (index === 0) {
		fetchParam.value = {
			distributionId: props.id || formData.value.id,
			sord: "desc",
			sidx: "createdDate"
		}
		pageSize.value = 20
		getTableData()
	}
}

/**
 * 获取线路列表
 */
function getListList(): Promise<null> {
	return new Promise<null>((resolve) => {
		BaseLineSysApi.getLineList()
			.then((res) => {
				lineList.value = res
			})
			.finally(() => resolve(null))
	})
}
onMounted(async () => {
	getDictByCodeList(["INVENTORY_UNIT", "MATERIAL_NATURE"])

	await getListList()
	await getDetail()

	fetchParam.value.sord = "desc"
	fetchParam.value.sidx = "createdDate"

	if (props.id) {
		fetchParam.value.distributionId = props.id
		getTableData()
	}
})

/**
 * 排序
 * @param column
 * @param prop 字段名称
 * @param order ascending 升序 descending 降序
 */
function handleSortChange(column: any, prop: any, order: any) {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		sord:
			order === "descending" ? "desc" : order === "ascending" ? "asc" : "desc",
		sidx: order
			? prop === "content_code"
				? "materialCode"
				: prop
			: "createdDate" // 排序字段
	}

	fetchTableData()
}

const lastTableData = ref<any[]>([])

const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	currentPage,
	pageTotal,
	pageSize,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = useTbInit()

fetchFunc.value = PurchaseDistributionItemApi.getPurchaseDistributionItemList

tableProp.value = [
	{
		label: "物资编码",
		prop: "content_code",
		width: 130,
		fixed: "left",
		sortable: true
	},
	{ label: "物资名称", prop: "content_label", width: 150 },
	{ label: "规格型号", prop: "content_version", width: 150 },
	{
		label: "技术参数",
		prop: "content_technicalParameter",
		minWidth: 200,
		align: "left"
	},
	{ label: "物资性质", prop: "attribute", needSlot: true, width: 120 },
	{ label: "采购单位", prop: "content_buyUnit", needSlot: true, width: 90 },
	{
		label: "预估采购单价",
		prop: "evaluation",
		needSlot: true,
		width: 120,
		align: "right"
	},
	{ label: "需求数量", prop: "planNum_view", align: "right", width: 100 },
	{
		label: "预估金额",
		prop: "planAmount",
		needSlot: true,
		width: 150,
		align: "right"
	},
	{
		label: "库存数量",
		prop: "storeNum",
		needSlot: true,
		align: "right",
		width: 100
	},
	{ label: "安全库存", prop: "safeStock", width: 100, align: "right" },
	{
		label: "采购数量",
		prop: "purchaseNum_view",
		align: "right",
		needSlot: true,
		width: 100,
		fixed: "right"
	},
	{
		label: "采购金额",
		prop: "purchaseAmount",
		needSlot: true,
		align: "right",
		width: 150,
		fixed: "right"
	}
]

/**
 * 补丁
 * 监听tableData 重组lastTableData
 */
watch([tableData], () => {
	XEUtils.map(tableData.value as any, (_d: { [propName: string]: any }) => {
		if (_d.content) {
			Array.from(Object.keys(_d?.content))?.map(
				(_k) => (_d[`content_${_k}`] = _d.content[_k])
			)
		}
	})
	lastTableData.value = tableData.value

	batchFormatterNumView(lastTableData.value, undefined, 0)
})

const queryArrList = computed(() => [
	{
		name: "物资编码",
		key: "materialCode",
		placeholder: "请输入物资编码",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资名称",
		key: "materialLabel",
		placeholder: "请输入物资名称",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "规格型号",
		key: "version",
		placeholder: "请输入规格型号",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资性质",
		key: "attribute",
		type: "select",
		children: dictOptions.value.MATERIAL_NATURE,
		placeholder: "请选择物资性质"
	}
])

const getTableData = (data?: { [propName: string]: any }) => {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		...data
	}
	fetchTableData()
}
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<!-- 左侧表单区域 -->
		<div class="drawer-column left" style="width: 310px">
			<div class="rows">
				<Title :title="drawerLeftTitle" />
				<el-scrollbar>
					<el-descriptions
						size="small"
						:column="1"
						:border="true"
						class="content"
					>
						<el-descriptions-item
							v-for="(el, index) in descOptions"
							label-align="center"
							:label="el.label"
							:key="index"
						>
							<span v-if="el.type == 'money'">
								<cost-tag :value="formData[el.name]" />
							</span>
							<span v-else-if="el.name == 'lineId'">
								<line-tag :options="lineList" :value="formData.lineId" />
							</span>
							<span v-else-if="el.needTooltip">
								<el-tooltip
									effect="dark"
									:content="formData?.[el.name]"
									:disabled="
										getRealLength(formData?.[el.name]) <= 100 ? true : false
									"
								>
									{{
										getRealLength(formData?.[el.name]) > 100
											? setString(formData?.[el.name], 100)
											: formData?.[el.name] || "---"
									}}
								</el-tooltip>
							</span>
							<span v-else>
								{{ formData[el.name] ? formData[el.name] : "---" }}
							</span>
						</el-descriptions-item>
					</el-descriptions>
				</el-scrollbar>
			</div>
		</div>
		<!-- 右侧table区域 -->
		<div
			class="drawer-column right"
			style="width: calc(100% - 310px)"
			:class="{ pdr10: !footerBtnVisible }"
		>
			<div class="rows">
				<Title :title="drawerRightTitle">
					<Tabs class="tabs" :tabs="tabList" @on-tab-change="handleTabChange" />
				</Title>
				<div
					class="mat-tab"
					style="height: calc(100% - 40px)"
					v-if="activeTab === 0"
				>
					<Query
						:queryArrList="queryArrList"
						@getQueryData="getTableData"
						style="margin: 10px 0px -10px 10px !important"
					/>
					<el-scrollbar>
						<PitayaTable
							ref="tableRef"
							:columns="tableProp"
							:table-data="lastTableData"
							:need-index="true"
							:need-pagination="true"
							:single-select="false"
							:need-selection="false"
							:total="pageTotal"
							@onSelectionChange="selectedTableList = $event"
							@on-current-page-change="onCurrentPageChange"
							:table-loading="tableLoading"
							@on-table-sort-change="handleSortChange"
						>
							<!-- 物资性质 -->
							<template #attribute="{ rowData }">
								<dict-tag
									:options="DictApi.getMatAttr()"
									:value="rowData.attribute"
								/>
							</template>

							<template #content_buyUnit="{ rowData }">
								<dict-tag
									:value="rowData.content_buyUnit"
									:options="dictOptions.INVENTORY_UNIT"
								/>
							</template>
							<template #evaluation="{ rowData }">
								<cost-tag :value="rowData.evaluation" />
							</template>
							<template #purchaseAmount="{ rowData }">
								<span class="num-blue">
									<cost-tag :value="rowData.purchaseAmount" />
								</span>
							</template>
							<template #purchaseNum_view="{ rowData }">
								<span class="num-blue">
									{{ rowData.purchaseNum_view }}
								</span>
							</template>

							<!-- 库存数量 -->
							<template #storeNum="{ rowData }">
								{{ toFixedTwo(rowData.storeNum) }}
							</template>

							<template #planAmount="{ rowData }">
								<cost-tag :value="rowData.planAmount" />
							</template>
						</PitayaTable>
					</el-scrollbar>
				</div>

				<el-scrollbar v-else-if="activeTab === 1">
					<TableFile
						ref="refFileTable"
						:mod="props.mode"
						:businessId="props.id"
						:businessType="fileBusinessType.purchaseDistribution"
						:needPage="true"
					/>
				</el-scrollbar>
			</div>
			<ButtonList
				v-if="props.footerBtnVisible"
				class="footer"
				:button="formBtnListRight"
				:loading="formBtnLoading"
				@on-btn-click="emit('close')"
			/>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.num-blue {
	color: $---color-info2;
}
</style>
