<script lang="ts" setup>
import { DictApi } from "@/app/baseline/api/dict"
import { useTbInit } from "@/app/baseline/views/components/tableBase"
import costTag from "@/app/baseline/views/components/costTag.vue"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import { defineProps, ref, onMounted } from "vue"
import XEUtils from "xe-utils"
import { useDictInit } from "../../components/dictBase"
import { PurchaseDistributionItemApi } from "@/app/baseline/api/purchase/purchaseDistributionItem"
import { batchFormatterNumView, toFixedTwo } from "@/app/baseline/utils"
import { map } from "lodash-es"
const { dictOptions, dictFilter, getDictByCodeList } = useDictInit()
export interface Props {
	id: string //采购划ID
	code: string //采购划号
	pid: string //采购分包id
}

const props = withDefaults(defineProps<Props>(), {})

const tbBtnLoading = ref(false)
const {
	tableData,
	tableProp,
	tableRef,
	tableLoading,
	currentPage,
	pageTotal,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = useTbInit()
tableProp.value = [
	{ label: "物资编码", prop: "content_code", width: 130, fixed: "left" },
	{ label: "物资名称", prop: "content_label", width: 150 },
	{ label: "规格型号", prop: "content_version", width: 150 },
	{
		label: "技术参数",
		prop: "content_technicalParameter",
		minWidth: 200,
		align: "left"
	},
	{ label: "物资性质", prop: "attribute", needSlot: true, width: 120 },
	{ label: "采购单位", prop: "content_buyUnit", needSlot: true, width: 90 },
	{
		label: "预估采购单价",
		prop: "evaluation",
		needSlot: true,
		width: 150,
		align: "right"
	},
	{
		label: "预估金额",
		prop: "planAmount",
		needSlot: true,
		align: "right",
		width: 200
	},
	{ label: "需求数量", prop: "num_view", align: "right", width: 100 },
	{
		label: "库存数量",
		prop: "storeNum",
		needSlot: true,
		align: "right",
		width: 100
	},
	{ label: "安全库存", prop: "content_safeStock", width: 100 },
	{
		label: "采购数量",
		prop: "purchaseNum_view",
		needSlot: true,
		width: 90,
		align: "right",
		fixed: "right"
	},
	{
		label: "采购金额",
		prop: "purchaseAmount",
		needSlot: true,
		width: 150,
		align: "right",
		fixed: "right"
	}
]

const emits = defineEmits<{
	(e: "save", rows: any[]): void
	(e: "close"): void
}>()

fetchFunc.value = (param: Record<string, any>) => {
	return new Promise((resolve) => {
		PurchaseDistributionItemApi.getPurchasePlanItemList({
			planPurchaseId: props.id as any,
			...param
		}).then((res) => {
			map(res.rows, (v: Record<string, any>) => {
				if (v.content)
					Array.from(Object.keys(v?.content))?.map(
						(c) => (v[`content_${c}`] = v.content[c])
					)
			})
			resolve(res)
		})
	})
}

watch(tableData, () => {
	batchFormatterNumView(tableData.value as any[], undefined, 0)
})

const rightTitle = ref<any>({
	name: ["选择物资"],
	icon: ["fas", "square-share-nodes"]
})
//查询
const queryArrList = computed(() => [
	{
		name: "物资编码",
		key: "materialCode",
		placeholder: "请输入物资编码",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资名称",
		key: "materialLabel",
		placeholder: "请输入物资名称",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "规格型号",
		key: "version",
		placeholder: "请输入规格型号",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资性质",
		key: "attribute",
		type: "select",
		children: dictOptions.value.MATERIAL_NATURE,
		placeholder: "请选择物资性质"
	}
])
const lastTableData = ref<any[]>([])
const formBtnList = ref([
	{ name: "取消", icon: ["fas", "circle-minus"] },
	{ name: "保存", icon: ["fas", "floppy-disk"] }
])
const getQueryData = (data?: { [propName: string]: any }) => {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		...data
	}

	fetchTableData()
}

async function onFormBtnList(btnName?: string) {
	if (btnName == "保存") {
		if (selectedTableList.value.length <= 0) {
			ElMessage.warning("请选择需要添加的物资")
			return false
		}

		tbBtnLoading.value = true
		try {
			await new Promise((resolve, reject) => {
				try {
					emits("save", selectedTableList.value)
				} catch (error) {
					// 如果事件处理出错，手动拒绝Promise
					reject(error)
				}
			})
		} finally {
			tbBtnLoading.value = false
		}
	} else {
		emits("close")
	}
}

function showCountAmount() {
	let amount = 0
	selectedTableList.value.forEach((item) => {
		const purchaseAmount = parseFloat(item.purchaseAmount)
		if (!isNaN(purchaseAmount)) {
			amount += purchaseAmount
		}
	})
	return (amount * 1.0).toFixed(5)
}
onMounted(async () => {
	fetchParam.value = {
		planPurchaseId: props.id,
		id: props.pid,
		sord: "desc",
		sidx: "createdDate"
	}

	getDictByCodeList(["INVENTORY_UNIT", "MATERIAL_NATURE"])
	rightTitle.value.name = ["选择物资【采购计划号：" + props.code + "】"]
	getQueryData()
})
</script>
<template>
	<div class="drawer-container">
		<!-- 右侧table区域 -->
		<div class="drawer-column">
			<div class="rows">
				<Title :title="rightTitle" />
				<Query
					:queryArrList="queryArrList"
					@getQueryData="getQueryData"
					class="custom-q"
				/>
				<PitayaTable
					ref="tableRef"
					:columns="tableProp"
					:table-data="tableData"
					:total="pageTotal"
					@onSelectionChange="selectedTableList = $event"
					@on-current-page-change="onCurrentPageChange"
					:table-loading="tableLoading"
					:need-index="true"
					:need-pagination="true"
					:single-select="false"
					:need-selection="true"
				>
					<!-- 物资性质 -->
					<template #attribute="{ rowData }">
						<dict-tag
							:options="DictApi.getMatAttr()"
							:value="rowData.attribute"
						/>
					</template>

					<!-- 采购单位 -->
					<template #content_buyUnit="{ rowData }">
						{{
							dictFilter("INVENTORY_UNIT", rowData.content_buyUnit)
								?.subitemName || "---"
						}}
					</template>

					<!-- 预估采购单价 -->
					<template #evaluation="{ rowData }">
						<cost-tag :value="rowData.evaluation" />
					</template>

					<template #storeNum="{ rowData }">
						{{ toFixedTwo(rowData.storeNum) }}
					</template>

					<!-- 采购数量 -->
					<template #purchaseNum_view="{ rowData }">
						<span class="num-blue">{{ rowData.purchaseNum_view }}</span>
					</template>

					<!-- 采购金额 -->
					<template #purchaseAmount="{ rowData }">
						<span class="num-blue">
							<cost-tag :value="rowData.purchaseAmount" />
						</span>
					</template>
					<template #planAmount="{ rowData }">
						<cost-tag :value="rowData.planAmount" />
					</template>
				</PitayaTable>
				<!-- <div class="tip" v-if="selectedTableList.length > 0">
					已分包{{ selectedTableList.length }}项物资，金额：
					<cost-tag :value="showCountAmount()" />
				</div> -->
			</div>
			<div
				class="footer"
				style="display: flex; justify-content: space-between"
				v-if="selectedTableList.length > 0"
			>
				<div class="blue-text">
					已分包{{ selectedTableList.length }}项物资，金额：
					<cost-tag :value="showCountAmount()" />
				</div>
				<ButtonList
					class="end"
					:button="formBtnList"
					:loading="tbBtnLoading"
					@on-btn-click="onFormBtnList"
				/>
			</div>

			<ButtonList
				v-else
				style="padding-right: 10px"
				class="footer end"
				:button="formBtnList"
				:loading="tbBtnLoading"
				@on-btn-click="onFormBtnList"
			/>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.blue-text {
	color: $---color-info2;
}

.drawer-container {
	.drawer-column {
		width: 100%;
	}
	.custom-q {
		margin: 10px 0px -10px 10px !important;
	}
	.tip {
		font-size: 14px;
		color: $---font-color-4;
		position: absolute;
		right: 20px;
		top: 50px;
	}
	.num-blue {
		color: $---color-info2;
	}
}
</style>
