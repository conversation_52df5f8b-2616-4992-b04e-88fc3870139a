<!-- 采购分包 新建/编辑 -->
<script lang="ts" setup>
import { watch, ref, reactive } from "vue"
import { ElMessage, type FormInstance, type FormRules } from "element-plus"
import { DictApi } from "@/app/baseline/api/dict"

import { FormElementType } from "../../components/define"
import FormElement from "../../components/formElement.vue"
import { fileBusinessType } from "../../../api/dict"
import { BaseLineSysApi } from "../../../api/system"
import { PurchaseDistributionApi } from "../../../api/purchase/purchaseDistribution"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import CostTag from "@/app/baseline/views/components/costTag.vue"

import TableFile from "../../components/tableFile.vue"
import selectPurchasePlan from "./selectPurchasePlan.vue"
import XEUtils from "xe-utils"
import PurchaseUser from "@/app/baseline/views/purchase/components/selectPurchaseUser.vue"
import {
	IIdempotentTokenType,
	IIdempotentTokenTypePre,
	IModalType
} from "@/app/baseline/utils/types/common"
import {
	getIdempotentToken,
	requiredValidator
} from "@/app/baseline/utils/validate"
import {
	batchFormatterNumView,
	getModalTypeLabel,
	toFixedTwo,
	toMoney
} from "@/app/baseline/utils"
import { useMessageBoxInit } from "../../components/messageBox"
import { useDictInit } from "../../components/dictBase"
import { useTbInit } from "../../components/tableBase"
import { PurchaseDistributionItemApi } from "@/app/baseline/api/purchase/purchaseDistributionItem"
import { modalSize } from "@/app/baseline/utils/layout-config"
import matManualList from "./matManualList.vue"
import { AddPurchaseDistributionItemRequest } from "@/app/baseline/api/defines"

interface Props {
	id?: string
	mode?: IModalType
}

const props = withDefaults(defineProps<Props>(), {
	mode: IModalType.create
})

const emit = defineEmits<{
	(e: "update"): void
	(e: "close"): void
}>()

const { showDelConfirm, showWarnConfirm } = useMessageBoxInit()
const { dictOptions, getDictByCodeList } = useDictInit()

/**
 * 保存旧的表单数据
 */
const oldFormData = ref<string>()

const drawerLoading = ref(false)

const formData = ref<{ [propName: string]: any }>({})

/**
 * 能否编辑扩展信息
 */
const canEditExtra = computed(() => Boolean(formData.value?.id || props.id))

const purchasePlanSelectorVisible = ref(false)
const formBtnLoading = ref(false)
const lineList = ref<[]>([])
const matSelectorVisible = ref(false)

/**
 * 左侧 title 配置
 */
const drawerLeftTitle = computed(() => ({
	name: [
		getModalTypeLabel(
			canEditExtra.value ? IModalType.edit : IModalType.create,
			"采购分包"
		)
	],
	icon: ["fas", "square-share-nodes"]
}))

/**
 * 右侧 title 配置
 */
const drawerRightTitle = {
	name: ["扩展信息"],
	icon: ["fas", "square-share-nodes"]
}

const formBtnList = [
	{ name: "取消", icon: ["fas", "circle-minus"] },
	{ name: "保存草稿", icon: ["fas", "floppy-disk"] }
]
const formBtnListRight = computed(() => [
	{
		name: "提交审核",
		icon: ["fas", "circle-check"],
		disabled: pageTotal.value > 0 ? false : true
	}
])
const tabList = ref(["物资明细", "相关附件"])

const purchaseUserNameSelectorVisible = ref(false)
const formEl = computed<FormElementType[][]>(() => [
	[{ label: "分包名称", name: "label", maxlength: 50 }],
	[
		{
			label: "采购计划",
			name: "planPurchaseCode",
			type: "drawer",
			clickApi: () => {
				purchasePlanSelectorVisible.value = true
			},
			disabled: false
		}
	],
	[
		{
			label: "采购计划名称",
			name: "planPurchaseLabel",
			type: "input",
			disabled: true
		}
	],
	[
		{
			label: "指定采购员",
			name: "purchaseUserName",
			type: "drawer",
			clickApi: () => {
				purchaseUserNameSelectorVisible.value = true
			}
		}
	],
	[
		{
			label: "线路",
			name: "lineId_view",
			vname: "lineId",
			maxlength: 50,
			type: "treeSelect",
			treeApi: BaseLineSysApi.getLineList
		}
	],
	[
		{
			label: "分包金额(元)",
			name: "sumAmount_view",
			type: "input",
			disabled: true
		}
	],
	[
		{
			label: "备注说明",
			name: "remark",
			maxlength: 200,
			rows: "5",
			type: "textarea"
		}
	]
])
const formRef = ref<FormInstance>()

const formRules = reactive<FormRules<typeof formData.value>>({
	label: requiredValidator("分包名称"), //[{ required: true, message: "计划年度不能为空", trigger: "change" }],
	planPurchaseCode: requiredValidator("采购计划"),
	purchaseUserName: requiredValidator("指定采购员")
})

/**
 * 选择采购员保存
 * @param selectedRow
 */
const saveSelectedUser = (selectedRow: any[]) => {
	if (selectedRow.length > 0) {
		const arrIds = selectedRow.map((item) => item.username).toString()
		const arrNames = selectedRow.map((item) => item.realname).toString()
		formData.value.purchaseUserId = arrIds
		formData.value.purchaseUserName = arrNames
	}
	purchaseUserNameSelectorVisible.value = false
}

/**
 * 获取详情
 */
async function getDetail() {
	if (!props.id && !formData.value.id) {
		return false
	}
	drawerLoading.value = true
	try {
		const res = await PurchaseDistributionApi.getPurchaseDistribution(
			props.id || formData.value.id
		)
		formData.value = { ...res }
		formData.value["sumAmount_view"] = toMoney(res.sumAmount as any)

		oldFormData.value = JSON.stringify(formData)
	} finally {
		drawerLoading.value = false
	}
}
// 左侧按钮点击
const onFormBtnList = async (btnName: string | undefined) => {
	if (btnName === "保存草稿") {
		handleSaveDraft()
	} else if (btnName == "提交审核") {
		handleSubmit()
	} else {
		emit("close")
	}
}

/**
 * 处理保存草稿（包含新增和编辑）
 */
async function handleSaveDraft() {
	if (!formRef.value) {
		return
	}

	formRef.value.validate(async (valid: any) => {
		if (!valid) {
			return
		}
		formBtnLoading.value = true
		// 表单校验通过
		try {
			const api = canEditExtra.value
				? PurchaseDistributionApi.updatePurchaseDistribution
				: PurchaseDistributionApi.addPurchaseDistribution

			let idempotentToken = ""
			if (!canEditExtra.value) {
				idempotentToken = getIdempotentToken(
					IIdempotentTokenTypePre.apply,
					IIdempotentTokenType.purchaseDistribution
				)
			}
			const r = await api(formData.value as any, idempotentToken)

			formData.value.id = r.id
			formData.value["sumAmount_view"] = toMoney(r.sumAmount as any)

			fetchParam.value.distributionId = r.id

			oldFormData.value = JSON.stringify(formData.value)

			getTableData()
			ElMessage.success("操作成功")
			emit("update")
		} finally {
			formBtnLoading.value = false
		}
	})
}

/**
 * 处理提交审核
 *
 * 需要表单校验
 */
function handleSubmit() {
	if (!formRef.value) {
		return
	}

	formRef.value.validate(async (valid: any) => {
		if (!valid) {
			return
		}

		await showWarnConfirm("请确认是否提交本次数据？")

		formBtnLoading.value = true
		drawerLoading.value = true

		try {
			// 如果主表有修改，则先更新主表数据
			if (oldFormData.value != JSON.stringify(formData.value)) {
				await PurchaseDistributionApi.updatePurchaseDistribution({
					...formData.value
				} as any)
			}

			const idempotentToken = getIdempotentToken(
				IIdempotentTokenTypePre.other,
				IIdempotentTokenType.purchaseDistribution,
				formData.value.id
			)

			await PurchaseDistributionApi.publishPurchaseDistribution(
				formData.value as any,
				idempotentToken
			)

			ElMessage.success("操作成功")
			emit("update")
			emit("close")
		} finally {
			drawerLoading.value = false
			formBtnLoading.value = false
		}
	})
}

// 选择供应商弹窗关闭
const onCloseDrawer = (refresh: boolean, selectRow: any) => {
	if (refresh) {
		formData.value.planPurchaseId = selectRow.id
		formData.value.planPurchaseLabel = selectRow.label
		formData.value.planPurchaseCode = selectRow.code
		purchasePlanSelectorVisible.value = false
	}
}

//扩展栏标签页切换
const activeTab = ref<number>(0)
const handleTabChange = (index: number) => {
	activeTab.value = index

	if (index === 0) {
		fetchParam.value = {
			distributionId: props.id || formData.value.id,
			sord: "desc",
			sidx: "createdDate"
		}
		pageSize.value = 20
		getTableData()
	}
}

/**
 * 获取线路列表
 */
function getListList(): Promise<null> {
	return new Promise<null>((resolve) => {
		BaseLineSysApi.getLineList()
			.then((res) => {
				lineList.value = res
			})
			.finally(() => resolve(null))
	})
}
onMounted(async () => {
	getDictByCodeList(["INVENTORY_UNIT", "MATERIAL_NATURE"])

	getListList()
	getDetail()

	fetchParam.value.sord = "desc"
	fetchParam.value.sidx = "createdDate"

	if (props.id) {
		fetchParam.value.distributionId = props.id
		getTableData()
	}
})

/**
 * 排序
 * @param column
 * @param prop 字段名称
 * @param order ascending 升序 descending 降序
 */
function handleSortChange(column: any, prop: any, order: any) {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		sord:
			order === "descending" ? "desc" : order === "ascending" ? "asc" : "desc",
		sidx: order
			? prop === "content_code"
				? "materialCode"
				: prop
			: "createdDate" // 排序字段
	}

	fetchTableData()
}

const lastTableData = ref<any[]>([])
const tbBtnLoading = ref(false)
const tbBtnConf = computed(() => {
	return [
		{
			name: "添加物资",
			icon: ["fas", "circle-plus"]
		},
		{
			name: "批量移除",
			icon: ["fas", "trash-alt"],
			disabled: selectedTableList.value?.length > 0 ? false : true
		}
	]
})
const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	currentPage,
	pageTotal,
	pageSize,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = useTbInit()

fetchFunc.value = PurchaseDistributionItemApi.getPurchaseDistributionItemList

tableProp.value = [
	{
		label: "物资编码",
		prop: "content_code",
		width: 130,
		fixed: "left",
		sortable: true
	},
	{ label: "物资名称", prop: "content_label", width: 150 },
	{ label: "规格型号", prop: "content_version", width: 150 },
	{
		label: "技术参数",
		prop: "content_technicalParameter",
		minWidth: 200,
		align: "left"
	},
	{ label: "物资性质", prop: "attribute", needSlot: true, width: 120 },
	{ label: "采购单位", prop: "content_buyUnit", needSlot: true, width: 90 },
	{
		label: "预估采购单价",
		prop: "evaluation",
		needSlot: true,
		width: 120,
		align: "right"
	},
	{ label: "需求数量", prop: "planNum_view", align: "right", width: 100 },
	{
		label: "预估金额",
		prop: "planAmount",
		needSlot: true,
		width: 150,
		align: "right"
	},
	{
		label: "库存数量",
		prop: "storeNum",
		needSlot: true,
		align: "right",
		width: 100
	},
	{ label: "安全库存", prop: "safeStock", width: 100, align: "right" },
	{
		label: "采购数量",
		prop: "purchaseNum_view",
		align: "right",
		needSlot: true,
		width: 100,
		fixed: "right"
	},
	{
		label: "采购金额",
		prop: "purchaseAmount",
		needSlot: true,
		align: "right",
		width: 150,
		fixed: "right"
	},
	{
		label: "操作",
		width: 100,
		prop: "operations",
		fixed: "right",
		needSlot: true
	}
]

/**
 * 补丁
 * 监听tableData 重组lastTableData
 */
watch([tableData], () => {
	XEUtils.map(tableData.value as any, (_d: { [propName: string]: any }) => {
		if (_d.content) {
			Array.from(Object.keys(_d?.content))?.map(
				(_k) => (_d[`content_${_k}`] = _d.content[_k])
			)
		}
	})
	lastTableData.value = tableData.value

	batchFormatterNumView(lastTableData.value, undefined, 0)
})

const queryArrList = computed(() => [
	{
		name: "物资编码",
		key: "materialCode",
		placeholder: "请输入物资编码",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资名称",
		key: "materialLabel",
		placeholder: "请输入物资名称",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "规格型号",
		key: "version",
		placeholder: "请输入规格型号",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资性质",
		key: "attribute",
		type: "select",
		children: dictOptions.value.MATERIAL_NATURE,
		placeholder: "请选择物资性质"
	}
])

const getTableData = (data?: { [propName: string]: any }) => {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		...data
	}
	fetchTableData()
}

async function tbBtnAction(btnName?: string) {
	if (btnName === "添加物资") {
		matSelectorVisible.value = true
	} else if (btnName === "批量移除") {
		await showWarnConfirm("确定要移除选中数据吗？")

		tbBtnLoading.value = true

		try {
			const ids = selectedTableList.value.map((item) => item.id).toString()
			await PurchaseDistributionItemApi.deletePurchaseDistributionItem(ids)
			ElMessage.success("移除成功")
			fetchTableData()
			getDetail()
			emit("update")
		} finally {
			tbBtnLoading.value = false
		}
	}
}

/**
 * 选择 物资保存
 * @param rows
 */
async function handleSaveMat(rows: any[]) {
	const params = rows.map((item) => ({
		distributionId: formData.value.id,
		purchaseNum: item.purchaseNum,
		evaluation: item.evaluation,
		materialId: item.materialId,
		num: item.num,
		storeNum: item.storeNum,
		safeStock: item.content_safeStock,
		planItemId: item.planItemId,
		planPurchaseMaterialItemId: item.id,
		materialCode: item.content_code,
		materialLabel: item.content_label,
		version: item.content_version,
		attribute: item.attribute
	}))

	const idempotentToken = getIdempotentToken(
		IIdempotentTokenTypePre.other,
		IIdempotentTokenType.purchaseDistribution,
		formData.value.id
	)

	await PurchaseDistributionItemApi.addBatchPurchaseDistributionItem(
		params as AddPurchaseDistributionItemRequest[],
		idempotentToken
	)
	matSelectorVisible.value = false
	getTableData()
	getDetail()
	emit("update")
}
/**
 * 删除行
 * @param row
 */
async function handleMatRowDel(row: any) {
	await showDelConfirm()
	await PurchaseDistributionItemApi.deletePurchaseDistributionItem(row.id)
	ElMessage.success("操作成功")
	fetchTableData()
	getDetail()
	emit("update")
}
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<!-- 左侧表单区域 -->
		<div class="drawer-column left" style="width: 310px">
			<div class="rows">
				<Title :title="drawerLeftTitle" />
				<el-scrollbar>
					<el-form
						class="content"
						:model="formData"
						:rules="formRules"
						ref="formRef"
						label-position="top"
						label-width="100px"
					>
						<FormElement :form-element="formEl" :form-data="formData" />
					</el-form>
				</el-scrollbar>
			</div>
			<ButtonList
				class="footer"
				:button="formBtnList"
				:loading="formBtnLoading"
				@on-btn-click="onFormBtnList"
			/>
		</div>
		<!-- 右侧table区域 -->
		<div
			class="drawer-column right"
			:class="canEditExtra ? '' : ' disabled'"
			style="width: calc(100% - 310px)"
		>
			<div class="rows">
				<Title
					:title="drawerRightTitle"
					style="justify-content: flex-start; margin-left: 10px"
				>
					<Tabs class="tabs" :tabs="tabList" @on-tab-change="handleTabChange" />
				</Title>

				<div
					class="mat-tab"
					style="height: calc(100% - 40px)"
					v-if="activeTab === 0"
				>
					<Query
						:queryArrList="queryArrList"
						@getQueryData="getTableData"
						style="margin: 10px 0px -10px 10px !important"
					/>
					<el-scrollbar>
						<PitayaTable
							ref="tableRef"
							:columns="tableProp"
							:table-data="lastTableData"
							:need-index="true"
							:need-pagination="true"
							:single-select="false"
							:need-selection="true"
							:total="pageTotal"
							@onSelectionChange="selectedTableList = $event"
							@on-current-page-change="onCurrentPageChange"
							:table-loading="tableLoading"
							@on-table-sort-change="handleSortChange"
						>
							<!-- 物资性质 -->
							<template #attribute="{ rowData }">
								<dict-tag
									:options="DictApi.getMatAttr()"
									:value="rowData.attribute"
								/>
							</template>

							<template #content_buyUnit="{ rowData }">
								<dict-tag
									:value="rowData.content_buyUnit"
									:options="dictOptions.INVENTORY_UNIT"
								/>
							</template>
							<template #evaluation="{ rowData }">
								<cost-tag :value="rowData.evaluation" />
							</template>
							<template #purchaseAmount="{ rowData }">
								<span class="num-blue">
									<cost-tag :value="rowData.purchaseAmount" />
								</span>
							</template>
							<template #purchaseNum_view="{ rowData }">
								<span class="num-blue">
									{{ rowData.purchaseNum_view }}
								</span>
							</template>

							<!-- 库存数量 -->
							<template #storeNum="{ rowData }">
								{{ toFixedTwo(rowData.storeNum) }}
							</template>

							<template #planAmount="{ rowData }">
								<cost-tag :value="rowData.planAmount" />
							</template>
							<template #operations="{ rowData }">
								<el-button v-btn link @click.stop="handleMatRowDel(rowData)">
									<font-awesome-icon :icon="['fas', 'trash-can']" />
									<span class="table-inner-btn">移除</span>
								</el-button>
							</template>
							<template #footerOperateLeft>
								<ButtonList
									class="btn-list"
									:is-not-radius="true"
									:button="tbBtnConf"
									:loading="tbBtnLoading"
									@on-btn-click="tbBtnAction"
								/>
							</template>
						</PitayaTable>
					</el-scrollbar>
				</div>
				<el-scrollbar style="height: 100%" v-else-if="activeTab === 1">
					<TableFile
						ref="refFileTable"
						:mod="props.mode"
						:businessId="props.id || formData.id"
						:businessType="fileBusinessType.purchaseDistribution"
						:needPage="true"
					/>
				</el-scrollbar>
			</div>
			<ButtonList
				class="footer"
				:button="formBtnListRight"
				:loading="formBtnLoading"
				@on-btn-click="onFormBtnList"
			/>
		</div>
		<!-- 弹窗 -->
		<Drawer
			:size="1300"
			v-model:drawer="purchasePlanSelectorVisible"
			:destroyOnClose="true"
		>
			<selectPurchasePlan
				:id="formData.planPurchaseId"
				@onSuccess="(selectRow) => onCloseDrawer(true, selectRow)"
				@onClosed="purchasePlanSelectorVisible = false"
			/>
		</Drawer>
		<Drawer
			:size="900"
			v-model:drawer="purchaseUserNameSelectorVisible"
			:destroyOnClose="true"
		>
			<purchase-user
				:user-ids="formData.purchaseUserId"
				@onSave="saveSelectedUser"
				@onClose="purchaseUserNameSelectorVisible = false"
			/>
		</Drawer>

		<Drawer
			:size="modalSize.lg"
			v-model:drawer="matSelectorVisible"
			:destroyOnClose="true"
		>
			<mat-manual-list
				:pid="formData.id"
				:id="formData.planPurchaseId"
				:code="formData.planPurchaseCode"
				@save="handleSaveMat"
				@close="matSelectorVisible = false"
			/>
		</Drawer>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.num-blue {
	color: $---color-info2;
}
</style>
