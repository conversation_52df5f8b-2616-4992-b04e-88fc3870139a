<!-- 厂商管理 -->
<script lang="ts" setup>
import { ref, onMounted, reactive } from "vue"
import { ElMessage } from "element-plus"
import {
	checkPermission,
	isCheckPermission
} from "@/app/platform/utils/permission"
import { DictApi } from "@/app/baseline/api/dict"
import { useTbInit } from "@/app/baseline/views/components/tableBase"
import { powerList } from "@/app/baseline/views/components/define.d"
import supplierEditor from "./supplier/supplierEditor.vue"
import supplierDetail from "./supplier/supplierDetail.vue"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import { PurchaseSupplierApi } from "@/app/baseline/api/purchase/purchaseSupplier"
import { hasEditByBpm, hasViewByBpm } from "../../utils"
import { useMessageBoxInit } from "@/app/baseline/views/components/messageBox"
import { IModalType } from "../../utils/types/common"

const { showDelConfirm } = useMessageBoxInit()

const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	currentPage,
	pageTotal,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = useTbInit()

/**
 * table 列 配置
 */
tableProp.value = [
	{ label: "供应商编码", prop: "code", width: 160, fixed: "left" },
	{ label: "供应商名称", prop: "label", minWidth: 250 },
	{ label: "法定代表人", prop: "legalRepresentative", width: 110 },
	{ label: "联系人", prop: "contact", width: 110 },
	{ label: "联系人手机号", prop: "contactPhoneNumber", width: 160 },
	{ label: "联系地址", prop: "contactAddress", width: 250, align: "left" },
	{ label: "电子邮箱", prop: "contactEmail", width: 160 },
	{ label: "审批状态", prop: "bpmStatus", needSlot: true, width: 90 },
	{ label: "创建人", prop: "createdBy_view", width: 120 },
	{ label: "创建时间", prop: "createdDate", width: 160, sortable: true },
	{
		label: "操作",
		width: 200,
		prop: "operations",
		fixed: "right",
		needSlot: true
	}
]

fetchFunc.value = PurchaseSupplierApi.getPurchaseSupplierList

const rightTitle = {
	name: ["厂商管理"],
	icon: ["fas", "square-share-nodes"]
}
const addBtn = ref([
	{
		name: "新建供应商",
		roles: powerList.needPlanBtnCreate,
		icon: ["fas", "square-plus"]
	}
])

/**
 * 查询条件配置
 */
const queryArrList = reactive([
	{
		name: "供应商编码",
		key: "code",
		type: "input",
		placeholder: "请输入供应商编码"
	},
	{
		name: "供应商名称",
		key: "label",
		type: "input",
		placeholder: "请输入供应商名称"
	},
	{
		name: "联系人",
		key: "contact",
		type: "input",
		placeholder: "请输入联系人"
	},
	/* {
		name: "公司",
		key: "sysCommunityId",
		type: "treeSelect",
		treeApi: BaseLineSysApi.getCompanyAllList,
		placeholder: "请选择公司"
	}, */
	{
		name: "审批状态",
		key: "bpmStatus",
		type: "select",
		placeholder: "请选择",
		children: DictApi.getQueryBpmStatus()
	}
])

/**
 * 查询回调
 * @param data
 */
const getTableData = (data: { [propName: string]: any }) => {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = { ...fetchParam.value, ...data }

	fetchTableData()
}

const supplierEditorId = ref<any>("")
const supplierEditorVisible = ref<boolean>(false)
const supplierDetailVisible = ref<boolean>(false)
const supplierEditorModal = ref(IModalType.create)

/**
 * 新增 操作
 */
function handleRowAdd() {
	supplierEditorId.value = ""
	supplierEditorModal.value = IModalType.create
	supplierEditorVisible.value = true
}

/**
 * 查看
 * @param row
 */
function handleRowView(row: any) {
	supplierEditorModal.value = IModalType.view
	supplierEditorId.value = row.id
	supplierDetailVisible.value = true
}
function handleRowEdit(row: any) {
	supplierEditorModal.value = IModalType.edit
	supplierEditorId.value = row.id
	supplierEditorVisible.value = true
}

/**
 * 删除 操作
 * @param row
 */
async function handleRowDel(row: any) {
	await showDelConfirm()
	await PurchaseSupplierApi.deletePurchaseSupplier(row.id)
	ElMessage.success("操作成功")
	fetchTableData()
}

onMounted(() => {
	fetchParam.value.sord = "desc"
	fetchParam.value.sidx = "createdDate"

	fetchTableData()
})

/**
 * 排序
 * @param column
 * @param prop 字段名称
 * @param order ascending 升序 descending 降序
 */
function handleSortChange(column: any, prop: any, order: any) {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		sord:
			order === "descending" ? "desc" : order === "ascending" ? "asc" : "desc",
		sidx: order ? prop : "createdDate" // 排序字段
	}

	fetchTableData()
}
</script>
<template>
	<div class="app-container">
		<div class="whole-frame">
			<ModelFrame class="top-frame">
				<Query
					:queryArrList="queryArrList"
					:needSingleSelect="true"
					@getQueryData="getTableData"
					class="ml10"
				/>
			</ModelFrame>
			<ModelFrame class="bottom-frame">
				<Title :title="rightTitle" :button="addBtn" @onBtnClick="handleRowAdd">
					<div class="app-tabs-wrapper" />
				</Title>
				<PitayaTable
					ref="tableRef"
					:columns="tableProp"
					:tableData="tableData"
					:total="pageTotal"
					:single-select="true"
					:need-selection="false"
					:need-index="true"
					@onSelectionChange="selectedTableList = $event"
					@on-current-page-change="onCurrentPageChange"
					:table-loading="tableLoading"
					@on-table-sort-change="handleSortChange"
				>
					<template #bpmStatus="{ rowData }">
						<dict-tag
							:options="DictApi.getBpmStatus()"
							:value="rowData.bpmStatus"
						/>
					</template>
					<template #operations="{ rowData }">
						<slot
							v-if="
								isCheckPermission(powerList.purchaseSupplierBtnPreview) ||
								(isCheckPermission(powerList.purchaseSupplierBtnEdit) &&
									hasEditByBpm(rowData.bpmStatus, rowData.createdBy)) ||
								(isCheckPermission(powerList.purchaseSupplierBtnDrop) &&
									hasEditByBpm(rowData.bpmStatus, rowData.createdBy))
							"
						>
							<el-button
								v-btn
								link
								@click="handleRowEdit(rowData)"
								:disabled="
									checkPermission(powerList.purchaseSupplierBtnEdit) &&
									hasEditByBpm(rowData.bpmStatus, rowData.createdBy)
								"
								v-if="
									isCheckPermission(powerList.purchaseSupplierBtnEdit) &&
									hasEditByBpm(rowData.bpmStatus, rowData.createdBy)
								"
							>
								<font-awesome-icon :icon="['fas', 'pen-to-square']" />
								<span class="table-inner-btn">编辑</span>
							</el-button>

							<el-button
								v-btn
								link
								@click="handleRowView(rowData)"
								:disabled="
									checkPermission(powerList.purchaseSupplierBtnPreview)
								"
								v-if="isCheckPermission(powerList.purchaseSupplierBtnPreview)"
							>
								<font-awesome-icon :icon="['fas', 'eye']" />
								<span class="table-inner-btn">查看</span>
							</el-button>

							<el-button
								v-btn
								link
								@click="handleRowDel(rowData)"
								:disabled="
									checkPermission(powerList.purchaseSupplierBtnDrop) &&
									hasEditByBpm(rowData.bpmStatus, rowData.createdBy)
								"
								v-if="
									isCheckPermission(powerList.purchaseSupplierBtnDrop) &&
									hasEditByBpm(rowData.bpmStatus, rowData.createdBy)
								"
							>
								<font-awesome-icon :icon="['fas', 'trash-can']" />
								<span class="table-inner-btn">移除</span>
							</el-button>
						</slot>
						<slot v-else>---</slot>
					</template>
				</PitayaTable>

				<!-- 新增/编辑 -->
				<Drawer
					size="350"
					v-model:drawer="supplierEditorVisible"
					:destroyOnClose="true"
				>
					<supplier-editor
						:id="supplierEditorId"
						:model="supplierEditorModal"
						@close="supplierEditorVisible = false"
						@update="fetchTableData"
					/>
				</Drawer>

				<!-- 查看 -->
				<Drawer
					size="350"
					v-model:drawer="supplierDetailVisible"
					:destroyOnClose="true"
				>
					<supplier-detail
						:id="supplierEditorId"
						:model="supplierEditorModal"
						@close="supplierDetailVisible = false"
						@update="fetchTableData"
					/>
				</Drawer>
			</ModelFrame>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
