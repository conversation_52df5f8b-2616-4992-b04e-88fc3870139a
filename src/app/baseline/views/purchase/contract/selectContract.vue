<script lang="ts" setup>
import { ElMessage } from "element-plus"
import { onMounted, ref } from "vue"
import { appStatus } from "../../../api/dict"
import { useDictInit } from "@/app/baseline/views/components/dictBase"

import CostTag from "@/app/baseline/views/components/costTag.vue"
import SelectDrawer from "@/app/baseline/views/components/selectDrawer.vue"
import { PurchaseContractApi } from "@/app/baseline/api/purchase/purchaseContract"

interface Props {
	id: string
	userIds: string
	bpmStatus?: number | undefined
}

const props = withDefaults(defineProps<Props>(), {
	userIds: undefined
})
const emits = defineEmits(["onSuccess", "onClosed"])

const { dictOptions, getDictByCodeList, dictFilter } = useDictInit()
const selectId = props.id ? [props.id] : []
const drawerLoading = ref(false)
const queryArrList = computed<any[]>(() => [
	{
		name: "合同编号",
		key: "contractCode",
		type: "input",
		placeholder: "请输入合同编号"
	},
	{
		name: "合同名称",
		key: "contractLabel",
		type: "input",
		placeholder: "请输入合同名称"
	},
	{
		name: "供应商名称",
		key: "supplierLabel",
		type: "input",
		placeholder: "请输入供应商名称"
	},
	{
		name: "合同类型",
		key: "contractType",
		type: "select",
		placeholder: "请输入合同类型",
		children: dictOptions.value.CONTRACT_TYPE //字典值
	}
])
const tableProp = [
	{ label: "合同编号", prop: "contractCode" },
	{ label: "合同名称", prop: "contractLabel" },
	{ label: "合同类型", prop: "contractType", needSlot: true, width: 100 },
	{ label: "供应商名称", prop: "supplierLabel", width: 250 },
	{
		label: "合同金额",
		prop: "contractAmount",
		needSlot: true,
		align: "right",
		width: 160
	}
]
const tableApi = PurchaseContractApi.getPurchaseContractList
const fetchParam = { bpmStatus: appStatus.approved }
// 左侧按钮点击
const onSave = (rowList: any[]) => {
	if (rowList.length <= 0) {
		ElMessage.warning("请选择合同！")
		return
	}
	emits("onSuccess", rowList[0])
	drawerLoading.value = false
}
const onClose = () => {
	emits("onClosed")
}

defineOptions({
	name: "SelectDrawer"
})

onMounted(() => {
	getDictByCodeList(["CONTRACT_TYPE"])
})
</script>
<template>
	<SelectDrawer
		title="合同信息"
		:query-arr-list="queryArrList"
		:table-prop="tableProp"
		:table-api="tableApi"
		:fetch-param="fetchParam"
		:selected="selectId"
		:loading="drawerLoading"
		:needSingleSelect="true"
		@on-close="onClose"
		@on-save="onSave"
	>
		<template #contractType="{ rowData }">
			{{ dictFilter("CONTRACT_TYPE", rowData.contractType)?.name || "---" }}
		</template>
		<template #contractAmount="{ rowData }">
			<cost-tag :value="rowData.contractAmount" />
		</template>
	</SelectDrawer>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index";
</style>
