<!-- 新建/编辑 采购合同 -->
<script lang="ts" setup>
import { defineProps, ref, reactive, onBeforeMount } from "vue"
import { ElMessage, type FormInstance, type FormRules } from "element-plus"
import { FormElementType } from "../../components/define"
import FormElement from "../../components/formElement.vue"
import { PurchaseContractApi } from "../../../api/purchase/purchaseContract"
import { appStatus, fileBusinessType } from "@/app/baseline/api/dict"
import TableFile from "@/app/baseline/views/components/tableFile.vue"
import selectSupplier from "./selectSupplier.vue"
import { useDictInit } from "@/app/baseline/views/components/dictBase"
import { getModalTypeLabel } from "@/app/baseline/utils"
import {
	getIdempotentToken,
	validateAndCorrectInput
} from "@/app/baseline/utils/validate"
import {
	IIdempotentTokenType,
	IIdempotentTokenTypePre,
	IModalType
} from "@/app/baseline/utils/types/common"
import { toNumber } from "lodash-es"
import { useMessageBoxInit } from "../../components/messageBox"

const props = withDefaults(
	defineProps<{
		model?: IModalType
		id?: string
	}>(),
	{
		model: IModalType.create
	}
)

const { dictOptions, getDictByCodeList } = useDictInit()

const { showWarnConfirm } = useMessageBoxInit()

/**
 * 保存旧的表单数据
 */
const oldFormData = ref<string>()

const emits = defineEmits<{
	(e: "close"): void
	(e: "update"): void
}>()

const formData = ref<{ [propName: string]: any }>({})

const formBtnLoading = ref(false)
const drawerLoading = ref(false)

const supplierSelectorVisible = ref(false)
const formRef = ref<FormInstance>()
const fileTableRef = ref()

/**
 * 能否编辑扩展信息
 */
const canEditExtra = computed(() => Boolean(formData.value?.id || props.id))

const drawerLeftTitle = computed(() => ({
	name: [
		getModalTypeLabel(
			canEditExtra.value ? IModalType.edit : IModalType.create,
			"采购合同"
		)
	],
	icon: ["fas", "square-share-nodes"]
}))

const drawerRightTitle = {
	name: ["扩展信息"],
	icon: ["fas", "square-share-nodes"]
}
const formBtnList = ref([
	{ name: "取消", icon: ["fas", "circle-minus"] },
	{ name: "保存草稿", icon: ["fas", "floppy-disk"] }
	/* { name: "提交审核", icon: ["fas", "circle-check"] } */
])

const formBtnRightList = ref([
	{ name: "提交审核", icon: ["fas", "circle-check"] }
])

const tabList = ["相关附件"]

const formEl = computed<FormElementType[][]>(() => [
	[
		{
			label: "合同编号",
			name: "contractCode",
			maxlength: 50,
			disabled: false
		}
	],
	[
		{
			label: "合同名称",
			name: "contractLabel",
			maxlength: 50,
			disabled: false
		}
	],
	[
		{
			label: "合同类型",
			name: "contractType",
			type: "select",
			data: dictOptions.value.CONTRACT_TYPE //字典值
		}
	],

	[
		{
			label: "合同金额",
			name: "contractAmount",
			type: "number",
			append: "元",
			input: (value: number) => {
				formData.value.contractAmount = validateAndCorrectInput(value, 5)
			},
			blur: (event: any) => {
				formData.value.contractAmount = toNumber(event.target.value)
			}
		}
	],
	[
		{
			label: "合同签订和截止日期",
			name: "contractSigningEndDate",
			type: "daterange",
			startPlaceholder: "签订日期",
			endPlaceholder: "截止日期"
		}
	],
	[
		{
			label: "供应商名称",
			name: "supplierLabel",
			type: "drawer",
			clickApi: () => {
				supplierSelectorVisible.value = true
			}
		}
	],
	[
		{
			label: "备注说明",
			name: "remark",
			maxlength: 200,
			rows: "5",
			type: "textarea"
		}
	]
])
const rules = reactive<FormRules<typeof formData.value>>({
	contractLabel: [
		{ required: true, message: "合同名称不能为空", trigger: "change" }
	],
	contractCode: [
		{ required: true, message: "合同编号不能为空", trigger: "change" }
	],
	supplierLabel: [
		{ required: true, message: "供应商不能为空", trigger: "change" }
	],
	contractSigningDate: [
		{ required: true, message: "合同签订日期不能为空", trigger: "change" }
	],
	contractEndDate: [
		{ required: true, message: "合同截止日期不能为空", trigger: "change" }
	],
	contractSigningEndDate: [
		{ required: true, message: "合同签订和截止日期不能为空", trigger: "change" }
	],
	contractType: [
		{ required: true, message: "合同类型不能为空", trigger: "change" }
	],
	contractAmount: [
		{ required: true, message: "合同金额不能为空", trigger: "change" }
	]
})

const onFormBtnList = (btnName: string | undefined) => {
	if (btnName === "保存草稿") {
		handleSaveDraft()
	} else if (btnName == "提交审核") {
		handleSubmit()
	} else {
		emits("close")
	}
}
/**
 * 处理保存草稿（包含新增和编辑）
 */
async function handleSaveDraft() {
	if (!formRef.value) {
		return
	}

	formRef.value.validate(async (valid: any) => {
		if (!valid) {
			return
		}
		formBtnLoading.value = true
		// 表单校验通过
		try {
			const api = canEditExtra.value
				? PurchaseContractApi.updatePurchaseContract
				: PurchaseContractApi.addPurchaseContract

			formData.value.contractSigningDate =
				formData.value.contractSigningEndDate[0]
			formData.value.contractEndDate = formData.value.contractSigningEndDate[1]

			let idempotentToken = ""
			if (!canEditExtra.value) {
				idempotentToken = getIdempotentToken(
					IIdempotentTokenTypePre.apply,
					IIdempotentTokenType.purchaseContract
				)
			}

			const r = await api(formData.value as any, idempotentToken)

			formData.value.id = r.id

			oldFormData.value = JSON.stringify(formData.value)

			ElMessage.success("操作成功")
			emits("update")
		} finally {
			formBtnLoading.value = false
		}
	})
}

/**
 * 处理提交审核
 *
 * 需要表单校验
 */
function handleSubmit() {
	if (!formRef.value) {
		return
	}

	formRef.value.validate(async (valid: any) => {
		if (!valid) {
			return
		}

		await showWarnConfirm("请确认是否提交本次数据？")

		formBtnLoading.value = true
		drawerLoading.value = true

		try {
			// 如果主表有修改，则先更新主表数据
			if (oldFormData.value != JSON.stringify(formData.value)) {
				formData.value.contractSigningDate =
					formData.value.contractSigningEndDate[0]
				formData.value.contractEndDate =
					formData.value.contractSigningEndDate[1]
				await PurchaseContractApi.updatePurchaseContract({
					...formData.value
				} as any)
			}

			const idempotentToken = getIdempotentToken(
				IIdempotentTokenTypePre.other,
				IIdempotentTokenType.purchaseContract,
				formData.value.id
			)

			await PurchaseContractApi.publishContract(
				formData.value.id,
				idempotentToken
			)

			ElMessage.success("操作成功")
			emits("update")
			emits("close")
		} finally {
			formBtnLoading.value = false
			drawerLoading.value = false
		}
	})
}

/**
 * 获取详情
 */
async function getDetail() {
	if (canEditExtra.value) {
		drawerLoading.value = true
		try {
			const res = await PurchaseContractApi.getPurchaseContract(
				props.id || formData.value.id
			)
			formData.value = { ...res }
			formData.value.contractSigningEndDate = [
				formData.value.contractSigningDate,
				formData.value.contractEndDate
			]

			oldFormData.value = JSON.stringify(formData.value)
		} finally {
			drawerLoading.value = false
		}
	}
}

// 选择供应商弹窗关闭
const onCloseDrawer = (refresh: boolean, selectRow: any) => {
	if (refresh) {
		formData.value.supplierId = selectRow.id
		formData.value.supplierLabel = selectRow.label
		supplierSelectorVisible.value = false
	}
}

//扩展栏标签页切换
const activeTab = ref<number>(0)
const handleTabChange = (index: number) => {
	activeTab.value = index
}

onBeforeMount(() => {
	getDictByCodeList(["CONTRACT_TYPE"])

	getDetail()
})
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<!-- 左侧表单区域 -->
		<div class="drawer-column left">
			<div class="rows">
				<Title :title="drawerLeftTitle" />
				<el-form
					style="padding-bottom: 30px"
					class="content form-base"
					ref="formRef"
					:model="formData"
					:rules="rules"
					label-position="top"
					label-width="100px"
				>
					<FormElement :form-element="formEl" :form-data="formData" />
				</el-form>
			</div>
			<ButtonList
				class="footer"
				:button="formBtnList"
				:loading="formBtnLoading"
				@onBtnClick="onFormBtnList"
			/>
		</div>
		<!-- 右侧table区域 -->
		<div class="drawer-column right" :class="!canEditExtra ? 'disabled' : ''">
			<div class="rows">
				<Title :title="drawerRightTitle">
					<Tabs class="tabs" :tabs="tabList" @on-tab-change="handleTabChange" />
				</Title>

				<el-scrollbar>
					<TableFile
						ref="fileTableRef"
						:mod="props.model"
						:businessId="props.id || formData.id"
						:businessType="fileBusinessType.purchaseContract"
					/>
				</el-scrollbar>
			</div>
			<ButtonList
				class="footer"
				:button="formBtnRightList"
				:loading="formBtnLoading"
				@onBtnClick="onFormBtnList"
			/>
		</div>

		<!-- 供应商名称 -->
		<Drawer
			:size="1200"
			v-model:drawer="supplierSelectorVisible"
			:destroyOnClose="true"
		>
			<selectSupplier
				:id="formData.supplierId"
				:bpm-status="appStatus.approved"
				@onSuccess="(selectRow) => onCloseDrawer(true, selectRow)"
				@onClosed="supplierSelectorVisible = false"
			/>
		</Drawer>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.drawer-container {
	.left {
		width: 310px;
	}
	.left-edit {
		width: 100%;
	}

	.right {
		width: calc(100% - 310px);
		.detail-table {
			width: 100%;
			height: 100%;
		}
	}

	.content {
		height: calc(100% - 73px);
	}
}
</style>
