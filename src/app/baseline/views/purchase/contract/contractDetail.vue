<!-- 查看 采购合同 -->
<script lang="ts" setup>
import { defineProps, ref, onBeforeMount } from "vue"
import { PurchaseContractApi } from "../../../api/purchase/purchaseContract"
import { fileBusinessType } from "@/app/baseline/api/dict"
import TableFile from "@/app/baseline/views/components/tableFile.vue"
import { useDictInit } from "@/app/baseline/views/components/dictBase"
import { getModalTypeLabel, getOnlyDate } from "@/app/baseline/utils"
import { IModalType } from "@/app/baseline/utils/types/common"
import CostTag from "@/app/baseline/views/components/costTag.vue"

import { useTbInit } from "@/app/baseline/views/components/tableBase"
import { PurchaseProjectApi } from "@/app/baseline/api/purchase/purchaseProject"
import projectDetail from "../project/projectDetail.vue"
import { modalSize } from "@/app/baseline/utils/layout-config"
import { getRealLength, setString } from "@/app/baseline/utils/validate"

const props = withDefaults(
	defineProps<{
		model?: IModalType
		id?: string
		footerBtnVisible?: boolean
	}>(),
	{
		model: IModalType.view,
		footerBtnVisible: true
	}
)

const { dictFilter, getDictByCodeList } = useDictInit()
/**
 * 保存旧的表单数据
 */
const oldFormData = ref<string>()

const emits = defineEmits<{
	(e: "close"): void
	(e: "update"): void
}>()

const formData = ref<{ [propName: string]: any }>({})

const formBtnLoading = ref(false)
const drawerLoading = ref(false)

/**
 * 能否编辑扩展信息
 */
const canEditExtra = computed(() => Boolean(formData.value?.id || props.id))

const drawerLeftTitle = computed(() => ({
	name: [getModalTypeLabel(props.model, "采购合同")],
	icon: ["fas", "square-share-nodes"]
}))

const drawerRightTitle = {
	name: ["扩展信息"],
	icon: ["fas", "square-share-nodes"]
}
const formBtnList = ref([{ name: "取消", icon: ["fas", "circle-minus"] }])

const tabList = ["采购项目", "相关附件"]

/**
 * 表单配置
 */
const descOptions = [
	{ label: "合同编号", key: "contractCode" },
	{ label: "合同名称", key: "contractLabel" },
	{ label: "合同类型", key: "contractType" },
	{ label: "供应商名称", key: "supplierLabel" },
	{ label: "合同签订日期", key: "contractSigningDate", type: "date" },
	{ label: "合同截止日期", key: "contractEndDate", type: "date" },
	{ label: "合同金额", key: "contractAmount" },
	{ label: "备注", key: "remark", needTooltip: true }
]

/**
 * 获取详情
 */
async function getDetail() {
	if (canEditExtra.value) {
		drawerLoading.value = true
		try {
			const res = await PurchaseContractApi.getPurchaseContract(
				props.id || formData.value.id
			)
			formData.value = { ...res }
			formData.value.contractSigningEndDate = [
				formData.value.contractSigningDate,
				formData.value.contractEndDate
			]

			oldFormData.value = JSON.stringify(formData.value)
		} finally {
			drawerLoading.value = false
		}
	}
}

//扩展栏标签页切换
const activeTab = ref<number>(0)
const handleTabChange = (index: number) => {
	activeTab.value = index
}

onBeforeMount(() => {
	getDictByCodeList(["CONTRACT_TYPE", "PURCHASE_TYPE"])

	getDetail()

	getTableData({})
})

const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	currentPage,
	pageTotal,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = useTbInit()

tableProp.value = [
	{ label: "采购项目编号", prop: "code", width: 200, fixed: "left" },
	{ label: "年度", prop: "year", width: 100 },
	{ label: "采购计划号", prop: "planPurchaseCode", width: 200 },
	{ label: "采购项目名称", prop: "label" },
	{ label: "采购方式", prop: "purchaseType", needSlot: true, width: 100 },
	{ label: "采购员", prop: "purchaseUserName" },
	{
		label: "操作",
		width: 100,
		prop: "operations",
		fixed: "right",
		needSlot: true
	}
]

fetchParam.value = {
	contractId: props.id || formData.value.id,
	sord: "desc",
	sidx: "createdDate"
}
fetchFunc.value = PurchaseProjectApi.getPurchaseProjectList

const getTableData = (data?: any) => {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		...data
	}
	fetchTableData()
}

const editorId = ref("")
const projectDetailVisible = ref(false)
/**
 * 采购项目 详情
 * @param row
 */
function handleProjectView(row: any) {
	editorId.value = row.id
	projectDetailVisible.value = true
}
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<!-- 左侧表单区域 -->
		<div class="drawer-column left">
			<div class="rows">
				<Title :title="drawerLeftTitle" />
				<el-scrollbar>
					<el-descriptions size="small" :column="1" border class="content">
						<el-descriptions-item
							v-for="desc in descOptions"
							:key="desc.label"
							:label="desc.label"
						>
							<!-- 开票金额 -->
							<cost-tag
								v-if="desc.key === 'contractAmount'"
								:value="formData.contractAmount"
							/>
							<span v-else-if="desc.type === 'date'">
								{{ getOnlyDate(formData[desc.key]) || "---" }}
							</span>
							<span v-else-if="desc.key === 'contractType'">
								{{
									dictFilter("CONTRACT_TYPE", formData.contractType)?.label ||
									"---"
								}}
							</span>
							<span v-else-if="desc.needTooltip">
								<el-tooltip
									effect="dark"
									:content="formData?.[desc.key]"
									:disabled="
										getRealLength(formData?.[desc.key]) <= 100 ? true : false
									"
								>
									{{
										getRealLength(formData?.[desc.key]) > 100
											? setString(formData?.[desc.key], 100)
											: formData?.[desc.key] || "---"
									}}
								</el-tooltip>
							</span>
							<span v-else>
								{{ formData?.[desc.key] || "---" }}
							</span>
						</el-descriptions-item>
					</el-descriptions>
				</el-scrollbar>
			</div>
		</div>
		<!-- 右侧table区域 -->
		<div class="drawer-column right" :class="{ pdr10: !footerBtnVisible }">
			<div class="rows">
				<Title :title="drawerRightTitle">
					<Tabs class="tabs" :tabs="tabList" @on-tab-change="handleTabChange" />
				</Title>

				<el-scrollbar v-if="activeTab === 0">
					<PitayaTable
						ref="tableRef"
						:columns="tableProp"
						:tableData="tableData"
						:total="pageTotal"
						:single-select="true"
						:need-selection="false"
						:need-index="true"
						@onSelectionChange="selectedTableList = $event"
						@on-current-page-change="onCurrentPageChange"
						:table-loading="tableLoading"
						style="width: 100%"
					>
						<!-- 采购方式 -->
						<template #purchaseType="{ rowData }">
							{{
								dictFilter("PURCHASE_TYPE", rowData.purchaseType)?.label ||
								"---"
							}}
						</template>

						<template #operations="{ rowData }">
							<el-button v-btn link @click="handleProjectView(rowData)">
								<font-awesome-icon
									:icon="['fas', 'eye']"
									style="color: var(--pitaya-btn-background)"
								/>
								<span class="table-inner-btn">查看</span>
							</el-button>
						</template>
					</PitayaTable>
				</el-scrollbar>

				<el-scrollbar v-else>
					<TableFile
						ref="fileTableRef"
						:mod="props.model"
						:businessId="props.id || formData.id"
						:businessType="fileBusinessType.purchaseContract"
					/>
				</el-scrollbar>
			</div>
			<ButtonList
				v-if="footerBtnVisible"
				class="footer"
				:button="formBtnList"
				:loading="formBtnLoading"
				@onBtnClick="emits('close')"
			/>
		</div>

		<!-- 查看 弹窗 -->
		<Drawer
			:size="modalSize.xl"
			v-model:drawer="projectDetailVisible"
			:destroyOnClose="true"
		>
			<project-detail :id="editorId" @close="projectDetailVisible = false" />
		</Drawer>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.drawer-container {
	.left {
		width: 310px;
	}
	.left-edit {
		width: 100%;
	}

	.right {
		width: calc(100% - 310px);
		.detail-table {
			width: 100%;
			height: 100%;
		}
	}

	.content {
		height: calc(100% - 73px);
	}
}
</style>
