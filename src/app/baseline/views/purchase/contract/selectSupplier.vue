<script lang="ts" setup>
import { ElMessage } from "element-plus"
import { ref } from "vue"
import SelectDrawer from "@/app/baseline/views/components/selectDrawer.vue"
import { baseUserApi } from "@/app/platform/api/system/baseUser"
import { BaseLineSysApi } from "@/app/baseline/api/system"
import { useUserStore } from "@/app/platform/store/modules/user"
import { PurchaseSupplierApi } from "@/app/baseline/api/purchase/purchaseSupplier"
import { appStatus, DictApi } from "../../../api/dict"
const userStore = useUserStore()
const { userInfo } = storeToRefs(userStore)
interface Props {
	id: string
	userIds: string
	bpmStatus: string
}

const props = withDefaults(defineProps<Props>(), {
	userIds: undefined
})
const emits = defineEmits(["onSuccess", "onClosed"])
const selectId = props.id ? [props.id] : []
const drawerLoading = ref(false)
const queryArrList = ref<any[]>([
	{
		name: "供应商编码",
		key: "code",
		type: "input",
		placeholder: "请输入供应商编码"
	},
	{
		name: "供应商名称",
		key: "label",
		type: "input",
		placeholder: "请输入供应商名称"
	},
	{
		name: "联系人",
		key: "contact",
		type: "input",
		placeholder: "请输入联系人"
	}
	/*{
		name: "公司",
		key: "sysCommunityId",
		type: "treeSelect",
		treeApi: BaseLineSysApi.getCompanyAllList,
		placeholder: "请选择所属公司"
	},
	{
		name: "审批状态",
		key: "bpmStatus",
		type: "select",
		placeholder: "请选择",
		children: DictApi.getQueryBpmStatus()
	}*/
])
const tableProp = [
	{ label: "供应商编码", prop: "code" },
	{ label: "供应商名称", prop: "label" },
	{ label: "联系人", prop: "contact" },
	{ label: "联系人手机号", prop: "contactPhoneNumber" },
	{ label: "电子邮箱", prop: "contactEmail" }
]
const tableApi = PurchaseSupplierApi.getPurchaseSupplierList
/*const fetchParam = { bpmStatus: "2" }*/
const fetchParam = {
	bpmStatus: appStatus.approved,
	sord: "desc",
	sidx: "createdDate"
}
// 左侧按钮点击
const onSave = (rowList: any[]) => {
	if (rowList.length <= 0) {
		ElMessage.warning("请选择供应商！")
		return
	}
	emits("onSuccess", rowList[0])
	drawerLoading.value = false
}
const onClose = () => {
	emits("onClosed")
}

defineOptions({
	name: "SelectDrawer"
})
</script>
<template>
	<SelectDrawer
		title="供应商信息"
		:query-arr-list="queryArrList"
		:table-prop="tableProp"
		:table-api="tableApi"
		:fetch-param="fetchParam"
		:selected="selectId"
		:loading="drawerLoading"
		:needSingleSelect="true"
		@on-close="onClose"
		@on-save="onSave"
	/>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index";

.drawer-container {
	.right {
		width: 100%;
	}
}
</style>
