<!-- 发票管理 详情 V2.0 -->
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<!-- 左侧表单区域 -->
		<div class="drawer-column left">
			<div class="rows">
				<Title :title="drawerLeftTitle" />
				<el-scrollbar>
					<el-descriptions size="small" :column="1" border class="content">
						<el-descriptions-item
							v-for="desc in descOptions"
							:key="desc.label"
							:label="desc.label"
						>
							<!-- 开票金额 -->
							<cost-tag v-if="desc.key === 'amount'" :value="formData.amount" />
							<span v-else-if="desc.key === 'invoiceDate'">
								{{
									formData.invoiceDate
										? XEUtils.toDateString(
												new Date(formData.invoiceDate),
												"yyyy-MM-dd"
										  )
										: "---"
								}}
							</span>
							<span v-else-if="desc.key === 'type'">
								{{ dictFilter("INVOICE_TYPE", formData.type)?.label || "---" }}
							</span>
							<span v-else-if="desc.key === 'invoiceColor'">
								{{
									dictFilter("INVOICE_COLOR", formData.invoiceColor)?.label ||
									"---"
								}}
							</span>
							<span v-else-if="desc.key === 'taxRate'">
								{{ formData.taxRate ? `${formData.taxRate}%` : "---" }}
							</span>
							<span v-else-if="desc.needTooltip">
								<el-tooltip
									effect="dark"
									:content="formData?.[desc.key]"
									:disabled="
										getRealLength(formData?.[desc.key]) <= 100 ? true : false
									"
								>
									{{
										getRealLength(formData?.[desc.key]) > 100
											? setString(formData?.[desc.key], 100)
											: formData?.[desc.key] || "---"
									}}
								</el-tooltip>
							</span>
							<span v-else>
								{{ formData?.[desc.key] || "---" }}
							</span>
						</el-descriptions-item>
					</el-descriptions>
				</el-scrollbar>
			</div>
		</div>
		<div class="drawer-column right" :class="{ pdr10: !footerBtnVisible }">
			<div class="rows">
				<Title :title="drawerRightTitle">
					<Tabs class="tabs" :tabs="tabList" @on-tab-change="handleTabChange" />
				</Title>
				<div class="detail-table">
					<invoice-order-table
						v-if="activeTab == 0 && formData.id"
						:supplierId="formData.supplierId"
						:supplierLabel="formData.supplierLabel"
						:invoiceId="formData.id"
						:invoiceColor="formData.invoiceColor"
						:blueInvoiceId="formData.blueInvoiceId"
						:needSelected="false"
						:model="props.mode"
						@update="handleUpdateAction"
					/>
					<invoice-list-table
						v-if="activeTab == 1 && formData.id"
						:invoice="formData"
						:model="props.mode"
						@update="handleUpdateAction"
					/>
					<TableFile
						v-if="activeTab == 2 && formData.id"
						ref="refFileTable"
						:mod="props.mode"
						:businessId="formData.id"
						:businessType="fileBusinessType.invoiceInfo"
						:needPage="true"
					/>
				</div>
			</div>
			<ButtonList
				v-if="footerBtnVisible"
				class="footer"
				:button="submitBtnList"
				:loading="btnLoading"
				@on-btn-click="emit('close')"
			/>
		</div>
	</div>
</template>
<script setup lang="ts">
import { useDictInit } from "../../components/dictBase"
import { fileBusinessType } from "@/app/baseline/api/dict"
import { IModalType } from "@/app/baseline/utils/types/common"
import { PurchaseInvoiceApi } from "@/app/baseline/api/purchase/purchaseInvoice"

import invoiceOrderTable from "./invoiceOrderTable.vue"
import invoiceListTable from "./invoiceListTable.vue"
import XEUtils from "xe-utils"
import TableFile from "@/app/baseline/views/components/tableFile.vue"
import CostTag from "@/app/baseline/views/components/costTag.vue"
import { getRealLength, setString } from "@/app/baseline/utils/validate"

const props = withDefaults(
	defineProps<{
		/**
		 * 业务id
		 */
		id: any

		/**
		 * 编辑模式：编辑/新建
		 */
		mode?: IModalType
		footerBtnVisible?: boolean
	}>(),
	{ footerBtnVisible: true, mode: IModalType.view }
)

const emit = defineEmits<{
	(e: "close"): void
	(e: "update"): void
}>()

const { dictFilter, getDictByCodeList } = useDictInit()

const drawerLoading = ref(false)
const btnLoading = ref(false)

const drawerLeftTitle = ref({
	name: ["发票基本信息"],
	icon: ["fas", "square-share-nodes"]
})

const drawerRightTitle = {
	name: ["扩展信息"],
	icon: ["fas", "square-share-nodes"]
}

const submitBtnList = [{ name: "取消", icon: ["fas", "circle-minus"] }]

const tabList = ref(["采购订单", "发票清单", "相关附件"])

const activeTab = ref(0)
const handleTabChange = (index: number) => {
	activeTab.value = index
}

const formData = ref<Record<string, any>>({
	amount_view: "￥0.00"
})

/**
 * 表单配置
 */
const descOptions = [
	{ label: "发票号码", key: "code" },
	{ label: "合同名称", key: "contractLabel" },
	{ label: "合同编号", key: "contractCode" },
	{ label: "供应商名称", key: "supplierLabel" },
	{ label: "开票日期", key: "invoiceDate" },
	{ label: "发票类型", key: "type" },
	{ label: "发票颜色", key: "invoiceColor" },
	{ label: "冲红对应蓝字发票", key: "blueInvoiceCode" },
	{ label: "开票金额", key: "amount" },
	{ label: "税率", key: "taxRate" },
	{ label: "录入人员", key: "createdBy_view" },
	{ label: "录入时间", key: "createdDate" },
	{ label: "备注说明", key: "remark", needTooltip: true }
]

async function getDetail() {
	if (props.id) {
		drawerLoading.value = true
		try {
			const res = await PurchaseInvoiceApi.getPurchaseInvoice(
				props.id || formData.value?.id
			)
			formData.value = { ...res }
			formData.value.amount_view =
				formData.value.amount > 0
					? `￥${`${XEUtils.commafy(formData.value.amount, { digits: 5 })}`}`
					: "￥0.00000"
		} finally {
			drawerLoading.value = false
		}
	}
}
onMounted(() => {
	getDictByCodeList(["INVOICE_TYPE", "INVOICE_STATUS", "INVOICE_COLOR"])
	getDetail()
})

function handleUpdateAction() {
	emit("update")
}
</script>
<style scoped lang="scss">
@import "@/app/baseline/assets/css/index.scss";
$--left-width: 350px;
.drawer-column {
	&.left {
		width: $--left-width;
		:deep(.el-descriptions__label) {
			width: 220px;
		}
		.el-scrollbar {
			height: calc(100% - 73px);
		}
	}
	&.right {
		width: calc(100% - $--left-width);
		.detail-table {
			height: 100%;
		}
	}
}
</style>
