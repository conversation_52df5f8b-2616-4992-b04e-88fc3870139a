<!-- 开票物资 选择器 -->
<script setup lang="ts">
import { onMounted } from "vue"
import { useTbInit } from "@/app/baseline/views/components/tableBase"
import { useDictInit } from "@/app/baseline/views/components/dictBase"
import costTag from "@/app/baseline/views/components/costTag.vue"
import { PurchaseInvoiceItemApi } from "@/app/baseline/api/purchase/purchaseInvoiceItem"
import { listMatStoragePaged } from "@/app/baseline/api/store/manage-api"
import { toFixedTwo } from "@/app/baseline/utils"
import { DictApi, IInvoiceColor } from "@/app/baseline/api/dict"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import XEUtils from "xe-utils"

export interface Props {
	id: string | number //订单ID
	invoiceColor?: string
	blueInvoiceId?: string
	needSelection?: boolean
	needQuery?: boolean
	isPublish?: boolean
}

const props = withDefaults(defineProps<Props>(), {
	id: "",
	needQuery: false,
	needSelection: false,
	isPublish: false
})
const emits = defineEmits(["onSelectChange", "onSelectCnt"])

const { dictOptions, getDictByCodeList, dictFilter } = useDictInit()

const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	currentPage,
	pageTotal,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange,
	onDataSelected
} = useTbInit()

tableProp.value = [
	{ label: "物资编码", prop: "materialCode", width: 130, fixed: "left" },
	{ label: "物资名称", prop: "materialLabel", width: 150 },
	{ label: "规格型号", prop: "version", width: 150 },
	{
		label: "技术参数",
		prop: "technicalParameter",
		minWidth: 200,
		align: "left"
	},
	{
		label: "物资性质",
		prop: "attribute",
		needSlot: true,
		width: 120
	},
	{ label: "入库时间", prop: "inStoreTime", width: 160 },
	{ label: "入库仓库名称", prop: "storeLabel", width: 200 },
	{ label: "入库批次", prop: "batchNo", width: 180 },
	{ label: "采购单位", prop: "buyUnit", needSlot: true, width: 90 },
	{
		label: "采购单价",
		prop: "unitPrice",
		needSlot: true,
		align: "right",
		width: 120
	},
	{
		label: "入库数量",
		prop: "inStoreNum",
		width: 120,
		needSlot: true,
		align: "right"
	},
	{
		label: "已开票数量",
		prop: "invoicedNum",
		width: 120,
		needSlot: true,
		align: "right"
	},
	{
		label: "可开票数量",
		prop: "canNum",
		width: 120,
		needSlot: true,
		align: "right",
		fixed: "right"
	},
	{
		label: "采购订单号",
		prop: "purchaseOrderCode",
		width: 180,
		fixed: "right"
	},
	{ label: "合同编号", prop: "contractCode", width: 180, fixed: "right" }
]

const queryArrList = computed(() => [
	/* {
		name: "入库仓库",
		key: "storeId",
		placeholder: "请选择仓库",
		type: "treeSelect",
		treeApi: async () => {
			const r = await listMatStorage({})
			return map(r, (v) => ({
				...v,
				name: v.label,
				value: v.id,
				allName: v.label
			}))
		}
	}, */
	{
		name: "入库仓库",
		key: "storeId",
		placeholder: "请选择入库仓库",
		type: "tableSelect",
		tableInfo: [
			{
				title: "请选择入库仓库",
				tableApi: listMatStoragePaged, //表格接口
				tableColumns: [
					{ label: "仓库编码", prop: "code", width: 120 },
					{ label: "仓库名称", prop: "label" },
					{
						label: "仓库级别",
						prop: "level_view",
						width: 100
					},
					{
						label: "仓库类型",
						prop: "type_view",
						width: 120
					},
					{
						label: "所属段区",
						prop: "depotId_view",
						width: 150
					}
				],
				queryArrList: [
					{
						name: "仓库编码",
						key: "code",
						type: "input",
						placeholder: "请输入仓库编码"
					},
					{
						name: "仓库名称",
						key: "label",
						type: "input",
						placeholder: "请输入仓库名称"
					}
				],
				params: {
					currentPage: 1,
					pageSize: 20
				}, //表格接口参数

				labelName: {
					key: "id", //回显标识
					label: "label", //input框要展示的字段
					value: "id" //要给后台传的字段
				}
			}
		]
	},
	{
		name: "采购订单号",
		key: "purchaseOrderCode",
		placeholder: "请输入采购订单号",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资编码",
		key: "materialCode",
		placeholder: "请输入物资编码",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资名称",
		key: "materialLabel",
		placeholder: "请输入物资名称",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "规格型号",
		key: "version",
		placeholder: "请输入规格型号",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资性质",
		key: "attribute",
		type: "select",
		children: dictOptions.value.MATERIAL_NATURE,
		placeholder: "请选择物资性质"
	},
	{
		name: "入库时间",
		key: "inStoreTime",
		type: "startAndEndTime",
		placeholder: "入库时间"
	}
])

//table 选择
onDataSelected.value = (rowList: { [propName: string]: any }[]) => {
	selectedTableList.value = rowList
	emits("onSelectChange", rowList)
}

fetchFunc.value = (param: Record<string, any>) => {
	if (props.invoiceColor != IInvoiceColor.red) {
		return PurchaseInvoiceItemApi.getPurchaseInvoiceAddItem({
			invoiceId: props.id,
			...param
		} as any)
	} else {
		return PurchaseInvoiceItemApi.getPurchaseInvoiceItemList({
			redInvoiceId: props.id,
			blueInvoiceId: props.blueInvoiceId,
			...param
		} as any)
	}
}

//搜索条件确认回调
function getQueryData(data: { [propName: string]: any }) {
	const _param = XEUtils.clone(data)
	if (_param?.inStoreTime?.length > 0) {
		_param["inStoreTime_start"] = _param.inStoreTime[0]
		_param["inStoreTime_end"] = _param.inStoreTime[1]
	} else {
		delete fetchParam.value["inStoreTime_start"]
		delete fetchParam.value["inStoreTime_end"]
	}

	delete _param.inStoreTime
	delete fetchParam.value.inStoreTime

	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = { ...fetchParam.value, ..._param }
	fetchTableData()
}

onMounted(() => {
	fetchParam.value.sord = "desc"
	fetchParam.value.sidx = "createdDate"

	getDictByCodeList(["INVENTORY_UNIT", "MATERIAL_NATURE"])
	fetchTableData()
})

/**
 * 获取 开票数量 <= 0 的数据
 */
const disabledIdList = ref<any[]>([])
watch(
	() => tableData.value,
	() => {
		disabledIdList.value = []
		tableData.value.forEach((item: any) => {
			if (item.canNum <= 0) {
				disabledIdList.value.push(item.id)
			}
		})
	},
	{
		immediate: true
	}
)
</script>
<template>
	<Query
		:queryArrList="queryArrList"
		@getQueryData="getQueryData"
		class="custom-q"
		v-if="props.needQuery"
	/>
	<PitayaTable
		ref="tableRef"
		:columns="tableProp"
		:table-data="tableData"
		:need-index="true"
		:need-pagination="true"
		:single-select="false"
		:need-selection="props.needSelection"
		:total="pageTotal"
		:disabled-ids="disabledIdList"
		@onSelectionChange="onDataSelected"
		@on-current-page-change="onCurrentPageChange"
		:table-loading="tableLoading"
	>
		<!-- 采购单价 -->
		<template #unitPrice="{ rowData }">
			<cost-tag :value="rowData.unitPrice" />
		</template>

		<!-- 物资性质 -->
		<template #attribute="{ rowData }">
			<dict-tag :options="DictApi.getMatAttr()" :value="rowData.attribute" />
		</template>
		<template #buyUnit="{ rowData }">
			{{ dictFilter("INVENTORY_UNIT", rowData.buyUnit)?.subitemName || "---" }}
		</template>

		<!-- 已开票数量 -->
		<template #invoicedNum="{ rowData }">
			{{ toFixedTwo(rowData.invoicedNum) }}
		</template>

		<!-- 可开票数量 -->
		<template #canNum="{ rowData }">
			{{ toFixedTwo(rowData.canNum) }}
		</template>

		<template #inStoreNum="{ rowData }">
			{{ toFixedTwo(rowData.inStoreNum) }}
		</template>
	</PitayaTable>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.custom-q {
	margin: 10px 0px -10px 10px !important;
}
.el-input-number {
	width: 95%;
}
</style>
