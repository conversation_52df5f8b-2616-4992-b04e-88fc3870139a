<!-- 发票清单 table V2.0-->
<script setup lang="ts">
import { ElMessage } from "element-plus"
import { defineProps, ref, onMounted } from "vue"

import { useTbInit } from "@/app/baseline/views/components/tableBase"
import { useDictInit } from "@/app/baseline/views/components/dictBase"
import { useMessageBoxInit } from "@/app/baseline/views/components/messageBox"
import { PurchaseInvoiceItemApi } from "@/app/baseline/api/purchase/purchaseInvoiceItem"

import TableMatItem from "@/app/baseline/views/purchase/invoice/tableMatItem.vue"
import costTag from "@/app/baseline/views/components/costTag.vue"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import {
	batchFormatterNumView,
	tableColFilter,
	toFixedTwo
} from "@/app/baseline/utils"
import { IInvoiceColor, DictApi } from "@/app/baseline/api/dict"
import {
	validateAndCorrectInput,
	maxValidateErrorInfo,
	maxValidateNum,
	getIdempotentToken
} from "@/app/baseline/utils/validate"
import { debounce, findIndex, map, toNumber } from "lodash-es"
import {
	IIdempotentTokenType,
	IIdempotentTokenTypePre,
	IModalType
} from "@/app/baseline/utils/types/common"
import { modalSize } from "@/app/baseline/utils/layout-config"

interface Props {
	invoice: { [propName: string]: any } //发票信息
	model: string
	viewLog: boolean
	tbCellClassName?: (params: any) => string
}
const props = withDefaults(defineProps<Props>(), {
	viewLog: false
})

const emit = defineEmits<{
	(e: "update"): void
}>()

const { dictOptions, getDictByCodeList, dictFilter } = useDictInit()
const { showDelConfirm } = useMessageBoxInit()

/*-------------------初始化表格-start-------------------*/

const queryArrList = computed(() => [
	{
		name: "物资编码",
		key: "materialCode",
		placeholder: "请输入物资编码",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资名称",
		key: "materialLabel",
		placeholder: "请输入物资名称",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "规格型号",
		key: "version",
		placeholder: "请输入规格型号",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资性质",
		key: "attribute",
		type: "select",
		children: dictOptions.value.MATERIAL_NATURE,
		placeholder: "请选择物资性质"
	}
])

const tbInit = useTbInit()
const {
	tableCache,
	tableData,
	tableRef,
	tableLoading,
	tbBtnLoading,
	pageTotal,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	currentPage,
	onCurrentPageChange
} = tbInit
tbInit.tableProp = computed(() => {
	const ls: TableColumnType[] = [
		{
			label: "物资编码",
			prop: "materialCode",
			width: 130,
			fixed: "left",
			sortable: true
		},
		{ label: "物资名称", prop: "materialLabel", width: 150 },
		{ label: "规格型号", prop: "version", width: 150 },
		{ label: "技术参数", prop: "technicalParameter", minWidth: 120 },
		{ label: "物资性质", prop: "attribute", needSlot: true, width: 120 },
		{ label: "采购单位", prop: "buyUnit", needSlot: true, width: 90 },
		{ label: "入库批次", prop: "batchNo", width: 160 },
		{ label: "入库仓库名称", prop: "storeLabel", width: 160 },
		{
			label: "采购单价",
			prop: "unitPrice",
			needSlot: true,
			align: "right",
			width: 100
		},
		{ label: "入库数量", prop: "inStoreNum_view", align: "right", width: 90 },
		{ label: "出库数量", prop: "outStoreNum_view", align: "right", width: 90 },
		{ label: "开票数量", prop: "blueNum_view", align: "right", width: 90 },
		{
			label: "可开票数量",
			prop: "canNum_view",
			width: 90,
			align: "right",
			fixed: "right"
		},
		{
			label: "可红冲数量",
			prop: "canNum_view",
			width: 90,
			align: "right",
			fixed: "right"
		},
		{
			label: "本次红冲数量",
			prop: "num",
			needSlot: true,
			width: 120,
			align: "right",
			fixed: "right"
		},
		{
			label: "本次开票数量",
			prop: "num",
			needSlot: true,
			width: 120,
			align: "right",
			fixed: "right"
		},
		{
			label: "开票金额",
			prop: "amount",
			align: "right",
			needSlot: true,
			width: 120,
			fixed: "right"
		},
		{
			label: "采购订单号",
			prop: "purchaseOrderCode",
			width: 180,
			fixed: "right"
		},
		{
			label: "操作",
			prop: "operations",
			width: 80,
			fixed: "right",
			needSlot: true
		}
	]
	if (props.model == IModalType.view) {
		if (props.invoice.invoiceColor == IInvoiceColor.red) {
			return tableColFilter(ls, [
				"入库数量",
				"出库数量",
				"可开票数量",
				"入库仓库名称",
				"本次开票数量",
				"可红冲数量",
				"操作"
			])
		} else {
			// 蓝色
			return tableColFilter(ls, [
				"入库数量",
				"出库数量",
				"开票数量",
				"可红冲数量",
				"本次红冲数量",
				"操作"
			])
		}
	} else {
		if (props.invoice.invoiceColor == IInvoiceColor.red) {
			return tableColFilter(ls, [
				"可开票数量",
				"入库仓库名称",
				"本次开票数量",
				"操作"
			])
		} else {
			// 蓝色
			return tableColFilter(ls, [
				"出库数量",
				"开票数量",
				"可红冲数量",
				"本次红冲数量"
			])
		}
	}
})

fetchFunc.value = PurchaseInvoiceItemApi.getPurchaseInvoiceItemList
watch(tableData, () => {
	batchFormatterNumView(tableData.value as any)
})

/**
 * 查询 回调
 * @param data
 */
function getTableData(data?: { [propName: string]: any }) {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		...data
	}
	fetchTableData()
}

const tbBtnConf = computed(() => [
	{
		name: "添加开票物资",
		icon: ["fas", "circle-plus"]
	},
	{
		name: "批量移除",
		icon: ["fas", "trash-can"],
		disabled: selectedTableList.value.length > 0 ? false : true
	}
])

/*-------------------初始化表格-end-------------------*/

const formBtnList = [
	{ name: "取消", icon: ["fas", "circle-minus"] },
	{ name: "保存", icon: ["fas", "floppy-disk"] }
]
const mergeMatTitle = ref<any>({
	name: ["选择物资"],
	icon: ["fas", "square-share-nodes"]
})

const showInvoiceMatVisible = ref(false)
const addBtnLoading = ref(false)
const mergeMatCache = ref<{ [propName: string]: any }[]>([])

/**
 * 更新本次开票数量
 */
const validateInvoiceNum = debounce(async (e: any) => {
	const msgType =
		props.invoice.invoiceColor == IInvoiceColor.red
			? "本次红冲数量"
			: "本次开票数量"
	const num = toNumber(e.num)
	const canNum = toNumber(e.canNum)

	const unitPrice = toNumber(e.unitPrice)

	const oldRow = tableCache.find((v) => v.id == e.id)

	e.num = num

	if (num <= 0) {
		e.num = oldRow.num
		return ElMessage.warning(`${msgType}不能小于等于0！`)
	} else {
		const price = toNumber(num * unitPrice)

		if (price > maxValidateNum) {
			e.num = oldRow.num
			ElMessage.warning(
				`您的开票金额已超过${maxValidateErrorInfo}元，请重新核对金额！`
			)
			return false
		}

		if (num > canNum) {
			ElMessage.warning(`${msgType}不能大于可开票数量！`)
			e!.num = canNum
		}
	}

	await PurchaseInvoiceItemApi.updatePurchaseInvoiceItem({
		id: e.id,
		num: e.num
	})
	//  更新 tableCache
	const rowIdx = findIndex(tableCache, (v) => v.id === e.id)
	if (rowIdx !== -1) {
		tableCache.splice(rowIdx, 1, { ...e })
	}

	ElMessage.success("操作成功")
	emit("update")
}, 300)

/**
 * 开票物资选择器
 * @param btnName
 */
const onFormBtnList = async (btnName: string | undefined) => {
	if (btnName === "保存") {
		addBtnLoading.value = true
		try {
			const params = map(mergeMatCache.value, (v: Record<string, any>) => ({
				invoiceId: props.invoice.id,
				materialId: v.materialId,
				purchaseOrderId: v.purchaseOrderId,
				materialCode: v.materialCode,
				materialLabel: v.materialLabel,
				version: v.version,
				attribute: v.attribute,
				num: v.canNum,
				storeBatchItemId:
					props.invoice.invoiceColor === IInvoiceColor.blue
						? v.id
						: v.storeBatchItemId
			}))

			const idempotentToken = getIdempotentToken(
				IIdempotentTokenTypePre.other,
				IIdempotentTokenType.purchaseInvoice,
				props.invoice.id
			)
			await PurchaseInvoiceItemApi.addPurchaseInvoiceItem(
				params,
				idempotentToken
			)
			ElMessage.success("保存成功")
			showInvoiceMatVisible.value = false
			getTableData()
			emit("update")
		} finally {
			addBtnLoading.value = false
		}
	} else if (btnName === "取消") {
		showInvoiceMatVisible.value = false
	}
}

/**
 * 移除
 * @param rowData
 */
async function onRowDelete(rowData: any) {
	await showDelConfirm()
	await PurchaseInvoiceItemApi.deleteAllPurchaseInvoiceItem([rowData.id])
	ElMessage.success("移除成功")
	fetchTableData()
	emit("update")
}

/**
 * 添加开票物资/批量删除
 * @param btnName
 */
async function handleTbAction(btnName?: string) {
	if (btnName === "添加开票物资") {
		mergeMatCache.value = []
		showInvoiceMatVisible.value = true
	} else if (btnName === "批量移除") {
		const ids = map(selectedTableList.value, ({ id }) => id)
		tbBtnLoading.value = true
		try {
			await showDelConfirm()
			await PurchaseInvoiceItemApi.deleteAllPurchaseInvoiceItem(ids)
			ElMessage.success("移除成功")
			fetchTableData()
			emit("update")
		} finally {
			tbBtnLoading.value = false
		}
	}
}

onMounted(() => {
	fetchParam.value.invoiceId = props.invoice.id
	fetchParam.value.sord = "desc"
	fetchParam.value.sidx = "createdDate"
	getDictByCodeList(["INVENTORY_UNIT", "MATERIAL_NATURE"]).then(() =>
		fetchTableData()
	)
})

/**
 * 排序
 * @param column
 * @param prop 字段名称
 * @param order ascending 升序 descending 降序
 */
function handleSortChange(column: any, prop: any, order: any) {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		sord:
			order === "descending" ? "desc" : order === "ascending" ? "asc" : "desc",
		sidx: order ? prop : "createdDate" // 排序字段
	}

	fetchTableData()
}

defineExpose({ getTableData })
</script>
<template>
	<div class="mat-list-table error-table-wrapper">
		<Query
			:queryArrList="queryArrList"
			@getQueryData="getTableData"
			class="custom-q"
		/>
		<el-scrollbar>
			<PitayaTable
				ref="tableRef"
				:columns="(tbInit.tableProp as any)"
				:total="pageTotal"
				:table-data="tableData"
				:need-index="true"
				:needSelection="
					props.model != IModalType.view &&
					props.invoice.invoiceColor == IInvoiceColor.blue
						? true
						: false
				"
				:single-select="false"
				@onSelectionChange="selectedTableList = $event"
				@on-current-page-change="onCurrentPageChange"
				:table-loading="tableLoading"
				@on-table-sort-change="handleSortChange"
				:cell-class-name="tbCellClassName"
			>
				<!-- 物资性质 -->
				<template #attribute="{ rowData }">
					<dict-tag
						:options="DictApi.getMatAttr()"
						:value="rowData.attribute"
					/>
				</template>

				<template #buyUnit="{ rowData }">
					{{ dictFilter("INVENTORY_UNIT", rowData.buyUnit)?.label || "---" }}
				</template>

				<template #unitPrice="{ rowData }">
					<cost-tag :value="rowData.unitPrice" />
				</template>
				<template #num="{ rowData }">
					<el-input
						v-if="props.model != IModalType.view"
						class="no-arrows"
						v-model="rowData.num"
						@click.stop
						@input="rowData.num = validateAndCorrectInput($event)"
						@change="validateInvoiceNum(rowData)"
					/>
					<template v-else>{{ toFixedTwo(rowData.num || 0) }}</template>
				</template>
				<!-- <template #value0="{ rowData }"> 0 </template> -->
				<template #amount="{ rowData }">
					<cost-tag :value="(rowData.unitPrice || 0) * (rowData.num || 0)" />
				</template>

				<template #operations="{ rowData }">
					<el-button v-btn link @click.stop="onRowDelete(rowData)">
						<font-awesome-icon :icon="['fas', 'trash-can']" />
						<span class="table-inner-btn">移除</span>
					</el-button>
				</template>
				<template #footerOperateLeft>
					<ButtonList
						class="btn-list"
						:is-not-radius="true"
						:button="tbBtnConf"
						v-if="
							props.model != IModalType.view &&
							props.invoice.invoiceColor !== IInvoiceColor.red
						"
						:loading="tbBtnLoading"
						@on-btn-click="handleTbAction"
					/>
				</template>
			</PitayaTable>
		</el-scrollbar>
	</div>

	<!--	添加开票物资 -->
	<Drawer
		:size="modalSize.lg"
		v-model:drawer="showInvoiceMatVisible"
		:destroyOnClose="true"
	>
		<div class="drawer-container">
			<div class="drawer-column" style="width: 100%">
				<Title :title="mergeMatTitle" />
				<div class="rows">
					<TableMatItem
						:id="props.invoice.id"
						:invoiceColor="props.invoice.invoiceColor"
						:blueInvoiceId="props.invoice.blueInvoiceId"
						:needQuery="true"
						:needSelection="true"
						:isPublish="true"
						@onSelectChange="(datas) => (mergeMatCache = datas)"
					/>
				</div>
				<ButtonList
					class="footer"
					:button="formBtnList"
					v-loading="addBtnLoading"
					@on-btn-click="onFormBtnList"
				/>
			</div>
		</div>
	</Drawer>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.custom-q {
	margin: 10px 0px -10px 10px !important;
}

.footer {
	border-top: solid 1px $---border-color;
	padding-top: 10px;
	justify-content: flex-end;
}
.tab-mat {
	height: calc(100% - 80px);
}
.mat-list-table {
	display: flex;
	flex-direction: column;
}

.error-table-wrapper {
	&:deep(.pitaya-table) {
		.error {
			.column-inner-item,
			.cell,
			.el-input__inner {
				color: red;
			}
		}
	}
}
</style>
