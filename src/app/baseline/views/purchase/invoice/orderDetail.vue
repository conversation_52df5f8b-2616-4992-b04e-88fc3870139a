<!-- 合同编号 下钻 -->
<script lang="ts" setup>
import { defineProps, ref, withDefaults, reactive, onMounted } from "vue"

import { FormElementType } from "@/app/baseline/views/components/define"
import { useTbInit } from "@/app/baseline/views/components/tableBase"
import { PurchaseContractApi } from "@/app/baseline/api/purchase/purchaseContract"
import { PurchaseInvoiceApi } from "@/app/baseline/api/purchase/purchaseInvoice"
import { DictApi } from "@/app/baseline/api/dict"

import FormElement from "@/app/baseline/views/components/formElement.vue"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import CostTag from "@/app/baseline/views/components/costTag.vue"
import XEUtils from "xe-utils"

interface Props {
	contractId: any //合同编号
}
const props = withDefaults(defineProps<Props>(), {})
const emits = defineEmits(["onClosed"])

/*-------------------初始化表格-start-------------------*/
const {
	tableProp,
	tableData,
	tableLoading,
	pageTotal,
	selectedTableList,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = useTbInit()
tableProp.value = [
	{ label: "发票号码", prop: "code", minWidth: 100 },
	{ label: "开票金额", prop: "amount", needSlot: true },
	{ label: "开票日期", prop: "invoiceDate", needSlot: true },
	{ label: "发票状态", prop: "status", needSlot: true },
	{ label: "审批状态", prop: "bpmStatus", needSlot: true },
	{ label: "录入人员", prop: "createdBy_view" },
	{ label: "录入时间", prop: "createdDate", width: 160 }
]

fetchFunc.value = (param?: Record<string, any>) =>
	PurchaseInvoiceApi.getPurchaseInvoiceListByContract({
		...param,
		contractId: formModal.id,
		sord: "desc",
		sidx: "createdDate"
	} as any)

const loading = ref(true)
const curStatistics = ref<{ [propName: string]: any }>({})
const leftTitle = ref({
	name: ["合同基本信息"],
	icon: ["fas", "square-share-nodes"]
})
const rightTitle = ref({
	name: ["关联发票"],
	icon: ["fas", "square-share-nodes"]
})
const baseFormBtnList = ref([{ name: "取消", icon: ["fas", "circle-minus"] }])

const formEl: FormElementType[][] = reactive([
	[
		{ label: "合同编号", name: "contractCode", disabled: true },
		{ label: "合同名称", name: "contractLabel", disabled: true },
		{ label: "供应商名称", name: "supplierLabel", disabled: true },
		{ label: "合同金额", name: "contractAmount_view", disabled: true }
	]
])
const formModal = reactive({
	id: "",
	contractCode: "",
	contractLabel: "",
	supplierLabel: "",
	amount: ""
})

/**
 * 获取当前合同对应发票的统计信息
 */
//@Todo
function getStatistics() {
	curStatistics.value = {
		count: pageTotal, //已开票
		taxRate: "---" //税率
		// matCodeNum : 13,	//物资编码数量
		// amount : 9999,	//开票金额
	}
	Promise.resolve()
}

function getContractInfo() {
	return new Promise<void>((resolve) => {
		PurchaseContractApi.getPurchaseContract(props.contractId as any).then(
			(res: Record<string, any>) => {
				res.contractAmount_view = `￥${`${XEUtils.commafy(res.contractAmount, {
					digits: 5
				})}`}`
				Object.assign(formModal, res)
				resolve()
			}
		)
	})
}

function onFormBtnList() {
	emits("onClosed")
}

onMounted(() => {
	loading.value = true
	Promise.all([getStatistics()])
		.then(() => getContractInfo())
		.then(() => fetchTableData())
		.then(() => {
			loading.value = false
		})
})
</script>
<template>
	<div class="drawer-container" v-loading="loading">
		<!-- 左侧表单区域 -->
		<div class="drawer-column left">
			<div class="rows">
				<Title :title="leftTitle" />
				<div class="request-title">
					<div>
						<span>{{ curStatistics.count }}</span>
						<label>已开票量（张）</label>
					</div>
					<!-- <div>
						<span>{{ curStatistics.taxRate }}</span>
						<label>税率（%）</label>
					</div> -->
				</div>

				<el-form
					class="content"
					:model="formModal"
					label-position="top"
					label-width="100px"
					:needSingleSelect="true"
				>
					<FormElement :form-element="formEl" :form-data="formModal" />
				</el-form>
			</div>
		</div>
		<!-- 右侧table区域 -->
		<div class="drawer-column right">
			<div class="rows">
				<Title :title="rightTitle" />
				<PitayaTable
					ref="refTable"
					:columns="tableProp"
					:tableData="tableData"
					:total="pageTotal"
					:single-select="false"
					:need-selection="false"
					:need-index="true"
					@onSelectionChange="selectedTableList = $event"
					@on-current-page-change="onCurrentPageChange"
					:table-loading="tableLoading"
				>
					<template #amount="{ rowData }">
						<cost-tag :value="rowData.amount" />
					</template>
					<template #status="{ rowData }">
						<dict-tag
							:options="DictApi.getInvoiceStatus(rowData.invoiceColor)"
							:value="rowData.bpmStatus"
						/>
					</template>
					<template #invoiceDate="{ rowData }">
						<label :class="rowData.invoiceColor == '1' ? 'error' : 'info'">
							{{
								rowData.invoiceDate
									? XEUtils.toDateString(
											new Date(rowData.invoiceDate),
											"yyyy-MM-dd"
									  )
									: "---"
							}}
						</label>
					</template>
					<template #bpmStatus="{ rowData }">
						<dict-tag
							:options="DictApi.getBpmStatus()"
							:value="rowData.bpmStatus"
						/>
					</template>
				</PitayaTable>
			</div>
			<!--			<div class="bottom">
				<labeL>开票物资编码（项）：{{ curStatistics.matCodeNum }}</labeL>
				<label>开票金额：<cost-tag :value="curStatistics.amount"/></labeL>
				<label>税额：<cost-tag :value="`${curStatistics.amount * curStatistics.taxRate / 100}`"/></labeL>
			</div>-->
			<ButtonList
				class="footer"
				:button="baseFormBtnList"
				@on-btn-click="onFormBtnList"
			/>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.drawer-container {
	.left {
		width: 320px;
	}
	.right {
		width: calc(100% - 320px);
		.bottom {
			position: relative;
			top: 35px;
			display: flex;
			color: $---font-color-4;
			font-size: $---font-size-m;
			> label {
				padding-right: calc($---spacing-m * 2);
			}
		}
	}
}
.request-title {
	display: flex;
	padding: $---spacing-m;
	border-bottom: solid 1px $---border-color;
	justify-content: center;

	span {
		color: $---color-info;
		font-size: 30px;
		font-weight: bold;
	}
	label {
		font-size: $---font-size-m;
		color: $---font-color-2;
	}

	div {
		display: flex;
		flex-direction: column;
		width: 50%;
		align-items: center;
		/* &:last-child {
			border-left: solid 1px $---border-color;
		} */
	}
}
</style>
