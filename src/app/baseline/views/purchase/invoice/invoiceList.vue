<!-- 冲红对应蓝字发票 V2.0-->
<script lang="ts" setup>
import { ElMessage } from "element-plus"
import { ref, onMounted } from "vue"
import XEUtils from "xe-utils"
import { PurchaseInvoiceApi } from "@/app/baseline/api/purchase/purchaseInvoice"
import { useDictInit } from "@/app/baseline/views/components/dictBase"
import { appStatus, DictApi } from "@/app/baseline/api/dict"
import SelectDrawer from "@/app/baseline/views/components/selectDrawer.vue"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import CostTag from "@/app/baseline/views/components/costTag.vue"
import { BaseLineSysApi } from "@/app/baseline/api/system"
import { PurchaseSupplierApi } from "@/app/baseline/api/purchase/purchaseSupplier"

const { dictOptions, getDictByCodeList, dictFilter } = useDictInit()
interface Props {
	id: string
	contractId?: string
	userIds: string
	bpmStatus: string
	addedFalg?: string
}

const props = withDefaults(defineProps<Props>(), {
	userIds: undefined,
	addedFalg: "false"
})
const emits = defineEmits(["onSuccess", "onClosed"])
const selectId = props.id ? [props.id] : []
const drawerLoading = ref(false)
const queryArrList = computed<any[]>(() => [
	{
		name: "发票号码",
		key: "code",
		type: "input",
		placeholder: "请输入发票号码",
		enableFuzzy: true
	},
	{
		name: "发票类型",
		key: "type",
		type: "select",
		children: [],
		placeholder: "请选择发票类型"
	},
	{
		name: "供应商",
		key: "supplierId",
		type: "tableSelect",
		placeholder: "请选择供应商",
		tableInfo: [
			{
				title: "请选择供应商",
				tableApi: PurchaseSupplierApi.getPurchaseSupplierList, //表格接口
				tableColumns: [
					{ label: "供应商编码", prop: "code", width: 160 },
					{ label: "供应商名称", prop: "label" }
				],
				queryArrList: [
					{
						name: "供应商编码",
						key: "code",
						type: "input",
						placeholder: "请输入供应商编码"
					},
					{
						name: "供应商名称",
						key: "label",
						type: "input",
						placeholder: "请输入供应商名称"
					}
				],
				params: {
					currentPage: 1,
					pageSize: 20
				}, //表格接口参数

				labelName: {
					key: "id", //回显标识
					label: "label", //input框要展示的字段
					value: "id" //要给后台传的字段
				}
			}
		]
	}
])
const tableProp = [
	{ label: "发票号码", prop: "code", needSlot: true, width: 160 },
	{ label: "发票类型", prop: "type", needSlot: true, width: 150 },
	{
		label: "开票金额",
		prop: "amount",
		needSlot: true,
		align: "right",
		width: 180
	},
	{ label: "供应商名称", prop: "supplierLabel", needSlot: true, minWidth: 150 },
	{ label: "开票日期", prop: "invoiceDate", needSlot: true, width: 160 },
	{ label: "发票状态", prop: "status", needSlot: true, width: 90 },
	{ label: "审批状态", prop: "bpmStatus", needSlot: true, width: 90 },
	{ label: "录入人员", prop: "createdBy_view", needSlot: true, width: 90 },
	{ label: "录入时间", prop: "createdDate", width: 160 }
]
const tableApi = (param: any) =>
	PurchaseInvoiceApi.getPurchaseInvoiceList({
		sord: "desc",
		sidx: "createdDate",
		bpmStatus: appStatus.approved,
		invoiceColor: "0",
		addedFalg: props.addedFalg,
		contractId: props.contractId,
		...param
	})
const fetchParam = {}
// 左侧按钮点击
const onSave = (rowList: any[]) => {
	if (rowList.length <= 0) {
		ElMessage.warning("请选择")
		return
	}
	emits("onSuccess", rowList[0])
	drawerLoading.value = false
}
const onClose = () => {
	emits("onClosed")
}

onMounted(() => {
	getDictByCodeList(["INVOICE_TYPE", "INVOICE_STATUS"]).then(() => {
		queryArrList.value.find((_q) => _q.key == "type").children =
			dictOptions.value.INVOICE_TYPE
	})
})

defineOptions({
	name: "SelectDrawer"
})
</script>
<template>
	<SelectDrawer
		title="发票信息"
		:query-arr-list="queryArrList"
		:table-prop="tableProp"
		:table-api="tableApi"
		:fetch-param="fetchParam"
		:selected="selectId"
		:loading="drawerLoading"
		:needSingleSelect="true"
		@on-close="onClose"
		@on-save="onSave"
	>
		<template #code="{ rowData }">
			<label :class="rowData.invoiceColor == '1' ? 'error' : 'info'">{{
				rowData.code || "---"
			}}</label>
		</template>
		<template #type="{ rowData }">
			<label :class="rowData.invoiceColor == '1' ? 'error' : 'info'">
				{{ dictFilter("INVOICE_TYPE", rowData.type)?.label || "---" }}
			</label>
		</template>
		<template #supplierLabel="{ rowData }">
			<label :class="rowData.invoiceColor == '1' ? 'error' : 'info'">{{
				rowData.supplierLabel || "---"
			}}</label>
		</template>
		<template #invoiceDate="{ rowData }">
			<label :class="rowData.invoiceColor == '1' ? 'error' : 'info'">
				{{
					rowData.invoiceDate
						? XEUtils.toDateString(new Date(rowData.invoiceDate), "yyyy-MM-dd")
						: "---"
				}}
			</label>
		</template>
		<template #createdBy_view="{ rowData }">
			<label :class="rowData.invoiceColor == '1' ? 'error' : 'info'">{{
				rowData.createdBy_view || "---"
			}}</label>
		</template>
		<template #amount="{ rowData }">
			<label :class="rowData.invoiceColor == '1' ? 'error' : 'info'">
				<cost-tag :value="rowData.amount" />
			</label>
		</template>
		<template #status="{ rowData }">
			<dict-tag
				:options="DictApi.getInvoiceStatus(rowData.invoiceColor)"
				:value="rowData.bpmStatus"
			/>
		</template>
		<template #bpmStatus="{ rowData }">
			<dict-tag :options="DictApi.getBpmStatus()" :value="rowData.bpmStatus" />
		</template>
	</SelectDrawer>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index";

.drawer-container {
	.right {
		width: 100%;
	}
}
label.error {
	color: $---color-error;
}
label.info {
	color: $---color-info;
}
</style>
