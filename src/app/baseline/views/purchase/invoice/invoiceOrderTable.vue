<!-- 采购订单 table V2.0 -->
<script lang="ts" setup>
import { defineProps, withDefaults, ref, onMounted } from "vue"
import { ElMessage } from "element-plus"
import { useTbInit } from "@/app/baseline/views/components/tableBase"
import { powerList } from "@/app/baseline/views/components/define.d"
import { PurchaseInvoiceOrderApi } from "@/app/baseline/api/purchase/purchaseInvoiceOrder"
import { useMessageBoxInit } from "@/app/baseline/views/components/messageBox"
import XEUtils from "xe-utils"

import invoiceOrderSelector from "./invoiceOrderSelector.vue"
import LinkTag from "@/app/baseline/views/components/linkTag.vue"
import CostTag from "@/app/baseline/views/components/costTag.vue"
import OrderDetail from "@/app/baseline/views/purchase/invoice/orderDetail.vue"
import {
	IIdempotentTokenType,
	IIdempotentTokenTypePre,
	IModalType
} from "@/app/baseline/utils/types/common"
import { tableColFilter } from "@/app/baseline/utils"
import { modalSize } from "@/app/baseline/utils/layout-config"
import { map } from "lodash-es"
import { IInvoiceColor } from "@/app/baseline/api/dict"
import { getIdempotentToken } from "@/app/baseline/utils/validate"

interface Props {
	invoiceId: number //发票编号
	supplierId: number //供应商编号
	needSelected: boolean
	model: string
	invoiceColor: string
	supplierLabel: string
	blueInvoiceId: number
}

const props = withDefaults(defineProps<Props>(), {
	needSelected: true
})

const emit = defineEmits<{
	(e: "update"): void
}>()

const { showDelConfirm } = useMessageBoxInit()

const tbInit = useTbInit()
const {
	tableData,
	tableRef,
	tableLoading,
	currentPage,
	pageTotal,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange,
	onBtnClick
} = tbInit

tbInit.tableProp = computed(() => {
	const defColumns: TableColumnType[] = [
		{ label: "采购订单号", prop: "content_code", width: 180, fixed: "left" },
		{ label: "采购订单名称", prop: "content_label", minWidth: 150 },
		{ label: "采购计划号", prop: "content_planPurchaseCode", width: 160 },
		{ label: "采购项目编号", prop: "content_projectCode", width: 200 },
		{ label: "采购项目名称", prop: "content_projectLabel", minWidth: 150 },
		{
			label: "订货金额",
			prop: "content_orderingAmount",
			needSlot: true,
			align: "right",
			width: 130
		},
		{ label: "供应商名称", prop: "content_supplierLabel", minWidth: 150 },
		{
			label: "合同编号",
			prop: "content_contractCode",
			needSlot: true,
			minWidth: 150,
			fixed: "right"
		},
		{
			label: "操作",
			prop: "operations",
			width: 80,
			needSlot: true,
			fixed: "right"
		}
	]

	if (
		props.model === IModalType.view ||
		props.invoiceColor === IInvoiceColor.red
	) {
		return tableColFilter(defColumns, ["操作"])
	} else {
		return defColumns
	}
})

fetchFunc.value = PurchaseInvoiceOrderApi.getPurchaseInvoiceOrderList

watch(tableData, () => {
	XEUtils.map(tableData.value as any, (_d: { [propName: string]: any }) => {
		if (_d.content)
			Array.from(Object.keys(_d?.content))?.map(
				(_k) => (_d[`content_${_k}`] = _d.content[_k])
			)
	})
})

const getTableData = (data?: any) => {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		...data
	}
	fetchTableData()
}
/**********************初始化table *********************************/

//标题
const comOrderListTitle = computed(() => ({
	name: [`采购订单【${props.supplierLabel}】`],
	icon: ["fas", "square-share-nodes"]
}))

tbInit.tbBtns.value = [
	{
		name: "添加采购订单",
		//roles: powerList.mergePlanBtnCreate,
		icon: ["fas", "circle-plus"],
		click: () => {
			orderSelectorVisible.value = true
			return Promise.reject()
		}
	}
]

const orderSelectorVisible = ref(false)
const contractDetailVisible = ref(false)
const editOrderId = ref()

/**
 * 添加采购订单 保存
 * @param datas
 */
async function handleSaveOrder(datas: any[]) {
	const orderIdList = map(datas, (v) => {
		return props.invoiceColor === IInvoiceColor.blue ? v.id : v.orderId
	})

	const idempotentToken = getIdempotentToken(
		IIdempotentTokenTypePre.other,
		IIdempotentTokenType.purchaseInvoice,
		props.invoiceId
	)
	await PurchaseInvoiceOrderApi.addPurchaseInvoiceOrder(
		{
			invoiceId: props.invoiceId,
			orderIdList
		},
		idempotentToken
	)

	orderSelectorVisible.value = false
	ElMessage.success("新增成功")
	emit("update")
	getTableData()
}

/**
 * 合同编号 下钻查看
 * @param orderInfo
 */
function handleContractDetail(orderInfo: any) {
	editOrderId.value = orderInfo.content_contractId
	contractDetailVisible.value = true
}

/**
 * 移除
 * @param row
 */
async function handleDelOrder(row: any) {
	await showDelConfirm()
	await PurchaseInvoiceOrderApi.deletePurchaseInvoiceOrder(row.id)
	ElMessage.success("移除成功")
	fetchTableData()
	emit("update")
}

onMounted(async () => {
	fetchParam.value.invoiceId = props.invoiceId
	fetchParam.value.sord = "desc"
	fetchParam.value.sidx = "createdDate"
	//getPurchaseSupplier()
	getTableData()
})

defineExpose({
	getTableData
})

defineOptions({
	name: "Order"
})
</script>
<template>
	<PitayaTable
		ref="tableRef"
		:columns="(tbInit.tableProp as any)"
		:tableData="tableData"
		:total="pageTotal"
		:single-select="true"
		:need-selection="props.needSelected"
		:need-index="true"
		@onSelectionChange="selectedTableList = $event"
		@on-current-page-change="onCurrentPageChange"
		:table-loading="tableLoading"
	>
		<!-- 合同编号 -->
		<template #content_contractCode="{ rowData }">
			<link-tag
				:value="rowData.content_contractCode"
				@on-click="handleContractDetail(rowData)"
			/>
		</template>

		<!-- 订货金额 -->
		<template #content_orderingAmount="{ rowData }">
			<cost-tag :value="rowData.content_orderingAmount" />
		</template>
		<template #operations="{ rowData }">
			<el-button v-btn link @click.stop="handleDelOrder(rowData)">
				<font-awesome-icon :icon="['fas', 'trash-can']" />
				<span class="table-inner-btn">移除</span>
			</el-button>
		</template>
		<template #footerOperateLeft>
			<!-- 红色发票时 没有按钮操作 -->
			<ButtonList
				v-if="
					props.model != IModalType.view &&
					props.invoiceColor !== IInvoiceColor.red
				"
				class="btn-list"
				:is-not-radius="true"
				:button="(tbInit.tbBtns as any)"
				@on-btn-click="onBtnClick"
			/>
		</template>
	</PitayaTable>

	<!-- 合同编号  下钻查看 -->
	<Drawer
		:size="modalSize.lg"
		:supplierId="props.supplierId"
		v-model:drawer="contractDetailVisible"
		:destroyOnClose="true"
	>
		<OrderDetail
			:contractId="editOrderId"
			@onClosed="contractDetailVisible = false"
		/>
	</Drawer>

	<!-- 添加采购订单 -->
	<Drawer
		:size="modalSize.lg"
		v-model:drawer="orderSelectorVisible"
		:destroyOnClose="true"
	>
		<div class="drawer-container">
			<div class="drawer-column" style="width: 100%">
				<Title :title="comOrderListTitle" />
				<invoiceOrderSelector
					:invoiceId="props.invoiceId"
					:invoiceColor="props.invoiceColor"
					:blueInvoiceId="props.blueInvoiceId"
					@onSelected="handleSaveOrder"
					@onClosed="orderSelectorVisible = false"
				/>
			</div>
		</div>
	</Drawer>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
