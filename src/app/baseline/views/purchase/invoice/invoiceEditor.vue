<!-- 发票管理 编辑 V2.0 -->
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<!-- 左侧表单区域 -->
		<div class="drawer-column left">
			<div class="rows">
				<Title :title="drawerLeftTitle" />
				<el-scrollbar>
					<el-form
						class="content"
						:model="formData"
						:rules="formRules"
						ref="formRef"
						label-position="top"
						label-width="100px"
						:needSingleSelect="true"
						:validate-on-rule-change="false"
					>
						<FormElement :form-element="formEl" :form-data="formData" />
					</el-form>
				</el-scrollbar>
				<ButtonList
					class="footer"
					:button="baseFormBtnList"
					:loading="btnLoading"
					@on-btn-click="handleFormBtnClick"
				/>
			</div>
		</div>
		<div class="drawer-column right" :class="!canEditExtra ? 'disabled' : ''">
			<div class="rows">
				<Title :title="drawerRightTitle">
					<Tabs
						class="tabs"
						:tabs="tabList"
						:active-index="activeTab"
						@on-tab-change="handleTabChange"
					/>
				</Title>
				<div class="detail-table">
					<invoice-order-table
						v-if="activeTab == 0 && formData.id"
						:supplierId="formData.supplierId"
						:supplierLabel="formData.supplierLabel"
						:invoiceId="formData.id"
						:invoiceColor="formData.invoiceColor"
						:blueInvoiceId="formData.blueInvoiceId"
						:needSelected="false"
						:model="props.mode"
						@update="handleUpdateAction"
						ref="invoiceOrderTableRef"
					/>
					<invoice-list-table
						v-if="activeTab == 1 && formData.id"
						:invoice="formData"
						:model="props.mode"
						@update="handleUpdateAction"
						ref="matTableRef"
						:tb-cell-class-name="tbCellClassName"
					/>
					<TableFile
						v-if="activeTab == 2 && formData.id"
						ref="refFileTable"
						:mod="props.mode"
						:businessId="formData.id"
						:businessType="fileBusinessType.invoiceInfo"
						:needPage="true"
					/>
				</div>
			</div>
			<ButtonList
				v-if="submitBtnList.length > 0"
				class="footer"
				:button="submitBtnList"
				:loading="btnLoading"
				@on-btn-click="handleFormBtnClick"
			/>
		</div>

		<!-- 合同 选择器 -->
		<Drawer
			:size="modalSize.lg"
			v-model:drawer="showContractVisible"
			:destroyOnClose="true"
		>
			<selectContract
				:id="formData.contractId"
				@onSuccess="(selectRow) => onCloseDrawer(true, selectRow)"
				@onClosed="showContractVisible = false"
			/>
		</Drawer>

		<!-- 冲红对应蓝字发票 选择器 -->
		<Drawer
			:size="modalSize.lg"
			v-model:drawer="showInvoiceVisible"
			:destroyOnClose="true"
		>
			<invoiceList
				:id="formData.blueInvoiceId"
				:contractId="formData.contractId"
				:bpmStatus="appStatus.approved"
				:addedFalg="`true`"
				@onSuccess="(selectRow) => onCloseInvoiceDrawer(true, selectRow)"
				@onClosed="showInvoiceVisible = false"
			/>
		</Drawer>
	</div>
</template>
<script setup lang="ts">
import { FormRules } from "element-plus"
import { FormElementType } from "../../components/define"
import { useDictInit } from "../../components/dictBase"
import FormElement from "@/app/baseline/views/components/formElement.vue"
import {
	IInvoiceColor,
	appStatus,
	fileBusinessType
} from "@/app/baseline/api/dict"
import {
	IIdempotentTokenType,
	IIdempotentTokenTypePre,
	IModalType
} from "@/app/baseline/utils/types/common"
import { PurchaseInvoiceApi } from "@/app/baseline/api/purchase/purchaseInvoice"
import selectContract from "@/app/baseline/views/purchase/contract/selectContract.vue"
import invoiceList from "@/app/baseline/views/purchase/invoice/invoiceList.vue"

import invoiceOrderTable from "./invoiceOrderTable.vue"
import invoiceListTable from "./invoiceListTable.vue"
import XEUtils from "xe-utils"
import { modalSize } from "@/app/baseline/utils/layout-config"
import TableFile from "@/app/baseline/views/components/tableFile.vue"
import { getModalTypeLabel, toMoney } from "@/app/baseline/utils"
import { useMessageBoxInit } from "../../components/messageBox"
import { includes } from "lodash-es"
import { getIdempotentToken } from "@/app/baseline/utils/validate"

const props = defineProps<{
	/**
	 * 业务id
	 */
	id: any

	/**
	 * 编辑模式：编辑/新建
	 */
	mode: IModalType
}>()

const emit = defineEmits<{
	(e: "close"): void
	(e: "update"): void
}>()

const { showWarnConfirm } = useMessageBoxInit()

const invoiceOrderTableRef = ref()
const formRef = ref()

const { dictOptions, getDictByCodeList } = useDictInit()

const drawerLoading = ref(false)
const btnLoading = ref(false)

const drawerLeftTitle = computed(() => ({
	name: [
		getModalTypeLabel(
			canEditExtra.value ? IModalType.edit : IModalType.create,
			"发票信息"
		)
	],
	icon: ["fas", "square-share-nodes"]
}))

const drawerRightTitle = {
	name: ["扩展信息"],
	icon: ["fas", "square-share-nodes"]
}

const baseFormBtnList = [
	{ name: "取消", icon: ["fas", "circle-minus"] },
	{ name: "保存草稿", icon: ["fas", "floppy-disk"] }
]

const submitBtnList = [{ name: "提交审核", icon: ["fas", "circle-check"] }]

const tabList = ref(["采购订单", "发票清单", "相关附件"])

const activeTab = ref(0)
const handleTabChange = (index: number) => {
	activeTab.value = index
}

const formData = ref<Record<string, any>>({
	/* amount_view: "￥0.00" */
})

/**
 * 保存旧的表单数据
 */
const oldFormData = ref<string>()

/**
 * 能否编辑扩展信息
 */
const canEditExtra = computed(() => Boolean(formData.value?.id || props.id))

const showContractVisible = ref(false)

const showInvoiceVisible = ref(false)
/**
 * 表单配置
 */
const formEl = computed<FormElementType[][]>(() => {
	return [
		[
			{ label: "发票号码", name: "code", maxlength: 50 },
			{
				label: "合同名称",
				name: "contractLabel",
				vname: "contractId",
				type: "drawer",
				disabled: canEditExtra.value,
				clickApi: () => {
					showContractVisible.value = true
				}
			},
			{
				label: "合同编号",
				name: "contractCode",
				disabled: true
			},
			{
				label: "供应商名称",
				name: "supplierLabel",
				vname: "supplierId",
				disabled: true
			},
			{ label: "开票日期", name: "invoiceDate", type: "date" },
			{
				label: "发票类型",
				name: "type",
				type: "select",
				data: dictOptions.value.INVOICE_TYPE,
				placeholder: "请选择发票类型",
				clear: false
			},
			{
				label: "发票颜色",
				name: "invoiceColor",
				type: "select",
				data: dictOptions.value.INVOICE_COLOR,
				disabled: canEditExtra.value,
				placeholder: "请选择发票类型"
			},
			{
				label: "冲红对应蓝字发票",
				name: "blueInvoiceCode",
				vname: "blueInvoiceId",
				type:
					formData.value.invoiceColor == IInvoiceColor.red
						? "drawer"
						: "hidden",
				disabled: canEditExtra.value,
				clickApi: () => {
					showInvoiceVisible.value = true
				}
			},
			{ label: "开票金额", name: "amount_view", type: "money", disabled: true },
			{
				label: "税率",
				name: "taxRate",
				type: "select",
				disabled:
					formData.value.invoiceColor == IInvoiceColor.red ? true : false,
				data: [
					{ label: "6%", value: 6 },
					{ label: "9%", value: 9 },
					{ label: "13%", value: 13 }
				]
			},
			{
				label: "备注说明",
				name: "remark",
				type: "textarea",
				rows: 5,
				maxlength: 200
			}
		]
	]
})

const formRules = computed<FormRules<typeof formData.value>>(() => {
	return {
		contractLabel: { required: true, message: "采购合同", trigger: "change" },
		code: { required: true, message: "发票号码不能为空", trigger: "change" },
		taxRate: {
			required: formData.value.invoiceColor == IInvoiceColor.blue,
			message: "发票税率不能为空",
			trigger: "change"
		},
		invoiceDate: {
			required: true,
			message: "开票日期不能为空",
			trigger: "change"
		},
		type: { required: true, message: "发票类型不能为空", trigger: "change" },
		invoiceColor: {
			required: true,
			message: "发票颜色不能为空",
			trigger: "change"
		},
		blueInvoiceCode: {
			message: "冲红对应蓝字发票不能为空",
			trigger: "change",
			required: formData.value.invoiceColor == IInvoiceColor.red
		}
	}
})

async function handleFormBtnClick(btnName?: string) {
	if (btnName == "保存草稿") {
		formRef.value.validate(async (valid: any) => {
			if (!valid) {
				return false
			}
			if (valid) {
				btnLoading.value = true

				try {
					const api = canEditExtra.value
						? PurchaseInvoiceApi.updatePurchaseInvoice
						: PurchaseInvoiceApi.addPurchaseInvoice

					let idempotentToken = ""
					if (!canEditExtra.value) {
						idempotentToken = getIdempotentToken(
							IIdempotentTokenTypePre.apply,
							IIdempotentTokenType.purchaseInvoice
						)
					}

					const res = await api(formData.value as any, idempotentToken)

					formData.value.id = res.id

					formData.value["amount_view"] = toMoney(res.amount as any)

					oldFormData.value = JSON.stringify(formData.value)
					ElMessage.success("操作成功")
					emit("update")
					invoiceOrderTableRef.value?.getTableData()
					matTableRef.value?.getTableData()
				} finally {
					btnLoading.value = false
				}
			}
		})
	} else if (btnName === "提交审核") {
		formRef.value.validate(async (valid: any) => {
			if (!valid) {
				return false
			}
			if (valid) {
				await showWarnConfirm("请确认是否提交本次数据？")

				btnLoading.value = true
				drawerLoading.value = true

				try {
					// 如果主表有修改，则先更新主表数据
					if (oldFormData.value != JSON.stringify(formData.value)) {
						await PurchaseInvoiceApi.updatePurchaseInvoice({
							...formData.value
						} as any)
					}

					const idempotentToken = getIdempotentToken(
						IIdempotentTokenTypePre.other,
						IIdempotentTokenType.purchaseInvoice,
						formData.value.id
					)

					const { code, data, msg } =
						await PurchaseInvoiceApi.publishPurchaseInvoice(
							formData.value.id,
							idempotentToken
						)

					if (data && code != 200) {
						errorGoodsIdList.value = data || []
						ElMessage.error(msg)
						activeTab.value = 1
						matTableRef.value?.getTableData()
					} else {
						ElMessage.success("提交审核成功")
						emit("update")
						emit("close")
					}
				} finally {
					btnLoading.value = false
					drawerLoading.value = false
				}
			}
		})
	} else {
		emit("close")
	}
}
async function getDetail() {
	if (canEditExtra.value) {
		drawerLoading.value = true
		try {
			const res = await PurchaseInvoiceApi.getPurchaseInvoice(
				props.id || formData.value?.id
			)
			formData.value = { ...res }

			formData.value["amount_view"] = toMoney(res.amount as any)

			oldFormData.value = JSON.stringify(formData.value)
		} finally {
			drawerLoading.value = false
		}
	}
}

const matTableRef = ref()
/**
 * 物资明细 table 行样式
 *
 * 库存物资异常，用红色标记该行
 */
const errorGoodsIdList = ref<number[]>([])
function tbCellClassName({ row }: any) {
	return includes(errorGoodsIdList.value, row.id) ? "error" : ""
}
onMounted(() => {
	getDictByCodeList(["INVOICE_TYPE", "INVOICE_STATUS", "INVOICE_COLOR"])
	getDetail()
})

function handleUpdateAction() {
	getDetail()
	emit("update")
}

// 选择合同弹窗关闭
const onCloseDrawer = (refresh: boolean, selectRow: any) => {
	if (refresh) {
		formData.value.contractId = selectRow.id
		formData.value.contractLabel = selectRow.contractLabel
		formData.value.contractCode = selectRow.contractCode
		formData.value.supplierId = selectRow.supplierId
		formData.value.supplierLabel = selectRow.supplierLabel
		formData.value.blueInvoiceId = ""
		formData.value.blueInvoiceCode = ""
		showContractVisible.value = false
		setTimeout(() => {
			formRef.value?.clearValidate()
		}, 0)
	}
}

// 选择发票弹窗关闭
const onCloseInvoiceDrawer = (refresh: boolean, selectRow: any) => {
	if (refresh) {
		formData.value.blueInvoiceId = selectRow.id
		formData.value.blueInvoiceCode = selectRow.code
		formData.value.taxRate = selectRow.taxRate
		showInvoiceVisible.value = false
	}
}
</script>
<style scoped lang="scss">
@import "@/app/baseline/assets/css/index.scss";
$--left-width: 350px;
.drawer-column {
	&.left {
		width: $--left-width;
		:deep(.el-descriptions__label) {
			width: 220px;
		}
		.el-scrollbar {
			height: calc(100% - 73px);
		}
	}
	&.right {
		width: calc(100% - $--left-width);
		.detail-table {
			height: 100%;
		}
	}
}
</style>
