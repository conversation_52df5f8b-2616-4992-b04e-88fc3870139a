<!-- 发票管理 详情 添加采购订单选择器 V2.0 -->
<script lang="ts" setup>
import { defineProps, toValue, withDefaults, onMounted } from "vue"
import { commonBoolan, IInvoiceColor } from "@/app/baseline/api/dict"
import { useTbInit } from "@/app/baseline/views/components/tableBase"
import { PurchaseInvoiceOrderApi } from "@/app/baseline/api/purchase/purchaseInvoiceOrder"
import CostTag from "@/app/baseline/views/components/costTag.vue"
import XEUtils from "xe-utils"

interface Props {
	invoiceId: string | number //当前发票ID
	needSelected: boolean
	singleSelect: boolean
	invoiceColor: string
	blueInvoiceId: number
}

const props = withDefaults(defineProps<Props>(), {
	needSelected: true,
	singleSelect: false
})
const emits = defineEmits(["onClosed", "onSelected"])

/**********************初始化table *********************************/
const queryArrList = [
	{
		name: "采购订单号",
		key: "purchaseOrderCode",
		placeholder: "请输入采购订单号",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "采购订单名称",
		key: "purchaseOrderLabel",
		placeholder: "请输入采购订单名称",
		enableFuzzy: true,
		type: "input"
	}
]

const tbInit = useTbInit()
const {
	tableData,
	tableRef,
	tableLoading,
	currentPage,
	pageTotal,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = tbInit

tbInit.tableProp = computed(() => {
	if (props.invoiceColor === IInvoiceColor.blue) {
		const defCols: TableColumnType[] = [
			{ label: "采购订单号", prop: "code", width: 200 },
			{ label: "采购订单名称", prop: "label", minWidth: 200 },
			{ label: "采购计划号", prop: "planPurchaseCode", width: 200 },
			{ label: "采购项目编号", prop: "projectCode", width: 200 },
			{ label: "采购项目名称", prop: "projectLabel", minWidth: 200 },
			{
				label: "订货金额",
				prop: "orderingAmount",
				needSlot: true,
				width: 130,
				align: "right"
			}
		]
		return defCols
	} else {
		return [
			{ label: "采购订单号", prop: "content_code", width: 200 },
			{ label: "采购订单名称", prop: "content_label", minWidth: 200 },
			{ label: "采购计划号", prop: "content_planPurchaseCode", width: 200 },
			{ label: "采购项目编号", prop: "content_projectCode", width: 200 },
			{ label: "采购项目名称", prop: "content_projectLabel", minWidth: 200 },
			{
				label: "订货金额",
				prop: "content_orderingAmount",
				needSlot: true,
				width: 130,
				align: "right"
			}
		]
	}
})

fetchFunc.value = (params: Record<string, any>) => {
	if (props.invoiceColor === IInvoiceColor.red) {
		return PurchaseInvoiceOrderApi.getPurchaseInvoiceOrderList({
			redInvoiceId: props.invoiceId,
			blueInvoiceId: props.blueInvoiceId,
			...params
		} as any)
	} else {
		return PurchaseInvoiceOrderApi.getPurchaseInvoiceOrderListCanAdd({
			id: props.invoiceId as any,
			confirmStatus: commonBoolan.true,
			...params
		} as any)
	}
}

const getTableData = (data?: any) => {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		...data
	}
	fetchTableData()
}
/**********************初始化table *********************************/

const formBtnList = computed(() => [
	{ name: "取消", icon: ["fas", "circle-minus"] },
	{
		name: "保存",
		icon: ["fas", "floppy-disk"],
		disabled: selectedTableList.value.length > 0 ? false : true
	}
])

function onFormBtnList(btnName?: string) {
	if (btnName == "保存") {
		emits("onSelected", toValue(selectedTableList))
	} else emits("onClosed")
}

onMounted(() => {
	fetchParam.value.sord = "desc"
	fetchParam.value.sidx = "createdDate"

	getTableData()
})

watch(tableData, () => {
	XEUtils.map(tableData.value as any, (_d: { [propName: string]: any }) => {
		if (_d.content)
			Array.from(Object.keys(_d?.content))?.map(
				(_k) => (_d[`content_${_k}`] = _d.content[_k])
			)
	})
})

defineOptions({
	name: "Order"
})
</script>
<template>
	<div class="rows">
		<Query
			:queryArrList="queryArrList"
			@getQueryData="getTableData"
			class="custom-q"
		/>
		<el-scrollbar>
			<PitayaTable
				ref="tableRef"
				:columns="(tbInit.tableProp as any)"
				:tableData="tableData"
				:total="pageTotal"
				:single-select="props.singleSelect"
				:need-selection="props.needSelected"
				:need-index="true"
				@onSelectionChange="selectedTableList = $event"
				@on-current-page-change="onCurrentPageChange"
				:table-loading="tableLoading"
			>
				<template #orderingAmount="{ rowData }">
					<cost-tag :value="rowData.orderingAmount" />
				</template>

				<template #content_orderingAmount="{ rowData }">
					<cost-tag :value="rowData.content_orderingAmount" />
				</template>
			</PitayaTable>
		</el-scrollbar>
	</div>
	<ButtonList
		v-if="formBtnList.length > 0"
		class="common-btn-list-wrapper footer"
		:button="formBtnList"
		@on-btn-click="onFormBtnList"
	/>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.custom-q {
	margin: 10px 0px -10px 10px !important;
}
.tab-mat {
	display: flex;
	flex-direction: column;
	height: 100%;
	width: 100%;
}
</style>
