//数据表全量字段定义
import { BaseLineSysApi } from "../../../api/system"
import { DictApi, purchaseOrderSource } from "../../../api/dict"
import { FormElementType } from "../../components/define"

export interface PurchaseOrderVo {
	/**
	 * 采购金额
	 */
	amount?: number | null
	/**
	 * 到货状态;0未到货 1部分到货 2已到货
	 */
	arrivalStatus?: null | string
	/**
	 * 审批状态;0待提交，1审批中，2已审批，3已驳回
	 */
	bpmStatus?: null | string
	/**
	 * 采购订单号
	 */
	code?: null | string
	/**
	 * 合同编号
	 */
	contractCode?: null | string
	/**
	 * 创建人
	 */
	createdBy?: null | string
	/**
	 * 创建时间
	 */
	createdDate?: null | string
	/**
	 * 外部单号
	 */
	externalOrder?: null | string
	/**
	 * ID
	 */
	id?: string | number | null
	/**
	 * 采购订单名称
	 */
	label?: null | string
	/**
	 * 修改人
	 */
	lastModifiedBy?: null | string
	/**
	 * 修改时间
	 */
	lastModifiedDate?: null | string
	/**
	 * 最晚送货日期
	 */
	latestDeliveryDate?: null | string
	/**
	 * 通知供应商状态;0待通知 1已通知
	 */
	notifySuppliersStatus?: null | string
	/**
	 * 采购计划ID
	 */
	planId?: number | null
	/**
	 * 订货计划ID
	 */
	purchasePlanId?: number | null
	/**
	 * 采购员ID
	 */
	purchaserId?: number | null
	/**
	 * 备注
	 */
	remark?: null | string
	/**
	 * 订单来源
	 */
	source?: null | string
	/**
	 * 订单状态;0待处理 1已完成
	 */
	status?: null | string
	/**
	 * 供货商ID
	 */
	supplierId?: number | null
	/**
	 * 计划年度
	 */
	year?: null | string
	contractId?: string | number | null
	contractLabel?: null | string
	supplierLabel?: null | string
	supplierCode?: null | string
	matCodeNum?: number
	orderingNum?: number
	purchaseAmount?: number
	confirmStatus?: string
	purchaseUserId?: string
	[property: string]: any
}
/**
 * 数据表全量字段定义
 */
const orderTableProps = [
	{ label: "年度", prop: "year", width: 85, fixed: "left" },
	{ label: "月份", prop: "month", width: 85, fixed: "left" },
	{ label: "采购订单号", prop: "code", width: 180, fixed: "left" },
	{ label: "采购订单名称", prop: "label", minWidth: 250 },
	{ label: "审批状态", prop: "bpmStatus", needSlot: true, width: 90 },
	{ label: "订单来源", prop: "source", needSlot: true, width: 120 },
	{ label: "到货状态", prop: "arrivalStatus", needSlot: true, width: 120 },
	{ label: "采购计划号", prop: "planPurchaseCode", width: 200 },
	{ label: "采购项目编号", prop: "projectCode", width: 130 },
	{ label: "采购项目名称", prop: "projectLabel", width: 130 },
	{ label: "采购方式", prop: "projectPurchaseType", width: 130 },
	{ label: "月度订货计划号", prop: "purchasePlanCode", width: 180 },
	{ label: "临时订货计划号", prop: "tempPlanCode", width: 180 },
	{ label: "供应商名称", prop: "supplierLabel", width: 150 },
	// { label: "外部单号", prop: "externalOrder", width: 150 },
	{ label: "合同编号", prop: "contractCode", width: 150 },
	{ label: "合同名称", prop: "contractLabel", width: 150 },
	{
		label: "订货金额",
		prop: "orderingAmount",
		needSlot: true,
		align: "right",
		width: 130
	},
	{
		label: "最晚送货日期",
		prop: "latestDeliveryDate",
		width: 130
	},
	{ label: "采购员", prop: "purchaseUserName", width: 130 },
	{
		label: "通知供应商",
		prop: "notifySuppliersStatus",
		needSlot: true,
		width: 120
	},

	{ label: "操作人", prop: "lastModifiedBy_view", needSlot: false, width: 90 },
	{ label: "订单生成时间", prop: "createdDate", width: 160 },
	{ label: "操作时间", prop: "lastModifiedDate", width: 160 },
	{
		label: "操作",
		width: 130,
		prop: "operations",
		fixed: "right",
		needSlot: true
	}
	//确认采购订单 confirmStatus
]
const orderFormEl: FormElementType[][] = [
	[
		{
			label: "采购订单ID",
			name: "id",
			maxlength: 50,
			disabled: true,
			type: "hidden"
		}
	],
	[
		{
			label: "计划年度",
			name: "year",
			type: "select",
			data: DictApi.getFutureYears(-1, 1),
			disabled: false
		}
	],
	[
		{
			label: "采购订单名称",
			name: "label",
			maxlength: 100
		}
	],
	[
		{
			label: "订单来源",
			name: "source",
			type: "select",
			data: DictApi.getPurchaseOrderSource().filter(
				(item) => item.value !== purchaseOrderSource.annualDemandPlan
			)
		}
	],
	/*[
		{
			label: "外部单号",
			name: "externalOrder",
			maxlength: 50
		}
	],*/
	/*	[
		{
			label: "采购计划号",
			name: "planPurchaseCode",
			maxlength: 50
		}
	],*/
	/*	[
		{
			label: "采购项目编号",
			name: "projectCode",
			maxlength: 50
		}
	],*/
	[
		{
			label: "合同名称",
			name: "contractLabel",
			type: "drawer",
			clickApi: () => {}
		}
	],
	[
		{
			label: "合同编号",
			name: "contractCode",
			disabled: true
		}
	],
	[
		{
			label: "供应商名称",
			name: "supplierLabel",
			disabled: true
		}
	],
	[
		{
			label: "供应商编码",
			name: "supplierCode",
			disabled: true
		}
	],
	[
		{
			label: "订货金额",
			name: "orderingAmount_view",
			disabled: true
		}
	],
	[
		{
			label: "最晚到货时间",
			name: "latestDeliveryDate",
			type: "hidden",
			data: DictApi.getPurchaseOrderSource().filter(
				(item) => item.value !== purchaseOrderSource.annualDemandPlan
			)
		}
	],
	[
		{
			label: "备注说明",
			name: "remark",
			maxlength: 200,
			rows: "5",
			type: "textarea"
		}
	]
]
const formRules = {
	year: [{ required: true, message: "计划年度不能为空", trigger: "change" }],
	label: [
		{ required: true, message: "采购订单名称不能为空", trigger: "change" }
	],
	source: [{ required: true, message: "订单来源不能为空", trigger: "change" }],
	supplierLabel: [
		{ required: false, message: "供应商名称不能为空", trigger: "change" }
	],
	supplierCode: [
		{ required: false, message: "供应商编码不能为空", trigger: "change" }
	],
	contractLabel: [
		{ required: true, message: "合同名称不能为空", trigger: "change" }
	],
	contractCode: [
		{ required: false, message: "合同编号不能为空", trigger: "change" }
	]
}
function getQueryArrList() {
	const arrQuery: any[] = []
	const input = [
		"code",
		"label",
		"planPurchaseCode",
		"projectCode",
		"projectLabel",
		"purchasePlanCode",
		"tempPlanCode"
	] //修改这个数组即可
	input.forEach((prop) => {
		const found = orderTableProps.find((obj) => obj.prop == prop)
		if (found) {
			arrQuery.push({
				name: found.label,
				key: found.prop,
				placeholder: `请输入${found.label}`,
				enableFuzzy: true,
				type: "input"
			})
		}
	})
	const select = [
		{
			name: "公司",
			key: "sysCommunityId",
			type: "treeSelect",
			treeApi: BaseLineSysApi.getCompanyAllList,
			placeholder: "请选择所属公司"
		},
		{
			name: "年度",
			key: "year",
			type: "select",
			children: DictApi.getFutureYears(-2, 2),
			placeholder: "请选择所属年度"
		},
		{
			name: "月份",
			key: "month",
			type: "select",
			children: DictApi.getMonthList(),
			placeholder: "请选择所属月份"
		},
		{
			name: "订单来源",
			key: "source",
			type: "select",
			children: DictApi.getPurchaseOrderSource(),
			placeholder: "请选择订单来源"
		}
	]
	return arrQuery.concat(select)
}
/**
 * 传递字段名称，返回对应的字段数组，保持定义一份
 * @param props
 */
function _getTableProps(props: any[], exclude?: any[]) {
	let retProps = orderTableProps
	if (Array.isArray(exclude) && exclude.length > 0) {
		retProps = orderTableProps.filter((obj) => !exclude.includes(obj.prop))
	}
	if (Array.isArray(props) && props.length > 0) {
		const filteredProps = retProps.filter((obj) => props.includes(obj.prop))
		const orderedProps = props.map((prop) =>
			filteredProps.find((obj) => obj.prop === prop)
		)

		return orderedProps
	} else {
		return retProps
	}
}
function getFormEl() {
	return orderFormEl
}
function getDescEl(props?: any[]) {
	const defaultProps = [
		"year",
		"month",
		"code",
		"label",
		// "planPurchaseCode",
		// "projectCode",
		"projectLabel",
		"supplierLabel",
		"contractCode",
		"contractLabel",
		"projectPurchaseType", //采购方式
		"orderingAmount",
		"latestDeliveryDate",
		"purchaseUserName",
		"source",
		"lastModifiedBy_view",
		"lastModifiedDate"
		/* "tempPlanCode" */
	]
	let lastProps = []
	if (Array.isArray(props) && props.length > 0) {
		lastProps = props
	} else {
		lastProps = defaultProps
	}
	const elArray: any[] = []
	if (Array.isArray(lastProps) && lastProps.length > 0) {
		lastProps.forEach((prop) => {
			const found = orderTableProps.find((obj) => obj.prop == prop)
			if (found) {
				elArray.push({
					label: found.label,
					name: found.prop
				})
			}
		})
	}
	return elArray
}
function getDescElNotify() {
	const props = [
		"year",
		"month",
		"source",
		"planPurchaseCode",
		"code",
		"label",
		"projectCode",
		"projectLabel",
		"contractCode",
		"contractLabel",
		"projectPurchaseType", //采购方式
		"orderingAmount",
		"purchaseUserName",
		"createdDate",
		"supplierLabel",
		"latestDeliveryDate"
	]
	return getDescEl(props)
}
function getFormRules() {
	return formRules
}
function getSingleFormEl(formEl: FormElementType[][], name: string) {
	const foundObject = formEl
		.flat()
		.find((formElement) => formElement.name === name)

	return foundObject
}

function getTableProps(type: number) {
	const exclude =
		type.toString() === "0"
			? [
					"arrivalStatus",
					"lastModifiedDate",
					"contractLabel",
					"projectPurchaseType",
					"notifySuppliersStatus",
					"tempPlanCode"
			  ]
			: [
					"bpmStatus",
					"createdDate",
					"contractLabel",
					"projectPurchaseType",
					"notifySuppliersStatus",
					"tempPlanCode"
			  ]
	return _getTableProps([], exclude)
}
export const orderPageApi = {
	getTableProps,
	getQueryArrList,
	getFormEl,
	getFormRules,
	getDescEl,
	getSingleFormEl,
	getDescElNotify
}
