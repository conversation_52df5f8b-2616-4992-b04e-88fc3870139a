<!-- 通知供应商 -->
<script lang="ts" setup>
import { ref, onMounted, reactive } from "vue"
import XEUtils from "xe-utils"
import { type FormInstance } from "element-plus"
import { FormElementType } from "@/app/baseline/views/components/define"
import CostTag from "@/app/baseline/views/components/costTag.vue"
import { PurchaseOrderApi } from "@/app/baseline/api/purchase/purchaseOrder"
import FormElement from "../../components/formElement.vue"
import { useDictInit } from "../../components/dictBase"
import DictTag from "../../components/dictTag.vue"
import { DictApi, purchaseOrderSource } from "../../../api/dict"
import { PurchaseSupplierApi } from "../../../api/purchase/purchaseSupplier"
import { useMessageBoxInit } from "@/app/baseline/views/components/messageBox"
import { PurchaseOrderItemApi } from "@/app/baseline/api/purchase/purchaseOrderItem"
import { isValidateNumber } from "@/app/baseline/utils/validate"
import { batchFormatterNumView } from "@/app/baseline/utils"
import VueQrcode from "@chenfengyuan/vue-qrcode"
import { downloadPDF } from "@/app/baseline/utils/downFile"
import { getRealLength, setString } from "@/app/baseline/utils/validate"
import EmptyData from "@/assets/images/empty/empty_data.svg?component"

const { showWarnConfirm } = useMessageBoxInit()
const { dictFilter, getDictByCodeList } = useDictInit()

const props = withDefaults(
	defineProps<{
		id: any //订单ID
	}>(),
	{}
)
const emits = defineEmits<{
	(e: "close"): void
	(e: "update"): void
}>()

const drawerLoading = ref(false)

const formBtnLoading = ref(false)

const formRef = ref<FormInstance>()
const drawerTitle = [
	{
		name: ["供应商信息"],
		icon: ["fas", "square-share-nodes"]
	},
	{
		name: ["采购订单信息"],
		icon: ["fas", "square-share-nodes"]
	},
	{
		name: ["订单明细"],
		icon: ["fas", "square-share-nodes"]
	}
]

const formBtnList = [
	{ name: "取消", icon: ["fas", "circle-minus"] },
	{ name: "确定", icon: ["fas", "circle-check"] },
	{ name: "PDF下载", icon: ["fas", "file-pdf"] }
]

/**
 * 采购订单 详情
 */
const formData = ref<Record<string, any>>({})
const descOption = computed(() => {
	const ls = [
		{ label: "年度", name: "year" },
		{ label: "月份", name: "month" },
		{ label: "订单来源", name: "source" },
		{ label: "采购计划号", name: "planPurchaseCode" },
		{ label: "采购订单号", name: "code" },
		{ label: "采购订单名称", name: "label" },
		{ label: "采购项目编号", name: "projectCode" },
		{ label: "采购项目名称", name: "projectLabel" },
		{ label: "月度订货计划号", name: "purchasePlanCode" },
		{ label: "临时订货计划号", name: "tempPlanCode" },
		{ label: "合同编号", name: "contractCode" },
		{ label: "合同名称", name: "contractLabel" },
		{ label: "采购方式", name: "projectPurchaseType" },
		{ label: "订货金额", name: "orderingAmount" },
		{ label: "采购员", name: "purchaseUserName" },
		{ label: "订单生成时间", name: "createdDate" },
		{ label: "供应商名称", name: "supplierLabel" },
		{ label: "最晚到货时间", name: "latestDeliveryDate" }
	]

	if (formData.value?.source != purchaseOrderSource.emergencyPlan) {
		return ls.filter((v) => !["临时订货计划号"].includes(v.label))
	} else {
		return ls.filter(
			(v) =>
				![
					"采购计划号",
					"采购项目编号",
					"采购项目名称",
					"月度订货计划号"
				].includes(v.label)
		)
	}
})

/**
 * 供就应商信息
 */
const formSupplierData = ref<any>({})

const formSupplierEl = reactive<FormElementType[][]>([
	[
		{
			label: "供应商名称",
			name: "label",
			width: 7,
			disabled: true
		},
		{
			label: "联系人",
			name: "contact",
			width: 4,
			disabled: true
		},
		{
			label: "联系人电话",
			name: "contactPhoneNumber",
			width: 6,
			disabled: true
		},
		{
			label: "电子邮箱",
			name: "contactEmail",
			width: 7,
			disabled: true
		}
	]
])

/**
 * 获取订单详情
 */
async function getDetail() {
	if (!props.id) {
		return false
	}
	drawerLoading.value = true

	try {
		const res = await PurchaseOrderApi.getPurchaseOrder(props.id as string)
		formData.value = { ...res }
	} finally {
		drawerLoading.value = false
	}
}

/**
 * 获取供应商信息
 * @param id
 */
async function getSupplierInfo(id: any) {
	const res = await PurchaseSupplierApi.getPurchaseSupplier(id)
	formSupplierData.value = { ...res }
}
async function handleFormBtnAction(btnName: string | undefined) {
	if (btnName === "确定") {
		await showWarnConfirm("请确认是否通知供应商？")
		formBtnLoading.value = true
		try {
			await PurchaseOrderApi.notifySuppliersPurchaseOrder(props.id)
			ElMessage.success("操作成功")
			emits("update")
			emits("close")
		} finally {
			formBtnLoading.value = false
		}
	} else if (btnName === "PDF下载") {
		formBtnLoading.value = true
		try {
			const res = await PurchaseOrderApi.exportPDFPurchaseOrder(
				props.id as string
			)
			downloadPDF(res, `采购订单`)
			emits("close")
		} finally {
			formBtnLoading.value = false
		}
	} else {
		emits("close")
	}
}
onMounted(async () => {
	getDictByCodeList(["ORDER_SOURCE", "PURCHASE_TYPE", "INVENTORY_UNIT"])
	await getDetail()
	getSupplierInfo(formData.value.supplierId)
	getTableData()
})

const lastTableData = ref<any[]>([])
const tableData = ref<any[]>([])

async function getTableData() {
	tableData.value = await PurchaseOrderItemApi.getPurchaseOrderItemListNoPage({
		orderId: props.id
	})
}

/**
 * 补丁
 * 监听tableData 重组lastTableData
 */
watch([tableData], () => {
	XEUtils.map(tableData.value as any, (_d: { [propName: string]: any }) => {
		if (_d.content) {
			Array.from(Object.keys(_d?.content))?.map(
				(_k) => (_d[`content_${_k}`] = _d.content[_k])
			)
		}
		_d.completedArrivedNum = _d.completedArrivedNum || 0
		_d.qualityPassNum = _d.qualityPassNum || 0
		_d.qualityFailNum = _d.qualityFailNum || 0
		_d.completedStoredNum = _d.completedStoredNum || 0
		_d.completedInvoicedNum = _d.completedInvoicedNum || 0
		_d.completedArrivedThisNum = _d.completedArrivedThisNum || 0

		/* 计算在途数量 */
		const completedComputedNum =
			(_d.orderingNum || 0) - (_d.completedArrivedNum || 0)

		_d.completedComputedNum = isValidateNumber(
			completedComputedNum as unknown as string
		)
			? completedComputedNum
			: Number(
					parseInt((completedComputedNum! * 10000) as unknown as string) / 10000
			  )
	})
	lastTableData.value = tableData.value

	batchFormatterNumView(lastTableData.value as any[])
})
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<!-- 左侧表单区域 -->
		<div class="drawer-column" style="width: 100%">
			<Title :title="drawerTitle[0]" />
			<el-scrollbar class="rows" style="padding-bottom: 10px">
				<el-form
					class="content"
					:model="formData"
					ref="formRef"
					label-position="left"
					label-width="auto"
				>
					<FormElement
						:form-element="formSupplierEl"
						:form-data="formSupplierData"
					/>
				</el-form>
				<Title :title="drawerTitle[1]" />
				<el-descriptions
					size="small"
					:column="2"
					:border="true"
					class="content"
				>
					<template>
						<el-descriptions-item
							label-align="center"
							label="标签码"
							key="00"
							:span="2"
						>
							<vue-qrcode
								:value="formData?.qrCode"
								style="width: 100px; height: 100px"
							/>
						</el-descriptions-item>
						<el-descriptions-item
							v-for="(el, index) in descOption"
							label-align="center"
							:label="el.label"
							:key="index"
						>
							<span v-if="el.name == 'source'">
								<dict-tag
									:value="formData[el.name]"
									:options="DictApi.getPurchaseOrderSource()"
								/>
							</span>
							<span v-else-if="el.name == 'projectPurchaseType'">
								{{
									dictFilter("PURCHASE_TYPE", formData[el.name])?.label || "---"
								}}
							</span>
							<span v-else-if="el.name == 'orderingAmount'">
								<cost-tag :value="formData[el.name]" />
							</span>
							<span v-else>
								{{ formData[el.name] ? formData[el.name] : "---" }}
							</span>
						</el-descriptions-item>
					</template>
				</el-descriptions>
				<Title :title="drawerTitle[2]">
					<div class="stat-tip">
						<span>物资编码：{{ formData.matCodeNum || "---" }}项 </span>
						<span
							>订货数量：{{
								XEUtils.commafy(Number(formData.orderingNum), { digits: 4 }) ||
								"---"
							}}
						</span>
						<span
							>订货金额：<cost-tag :value="formData.orderingAmount" />
						</span>
					</div>
				</Title>

				<div
					style="
						display: flex;
						padding: 10px 0 10px 10px;
						flex-wrap: wrap;
						justify-content: flex-start;
					"
					v-if="tableData.length > 0"
				>
					<div
						class="qr-code-wrap"
						v-for="rowData in tableData"
						:key="rowData.id!"
						style="margin-bottom: 28px"
					>
						<div class="qr-code-title">订单号 {{ formData.code }}</div>
						<div class="qr-code-info">
							<div class="qr-code-info-left">
								<p class="info-text" style="margin-top: 0">
									物资编码:{{ rowData.content_code }}
								</p>
								<p class="info-text">
									物资名称:
									<el-tooltip
										effect="dark"
										:content="rowData.content_label"
										:disabled="
											getRealLength(rowData.content_label) <= 60 ? true : false
										"
									>
										{{
											getRealLength(rowData.content_label) > 60
												? setString(rowData.content_label, 60)
												: rowData.content_label || "---"
										}}
									</el-tooltip>
								</p>
								<p class="info-text">
									采购单位:{{
										dictFilter("INVENTORY_UNIT", rowData.content_buyUnit)
											?.label || "---"
									}}
									&nbsp; 订货数量:{{ rowData.orderingNum_view }}
								</p>
							</div>
							<div class="qr-code-info-right">
								<!-- <div class="label-type">
									<span class="type-text">到&emsp;货</span>
								</div> -->

								<vue-qrcode :value="rowData?.qrCode" class="label-qrcode" />
							</div>
						</div>
						<p class="store-label-wrap">
							规格型号：<el-tooltip
								effect="dark"
								:content="rowData.content_version"
								:disabled="
									getRealLength(rowData.content_version) <= 100 ? true : false
								"
							>
								{{
									getRealLength(rowData.content_version) > 100
										? setString(rowData.content_version, 100)
										: rowData.content_version || "---"
								}}
							</el-tooltip>
						</p>
					</div>
				</div>

				<div v-else>
					<div class="empty-table">
						<EmptyData class="empty_img" />
						<p>未查询到相关数据</p>
					</div>
				</div>
			</el-scrollbar>
			<ButtonList
				class="footer"
				:button="formBtnList"
				:loading="formBtnLoading"
				@on-btn-click="handleFormBtnAction"
			/>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index";
:deep(.el-form-item__label-wrap) {
	margin-right: 0px !important;
}

.empty-table {
	text-align: center;
	line-height: 0rem;
	padding-bottom: 40px;
	.empty_img {
		width: 150px;
	}
	p {
		color: #666666;
		font-size: 12px;
		text-align: center;
	}
}
.drawer-container {
	.drawer-column {
		width: 100%;
		.pb0 {
			padding-bottom: 0;
		}
		.last {
			flex: 1;
		}
	}

	.tab-mat {
		height: 100%;
	}

	//临时
	.stat-tip {
		padding-right: 10px;
		> span {
			margin-left: 20px;
		}
		font-size: $---font-size-m;
		position: absolute;
		right: 0;
	}
}

.label-qrcode {
	width: 25mm !important;
	height: 25mm !important;
	//border: 1px solid #ccc;
}

.qr-code-wrap {
	font-size: 12px;
	border: 1px solid #ccc;

	display: flex;
	flex-direction: column;
	justify-content: flex-start;

	width: calc((100% - 30px) / 3);
	padding: 0 0 1mm;
	height: 50mm;
	font-family: "Microsoft YaHei", "Microsoft YaHei UI", "PingFang SC", "Arial",
		"sans-serif";
	color: var(--color-text);
	margin-right: 10px;

	.qr-code-title {
		height: 10mm;
		line-height: 10mm;
		background: #f5f5f5;
		text-align: center;
		border-bottom: 1px solid #ddd;
	}
	.qr-code-info {
		display: flex;
		justify-content: space-between;
		padding: 1mm;
		.qr-code-info-left {
			display: flex;
			flex-direction: column;
			justify-content: space-around;
			max-height: 25mm;
			width: calc(100% - 130px);
			.info-text {
				text-align: left;
				width: 100%;
				/* 中文自动换行，英文单词尽量保持完整，数字不被拆分 */
				word-wrap: break-word;
				word-break: normal;
				white-space: normal;
				display: -webkit-box;
				-webkit-box-orient: vertical;
				overflow: hidden;
				text-overflow: ellipsis;
				margin-top: 5px;
				line-height: 18px;
				max-height: 36px;
			}
			.label-logo {
				width: 25mm;
				height: 6.3mm;
			}
		}

		.qr-code-info-right {
			display: flex;
			max-height: 25mm;
			position: relative;
			text-align: center;
			.label-type {
				background-color: #000;
				color: #fff;
				max-height: 25mm;
				// 上下边距 固定参数
				padding: 6mm 1mm;
			}
			.type-text {
				font-size: 12pt;
				font-family: 思源黑;
				font-weight: bold;
				line-height: 1;
				writing-mode: vertical-rl;
			}

			.label-qrcode {
				width: 25mm !important;
				height: 25mm !important;
				/* border: 1px solid #000; */
			}
		}
	}

	.store-label-wrap {
		text-align: left;
		max-width: 100%;
		/* 中文自动换行，英文单词尽量保持完整，数字不被拆分 */
		word-wrap: break-word;
		word-break: normal;
		white-space: normal;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		overflow: hidden;
		text-overflow: ellipsis;
		margin-top: 5px;
		line-height: 18px;
		max-height: 36px;
		padding-left: 1mm;
	}
}
</style>
