<!-- 采购订单 详情 查看 -->
<script lang="ts" setup>
import { ElMessage } from "element-plus"
import {
	DictApi,
	fileBusinessType,
	purchaseOrderSource
} from "@/app/baseline/api/dict"
import { FormElementType } from "@/app/baseline/views/components/define"
import TableFile from "@/app/baseline/views/components/tableFile.vue"
import CostTag from "@/app/baseline/views/components/costTag.vue"
import { ref, reactive } from "vue"
import { PurchaseOrderApi } from "@/app/baseline/api/purchase/purchaseOrder"
import DictTag from "../../components/dictTag.vue"
import { useDictInit } from "@/app/baseline/views/components/dictBase"
import {
	batchFormatterNumView,
	getOnlyDate,
	tableColFilter
} from "@/app/baseline/utils"
import { useTbInit } from "../../components/tableBase"
import XEUtils from "xe-utils"
import {
	getIdempotentToken,
	isValidateNumber
} from "@/app/baseline/utils/validate"
import { PurchaseOrderItemApi } from "@/app/baseline/api/purchase/purchaseOrderItem"
import { first, isNil, map, round, subtract, toNumber } from "lodash-es"
import { validateAndCorrectInput } from "@/app/baseline/utils/validate"
import storeTable from "@/app/baseline/views/store/components/storeTable.vue"
import { modalSize } from "@/app/baseline/utils/layout-config"
import { useMessageBoxInit } from "../../components/messageBox"
import {
	IWarehouseStatus,
	IWarehouseType
} from "@/app/baseline/utils/types/store-manage"
import {
	IIdempotentTokenType,
	IIdempotentTokenTypePre,
	IModalType
} from "@/app/baseline/utils/types/common"

const { dictOptions, dictFilter, getDictByCodeList } = useDictInit()

const props = withDefaults(
	defineProps<{
		id: any //订单ID
		model?: string //显示模式 view confirm finish
		footerBtnVisible?: boolean
		storekeeperFalg?: boolean // 处理中时 增加库管员权限 默认为false
	}>(),
	{
		model: "view",
		footerBtnVisible: true,
		storekeeperFalg: false
	}
)

const emits = defineEmits<{
	(e: "close"): void
	(e: "update"): void
}>()

const { showWarnConfirm } = useMessageBoxInit()

const formData = ref<Record<string, any>>({})

const formBtnLoading = ref(false)

const drawerLeftTitle = {
	name: ["采购订单信息"],
	icon: ["fas", "square-share-nodes"]
}

const drawerRightTitle = {
	name: ["扩展信息"],
	icon: ["fas", "square-share-nodes"]
}

const formBtnList = computed(() => {
	switch (props.model) {
		case "view":
			return [{ name: "取消", icon: ["fas", "circle-minus"] }]
		default:
			return [
				{ name: "取消", icon: ["fas", "circle-minus"] },
				{ name: "确认", icon: ["fas", "circle-check"] }
			]
	}
})

const formEl = computed<FormElementType[]>(() => {
	const ls = [
		{ label: "年度", name: "year" },
		{ label: "月份", name: "month" },
		{ label: "采购订单号", name: "code" },
		{ label: "采购订单名称", name: "label" },
		{ label: "采购项目名称", name: "projectLabel" },
		{ label: "供应商名称", name: "supplierLabel" },
		{ label: "合同编号", name: "contractCode" },
		{ label: "合同名称", name: "contractLabel" },
		{ label: "采购方式", name: "projectPurchaseType" },
		{ label: "订货金额", name: "orderingAmount" },
		{ label: "最晚送货日期", name: "latestDeliveryDate", type: "date" },
		{ label: "采购员", name: "purchaseUserName" },
		{ label: "订单来源", name: "source" },
		{ label: "采购计划号", name: "planPurchaseCode" },
		{ label: "采购项目编号", name: "projectCode" },
		{ label: "采购项目名称", name: "projectLabel" },
		{ label: "月度订货计划号", name: "purchasePlanCode" },
		{ label: "临时订货计划号", name: "tempPlanCode" },
		{ label: "关闭原因", name: "closeReason" },
		{ label: "关闭原因描述", name: "closeReasonDesc" },
		{ label: "质保期", name: "warrantyPeriod_view" },
		{ label: "操作人", name: "createdBy_view" },
		{ label: "更新时间", name: "lastModifiedDate" }
	]

	if (formData.value?.status != "2") {
		if (formData.value?.source != purchaseOrderSource.emergencyPlan) {
			return ls.filter(
				(v) =>
					!["临时订货计划号", "关闭原因", "关闭原因描述", "质保期"].includes(
						v.label
					) //v.label !== "关闭原因" && v.label !== "质保期"
			)
		} else {
			return ls.filter(
				(v) =>
					![
						"采购计划号",
						"采购项目编号",
						"采购项目名称",
						"月度订货计划号",
						"关闭原因",
						"关闭原因描述"
					].includes(v.label)
			)
		}
	} else {
		if (formData.value.source != purchaseOrderSource.emergencyPlan) {
			return ls.filter(
				(v) => !["临时订货计划号", "质保期"].includes(v.label) //v.label !== "质保期"
			)
		} else {
			return ls.filter(
				(v) =>
					![
						"采购计划号",
						"采购项目编号",
						"采购项目名称",
						"月度订货计划号"
					].includes(v.label)
			)
		}
	}
})

const childTableLoading = ref(false)
const drawerLoading = ref(false)

/**
 * 获取详情
 */
async function getDetail() {
	if (!props.id) {
		return false
	}

	drawerLoading.value = true

	try {
		const res = await PurchaseOrderApi.getPurchaseOrder(props.id)
		formData.value = { ...res }
	} finally {
		drawerLoading.value = false
	}
}
// 表单按钮点击
const handleFormBtnAction = async (btnName?: string) => {
	//确认采购订单
	if (props.model === "confirm" && btnName === "确认") {
		await showWarnConfirm("请确认是否确认采购订单？")

		formBtnLoading.value = true
		drawerLoading.value = true
		try {
			const idempotentToken = getIdempotentToken(
				IIdempotentTokenTypePre.other,
				IIdempotentTokenType.purchaseOrder,
				formData.value.id
			)

			await PurchaseOrderApi.confirmPurchaseOrder(props.id, idempotentToken)
			ElMessage.success("操作成功")
			emits("update")
			emits("close")
		} finally {
			formBtnLoading.value = false
			drawerLoading.value = false
		}
	}

	//确认到货
	if (props.model === "finish" && btnName === "确认") {
		await showWarnConfirm("请确认是否确认到货？")

		formBtnLoading.value = true
		drawerLoading.value = true

		try {
			const idempotentToken = getIdempotentToken(
				IIdempotentTokenTypePre.other,
				IIdempotentTokenType.purchaseOrder,
				formData.value.id
			)

			await PurchaseOrderApi.confirmPurchaseOrderItemThisArrival(
				{
					id: props.id
				},
				idempotentToken
			)
			ElMessage.success("操作成功")
			emits("update")
			emits("close")
		} finally {
			formBtnLoading.value = false
			drawerLoading.value = false
		}
	}

	if (btnName === "取消") {
		emits("close")
	}
}

const tabList = ["采购订单明细", "相关附件"]
const activeTab = ref<number>(0)
const handleTabChange = (index: number) => {
	activeTab.value = index

	if (index === 0) {
		fetchParam.value = {
			orderId: props.id || formData.value.id,
			sord: "desc",
			sidx: "createdDate"
		}
		pageSize.value = 20
		getTableData()
	}
}
onMounted(async () => {
	getDictByCodeList([
		"PURCHASE_TYPE",
		"INVENTORY_UNIT",
		"WARRANTY_PERIOD",
		"MATERIAL_NATURE"
	])
	await getDetail()
	getTableData()
})

const queryArrList = computed(() => [
	{
		name: "物资编码",
		key: "materialCode",
		placeholder: "请输入物资编码",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资名称",
		key: "materialLabel",
		placeholder: "请输入物资名称",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "规格型号",
		key: "version",
		placeholder: "请输入规格型号",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资性质",
		key: "attribute",
		type: "select",
		children: dictOptions.value.MATERIAL_NATURE,
		placeholder: "请选择物资性质"
	}
])

const lastTableData = ref<any[]>([])
const tbInit = useTbInit()
const {
	tableData,
	tableRef,
	tableLoading,
	currentPage,
	pageTotal,
	pageSize,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = tbInit

tbInit.tableProp = computed(() => {
	const defList: Record<string, any> = {
		base: [
			{ label: "物资编码", prop: "content_code", width: 130, fixed: "left" },
			{ label: "物资名称", prop: "content_label", width: 150 },
			{ label: "规格型号", prop: "content_version", width: 150 },
			{
				label: "技术参数",
				prop: "content_technicalParameter",
				minWidth: 200,
				align: "left"
			},
			{
				label: "物资性质",
				prop: "attribute",
				needSlot: true,
				width: 120
			},
			{ label: "采购单位", prop: "content_buyUnit", needSlot: true, width: 90 },
			{
				label: "采购单价",
				prop: "purchaseUnitPrice",
				needSlot: true,
				width: 150,
				align: "right"
			}
		],
		confirm: [
			{
				label: "订货数量",
				prop: "orderingNum_view",
				width: 150,
				fixed: "right",
				align: "right"
			},
			{
				label: "订货金额",
				prop: "orderingAmount",
				needSlot: true,
				width: 150,
				align: "right",
				fixed: "right"
			},
			{ label: "配送仓库名称", prop: "storeLabel", width: 150, fixed: "right" }
		],
		finish: [
			{
				label: "采购数量",
				prop: "purchaseNum_view",
				align: "right",
				width: 90
			},
			{
				label: "订货数量",
				prop: "orderingNum_view",
				align: "right",
				width: 90
			},
			{
				label: "订货金额",
				prop: "orderingAmount",
				needSlot: true,
				align: "right",
				width: 150
			},
			{
				label: "已到货",
				prop: "completedArrivedNum_view",
				width: 90,
				align: "right",
				fixed: "right"
			},
			{
				label: "质检合格",
				prop: "qualityPassNum_view",
				align: "right",
				width: 90,
				fixed: "right"
			},
			{
				label: "退货数量",
				prop: "completedReturnedNum_view",
				align: "right",
				width: 90,
				fixed: "right"
			},
			{
				label: "本次到货数量",
				prop: "completedArrivedThisNum",
				needSlot: true,
				width: 130,
				fixed: "right"
			},
			{ label: "配送仓库名称", prop: "storeLabel", width: 150, fixed: "right" }
		],
		view: [
			{
				label: "采购数量",
				prop: "purchaseNum_view",
				align: "right",
				width: 90
			},
			{
				label: "订货数量",
				prop: "orderingNum_view",
				width: 90,
				align: "right"
			},
			{
				label: "订货金额",
				prop: "orderingAmount",
				align: "right",
				needSlot: true,
				width: 150
			},
			{
				label: "质保期",
				prop: "warrantyPeriod_view",
				width: 100
			},
			{
				label: "已到货",
				prop: "completedArrivedNum_view",
				align: "right",
				width: 90
			},
			{
				label: "在途数量",
				prop: "completedComputedNum_view",
				align: "right",
				width: 100
			},
			{
				label: "质检合格",
				prop: "qualityPassNum_view",
				align: "right",
				width: 90
			},
			{
				label: "质检不合格",
				prop: "qualityFailNum_view",
				align: "right",
				width: 90
			},
			{
				label: "已入库量",
				prop: "completedStoredNum_view",
				align: "right",
				width: 90,
				fixed: "right"
			},
			{
				label: "退货数量",
				prop: "completedReturnedNum_view",
				width: 90,
				align: "right",
				fixed: "right"
			},
			{
				label: "已开票",
				prop: "completedInvoicedNum_view",
				width: 90,
				align: "right",
				fixed: "right"
			},
			{
				label: "关闭数量",
				prop: "closeNum_view",
				width: 90,
				align: "right",
				fixed: "right"
			},
			{ label: "配送仓库名称", prop: "storeLabel", width: 150, fixed: "right" }
		]
	}

	const newLs = [...defList["base"], ...defList[props.model]]

	if (props.model == "view") {
		if (formData.value?.status != "2") {
			if (formData.value?.source != purchaseOrderSource.emergencyPlan) {
				return tableColFilter(newLs, ["关闭数量", "质保期"])
			} else {
				return tableColFilter(newLs, ["关闭数量"])
			}
		}
	}

	return newLs
})

function getTableData(data?: { [propName: string]: any }) {
	if (props.id) {
		fetchParam.value = { orderId: props.id }
		fetchFunc.value = PurchaseOrderItemApi.getPurchaseOrderItemList
	}
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		...data,
		sord: "desc",
		sidx: "createdDate",
		storekeeperFalg: props.storekeeperFalg
	}
	fetchTableData()
}

/**
 * 补丁
 * 监听tableData 重组lastTableData
 */
watch([tableData], () => {
	XEUtils.map(tableData.value as any, (_d: { [propName: string]: any }) => {
		if (_d.content) {
			Array.from(Object.keys(_d?.content))?.map(
				(_k) => (_d[`content_${_k}`] = _d.content[_k])
			)
		}
		_d.completedArrivedNum = _d.completedArrivedNum || 0
		_d.qualityPassNum = _d.qualityPassNum || 0
		_d.qualityFailNum = _d.qualityFailNum || 0
		_d.completedStoredNum = _d.completedStoredNum || 0
		_d.completedInvoicedNum = _d.completedInvoicedNum || 0
		_d.completedArrivedThisNum = _d.completedArrivedThisNum || 0

		/* 计算在途数量 */
		const completedComputedNum =
			(_d.orderingNum || 0) - (_d.completedArrivedNum || 0)

		_d.completedComputedNum = isValidateNumber(
			completedComputedNum as unknown as string
		)
			? completedComputedNum
			: Number(
					parseInt((completedComputedNum! * 10000) as unknown as string) / 10000
			  )
	})
	lastTableData.value = tableData.value

	batchFormatterNumView(lastTableData.value as any[])
})

const tableBtnsConf = computed(() => {
	return [
		{
			name: "指定配送仓库",
			icon: ["fas", "home"],
			disabled: selectedTableList.value.length > 0 ? false : true
		}
	]
})

const storeSelectorVisible = ref(false)
function handleTableAction(btnName?: string) {
	if (btnName == "指定配送仓库") {
		storeSelectorVisible.value = true
	}
}

/**
 * 指定配送仓库
 * @param e
 */

async function handleStoreSave(btnName: string, e: any) {
	if (btnName == "保存") {
		const matIds = map(selectedTableList.value, (v) => v.id).toString()

		formBtnLoading.value = true

		try {
			await PurchaseOrderItemApi.updateStore(matIds, e.id)
			getTableData()
			ElMessage.success("操作成功")
			storeSelectorVisible.value = false
		} finally {
			formBtnLoading.value = false
		}
	} else {
		storeSelectorVisible.value = false
	}
}

/**
 * 更新本次到货数量
 * @param e
 * @param row
 * @param field
 */
function checkItemNum(row: Record<string, any>, field: string) {
	if (field == "completedArrivedThisNum") {
		const max = subtract(row.orderingNum, row.completedArrivedNum) // 订货 - 已到货
		const completedArrivedThisNum = toNumber(row.completedArrivedThisNum)
		if (completedArrivedThisNum < 0) {
			row.completedArrivedThisNum = round(max, 4)
			ElMessage.warning("本次到货数量不能小于0！")
		}
		if (completedArrivedThisNum > max) {
			ElMessage.warning("本次到货数量不能大于订货数量！")
			row.completedArrivedThisNum = round(max, 4)
		} else if (isNil(completedArrivedThisNum)) {
			row.completedArrivedThisNum = 0
		} else {
			row.completedArrivedThisNum = toNumber(row.completedArrivedThisNum) || 0
		}
	} else {
		row[field] = isNaN(parseInt(row[field])) ? 0 : parseInt(row[field])
	}

	updatePurchaseOrderItemThisNum(row)
}

async function updatePurchaseOrderItemThisNum(row: any) {
	await PurchaseOrderItemApi.savePurchaseOrderItemThisNum({
		id: row.id,
		completedArrivedThisNum: row.completedArrivedThisNum
	})

	ElMessage.success("操作成功")
}
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<!-- 左侧表单区域 -->
		<div class="drawer-column left">
			<div class="rows">
				<Title :title="drawerLeftTitle" />
				<el-descriptions
					size="small"
					:column="1"
					:border="true"
					class="content"
				>
					<template>
						<el-descriptions-item
							v-for="(el, index) in formEl"
							label-align="center"
							:label="el.label"
							:key="index"
						>
							<span v-if="el.name == 'orderingAmount'">
								<cost-tag :value="formData[el.name]" />
							</span>
							<span v-else-if="el.name == 'source'">
								<dict-tag
									:value="formData[el.name]"
									:options="DictApi.getPurchaseOrderSource()"
								/>
							</span>
							<span v-else-if="el.name == 'projectPurchaseType'">
								{{
									dictFilter("PURCHASE_TYPE", formData[el.name])?.label || "---"
								}}
							</span>
							<span v-else-if="el.type == 'date'">
								{{ getOnlyDate(formData[el.name]) || "---" }}
							</span>
							<span v-else>
								{{ formData[el.name] ? formData[el.name] : "---" }}
							</span>
						</el-descriptions-item>
					</template>
				</el-descriptions>
			</div>
		</div>
		<!-- 右侧table区域 -->
		<div
			class="drawer-column right"
			:class="{ pdr10: !props.footerBtnVisible }"
		>
			<div class="rows">
				<Title :title="drawerRightTitle">
					<Tabs class="tabs" :tabs="tabList" @on-tab-change="handleTabChange" />
				</Title>
				<div v-if="activeTab === 0" class="tab-mat">
					<Query
						:queryArrList="queryArrList"
						@getQueryData="getTableData"
						class="custom-q"
					/>

					<PitayaTable
						ref="tableRef"
						:columns="(tbInit.tableProp as any)"
						:table-data="lastTableData"
						:need-index="true"
						:need-pagination="true"
						:single-select="false"
						:need-selection="props.model === 'confirm' ? true : false"
						:total="pageTotal"
						@onSelectionChange="selectedTableList = $event"
						@on-current-page-change="onCurrentPageChange"
						:table-loading="tableLoading"
					>
						<!-- 物资性质 -->
						<template #attribute="{ rowData }">
							<dict-tag
								:options="DictApi.getMatAttr()"
								:value="rowData.attribute"
							/>
						</template>

						<!-- 采购单位 -->
						<template #content_buyUnit="{ rowData }">
							{{
								dictFilter("INVENTORY_UNIT", rowData.content_buyUnit)?.label ||
								"---"
							}}
						</template>

						<!-- 采购单价 -->
						<template #purchaseUnitPrice="{ rowData }">
							<cost-tag :value="rowData.purchaseUnitPrice" />
						</template>

						<!-- 订货金额 -->
						<template #orderingAmount="{ rowData }">
							<cost-tag
								:value="
									(rowData.orderingNum || 0) * (rowData.purchaseUnitPrice || 0)
								"
							/>
						</template>

						<!-- 本次到货数量 -->
						<template #completedArrivedThisNum="{ rowData }">
							<el-input
								class="no-arrows"
								v-model="rowData.completedArrivedThisNum"
								@click.stop
								@input="
									rowData.completedArrivedThisNum =
										validateAndCorrectInput($event)
								"
								:disabled="
									round(rowData.orderingNum - rowData.completedArrivedNum, 4) <=
									0
										? true
										: false
								"
								@blur="checkItemNum(rowData, 'completedArrivedThisNum')"
							/>
						</template>
						<template #footerOperateLeft>
							<ButtonList
								v-if="props.model === 'confirm'"
								class="btn-list"
								:is-not-radius="true"
								:button="tableBtnsConf"
								@on-btn-click="handleTableAction"
							/>
						</template>
					</PitayaTable>
				</div>
				<div v-if="activeTab === 1">
					<TableFile
						:mod="
							props.model === 'finish' ||
							(formData.arrivalStatus != 2 &&
								formData.notifySuppliersStatus == 1 &&
								formData.status != 2)
								? IModalType.edit
								: IModalType.view
						"
						:business-type="fileBusinessType.purchaseOrder"
						:business-id="props.id || formData.id"
						v-loading="childTableLoading"
					/>
				</div>
			</div>
			<ButtonList
				v-if="footerBtnVisible"
				class="footer"
				:button="formBtnList"
				:loading="formBtnLoading"
				@on-btn-click="handleFormBtnAction"
			/>
		</div>

		<!--指定配送仓库 -->
		<Drawer
			:size="modalSize.lg"
			v-model:drawer="storeSelectorVisible"
			:destroyOnClose="true"
		>
			<store-table
				:selected-ids="
					selectedTableList.length > 1
						? []
						: [first(selectedTableList)?.storeId]
				"
				@on-save="handleStoreSave"
				:table-api-params="{
					status: `${IWarehouseStatus.activated},${IWarehouseStatus.inventoryInProgress}`,
					type: `${IWarehouseType.default},${IWarehouseType.dangerousWarehouse}`
				}"
			/>
		</Drawer>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index";
.custom-q {
	margin: 10px 0px -10px 10px !important;
}
.drawer-container {
	.left {
		width: 340px;
	}

	.right {
		width: calc(100% - 340px);
	}

	.tab-mat {
		height: 100%;
	}

	//临时
	.tmp {
		height: 50%;
		display: flex;
		justify-content: center;
		align-items: center;
		flex-direction: column;
	}
}
</style>
