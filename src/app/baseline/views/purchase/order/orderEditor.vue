<!-- 订单 编辑 -->
<script lang="ts" setup>
import { ElMessage, type FormInstance, type FormRules } from "element-plus"
import TableFile from "@/app/baseline/views/components/tableFile.vue"
import { FormElementType } from "@/app/baseline/views/components/define"
import FormElement from "@/app/baseline/views/components/formElement.vue"
import CostTag from "@/app/baseline/views/components/costTag.vue"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import {
	appStatus,
	DictApi,
	fileBusinessType,
	purchaseOrderSource
} from "@/app/baseline/api/dict"
import { ref, onMounted, reactive, watch } from "vue"
import { PurchaseOrderVo } from "./orderPage"
import { PurchaseOrderApi } from "@/app/baseline/api/purchase/purchaseOrder"

/* import SelectContract from "../components/selectContract.vue" */
import contractSelector from "../components/contractSelector.vue"
import XEUtils from "xe-utils"
import { useDictInit } from "../../components/dictBase"
import { findIndex, first, map, toNumber, toString } from "lodash-es"
import { PurchaseContractApi } from "../../../api/purchase/purchaseContract"
import { modalSize } from "@/app/baseline/utils/layout-config"
import { useMessageBoxInit } from "../../components/messageBox"
import {
	IIdempotentTokenType,
	IIdempotentTokenTypePre,
	IModalType
} from "@/app/baseline/utils/types/common"
import { useTbInit } from "@/app/baseline/views/components/tableBase"
import { PurchaseOrderItemApi } from "@/app/baseline/api/purchase/purchaseOrderItem"
import {
	validateAndCorrectInput,
	isValidateNumber,
	maxValidateErrorInfo,
	maxValidateNum,
	getIdempotentToken
} from "@/app/baseline/utils/validate"
import { batchFormatterNumView, toMoney } from "@/app/baseline/utils"

const { dictFilter, dictOptions, getDictByCodeList } = useDictInit()
const { showWarnConfirm } = useMessageBoxInit()

const props = defineProps<{
	/**
	 * 业务id
	 */
	id: any

	/**
	 * 编辑模式：编辑/新建
	 */
	mode?: IModalType
}>()

const emits = defineEmits<{
	(e: "close"): void
	(e: "update"): void
}>()

/**
 * 能否编辑扩展信息
 */
const canEditExtra = computed(() => Boolean(formData.value?.id || props.id))

/**
 * 保存旧的表单数据
 */
const oldFormData = ref<string>()

const drawerLeftTitle = {
	name: ["采购订单"],
	icon: ["fas", "square-share-nodes"]
}
const drawerRightTitle = {
	name: ["扩展信息"],
	icon: ["fas", "square-share-nodes"]
}

const formBtnList = reactive([
	{ name: "取消", icon: ["fas", "circle-minus"] },
	{ name: "保存", icon: ["fas", "floppy-disk"] }
])
const formBtnListRight = reactive([
	{ name: "提交审核", icon: ["fas", "circle-check"] }
])
const formRef = ref<FormInstance>()
//表单默认值
const futureYears = DictApi.getFutureYears(-1, 1)
const formData = ref<PurchaseOrderVo>({
	year: futureYears[1].value
})
const contractSelectorVisible = ref(false)

/**
 * 表单定义
 */
const formEl = computed<FormElementType[][]>(() => [
	[
		{
			label: "采购订单ID",
			name: "id",
			maxlength: 50,
			disabled: true,
			type: "hidden"
		}
	],
	[
		{
			label: "年度",
			name: "year",
			type: "select",
			data: DictApi.getFutureYears(-1, 1),
			disabled:
				canEditExtra.value &&
				formData.value.source == purchaseOrderSource.emergencyPlan
					? true
					: false
		}
	],
	[
		{
			label: "采购订单名称",
			name: "label",
			maxlength: 100,
			disabled:
				canEditExtra.value &&
				formData.value.source == purchaseOrderSource.emergencyPlan
					? true
					: false
		}
	],
	[
		{
			label: "订单来源",
			name: "source",
			type: "select",
			data: DictApi.getPurchaseOrderSource().filter(
				(item) => item.value !== purchaseOrderSource.annualDemandPlan
			),
			disabled:
				canEditExtra.value &&
				formData.value.source == purchaseOrderSource.emergencyPlan
					? true
					: false
		}
	],
	[
		{
			label: "合同名称",
			name: "contractLabel",
			type: "drawer",
			clickApi: () => (contractSelectorVisible.value = true)
		}
	],
	[{ label: "合同编号", name: "contractCode", disabled: true }],
	[{ label: "供应商名称", name: "supplierLabel", disabled: true }],
	[{ label: "供应商编码", name: "supplierCode", disabled: true }],
	[{ label: "订货金额", name: "orderingAmount_view", disabled: true }],
	[
		{
			label: "最晚到货时间",
			name: "latestDeliveryDate",
			valueFormat: "YYYY-MM-DD",
			type:
				canEditExtra.value &&
				formData.value.source == purchaseOrderSource.emergencyPlan
					? "date"
					: "hidden",
			data: DictApi.getPurchaseOrderSource().filter(
				(item) => item.value !== purchaseOrderSource.annualDemandPlan
			),
			disabledDate: (time: any) => {
				return time.getTime() < Date.now() - 8.64e7
			}
		}
	],
	[
		{
			label: "质保期",
			name: "warrantyPeriod",
			type:
				canEditExtra.value &&
				formData.value.source == purchaseOrderSource.emergencyPlan
					? "select"
					: "hidden",
			data: dictOptions.value.WARRANTY_PERIOD
		}
	],
	[
		{
			label: "备注说明",
			name: "remark",
			maxlength: 200,
			rows: "5",
			type: "textarea"
		}
	]
])

const formRules = computed<FormRules<typeof formData.value>>(() => ({
	year: [{ required: true, message: "计划年度不能为空", trigger: "change" }],
	label: [
		{ required: true, message: "采购订单名称不能为空", trigger: "change" }
	],
	source: [{ required: true, message: "订单来源不能为空", trigger: "change" }],

	contractLabel: [
		{ required: true, message: "合同名称不能为空", trigger: "change" }
	],
	warrantyPeriod: [
		{
			required:
				canEditExtra.value &&
				formData.value.source == purchaseOrderSource.emergencyPlan
					? true
					: false,
			message: "质保期不能为空",
			trigger: "change"
		}
	]
}))

const formBtnLoading = ref(false)
const childTableLoading = ref(false)
const drawerLoading = ref(false)

/**
 * 选择合同保存
 * @param selectedRow
 */
const handleContractSave = (selectedRow: PurchaseOrderVo[]) => {
	const row = first(selectedRow)
	if (selectedRow) {
		formData.value.contractId = row?.id
		formData.value.contractCode = row?.contractCode
		formData.value.contractLabel = row?.contractLabel
		formData.value.supplierId = row?.supplierId
		formData.value.supplierLabel = row?.supplierLabel
		formData.value.supplierCode = row?.supplierCode
	}
	contractSelectorVisible.value = false
}

const onFormBtnList = (btnName: string | undefined) => {
	if (!formRef.value) return
	if (btnName === "保存") {
		handleSaveDraft()
	} else if (btnName === "提交审核") {
		formRef.value.validate(async (valid) => {
			if (valid) {
				await showWarnConfirm("请确认是否提交本次数据？")
				formBtnLoading.value = true
				drawerLoading.value = true
				try {
					// 如果主表有修改，则先更新主表数据
					if (oldFormData.value != JSON.stringify(formData.value)) {
						await PurchaseOrderApi.updatePurchaseOrder({
							...formData.value
						} as any)
					}
					if (editedTableRowStack.value.length > 0) {
						//await showWarnConfirm("是否保存并确认提交审核？")
						const params = editedTableRowStack.value.map((v) => ({
							id: v.id,
							purchaseUnitPrice: v.purchaseUnitPrice,
							orderingNum: v.orderingNum,
							warrantyPeriod: v.warrantyPeriod
						}))
						await PurchaseOrderApi.updateBatchPurchaseOrderItem(params)
					}

					const idempotentToken = getIdempotentToken(
						IIdempotentTokenTypePre.other,
						IIdempotentTokenType.purchaseOrder,
						formData.value.id
					)
					await PurchaseOrderApi.publish(formData.value as any, idempotentToken)
					ElMessage.success("提交审核成功")
					emits("update")
					emits("close")
					editedTableRowStack.value = []
				} finally {
					formBtnLoading.value = false
					drawerLoading.value = false
				}
			}
		})
	} else if (btnName === "取消") {
		emits("close")
	}
}

/**
 * 处理保存草稿（包含新增和编辑）
 */
async function handleSaveDraft() {
	if (!formRef.value) {
		return
	}

	formRef.value.validate(async (valid: any) => {
		if (!valid) {
			return
		}
		formBtnLoading.value = true
		// 表单校验通过
		try {
			const api = canEditExtra.value
				? PurchaseOrderApi.updatePurchaseOrder
				: PurchaseOrderApi.addPurchaseOrder

			const r: Record<string, any> = await api(formData.value as any)

			formData.value.id = r.id
			fetchParam.value.orderId = r.id

			oldFormData.value = JSON.stringify(formData.value)

			if (editedTableRowStack.value.length > 0) {
				const params = map(
					editedTableRowStack.value,
					(v: Record<string, any>) => ({
						...v,
						warrantyPeriod: formData.value.warrantyPeriod
					})
				)

				editedTableRowStack.value = params
			}

			getTableData()
			ElMessage.success("操作成功")
			emits("update")
		} finally {
			formBtnLoading.value = false
		}
	})
}

//扩展栏标签页切换
const tabList = ["采购订单明细", "相关附件"]
const activeTab = ref<number>(0)
const handleTabChange = (index: number) => {
	activeTab.value = index

	if (index === 0) {
		fetchParam.value = {
			orderId: props.id || formData.value.id,
			sord: "desc",
			sidx: "createdDate"
		}
		pageSize.value = 20
		getTableData()
	}
}

/**
 * 获取详情
 */
async function getDetail() {
	if (!canEditExtra.value) {
		return false
	}

	drawerLoading.value = true
	try {
		const res: Record<string, any> = await PurchaseOrderApi.getPurchaseOrder(
			props.id || formData.value.id
		)

		formData.value = { ...res }

		formData.value["orderingAmount_view"] = toMoney(res.orderingAmount as any)
		formData.value["warrantyPeriod"] = toString(res.warrantyPeriod)

		oldFormData.value = JSON.stringify(formData.value)

		setTimeout(() => {
			formRef.value?.clearValidate()
		}, 0)
	} finally {
		drawerLoading.value = false
	}
}

/**
 * 编辑后的table row 数据
 */
const editedTableRowStack = ref<any[]>([])

/**
 * 输入框失焦 handler
 *
 * 失焦后，记录编辑过的数据，用于保存任务数据
 */
function handleInputBlur(e: any) {
	if (!editedTableRowStack.value.length) {
		editedTableRowStack.value.push({ ...e })
		return
	}

	/**
	 * 队列存在数据，验证重复
	 *
	 * - 不重复->push
	 * - 重复->更新该条数据
	 */
	const rowIdx = findIndex(editedTableRowStack.value, (v) => v.id === e.id)
	if (rowIdx !== -1) {
		editedTableRowStack.value.splice(rowIdx, 1, { ...e })
		return
	}

	editedTableRowStack.value.push({ ...e })
}

onMounted(async () => {
	getDictByCodeList([
		"ORDER_SOURCE",
		"WARRANTY_PERIOD",
		"INVENTORY_UNIT",
		"MATERIAL_NATURE"
	])
	await getDetail()
	getTableData()
})

const queryArrList = computed(() => [
	{
		name: "物资编码",
		key: "materialCode",
		placeholder: "请输入物资编码",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资名称",
		key: "materialLabel",
		placeholder: "请输入物资名称",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "规格型号",
		key: "version",
		placeholder: "请输入规格型号",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资性质",
		key: "attribute",
		type: "select",
		children: dictOptions.value.MATERIAL_NATURE,
		placeholder: "请选择物资性质"
	}
])

const lastTableData = ref<any[]>([])

const tbInit = useTbInit()
const {
	tableData,
	tableRef,
	tableLoading,
	currentPage,
	pageTotal,
	pageSize,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	tableCache,
	onCurrentPageChange
} = tbInit

tbInit.tableProp = computed(() => {
	return [
		{ label: "物资编码", prop: "content_code", width: 130, fixed: "left" },
		{ label: "物资名称", prop: "content_label", width: 150 },
		{ label: "规格型号", prop: "content_version", width: 150 },
		{
			label: "技术参数",
			prop: "content_technicalParameter",
			align: "left"
		},
		{ label: "物资性质", prop: "attribute", needSlot: true, width: 120 },
		{ label: "采购单位", prop: "content_buyUnit", needSlot: true, width: 90 },
		{
			label: "采购单价",
			prop: "purchaseUnitPrice",
			needSlot: true,
			width: 150,
			align: "right"
		},
		{
			label: "订货数量",
			prop: "orderingNum",
			needSlot: true,
			width: 120,
			align: "right",
			fixed: "right"
		},
		{
			label: "订货金额",
			prop: "orderingAmount",
			needSlot: true,
			width: 120,
			align: "right",
			fixed: "right"
		},
		{
			label: "质保期",
			prop: "warrantyPeriod",
			needSlot: true,
			width: 130,
			fixed: "right"
		}
	]
})

function getTableData(data?: { [propName: string]: any }) {
	if (props.id) {
		fetchParam.value = { orderId: props.id }
		fetchFunc.value = PurchaseOrderItemApi.getPurchaseOrderItemList
	}
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		...data,
		sord: "desc",
		sidx: "createdDate"
	}
	fetchTableData()
}

/**
 * 补丁
 * 监听tableData 重组lastTableData
 */
watch([tableData], () => {
	XEUtils.map(tableData.value as any, (_d: { [propName: string]: any }) => {
		if (_d.content) {
			Array.from(Object.keys(_d?.content))?.map(
				(_k) => (_d[`content_${_k}`] = _d.content[_k])
			)
		}
		_d.completedArrivedNum = _d.completedArrivedNum || 0
		_d.qualityPassNum = _d.qualityPassNum || 0
		_d.qualityFailNum = _d.qualityFailNum || 0
		_d.completedStoredNum = _d.completedStoredNum || 0
		_d.completedInvoicedNum = _d.completedInvoicedNum || 0
		_d.completedArrivedThisNum = _d.completedArrivedThisNum || 0
		_d.warrantyPeriod = toString(_d.warrantyPeriod)

		/* 计算在途数量 */
		const completedComputedNum =
			(_d.orderingNum || 0) - (_d.completedArrivedNum || 0)

		_d.completedComputedNum = isValidateNumber(
			completedComputedNum as unknown as string
		)
			? completedComputedNum
			: Number(
					parseInt((completedComputedNum! * 10000) as unknown as string) / 10000
			  )
	})
	lastTableData.value = tableData.value

	batchFormatterNumView(lastTableData.value as any[])
})

/**
 * 编辑 采购单价/订货数量/质保期
 * @param e
 * @param row
 */
async function checkEditItemNum(e: any, field: string) {
	if (field === "purchaseUnitPrice") {
		const purchaseUnitPrice = toNumber(e.purchaseUnitPrice)
		const orderingNum = toNumber(e.orderingNum)

		const oldRow = tableCache.find((v) => v.id == e.id)

		e.purchaseUnitPrice = purchaseUnitPrice
		if (purchaseUnitPrice < 0) {
			e.purchaseUnitPrice = oldRow.purchaseUnitPrice
			return ElMessage.warning("采购单价不能小于0！")
		} else {
			const price = toNumber(orderingNum * purchaseUnitPrice)

			if (price > maxValidateNum) {
				e.purchaseUnitPrice = oldRow.purchaseUnitPrice
				e.orderingNum = oldRow.orderingNum
				ElMessage.warning(
					`您的订货金额已超过${maxValidateErrorInfo}元，请重新核对金额！`
				)
				return false
			}
		}
	} else if (field === "orderingNum") {
		const orderingNum = toNumber(e.orderingNum)
		const purchaseUnitPrice = toNumber(e.purchaseUnitPrice)
		const oldRow = tableCache.find((v) => v.id == e.id)

		e.orderingNum = orderingNum
		if (orderingNum < 0) {
			e.orderingNum = oldRow.orderingNum
			return ElMessage.warning("订货数量不能小于0！")
		} else {
			const price = toNumber(orderingNum * purchaseUnitPrice)

			if (price > maxValidateNum) {
				e.orderingNum = oldRow.orderingNum
				e.purchaseUnitPrice = oldRow.purchaseUnitPrice
				ElMessage.warning(
					`您的订货金额已超过${maxValidateErrorInfo}元，请重新核对金额！`
				)
				return false
			}
		}
	}
	handleInputBlur(e)
}
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<!-- 左侧表单区域 -->
		<div class="drawer-column left">
			<div class="rows">
				<Title :title="drawerLeftTitle" />
				<el-scrollbar>
					<el-form
						style="padding-bottom: 30px"
						class="content"
						:model="formData"
						:rules="formRules"
						ref="formRef"
						label-position="top"
						label-width="100px"
					>
						<FormElement :form-element="formEl" :form-data="formData" />
					</el-form>
				</el-scrollbar>
			</div>
			<ButtonList
				class="footer"
				:button="formBtnList"
				:loading="formBtnLoading"
				@on-btn-click="onFormBtnList"
			/>
		</div>
		<!-- 右侧table区域 -->
		<div class="drawer-column right" :class="!canEditExtra ? 'disabled' : ''">
			<div class="rows">
				<Title :title="drawerRightTitle">
					<Tabs class="tabs" :tabs="tabList" @on-tab-change="handleTabChange" />
				</Title>
				<div class="tab-mat" v-if="activeTab === 0">
					<Query
						:queryArrList="queryArrList"
						@getQueryData="getTableData"
						class="custom-q"
					/>
					<PitayaTable
						ref="tableRef"
						:columns="(tbInit.tableProp as any)"
						:table-data="lastTableData"
						:need-index="true"
						:need-pagination="true"
						:single-select="false"
						:need-selection="false"
						:total="pageTotal"
						@onSelectionChange="selectedTableList = $event"
						@on-current-page-change="onCurrentPageChange"
						:table-loading="tableLoading"
					>
						<!-- 物资性质 -->
						<template #attribute="{ rowData }">
							<dict-tag
								:options="DictApi.getMatAttr()"
								:value="rowData.attribute"
							/>
						</template>

						<!-- 采购单位 -->
						<template #content_buyUnit="{ rowData }">
							{{
								dictFilter("INVENTORY_UNIT", rowData.content_buyUnit)?.label ||
								"---"
							}}
						</template>

						<!-- 采购单价 -->
						<template #purchaseUnitPrice="{ rowData }">
							<el-input
								class="no-arrows"
								v-model="rowData.purchaseUnitPrice"
								@click.stop
								@input="
									rowData.purchaseUnitPrice = validateAndCorrectInput($event, 5)
								"
								@change="checkEditItemNum(rowData, 'purchaseUnitPrice')"
							/>
						</template>

						<!-- 订货数量 -->
						<template #orderingNum="{ rowData }">
							<el-input
								class="no-arrows"
								v-model="rowData.orderingNum"
								@click.stop
								@input="rowData.orderingNum = validateAndCorrectInput($event)"
								@change="checkEditItemNum(rowData, 'orderingNum')"
							/>
						</template>
						<template #purchaseAmount="{ rowData }">
							<cost-tag :value="rowData.purchaseAmount" />
						</template>

						<!-- 订货金额 -->
						<template #orderingAmount="{ rowData }">
							<cost-tag
								:value="
									(rowData.orderingNum || 0) * (rowData.purchaseUnitPrice || 0)
								"
							/>
						</template>

						<!-- 质保期 -->
						<template #warrantyPeriod="{ rowData }">
							<el-select
								type="select"
								v-model="rowData.warrantyPeriod"
								placeholder="请选择"
								@change="checkEditItemNum(rowData, 'warrantyPeriod')"
							>
								<el-option
									v-for="(opt, index) in dictOptions.WARRANTY_PERIOD"
									:key="index"
									:label="opt.label"
									:value="opt.value"
								/>
							</el-select>
						</template>
					</PitayaTable>
				</div>
				<div v-else-if="activeTab === 1">
					<TableFile
						:table-loading="childTableLoading"
						:business-type="fileBusinessType.purchaseOrder"
						:business-id="props.id || formData.id"
					/>
				</div>
			</div>
			<ButtonList
				class="footer"
				:button="formBtnListRight"
				:loading="formBtnLoading"
				@on-btn-click="onFormBtnList"
			/>
		</div>

		<!-- 合同选择器 -->
		<Drawer
			:size="modalSize.lg"
			v-model:drawer="contractSelectorVisible"
			destroyOnClose
		>
			<contract-selector
				:tableApi="PurchaseContractApi.getPurchaseContractList"
				:table-req-params="{
					bpmStatus: appStatus.approved,
					sord: 'desc',
					sidx: 'createdDate'
				}"
				:selectedIds="[formData.contractId]"
				@close="contractSelectorVisible = false"
				@save="handleContractSave"
			/>
		</Drawer>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.custom-q {
	margin: 10px 0px -10px 10px !important;
}
.drawer-container {
	.left {
		width: 310px;
	}
	.right {
		width: calc(100% - 310px);
	}
}

.el-input-number {
	width: 95%;
}
</style>
