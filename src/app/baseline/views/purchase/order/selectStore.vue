<!-- 废弃 2024.12.20；请引用 store/components/storeTable.vue -->
<script lang="ts" setup>
import { ElMessage } from "element-plus"
import { onMounted, ref } from "vue"
import { useDictInit } from "../../components/dictBase"
import SelectDrawer from "@/app/baseline/views/components/selectDrawer.vue"
import colorTag from "@/app/baseline/views/store/components/colorTag.vue"
import { BaseLineSysApi } from "../../../api/system"
import { useUserStore } from "../../../../platform/store/modules/user"
import { request } from "@/app/platform/utils/service"
import { listMatStoragePaged } from "@/app/baseline/api/store/manage-api"
import {
	IWarehouseStatus,
	IWarehouseType
} from "@/app/baseline/utils/types/store-manage"
import { warehouseTypeTagColorMap } from "@/app/baseline/utils/colors"
const userStore = useUserStore()
const { userInfo } = storeToRefs(userStore)
interface Props {
	orderId: string
	matIds: string
	status?: string
}

const props = withDefaults(defineProps<Props>(), {
	matIds: undefined,
	status: IWarehouseStatus.activated
})
const emits = defineEmits(["onSaveOrClose", "onSelected"])
const { dictOptions, getDictByCodeList, dictFilter } = useDictInit()
const selectId = props.matIds ? props.matIds.toString().split(",") : []
const drawerLoading = ref(false)
const queryArrList = ref<any[]>()
const tableProp = [
	{ label: "仓库编码", prop: "code", width: 120 },
	{ label: "仓库名称", prop: "label", width: 140 },
	{ label: "仓库类型", prop: "level_view", width: 100, needSlot: true },
	// { label: "仓库类型", prop: "type_view", width: 150 },
	{ label: "所属公司", prop: "sysCommunityId_view" },
	{ label: "所属段区", prop: "depotId_view" },
	{ label: "成本中心", prop: "costCenterId", needSlot: true },
	{ label: "仓库位置", prop: "positionId" },
	{ label: "库管员", prop: "storeManage" }
]
const tableApi = (param: any) =>
	listMatStoragePaged({
		type: IWarehouseType.default + "," + IWarehouseType.dangerousWarehouse,
		...param
	})
const fetchParam = {
	sysCommunityId: userInfo.value.companyId,
	status: props.status,
	sord: "desc",
	sidx: "createdDate"
}
// const fetchParam = { }
// 左侧按钮点击
const onSave = (rowList: any[]) => {
	if (rowList.length <= 0) {
		ElMessage.warning("请选择要配送的仓库！")
		return
	} else emits("onSelected", rowList)
}
const onClose = () => {
	emits("onSaveOrClose", "cancel")
}

onMounted(() => {
	drawerLoading.value = true

	getDictByCodeList([
		`${userInfo.value.companyCode}_COST_CENTER`,
		"STORE_LEVEL",
		"STORE_TYPE",
		"STORE_STATUS"
	]).then(() => {
		queryArrList.value = [
			{
				name: "所属段区",
				key: "orgId",
				type: "treeSelect",
				treeApi: () => BaseLineSysApi.getDepotList(),
				placeholder: "请选择所属段区"
			},
			{
				name: "仓库级别",
				key: "level",
				type: "select",
				children: dictOptions.value["STORE_LEVEL"]
			},
			{
				name: "仓库类型",
				key: "type",
				type: "select",
				children: dictOptions.value["STORE_TYPE"].filter((v) =>
					[IWarehouseType.default, IWarehouseType.dangerousWarehouse].includes(
						v.value
					)
				)
			},
			{
				name: "成本中心",
				key: "costCenterId",
				type: "treeSelect",
				treeApi: () =>
					Promise.resolve(
						dictOptions.value[`${userInfo.value.companyCode}_COST_CENTER`]
					),
				placeholder: "请选择成本中心"
			},
			{
				name: "仓库编码",
				key: "code",
				type: "input",
				placeholder: "请输入仓库编码"
			},
			{
				name: "仓库名称",
				key: "label",
				type: "input",
				placeholder: "请输入仓库名称"
			}
		]
		drawerLoading.value = false
	})
})

defineOptions({
	name: "SelectDrawer"
})
</script>
<template>
	<SelectDrawer
		title="选择仓库"
		:query-arr-list="queryArrList"
		:table-prop="tableProp"
		:table-api="tableApi"
		:fetch-param="fetchParam"
		:selected="selectId"
		:loading="drawerLoading"
		:needSingleSelect="true"
		@on-close="onClose"
		@on-save="onSave"
	>
		<!-- 成本中心 -->
		<template #costCenterId="{ rowData }">
			{{
				dictFilter(`${userInfo.companyCode}_COST_CENTER`, rowData.costCenterId)
					?.label || "---"
			}}
		</template>

		<template #level_view="{ rowData }">
			<color-tag
				:bg-color="warehouseTypeTagColorMap[rowData.type as IWarehouseType]"
			>
				{{ rowData.type_view }}
			</color-tag>
		</template>
		<template #sex="{ rowData }">
			<!-- 在爷爷组件中定义的插槽内容 -->
			<div>{{ rowData.sex == 1 ? "男" : "女" }}</div>
		</template>
	</SelectDrawer>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index";

.drawer-container {
	.right {
		width: 100%;
	}
}
</style>
