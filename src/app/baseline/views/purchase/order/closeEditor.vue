<!-- 采购订单 关闭窗 -->
<script setup lang="ts">
import { ElMessage, FormInstance, FormRules } from "element-plus"
import { FormElementType } from "@/app/baseline/views/components/define"
import { onMounted, reactive, ref } from "vue"

import FormElement from "@/app/baseline/views/components/formElement.vue"
import { useMessageBoxInit } from "../../components/messageBox"
import { useDictInit } from "../../components/dictBase"
import {
	getIdempotentToken,
	requiredValidator
} from "@/app/baseline/utils/validate"
import { PurchaseOrderApi } from "@/app/baseline/api/purchase/purchaseOrder"
import { DictApi, purchaseOrderSource } from "@/app/baseline/api/dict"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import {
	IIdempotentTokenType,
	IIdempotentTokenTypePre
} from "@/app/baseline/utils/types/common"

export interface Props {
	id: any
}
const { showWarnConfirm } = useMessageBoxInit()

const { dictOptions, getDictByCodeList } = useDictInit()

const props = defineProps<Props>()

const emits = defineEmits<{
	(e: "update"): void
	(e: "close"): void
}>()
const loading = ref(false)

const drawerLoading = ref(false)

const formRef = ref<FormInstance>()

/**
 * 按钮 配置
 */
const formBtnListEdit = [
	{ name: "取消", icon: ["fas", "circle-minus"] },
	{ name: "确认", icon: ["fas", "circle-check"] }
]

/**
 * 表单数据源
 */
const formModal = ref<Record<string, any>>({
	reason: ""
})
const descData = ref<Record<string, any>>({})

/**
 * 左侧title 配置
 */
const drawerLeftTitle = {
	name: ["关闭采购订单"],
	icon: ["fas", "square-share-nodes"]
}

/**
 * 左侧表单 配置
 */
const formEl = computed<FormElementType[]>(() => {
	const ls = [
		{ label: "年度", name: "year" },
		{ label: "月份", name: "month" },
		{ label: "采购订单号", name: "code" },
		{ label: "采购订单名称", name: "label" },
		{ label: "订单来源", name: "source" },
		{ label: "采购计划号", name: "planPurchaseCode" },
		{ label: "采购项目编号", name: "projectCode" },
		{ label: "采购项目名称", name: "projectLabel" },
		{ label: "月度订货计划号", name: "purchasePlanCode" },
		{ label: "临时订货计划号", name: "tempPlanCode" },
		{ label: "到货状态", name: "arrivalStatus" },
		{ label: "采购员", name: "purchaseUserName" },
		{ label: "订单生成时间", name: "createdDate" }
	]
	if (descData.value?.source != purchaseOrderSource.emergencyPlan) {
		return ls.filter((v) => !["临时订货计划号"].includes(v.label))
	} else {
		return ls.filter(
			(v) =>
				![
					"采购计划号",
					"采购项目编号",
					"采购项目名称",
					"月度订货计划号"
				].includes(v.label)
		)
	}
})

/**
 *表 单按钮 操作
 * @param btnName
 */
const onFormBtnList = async (btnName: string | undefined) => {
	if (btnName === "取消") {
		emits("close")
	} else if (btnName === "确认") {
		if (!formRef.value) {
			return
		}
		formRef.value?.validate(async (valid) => {
			if (!valid) {
				return
			}
			await showWarnConfirm("请确认是否关闭此采购订单？")
			loading.value = true
			try {
				const idempotentToken = getIdempotentToken(
					IIdempotentTokenTypePre.other,
					IIdempotentTokenType.purchaseOrder,
					props.id
				)
				await PurchaseOrderApi.closeOurchaseOrder(
					{
						id: props.id,
						closeReason: formModal.value.closeReason,
						closeReasonDesc: formModal.value.closeReasonDesc
					},
					idempotentToken
				)

				ElMessage.success("操作成功")
				emits("update")
				emits("close")
			} finally {
				loading.value = false
			}
		})
	}
}
const formElBase = computed(() => [
	[
		{
			label: "关闭原因",
			name: "closeReason",
			type: "select",
			data: dictOptions.value.ORDER_CLOSE_REASON,
			change: () => {
				formModal.value.closeReasonDesc = ""
				setTimeout(() => {
					formRef.value?.clearValidate()
				})
			}
		},
		{
			label: "原因描述",
			name: "closeReasonDesc",
			type: "textarea",
			maxlength: 200,
			rows: "5"
		}
	]
])

// 左侧表单校验
const formRules = computed<FormRules<typeof formModal.value>>(() => {
	const def = {
		closeReason: requiredValidator("关闭原因"),
		closeReasonDesc: requiredValidator("原因描述")
	}

	if (formModal.value.closeReason == "其他") {
		return def
	} else {
		return { closeReason: requiredValidator("关闭原因") }
	}
})
const getDetail = () => {
	if (!props.id) {
		return false
	}
	drawerLoading.value = true
	PurchaseOrderApi.getPurchaseOrder(props.id)
		.then((res: any) => {
			descData.value = res
		})
		.finally(() => {
			drawerLoading.value = false
		})
}

onMounted(async () => {
	getDictByCodeList(["ORDER_CLOSE_REASON"])
	getDetail()
})
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<!-- 左侧表单区域 -->
		<div class="drawer-column">
			<Title :title="drawerLeftTitle" />
			<el-scrollbar class="rows">
				<el-descriptions size="small" :column="1" border class="content">
					<template>
						<el-descriptions-item
							v-for="(el, index) in formEl"
							label-align="center"
							:label="el.label"
							:key="index"
						>
							<span v-if="el.name == 'source'">
								<dict-tag
									:options="DictApi.getPurchaseOrderSource()"
									:value="descData[el.name]"
								/>
							</span>

							<span v-else-if="el.name == 'arrivalStatus'">
								<dict-tag
									:options="DictApi.getArrivalStatus()"
									:value="descData[el.name]"
								/>
							</span>

							<span v-else>
								{{
									descData[el.name] || descData[el.name] == 0
										? descData[el.name]
										: "---"
								}}
							</span>
						</el-descriptions-item>
					</template>
				</el-descriptions>

				<el-form
					class="content"
					:model="formModal"
					ref="formRef"
					label-position="top"
					label-width="100px"
					:rules="formRules"
				>
					<form-element :form-element="formElBase" :form-data="formModal" />
				</el-form>
			</el-scrollbar>
			<ButtonList
				class="footer"
				:button="formBtnListEdit"
				:loading="loading"
				@on-btn-click="onFormBtnList"
			/>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
