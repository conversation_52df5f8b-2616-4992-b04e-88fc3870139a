<!-- 已废弃 请用contractSelector.vue -->
<script lang="ts" setup>
import { ElMessage } from "element-plus"
import { ref } from "vue"
import SelectDrawer from "../../components/selectDrawer.vue"
import { useUserStore } from "../../../../platform/store/modules/user"
import { PurchaseContractApi } from "../../../api/purchase/purchaseContract"
import CostTag from "../../components/costTag.vue"
import { appStatus } from "../../../api/dict"
const userStore = useUserStore()
const { userInfo } = storeToRefs(userStore)
interface Props {
	id?: string
	userIds: string
}

const props = withDefaults(defineProps<Props>(), {
	userIds: undefined
})
const emits = defineEmits(["onSave", "onClose"])
const selectId = props.id ? [props.id] : []
const drawerLoading = ref(false)
const queryArrList = ref<any[]>([
	{
		name: "合同编号",
		key: "contractCode",
		type: "input",
		placeholder: "请输入合同编号"
	},
	{
		name: "合同名称",
		key: "contractLabel",
		type: "input",
		placeholder: "请输入合同名称"
	}
])
const tableProp = [
	{ label: "ID", prop: "id", width: 80 },
	{ label: "合同编号", prop: "contractCode", width: 160 },
	{ label: "合同名称", prop: "contractLabel", minWidth: 260 },
	{
		label: "合同签订日期",
		prop: "contractSigningDate",
		width: 120
	},
	{
		label: "合同截止日期",
		prop: "contractEndDate",
		width: 120
	},
	{
		label: "合同金额",
		prop: "contractAmount",
		needSlot: true,
		align: "right",
		width: 120
	},
	{ label: "供应商名称", prop: "supplierLabel", width: 260 }
]
const tableApi = PurchaseContractApi.getPurchaseContractList
const fetchParam = { bpmStatus: appStatus.approved }
// 左侧按钮点击
const onSave = (rowList: any[]) => {
	if (rowList.length <= 0) {
		ElMessage.warning("请选择合同！")
		return
	}
	emits("onSave", rowList[0])
	drawerLoading.value = false
}
const onClose = () => {
	emits("onClose")
}

defineOptions({
	name: "SelectDrawer"
})
</script>
<template>
	<SelectDrawer
		title="选择合同"
		:query-arr-list="queryArrList"
		:table-prop="tableProp"
		:table-api="tableApi"
		:fetch-param="fetchParam"
		:selected="selectId"
		:loading="drawerLoading"
		:needSingleSelect="true"
		@on-close="onClose"
		@on-save="onSave"
	>
		<template #contractAmount="{ rowData }">
			<cost-tag :value="rowData.contractAmount" />
		</template>
	</SelectDrawer>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index";

.drawer-container {
	.right {
		width: 100%;
	}
}
</style>
