<!-- 合同选择器 V2.0 -->
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<div class="drawer-column" style="width: 100%">
			<Title :title="titleConf" />

			<div class="rows mat-selector-table">
				<Query
					v-if="queryVisible"
					:query-arr-list="(queryConf as any)"
					style="margin: 10px 10px -10px"
					@get-query-data="getQueryData"
				/>
				<el-scrollbar>
					<pitaya-table
						ref="tableRef"
						:columns="tableProp"
						:table-data="tableData"
						:need-selection="true"
						:single-select="!multiple"
						:need-index="true"
						:need-pagination="true"
						:total="pageTotal"
						:table-loading="tableLoading"
						:cell-class-name="tbCellClassName"
						@on-selection-change="selectedTableList = $event"
						@on-current-page-change="fetchTableDataWithSetRowsCheck"
					>
						<!-- 合同签订日期 -->
						<template #contractSigningDate="{ rowData }">
							{{ getOnlyDate(rowData.contractSigningDate) }}
						</template>

						<!-- 合同截止日期 -->
						<template #contractEndDate="{ rowData }">
							{{ getOnlyDate(rowData.contractEndDate) }}
						</template>

						<template #contractAmount="{ rowData }">
							<cost-tag :value="rowData.contractAmount" />
						</template>
					</pitaya-table>
				</el-scrollbar>
			</div>

			<button-list
				class="footer"
				:button="btnConf"
				:loading="btnLoading"
				@on-btn-click="handleBtnClick"
			/>
		</div>
	</div>
</template>

<script setup lang="ts">
import { useTbInit } from "../../components/tableBase"
import CostTag from "../../components/costTag.vue"

import { includes } from "lodash-es"
import { useTableSelectorUtils } from "../../store/hooks/table-selector-utils"
import { getOnlyDate } from "@/app/baseline/utils"

const emit = defineEmits<{
	(e: "close"): void
	(e: "save", v: any[]): void
}>()

const props = withDefaults(
	defineProps<{
		title?: string
		/**
		 * 当前选中的id 集合
		 */
		selectedIds?: any[]
		/**
		 * table 数据源
		 */
		tableApi: (params: any) => Promise<any>

		/**
		 * table 的列配置
		 */
		columns?: TableColumnType[]

		/**
		 * 是否支持多选
		 */
		multiple?: boolean

		/**
		 * table 请求参数
		 */
		tableReqParams?: any

		/**
		 * 筛选是否可见
		 */
		queryVisible?: boolean
		/**
		 * 标红的ID
		 */
		errorIdList?: any[]
		queryArrList?: any
	}>(),
	{
		multiple: false,
		tableReqParams: () => ({}),
		columns: () => [],
		queryVisible: true,
		title: "选择合同"
	}
)

const titleConf = computed(() => ({
	name: [props.title],
	icon: ["fas", "square-share-nodes"]
}))

const queryConf = computed(() => {
	const ls = [
		{
			name: "合同编号",
			key: "contractCode",
			type: "input",
			placeholder: "请输入合同编号"
		},
		{
			name: "合同名称",
			key: "contractLabel",
			type: "input",
			placeholder: "请输入合同名称"
		}
	]

	return props.queryArrList || ls
})

const drawerLoading = ref(false)

const btnLoading = ref(false)

const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	selectedTableList,
	fetchParam,
	currentPage,
	fetchTableData,
	fetchFunc
} = useTbInit()

// eslint-disable-next-line vue/no-setup-props-destructure
fetchFunc.value = props.tableApi

tableProp.value = props.columns?.length
	? props.columns
	: [
			{ label: "合同编号", prop: "contractCode", width: 150 },
			{ label: "合同名称", prop: "contractLabel" },
			{
				label: "合同签订日期",
				prop: "contractSigningDate",
				width: 160,
				needSlot: true
			},
			{
				label: "合同截止日期",
				prop: "contractEndDate",
				width: 160,
				needSlot: true
			},
			{
				label: "合同金额",
				prop: "contractAmount",
				needSlot: true,
				align: "right",
				width: 150
			},
			{ label: "供应商名称", prop: "supplierLabel" }
	  ]

const { fetchTableDataWithSetRowsCheck } = useTableSelectorUtils({
	tableData,
	tableRef,
	selectedIds: props.selectedIds,
	fetchTableData
})

const btnConf = computed(() => {
	return [
		{
			name: "取消",
			icon: ["fas", "circle-minus"]
		},
		{
			name: "保存",
			icon: ["fas", "floppy-disk"],
			disabled: selectedTableList.value.length > 0 ? false : true
		}
	]
})

function tbCellClassName(data: {
	row: any
	column: any
	rowIndex: number
	columnIndex: number
}): string {
	return includes(props.errorIdList ?? [], data.row.id) ? "error" : ""
}

onMounted(() => {
	fetchParam.value = { ...fetchParam.value, ...props.tableReqParams }

	fetchTableDataWithSetRowsCheck()
})

function getQueryData(e: any) {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = { ...fetchParam.value, ...e }
	fetchTableDataWithSetRowsCheck()
}

async function handleBtnClick(name?: string) {
	if (name === "取消") {
		return emit("close")
	}

	btnLoading.value = true
	try {
		emit("save", selectedTableList.value)
	} finally {
		btnLoading.value = false
	}
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.mat-selector-table {
	&:deep(.pitaya-table) {
		.error {
			.column-inner-item,
			.cell,
			.el-input__inner {
				color: red;
			}
		}
	}
}
</style>
