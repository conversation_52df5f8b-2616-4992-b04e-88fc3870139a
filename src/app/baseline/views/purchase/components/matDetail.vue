<script lang="ts" setup>
import { ref, defineProps, onMounted,watch } from "vue"
import { ElMessage } from "element-plus"
import MatDetail from "@/app/baseline/views/plan/components/plan/matDetail.vue"
import { PlanApi } from "../../../api/plan/plan"

export interface Props {
	planId?: string | number
	matId?: string | number
	model?: string
	withInfo?: boolean
}

const props = withDefaults(defineProps<Props>(), {
	planId: "",
	matId: "",
	model: "view",
	withInfo: false
})
const emits 						= defineEmits(["onDataChange"])

const curPlanMatId = ref("")
const isLoading = ref(false)
const getMatList = () => {
	isLoading.value = true
	if (props.planId && props.matId) {
		PlanApi.getMatListByPlanId({
			planId: props.planId,
			materialId: props.matId
		}).then((_r) => {
			if (_r?.rows?.length > 0) {
				curPlanMatId.value = _r.rows[0].id
				isLoading.value = false
			} else {
				isLoading.value = false
			}
		})
	} else {
		isLoading.value = false
	}
}
onMounted(() => {
	getMatList()
})
watch(
	() => props.planId,
	(newVal) => {
		getMatList()
	}
)
</script>
<template>
	<MatDetail
		v-if="!isLoading"
		:id="curPlanMatId"
		:withInfo="props.withInfo"
		:model="props.model"
		@onDataChange="emits('onDataChange')"
		detailModel="view"
	/>
</template>
<style lang="scss" scoped></style>
