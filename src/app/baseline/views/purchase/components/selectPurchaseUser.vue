<script lang="ts" setup>
import { ElMessage } from "element-plus"
import { ref } from "vue"
import SelectDrawer from "../../components/selectDrawer.vue"
import { baseUserApi } from "../../../../platform/api/system/baseUser"
import { BaseLineSysApi } from "../../../api/system"
import { useUserStore } from "../../../../platform/store/modules/user"
const userStore = useUserStore()
const { userInfo } = storeToRefs(userStore)
interface Props {
	userIds?: string
}

const props = withDefaults(defineProps<Props>(), {
	userIds: undefined
})
const emits = defineEmits(["onSave", "onClose"])
const selectId = props.userIds ? props.userIds : []
const drawerLoading = ref(true)
const queryArrList = ref<any[]>([
	{
		name: "部门",
		key: "orgId",
		type: "treeSelect",
		treeApi: () =>
			BaseLineSysApi.getDepartmentTreeWithCompanyDisabled(
				userInfo.value.companyId
			),
		placeholder: "请选择所属部门"
	},
	{
		name: "姓名",
		key: "realname",
		type: "input",
		enableFuzzy: false,
		placeholder: "请输入用户姓名"
	}
])
const tableProp = [
	{ label: "用户姓名", prop: "realname", width: 120 },
	{ label: "用户账号", prop: "username", width: 140 },
	{ label: "性别", prop: "sex", width: 100, needSlot: true },
	{ label: "手机号码", prop: "phone", width: 150 },
	{ label: "部门", prop: "orgAllName" }
]
const tableApi = baseUserApi.getBaseUserListApi
const fetchParam = {
	companyId: userInfo.value.companyId,
	sord: "asc",
	sidx: "realname"
}
// 左侧按钮点击
const onSave = (rowList: any[]) => {
	if (rowList.length <= 0) {
		ElMessage.warning("请选择用户！")
		return
	}
	emits("onSave", rowList)
	drawerLoading.value = false
}
const onClose = () => {
	emits("onClose")
}

defineOptions({
	name: "SelectDrawer"
})
</script>
<template>
	<SelectDrawer
		title="选择用户"
		:query-arr-list="queryArrList"
		:table-prop="tableProp"
		:table-api="tableApi"
		:fetch-param="fetchParam"
		:selected="selectId"
		:loading="drawerLoading"
		selected-key="username"
		@on-close="onClose"
		@on-save="onSave"
	>
		<template #sex="{ rowData }">
			<!-- 在爷爷组件中定义的插槽内容 -->
			<div>{{ rowData.sex == 1 ? "男" : "女" }}</div>
		</template>
	</SelectDrawer>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index";

.drawer-container {
	.right {
		width: 100%;
	}
}
</style>
