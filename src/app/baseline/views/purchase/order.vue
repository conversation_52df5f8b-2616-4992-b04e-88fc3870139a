<!-- 采购订单 主表 -->
<script lang="ts" setup>
import { ref, onMounted } from "vue"
import { ElMessage } from "element-plus"
import {
	checkPermission,
	isCheckPermission
} from "@/app/platform/utils/permission"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import {
	appStatus,
	DictApi,
	purchaseOrderSource
} from "@/app/baseline/api/dict"
import { useTbInit } from "@/app/baseline/views/components/tableBase"
import { powerList } from "@/app/baseline/views/components/define.d"
import CostTag from "@/app/baseline/views/components/costTag.vue"
import {
	getOnlyDate,
	hasEditByBpm,
	hasViewByBpm,
	tableColFilter
} from "../../utils"
import { PurchaseOrderApi } from "../../api/purchase/purchaseOrder"
import NotifySuppliers from "./order/notifySuppliers.vue"
import SelectPurchaseUser from "./components/selectPurchaseUser.vue"
import { useMessageBoxInit } from "@/app/baseline/views/components/messageBox"
import { modalSize } from "@/app/baseline/utils/layout-config"
import { useDictInit } from "../components/dictBase"
import { getTaskByBusinessIds, listCompanyWithFormat } from "../../api/system"
import { first, map, omit } from "lodash-es"
import orderEditor from "./order/orderEditor.vue"
import orderDetail from "./order/orderDetail.vue"
import closeEditor from "./order/closeEditor.vue"
import { useUserStore } from "@/app/platform/store/modules/user"
import ApprovedDrawer from "@/app/baseline/views/components/approvedDrawer.vue"

const userStore = useUserStore()
const { userInfo } = storeToRefs(userStore)

const { showDelConfirm, showInfoConfirm } = useMessageBoxInit()
const { getDictByCodeList } = useDictInit()

const titleConf = {
	name: ["采购订单"],
	icon: ["fas", "square-share-nodes"]
}

/**
 * 公司列表
 */
const companyOptions = ref<any>([])

/**
 * 查询条件 配置
 */
const queryArrList = computed(() => {
	return [
		{
			name: "采购订单号",
			key: "code",
			placeholder: "请输入采购订单号",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "采购订单名称",
			key: "label",
			placeholder: "请输入采购订单名称",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "采购计划号",
			key: "planPurchaseCode",
			placeholder: "请输入采购计划号",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "采购项目编号",
			key: "projectCode",
			placeholder: "请输入采购项目编号",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "采购项目名称",
			key: "projectLabel",
			placeholder: "请输入采购项目名称",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "月度订货计划号",
			key: "purchasePlanCode",
			placeholder: "请输入月度订货计划号",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "临时订货计划号",
			key: "tempPlanCode",
			placeholder: "请输入临时订货计划号",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "公司",
			key: "sysCommunityId",
			type: "select",
			children: companyOptions.value,
			placeholder: "请选择所属公司"
		},
		{
			name: "年度",
			key: "year",
			type: "select",
			children: DictApi.getFutureYears(-2, 2),
			placeholder: "请选择所属年度"
		},
		{
			name: "月份",
			key: "month",
			type: "select",
			children: DictApi.getMonthList(),
			placeholder: "请选择所属月份"
		},
		{
			name: "订单来源",
			key: "source",
			type: "select",
			children: DictApi.getPurchaseOrderSource(),
			placeholder: "请选择订单来源"
		},
		{
			name: "供应商名称",
			key: "supplierLabel",
			placeholder: "请输入供应商名称",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "合同编号",
			key: "contractCode",
			placeholder: "请输入合同编号",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "操作人",
			key: "createdByLike",
			placeholder: "请输入操作人",
			enableFuzzy: true,
			type: "input"
		}
	]
})

const tbBtnLoading = ref(false)
const tbInit = useTbInit()
const {
	tableData,
	tableRef,
	tableLoading,
	currentPage,
	pageTotal,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = tbInit

tbInit.tableProp = computed(() => {
	const defColumns: TableColumnType[] = [
		{ label: "年度", prop: "year", width: 85, fixed: "left", sortable: true },
		{ label: "月份", prop: "month", width: 85, fixed: "left", sortable: true },
		{ label: "采购订单号", prop: "code", width: 180, fixed: "left" },
		{ label: "采购订单名称", prop: "label", minWidth: 250 },
		{ label: "审批状态", prop: "bpmStatus", needSlot: true, width: 90 },
		{ label: "订单来源", prop: "source", needSlot: true, width: 120 },
		{ label: "到货状态", prop: "arrivalStatus", needSlot: true, width: 120 },
		{ label: "采购计划号", prop: "planPurchaseCode", width: 180 },
		{ label: "采购项目编号", prop: "projectCode", width: 180 },
		{ label: "采购项目名称", prop: "projectLabel", width: 130 },
		{ label: "月度订货计划号", prop: "purchasePlanCode", width: 180 },
		{ label: "临时订货计划号", prop: "tempPlanCode", width: 180 },
		{ label: "供应商名称", prop: "supplierLabel", width: 150 },
		{ label: "合同编号", prop: "contractCode", width: 150 },
		{
			label: "订货金额",
			prop: "orderingAmount",
			needSlot: true,
			align: "right",
			width: 130
		},
		{
			label: "最晚送货日期",
			prop: "latestDeliveryDate",
			width: 130,
			needSlot: true,
			sortable: true
		},
		{ label: "采购员", prop: "purchaseUserName", width: 130 },
		{
			label: "操作人",
			prop: activeIndex.value == "3" ? "lastModifiedBy_view" : "createdBy_view",
			needSlot: false,
			width: 90
		},
		{ label: "订单生成时间", prop: "createdDate", width: 160, sortable: true },
		{ label: "更新时间", prop: "lastModifiedDate", width: 160, sortable: true },
		{
			label: "操作",
			width: 130,
			prop: "operations",
			fixed: "right",
			needSlot: true
		}
	]

	switch (activeIndex.value) {
		case "0":
			return tableColFilter(defColumns, ["到货状态", "更新时间", "合同名称"])

		default:
			return tableColFilter(defColumns, ["审批状态", "订单生成时间"])
	}
})

/**
 * 获取表格默认参数
 */
fetchParam.value.notifySuppliersStatus = "0"
fetchParam.value.sord = "desc"
fetchParam.value.sidx = "createdDate"

fetchFunc.value = PurchaseOrderApi.getPurchaseOrderList

/**
 * table footer 按钮配置
 */
const tbFooterBtnConf = computed(() => {
	const selectedRow = first(selectedTableList.value)

	/**
	 * 确认采购订单
	 */
	//const isConfirmStatus = selectedRow?.confirmStatus == "1"
	const isConfirmStatus = selectedTableList.value.every(
		(item) => item.confirmStatus == "1"
	)

	/**
	 * 审批状态
	 */
	const isBpm =
		selectedRow?.bpmStatus == appStatus.approved ||
		[purchaseOrderSource.annualDemandPlan].includes(selectedRow?.source)

	/**
	 * 通知供应商状态;0待通知 1已通知
	 */
	//const isNotify = selectedRow?.notifySuppliersStatus == "1" //
	const isNotify = selectedTableList.value.every(
		(item) => item.notifySuppliersStatus == "1"
	)

	const canDisabledCloseBtn = () => {
		const selectedRow = first(selectedTableList.value)

		if (
			selectedTableList.value?.length == 1 &&
			selectedRow?.notifySuppliersStatus == "1"
		) {
			return false
		} else {
			return true
		}
	}

	// 指定采购员 是否可操作
	const canEditOrderUserBtn = selectedTableList.value.some(
		(item: Record<string, any>) => item.confirmStatus == "1"
	)

	switch (activeIndex.value) {
		case "0":
			return [
				{
					name: "指定采购员",
					roles: powerList.purchaseOrderBtnDesignatedPersonnel,
					icon: ["fas", "circle-user"],
					//disabled: selectedRow && !isConfirmStatus ? false : true
					disabled:
						selectedTableList.value?.length > 0 && !canEditOrderUserBtn
							? false
							: true
				},
				{
					name: "确认采购订单",
					roles: powerList.purchaseOrderBtnConfirmOrder,
					icon: ["fas", "circle-check"],
					disabled:
						selectedTableList.value?.length == 1 &&
						selectedRow &&
						isBpm &&
						selectedRow?.contractCode &&
						!isConfirmStatus &&
						selectedRow?.purchaseUserId
							? false
							: true
					// 已审批、有合同，有采购员，未确认采购订单
				},
				{
					name: "通知供应商",
					roles: powerList.purchaseOrderBtnNotifySuppliers,
					icon: ["fas", "envelope"],
					disabled:
						selectedTableList.value?.length > 0 && isConfirmStatus && !isNotify
							? false
							: true
				}
			]
		case "1":
			return [
				{
					name: "确认到货",
					roles: powerList.purchaseOrderBtnArrival,
					icon: ["fas", "circle-check"],
					disabled:
						selectedTableList.value?.length == 1 &&
						selectedRow &&
						selectedRow.notifySuppliersStatus == "1" &&
						selectedRow.arrivalStatus != "2"
							? false
							: true
				},
				{
					name: "关闭",
					roles: powerList.purchaseOrderBtnClose,
					icon: ["fas", "times-circle"],
					disabled: canDisabledCloseBtn()
				}
			]
		default:
			return []
	}
})

/**
 * 指定采购员 选择器
 */
const userSelectorVisible = ref(false)

/**
 * 确认采购订单 visible
 */
/* const confirmOrderVisible = ref(false) */

/**
 * 通知供应商 visible
 */
const notifySupplierVisible = ref(false)

/**
 * 确认到货 visible
 */
/* const confirmArrivedVisible = ref(false) */

const tableFooterActionModel = ref("")

/**
 * 关闭订单 visible
 */
const closeEditorVisible = ref(false)

function handleTbFooterAction(btnName?: string) {
	switch (btnName) {
		case "指定采购员":
			userSelectorVisible.value = true
			break
		case "确认采购订单":
			tableFooterActionModel.value = "confirm"
			orderDetailVisible.value = true
			break
		case "生成到货码":
			ElMessage.warning("功能暂未开放")
			break
		case "通知供应商":
			handleNotifySupplier()
			break
		case "确认到货":
			tableFooterActionModel.value = "finish"
			orderDetailVisible.value = true
			break
		case "关闭":
			closeEditorVisible.value = true

			break
		default:
			break
	}
}

/**
 * 通知供应商 操作
 */
async function handleNotifySupplier() {
	if (selectedTableList.value?.length > 1) {
		// 批量通知供应商
		await showInfoConfirm("将所选采购订单批量处理，并将数据更新到【处理中】")

		try {
			tbBtnLoading.value = true
			const idList = map(selectedTableList.value, ({ id }) => id)
			await PurchaseOrderApi.batchNotifySuppliersPurchaseOrder(idList)
			ElMessage.success("操作成功")
			getTableData()
		} finally {
			tbBtnLoading.value = false
		}
	} else {
		// 单个
		notifySupplierVisible.value = true
	}
}
/**
 * 获取订单状态数量
 */
async function getTypeNumbers() {
	const res = await PurchaseOrderApi.getPurchaseOrderStatusCnt(
		omit(
			{
				...fetchParam.value
			},
			"status",
			"currentPage",
			"pageSize",
			"notifySuppliersStatus",
			"arrivalStatus"
		) as any
	)

	tabNum.value[0] = res[0] ?? 0
	tabNum.value[1] = res[1] ?? 0
	tabNum.value[2] = res[2] ?? 0
	tabNum.value[3] = res[3] ?? 0
}

const getTableData = (data?: any) => {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		...data
	}
	fetchTableData()
	getTypeNumbers()
}

/**
 * 保存指定采购员
 * @param rows
 */
async function handleUserSave(rows: any[]) {
	const selIds = map(rows, (item) => item.username)
	const idList = map(selectedTableList.value, ({ id }) => id)

	await PurchaseOrderApi.updatePurchaseUser({
		idList,
		purchaseUserId: selIds.join(",")
	} as any)

	ElMessage.success("操作成功")
	getTableData()
	userSelectorVisible.value = false
}

/**
 * tab 相关操作
 */
const tabList = [
	{
		name: "采购中",
		icon: ["fas", "square-plus"]
	},
	{
		name: "处理中",
		icon: ["fas", "square-plus"]
	},
	{
		name: "已完成",
		icon: ["fas", "square-plus"]
	},
	{
		name: "已关闭",
		icon: ["fas", "square-plus"]
	}
]
const tabNum = ref(Array.from({ length: tabList.length }, () => 0))

const activeName = ref(tabList[0].name)
const activeIndex = ref("0")
//tab 点击
const handleTabsClick = (tab: any) => {
	activeName.value = tab.paneName
	activeIndex.value = tab.index
	if (tab.index == 0) {
		fetchParam.value.notifySuppliersStatus = "0"
		fetchParam.value.arrivalStatus = undefined
		fetchParam.value.status = undefined
		fetchParam.value.storekeeperFalg = false
	} else if (tab.index == 1) {
		fetchParam.value.notifySuppliersStatus = "1"
		fetchParam.value.arrivalStatus = "0,1,2"
		fetchParam.value.status = "0"
		fetchParam.value.storekeeperFalg = true
	} else if (tab.index == 3) {
		// 已关闭
		fetchParam.value.notifySuppliersStatus = undefined
		fetchParam.value.arrivalStatus = undefined
		fetchParam.value.status = "2"
		fetchParam.value.storekeeperFalg = false
	} else {
		fetchParam.value.notifySuppliersStatus = undefined
		fetchParam.value.status = "1"
		fetchParam.value.storekeeperFalg = false
	}
	getTableData()
}

/********************** drawer相关操作 *********************************/

const editorId = ref<any>("")
const orderEditorVisible = ref<boolean>(false)
const orderDetailVisible = ref<boolean>(false)

const approvedVisible = ref(false)
const editTask = ref<Record<string, any>>()

const onRowView = async (row: any) => {
	editorId.value = row.id
	tableFooterActionModel.value = "view"
	/* orderDetailVisible.value = true */

	if (row.bpmStatus == appStatus.pendingApproval) {
		orderDetailVisible.value = true
	} else {
		const res = await getTaskByBusinessIds({
			businessIds: [row.id],
			camundaKey: "purchase_order"
		})

		if (Array.isArray(res) && res.length > 0) {
			editTask.value = { ...res[res.length - 1] }
			approvedVisible.value = true
		} else {
			orderDetailVisible.value = true
		}
	}
}
const onRowEdit = (row: any) => {
	editorId.value = row.id
	orderEditorVisible.value = true
}
async function handleRowDel(row: any) {
	await showDelConfirm()
	await PurchaseOrderApi.deletePurchaseOrder(row.id)
	ElMessage.success("移除成功")
	refreshTable()
}

/**
 * 可编辑 判断
 * 1. 采购员 || 创建者
 * 2. source != 年度需求计划
 * 3. notifySuppliersStatus !== '1'
 * @param rowData
 */
function hasEdit(rowData: any) {
	return (
		([rowData.createdBy].includes(userInfo.value.userName) ||
			(rowData.purchaseUserId?.split(",") || []).includes(
				userInfo.value.userName
			)) &&
		[appStatus.pendingApproval, appStatus.rejected].includes(
			rowData.bpmStatus
		) &&
		![purchaseOrderSource.annualDemandPlan].includes(rowData.source) &&
		rowData.notifySuppliersStatus !== "1"
	)
}
function hasView(rowData: any) {
	return (
		[purchaseOrderSource.annualDemandPlan].includes(rowData.source) ||
		hasViewByBpm(rowData.bpmStatus) ||
		rowData.notifySuppliersStatus == "1"
	)
}

const refreshTable = () => {
	fetchTableData()
	getTypeNumbers()
}
onMounted(() => {
	getDictByCodeList(["ORDER_CLOSE_REASON"])
	listCompanyWithFormat().then((r) => (companyOptions.value = r))
	getTableData()
})

/**
 * 排序
 * @param column
 * @param prop 字段名称
 * @param order ascending 升序 descending 降序
 */
function handleSortChange(column: any, prop: any, order: any) {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		sord:
			order === "descending" ? "desc" : order === "ascending" ? "asc" : "desc",
		sidx: order ? prop : "createdDate" // 排序字段
	}

	fetchTableData()
}

defineOptions({
	name: "Order"
})
</script>
<template>
	<div class="app-container">
		<div class="whole-frame">
			<ModelFrame class="top-frame">
				<Query
					:queryArrList="queryArrList"
					@getQueryData="getTableData"
					class="ml10"
				/>
			</ModelFrame>
			<ModelFrame class="bottom-frame">
				<Title :title="titleConf">
					<div class="app-tabs-wrapper">
						<el-tabs v-model="activeName" @tab-click="handleTabsClick">
							<el-tab-pane
								v-for="(tab, index) in tabList"
								:key="index"
								:label="`${tab.name}（${tabNum[index]}）`"
								:name="tab.name"
								:index="tab.name"
							/>
						</el-tabs>
					</div>
				</Title>
				<PitayaTable
					ref="tableRef"
					:columns="(tbInit.tableProp as any)"
					:tableData="tableData"
					:total="pageTotal"
					:single-select="false"
					:need-selection="activeIndex == '0' || activeIndex == '1'"
					:need-index="true"
					@onSelectionChange="selectedTableList = $event"
					@on-current-page-change="onCurrentPageChange"
					:table-loading="tableLoading"
					@on-table-sort-change="handleSortChange"
				>
					<!-- 最晚送货日期 -->
					<template #latestDeliveryDate="{ rowData }">
						{{ getOnlyDate(rowData.latestDeliveryDate) || "---" }}
					</template>
					<template #purchaseAmount="{ rowData }">
						<cost-tag :value="rowData.purchaseAmount" />
					</template>
					<template #orderingAmount="{ rowData }">
						<cost-tag :value="rowData.orderingAmount" />
					</template>
					<template #source="{ rowData }">
						<dict-tag
							:options="DictApi.getPurchaseOrderSource()"
							:value="rowData.source"
						/>
					</template>

					<template #arrivalStatus="{ rowData }">
						<dict-tag
							:options="DictApi.getArrivalStatus()"
							:value="rowData.arrivalStatus"
						/>
					</template>
					<template #notifySuppliersStatus="{ rowData }">
						<dict-tag
							:options="DictApi.getNotifySuppliersStatus()"
							:value="rowData.notifySuppliersStatus"
						/>
					</template>
					<template #bpmStatus="{ rowData }">
						<slot
							v-if="
								[purchaseOrderSource.annualDemandPlan].includes(rowData.source)
							"
							>---</slot
						>
						<slot v-else>
							<dict-tag
								:options="DictApi.getBpmStatus()"
								:value="rowData.bpmStatus"
							/>
						</slot>
					</template>
					<template #operations="{ rowData }">
						<slot
							v-if="
								isCheckPermission(powerList.purchaseOrderBtnPreview) ||
								(isCheckPermission(powerList.purchaseOrderBtnEdit) &&
									hasEdit(rowData))
							"
						>
							<!-- ||
								(isCheckPermission(powerList.purchaseOrderBtnDrop) &&
									hasEdit(rowData) &&
									rowData.source !== purchaseOrderSource.emergencyPlan) -->

							<el-button
								v-btn
								link
								@click.stop="onRowEdit(rowData)"
								:disabled="
									checkPermission(powerList.purchaseOrderBtnEdit) ||
									!hasEdit(rowData)
								"
								v-if="
									isCheckPermission(powerList.purchaseOrderBtnEdit) &&
									hasEdit(rowData)
								"
							>
								<font-awesome-icon :icon="['fas', 'pen-to-square']" />
								<span class="table-inner-btn">编辑</span>
							</el-button>
							<el-button
								v-btn
								link
								@click.stop="onRowView(rowData)"
								:disabled="checkPermission(powerList.purchaseOrderBtnPreview)"
								v-if="isCheckPermission(powerList.purchaseOrderBtnPreview)"
							>
								<font-awesome-icon
									:icon="['fas', 'eye']"
									style="color: var(--pitaya-btn-background)"
								/>
								<span class="table-inner-btn">查看</span>
							</el-button>
							<!-- <el-button
								v-btn
								link
								@click.stop="handleRowDel(rowData)"
								:disabled="
									checkPermission(powerList.purchaseOrderBtnDrop) ||
									!hasEdit(rowData)
								"
								v-if="
									isCheckPermission(powerList.purchaseOrderBtnDrop) &&
									hasEdit(rowData) &&
									rowData.source !== purchaseOrderSource.emergencyPlan
								"
							>
								<font-awesome-icon :icon="['fas', 'trash-can']" />
								<span class="table-inner-btn">移除</span>
							</el-button> -->
						</slot>
						<slot v-else>---</slot>
					</template>
					<template #footerOperateLeft>
						<ButtonList
							class="btn-list"
							:is-not-radius="true"
							:button="tbFooterBtnConf"
							:loading="tbBtnLoading"
							@on-btn-click="handleTbFooterAction"
						/>
					</template>
				</PitayaTable>

				<!-- 指定采购员 -->
				<Drawer
					:size="modalSize.md"
					v-model:drawer="userSelectorVisible"
					:destroyOnClose="true"
				>
					<select-purchase-user
						:user-ids="
							selectedTableList.length > 1
								? ''
								: first(selectedTableList)?.purchaseUserId
						"
						@onSave="handleUserSave"
						@onClose="
							() => {
								userSelectorVisible = false
								refreshTable()
							}
						"
					/>
				</Drawer>

				<!-- 编辑 采购订单 -->
				<Drawer
					:size="modalSize.xl"
					v-model:drawer="orderEditorVisible"
					:destroyOnClose="true"
				>
					<order-editor
						:id="editorId"
						@close="orderEditorVisible = false"
						@update="refreshTable"
					/>
				</Drawer>

				<!-- 查看/确认采购订单/确认到货 -->
				<Drawer
					:size="modalSize.xl"
					v-model:drawer="orderDetailVisible"
					:destroyOnClose="true"
				>
					<order-detail
						:id="
							tableFooterActionModel == 'view'
								? editorId
								: first(selectedTableList)?.id
						"
						:model="tableFooterActionModel"
						:storekeeperFalg="activeIndex == '1' ? true : false"
						@close="orderDetailVisible = false"
						@update="refreshTable"
					/>
				</Drawer>

				<Drawer
					:size="1200"
					v-model:drawer="notifySupplierVisible"
					:destroyOnClose="true"
				>
					<notify-suppliers
						:id="first(selectedTableList)?.id"
						@close="notifySupplierVisible = false"
						@update="refreshTable"
					/>
				</Drawer>

				<!-- 关闭 -->
				<Drawer
					:size="350"
					v-model:drawer="closeEditorVisible"
					:destroyOnClose="true"
				>
					<close-editor
						:id="first(selectedTableList)?.id"
						@close="closeEditorVisible = false"
						@update="refreshTable"
					/>
				</Drawer>

				<!-- 审批、查看弹窗 -->
				<Drawer
					:size="modalSize.xxl"
					v-model:drawer="approvedVisible"
					:destroyOnClose="true"
				>
					<approved-drawer
						:mod="tableFooterActionModel"
						:task-value="editTask"
						@on-save-or-close="approvedVisible = false"
					/>
				</Drawer>
			</ModelFrame>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.dialog-box {
	:deep(.el-dialog__body) {
		margin: 10px;
		padding: 20px 0 10px !important;
		border-top: 1px solid #ddd;
		border-bottom: 1px solid #ddd;
	}
}
</style>
