<!-- 采购合同 V2.0-->
<script lang="ts" setup>
import { ElMessage } from "element-plus"
import {
	checkPermission,
	isCheckPermission
} from "@/app/platform/utils/permission"
import { appStatus, DictApi } from "@/app/baseline/api/dict"
import { useTbInit } from "@/app/baseline/views/components/tableBase"
import { powerList } from "@/app/baseline/views/components/define.d"
import {
	getTaskByBusinessIds,
	listCompanyWithFormat
} from "@/app/baseline/api/system"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import CostTag from "@/app/baseline/views/components/costTag.vue"
import { ref, onMounted, reactive } from "vue"
import { PurchaseContractApi } from "@/app/baseline/api/purchase/purchaseContract"
import { getOnlyDate, hasEditByBpm, hasViewByBpm } from "../../utils"
import LinkTag from "../components/linkTag.vue"
import { useMessageBoxInit } from "@/app/baseline/views/components/messageBox"
import XEUtils from "xe-utils"
import { modalSize } from "@/app/baseline/utils/layout-config"
import { IModalType } from "../../utils/types/common"
import { useDictInit } from "@/app/baseline/views/components/dictBase"
import contractEditor from "./contract/contractEditor.vue"
import contractDetail from "./contract/contractDetail.vue"
import ApprovedDrawer from "@/app/baseline/views/components/approvedDrawer.vue"

const { dictFilter, getDictByCodeList } = useDictInit()

const { showDelConfirm } = useMessageBoxInit()

/*-------------------初始化表格-start-------------------*/
const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	tbBtnLoading,
	currentPage,
	pageTotal,
	selectedTableList,
	fetchParam,
	tbBtns,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange,
	onBtnClick
} = useTbInit()

/**
 * table 列配置
 */
tableProp.value = [
	{ label: "合同编号", prop: "contractCode", width: 160, fixed: "left" },
	{ label: "合同名称", prop: "contractLabel", minWidth: 250 },
	{ label: "合同类型", prop: "contractType", needSlot: true, width: 100 },
	{
		label: "采购项目编号",
		prop: "purchaseProjectCode",
		needSlot: true,
		width: 250,
		align: "left"
	},
	{ label: "供应商名称", prop: "supplierLabel", width: 250 },
	{
		label: "合同签订日期",
		prop: "contractSigningDate",
		needSlot: true,
		width: 160,
		sortable: true
	},
	{
		label: "合同截止日期",
		prop: "contractEndDate",
		needSlot: true,
		width: 160,
		sortable: true
	},
	{
		label: "合同金额",
		prop: "contractAmount",
		needSlot: true,
		align: "right",
		width: 160
	},
	{ label: "审批状态", prop: "bpmStatus", needSlot: true, width: 90 },
	{ label: "创建人", prop: "createdBy_view", width: 120 },
	{ label: "创建时间", prop: "createdDate", width: 160, sortable: true },
	{
		label: "操作",
		width: 200,
		prop: "operations",
		fixed: "right",
		needSlot: true
	}
]

fetchFunc.value = PurchaseContractApi.getPurchaseContractList

const titleConf = {
	name: ["采购合同"],
	icon: ["fas", "square-share-nodes"]
}
const addBtnConf = ref([
	{
		name: "新建采购合同",
		roles: powerList.needPlanBtnCreate,
		icon: ["fas", "square-plus"]
	}
])

/**
 * 公司列表
 */
const companyOptions = ref<any>([])

/**
 * 查询条件 配置
 */
const queryArrList = computed(() => [
	{
		name: "合同编号",
		key: "contractCode",
		type: "input",
		placeholder: "请输入合同编号"
	},
	{
		name: "合同名称",
		key: "contractLabel",
		type: "input",
		placeholder: "请输入合同名称"
	},
	{
		name: "公司",
		key: "sysCommunityId",
		type: "select",
		children: companyOptions.value,
		placeholder: "请选择所属公司"
	},
	{
		name: "审批状态",
		key: "bpmStatus",
		type: "select",
		placeholder: "请选择",
		children: DictApi.getQueryBpmStatus()
	}
])

/**
 * 查询 回调
 * @param data
 */
const getTableData = (data: { [propName: string]: any }) => {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = { ...fetchParam.value, ...data }
	fetchTableDataExt()
}
const fetchTableDataExt = () => {
	fetchTableData().then((_r) => {
		XEUtils.map(_r.value, (_d: { [propName: string]: any }) => {
			const arrProjectCode = _d?.purchaseProjectVoList.map(
				(item: any) => item.code
			)
			_d.purchaseProjectCode =
				arrProjectCode.length > 0
					? arrProjectCode.join(",")
					: _d.purchaseProjectCode
		})
		tableData.value = _r.value
	})
}

const contractEditorId = ref("")
const contractEditorVisible = ref(false)
const contractDetailVisible = ref(false)
const contractEditorMode = ref(IModalType.create)

const approvedVisible = ref(false)
const editTask = ref<Record<string, any>>()

/**
 * 新建 操作
 */
function handleRowAdd() {
	contractEditorId.value = ""
	contractEditorMode.value = IModalType.create
	contractEditorVisible.value = true
}

/**
 * 查看 操作
 * @param row
 */
async function handleRowView(row: any) {
	contractEditorId.value = row.id
	contractEditorMode.value = IModalType.view
	/* contractDetailVisible.value = true */

	if (row.bpmStatus == appStatus.pendingApproval) {
		contractDetailVisible.value = true
	} else {
		const res = await getTaskByBusinessIds({
			businessIds: [row.id],
			camundaKey: "purchase_contract"
		})

		if (Array.isArray(res) && res.length > 0) {
			editTask.value = { ...res[res.length - 1] }
			approvedVisible.value = true
		} else {
			contractDetailVisible.value = true
		}
	}
}

/**
 * 编辑 操作
 * @param row
 */
function handleRowEdit(row: any) {
	contractEditorId.value = row.id
	contractEditorMode.value = IModalType.edit
	contractEditorVisible.value = true
}

/**
 * 删除 操作
 * @param row
 */
async function handleRowDel(row: any) {
	await showDelConfirm()
	await PurchaseContractApi.deletePurchaseContract(row.id)
	ElMessage.success("移除成功")
	fetchTableDataExt()
}

onMounted(() => {
	fetchParam.value.sord = "desc"
	fetchParam.value.sidx = "createdDate"
	getDictByCodeList(["CONTRACT_TYPE"])
	listCompanyWithFormat().then((r) => (companyOptions.value = r))

	fetchTableDataExt()
})

/**
 * 排序
 * @param column
 * @param prop 字段名称
 * @param order ascending 升序 descending 降序
 */
function handleSortChange(column: any, prop: any, order: any) {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		sord:
			order === "descending" ? "desc" : order === "ascending" ? "asc" : "desc",
		sidx: order ? prop : "createdDate" // 排序字段
	}

	fetchTableDataExt()
}

defineOptions({
	name: "purchaseManagement"
})
</script>
<template>
	<div class="app-container">
		<div class="whole-frame">
			<ModelFrame class="top-frame">
				<Query
					:queryArrList="queryArrList"
					:needSingleSelect="true"
					@getQueryData="getTableData"
					class="ml10"
				/>
			</ModelFrame>
			<ModelFrame class="bottom-frame">
				<Title
					:title="titleConf"
					:button="addBtnConf"
					@onBtnClick="handleRowAdd"
				/>
				<PitayaTable
					ref="tableRef"
					:columns="tableProp"
					:tableData="tableData"
					:total="pageTotal"
					:single-select="true"
					:need-selection="false"
					:need-index="true"
					@onSelectionChange="selectedTableList = $event"
					@on-current-page-change="onCurrentPageChange"
					:table-loading="tableLoading"
					@on-table-sort-change="handleSortChange"
				>
					<!-- 采购项目编号 -->
					<template #purchaseProjectCode="{ rowData }">
						<link-tag
							:value="rowData.purchaseProjectCode"
							@on-click="handleRowView(rowData)"
						/>
					</template>

					<!-- 合同类型 -->
					<template #contractType="{ rowData }">
						{{ dictFilter("CONTRACT_TYPE", rowData.contractType)?.subitemName }}
					</template>

					<!-- 合同签订日期 -->
					<template #contractSigningDate="{ rowData }">
						{{ getOnlyDate(rowData.contractSigningDate) }}
					</template>

					<!-- 合同截止日期 -->
					<template #contractEndDate="{ rowData }">
						{{ getOnlyDate(rowData.contractEndDate) }}
					</template>

					<!-- 审批状态 -->
					<template #bpmStatus="{ rowData }">
						<dict-tag
							:options="DictApi.getBpmStatus()"
							:value="rowData.bpmStatus"
						/>
					</template>

					<!-- 合同金额 -->
					<template #contractAmount="{ rowData }">
						<cost-tag :value="rowData.contractAmount" />
					</template>
					<template #operations="{ rowData }">
						<slot
							v-if="
								isCheckPermission(powerList.purchaseContractBtnPreview) ||
								(isCheckPermission(powerList.purchaseContractBtnEdit) &&
									hasEditByBpm(rowData.bpmStatus, rowData.createdBy)) ||
								isCheckPermission(powerList.purchaseContractBtnDrop)
							"
						>
							<el-button
								v-btn
								link
								@click="handleRowEdit(rowData)"
								:disabled="checkPermission(powerList.purchaseContractBtnEdit)"
								v-if="
									isCheckPermission(powerList.purchaseContractBtnEdit) &&
									hasEditByBpm(rowData.bpmStatus, rowData.createdBy)
								"
							>
								<font-awesome-icon :icon="['fas', 'pen-to-square']" />
								<span class="table-inner-btn">编辑</span>
							</el-button>

							<el-button
								v-btn
								link
								@click="handleRowView(rowData)"
								:disabled="
									checkPermission(powerList.purchaseContractBtnPreview)
								"
								v-if="isCheckPermission(powerList.purchaseContractBtnPreview)"
							>
								<font-awesome-icon :icon="['fas', 'eye']" />
								<span class="table-inner-btn">查看</span>
							</el-button>

							<el-button
								v-btn
								link
								@click="handleRowDel(rowData)"
								:disabled="checkPermission(powerList.purchaseContractBtnDrop)"
								v-if="isCheckPermission(powerList.purchaseContractBtnDrop)"
							>
								<font-awesome-icon :icon="['fas', 'trash-can']" />
								<span class="table-inner-btn">移除</span>
							</el-button>
						</slot>
						<slot v-else>---</slot>
					</template>
					<template #footerOperateLeft>
						<ButtonList
							class="btn-list"
							:is-not-radius="true"
							:button="tbBtns"
							v-loading="tbBtnLoading"
							@on-btn-click="onBtnClick"
						/>
					</template>
				</PitayaTable>

				<!-- 新建、编辑 弹窗 -->
				<Drawer
					:size="modalSize.xl"
					v-model:drawer="contractEditorVisible"
					:destroyOnClose="true"
				>
					<contract-editor
						:id="contractEditorId"
						:model="contractEditorMode"
						@close="contractEditorVisible = false"
						@update="fetchTableDataExt"
					/>
				</Drawer>

				<!-- 查看 弹窗 -->
				<Drawer
					:size="modalSize.xxl"
					v-model:drawer="contractDetailVisible"
					:destroyOnClose="true"
				>
					<contract-detail
						:id="contractEditorId"
						:model="contractEditorMode"
						@close="contractDetailVisible = false"
						@update="fetchTableDataExt"
					/>
				</Drawer>

				<!-- 审批、查看弹窗 -->
				<Drawer
					:size="modalSize.xxl"
					v-model:drawer="approvedVisible"
					:destroyOnClose="true"
				>
					<approved-drawer
						:mod="contractEditorMode"
						:task-value="editTask"
						@on-save-or-close="approvedVisible = false"
					/>
				</Drawer>
			</ModelFrame>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
