<!-- 采购分包 主表 V2.0-->
<script lang="ts" setup>
import { ElMessage } from "element-plus"
import {
	checkPermission,
	isCheckPermission
} from "@/app/platform/utils/permission"
import { DictApi } from "@/app/baseline/api/dict"
import { useTbInit } from "@/app/baseline/views/components/tableBase"
import { powerList } from "@/app/baseline/views/components/define.d"
import { appStatus } from "@/app/baseline/api/dict"
import {
	BaseLineSysApi,
	getTaskByBusinessIds,
	listCompanyWithFormat
} from "@/app/baseline/api/system"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import CostTag from "@/app/baseline/views/components/costTag.vue"
import LineTag from "@/app/baseline/views/components/lineTag.vue"
import { ref, onMounted, reactive } from "vue"
import { PurchaseDistributionApi } from "@/app/baseline/api/purchase/purchaseDistribution"
import { hasEditByBpm, hasViewByBpm } from "../../utils"
import { useMessageBoxInit } from "@/app/baseline/views/components/messageBox"
import { modalSize } from "@/app/baseline/utils/layout-config"
import { omit } from "lodash-es"
import { IModalType, LineVo } from "../../utils/types/common"
import distributionEditor from "./distribution/distributionEditor.vue"
import distributionDetail from "./distribution/distributionDetail.vue"
import ApprovedDrawer from "@/app/baseline/views/components/approvedDrawer.vue"

const { showDelConfirm } = useMessageBoxInit()

const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	currentPage,
	pageTotal,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = useTbInit()
tableProp.value = [
	{ label: "分包编码", prop: "code", width: 200 },
	{ label: "分包名称", prop: "label", minWidth: 260 },
	{ label: "关联采购计划号", prop: "planPurchaseCode", width: 160 },
	{
		label: "分包金额",
		prop: "sumAmount",
		needSlot: true,
		align: "right",
		width: 150
	},
	{ label: "采购员", prop: "purchaseUserName", width: 150 },
	{ label: "线路", prop: "lineId", needSlot: true, width: 120 },
	{ label: "审批状态", prop: "bpmStatus", needSlot: true, width: 90 },
	{ label: "创建人", prop: "createdBy_view", needSlot: false, width: 120 },
	{ label: "创建时间", prop: "createdDate", width: 160, sortable: true },
	{
		label: "操作",
		width: 200,
		prop: "operations",
		fixed: "right",
		needSlot: true
	}
]

fetchFunc.value = PurchaseDistributionApi.getPurchaseDistributionList

/*-------------------初始化表格-end-------------------*/

const rightTitle = {
	name: ["采购分包"],
	icon: ["fas", "square-share-nodes"]
}
const addBtn = ref([
	{
		name: "新建采购分包",
		roles: powerList.purchaseDistributionBtnCreate,
		icon: ["fas", "square-plus"]
	}
])

// 线路列表
const lineList = ref<LineVo[]>([])

/**
 * 公司列表
 */
const companyOptions = ref<any>([])

/**
 * 查询条件 配置
 */
const queryArrList = computed(() => [
	{
		name: "分包编码",
		key: "code",
		type: "input",
		placeholder: "请输入分包编码"
	},
	{
		name: "分包名称",
		key: "label",
		type: "input",
		placeholder: "请输入分包名称"
	},
	{
		name: "采购计划号",
		key: "planPurchaseCode",
		type: "input",
		placeholder: "请输入采购计划号"
	},
	{
		name: "公司",
		key: "sysCommunityId",
		type: "select",
		children: companyOptions.value,
		placeholder: "请选择公司"
	},
	{
		name: "年度",
		key: "planPurchaseYear",
		type: "select",
		children: DictApi.getFutureYears(-2, 2),
		placeholder: "请选择年度"
	},
	{
		name: "线路",
		key: "lineId",
		type: "select",
		children: lineList.value,
		placeholder: "请选择线路"
	}
])

/**
 * 获取线路配置
 */
function getLineList() {
	BaseLineSysApi.getLineOptions().then((res) => {
		lineList.value = res
	})
}

/**
 * tab 配置
 */
const tabList = [
	{
		name: "草稿箱",
		icon: ["fas", "square-plus"]
	},
	{
		name: "审批中",
		icon: ["fas", "square-plus"]
	},
	{
		name: "已审批",
		icon: ["fas", "square-plus"]
	}
]

const tabNum = ref([0, 0, 0])
const activeName = ref(tabList[0].name)
const tabStatus = reactive([
	[appStatus.pendingApproval, appStatus.rejected],
	[appStatus.underApproval],
	[appStatus.approved]
])

/**
 * 获取状态数量
 */
async function getTypeNumbers() {
	const res = await PurchaseDistributionApi.getPurchaseDistributionCnt(
		omit(fetchParam.value, "currentPage", "pageSize", "bpmStatus")
	)
	tabNum.value[0] =
		(res[appStatus.rejected] ?? 0) + (res[appStatus.pendingApproval] ?? 0)
	tabNum.value[1] = res[appStatus.underApproval] ?? 0

	tabNum.value[2] = res[appStatus.approved] ?? 0
}

const handleTabsClick = (tab: any) => {
	activeName.value = tab.paneName
	fetchParam.value.bpmStatus = tabStatus[tab.index].join(",")
	getTableData()
}

/**
 * 查询 回调
 * @param data
 */
const getTableData = (data?: { [propName: string]: any }) => {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		...data
	}
	fetchTableData()
	getTypeNumbers()
}

const editorId = ref<any>("")
const distributionEditorVisible = ref(false)
const distributionDetailVisible = ref(false)
const distributionEditorMode = ref(IModalType.create)

const approvedVisible = ref(false)
const editTask = ref<Record<string, any>>()

/**
 * 新增
 */
function handleAdd() {
	editorId.value = ""
	distributionEditorMode.value = IModalType.create
	distributionEditorVisible.value = true
}
/**
 * 查看
 * @param row
 */
async function handleRowView(row: any) {
	editorId.value = row.id
	distributionEditorMode.value = IModalType.view
	/* distributionDetailVisible.value = true */

	if (row.bpmStatus == appStatus.pendingApproval) {
		distributionDetailVisible.value = true
	} else {
		const res = await getTaskByBusinessIds({
			businessIds: [row.id],
			camundaKey: "distribution"
		})

		if (Array.isArray(res) && res.length > 0) {
			editTask.value = { ...res[res.length - 1] }
			approvedVisible.value = true
		} else {
			distributionDetailVisible.value = true
		}
	}
}

/**
 * 编辑
 * @param row
 */
function handleRowEdit(row: any) {
	editorId.value = row.id
	distributionEditorMode.value = IModalType.edit
	distributionEditorVisible.value = true
}

/**
 * 删除 操作
 * @param row
 */
async function handleRowDel(row: any) {
	await showDelConfirm()
	await PurchaseDistributionApi.deletePurchaseDistribution(row.id)
	ElMessage.success("移除成功")
	refreshTable()
}

const refreshTable = () => {
	fetchTableData()
	getTypeNumbers()
}

onMounted(async () => {
	fetchParam.value = {
		bpmStatus: tabStatus[0].join(","),
		sord: "desc",
		sidx: "createdDate"
	}
	getLineList()
	listCompanyWithFormat().then((r) => (companyOptions.value = r))
	getTableData()
})

/**
 * 排序
 * @param column
 * @param prop 字段名称
 * @param order ascending 升序 descending 降序
 */
function handleSortChange(column: any, prop: any, order: any) {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		sord:
			order === "descending" ? "desc" : order === "ascending" ? "asc" : "desc",
		sidx: order ? prop : "createdDate" // 排序字段
	}

	fetchTableData()
}

defineOptions({
	name: "distributionManagement"
})
</script>
<template>
	<div class="app-container">
		<div class="whole-frame">
			<ModelFrame class="top-frame">
				<Query
					:queryArrList="queryArrList"
					:needSingleSelect="true"
					@getQueryData="getTableData"
					class="ml10"
				/>
			</ModelFrame>
			<ModelFrame class="bottom-frame">
				<Title :title="rightTitle" :button="addBtn" @onBtnClick="handleAdd">
					<div class="app-tabs-wrapper">
						<el-tabs v-model="activeName" @tab-click="handleTabsClick">
							<el-tab-pane
								v-for="(tab, index) in tabList"
								:key="index"
								:label="`${tab.name}（${tabNum[index]}）`"
								:name="tab.name"
								:index="tab.name"
							/>
						</el-tabs>
					</div>
				</Title>
				<PitayaTable
					ref="tableRef"
					:columns="tableProp"
					:tableData="tableData"
					:total="pageTotal"
					:single-select="true"
					:need-selection="false"
					:need-index="true"
					@onSelectionChange="selectedTableList = $event"
					@on-current-page-change="onCurrentPageChange"
					:table-loading="tableLoading"
					@on-table-sort-change="handleSortChange"
				>
					<!-- 线路 -->
					<template #lineId="{ rowData }">
						<line-tag :options="lineList" :value="rowData.lineId" />
					</template>

					<!-- 审批状态 -->
					<template #bpmStatus="{ rowData }">
						<dict-tag
							:options="DictApi.getBpmStatus()"
							:value="rowData.bpmStatus"
						/>
					</template>

					<!-- 分包金额 -->
					<template #sumAmount="{ rowData }">
						<cost-tag :value="rowData.sumAmount" />
					</template>

					<!-- 操作 -->
					<template #operations="{ rowData }">
						<slot
							v-if="
								isCheckPermission(powerList.purchaseDistributionBtnPreview) ||
								(isCheckPermission(powerList.purchaseDistributionBtnEdit) &&
									hasEditByBpm(rowData.bpmStatus, rowData.createdBy)) ||
								(isCheckPermission(powerList.purchaseDistributionBtnDrop) &&
									hasEditByBpm(rowData.bpmStatus, rowData.createdBy))
							"
						>
							<el-button
								v-btn
								link
								@click="handleRowEdit(rowData)"
								:disabled="
									checkPermission(powerList.purchaseDistributionBtnEdit)
								"
								v-if="
									isCheckPermission(powerList.purchaseDistributionBtnEdit) &&
									hasEditByBpm(rowData.bpmStatus, rowData.createdBy)
								"
							>
								<font-awesome-icon :icon="['fas', 'pen-to-square']" />
								<span class="table-inner-btn">编辑</span>
							</el-button>

							<el-button
								v-btn
								link
								@click="handleRowView(rowData)"
								:disabled="
									checkPermission(powerList.purchaseDistributionBtnPreview)
								"
								v-if="
									isCheckPermission(powerList.purchaseDistributionBtnPreview)
								"
							>
								<font-awesome-icon :icon="['fas', 'eye']" />
								<span class="table-inner-btn">查看</span>
							</el-button>

							<el-button
								v-btn
								link
								@click="handleRowDel(rowData)"
								:disabled="
									checkPermission(powerList.purchaseDistributionBtnDrop)
								"
								v-if="
									isCheckPermission(powerList.purchaseDistributionBtnDrop) &&
									hasEditByBpm(rowData.bpmStatus, rowData.createdBy)
								"
							>
								<font-awesome-icon :icon="['fas', 'trash-can']" />
								<span class="table-inner-btn">移除</span>
							</el-button>
						</slot>
						<slot v-else>---</slot>
					</template>
				</PitayaTable>

				<!-- 新增、编辑 弹窗 -->
				<Drawer
					:size="modalSize.xl"
					v-model:drawer="distributionEditorVisible"
					:destroyOnClose="true"
				>
					<distribution-editor
						:id="editorId"
						:mode="distributionEditorMode"
						@close="distributionEditorVisible = false"
						@update="refreshTable"
					/>
				</Drawer>

				<!-- 查看详情 -->
				<Drawer
					:size="modalSize.xl"
					v-model:drawer="distributionDetailVisible"
					:destroyOnClose="true"
				>
					<distribution-detail
						:id="editorId"
						:mode="distributionEditorMode"
						@close="distributionDetailVisible = false"
					/>
				</Drawer>

				<!-- 审批、查看弹窗 -->
				<Drawer
					:size="modalSize.xxl"
					v-model:drawer="approvedVisible"
					:destroyOnClose="true"
				>
					<approved-drawer
						:mod="distributionEditorMode"
						:task-value="editTask"
						@on-save-or-close="approvedVisible = false"
					/>
				</Drawer>
			</ModelFrame>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
