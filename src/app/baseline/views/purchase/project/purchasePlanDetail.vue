<!-- 已生成订货计划 查看 -->
<script lang="ts" setup>
import TableFile from "@/app/baseline/views/components/tableFile.vue"
import { DictApi, fileBusinessType } from "@/app/baseline/api/dict"
import CostTag from "@/app/baseline/views/components/costTag.vue"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import { ref, onMounted } from "vue"
import { PurchaseProjectApi } from "@/app/baseline/api/purchase/purchaseProject"
import { useDictInit } from "../../components/dictBase"
import { getOnlyDate } from "../../../utils"
import XEUtils from "xe-utils"
import { modalSize } from "@/app/baseline/utils/layout-config"
import { toString } from "lodash-es"
import { IModalType } from "@/app/baseline/utils/types/common"
import { useTbInit } from "../../components/tableBase"
import { PurchasePlanApi } from "@/app/baseline/api/purchase/purchasePlan"
import planDetail from "../plan/planDetail.vue"
import { getRealLength, setString } from "@/app/baseline/utils/validate"

const { dictFilter, getDictByCodeList } = useDictInit()

const props = withDefaults(
	defineProps<{ id?: string; mode?: IModalType; footerBtnVisible?: boolean }>(),
	{
		mode: IModalType.view,
		footerBtnVisible: true
	}
)

const emit = defineEmits<{
	(e: "close"): void
	(e: "update"): void
}>()

/**
 * 能否编辑扩展信息
 */
const canEditExtra = computed(() => Boolean(formData.value?.id || props.id))

const drawerLeftTitle = {
	name: ["采购项目"],
	icon: ["fas", "square-share-nodes"]
}
const drawerRightTitle = computed(() => ({
	name: ["扩展信息"],
	icon: ["fas", "square-share-nodes"]
}))

const formBtnList = [{ name: "取消", icon: ["fas", "circle-minus"] }]

const futureYears = DictApi.getFutureYears()

const formData = ref<Record<string, any>>({
	year: futureYears[0].value
})

/**
 * 表单配置
 */
const descOptions = [
	{ label: "年度", name: "year" },
	{ label: "采购项目编号", name: "code" },
	{ label: "采购项目名称", name: "label" },
	{ label: "采购计划号", name: "planPurchaseCode" },
	{ label: "合同名称", name: "contractLabel" },
	{ label: "合同编号", name: "contractCode" },
	{ label: "采购方式", name: "purchaseType" },
	{ label: "采购员", name: "purchaseUserName" },
	{ label: "合同类型", name: "contractType" },
	{ label: "合同金额", name: "contractAmount_view" },
	{ label: "合同签订日期", name: "contractSigningDate" },
	{ label: "合同截止日期", name: "contractEndDate" },
	{ label: "供应商名称", name: "supplierLabel" },
	{ label: "创建日期", name: "createdDate" },
	{ label: "质保期", name: "warrantyPeriod_view" },
	{ label: "备注说明", name: "remark", needTooltip: true }
]

const drawerLoading = ref(false)

//扩展栏标签页切换
const tabList = ["订货计划"]
const activeTab = ref<number>(0)
const handleTabChange = (index: number) => {
	activeTab.value = index
}

/**
 * 获取 详情
 */
async function getDetail() {
	if (props.id) {
		drawerLoading.value = true

		try {
			const res = await PurchaseProjectApi.getPurchaseProject(props.id)
			formData.value = { ...res }

			formData.value.warrantyPeriod = (toString(res.warrantyPeriod) ??
				"") as any //toString(res.warrantyPeriod as any)
			formData.value.contractSigningDate = getOnlyDate(
				res.contractSigningDate as any
			)
			formData.value.contractEndDate = getOnlyDate(res.contractEndDate as any)
			const tmpAmount = `${XEUtils.commafy(res.contractAmount as any, {
				digits: 5
			})}`
			formData.value["contractAmount_view"] = tmpAmount
				? `￥${tmpAmount}`
				: "---"
		} finally {
			drawerLoading.value = false
		}
	}
}

onMounted(async () => {
	getDictByCodeList([
		"PURCHASE_TYPE",
		"CONTRACT_TYPE",
		"WARRANTY_PERIOD",
		"INVENTORY_UNIT"
	])
	if (props.id) {
		getDetail()
		getTableData()
	}
})

const queryArrList = [
	{
		name: "月度订货计划号",
		key: "code",
		placeholder: "请输入月度订货计划号",
		enableFuzzy: true,
		type: "input"
	}
]

const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	currentPage,
	pageTotal,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = useTbInit()

tableProp.value = [
	{ label: "年度", prop: "year", width: 80, fixed: "left" },
	{ label: "月份", prop: "month", width: 80 },
	{ label: "月度订货计划号", prop: "code" },
	{
		label: "采购金额",
		prop: "amount",
		needSlot: true,
		align: "right"
	},
	{
		label: "月度订货生成时间",
		prop: "generationDate",
		width: 160
	},
	{
		label: "采购订单状态",
		prop: "generatePurchaseStatus",
		width: 120,
		needSlot: true
	},
	{
		label: "操作人",
		prop: "createdBy_view",
		width: 150
	},
	{
		label: "操作时间",
		prop: "createdDate",
		width: 160
	},

	{
		label: "操作",
		width: 130,
		prop: "operations",
		fixed: "right",
		needSlot: true
	}
]

function getTableData(data?: { [propName: string]: any }) {
	//合并计划的物资明细
	if (props.id) {
		fetchFunc.value = PurchasePlanApi.getPurchasePlanList

		currentPage.value = 1
		tableRef.value?.resetCurrentPage()

		fetchParam.value = {
			projectId: props.id,
			sord: "desc",
			sidx: "createdDate",
			...data
		}
		fetchTableData()
	}
}

const purchasePlanDetailVisible = ref(false)
const editRow = ref()
const handlePurchasePlanDetail = (row: any) => {
	editRow.value = { ...row }
	purchasePlanDetailVisible.value = true
}
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<!-- 左侧表单区域 -->
		<div class="drawer-column left">
			<div class="rows">
				<Title :title="drawerLeftTitle" />
				<el-scrollbar>
					<el-descriptions size="small" :column="1" border class="content">
						<template>
							<el-descriptions-item
								v-for="(el, index) in descOptions"
								label-align="center"
								:label="el.label"
								:key="index"
							>
								<span v-if="el.name == 'contractAmount'">
									<cost-tag :value="formData[el.name]" />
								</span>
								<span v-else-if="el.name == 'purchaseType'">
									{{
										dictFilter("PURCHASE_TYPE", formData.purchaseType)?.label ||
										"---"
									}}
								</span>
								<span v-else-if="el.name == 'contractType'">
									{{
										dictFilter("CONTRACT_TYPE", formData.contractType)?.label ||
										"---"
									}}
								</span>
								<span
									v-else-if="
										[
											'contractSigningDate',
											'contractEndDate',
											'expireDate'
										].includes(el.name)
									"
								>
									{{ getOnlyDate(formData[el.name]) || "---" }}
								</span>
								<span v-else-if="el.needTooltip">
									<el-tooltip
										effect="dark"
										:content="formData?.[el.name]"
										:disabled="
											getRealLength(formData?.[el.name]) <= 100 ? true : false
										"
									>
										{{
											getRealLength(formData?.[el.name]) > 100
												? setString(formData?.[el.name], 100)
												: formData?.[el.name] || "---"
										}}
									</el-tooltip>
								</span>

								<span v-else>
									{{ formData[el.name] ? formData[el.name] : "---" }}
								</span>
							</el-descriptions-item>
						</template>
					</el-descriptions>
				</el-scrollbar>
			</div>
		</div>
		<!-- 右侧table区域 -->
		<div class="drawer-column right" :class="canEditExtra ? '' : ' disabled'">
			<div class="rows">
				<Title :title="drawerRightTitle">
					<Tabs class="tabs" :tabs="tabList" @on-tab-change="handleTabChange" />
				</Title>
				<div class="tab-mat" v-if="activeTab === 0">
					<Query
						:queryArrList="queryArrList"
						@getQueryData="getTableData"
						style="margin: 10px 0px -10px 10px !important"
					/>

					<el-scrollbar>
						<PitayaTable
							ref="tableRef"
							:columns="tableProp"
							:table-data="tableData"
							:need-index="true"
							:need-pagination="true"
							:single-select="false"
							:need-selection="false"
							:total="pageTotal"
							@onSelectionChange="selectedTableList = $event"
							@on-current-page-change="onCurrentPageChange"
							:table-loading="tableLoading"
						>
							<!-- 采购单价 -->
							<template #amount="{ rowData }">
								<cost-tag :value="rowData.amount" />
							</template>

							<!-- 采购订单状态 -->
							<template #generatePurchaseStatus="{ rowData }">
								<dict-tag
									:options="DictApi.getDealStatus()"
									:value="rowData.generatePurchaseStatus"
								/>
							</template>

							<template #operations="{ rowData }">
								<el-button
									v-btn
									link
									@click="handlePurchasePlanDetail(rowData)"
								>
									<font-awesome-icon :icon="['fas', 'eye']" />
									<span class="table-inner-btn">查看订单明细</span>
								</el-button>
							</template>
						</PitayaTable>
					</el-scrollbar>
				</div>

				<div v-if="activeTab === 1">
					<TableFile
						:business-type="fileBusinessType.purchaseProject"
						:business-id="props.id || formData.id"
					/>
				</div>
			</div>
			<ButtonList
				class="footer"
				:button="formBtnList"
				@on-btn-click="emit('close')"
			/>
		</div>

		<!-- 需求计划 查看详情 -->
		<Drawer
			:size="modalSize.xl"
			v-model:drawer="purchasePlanDetailVisible"
			:destroyOnClose="true"
		>
			<plan-detail
				:id="editRow.id"
				:model="IModalType.view"
				@close="purchasePlanDetailVisible = false"
			/>
		</Drawer>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.drawer-container {
	.left {
		width: 310px;
	}

	.right {
		width: calc(100% - 310px);
	}
}

.tab-mat {
	display: flex;
	flex-direction: column;
	height: 100%;
	width: 100%;
}
</style>
