<!-- 关联需求计划 详情 -->
<script lang="ts" setup>
import { ref, onMounted } from "vue"
import { DictApi, fileBusinessType } from "@/app/baseline/api/dict"
import { useDictInit } from "@/app/baseline/views/components/dictBase"

import TableFile from "../../components/tableFile.vue"
import CostTag from "@/app/baseline/views/components/costTag.vue"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import { first } from "lodash-es"
import { useTbInit } from "@/app/baseline/views/components/tableBase"
import monthNeedNumDetail from "./monthNeedNumDetail.vue"
import { toFixedTwo } from "@/app/baseline/utils"
import { PlanPurchaseMaterialItemApi } from "@/app/baseline/api/plan/planPurchaseMaterialItem"
import { IModalType } from "@/app/baseline/utils/types/common"
import { getRealLength, setString } from "@/app/baseline/utils/validate"

const { getDictByCodeList, dictFilter } = useDictInit()

const props = withDefaults(
	defineProps<{
		row: Record<string, any>
		purchasePlanId: string
		matId?: string
		footerBtnVisible?: boolean
		type?: string // 'orderGoods'
	}>(),
	{
		footerBtnVisible: true
	}
)
const emit = defineEmits<{
	(e: "save"): void
	(e: "close"): void
}>()

const descOptions = computed(() => {
	const ls = [
		{ label: "物资编码", name: "content_code" },
		{ label: "物资名称", name: "content_label" },
		{ label: "规格型号", name: "content_version" },
		{
			label: "技术参数",
			name: "content_technicalParameter",
			needTooltip: true
		},
		{ label: "物资性质", name: "attribute" },
		{ label: "采购单位", name: "content_buyUnit" },
		{ label: "预估采购周期（月）", name: "content_evaluationCycle" },
		{ label: "采购单价", name: "purchasePrice", type: "money" },
		{ label: "采购单价", name: "evaluation", type: "money" }, // 订货计划 - 查看需求计划 需要
		{ label: "采购数量", name: "purchaseNum" },
		{ label: "采购金额", name: "purchaseAmount", type: "money" },
		{ label: "合同余量", name: "contractSurplus" },
		{ label: "采购完成率", name: "rate" }
	]

	if (props.type === "orderGoods") {
		return ls.filter((v) => v.name != "purchasePrice")
	} else {
		return ls.filter((v) => v.name != "evaluation")
	}
})

const drawerLeftTitle = ref({
	name: ["物资信息"],
	icon: ["fas", "square-share-nodes"]
})

const drawerRightTitle = {
	name: ["扩展信息"],
	icon: ["fas", "square-share-nodes"]
}

const tabList = ref(["物资明细", "相关附件"])

const baseFormBtnList = computed(() => [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	}
])

//编辑表单
const formData = ref<Record<string, any>>({
	...props.row
})

const drawerLoading = ref(false)
const refFileTable = ref<any>()

//扩展栏标签页切换
const activeTab = ref<number>(0)
const handleTabChange = (index: number) => {
	activeTab.value = index

	if (index === 0) {
		getTableData({})
	}
}

onMounted(async () => {
	getDictByCodeList(["INVENTORY_UNIT", "PLAN_CATEGORY"])

	fetchParam.value = {
		purchasePlanId: props.purchasePlanId,
		materialId: props.matId,
		sord: "desc",
		sidx: "createdDate"
	}

	getTableData({})
})

const tbInit = useTbInit()
const {
	tableData,
	tableRef,
	tableLoading,
	currentPage,
	pageTotal,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = tbInit

tbInit.tableProp = computed(() => {
	const defTableColumns: TableColumnType[] = [
		{ label: "需求部门", prop: "sysOrgId_view", width: 160, fixed: "left" },
		{ label: "需求计划号", prop: "code", width: 160 },
		{ label: "需求计划名称", prop: "label", minWidth: 200 },
		{ label: "需求清单编号", prop: "planNeedCode", width: 150 },
		{ label: "需求数量", prop: "num", needSlot: true, width: 120 },
		{ label: "创建人", prop: "createdBy_view", width: 90 },
		{ label: "创建时间", prop: "createdDate", width: 160 }
	]

	return defTableColumns
})

fetchFunc.value =
	PlanPurchaseMaterialItemApi.getPlanPurchaseMaterialItemNeedPlanList

function getTableData(data: { [propName: string]: any }) {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		...data
	}

	fetchTableData()
}

/**
 * 补丁
 * 监听 tableData 变化；设置物资Id为第一个
 */
watch(
	() => tableData.value,
	() => {
		if (tableData.value?.length > 0) {
			nextTick(() => {
				tableRef.value.pitayaTableRef!.toggleRowSelection(
					first(tableData.value),
					true
				)
			})
		}
	}
)
</script>
<template>
	<div class="needplan-draw drawer-container" v-loading="drawerLoading">
		<!-- 左侧表单区域 -->
		<div class="drawer-column left">
			<div class="rows">
				<Title :title="drawerLeftTitle" />
				<el-scrollbar>
					<el-descriptions
						size="small"
						:column="1"
						:border="true"
						class="content"
					>
						<el-descriptions-item
							v-for="(el, index) in descOptions"
							label-align="center"
							:label="el.label"
							:key="index"
						>
							<span v-if="el.type == 'money'">
								<cost-tag :value="formData[el.name]" />
							</span>
							<span v-else-if="el.name === 'attribute'">
								<dict-tag
									:options="DictApi.getMatAttr()"
									:value="formData?.attribute"
								/>
							</span>
							<span v-else-if="el.name === 'content_buyUnit'">
								{{
									dictFilter("INVENTORY_UNIT", formData.content_buyUnit)
										?.subitemName || "---"
								}}
							</span>
							<span v-else-if="el.name === 'purchaseNum'">
								{{ toFixedTwo(formData.purchaseNum, 0) }}
							</span>
							<span v-else-if="el.name === 'contractSurplus'">
								{{ toFixedTwo(formData.contractSurplus, 0) }}
							</span>
							<span v-else-if="el.name === 'rate'">
								{{ formData.rate || 0 }}%
							</span>
							<span v-else-if="el.needTooltip">
								<el-tooltip
									effect="dark"
									:content="formData?.[el.name]"
									:disabled="
										getRealLength(formData?.[el.name]) <= 100 ? true : false
									"
								>
									{{
										getRealLength(formData?.[el.name]) > 100
											? setString(formData?.[el.name], 100)
											: formData?.[el.name] || "---"
									}}
								</el-tooltip>
							</span>
							<span v-else>
								{{ formData[el.name] ? formData[el.name] : "---" }}
							</span>
						</el-descriptions-item>
					</el-descriptions>
				</el-scrollbar>
			</div>
		</div>
		<!-- 右侧table区域 -->
		<div class="drawer-column right" :class="{ pdr10: !footerBtnVisible }">
			<div class="rows">
				<Title :title="drawerRightTitle">
					<Tabs class="tabs" :tabs="tabList" @on-tab-change="handleTabChange" />
				</Title>

				<div class="detail-table" v-if="activeTab === 0" style="display: flex">
					<div class="data-table editor-table-wrapper computedWidth">
						<PitayaTable
							ref="tableRef"
							:columns="(tbInit.tableProp as any)"
							:tableData="tableData"
							:need-index="true"
							:need-pagination="true"
							:single-select="true"
							:need-selection="true"
							:total="pageTotal"
							@onSelectionChange="selectedTableList = $event"
							@on-current-page-change="onCurrentPageChange"
							:table-loading="tableLoading"
						>
							<template #num="{ rowData }">
								{{ toFixedTwo(rowData.num, 0) }}
							</template>
						</PitayaTable>
					</div>

					<month-need-num-detail
						v-if="tableData.length > 0"
						:mat-id="props.matId"
						:plan-id="(first(tableRef?.pitayaTableRef?.getSelectionRows()) as any)?.id"
					/>
				</div>
				<div class="detail-table" v-else>
					<TableFile
						ref="refFileTable"
						v-if="formData.id"
						:mod="IModalType.view"
						:businessId="formData.id"
						:businessType="fileBusinessType.needPlan"
						:needPage="true"
					/>
				</div>
			</div>
			<ButtonList
				v-if="props.footerBtnVisible"
				class="footer"
				:button="baseFormBtnList"
				@on-btn-click="emit('close')"
			/>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

$--detail--width: 220px;

.custom-q {
	margin: 10px 0px -10px 10px !important;
}

.editor-table-wrapper {
	&:deep(.pitaya-table) {
		.error {
			.column-inner-item,
			.cell,
			.el-input__inner {
				color: red;
			}
		}
	}
}
.data-table {
	width: 100%;
}
.computedWidth {
	width: calc(100% - 220px);
}

.drawer-container {
	width: 100%;

	.left {
		width: 300px;
		.el-scrollbar {
			height: calc(100% - 73px);
		}
		.content {
			width: 100%;
			.el-form {
				width: calc(100% - 13px);
			}
		}
	}
	.right {
		width: calc(100% - 300px);
		.detail-table {
			width: 100%;
			height: 100%;
		}
	}
}
</style>
