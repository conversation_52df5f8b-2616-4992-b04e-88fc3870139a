<!-- 生成月度计划 -->
<script lang="ts" setup>
import { ref } from "vue"
import { PurchaseProjectApi } from "../../../api/purchase/purchaseProject"
import GridPanel from "../../store/components/gridPanel.vue"
import { PurchasePlanApi } from "../../../api/purchase/purchasePlan"
import { appStatus } from "@/app/baseline/api/dict"
import { useMessageBoxInit } from "@/app/baseline/views/components/messageBox"
import { getIdempotentToken } from "@/app/baseline/utils/validate"
import {
	IIdempotentTokenType,
	IIdempotentTokenTypePre
} from "@/app/baseline/utils/types/common"

const { showWarnConfirm } = useMessageBoxInit()

const props = withDefaults(
	defineProps<{
		id: string | number
		row?: Record<string, any>
	}>(),
	{}
)
const emits = defineEmits<{
	(e: "update"): void
	(e: "close"): void
}>()

const titleConf = {
	name: ["生成月度计划"],
	icon: ["fas", "square-share-nodes"]
}
const formBtnList = [
	{ name: "取消", icon: ["fas", "circle-minus"] },
	{ name: "保存", icon: ["fas", "floppy-disk"] }
]
const drawerLoading = ref(false)
const formBtnLoading = ref(false)

const selectedDate = computed(() => {
	/**
	 *  当前月份
	 */
	const currentMonth = new Date().getMonth() + 1

	/**
	 *  当前年份
	 */
	const currentYear = new Date().getFullYear()

	return {
		year: currentMonth == 12 ? currentYear + 1 : currentYear,
		month: currentMonth == 12 ? 1 : currentMonth + 1
	}
})

// 左侧按钮点击
async function formBtnAction(btnName: string | undefined) {
	if (btnName === "保存") {
		if (props.id) {
			await showWarnConfirm("请确认是否生成月度计划？")

			formBtnLoading.value = true
			try {
				const res = await PurchasePlanApi.getPurchasePlanList({
					projectId: props.row?.id,
					year: props.row?.year,
					month: selectedDate.value.month,
					bpmStatus: `${appStatus.pendingApproval},${appStatus.rejected}`
				})

				if (res?.records) {
					await showWarnConfirm("本月已生成月度订货计划，是否更新？")
				}

				const params = objectToFormData({
					id: props.id
				})

				const idempotentToken = getIdempotentToken(
					IIdempotentTokenTypePre.other,
					IIdempotentTokenType.purchaseProject,
					props.id
				)
				await PurchaseProjectApi.generateMonthlyPurchasePlan(
					params as any,
					idempotentToken
				)

				ElMessage.success("月度计划生成成功")
				emits("update")
				emits("close")
			} finally {
				formBtnLoading.value = false
			}
		}
	}
	if (btnName === "取消") {
		emits("close")
	}
}

const gridPanelOptions = computed(() => {
	return [
		{
			label: "年度",
			value: selectedDate.value.year
		},
		{
			label: "月份",
			value: selectedDate.value.month
		}
	]
})

/**
 * 关联业务单数据
 */
const descList = ref([
	{ label: "年度", key: "year" },
	{ label: "月份", key: "month" },
	{ label: "采购项目编号", key: "code" },
	{ label: "采购项目名称", key: "label" },
	{ label: "供应商名称", key: "supplierLabel" },
	{ label: "采购员", key: "purchaseUserName" }
])
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<!-- 右侧table区域 -->
		<div class="drawer-column">
			<div class="rows">
				<Title :title="titleConf" />
				<el-scrollbar>
					<grid-panel :options="gridPanelOptions" :isFormat="false" />
					<el-descriptions size="small" :column="1" border class="content">
						<template>
							<el-descriptions-item
								v-for="desc in descList"
								:key="desc.key"
								:label="desc.label"
							>
								<span v-if="desc.key === 'month'">
									{{ selectedDate.month }}
								</span>
								<span v-else>
									{{ props.row?.[desc.key] || "---" }}
								</span>
							</el-descriptions-item>
						</template>
					</el-descriptions>
				</el-scrollbar>
			</div>
			<ButtonList
				class="footer"
				:button="formBtnList"
				:loading="formBtnLoading"
				@on-btn-click="formBtnAction"
			/>
		</div>
	</div>
</template>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index";

.drawer-container {
	.drawer-column {
		width: 100%;
	}
}
</style>
