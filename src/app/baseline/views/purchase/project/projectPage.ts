//数据表全量字段定义
import { BaseLineSysApi } from "../../../api/system"
import { Dict<PERSON><PERSON> } from "../../../api/dict"
import { FormElementType } from "../../components/define"
import { PurchaseContractApi } from "../../../api/purchase/purchaseContract"
import { PurchaseSupplierApi } from "../../../api/purchase/purchaseSupplier"
export interface PurchaseProjectVo {
	/**
	 * 集采项目编号
	 */
	centralizePurchaseProjectCode?: null | string
	/**
	 * 采购项目编号
	 */
	code?: null | string
	/**
	 * 合同ID
	 */
	contractId?: string
	/**
	 * 创建人
	 */
	createdBy?: null | string
	/**
	 * 创建时间
	 */
	createdDate?: null | string
	/**
	 * 有效截止日期
	 */
	expireDate?: null | string
	/**
	 * ID
	 */
	id?: string
	/**
	 * 修改人
	 */
	lastModifiedBy?: null | string
	/**
	 * 修改时间
	 */
	lastModifiedDate?: null | string
	/**
	 * 采购项目名称
	 */
	name?: null | string
	/**
	 * 采购计划id
	 */
	planId?: null | string
	/**
	 * 创建时间
	 */
	purchaseCreateDate?: null | string
	/**
	 * 采购方式
	 */
	purchaseType?: null | string
	/**
	 * 采购员id
	 */
	purchaseUserId?: string
	/**
	 * 备注
	 */
	remark?: null | string
	/**
	 * 供货商id
	 */
	supplierId?: string | null
	/**
	 * 年度
	 */
	year?: null | string
	contractCode?: string
	contractLabel?: string
	contractSigningDate?: string | null
	contractEndDate?: string | null
	contractType?: string
	contractAmount?: string
	supplierLabel?: string
	purchaseUserName?: string
	planPurchaseId?: string
	[property: string]: any
}
/**
 * 数据表全量字段定义
 */
const projectTableProps = [
	{ label: "年度", prop: "year", width: 85, fixed: "left" },
	{ label: "采购计划号", prop: "planPurchaseCode", width: 150, fixed: "left" },
	{ label: "采购项目编号", prop: "code", width: 180, fixed: "left" },
	{ label: "采购项目名称", prop: "label", minWidth: 200 },
	{
		label: "已生成订货计划",
		prop: "purchasePlanNum",
		needSlot: true,
		width: 200
	},
	{ label: "合同编号", prop: "contractCode", width: 200 },
	{ label: "合同名称", prop: "contractLabel", width: 250 },
	{
		label: "合同签订日期",
		prop: "contractSigningDate",
		needSlot: true,
		width: 160
	},
	{
		label: "合同截止日期",
		prop: "contractEndDate",
		needSlot: true,
		width: 160
	},
	{ label: "合同类型", prop: "contractType", needSlot: true, width: 130 },
	{
		label: "合同金额",
		prop: "contractAmount",
		needSlot: true,
		width: 130,
		align: "right"
	},
	{ label: "供应商", prop: "supplierLabel", width: 200 },
	{ label: "采购方式", prop: "purchaseType", needSlot: true, width: 130 },
	// { label: "有效截止日期", prop: "expireDate", needSlot: true, width: 160 },
	{ label: "采购员", prop: "purchaseUserName", width: 130 },
	{ label: "创建时间", prop: "createdDate", width: 160 },
	{ label: "备注说明", prop: "remark", width: 130 },
	{
		label: "操作",
		width: 160,
		prop: "operations",
		fixed: "right",
		needSlot: true
	}
]
const projectFormEl: FormElementType[][] = [
	[
		{
			label: "采购项目ID",
			name: "id",
			maxlength: 50,
			disabled: true,
			type: "hidden"
		}
	],
	[
		{
			label: "采购项目编号",
			name: "code",
			maxlength: 50,
			disabled: true
		}
	],
	[
		{
			label: "采购项目名称",
			name: "label",
			maxlength: 50,
			disabled: true
		}
	],
	[
		{
			label: "采购计划号",
			name: "planPurchaseCode",
			maxlength: 50,
			disabled: true
		}
	],
	[
		{
			label: "年度",
			name: "year",
			type: "select",
			data: DictApi.getFutureYears(),
			disabled: true
		}
	],
	[
		{
			label: "采购方式",
			name: "purchaseType",
			type: "select",
			data: [],
			width: 12
		},
		{
			label: "采购员",
			name: "purchaseUserName",
			type: "drawer",
			clickApi: () => {},
			width: 12,
			disabled: true
		}
	],
	[
		{
			label: "合同名称",
			name: "contractLabel",
			type: "drawer",
			clickApi: () => {}
		}
	],
	[
		{
			label: "合同编号",
			name: "contractCode",
			disabled: true
		}
	],
	[
		{
			label: "合同签订日期",
			name: "contractSigningDate",
			width: 12,
			disabled: true
		},
		{
			label: "合同截止日期",
			name: "contractEndDate",
			width: 12,
			disabled: true
		}
	],
	[
		{
			label: "合同类型",
			name: "contractType",
			type: "select",
			width: 12,
			disabled: true
		},
		{
			label: "合同金额",
			name: "contractAmount_view",
			width: 12,
			disabled: true,
			type: "money"
		}
	],
	[
		{
			label: "供应商名称",
			name: "supplierLabel",
			disabled: true
		}
	],
	[
		{
			label: "创建日期",
			name: "createdDate",
			disabled: true
		}
	],
	/*[
		{
			label: "有效截止日期",
			name: "expireDate",
			type: "date"
		}
	],*/
	[
		{
			label: "备注说明",
			name: "remark",
			maxlength: 200,
			rows: "5",
			type: "textarea"
		}
	]
]
const projectFormRules = {
	contractLabel: [
		{ required: true, message: "合同名称不能为空", trigger: "change" }
	]
}
function getProjectQueryArrList() {
	const arrQuery: { [propName: string]: any }[] = []
	const input = ["planPurchaseCode", "code", "label"] //修改这个数组即可
	input.forEach((prop) => {
		const found = projectTableProps.find((obj) => obj.prop == prop)
		if (found) {
			arrQuery.push({
				name: found.label,
				key: found.prop,
				placeholder: `请输入${found.label}`,
				enableFuzzy: true,
				type: "input"
			})
		}
	})
	const select = [
		{
			name: "年度",
			key: "year",
			type: "select",
			children: DictApi.getFutureYears(-2, 2),
			placeholder: "请选择所属年度"
		},
		{
			name: "公司",
			key: "sysCommunityId",
			type: "treeSelect",
			treeApi: BaseLineSysApi.getCompanyAllList,
			placeholder: "请选择所属公司"
		},
		{
			name: "合同类型",
			key: "contractType",
			type: "elTreeSelect",
			children: [],
			placeholder: "请选择合同类型"
		},
		{
			name: "采购方式",
			key: "purchaseType",
			type: "select",
			children: [],
			placeholder: "请选择项采购方式"
		},
		// {
		// 	name: "合同编号",
		// 	key: "contractCode",
		// 	type: "tableSelect",
		// 	tableApi: PurchaseContractApi.getPurchaseContractList,
		// 	tableColumns:[{ label: "ID", prop: "id", width: 80 },
		// 		{ label: "合同编号", prop: "contractCode", width: 160 },
		// 		{ label: "合同名称", prop: "contractLabel", minWidth: 260 }],
		// 	placeholder: "请选择所属公司"
		// },
		// {
		// 	name: "供应商",
		// 	key: "supplierLabel",
		// 	type: "tableSelect",
		// 	tableApi: PurchaseSupplierApi.getPurchaseSupplierList,
		// 	tableColumns:[	{ label: "供应商编码", prop: "code", width: 160 },
		// 		{ label: "供应商名称", prop: "label", width: 400 }],
		// 	placeholder: "请选择所属公司"
		// },
		{
			name: "合同签订日期",
			key: "contractSigningDate",
			type: "startAndEndTime"
		},
		{
			name: "合同截止日期",
			key: "contractEndDate",
			type: "startAndEndTime"
		}
	]
	return arrQuery.concat(select)
}
/**
 * 传递字段名称，返回对应的字段数组，保持定义一份
 * @param props
 */
function getProjectTableProps(props?: any[], exclude?: any[]) {
	let retProps = projectTableProps
	if (Array.isArray(exclude) && exclude.length > 0) {
		retProps = projectTableProps.filter((obj) => !exclude.includes(obj.prop))
	}
	if (Array.isArray(props) && props.length > 0) {
		const filteredProps = retProps.filter((obj) => props.includes(obj.prop))
		const orderedProps = props.map((prop) =>
			filteredProps.find((obj) => obj.prop === prop)
		)

		return orderedProps
	} else {
		return retProps
	}
}
function getProjectFormEl() {
	return projectFormEl
}
function getProjectDescEl(props: any[]) {
	const elArray: { [propName: string]: any }[] = []
	if (Array.isArray(props) && props.length > 0) {
		props.forEach((prop) => {
			const found = projectTableProps.find((obj) => obj.prop == prop)
			if (found) {
				elArray.push({
					label: found.label,
					name: found.prop
				})
			}
		})
	}
	return elArray
}
function getProjectFormRules() {
	return projectFormRules
}
function getProjectSingleFormEl(formEl: FormElementType[][], name: string) {
	const foundObject = formEl
		.flat()
		.find((formElement) => formElement.name === name)

	return foundObject
}
function getQueryFormEl(formEl: any[], key: string) {
	const foundObject = formEl.find((formElement) => formElement.key === key)
	return foundObject
}
export const projectPageApi = {
	getProjectTableProps,
	getProjectQueryArrList,
	getProjectFormRules,
	getProjectFormEl,
	getProjectDescEl,
	getProjectSingleFormEl,
	getQueryFormEl
}
