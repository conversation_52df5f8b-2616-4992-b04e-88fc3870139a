<!-- 采购项目 查看 -->
<script lang="ts" setup>
import TableFile from "@/app/baseline/views/components/tableFile.vue"
import { DictApi, fileBusinessType } from "@/app/baseline/api/dict"
import CostTag from "@/app/baseline/views/components/costTag.vue"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import { ref, onMounted } from "vue"
import { PurchaseProjectApi } from "@/app/baseline/api/purchase/purchaseProject"
import { useDictInit } from "../../components/dictBase"
import { getOnlyDate, toFixedTwo, toMoney } from "../../../utils"
import XEUtils from "xe-utils"
import { modalSize } from "@/app/baseline/utils/layout-config"
import { toString } from "lodash-es"
import { IModalType } from "@/app/baseline/utils/types/common"
import { useTbInit } from "../../components/tableBase"
import { PurchaseProjectItemApi } from "../../../api/purchase/purchaseProjectItem"
import needPlanDetail from "./needPlanDetail.vue"
import { getRealLength, setString } from "@/app/baseline/utils/validate"

const { dictFilter, dictOptions, getDictByCodeList } = useDictInit()

const props = withDefaults(
	defineProps<{ id?: string; mode?: IModalType; footerBtnVisible?: boolean }>(),
	{
		mode: IModalType.view,
		footerBtnVisible: true
	}
)

const emit = defineEmits<{
	(e: "close"): void
	(e: "update"): void
}>()

/**
 * 能否编辑扩展信息
 */
const canEditExtra = computed(() => Boolean(formData.value?.id || props.id))

const drawerLeftTitle = {
	name: ["采购项目"],
	icon: ["fas", "square-share-nodes"]
}
const drawerRightTitle = computed(() => ({
	name: formData.value?.label ? [`【${formData.value.label}】`] : ["扩展信息"],
	icon: ["fas", "square-share-nodes"]
}))

const formBtnList = [{ name: "取消", icon: ["fas", "circle-minus"] }]

const futureYears = DictApi.getFutureYears()

const formData = ref<Record<string, any>>({
	year: futureYears[0].value
})

/**
 * 表单配置
 */
const descOptions = [
	{ label: "年度", name: "year" },
	{ label: "采购项目编号", name: "code" },
	{ label: "采购项目名称", name: "label" },
	{ label: "采购计划号", name: "planPurchaseCode" },
	{ label: "合同名称", name: "contractLabel" },
	{ label: "合同编号", name: "contractCode" },
	{ label: "采购方式", name: "purchaseType" },
	{ label: "采购员", name: "purchaseUserName" },
	{ label: "合同类型", name: "contractType" },
	{ label: "合同金额", name: "contractAmount_view" },
	{ label: "合同签订日期", name: "contractSigningDate" },
	{ label: "合同截止日期", name: "contractEndDate" },
	{ label: "供应商名称", name: "supplierLabel" },
	{ label: "创建日期", name: "createdDate" },
	{ label: "质保期", name: "warrantyPeriod_view" },
	{ label: "备注说明", name: "remark", needTooltip: true }
]

const drawerLoading = ref(false)

//扩展栏标签页切换
const tabList = ["物资明细"]
const activeTab = ref<number>(0)
const handleTabChange = (index: number) => {
	activeTab.value = index
}

/**
 * 获取 详情
 */
async function getDetail() {
	if (props.id) {
		drawerLoading.value = true

		try {
			const res = await PurchaseProjectApi.getPurchaseProject(props.id)
			formData.value = { ...res }

			formData.value.warrantyPeriod = (toString(res.warrantyPeriod) ??
				"") as any //toString(res.warrantyPeriod as any)
			formData.value.contractSigningDate = getOnlyDate(
				res.contractSigningDate as any
			)
			formData.value.contractEndDate = getOnlyDate(res.contractEndDate as any)

			formData.value["contractAmount_view"] = toMoney(res.contractAmount as any)
		} finally {
			drawerLoading.value = false
		}
	}
}

onMounted(async () => {
	getDictByCodeList([
		"PURCHASE_TYPE",
		"CONTRACT_TYPE",
		"WARRANTY_PERIOD",
		"INVENTORY_UNIT",
		"MATERIAL_NATURE"
	])
	if (props.id) {
		getDetail()
		getTableData()
	}
})

/**
 * 排序
 * @param column
 * @param prop 字段名称
 * @param order ascending 升序 descending 降序
 */
function handleSortChange(column: any, prop: any, order: any) {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	const computedProp = () => {
		if (prop === "content_code") {
			return "materialCode"
		} else if (prop === "purchasePrice") {
			return "unitPrice"
		} else {
			return prop
		}
	}

	fetchParam.value = {
		...fetchParam.value,
		sord:
			order === "descending" ? "desc" : order === "ascending" ? "asc" : "desc",
		sidx: order ? computedProp() : "createdDate" // 排序字段
	}

	fetchTableData()
}

const queryArrList = computed(() => [
	{
		name: "物资编码",
		key: "materialCode",
		placeholder: "请输入物资编码",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资名称",
		key: "materialLabel",
		placeholder: "请输入物资名称",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "规格型号",
		key: "version",
		placeholder: "请输入规格型号",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资性质",
		key: "attribute",
		type: "select",
		children: dictOptions.value.MATERIAL_NATURE,
		placeholder: "请选择物资性质"
	}
])

const lastTableData = ref<any[]>([])

const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	currentPage,
	pageTotal,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = useTbInit()

tableProp.value = [
	{
		label: "物资编码",
		prop: "content_code",
		width: 130,
		fixed: "left",
		sortable: true
	},
	{ label: "物资名称", prop: "content_label", width: 150 },
	{ label: "规格型号", prop: "content_version", width: 120 },
	{
		label: "技术参数",
		prop: "content_technicalParameter",
		minWidth: 200,
		align: "left"
	},
	{ label: "物资性质", prop: "attribute", needSlot: true, width: 120 },
	{ label: "采购单位", prop: "content_buyUnit", needSlot: true, width: 90 },
	{
		label: "预估采购周期（月）",
		prop: "content_evaluationCycle",
		width: 150
	},
	{
		label: "采购单价",
		prop: "purchasePrice",
		needSlot: true,
		width: 150,
		align: "right",
		sortable: true
	},
	{
		label: "采购数量",
		prop: "purchaseNum",
		needSlot: true,
		width: 100,
		align: "right"
	},
	{ label: "质保期", prop: "warrantyPeriod_view", width: 130 },
	{
		label: "采购金额",
		prop: "purchaseAmount",
		needSlot: true,
		width: 150,
		align: "right"
	},
	{ label: "合同余量", prop: "contractSurplus", width: 100, align: "right" },
	{
		label: "采购完成率",
		prop: "rate",
		needSlot: true,
		width: 100,
		align: "right"
	},
	{
		label: "操作",
		width: 130,
		prop: "operations",
		fixed: "right",
		needSlot: true
	}
]

function getTableData(data?: { [propName: string]: any }) {
	//合并计划的物资明细
	if (props.id) {
		fetchFunc.value = PurchaseProjectItemApi.getPurchaseProjectItemList

		currentPage.value = 1
		tableRef.value?.resetCurrentPage()

		fetchParam.value = {
			projectId: props.id,
			sord: "desc",
			sidx: "createdDate",
			...data
		}
		fetchTableData()
	}
}

/**
 * 补丁
 * 监听tableData,重组 lastTableData
 */
watch([tableData], () => {
	XEUtils.map(tableData.value as any, (_d: { [propName: string]: any }) => {
		if (_d.content) {
			Array.from(Object.keys(_d?.content))?.map(
				(_k) => (_d[`content_${_k}`] = _d.content[_k])
			)
		}

		_d.contractSurplus = _d.contractSurplus || 0
		_d.rate = _d.rate || 0
		_d.warrantyPeriod = toString(_d.warrantyPeriod)
	})
	lastTableData.value = tableData.value
})

const needPlanDetailVisible = ref(false)
const editRow = ref()
const handleNeedPlanDetail = (row: any) => {
	editRow.value = { ...row }
	needPlanDetailVisible.value = true
}
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<!-- 左侧表单区域 -->
		<div class="drawer-column left">
			<div class="rows">
				<Title :title="drawerLeftTitle" />
				<el-scrollbar>
					<el-descriptions size="small" :column="1" border class="content">
						<template>
							<el-descriptions-item
								v-for="(el, index) in descOptions"
								label-align="center"
								:label="el.label"
								:key="index"
							>
								<span v-if="el.name == 'contractAmount'">
									<cost-tag :value="formData[el.name]" />
								</span>
								<span v-else-if="el.name == 'purchaseType'">
									{{
										dictFilter("PURCHASE_TYPE", formData.purchaseType)?.label ||
										"---"
									}}
								</span>
								<span v-else-if="el.name == 'contractType'">
									{{
										dictFilter("CONTRACT_TYPE", formData.contractType)?.label ||
										"---"
									}}
								</span>
								<span
									v-else-if="
										[
											'contractSigningDate',
											'contractEndDate',
											'expireDate'
										].includes(el.name)
									"
								>
									{{ getOnlyDate(formData[el.name]) || "---" }}
								</span>
								<span v-else-if="el.needTooltip">
									<el-tooltip
										effect="dark"
										:content="formData?.[el.name]"
										:disabled="
											getRealLength(formData?.[el.name]) <= 100 ? true : false
										"
									>
										{{
											getRealLength(formData?.[el.name]) > 100
												? setString(formData?.[el.name], 100)
												: formData?.[el.name] || "---"
										}}
									</el-tooltip>
								</span>
								<span v-else>
									{{ formData[el.name] ? formData[el.name] : "---" }}
								</span>
							</el-descriptions-item>
						</template>
					</el-descriptions>
				</el-scrollbar>
			</div>
		</div>
		<!-- 右侧table区域 -->
		<div class="drawer-column right" :class="canEditExtra ? '' : ' disabled'">
			<div class="rows">
				<Title :title="drawerRightTitle">
					<Tabs class="tabs" :tabs="tabList" @on-tab-change="handleTabChange" />
				</Title>
				<div class="tab-mat" v-if="activeTab === 0">
					<Query
						:queryArrList="queryArrList"
						@getQueryData="getTableData"
						style="margin: 10px 0px -10px 10px !important"
					/>

					<el-scrollbar>
						<PitayaTable
							ref="tableRef"
							:columns="tableProp"
							:table-data="lastTableData"
							:need-index="true"
							:need-pagination="true"
							:single-select="false"
							:need-selection="false"
							:total="pageTotal"
							@onSelectionChange="selectedTableList = $event"
							@on-current-page-change="onCurrentPageChange"
							:table-loading="tableLoading"
							@on-table-sort-change="handleSortChange"
						>
							<!-- 物资性质 -->
							<template #attribute="{ rowData }">
								<dict-tag
									:options="DictApi.getMatAttr()"
									:value="rowData.attribute"
								/>
							</template>

							<template #content_buyUnit="{ rowData }">
								<dict-tag
									:options="dictOptions.INVENTORY_UNIT"
									:value="rowData.content_buyUnit"
								/>
							</template>
							<!-- 采购单价 -->
							<template #purchasePrice="{ rowData }">
								<cost-tag :value="rowData.purchasePrice" />
							</template>
							<template #purchaseAmount="{ rowData }">
								<cost-tag :value="rowData.purchaseAmount" />
							</template>

							<template #rate="{ rowData }"> {{ rowData.rate }}% </template>

							<template #purchaseNum="{ rowData }">
								{{ toFixedTwo(rowData.purchaseNum, 0) }}
							</template>
							<template #operations="{ rowData }">
								<el-button v-btn link @click="handleNeedPlanDetail(rowData)">
									<font-awesome-icon :icon="['fas', 'eye']" />
									<span class="table-inner-btn">关联需求计划</span>
								</el-button>
							</template>
						</PitayaTable>
					</el-scrollbar>
				</div>

				<div v-if="activeTab === 1">
					<TableFile
						:business-type="fileBusinessType.purchaseProject"
						:business-id="props.id || formData.id"
					/>
				</div>
			</div>
			<ButtonList
				class="footer"
				:button="formBtnList"
				@on-btn-click="emit('close')"
			/>
		</div>

		<!-- 需求计划 查看详情 -->
		<Drawer
			:size="modalSize.xl"
			v-model:drawer="needPlanDetailVisible"
			:destroyOnClose="true"
		>
			<need-plan-detail
				:row="editRow"
				:mat-id="editRow.content_id"
				:id="formData.id"
				:purchase-plan-id="formData.planPurchaseId"
				@close="needPlanDetailVisible = false"
			/>
		</Drawer>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.drawer-container {
	.left {
		width: 310px;
	}

	.right {
		width: calc(100% - 310px);
	}
}

.tab-mat {
	display: flex;
	flex-direction: column;
	height: 100%;
	width: 100%;
}
</style>
