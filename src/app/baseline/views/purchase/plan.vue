<!-- 订货计划 主表 -->
<script lang="ts" setup>
import { ref, onMounted } from "vue"
import { useTbInit } from "@/app/baseline/views/components/tableBase"
import { useDictInit } from "@/app/baseline/views/components/dictBase"
import {
	checkPermission,
	isCheckPermission
} from "@/app/platform/utils/permission"
import { powerList } from "@/app/baseline/views/components/define.d"
import { getTaskByBusinessIds, listCompanyWithFormat } from "../../api/system"
import { appStatus, dealStatus, DictApi } from "../../api/dict"
import { PurchasePlanApi } from "../../api/purchase/purchasePlan"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import CostTag from "@/app/baseline/views/components/costTag.vue"

import { modalSize } from "@/app/baseline/utils/layout-config"
import { first, map } from "lodash-es"
import { IModalType } from "../../utils/types/common"
import planEditor from "./plan/planEditor.vue"
import planDetail from "./plan/planDetail.vue"
import { PurchaseSupplierApi } from "../../api/purchase/purchaseSupplier"
import createdOrderEditor from "./plan/createdOrderEditor.vue"
import ApprovedDrawer from "@/app/baseline/views/components/approvedDrawer.vue"

const { getDictByCodeList } = useDictInit()

/**
 * 公司列表
 */
const companyOptions = ref<any>([])

/**
 * 查询条件 配置
 */
const queryArrList = computed(() => [
	{
		name: "月度订货计划号",
		key: "code",
		type: "input",
		placeholder: "请输入月度订货计划号",
		enableFuzzy: true
	},
	{
		name: "采购计划号",
		key: "planPurchaseCode",
		type: "input",
		placeholder: "请输入采购计划号",
		enableFuzzy: true
	},
	{
		name: "采购项目编号",
		key: "purchaseProjectCode",
		type: "input",
		placeholder: "请输入采购项目编号",
		enableFuzzy: true
	},
	{
		name: "采购项目名称",
		key: "purchaseProjectLabel",
		type: "input",
		placeholder: "请输入采购项目名称",
		enableFuzzy: true
	},
	{
		name: "公司",
		key: "sysCommunityId",
		type: "select",
		children: companyOptions.value,
		placeholder: "请选择所属公司"
	},
	{
		name: "年度",
		key: "year",
		type: "select",
		children: DictApi.getFutureYears(-2, 2),
		placeholder: "请选择年度"
	},
	{
		name: "月份",
		key: "month",
		type: "select",
		children: DictApi.getMonthList(),
		placeholder: "请选择月份"
	},
	{
		name: "审批状态",
		key: "bpmStatus",
		type: "select",
		placeholder: "请选择审批状态",
		children: DictApi.getQueryBpmStatus()
	},
	{
		name: "采购订单状态",
		key: "generatePurchaseStatus",
		type: "select",
		placeholder: "请选择采购订单状态",
		children: DictApi.getDealStatus()
	},
	{
		name: "供应商",
		key: "supplierId",
		type: "tableSelect",
		placeholder: "请选择供应商",
		tableInfo: [
			{
				title: "请选择供应商",
				tableApi: PurchaseSupplierApi.getPurchaseSupplierList, //表格接口
				tableColumns: [
					{ label: "供应商编码", prop: "code", width: 160 },
					{ label: "供应商名称", prop: "label" }
				],
				queryArrList: [
					{
						name: "供应商编码",
						key: "code",
						type: "input",
						placeholder: "请输入供应商编码"
					},
					{
						name: "供应商名称",
						key: "label",
						type: "input",
						placeholder: "请输入供应商名称"
					}
				],
				params: {
					currentPage: 1,
					pageSize: 20
				}, //表格接口参数

				labelName: {
					key: "id", //回显标识
					label: "label", //input框要展示的字段
					value: "id" //要给后台传的字段
				}
			}
		]
	}
])

const tbInit = useTbInit()
const {
	tableData,
	tableRef,
	tableLoading,
	currentPage,
	pageTotal,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = tbInit

tbInit.tableProp = computed(() => {
	const ls: TableColumnType[] = [
		{ label: "年度", prop: "year", width: 80, fixed: "left", sortable: true },
		{ label: "月份", prop: "month", width: 80, fixed: "left", sortable: true },
		{ label: "月度订货计划号", prop: "code", width: 180, fixed: "left" },
		{ label: "采购计划号", prop: "planPurchaseCode", width: 160 },
		{ label: "关联采购项目编号", prop: "purchaseProjectCode", width: 180 },
		{ label: "采购项目名称", prop: "purchaseProjectLabel", width: 150 },
		{ label: "合同编号", prop: "contractCode", minWidth: 150 },
		{ label: "合同名称", prop: "contractLabel", width: 180 },
		{ label: "供应商名称", prop: "supplierLabel", minWidth: 180 },
		{
			label: "订货金额",
			prop: "amount",
			needSlot: true,
			align: "right",
			width: 160
		},
		{ label: "审批状态", prop: "bpmStatus", needSlot: true, width: 110 },
		{
			label: "采购订单状态",
			prop: "generatePurchaseStatus",
			needSlot: true,
			width: 110
		},
		{
			label: "月度订货生成时间",
			prop: "generationDate",
			width: 160,
			sortable: true
		},

		{
			label: "操作人",
			prop: "lastModifiedBy_view",
			needSlot: false,
			width: 120
		},
		{ label: "更新时间", prop: "lastModifiedDate", width: 160, sortable: true },
		{ label: "作废原因", prop: "scrapReason", width: 160, fixed: "right" },
		{ label: "作废时间", prop: "lastModifiedDate", width: 160, fixed: "right" },
		{
			label: "操作",
			prop: "operations",
			width: 140,
			fixed: "right",
			needSlot: true
		}
	]
	return activeType.value == "1"
		? ls.filter(
				(v) =>
					v.prop != "bpmStatus" &&
					v.prop != "scrapReason" &&
					v.label != "作废时间"
		  )
		: activeType.value == "0"
		? ls.filter(
				(v) =>
					v.prop != "generatePurchaseStatus" &&
					v.label != "更新时间" &&
					v.prop != "scrapReason" &&
					v.label != "作废时间"
		  )
		: ls.filter(
				(v) => v.prop != "generatePurchaseStatus" && v.label != "更新时间"
		  )
})

fetchParam.value.status = "0"

fetchFunc.value = PurchasePlanApi.getPurchasePlanList

const tbFooterBtnConf = computed(() => {
	const canEdit =
		activeType.value == "1" &&
		selectedTableList.value?.length === 1 &&
		first(selectedTableList.value)?.generatePurchaseStatus ==
			dealStatus.notGenerated
	const canEditBatch =
		activeType.value == "1" &&
		selectedTableList.value?.length > 0 &&
		selectedTableList.value.every(
			(v) => v.generatePurchaseStatus == dealStatus.notGenerated
		)
	return [
		{
			name: "生成采购订单",
			roles: powerList.purchaseOrderPlanBtnOrder,
			icon: ["fas", "file-edit"],
			disabled: !canEdit
		},
		{
			name: "批量生成采购订单",
			roles: powerList.purchaseOrderPlanBtnOrder,
			icon: ["fas", "file-edit"],
			disabled: !canEditBatch
		}
	]
})

const rightTitle = {
	name: ["订货计划"],
	icon: ["fas", "square-share-nodes"]
}
const tabList = ref<{ [propName: string]: any }[]>([
	{ subitemName: "待确认", subitemValue: "0" }, //包括待提交和审批中的
	{ subitemName: "已确认", subitemValue: "1" }, //审批通过的，可进行【生成采购订单】操作
	{ subitemName: "已作废", subitemValue: "2" } //当月最后一天没有及时提交审批，系统自动判定为该月度订货计划“作废”，默认该月度没有订货计划
])

const tabNum = ref([0, 0, 0])
const activeType = ref("0")

const planEditId = ref("")
const planModel = ref(IModalType.create)
const planEditorVisible = ref(false)
const planDetailVisible = ref(false)
const planOrderVisible = ref(false)
const createdOrderVisible = ref(false)

const approvedVisible = ref(false)
const editTask = ref<Record<string, any>>()

/**
 * 查询 回调
 * @param data
 */
function getTableData(data?: { [propName: string]: any }) {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		...data
	}
	refreshAll()
}

const handleTabsClick = (tab: any) => {
	activeType.value = tab.index
	fetchParam.value.status = tabList.value[tab.index].subitemValue
	getTableData()
}

/**
 * 编辑 操作
 * @param row
 */
function handleRowEdit(row: any) {
	planEditId.value = row.id
	planModel.value = IModalType.edit
	planEditorVisible.value = true
}

/**
 * 查看 操作
 * @param row
 */
async function handleRowView(row: any) {
	planEditId.value = row.id
	planModel.value = IModalType.view
	/* planDetailVisible.value = true */

	if (row.bpmStatus == appStatus.pendingApproval) {
		planDetailVisible.value = true
	} else {
		const res = await getTaskByBusinessIds({
			businessIds: [row.id],
			camundaKey: "order_verify"
		})

		if (Array.isArray(res) && res.length > 0) {
			editTask.value = { ...res[res.length - 1] }
			approvedVisible.value = true
		} else {
			planDetailVisible.value = true
		}
	}
}

/**
 * 生成采购订单
 */
function handleCreateOrder(btnName?: string) {
	if (btnName === "生成采购订单") {
		planEditId.value = first(selectedTableList.value)?.id
		planOrderVisible.value = true
		planModel.value = IModalType.edit
	} else if (btnName === "批量生成采购订单") {
		createdOrderVisible.value = true
	}
}

/**
 * 获取 状态统计
 */
const getTypeNumbers = () => {
	return new Promise<void>((resolve) => {
		const _param = JSON.parse(JSON.stringify(fetchParam.value))
		delete _param["currentPage"]
		delete _param["pageSize"]
		delete _param["status"]
		PurchasePlanApi.getPurchasePlanCnt(_param)
			.then((_r) => {
				tabNum.value[0] =
					(_r[appStatus.rejected] ? _r[appStatus.rejected] : 0) +
					(_r[appStatus.pendingApproval] ? _r[appStatus.pendingApproval] : 0)
				tabNum.value[1] = _r[appStatus.underApproval]
					? _r[appStatus.underApproval]
					: 0
				tabNum.value[2] = _r[appStatus.approved] ? _r[appStatus.approved] : 0
			})
			.finally(() => {
				resolve()
			})
	})
}

function refreshAll() {
	getTypeNumbers()
	fetchTableData()
}

onMounted(() => {
	fetchParam.value.sord = "desc"
	fetchParam.value.sidx = "createdDate"
	getDictByCodeList(["INVENTORY_UNIT"])
	listCompanyWithFormat().then((r) => (companyOptions.value = r))

	getTableData()
})

/**
 * 排序
 * @param column
 * @param prop 字段名称
 * @param order ascending 升序 descending 降序
 */
function handleSortChange(column: any, prop: any, order: any) {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		sord:
			order === "descending" ? "desc" : order === "ascending" ? "asc" : "desc",
		sidx: order ? prop : "createdDate" // 排序字段
	}

	fetchTableData()
}
</script>
<template>
	<div class="app-container">
		<div class="whole-frame">
			<ModelFrame class="top-frame">
				<Query
					:queryArrList="queryArrList"
					class="ml10"
					@getQueryData="getTableData"
				/>
			</ModelFrame>
			<ModelFrame class="bottom-frame">
				<Title :title="rightTitle">
					<div class="app-tabs-wrapper">
						<el-tabs @tab-click="handleTabsClick">
							<el-tab-pane
								v-for="(tab, index) in tabList"
								:key="tab.subitemValue"
								:label="`${tab.subitemName}（${tabNum[index]}）`"
								:name="tab.subitemValue"
								:index="tab.subitemValue"
							/>
						</el-tabs>
					</div>
				</Title>
				<PitayaTable
					ref="tableRef"
					:columns="(tbInit.tableProp as any)"
					:tableData="tableData"
					:total="pageTotal"
					:single-select="false"
					:need-selection="activeType == '1'"
					:need-index="true"
					@onSelectionChange="selectedTableList = $event"
					@on-current-page-change="onCurrentPageChange"
					:table-loading="tableLoading"
					@on-table-sort-change="handleSortChange"
				>
					<template #amount="{ rowData }">
						<cost-tag :value="rowData.amount" />
					</template>
					<template #bpmStatus="{ rowData }">
						<dict-tag
							:options="DictApi.getBpmStatus()"
							:value="rowData.bpmStatus"
						/>
					</template>
					<template #generatePurchaseStatus="{ rowData }">
						<dict-tag
							:options="DictApi.getDealStatus()"
							:value="rowData.generatePurchaseStatus"
						/>
					</template>
					<template #operations="{ rowData }">
						<slot
							v-if="
								(isCheckPermission(powerList.purchaseOrderPlanBtnEdit) &&
									activeType == '0' &&
									(rowData.bpmStatus === '0' || rowData.bpmStatus === '3')) ||
								isCheckPermission(powerList.purchaseOrderPlanBtnManagerPreview)
							"
						>
							<el-button
								v-btn
								link
								@click.stop="handleRowEdit(rowData)"
								:disabled="checkPermission(powerList.purchaseOrderPlanBtnEdit)"
								v-if="
									isCheckPermission(powerList.purchaseOrderPlanBtnEdit) &&
									activeType == '0' &&
									(rowData.bpmStatus === '0' || rowData.bpmStatus === '3')
								"
							>
								<font-awesome-icon :icon="['fas', 'pen-to-square']" />
								<span class="table-inner-btn">编辑</span>
							</el-button>
							<el-button
								v-btn
								link
								@click.stop="handleRowView(rowData)"
								:disabled="
									checkPermission(powerList.purchaseOrderPlanBtnManagerPreview)
								"
								v-if="
									isCheckPermission(
										powerList.purchaseOrderPlanBtnManagerPreview
									)
								"
							>
								<font-awesome-icon :icon="['fas', 'eye']" />
								<span class="table-inner-btn">查看</span>
							</el-button>
						</slot>
						<slot v-else>---</slot>
					</template>
					<template #footerOperateLeft>
						<ButtonList
							v-if="activeType == '1'"
							class="btn-list"
							:is-not-radius="true"
							:button="tbFooterBtnConf"
							@on-btn-click="handleCreateOrder"
						/>
					</template>
				</PitayaTable>

				<!-- 新增/修改 弹窗 -->
				<Drawer
					:size="modalSize.xl"
					v-model:drawer="planEditorVisible"
					:destroyOnClose="true"
				>
					<plan-editor
						:id="planEditId"
						:model="planModel"
						@close="planEditorVisible = false"
						@update="refreshAll"
					/>
				</Drawer>

				<!-- 查看 弹窗 -->
				<Drawer
					:size="modalSize.xl"
					v-model:drawer="planDetailVisible"
					:destroyOnClose="true"
				>
					<plan-detail
						:id="planEditId"
						:model="planModel"
						@close="planDetailVisible = false"
					/>
				</Drawer>

				<!-- 生成采购订单 -->
				<Drawer
					:size="modalSize.xl"
					v-model:drawer="planOrderVisible"
					:destroyOnClose="true"
				>
					<plan-detail
						:id="planEditId"
						:model="planModel"
						@close="planOrderVisible = false"
						@update="refreshAll"
					/>
				</Drawer>

				<!-- 批量生成采购订单弹窗 -->
				<Drawer
					:size="modalSize.sm"
					v-model:drawer="createdOrderVisible"
					:destroyOnClose="true"
				>
					<created-order-editor
						:idList="map(selectedTableList || [], (v) => v.id)"
						@close="createdOrderVisible = false"
						@update="refreshAll"
					/>
				</Drawer>

				<!-- 审批、查看弹窗 -->
				<Drawer
					:size="modalSize.xxl"
					v-model:drawer="approvedVisible"
					:destroyOnClose="true"
				>
					<approved-drawer
						:mod="planModel"
						:task-value="editTask"
						@on-save-or-close="approvedVisible = false"
					/>
				</Drawer>
			</ModelFrame>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
