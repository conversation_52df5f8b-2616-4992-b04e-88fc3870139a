<!-- 批量生成采购订单 -->
<script setup lang="ts">
import { ElMessage, FormInstance } from "element-plus"
import { onMounted, ref } from "vue"

import FormElement from "@/app/baseline/views/components/formElement.vue"
import { useMessageBoxInit } from "../../components/messageBox"

import { PurchasePlanApi } from "@/app/baseline/api/purchase/purchasePlan"
import { getIdempotentToken } from "@/app/baseline/utils/validate"
import {
	IIdempotentTokenType,
	IIdempotentTokenTypeAction,
	IIdempotentTokenTypePre
} from "@/app/baseline/utils/types/common"

export interface Props {
	idList: any[]
}
const { showWarnConfirm } = useMessageBoxInit()

const props = defineProps<Props>()

const emits = defineEmits<{
	(e: "update"): void
	(e: "close"): void
}>()
const loading = ref(false)

const formRef = ref<FormInstance>()

/**
 * 按钮 配置
 */
const formBtnListEdit = [
	{ name: "取消", icon: ["fas", "circle-minus"] },
	{ name: "确认", icon: ["fas", "circle-check"] }
]

/**
 * 表单数据源
 */
const formModal = ref<Record<string, any>>({
	latestDeliveryDate: ""
})

/**
 * 左侧title 配置
 */
const drawerLeftTitle = {
	name: ["批量生成采购订单"],
	icon: ["fas", "square-share-nodes"]
}

/**
 *表 单按钮 操作
 * @param btnName
 */
const onFormBtnList = async (btnName: string | undefined) => {
	if (btnName === "取消") {
		emits("close")
	} else if (btnName === "确认") {
		if (!formRef.value) {
			return
		}
		formRef.value?.validate(async (valid) => {
			if (!valid) {
				return
			}
			await showWarnConfirm("请确认是否生成采购订单？")
			loading.value = true
			try {
				const idempotentToken = getIdempotentToken(
					IIdempotentTokenTypePre.batch,
					IIdempotentTokenType.purchasePlan,
					"",
					IIdempotentTokenTypeAction.purchaseOrder
				)

				await PurchasePlanApi.generateOrderBatch(
					props.idList,
					formModal.value.latestDeliveryDate,
					idempotentToken
				)
				ElMessage.success("操作成功")
				emits("update")
				emits("close")
			} finally {
				loading.value = false
			}
		})
	}
}
const formElBase = computed(() => [
	[
		{
			label: "最晚送货日期",
			name: "latestDeliveryDate",
			type: "date"
		}
	]
])

onMounted(async () => {})
</script>
<template>
	<div class="drawer-container">
		<!-- 左侧表单区域 -->
		<div class="drawer-column" style="width: 100%">
			<Title :title="drawerLeftTitle" />
			<el-scrollbar class="rows">
				<el-form
					class="content"
					:model="formModal"
					ref="formRef"
					label-position="top"
					label-width="100px"
				>
					<form-element :form-element="formElBase" :form-data="formModal" />
				</el-form>
			</el-scrollbar>
			<ButtonList
				class="footer"
				:button="formBtnListEdit"
				:loading="loading"
				@on-btn-click="onFormBtnList"
			/>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
