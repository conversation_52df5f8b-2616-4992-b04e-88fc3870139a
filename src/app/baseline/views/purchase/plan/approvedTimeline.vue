<script lang="ts" setup>
import { defineProps, withDefaults } from "vue"
import { Clock, MoreFilled, Timer } from "@element-plus/icons-vue"
import { ApprovedTasks } from "../material/components/define"
import EmptyData from "@/assets/images/empty/empty_data.svg?component"

/*通用变量定义*/
interface props {
	datalist: ApprovedTasks[]
	datalist1: ApprovedTasks[]
	updateTime: string
}

const props = withDefaults(defineProps<props>(), {
	datalist: () => [],
	datalist1: () => [],
	updateTime: ""
})

defineExpose({})
</script>

<template>
	<div class="content approved-timeline fix">
		<el-scrollbar>
			<div
				class="empty_table"
				v-if="props.datalist.length === 0 && props.datalist1.length === 0"
			>
				<EmptyData class="empty_img" />
				<p>未查询到相关数据</p>
			</div>
			<el-timeline class="customer" v-else>
				<el-timeline-item :icon="Clock" class="status">
					<div class="title">{{ props.updateTime }}</div>
					<div>
						<div class="name" v-if="props.datalist.length > 0">
							本月应订货数量：
						</div>
						<template v-for="(step, index) in props.datalist" :key="index">
							<div class="msg_content msg_after">
								<p>物资编码：{{ step.materialCode }}</p>
								<p>物资名称：{{ step.materialLabel }}</p>
								<p>订货数量：{{ step.updateRecord }}</p>
							</div>
						</template>
					</div>
					<div>
						<div class="name" v-if="props.datalist1.length > 0">
							添加提前订货：
						</div>
						<template v-for="(step, index) in props.datalist1" :key="index">
							<div class="msg_content msg_befor">
								<p>物资编码：{{ step.materialCode }}</p>
								<p>物资名称：{{ step.materialLabel }}</p>
								<p>采购数量：{{ step.purchaseNum }}</p>
								<p>采购单位：{{ step.buyUnit }}</p>
								<p>预估采购周期：{{ step.evaluationCycle }}</p>
								<p>可订货量：{{ step.contractSurplus }}</p>
								<p>本月订货量：{{ step.num }}</p>
							</div>
						</template>
					</div>
				</el-timeline-item>
				<el-timeline-item :icon="Clock" class="hidden_view" />
			</el-timeline>
		</el-scrollbar>
	</div>
</template>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.title {
	color: $---color-warn;
	font-size: $---font-size-m;
}

.name {
	color: $---color-error;
	font-size: $---font-size-m;
	margin-bottom: 5px;
}

.content {
	color: $---font-color-2;
	font-size: $---font-size-m;
}

:deep(.el-timeline-item__node) {
	background: $---color-warn;
}

.msg_content {
	position: relative;
	p {
		font-size: $---font-size-m;
		margin-bottom: 2px;
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
	}
}

.msg_content:not(:last-child) {
	padding-bottom: 5px;
	margin-bottom: 5px;
}

.el-timeline.customer {
	:deep(.el-timeline-item__content > *) {
		padding-bottom: 5px !important;
	}
}

.msg_after:not(:last-child)::after {
	content: "";
	display: block;
	position: absolute;
	left: 0;
	bottom: 0;
	height: 1px;
	width: 48px;
	border-bottom: 1px dashed $---font-color-2;
}

.msg_befor:not(:last-child)::after {
	content: "";
	display: block;
	position: absolute;
	left: 0;
	bottom: 0;
	height: 1px;
	width: 60px;
	border-bottom: 1px dashed $---font-color-2;
}
.empty_table {
	line-height: 0rem;
	padding-bottom: 40px;
	text-align: center;
	.empty_img {
		width: 150px;
	}
	p {
		color: #666666;
		font-size: 12px;
		text-align: center;
	}
}
.hidden_view {
	display: none;
}

.status {
	:deep(.el-timeline-item__tail) {
		border-left: 2px dotted#F59B22;
	}
}

.approved-timeline {
	:deep(.el-scrollbar) {
		height: calc(100% - 72px) !important;
	}
}
</style>
