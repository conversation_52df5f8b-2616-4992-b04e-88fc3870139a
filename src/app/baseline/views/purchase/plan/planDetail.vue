<!-- 订货计划 查看/确认生成采购订单  -->
<script lang="ts" setup>
import { defineProps, ref, withDefaults, onMounted, reactive } from "vue"
import { PurchasePlanApi } from "../../../api/purchase/purchasePlan"
import { DictApi, fileBusinessType, dealStatus } from "@/app/baseline/api/dict"
import XEUtils from "xe-utils"
import CostTag from "@/app/baseline/views/components/costTag.vue"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import TableFile from "@/app/baseline/views/components/tableFile.vue"
import { PurchasePlanItemApi } from "@/app/baseline/api/purchase/purchasePlanItem"
import { ElMessage, FormInstance, FormRules } from "element-plus"
import { toNumber } from "lodash-es"
import {
	IIdempotentTokenType,
	IIdempotentTokenTypePre,
	IModalType
} from "@/app/baseline/utils/types/common"
import { batchFormatterNumView, tableColFilter } from "@/app/baseline/utils"
import { useTbInit } from "@/app/baseline/views/components/tableBase"
import { useDictInit } from "@/app/baseline/views/components/dictBase"
import MatDetailPlan from "./matDetailPlan.vue"
import { modalSize } from "@/app/baseline/utils/layout-config"

import needPlanDetail from "../project/needPlanDetail.vue"
import { FormElementType } from "../../components/define"
import FormElement from "@/app/baseline/views/components/formElement.vue"
import ApprovedTimeLine from "@/app/baseline/views/purchase/plan/approvedTimeline.vue"
import { useMessageBoxInit } from "../../components/messageBox"
import { getIdempotentToken } from "@/app/baseline/utils/validate"

const props = withDefaults(
	defineProps<{
		id: string
		model: IModalType
		type?: string
		footerBtnVisible?: boolean
	}>(),
	{
		model: IModalType.edit,
		footerBtnVisible: true
	}
)

const emits = defineEmits<{
	(e: "close"): void
	(e: "update"): void
}>()

const drawerLeftTitle = ref({
	name: ["月度订货计划信息"],
	icon: ["fas", "square-share-nodes"]
})

const drawerRightTitle = {
	name: ["扩展信息"],
	icon: ["fas", "square-share-nodes"]
}

const { showWarnConfirm } = useMessageBoxInit()

const formRef = ref<FormInstance>()
const formModelData = reactive<{ [propName: string]: any }>({
	id: props.id,
	latestDeliveryDate: "" //最晚送货日期
})

const formEl = reactive<FormElementType[][]>([
	[
		{
			label: "最晚送货日期",
			name: "latestDeliveryDate",
			maxlength: 50,
			type: "date"
		}
	]
])
const rules = reactive<FormRules<typeof formModelData>>({})

/**
 * 表单配置
 */
const descOptions = [
	{ label: "年度", key: "year" },
	{ label: "月份", key: "month" },
	{ label: "月度订货计划号", key: "code" },
	{ label: "采购计划号", key: "planPurchaseCode" },
	{ label: "关联采购项目编号", key: "purchaseProjectCode" },
	{ label: "关联采购项目名称", key: "purchaseProjectLabel" },
	{ label: "订货金额", key: "amount" },
	{ label: "供应商名称", key: "supplierLabel" },
	{ label: "月度订货生成时间", key: "generationDate" },
	{ label: "需求部门", key: "sysOrgId_view" },
	{ label: "操作人", key: "lastModifiedBy_view" },
	{ label: "操作时间", key: "lastModifiedDate" }
]

const { dictOptions, getDictByCodeList, dictFilter } = useDictInit()

const drawerLoading = ref(true)
const formBtnLoading = ref(false)

const formBtnList = computed(() => {
	if (props.model == IModalType.view) {
		return [{ name: "取消", icon: ["fas", "circle-minus"] }]
	} else {
		return [
			{ name: "取消", icon: ["fas", "circle-minus"] },
			{ name: "保存", icon: ["fas", "circle-check"] }
		]
	}
})
const formData = ref<{ [propName: string]: any }>({})

const tabList = ref(["订货明细", "相关附件"])

//扩展栏标签页切换
const activeTab = ref("订货明细")
const handleTabChange = (index: number) => {
	activeTab.value = tabList.value[index]

	if (index === 0) {
		fetchParam.value = {
			purchasePlanId: props.id || formData.value.id,
			sord: "desc",
			sidx: "createdDate"
		}
		pageSize.value = 20
		getTableData({})
	}
}

/**
 * 获取 详情
 */
async function getDetail() {
	if (props.id) {
		drawerLoading.value = true
		try {
			const res = await PurchasePlanApi.getPurchasePlan(props.id)
			formData.value = { ...res }
		} finally {
			drawerLoading.value = false
		}
	}
}

onMounted(async () => {
	await getDictByCodeList(["INVENTORY_UNIT", "MATERIAL_NATURE"])
	await getDetail()
	fetchParam.value.purchasePlanId = formData.value?.id
	fetchParam.value.sord = "desc"
	fetchParam.value.sidx = "createdDate"

	getTableData({})

	if (formData.value.generatePurchaseStatus == dealStatus.generated) {
		getListRecord()
		updateTime.value = formData.value.lastModifiedDate
	}
})

/**
 * 排序
 * @param column
 * @param prop 字段名称
 * @param order ascending 升序 descending 降序
 */
function handleSortChange(column: any, prop: any, order: any) {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	const computedProp = () => {
		if (prop === "content_code") {
			return "materialCode"
		} else if (prop === "planNum_view") {
			return "planNum"
		} else {
			return prop
		}
	}

	fetchParam.value = {
		...fetchParam.value,
		sord:
			order === "descending" ? "desc" : order === "ascending" ? "asc" : "desc",
		sidx: order ? computedProp() : "createdDate" // 排序字段
	}

	fetchTableData()
}

const queryArrList = computed(() => [
	{
		name: "物资编码",
		key: "materialCode",
		placeholder: "请输入物资编码",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资名称",
		key: "materialLabel",
		placeholder: "请输入物资名称",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "规格型号",
		key: "version",
		placeholder: "请输入规格型号",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资性质",
		key: "attribute",
		type: "select",
		children: dictOptions.value.MATERIAL_NATURE,
		placeholder: "请选择物资性质"
	}
])

const lastTableData = ref<any[]>([])
const tbInit = useTbInit()
const {
	tableData,
	tableRef,
	tableLoading,
	currentPage,
	pageTotal,
	pageSize,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = tbInit

tbInit.tableProp = computed(() => {
	const defCols: TableColumnType[] = [
		{
			label: "物资编码",
			prop: "content_code",
			width: 130,
			fixed: "left",
			sortable: true
		},
		{ label: "物资名称", prop: "content_label", width: 150 },
		{ label: "规格型号", prop: "content_version", width: 150 },
		{ label: "技术参数", prop: "content_technicalParameter", minWidth: 100 },
		{ label: "物资性质", prop: "attribute", needSlot: true, width: 120 },
		{ label: "采购单位", prop: "content_buyUnit", needSlot: true, width: 90 },
		{
			label: "采购单价",
			prop: "evaluation",
			needSlot: true,
			width: 150,
			align: "right",
			sortable: true
		},
		{
			label: "预估采购周期（月）",
			prop: "content_evaluationCycle",
			width: 140
		},
		{
			label: "需求数量",
			prop: "planNum_view",
			align: "right",
			width: 120,
			sortable: true
		},
		{ label: "采购数量", prop: "purchaseNum_view", align: "right", width: 90 },
		{
			label: "已确认量",
			prop: "orderedNum_view",
			width: 90,
			align: "right"
		},
		{ label: "已关闭数量", prop: "closedNum_view", width: 90, align: "right" },
		{ label: "关闭数量", prop: "closeNum_view", width: 90, align: "right" },
		{ label: "合同余量", prop: "contractSurplus", width: 90, align: "right" },
		{
			label: "本月应订货",
			prop: "shouldNum_view",
			needSlot: true,
			width: 120,
			align: "right"
		},
		{
			label: "可订货量",
			prop: "orderableNum_view",
			width: 100,
			align: "right"
		},
		{
			label: "本月订货数量",
			prop: "num_view",
			width: 120,
			align: "right"
		},
		{
			label: "本月订货金额",
			prop: "cost",
			needSlot: true,
			width: 120,
			align: "right"
		},
		{
			label: "配送仓库名称",
			prop: "storeLabel",
			width: 120
		},
		{
			label: "操作",
			prop: "operations",
			width: 140,
			fixed: "right",
			needSlot: true
		}
	]

	if (formData.value.status == "1") {
		return tableColFilter(defCols, ["已关闭数量"])
	} else if (formData.value.status == "0") {
		return tableColFilter(defCols, ["关闭数量"])
	} else {
		return tableColFilter(defCols, ["已关闭数量", "已关闭"])
	}
})

fetchFunc.value = PurchasePlanItemApi.getPurchasePlanMaterialItem

/**
 * 补丁
 * 监听tableData,重组 lastTableData
 */
watch([tableData], () => {
	XEUtils.map(tableData.value as any, (_d: { [propName: string]: any }) => {
		if (_d.content) {
			Array.from(Object.keys(_d?.content))?.map(
				(_k) => (_d[`content_${_k}`] = _d.content[_k])
			)
		}
	})
	lastTableData.value = tableData.value
	batchFormatterNumView(lastTableData.value, undefined, 0)
})

/**
 * 查询 回调
 * @param data
 */
function getTableData(data: { [propName: string]: any }) {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		...data
	}
	fetchTableData()
}

const getCost = (row: any) => {
	if (row.evaluation && row.num) {
		return (toNumber(row.evaluation) * toNumber(row.num)).toFixed(5)
	}
	return 0
}

/**
 * 月度订货计划 查看
 */
const editRow = ref()
const monthPlanDetailVisible = ref(false)
function handleMonthPlanView(row: any) {
	editRow.value = { ...row }
	monthPlanDetailVisible.value = true
}

const needPlanDetailVisible = ref(false)
/**
 *  查看需求计划
 * @param row
 */
function handleNeedPlanDetail(row?: any) {
	editRow.value = { ...row }
	needPlanDetailVisible.value = true
}

async function onFormBtnList(btnName?: string) {
	if (btnName === "保存") {
		await showWarnConfirm("请确认是否生成采购订单？")

		formBtnLoading.value = true
		try {
			const idempotentToken = getIdempotentToken(
				IIdempotentTokenTypePre.other,
				IIdempotentTokenType.purchasePlan,
				props.id
			)
			await PurchasePlanApi.generateOrder(
				props.id,
				formModelData.latestDeliveryDate,
				idempotentToken
			)
			ElMessage.success("操作成功")
			emits("update")
			emits("close")
		} finally {
			formBtnLoading.value = false
		}
	} else {
		emits("close")
	}
}

const logTitle = ref<any>({
	name: ["修改记录"],
	icon: ["fas", "square-share-nodes"]
})

const logData = ref<any[]>([])
const logData1 = ref<any[]>([])
const updateTime = ref("")

const getListRecord = () => {
	const params = {
		purchasePlanId: formData.value.id
	}
	logData1.value = []
	logData.value = []
	PurchasePlanItemApi.getListUpdateRecord(params).then((res: any) => {
		res.forEach((data: any) => {
			if (data.advanceFlag == "1") {
				logData1.value.push(data)
			} else {
				logData.value.push(data)
			}
		})
	})
}
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<!-- 左侧表单区域 -->
		<div class="drawer-column left">
			<div class="rows">
				<Title :title="drawerLeftTitle" />
				<el-scrollbar>
					<el-descriptions size="small" :column="1" border class="content">
						<el-descriptions-item
							v-for="desc in descOptions"
							:key="desc.label"
							:label="desc.label"
						>
							<!-- 订货金额 -->
							<cost-tag v-if="desc.key === 'amount'" :value="formData.amount" />
							<!-- <span v-else-if="desc.type === 'date'">
								{{ getOnlyDate(formData[desc.key]) || "---" }}
							</span> -->

							<span v-else>
								{{ formData?.[desc.key] || "---" }}
							</span>
						</el-descriptions-item>
					</el-descriptions>

					<el-form
						class="content form-base"
						ref="formRef"
						:model="formModelData"
						:rules="rules"
						label-position="top"
						v-if="props.model == IModalType.edit"
					>
						<FormElement :form-element="formEl" :form-data="formModelData" />
					</el-form>
				</el-scrollbar>
			</div>
		</div>
		<!-- 右侧table区域 -->
		<div
			class="drawer-column right"
			:class="{ pdr10: !props.footerBtnVisible }"
		>
			<div class="rows">
				<Title :title="drawerRightTitle">
					<Tabs class="tabs" :tabs="tabList" @on-tab-change="handleTabChange" />
				</Title>
				<div class="detail-table" v-if="activeTab === '订货明细'">
					<div class="mat-list-table">
						<el-scrollbar class="editor-table-wrapper">
							<Query
								:queryArrList="queryArrList"
								:num-in-row="
									!props.footerBtnVisible &&
									formData.generatePurchaseStatus == dealStatus.generated
										? 2
										: 4
								"
								:query-btn-col-span="
									!props.footerBtnVisible &&
									formData.generatePurchaseStatus == dealStatus.generated
										? 8
										: 4
								"
								@getQueryData="getTableData"
								class="custom-q"
							/>
							<PitayaTable
								ref="tableRef"
								:columns="(tbInit.tableProp as any)"
								:total="pageTotal"
								:table-data="lastTableData"
								:need-index="true"
								:needSelection="false"
								:single-select="false"
								@onSelectionChange="selectedTableList = $event"
								@on-current-page-change="onCurrentPageChange"
								:table-loading="tableLoading"
								@on-table-sort-change="handleSortChange"
							>
								<!-- 物资性质 -->
								<template #attribute="{ rowData }">
									<dict-tag
										:options="DictApi.getMatAttr()"
										:value="rowData.attribute"
									/>
								</template>

								<!-- 采购单位 -->
								<template #content_buyUnit="{ rowData }">
									{{
										dictFilter("INVENTORY_UNIT", rowData.content_buyUnit)
											?.label || "---"
									}}
								</template>

								<!-- 本月应订货 -->
								<template #shouldNum_view="{ rowData }">
									<span>{{ rowData.shouldNum_view }}</span>
									<font-awesome-icon
										:icon="['fas', 'magnifying-glass']"
										style="margin-left: 10px; cursor: pointer"
										@click="handleMonthPlanView(rowData)"
									/>
								</template>

								<!-- 采购单价 -->
								<template #evaluation="{ rowData }">
									<cost-tag :value="rowData.evaluation" />
								</template>

								<!-- 本月订货金额 -->
								<template #cost="{ rowData }">
									<cost-tag :value="getCost(rowData)" />
								</template>

								<template #operations="{ rowData }">
									<el-button v-btn link @click="handleNeedPlanDetail(rowData)">
										<font-awesome-icon :icon="['fas', 'eye']" />
										<span class="table-inner-btn">查看需求计划</span>
									</el-button>
								</template>
							</PitayaTable>
						</el-scrollbar>

						<div
							class="item-log"
							v-if="formData.generatePurchaseStatus == dealStatus.generated"
						>
							<Title :title="logTitle" />
							<ApprovedTimeLine
								:datalist="logData"
								:datalist1="logData1"
								:updateTime="updateTime"
							/>
						</div>
					</div>
				</div>
				<div class="detail-table" v-else-if="activeTab === '相关附件'">
					<TableFile
						ref="refFileTable"
						:mod="props.model"
						:businessId="formData.id"
						:businessType="fileBusinessType.orderPlan"
						:needPage="true"
					/>
				</div>
			</div>
			<ButtonList
				v-if="props.footerBtnVisible"
				class="footer"
				:button="formBtnList"
				:loading="formBtnLoading"
				@on-btn-click="onFormBtnList"
			/>
		</div>

		<!-- 本月应订货 详情 -->
		<Drawer
			size="450"
			v-model:drawer="monthPlanDetailVisible"
			:destroyOnClose="true"
		>
			<mat-detail-plan
				:matInfo="editRow"
				@onClose="monthPlanDetailVisible = false"
			/>
		</Drawer>

		<!-- 需求计划 查看详情 -->
		<Drawer
			:size="modalSize.lg"
			v-model:drawer="needPlanDetailVisible"
			:destroyOnClose="true"
		>
			<need-plan-detail
				:row="editRow"
				:mat-id="editRow.content_id"
				:id="formData.id"
				:purchase-plan-id="formData.planPurchaseId"
				:type="'orderGoods'"
				@close="needPlanDetailVisible = false"
			/>
		</Drawer>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
$--left-width: 300px;

.drawer-column {
	&.left {
		width: $--left-width;
		:deep(.el-descriptions__label) {
			width: 300px;
		}
	}
	&.right {
		width: calc(100% - $--left-width);
		.detail-table {
			height: 100%;
		}
	}
}
:deep(.mat-list-table) {
	height: 100%;
}

.custom-q {
	margin: 10px 0px -10px 10px !important;
}
.tab-mat {
	height: calc(100% - 80px);
}

.mat-list-table {
	display: flex;

	.item-log {
		flex-shrink: 0;
		width: 300px;
		padding-top: $---spacing-m;
	}

	.editor-table-wrapper {
		&:deep(.pitaya-table) {
			.error {
				.column-inner-item,
				.cell,
				.el-input__inner {
					color: red;
				}
			}
		}
	}
}
.item-log {
	border-left: solid 1px $---border-color;
	padding-left: $---spacing-m;
}
</style>
