<script lang="ts" setup>
import { ref, onMounted, reactive } from "vue"
import gridPanel from "@/app/baseline/views/store/components/gridPanel.vue"
import EmptyData from "@/assets/images/empty/empty_data.svg?component"
import { PurchasePlanItemApi } from "@/app/baseline/api/purchase/purchasePlanItem"
import { PurchasePlanMaterialItemApi } from "@/app/baseline/api/purchase/purchasePlanMaterialItem"

export interface Props {
	matInfo?: any
	model?: string
}

const props = withDefaults(defineProps<Props>(), {
	matInfo: {},
	model: "edit"
})

const emits = defineEmits(["onChange", "onClose"])

const drawerRightTitle = reactive({
	name: [""],
	icon: ["fas", "square-share-nodes"]
})
const formBtnList = ref([{ name: "取消", icon: ["fas", "circle-minus"] }])
/* { name: "保存", icon: ["fas", "circle-minus"] } */
const loading = ref(true)
const empty = ref(false)
const needInfo = ref<{ [propName: string]: any }>({})
const PlanData = reactive(new Array(12).fill(0))
const formData = reactive(new Array(12).fill(0))

/**
 * 获取订货计划信息
 * @param id
 */
function getInfoById(id: string) {
	return new Promise<void>((resolve, reject) => {
		PurchasePlanMaterialItemApi.getPurchasePlanItem(id)
			.then((_r) => {
				needInfo.value = _r
				resolve()
			})
			.catch((_e) => reject())
	})
}

/**
 * 获取需求计划配置
 * @param planItemId
 */
function getNeedPlanData(planItemId: any, year: any) {
	return new Promise<void>((resolve, reject) => {
		PurchasePlanItemApi.getByPlanItemIdAndYearList({ planItemId, year })
			.then((_r: any) => {
				_r.map((_d: any) => {
					const _monthNum =
						parseInt(_d.month) -
						1 -
						(needInfo.value?.content?.evaluationCycle || 0)
					if (PlanData[_monthNum] != undefined) {
						PlanData[_monthNum] = _d.num
					}
				})
				resolve()
			})
			.catch((_e) => reject(_e))
	})
}

const maxNumber = ref(0)
//获取已分配额度
function getPlanItemMonthlyList(id: string): Promise<void> {
	return new Promise((resolve, reject) => {
		PurchasePlanItemApi.getPlanItemMonthlyList({ planPurchaseItemId: id })
			.then((_r: any) => {
				_r.map((_d: any) => {
					const _monthNum = parseInt(_d.month) - 1
					if (formData[_monthNum] != undefined) {
						formData[_monthNum] = _d.num
					}
					//需要配置订货数量的月份
					if (_monthNum == needInfo.value.month - 1) {
						formData[_monthNum] = _d.num == 0 ? props.matInfo.shouldNum : _d.num
					}
				})
				maxNumber.value = needInfo.value.orderableNum
				resolve()
			})
			.catch((_e) => reject(_e))
	})
}

function loadData() {
	loading.value = true
	drawerRightTitle.name = [`月度订货数量【${props.matInfo.content_label}】`]
	Promise.all([
		getNeedPlanData(props.matInfo.planItemId, props.matInfo.year),
		getPlanItemMonthlyList(props.matInfo.planPurchaseItemId)
	]).then((_r) => {
		loading.value = false
	})
}

const gridPanelOptions = computed(() => {
	return [
		{
			label: "总需求数量",
			value: needInfo.value.planNum ?? 0
		},
		{
			label: "总采购数量",
			value: needInfo.value.purchaseNum ?? 0
		},
		{
			label: "已确认数量",
			value: needInfo.value.orderedNum ?? 0
		},
		{
			label: "可订货量",
			value: needInfo.value.orderableNum ?? 0
		}
	]
})

onMounted(() => {
	Promise.all([getInfoById(props.matInfo.id)]).then(() => loadData())
})
</script>
<template>
	<div class="mat-detail drawer-container" v-loading="loading && !empty">
		<!-- 右侧table区域 -->
		<div class="drawer-column right" :class="props.model">
			<div class="rows">
				<Title :title="drawerRightTitle" />
				<template v-if="empty">
					<EmptyData class="empty_img" />
					<p>未查询到相关数据</p>
				</template>
				<div v-else class="request-form">
					<grid-panel :options="gridPanelOptions" />

					<div class="request-list">
						<div class="list-title">
							<span>订货月份</span>
							<span>月度需求量</span>
							<span>月度订货量</span>
						</div>
						<el-scrollbar v-if="!loading">
							<div
								class="list-item"
								v-for="(value, index) in formData"
								:key="index"
							>
								<span>{{ (index + 1).toString().padStart(2, "0") }}</span>
								<label>{{ PlanData[index] }}</label>
								<el-input-number
									v-model="formData[index]"
									:max="maxNumber"
									disabled
									:min="0"
								/>
							</div>
						</el-scrollbar>
					</div>
				</div>
			</div>

			<ButtonList
				class="footer"
				:button="formBtnList"
				@on-btn-click="emits('onClose')"
			/>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
$--input-font-size: 20px;
.right {
	width: 450px;
	&.view {
		padding: 0px;
		.request-title {
			display: none;
		}
		:deep(.common-title-wrapper) {
			border: none;
			padding: $---spacing-s 0px $---spacing-m 0px;
		}
		.list-item {
			width: 100% !important;
			padding: 0px $---spacing-m;
		}
		:deep(.el-input__wrapper) {
			background: transparent;
			.el-input__inner {
				color: red !important;
			}
		}
		:deep(.el-input-number__decrease),
		:deep(.el-input-number__increase) {
			display: none;
		}
	}
}
.request-form {
	width: 100%;
	height: calc(100% - 30px);
	span {
		color: $---color-info;
		font-size: 30px;
		font-weight: bold;
	}
	.request-title {
		display: flex;
		padding: $---spacing-m;
		div {
			display: flex;
			flex-direction: column;
			width: 50%;
			align-items: center;
			border-right: solid 1px $---border-color;
			&:last-child {
				border-right: none;
			}
		}
	}
	.request-list {
		margin-top: 5px;
		display: flex;
		flex-direction: column;
		align-items: center;
		height: calc(100% - 120px);

		.list-title {
			span {
				font-size: 12px !important;
				display: inline-block;
				text-align: center;
				width: 160px;
				&:first-child {
					width: 80px;
				}
			}
		}
		.list-item {
			display: flex;
			justify-content: center;
			width: 400px;
			padding-top: $---spacing-m;
			> * {
				height: 50px;
				line-height: 50px;
			}
			span {
				border: solid 1px $---border-color;
				border-radius: $---border-radius-m 0px 0px $---border-radius-m;
				text-align: center;
				width: 80px;
				background: $---color-background;
				&:after {
					content: "月";
					pading-left: $---spacing-s;
					color: $---color-info;
					font-size: $---font-size-m;
				}
			}
			label {
				width: 160px;
				border: solid 1px $---border-color;
				border-radius: 0px $---border-radius-m $---border-radius-m 0px;
				border-left: none;
				font-size: calc($--input-font-size + 10px);
				text-align: center;
				color: $---color-info;
			}
			.el-input-number,
			.el-input {
				width: 160px;
				border: solid 1px $---border-color;
				border-radius: 0px $---border-radius-m $---border-radius-m 0px;
				border-left: none;
				:deep(.el-input-number__decrease) {
					background: transparent !important;
				}
				:deep(.el-input-number__increase) {
					background: transparent !important;
				}
				:deep(.el-input__wrapper) {
					box-shadow: none;
				}
				.el-input {
					width: 200px;
				}
				:deep(.el-input__inner) {
					font-size: $--input-font-size !important;
					text-align: center !important;
				}
			}
		}
	}
}
.empty_img {
	margin: auto;
	width: 150px;
	display: block;
	margin-top: calc(100% - 80px);
	+ p {
		color: $---font-color-2;
		font-size: $---font-size-m;
		text-align: center;
	}
}
</style>
