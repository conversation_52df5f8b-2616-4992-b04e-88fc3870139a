<!-- 添加提前订货物资 V2.0 -->
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<div class="drawer-column" style="width: 100%">
			<Title :title="titleConf" />

			<div class="rows mat-selector-table">
				<Query
					v-if="queryVisible"
					:query-arr-list="(queryConf as any)"
					style="margin: 10px 10px -10px"
					@get-query-data="getQueryData"
				/>
				<el-scrollbar>
					<pitaya-table
						ref="tableRef"
						:columns="tableProp"
						:table-data="lastTableData"
						:need-selection="true"
						:single-select="!multiple"
						:need-index="true"
						:need-pagination="true"
						:total="pageTotal"
						:table-loading="tableLoading"
						:cell-class-name="tbCellClassName"
						@on-selection-change="selectedTableList = $event"
						@on-current-page-change="fetchTableDataWithSetRowsCheck"
					>
						<!-- 物资性质 -->
						<template #attribute="{ rowData }">
							<dict-tag
								:options="DictApi.getMatAttr()"
								:value="rowData.attribute"
							/>
						</template>

						<template #content_buyUnit="{ rowData }">
							{{
								dictFilter("INVENTORY_UNIT", rowData.content_buyUnit)?.label ||
								"---"
							}}
						</template>
						<template #evaluation="{ rowData }">
							<cost-tag :value="rowData.evaluation" />
						</template>

						<template #purchaseNum="{ rowData }">
							{{ toFixedTwo(rowData.purchaseNum, 0) }}
						</template>
					</pitaya-table>
				</el-scrollbar>
			</div>

			<button-list
				class="footer"
				:button="btnConf"
				:loading="btnLoading"
				@on-btn-click="handleBtnClick"
			/>
		</div>
	</div>
</template>

<script setup lang="ts">
import { DictApi } from "@/app/baseline/api/dict"
import { useTbInit } from "../../components/tableBase"
import CostTag from "../../components/costTag.vue"
import DictTag from "@/app/baseline/views/components/dictTag.vue"

import { includes } from "lodash-es"
import { useTableSelectorUtils } from "../../store/hooks/table-selector-utils"
import { useDictInit } from "../../components/dictBase"
import XEUtils from "xe-utils"
import { toFixedTwo } from "@/app/baseline/utils"

const { dictOptions, getDictByCodeList, dictFilter } = useDictInit()

const emit = defineEmits<{
	(e: "close"): void
	(e: "save", v: any[]): void
}>()

const props = withDefaults(
	defineProps<{
		title?: string
		/**
		 * 当前选中的id 集合
		 */
		selectedIds?: any[]
		/**
		 * table 数据源
		 */
		tableApi: (params: any) => Promise<any>

		/**
		 * table 的列配置
		 */
		columns?: TableColumnType[]

		/**
		 * 是否支持多选
		 */
		multiple?: boolean

		/**
		 * table 请求参数
		 */
		tableReqParams?: any

		/**
		 * 筛选是否可见
		 */
		queryVisible?: boolean
		/**
		 * 标红的ID
		 */
		errorIdList?: any[]
		queryArrList?: any
	}>(),
	{
		multiple: false,
		tableReqParams: () => ({}),
		columns: () => [],
		queryVisible: true,
		title: "选择物资"
	}
)

const titleConf = computed(() => ({
	name: [props.title],
	icon: ["fas", "square-share-nodes"]
}))

const queryConf = computed(() => {
	const ls = [
		{
			name: "物资编码",
			key: "materialCode",
			placeholder: "请输入物资编码",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "物资名称",
			key: "materialLabel",
			placeholder: "请输入物资名称",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "规格型号",
			key: "version",
			placeholder: "请输入规格型号",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "物资性质",
			key: "attribute",
			type: "select",
			children: dictOptions.value.MATERIAL_NATURE,
			placeholder: "请选择物资性质"
		}
		/* {
			name: "预估采购周期（月）",
			key: "evaluationCycle",
			placeholder: "请选择预估采购周期",
			type: "select",
			children: initEvaluationCycle()
		} */
	]

	return props.queryArrList || ls
})

/* function initEvaluationCycle() {
	const _rst: { [propName: string]: any }[] = []
	for (let _i = 1; _i <= 12; _i++) {
		_rst.push({
			label: _i,
			value: _i
		})
	}
	return _rst
} */

const drawerLoading = ref(false)

const btnLoading = ref(false)

const lastTableData = ref<any[]>([])

const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	selectedTableList,
	fetchParam,
	currentPage,
	fetchTableData,
	fetchFunc
} = useTbInit()

// eslint-disable-next-line vue/no-setup-props-destructure
fetchFunc.value = props.tableApi
tableProp.value = props.columns?.length
	? props.columns
	: [
			{ label: "物资编码", prop: "content_code", width: 150, fixed: "left" },
			{ label: "物资名称", prop: "content_label", width: 150 },
			{ label: "规格型号", prop: "content_version", width: 150 },
			{ label: "技术参数", prop: "content_technicalParameter", minWidth: 100 },
			{
				label: "物资性质",
				prop: "attribute",
				needSlot: true,
				width: 120
			},
			{ label: "采购单位", prop: "content_buyUnit", needSlot: true, width: 90 },
			{
				label: "采购单价",
				prop: "evaluation",
				needSlot: true,
				width: 150
			},
			{
				label: "采购数量",
				prop: "purchaseNum",
				needSlot: true,
				align: "right"
			},
			{
				label: "预估采购周期（月）",
				prop: "content_evaluationCycle",
				width: 140
			},
			{ label: "质保期", prop: "warrantyPeriod_view", width: 120 }
	  ]

const { fetchTableDataWithSetRowsCheck } = useTableSelectorUtils({
	tableData,
	tableRef,
	selectedIds: props.selectedIds,
	fetchTableData
})

const btnConf = computed(() => {
	return [
		{
			name: "取消",
			icon: ["fas", "circle-minus"]
		},
		{
			name: "保存",
			icon: ["fas", "floppy-disk"],
			disabled: selectedTableList.value.length > 0 ? false : true
		}
	]
})

watch([tableData], () => {
	XEUtils.map(tableData.value as any, (_d: { [propName: string]: any }) => {
		if (_d.content) {
			Array.from(Object.keys(_d?.content))?.map(
				(_k) => (_d[`content_${_k}`] = _d.content[_k])
			)
		}
	})
	lastTableData.value = tableData.value
})

function tbCellClassName(data: {
	row: any
	column: any
	rowIndex: number
	columnIndex: number
}): string {
	return includes(props.errorIdList ?? [], data.row.id) ? "error" : ""
}

onMounted(() => {
	fetchParam.value = {
		...fetchParam.value,
		...props.tableReqParams,
		sord: "desc",
		sidx: "createdDate"
	}
	getDictByCodeList(["INVENTORY_UNIT", "MATERIAL_NATURE"])
	fetchTableDataWithSetRowsCheck()
})

function getQueryData(e: any) {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = { ...fetchParam.value, ...e }
	fetchTableDataWithSetRowsCheck()
}

async function handleBtnClick(name?: string) {
	if (name === "取消") {
		return emit("close")
	}

	btnLoading.value = true
	try {
		emit("save", selectedTableList.value)
	} finally {
		btnLoading.value = false
	}
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.mat-selector-table {
	&:deep(.pitaya-table) {
		.error {
			.column-inner-item,
			.cell,
			.el-input__inner {
				color: red;
			}
		}
	}
}
</style>
