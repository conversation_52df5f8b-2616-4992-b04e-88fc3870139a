<!-- 订货 编辑  -->
<script lang="ts" setup>
import { defineProps, ref, withDefaults, onMounted, reactive } from "vue"
import { PurchasePlanApi } from "../../../api/purchase/purchasePlan"
import { DictApi, fileBusinessType } from "@/app/baseline/api/dict"
import XEUtils from "xe-utils"
import CostTag from "@/app/baseline/views/components/costTag.vue"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import TableFile from "@/app/baseline/views/components/tableFile.vue"
import { PurchasePlanItemApi } from "@/app/baseline/api/purchase/purchasePlanItem"
import { ElMessage, FormInstance, FormRules } from "element-plus"
import {
	validateAndCorrectInput,
	maxValidateNum,
	maxValidateErrorInfo,
	getIdempotentToken
} from "@/app/baseline/utils/validate"
import { useMessageBoxInit } from "../../components/messageBox"
import {
	debounce,
	findIndex,
	first,
	floor,
	includes,
	map,
	toNumber
} from "lodash-es"
import {
	IIdempotentTokenType,
	IIdempotentTokenTypePre,
	IModalType
} from "@/app/baseline/utils/types/common"
import { batchFormatterNumView, tableColFilter } from "@/app/baseline/utils"
import { useTbInit } from "@/app/baseline/views/components/tableBase"
import { useDictInit } from "@/app/baseline/views/components/dictBase"
import { PurchasePlanMaterialItemApi } from "@/app/baseline/api/purchase/purchasePlanMaterialItem"
import MatDetailPlan from "./matDetailPlan.vue"
import { modalSize } from "@/app/baseline/utils/layout-config"
import orderPlanSelector from "./orderPlanSelector.vue"

import needPlanDetail from "../project/needPlanDetail.vue"
import storeTable from "@/app/baseline/views/store/components/storeTable.vue"
import {
	IWarehouseStatus,
	IWarehouseType
} from "@/app/baseline/utils/types/store-manage"

const props = withDefaults(
	defineProps<{
		id: string
		model: IModalType
		type?: string
	}>(),
	{
		model: IModalType.edit
	}
)

const emits = defineEmits<{
	(e: "close"): void
	(e: "update"): void
}>()

const drawerLeftTitle = ref({
	name: ["月度订货计划信息"],
	icon: ["fas", "square-share-nodes"]
})

const drawerRightTitle = {
	name: ["扩展信息"],
	icon: ["fas", "square-share-nodes"]
}

/**
 * 表单配置
 */
const descOptions = [
	{ label: "年度", key: "year" },
	{ label: "月份", key: "month" },
	{ label: "月度订货计划号", key: "code" },
	{ label: "采购计划号", key: "planPurchaseCode" },
	{ label: "关联采购项目编号", key: "purchaseProjectCode" },
	{ label: "关联采购项目名称", key: "purchaseProjectLabel" },
	{ label: "订货金额", key: "amount" },
	{ label: "供应商名称", key: "supplierLabel" },
	{ label: "月度订货生成时间", key: "generationDate" },
	{ label: "需求部门", key: "sysOrgId_view" },
	{ label: "操作人", key: "lastModifiedBy_view" },
	{ label: "操作时间", key: "lastModifiedDate" }
]

/**
 * 物资明细 table 行样式
 *
 * 库存物资异常，用红色标记该行
 */
const errorGoodsIdList = ref<number[]>([])
function tbCellClassName({ row }: any) {
	return includes(errorGoodsIdList.value, row.id) ? "error" : ""
}

const { showWarnConfirm, showDelConfirm } = useMessageBoxInit()
const { dictOptions, getDictByCodeList, dictFilter } = useDictInit()

const drawerLoading = ref(true)
const formBtnLoading = ref(false)

const formBtnList = computed(() => [
	{ name: "提交审核", icon: ["fas", "circle-check"] }
])
const formData = ref<{ [propName: string]: any }>({})

const tabList = ref(["订货明细", "相关附件"])

//扩展栏标签页切换
const activeTab = ref("订货明细")
const handleTabChange = (index: number) => {
	activeTab.value = tabList.value[index]

	if (index === 0) {
		fetchParam.value = {
			purchasePlanId: props.id || formData.value.id,
			sord: "desc",
			sidx: "createdDate"
		}
		pageSize.value = 20
		getTableData({})
	}
}

/**
 * 获取 详情
 */
async function getDetail() {
	if (props.id) {
		drawerLoading.value = true
		try {
			const res = await PurchasePlanApi.getPurchasePlan(props.id)
			formData.value = { ...res }
		} finally {
			drawerLoading.value = false
		}
	}
}

async function onFormBtnList(keyName?: string) {
	//btnLoading.value = true
	if (keyName == "提交审核") {
		await showWarnConfirm("请确认是否提交本次数据？")

		formBtnLoading.value = true
		drawerLoading.value = true
		try {
			const { code, msg, data } =
				await PurchasePlanItemApi.checkPublishPurchasePlan(props.id)

			if (data && code != 200) {
				errorGoodsIdList.value = data || []
				//matTableRef.value?.getTableData() todo
				await showWarnConfirm("存在订货数量为0的物资，请确认是否提交？")
			}

			const idempotentToken = getIdempotentToken(
				IIdempotentTokenTypePre.other,
				IIdempotentTokenType.purchasePlan,
				formData.value.id
			)
			await PurchasePlanItemApi.publishPurchasePlanItem(
				props.id,
				idempotentToken
			)
			ElMessage.success("操作成功")
			emits("update")
			emits("close")
		} finally {
			formBtnLoading.value = false
			drawerLoading.value = false
		}
	} else if (keyName == "保存") {
		/* try {
			btnLoading.value = true
			await PurchasePlanApi.generateOrder(
				props.id,
				formModelData.latestDeliveryDate
			)
			ElMessage.success("操作成功")
			emits("onSuccess")
		} finally {
			btnLoading.value = false
		} */
	} else {
		emits("close")
	}
}

onMounted(async () => {
	await getDictByCodeList(["INVENTORY_UNIT", "MATERIAL_NATURE"])
	await getDetail()
	fetchParam.value.purchasePlanId = formData.value?.id
	fetchParam.value.sord = "desc"
	fetchParam.value.sidx = "createdDate"
	getTableData({})
})

/**
 * 排序
 * @param column
 * @param prop 字段名称
 * @param order ascending 升序 descending 降序
 */
function handleSortChange(column: any, prop: any, order: any) {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	const computedProp = () => {
		if (prop === "content_code") {
			return "materialCode"
		} else if (prop === "planNum_view") {
			return "planNum"
		} else {
			return prop
		}
	}

	fetchParam.value = {
		...fetchParam.value,
		sord:
			order === "descending" ? "desc" : order === "ascending" ? "asc" : "desc",
		sidx: order ? computedProp() : "createdDate" // 排序字段
	}

	fetchTableData()
}

const queryArrList = computed(() => [
	{
		name: "物资编码",
		key: "materialCode",
		placeholder: "请输入物资编码",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资名称",
		key: "materialLabel",
		placeholder: "请输入物资名称",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "规格型号",
		key: "version",
		placeholder: "请输入规格型号",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资性质",
		key: "attribute",
		type: "select",
		children: dictOptions.value.MATERIAL_NATURE,
		placeholder: "请选择物资性质"
	}
])

const lastTableData = ref<any[]>([])
const tbInit = useTbInit()
const {
	tableCache,
	tableData,
	tableRef,
	tableLoading,
	currentPage,
	pageTotal,
	pageSize,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = tbInit

tbInit.tableProp = computed(() => {
	const defCols: TableColumnType[] = [
		{
			label: "物资编码",
			prop: "content_code",
			width: 130,
			fixed: "left",
			sortable: true
		},
		{ label: "物资名称", prop: "content_label", width: 150 },
		{ label: "规格型号", prop: "content_version", width: 150 },
		{ label: "技术参数", prop: "content_technicalParameter", minWidth: 100 },
		{ label: "物资性质", prop: "attribute", needSlot: true, width: 120 },
		{ label: "采购单位", prop: "content_buyUnit", needSlot: true, width: 90 },
		{
			label: "采购单价",
			prop: "evaluation",
			needSlot: true,
			width: 150,
			align: "right",
			sortable: true
		},
		{
			label: "预估采购周期（月）",
			prop: "content_evaluationCycle",
			width: 140
		},
		{
			label: "需求数量",
			prop: "planNum_view",
			align: "right",
			width: 120,
			sortable: true
		},
		{ label: "采购数量", prop: "purchaseNum_view", align: "right", width: 90 },
		{
			label: "已确认量",
			prop: "orderedNum_view",
			width: 90,
			align: "right"
		},
		{ label: "已关闭数量", prop: "closedNum_view", width: 90, align: "right" },
		{ label: "关闭数量", prop: "closeNum_view", width: 90, align: "right" },
		{ label: "合同余量", prop: "contractSurplus", width: 90, align: "right" },
		{
			label: "本月应订货",
			prop: "shouldNum_view",
			needSlot: true,
			width: 120,
			align: "right"
		},
		{
			label: "可订货量",
			prop: "orderableNum_view",
			width: 100,
			align: "right",
			fixed: "right"
		},
		{
			label: "本月订货数量",
			prop: "num",
			needSlot: true,
			width: 120,
			fixed: "right",
			align: "right"
		},
		{
			label: "本月订货金额",
			prop: "cost",
			needSlot: true,
			width: 120,
			align: "right",
			fixed: "right"
		},
		{
			label: "配送仓库名称",
			prop: "storeLabel",
			width: 120,
			fixed: "right"
		},
		{
			label: "操作",
			prop: "operations",
			width: 180,
			fixed: "right",
			needSlot: true
		}
	]

	if (formData.value.status == "1") {
		return tableColFilter(defCols, ["已关闭数量"])
	} else if (formData.value.status == "0") {
		return tableColFilter(defCols, ["关闭数量"])
	} else {
		return tableColFilter(defCols, ["已关闭数量", "已关闭"])
	}
})

fetchFunc.value = PurchasePlanItemApi.getPurchasePlanMaterialItem

/**
 * 补丁
 * 监听tableData,重组 lastTableData
 */
watch([tableData], () => {
	XEUtils.map(tableData.value as any, (_d: { [propName: string]: any }) => {
		if (_d.content) {
			Array.from(Object.keys(_d?.content))?.map(
				(_k) => (_d[`content_${_k}`] = _d.content[_k])
			)
		}
	})
	lastTableData.value = tableData.value
	batchFormatterNumView(lastTableData.value, undefined, 0)
})

/* function fetchItemList(param?: any) {
	return new Promise((resolve) => {
		PurchasePlanItemApi.getPurchasePlanMaterialItem({
			purchasePlanId: formData.value?.id,
			...param
		}).then((_r) => {
			XEUtils.map(_r.rows, (_d: { [propName: string]: any }) => {
				if (_d.content)
					Array.from(Object.keys(_d?.content))?.map(
						(_k) => (_d[`content_${_k}`] = _d.content[_k])
					)
			})

			resolve(_r)
		})
	})
} */

const tbBtnsConf = computed(() => {
	return [
		{
			name: "添加提前订货计划",
			icon: ["fas", "circle-plus"]
		},
		{
			name: "指定配送仓库",
			icon: ["fas", "home"],
			disabled: selectedTableList.value.length > 0 ? false : true
		}
	]
})

/**
 * 本月订货数量
 * @param e
 */
function validateNum(e: any) {
	const num = toNumber(e.num)
	const max = toNumber(e.orderableNum)
	const evaluation = toNumber(e.evaluation)

	const oldRow = tableCache.find((v) => v.id == e.id)

	e.num = num
	if (num < 0) {
		e.num = oldRow.num
		return ElMessage.warning("本月订货数量不能小于0！")
	} else {
		const price = toNumber(num * evaluation)

		if (price > maxValidateNum) {
			e.num = oldRow.num
			ElMessage.warning(
				`您的订货金额已超过${maxValidateErrorInfo}元，请重新核对金额！`
			)
			return false
		}

		if (num > max) {
			ElMessage.warning("本月订货数量不能大于可订货数量！")
			e.num = floor(max, 0)
		}
	}

	updateNum(e)
}

/**
 * 更新本月订货数量 api
 */
const updateNum = debounce(async (e: any) => {
	const res = await PurchasePlanMaterialItemApi.updatePurchasePlanMaterialItem({
		id: e.id,
		num: e.num
	})

	// 失焦保存 更新预估采购金额
	e.purchaseAmount = res.purchaseAmount

	//  更新 tableCache
	const rowIdx = findIndex(tableCache, (v) => v.id === e.id)
	if (rowIdx !== -1) {
		tableCache.splice(rowIdx, 1, { ...e })
	}

	// 过滤标红的 id
	const errorRowIdx = findIndex(errorGoodsIdList.value, (v) => v === e.id)
	if (errorRowIdx !== -1) {
		errorGoodsIdList.value.splice(errorRowIdx, 1)
	}
	ElMessage.success("操作成功")
	getDetail()
	emits("update")
}, 300)

/**
 * 查询 回调
 * @param data
 */
function getTableData(data: { [propName: string]: any }) {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		...data
	}
	fetchTableData()
}

const getCost = (row: any) => {
	if (row.evaluation && row.num) {
		return (toNumber(row.evaluation) * toNumber(row.num)).toFixed(5)
	}
	return 0
}

/**
 * 月度订货计划 查看
 */
const editRow = ref()
const monthPlanDetailVisible = ref(false)
function handleMonthPlanView(row: any) {
	editRow.value = { ...row }
	monthPlanDetailVisible.value = true
}

const needPlanDetailVisible = ref(false)
/**
 *  查看需求计划
 * @param row
 */
function handleNeedPlanDetail(row?: any) {
	editRow.value = { ...row }
	needPlanDetailVisible.value = true
}

/**
 * 移除
 * @param row
 */
async function handleDelRow(row?: any) {
	await showDelConfirm()
	await PurchasePlanMaterialItemApi.deletePurchasePlanMaterialItem({
		ids: [row?.id]
	})
	ElMessage.success("操作成功")
	getTableData({})
}

const orderGoodsSelectorVisible = ref(false)
const storeSelectorVisible = ref(false)
/**
 * table 按钮操作
 * @param btnName
 */
function handleTableFooterAction(btnName?: string) {
	if (btnName == "添加提前订货计划") {
		orderGoodsSelectorVisible.value = true
	} else if (btnName == "指定配送仓库") {
		storeSelectorVisible.value = true
	}
}

async function handleSaveOrderGoods(rows: any[]) {
	const params = rows.map((v) => {
		return {
			materialId: v.content.id, //物资ID
			planNum: v.planNum, //需求数量
			purchaseNum: v.purchaseNum, //采购数量
			evaluation: v.evaluation, //采购单价
			purchasePlanId: formData.value.id, //订货计划ID
			planPurchaseItemId: v.planPurchaseItemId, //采购计划物资ID
			advanceFlag: "1", //提前订货标记
			warrantyPeriod: v.warrantyPeriod,
			planItemId: v.planItemId,
			materialCode: v.content_code,
			materialLabel: v.content_label,
			version: v.content_version,
			attribute: v.attribute
		}
	})

	const idempotentToken = getIdempotentToken(
		IIdempotentTokenTypePre.other,
		IIdempotentTokenType.purchasePlan,
		formData.value.id
	)
	await PurchasePlanMaterialItemApi.addBatchPurchasePlanMaterialItem(
		params,
		idempotentToken
	)

	ElMessage.success("操作成功")
	getTableData({})
	getDetail()
	emits("update")
	orderGoodsSelectorVisible.value = false
}

/**
 * 指定配送仓库 回调
 * @param e
 */

async function handleStoreSave(btnName: string, e: any) {
	if (btnName == "保存") {
		const matIds = map(selectedTableList.value, (v) => v.id).toString()

		formBtnLoading.value = true

		try {
			await PurchasePlanItemApi.updateStore(matIds, e.id)
			fetchTableData()
			ElMessage.success("操作成功")
			storeSelectorVisible.value = false
		} finally {
			formBtnLoading.value = false
		}
	} else {
		storeSelectorVisible.value = false
	}
}
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<!-- 左侧表单区域 -->
		<div class="drawer-column left">
			<div class="rows">
				<Title :title="drawerLeftTitle" />
				<el-scrollbar>
					<el-descriptions size="small" :column="1" border class="content">
						<el-descriptions-item
							v-for="desc in descOptions"
							:key="desc.label"
							:label="desc.label"
						>
							<!-- 订货金额 -->
							<cost-tag v-if="desc.key === 'amount'" :value="formData.amount" />

							<span v-else>
								{{ formData?.[desc.key] || "---" }}
							</span>
						</el-descriptions-item>
					</el-descriptions>
				</el-scrollbar>
			</div>
		</div>
		<!-- 右侧table区域 -->
		<div class="drawer-column right">
			<div class="rows">
				<Title :title="drawerRightTitle">
					<Tabs class="tabs" :tabs="tabList" @on-tab-change="handleTabChange" />
				</Title>
				<div class="detail-table" v-if="activeTab === '订货明细'">
					<Query
						:queryArrList="queryArrList"
						@getQueryData="getTableData"
						class="custom-q"
					/>
					<div class="mat-list-table">
						<el-scrollbar class="editor-table-wrapper">
							<PitayaTable
								ref="tableRef"
								:columns="(tbInit.tableProp as any)"
								:total="pageTotal"
								:table-data="lastTableData"
								:need-index="true"
								:needSelection="true"
								:single-select="false"
								@onSelectionChange="selectedTableList = $event"
								@on-current-page-change="onCurrentPageChange"
								:table-loading="tableLoading"
								:cell-class-name="tbCellClassName"
								@on-table-sort-change="handleSortChange"
							>
								<!-- 物资性质 -->
								<template #attribute="{ rowData }">
									<dict-tag
										:options="DictApi.getMatAttr()"
										:value="rowData.attribute"
									/>
								</template>

								<!-- 采购单位 -->
								<template #content_buyUnit="{ rowData }">
									{{
										dictFilter("INVENTORY_UNIT", rowData.content_buyUnit)
											?.label || "---"
									}}
								</template>

								<!-- 本月应订货 -->
								<template #shouldNum_view="{ rowData }">
									<span>{{ rowData.shouldNum_view }}</span>
									<font-awesome-icon
										:icon="['fas', 'magnifying-glass']"
										style="margin-left: 10px; cursor: pointer"
										@click="handleMonthPlanView(rowData)"
									/>
								</template>

								<!-- 采购单价 -->
								<template #evaluation="{ rowData }">
									<cost-tag :value="rowData.evaluation" />
								</template>

								<!-- 本月订货金额 -->
								<template #cost="{ rowData }">
									<cost-tag :value="getCost(rowData)" />
								</template>

								<!-- 本月订货数量 -->
								<template #num="{ rowData }">
									<el-input
										class="no-arrows"
										v-model="rowData.num"
										@click.stop
										@input="rowData.num = validateAndCorrectInput($event, 0)"
										@change="validateNum(rowData)"
									/>
								</template>
								<template #operations="{ rowData }">
									<el-button v-btn link @click.stop="handleDelRow(rowData)">
										<font-awesome-icon :icon="['fas', 'trash-can']" />
										<span class="table-inner-btn">移除</span>
									</el-button>

									<el-button
										v-btn
										link
										@click.stop="handleNeedPlanDetail(rowData)"
									>
										<font-awesome-icon :icon="['fas', 'eye']" />
										<span class="table-inner-btn">查看需求计划</span>
									</el-button>
								</template>
								<template #footerOperateLeft>
									<ButtonList
										class="btn-list"
										:is-not-radius="true"
										:button="tbBtnsConf"
										:loading="formBtnLoading"
										@on-btn-click="handleTableFooterAction"
									/>
								</template>
							</PitayaTable>
						</el-scrollbar>
					</div>
				</div>
				<div class="detail-table" v-else-if="activeTab === '相关附件'">
					<TableFile
						ref="refFileTable"
						:mod="props.model"
						:businessId="formData.id"
						:businessType="fileBusinessType.orderPlan"
						:needPage="true"
					/>
				</div>
			</div>
			<ButtonList
				class="footer"
				:button="formBtnList"
				:loading="formBtnLoading"
				@on-btn-click="onFormBtnList"
			/>
		</div>

		<!-- 本月应订货 详情 -->
		<Drawer
			size="450"
			v-model:drawer="monthPlanDetailVisible"
			:destroyOnClose="true"
		>
			<mat-detail-plan
				:matInfo="editRow"
				@onClose="monthPlanDetailVisible = false"
			/>
		</Drawer>

		<!-- 需求计划 查看详情 -->
		<Drawer
			:size="modalSize.lg"
			v-model:drawer="needPlanDetailVisible"
			:destroyOnClose="true"
		>
			<need-plan-detail
				:row="editRow"
				:mat-id="editRow.content_id"
				:id="formData.id"
				:purchase-plan-id="formData.planPurchaseId"
				:type="'orderGoods'"
				@close="needPlanDetailVisible = false"
			/>
		</Drawer>

		<!--	添加提前订货物资-->
		<Drawer
			:size="modalSize.lg"
			v-model:drawer="orderGoodsSelectorVisible"
			:destroyOnClose="true"
		>
			<order-plan-selector
				:multiple="true"
				:table-req-params="{ purchasePlanId: formData.id }"
				:table-api="PurchasePlanItemApi.listAdvanceOrderItem"
				@close="orderGoodsSelectorVisible = false"
				@save="handleSaveOrderGoods"
			/>
		</Drawer>

		<!--指定配送仓库 -->
		<Drawer
			:size="1400"
			v-model:drawer="storeSelectorVisible"
			:destroyOnClose="true"
		>
			<store-table
				:selected-ids="
					selectedTableList.length > 1
						? []
						: [first(selectedTableList)?.storeId]
				"
				@on-save="handleStoreSave"
				:table-api-params="{
					status: `${IWarehouseStatus.activated},${IWarehouseStatus.inventoryInProgress}`,
					type: `${IWarehouseType.default},${IWarehouseType.dangerousWarehouse}`
				}"
			/>
		</Drawer>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
$--left-width: 300px;
.drawer-column {
	&.left {
		width: $--left-width;
		:deep(.el-descriptions__label) {
			width: 300px;
		}
	}
	&.right {
		width: calc(100% - $--left-width);
		.detail-table {
			height: 100%;
		}
	}
}
:deep(.mat-list-table) {
	height: 100%;
}

.custom-q {
	margin: 10px 0px -10px 10px !important;
}
.tab-mat {
	height: calc(100% - 80px);
}

.mat-list-table {
	display: flex;

	.editor-table-wrapper {
		&:deep(.pitaya-table) {
			.error {
				.column-inner-item,
				.cell,
				.el-input__inner {
					color: red;
				}
			}
		}
	}
}
</style>
