<!-- 新建/编辑  V2.0 -->
<script lang="ts" setup>
import { ref, onMounted, reactive } from "vue"
import { ElMessage, type FormInstance, type FormRules } from "element-plus"
import { FormElementType } from "../../components/define"
import FormElement from "../../components/formElement.vue"
import { PurchaseSupplierApi } from "../../../api/purchase/purchaseSupplier"
import {
	getIdempotentToken,
	validateEmail,
	validatePhone
} from "../../../utils/validate"
import {
	IIdempotentTokenType,
	IIdempotentTokenTypePre,
	IModalType
} from "@/app/baseline/utils/types/common"
import { getModalTypeLabel } from "@/app/baseline/utils"
import { useMessageBoxInit } from "../../components/messageBox"

const props = defineProps<{
	/**
	 * 业务id
	 */
	id: any

	/**
	 * 编辑模式：编辑/新建
	 */
	model: IModalType
}>()

const emit = defineEmits<{
	(e: "close"): void
	(e: "update"): void
}>()

const { showWarnConfirm } = useMessageBoxInit()

const drawerLoading = ref(false)
const formBtnLoading = ref(false)
const formRef = ref<FormInstance>()

const formData = ref<{ [propName: string]: any }>({})
/**
 * 保存旧的表单数据
 */
const oldFormData = ref<string>()

/**
 * 能否编辑扩展信息
 */
const canEditExtra = computed(() => Boolean(formData.value?.id || props.id))

const drawerLeftTitle = computed(() => ({
	name: [
		getModalTypeLabel(
			canEditExtra.value ? IModalType.edit : IModalType.create,
			"供应商"
		)
	],
	icon: ["fas", "square-share-nodes"]
}))

const formBtnList = computed(() => [
	{
		name: "保存草稿",
		icon: ["fas", "floppy-disk"]
	},
	{
		name: "提交审核",
		icon: ["fas", "circle-check"],
		disabled: !canEditExtra.value
	}
])

const formEl = reactive<FormElementType[][]>([
	[
		{
			label: "供应商编码",
			name: "code",
			maxlength: 50,
			disabled: false
		}
	],
	[
		{
			label: "供应商名称",
			name: "label",
			maxlength: 50,
			disabled: false
		}
	],
	[
		{
			label: "法定代表人",
			name: "legalRepresentative",
			maxlength: 50,
			disabled: false
		}
	],
	[{ label: "联系人", name: "contact", maxlength: 50 }],
	[{ label: "联系人手机号", name: "contactPhoneNumber", maxlength: 11 }],
	[
		{
			label: "联系地址",
			name: "contactAddress",
			maxlength: 200
		}
	],
	[
		{
			label: "电子邮箱",
			name: "contactEmail",
			disabled: false,
			maxlength: 50,
			clear: false
		}
	],
	[
		{
			label: "备注说明",
			name: "remark",
			maxlength: 200,
			rows: "5",
			type: "textarea"
		}
	]
])
const rules = reactive<FormRules<typeof formData.value>>({
	code: [{ required: true, message: "供应商编码不能为空", trigger: "change" }],
	label: [{ required: true, message: "供应商名称不能为空", trigger: "change" }],
	legalRepresentative: [
		{ required: true, message: "法定代表人不能为空", trigger: "change" }
	],
	contact: [{ required: true, message: "联系人不能为空", trigger: "change" }],
	contactPhoneNumber: [
		{ required: true, message: "联系人手机号不能为空", trigger: "change" },
		{ validator: validatePhone, required: true, trigger: "change" }
	],
	contactEmail: [
		{ required: true, message: "电子邮箱不能为空", trigger: "change" },
		{ validator: validateEmail, required: true, trigger: "change" }
	]
})

const onFormBtnList = (btnName: string | undefined) => {
	if (btnName === "保存草稿") {
		handleSaveDraft()
	} else if (btnName === "提交审核") {
		handleSubmit()
	}
}

/**
 * 处理保存草稿（包含新增和编辑）
 */
async function handleSaveDraft() {
	if (!formRef.value) {
		return
	}

	formRef.value.validate(async (valid: any) => {
		if (!valid) {
			return
		}
		formBtnLoading.value = true
		// 表单校验通过
		try {
			const api = canEditExtra.value
				? PurchaseSupplierApi.updatePurchaseSupplier
				: PurchaseSupplierApi.addPurchaseSupplier

			let idempotentToken = ""
			if (!canEditExtra.value) {
				idempotentToken = getIdempotentToken(
					IIdempotentTokenTypePre.apply,
					IIdempotentTokenType.purchaseSupplier
				)
			}
			const r = await api(formData.value as any, idempotentToken)

			formData.value.id = r.id

			oldFormData.value = JSON.stringify(formData.value)

			ElMessage.success("操作成功")
			emit("update")
		} finally {
			formBtnLoading.value = false
		}
	})
}

/**
 * 处理提交审核
 *
 * 需要表单校验
 */
function handleSubmit() {
	if (!formRef.value) {
		return
	}

	formRef.value.validate(async (valid: any) => {
		if (!valid) {
			return
		}

		await showWarnConfirm("请确认是否提交本次数据？")
		formBtnLoading.value = true
		drawerLoading.value = true

		try {
			// 如果主表有修改，则先更新主表数据
			if (oldFormData.value != JSON.stringify(formData.value)) {
				await PurchaseSupplierApi.updatePurchaseSupplier({
					...formData.value
				} as any)
			}

			const idempotentToken = getIdempotentToken(
				IIdempotentTokenTypePre.other,
				IIdempotentTokenType.purchaseSupplier,
				formData.value.id
			)

			await PurchaseSupplierApi.publishSupplier(
				formData.value.id,
				idempotentToken
			)

			ElMessage.success("操作成功")
			emit("update")
			emit("close")
		} finally {
			formBtnLoading.value = false
			drawerLoading.value = false
		}
	})
}

/**
 * 获取详情
 */
async function getDetail() {
	if (canEditExtra.value) {
		const res = await PurchaseSupplierApi.getPurchaseSupplier(props.id)

		formData.value = { ...res }

		oldFormData.value = JSON.stringify(formData.value)
	}
}

onMounted(async () => {
	getDetail()
})
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<div class="drawer-column">
			<div class="rows">
				<Title :title="drawerLeftTitle" />
				<el-scrollbar>
					<el-form
						class="content form-base"
						ref="formRef"
						:model="formData"
						:rules="rules"
						label-position="top"
						v-if="props.model != 'view'"
					>
						<FormElement :form-element="formEl" :form-data="formData" />
					</el-form>
				</el-scrollbar>
			</div>
			<ButtonList
				class="footer"
				:button="formBtnList"
				@onBtnClick="onFormBtnList"
				:loading="formBtnLoading"
			/>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.drawer-column {
	width: 100%;
}
.el-scrollbar {
	height: calc(100% - 30px);
}
</style>
