<!-- 查看  V2.0 -->
<script lang="ts" setup>
import { ref, onMounted } from "vue"
import { PurchaseSupplierApi } from "../../../api/purchase/purchaseSupplier"
import { IModalType } from "@/app/baseline/utils/types/common"
import { getModalTypeLabel } from "@/app/baseline/utils"
import { getRealLength, setString } from "@/app/baseline/utils/validate"

interface Props {
	id?: any
	model?: IModalType
	footerBtnVisible?: boolean
}

const props = withDefaults(defineProps<Props>(), {
	model: IModalType.view,
	footerBtnVisible: true
})

const emit = defineEmits<{
	(e: "close"): void
}>()

const drawerLoading = ref(false)

const formData = ref<{ [propName: string]: any }>({})

/**
 * 能否编辑扩展信息
 */
const canEditExtra = computed(() => Boolean(formData.value?.id || props.id))

const drawerLeftTitle = computed(() => ({
	name: [getModalTypeLabel(props.model, "供应商")],
	icon: ["fas", "square-share-nodes"]
}))

const formBtnList = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	}
]

const descOptions = [
	{ label: "供应商编码", name: "code" },
	{ label: "供应商名称", name: "label" },
	{ label: "法定代表人", name: "legalRepresentative" },
	{ label: "联系人", name: "contact" },
	{ label: "联系人手机号", name: "contactPhoneNumber" },
	{ label: "联系地址", name: "contactAddress" },
	{ label: "电子邮箱", name: "contactEmail" },
	{ label: "备注说明", name: "remark", needTooltip: true }
]

/**
 * 获取详情
 */
async function getDetail() {
	if (canEditExtra.value) {
		const res = await PurchaseSupplierApi.getPurchaseSupplier(props.id)

		formData.value = { ...res }
	}
}

onMounted(async () => {
	getDetail()
})
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<div class="drawer-column" :class="{ pdr10: !footerBtnVisible }">
			<div class="rows">
				<Title :title="drawerLeftTitle" />
				<el-scrollbar>
					<el-descriptions
						size="small"
						:column="1"
						:border="true"
						class="content"
					>
						<el-descriptions-item
							v-for="(el, index) in descOptions"
							label-align="center"
							:label="el.label"
							:key="index"
						>
							<span v-if="el.needTooltip">
								<el-tooltip
									effect="dark"
									:content="formData?.[el.name]"
									:disabled="
										getRealLength(formData?.[el.name]) <= 100 ? true : false
									"
								>
									{{
										getRealLength(formData?.[el.name]) > 100
											? setString(formData?.[el.name], 100)
											: formData?.[el.name] || "---"
									}}
								</el-tooltip>
							</span>
							<span v-else>
								{{ formData[el.name] ? formData[el.name] : "---" }}
							</span>
						</el-descriptions-item>
					</el-descriptions>
				</el-scrollbar>
			</div>
			<ButtonList
				v-if="footerBtnVisible"
				class="footer"
				:button="formBtnList"
				@onBtnClick="emit('close')"
			/>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.drawer-column {
	width: 100%;
}
.el-scrollbar {
	height: calc(100% - 30px);
}
</style>
