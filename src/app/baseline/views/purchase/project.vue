<!-- 采购项目 主表 V2.0-->
<script lang="ts" setup>
import { ref, onMounted, reactive } from "vue"
import { ElMessage } from "element-plus"
import {
	checkPermission,
	isCheckPermission
} from "@/app/platform/utils/permission"
import { useTbInit } from "@/app/baseline/views/components/tableBase"
import { powerList } from "@/app/baseline/views/components/define.d"
import CostTag from "@/app/baseline/views/components/costTag.vue"
import { PurchaseProjectApi } from "@/app/baseline/api/purchase/purchaseProject"
import MonthPlan from "./project/monthPlan.vue"
import { useDictInit } from "../components/dictBase"
import { useMessageBoxInit } from "@/app/baseline/views/components/messageBox"
import LinkTag from "../components/linkTag.vue"
import { getOnlyDate, tableColFilter } from "../../utils"
import { modalSize } from "@/app/baseline/utils/layout-config"
import {
	toNumber,
	first,
	includes,
	map,
	forEach,
	intersection
} from "lodash-es"
import {
	IIdempotentTokenType,
	IIdempotentTokenTypeAction,
	IIdempotentTokenTypePre,
	IModalType
} from "../../utils/types/common"
import { DictApi } from "../../api/dict"
import { BaseLineSysApi, listCompanyWithFormat } from "../../api/system"
import projectEditor from "./project/projectEditor.vue"
import projectDetail from "./project/projectDetail.vue"
import purchasePlanDetaiil from "./project/purchasePlanDetail.vue"
import closeEditor from "./project/closeEditor.vue"
import { getIdempotentToken } from "../../utils/validate"

const { dictFilter, dictOptions, getDictByCodeList } = useDictInit()
const { showDelConfirm, showWarnConfirm } = useMessageBoxInit()

/**
 * 公司列表
 */
const companyOptions = ref<any>([])

/**
 * 查询条件 配置
 */
const queryArrList = computed(() => [
	{
		name: "采购计划号",
		key: "planPurchaseCode",
		placeholder: `请输入采购计划号`,
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "采购项目编号",
		key: "code",
		placeholder: `请输入采购项目编号`,
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "采购项目名称",
		key: "label",
		placeholder: `请输入采购项目名称`,
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "年度",
		key: "year",
		type: "select",
		children: DictApi.getFutureYears(-2, 2),
		placeholder: "请选择所属年度"
	},
	{
		name: "公司",
		key: "sysCommunityId",
		type: "select",
		children: companyOptions.value,
		placeholder: "请选择所属公司"
	},
	{
		name: "合同类型",
		key: "contractType",
		type: "elTreeSelect",
		children: dictOptions.value.CONTRACT_TYPE,
		placeholder: "请选择合同类型"
	},
	{
		name: "采购方式",
		key: "purchaseType",
		type: "select",
		children: dictOptions.value.PURCHASE_TYPE,
		placeholder: "请选择项采购方式"
	},
	{
		name: "合同签订日期",
		key: "contractSigningDate",
		type: "startAndEndTime"
	},
	{
		name: "合同截止日期",
		key: "contractEndDate",
		type: "startAndEndTime"
	}
])

/**
 * 页面标题
 */
const titleConf = {
	name: ["采购项目"],
	icon: ["fas", "square-share-nodes"]
}

const tbBtnLoading = ref(false)

const tbInit = useTbInit()
const {
	tableData,
	tableRef,
	tableLoading,
	currentPage,
	pageTotal,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = tbInit

/**
 * table 列 配置
 */
tbInit.tableProp = computed(() => {
	const defColumn: TableColumnType[] = [
		{ label: "年度", prop: "year", width: 85, fixed: "left", sortable: true },
		{
			label: "采购计划号",
			prop: "planPurchaseCode",
			width: 150,
			fixed: "left"
		},
		{ label: "采购项目编号", prop: "code", width: 180, fixed: "left" },
		{ label: "采购项目名称", prop: "label", minWidth: 200 },
		{
			label: "已生成订货计划",
			prop: "purchasePlanNum",
			needSlot: true,
			width: 200
		},
		{ label: "合同编号", prop: "contractCode", width: 200 },
		{ label: "合同名称", prop: "contractLabel", width: 250 },
		{
			label: "合同签订日期",
			prop: "contractSigningDate",
			needSlot: true,
			width: 160,
			sortable: true
		},
		{
			label: "合同截止日期",
			prop: "contractEndDate",
			needSlot: true,
			width: 160,
			sortable: true
		},
		{ label: "合同类型", prop: "contractType", needSlot: true, width: 130 },
		{
			label: "合同金额",
			prop: "contractAmount",
			needSlot: true,
			width: 130,
			align: "right",
			sortable: true
		},
		{ label: "供应商", prop: "supplierLabel", width: 200 },
		{ label: "采购方式", prop: "purchaseType", needSlot: true, width: 130 },
		{ label: "采购员", prop: "purchaseUserName", width: 130 },
		{ label: "执行状态", prop: "status", needSlot: true },
		{ label: "关闭原因", prop: "closeReason", width: 130 },
		{ label: "操作人", prop: "lastModifiedBy_view", width: 130 },
		{ label: "更新时间", prop: "lastModifiedDate", width: 160 },
		{
			label: "操作",
			width: 200,
			prop: "operations",
			fixed: "right",
			needSlot: true
		}
	]

	if (activeIndex.value == 0) {
		return tableColFilter(defColumn, [
			"执行状态",
			"关闭原因",
			"关闭原因",
			"更新时间"
		])
	} else {
		return defColumn
	}
})

fetchFunc.value = PurchaseProjectApi.getPurchaseProjectList

/**
 * table 按钮 操作
 */
const tableBtnConf = computed(() => {
	const isCanMakeMonthPlan = (row?: any) => {
		const checkFields = [
			"contractCode",
			"contractSigningDate",
			"contractEndDate",
			"supplierLabel"
		]
		let ret = true
		checkFields.forEach((field) => {
			if (
				row[field] == null ||
				row[field] === "" ||
				row[field].toString() === "0"
			) {
				ret = false
			}
		})
		return ret
	}

	const batchIsCanMakeMonthPlan = selectedTableList.value.every((item) => {
		return isCanMakeMonthPlan(item)
	})

	const canEdit =
		selectedTableList.value.length > 0
			? batchIsCanMakeMonthPlan
				? true
				: false
			: false
	return [
		{
			name: "生成月度计划",
			roles: powerList.purchaseProjectBtnGeneratePlan,
			icon: ["fas", "file-signature"],
			disabled: !canEdit
		},
		{
			name: "关闭",
			roles: powerList.purchaseProjectBtnClose,
			icon: ["fas", "times-circle"],
			disabled:
				selectedTableList.value.length === 1 &&
				first(selectedTableList.value)?.purchasePlanNum > 0
					? false
					: true
		}
	]
})

const editorId = ref("")
const projectEditorModel = ref(IModalType.create)
const projectEditorVisible = ref(false)
const projectDetailVisible = ref(false)
const projectMonthPlanVisible = ref(false)

const currentDate = computed(() => {
	/**
	 *  当前月份
	 */
	const currentMonth = new Date().getMonth() + 1

	/**
	 *  当前年份
	 */
	const currentYear = new Date().getFullYear()

	return {
		year: currentMonth == 12 ? currentYear + 1 : currentYear,
		month: currentMonth == 12 ? 1 : currentMonth + 1
	}
})

/**
 * table 行样式
 *
 * 单据异常，用红色标记该行
 */

const errorIdList = ref()
function tbCellClassName({ row }: any) {
	return includes(errorIdList.value, row.id) ? "error" : ""
}

/**
 * table 底部 按钮操作
 * @param btnName 生成月度计划 | 关闭
 */
async function handleTableFooterAction(btnName?: string) {
	if (btnName === "生成月度计划") {
		if (selectedTableList.value.length > 1) {
			await showWarnConfirm(
				`是否将所选采购项目批量生成【${currentDate.value.month}月】月度订货计划？`
			)

			const idempotentToken = getIdempotentToken(
				IIdempotentTokenTypePre.batch,
				IIdempotentTokenType.purchaseProject,
				"",
				IIdempotentTokenTypeAction.purchasePlan
			)

			const idList = map(selectedTableList.value, ({ id }) => id)
			const { code, msg, data } =
				await PurchaseProjectApi.batchGenerateMonthlyPurchasePlan(
					idList,
					idempotentToken
				)
			if (data && code != 200) {
				errorIdList.value = data
				ElMessage({
					type: "warning",
					message: `本次批量生成月度订货计划有${data?.length}条错误数据，批量生成月度计划失败`,
					duration: 5000
				})
			} else {
				ElMessage.success("操作成功")
				errorIdList.value = []
				getTableData()
			}
		} else {
			editorId.value = first(selectedTableList.value)?.id

			projectMonthPlanVisible.value = true
		}
	} else if (btnName === "关闭") {
		handleCloseAction()
	}
}

watch([selectedTableList], () => {
	if (selectedTableList.value.length > 0) {
		const idList = map(selectedTableList.value, ({ id }) => id)
		errorIdList.value = intersection(idList, errorIdList.value)

		console.log("errorIdList", intersection(idList, errorIdList.value))
	} else {
		errorIdList.value = []
	}
})

/**
 * 关闭 操作
 */
const closeEditorVisible = ref(false)

async function handleCloseAction() {
	/**
	 * 检查是否可关闭，并判断是否存在待提交|已驳回的订货计划
	 *  如果存在，返回true，否则返回false
	 */
	const res = await PurchaseProjectApi.closeCheckPurchaseProject(
		first(selectedTableList.value)
	)

	if (res) {
		// 存在待确认的订货计划，关闭采购项目将作废该订货计划，请确认是否作废
		await showWarnConfirm(
			"存在待确认的订货计划，关闭采购项目将作废该订货计划，请确认是否作废"
		)
	}
	closeEditorVisible.value = true
}

const getTableData = (data?: any) => {
	if (data) {
		const { contractSigningDate, contractEndDate } = data
		data.contractSigningDate_start = contractSigningDate
			? `${contractSigningDate[0]} 00:00:00`
			: ""
		data.contractSigningDate_end = contractSigningDate
			? `${contractSigningDate[1]} 23:59:59`
			: ""
		delete data.contractSigningDate
		data.contractEndDate_start = contractEndDate
			? `${contractEndDate[0]} 00:00:00`
			: ""
		data.contractEndDate_end = contractEndDate
			? `${contractEndDate[1]} 23:59:59`
			: ""
		delete data.contractEndDate
	}
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		...data
	}

	errorIdList.value = []
	fetchTableData()
	getTypeNumbers()
}

//查询

/**********************tab 相关操作 *********************************/
const tabList = [
	{
		name: "执行中",
		icon: ["fas", "square-plus"]
	},
	{
		name: "已完成",
		icon: ["fas", "square-plus"]
	}
]
const tabNum = ref(Array.from({ length: tabList.length }, () => 0))

const tabStatus = reactive([["0"], ["1", "2"]])

const activeName = ref(tabList[0].name)
const activeIndex = ref(0)

/**
 * 单击 操作
 * @param tab
 */
const handleTabsClick = (tab: any) => {
	activeName.value = tab.paneName
	activeIndex.value = tab.index
	fetchParam.value.status = tabStatus[tab.index].join(",")
	getTableData()
}

//drawer
/* const curRowId = ref<any>("")
const curRowData = ref<any>("")

const showEditDrawer = ref<boolean>(false)
const showViewDrawer = ref<boolean>(false)
const showViewOrderPlanDrawer = ref<boolean>(false) */

/**
 * 查看 操作
 * @param row
 */
function handleRowView(row: any) {
	editorId.value = row.id
	projectDetailVisible.value = true
	projectEditorModel.value = IModalType.view
}

/**
 * 编辑 操作
 * @param row
 */
function handleRowEdit(row: any) {
	editorId.value = row.id
	projectEditorModel.value = IModalType.edit
	projectEditorVisible.value = true
}

/**
 * 删除 操作
 * @param row
 */
async function handleRowDel(row: any) {
	await showDelConfirm()
	await PurchaseProjectApi.deletePurchaseProject(row.id)

	ElMessage.success("操作成功")
	refreshTable()
}

const orderGoodsPlanDetailVisible = ref(false)
const curRowData = ref({})
const handleOrderGoodsPlanView = (row: any) => {
	orderGoodsPlanDetailVisible.value = true
	curRowData.value = { ...row }
	editorId.value = row.id
	/* curRowId.value = row.id
	curRowData.value = row
	showViewOrderPlanDrawer.value = true */
}

// 弹窗关闭
const closeDrawer = (msg: string) => {
	if (msg === "pub") {
		getTableData()
	} else if (msg === "save") {
		getTableData()
	} else {
		orderGoodsPlanDetailVisible.value = false
	}
}
const getTypeNumbers = () => {
	const params = JSON.parse(JSON.stringify(fetchParam.value))
	delete params.currentPage
	delete params.pageSize
	delete params.status
	PurchaseProjectApi.getStatusStatistics(params)
		.then((res) => {
			tabNum.value[0] = toNumber(res["0"] ?? 0) //  ? res["0"] : 0
			tabNum.value[1] = toNumber(res["1"] ?? 0) + toNumber(res["2"] ?? 0)
		})
		.finally(() => {})
}

function refreshTable() {
	fetchTableData()
	getTypeNumbers()
}
onMounted(async () => {
	fetchParam.value = {
		status: tabStatus[0].join(","),
		sord: "desc",
		sidx: "createdDate"
	}

	getDictByCodeList([
		"PURCHASE_TYPE",
		"CONTRACT_TYPE",
		"PURCHASE_PROJECT_CLOSE_REASON"
	])

	listCompanyWithFormat().then((r) => (companyOptions.value = r))

	getTableData()
})

/**
 * 排序
 * @param column
 * @param prop 字段名称
 * @param order ascending 升序 descending 降序
 */
function handleSortChange(column: any, prop: any, order: any) {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	const computedProp = () => {
		if (prop === "contractSigningDate") {
			return "purchaseContractContractSigningDate"
		} else if (prop === "contractEndDate") {
			return "purchaseContractContractEndDate"
		} else if (prop === "contractAmount") {
			return "purchaseContractContractAmount"
		} else {
			return prop
		}
	}

	fetchParam.value = {
		...fetchParam.value,
		sord:
			order === "descending" ? "desc" : order === "ascending" ? "asc" : "desc",
		sidx: order ? computedProp() : "createdDate" // 排序字段
	}

	fetchTableData()
}

defineOptions({
	name: "Project"
})
</script>
<template>
	<div class="app-container">
		<div class="whole-frame">
			<ModelFrame class="top-frame">
				<Query
					:queryArrList="queryArrList"
					@getQueryData="getTableData"
					class="ml10"
				/>
			</ModelFrame>
			<ModelFrame class="bottom-frame project-table-wrapper">
				<Title :title="titleConf">
					<div class="app-tabs-wrapper">
						<el-tabs v-model="activeName" @tab-click="handleTabsClick">
							<el-tab-pane
								v-for="(tab, index) in tabList"
								:key="index"
								:label="`${tab.name}（${tabNum[index]}）`"
								:name="tab.name"
								:index="tab.name"
							/>
						</el-tabs>
					</div>
				</Title>
				<PitayaTable
					ref="tableRef"
					:columns="(tbInit.tableProp as any)"
					:tableData="tableData"
					:total="pageTotal"
					:single-select="false"
					:need-selection="true"
					:need-index="true"
					@onSelectionChange="selectedTableList = $event"
					@on-current-page-change="onCurrentPageChange"
					:table-loading="tableLoading"
					@on-table-sort-change="handleSortChange"
					:cell-class-name="tbCellClassName"
				>
					<!-- 合同签订日期 -->
					<template #contractSigningDate="{ rowData }">
						{{ getOnlyDate(rowData.contractSigningDate) || "---" }}
					</template>

					<!-- 合同截止日期 -->
					<template #contractEndDate="{ rowData }">
						{{ getOnlyDate(rowData.contractEndDate) || "---" }}
					</template>

					<!-- 合同金额 -->
					<template #contractAmount="{ rowData }">
						<cost-tag :value="rowData.contractAmount" />
					</template>

					<!-- 已生成订货计划 -->
					<template #purchasePlanNum="{ rowData }">
						<link-tag
							:value="rowData.purchasePlanNum"
							@on-click="handleOrderGoodsPlanView(rowData)"
						/>
					</template>

					<!-- 采购方式 -->
					<template #purchaseType="{ rowData }">
						{{
							dictFilter("PURCHASE_TYPE", rowData.purchaseType)?.label || "---"
						}}
					</template>

					<!-- 合同类型 -->
					<template #contractType="{ rowData }">
						{{
							dictFilter("CONTRACT_TYPE", rowData.contractType)?.label || "---"
						}}
					</template>

					<!-- 执行状态 -->
					<template #status="{ rowData }">
						<el-tag
							:disable-transitions="true"
							:type="rowData.status == '1' ? 'success' : 'danger'"
						>
							{{ rowData.status == "1" ? "已完成" : "已关闭" }}
						</el-tag>
					</template>
					<template #operations="{ rowData }">
						<slot
							v-if="
								isCheckPermission(powerList.purchaseProjectBtnPreview) ||
								(isCheckPermission(powerList.purchaseProjectBtnEdit) &&
									rowData.purchasePlanNum <= 0) ||
								(isCheckPermission(powerList.purchaseProjectBtnDrop) &&
									rowData.purchasePlanNum <= 0)
							"
						>
							<el-button
								v-btn
								link
								@click.stop="handleRowEdit(rowData)"
								:disabled="
									checkPermission(powerList.purchaseProjectBtnEdit) &&
									rowData.purchasePlanNum <= 0
								"
								v-if="
									isCheckPermission(powerList.purchaseProjectBtnEdit) &&
									rowData.purchasePlanNum <= 0
								"
							>
								<font-awesome-icon :icon="['fas', 'pen-to-square']" />
								<span class="table-inner-btn">编辑</span>
							</el-button>

							<el-button
								v-btn
								link
								@click.stop="handleRowView(rowData)"
								:disabled="checkPermission(powerList.purchaseProjectBtnPreview)"
								v-if="isCheckPermission(powerList.purchaseProjectBtnPreview)"
							>
								<font-awesome-icon
									:icon="['fas', 'eye']"
									style="color: var(--pitaya-btn-background)"
								/>
								<span class="table-inner-btn">查看</span>
							</el-button>

							<el-button
								v-btn
								link
								@click.stop="handleRowDel(rowData)"
								:disabled="
									checkPermission(powerList.purchaseProjectBtnDrop) &&
									rowData.purchasePlanNum <= 0
								"
								v-if="
									isCheckPermission(powerList.purchaseProjectBtnDrop) &&
									rowData.purchasePlanNum <= 0
								"
							>
								<font-awesome-icon :icon="['fas', 'trash-can']" />
								<span class="table-inner-btn">移除</span>
							</el-button>
						</slot>
						<slot v-else>---</slot>
					</template>
					<template #footerOperateLeft>
						<ButtonList
							v-if="activeIndex == 0"
							class="btn-list"
							:is-not-radius="true"
							:button="tableBtnConf"
							:loading="tbBtnLoading"
							@on-btn-click="handleTableFooterAction"
						/>
					</template>
				</PitayaTable>

				<!-- 生成月度计划 操作 -->
				<Drawer
					:size="modalSize.sm"
					v-model:drawer="projectMonthPlanVisible"
					:destroyOnClose="true"
				>
					<month-plan
						:id="editorId"
						:row="first(selectedTableList)"
						@close="projectMonthPlanVisible = false"
						@update="refreshTable"
					/>
				</Drawer>

				<!-- 新增/编辑 -->
				<Drawer
					:size="modalSize.xxl"
					v-model:drawer="projectEditorVisible"
					:destroyOnClose="true"
				>
					<project-editor
						:id="editorId"
						:model="projectEditorModel"
						@close="projectEditorVisible = false"
						@update="refreshTable"
					/>
				</Drawer>

				<!-- 查看弹窗 -->
				<Drawer
					:size="modalSize.xxl"
					v-model:drawer="projectDetailVisible"
					:destroyOnClose="true"
				>
					<project-detail
						:id="editorId"
						:model="projectEditorModel"
						@close="projectDetailVisible = false"
					/>
				</Drawer>

				<Drawer
					:size="modalSize.xxl"
					v-model:drawer="orderGoodsPlanDetailVisible"
					:destroyOnClose="true"
				>
					<purchase-plan-detaiil
						:id="editorId"
						:row="curRowData"
						model="viewPlan"
						@on-save-or-close="closeDrawer"
						@on-select-change="fetchTableData"
					/>
				</Drawer>

				<Drawer
					:size="350"
					v-model:drawer="closeEditorVisible"
					:destroyOnClose="true"
				>
					<close-editor
						:id="first(selectedTableList)?.id"
						@close="closeEditorVisible = false"
						@update="refreshTable"
					/>
				</Drawer>
			</ModelFrame>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.underline-link {
	text-decoration: none;
	border-bottom: 1px solid;
	display: inline-block;
	transition: border-bottom-color 0.2s;
}

.underline-link:hover {
	border-bottom-color: transparent;
}

.dialog-box {
	:deep(.el-dialog__body) {
		margin: 10px;
		padding: 20px 0 10px !important;
		border-top: 1px solid #ddd;
		border-bottom: 1px solid #ddd;
	}
}

.project-table-wrapper {
	&:deep(.pitaya-table) {
		.error {
			.column-inner-item,
			.cell,
			.el-input__inner {
				color: red;
			}
		}
	}
}
</style>
