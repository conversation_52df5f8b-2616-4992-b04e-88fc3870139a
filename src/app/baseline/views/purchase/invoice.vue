<!-- 发票管理 主表 重构 V2.0 -->
<script lang="ts" setup>
import { ref, onMounted } from "vue"
import { ElMessage } from "element-plus"
import XEUtils from "xe-utils"
import { useTbInit } from "@/app/baseline/views/components/tableBase"
import { useDictInit } from "@/app/baseline/views/components/dictBase"
import { useMessageBoxInit } from "@/app/baseline/views/components/messageBox"
import {
	checkPermission,
	isCheckPermission
} from "@/app/platform/utils/permission"
import { powerList } from "@/app/baseline/views/components/define.d"
import {
	getTaskByBusinessIds,
	listCompanyWithFormat
} from "@/app/baseline/api/system"
import { appStatus, DictApi, IInvoiceColor } from "@/app/baseline/api/dict"
import { PurchaseInvoiceApi } from "@/app/baseline/api/purchase/purchaseInvoice"
import { useUserStore } from "@/app/platform/store/modules/user"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import CostTag from "@/app/baseline/views/components/costTag.vue"
import invoiceEditor from "./invoice/invoiceEditor.vue"
import invoiceDetail from "./invoice/invoiceDetail.vue"
import { omit } from "lodash-es"
import { IModalType } from "../../utils/types/common"
import { modalSize } from "@/app/baseline/utils/layout-config"
import { PurchaseSupplierApi } from "../../api/purchase/purchaseSupplier"
import ApprovedDrawer from "@/app/baseline/views/components/approvedDrawer.vue"

import { hasEditByBpm } from "../../utils"

/**
 * 当前用户信息
 */
const userStore = useUserStore()
const { userInfo } = storeToRefs(userStore)

const { showDelConfirm } = useMessageBoxInit()

const { dictOptions, getDictByCodeList, dictFilter } = useDictInit()

const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	currentPage,
	pageTotal,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange,
	onDataSelected
} = useTbInit()

tableProp.value = [
	{ label: "发票号码", prop: "code", needSlot: true, width: 160 },
	{ label: "发票类型", prop: "type", needSlot: true, width: 160 },
	{
		label: "开票金额",
		prop: "amount",
		needSlot: true,
		align: "right",
		width: 180
	},
	{ label: "合同编号", prop: "contractCode", needSlot: true, width: 180 },
	{ label: "供应商名称", prop: "supplierLabel", needSlot: true, minWidth: 180 },
	{
		label: "开票日期",
		prop: "invoiceDate",
		needSlot: true,
		width: 160,
		sortable: true
	},
	{ label: "发票状态", prop: "status", needSlot: true, width: 120 },
	{ label: "审批状态", prop: "bpmStatus", needSlot: true, width: 120 },
	{ label: "录入人员", prop: "createdBy_view", needSlot: true, width: 120 },
	{
		label: "录入时间",
		prop: "createdDate",
		needSlot: true,
		width: 160,
		sortable: true
	},
	{
		label: "操作",
		prop: "operations",
		width: 200,
		needSlot: true,
		fixed: "right"
	}
]

fetchFunc.value = PurchaseInvoiceApi.getPurchaseInvoiceList

/**
 * title 配置
 */
const titleConf = {
	name: ["发票管理"],
	icon: ["fas", "square-share-nodes"]
}
const addBtnConf = computed(() => [
	{
		name: "新建发票信息",
		roles: powerList.purchaseInvoiceBtnCreate,
		icon: ["fas", "square-plus"]
	}
])
const tabsConf = [
	{
		name: "草稿箱",
		value: `${appStatus.rejected},${appStatus.pendingApproval}`
	},
	{ name: "审批中", value: appStatus.underApproval },
	{ name: "已审批", value: appStatus.approved }
]

const statusCnt = ref<number[]>([])

const handleTabsClick = (tab: any) => {
	activeName.value = tab.paneName
	activeType.value = Number(tab.index)
	fetchParam.value.bpmStatus = tabsConf[tab.index].value

	handleUpdate()
}

/**
 * 公司列表
 */
const companyOptions = ref<any>([])

/**
 * 查询条件 配置
 */
const queryArrList = computed(() => {
	return [
		{
			name: "发票号码",
			key: "code",
			type: "input",
			placeholder: "请输入发票号码",
			enableFuzzy: true
		},
		{
			name: "录入人员",
			key: "createdUserRealname",
			type: "input",
			placeholder: "请输入录入人员",
			enableFuzzy: true
		},
		{
			name: "公司",
			key: "sysCommunityId",
			type: "select",
			children: companyOptions.value,
			placeholder: "请选择公司"
		},
		{
			name: "发票类型",
			key: "type",
			type: "select",
			children: dictOptions.value.INVOICE_TYPE,
			placeholder: "请选择发票类型"
		},
		{
			name: "供应商",
			key: "supplierId",
			type: "tableSelect",
			placeholder: "请选择供应商",
			tableInfo: [
				{
					title: "请选择供应商",
					tableApi: PurchaseSupplierApi.getPurchaseSupplierList, //表格接口
					tableColumns: [
						{ label: "供应商编码", prop: "code", width: 160 },
						{ label: "供应商名称", prop: "label" }
					],
					queryArrList: [
						{
							name: "供应商编码",
							key: "code",
							type: "input",
							placeholder: "请输入供应商编码"
						},
						{
							name: "供应商名称",
							key: "label",
							type: "input",
							placeholder: "请输入供应商名称"
						}
					],
					params: {
						currentPage: 1,
						pageSize: 20
					}, //表格接口参数

					labelName: {
						key: "id", //回显标识
						label: "label", //input框要展示的字段
						value: "id" //要给后台传的字段
					}
				}
			]
		},
		{
			name: "开票日期",
			key: "invoiceDate",
			type: "startAndEndTime",
			placeholder: "开票日期"
		}
	]
})

// /chart/branch/id=1010

// /baseline/views/chart/branch/index.vue

// chart-simple

// chart: branch
const activeType = ref(0)
const activeName = ref(tabsConf[0].name)
const editorModel = ref(IModalType.create)
const viewDetailVisible = ref(false)
const editorVisible = ref(false)
const editorId = ref()
const currentUser = ref(userInfo.value.userName)

const approvedVisible = ref(false)
const editTask = ref<Record<string, any>>()

/**
 * 添加 操作
 */
const handleAddAction = () => {
	editorModel.value = IModalType.create
	editorVisible.value = true
	editorId.value = ""
}

/**
 * 编辑 操作
 * @param e
 */
const handleEditAction = (e: Record<string, any>) => {
	editorModel.value = IModalType.edit
	editorVisible.value = true
	editorId.value = e.id
}

/**
 * 查看 操作
 * @param e
 */
const handleViewAction = async (e: Record<string, any>) => {
	editorModel.value = IModalType.view
	/* viewDetailVisible.value = true */
	editorId.value = e.id

	if (e.bpmStatus == appStatus.pendingApproval) {
		viewDetailVisible.value = true
	} else {
		const res = await getTaskByBusinessIds({
			businessIds: [e.id],
			camundaKey: "invoice"
		})

		if (Array.isArray(res) && res.length > 0) {
			editTask.value = { ...res[res.length - 1] }
			approvedVisible.value = true
		} else {
			viewDetailVisible.value = true
		}
	}
}

/**
 * 删除
 * @param row
 */
const handleDeleteAction = async (row: any) => {
	await showDelConfirm()
	await PurchaseInvoiceApi.deletePurchaseInvoice(row.id)
	ElMessage.success("移除成功")
	handleUpdate()
}

//筛选条件查询
function getTableData(data: { [propName: string]: any }) {
	const _param = XEUtils.clone(data)
	if (_param?.invoiceDate?.length > 0) {
		_param["invoiceDate_start"] = _param.invoiceDate[0]
		_param["invoiceDate_end"] = _param.invoiceDate[1]
	} else {
		delete fetchParam.value["invoiceDate_start"]
		delete fetchParam.value["invoiceDate_end"]
	}

	delete _param.invoiceDate
	delete fetchParam.value.invoiceDate
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		..._param
	}
	handleUpdate()
}

/**
 * 更新主表/statusCnt
 */
const handleUpdate = async () => {
	fetchTableData()
	const res = await PurchaseInvoiceApi.getPurchaseInvoiceStatusCount(
		omit({ ...fetchParam.value }, "bpmStatus", "currentPage", "pageSize") as any
	)
	statusCnt.value[appStatus.pendingApproval] =
		((res[appStatus.rejected] || 0) as any) +
		(res[appStatus.pendingApproval] || 0)
	statusCnt.value[appStatus.underApproval] =
		(res[appStatus.underApproval] as any) || 0

	statusCnt.value[appStatus.approved] = (res[appStatus.approved] as any) || 0
}

onMounted(() => {
	fetchParam.value.bpmStatus = tabsConf[0].value
	fetchParam.value.sord = "desc"
	fetchParam.value.sidx = "createdDate"

	getDictByCodeList(["INVOICE_TYPE", "INVOICE_STATUS"])
	listCompanyWithFormat().then((r) => (companyOptions.value = r))

	handleUpdate()
})

/**
 * 排序
 * @param column
 * @param prop 字段名称
 * @param order ascending 升序 descending 降序
 */
function handleSortChange(column: any, prop: any, order: any) {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		sord:
			order === "descending" ? "desc" : order === "ascending" ? "asc" : "desc",
		sidx: order ? prop : "createdDate" // 排序字段
	}

	fetchTableData()
}
</script>
<template>
	<div class="app-container">
		<div class="whole-frame">
			<ModelFrame class="top-frame">
				<Query
					:queryArrList="queryArrList"
					@getQueryData="getTableData"
					class="ml10"
				/>
			</ModelFrame>
			<ModelFrame class="bottom-frame">
				<Title
					:title="titleConf"
					:button="addBtnConf"
					@onBtnClick="handleAddAction"
				>
					<div class="app-tabs-wrapper">
						<el-tabs v-model="activeName" @tab-click="handleTabsClick">
							<el-tab-pane
								v-for="(tab, index) in tabsConf"
								:key="tab.value"
								:label="`${tab.name}（${statusCnt[index] || 0}）`"
								:name="tab.name"
								:index="index"
							/>
						</el-tabs>
					</div>
				</Title>
				<PitayaTable
					ref="tableRef"
					:columns="tableProp"
					:tableData="tableData"
					:total="pageTotal"
					:single-select="false"
					:need-selection="false"
					:need-index="true"
					@onSelectionChange="onDataSelected"
					@on-current-page-change="onCurrentPageChange"
					:table-loading="tableLoading"
					@on-table-sort-change="handleSortChange"
				>
					<template #code="{ rowData }">
						<label
							:class="
								rowData.invoiceColor == IInvoiceColor.red ? 'error' : 'info'
							"
							>{{ rowData.code || "---" }}</label
						>
					</template>
					<template #type="{ rowData }">
						<label
							:class="
								rowData.invoiceColor == IInvoiceColor.red ? 'error' : 'info'
							"
						>
							{{ dictFilter("INVOICE_TYPE", rowData.type)?.label || "---" }}
						</label>
					</template>

					<template #contractCode="{ rowData }">
						<label
							:class="
								rowData.invoiceColor == IInvoiceColor.red ? 'error' : 'info'
							"
							>{{ rowData.contractCode || "---" }}</label
						>
					</template>

					<template #supplierLabel="{ rowData }">
						<label
							:class="
								rowData.invoiceColor == IInvoiceColor.red ? 'error' : 'info'
							"
							>{{ rowData.supplierLabel || "---" }}</label
						>
					</template>
					<template #invoiceDate="{ rowData }">
						<label
							:class="
								rowData.invoiceColor == IInvoiceColor.red ? 'error' : 'info'
							"
						>
							{{
								rowData.invoiceDate
									? XEUtils.toDateString(
											new Date(rowData.invoiceDate),
											"yyyy-MM-dd"
									  )
									: "---"
							}}
						</label>
					</template>
					<template #createdBy_view="{ rowData }">
						<label
							:class="
								rowData.invoiceColor == IInvoiceColor.red ? 'error' : 'info'
							"
							>{{ rowData.createdBy_view || "---" }}</label
						>
					</template>
					<template #createdDate="{ rowData }">
						<label
							:class="
								rowData.invoiceColor == IInvoiceColor.red ? 'error' : 'info'
							"
							>{{ rowData.createdDate || "---" }}</label
						>
					</template>
					<template #amount="{ rowData }">
						<label
							:class="
								rowData.invoiceColor == IInvoiceColor.red ? 'error' : 'info'
							"
						>
							<cost-tag :value="rowData.amount" />
						</label>
					</template>
					<template #status="{ rowData }">
						<dict-tag
							:options="DictApi.getInvoiceStatus(rowData.invoiceColor)"
							:value="rowData.bpmStatus"
						/>
					</template>
					<template #bpmStatus="{ rowData }">
						<dict-tag
							:options="DictApi.getBpmStatus()"
							:value="rowData.bpmStatus"
						/>
					</template>
					<template #operations="{ rowData }">
						<slot
							v-if="
								isCheckPermission(powerList.purchaseInvoiceBtnPreview) ||
								(isCheckPermission(powerList.purchaseInvoiceBtnEdit) &&
									hasEditByBpm(rowData.bpmStatus, rowData.createdBy)) ||
								(isCheckPermission(powerList.purchaseInvoiceBtnDrop) &&
									hasEditByBpm(rowData.bpmStatus, rowData.createdBy))
							"
						>
							<el-button
								v-btn
								link
								@click="handleEditAction(rowData)"
								:disabled="checkPermission(powerList.purchaseInvoiceBtnEdit)"
								v-if="
									isCheckPermission(powerList.purchaseInvoiceBtnEdit) &&
									hasEditByBpm(rowData.bpmStatus, rowData.createdBy)
								"
							>
								<font-awesome-icon :icon="['fas', 'pen-to-square']" />
								<span class="table-inner-btn">编辑</span>
							</el-button>

							<el-button
								v-btn
								link
								@click="handleViewAction(rowData)"
								:disabled="checkPermission(powerList.purchaseInvoiceBtnPreview)"
								v-if="isCheckPermission(powerList.purchaseInvoiceBtnPreview)"
							>
								<font-awesome-icon :icon="['fas', 'eye']" />
								<span class="table-inner-btn">查看</span>
							</el-button>

							<el-button
								v-btn
								link
								@click="handleDeleteAction(rowData)"
								:disabled="checkPermission(powerList.purchaseInvoiceBtnDrop)"
								v-if="
									isCheckPermission(powerList.purchaseInvoiceBtnDrop) &&
									hasEditByBpm(rowData.bpmStatus, rowData.createdBy)
								"
							>
								<font-awesome-icon :icon="['fas', 'trash-can']" />
								<span class="table-inner-btn">移除</span>
							</el-button>
						</slot>
						<slot v-else>---</slot>
					</template>
				</PitayaTable>

				<!-- 新建/编辑 -->
				<Drawer
					:size="modalSize.xl"
					v-model:drawer="editorVisible"
					destroyOnClose
				>
					<invoice-editor
						:id="editorId"
						:mode="editorModel"
						@close="editorVisible = false"
						@update="handleUpdate"
					/>
				</Drawer>

				<!-- 查看-->
				<Drawer
					:size="modalSize.xl"
					v-model:drawer="viewDetailVisible"
					destroyOnClose
				>
					<invoice-detail
						:id="editorId"
						:mode="editorModel"
						@close="viewDetailVisible = false"
					/>
				</Drawer>

				<!-- 审批、查看弹窗 -->
				<Drawer
					:size="modalSize.xxl"
					v-model:drawer="approvedVisible"
					:destroyOnClose="true"
				>
					<approved-drawer
						:mod="editorModel"
						:task-value="editTask"
						@on-save-or-close="approvedVisible = false"
					/>
				</Drawer>
			</ModelFrame>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
label.error {
	color: $---color-error;
}
label.info {
	color: $---color-info;
}
</style>
