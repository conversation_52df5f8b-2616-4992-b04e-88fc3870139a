<script setup lang="ts">
import { onMounted, reactive, ref } from "vue"
import { FormInstance, FormRules } from "element-plus"
import { useUserStore } from "@/app/platform/store/modules/user"

import Upload from "@/app/baseline/views/components/Upload.vue"
import { useRoute } from "vue-router"
import { DictApi } from "@/app/baseline/api/dict"
import { addReportInfo } from "@/app/baseline/api/chart/branch"
import { IModalType } from "@/app/baseline/utils/types/common"
import { BaseLineSysApi } from "@/app/baseline/api/system"

const props = defineProps<{
	mod: string
	row: Record<string, any>
}>()
const files = ref<any[]>([])
const route = useRoute()
const companyId = route.fullPath.split("id=")[1]
const userStore = useUserStore()
const { userInfo } = storeToRefs(userStore)
const emit = defineEmits(["onSuccess", "onClosed"])

const formData = ref<Record<string, any>>({
	...props.row,
	companyName: userInfo.value.companyName,
	sysCommunityId: companyId,
	attachmentId: props.mod === IModalType.edit ? props.row.attachmentVo?.id : "",
	reportTreeCode:
		props.mod === IModalType.edit ? props.row.categoryCode ?? "" : "",
	sortedBy: props.row.sortedBy ?? 1
})
const pageTitle = ref<{ name?: string[]; icon?: string[] }>({
	name: ["新增报表"],
	icon: ["fas", "square-share-nodes"]
})

const detailBtns = [
	{ name: "取消", icon: ["fas", "circle-minus"] },
	{ name: "保存", icon: ["fas", "floppy-disk"] }
]

const formRules = reactive<FormRules>({
	name: [{ required: true, message: "请输入报表名称", trigger: "change" }],
	url: [{ required: true, message: "请输入报表URL", trigger: "change" }],
	attachmentId: [
		{ required: true, message: "请选择报表图标", trigger: "change" }
	],
	reportTreeCode: [
		{ required: true, message: "请选择报表分类", trigger: "change" }
	],
	sortedBy: [{ required: true, message: "请输入排序", trigger: "change" }]
})

const formRef = ref<FormInstance>()
const isLoading = ref(true)
const buttonLoading = ref(false)

function onBtnClick(btnName: string | undefined) {
	if (btnName == "保存") {
		saveData()
	} else emit("onClosed")
}

/**
 * 保存 操作
 */
const saveData = () => {
	formRef.value?.validate(async (valid) => {
		if (valid) {
			try {
				buttonLoading.value = true

				await addReportInfo({
					...formData.value,
					categoryCode: `${formData.value.reportTreeCode}`
				})
				ElMessage.success("操作成功")
				emit("onSuccess")
			} finally {
				buttonLoading.value = false
			}
		}
	})
}

const reportTreeList = ref<any>([])

onMounted(() => {
	// 获取报表 字典
	/* DictApi.getComponentReport().then((res: any) => {
		reportTreeList.value = res
	}) */

	const data = {
		treeCode: "COMPANY_REPORT",
		composeSubitemValue: `${companyId}-%`,
		disable: false,
		subitemCodes: null
	}

	BaseLineSysApi.listSubitemLikeComposeCodes(data).then((res) => {
		reportTreeList.value = res
	})

	isLoading.value = false

	if (props.mod === IModalType.edit) {
		files.value = [props.row.attachmentVo]
	}
})

/**
 * 上传组件相关参数 限制文件上传类型
 */
const assetFileType = "image/jpg, image/jpeg, image/png"

/**
 * 上传文件回调
 * @param fileList
 */
const handleUpload = (fileList: any) => {
	files.value = fileList.value
	formData.value.attachmentId = files.value.map((item: any) => item.id)[0]
	if (formData.value.attachmentId) formRef.value?.clearValidate("attachmentId")
}

/**
 * 文件列表移除文件时回调
 */
const handleRemove = () => {
	formData.value.attachmentId = ""
}
</script>

<template>
	<div class="form-body">
		<div v-loading="buttonLoading">
			<Title :title="pageTitle" />
			<el-scrollbar v-if="!isLoading">
				<el-form
					:model="formData"
					:rules="formRules"
					label-position="top"
					label-width="420"
					class="form"
					ref="formRef"
				>
					<el-form-item label="所属公司">
						<el-input
							v-model="formData.companyName"
							show-word-limit
							disabled
							maxlength="50"
						/>
					</el-form-item>
					<el-form-item label="报表分类" prop="reportTreeCode">
						<el-select
							style="width: 100%"
							v-model.trim="formData.reportTreeCode"
							placeholder="请选择报表分类"
						>
							<el-option
								v-for="item in reportTreeList"
								:key="item.composeSubitemValue"
								:label="item.composeSubitemName"
								:value="item.composeSubitemValue"
							/>
						</el-select>
					</el-form-item>

					<el-form-item label="报表名称" prop="label">
						<el-input
							v-model.trim="formData.label"
							show-word-limit
							maxlength="50"
						/>
					</el-form-item>
					<el-form-item label="报表URL" prop="url">
						<el-input
							v-model.trim="formData.url"
							show-word-limit
							maxlength="500"
						/>
					</el-form-item>
					<el-form-item label="报表图标" prop="attachmentId">
						<Upload
							:list-type="''"
							:maxCount="1"
							:file-list="files"
							:accept="assetFileType"
							@onSuccess="handleUpload"
							@handleRemove="handleRemove"
							:data="{
								businessType: 11
							}"
							:multiple="false"
						/>
					</el-form-item>

					<el-form-item label="描述" prop="description">
						<el-input
							v-model.trim="formData.description"
							type="textarea"
							rows="10"
							placeholder="请输入描述内容"
							maxlength="200"
							show-word-limit
						/>
					</el-form-item>
					<el-form-item label="排序" prop="sortedBy">
						<el-input
							v-model.trim="formData.sortedBy"
							type="number"
							placeholder="请输入顺序"
						/>
					</el-form-item>
				</el-form>
			</el-scrollbar>
		</div>
		<div class="btn-groups">
			<ButtonList
				:button="detailBtns"
				:loading="buttonLoading"
				@on-btn-click="onBtnClick"
			/>
		</div>
	</div>
</template>

<style lang="scss" scoped>
.form-body {
	height: 100%;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	> div:first-child {
		.el-scrollbar {
			display: flex;
			flex-direction: column;
			height: calc(100vh - 110px);
		}
	}
}
.form {
	padding: 10px;
	.el-select {
		width: 100%;
	}
}
.btn-groups {
	border-top: 1px solid #ccc;
	padding: 10px 10px 0px 10px;
	display: flex;
	justify-content: flex-end;
}
</style>
