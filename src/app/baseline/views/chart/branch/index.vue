<!-- 分公司报表 -->
<script lang="ts" setup>
import { ref } from "vue"
import { useRoute } from "vue-router"
import branchEditor from "./branchEditor.vue"
import {
	delReportInfo,
	listFindReportGroup
} from "@/app/baseline/api/chart/branch"
import { getFileHttpUrl } from "@/app/platform/utils/common"
import { IModalType } from "@/app/baseline/utils/types/common"
import { useMessageBoxInit } from "../../components/messageBox"
import EmptyData from "@/assets/images/empty/empty_data.svg?component"
import { useBackBtnStore } from "@/app/platform/store/modules/backBtn"

const tableLoading = ref<boolean>(false)
const route = useRoute()
const companyId = route.fullPath.split("id=")[1]
const companyVisible = ref<boolean>(false)
const backBtnStore = useBackBtnStore()
const { backBtnState } = storeToRefs(backBtnStore)

const { showWarnConfirm } = useMessageBoxInit()

const tableData = ref<any[]>([])
const queryArrList = ref([
	{
		name: "报表名称",
		key: "label",
		placeholder: "请输入报表名称",
		type: "input",
		enableFuzzy: false
	}
])
const queryParams = ref({})
const getQueryData = (queryData: any) => {
	queryParams.value = queryData
	getTableData()
}

/**
 * 分页获取计划列表
 */
const getTableData = async () => {
	tableLoading.value = true
	listFindReportGroup({
		...queryParams.value,
		sysCommunityId: companyId as unknown as number
	})
		.then((res: any) => {
			tableData.value = res

			console.log(res)
		})
		.finally(() => {
			tableLoading.value = false
		})
}

const tableTitle = {
	name: ["报表"],
	icon: ["fas", "square-share-nodes"]
}
const button = [
	{
		name: "新增报表",
		icon: ["fas", "square-plus"],
		roles: "chart:branch:btn:add"
	}
]

onMounted(() => {
	getTableData()
})

/**
 *
 *
 * 新增报表
 *
 */
const editorVisible = ref(false)
const editType = ref()
const editRow = ref({})
function onCreate() {
	editType.value = IModalType.create
	editorVisible.value = true
	editRow.value = {}
}

function onRowEdit(data?: any) {
	editType.value = IModalType.edit
	editRow.value = data
	editorVisible.value = true
}

const fineReportUrl = ref("")
const onRowView = (data: any) => {
	fineReportUrl.value =
		data?.url && data.url.indexOf("?") > -1
			? data.url + "&sysCommunityId=" + companyId
			: data.url + "?sysCommunityId=" + companyId

	companyVisible.value = true
	backBtnState.value.name = "groupapproval"
	backBtnState.value.state = false
}
watchEffect(() => {
	if (backBtnState.value.name === "groupapproval" && backBtnState.value.state) {
		companyVisible.value = false
		backBtnState.value.name = ""
		backBtnState.value.state = false
	}
})

async function onDelete(data?: any) {
	await showWarnConfirm("确定移除该条报表信息吗？")
	await delReportInfo(data.id)
	getTableData()
}

function handleSubmit() {
	getTableData()
	editorVisible.value = false
}

/***
 *
 * 按钮权限
 *
 * **/
const view = computed(() => {
	return checkPermission("chart:branch:btn:preview" + companyId)
})
const viewShow = computed(() => {
	return isCheckPermission("chart:branch:btn:preview" + companyId)
})

const edit = computed(() => {
	return checkPermission("chart:branch:btn:edit" + companyId)
})
const editShow = computed(() => {
	return isCheckPermission("chart:branch:btn:edit" + companyId)
})

const deleteInfo = computed(() => {
	return checkPermission("chart:branch:btn:drop" + companyId)
})
const deleteShow = computed(() => {
	return isCheckPermission("chart:branch:btn:drop" + companyId)
})
</script>
<template>
	<div class="app-container" v-loading="tableLoading">
		<div class="content" v-show="!companyVisible">
			<model-frame class="mb10">
				<Query
					:queryArrList="queryArrList"
					@getQueryData="getQueryData"
					class="ml10"
				/>
			</model-frame>

			<model-frame class="table-model-frame">
				<Title
					:title="tableTitle"
					:button="(button as any)"
					@onBtnClick="onCreate()"
				/>
				<el-scrollbar style="padding-bottom: 30px" v-if="tableData.length > 0">
					<div v-for="(item, index) in tableData" :key="index">
						<!-- 表头 -->
						<div class="report-title">
							<span class="sub-title_text">{{ item.reportTreeName }}</span>
						</div>
						<!-- 卡片 -->
						<div class="group-approval_content" v-if="item.chirdren.length > 0">
							<div class="group-approval_list">
								<div
									class="group-approval_item"
									v-for="cur in item.chirdren"
									:key="cur.companyId"
								>
									<div class="group-approval">
										<img
											class="preview-warapper"
											w-full
											:src="getFileHttpUrl(cur.attachmentVo?.filePath)"
										/>
										<div class="group-approval_details">
											<el-tooltip
												v-if="cur.label.length > 17"
												class="box-item"
												effect="dark"
												:content="cur.label"
												placement="top-start"
											>
												<p class="group-approval_title">
													{{ cur.label.slice(0, 17) + "..." }}
												</p>
											</el-tooltip>
											<p v-else class="group-approval_title">
												{{ cur.label || "---" }}
											</p>
											<p>
												描述：
												<el-tooltip
													v-if="cur.description?.length > 17"
													class="box-item"
													effect="dark"
													:content="cur.description"
													placement="top-start"
												>
													<span class="remark">{{
														cur.description?.slice(0, 17) + "..."
													}}</span>
												</el-tooltip>
												<span v-else class="description">{{
													cur.description || "---"
												}}</span>
											</p>
											<p>
												所属公司：
												<span class="remark">{{
													cur.sysCommunityId_view || "---"
												}}</span>
											</p>
										</div>
									</div>
									<div class="group-approval_foot">
										<slot v-if="editShow || deleteShow || viewShow">
											<el-button
												v-btn
												link
												:disabled="edit"
												@click="onRowEdit(cur)"
												v-if="editShow"
											>
												<font-awesome-icon
													:icon="['fas', 'pen-to-square']"
													:class="edit ? 'disabled' : ''"
													style="color: var(--pitaya-btn-background)"
												/>
												<span
													class="table-inner-btn"
													:class="edit ? 'disabled' : ''"
													>编辑</span
												>
											</el-button>
											<el-button
												v-btn
												link
												:disabled="deleteInfo"
												@click="onDelete(cur)"
												v-if="deleteShow"
											>
												<font-awesome-icon
													:icon="['fas', 'trash-can']"
													:class="deleteInfo ? 'disabled' : ''"
													style="color: var(--pitaya-btn-background)"
												/>
												<span
													class="table-inner-btn"
													:class="deleteInfo ? 'disabled' : ''"
													>移除</span
												>
											</el-button>
											<el-button
												v-btn
												link
												:disabled="view"
												@click="onRowView(cur)"
												v-if="viewShow"
											>
												<font-awesome-icon
													:icon="['fas', 'eye']"
													:class="view ? 'disabled' : ''"
													style="color: var(--pitaya-btn-background)"
												/>
												<span
													class="table-inner-btn"
													:class="view ? 'disabled' : ''"
													>查看详情</span
												>
											</el-button>
										</slot>
										<slot v-else>---</slot>
									</div>
								</div>
							</div>
						</div>
						<div class="empty_content" v-else>
							<div class="empty_table">
								<EmptyData class="empty_img" />
								<p>未查询到相关数据</p>
							</div>
						</div>
					</div>
				</el-scrollbar>
				<div class="empty_content" v-else>
					<div class="empty_table">
						<EmptyData class="empty_img" />
						<p>未查询到相关数据</p>
					</div>
				</div>
			</model-frame>
		</div>
		<div v-if="companyVisible" class="content">
			<iframe
				:src="fineReportUrl"
				width="100%"
				height="1000"
				style="border: none"
			/>
		</div>
		<!--新建-->
		<Drawer
			class="drawer-hidden-box"
			:destroyOnClose="true"
			v-model:drawer="editorVisible"
			size="310"
			style="max-width: 95%"
		>
			<branch-editor
				:row="editRow"
				:mod="editType"
				@on-closed="editorVisible = false"
				@on-success="handleSubmit"
			/>
		</Drawer>
	</div>
</template>
<style lang="scss" scoped>
.empty_content {
	display: flex;
	justify-content: center;
	align-items: center;
}
.el-scrollbar {
	padding: 10px 0px;
	height: 80vh;
}
.empty_table {
	line-height: 1rem;
	padding-bottom: 40px;
	.empty_img {
		width: 150px;
	}
	p {
		color: #666666;
		font-size: 12px;
		text-align: center;
	}
}
.app-container.padding_none {
	padding: 0 !important;
}
.content {
	margin-top: 0;
	display: flex;
	flex-direction: column;
	.table-model-frame {
		flex: 1;
		//overflow-y: scroll;
		.report-title {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 10px 15px 0 10px;
			.sub-title_text {
				font-weight: 700;
				font-size: var(--pitaya-fs-16);
				color: var(--pitaya-table-font-color);
			}
		}
	}

	.group-approval_content {
		padding: 5px;

		.group-approval_list {
			display: flex;
			justify-content: flex-start;
			align-items: center;
			flex-wrap: wrap;

			.group-approval_item {
				width: 337px;
				margin: 5px 5px;
				padding: 15px;
				border: 1px solid var(--pitaya-border-color);

				.group-approval {
					display: flex;
					justify-content: flex-start;
					img {
						width: 40px;
						height: 40px;
						min-width: 40px;
						border-radius: 20px;
						margin-right: 10px;
						object-fit: cover;
					}

					.group-approval_title {
						width: 270px;
						font-size: var(--pitaya-fs-14);
						position: relative;
						color: #333;
						margin: 10px 0 15px 0;
					}
					p {
						max-width: 255px;
						// 隐藏溢出的文本
						// overflow: hidden;
						// 防止文本换行
						white-space: nowrap;
						// 文本内容溢出容器时，文本末尾显示省略号
						text-overflow: ellipsis;
					}
				}

				.group-approval_details {
					font-size: var(--pitaya-fs-12);
					color: #666;

					p {
						margin-bottom: 10px;
					}
					.remark {
						color: #999;
					}
				}

				.group-approval_foot {
					margin-top: 20px;
					padding-top: 10px;
					display: flex;
					justify-content: flex-end;
					align-items: flex-end;
					border-top: 1px solid var(--pitaya-border-color);
				}
			}
		}
	}
}
</style>
