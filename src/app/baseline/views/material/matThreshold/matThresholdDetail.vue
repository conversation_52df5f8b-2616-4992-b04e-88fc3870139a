<!-- 物资 - 相似物资阈值配置 查看详情 -->
<script setup lang="ts">
import { FormElementType } from "@/app/baseline/views/components/define"
import { onMounted, reactive, ref } from "vue"
import { getMaterialThresholdStatus } from "@/app/baseline/api/materialDict"

import { MaterialThresholdVo } from "@/app/baseline/utils/types/material-threshold"
import { getMaterialThresholdById } from "@/app/baseline/api/material/matThreshold"
import { useDictInit } from "@/app/baseline/views/components/dictBase"
import DictTag from "@/app/baseline/views/components/dictTag.vue"

export interface Props {
	id: string | number //
}

const props = defineProps<Props>()
const emits = defineEmits<{
	(e: "update"): void
	(e: "close"): void
}>()
const loading = ref(false)

const drawerLoading = ref(false)

const { getDictByCodeList } = useDictInit()

/**
 * 按钮 配置
 */
const formBtnListEdit = [{ name: "取消", icon: ["fas", "circle-minus"] }]

/**
 * 表单数据源
 */
const formModal = ref<MaterialThresholdVo>({})

/**
 * 左侧title 配置
 */
const titleConf = {
	name: ["查看相似阈值配置"],
	icon: ["fas", "square-share-nodes"]
}

/**
 * 左侧表单 配置
 */
const formEl = reactive<FormElementType[]>([
	{ label: "配置名称", name: "label" },
	{ label: "相似阈值", name: "thresholdNum" },
	{ label: "已达阈值的处理方式", name: "handMethod_view" },
	{ label: "状态", name: "status" },
	{ label: "配置说明", name: "remark" },
	{ label: "更新人", name: "lastModifiedBy_view" },
	{ label: "更新时间", name: "lastModifiedDate" }
])

const getDetail = () => {
	if (props.id) {
		drawerLoading.value = true
		getMaterialThresholdById(props.id as number)
			.then((res: any) => {
				formModal.value = { ...res }
			})
			.finally(() => {
				drawerLoading.value = false
			})
	}
}

onMounted(async () => {
	getDictByCodeList(["MATERIAL_NATURE"])
	getDetail()
})
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<!-- 左侧表单区域 -->
		<div class="drawer-column" style="width: 100%">
			<Title :title="titleConf" />
			<el-scrollbar class="rows">
				<el-descriptions size="small" :column="1" border class="content">
					<template>
						<el-descriptions-item
							v-for="(el, index) in formEl"
							label-align="center"
							:label="el.label"
							:key="index"
						>
							<span v-if="el.name === 'thresholdNum'">
								{{ formModal[el.name]! * 100 }}%
							</span>
							<span v-else-if="el.name === 'status'">
								<dict-tag
									:options="getMaterialThresholdStatus()"
									:value="formModal[el.name]!"
								/>
							</span>
							<span v-else>
								{{ formModal[el.name] ? formModal[el.name] : "---" }}
							</span>
						</el-descriptions-item>
					</template>
				</el-descriptions>
			</el-scrollbar>
			<ButtonList
				class="footer"
				:button="formBtnListEdit"
				:loading="loading"
				@on-btn-click="emits('close')"
			/>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
