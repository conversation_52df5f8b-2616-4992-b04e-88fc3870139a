<!-- 物资 - 相似物资阈值配置 新建/编辑 -->
<script setup lang="ts">
import { ElMessage, FormInstance, FormRules } from "element-plus"
import { IModalType } from "../../../utils/types/common"
import { onMounted, reactive, ref } from "vue"
import { getModalTypeLabel } from "@/app/baseline/utils"

import formElement from "../../components/formElement.vue"
import { inputMaxLength } from "@/app/baseline/utils/layout-config"
import { validateAndCorrectInput } from "../../../utils/validate"
import { useDictInit } from "../../components/dictBase"
import {
	addMaterialThreshold,
	getMaterialThresholdById,
	updateMaterialThreshold
} from "@/app/baseline/api/material/matThreshold"
import { MaterialThresholdVo } from "@/app/baseline/utils/types/material-threshold"
import { getBaselineToken } from "@/app/baseline/api/system"

const props = defineProps<{
	id?: any // id
	mode: IModalType // create || edit || create
}>()

const emits = defineEmits<{
	(e: "update"): void
	(e: "close"): void
}>()

const loading = ref(false)

const drawerLoading = ref(false)

const { dictOptions, getDictByCodeList } = useDictInit()

const canEditExtra = computed(() => Boolean(formData.value?.id || props.id))

const formRef = ref<FormInstance>()

/**
 * 按钮 配置
 */
const formBtnListEdit = [
	{ name: "取消", icon: ["fas", "circle-minus"] },
	{ name: "保存", icon: ["fas", "floppy-disk"] }
]

/**
 * 表单数据源
 */
const formData = ref<MaterialThresholdVo>({})

/**
 * 左侧title 配置
 */
const titleConf = computed(() => ({
	name: [getModalTypeLabel(props.mode, "相似阈值配置")],
	icon: ["fas", "square-share-nodes"]
}))

/**
 *表 单按钮 操作
 * @param btnName
 */
const onFormBtnList = async (btnName: string | undefined) => {
	if (btnName === "保存") {
		if (!formRef.value) {
			return
		}
		formRef.value?.validate(async (valid) => {
			if (!valid) {
				return
			}
			loading.value = true
			try {
				const api = canEditExtra.value
					? updateMaterialThreshold
					: addMaterialThreshold

				let idempotentToken = ""
				if (!canEditExtra.value) {
					idempotentToken = await getBaselineToken()
				}
				const res = await api(formData.value, idempotentToken)
				formData.value.id = res.id

				ElMessage.success("操作成功")
				emits("update")
				emits("close")
			} finally {
				loading.value = false
			}
		})
	}
}

/**
 * 表单配置
 */
const formElBase = computed(() => {
	return [
		[{ label: "配置名称", name: "label", maxlength: inputMaxLength.input }],
		/* [
			{
				label: "物资性质",
				name: "attribute",
				type: "select",
				data: dictOptions.value.MATERIAL_NATURE
			}
		], */
		[
			{
				label: "相似阈值",
				name: "thresholdNum",
				type: "number",
				append: "%",
				input: (val: number) => {
					formData.value.thresholdNum = validateAndCorrectInput(val)
				}
			}
		],
		[
			{
				label: "已达阈值的处理方式",
				name: "handMethod",
				type: "select",
				data: dictOptions.value.THRESHOLD_HAND_METHOD
			}
		],
		[
			{
				label: "配置说明",
				name: "remark",
				type: "textarea",
				rows: "5",
				maxlength: inputMaxLength.textarea
			}
		]
	]
})

/**
 * 表单校验
 */
const formRules = reactive<FormRules<typeof formData>>({
	label: [{ required: true, message: "配置名称不能为空", trigger: "change" }],
	/* attribute: [
		{ required: true, message: "物资性质不能为空", trigger: "change" }
	], */
	thresholdNum: [
		{ required: true, message: "相似阈值不能为空", trigger: "change" },
		{
			validator: (rule: any, value: any, callback: any) => {
				if (!rule.required && !value) {
					callback()
				} else {
					if (value > 100) {
						return callback(new Error("相似阈值不能大于100%"))
					}
					callback()
				}
			},
			required: true,
			trigger: "change"
		}
	],
	handMethod: [
		{ required: true, message: "处理方式不能为空", trigger: "change" }
	]
})

/**
 * 获取详情
 */
const getDetail = () => {
	if (props.id) {
		drawerLoading.value = true
		getMaterialThresholdById(props.id as number)
			.then((res: any) => {
				formData.value = {
					...res,
					thresholdNum: (res.thresholdNum * 100).toFixed(2)
				}
			})
			.finally(() => {
				drawerLoading.value = false
			})
	}
}

onMounted(async () => {
	getDictByCodeList(["THRESHOLD_HAND_METHOD"])
	getDetail()
})
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<!-- 左侧表单区域 -->
		<div class="drawer-column" style="width: 100%">
			<Title :title="titleConf" />
			<el-scrollbar class="rows">
				<el-form
					class="content"
					:model="formData"
					ref="formRef"
					label-position="top"
					label-width="100px"
					:rules="formRules"
				>
					<form-element :form-element="formElBase" :form-data="formData" />
				</el-form>
			</el-scrollbar>
			<ButtonList
				class="footer"
				:button="formBtnListEdit"
				:loading="loading"
				@on-btn-click="onFormBtnList"
			/>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
