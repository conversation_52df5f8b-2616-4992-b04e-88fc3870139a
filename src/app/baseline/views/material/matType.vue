<script lang="ts" setup>
import { ref, onMounted } from "vue"
import { MatTypeApi } from "@/app/baseline/api/material/matType"
import { ElMessage } from "element-plus"
import {
	checkPermission,
	isCheckPermission
} from "@/app/platform/utils/permission"
import TreeMatType from "@/app/baseline/views/components/v2/treeMatType.vue"
import MatTypeDrawer from "@/app/baseline/views/material/components/matTypeDrawer.vue"
import matTypePropertyEditor from "@/app/baseline/views/material/components/matTypePropertyEditor.vue"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import { DictApi, matStatus } from "@/app/baseline/api/dict"
import { powerList } from "@/app/baseline/views/components/define.d.ts"
import { useTbInit } from "@/app/baseline/views/components/tableBase.ts"
import { hasPermi } from "../../utils"
import { useMessageBoxInit } from "@/app/baseline/views/components/messageBox.ts"
import { modalSize } from "@/app/baseline/utils/layout-config"
const { showDelConfirm, showWarnConfirm } = useMessageBoxInit()
/*-------------------初始化表格-start-------------------*/
const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	tbBtnLoading,
	pageSize,
	currentPage,
	pageTotal,
	selectedTableList,
	fetchParam,
	tbBtns,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange,
	onDataSelected,
	onBtnClick
} = useTbInit()

tableProp.value = [
	{ label: "物资分类编码", prop: "code", width: 140, sortable: true },
	{ label: "物资分类名称", prop: "label", width: 200 },
	{
		label: "关联低值易耗类型",
		prop: "lowValueType",
		needSlot: true,
		width: 140
	},
	{
		label: "关联资产类型",
		prop: "propertyType",
		needSlot: true,
		width: 120
	},
	{ label: "分类状态", prop: "status", needSlot: true, width: 100 },
	{
		label: "备注说明",
		prop: "remark",
		needSlot: false,
		minWidth: 200,
		align: "left"
	},
	{ label: "创建人", prop: "createdBy_view", needSlot: false, width: 120 },
	{ label: "创建时间", prop: "createdDate", width: 160, sortable: true },
	{
		label: "操作",
		width: 130,
		prop: "operations",
		fixed: "right",
		needSlot: true
	}
]
const isAllElEqua = (array, value) => {
	return array.every((element) => element === value)
}
onDataSelected.value = (rowList: { [propName: string]: any }[]) => {
	selectedTableList.value = rowList
	const isNoSel = rowList.length <= 0
	const isMulti = rowList.length > 1
	const arrStatus = rowList.map((item) => item.status)

	tbBtns.value[0].disabled =
		!isAllElEqua(arrStatus, matStatus.drafted) || isNoSel
	tbBtns.value[1].disabled =
		!isAllElEqua(arrStatus, matStatus.normal) || isNoSel || isMulti
	tbBtns.value[2].disabled =
		!isAllElEqua(arrStatus, matStatus.freeze) || isNoSel || isMulti
	tbBtns.value[3].disabled =
		!isAllElEqua(arrStatus, matStatus.freeze) || isNoSel || isMulti

	emits("onDataSelected", rowList)
}
fetchFunc.value = MatTypeApi.getMatTypeList
tbBtns.value = [
	{
		name: "启用",
		roles: powerList.matCodeBtnStart,
		icon: ["fas", "power-off"],
		disabled: true,
		//confirm: true,
		click: () => handleMatStatus(matStatus.normal, "启用")
	},
	{
		name: "冻结",
		roles: powerList.matCodeBtnFreeze,
		icon: ["fas", "lock"],
		disabled: true,
		//confirm: true,
		click: () => handleMatStatus(matStatus.freeze, "冻结")
	},
	{
		name: "作废",
		roles: powerList.matCodeBtnVoid,
		icon: ["fas", "trash-can"],
		disabled: true,
		//confirm: true,
		click: () => handleMatStatus(matStatus.canceled, "作废")
	},
	{
		name: "解冻",
		roles: powerList.matCodeBtnThawing,
		icon: ["fas", "unlock-keyhole"],
		disabled: true,
		//confirm: true,
		click: () => handleMatStatus(matStatus.normal, "解冻")
	}
]

const handleMatStatus = async (status: string, msg: string) => {
	if (selectedTableList.value.length <= 0) {
		return ElMessage.warning("请选择物资分类")
	}

	await showWarnConfirm(`请确认是否${msg}此物资分类？`)

	// if (!checkStatus(selectedTableList, status)) return false
	const ids = selectedTableList.value.map((item) => item.id)

	tbBtnLoading.value = true
	try {
		const formData = new FormData()
		formData.append("ids", ids)
		formData.append("status", status)
		await MatTypeApi.updateStatusBatch(formData)
		ElMessage.success(msg + "成功")
		//fetchTableData()
		tableRef.value.clearSelectedTableData()
		if (status == matStatus.canceled) {
			treeMatTypeRef.value.resetTreeData()
		} else {
			setTimeout(() => {
				const treeRef = treeMatTypeRef.value?.treeRef

				const selectedData = treeRef.PitayaTreeRef?.getCurrentNode()
				const selectedNode = treeRef.PitayaTreeRef?.getNode(selectedData.id)
				selectedNode.loaded = false
				selectedNode.expand()
			}, 0)
		}
	} finally {
		tbBtnLoading.value = false
	}
}

/*-------------------初始化表格-end-------------------*/

interface props {
	model?: string //显示模式  view : 查看, edit : 编辑
	tableData: object[] //已选中物资列表
	singleSelect: boolean //是否单选
	selectDatas: any[] //已选中的ID列表
	typeTitle: string
	listTitle: string
	parentTypeIds: number[] | null //上级物资分类编码
	matchStatus: string[] | null //物资分类状态
	queryKeys: string[]
}

const props = withDefaults(defineProps<props>(), {
	model: "edit",
	singleSelect: false,
	selectDatas: undefined,
	parentTypeId: null,
	status: null,
	typeTitle: "",
	listTitle: "",
	matchStatus: undefined,
	queryKeys: undefined
})
const emits = defineEmits(["onDataSelected"])

const leftTitle = ref({
	name: ["编码分类"],
	icon: ["fas", "square-share-nodes"]
})

const rightTitle = ref<any>({
	name: [""],
	icon: ["fas", "square-share-nodes"]
})

const addBtn = [
	{
		name: "新增物资分类",
		icon: ["fas", "square-plus"],
		roles: powerList.matCodeBtnCreate
	}
]
// 编辑的回显
const tableEditDetail = ref<any>({})

//drawer
const drawerSize = 310
const showDrawer = ref<boolean>(false)

const editorPropertyVisible = ref(false)

// 当前点击的树节点信息
const currentClickTreeNode = ref<any>({ leafFlag: 0, code: "" })
const treeClick = (data: any, treeData: any, status: string) => {
	//更新table标题
	rightTitle.value.name = props.listTitle
		? [props.listTitle]
		: getTreeTitle(treeData, "label")
	currentClickTreeNode.value = data
	fetchParam.value.fid = data.id
	//fetchParam.value.status = status

	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchTableData()
}
const onAddBtn = (_btnName: string | undefined) => {
	const treeRef = treeMatTypeRef.value?.treeRef
	const selectedData = treeRef.PitayaTreeRef?.getCurrentNode()

	if (selectedData.status === matStatus.freeze) {
		return ElMessage.warning("父级分类是冻结状态，不能增加子分类")
	} else if (selectedData.status === matStatus.canceled) {
		return ElMessage.warning("父级分类是作废状态，不能增加子分类")
	} else if (selectedData.status === matStatus.drafted) {
		return ElMessage.warning("父级分类是草稿状态，不能增加子分类")
	}

	tableEditDetail.value = {}
	curModel.value = "edit"
	showDrawer.value = true
}
const treeMatTypeRef = ref<any>()
const onCloseDrawer = (refresh: boolean) => {
	if (refresh) {
		fetchTableData()
		setTimeout(() => {
			const treeRef = treeMatTypeRef.value?.treeRef

			const selectedData = treeRef.PitayaTreeRef?.getCurrentNode()
			const selectedNode = treeRef.PitayaTreeRef?.getNode(selectedData.id)
			selectedNode.loaded = false
			selectedNode.expand()
		}, 0)
	}
	showDrawer.value = false
}
const curModel = ref("edit")
const onRowEdit = (row: any) => {
	tableEditDetail.value = row
	curModel.value = "edit"
	showDrawer.value = true
}

/**
 * 状态为正常时  编辑操作
 * @param row
 */
function onRowPropertyEdit(row: any) {
	tableEditDetail.value = row
	curModel.value = "view"
	editorPropertyVisible.value = true
}

const onRowView = (row: any) => {
	tableEditDetail.value = row
	curModel.value = "view"
	showDrawer.value = true
}
const onRowDelete = (row: any) => {
	showDelConfirm.then(() => {
		MatTypeApi.deleteMatType(row.id)
			.then(() => {
				ElMessage.success("移除成功")
				fetchTableData()
				treeMatTypeRef.value.resetTreeData()
			})
			.catch(() => {
				ElMessage.error("移除失败")
			})
	})
}
const lowValueTypeDict = ref<any[]>([])
const propertyTypeDict = ref<any[]>([])

function getLowDict(): Promise<null> {
	return new Promise((resolve) => {
		DictApi.getDictByCode("LOW_VALUE_TYPE")
			.then((res) => {
				lowValueTypeDict.value = res
			})
			.finally(() => {
				resolve(null)
			})
	})
}

function getPropertyDict(): Promise<null> {
	return new Promise((resolve) => {
		DictApi.getDictByCode("ASSET_TYPE")
			.then((res) => {
				propertyTypeDict.value = res
			})
			.finally(() => {
				resolve(null)
			})
	})
}

onMounted(() => {
	fetchParam.value.sord = "desc"
	fetchParam.value.sidx = "createdDate"
	rightTitle.value.name = [props.listTitle]
	Promise.all([getLowDict(), getPropertyDict()]).then(() => {})
})

/**
 * 按时间排序
 * @param column
 * @param prop 字段名称
 * @param order ascending 升序 descending 降序
 */
function handleSortChange(column: any, prop: any, order: any) {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		sord:
			order === "descending" ? "desc" : order === "ascending" ? "asc" : "desc",
		sidx: order ? prop : "createdDate" // 排序字段
	}

	fetchTableData()
}
defineOptions({
	name: "MatTypeManagement"
})
</script>
<template>
	<div class="app-container">
		<ModelFrame class="left-frame">
			<TreeMatType
				ref="treeMatTypeRef"
				:title="leftTitle"
				@onTreeClick="treeClick"
				:need-check-box="false"
				:currentNodeKey="fetchParam.fid || 0"
				:needSingleSelect="props.singleSelect"
			/>
		</ModelFrame>
		<div class="right-frame">
			<ModelFrame class="whole-frame">
				<!--<div :class="props.model">-->
				<Title
					:title="rightTitle"
					:button="
						currentClickTreeNode.leafFlag != '1' &&
						currentClickTreeNode.code?.length < 8
							? addBtn
							: []
					"
					@onBtnClick="onAddBtn"
				/>

				<el-scrollbar>
					<PitayaTable
						ref="tableRef"
						:columns="tableProp"
						:table-data="tableData"
						:needSelection="true"
						:single-select="props.singleSelect"
						:selectedTableData="props.selectDatas"
						:need-index="true"
						:need-pagination="true"
						:total="pageTotal"
						@onSelectionChange="onDataSelected"
						@on-current-page-change="onCurrentPageChange"
						:table-loading="tableLoading"
						@on-table-sort-change="handleSortChange"
					>
						<template #propertyType="{ rowData }">
							<dict-tag
								:options="propertyTypeDict"
								:value="rowData.propertyType"
							/>
						</template>
						<template #lowValueType="{ rowData }">
							<dict-tag
								:options="lowValueTypeDict"
								:value="rowData.lowValueType"
							/>
						</template>
						<template #status="{ rowData }">
							<dict-tag
								:options="DictApi.getMatStatus()"
								:value="rowData.status"
							/>
						</template>
						<template #operations="{ rowData }">
							<slot
								rowData="rowData"
								name="operations"
								v-if="
									(isCheckPermission(powerList.matCodeBtnEdit) &&
										hasPermi(rowData.createdBy) &&
										rowData.status == matStatus.drafted) ||
									(isCheckPermission(powerList.matCodeBtnEdit) &&
										hasPermi(rowData.createdBy) &&
										rowData.leafFlag == '1' &&
										rowData.status == matStatus.normal) ||
									(isCheckPermission(powerList.matCodeBtnDrop) &&
										hasPermi(rowData.createdBy)) ||
									isCheckPermission(powerList.matCodeBtnPreview)
								"
							>
								<el-button
									v-btn
									link
									@click.stop="onRowEdit(rowData)"
									:disabled="checkPermission(powerList.matCodeBtnEdit)"
									v-if="
										isCheckPermission(powerList.matCodeBtnEdit) &&
										hasPermi(rowData.createdBy) &&
										rowData.status == matStatus.drafted
									"
								>
									<font-awesome-icon :icon="['fas', 'pen-to-square']" />
									<span class="table-inner-btn">编辑</span>
								</el-button>

								<!--
								1. 编辑权限
								2. 状态为正常，末级分类
								-->
								<el-button
									v-btn
									link
									@click.stop="onRowPropertyEdit(rowData)"
									:disabled="checkPermission(powerList.matCodeBtnEdit)"
									v-if="
										isCheckPermission(powerList.matCodeBtnEdit) &&
										hasPermi(rowData.createdBy) &&
										rowData.leafFlag == '1' &&
										rowData.status == matStatus.normal
									"
								>
									<font-awesome-icon :icon="['fas', 'pen-to-square']" />
									<span class="table-inner-btn">编辑</span>
								</el-button>

								<el-button
									v-btn
									link
									@click.stop="onRowDelete(rowData)"
									:disabled="checkPermission(powerList.matCodeBtnDrop)"
									v-if="
										isCheckPermission(powerList.matCodeBtnDrop) &&
										hasPermi(rowData.createdBy)
									"
								>
									<font-awesome-icon :icon="['fas', 'trash-can']" />
									<span class="table-inner-btn">移除</span>
								</el-button>

								<el-button
									v-btn
									link
									@click.stop="onRowView(rowData)"
									:disabled="checkPermission(powerList.matCodeBtnPreview)"
									v-if="isCheckPermission(powerList.matCodeBtnPreview)"
								>
									<font-awesome-icon :icon="['fas', 'eye']" />
									<span class="table-inner-btn">查看</span>
								</el-button>
							</slot>

							<slot v-else>---</slot>
						</template>
						<template #footerOperateLeft>
							<ButtonList
								class="btn-list"
								:is-not-radius="true"
								:button="tbBtns"
								:loading="tbBtnLoading"
								@on-btn-click="onBtnClick"
							/>
						</template>
					</PitayaTable>
				</el-scrollbar>

				<Drawer :size="drawerSize" v-model:drawer="showDrawer">
					<MatTypeDrawer
						v-if="showDrawer"
						:form-data="tableEditDetail"
						:current-node="currentClickTreeNode"
						:model="curModel"
						@on-close="onCloseDrawer"
					/>
				</Drawer>

				<Drawer
					:size="modalSize.lg"
					v-model:drawer="editorPropertyVisible"
					destroy-on-close
				>
					<matTypePropertyEditor
						:form-data="tableEditDetail"
						:current-node="currentClickTreeNode"
						:model="curModel"
						@close="editorPropertyVisible = false"
					/>
				</Drawer>

				<!--</div>-->
				<!--<slot name="options" :data="selectedTableList" />-->
			</ModelFrame>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
