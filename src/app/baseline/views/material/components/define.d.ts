/**
 * 返回数据
 */
export interface MatApplyObj {
	/**
	 * 物资性质ID
	 */
	attributeId: number
	/**
	 * 审批状态;0待提交，1审批中，2已审批，3已驳回
	 */
	bpmStatus: string
	/**
	 * 采购单位
	 */
	buyUnit: number
	/**
	 * 物资编码
	 */
	code: string
	/**
	 * 库存转换比例
	 */
	conversionUnit: number
	/**
	 * 创建人
	 */
	createBy: string
	/**
	 * 创建日期
	 */
	createdDate: Date
	/**
	 * 是否包含危险废物
	 */
	dangerousWasteFlag: string
	/**
	 * 预估采购单价
	 */
	evaluation: number
	/**
	 * 是否可维修
	 */
	fixFlag: string
	/**
	 * ID
	 */
	id: number
	/**
	 * 编码名称
	 */
	label: string
	/**
	 * 更新人
	 */
	lastModifiedBy: string
	/**
	 * 更新日期
	 */
	lastModifiedDate: Date
	/**
	 * 物资分类ID
	 */
	materialTypeId: number
	/**
	 * 图片
	 */
	photoName?: string
	/**
	 * 主要材质;{"铜":0.5，"铁":0.8}
	 */
	quality: string
	/**
	 * 是否可回收
	 */
	recoveryFlag: string
	/**
	 * 物资编码状态;0正常 1作废 2冻结 3新增
	 */
	status: string
	/**
	 * 所属公司ID
	 */
	sysCommunityId?: number
	/**
	 * 所属部门ID
	 */
	sysOrgId?: number
	/**
	 * 技术参数
	 */
	technicalParameter: string
	/**
	 * 库存单位
	 */
	useUnit: number
	/**
	 * 规格型号
	 */
	version: string
	/**
	 * 废旧物资类型
	 */
	wasteMaterialTypeId: number
	/**
	 * 物资分类编码
	 */
	materialTypeCode: string
	/**
	 * 物资分类名称
	 */
	materialTypeLabel: string
	/**
	 * 物资编码数量（采购订单里有多少种物资）
	 */
	matCodeNum: number
	/**
	 *  物资数量（采购订单里物资一共多少个）
	 */
	matNum: number
	/**
	 *  预估采购金额（总额）
	 */
	purchaseAmount: number
}

export interface ApprovedTasks {
	/**
	 * 发起者
	 */
	applyer: string
	assignee: string
	description: null
	formData: {
		agree?: boolean
		applyer: string
		taskId: string
		taskTitle: string
	}
	name: string
	/**
	 * 流程定义ID
	 */
	processDefinitionId: string
	/**
	 * 流程名称
	 */
	processDefinitionName: string
	/**
	 * 业务标题
	 */
	processDefinitionTitle: string
	/**
	 * 流程实例ID
	 */
	processInstanceId: string
	taskDefinitionKey: null
	taskId: string
	/**
	 * 耗时
	 */
	timeConsuming: string
	time: null | string
	label: null | string
	content: null | string
	/**
	 * 结束时间
	 */
	timeEnd: null | string
	/**
	 * 开始时间
	 */
	timeStart: string
	version: number
	icon?: string
	status?: string
}
/**
 * MaterialProcureInfoVO
 */
export interface MaterialProcureInfo {
	/**
	 * 创建人
	 */
	createBy: string
	/**
	 * 创建日期
	 */
	createdDate: Date
	/**
	 * 预估采购单价
	 */
	evaluation?: number
	/**
	 * 预估采购周期
	 */
	evaluationCycle?: number
	/**
	 * ID
	 */
	id: number
	/**
	 * 更新人
	 */
	lastModifiedBy: string
	/**
	 * 更新日期
	 */
	lastModifiedDate: Date
	/**
	 * 物资ID
	 */
	materialId: number
	/**
	 * 最小订货数量
	 */
	minBuyNum?: number
	/**
	 * 安全库存
	 */
	safeStock?: number
	/**
	 * 所属公司id
	 */
	sysCommunityId?: number
	/**
	 * 库存单位
	 */
	useUnit?: string

	/**
	 * 采购单位
	 */
	buyUnit?: string
	disabled?: boolean
}
