<template>
	<div v-loading="tableLoading">
		<el-scrollbar
			v-if="tableData && tableData.length > 0"
			:max-height="`${isSmall ? props.maxHeight : maxTableHeight - 40}px`"
			style="height: auto"
		>
			<div class="similar-img-box">
				<div
					class="similar-img-item"
					v-for="(item, index) in tableData"
					:key="index"
					@click="handleViewAction(item)"
				>
					<div class="similar-detail-goods-img-wrapper">
						<el-image
							:src="FileApi.buildPreviewUrl() + (item?.photoName ?? '')"
							:class="`similar-detail-goods-img${isSmall ? '__small' : ''}`"
							style="vertical-align: middle; max-height: 198px"
							fit="scale-down"
						/>
						<el-tag type="warning" class="similar-detail-goods-tag">
							相似度{{ ((item.similarityRate as any) * 100).toFixed(2) }}%
						</el-tag>
					</div>
					<div class="right-desc-box">
						<div class="right-desc-box_row-item">
							<div class="right-desc-box_row-item_label">物资编码</div>
							<div class="right-desc-box_row-item_content">
								{{ item.code }}
							</div>
						</div>
						<div class="right-desc-box_row-item">
							<div class="right-desc-box_row-item_label">物资名称</div>
							<div class="right-desc-box_row-item_content">
								<el-tooltip
									effect="dark"
									:content="item?.label"
									:disabled="getRealLength(item.label!) <= singleLen ? true : false"
								>
									{{
										getRealLength(item.label!) > singleLen
											? setString(item.label!, singleLen)
											: item?.label || "---"
									}}
								</el-tooltip>
							</div>
						</div>
						<div class="right-desc-box_row-item">
							<div class="right-desc-box_row-item_label">规格型号</div>
							<div class="right-desc-box_row-item_content">
								<el-tooltip
									effect="dark"
									:content="item?.version"
									:disabled="getRealLength(item.version!) <= singleLen ? true : false"
								>
									{{
										getRealLength(item.version!) > singleLen
											? setString(item.version!, singleLen)
											: item?.version || "---"
									}}
								</el-tooltip>
							</div>
						</div>
						<div class="right-desc-box_row-item">
							<div class="right-desc-box_row-item_label">技术参数</div>
							<div class="right-desc-box_row-item_content3">
								<div>
									<el-tooltip
										effect="dark"
										:content="item?.technicalParameter"
										:disabled="getRealLength(item.technicalParameter!) <= multLen ? true : false"
									>
										{{
											getRealLength(item.technicalParameter!) > multLen
												? setString(item.technicalParameter!, multLen) //item.technicalParameter!.substring(0, multLen) + "..."
												: item?.technicalParameter || "---"
										}}
									</el-tooltip>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</el-scrollbar>

		<el-scrollbar v-else>
			<div class="empty-table">
				<EmptyData class="empty_img" />
				<p>未查询到相关数据</p>
			</div>
		</el-scrollbar>

		<div
			class="footer-operate-pagination"
			v-if="tableData && tableData.length > 0"
		>
			<el-pagination
				@current-change="currentPagechange"
				@size-change="handleSizeChange1"
				v-model:current-page="paginationData.currentPage"
				v-model:page-size="paginationData.pageSize"
				:total="total"
				:page-sizes="[10, 20, 30, 40]"
				:layout="`prev, pager, next, sizes, total, jumper, slot`"
				:pager-count="5"
				prev-text="上一页"
				next-text="下一页"
			>
				<button class="jumper-slot-btn">GO</button>
			</el-pagination>
		</div>
	</div>

	<!-- 详情 -->
	<Drawer
		:size="modalSize.ssm"
		v-model:drawer="similarDetailVisible"
		:destroyOnClose="true"
	>
		<table-similar-type-detail
			:row="editTableRow"
			@close="similarDetailVisible = false"
		/>
	</Drawer>
</template>
<script setup lang="ts">
import { modalSize } from "@/app/baseline/utils/layout-config"
import { MatApplyApi } from "@/app/baseline/api/material/matApply"
import { usePagination } from "@/app/platform/hooks/usePagination"
import { FileApi } from "@/app/baseline/api/file"
import tableSimilarTypeDetail from "./tableSimilarTypeDetail.vue"
import {
	IShowType,
	ISimilarityType,
	SimilarityMaterialCodeVo
} from "@/app/baseline/utils/types/material"
import EmptyData from "@/assets/images/empty/empty_data.svg?component"
import { maxTableHeight } from "@/app/baseline/utils"
import { pick, omit } from "lodash-es"
import { getRealLength, setString } from "@/app/baseline/utils/validate"

const props = withDefaults(
	defineProps<{
		id: number
		/**
		 * table 请求参数
		 */
		tableReqParams?: any

		similarityType?: ISimilarityType

		isSmall?: boolean
		maxHeight?: number
	}>(),
	{
		tableReqParams: () => ({}),
		similarityType: () => ISimilarityType.default,
		isSmall: false
	}
)

const singleLen = computed(() => {
	return props.isSmall ? 25 : 32
})

const multLen = computed(() => {
	return props.isSmall ? 50 : 80
})
const tableLoading = ref(false)
const { paginationData, handleSizeChange, handleCurrentChange } =
	usePagination()

const pageSize = computed(() => {
	return paginationData.pageSize
})
const currentPage = computed(() => {
	return paginationData.currentPage
})

const total = ref<any>(0)
const tableData = ref<SimilarityMaterialCodeVo[]>()
const currentPagechange = (value: number) => {
	handleCurrentChange(value)

	getList()
}

const handleSizeChange1 = (value: number) => {
	handleSizeChange(value)
	handleCurrentChange(1)
	getList()
}

async function getList() {
	/* 	if (
		!props.tableReqParams.photoName &&
		props.similarityType === ISimilarityType.otherSimilar
	) {
		return ElMessage.warning("请先上传物资图片")
	} */
	tableLoading.value = true
	try {
		const params =
			props.similarityType === ISimilarityType.otherSimilar
				? {
						...pick(props.tableReqParams, "photoName"),
						filteredId: props.id,
						showType: IShowType.img,
						similarityType: props.similarityType,
						pageSize: pageSize.value,
						currentPage: currentPage.value
				  }
				: {
						...omit(props.tableReqParams, "photoName"),
						filteredId: props.id,
						showType: IShowType.img,
						similarityType: props.similarityType,
						pageSize: pageSize.value,
						currentPage: currentPage.value
				  }
		const res = await MatApplyApi.getSimilarityMatPaged(params as any)

		total.value = res.records
		tableData.value = res.rows as SimilarityMaterialCodeVo[]
	} finally {
		tableLoading.value = false
	}
}

defineExpose({ pageTotal: total, getTableData: getList })
onMounted(() => {
	/* getList() */
})
/**
 * 物资明细配置
 */
const descConf = [
	{ label: "物资编码", key: "code" },
	{ label: "物资名称", key: "label" },
	{ label: "规格型号", key: "version" },
	{ label: "技术参数", key: "technicalParameter" }
]

const editTableRow = ref({})
const similarDetailVisible = ref(false)

/**
 * 查看详情 操作
 */
function handleViewAction(e: Record<string, any>) {
	editTableRow.value = { ...e }
	similarDetailVisible.value = true
}
</script>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.empty-table {
	text-align: center;
	line-height: 0rem;
	padding-bottom: 40px;
	.empty_img {
		width: 150px;
	}
	p {
		color: #666666;
		font-size: 12px;
		text-align: center;
	}
}
.similar-img-box {
	display: flex;
	justify-content: space-between;
	flex-wrap: wrap;
	height: calc(100% - 30px);
	padding: 0 10px;
	.similar-img-item {
		display: flex;
		cursor: pointer;
		flex-direction: row;
		justify-content: space-between;
		margin: 10px 0;
		height: 200px;
		width: 48%;
		border: 1px solid #ddd;
		overflow: hidden;

		.similar-detail-goods-img {
			&-wrapper {
				padding: 0;
				/* width: 200px; */
				align-content: space-around;
				.similar-detail-goods-tag {
					position: absolute;
					top: 0px;
					left: 0px;
					border-radius: 0 !important;
				}
			}

			/* width: 100%; */
			width: 198px;
			height: 268px;

			vertical-align: middle;
			/* border: 1px solid #ccc; */
			border-bottom: 0;

			&__small {
				width: 120px;
				max-height: 268px;
				align-content: space-around;
				//height: 198px;
			}
		}

		.right-desc-box {
			display: flex;
			flex-direction: column;
			flex: 1;
			font-size: 12px;
			border-left: 1px solid #ddd;
			overflow: hidden;

			&_row-item {
				display: flex;
				height: 30px;
				line-height: 30px;
				border-bottom: 1px solid #ddd;

				&_label {
					width: 100px;
					border-right: 1px solid #ddd;
					text-align: center;
				}
				&_content {
					padding-left: 5px;
					padding-right: 5px;
					width: 100%;
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
				}
				&_content3 {
					padding-left: 10px;
					padding-right: 10px;
					width: 100%;
					overflow: hidden;
					/*.desc-text {
						display: -webkit-box;
						overflow: hidden;
						text-overflow: ellipsis;
						-webkit-line-clamp: 3;
						-webkit-box-orient: vertical;
					}*/
				}
				&:last-child {
					flex: 1;
					border-bottom: none;
				}
			}
		}
	}
}
.footer-operate-pagination {
	padding-top: 10px;
	display: flex;
	align-items: center;
	justify-content: flex-end;
	:deep(.el-pagination__total) {
		height: 32px;
		padding: 1px 10px;
		margin-left: 0px;
		border: 1px solid #dcdfe6;
		border-left: none;
		font-size: var(--pitaya-fs-12);
		display: flex;
		align-items: center;
	}

	.jumper-slot-btn {
		display: flex;
		align-items: center;
		justify-content: center;
		border: 1px solid var(--pitaya-border-color);
		border-left: none;
		width: 32px;
		height: 32px;
		font-size: var(--pitaya-fs-12);
		color: #fff;
		background-color: var(--pitaya-btn-background);
		border-radius: 0;
		cursor: pointer;
		&:hover {
			background-color: var(--pitaya-hover-btn-background);
		}
		&:active {
			background-color: var(--pitaya-active-btn-background);
		}
	}
	:deep(.el-input__wrapper) {
		padding: 1px 10px;
	}
	:deep(.el-pager) {
		.number,
		.more {
			border: 1px solid var(--pitaya-border-color);
			//border-left: none;
			border-radius: 0;
		}
		.is-active {
			background-color: #0a4e9a;
			color: #fff;
			border-color: #0a4e9a;
		}
	}
	:deep(.el-pager) {
		li {
			font-size: var(--pitaya-fs-12);
		}
	}
	:deep(.btn-prev),
	:deep(.btn-next) {
		height: 32px;
		width: 70px;
		text-align: center;
		line-height: 32px;
		border: 1px solid var(--pitaya-btn-background) !important;
		background-color: var(--pitaya-btn-background);
		color: #fff;
		border-radius: 0;
		span {
			font-size: var(--pitaya-fs-12);
		}
		&:hover {
			background-color: var(--pitaya-hover-btn-background);
		}
		&:active {
			background-color: var(--pitaya-active-btn-background);
		}
		&:disabled {
			border: 1px solid var(--pitaya-border-color) !important;
		}
	}
	:deep(.btn-next) {
		border-left: none !important;
	}
	:deep(.btn-prev) {
		border-right: none !important;
	}
	:deep(.el-pagination__sizes) {
		margin-left: 10px;
		.el-input {
			width: 100px;
			font-size: var(--pitaya-fs-12);
		}
		.el-input__wrapper {
			border-radius: 0px;
		}
	}
	:deep(.el-pagination__jump) {
		margin-left: 10px;
		.el-pagination__goto,
		.el-pagination__classifier {
			display: none;
		}
		.el-input__wrapper {
			padding: 0 10px;
			border-radius: 0px;
			box-shadow: none;
			border: 1px solid var(--pitaya-border-color);
			//border-right: none;
		}
	}
}
</style>
