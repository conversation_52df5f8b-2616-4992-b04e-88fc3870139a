<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<!-- 物资信息 -->
		<div class="drawer-column">
			<Title :title="titleConf" />
			<el-scrollbar class="rows">
				<!-- 物资图片展示 -->
				<div class="similar-detail-goods-img-wrapper">
					<el-image :src="coverUrl" class="similar-detail-goods-img" />

					<el-tag type="warning" class="similar-detail-goods-tag">
						相似度{{ (descData.similarityRate * 100).toFixed(2) }}%
					</el-tag>
				</div>
				<el-descriptions
					size="small"
					:column="1"
					border
					class="content"
					style="padding-top: 0"
				>
					<el-descriptions-item
						v-for="desc in descConf"
						:key="desc.label"
						:label="desc.label"
					>
						<span v-if="desc.key === 'useUnit' || desc.key === 'buyUnit'">
							{{
								dictFilter("INVENTORY_UNIT", descData?.[desc.key])
									?.subitemName || "---"
							}}
						</span>
						<span v-else-if="desc.key === 'quality'">
							{{
								dictFilter("MAIN_MATERIALS", descData?.[desc.key])
									?.subitemName || "---"
							}}
						</span>
						<span v-else-if="desc.key === 'attribute'">
							<dict-tag
								:options="DictApi.getMatAttr()"
								:value="descData?.[desc.key]"
							/>
						</span>
						<span v-else-if="desc.key === 'evaluation'">
							<cost-tag :value="descData?.[desc.key]" />
						</span>
						<span v-else-if="desc.needTooltip">
							<el-tooltip
								effect="dark"
								:content="descData?.[desc.key]"
								:disabled="
									getRealLength(descData?.[desc.key]) <= 100 ? true : false
								"
							>
								{{
									getRealLength(descData?.[desc.key]) > 100
										? setString(descData?.[desc.key], 100)
										: descData?.[desc.key] || "---"
								}}
							</el-tooltip>
						</span>
						<span v-else>
							{{ descData?.[desc.key] || "---" }}
						</span>
					</el-descriptions-item>
				</el-descriptions>
			</el-scrollbar>
			<ButtonList
				class="footer"
				:button="formBtnList"
				@on-btn-click="emit('close')"
			/>
		</div>
	</div>
</template>
<script setup lang="ts">
import { FileApi } from "@/app/baseline/api/file"
import { useDictInit } from "../../../components/dictBase"
import CostTag from "@/app/baseline/views/components/costTag.vue"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import { DictApi } from "@/app/baseline/api/dict"
import { SimilarityMaterialCodeVo } from "@/app/baseline/utils/types/material"
import { Picture as IconPicture } from "@element-plus/icons-vue"
import { getRealLength, setString } from "@/app/baseline/utils/validate"

const { dictFilter, getDictByCodeList } = useDictInit()

const props = defineProps<{
	row: SimilarityMaterialCodeVo
}>()

const emit = defineEmits<{ (e: "close"): void }>()

const drawerLoading = ref(false)

const titleConf = computed(() => {
	return {
		name: [`${descData.value.label}`],
		icon: ["fas", "square-share-nodes"]
	}
})

/**
 * 按钮 配置
 */
const formBtnList = [{ name: "取消", icon: ["fas", "circle-minus"] }]

/**
 * 物资明细配置
 */
const descConf = [
	{ label: "物资分类编码", key: "materialTypeCode" },
	{ label: "物资分类名称", key: "materialTypeLabel" },
	{ label: "物资编码", key: "code" },
	{ label: "物资名称", key: "label" },
	{ label: "物资性质", key: "attribute" },
	{ label: "规格型号", key: "version" },
	{ label: "技术参数", key: "technicalParameter", needTooltip: true },
	{ label: "主要材质", key: "quality" },
	{ label: "采购单位", key: "buyUnit" },
	{ label: "库存转换比例", key: "conversionUnit" },
	{ label: "库存单位", key: "useUnit" },
	{ label: "预估采购单价", key: "evaluation" },
	{ label: "申请公司", key: "sysCommunityId_view" },
	{ label: "申请人", key: "createdBy_view" },
	{ label: "申请时间", key: "createdDate" }
]

/**
 * 编码明细数据
 */
const descData = ref<Record<string, any>>({})

/**
 * 封面
 */
const coverUrl = computed(() => {
	return FileApi.buildPreviewUrl() + (descData?.value.photoName ?? "")
})

onMounted(() => {
	descData.value = { ...props.row }
	getDictByCodeList(["INVENTORY_UNIT", "MAIN_MATERIALS"])
})
</script>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.similar-detail-goods-img {
	&-wrapper {
		margin: 10px 10px 0;
		text-align: center;
		border: 1px solid #ccc;
		border-bottom: 0;
		height: 268px;

		.similar-detail-goods-tag {
			position: absolute;
			top: 0px;
			left: 0px;
			border-radius: 0 !important;
		}
	}
	width: 100%;
	max-height: 268px;

	vertical-align: middle;
}
.error-img {
	width: 100%;
	height: 268px;
}

.el-image {
	max-width: 100%;
	max-height: 268px;
	width: auto;
	overflow: hidden;
	/* margin: auto; */ /* 居中对齐 */
	::v-deep(.el-image__inner) {
		max-width: 100% !important;
		max-height: 268px !important;
		vertical-align: middle !important;
	}
	::v-deep(.el-image__error) {
		height: 268px;
		width: 320px;
	}
}
</style>
