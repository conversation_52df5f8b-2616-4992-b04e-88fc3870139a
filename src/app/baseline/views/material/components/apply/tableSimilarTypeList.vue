<template>
	<PitayaTable
		ref="tableRef"
		:columns="tableProp"
		:tableData="tableData"
		:total="pageTotal"
		:need-index="true"
		:single-select="true"
		:need-selection="false"
		@onSelectionChange="onDataSelected"
		@on-current-page-change="onCurrentPageChange"
		:table-loading="tableLoading"
		:customize-height-number="customizeHeightNumber"
	>
		<!-- :max-height="props.maxHeight || maxTableHeight" -->
		<template #similarityRate="{ rowData }">
			<!-- 是否标红 1-是 0-否 （有阈值且相似度大于阈值时，是1，否则是0） -->
			<span v-if="rowData.redFalg" style="color: #f00">
				{{ (rowData.similarityRate * 100).toFixed(2) }}%
			</span>

			<span v-else>{{ (rowData.similarityRate * 100).toFixed(2) }}%</span>
		</template>
		<template #operations="{ rowData }">
			<el-button v-btn link @click="handleViewAction(rowData)">
				<font-awesome-icon :icon="['fas', 'eye']" />
				<span class="table-inner-btn">查看</span>
			</el-button>
		</template>
	</PitayaTable>

	<!-- 详情 -->
	<Drawer
		:size="modalSize.ssm"
		v-model:drawer="similarDetailVisible"
		:destroyOnClose="true"
	>
		<table-similar-type-detail
			:row="editTableRow"
			@close="similarDetailVisible = false"
		/>
	</Drawer>
</template>
<script setup lang="ts">
import { modalSize } from "@/app/baseline/utils/layout-config"
import { MatApplyApi } from "@/app/baseline/api/material/matApply"
import { useTbInit } from "@/app/baseline/views/components/tableBase"
import tableSimilarTypeDetail from "./tableSimilarTypeDetail.vue"
import { maxTableHeight } from "@/app/baseline/utils"
import {
	IShowType,
	ISimilarityType,
	SimilarityMaterialCodeVo,
	SimilarityMaterialCodeVoRequest
} from "@/app/baseline/utils/types/material"
import { omit } from "lodash-es"

const props = withDefaults(
	defineProps<{
		id: number
		/**
		 * table 请求参数
		 */
		tableReqParams?: any

		similarityType?: ISimilarityType

		maxHeight?: number

		offsetHeight?: number
	}>(),
	{ tableReqParams: () => ({}), similarityType: ISimilarityType.default }
)

const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	fetchTableData,
	fetchParam,
	fetchFunc,
	onCurrentPageChange,
	onDataSelected
} = useTbInit<SimilarityMaterialCodeVo, SimilarityMaterialCodeVoRequest>()

tableProp.value = [
	{ label: "物资分类编码", prop: "materialTypeCode", width: 100 },
	{ label: "物资分类名称", prop: "materialTypeLabel", width: 150 },
	{ label: "物资编码", prop: "code", width: 130 },
	{ label: "物资名称", prop: "label", width: 200 },
	{ label: "规格型号", prop: "version", width: 150 },
	{
		label: "技术参数",
		prop: "technicalParameter",
		minWidth: 200,
		align: "left"
	},
	{ label: "申请公司", prop: "sysCommunityId_view", width: 120 },
	{ label: "申请人", prop: "createdBy_view", width: 120 },
	{ label: "申请时间", prop: "createdDate", width: 150 },
	{
		label: "相似度",
		prop: "similarityRate",
		needSlot: true,
		align: "right",
		fixed: "right",
		width: 100
	},
	{
		label: "操作",
		width: 80,
		prop: "operations",
		fixed: "right",
		needSlot: true
	}
]

fetchFunc.value = MatApplyApi.getSimilarityMatPaged

defineExpose({
	pageTotal,
	getTableData: async () => {
		fetchParam.value = {
			...omit(props.tableReqParams, "photoName"),
			showType: IShowType.list,
			similarityType: props.similarityType,
			filteredId: props.id
		}
		fetchTableData()
	}
})

const customizeHeightNumber = ref(-168)
watch(
	() => props.offsetHeight,
	() => {
		if (props.offsetHeight) {
			customizeHeightNumber.value = Math.abs(props.offsetHeight) * -1
		}
	}
)

onMounted(() => {
	console.log("similarityType", props.similarityType)
	/* if (props.id) {
		fetchParam.value = {
			...omit(props.tableReqParams, "photoName"),
			showType: IShowType.list,
			similarityType: props.similarityType,
			filteredId: props.id
		}

		fetchTableData()
	} */
})

const editTableRow = ref({})
const similarDetailVisible = ref(false)

/**
 * 查看详情 操作
 */
function handleViewAction(e: Record<string, any>) {
	editTableRow.value = { ...e }
	similarDetailVisible.value = true
}
</script>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
