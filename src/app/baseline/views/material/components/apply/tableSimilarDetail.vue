<template>
	<div class="drawer-box">
		<div class="drawer-top">
			<span class="txt" v-if="similarShowType === IShowType.list">
				输入物资名称、规格型号、技术参数后，系统运用语议分析计算，展示超过相似阈值的相似物资！
				<!-- 共查询到{{ tableSimilarRef?.pageTotal }}条相似物资记录 -->
			</span>
			<span class="txt" v-else-if="similarShowType === IShowType.img">
				拍摄图片需遵守【拍摄指南】，经pHash算法与Java深度学习处理，呈现超过相似阈值的相似物资！
				<!-- 共查询到{{ tableSimilarRef?.pageTotal }}条相似物资记录 -->
			</span>
			<el-radio-group v-model="similarShowType">
				<el-radio-button :label="IShowType.list">
					<font-awesome-icon :icon="['fas', 'list']" />
					列表
				</el-radio-button>
				<el-radio-button :label="IShowType.img">
					<font-awesome-icon :icon="['fas', 'image']" />
					图片
				</el-radio-button>
			</el-radio-group>
		</div>
		<el-scrollbar v-if="similarShowType === IShowType.list" class="rows">
			<table-similar-type-list
				ref="tableSimilarRef"
				:id="props.id"
				:similarityType="props.similarityType"
				:table-req-params="props.tableReqParams"
			/>
		</el-scrollbar>

		<table-similar-type-img
			v-else-if="similarShowType === IShowType.img"
			ref="tableSimilarRef"
			:id="props.id"
			:similarityType="props.similarityType"
			:table-req-params="props.tableReqParams"
		/>
	</div>
</template>
<script setup lang="ts">
import tableSimilarTypeList from "./tableSimilarTypeList.vue"
import tableSimilarTypeImg from "./tableSimilarTypeImg.vue"
import { IShowType, ISimilarityType } from "@/app/baseline/utils/types/material"

const props = withDefaults(
	defineProps<{
		/**
		 * 物资Id
		 */
		id: number
		/**
		 * table 请求参数
		 */
		tableReqParams?: any

		similarityType?: ISimilarityType
	}>(),
	{ tableReqParams: () => ({}), similarityType: () => ISimilarityType.default }
)
const tableSimilarRef = ref()

const similarShowType = ref(IShowType.list)

watch(
	() => similarShowType.value,
	() => {
		setTimeout(() => {
			tableSimilarRef.value.getTableData()
		})
	}
)

defineExpose({
	getTableData: () => tableSimilarRef.value.getTableData(),
	similarShowType
})
</script>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.drawer-box {
	height: calc(100% - 20px);
	display: flex;
	flex-direction: column;
	/* align-items: center;
	justify-content: row; */
	font-size: 12px;
	padding: 10px 0px;
	.drawer-top {
		display: flex;
		flex-direction: row;
		align-items: center;
		height: 30px;
		padding: 0 10px;
		justify-content: space-between;
		.txt {
			font-size: 12px;
			color: #666;
		}
	}
}
</style>
