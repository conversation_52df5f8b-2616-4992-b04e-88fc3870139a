<template>
	<div class="drawer-container">
		<!-- 物资信息 -->
		<div class="drawer-column">
			<Title :title="titleConf" />

			<el-scrollbar class="drawer-column-content">
				<div
					class="drawer-column-content_item"
					v-for="desc in descOptions"
					:key="desc.title"
					:label="desc.title"
				>
					<p>
						<el-text>{{ desc.title }}</el-text>
					</p>
					<p>
						<el-text size="small">{{ desc.desc }}</el-text>
					</p>

					<div class="photo-warp">
						<div class="photo-item">
							<div class="photo-item_img">
								<el-image
									:src="desc.urlCheck"
									style="max-width: 100%; max-height: 100%"
								/>
							</div>

							<span class="photo-item-icon">
								<font-awesome-icon
									:icon="['fas', 'check-circle']"
									style="color: var(--el-color-success)"
								/>
							</span>
						</div>

						<div class="photo-item">
							<div class="photo-item_img">
								<el-image
									:src="desc.urlClose"
									style="max-width: 60%; max-height: 100%"
									v-if="desc.isSpecial"
								/>
								<el-image
									:src="desc.urlClose"
									style="max-width: 100%; max-height: 100%"
									v-else
								/>
							</div>

							<span class="photo-item-icon">
								<font-awesome-icon
									:icon="['fas', 'times-circle']"
									style="color: var(--el-color-danger)"
								/>
							</span>
						</div>
					</div>
				</div>
			</el-scrollbar>

			<button-list
				class="footer"
				:button="[
					{
						name: '取消',
						icon: ['fas', 'circle-minus']
					}
				]"
				@on-btn-click="emit('close')"
			/>
		</div>
	</div>
</template>
<script setup lang="ts">
import jpg11 from "@/app/baseline/assets/image/material/1-1.jpg"
import jpg12 from "@/app/baseline/assets/image/material/1-2.jpg"
import jpg21 from "@/app/baseline/assets/image/material/2-1.jpg"
import jpg22 from "@/app/baseline/assets/image/material/2-2.jpg"
import jpg31 from "@/app/baseline/assets/image/material/3-1.png"
import jpg32 from "@/app/baseline/assets/image/material/3-2.png"
import jpg41 from "@/app/baseline/assets/image/material/4-1.png"
import jpg42 from "@/app/baseline/assets/image/material/4-2.png"
import jpg51 from "@/app/baseline/assets/image/material/5-1.png"
import jpg52 from "@/app/baseline/assets/image/material/5-2.jpg"
const emit = defineEmits<{
	(e: "close"): void
}>()

const titleConf = {
	name: ["拍摄指南"],
	icon: ["fas", "square-share-nodes"]
}

const descOptions = computed(() => [
	{
		title: "1、拍摄比例",
		desc: "使用相机设置为1:1的比例进行拍摄",
		urlCheck: jpg11,
		urlClose: jpg12,
		isSpecial: true
	},
	{
		title: "2、拍摄背景",
		desc: "选取简洁、纯色的背景，以减少背景杂乱对主体的干扰",
		urlCheck: jpg21,
		urlClose: jpg22
	},
	{
		title: "3、单独拍摄",
		desc: "每次仅拍摄一个物资，避免多个物资同时拍摄",
		urlCheck: jpg31,
		urlClose: jpg32
	},
	{
		title: "4、角度和距离",
		desc: "保持正面角度拍摄，确保物资居中且突出，避免物资显得过小或偏离中心",
		urlCheck: jpg41,
		urlClose: jpg42
	},
	{
		title: "5、拍摄光线",
		desc: "优先使用自然光，避免在强光、阴影或反光条件下拍摄",
		urlCheck: jpg51,
		urlClose: jpg52
	}
])
</script>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.drawer-column {
	width: 100%;
	&-content {
		padding: 10px;
	}

	.photo-warp {
		display: flex;
		justify-content: space-between;
		margin-top: 10px;

		.photo-item {
			display: flex;
			flex-direction: column;
			&_img {
				width: 100px;
				height: 100px;
				border: 1px solid #ddd;
				text-align: center;
			}

			&-icon {
				padding-top: 5px;
				text-align: center;
			}
		}
	}
}
::v-deep(.el-image__inner) {
	vertical-align: middle;
}
</style>
