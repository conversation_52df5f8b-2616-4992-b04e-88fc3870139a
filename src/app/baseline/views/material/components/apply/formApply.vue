<script setup lang="ts">
import { watch, toRef, ref, onMounted } from "vue"
import TreeMatType from "@/app/baseline/views/components/v2/treeMatType.vue"
import { ElMessage } from "element-plus"
import type {
	UploadInstance,
	UploadProps,
	UploadProgressEvent,
	UploadRequestOptions
} from "element-plus"
import { DictApi, matStatus, fileBusinessType } from "@/app/baseline/api/dict"
import XEUtils from "xe-utils"
import { FileApi } from "@/app/baseline/api/file"
import CustomInputNumber from "@/app/baseline/views/components/inputNumber.vue"
import {
	getRealLength,
	validateAndCorrectInput,
	inputLimit
} from "@/app/baseline/utils/validate"
import { map, toNumber } from "lodash-es"
import photoDetail from "./photoDetail.vue"
import { modalSize } from "@/app/baseline/utils/layout-config"
import { MatApplyApi } from "@/app/baseline/api/material/matApply"
import { filterMultipleSpaces } from "@/app/baseline/utils/validate"

interface Props {
	formData?: any
	readonly?: boolean
	needEval?: boolean
}

const props = defineProps<Props>()
const formModal = toRef(props, "formData")
const emit = defineEmits<{
	(e: "changeUseAloneFlag"): void
	(e: "changeWasteMatType"): void
	(e: "changeRecoveryFlag"): void
	(e: "changeSimilarMat", field: string, val: string): void
	(e: "setPhotoName", filePath?: any): void
}>()
watch(
	() => props.formData,
	(val: any) => {
		if (val) {
			Object.assign(formModal, val)
		}
	},
	{ immediate: true }
)

const drawerTitle = {
	name: ["请选择物资分类"],
	icon: ["fas", "square-share-nodes"]
}
const showMatTypeDrawer = ref<boolean>(false)
const matTypeDrawerSize = 310
const handleShowMatType = () => {
	showMatTypeDrawer.value = true
}

const properties = ref<any>(props.formData.materialProperties)
const onMatTypeBtnClick = async (selected: any, btn) => {
	if (btn == "cancel") {
		showMatTypeDrawer.value = false
		return
	}
	// 保存
	if (selected) {
		if (selected.leafFlag === "1") {
			formModal.value.materialTypeId = selected.id
			formModal.value.materialTypeCode = selected.code
			formModal.value.materialTypeLabel = selected.label

			properties.value = await MatApplyApi.getMatMaterialProperties({
				materialTypeId: selected.id
			})

			formModal.value.materialProperties = properties.value
			showMatTypeDrawer.value = false
		} else {
			ElMessage.warning("需选择末端分类")
		}
	} else {
		ElMessage.warning("需选择末端分类")
	}
}

//*******************文件上传*********************************/
const previewUrl = FileApi.buildPreviewUrl()

const filePercents = ref<any>({})
const upload = ref<UploadInstance>()
const handleBeforeUpload: UploadProps["beforeUpload"] = (rawFile) => {
	const str = rawFile.name
	const lastIndex = str.lastIndexOf(".")
	const fmt = str.substring(lastIndex + 1)

	if (!["png", "bmp", "jpeg", "jpg"].includes(fmt.toLowerCase())) {
		ElMessage.warning("请选择格式为JPEG、PNG或BMP的图片文件")
		return false
	}
	//imageUrl.value = URL.createObjectURL(rawFile)
	filePercents.value = 0
	return true
}
const handleProgress: UploadProps["onProgress"] = (
	evt: UploadProgressEvent
) => {
	//filePercents.value = evt.percent.toFixed(0)
}
const handleSuccess: UploadProps["onSuccess"] = (response, uploadFile) => {
	//imageUrl.value = URL.createObjectURL(uploadFile.raw!)
}
const handleUploadFile = (params: UploadRequestOptions) => {
	const formData = new FormData()
	formData.append("file", params.file)
	request({
		url:
			"/pitaya/system/common/upload?businessType=" +
			fileBusinessType.matCodePhoto +
			"&businessId=0",
		method: "POST",
		headers: {
			Authorization: "Bearer " + getToken(),
			"Content-Type": "multipart/form-data"
		},
		data: formData
	})
		.then(async (res: any) => {
			//	imageUrl.value = previewUrl + res.filePath
			emit("setPhotoName", res.filePath)
		})
		.catch(() => {})
}

//获取字典
const dictOptions = ref<Record<string, any[]>>({
	MAIN_MATERIALS: [],
	HAZARDOUS_WASTE: [],
	WASTE_MATERIALS_TYPE: [],
	//AUXILIARY_MATERIALS: [],
	MATERIAL_NATURE: [],
	INVENTORY_UNIT: [],
	USE_ALONE_PERIOD: []
})

function getDictByCodeList(): Promise<null> {
	return new Promise((resolve) => {
		DictApi.getDictByCodeList(dictOptions)
			.then((res) => {
				dictOptions.value = res
			})
			.finally(() => {
				resolve(null)
			})
	})
}

onMounted(() => {
	Promise.all([getDictByCodeList()]).then(() => {})
})
watch(
	[() => props.formData.buyUnit, () => props.formData.useUnit],
	([buyUnit, useUnit], [oldBuy, oldUse]) => {
		if (buyUnit != oldBuy) {
			props.formData.useUnit = buyUnit
		}
		if (useUnit != oldUse) {
			props.formData.buyUnit = useUnit
		}
	},
	{ immediate: true }
)

watch([() => props.formData.materialProperties], () => {
	if (props.formData.materialProperties) {
		properties.value = props.formData.materialProperties
	}
})
/* const wasteMaterialTypeDisabled = ref(false)
const optionsBDisabled = ref(false)

watch(
	[() => props.formData.dangerousWasteFlag],
	([d], [_d]) => {
		if (props.readonly) return

		if (d && d !== "1") {
			props.formData.wasteMaterialType = "1" //B类
			wasteMaterialTypeDisabled.value = true
		} else {
			if (props.formData.wasteMaterialType === "1") {
				props.formData.wasteMaterialType = ""
				wasteMaterialTypeDisabled.value = false
			}
		}
		optionsBDisabled.value = !wasteMaterialTypeDisabled.value && d == "1"
	},
	{ immediate: true }
) */
// watch(
// 	[() => props.formData.quality, () => props.formData.dangerousWasteFlag],
// 	([q, d], [_q, _d]) => {
// 		if (q == "1" && d == "1") {
// 			if (!props.formData.wasteMaterialType) {
// 				props.formData.wasteMaterialType = "0"
// 			}
// 		}
// 	},
// 	{ immediate: true }
// )

/**
 * 拍摄指南
 */
const photoDetailVisible = ref(false)

function handlePhotoDetail() {
	photoDetailVisible.value = true
}
</script>

<template>
	<el-row :gutter="10" class="form-base" type="flex">
		<div style="width: 240px">
			<el-form-item label="实物照片" prop="photoName">
				<template #label>
					实物照片
					<span
						v-if="!props.readonly"
						style="
							cursor: pointer;
							padding-left: 10px;
							color: var(--el-color-warning);
						"
						@click="handlePhotoDetail"
					>
						<font-awesome-icon :icon="['fas', 'exclamation-circle']" />
						拍摄指南
					</span>
				</template>
			</el-form-item>
			<div class="upload-content">
				<div class="upload-wrapper">
					<el-upload
						ref="upload"
						:before-upload="handleBeforeUpload"
						:on-progress="handleProgress"
						:on-success="handleSuccess"
						:show-file-list="false"
						:http-request="handleUploadFile"
					>
						<el-image
							v-if="formModal.photoName && !props.readonly"
							:src="previewUrl + formModal.photoName"
							fit="scale-down"
							class="image"
						/>

						<el-image
							v-else-if="formModal.photoName && props.readonly"
							:src="previewUrl + formModal.photoName"
							fit="scale-down"
							class="image"
							:zoom-rate="1.2"
							:max-scale="7"
							:min-scale="0.5"
							:preview-src-list="[previewUrl + formModal.photoName]"
							:initial-index="4"
						/>
						<div v-else class="opert">
							<div class="tip" v-if="props.readonly" :disabled="true">
								暂无图片
							</div>
							<el-button v-else v-btn class="file-btn">
								<font-awesome-icon
									class="icon"
									:icon="['fas', 'circle-plus']"
								/>
								<span> 添加物资图片 </span>
							</el-button>
							<div v-if="!props.readonly">
								<div class="tip">
									请先查看
									<span
										style="color: var(--el-color-warning)"
										@click.stop="handlePhotoDetail"
									>
										【拍摄指南】
									</span>
								</div>
								<div class="tip">图片清晰，实物大小适中</div>
								<div class="tip">格式为JPEG、PNG或BMP</div>
								<div class="tip" v-if="filePercents > 0">
									<el-progress
										:text-inside="true"
										:stroke-width="14"
										:percentage="filePercents"
										status="success"
									/>
								</div>
							</div>
						</div>
					</el-upload>
				</div>
			</div>
			<el-input type="hidden" v-model.trim="formModal.photoName" />
			<div class="el-left">
				<el-form-item
					label="物资分类编码"
					prop="materialTypeCode"
					:show-message="!formModal.materialTypeCode"
					:validate-status="formModal.materialTypeCode ? '' : 'error'"
				>
					<el-input
						@click="handleShowMatType"
						v-model="formModal.materialTypeCode"
						readonly
						placeholder="请选择物资分类编码"
						clearable
					>
						<template #append>
							<font-awesome-icon
								:icon="['fas', 'layer-group']"
								style="color: #ccc"
								@click="handleShowMatType"
							/>
						</template>
					</el-input>
				</el-form-item>
				<el-form-item
					label="物资分类名称"
					prop="materialTypeLabel"
					:show-message="!formModal.materialTypeLabel"
					:validate-status="formModal.materialTypeLabel ? '' : 'error'"
				>
					<el-input
						readonly
						disabled
						placeholder="请输入物资分类名称"
						v-model.trim="formModal.materialTypeLabel"
						clearable
					/>
				</el-form-item>
				<el-form-item
					label="物资编码"
					prop="code"
					v-if="!props.needEval && formModal.recoveryFlag !== '0'"
				>
					<el-input
						placeholder="请输入物资编码"
						v-model.trim="formModal.code"
						maxlength="50"
						:show-word-limit="true"
						clearable
					/>
				</el-form-item>
				<el-form-item
					label="物资名称"
					prop="label"
					v-if="props.needEval && formModal.recoveryFlag !== '0'"
				>
					<el-tooltip
						effect="dark"
						:content="formModal?.label"
						:disabled="
							!props.readonly || getRealLength(formModal?.label) < 40
								? true
								: false
						"
					>
						<el-input
							placeholder="请输入物资名称"
							v-model="formModal.label"
							maxlength="50"
							:show-word-limit="true"
							clearable
							:class="props.readonly ? 'ellipsis-input' : ''"
							@blur="formModal.label = formModal.label.trim()"
							@change="(val:any) => {
												//if (formModal.version && formModal.label) {
												emit('changeSimilarMat', 'label', val)
												//}
												}"
						/>
					</el-tooltip>
				</el-form-item>
				<el-form-item
					label="规格型号"
					prop="version"
					v-if="formModal.recoveryFlag !== '0'"
				>
					<el-tooltip
						effect="dark"
						:content="formModal?.version"
						:disabled="
							!props.readonly || getRealLength(formModal?.version) < 40
								? true
								: false
						"
					>
						<el-input
							placeholder="请输入规格型号"
							v-model="formModal.version"
							maxlength="50"
							:show-word-limit="true"
							clearable
							:class="props.readonly ? 'ellipsis-input' : ''"
							@blur="formModal.version = formModal.version.trim()"
							@change="(val:any)=>{
								emit('changeSimilarMat', 'version', val)
							}"
						/>
					</el-tooltip>
				</el-form-item>
			</div>
		</div>
		<div :span="12" style="margin-left: 10px; width: calc(100% - 250px)">
			<el-form-item label="物资性质" prop="attribute">
				<!-- <el-select
					v-model="formModal.attribute"
					:data="materialAttribute"
					:render-after-expand="false"
					style="width: 100%"
					clearable
				/> -->

				<el-select style="width: 100%" clearable v-model="formModal.attribute">
					<el-option
						v-for="(opt, index) in dictOptions.MATERIAL_NATURE"
						:key="index"
						:label="opt.label"
						:value="opt.value"
					/>
				</el-select>
			</el-form-item>
			<el-form-item
				label="预估采购单价"
				prop="evaluation"
				v-if="props.needEval"
			>
				<el-input
					v-if="props.readonly"
					placeholder="请输入预估采购单价"
					v-model.trim="formModal.evaluation_view"
					clearable
				>
					<template #append>元</template>
				</el-input>
				<el-input
					v-else
					placeholder="请输入预估采购单价"
					v-model.trim="formModal.evaluation"
					clearable
					@keydown="inputLimit"
					@input="formModal.evaluation = validateAndCorrectInput($event, 5)"
					@blur="formModal.evaluation = toNumber($event.target.value) || 1"
				>
					<template #append>元</template>
				</el-input>
			</el-form-item>
			<el-row :gutter="10">
				<el-col :span="12">
					<el-form-item label="是否可维修" prop="fixFlag">
						<el-select
							style="width: 100%"
							clearable
							v-model="formModal.fixFlag"
						>
							<el-option
								v-for="(opt, index) in DictApi.getYesNo()"
								:key="index"
								:label="opt.label"
								:value="opt.value"
							/>
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="是否可回收" prop="recoveryFlag">
						<el-select
							style="width: 100%"
							clearable
							v-model="formModal.recoveryFlag"
							@change="emit('changeRecoveryFlag')"
						>
							<el-option
								v-for="(opt, index) in DictApi.getYesNo()"
								:key="index"
								:label="opt.label"
								:value="opt.value"
							/>
						</el-select>
					</el-form-item>
				</el-col>
			</el-row>
			<div v-if="formModal.recoveryFlag !== '0'">
				<el-row :gutter="10">
					<el-col :span="12">
						<el-form-item label="主要材质" prop="quality">
							<el-select
								style="width: 100%"
								clearable
								v-model="formModal.quality"
							>
								<el-option
									v-for="(opt, index) in dictOptions.MAIN_MATERIALS"
									:key="index"
									:label="opt.label"
									:value="opt.value"
								/>
							</el-select>
						</el-form-item>
					</el-col>

					<el-col :span="12">
						<el-form-item label="辅助材质" prop="auxiliaryQuality">
							<el-select
								style="width: 100%"
								clearable
								v-model="formModal.auxiliaryQuality"
							>
								<el-option
									v-for="(opt, index) in dictOptions.MAIN_MATERIALS"
									:key="index"
									:label="opt.label"
									:value="opt.value"
								/>
							</el-select>
						</el-form-item>
					</el-col>
				</el-row>
				<el-row :gutter="10">
					<el-col :span="12">
						<el-form-item label="废旧物资类型" prop="wasteMaterialType">
							<el-select
								style="width: 100%"
								clearable
								v-model="formModal.wasteMaterialType"
								@change="emit('changeWasteMatType')"
							>
								<el-option
									v-for="(opt, index) in dictOptions.WASTE_MATERIALS_TYPE"
									:key="index"
									:label="opt.label"
									:value="opt.value"
								/>
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="危险废物类型" prop="dangerousWasteFlag">
							<el-select
								style="width: 100%"
								clearable
								v-model="formModal.dangerousWasteFlag"
								:disabled="['0', '2'].includes(formModal.wasteMaterialType)"
							>
								<el-option
									v-for="(opt, index) in dictOptions.HAZARDOUS_WASTE"
									:key="index"
									:label="opt.label"
									:value="opt.value"
								/>
							</el-select>
						</el-form-item>
					</el-col>
				</el-row>

				<el-form-item label="预估回收重量" prop="recoveryWeight">
					<el-input
						placeholder="请输入预估回收重量"
						v-model="formModal.recoveryWeight"
						clearable
						:min="0"
						@keydown="inputLimit"
						@input="formModal.recoveryWeight = validateAndCorrectInput($event)"
						@blur="formModal.recoveryWeight = toNumber($event.target.value)"
					>
						<template #append>Kg</template>
					</el-input>
				</el-form-item>
			</div>
			<div class="el-inline">
				<el-form-item label="采购单位" prop="buyUnit" class="el-item">
					<el-select style="width: 100%" clearable v-model="formModal.buyUnit">
						<el-option
							v-for="(opt, index) in dictOptions.INVENTORY_UNIT"
							:key="index"
							:label="opt.label"
							:value="opt.value"
						/>
					</el-select>
				</el-form-item>
				<el-form-item
					label="库存转换比例"
					prop="conversionUnitText"
					class="el-item w40"
				>
					<el-input
						placeholder="请输入库存转换比例"
						v-model.trim="formModal.conversionUnitText"
						:disabled="true"
						clearable
					/>
					<el-input
						type="hidden"
						v-model.trim="formModal.conversionUnit"
						:disabled="true"
						clearable
					/>
				</el-form-item>
				<el-form-item label="库存单位" prop="useUnit" class="el-item">
					<el-select style="width: 100%" clearable v-model="formModal.useUnit">
						<el-option
							v-for="(opt, index) in dictOptions.INVENTORY_UNIT"
							:key="index"
							:label="opt.label"
							:value="opt.value"
						/>
					</el-select>
				</el-form-item>
			</div>
			<!-- 当选否的时候 -->
			<el-form-item
				label="物资编码"
				prop="code"
				v-if="!props.needEval && formModal.recoveryFlag === '0'"
			>
				<el-input
					placeholder="请输入物资编码"
					v-model.trim="formModal.code"
					maxlength="50"
					:show-word-limit="true"
					clearable
				/>
			</el-form-item>
			<el-form-item
				label="物资名称"
				prop="label"
				v-if="formModal.recoveryFlag === '0' || !props.needEval"
			>
				<el-tooltip
					effect="dark"
					:content="formModal?.label"
					:disabled="
						!props.readonly || getRealLength(formModal?.label) < 40
							? true
							: false
					"
				>
					<el-input
						placeholder="请输入物资名称"
						v-model="formModal.label"
						maxlength="50"
						:show-word-limit="true"
						clearable
						:class="props.readonly ? 'ellipsis-input' : ''"
						@blur="formModal.label = formModal.label.trim()"
						@change="(val:any)=>{
							emit('changeSimilarMat', 'label', val)
						}"
					/>
				</el-tooltip>
			</el-form-item>
			<el-row :gutter="10">
				<el-col :span="12">
					<el-form-item label="是否独立使用" prop="useAloneFlag">
						<el-select
							style="width: 100%"
							clearable
							v-model="formModal.useAloneFlag"
							@change="emit('changeUseAloneFlag')"
						>
							<el-option
								v-for="(opt, index) in DictApi.getTrueFalse()"
								:key="index"
								:label="opt.label"
								:value="opt.value"
							/>
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="使用期限" prop="useAlonePeriod">
						<el-select
							style="width: 100%"
							clearable
							v-model="formModal.useAlonePeriod"
							:disabled="!formModal.useAloneFlag"
						>
							<el-option
								v-for="(opt, index) in dictOptions.USE_ALONE_PERIOD"
								:key="index"
								:label="opt.label"
								:value="opt.value"
							/>
						</el-select>
					</el-form-item>
				</el-col>
			</el-row>
		</div>
	</el-row>
	<el-row :gutter="10">
		<el-col :span="24">
			<el-form-item
				label="规格型号"
				prop="version"
				v-if="formModal.recoveryFlag === '0'"
			>
				<el-tooltip
					effect="dark"
					:content="formModal?.version"
					:disabled="
						!props.readonly || getRealLength(formModal?.version) < 80
							? true
							: false
					"
				>
					<el-input
						placeholder="请输入规格型号"
						v-model="formModal.version"
						maxlength="50"
						:show-word-limit="true"
						:class="props.readonly ? 'ellipsis-input' : ''"
						clearable
						@blur="formModal.version = formModal.version.trim()"
						@change="(val:any)=>{
							emit('changeSimilarMat', 'version', val)
						}"
					/>
					<!--					@change="-->
					<!--					(val:any) => {-->
					<!--					//if (formModal.version && formModal.label) {-->
					<!--					emit('changeSimilarMat', 'version', val)-->
					<!--					//}-->
					<!--					}-->
					<!--					"-->
				</el-tooltip>
			</el-form-item>
		</el-col>
	</el-row>
	<el-row :gutter="10">
		<el-col :span="24" style="padding: 0">
			<el-form-item label="技术参数" prop="technicalParameter">
				<el-input
					type="textarea"
					:rows="10"
					placeholder="请输入技术参数"
					v-model="formModal.technicalParameter"
					maxlength="1000"
					:show-word-limit="true"
					clearable
					@blur="
						formModal.technicalParameter = formModal.technicalParameter.trim()
					"
					@change="(val:any)=>{
						emit('changeSimilarMat', 'technicalParameter', val)
					}"
				/>
				<!--				@change="-->
				<!--				(val:any) => {-->
				<!--				//if (formModal.version && formModal.label) {-->
				<!--				emit('changeSimilarMat', 'technicalParameter', val)-->
				<!--				//}-->
				<!--				}-->
				<!--				"-->
			</el-form-item>
		</el-col>
	</el-row>

	<template v-if="properties && properties.length > 0">
		<el-row :gutter="10">
			<h5 style="margin-left: 5px">其他属性</h5>
		</el-row>
		<el-row :gutter="10">
			<el-col :span="12" v-for="item in properties" :key="item.propertyName">
				<el-form-item :label="item.propertyName">
					<el-input
						v-model="item.propertyValue"
						v-if="item.propertyType === 1"
					/>
					<el-select v-model="item.propertyValue" style="width: 100%" v-else>
						<el-option
							v-for="op in item.optionList.split(',')"
							:key="item.propertyName + op"
							:label="op"
							:value="op"
						/>
					</el-select>
				</el-form-item>
			</el-col>
		</el-row>
	</template>
	<Drawer :size="matTypeDrawerSize" v-model:drawer="showMatTypeDrawer">
		<TreeMatType
			:title="drawerTitle"
			:foot-btn-visible="true"
			:currentNodeKey="formModal.materialTypeId"
			@onBtnClick="onMatTypeBtnClick"
			:checkStrictly="true"
			:needSingleSelect="true"
			:need-switch="false"
			:status="matStatus.normal"
			:defaultExpandedKeys="[0]"
		/>
	</Drawer>

	<Drawer :size="modalSize.sm" v-model:drawer="photoDetailVisible">
		<photo-detail @close="photoDetailVisible = false" />
	</Drawer>
</template>

<style scoped lang="scss">
@import "@/app/baseline/assets/css/index.scss";

.ellipsis-input {
	:deep(.el-input__inner) {
		overflow: hidden; /* 超出部分隐藏 */
		text-overflow: ellipsis; /* 使用省略号显示超出部分 */
		white-space: nowrap; /* 不换行 */
		max-width: 100%; /* 根据需要设置最大宽度 */
	}
}

::v-deep(.el-input-group__append) {
	font-size: 12px;
	padding: 0 9px !important;
}

.upload-content {
	height: 240px;
	width: 100%;
	margin-top: -10px;

	.upload-wrapper {
		display: flex;
		align-items: center;
		justify-content: center;
		border: 1px solid #dcdfe6;
		border-radius: 3px;
		flex-direction: column;
		height: 240px;
		width: 100%;
		overflow: hidden;
		position: relative;
		padding: 0 10px;

		&:hover {
			border: 1px solid #ccc;
		}

		.el-image {
			max-width: 100%;
			max-height: 219px;
			overflow: hidden;
			/* margin: auto; */
			/* 居中对齐 */
			::v-deep(.el-image__inner) {
				max-width: 100% !important;
				max-height: 212px !important;
				vertical-align: middle !important;
			}
		}

		.opert {
			display: flex;
			align-items: center;
			justify-content: center;
			flex-direction: column;

			.tip {
				font-size: 12px;
				color: #cccccc;
				//width: 143px;
				padding-top: 10px;
			}

			.file-btn {
				color: #fff;
				font-weight: 500;
				text-align: center;

				.icon {
					color: #fff !important;
					padding-right: 5px;
				}
			}
		}
	}
}

.el-inline {
	display: flex;
	justify-content: space-between;

	.el-item {
		flex-basis: calc((100% - 20px) / 3);
		margin-right: 10px;
	}

	.el-item:nth-child(2) {
		flex-basis: 35%;
	}

	.el-item:last-child {
		margin-right: 0px;
	}
}

.el-left {
	margin-top: 10px;
}

.container {
	display: grid;
	grid-template-columns: 1fr 1fr; /* 两列布局 */
	grid-auto-rows: minmax(0, 1fr); /* 自动调整行高度 */
	gap: 0px 10px; /* 格子之间的间隔 */
	.item {
		box-sizing: border-box;
	}

	.el-form-item {
		box-sizing: border-box;
	}
}
</style>
