<script lang="ts" setup>
import { watch, toRef, ref, reactive } from "vue"
import { type FormInstance, FormRules } from "element-plus"
import { MatApplyApi } from "@/app/baseline/api/material/matApply"
import {
	MatApplyObj,
	MaterialProcureInfo
} from "@/app/baseline/views/material/components/define"
import TableFile from "../../components/tableFile.vue"
import TableSimilar from "./apply/tableSimilar.vue"
import FormApply from "./apply/formApply.vue"
import {
	fileBusinessType,
	tableSimilarTypeDefault
} from "@/app/baseline/api/dict"
import XEUtils from "xe-utils"
import CustomInputNumber from "@/app/baseline/views/components/inputNumber.vue"
import { useDictInit } from "../../components/dictBase"
import fileDrawer from "../../components/fileDrawer.vue"
import { FileApi } from "@/app/baseline/api/file"
import { convertBytesToMegabytes } from "@/app/baseline/utils"
import { useMessageBoxInit } from "@/app/baseline/views/components/messageBox"
import {
	validateAndCorrectInput,
	inputLimit
} from "@/app/baseline/utils/validate"
import tableSimilarTypeList from "./apply/tableSimilarTypeList.vue"
import tableSimilarTypeImg from "./apply/tableSimilarTypeImg.vue"
import { IShowType, ISimilarityType } from "@/app/baseline/utils/types/material"
import { maxTableHeight } from "@/app/baseline/utils"
import { toNumber } from "lodash-es"

const { dictFilter, getDictByCodeList } = useDictInit()
const { showDelConfirm } = useMessageBoxInit()
const props = defineProps({
	id: {
		type: [String, Number],
		required: false
	},
	mod: {
		type: [String],
		required: false,
		default: "view"
	},
	approveadFormData: {
		type: Object,
		default: () => {},
		requeired: false
	}
})
const currentId = toRef(props, "id")
const emits = defineEmits(["onSaveOrClose"])

const drawerLeftTitle = {
	name: ["物资基本信息"],
	icon: ["fas", "square-share-nodes"]
}
const drawerMidTitle = [
	{
		name: ["采购信息"],
		icon: ["fas", "square-share-nodes"]
	},
	{
		name: ["相关附件"],
		icon: ["fas", "square-share-nodes"]
	},
	{
		name: ["相似物资查询结果"],
		icon: ["fas", "square-share-nodes"]
	}
]
const drawerRightTitle = {
	name: ["审批详情"],
	icon: ["fas", "square-share-nodes"]
}
const tableFileEndBtn = {
	roles: "system:department:btn:add",
	name: "上传附件",
	icon: ["fas", "arrow-up-from-bracket"]
}

const formModal = reactive<Record<string, any>>({
	id: props.id,
	conversionUnitText: "1:1"
})
const drawerLoading = ref(false)
const childTableLoading = ref(false)
const ruleFormRef = ref<FormInstance>()
const loadResult = ref(false)

const getMatApplyInfo = () => {
	drawerLoading.value = true
	loadResult.value = false
	MatApplyApi.getMatApplyById(currentId.value)
		.then((res: any) => {
			Object.assign(formModal, res)
			// formModal.materialTypeCode = res.materialType.code
			// formModal.materialTypeLabel = res.materialType.label
			const tmpEvaluation = `${XEUtils.commafy(formModal.evaluation, {
				digits: 5
			})}`
			formModal["evaluation_view"] = tmpEvaluation
				? `￥${tmpEvaluation}`
				: "￥0.00"
			formModal.useUnit = String(res.useUnit)
			formModal.buyUnit = String(res.buyUnit)
			formModal.useAlonePeriod = res.useAlonePeriod
				? String(res.useAlonePeriod)
				: ""

			setTimeout(() => {
				tableSimilarRef.value?.getTableData()
			})
		})
		.finally(() => {
			drawerLoading.value = false
			loadResult.value = true
		})
}

const formModalProcureInfo = reactive<Record<string, any>>({
	materialId: props.id
})

/**
 * 预估采购单价不能小于1
 * @param rule
 * @param value
 * @param callback
 */
const validatePrice = (rule: any, value: any, callback: any) => {
	if (!rule.required && !value) {
		callback()
	} else {
		if (value < 1) {
			return callback(new Error("预估采购单价不能小于1"))
		}
		callback()
	}
}

const formRulesProcureInfo = reactive<FormRules>({
	evaluationCycle: [
		{ required: true, trigger: "change", message: "预估采购周期不能为空" }
	],
	evaluation: [
		{
			required: true,
			trigger: "change",
			message: "预估采购单价不能为空"
		},
		{
			validator: validatePrice,
			required: true,
			trigger: "change"
		}
	],
	safeStock: [
		{
			required: true,
			trigger: "change",
			message: "安全库存不能为空"
		}
	],
	minBuyNum: [
		{ required: true, trigger: "change", message: "最小订货数量不能为空" }
	]
})

const saveMatProcureInfo = (): Promise<null> => {
	return new Promise((resolve, reject) => {
		if (ruleFormRef.value) {
			ruleFormRef.value.validate((valid) => {
				if (valid) {
					drawerLoading.value = true
					const params = { ...formModalProcureInfo }

					if (params.id) {
						//修改
						MatApplyApi.updateMatProcureInfo(params)
							.then(() => {
								resolve(null)
							})
							.finally(() => {
								drawerLoading.value = false
							})
					} else {
						//添加
						MatApplyApi.addMatProcureInfo(params)
							.then(() => {
								resolve(null)
							})
							.finally(() => {
								drawerLoading.value = false
							})
					}
				} else {
					//reject()
				}
			})
		}
		//resolve(null)
	})
}

const getMatProcureInfo = () => {
	childTableLoading.value = true
	MatApplyApi.getMatProcureInfoByMatId(currentId.value)
		.then((res: any) => {
			Object.assign(formModalProcureInfo, res)
			if (props.mod == "view") {
				for (const key in formModalProcureInfo) {
					if (formModalProcureInfo[key] === null) {
						formModalProcureInfo[key] = "---"
					}
				}
			} else {
				formModalProcureInfo.evaluationCycle = res.evaluationCycle
					? res.evaluationCycle
					: 1
			}
		})
		.finally(() => {
			childTableLoading.value = false
		})
}
const isNotValue = (item: any, attr: string) => {
	return (item.disabled || props.mod == "view") && item[attr] == "---"
}
watch(
	() => props.id,
	(id: any) => {
		if (id) {
			getMatApplyInfo()
			getMatProcureInfo()
		}
	},
	{ immediate: true }
)
// watch(
// 	() => formModalProcureInfo,
// 	(newVal: any) => {
// 		if (newVal) {
// 			const updatedParentObject = { ...props.approveadFormData, newVal }
// 			emits("update:approveadFormData", updatedParentObject)
// 		}
// 	},
// 	{ deep: true, immediate: true }
// )

/**
 * 相关附件
 */
const fileDrawerVisible = ref(false)
const tableLoading = ref(false)
const tableProp: TableColumnType[] = [
	{ label: "附件编号", prop: "code", needSlot: true, width: 150 },
	{ label: "附件名称", prop: "customFileName", minWidth: 150 },
	{ label: "附件格式", prop: "fileType", width: 85 },
	{ label: "附件大小", prop: "fileSize", needSlot: true, width: 120 },
	{ label: "上传人", prop: "createdBy_view", width: 100 },
	{ label: "上传时间", prop: "createdDate", width: 160 },
	{
		label: "操作",
		prop: "operations",
		fixed: "right",
		width: props.mod == "view" ? 70 : 130,
		needSlot: true
	}
]
function getCode(row: any) {
	const paddedNum1 = String(row.businessType).padEnd(3, "0")
	const paddedNum2 = String(row.businessId).padEnd(6, "0")
	const paddedNum3 = String(row.id).padStart(7, "0")
	return paddedNum1 + paddedNum2 + paddedNum3
}

const tableData = ref([])
const getTableData = () => {
	tableLoading.value = true
	const params = {
		businessType: fileBusinessType.matCode,
		businessId: props.id
	}

	FileApi.getAllFileList(params)
		.then((res: any) => {
			tableData.value = res
		})
		.finally(() => {
			tableLoading.value = false
		})
}
/**
 * 上传附件 handle
 */
function handleUploadFile() {
	fileDrawerVisible.value = true
}

/**
 * 附件下载
 * @param row
 */
function handleRowDownLoad(row: any) {
	FileApi.downloadFile(row.filePath)
}

/**
 * 附件移除
 * @param row
 */
async function handleRowDelete(row: any) {
	await showDelConfirm()
	await FileApi.deleteFile({ id: row.id })
	ElMessage.success("移除成功")
	getTableData()
}

defineOptions({
	name: "MatApplyViewDrawer"
})
defineExpose({
	saveMatProcureInfo
})

const tableSimilarRef = ref()

const computedRef = ref()

const computedOffsetHeight = computed(() => {
	return computedRef.value?.getBoundingClientRect().top
})

onMounted(() => {
	getDictByCodeList(["INVENTORY_UNIT"])
	getTableData()
})

const similarShowType = ref(IShowType.list)

watch(
	() => similarShowType.value,
	() => {
		setTimeout(() => {
			tableSimilarRef.value.getTableData()
		})
	}
)
</script>
<template>
	<div
		class="drawer-container mat-apply-view-drawer"
		v-loading="drawerLoading"
		style="padding-right: 0 !important"
	>
		<!-- 左侧表单区域 -->
		<div class="drawer-column left" style="align-items: stretch">
			<div class="rows">
				<Title :title="drawerLeftTitle" />
				<el-scrollbar>
					<el-form
						style="padding-bottom: 30px"
						class="content form-base"
						:model="formModal"
						label-position="top"
						label-width="100px"
						disabled
					>
						<FormApply
							:form-data="formModal"
							:readonly="true"
							:need-eval="true"
						/>
					</el-form>
				</el-scrollbar>
			</div>
		</div>
		<!-- 中间table区域 -->
		<div class="drawer-column right">
			<div class="rows-auto pb0">
				<Title :title="drawerMidTitle[0]" />
				<el-form
					class="content form-base"
					:model="formModalProcureInfo"
					:rules="formRulesProcureInfo"
					ref="ruleFormRef"
					label-position="top"
					label-width="100px"
					:disabled="props.mod == 'view'"
				>
					<el-row :gutter="10">
						<el-col :span="6">
							<el-form-item label="预估采购周期" prop="evaluationCycle">
								<custom-input-number
									:placeholder="
										isNotValue(formModalProcureInfo, 'evaluationCycle')
											? '---'
											: '请输入预估采购周期'
									"
									v-model.trim="formModalProcureInfo.evaluationCycle"
									clearable
									:min="1"
									:max="11"
								>
									<template #append>月</template>
								</custom-input-number>
							</el-form-item>
						</el-col>
						<el-col :span="6">
							<el-form-item label="最小订货数量" prop="minBuyNum">
								<el-input
									type="number"
									:placeholder="
										isNotValue(formModalProcureInfo, 'minBuyNum')
											? '---'
											: '请输入最小订货数量'
									"
									v-model.trim="formModalProcureInfo.minBuyNum"
									@input="
										formModalProcureInfo.minBuyNum =
											validateAndCorrectInput($event)
									"
									@blur="
										formModalProcureInfo.minBuyNum = toNumber(
											$event.target.value
										)
									"
									@keydown="inputLimit"
								>
									<template #append>
										{{
											dictFilter(
												"INVENTORY_UNIT",
												formModalProcureInfo.buyUnit!
											)?.subitemName || "---"
										}}
									</template>
								</el-input>
							</el-form-item>
						</el-col>
						<el-col :span="6" v-if="props.mod != 'view'">
							<el-form-item label="预估采购单价" prop="evaluation">
								<el-input
									type="number"
									:placeholder="
										isNotValue(formModalProcureInfo, 'evaluation')
											? '---'
											: '请输入预估采购单价'
									"
									v-model.trim="formModalProcureInfo.evaluation"
									@input="
										formModalProcureInfo.evaluation = validateAndCorrectInput(
											$event,
											5
										)
									"
									@blur="
										formModalProcureInfo.evaluation =
											toNumber($event.target.value) || 1
									"
									@keydown="inputLimit"
								>
									<template #append>元</template>
								</el-input>
							</el-form-item>
						</el-col>
						<el-col :span="6">
							<el-form-item label="安全库存" prop="safeStock">
								<el-input
									type="number"
									:placeholder="
										isNotValue(formModalProcureInfo, 'safeStock')
											? '---'
											: '请输入安全库存'
									"
									v-model.trim="formModalProcureInfo.safeStock"
									@input="
										formModalProcureInfo.safeStock =
											validateAndCorrectInput($event)
									"
									@blur="
										formModalProcureInfo.safeStock = toNumber(
											$event.target.value
										)
									"
									@keydown="inputLimit"
								>
									<template #append>
										{{
											dictFilter(
												"INVENTORY_UNIT",
												formModalProcureInfo.useUnit!
											)?.subitemName || "---"
										}}
									</template>
								</el-input>
							</el-form-item>
						</el-col>
					</el-row>
				</el-form>
			</div>
			<div class="rows-auto pb0">
				<Title
					:title="drawerMidTitle[1]"
					style="justify-content: space-between"
				>
					<div
						class="upload-container"
						v-if="props.mod !== 'view'"
						@click="handleUploadFile"
					>
						<div :class="tableFileEndBtn.roles">
							<font-awesome-icon
								v-if="tableFileEndBtn.icon"
								class="place-icon"
								:icon="tableFileEndBtn.icon"
							/>
							<span class="btn-text">{{ tableFileEndBtn.name }}</span>
						</div>
					</div>
				</Title>
				<div>
					<PitayaTable
						ref="refFileTable"
						:columns="tableProp"
						:table-data="tableData"
						:needSelection="false"
						:single-select="false"
						:need-index="true"
						:need-pagination="false"
						:table-loading="tableLoading"
						:max-height="400"
					>
						<template #code="{ rowData }">
							{{ getCode(rowData) }}
						</template>
						<template #fileSize="{ rowData }">
							{{ convertBytesToMegabytes(rowData.fileSize) }}
						</template>
						<template #operations="{ rowData }">
							<div v-if="rowData.id !== -1">
								<el-button v-btn link @click="handleRowDownLoad(rowData)">
									<font-awesome-icon :icon="['fas', 'file-arrow-down']" />
									<span class="table-inner-btn">下载</span>
								</el-button>
								<template v-if="props.mod == 'edit'">
									<el-button v-btn link @click="handleRowDelete(rowData)">
										<font-awesome-icon :icon="['fas', 'trash-can']" />
										<span class="table-inner-btn">移除</span>
									</el-button>
								</template>
							</div>
						</template>
					</PitayaTable>
				</div>
				<!-- <TableFile
					:need-page="false"
					:mod="props.mod"
					:business-type="fileBusinessType.matCode"
					:business-id="props.id"
				/> -->
			</div>

			<div
				class="rows-auto pb0"
				ref="computedRef"
				style="
					height: 100%;
					display: flex;
					flex-direction: column;
					overflow: auto;
					flex: 1;
				"
			>
				<Title
					:title="drawerMidTitle[2]"
					style="justify-content: space-between"
				>
					<el-radio-group v-model="similarShowType" :size="'small'">
						<el-radio-button :label="IShowType.list">
							<font-awesome-icon :icon="['fas', 'list']" />
							列表
						</el-radio-button>
						<el-radio-button :label="IShowType.img">
							<font-awesome-icon :icon="['fas', 'image']" />
							图片
						</el-radio-button>
					</el-radio-group>
				</Title>

				<!-- <TableSimilar class="content" :table-loading="childTableLoading" />-->

				<table-similar-type-list
					v-if="similarShowType === IShowType.list"
					ref="tableSimilarRef"
					:id="(props.id as number)"
					:max-height="computedRef?.clientHeight - 100"
					:offset-height="computedOffsetHeight"
					:similarityType="tableSimilarTypeDefault"
					:table-req-params="
						tableSimilarTypeDefault == ISimilarityType.otherSimilar
							? {
									label: formModal.label,
									version: formModal.version,
									attribute: formModal.attribute,
									technicalParameter: formModal.technicalParameter,
									similarityType: ISimilarityType.otherSimilar
							  }
							: {
									label: formModal.label ? `*${formModal.label}*` : undefined,
									version: formModal.version
										? `*${formModal.version}*`
										: undefined,
									attribute: formModal.attribute,
									technicalParameter: formModal.technicalParameter
										? `*${formModal.technicalParameter}*`
										: undefined,
									similarityType: ISimilarityType.otherSimilar
							  }
					"
				/>

				<table-similar-type-img
					v-else-if="similarShowType === IShowType.img"
					ref="tableSimilarRef"
					:id="(props.id as number)"
					:is-small="true"
					:max-height="computedRef?.clientHeight - 100"
					:similarityType="ISimilarityType.otherSimilar"
					:table-req-params="{
						label: formModal.label,
						version: formModal.version,
						photoName: formModal.photoName,
						attribute: formModal.attribute,
						technicalParameter: formModal.technicalParameter
					}"
				/>
			</div>
		</div>
		<!--		<div class="drawer-column right">-->
		<!--			<Title :title="drawerRightTitle" />-->
		<!--			<ApprovedTimeline-->
		<!--				v-if="loadResult"-->
		<!--				:mod="props.mod"-->
		<!--				:id="formModal.procInsId"-->
		<!--				:passCallBack="saveMatProcureInfo"-->
		<!--			/>-->
		<!--		</div>-->

		<Drawer
			:size="310"
			v-model:drawer="fileDrawerVisible"
			:destroyOnClose="true"
		>
			<file-drawer
				:businessType="fileBusinessType.matCode"
				:businessId="props.id"
				:fileTxtConf="{
					btnName: '添加附件',
					fileName: '附件名称',
					fileLabel: '上传附件',
					title: '上传附件'
				}"
				@onUpdateList="getTableData"
				@onSaveOrClose="fileDrawerVisible = false"
			/>
		</Drawer>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

:deep() {
	input::-webkit-outer-spin-button,
	input::-webkit-inner-spin-button {
		-webkit-appearance: none !important;
	}

	input[type="number"] {
		-moz-appearance: textfield;
	}
}

.drawer-container {
	.left {
		width: 600px;
	}

	.right {
		width: calc(100% - 600px);
		.row {
			flex: 0 !important;
		}

		.pb0 {
			padding-bottom: 0;
		}

		.upload-container {
			display: flex;
			align-items: center; /* 垂直居中子元素 */
			justify-content: center;
			font-size: 12px;
			cursor: pointer;

			.btn-text {
				margin-left: 5px;
				padding-right: 10px;
			}
		}
	}

	//.right {
	//	width: calc(100% - 600px - 750px);
	//
	//	:deep(.content) {
	//		height: calc(100% - 20px);
	//	}
	//}
}
</style>
