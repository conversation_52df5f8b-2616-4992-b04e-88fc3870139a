<script lang="ts" setup>
import { watch, ref, onMounted, reactive, nextTick } from "vue"
import { ElMessage, type FormInstance, type FormRules } from "element-plus"
import { FormElementType } from "@/app/baseline/views/components/define"
import FormElement from "@/app/baseline/views/components/formElement.vue"
import { MatTypeApi } from "@/app/baseline/api/material/matType"
import { DictApi, matStatus } from "@/app/baseline/api/dict"
import {
	getIdempotentToken,
	validateMatCode
} from "@/app/baseline/utils/validate"
import { refreshTitle } from "../../../../platform/utils/common"
import { UpdMatTypeRequest } from "@/app/baseline/api/defines"
import { useMessageBoxInit } from "../../components/messageBox"
import {
	IIdempotentTokenType,
	IIdempotentTokenTypePre
} from "@/app/baseline/utils/types/common"

interface Props {
	currentNode?: any
	formData?: any
	model?: any
}
const { showWarnConfirm } = useMessageBoxInit()

const props = defineProps<Props>()
const emits = defineEmits(["onClose"])

const leftTitle = {
	name:
		props.formData.id && props.model == "edit"
			? ["编辑编码分类"]
			: props.formData.id && props.model == "view"
			? ["查看编码分类"]
			: ["新增编码分类"],
	icon: ["fas", "square-share-nodes"]
}
const formBtnList = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "确认提交",
		icon: ["fas", "floppy-disk"]
	}
]
const formBtnListView = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	}
]
const drawerLoading = ref(false)
const refForm = ref<FormInstance>()

const formModelData = ref<{ [propName: string]: any }>({
	fid: "",
	id: "",
	fCode: "",
	fLabel: "",
	code: "",
	label: "",
	remark: "",
	leafFlag: "0",
	lowValue: "0",
	property: "0",
	lowValueType: "",
	propertyType: "",
	status: matStatus.drafted
})
const lowValueTypeDict = ref<any[]>([])
const propertyTypeDict = ref<any[]>([])
const formEl = reactive<FormElementType[][]>([
	[
		{
			label: "上级物资分类Id",
			name: "fid",
			disabled: true,
			type: "hidden"
		}
	],
	[
		{
			label: "上级物资分类编码",
			name: "fCode",
			disabled: true
		}
	],
	[
		{
			label: "上级物资分类名称",
			name: "fLabel",
			maxlength: 50,
			disabled: true
		}
	],
	[{ label: "物资分类编码", name: "code", maxlength: 2 }],
	[{ label: "物资分类名称", name: "label", maxlength: 50 }],
	[
		{
			label: "备注说明",
			name: "remark",
			maxlength: 200,
			rows: 5,
			type: "textarea"
		}
	],
	[
		{
			label: "是否包含下级分类",
			name: "leafFlag",
			type: "select",
			data: [
				{
					label: "是",
					value: "0"
				},
				{
					label: "否",
					value: "1"
				}
			],
			disabled: false,
			clear: false
		}
	],
	[
		{
			label: "是否关联低值易耗",
			name: "lowValue",
			type: "select",
			width: 12,
			data: DictApi.getYesNo(),
			clear: false,
			slotLabelContent: `低值物资是指购置单价在1000元（含）以上、<br />5000元（不含）以下，具有独立使用功能，<br />且使用期限在1年（含）以上的工器具、<br />仪器仪表、家具电器、办公设备等`
		},
		{
			label: "低值易耗类型",
			name: "lowValueType",
			type: "select",
			data: lowValueTypeDict.value,
			width: 12
		}
	],
	[
		{
			label: "是否关联资产",
			name: "property",
			type: "select",
			data: DictApi.getYesNo(),
			width: 12,
			clear: false
		},
		{
			label: "资产类型",
			name: "propertyType",
			type: "select",
			data: propertyTypeDict.value,
			width: 12
		}
	]
])
const validateLowValueType = (rule: any, value: any, callback: any) => {
	if (formModelData.value.leafFlag == "1") {
		if (
			formModelData.value.lowValue == "1" &&
			!formModelData.value.lowValueType
		) {
			return callback(new Error("低值易耗类型不能为空"))
		}
	}
	callback()
}
const validatePropertyType = (rule: any, value: any, callback: any) => {
	if (formModelData.value.leafFlag == "1") {
		if (
			formModelData.value.property == "1" &&
			!formModelData.value.propertyType
		) {
			return callback(new Error("资产类型不能为空"))
		}
	}
	callback()
}

const rules = reactive<FormRules<typeof formModelData.value>>({
	code: [
		{ required: true, message: "物资分类编码不能为空", trigger: "change" },
		{ validator: validateMatCode, required: true, trigger: "change" }
	],
	label: [
		{ required: true, message: "物资分类名称不能为空", trigger: "change" }
	],
	leafFlag: [
		{ required: true, message: "是否包含下级分类不能为空", trigger: "change" }
	],
	lowValueType: [{ validator: validateLowValueType, trigger: "change" }],
	propertyType: [{ validator: validatePropertyType, trigger: "change" }]
})

// 按钮点击
const onFormBtnList = (btnName: string | undefined) => {
	if (btnName === "确认提交") {
		refForm.value?.validate(async (valid) => {
			if (valid) {
				await showWarnConfirm("请确认是否提交本次数据？")

				drawerLoading.value = true

				const params = { ...formModelData.value }
				if (formModelData.value.id) {
					MatTypeApi.updateMatType(params as UpdMatTypeRequest)
						.then(() => {
							emits("onClose", true)
							ElMessage.success("编辑成功")
						})
						.finally(() => {
							drawerLoading.value = false
						})
				} else {
					const idempotentToken = getIdempotentToken(
						IIdempotentTokenTypePre.apply,
						IIdempotentTokenType.materialType
					)
					MatTypeApi.addMatType(params, idempotentToken)
						.then(() => {
							emits("onClose", true)
							ElMessage.success("新增成功")
						})
						.finally(() => {
							drawerLoading.value = false
						})
					//})
				}
			} else {
				return false
			}
		})
		return
	}
	if (btnName === "取消") {
		Object.keys(formModelData.value).map(
			(key) => (formModelData.value[key] = "")
		)
		refForm.value?.clearValidate()
		emits("onClose")
		return
	}
}

function getLowDict(): Promise<null> {
	return new Promise((resolve) => {
		DictApi.getDictByCode("LOW_VALUE_TYPE")
			.then((res) => {
				lowValueTypeDict.value = res as any
			})
			.finally(() => {
				resolve(null)
			})
	})
}

function getPropertyDict(): Promise<null> {
	return new Promise((resolve) => {
		DictApi.getDictByCode("ASSET_TYPE")
			.then((res) => {
				propertyTypeDict.value = res as any
			})
			.finally(() => {
				resolve(null)
			})
	})
}
function getLastTwoChars(str?: string) {
	if (str && str.length >= 2) {
		return str.slice(-2)
	} else {
		// 处理字符串为空或长度不足两位的情况
		return ""
	}
}
function getInfo() {
	Object.assign(formModelData.value, props.formData)
	formModelData.value.fid = props.currentNode.id
	formModelData.value.fCode = props.currentNode.code
		? props.currentNode.code
		: "---"
	formModelData.value.fLabel = props.currentNode.label
	//formModelData.value.lowValue = formModelData.value.lowValueType ? "1" : "0"
	//formModelData.value.property = formModelData.value.propertyType ? "1" : "0"
	formModelData.value.code = getLastTwoChars(formModelData.value.code) //获取最后两位

	//如果父是第三级或者本身已有孩子，不能修改是否包含
	if (formModelData.value.id) {
		// Edit mode 父级是3极了，子不能改变模式了，
		formEl[6][0].disabled =
			formModelData.value.fCode.length >= 6 ||
			formModelData.value.childCount > 0
	} else {
		// Add mode,看父级是第几级了
		const isDisabled = formModelData.value.fCode.length >= 6
		formEl[6][0].disabled = isDisabled
		formModelData.value.leafFlag = isDisabled ? "1" : "0"
	}

	console.log("formModelData.value", formModelData.value)
}
watch(
	[
		() => formModelData.value.leafFlag,
		() => formModelData.value.lowValue,
		() => formModelData.value.property
	],
	([flag, isLow, isProp]) => {
		//隐藏下面4个元素
		formEl[7][0].hidden = flag == "0"
		formEl[7][1].hidden = flag == "0"
		formEl[8][0].hidden = flag == "0"
		formEl[8][1].hidden = flag == "0"
		formEl[7][1].disabled = isLow == "0"
		formEl[8][1].disabled = isProp == "0"

		if (flag == "0") {
			formModelData.value.lowValueType = ""
			formModelData.value.lowValue = "0"

			formModelData.value.propertyType = ""
			formModelData.value.property = "0"
		}
		if (isProp == "0" && flag == "1") {
			formModelData.value.propertyType = ""
		}
		if (isLow == "0" && flag == "1") {
			formModelData.value.lowValueType = ""
		}
	},
	{ immediate: true }
)

onMounted(async () => {
	Promise.all([getLowDict(), getPropertyDict()]).then(() => {
		formEl[7][1].data = lowValueTypeDict.value
		formEl[8][1].data = propertyTypeDict.value
		getInfo()
	})
})
defineOptions({
	name: "matTypeDrawer"
})
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<div class="drawer-column">
			<div class="rows">
				<Title :title="leftTitle" />
				<el-scrollbar>
					<el-form
						style="padding-bottom: 30px"
						class="content form-base"
						ref="refForm"
						:model="formModelData"
						:rules="rules"
						label-position="top"
						:disabled="props.model == 'view'"
					>
						<FormElement :form-element="formEl" :form-data="formModelData" />
					</el-form>
				</el-scrollbar>
			</div>
			<ButtonList
				class="footer"
				:button="props.model == 'view' ? formBtnListView : formBtnList"
				@onBtnClick="onFormBtnList"
			/>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.drawer-column {
	width: 100%;
}
</style>
