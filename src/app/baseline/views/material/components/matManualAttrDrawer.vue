<!-- 废弃 时间：2024-10-30 -->
<script lang="ts" setup>
import { ref } from "vue"
import { ElMessage } from "element-plus"
import { MatApplyApi } from "@/app/baseline/api/material/matApply"
import TreeMatAttrData from "@/app/baseline/views/components/treeMatAttrData.vue"
import { DictApi } from "@/app/baseline/api/dict"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import { MatApplyObj } from "@/app/baseline/views/material/components/define"
import { useDictInit } from "../../components/dictBase"

const props = defineProps({
	id: {
		type: [String, Number],
		required: false
	},
	selMatList: {
		type: [Array],
		required: true,
		default: []
	}
})

const { dictFilter, getDictByCodeList } = useDictInit()

const drawerLeftTitle = {
	name: ["物资编码信息"],
	icon: ["fas", "square-share-nodes"]
}
const drawerRightTitle = {
	name: ["物资性质"],
	icon: ["fas", "square-share-nodes"]
}
const formBtnList = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "保存",
		icon: ["fas", "floppy-disk"]
	}
]

const tableEndBtn = ref([
	{
		name: "移除",
		icon: ["fas", "trash-alt"],
		disabled: true
	}
])
const drawerLoading = ref(false)
const emits = defineEmits(["onSaveOrClose"])

// 左侧按钮点击
const formModal = ref<any>({
	ids: "",
	attribute: ""
})
const treeList = ref() //保存用户当前点击树信息
const treeClick = (selected: any) => {
	treeList.value = selected
}
const onFormBtnList = (btnName: string | undefined) => {
	if (btnName === "保存") {
		//table
		const arrId = tableData.value.map((item) => item.id)
		if (arrId.length <= 0) {
			ElMessage.warning("需选择要变更的物资")
			return
		}

		//选择的物资性质
		if (treeList.value) {
			formModal.value.attribute = treeList.value.value
		} else {
			ElMessage.warning("请选择物资性质")
			return
		}

		formModal.value.ids = arrId.join(",")
		drawerLoading.value = true
		MatApplyApi.updateMatAttributeBatch(formModal.value)
			.then(() => {
				ElMessage.success("修改成功")
				emits("onSaveOrClose", true)
			})
			.finally(() => {
				drawerLoading.value = false
			})
	}
	if (btnName === "取消") {
		emits("onSaveOrClose", false)
		return
	}
}
const tableRef = ref()
const selectedTableList = ref<{ [propName: string]: any }[]>([]) //表格选中行
const onDataSelected = (rowList: { [propName: string]: any }[]) => {
	//rowList有bug
	selectedTableList.value = tableRef.value.pitayaTableRef!.getSelectionRows()
	const isNoSel = selectedTableList.value.length <= 0
	tableEndBtn.value[0].disabled = isNoSel
}

const onTableEndBtn = (btnName: any) => {
	switch (btnName) {
		case "移除":
			handleRemove()
			break
	}
}
const handleRemove = () => {
	const ids = selectedTableList.value.map((item) => item.id)
	const filteredArray = tableData.value.filter((obj) => !ids.includes(obj.id))
	tableData.value = filteredArray
}
const tableData = ref<any[]>(props.selMatList)

const tableProp: TableColumnType[] = [
	{ label: "物资编码", prop: "code", width: 150 },
	{ label: "物资名称", prop: "label", minWidth: 200 },
	{ label: "物资分类编码", prop: "materialTypeCode", width: 100 },
	{
		label: "物资分类名称",
		prop: "materialTypeLabel",
		minWidth: 200
	},
	{
		label: "规格型号",
		prop: "version",
		needSlot: false,
		width: 100
	},
	{
		label: "技术参数",
		prop: "technicalParameter",
		needSlot: false,
		minWidth: 100
	},
	{ label: "采购单位", prop: "buyUnit", needSlot: true, width: 85 },
	{
		label: "物资性质",
		prop: "attribute",
		needSlot: true,
		width: 130
	}
]
defineOptions({
	name: "MatManualAttrDrawer"
})

onMounted(() => {
	getDictByCodeList(["INVENTORY_UNIT"])
})
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<!-- 左侧表单区域 -->
		<div class="drawer-column left">
			<Title :title="drawerLeftTitle" />
			<PitayaTable
				ref="tableRef"
				:columns="tableProp"
				:table-data="tableData"
				:need-index="true"
				:need-selection="true"
				:need-pagination="false"
				@onSelectionChange="onDataSelected"
			>
				<template #attribute="{ rowData }">
					<dict-tag
						:options="DictApi.getMatAttr()"
						:value="rowData.attribute"
					/>
				</template>
				<template #buyUnit="{ rowData }">
					{{
						dictFilter("INVENTORY_UNIT", rowData.buyUnit)?.subitemName || "---"
					}}
				</template>
				<template #footerOperateLeft>
					<ButtonList
						:is-not-radius="true"
						:button="tableEndBtn"
						@on-btn-click="onTableEndBtn"
					/>
				</template>
			</PitayaTable>
		</div>
		<!-- 右侧table区域 -->
		<div class="drawer-column right">
			<div class="rows">
				<Title :title="drawerRightTitle" />
				<TreeMatAttrData @on-tree-click="treeClick" />
			</div>
			<ButtonList
				class="footer"
				:button="formBtnList"
				@on-btn-click="onFormBtnList"
			/>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index";

.drawer-container {
	.left {
		width: calc(100% - 310px);
	}

	.right {
		width: 310px;
	}
}
</style>
