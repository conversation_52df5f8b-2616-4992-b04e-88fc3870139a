<!-- 废弃 时间：2024-10-30 -->
<script lang="ts" setup>
import { watch, toRef, ref, reactive } from "vue"
import { ElMessage, type FormRules } from "element-plus"
import TableFile from "../../components/tableFile.vue"
import FormApply from "./apply/formApply.vue"
import { MatApplyApi } from "@/app/baseline/api/material/matApply"
import {
	MatApplyObj,
	MaterialProcureInfo
} from "@/app/baseline/views/material/components/define.d"
import FormBuyInfo from "@/app/baseline/views/material/components/manual/formBuyInfo.vue"
import { appStatus, fileBusinessType } from "@/app/baseline/api/dict"
import { useUserStore } from "@/app/platform/store/modules/user"
import { validatePrice } from "@/app/baseline/utils/validate"
const userStore = useUserStore()
const { userInfo } = storeToRefs(userStore)
const props = defineProps({
	id: {
		type: [String, Number],
		required: false
	},
	model: {
		type: [String],
		default: "edit",
		requeired: false
	},
	materialProcureInfo: {
		type: Object,
		default: () => null,
		requeired: false
	}
})
const loading = ref(false)
const drawerLeftTitle = {
	name: ["物资基本信息"],
	icon: ["fas", "square-share-nodes"]
}
const drawerRightTitle = {
	name: ["扩展信息"],
	icon: ["fas", "square-share-nodes"]
}
const formBtnList = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "保存",
		icon: ["fas", "floppy-disk"]
	}
]
const tabList = ["采购信息", "相关附件"]
//物资信息表单
const formModal = reactive<Record<string, any>>({
	id: props.id,
	conversionUnitText: "1:1"
})

//采购信息表单

const ruleFormRef = ref<any>(null)

const formModalProcureInfoList = ref<MaterialProcureInfo[]>([])
const formRulesProcureInfo = reactive<FormRules<typeof formModal>>({
	evaluationCycle: [
		{ required: true, trigger: "change", message: "预估采购周期不能为空" }
	],
	evaluation: [
		{ required: true, trigger: "change", message: "预估采购单价不能为空" },
		{
			validator: validatePrice,
			required: true,
			trigger: "change"
		}
	],
	safeStock: [
		{
			required: true,
			trigger: "change",
			message: "安全库存不能为空"
		}
	],
	minBuyNum: [
		{ required: true, trigger: "change", message: "最小订货数量不能为空" }
	]
})

const drawerLoading = ref(false)
const childTableLoading = ref(false)
const emits = defineEmits(["onSaveOrClose"])
const currentId = toRef(props, "id")

const getMatApplyInfo = () => {
	drawerLoading.value = true
	MatApplyApi.getMatApplyById(currentId.value)
		.then((res: any) => {
			Object.assign(formModal, res)
			// formModal.materialTypeCode = res.materialType.code
			// formModal.materialTypeLabel = res.materialType.label
			formModal.useUnit = String(res.useUnit)
			formModal.buyUnit = String(res.buyUnit)
			formModal.useAlonePeriod = res.useAlonePeriod
				? String(res.useAlonePeriod)
				: ""
		})
		.finally(() => {
			drawerLoading.value = false
		})
}
const isCanPublish = ref(false)
const getMatProcureInfo = () => {
	childTableLoading.value = true
	// if (props.materialProcureInfo) {
	// 	formModalProcureInfoList.value = [props.materialProcureInfo]
	// } else {
	MatApplyApi.getMatProcureInfoList({ materialId: currentId.value })
		.then((res: any) => {
			formModalProcureInfoList.value = res
			//找到是否自己公司的，且看当前审核状态
			const item = formModalProcureInfoList.value.find(
				(obj) => obj.sysCommunityId === userInfo.value.companyId
			)
			isCanPublish.value =
				item &&
				[appStatus.pendingApproval, appStatus.rejected].includes(item.bpmStatus)
		})
		.finally(() => {
			childTableLoading.value = false
		})
	//}
}
// 左侧按钮点击
const onFormBtnList = (btnName: string | undefined) => {
	if (btnName === "保存") {
		ruleFormRef.value?.$refs.ruleFormRef[0].validate((valid: any) => {
			if (valid) {
				const targetItem = formModalProcureInfoList.value.find(
					(item) => item.disabled !== true
				)
				if (targetItem) {
					loading.value = true
					drawerLoading.value = true
					const params: Record<string, any> = { ...targetItem }

					// 过滤值为 '---'  =》 ''
					const keys = Object.keys(params)
					keys.forEach((k: string) => {
						if (params[k] == "---") {
							params[k] = ""
						}
					})

					if (targetItem.id) {
						MatApplyApi.updateMatProcureInfo(params)
							.then(() => {
								ElMessage.success("保存成功")
								emits("onSaveOrClose", true)
							})
							.finally(() => {
								loading.value = false
								drawerLoading.value = false
							})
					} else {
						MatApplyApi.addMatProcureInfo(params)
							.then(() => {
								ElMessage.success("保存成功")
								emits("onSaveOrClose", true)
							})
							.finally(() => {
								loading.value = false
								drawerLoading.value = false
							})
					}
				}
			}
		})
	}
	if (btnName === "提交审核") {
		ruleFormRef.value.$refs.ruleFormRef[0].validate((valid: any) => {
			if (valid) {
				const targetItem = formModalProcureInfoList.value.find(
					(item) => item.disabled !== true
				)
				if (targetItem) {
					const params = {
						id: targetItem.id,
						materialId: targetItem.materialId,
						evaluationCycle: targetItem.evaluationCycle,
						evaluation: targetItem.evaluation,
						safeStock: targetItem.safeStock,
						minBuyNum: targetItem.minBuyNum,
						sysCommunityId: targetItem.sysCommunityId
					}
					MatApplyApi.publishApplyProcureInfo(params)
						.then(() => {
							emits("onSaveOrClose", true)
							ElMessage.success("提交审核成功")
						})
						.finally(() => {
							drawerLoading.value = false
						})
				}
			}
		})
	}
	if (btnName === "取消") {
		emits("onSaveOrClose", false)
		return
	}
}

//扩展栏标签页切换
const activeTab = ref<number>(0)
const handleTabChange = (index: number) => {
	activeTab.value = index
}

watch(
	() => props.id,
	(id: any) => {
		if (id) {
			getMatApplyInfo()
			getMatProcureInfo()
		}
	},
	{ immediate: true }
)

defineOptions({
	name: "MatManualBuyDrawer"
})
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<!-- 左侧表单区域 -->
		<div class="drawer-column left">
			<div class="rows">
				<Title :title="drawerLeftTitle" />
				<el-scrollbar>
					<el-form
						style="padding-bottom: 30px"
						class="content form-base"
						:model="formModal"
						label-position="top"
						label-width="100px"
						disabled
					>
						<FormApply
							:form-data="formModal"
							:readonly="true"
							:need-eval="false"
						/>
					</el-form>
				</el-scrollbar>
			</div>
		</div>
		<!-- 右侧采购信息表单区域 -->
		<div class="drawer-column right">
			<div class="rows">
				<Title :title="drawerRightTitle">
					<Tabs :tabs="tabList" @on-tab-change="handleTabChange" />
				</Title>
				<el-scrollbar style="padding-bottom: 20px" v-if="activeTab === 0">
					<div class="content">
						<FormBuyInfo
							:form-data="formModalProcureInfoList"
							:material-id="(currentId as number)"
							:rules="formRulesProcureInfo"
							:model="props.model"
							ref="ruleFormRef"
							v-loading="childTableLoading"
						/>
					</div>
				</el-scrollbar>
				<div v-if="activeTab === 1">
					<!-- mod: view-edit 需要支持授权用户可以补充附件，但不能删除已有附件 -->
					<TableFile
						:business-type="fileBusinessType.matCode"
						:business-id="props.id"
						mod="view-edit"
						:table-loading="childTableLoading"
					/>
				</div>
			</div>
			<ButtonList
				v-if="props.model === 'edit'"
				class="footer"
				:button="formBtnList"
				:loading="loading"
				@on-btn-click="onFormBtnList"
			/>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.drawer-container {
	.left {
		width: 600px;
	}
	.right {
		width: calc(100% - 600px);
	}
}
</style>
