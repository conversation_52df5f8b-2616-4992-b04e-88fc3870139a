<!-- 物资分类 自定义其他属性 -->
<template>
	<PitayaTable ref="tableRef" :table-data="tableData" :columns="columns">
		<template #propertyName="{ rowData }">
			<div v-if="editRowId === rowData.id">
				<el-input size="small" v-model.trim="tempRowData.propertyName" />
			</div>
			<div v-else>{{ rowData.propertyName }}</div>
		</template>
		<template #propertyType="{ rowData }">
			<div v-if="editRowId === rowData.id">
				<el-select size="small" v-model="tempRowData.propertyType">
					<el-option
						v-for="op in propertyTypeOption"
						:label="op.label"
						:value="op.value"
						:key="op.label"
					/>
				</el-select>
			</div>
			<div v-else>
				{{
					propertyTypeOption.find((item) => item.value === rowData.propertyType)
						?.label
				}}
			</div>
		</template>
		<template #optionList="{ rowData }">
			<div v-if="editRowId === rowData.id">
				<template v-if="tempRowData.propertyType === 2">
					<el-select
						size="small"
						class="m-2 textAlign"
						style="width: calc(100% - 24px)"
						multiple
						filterable
						allow-create
						:reserve-keyword="false"
						@keyup.enter="handleEnter"
						v-model="tempRowData.optionList"
					>
						<template #tag>
							<div
								v-for="item in tempRowData.optionList"
								:key="'custom-tag-' + item"
							>
								{{ item }}
							</div>
						</template>
						<el-option
							v-for="item in tempRowData.optionList"
							:label="item"
							:key="'tempItem' + item"
							:value="item"
						/>
					</el-select>
				</template>
				<span v-else>---</span>
			</div>
			<div v-else>{{ rowData.optionList || "---" }}</div>
		</template>
		<template #sortedBy="{ rowData }">
			<div v-if="editRowId === rowData.id">
				<el-input
					class="numInput"
					size="small"
					v-model="tempRowData.sortedBy"
					type="number"
				/>
			</div>
			<div v-else>
				{{ rowData.sortedBy }}
			</div>
		</template>
		<template #operations="{ rowData }">
			<div v-if="editRowId === rowData.id">
				<el-button v-btn link @click.stop="onSave(rowData)">
					<font-awesome-icon
						:icon="['fas', 'pen-to-square']"
						style="color: var(--pitaya-btn-background)"
					/>
					<span class="table-inner-btn">保存</span>
				</el-button>
				<el-button v-btn link @click.stop="onClose()">
					<font-awesome-icon
						:icon="['fas', 'circle-minus']"
						style="color: var(--pitaya-btn-background)"
					/>
					<span class="table-inner-btn">取消</span>
				</el-button>
			</div>
			<div v-else>
				<el-button v-btn link @click.stop="onEdit(rowData)">
					<font-awesome-icon
						:icon="['fas', 'pen-to-square']"
						style="color: var(--pitaya-btn-background)"
					/>
					<span class="table-inner-btn">编辑</span>
				</el-button>
				<el-button v-btn link @click.stop="onRemove(rowData)">
					<font-awesome-icon
						:icon="['fas', 'trash-can']"
						style="color: var(--pitaya-btn-background)"
					/>
					<span class="table-inner-btn">移除</span>
				</el-button>
			</div>
		</template>
		<template #footerOperateLeft>
			<ButtonList
				:button="paginationBtnList"
				:isNotRadius="true"
				@onBtnClick="onPaginationBtnClick"
			/>
		</template>
	</PitayaTable>
</template>
<script lang="ts" setup>
import _ from "lodash"
import { CustomMessageBox } from "@/app/platform/utils/message"
import { MatTypeApi } from "@/app/baseline/api/material/matType"

interface Props {
	id: any
}
const prop = defineProps<Props>()
const paginationBtnList = ref([
	{
		name: "添加",
		icon: ["fas", "circle-plus"]
	}
])
const columns = ref<TableColumnType[]>([
	{ prop: "propertyName", label: "字段名称", needSlot: true },
	{ prop: "propertyType", label: "类型", width: 120, needSlot: true },
	{
		prop: "optionList",
		width: 300,
		label: "枚举值",
		showOverflowTooltip: false,
		needSlot: true
	},
	{ prop: "sortedBy", label: "排序", needSlot: true },
	{
		label: "操作",
		prop: "operations",
		fixed: "right",
		needSlot: true,
		width: 140
	}
])
const onPaginationBtnClick = () => {
	if (editRowId.value) {
		return
	}
	const newValue = {
		id: Date.now(),
		propertyType: 1,
		sortedBy: 1000,
		isNew: 1,
		materialTypeId: prop.id
	}
	//使用isNew临时字段记录数据状态
	// 1 为未保存的新数据
	// 2 点击保存按钮后的新数据
	tableData.value.push(newValue)
	onEdit(newValue)
}
const tableData = ref<any[]>([])
const oldIdList = ref<number[]>([])
const editRowId = ref<any>(null)
const tempRowData = ref<any>()

const init = (rows: any[]) => {
	tableData.value = rows
	oldIdList.value = rows.map((item) => item.id)
}
const propertyTypeOption = [
	{ value: 1, label: "文本" },
	{ value: 2, label: "选择项" }
]

// 获取要插入/更新的数据、  要移除的数据
const getRows = () => {
	if (editRowId.value) {
		ElMessage.warning("请先保存自定义属性")
		return
	}
	const nowIdList = tableData.value.map((item) => item.id)
	const removeIdList = _.difference(oldIdList.value, nowIdList)
	const updateInfo = tableData.value.map((item) => {
		const temp = { ...item }
		if (temp.isNew === 2) {
			temp.id = ""
		}
		return temp
	})
	return [updateInfo, removeIdList]
}
const hasEmptyValue = (array: any) => {
	for (let i = 0; i < array.length; i++) {
		if (
			array[i] === null ||
			array[i] === undefined ||
			array[i] === "" ||
			(typeof array[i] === "string" && array[i].trim() === "") ||
			(Array.isArray(array[i]) && array[i].length === 0)
		) {
			return true
		}
	}
	return false
}
const onSave = (rowData: any) => {
	if (!tempRowData.value.propertyName) {
		return ElMessage.warning("字段名称不能为空")
	}
	if (tempRowData.value.propertyType === 2) {
		if (
			!tempRowData.value.optionList ||
			tempRowData.value.optionList.length === 0
		) {
			return ElMessage.warning("请填写枚举值")
		}

		const hasEmptyValueResult = hasEmptyValue(tempRowData.value.optionList)
		if (hasEmptyValueResult) {
			return ElMessage.warning("枚举值选项不能为空")
		}
		console.log(tempRowData.value.optionList)
	}
	const findRow = tableData.value.find((item: any) => item.id === rowData.id)
	findRow.propertyName = tempRowData.value.propertyName
	findRow.propertyType = tempRowData.value.propertyType
	if (tempRowData.value.propertyType === 1) {
		findRow.optionList = ""
	} else {
		findRow.optionList = tempRowData.value.optionList.join(",")
	}
	findRow.sortedBy = tempRowData.value.sortedBy
	// 保存的时候判断是否是新增数据，如果是设置isNew = 2
	if (findRow.isNew === 1) {
		findRow.isNew = 2
	}
	//重新对表格进行排序
	tableData.value = tableData.value.sort((a, b) => a.sortedBy - b.sortedBy)

	onClose()
}
const onClose = () => {
	if (tempRowData.value.isNew) {
		//取消的时候判断是否是新增的数据，如果是新增的，移除掉
		tableData.value = tableData.value.filter((item) => item.isNew !== 1)
	}
	editRowId.value = ""
	tempRowData.value = ""
}
const onEdit = (rowData: any) => {
	editRowId.value = rowData.id

	console.log("rowData.optionList", rowData.optionList)
	tempRowData.value = {
		...rowData,
		optionList: rowData.optionList ? rowData.optionList.split(",") : []
	}
}

const onRemove = (rowData: any) => {
	CustomMessageBox({ message: "确定要移除吗?" }, (res) => {
		if (res) {
			tableData.value = tableData.value.filter((item) => item.id !== rowData.id)
		}
	})
}

const handleEnter = (event: any) => {
	if (tempRowData.value.optionList.includes(event.target.value)) {
		return
	}
	if (event.target.value === "") return
	tempRowData.value.optionList.push(event.target.value)
}

onMounted(() => {
	if (prop.id) {
		MatTypeApi.getMatTypeProperties({ materialTypeId: prop.id }).then(
			(res: any) => {
				init(res)
			}
		)
	} else {
		init([])
	}
})

defineOptions({
	name: "CustomProperties"
})
defineExpose({
	getRows,
	fetchTableData: () => {
		MatTypeApi.getMatTypeProperties({ materialTypeId: prop.id }).then(
			(res: any) => {
				init(res)
			}
		)
	}
})
</script>
<style scoped lang="scss">
.cell > div {
	overflow: hidden;
	text-overflow: unset !important;
}
.app-el-scrollbar-wrapper {
	flex: 1;
	height: 0;
}
.textAlign {
	:deep(.el-input__inner) {
		text-align: center;
	}
}
:deep(.el-tag) {
	justify-content: baseline !important;
	padding: 0 5px !important;
	font-size: 12px !important;
	line-height: 18px !important;
	height: 18px !important;
}
:deep(.el-select-tags-wrapper) {
	text-align: left;
}
</style>
