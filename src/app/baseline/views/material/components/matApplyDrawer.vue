<script lang="ts" setup>
import { watch, ref, reactive } from "vue"
import { ElMessage, type FormInstance, type FormRules } from "element-plus"
import TableFile from "../../components/tableFile.vue"
import TableSimilar from "./apply/tableSimilar.vue"
import FormApply from "./apply/formApply.vue"
import { MatApplyApi } from "@/app/baseline/api/material/matApply"
import { MatApplyObj } from "@/app/baseline/views/material/components/define"
import {
	filterMultipleSpaces,
	getIdempotentToken,
	maxValidateErrorInfo,
	maxValidateNum,
	validateNumber
} from "@/app/baseline/utils/validate"
import {
	fileBusinessType,
	tableSimilarTypeDefault
} from "@/app/baseline/api/dict"
import XEUtils from "xe-utils"
import tableSimilarDetail from "./apply/tableSimilarDetail.vue"
import { ISimilarityType, IShowType } from "@/app/baseline/utils/types/material"
import { useMessageBoxInit } from "../../components/messageBox"
import { toNumber } from "lodash-es"
import {
	IIdempotentTokenType,
	IIdempotentTokenTypePre
} from "@/app/baseline/utils/types/common"

interface Props {
	id: string
}

const props = defineProps<Props>()
const currentId = ref(props.id)
const emits = defineEmits(["onSaveOrClose"])

const { showWarnConfirm } = useMessageBoxInit()

const drawerLeftTitle = {
	name: props.id ? ["编辑物资编码"] : ["新增物资编码"],
	icon: ["fas", "square-share-nodes"]
}
const drawerRightTitle = {
	name: ["扩展信息"],
	icon: ["fas", "square-share-nodes"]
}
const formBtnList = [
	{ name: "取消", icon: ["fas", "circle-minus"] },
	{ name: "保存草稿", icon: ["fas", "floppy-disk"] }
]

const submitBtnList = computed(() => [
	{
		name: "提交审核",
		icon: ["fas", "circle-check"],
		disabled: formModal.value?.id ? false : true
	}
])
const formBtnLoading = ref(false)

const tabList = ["相似物资", "相关附件"]

const formModal = ref<Record<string, any>>({
	id: currentId.value,
	conversionUnitText: "1:1",
	materialTypeCode: ""
})

/**
 * 保存旧的表单数据
 */
const oldFormData = ref<string>()

/**
 * 预估采购单价不能小于1
 * @param rule
 * @param value
 * @param callback
 */
const validatePrice = (rule: any, value: any, callback: any) => {
	if (!rule.required && !value) {
		callback()
	} else {
		if (value < 1) {
			return callback(new Error("预估采购单价不能小于1"))
		}
		callback()
	}
}

const formRules = computed<FormRules<typeof formModal.value>>(() => {
	return {
		attribute: [
			{ required: true, message: "物资性质不能为空", trigger: "change" }
		],
		/* auxiliaryQuality: [
			{ required: true, message: "辅助材质不能为空", trigger: "change" }
		], */
		evaluation: [
			{ required: true, message: "预估采购单价不能为空", trigger: "change" },
			{
				validator: validatePrice,
				required: true,
				trigger: "change"
			}
		],
		recoveryWeight: [
			{ required: true, message: "预估回收重量不能为空", trigger: "change" }

			/* {
				validator: validateNumber,
				required: true,
				trigger: "change"
			}, */
		],
		fixFlag: [
			{ required: true, message: "是否可维修不能为空", trigger: "change" }
		],
		recoveryFlag: [
			{ required: true, message: "是否可回收不能为空", trigger: "change" }
		],
		quality: [
			{
				required: true,
				message: "主要材质不能为空",
				trigger: "change"
			}
		],
		dangerousWasteFlag: [
			{
				required: formModal.value.wasteMaterialType == "1", // 废旧物资类型为B时 必填
				message: "危险废物类型不能为空",
				trigger: "change"
			}
		],
		wasteMaterialType: [
			{ required: true, message: "废旧物资类型不能为空", trigger: "change" }
		],
		materialTypeId: [
			{ required: true, message: "物资分类不能为空", trigger: "change" }
		],
		materialTypeCode: [
			{ required: true, message: "物资分类编码不能为空", trigger: "change" }
		],
		materialTypeLabel: [
			{ required: true, message: "物资分类名称不能为空", trigger: "change" }
		],
		label: [{ required: true, message: "物资名称不能为空", trigger: "change" }],
		//预估回收重量
		buyUnit: [
			{ required: true, message: "采购单位不能为空", trigger: "change" }
		],
		conversionUnit: [
			{ required: true, message: "库存转换比例不能为空", trigger: "change" }
		],
		useUnit: [
			{ required: true, message: "库存单位不能为空", trigger: "change" }
		],
		useAloneFlag: [
			{ required: true, message: "是否独立使用不能为空", trigger: "change" }
		],
		useAlonePeriod: [
			{
				required: formModal.value.useAloneFlag!,
				message: "使用期限不能为空",
				trigger: "change"
			}
		],
		version: [
			{ required: true, message: "规格型号不能为空", trigger: "change" }
		],
		technicalParameter: [
			{ required: true, message: "技术参数不能为空", trigger: "change" }
		]
	}
})
const drawerLoading = ref(false)
const childTableLoading = ref(false)
const ruleFormRef = ref<FormInstance>()

const getMatApplyInfo = () => {
	drawerLoading.value = true
	MatApplyApi.getMatApplyById(currentId.value)
		.then((res: any) => {
			Object.assign(formModal.value, res)
			// formModal.materialTypeCode = res.materialType.code
			// formModal.materialTypeLabel = res.materialType.label
			formModal.value.useUnit = String(res.useUnit)
			formModal.value.buyUnit = String(res.buyUnit)
			formModal.value.useAlonePeriod = res.useAlonePeriod
				? String(res.useAlonePeriod)
				: ""

			setTimeout(() => {
				tableSimilarDetailRef.value?.getTableData()
			}, 0)

			oldFormData.value = JSON.stringify(formModal.value)
		})
		.finally(() => {
			drawerLoading.value = false
		})
}
function handleSaveDraft() {
	if (!ruleFormRef.value) {
		return
	}

	ruleFormRef.value.validate(async (valid) => {
		if (!valid) {
			return
		}
		formBtnLoading.value = true
		// 表单校验通过
		try {
			const params = { ...formModal.value }
			// 处理一些字段
			params.conversionUnit = 1

			if (currentId.value) {
				params.id = currentId.value
				await MatApplyApi.updateMatApply(params)
			} else {
				const idempotentToken = getIdempotentToken(
					IIdempotentTokenTypePre.apply,
					IIdempotentTokenType.materialCode
				)

				const res = await MatApplyApi.addMatApply(params, idempotentToken)

				currentId.value = res.id as any
				formModal.value.id = res.id
			}

			oldFormData.value = JSON.stringify(formModal.value)

			getMatApplyInfo()

			ElMessage.success("操作成功")
			emits("onSaveOrClose", "save")
		} finally {
			formBtnLoading.value = false
		}
	})
}

const onFormBtnList = async (btnName: string | undefined) => {
	if (!ruleFormRef.value) return
	if (btnName === "保存草稿") {
		handleSaveDraft()
		tableSimilarDetailRef.value?.getTableData()

		emits("onSaveOrClose", "save")
	}
	if (btnName === "提交审核") {
		await showWarnConfirm("请确认是否提交本次数据？")
		formBtnLoading.value = true
		drawerLoading.value = true

		try {
			// 如果主表有修改，则先更新主表数据
			if (oldFormData.value != JSON.stringify(formModal.value)) {
				await MatApplyApi.updateMatApply(formModal.value)
			}

			const res = await MatApplyApi.getSimilarityMatTypePaged({
				filteredId: formModal.value.id,
				similarityType: tableSimilarTypeDefault.value,
				showType: "01", //tableSimilarDetailRef.value.similarShowType,
				label: formModal.value.label,
				version: formModal.value.version,
				technicalParameter: formModal.value.technicalParameter,
				attribute: formModal.value.attribute,
				photoName: formModal.value.photoName
			} as any)
			if (res && res.length > 0) {
				await showWarnConfirm(
					`系统为您匹配到在（${res.join(
						"，"
					)}）分类下存在相似物资，请您仔细核对再行决定是否继续后续的申请流程！`
				)
			}

			const formData = new FormData()
			formData.append("id", currentId?.value)

			const idempotentToken = getIdempotentToken(
				IIdempotentTokenTypePre.other,
				IIdempotentTokenType.materialCode,
				currentId?.value
			)
			await MatApplyApi.publishApply(formData as any, idempotentToken)
			ElMessage.success("提交审核成功")
			emits("onSaveOrClose", "pub")
		} finally {
			formBtnLoading.value = false
			drawerLoading.value = false
		}
	}
	if (btnName === "取消") {
		ruleFormRef.value?.clearValidate()
		emits("onSaveOrClose", "cancel")
		return
	}
}

//扩展栏标签页切换
const activeTab = ref<number>(0)
const handleTabChange = (index: number) => {
	/* currentType.value = tab.props.name */
	// if (index == 1 && !currentId.value) {
	// 	ElMessage.warning("请先保存物资编码信息，再上传相关附件")
	// 	activeTab.value = 0
	// 	return
	// }
	activeTab.value = index

	if (activeTab.value == 0) {
		setTimeout(() => {
			tableSimilarDetailRef.value?.getTableData()
		}, 0)
	}
}
/**
 * 是否独立使用 change
 */
function handeChangeUseAloneFlag() {
	formModal.value.useAlonePeriod = ""
	setTimeout(() => {
		ruleFormRef.value?.clearValidate()
	}, 0)
}

/**
 * 危险废物类型
 */
function handleChangeWasteMatType() {
	formModal.value.dangerousWasteFlag = ""
	setTimeout(() => {
		ruleFormRef.value?.clearValidate()
	}, 0)
}

/**
 * 是否可回收
 */
function handleChangeRecoveryFlag() {
	// 不可回收时 清空主要材质/辅助材质/废旧物资类型/危险废物类型/预估回收重量
	formModal.value.quality = ""
	formModal.value.auxiliaryQuality = ""
	formModal.value.dangerousWasteFlag = ""
	formModal.value.dangerousWasteFlag = ""
	formModal.value.recoveryWeight = 0

	setTimeout(() => {
		ruleFormRef.value?.clearValidate()
	}, 0)
}
/* watch(
	() => props.id,
	(id: any) => {
		if (id) {
			getMatApplyInfo()
		}
	},
	{ immediate: true }
) */

onMounted(() => {
	if (props.id) {
		getMatApplyInfo()
	}
})
defineOptions({
	name: "MatApplyDrawer"
})

/**
 * 相似物资 相关 ------------------------------------------------------------
 */
const tableSimilarDetailRef = ref()

/**
 * 更新物资图片
 */
function handleSetPhotoName(filePath?: string) {
	formModal.value.photoName = filePath

	/**
	 * 1.对接丰数接口 1
	 * 2.相似物资展示类型为 图片
	 */
	if (
		tableSimilarTypeDefault.value === ISimilarityType.otherSimilar &&
		tableSimilarDetailRef.value.similarShowType == IShowType.img
	) {
		setTimeout(() => {
			tableSimilarDetailRef.value?.getTableData()
		})
	}
}
/**
 * 更新相似物资列表
 */
function handleChangeSimilarMat(field: string, val: string) {
	formModal.value[field] = filterMultipleSpaces(val)
	if (tableSimilarDetailRef.value.similarShowType == IShowType.list) {
		if (
			formModal.value.label &&
			formModal.value.version &&
			formModal.value.technicalParameter
		) {
			setTimeout(() => {
				tableSimilarDetailRef.value?.getTableData()
			}, 1)
		}
	}
}
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<!-- 左侧表单区域 -->
		<div class="drawer-column left">
			<div class="rows">
				<Title :title="drawerLeftTitle" />
				<el-scrollbar>
					<el-form
						style="padding-bottom: 30px"
						class="content form-base"
						:model="formModal"
						:rules="formRules"
						ref="ruleFormRef"
						label-position="top"
						label-width="100px"
					>
						<FormApply
							:form-data="formModal"
							:need-eval="true"
							@setPhotoName="handleSetPhotoName"
							@changeWasteMatType="handleChangeWasteMatType"
							@changeRecoveryFlag="handleChangeRecoveryFlag"
							@changeUseAloneFlag="handeChangeUseAloneFlag"
							@changeSimilarMat="handleChangeSimilarMat"
						/>
					</el-form>
				</el-scrollbar>
			</div>
			<ButtonList
				class="footer"
				:button="formBtnList"
				:loading="formBtnLoading"
				@on-btn-click="onFormBtnList"
			/>
		</div>
		<!-- 右侧table区域 -->
		<div class="drawer-column right">
			<div
				:class="
					currentId ||
					(formModal.label &&
						formModal.version &&
						formModal.technicalParameter) ||
					formModal.photoName
						? 'rows'
						: 'rows disabled'
				"
			>
				<Title :title="drawerRightTitle">
					<Tabs
						class="tabs"
						:tabs="tabList"
						@on-tab-change="handleTabChange"
						:disabled="!currentId"
					/>
				</Title>
				<div v-if="activeTab == 0">
					<!-- <TableSimilar :table-loading="childTableLoading" /> -->

					<!-- 相似物资查询类型 similarityType:  0-一期接口  1-对接丰数接口;  -->
					<table-similar-detail
						ref="tableSimilarDetailRef"
						:id="formModal.id"
						:similarityType="tableSimilarTypeDefault"
						:table-req-params="
							tableSimilarTypeDefault == ISimilarityType.otherSimilar
								? {
										label: formModal.label,
										version: formModal.version,
										photoName: formModal.photoName,
										attribute: formModal.attribute,
										technicalParameter: formModal.technicalParameter
								  }
								: {
										label: formModal.label ? `*${formModal.label}*` : undefined,
										version: formModal.version
											? `*${formModal.version}*`
											: undefined,
										photoName: formModal.photoName,
										attribute: formModal.attribute,
										technicalParameter: formModal.technicalParameter
											? `*${formModal.technicalParameter}*`
											: undefined
								  }
						"
					/>
				</div>
				<div v-if="activeTab == 1" :class="currentId ? '' : 'disabled'">
					<TableFile
						:business-type="fileBusinessType.matCode"
						:business-id="currentId"
						:table-loading="childTableLoading"
					/>
				</div>
			</div>
			<ButtonList
				class="footer"
				:button="submitBtnList"
				:loading="formBtnLoading"
				@on-btn-click="onFormBtnList"
			/>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.drawer-container {
	.left {
		width: 700px;
	}
	.right {
		width: calc(100% - 700px);
	}
	.disabled {
		pointer-events: none;
		opacity: 0.3;
		/* 其他样式，根据需要自定义 */
	}

	:deep(.el-textarea__inner) {
		/* 针对 WebKit 浏览器隐藏滚动条 */
		-webkit-scrollbar: none;
		/* Firefox */
		scrollbar-width: none; /* Firefox 64+ */
	}
}
</style>
