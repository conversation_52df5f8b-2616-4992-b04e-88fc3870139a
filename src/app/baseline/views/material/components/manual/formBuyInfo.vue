<script setup lang="ts">
import { watch, toRef, ref, onMounted } from "vue"
import { useUserStore } from "@/app/platform/store/modules/user"
import { MaterialProcureInfo } from "@/app/baseline/views/material/components/define"
import { FormInstance } from "element-plus"
import XEUtils from "xe-utils"
import CustomInputNumber from "../../../components/inputNumber.vue"
import { appStatus, DictApi } from "@/app/baseline/api/dict"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import { useDictInit } from "../../../components/dictBase"
import { validateAndCorrectInput } from "@/app/baseline/utils/validate"
import { toNumber } from "lodash-es"

const { dictFilter, getDictByCodeList } = useDictInit()

interface Props {
	formData?: any[]
	materialId: number
	rules: []
	model: string
	ref: FormInstance
	detailInfo?: any
}
const props = defineProps<Props>()
const userStore = useUserStore()
const { userInfo } = storeToRefs(userStore)
const formList = toRef(props, "formData")
const getFormList = () => {
	if (formList.value) {
		for (const item of formList.value) {
			item.disabled = item.sysCommunityId != userInfo.value.companyId //|| ![appStatus.pendingApproval, appStatus.rejected].includes(item.bpmStatus)
			item.isShow =
				item.sysCommunityId == userInfo.value.companyId ||
				userInfo.value.companyType == 1
			if (item.disabled || props.model !== "edit") {
				const tmpEvaluation = `${XEUtils.commafy(item.evaluation, {
					digits: 5
				})}`
				item.evaluation_view = tmpEvaluation
					? `￥${tmpEvaluation}`
					: "￥0.00000"
				item.evaluationCycle = item.evaluationCycle
					? item.evaluationCycle
					: "---"
				/* item.minBuyNum = item.minBuyNum ? item.minBuyNum : "---"
				item.safeStock = item.safeStock ? item.safeStock : "---" */
			}
			//任何情况都是置灰的
			const tmpAmount = `${XEUtils.commafy(item.lastPurchaseUnitPrice, {
				digits: 5
			})}`
			item.lastPurchaseUnitPrice_view = tmpAmount
				? `￥${tmpAmount}`
				: "￥0.00000"
			item.lastPurchaseDate = item.lastPurchaseDate
				? item.lastPurchaseDate
				: "---"
		}
		const isHasSelf = formList.value.find((obj) => obj.isShow == true)
		if (!isHasSelf) {
			//如果没有，则补齐当前公司的空表单
			const item: MaterialProcureInfo = {
				materialId: props.materialId,
				sysCommunityId: userInfo.value.companyId,
				companyName: userInfo.value.companyName,
				disabled: false,
				lastPurchaseUnitPrice_view: "￥0.00",
				lastPurchaseDate: "---",
				isShow: true,
				useUnit: String(props.detailInfo?.useUnit),
				buyUnit: String(props.detailInfo?.buyUnit)
				/* evaluationCycle: 1,
				minBuyNum: 0 */
				//bpmStatus: appStatus.pendingApproval
			}
			formList.value.push(item)
		}
	}
}
const ruleFormRef = ref<FormInstance>()
const isNotValue = (item: any, attr: string) => {
	return (item.disabled || props.model !== "edit") && item[attr] == "---"
}
watch(
	() => props.formData,
	(val: any) => {
		if (val) {
			Object.assign(formList, val)
			getFormList()
		}
	},
	{ immediate: true }
)
onMounted(() => {
	getDictByCodeList(["INVENTORY_UNIT"])
})
</script>

<template>
	<template v-for="(item, index) in formList">
		<el-form
			v-if="item.isShow"
			class="form-base buy-container"
			:model="item"
			:rules="props.rules"
			:ref="item.disabled ? '' : 'ruleFormRef'"
			label-position="top"
			label-width="100px"
			:disabled="props.model !== 'edit'"
			:key="index"
		>
			<div class="buy-item">
				<div class="item-name">{{ item.companyName }}</div>
			</div>
			<div class="buy-item">
				<el-row :gutter="24">
					<el-col :span="4">
						<el-form-item
							label="预估采购周期"
							:prop="item.disabled ? '' : 'evaluationCycle'"
						>
							<custom-input-number
								:placeholder="
									isNotValue(item, 'evaluationCycle')
										? '---'
										: '请输入预估采购周期'
								"
								v-model.trim="item.evaluationCycle"
								:disabled="item.disabled"
								clearable
								:min="1"
								:max="11"
							>
								<template #append>月</template>
							</custom-input-number>
						</el-form-item>
					</el-col>
					<el-col :span="4">
						<el-form-item
							label="最小订货数量"
							:prop="item.disabled ? '' : 'minBuyNum'"
						>
							<el-input
								type="number"
								:placeholder="
									isNotValue(item, 'minBuyNum') ? '---' : '请输入最小订货数量'
								"
								v-model.trim="item.minBuyNum"
								:disabled="item.disabled"
								@input="item.minBuyNum = validateAndCorrectInput($event)"
								@blur="item.minBuyNum = toNumber($event.target.value)"
							>
								<template #append>
									{{
										dictFilter("INVENTORY_UNIT", item.buyUnit)?.subitemName ||
										"---"
									}}
								</template>
							</el-input>
						</el-form-item>
					</el-col>
					<el-col :span="4">
						<el-form-item
							label="预估采购单价"
							:prop="item.disabled ? '' : 'evaluation'"
						>
							<el-input
								v-if="item.disabled || props.model !== 'edit'"
								placeholder="请输入预估采购单价"
								v-model.trim="item.evaluation_view"
								:disabled="item.disabled"
							>
								<template #append>元</template>
							</el-input>
							<el-input
								type="number"
								v-else
								placeholder="请输入预估采购单价"
								v-model.trim="item.evaluation"
								@input="item.evaluation = validateAndCorrectInput($event, 5)"
								@blur="item.evaluation = toNumber($event.target.value) || 1"
							>
								<template #append>元</template>
							</el-input>
						</el-form-item>
					</el-col>
					<el-col :span="4">
						<el-form-item
							label="安全库存"
							:prop="item.disabled ? '' : 'safeStock'"
						>
							<el-input
								type="number"
								:placeholder="
									isNotValue(item, 'safeStock') ? '---' : '请输入安全库存'
								"
								v-model.trim="item.safeStock"
								:disabled="item.disabled"
								@input="item.safeStock = validateAndCorrectInput($event)"
								@blur="item.safeStock = toNumber($event.target.value) || 1"
							>
								<template #append>
									{{
										dictFilter("INVENTORY_UNIT", item.useUnit)?.subitemName ||
										"---"
									}}
								</template>
							</el-input>
						</el-form-item>
					</el-col>
					<el-col :span="4">
						<el-form-item
							label="最新采购单价"
							:prop="item.disabled ? '' : 'newEvaluation'"
							:class="{ 'item-input-point': item.lastPurchaseSupplier }"
							:title="item.lastPurchaseSupplier"
						>
							<el-input
								placeholder="请输入最新采购单价"
								v-model.trim="item.lastPurchaseUnitPrice_view"
								disabled
							>
								<template #append>元</template>
							</el-input>
						</el-form-item>
					</el-col>
					<el-col :span="4">
						<el-form-item
							label="最新采购时间"
							disabled
							:title="item.lastPurchaseSupplier"
							:class="{ 'item-input-point': item.lastPurchaseSupplier }"
						>
							<el-input
								placeholder="请输入最新采购时间"
								v-model.trim="item.lastPurchaseDate"
								disabled
								clearable
							/>
						</el-form-item>
					</el-col>
				</el-row>
			</div>
			<div class="buy-item" v-if="false">
				<el-form-item label="审批状态" style="width: 60px">
					<dict-tag :options="DictApi.getBpmStatus()" :value="item.bpmStatus" />
				</el-form-item>
			</div>
		</el-form>
		<el-divider v-if="item.isShow" />
	</template>
</template>

<style scoped lang="scss">
@import "@/app/baseline/assets/css/index.scss";

.el-divider--horizontal {
	margin: 5px 0 15px 0 !important;
	border-top: 1px dotted var(--el-border-color);
}

::v-deep(.item-input-point) {
	.el-input.is-disabled {
		cursor: pointer !important;
	}
	.el-input.is-disabled .el-input__inner {
		cursor: pointer !important;
	}
}

.buy-container {
	display: flex;
	justify-content: flex-start;
	width: 100%;
	.buy-item {
		margin-right: 10px;
		.item-name {
			display: flex;
			justify-content: center; /* 水平居中 */
			align-items: center; /* 垂直居中 */
			background: #eeffe7;
			height: calc(100% - 9px);
			border: 1px solid #81c100;
			border-radius: 3px;
			font-size: 12px;
			text-align: center;
			padding: 0 10px;
			color: $---font-color-2;
			width: 68px;
		}
	}
	.buy-item:last-child {
		margin-right: 0;
	}
}

:deep() {
	input::-webkit-outer-spin-button,
	input::-webkit-inner-spin-button {
		-webkit-appearance: none !important;
	}

	input[type="number"] {
		-moz-appearance: textfield;
	}
}
</style>
