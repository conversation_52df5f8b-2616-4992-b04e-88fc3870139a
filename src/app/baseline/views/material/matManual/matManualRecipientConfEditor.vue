<!-- 编码手册 - 接收人配置 选择器 -->
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<div class="drawer-column" style="width: 100%">
			<Title :title="titleConf" />

			<div class="rows mat-selector-table">
				<Query
					ref="queryRef"
					:query-arr-list="(queryConf as any)"
					style="margin: 10px 10px -10px"
					@get-query-data="getQueryData"
				/>
				<el-scrollbar>
					<pitaya-table
						ref="tableRef"
						:columns="tableProp"
						:table-data="tableData"
						:need-selection="true"
						:single-select="false"
						:need-index="true"
						:need-pagination="true"
						:total="pageTotal"
						:table-loading="tableLoading"
						@on-selection-change="selectedTableList = $event"
						@on-current-page-change="onCurrentPageChange"
					>
						<!-- 业务名称 -->
						<template #type="{ rowData }">
							{{ typeConf[rowData.type as IMaterialBusinessType]?.label }}
						</template>
						<template #sex="{ rowData }">
							{{ rowData.sex === 1 ? "男" : "女" }}
						</template>

						<template #footerOperateLeft>
							<ButtonList
								class="btn-list"
								:is-not-radius="true"
								:button="tableFooterBtns"
								:loading="tbBtnLoading"
								@on-btn-click="handleFooterBtnClick"
							/>
						</template>
					</pitaya-table>
				</el-scrollbar>
			</div>

			<button-list
				class="footer"
				:button="btnConf"
				:loading="tbBtnLoading"
				@on-btn-click="emit('close')"
			/>
		</div>

		<Drawer
			:size="modalSize.mmd"
			v-model:drawer="userSelectorVisible"
			:destroyOnClose="true"
		>
			<user-selector
				:table-fetch-params="{
					companyId: fetchParam.companyId,
					type: fetchParam.type
				}"
				@close="userSelectorVisible = false"
				@save="handleSaveUser"
			/>
		</Drawer>
	</div>
</template>

<script setup lang="ts">
import { useTbInit } from "../../components/tableBase"
import { BaseLineSysApi } from "@/app/baseline/api/system"
import { MatApplyApi } from "@/app/baseline/api/material/matApply"
import { useUserStore } from "@/app/platform/store/modules/user"
import { useMessageBoxInit } from "../../components/messageBox"
import userSelector from "./userSelector.vue"
import { modalSize } from "@/app/baseline/utils/layout-config"
import { IMaterialBusinessType } from "@/app/baseline/utils/types/material"

const emit = defineEmits<{
	(e: "close"): void
	(e: "update"): void
}>()

const { userInfo } = storeToRefs(useUserStore())
const { showDelConfirm } = useMessageBoxInit()

const titleConf = computed(() => ({
	name: ["配置接收人"],
	icon: ["fas", "square-share-nodes"]
}))

const queryRef = ref()
const companyList = ref([])

const typeConf = ref({
	[IMaterialBusinessType.materialSafeStock]: { label: "安全库存" },
	[IMaterialBusinessType.materialEvaluation]: { label: "预估采购单价" },
	[IMaterialBusinessType.materialQuality]: { label: "物资材质" },
	[IMaterialBusinessType.materialType]: { label: "物资性质" },
	[IMaterialBusinessType.materialFreeze]: { label: "物资冻结" },
	[IMaterialBusinessType.materialCancel]: { label: "物资作废" },
	[IMaterialBusinessType.materialThawing]: { label: "物资解冻" }
})
const queryConf = computed(() => {
	const ls = [
		{
			name: "业务名称",
			key: "type",
			placeholder: "请选择业务名称",
			type: "select",
			children: [
				{ label: "安全库存", value: IMaterialBusinessType.materialSafeStock },
				{
					label: "预估采购单价",
					value: IMaterialBusinessType.materialEvaluation
				},
				{ label: "物资材质", value: IMaterialBusinessType.materialQuality },
				{ label: "物资性质", value: IMaterialBusinessType.materialType },
				{ label: "物资冻结", value: IMaterialBusinessType.materialFreeze },
				{ label: "物资作废", value: IMaterialBusinessType.materialCancel },
				{ label: "物资解冻", value: IMaterialBusinessType.materialThawing }
			]
		},
		{
			name: "所属公司",
			key: "companyId",
			type: "select",
			children: companyList.value
		}
	]

	if (userInfo.value.companyType == "1") {
		return ls
	} else {
		return ls.filter((item) => item.key != "companyId")
	}
})

const drawerLoading = ref(false)

const tbBtnLoading = ref(false)

const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	selectedTableList,
	fetchParam,
	currentPage,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = useTbInit()

fetchFunc.value = MatApplyApi.getMessageReceiveUserPaged //listSysUserPaged

tableProp.value = [
	{ prop: "type", label: "业务名称", needSlot: true },
	{ prop: "realname", label: "用户姓名" },
	{ prop: "receiveUserName", label: "用户账号" },
	{ prop: "sex", label: "性别", needSlot: true },
	{ prop: "phone", label: "手机号" },
	{ prop: "sysOrgId_view", label: "部门" },
	{ prop: "companyId_view", label: "所属公司" }
]

const tableFooterBtns = computed(() => {
	return [
		{
			name: "添加接收人",
			icon: ["fas", "user-alt"]
		},
		{
			name: "批量移除",
			icon: ["fas", "trash-can"],
			disabled: selectedTableList.value.length > 0 ? false : true
		}
	]
})

/**
 * 按钮操作
 * 添加接收人/ 批量移除
 * @param btnName
 */
const userSelectorVisible = ref(false)
async function handleFooterBtnClick(btnName?: string) {
	if (btnName === "添加接收人") {
		if (!fetchParam.value.type) {
			return ElMessage.warning("请选择业务名称！")
		}

		if (!fetchParam.value.companyId) {
			return ElMessage.warning("请选择所属公司！")
		}

		userSelectorVisible.value = true
	} else {
		await showDelConfirm()
		try {
			tbBtnLoading.value = true
			const idList = selectedTableList.value.map((e) => e.id)
			await MatApplyApi.removeMessageReceiveUser(idList)
			ElMessage.success("操作成功")
			getQueryData()
		} finally {
			tbBtnLoading.value = false
		}
	}
}

async function handleSaveUser(rows: any, callback?: any) {
	if (!rows.length) {
		callback()
		return ElMessage.warning("请选择用户")
	}
	const userList = rows.map((e: any) => ({ id: e.id, username: e.username }))
	await MatApplyApi.addMessageReceiveUser({
		userList,
		type: fetchParam.value.type,
		companyId: fetchParam.value.companyId
	})

	ElMessage.success("操作成功")

	userSelectorVisible.value = false
	getQueryData()
}

const btnConf = computed(() => {
	return [
		{
			name: "取消",
			icon: ["fas", "circle-minus"]
		},
		{
			name: "保存",
			icon: ["fas", "circle-check"]
		}
	]
})

onMounted(() => {
	BaseLineSysApi.getCompanyAllList().then((r) => (companyList.value = r))
	if (userInfo.value.companyType != "1") {
		// 1:集团
		fetchParam.value.companyId = userInfo.value.companyId
	}
	getQueryData({})
})

watch(
	[
		() => queryRef.value?.queryData.type,
		() => queryRef.value?.queryData.companyId
	],
	() => {
		if (queryRef.value?.queryData.type) {
			fetchParam.value.type = queryRef.value?.queryData.type
			getQueryData()
		}

		if (queryRef.value?.queryData.companyId) {
			fetchParam.value.companyId = queryRef.value?.queryData.companyId
			getQueryData()
		}
	}
)
function getQueryData(e?: any) {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = { ...fetchParam.value, ...e }

	fetchTableData()
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.mat-selector-table {
	&:deep(.pitaya-table) {
		.error {
			.column-inner-item,
			.cell,
			.el-input__inner {
				color: red;
			}
		}
	}
}
</style>
