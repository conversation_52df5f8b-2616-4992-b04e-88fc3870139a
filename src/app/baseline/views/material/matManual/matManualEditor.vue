<!-- 维护采购信息/查看 重构V2.0 -->
<script lang="ts" setup>
import { watch, toRef, ref, reactive } from "vue"
import { ElMessage, type FormRules } from "element-plus"
import TableFile from "../../components/tableFile.vue"
import FormApply from "../components/apply/formApply.vue"
import { MatApplyApi } from "@/app/baseline/api/material/matApply"
import FormBuyInfo from "@/app/baseline/views/material/components/manual/formBuyInfo.vue"
import {
	fileBusinessType,
	tableSimilarTypeDefault
} from "@/app/baseline/api/dict"
import {
	IIdempotentTokenType,
	IIdempotentTokenTypePre,
	IModalType
} from "@/app/baseline/utils/types/common"
import tableSimilarDetail from "../components/apply/tableSimilarDetail.vue"
import { ISimilarityType, IShowType } from "@/app/baseline/utils/types/material"
import matManualRecordTable from "./matManualRecordTable.vue"
import { useMessageBoxInit } from "../../components/messageBox"
import { getIdempotentToken } from "@/app/baseline/utils/validate"

const props = withDefaults(
	defineProps<{
		id?: any // id
		mode: IModalType // create || edit || create
		footerBtnVisible?: boolean
	}>(),
	{ footerBtnVisible: true }
)

const emits = defineEmits<{
	(e: "close"): void
	(e: "update"): void
}>()

const { showWarnConfirm } = useMessageBoxInit()

const btnLoading = ref(false)
const drawerLeftTitle = {
	name: ["物资基本信息"],
	icon: ["fas", "square-share-nodes"]
}
const drawerRightTitle = {
	name: ["扩展信息"],
	icon: ["fas", "square-share-nodes"]
}
const formBtnList = computed(() => {
	if (props.mode === IModalType.view) {
		return [
			{
				name: "取消",
				icon: ["fas", "circle-minus"]
			}
		]
	} else {
		return [
			{
				name: "取消",
				icon: ["fas", "circle-minus"]
			},
			{
				name: "保存",
				icon: ["fas", "floppy-disk"]
			}
		]
	}
})
const tabList = ["采购信息", "相似物资", "相关附件", "操作记录"]
//物资信息表单
const formData = ref<Record<string, any>>({
	conversionUnitText: "1:1"
})

//采购信息表单

const ruleFormRef = ref<any>(null)

const formDataProcureInfoList = ref<any[]>()

/**
 * 预估采购单价不能小于1
 * @param rule
 * @param value
 * @param callback
 */
const validatePrice = (rule: any, value: any, callback: any) => {
	if (!rule.required && !value) {
		callback()
	} else {
		if (value < 1) {
			return callback(new Error("预估采购单价不能小于1"))
		}
		callback()
	}
}

const formRulesProcureInfo = reactive<FormRules<typeof formData>>({
	evaluationCycle: [
		{ required: true, trigger: "change", message: "预估采购周期不能为空" }
	],
	evaluation: [
		{ required: true, trigger: "change", message: "预估采购单价不能为空" },
		{
			validator: validatePrice,
			required: true,
			trigger: "change"
		}
	],
	safeStock: [
		{
			required: true,
			trigger: "change",
			message: "安全库存不能为空"
		}
	],
	minBuyNum: [
		{ required: true, trigger: "change", message: "最小订货数量不能为空" }
	]
})

const drawerLoading = ref(false)
const childTableLoading = ref(false)

const currentId = toRef(props, "id")

/**
 * 获取详情
 */
async function getMatApplyInfo() {
	if (!props.id) {
		return false
	}

	drawerLoading.value = true
	try {
		const res = await MatApplyApi.getMatApplyById(props.id)
		formData.value = { ...res }

		formData.value.conversionUnitText = "1:1"
		formData.value.wasteMaterialType = String(res.wasteMaterialType)
		formData.value.useUnit = String(res.useUnit)
		formData.value.buyUnit = String(res.buyUnit)
		formData.value.useAlonePeriod = res.useAlonePeriod
			? String(res.useAlonePeriod)
			: ""
	} finally {
		drawerLoading.value = false
	}
}

/**
 * 采购信息
 */
async function getMatProcureInfo() {
	if (!props.id) {
		return false
	}
	childTableLoading.value = true
	try {
		const res = await MatApplyApi.getMatProcureInfoList({
			materialId: props.id
		})
		formDataProcureInfoList.value = res || []
	} finally {
		childTableLoading.value = false
	}
}
// 左侧按钮点击
const onFormBtnList = (btnName: string | undefined) => {
	if (btnName === "保存") {
		ruleFormRef.value?.$refs.ruleFormRef[0].validate(async (valid: any) => {
			if (!valid) {
				return false
			}
			if (valid) {
				const targetItem = formDataProcureInfoList.value?.find(
					(item) => item.disabled !== true
				)
				if (targetItem) {
					const params: Record<string, any> = { ...targetItem }

					// 过滤值为 '---'  =》 ''
					const keys = Object.keys(params)
					keys.forEach((k: string) => {
						if (params[k] == "---") {
							params[k] = ""
						}
					})
					await showWarnConfirm("请确认是否保存本次数据？")
					try {
						btnLoading.value = true
						const api = targetItem.id
							? MatApplyApi.updateMatProcureInfo
							: MatApplyApi.addMatProcureInfo

						let idempotentToken = ""

						if (!targetItem.id) {
							idempotentToken = getIdempotentToken(
								IIdempotentTokenTypePre.other,
								IIdempotentTokenType.materialCode,
								targetItem?.materialId
							)
						}

						await api(params, idempotentToken)
						ElMessage.success("操作成功")
						emits("update")
						emits("close")
					} finally {
						btnLoading.value = false
					}
				}
			}
		})
	} else {
		emits("close")
	}
}

//扩展栏标签页切换
const activeTab = ref<number>(0)

const tableSimilarDetailRef = ref()
const handleTabChange = (index: number) => {
	activeTab.value = index

	if (index === 1) {
		nextTick(() => {
			tableSimilarDetailRef.value?.getTableData()
		})
	}
}

watch(
	() => props.id,
	(id: any) => {
		if (id) {
			getMatApplyInfo()
			getMatProcureInfo()
		}
	},
	{ immediate: true }
)

defineOptions({
	name: "MatManualBuyDrawer"
})
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<!-- 左侧表单区域 -->
		<div class="drawer-column left">
			<div class="rows">
				<Title :title="drawerLeftTitle" />
				<el-scrollbar>
					<el-form
						style="padding-bottom: 30px"
						class="content form-base"
						:model="formData"
						label-position="top"
						label-width="100px"
						disabled
					>
						<FormApply
							:form-data="formData"
							:readonly="true"
							:need-eval="false"
						/>
					</el-form>
				</el-scrollbar>
			</div>
		</div>
		<!-- 右侧采购信息表单区域 -->
		<div class="drawer-column right">
			<div class="rows">
				<Title :title="drawerRightTitle">
					<Tabs :tabs="tabList" @on-tab-change="handleTabChange" />
				</Title>
				<el-scrollbar style="padding-bottom: 20px" v-if="activeTab === 0">
					<div class="content">
						<FormBuyInfo
							:form-data="formDataProcureInfoList"
							:material-id="(currentId as number)"
							:rules="formRulesProcureInfo"
							:model="props.mode"
							ref="ruleFormRef"
							:detailInfo="formData"
							v-loading="childTableLoading"
						/>
					</div>
				</el-scrollbar>
				<div v-if="activeTab === 1">
					<!-- 相似物资查询类型 similarityType:  0-一期接口  1-对接丰数接口;  -->
					<table-similar-detail
						ref="tableSimilarDetailRef"
						:id="formData.id"
						:similarityType="tableSimilarTypeDefault"
						:table-req-params="
							tableSimilarTypeDefault == ISimilarityType.otherSimilar
								? {
										label: formData.label,
										version: formData.version,
										photoName: formData.photoName,
										attribute: formData.attribute,
										technicalParameter: formData.technicalParameter
								  }
								: {
										label: formData.label ? `*${formData.label}*` : undefined,
										version: formData.version
											? `*${formData.version}*`
											: undefined,
										photoName: formData.photoName,
										attribute: formData.attribute,
										technicalParameter: formData.technicalParameter
											? `*${formData.technicalParameter}*`
											: undefined
								  }
						"
					/>
				</div>
				<div v-if="activeTab === 2">
					<!-- mod: view-edit 需要支持授权用户可以补充附件，但不能删除已有附件 -->
					<TableFile
						:business-type="fileBusinessType.matCode"
						:business-id="props.id"
						mod="view-edit"
						:table-loading="childTableLoading"
					/>
				</div>

				<div v-if="activeTab === 3">
					<mat-manual-record-table :material-id="currentId" />
				</div>
			</div>
			<ButtonList
				v-if="props.footerBtnVisible"
				class="footer"
				:button="formBtnList"
				:loading="btnLoading"
				@on-btn-click="onFormBtnList"
			/>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.drawer-container {
	.left {
		width: 600px;
	}
	.right {
		width: calc(100% - 600px);
	}
}
</style>
