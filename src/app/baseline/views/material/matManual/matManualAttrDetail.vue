<!-- 变更物资性质 审批 -->
<script lang="ts" setup>
import { ref } from "vue"
import { MatApplyApi } from "@/app/baseline/api/material/matApply"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import { IModalType } from "@/app/baseline/utils/types/common"
import { useTbInit } from "@/app/baseline/views/components/tableBase"
import { useDictInit } from "../../components/dictBase"
import XEUtils from "xe-utils"
import { DictApi } from "@/app/baseline/api/dict"

const props = withDefaults(
	defineProps<{
		businessId?: any
		mode: IModalType // create || edit || create
		footerBtnVisible?: boolean
	}>(),
	{ footerBtnVisible: true }
)

const { dictFilter, getDictByCodeList } = useDictInit()

const drawerMidTitle = {
	name: ["物资信息"],
	icon: ["fas", "square-share-nodes"]
}

const drawerLoading = ref(false)

const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = useTbInit()

tableProp.value = [
	{ label: "物资编码", prop: "content_code", fixed: "left" },
	{ label: "物资名称", prop: "content_label" },
	{ label: "物资分类编码", prop: "content_materialTypeCode" },
	{ label: "物资分类名称", prop: "content_materialTypeLabel" },
	{ label: "规格型号", prop: "content_version" },
	{ label: "技术参数", prop: "content_technicalParameter" },
	{ label: "采购单位", prop: "content_buyUnit", needSlot: true },
	{ label: "更新前物资性质", prop: "beforeValue", needSlot: true },
	{ label: "更新后物资性质", prop: "afterValue", needSlot: true }
]

fetchFunc.value = (param: Record<string, any>) => {
	return new Promise((resolve) => {
		MatApplyApi.getMatOperateItemList({
			...param
		}).then((res: any[]) => {
			XEUtils.map(res || [], (item: Record<string, any>) => {
				if (item.content)
					Array.from(Object.keys(item?.content))?.map(
						(_k) => (item[`content_${_k}`] = item.content[_k])
					)
			})
			resolve(res)
		})
	})
}

onMounted(async () => {
	await getDictByCodeList(["INVENTORY_UNIT"])
	fetchParam.value.operateId = props.businessId

	fetchTableData()
})
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<!-- 中间table区域 -->
		<div class="drawer-column" style="width: 100%">
			<Title :title="drawerMidTitle" />
			<el-scrollbar>
				<PitayaTable
					ref="tableRef"
					:columns="tableProp"
					:table-data="tableData"
					:need-index="true"
					:need-pagination="true"
					:single-select="false"
					:need-selection="false"
					:total="pageTotal"
					@onSelectionChange="selectedTableList = $event"
					@on-current-page-change="onCurrentPageChange"
					:table-loading="tableLoading"
				>
					<!-- 采购单位 -->
					<template #content_buyUnit="{ rowData }">
						{{
							dictFilter("INVENTORY_UNIT", rowData.content_buyUnit)
								?.subitemName || "---"
						}}
					</template>

					<!-- 更新前物资性质 -->
					<template #beforeValue="{ rowData }">
						<dict-tag
							:options="DictApi.getMatAttr()"
							:value="rowData.beforeValue"
						/>
					</template>

					<!-- 更新后物资性质 -->
					<template #afterValue="{ rowData }">
						<dict-tag
							:options="DictApi.getMatAttr()"
							:value="rowData.afterValue"
						/>
					</template>
				</PitayaTable>
			</el-scrollbar>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
