<!-- 文件上传附件 -->
<script setup lang="ts">
import { reactive, ref, toRef, watchEffect } from "vue"
import EmptyData from "@/assets/images/empty/empty_data.svg?component"
import { FormInstance, FormRules, UploadInstance } from "element-plus"
import formElement from "@/app/baseline/views/components/formElement.vue"
//当前用户信息
import { useUserStore } from "@/app/platform/store/modules/user"
import { first } from "lodash-es"
import { MatApplyApi } from "@/app/baseline/api/material/matApply"
import { downloadExcel } from "@/app/baseline/utils/download"
const userStore = useUserStore()
const { userInfo } = storeToRefs(userStore)

interface FileTxtConf {
	title?: string //drawer 名称
	fileLable?: string // 上传表单名称
}

interface Props {
	saveParams?: Record<string, any> //保存时，URL上携带参数
	downloadApi?: Function // 下载模版方法
	checkUrl: string // 上传文件验证接口
	saveApi: Function // 保存数据接口
	fileTxtConf?: FileTxtConf
	allExtensions?: string[]
	accept?: string // 上传文件类型
	allowsize?: number
	formModel?: any[] // form表单
	formData?: Record<string, any> // 表单默认值
	rules?: Record<string, any> // 校验规则
}

const props = withDefaults(defineProps<Props>(), {
	allowsize: 100,
	accept: ".xlsx,.xls",
	allExtensions: () => [".xls", ".xlsx"],
	checkUrl: undefined,
	saveApi: undefined,
	fileTxtConf: () => {
		return {
			title: "导入数据",
			fileLable: ""
		}
	},
	formModel: () => {
		return []
	},
	rules: () => {
		return {}
	},
	formData: () => {
		return {}
	},
	downloadApi: () => {},
	saveParams: () => {
		return {}
	}
})

const formModelData = ref<any>({})
const formModelUpload = ref<any[][]>([])

watchEffect(() => {
	if (props.formModel.length === 0) {
		formModelData.value = {
			createdBy_view: userInfo.value.realName as any,
			createdBy: userInfo.value.userName as any,
			// 部门
			sysOrgId: userInfo.value.orgId as any,
			sysOrgId_view: userInfo.value.orgName
		}
		formModelUpload.value = [
			[
				{
					label: "更新人",
					name: "createdBy_view",
					type: "input",
					clear: false,
					disabled: true
				},
				{
					label: "部门",
					name: "sysOrgId_view",
					type: "input",
					clear: false,
					disabled: true
				}
			]
		]
	} else {
		formModelUpload.value = props.formModel
		formModelData.value = props.formData
	}
})
const emits = defineEmits(["onUpdateList", "onClose"])
const ruleFormRef = ref<FormInstance>()
const rules = toRef(props, "rules")
const qualityDrawerLeftTitle = computed(() => {
	return {
		name: [props.fileTxtConf.title],
		icon: ["fas", "square-share-nodes"]
	}
})

const errorInfo = ref({
	totalLines: 0,
	errorLines: 0
})

const formBtnList = computed(() => [
	{ name: "取消", icon: ["fas", "circle-minus"] },
	{
		name: "完成",
		icon: ["fas", "check-circle"],
		disabled: errorInfo.value.errorLines > 0 ? true : false
	}
])
const drawerBtnLoading = ref(false)
const uploadFile = ref<any>()
const uploadData = (data: any) => {
	uploadFile.value = first(data)
	//uploadFile.value = data && data.raw ? data.raw : null
}
const onFormBtnList = (btnName: string | undefined) => {
	if (btnName === "完成") {
		if (!props.saveApi) {
			ElMessage.error(`请配置表单数据接口！`)
			return false
		}
		if (!uploadFile.value) {
			ElMessage.error(`请先上传附件！`)
			return false
		}
		if (ruleFormRef.value) {
			ruleFormRef.value.validate((valid) => {
				if (valid) {
					const formData = new FormData()

					formData.append("file", uploadFile.value, uploadFile.value?.name)
					drawerBtnLoading.value = true
					props
						.saveApi(formData)
						.then((res: any) => {
							if (!res?.errorLines) {
								emits("onUpdateList")
								emits("onClose")
							} else {
								errorInfo.value = {
									totalLines: res.totalLines,
									errorLines: res.errorLines
								}
								ElMessage.warning(res.msg)
							}
						})
						.finally(() => {
							drawerBtnLoading.value = false
						})
				}
			})
		}
	} else if (btnName === "取消") {
		emits("onClose")
	}
}

const uploadUrl = ref<string>("")

watchEffect(() => {
	if (props.checkUrl) {
		uploadUrl.value = import.meta.env.VITE_BASE_API + props.checkUrl
	}
})

const onExcleDown = () => {
	if (props.downloadApi) {
		props.downloadApi()
	}
}

const rightTitle = {
	name: ["校验信息"],
	icon: ["fas", "square-share-nodes"]
}

/**
 * 附件上传成功 回调
 * @param response
 */
const handleSuccess = (response: Record<string, any>) => {
	if (response.code == "200") {
		const res = response.data
		errorInfo.value = {
			totalLines: res?.totalLines,
			errorLines: res?.errorLines
		}
	} else {
		return ElMessage.warning(response.msg || "error")
	}
}
/**
 * 手动删除附件 回调
 * @param uploadFile
 * @param fileList
 */
const handleRemove = (uploadFile: any, fileList: any) => {
	errorInfo.value = {
		totalLines: 0,
		errorLines: 0
	}
}

/**
 * 点击下载
 */
function handleErrorDown() {
	const formData = new FormData()

	formData.append("file", uploadFile.value, uploadFile.value?.name)
	MatApplyApi.getExcelError(formData).then((res: any) => {
		downloadExcel(res, "更新预估采购单价错误数据.xlsx")
	})
}
</script>
<template>
	<div class="drawer-container">
		<div class="drawer-column left">
			<div class="rows">
				<Title :title="qualityDrawerLeftTitle" />
				<el-scrollbar>
					<el-form
						style="padding-bottom: 30px"
						class="content form-base"
						ref="ruleFormRef"
						:model="formModelData"
						:rules="rules"
						label-position="top"
					>
						<el-form-item
							prop="id"
							:label="props.fileTxtConf.fileLable"
							class="position_r"
						>
							<div class="show-down_btn">
								<el-button v-btn link @click="onExcleDown()">
									<font-awesome-icon :icon="['fas', 'cloud-download']" />
									<span class="table-inner-btn">下载导入模版</span>
								</el-button>
							</div>
							<UploadFile
								:btnLoading="drawerBtnLoading"
								:action="uploadUrl"
								:viewDesc="true"
								:accept="props.accept"
								:allExtensions="props.allExtensions"
								:allowsize="props.allowsize"
								listType="text"
								:maxCount="1"
								@update:files="uploadData"
								@onSuccess="handleSuccess"
								@handle-remove="handleRemove"
							/>
						</el-form-item>
						<form-element
							:form-element="formModelUpload"
							:form-data="formModelData"
						/>
					</el-form>
				</el-scrollbar>
			</div>
		</div>

		<div class="drawer-column right">
			<Title :title="rightTitle" />

			<div class="rows">
				<div
					class="tips-wrap"
					v-if="errorInfo.totalLines && errorInfo.errorLines"
				>
					<div class="item">
						<font-awesome-icon
							:icon="['fas', 'exclamation-circle']"
							style="
								color: #f59b22;
								cursor: pointer;
								padding-right: 2px;
								font-size: 14px;
							"
						/>
						文件上传成功，但校验数据有误！
					</div>
					<div class="item" style="padding-left: 18px">
						总上传行数：{{ errorInfo.totalLines }}
					</div>
					<div
						class="item"
						style="
							padding-left: 18px;
							display: flex;
							justify-content: space-between;
						"
					>
						<span>错误行数：{{ errorInfo.errorLines }}</span>
						<span>
							<i
								style="margin-right: 5px; color: #409eff; cursor: pointer"
								@click="handleErrorDown"
								>点击下载</i
							>
							修改后重新上传
						</span>
					</div>
				</div>

				<div
					class="tips-wrap"
					style="
						border: 1px solid #67c23a;
						background-color: rgb(239.8, 248.9, 235.3);
					"
					v-else-if="errorInfo.totalLines && !errorInfo.errorLines"
				>
					<div class="item">
						<font-awesome-icon
							:icon="['fas', 'check-circle']"
							style="color: #67c23a; cursor: pointer; padding-right: 2px"
						/>
						文件上传成功！数据校验成功！
					</div>
					<div class="item" style="padding-left: 18px">
						总上传行数：{{ errorInfo.totalLines }}
					</div>
				</div>
				<div class="empty_content" v-else>
					<div class="empty_table">
						<EmptyData class="empty_img" />
						<p>未查询到相关数据</p>
					</div>
				</div>
			</div>

			<ButtonList
				class="footer"
				:button="formBtnList"
				:loading="drawerBtnLoading"
				@onBtnClick="onFormBtnList"
			/>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

:deep(.el-form-item__error) {
	top: 34px !important;
}
:deep(.el-upload__tip) {
	margin-top: 12px !important;
}
:deep(.position_r) {
	position: relative;
	.show-down_btn {
		position: absolute;
		right: 0;
		top: -32px;
	}
}

.drawer-container {
	.left {
		width: 310px;
	}

	.right {
		width: calc(100% - 310px);
	}
}

.empty_content {
	display: flex;
	justify-content: center;
	align-items: center;
}

.empty_table {
	line-height: 1rem;
	padding-bottom: 40px;
	.empty_img {
		width: 150px;
	}
	p {
		color: #666666;
		font-size: 12px;
		text-align: center;
	}
}

.tips-wrap {
	color: #666;
	font-size: 12px;
	/* height: 50px;
	line-height: 50px; */
	border: 1px solid #f59b22;
	border-radius: 5px;
	margin: 20px 10px 10px;
	background-color: rgb(252.5, 245.7, 235.5);
	padding: 10px 20px;
	.font-bold {
		padding: 0 2px;
	}

	.item {
		line-height: 30px;
	}
}
</style>
