<!-- 更新物资材质 操作页面 -->
<script setup lang="ts">
import { onMounted, ref } from "vue"
import FormElement from "@/app/baseline/views/components/formElement.vue"
import { ElMessage, type FormInstance, type FormRules } from "element-plus"
import { FormElementType } from "../../components/define"

import { MatApplyApi } from "@/app/baseline/api/material/matApply"
import { useDictInit } from "../../components/dictBase"
import { useUserStore } from "@/app/platform/store/modules/user"
import { useMessageBoxInit } from "../../components/messageBox"

export interface Props {
	selMatList?: any[]
}

const props = defineProps<Props>()

const emit = defineEmits<{
	(e: "update"): void
	(e: "close"): void
}>()

const { showWarnConfirm } = useMessageBoxInit()

const { userInfo } = storeToRefs(useUserStore())

const { dictOptions, getDictByCodeList } = useDictInit()

const drawerLoading = ref(false)

/**
 * Title 配置
 */
const titleConf = {
	name: ["更新物资材质"],
	icon: ["fas", "square-share-nodes"]
}

/**
 * 按钮 配置
 */
const formBtnList = ref([
	{ name: "取消", icon: ["fas", "circle-minus"] },
	{ name: "保存", icon: ["fas", "floppy-disk"] }
])

const ruleFormRef = ref<FormInstance>()

const formModelData = ref<Record<string, any>>({})

/**
 * 表单 配置
 */
const formEl = computed<FormElementType[][]>(() => [
	[
		{
			label: "主要材质",
			name: "quality",
			type: "select",
			data: dictOptions.value["MAIN_MATERIALS"],
			placeholder: "请选择主要材质"
		},
		{
			label: "辅助材质",
			name: "auxiliaryQuality",
			type: "select",
			data: dictOptions.value["MAIN_MATERIALS"],
			placeholder: "请选择辅助材质"
		},
		{
			label: "更新人",
			name: "createdBy_view",
			type: "input",
			clear: false,
			disabled: true
		},
		{
			label: "更新部门",
			name: "sysOrgId_view",
			type: "input",
			clear: false,
			disabled: true
		}
	]
])

/**
 * 表单校验 配置
 */
const rules = computed<FormRules<typeof formModelData.value>>(() => ({
	quality: {
		required: true,
		message: "主要材质不能为空",
		trigger: "change"
	}
}))

/**
 * 初始化更新人信息
 */
function initUpdateUserInfo() {
	// 申请人
	formModelData.value.createdBy_view = userInfo.value.realName as any
	formModelData.value.createdBy = userInfo.value.userName as any
	// 申请部门
	formModelData.value.sysOrgId = userInfo.value.orgId as any
	formModelData.value.sysOrgId_view = userInfo.value.orgName
}

/**
 * 表单 操作
 */
const formBtnLoading = ref(false)
const formBtnAction = (btnName: string | undefined) => {
	if (btnName === "保存") {
		if (!ruleFormRef.value) {
			return
		}
		ruleFormRef.value?.validate(async (valid: any) => {
			if (!valid) {
				return
			}
			await showWarnConfirm("请确认是否保存本次数据？")

			formBtnLoading.value = true
			try {
				const materialIdList = props.selMatList?.map((item: any) => item.id)
				await MatApplyApi.updateQuality({
					materialIdList,
					qualityAfter: formModelData.value.quality,
					auxiliaryQualityAfter: formModelData.value.auxiliaryQuality
				})

				ElMessage.success("操作成功")
				emit("close")
				emit("update")
			} finally {
				formBtnLoading.value = false
			}
		})
	} else if (btnName === "取消") {
		emit("close")
	}
}

onMounted(() => {
	initUpdateUserInfo()
	getDictByCodeList(["MAIN_MATERIALS"])
})
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<div class="drawer-column" style="width: 100%">
			<Title :title="titleConf" />
			<el-form
				style="padding-bottom: 30px"
				class="content form-base"
				ref="ruleFormRef"
				:model="formModelData"
				:rules="rules"
				label-position="top"
			>
				<FormElement :form-element="formEl" :form-data="formModelData" />
			</el-form>
			<ButtonList
				class="footer"
				:button="formBtnList"
				:loading="formBtnLoading"
				@onBtnClick="formBtnAction"
			/>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.drawer-container {
	.left {
		width: 310px;
	}

	.right {
		width: calc(100% - 310px);
	}
}
</style>
