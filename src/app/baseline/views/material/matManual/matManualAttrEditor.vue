<!-- 更新物资性质 V2.0 重构 -->
<script lang="ts" setup>
import { ref } from "vue"
import { ElMessage } from "element-plus"
import { MatApplyApi } from "@/app/baseline/api/material/matApply"
import TreeMatAttrData from "@/app/baseline/views/components/treeMatAttrData.vue"
import { DictApi } from "@/app/baseline/api/dict"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import { useDictInit } from "../../components/dictBase"
import { IMaterialBusinessType } from "@/app/baseline/utils/types/material"
import { useMessageBoxInit } from "../../components/messageBox"
import { getIdempotentToken } from "@/app/baseline/utils/validate"
import {
	IIdempotentTokenType,
	IIdempotentTokenTypeAction,
	IIdempotentTokenTypePre
} from "@/app/baseline/utils/types/common"

const props = defineProps<{
	id?: any // id
	selMatList?: any[] // create || edit || create
}>()

const emit = defineEmits<{
	(e: "update"): void
	(e: "close"): void
}>()

const { showWarnConfirm } = useMessageBoxInit()

const tableData = ref<any[]>(props.selMatList || [])
const tableRef = ref()
const selectedTableList = ref<{ [propName: string]: any }[]>([]) //表格选中行

const drawerLoading = ref(false)
const btnLoading = ref(false)

const formData = ref<any>({})
const treeList = ref() //保存用户当前点击树信息

const { dictFilter, getDictByCodeList } = useDictInit()

const drawerLeftTitle = {
	name: ["物资编码信息"],
	icon: ["fas", "square-share-nodes"]
}
const drawerRightTitle = {
	name: ["物资性质"],
	icon: ["fas", "square-share-nodes"]
}
const formBtnList = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "提交审核",
		icon: ["fas", "circle-check"]
	}
]

const tableEndBtn = computed(() => [
	{
		name: "移除",
		icon: ["fas", "trash-alt"],
		disabled: selectedTableList.value.length > 0 ? false : true
	}
])

const treeClick = (selected: any) => {
	treeList.value = selected
}
async function onFormBtnList(btnName: string | undefined) {
	if (btnName === "提交审核") {
		const arrId = tableData.value?.map((item: any) => item.id)
		if (arrId.length <= 0) {
			return ElMessage.warning("请选择要变更的物资!")
		}

		if (treeList.value) {
			formData.value.attribute = treeList.value.value
		} else {
			return ElMessage.warning("请选择物资性质!")
		}

		formData.value.ids = arrId.join(",")

		await showWarnConfirm("请确认是否提交本次数据？")

		btnLoading.value = true
		drawerLoading.value = true
		try {
			const idempotentToken = getIdempotentToken(
				IIdempotentTokenTypePre.batch,
				IIdempotentTokenType.materialCode,
				"",
				IIdempotentTokenTypeAction.update
			)

			await MatApplyApi.matManualPublishOperate(
				{
					materialIdList: arrId,
					type: IMaterialBusinessType.materialType,
					attributeAfter: formData.value.attribute
				},
				idempotentToken
			)
			emit("update")
			emit("close")
		} finally {
			btnLoading.value = false
			drawerLoading.value = false
		}
	} else {
		emit("close")
	}
}

const onDataSelected = () => {
	selectedTableList.value = tableRef.value.pitayaTableRef!.getSelectionRows()
}

const onTableEndBtn = (btnName: any) => {
	switch (btnName) {
		case "移除":
			handleRemove()
			break
	}
}
const handleRemove = () => {
	const ids = selectedTableList.value.map((item) => item.id)
	const filteredArray = tableData.value.filter((obj) => !ids.includes(obj.id))
	tableData.value = filteredArray
}

const tableProp: TableColumnType[] = [
	{ label: "物资编码", prop: "code", width: 150 },
	{ label: "物资名称", prop: "label", minWidth: 200 },
	{ label: "物资分类编码", prop: "materialTypeCode", width: 100 },
	{
		label: "物资分类名称",
		prop: "materialTypeLabel",
		minWidth: 200
	},
	{
		label: "规格型号",
		prop: "version",
		needSlot: false,
		width: 100
	},
	{
		label: "技术参数",
		prop: "technicalParameter",
		needSlot: false,
		minWidth: 100
	},
	{ label: "采购单位", prop: "buyUnit", needSlot: true, width: 85 },
	{
		label: "物资性质",
		prop: "attribute",
		needSlot: true,
		width: 130
	}
]

onMounted(async () => {
	getDictByCodeList(["INVENTORY_UNIT"])
})
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<!-- 左侧表单区域 -->
		<div class="drawer-column left">
			<Title :title="drawerLeftTitle" />
			<PitayaTable
				ref="tableRef"
				:columns="tableProp"
				:table-data="tableData"
				:need-index="false"
				:need-selection="true"
				:need-pagination="false"
				@onSelectionChange="onDataSelected"
			>
				<template #attribute="{ rowData }">
					<dict-tag
						:options="DictApi.getMatAttr()"
						:value="rowData.attribute"
					/>
				</template>
				<template #buyUnit="{ rowData }">
					{{
						dictFilter("INVENTORY_UNIT", rowData.buyUnit)?.subitemName || "---"
					}}
				</template>
				<template #footerOperateLeft>
					<ButtonList
						:is-not-radius="true"
						:button="tableEndBtn"
						@on-btn-click="onTableEndBtn"
					/>
				</template>
			</PitayaTable>
		</div>
		<!-- 右侧table区域 -->
		<div class="drawer-column right">
			<div class="rows">
				<Title :title="drawerRightTitle" />
				<TreeMatAttrData @on-tree-click="treeClick" />
			</div>
			<ButtonList
				class="footer"
				:button="formBtnList"
				:loading="btnLoading"
				@on-btn-click="onFormBtnList"
			/>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index";

.drawer-container {
	.left {
		width: calc(100% - 310px);
	}

	.right {
		width: 310px;
	}
}
</style>
