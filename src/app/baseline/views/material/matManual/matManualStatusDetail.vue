<!-- 物资状态 审批 -->
<script lang="ts" setup>
import { ref } from "vue"
import { MatApplyApi } from "@/app/baseline/api/material/matApply"

import FormApply from "../components/apply/formApply.vue"
import { fileBusinessType } from "@/app/baseline/api/dict"
import XEUtils from "xe-utils"
import fileDrawer from "../../components/fileDrawer.vue"
import { FileApi } from "@/app/baseline/api/file"
import { convertBytesToMegabytes } from "@/app/baseline/utils"
import { useMessageBoxInit } from "@/app/baseline/views/components/messageBox"

import { IModalType } from "@/app/baseline/utils/types/common"
import { first } from "lodash-es"
import { useTbInit } from "@/app/baseline/views/components/tableBase"
import { IMaterialBusinessType } from "@/app/baseline/utils/types/material"

const { showDelConfirm } = useMessageBoxInit()

const props = withDefaults(
	defineProps<{
		businessId?: any
		mode: IModalType // create || edit || create
		footerBtnVisible?: boolean
		operateType?: IMaterialBusinessType
	}>(),
	{ footerBtnVisible: true }
)

const materialId = ref("")

const businessType = computed(() => {
	switch (props.operateType) {
		case IMaterialBusinessType.materialFreeze:
			return fileBusinessType.matMaterialFreeze
		case IMaterialBusinessType.materialThawing:
			return fileBusinessType.matMaterialThawing
		default:
			return fileBusinessType.matMaterialCancel
	}
})

const title = computed(() => {
	switch (props.operateType) {
		case IMaterialBusinessType.materialFreeze:
			return "冻结"
		case IMaterialBusinessType.materialThawing:
			return "解冻"
		default:
			return "作废"
	}
})

const drawerLeftTitle = {
	name: ["物资基本信息"],
	icon: ["fas", "square-share-nodes"]
}
const drawerMidTitle = computed(() => [
	{
		name: [`${title.value}信息`],
		icon: ["fas", "square-share-nodes"]
	},
	{
		name: ["相关附件"],
		icon: ["fas", "square-share-nodes"]
	}
])

const tableFileEndBtn = {
	roles: "system:department:btn:add",
	name: "上传附件",
	icon: ["fas", "arrow-up-from-bracket"]
}

const formData = ref<Record<string, any>>({
	conversionUnitText: "1:1"
})
const drawerLoading = ref(false)

/**
 * 获取详情数据
 */
async function getMatApplyInfo() {
	if (!materialId.value) {
		return false
	}

	drawerLoading.value = true
	try {
		const res = await MatApplyApi.getMatApplyById(materialId.value)
		formData.value = { ...res }
		formData.value.conversionUnitText = "1:1"
		const tmpEvaluation = `${XEUtils.commafy(formData.value.evaluation, {
			digits: 5
		})}`
		formData.value["evaluation_view"] = tmpEvaluation
			? `￥${tmpEvaluation}`
			: "---"
		formData.value.useUnit = String(res.useUnit)
		formData.value.buyUnit = String(res.buyUnit)
		formData.value.useAlonePeriod = res.useAlonePeriod
			? String(res.useAlonePeriod)
			: ""
	} finally {
		drawerLoading.value = false
	}
}

/**
 * 相关附件
 */
const fileDrawerVisible = ref(false)
const filteTableLoading = ref(false)
const fileTableProp: TableColumnType[] = [
	{ label: "附件编号", prop: "code", needSlot: true, width: 150 },
	{ label: "附件名称", prop: "customFileName", minWidth: 150 },
	{ label: "附件格式", prop: "fileType", width: 85 },
	{ label: "附件大小", prop: "fileSize", needSlot: true, width: 120 },
	{ label: "上传人", prop: "createdBy_view", width: 100 },
	{ label: "上传时间", prop: "createdDate", width: 160 },
	{
		label: "操作",
		prop: "operations",
		fixed: "right",
		width: props.mode == IModalType.view ? 70 : 130,
		needSlot: true
	}
]
function getCode(row: any) {
	const paddedNum1 = String(row.businessType).padEnd(3, "0")
	const paddedNum2 = String(row.businessId).padEnd(6, "0")
	const paddedNum3 = String(row.id).padStart(7, "0")
	return paddedNum1 + paddedNum2 + paddedNum3
}

const fileTableData = ref([])
const getTableData = () => {
	filteTableLoading.value = true
	const params = {
		businessType: businessType.value,
		businessId: props.businessId //props.id
	}

	FileApi.getAllFileList(params)
		.then((res: any) => {
			fileTableData.value = res
		})
		.finally(() => {
			filteTableLoading.value = false
		})
}
/**
 * 上传附件 handle
 */
function handleUploadFile() {
	fileDrawerVisible.value = true
}

/**
 * 附件下载
 * @param row
 */
function handleRowDownLoad(row: any) {
	FileApi.downloadFile(row.filePath)
}

/**
 * 附件移除
 * @param row
 */
async function handleRowDelete(row: any) {
	await showDelConfirm()
	await FileApi.deleteFile({ id: row.id })
	ElMessage.success("移除成功")
	getTableData()
}

const computedRef = ref()
const tbInit = useTbInit()
const {
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = tbInit

tbInit.tableProp = computed(() => {
	const defColumn: TableColumnType[] = [
		{ label: `${title.value}原因`, prop: "reason" },
		{ label: "替换物资编码", prop: "replaceMaterialCode", width: 150 }
	]

	if (props.operateType === IMaterialBusinessType.materialThawing) {
		return defColumn.filter((i) => i.label != "替换物资编码")
	} else {
		return defColumn
	}
})

watch([tableData], async () => {
	if (tableData.value.length > 0) {
		materialId.value = (first(tableData.value) as any).materialId
		await getMatApplyInfo()

		getTableData()
	}
})
onMounted(async () => {
	fetchParam.value.operateId = props.businessId

	fetchFunc.value = MatApplyApi.getMatOperateItemList
	fetchTableData()
})
</script>
<template>
	<div
		class="drawer-container mat-apply-view-drawer"
		v-loading="drawerLoading"
		style="padding-right: 0 !important"
	>
		<!-- 左侧表单区域 -->
		<div class="drawer-column left" style="align-items: stretch">
			<div class="rows">
				<Title :title="drawerLeftTitle" />
				<el-scrollbar>
					<el-form
						style="padding-bottom: 30px"
						class="content form-base"
						:model="formData"
						label-position="top"
						label-width="100px"
						disabled
					>
						<FormApply
							:form-data="formData"
							:readonly="true"
							:need-eval="true"
						/>
					</el-form>
				</el-scrollbar>
			</div>
		</div>
		<!-- 中间table区域 -->
		<div class="drawer-column right">
			<div class="rows-auto pb0">
				<Title :title="drawerMidTitle[0]" />
				<div>
					<PitayaTable
						ref="tableRef"
						:columns="(tbInit.tableProp as any)"
						:table-data="tableData"
						:need-index="true"
						:need-pagination="false"
						:single-select="false"
						:need-selection="false"
						:total="pageTotal"
						@onSelectionChange="selectedTableList = $event"
						@on-current-page-change="onCurrentPageChange"
						:table-loading="tableLoading"
					/>
				</div>
			</div>
			<div
				class="rows-auto pb0"
				ref="computedRef"
				style="
					height: 100%;
					display: flex;
					flex-direction: column;
					overflow: auto;
					flex: 1;
					margin-top: 10px;
				"
			>
				<Title
					:title="drawerMidTitle[1]"
					style="justify-content: space-between"
				>
					<div
						class="upload-container"
						v-if="props.mode !== IModalType.view"
						@click="handleUploadFile"
					>
						<div :class="tableFileEndBtn.roles">
							<font-awesome-icon
								v-if="tableFileEndBtn.icon"
								class="place-icon"
								:icon="tableFileEndBtn.icon"
							/>
							<span class="btn-text">{{ tableFileEndBtn.name }}</span>
						</div>
					</div>
				</Title>
				<div>
					<PitayaTable
						ref="fileTableRef"
						:columns="fileTableProp"
						:table-data="fileTableData"
						:needSelection="false"
						:single-select="false"
						:need-index="true"
						:need-pagination="false"
						:table-loading="filteTableLoading"
						:max-height="computedRef?.clientHeight - 60"
					>
						<template #code="{ rowData }">
							{{ getCode(rowData) }}
						</template>
						<template #fileSize="{ rowData }">
							{{ convertBytesToMegabytes(rowData.fileSize) }}
						</template>
						<template #operations="{ rowData }">
							<div v-if="rowData.id !== -1">
								<el-button v-btn link @click="handleRowDownLoad(rowData)">
									<font-awesome-icon :icon="['fas', 'file-arrow-down']" />
									<span class="table-inner-btn">下载</span>
								</el-button>
								<template v-if="props.mode == IModalType.edit">
									<el-button v-btn link @click="handleRowDelete(rowData)">
										<font-awesome-icon :icon="['fas', 'trash-can']" />
										<span class="table-inner-btn">移除</span>
									</el-button>
								</template>
							</div>
						</template>
					</PitayaTable>
				</div>
			</div>
		</div>

		<Drawer
			:size="310"
			v-model:drawer="fileDrawerVisible"
			:destroyOnClose="true"
		>
			<file-drawer
				:businessType="businessType"
				:businessId="props.businessId"
				:fileTxtConf="{
					btnName: '添加附件',
					fileName: '附件名称',
					fileLabel: '上传附件',
					title: '上传附件'
				}"
				@onUpdateList="getTableData"
				@onSaveOrClose="fileDrawerVisible = false"
			/>
		</Drawer>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

:deep() {
	input::-webkit-outer-spin-button,
	input::-webkit-inner-spin-button {
		-webkit-appearance: none !important;
	}

	input[type="number"] {
		-moz-appearance: textfield;
	}
}

.drawer-container {
	.left {
		width: 600px;
	}

	.right {
		width: calc(100% - 600px);
		.row {
			flex: 0 !important;
		}

		.pb0 {
			padding-bottom: 0;
		}

		.upload-container {
			display: flex;
			align-items: center; /* 垂直居中子元素 */
			justify-content: center;
			font-size: 12px;
			cursor: pointer;

			.btn-text {
				margin-left: 5px;
				padding-right: 10px;
			}
		}
	}
}
</style>
