<!-- 物资选择器 -->
<script lang="ts" setup>
import { MatApplyApi } from "@/app/baseline/api/material/matApply"
import { appStatus, DictApi } from "@/app/baseline/api/dict"

import { useTbInit } from "@/app/baseline/views/components/tableBase"
import { matStatus } from "@/app/baseline/api/dict"

import TreeMatType from "@/app/baseline/views/components/v2/treeMatType.vue"
import costTag from "@/app/baseline/views/components/costTag.vue"
import { defineProps, ref, withDefaults, onMounted } from "vue"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import { useTableSelectorUtils } from "../../store/hooks/table-selector-utils"
import { useDictInit } from "../../components/dictBase"
import { IMateOperateStatus } from "@/app/baseline/utils/types/material"

interface Props {
	planId?: string | number
	businessId?: string | number // 业务Id  如：交旧申请主表Id

	/**
	 * table 的列配置
	 */
	columns?: TableColumnType[]

	defaultExpandedKeys?: any
	ifClick?: boolean
	/**
	 * table 数据源
	 */
	tableApi?: (params: any) => Promise<any>

	/**
	 * table 请求参数
	 */
	tableReqParams?: any

	queryArrList?: any
	multiple?: boolean

	selectedIds?: any[]
}
const props = withDefaults(defineProps<Props>(), {
	planId: undefined,
	businessId: undefined,
	multiple: true,
	columns: () => [],
	defaultExpandedKeys: () => [0]
})

const emit = defineEmits<{
	(e: "close"): void
	(e: "save", v?: any[]): void
}>()

const { dictFilter, getDictByCodeList } = useDictInit()

const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	currentPage,
	pageTotal,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = useTbInit()
tableProp.value = props.columns?.length
	? props.columns
	: [
			{ label: "物资编码", prop: "code", width: 150 },
			{ label: "物资名称", prop: "label", width: 150 },
			{
				label: "物资分类编码",
				prop: "materialTypeCode",
				width: 100
			},
			{
				label: "物资分类名称",
				prop: "materialTypeLabel",
				width: 200
			},
			{
				label: "规格型号",
				prop: "version",
				needSlot: false,
				width: 150
			},
			{
				label: "技术参数",
				prop: "technicalParameter",
				needSlot: false,
				minWidth: 100,
				align: "left"
			},
			{ label: "采购单位", prop: "buyUnit", needSlot: true, width: 90 },
			{
				label: "预估采购单价",
				prop: "evaluation",
				needSlot: true,
				width: 150,
				align: "right"
			}
	  ]

const { fetchTableDataWithSetRowsCheck, setTableSelect } =
	useTableSelectorUtils({
		tableData,
		tableRef,
		selectedIds: props.selectedIds,
		fetchTableData
	})

fetchFunc.value = MatApplyApi.getMatApplyList

const leftTitle = {
	name: ["物资编码分类"],
	icon: ["fas", "square-share-nodes"]
}
const rightTitle = ref<any>({
	name: ["选择物资"],
	icon: ["fas", "square-share-nodes"]
})

/**
 * 查询 配置
 */
const queryConf = computed(() => {
	const ls = [
		{
			name: "物资编码",
			key: "code",
			placeholder: "请输入物资编码",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "物资名称",
			key: "label",
			placeholder: "请输入物资名称",
			enableFuzzy: true,
			type: "input"
		}
	]

	return props.queryArrList || ls
})

const formBtnList = ref([
	{ name: "取消", icon: ["fas", "circle-minus"] },
	{ name: "保存", icon: ["fas", "floppy-disk"] }
])

const treeCheck = (selectedId: any, status: string) => {
	getQueryData({
		materialTypeIds: selectedId.join(","),
		materialTypeStatus: status
	})
}
const getQueryData = (data: { [propName: string]: any }) => {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		status: matStatus.normal,
		operateStatus: `${IMateOperateStatus.normal},${IMateOperateStatus.update}`,
		...{ bpmStatus: appStatus.approved },
		...data,
		...props.tableReqParams
	}

	fetchTableDataWithSetRowsCheck()
}

function handleCurrentPageChange(data: any) {
	onCurrentPageChange(data).then(setTableSelect)
}

function onFormBtnList(btnName?: string) {
	const selectRows = tableRef.value?.pitayaTableRef?.getSelectionRows()
	if (btnName == "保存") {
		emit("save", selectRows)
	} else {
		emit("close")
	}
}

const treeLoading = ref(false)
const defaultStatus = matStatus.freeze + "," + matStatus.normal
onMounted(async () => {
	treeLoading.value = true
	try {
		await getDictByCodeList(["INVENTORY_UNIT"])

		treeCheck([], defaultStatus)
	} finally {
		treeLoading.value = false
	}
})
</script>
<template>
	<div class="app-container">
		<ModelFrame class="left-frame">
			<TreeMatType
				v-loading="treeLoading"
				:title="leftTitle"
				:status="defaultStatus"
				:needSwitch="false"
				:defaultExpandedKeys="props.defaultExpandedKeys"
				@onTreeCheck="treeCheck"
			/>
		</ModelFrame>
		<div class="right-frame">
			<Title :title="rightTitle" />
			<ModelFrame class="whole-frame">
				<Query :queryArrList="queryConf" @getQueryData="getQueryData" />
				<PitayaTable
					ref="tableRef"
					:columns="tableProp"
					:table-data="tableData"
					:total="pageTotal"
					@onSelectionChange="selectedTableList = $event"
					@on-current-page-change="handleCurrentPageChange"
					:table-loading="tableLoading"
					:need-index="false"
					:need-pagination="true"
					:single-select="props.multiple ? false : true"
					:need-selection="true"
				>
					<template #buyUnit="{ rowData }">
						{{
							dictFilter("INVENTORY_UNIT", rowData.buyUnit)?.subitemName ||
							"---"
						}}
					</template>

					<!-- 物资性质 -->
					<template #attribute="{ rowData }">
						<dict-tag
							:options="DictApi.getMatAttr()"
							:value="rowData.attribute"
						/>
					</template>
					<template #evaluation="{ rowData }">
						<cost-tag :value="rowData.evaluation" />
					</template>
				</PitayaTable>
			</ModelFrame>

			<ButtonList
				style="padding-right: 10px"
				class="footer end"
				:button="formBtnList"
				@on-btn-click="onFormBtnList"
			/>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.app-container {
	padding: 0px $---spacing-m;
}
.left-frame,
.whole-frame {
	padding: 0px;
	border: none;
}
.left-frame {
	padding-right: $---spacing-m;
}
.right-frame {
	padding-left: $---spacing-m;
	border-left: solid 1px $---border-color;
}

:deep(.common-query-wrapper) {
	padding-top: 10px;
	padding-left: 10px;
}
:deep(.pitaya-table) {
	padding-top: 0px;
	margin-top: 0px;
}
.common-btn-list-wrapper {
	border-top: solid 1px $---border-color;
	padding-top: $---spacing-m;
}
</style>
