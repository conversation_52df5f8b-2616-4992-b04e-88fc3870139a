<script lang="ts" setup>
import { ref, onMounted } from "vue"
import { type FormInstance } from "element-plus"
import { FormElementType } from "@/app/baseline/views/components/define"
import FormElement from "@/app/baseline/views/components/formElement.vue"
import {
	IMaterialBusinessType,
	IMatUpdateType
} from "@/app/baseline/utils/types/material"
import { filter } from "lodash-es"
import { useDictInit } from "../../components/dictBase"

const props = defineProps<{
	title?: any
	matBusinessType: IMaterialBusinessType
	detailInfo: Record<string, any>
}>()

const { dictFilter, getDictByCodeList } = useDictInit()

const emit = defineEmits<{
	(e: "close"): void
}>()

const leftTitle = computed(() => ({
	name: [props.title],
	icon: ["fas", "square-share-nodes"]
}))

const formBtnList = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	}
]

const drawerLoading = ref(false)
const refForm = ref<FormInstance>()

const formModelData = ref<Record<string, any>>({
	...props.detailInfo
})

/**
 * 初始化数据
 * 物资性质
 */
formModelData.value.attributeData = formModelData.value.operateItemVoList.find(
	(v: Record<string, any>) => v.updateType === IMatUpdateType.materialType
)

const formatAttributeData = computed(() => {
	const attributeData = formModelData.value.attributeData
	if (attributeData) {
		return {
			attributeBefore_view:
				dictFilter("MATERIAL_NATURE", attributeData.beforeValue)?.subitemName ||
				"---",
			attributeAfter_view:
				dictFilter("MATERIAL_NATURE", attributeData.afterValue)?.subitemName ||
				"---"
		}
	}
	return {}
})

/**
 * 初始化数据
 * 物资材质
 */
formModelData.value.qualityData = formModelData.value.operateItemVoList.find(
	(v: Record<string, any>) => v.updateType === IMatUpdateType.materialQuality
)

const formatQualityData = computed(() => {
	const qualityData = formModelData.value.qualityData
	if (qualityData) {
		return {
			qualityBefore_view:
				dictFilter("MAIN_MATERIALS", qualityData.beforeValue)?.subitemName ||
				"---",
			qualityAfter_view:
				dictFilter("MAIN_MATERIALS", qualityData.afterValue)?.subitemName ||
				"---"
		}
	}
	return {}
})

/**
 * 初始化数据
 * 辅助材质
 */
formModelData.value.auxiliaryQualityData =
	formModelData.value.operateItemVoList.find(
		(v: Record<string, any>) => v.updateType === IMatUpdateType.auxiliaryQuality
	)

const formatAuxiliaryQualityData = computed(() => {
	const auxiliaryQualityData = formModelData.value.auxiliaryQualityData
	if (auxiliaryQualityData) {
		return {
			auxiliaryQualityBefore_view:
				dictFilter("MAIN_MATERIALS", auxiliaryQualityData.beforeValue)
					?.subitemName || "---",
			auxiliaryQualityAfter_view:
				dictFilter("MAIN_MATERIALS", auxiliaryQualityData.afterValue)
					?.subitemName || "---"
		}
	}
	return {}
})
/**
 * 初始化数据
 * 安全库存
 */
formModelData.value.safeStockData = formModelData.value.operateItemVoList.find(
	(v: Record<string, any>) => v.updateType === IMatUpdateType.materialSafeStock
)

const formatSafeStockData = computed(() => {
	const safeStockData = formModelData.value.safeStockData
	if (safeStockData) {
		return {
			safeStockBefore: safeStockData.beforeValue,
			safeStockAfter: safeStockData.afterValue,
			content_unit_view:
				dictFilter("INVENTORY_UNIT", safeStockData.content?.useUnit)
					?.subitemName || "---"
		}
	}
	return {}
})

/**
 * 初始化数据
 * 更新前预估采购单价
 */

formModelData.value.materialEvaluationData =
	formModelData.value.operateItemVoList.find(
		(v: Record<string, any>) =>
			v.updateType === IMatUpdateType.materialEvaluation
	)

const formatEvaluationData = computed(() => {
	const materialEvaluationData = formModelData.value.materialEvaluationData
	if (materialEvaluationData) {
		return {
			evaluationBefore: materialEvaluationData.beforeValue,
			evaluationAfter: materialEvaluationData.afterValue
		}
	}
	return {}
})

const formatFormData = computed(() => {
	return {
		...props.detailInfo,
		...formatQualityData.value,
		...formatEvaluationData.value,
		...formatAuxiliaryQualityData.value,
		...formatSafeStockData.value,
		...formatAttributeData.value
	}
})

const formEl = computed<FormElementType[][]>(() => {
	const ls = [
		{
			label: "更新前安全库存",
			name: "safeStockBefore",
			disabled: true,
			type: "number",
			append: `${formatFormData.value.content_unit_view}`
		},
		{
			label: "更新后安全库存",
			name: "safeStockAfter",
			disabled: true,
			type: "number",
			append: `${formatFormData.value.content_unit_view}`
		},
		{
			label: "更新前预估采购单价",
			name: "evaluationBefore",
			disabled: true,
			type: "number",
			append: "元"
		},
		{
			label: "更新后预估采购单价",
			name: "evaluationAfter",
			disabled: true,
			type: "number",
			append: "元"
		},
		{
			label: "更新前主要材质",
			name: "qualityBefore_view",
			disabled: true
		},
		{
			label: "更新后主要材质",
			name: "qualityAfter_view",
			disabled: true
		},
		{
			label: "更新前辅助材质",
			name: "auxiliaryQualityBefore_view",
			disabled: true
		},
		{
			label: "更新后辅助材质",
			name: "auxiliaryQualityAfter_view",
			disabled: true
		},
		{
			label: "冻结原因",
			name: "reason",
			disabled: true
		},
		{
			label: "作废原因",
			name: "reason",
			disabled: true
		},
		{
			label: "解冻原因",
			name: "reason",
			disabled: true
		},
		{
			label: "替换物资编码",
			name: "replaceMaterialCode",
			disabled: true
		},
		{
			label: "更新前物资性质",
			name: "attributeBefore_view",
			disabled: true
		},
		{
			label: "更新后物资性质",
			name: "attributeAfter_view",
			disabled: true
		}
	]

	switch (props.matBusinessType) {
		case IMaterialBusinessType.materialSafeStock:
			return [
				filter(ls, (v) =>
					["更新前安全库存", "更新后安全库存"].includes(v.label)
				)
			]
		case IMaterialBusinessType.materialEvaluation:
			return [
				filter(ls, (v) =>
					["更新前预估采购单价", "更新后预估采购单价"].includes(v.label)
				)
			]
		case IMaterialBusinessType.materialQuality:
			return [
				filter(ls, (v) =>
					[
						"更新前主要材质",
						"更新后主要材质",
						"更新前辅助材质",
						"更新后辅助材质"
					].includes(v.label)
				)
			]
		case IMaterialBusinessType.materialFreeze:
			return [filter(ls, (v) => ["冻结原因", "替换物资编码"].includes(v.label))]
		case IMaterialBusinessType.materialCancel:
			return [filter(ls, (v) => ["作废原因", "替换物资编码"].includes(v.label))]
		case IMaterialBusinessType.materialThawing:
			return [filter(ls, (v) => ["解冻原因"].includes(v.label))]
		case IMaterialBusinessType.materialType:
			return [
				filter(ls, (v) =>
					["更新前物资性质", "更新后物资性质"].includes(v.label)
				)
			]

		default:
			break
	}
	return [ls]
})

onMounted(async () => {
	getDictByCodeList(["INVENTORY_UNIT", "MAIN_MATERIALS", "MATERIAL_NATURE"])
})
defineOptions({
	name: "matTypeDrawer"
})
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<div class="drawer-column">
			<div class="rows">
				<Title :title="leftTitle" />
				<el-scrollbar>
					<el-form
						style="padding-bottom: 30px"
						class="content form-base"
						ref="refForm"
						:model="formModelData"
						label-position="top"
						:disabled="true"
					>
						<FormElement :form-element="formEl" :form-data="formatFormData" />
					</el-form>
				</el-scrollbar>
			</div>
			<ButtonList
				class="footer"
				:button="formBtnList"
				@onBtnClick="emit('close')"
			/>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.drawer-column {
	width: 100%;
}
</style>
