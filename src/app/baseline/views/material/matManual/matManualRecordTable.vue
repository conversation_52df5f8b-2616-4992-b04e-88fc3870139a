<!-- 编码手册 - 操作记录 -->
<template>
	<Query
		ref="queryRef"
		:query-arr-list="(queryConf as any)"
		style="margin: 10px 10px -10px"
		@get-query-data="getQueryData"
	/>
	<el-scrollbar>
		<pitaya-table
			ref="tableRef"
			:columns="tableProp"
			:table-data="tableData"
			:need-selection="false"
			:single-select="false"
			:need-index="true"
			:need-pagination="true"
			:total="pageTotal"
			:table-loading="tableLoading"
			@on-selection-change="selectedTableList = $event"
			@on-current-page-change="onCurrentPageChange"
		>
			<!-- 业务名称 -->
			<template #type="{ rowData }">
				{{ typeConf[rowData.type as IMaterialBusinessType]?.label }}
			</template>
			<template #operations="{ rowData }">
				<el-button v-btn link @click="handleRowView(rowData)">
					<font-awesome-icon :icon="['fas', 'eye']" />
					<span class="table-inner-btn">查看</span>
				</el-button>
			</template>
		</pitaya-table>
	</el-scrollbar>

	<Drawer
		:size="modalSize.sm"
		v-model:drawer="detailVisible"
		:destroyOnClose="true"
	>
		<matManualRecordDetail
			:title="typeConf[editRecord.type as IMaterialBusinessType].title"
			:mat-business-type="editRecord.type"
			:detail-info="editRecord"
			@close="detailVisible = false"
		/>
	</Drawer>
</template>

<script setup lang="ts">
import { useTbInit } from "../../components/tableBase"
import { MatApplyApi } from "@/app/baseline/api/material/matApply"
import { modalSize } from "@/app/baseline/utils/layout-config"
import { IMaterialBusinessType } from "@/app/baseline/utils/types/material"
import matManualRecordDetail from "./matManualRecordDetail.vue"

import { appStatus } from "@/app/baseline/api/dict"

const props = withDefaults(
	defineProps<{
		materialId?: any // id
	}>(),
	{}
)

const queryRef = ref()

const typeConf = ref({
	[IMaterialBusinessType.materialSafeStock]: {
		label: "安全库存",
		title: "安全库存更新"
	},
	[IMaterialBusinessType.materialEvaluation]: {
		label: "预估采购单价",
		title: "预估采购单价更新"
	},
	[IMaterialBusinessType.materialQuality]: {
		label: "物资材质",
		title: "物资材质更新"
	},
	[IMaterialBusinessType.materialType]: {
		label: "物资性质",
		title: "物资性质更新"
	},
	[IMaterialBusinessType.materialFreeze]: {
		label: "物资冻结",
		title: "物资冻结更新"
	},
	[IMaterialBusinessType.materialCancel]: {
		label: "物资作废",
		title: "物资作废更新"
	},
	[IMaterialBusinessType.materialThawing]: {
		label: "物资解冻",
		title: "物资解冻更新"
	}
})
const queryConf = computed(() => {
	const ls = [
		{
			name: "操作类型",
			key: "type",
			placeholder: "请选择操作类型",
			type: "select",
			children: [
				{ label: "安全库存", value: IMaterialBusinessType.materialSafeStock },
				{
					label: "预估采购单价",
					value: IMaterialBusinessType.materialEvaluation
				},
				{ label: "物资材质", value: IMaterialBusinessType.materialQuality },
				{ label: "物资性质", value: IMaterialBusinessType.materialType },
				{ label: "物资冻结", value: IMaterialBusinessType.materialFreeze },
				{ label: "物资作废", value: IMaterialBusinessType.materialCancel },
				{ label: "物资解冻", value: IMaterialBusinessType.materialThawing }
			]
		},
		{
			name: "操作人",
			key: "createdName",
			type: "input",
			placeholder: "请输入操作人"
		},
		{
			name: "操作日期",
			key: "startAndEndTime",
			type: "startAndEndTime"
		}
	]

	return ls
})

const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	selectedTableList,
	fetchParam,
	currentPage,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = useTbInit()

fetchFunc.value = MatApplyApi.getMatOperateItemPage //listSysUserPaged

tableProp.value = [
	{ prop: "type", label: "操作类型", needSlot: true },
	{ prop: "createdBy_view", label: "操作人" },
	{ prop: "sysCommunityId_view", label: "所属公司" },
	{ prop: "lastModifiedDate", label: "操作日期" },
	{
		label: "操作",
		width: 100,
		prop: "operations",
		fixed: "right",
		needSlot: true
	}
]

onMounted(() => {
	fetchParam.value.materialId = props.materialId
	getQueryData({})
})

function getQueryData(data?: any) {
	const { startAndEndTime } = data

	data.lastModifiedDate_start = startAndEndTime && startAndEndTime[0]
	data.lastModifiedDate_end = startAndEndTime && startAndEndTime[1]
	delete data.startAndEndTime

	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		...data,
		sidx: "lastModifiedDate",
		sord: "desc",
		bpmStatus: appStatus.approved
	}

	fetchTableData()
}

const detailVisible = ref(false)

const editRecord = ref()
function handleRowView(e?: any) {
	detailVisible.value = true

	editRecord.value = { ...e }
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.mat-selector-table {
	&:deep(.pitaya-table) {
		.error {
			.column-inner-item,
			.cell,
			.el-input__inner {
				color: red;
			}
		}
	}
}
</style>
