<!-- 冻结 作废 解冻 操作页面 -->
<script setup lang="ts">
import { onMounted, ref } from "vue"
import FormElement from "@/app/baseline/views/components/formElement.vue"
import { ElMessage, type FormInstance, type FormRules } from "element-plus"
import { FormElementType } from "../../components/define"

import { fileBusinessType } from "@/app/baseline/api/dict"
import { MatApplyApi } from "@/app/baseline/api/material/matApply"
import { IMaterialBusinessType } from "@/app/baseline/utils/types/material"
import matManualSelector from "./matManualSelector.vue"
import { modalSize } from "@/app/baseline/utils/layout-config"
import { first } from "lodash-es"
import TableFile from "../../components/tableFile.vue"
import {
	IIdempotentTokenType,
	IIdempotentTokenTypePre,
	IModalType
} from "@/app/baseline/utils/types/common"
import { useMessageBoxInit } from "../../components/messageBox"
import { getIdempotentToken } from "@/app/baseline/utils/validate"

export interface Props {
	matId: any
	title: string
	matCode?: string

	/* 操作类型 */
	operateType?: IMaterialBusinessType
}

const props = defineProps<Props>()

const emit = defineEmits<{
	(e: "update"): void
	(e: "close"): void
}>()

const { showWarnConfirm } = useMessageBoxInit()

const drawerLoading = ref(false)
const canEditExtra = computed(() => Boolean(formModelData.value?.id))

const matSelectorVisible = ref(false)
const businessType = computed(() => {
	switch (props.operateType) {
		case IMaterialBusinessType.materialFreeze:
			return fileBusinessType.matMaterialFreeze
		case IMaterialBusinessType.materialThawing:
			return fileBusinessType.matMaterialThawing
		default:
			return fileBusinessType.matMaterialCancel
	}
})

/**
 * Title 配置
 */
const titleConf = computed(() => {
	return {
		name: [`物资${props.title}`],
		icon: ["fas", "square-share-nodes"]
	}
})

const rightTitle = {
	name: ["相关附件"],
	icon: ["fas", "square-share-nodes"]
}

/**
 * 按钮 配置
 */
const formBtnList = ref([
	{ name: "取消", icon: ["fas", "circle-minus"] },
	{ name: "保存", icon: ["fas", "floppy-disk"] }
])

const formSubmitBtnList = ref([
	{ name: "提交审核", icon: ["fas", "circle-check"] }
])

const ruleFormRef = ref<FormInstance>()

const formModelData = ref<{ [propName: string]: any }>({
	code: props.matCode
})

/**
 * 保存旧的表单数据
 */
const oldFormData = ref<string>()

/**
 * 表单 配置
 */
const formEl = computed<FormElementType[][]>(() => [
	[
		{
			label: `${props.title}物资编码`,
			name: "code",
			disabled: true
		}
	],
	[
		{
			label: `请填写${props.title}原因`,
			name: "reason",
			maxlength: 200,
			rows: 5,
			type: "textarea"
		}
	],
	[
		{
			label: "替换物资编码",
			name: "replaceMaterialCode",
			vname: "replaceMaterialId",
			type: "drawer",
			hidden: props.operateType === IMaterialBusinessType.materialThawing,
			clear: true,
			clickApi: () => {
				matSelectorVisible.value = true
			}
		}
	]
])

/**
 * 表单校验 配置
 */
const rules = computed<FormRules<typeof formModelData.value>>(() => ({
	reason: {
		required: true,
		message: `${props.title == "冻结" ? "冻结原因" : "解冻原因"}不能为空`,
		trigger: "change"
	}
	/* replaceMaterialCode: {
		required:
			props.operateType === IMaterialBusinessType.materialThawing
				? false
				: true,
		message: "替换物资编码不能为空",
		trigger: "change"
	} */
}))

/* 表单 操作 */
const formBtnLoading = ref(false)
const formBtnAction = (btnName: string | undefined) => {
	if (btnName === "提交审核") {
		if (!ruleFormRef.value) {
			return
		}
		ruleFormRef.value?.validate(async (valid: any) => {
			if (!valid) {
				return
			}
			await showWarnConfirm("请确认是否提交本次数据？")
			formBtnLoading.value = true
			drawerLoading.value = true
			try {
				// 如果主表有修改，则先更新主表数据
				if (oldFormData.value != JSON.stringify(formModelData.value)) {
					await MatApplyApi.saveMatOperateInfo({
						id: formModelData.value.id,
						materialId: props.matId,
						type: props.operateType,
						replaceMaterialId: formModelData.value.replaceMaterialId,
						reason: formModelData.value.reason
					})

					oldFormData.value = JSON.stringify(formModelData.value)
				}

				const idempotentToken = getIdempotentToken(
					IIdempotentTokenTypePre.other,
					IIdempotentTokenType.materialCode,
					props.matId
				)

				await MatApplyApi.matManualPublishOperate(
					{
						id: formModelData.value.id, // 操作Id即主表Id
						materialIdList: [props.matId],
						type: props.operateType,
						replaceMaterialId: formModelData.value.replaceMaterialId,
						reason: formModelData.value.reason
					},
					idempotentToken
				)

				ElMessage.success("操作成功")

				emit("update")
				emit("close")
			} finally {
				formBtnLoading.value = false
				drawerLoading.value = false
			}
		})
	} else if (btnName === "保存") {
		if (!ruleFormRef.value) {
			return
		}
		ruleFormRef.value?.validate(async (valid: any) => {
			if (!valid) {
				return
			}
			formBtnLoading.value = true
			try {
				const res = await MatApplyApi.saveMatOperateInfo({
					id: formModelData.value.id,
					materialId: props.matId,
					type: props.operateType,
					replaceMaterialId: formModelData.value.replaceMaterialId,
					reason: formModelData.value.reason
				})

				formModelData.value.id = res.id // 明细 Id
				//	formModelData.value.operateId = res.operateId
				ElMessage.success("操作成功")

				oldFormData.value = JSON.stringify(formModelData.value)
			} finally {
				formBtnLoading.value = false
			}
		})
	} else if (btnName === "取消") {
		emit("close")
	}
}

const matManualListColumns = ref<TableColumnType[]>([
	{ label: "物资编码", prop: "code", width: 150 },
	{ label: "物资名称", prop: "label", width: 150 },
	{
		label: "物资分类编码",
		prop: "materialTypeCode",
		width: 100
	},
	{
		label: "物资分类名称",
		prop: "materialTypeLabel",
		width: 200
	},
	{
		label: "规格型号",
		prop: "version",
		needSlot: false,
		width: 150
	},
	{
		label: "技术参数",
		prop: "technicalParameter",
		needSlot: false,
		minWidth: 100,
		align: "left"
	},
	{ label: "物资性质", prop: "attribute", needSlot: true, width: 120 },
	{ label: "库存单位", prop: "buyUnit", needSlot: true, width: 90 }
])

async function handleSaveMat(e?: any[]) {
	formModelData.value.replaceMaterialId = first(e)?.id
	formModelData.value.replaceMaterialCode = first(e)?.code
	matSelectorVisible.value = false
}

async function getDetail() {
	if (!(props.matId && props.operateType)) {
		return false
	}

	drawerLoading.value = true
	try {
		const res = await MatApplyApi.getMatOperateInfo({
			type: props.operateType,
			materialId: props.matId
		})

		formModelData.value = { ...formModelData.value, ...res }

		oldFormData.value = JSON.stringify(formModelData.value)
	} finally {
		drawerLoading.value = false
	}
}

onMounted(() => {
	getDetail()
})
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<div class="drawer-column left">
			<Title :title="titleConf" />
			<el-form
				style="padding-bottom: 30px"
				class="content form-base"
				ref="ruleFormRef"
				:model="formModelData"
				:rules="rules"
				label-position="top"
			>
				<FormElement :form-element="formEl" :form-data="formModelData" />
			</el-form>
			<ButtonList
				class="footer"
				:button="formBtnList"
				:loading="formBtnLoading"
				@onBtnClick="formBtnAction"
			/>
		</div>
		<div class="drawer-column right" :class="canEditExtra ? '' : ' disabled'">
			<Title :title="rightTitle" />

			<el-scrollbar>
				<TableFile
					:business-type="businessType"
					:business-id="formModelData.id"
					:mod="IModalType.edit"
				/>
			</el-scrollbar>

			<ButtonList
				class="footer"
				:button="formSubmitBtnList"
				:loading="formBtnLoading"
				@onBtnClick="formBtnAction"
			/>
		</div>

		<!-- 物资编码手册选择器 -->
		<Drawer
			v-model:drawer="matSelectorVisible"
			:size="modalSize.lg"
			destroy-on-close
		>
			<mat-manual-selector
				:multiple="false"
				:selectedIds="[formModelData.replaceMaterialId]"
				:columns="matManualListColumns"
				:tableReqParams="{ ids: `!${props.matId}` }"
				@save="handleSaveMat"
				@close="matSelectorVisible = false"
			/>
		</Drawer>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.drawer-container {
	.left {
		width: 310px;
	}

	.right {
		width: calc(100% - 310px);
	}
}
</style>
