<script lang="ts" setup>
import { watch, ref, onMounted, reactive } from "vue"
import { MatApplyApi } from "@/app/baseline/api/material/matApply"
import { ElMessage } from "element-plus"
import {
	checkPermission,
	isCheckPermission
} from "@/app/platform/utils/permission"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import { appStatus, DictApi } from "@/app/baseline/api/dict"
import ApplyDrawer from "./components/matApplyDrawer.vue"
import { powerList } from "@/app/baseline/views/components/define.d.ts"
import { useTbInit } from "@/app/baseline/views/components/tableBase.ts"
import ApprovedDrawer from "@/app/baseline/views/components/approvedDrawer.vue"
import MatApplyViewDrawer from "@/app/baseline/views/material/components/matApplyViewDrawer.vue"
import { hasEditByBpm, hasViewByBpm } from "../../utils"
import { useMessageBoxInit } from "@/app/baseline/views/components/messageBox.ts"
import { modalSize } from "@/app/baseline/utils/layout-config"

const { showDelConfirm } = useMessageBoxInit()
/*-------------------初始化表格-start-------------------*/
const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	tbBtnLoading,
	pageSize,
	currentPage,
	pageTotal,
	selectedTableList,
	fetchParam,
	tbBtns,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange,
	onDataSelected,
	onBtnClick
} = useTbInit()
tableProp.value = [
	{
		label: "物资分类编码",
		prop: "materialTypeCode",
		width: 100
	},
	{
		label: "物资分类名称",
		prop: "materialTypeLabel",
		width: 200
	},
	{ label: "物资编码", prop: "code", width: 150 },
	{ label: "物资名称", prop: "label", width: 200 },
	{
		label: "规格型号",
		prop: "version",
		needSlot: false,
		width: 150
	},
	{
		label: "技术参数",
		prop: "technicalParameter",
		needSlot: false,
		minWidth: 200,
		align: "left"
	},
	{
		label: "物资性质",
		prop: "attribute",
		needSlot: true,
		width: 150
	},
	{ label: "采购单位", prop: "buyUnit", needSlot: true, width: 90 },
	{ label: "审批状态", prop: "bpmStatus", needSlot: true, width: 90 },
	{ label: "创建人", prop: "createdBy_view", width: 120 },
	{ label: "创建时间", prop: "createdDate", width: 160 },
	{
		label: "操作",
		width: 200,
		prop: "operations",
		fixed: "right",
		needSlot: true
	}
]
onDataSelected.value = (rowList: { [propName: string]: any }[]) => {
	selectedTableList.value = rowList
	const isNoSel = rowList.length <= 0
	//单选
	const arrStatus = rowList.map((item) => item.bpmStatus)
	tbBtns.value[0].disabled =
		isNoSel || !arrStatus.includes(appStatus.needApproval)
}
fetchFunc.value = MatApplyApi.getMatApplyListUser
tbBtns.value = []
/*-------------------初始化表格-end-------------------*/

const rightTitle = {
	name: ["编码申请"],
	icon: ["fas", "square-share-nodes"]
}
const addBtn = [
	{
		name: "新增物资编码",
		roles: powerList.matApplyBtnCreate,
		icon: ["fas", "square-plus"]
	}
]

const dictOptions = ref<Record<string, any[]>>({
	INVENTORY_UNIT: [],
	MATERIAL_NATURE: []
})

//查询
const queryArrList = computed(() => [
	{
		name: "物资编码",
		key: "code",
		placeholder: "请输入物资编码",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资名称",
		key: "label",
		placeholder: "请输入物资名称",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资性质",
		key: "attribute",
		type: "select",
		children: dictOptions.value.MATERIAL_NATURE,
		placeholder: "请选择物资性质"
	}
	// {
	// 	name: "审批状态",
	// 	key: "bpmStatus",
	// 	type: "select",
	// 	placeholder: "请选择",
	// 	children: DictApi.getBpmStatus()
	// }
])

////tabs
const tabList = [
	{
		name: "草稿箱",
		icon: ["fas", "square-plus"]
	},
	{
		name: "审批中",
		icon: ["fas", "square-plus"]
	},
	{
		name: "已审批",
		icon: ["fas", "square-plus"]
	}
]
const tabNum = ref([0, 0, 0])
const tabStatus = reactive([
	[appStatus.pendingApproval, appStatus.rejected],
	[appStatus.underApproval],
	[appStatus.approved]
])
const activeName = ref(tabList[0].name)
const handleTabsClick = (tab: any) => {
	activeName.value = tab.paneName
	fetchParam.value.bpmStatus = tabStatus[tab.index].join(",")
	getTableData()
}

//table
const editId = ref<any>("")
const editTask = reactive<any>({})
//drawer
const showDrawer = ref<boolean>(false)
const showViewDrawer = ref<boolean>(false)
const showViewDrawerModel = ref<string>("view")
const drawerSize = 1800
// 审批

const getTaskByBusinessIds = (id: any) => {
	const data = {
		businessIds: [id],
		camundaKey: "material_code"
	}
	MatApplyApi.getTaskByBusinessIds(data).then((res: any) => {
		if (Array.isArray(res) && res.length > 0) {
			Object.assign(editTask, res[res.length - 1])
			showViewDrawer.value = true
		} else {
			showDrawer.value = true
			showViewDrawerModel.value = "view"
			//showViewDrawerModel.value = "view"
			//ElMessage.warning("未查询到审批信息")
		}
	})
}
const onApproval = () => {
	const selLen = selectedTableList.value.length
	if (selLen <= 0) {
		return ElMessage.warning("请选择物资")
	} else if (selLen === 1) {
		editId.value = selectedTableList.value[0].id
		showViewDrawerModel.value = "edit"
		getTaskByBusinessIds(editId.value)
	}
	return Promise.reject()
}
const onRowView = (row: any) => {
	editId.value = row.id
	showViewDrawerModel.value = "view"
	getTaskByBusinessIds(editId.value)
}
const onRowEdit = (row: any) => {
	editId.value = row.id
	showDrawer.value = true
	showViewDrawerModel.value = "edit"
}
const onRowDelete = (row: any) => {
	showDelConfirm().then(() => {
		MatApplyApi.deleteMatApply(row.id).then(() => {
			ElMessage.success("移除成功")
			getTableData()
		})
	})
}
fetchParam.value = {
	sord: "desc",
	sidx: "createdDate",
	bpmStatus: tabStatus[0].join(",")
}
// 获取表格
const getTableData = (data?: any) => {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		...data
	}
	//ElMessage.info(JSON.stringify(fetchParam.value))
	fetchTableData()
	getBmpStatusStatistics()
}

const onAddBtn = () => {
	showViewDrawerModel.value = "edit"
	editId.value = ""
	showDrawer.value = true
}
// 弹窗关闭
const onCloseDrawer = (msg: string) => {
	if (msg === "pub") {
		fetchTableData()
		getBmpStatusStatistics()

		//getTableData()
		showDrawer.value = false
	} else if (msg === "save") {
		fetchTableData()
		getBmpStatusStatistics()

		//getTableData()
		showViewDrawer.value = false
	} else {
		showDrawer.value = false
	}
}

function getDictByCodeList(): Promise<null> {
	return new Promise((resolve) => {
		DictApi.getDictByCodeList(dictOptions)
			.then((res) => {
				dictOptions.value = res
			})
			.finally(() => {
				resolve(null)
			})
	})
}

function getBmpStatusStatistics(): Promise<null> {
	return new Promise((resolve) => {
		const params = JSON.parse(JSON.stringify(fetchParam.value))
		delete params.currentPage
		delete params.pageSize
		delete params.bpmStatus
		MatApplyApi.getBmpStatusStatistics(params)
			.then((res) => {
				tabNum.value[0] =
					(res[appStatus.rejected] ? res[appStatus.rejected] : 0) +
					(res[appStatus.pendingApproval] ? res[appStatus.pendingApproval] : 0)
				tabNum.value[1] = res[appStatus.underApproval]
					? res[appStatus.underApproval]
					: 0
				tabNum.value[2] = res[appStatus.approved] ? res[appStatus.approved] : 0
			})
			.finally(() => {
				resolve(null)
			})
	})
}

watch(
	() => showDrawer.value,
	(val: boolean) => {
		if (!val) {
			editId.value = ""
		}
	},
	{ immediate: true }
)

onMounted(() => {
	Promise.all([getDictByCodeList()]).then(() => {
		getTableData()
	})
})

defineOptions({
	name: "MatApplyManagement"
})
</script>
<template>
	<div class="app-container">
		<div class="whole-frame">
			<ModelFrame class="top-frame">
				<Query
					:queryArrList="queryArrList"
					class="ml10"
					@getQueryData="getTableData"
				/>
			</ModelFrame>
			<ModelFrame class="bottom-frame">
				<Title :title="rightTitle" :button="addBtn" @onBtnClick="onAddBtn">
					<div class="app-tabs-wrapper">
						<el-tabs v-model="activeName" @tab-click="handleTabsClick">
							<el-tab-pane
								v-for="(tab, index) in tabList"
								:key="index"
								:label="`${tab.name}（${tabNum[index]}）`"
								:name="tab.name"
								:index="tab.name"
							/>
						</el-tabs>
					</div>
				</Title>
				<PitayaTable
					ref="tableRef"
					:need-index="true"
					:columns="tableProp"
					:tableData="tableData"
					:total="pageTotal"
					:single-select="true"
					:need-selection="false"
					@onSelectionChange="onDataSelected"
					@on-current-page-change="onCurrentPageChange"
					:table-loading="tableLoading"
				>
					<template #buyUnit="{ rowData }">
						<dict-tag
							:options="dictOptions.INVENTORY_UNIT"
							:value="rowData.buyUnit"
						/>
					</template>
					<template #attribute="{ rowData }">
						<dict-tag
							:options="DictApi.getMatAttr()"
							:value="rowData.attribute"
						/>
					</template>
					<template #bpmStatus="{ rowData }">
						<dict-tag
							:options="DictApi.getBpmStatus()"
							:value="rowData.bpmStatus"
						/>
					</template>
					<template #operations="{ rowData }">
						<slot
							v-if="
								isCheckPermission(powerList.matApplyBtnPreview) ||
								(isCheckPermission(powerList.matApplyBtnEdit) &&
									hasEditByBpm(rowData.bpmStatus, rowData.createdBy)) ||
								(isCheckPermission(powerList.matApplyBtnDrop) &&
									hasEditByBpm(rowData.bpmStatus, rowData.createdBy))
							"
						>
							<el-button
								v-btn
								link
								@click="onRowEdit(rowData)"
								:disabled="checkPermission(powerList.matApplyBtnEdit)"
								v-if="
									isCheckPermission(powerList.matApplyBtnEdit) &&
									hasEditByBpm(rowData.bpmStatus, rowData.createdBy)
								"
							>
								<font-awesome-icon :icon="['fas', 'pen-to-square']" />
								<span class="table-inner-btn">编辑</span>
							</el-button>

							<el-button
								v-btn
								link
								@click="onRowView(rowData)"
								:disabled="checkPermission(powerList.matApplyBtnPreview)"
								v-if="isCheckPermission(powerList.matApplyBtnPreview)"
							>
								<font-awesome-icon :icon="['fas', 'eye']" />
								<span class="table-inner-btn">查看</span>
							</el-button>

							<el-button
								v-btn
								link
								@click="onRowDelete(rowData)"
								:disabled="checkPermission(powerList.matApplyBtnDrop)"
								v-if="
									isCheckPermission(powerList.matApplyBtnDrop) &&
									hasEditByBpm(rowData.bpmStatus, rowData.createdBy)
								"
							>
								<font-awesome-icon :icon="['fas', 'trash-can']" />
								<span class="table-inner-btn">移除</span>
							</el-button>
						</slot>
						<slot v-else>---</slot>
					</template>
					<template #footerOperateLeft>
						<ButtonList
							v-if="activeName == '审批中'"
							class="btn-list"
							:is-not-radius="true"
							:button="tbBtns"
							:loading="tbBtnLoading"
							@on-btn-click="onBtnClick"
						/>
					</template>
				</PitayaTable>
				<!-- 新增、编辑弹窗 -->
				<Drawer
					:size="modalSize.xxl"
					v-model:drawer="showDrawer"
					:destroyOnClose="true"
				>
					<ApplyDrawer
						v-if="showViewDrawerModel == 'edit'"
						:id="editId"
						:table-data="tableData"
						@on-save-or-close="onCloseDrawer"
					/>

					<MatApplyViewDrawer
						v-else-if="showViewDrawerModel == 'view'"
						:id="editId"
						:table-data="tableData"
						:mod="showViewDrawerModel"
						@on-save-or-close="onCloseDrawer"
					/>
				</Drawer>
				<!-- 审批、查看弹窗 -->
				<Drawer
					:size="modalSize.xxl"
					v-model:drawer="showViewDrawer"
					:destroyOnClose="true"
				>
					<approved-drawer
						:mod="showViewDrawerModel"
						:task-value="editTask"
						@on-save-or-close="onCloseDrawer"
					/>
					<!--					<MatApplyViewDrawer-->
					<!--						:id="editId"-->
					<!--						:table-data="tableData"-->
					<!--						:mod="showViewDrawerModel"-->
					<!--						@on-save-or-close="onCloseDrawer"-->
					<!--					/>-->
				</Drawer>
			</ModelFrame>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
