<!-- 编码手册 V1.0 废弃-->
<script lang="ts" setup>
import { ref, onMounted } from "vue"
import {
	checkPermission,
	isCheckPermission
} from "@/app/platform/utils/permission"
import TreeMatType from "@/app/baseline/views/components/v2/treeMatType.vue"
import MatManualBuyDrawer from "@/app/baseline/views/material/components/matManualBuyDrawer.vue"
import MatManualAttrDrawer from "@/app/baseline/views/material/components/matManualAttrDrawer.vue"
import { MatApplyApi } from "@/app/baseline/api/material/matApply"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import { appStatus, DictApi } from "@/app/baseline/api/dict"

import { useTbInit } from "@/app/baseline/views/components/tableBase.ts"
import { matStatus } from "@/app/baseline/api/dict.ts"
import { ElMessage } from "element-plus"
import { powerList } from "@/app/baseline/views/components/define.d.ts"
import { modalSize } from "@/app/baseline/utils/layout-config"

const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	tbBtnLoading,
	pageSize,
	currentPage,
	pageTotal,
	selectedTableList,
	fetchParam,
	tbBtns,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange,
	onDataSelected,
	onBtnClick
} = useTbInit()
tableProp.value = [
	{ label: "物资编码", prop: "code", width: 150 },
	{ label: "物资名称", prop: "label", width: 200 },
	{
		label: "物资分类编码",
		prop: "materialTypeCode",
		width: 100
	},
	{
		label: "物资分类名称",
		prop: "materialTypeLabel",
		width: 200
	},
	{
		label: "物资性质",
		prop: "attribute",
		needSlot: true,
		width: 150
	},
	{ label: "物资状态", prop: "status", needSlot: true, width: 90 },
	{
		label: "规格型号",
		prop: "version",
		needSlot: false,
		width: 150
	},
	{
		label: "技术参数",
		prop: "technicalParameter",
		needSlot: false,
		minWidth: 200,
		align: "left"
	},
	{ label: "采购单位", prop: "buyUnit", needSlot: true, width: 90 },

	{ label: "更新人", prop: "lastModifiedBy_view", width: 120 },
	{ label: "更新时间", prop: "lastModifiedDate", width: 160 },
	{ label: "申请人", prop: "createdBy_view", width: 120 },
	{ label: "创建时间", prop: "createdDate", width: 160 },
	{ label: "所属公司", prop: "sysCommunityId_view", width: 120 },
	{
		label: "操作",
		width: 100,
		prop: "operations",
		fixed: "right",
		needSlot: true
	}
]
const isAllElEqua = (array, value) => {
	return array.every((element) => element === value)
}
onDataSelected.value = (rowList: { [propName: string]: any }[]) => {
	selectedTableList.value = rowList
	const isNoSel = rowList.length <= 0
	const isMulti = rowList.length > 1
	const arrStatus = rowList.map((item) => item.status)

	tbBtns.value[0].disabled =
		!isAllElEqua(arrStatus, matStatus.normal) || isNoSel
	tbBtns.value[1].disabled =
		!isAllElEqua(arrStatus, matStatus.freeze) || isNoSel
	tbBtns.value[2].disabled =
		!isAllElEqua(arrStatus, matStatus.freeze) || isNoSel
	tbBtns.value[3].disabled = isNoSel || isMulti
	tbBtns.value[4].disabled = isNoSel
}
fetchFunc.value = MatApplyApi.getMatApplyList
tbBtns.value = [
	{
		name: "冻结",
		roles: powerList.matManualBtnFreeze,
		icon: ["fas", "lock"],
		disabled: true,
		confirm: true,
		click: () => handleMatStatus(matStatus.freeze, "冻结")
	},
	{
		name: "作废",
		roles: powerList.matManualBtnVoid,
		icon: ["fas", "trash-can"],
		disabled: true,
		confirm: true,
		click: () => handleMatStatus(matStatus.canceled, "作废")
	},
	{
		name: "解冻",
		roles: powerList.matManualBtnThawing,
		icon: ["fas", "unlock-keyhole"],
		disabled: true,
		confirm: true,
		click: () => handleMatStatus(matStatus.normal, "解冻")
	},
	{
		name: "维护采购信息",
		roles: powerList.matManualBtnProcurement,
		icon: ["fas", "cart-shopping"],
		disabled: true,
		click: () => handleBuyInfo()
	},
	{
		name: "更新物资性质",
		roles: powerList.matManualBtnMaterialProperties,
		icon: ["fas", "file-signature"],
		disabled: true,
		click: () => handleUpdateMatAttr()
	}
]

const leftTitle = {
	name: ["物资编码分类"],
	icon: ["fas", "square-share-nodes"]
}
const rightTitle = ref<any>({
	name: ["物资编码手册"],
	icon: ["fas", "square-share-nodes"]
})
//获取字典
const dictOptions = ref<Record<string, any[]>>({
	INVENTORY_UNIT: [],
	MATERIAL_NATURE: []
})

//查询
const queryArrList = computed(() => [
	{
		name: "物资编码",
		key: "code",
		placeholder: "请输入物资编码",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资名称",
		key: "label",
		placeholder: "请输入物资名称",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资性质",
		key: "attribute",
		type: "select",
		children: dictOptions.value?.MATERIAL_NATURE,
		placeholder: "请选择物资性质"
	},
	{
		name: "物资状态",
		key: "status",
		type: "select",
		placeholder: "请选择物资状态",
		children: DictApi.getMatStatus()
	},
	{
		name: "更新人",
		key: "lastModifiedRealName",
		placeholder: "请输入更新人",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "更新时间",
		key: "startAndEndTime",
		type: "startAndEndTime"
	}
])

//抽屉
/* const drawerBuySize = 1800
const drawerAttrSize = 1500 */
const showBuyDrawer = ref<boolean>(false)
const buyDrawerModel = ref<string>("edit")
const showAttrDrawer = ref<boolean>(false)

const handleMatStatus = (status: string, msg: string) => {
	if (selectedTableList.value.length <= 0)
		return ElMessage.warning("请选择物资")

	tbBtnLoading.value = true
	const arrId = selectedTableList.value.map((item) => item.id)
	const formData = new FormData()
	formData.append("ids", arrId.join(","))
	formData.append("status", status)
	MatApplyApi.updateMatStatusBatch(formData)
		.then((res) => {
			if (res) {
				ElMessage.success(msg + "成功")
				fetchTableData()
				tableRef.value.clearSelectedTableData()
			}
		})
		.catch((err) => {
			throw new Error("enabledOrNotApi():::" + err)
		})
		.finally(() => {
			tbBtnLoading.value = false
		})
}

const handleBuyInfo = () => {
	if (selectedTableList.value.length <= 0)
		return ElMessage.warning("请选择物资")
	const arrId = selectedTableList.value.map((item) => item.id)
	editId.value = arrId[0]
	buyDrawerModel.value = "edit"
	showBuyDrawer.value = true
	return Promise.reject()
}
const handleUpdateMatAttr = () => {
	if (selectedTableList.value.length <= 0)
		return ElMessage.warning("请选择物资")
	showAttrDrawer.value = true
	return Promise.reject()
}

const treeCheck = (selectedId: any, status: string) => {
	getQueryData({
		materialTypeIds: selectedId.join(","),
		materialTypeStatus: status
	})
}

const getQueryData = (data: { [propName: string]: any }) => {
	const { startAndEndTime } = data
	data.lastModifiedDate_start = startAndEndTime
		? `${startAndEndTime[0]} 00:00:00`
		: ""
	data.lastModifiedDate_end = startAndEndTime
		? `${startAndEndTime[1]} 23:59:59`
		: ""
	delete data.startAndEndTime
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		...{ bpmStatus: appStatus.approved },
		...data,
		sidx: "code",
		sord: "asc"
	}
	fetchTableData()
}
const editId = ref<any>("")
const onRowView = (row: any) => {
	buyDrawerModel.value = "view"
	editId.value = row.id
	showBuyDrawer.value = true
}
const onCloseBuyDrawer = (refresh: boolean) => {
	if (refresh) {
		fetchTableData()
	}
	showBuyDrawer.value = false
}
const onCloseAttrDrawer = (refresh: boolean) => {
	if (refresh) {
		fetchTableData()
	}
	showAttrDrawer.value = false
}

function getDictByCodeList(): Promise<null> {
	return new Promise((resolve) => {
		DictApi.getDictByCodeList(dictOptions)
			.then((res) => {
				dictOptions.value = res
			})
			.finally(() => {
				resolve(null)
			})
	})
}

const defaultStatus = matStatus.freeze + "," + matStatus.normal
onMounted(() => {
	Promise.all([getDictByCodeList()]).then(() => {
		treeCheck([], defaultStatus)
	})
})

// 分页

defineOptions({
	name: "MatManualManagement"
})
</script>
<template>
	<div class="app-container">
		<ModelFrame class="left-frame">
			<TreeMatType
				:title="leftTitle"
				:status="defaultStatus"
				:need-label-count="true"
				@onTreeCheck="treeCheck"
			/>
		</ModelFrame>
		<div class="right-frame">
			<ModelFrame class="top-frame">
				<Query :queryArrList="queryArrList" @getQueryData="getQueryData" />
			</ModelFrame>
			<ModelFrame class="whole-frame">
				<Title :title="rightTitle" />
				<PitayaTable
					ref="tableRef"
					:columns="tableProp"
					:table-data="tableData"
					:need-index="true"
					:need-pagination="true"
					:single-select="false"
					:need-selection="true"
					:total="pageTotal"
					@onSelectionChange="onDataSelected"
					@on-current-page-change="onCurrentPageChange"
					:table-loading="tableLoading"
				>
					<template #buyUnit="{ rowData }">
						<dict-tag
							:options="dictOptions.INVENTORY_UNIT"
							:value="rowData.buyUnit"
						/>
					</template>
					<template #attribute="{ rowData }">
						<dict-tag
							:options="DictApi.getMatAttr()"
							:value="rowData.attribute"
						/>
					</template>
					<template #bpmStatus="{ rowData }">
						<dict-tag
							:options="DictApi.getBpmStatus()"
							:value="rowData.bpmStatus"
						/>
					</template>
					<template #status="{ rowData }">
						<dict-tag
							:options="DictApi.getMatStatus()"
							:value="rowData.status"
						/>
					</template>
					<template #operations="{ rowData }">
						<slot v-if="isCheckPermission(powerList.matManualBtnPreview)">
							<el-button
								v-btn
								link
								@click="onRowView(rowData)"
								:disabled="checkPermission(powerList.matManualBtnPreview)"
								v-if="isCheckPermission(powerList.matManualBtnPreview)"
							>
								<font-awesome-icon :icon="['fas', 'eye']" />
								<span class="table-inner-btn">查看</span>
							</el-button>
						</slot>
						<slot v-else>---</slot>
					</template>
					<template #footerOperateLeft>
						<ButtonList
							class="btn-list"
							:is-not-radius="true"
							:button="tbBtns"
							:loading="tbBtnLoading"
							@on-btn-click="onBtnClick"
						/>
					</template>
				</PitayaTable>
				<!--维护采购信息-->
				<Drawer
					:size="modalSize.xxl"
					v-model:drawer="showBuyDrawer"
					:destroyOnClose="true"
				>
					<MatManualBuyDrawer
						:id="editId"
						:model="buyDrawerModel"
						@on-save-or-close="onCloseBuyDrawer"
					/>
				</Drawer>
				<Drawer
					:size="modalSize.xxl"
					v-model:drawer="showAttrDrawer"
					:destroyOnClose="true"
				>
					<MatManualAttrDrawer
						:sel-mat-list="selectedTableList"
						@on-save-or-close="onCloseAttrDrawer"
					/>
				</Drawer>
			</ModelFrame>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
