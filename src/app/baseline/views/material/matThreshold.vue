<!-- 物资 - 阈值管理 主表 -->
<script lang="ts" setup>
import { ref, onMounted } from "vue"
import { ElMessage } from "element-plus"

import {
	checkPermission,
	isCheckPermission
} from "@/app/platform/utils/permission"

import { DictApi } from "@/app/baseline/api/dict"
import {
	IMaterialThresholdStatus,
	getMaterialThresholdStatus
} from "@/app/baseline/api/materialDict"
import { useTbInit } from "@/app/baseline/views/components/tableBase"
import { useDictInit } from "@/app/baseline/views/components/dictBase"

import DictTag from "@/app/baseline/views/components/dictTag.vue"
import { useMessageBoxInit } from "@/app/baseline/views/components/messageBox"
import { first } from "lodash-es"
import { IModalType } from "../../utils/types/common"
import matThresholdEditor from "./matThreshold/matThresholdEditor.vue"
import matThresholdDetail from "./matThreshold/matThresholdDetail.vue"
import { modalSize } from "@/app/baseline/utils/layout-config"
import { MaterialThresholdVo } from "../../utils/types/material-threshold"
import {
	closeMaterialThreshold,
	delMaterialThreshold,
	enableMaterialThreshold,
	getMaterialThresholdPaged
} from "../../api/material/matThreshold"
import { hasPermi } from "../../utils"
import { powerList } from "@/app/baseline/views/components/define.d"

const { showDelConfirm, showWarnConfirm } = useMessageBoxInit()
const { dictOptions, getDictByCodeList } = useDictInit()

const tbBtnLoading = ref(false)
const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	currentPage,
	pageTotal,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = useTbInit<MaterialThresholdVo, any>()

tableProp.value = [
	{ label: "配置名称", prop: "label", minWidth: 120, fixed: "left" },
	{
		label: "相似阈值",
		prop: "thresholdNum",
		needSlot: true,
		minWidth: 120,
		align: "right"
	},
	{ label: "已达阈值的处理方式", prop: "handMethod_view", width: 150 },
	{ label: "状态", prop: "status", needSlot: true, minWidth: 85 },
	{ label: "配置说明", prop: "remark", minWidth: 120 },
	{ label: "更新人", prop: "lastModifiedBy_view", width: 120 },
	{ label: "更新时间", prop: "lastModifiedDate", width: 160 },
	{
		label: "操作",
		width: 200,
		prop: "operations",
		fixed: "right",
		needSlot: true
	}
]

/**
 * table 底部 按钮 配置
 */
const tableFooterBtns = computed(() => {
	return [
		{
			name: "启用",
			roles: powerList.matThresholdBtnStart,
			icon: ["fas", "power-off"],
			disabled:
				selectedTableList.value.length == 0 ||
				first(selectedTableList.value)?.status ==
					IMaterialThresholdStatus.started.toString()
		},
		{
			name: "关闭",
			roles: powerList.matThresholdBtnClose,
			icon: ["fas", "circle-stop"],
			disabled:
				selectedTableList.value.length == 0 ||
				first(selectedTableList.value)?.status !==
					IMaterialThresholdStatus.started.toString()
		}
	]
})

fetchFunc.value = getMaterialThresholdPaged

const rightTitle = {
	name: ["相似物资阈值配置"],
	icon: ["fas", "square-share-nodes"]
}
const titleBtnConf = [
	{
		name: "新建相似配置",
		roles: powerList.matThresholdBtnCreate,
		icon: ["fas", "square-plus"]
	}
]

/**
 * 查询条件 配置
 */
const queryArrList = computed(() => [
	{
		name: "配置名称",
		key: "label",
		placeholder: "请输入配置名称",
		enableFuzzy: true,
		type: "input"
	},
	/* {
		name: "物资性质",
		key: "attribute",
		placeholder: "请选择",
		enableFuzzy: false,
		type: "select",
		children: dictOptions.value.MATERIAL_NATURE
	}, */
	{
		name: "处理方式",
		key: "handMethod",
		placeholder: "请选择",
		enableFuzzy: false,
		type: "select",
		children: dictOptions.value.THRESHOLD_HAND_METHOD
	},

	{
		name: "状态",
		key: "status",
		placeholder: "请选择",
		type: "select",
		children: DictApi.getStpStatus()
	}
])

//搜索条件确认回调
function getQueryData(data: { [propName: string]: any }) {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		...data,
		sidx: "lastModifiedDate",
		sord: "desc"
	}
	fetchTableData()
}

/**
 * 启用/关闭 操作
 * @param status
 */
async function handleChangeStatus(btnName?: string) {
	await showWarnConfirm(`请确认是否${btnName}？`)

	tbBtnLoading.value = true

	try {
		const api =
			btnName === "启用" ? enableMaterialThreshold : closeMaterialThreshold
		await api(first(selectedTableList.value)?.id)
		fetchTableData()
		ElMessage.success("操作成功")
	} finally {
		tbBtnLoading.value = false
	}
}

/**
 * 当前编辑行 id
 */
const editId = ref("")

const editorVisible = ref(false)
const detailVisible = ref(false)
const editorMode = ref(IModalType.create)

/**
 * 添加
 */
function handleAdd() {
	editId.value = ""
	editorVisible.value = true
	editorMode.value = IModalType.create
}

/**
 * 编辑
 * @param row
 */
function handleRowEdit(row: any) {
	editorVisible.value = true
	editorMode.value = IModalType.edit
	editId.value = row.id
}

/**
 * 查看
 * @param row
 */
function handleRowView(row: any) {
	detailVisible.value = true
	editorMode.value = IModalType.view
	editId.value = row.id
}

/**
 * 删除
 * @param row
 */
async function handleRowDel(row: any) {
	await showDelConfirm()
	await delMaterialThreshold(row.id)

	ElMessage.success("操作成功")
	fetchTableData()
}

onMounted(() => {
	getDictByCodeList(["THRESHOLD_HAND_METHOD"])
	getQueryData({})
})
</script>
<template>
	<div class="app-container">
		<div class="whole-frame">
			<ModelFrame class="top-frame">
				<Query
					:queryArrList="queryArrList"
					class="ml10"
					@getQueryData="getQueryData"
				/>
			</ModelFrame>
			<ModelFrame class="bottom-frame">
				<Title
					:title="rightTitle"
					:button="(titleBtnConf as any)"
					@onBtnClick="handleAdd"
				/>
				<PitayaTable
					ref="tableRef"
					:columns="tableProp"
					:table-data="tableData"
					:needSelection="true"
					:single-select="true"
					:need-index="true"
					:need-pagination="true"
					:total="pageTotal"
					@onSelectionChange="selectedTableList = $event"
					@on-current-page-change="onCurrentPageChange"
					:table-loading="tableLoading"
				>
					<template #thresholdNum="{ rowData }">
						{{ (rowData.thresholdNum * 100).toFixed(2) }}%
					</template>

					<!-- <template #attribute="{ rowData }">
						<dict-tag
							:options="DictApi.getMatAttr()"
							:value="rowData.attribute"
						/>
					</template> -->

					<template #status="{ rowData }">
						<dict-tag
							:options="getMaterialThresholdStatus()"
							:value="rowData.status"
						/>
					</template>

					<template #operations="{ rowData }">
						<slot
							v-if="
								(rowData.status == IMaterialThresholdStatus.drafted &&
									isCheckPermission(powerList.matThresholdBtnEdit) &&
									hasPermi(rowData.createdBy)) ||
								isCheckPermission(powerList.matThresholdBtnPreview) ||
								(rowData.status != IMaterialThresholdStatus.started &&
									hasPermi(rowData.createdBy) &&
									isCheckPermission(powerList.matThresholdBtnDrop))
							"
						>
							<el-button
								v-btn
								link
								@click.stop="handleRowEdit(rowData)"
								:disabled="checkPermission(powerList.matThresholdBtnEdit)"
								v-if="
									rowData.status == IMaterialThresholdStatus.drafted &&
									isCheckPermission(powerList.matThresholdBtnEdit) &&
									hasPermi(rowData.createdBy)
								"
							>
								<font-awesome-icon :icon="['fas', 'pen-to-square']" />
								<span class="table-inner-btn">编辑</span>
							</el-button>
							<el-button
								v-btn
								link
								@click.stop="handleRowView(rowData)"
								:disabled="checkPermission(powerList.matThresholdBtnPreview)"
								v-if="isCheckPermission(powerList.matThresholdBtnPreview)"
							>
								<font-awesome-icon :icon="['fas', 'eye']" />
								<span class="table-inner-btn">查看</span>
							</el-button>
							<el-button
								v-btn
								link
								@click.stop="handleRowDel(rowData)"
								:disabled="checkPermission(powerList.matThresholdBtnDrop)"
								v-if="
									rowData.status != IMaterialThresholdStatus.started &&
									hasPermi(rowData.createdBy) &&
									isCheckPermission(powerList.matThresholdBtnDrop)
								"
							>
								<font-awesome-icon :icon="['fas', 'trash-can']" />
								<span class="table-inner-btn">移除</span>
							</el-button>
						</slot>
						<slot v-else>---</slot>
					</template>
					<template #footerOperateLeft>
						<ButtonList
							class="btn-list"
							:is-not-radius="true"
							:button="tableFooterBtns"
							:loading="tbBtnLoading"
							@on-btn-click="handleChangeStatus"
						/>
					</template>
				</PitayaTable>

				<!-- 新建/编辑 -->
				<Drawer
					:size="modalSize.sm"
					v-model:drawer="editorVisible"
					:destroyOnClose="true"
				>
					<mat-threshold-editor
						:id="editId"
						:mode="editorMode"
						@update="fetchTableData"
						@close="editorVisible = false"
					/>
				</Drawer>

				<!-- 查看  -->
				<Drawer
					:size="modalSize.sm"
					v-model:drawer="detailVisible"
					:destroyOnClose="true"
				>
					<mat-threshold-detail :id="editId" @close="detailVisible = false" />
				</Drawer>
			</ModelFrame>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
