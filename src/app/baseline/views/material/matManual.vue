<!-- 编码手册 V2.0 重构-->
<script lang="ts" setup>
import { ref, onMounted } from "vue"
import {
	checkPermission,
	isCheckPermission
} from "@/app/platform/utils/permission"
import TreeMatType from "@/app/baseline/views/components/v2/treeMatType.vue"
import { MatApplyApi } from "@/app/baseline/api/material/matApply"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import { appStatus, DictApi } from "@/app/baseline/api/dict"

import { useTbInit } from "@/app/baseline/views/components/tableBase"
import { matStatus } from "@/app/baseline/api/dict"
import { ElMessage } from "element-plus"
import { powerList } from "@/app/baseline/views/components/define.d"
import { modalSize } from "@/app/baseline/utils/layout-config"
import matManualStatusEditor from "./matManual/matManualStatusEditor.vue"
import { first } from "lodash-es"
import { IMaterialBusinessType } from "../../utils/types/material"
import { IModalType } from "@/app/baseline/utils/types/common"
import matManualEditor from "./matManual/matManualEditor.vue"
import matManualAttrEditor from "./matManual/matManualAttrEditor.vue"
import matManualQualityEditor from "./matManual/matManualQualityEditor.vue"
import matManualRecipientConfEditor from "./matManual/matManualRecipientConfEditor.vue"
import { BaseLineSysApi } from "../../api/system"
import matManualImportDrawer from "./matManual/matManualImportDrawer.vue"
import { downloadExcel } from "../../utils/download"

const tbBtnLoading = ref(false)
const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	currentPage,
	pageTotal,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = useTbInit()
tableProp.value = [
	{
		label: "物资编码",
		prop: "code",
		width: 150,
		fixed: "left",
		sortable: true
	},
	{ label: "物资名称", prop: "label", width: 200 },
	{
		label: "物资分类编码",
		prop: "materialTypeCode",
		width: 140,
		sortable: true
	},
	{
		label: "物资分类名称",
		prop: "materialTypeLabel",
		width: 200
	},
	{
		label: "物资性质",
		prop: "attribute",
		needSlot: true,
		width: 120
	},
	{ label: "物资状态", prop: "status", needSlot: true, width: 90 },
	{ label: "操作状态", prop: "operateStatus", needSlot: true, width: 120 },
	{
		label: "规格型号",
		prop: "version",
		needSlot: false,
		width: 150
	},
	{
		label: "技术参数",
		prop: "technicalParameter",
		needSlot: false,
		minWidth: 200,
		align: "left"
	},
	{ label: "采购单位", prop: "buyUnit", needSlot: true, width: 90 },

	{ label: "更新人", prop: "lastModifiedBy_view", width: 120 },
	{ label: "更新时间", prop: "lastModifiedDate", width: 160, sortable: true },
	{ label: "申请人", prop: "createdBy_view", width: 120 },
	{ label: "创建时间", prop: "createdDate", width: 160, sortable: true },
	{ label: "所属公司", prop: "sysCommunityId_view", width: 120 },
	{
		label: "操作",
		width: 100,
		prop: "operations",
		fixed: "right",
		needSlot: true
	}
]

fetchFunc.value = MatApplyApi.getMatApplyList

const leftTitle = {
	name: ["编码分类"],
	icon: ["fas", "square-share-nodes"]
}
const rightTitle = ref<any>({
	name: ["物资编码手册"],
	icon: ["fas", "square-share-nodes"]
})
//获取字典
const dictOptions = ref<Record<string, any[]>>({
	INVENTORY_UNIT: [],
	MATERIAL_NATURE: []
})

/**
 * 查询条件配置
 */
const companyList = ref([])
const queryArrList = computed(() => [
	{
		name: "物资编码",
		key: "code",
		placeholder: "请输入物资编码",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资名称",
		key: "label",
		placeholder: "请输入物资名称",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "规格型号",
		key: "version",
		placeholder: "请输入规格型号",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "技术参数",
		key: "technicalParameter",
		placeholder: "请输入技术参数",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "物资性质",
		key: "attribute",
		type: "select",
		children: dictOptions.value?.MATERIAL_NATURE,
		placeholder: "请选择物资性质"
	},
	{
		name: "物资状态",
		key: "status",
		type: "select",
		placeholder: "请选择物资状态",
		children: DictApi.getMatStatus()
	},
	{
		name: "公司",
		key: "sysCommunityId",
		type: "select",
		children: companyList.value
	},
	{
		name: "更新人",
		key: "lastModifiedRealName",
		placeholder: "请输入更新人",
		enableFuzzy: true,
		type: "input"
	},
	{
		name: "创建时间",
		key: "createdStartAndEndTime",
		type: "startAndEndTime"
	},
	{
		name: "更新时间",
		key: "startAndEndTime",
		type: "startAndEndTime"
	}
])

const editorId = ref()

/**
 *  维护采购信息 编辑器  是否显示
 */
const editorVisible = ref<boolean>(false)
const editorMode = ref<IModalType>(IModalType.view)

/**
 * 更新物资性质  编辑器 是否显示
 */
const changeAttrVisible = ref<boolean>(false)

const treeCheck = (selectedId: any, status: string) => {
	getQueryData({
		materialTypeIds: selectedId.join(","),
		materialTypeStatus: status
	})
}

const getQueryData = (data: { [propName: string]: any }) => {
	const { startAndEndTime, createdStartAndEndTime } = data
	data.lastModifiedDate_start = startAndEndTime
		? `${startAndEndTime[0]} 00:00:00`
		: ""
	data.lastModifiedDate_end = startAndEndTime
		? `${startAndEndTime[1]} 23:59:59`
		: ""

	data.createdDate_start = createdStartAndEndTime
		? `${createdStartAndEndTime[0]} 00:00:00`
		: ""
	data.createdDate_end = createdStartAndEndTime
		? `${createdStartAndEndTime[1]} 23:59:59`
		: ""

	delete data.startAndEndTime
	delete data.createdStartAndEndTime

	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		...{ bpmStatus: appStatus.approved },
		...data,
		sidx: "code",
		sord: "asc"
	}
	fetchTableData()
}

function handleRowView(row: any) {
	editorVisible.value = true
	editorId.value = row.id
	editorMode.value = IModalType.view
}

function getDictByCodeList(): Promise<null> {
	return new Promise((resolve) => {
		DictApi.getDictByCodeList(dictOptions)
			.then((res) => {
				dictOptions.value = res
			})
			.finally(() => {
				resolve(null)
			})
	})
}
const dropdownMenu = computed(() => {
	/**
	 * 冻结 按钮 判断; 单选
	 */
	const isFreezeBtnDisable =
		selectedTableList.value.length === 1 &&
		first(selectedTableList.value)?.status === matStatus.normal

	/**
	 * 作废 按钮 判断; 单选
	 */
	const isCancelBtnDisable =
		selectedTableList.value.length === 1 &&
		first(selectedTableList.value)?.status === matStatus.freeze

	/**
	 * 解冻 按钮 判断; 目前单选; 后面改成 多选
	 */
	const isThawingBtnDisable =
		selectedTableList.value.length === 1 &&
		selectedTableList.value.every(
			(item: Record<string, any>) => item.status == matStatus.freeze.toString()
		)

	return {
		冻结: {
			name: "冻结",
			roles: powerList.matManualBtnFreeze,
			icon: ["fas", "lock"],
			disabled: !isFreezeBtnDisable
		},
		作废: {
			name: "作废",
			roles: powerList.matManualBtnVoid,
			icon: ["fas", "trash-can"],
			disabled: !isCancelBtnDisable
		},
		解冻: {
			name: "解冻",
			roles: powerList.matManualBtnThawing,
			icon: ["fas", "unlock-keyhole"],
			disabled: !isThawingBtnDisable
		},
		更新物资性质: {
			name: "更新物资性质",
			roles: powerList.matManualBtnMaterialProperties,
			icon: ["fas", "file-signature"],
			disabled: selectedTableList.value?.length > 0 ? false : true
		},
		更新物资材质: {
			name: "更新物资材质",
			roles: powerList.matManualBtnMaterialQuality,
			icon: ["fas", "file-signature"],
			disabled: selectedTableList.value?.length > 0 ? false : true
		}
	}
})
const tableFooterBtns = computed(() => {
	/**
	 * 冻结 按钮 判断; 单选
	 */
	const isFreezeBtnDisable =
		selectedTableList.value.length === 1 &&
		first(selectedTableList.value)?.status === matStatus.normal

	/**
	 * 作废 按钮 判断; 单选
	 */
	const isCancelBtnDisable =
		selectedTableList.value.length === 1 &&
		first(selectedTableList.value)?.status === matStatus.freeze

	/**
	 * 解冻 按钮 判断; 目前单选; 后面改成 多选
	 */
	const isThawingBtnDisable =
		selectedTableList.value.length === 1 &&
		selectedTableList.value.every(
			(item: Record<string, any>) => item.status == matStatus.freeze.toString()
		)

	return [
		/* {
			name: "冻结",
			roles: powerList.matManualBtnFreeze,
			icon: ["fas", "lock"],
			disabled: !isFreezeBtnDisable
		},
		{
			name: "作废",
			roles: powerList.matManualBtnVoid,
			icon: ["fas", "trash-can"],
			disabled: !isCancelBtnDisable
		},
		{
			name: "解冻",
			roles: powerList.matManualBtnThawing,
			icon: ["fas", "unlock-keyhole"],
			disabled: !isThawingBtnDisable
		}, */
		{
			name: "维护采购信息",
			roles: powerList.matManualBtnProcurement,
			icon: ["fas", "cart-shopping"],
			disabled: selectedTableList.value?.length === 1 ? false : true
		},
		/* {
			name: "更新物资性质",
			roles: powerList.matManualBtnMaterialProperties,
			icon: ["fas", "file-signature"],
			disabled: selectedTableList.value?.length > 0 ? false : true
		},
		{
			name: "更新物资材质",
			roles: powerList.matManualBtnMaterialQuality,
			icon: ["fas", "file-signature"],
			disabled: selectedTableList.value?.length > 0 ? false : true
		}, */
		{
			name: "接收人配置",
			roles: powerList.matManualBtnMaterialRecipientConf,
			icon: ["fas", "location-arrow"]
		},
		{
			name: "更新预估采购单价",
			roles: powerList.matManualBtnMaterialUpdatePrice,
			icon: ["fas", "file-signature"]
		}
	]
})

/**
 * 冻结/作废/解冻 弹窗 是否显示
 */
const matStatusEditorVisible = ref(false)
const matStatusEditorTitle = ref("")
const matOperateType = ref<IMaterialBusinessType>()

/**
 * 更新物资性质 弹窗 是否显示
 */
const matManualQualityEditorVisible = ref(false)

/**
 * 接收人配置 弹窗 是否显示
 */
const matManualRecipientConfEditorVisible = ref(false)

/**
 * 更新预估采购单价 弹窗 是否显示
 */
const updatePriceEditorVisible = ref<boolean>(false)

async function handleChangeStatus(btnName?: string) {
	if (btnName == "冻结" || btnName == "解冻" || btnName == "作废") {
		matOperateType.value =
			btnName == "冻结"
				? IMaterialBusinessType.materialFreeze
				: btnName == "解冻"
				? IMaterialBusinessType.materialThawing
				: IMaterialBusinessType.materialCancel

		matStatusEditorTitle.value = btnName

		tbBtnLoading.value = true
		try {
			await MatApplyApi.matManualCheckOperate({
				materialIdList: [first(selectedTableList.value)?.id],
				type: matOperateType.value
			})

			matStatusEditorVisible.value = true
		} finally {
			tbBtnLoading.value = false
		}
	} else if (btnName == "维护采购信息") {
		editorId.value = first(selectedTableList.value)?.id // arrId[0]
		editorMode.value = IModalType.edit
		editorVisible.value = true
	} else if (btnName == "更新物资性质") {
		tbBtnLoading.value = true

		try {
			await MatApplyApi.matManualCheckOperate({
				materialIdList: selectedTableList.value?.map((v) => v.id),
				type: IMaterialBusinessType.materialType
			})

			editorMode.value = IModalType.edit
			changeAttrVisible.value = true
		} finally {
			tbBtnLoading.value = false
		}
	} else if (btnName === "更新物资材质") {
		matManualQualityEditorVisible.value = true
	} else if (btnName === "接收人配置") {
		matManualRecipientConfEditorVisible.value = true
	} else if (btnName === "更新预估采购单价") {
		updatePriceEditorVisible.value = true
	}
}

const defaultStatus = matStatus.freeze + "," + matStatus.normal
onMounted(() => {
	BaseLineSysApi.getCompanyAllList().then((r) => (companyList.value = r))

	Promise.all([getDictByCodeList()]).then(() => {
		treeCheck([], defaultStatus)
	})
})

/**
 * 按库龄排序
 * @param column
 * @param prop 字段名称
 * @param order ascending 升序 descending 降序
 */
function handleSortChange(column: any, prop: any, order: any) {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		sord: order === "descending" ? "desc" : "asc",
		sidx: order ? prop : "code" // 排序字段
	}

	fetchTableData()
}

// 导入
const fileTxtConf = reactive({
	title: "更新预估采购单价",
	fileLable: "附件上传"
})

const uploadValidateUrl = ref<string>(
	"/baseline/material/materialProcureInfo/uploadValidate"
)

const downloadExcle = () => {
	MatApplyApi.getExcelModel({}).then((res) => {
		downloadExcel(res, "更新预估采购单价导入模板.xlsx")
	})
}

// 分页

defineOptions({
	name: "MatManualManagement"
})
</script>
<template>
	<div class="app-container">
		<ModelFrame class="left-frame">
			<TreeMatType
				:title="leftTitle"
				:status="defaultStatus"
				:need-label-count="true"
				@onTreeCheck="treeCheck"
			/>
		</ModelFrame>
		<div class="right-frame">
			<ModelFrame class="top-frame">
				<Query
					:queryArrList="queryArrList"
					class="ml10"
					@getQueryData="getQueryData"
				/>
			</ModelFrame>
			<ModelFrame class="whole-frame">
				<Title :title="rightTitle" />
				<PitayaTable
					ref="tableRef"
					:columns="tableProp"
					:table-data="tableData"
					:need-index="true"
					:need-pagination="true"
					:single-select="false"
					:need-selection="true"
					:total="pageTotal"
					@onSelectionChange="selectedTableList = $event"
					@on-current-page-change="onCurrentPageChange"
					@on-table-sort-change="handleSortChange"
					:table-loading="tableLoading"
				>
					<template #buyUnit="{ rowData }">
						<dict-tag
							:options="dictOptions.INVENTORY_UNIT"
							:value="rowData.buyUnit"
						/>
					</template>
					<template #attribute="{ rowData }">
						<dict-tag
							:options="DictApi.getMatAttr()"
							:value="rowData.attribute"
						/>
					</template>

					<!-- 审批状态 -->
					<template #operateStatus="{ rowData }">
						<dict-tag
							v-if="rowData.operateStatus > 0"
							:options="DictApi.getMatOperateTypeStatus()"
							:value="rowData.operateStatus"
						/>
						<span v-else>---</span>
					</template>
					<template #status="{ rowData }">
						<dict-tag
							:options="DictApi.getMatStatus()"
							:value="rowData.status"
						/>
					</template>
					<template #operations="{ rowData }">
						<slot v-if="isCheckPermission(powerList.matManualBtnPreview)">
							<el-button
								v-btn
								link
								@click.stop="handleRowView(rowData)"
								:disabled="checkPermission(powerList.matManualBtnPreview)"
								v-if="isCheckPermission(powerList.matManualBtnPreview)"
							>
								<font-awesome-icon :icon="['fas', 'eye']" />
								<span class="table-inner-btn">查看</span>
							</el-button>
						</slot>
						<slot v-else>---</slot>
					</template>
					<template #footerOperateLeft>
						<ButtonList
							class="btn-list"
							:is-not-radius="true"
							:button="tableFooterBtns"
							:loading="tbBtnLoading"
							@on-btn-click="handleChangeStatus"
						>
							<!-- TODO: 临时使用，等框架后续更新 -->
							<el-dropdown
								split-button
								type="primary"
								v-if="dropdownMenu"
								style="margin-left: 10px"
							>
								批量操作
								<template #dropdown>
									<el-dropdown-menu>
										<el-dropdown-item
											v-if="dropdownMenu['冻结']?.roles"
											:disabled="
												checkPermission(dropdownMenu['冻结']?.roles) ||
												dropdownMenu['冻结']?.disabled
											"
											@click="handleChangeStatus(dropdownMenu['冻结']?.name)"
										>
											冻结
										</el-dropdown-item>
										<el-dropdown-item
											v-if="dropdownMenu['解冻']?.roles"
											:disabled="
												checkPermission(dropdownMenu['解冻']?.roles) ||
												dropdownMenu['解冻']?.disabled
											"
											@click="handleChangeStatus(dropdownMenu['解冻']?.name)"
										>
											解冻
										</el-dropdown-item>
										<el-dropdown-item
											v-if="dropdownMenu['作废']?.roles"
											:disabled="
												checkPermission(dropdownMenu['作废']?.roles) ||
												dropdownMenu['作废']?.disabled
											"
											@click="handleChangeStatus(dropdownMenu['作废']?.name)"
										>
											作废
										</el-dropdown-item>
										<el-dropdown-item
											v-if="dropdownMenu['更新物资性质']?.roles"
											:disabled="
												checkPermission(dropdownMenu['更新物资性质']?.roles) ||
												dropdownMenu['更新物资性质']?.disabled
											"
											@click="
												handleChangeStatus(dropdownMenu['更新物资性质']?.name)
											"
										>
											更新物资性质
										</el-dropdown-item>
										<el-dropdown-item
											v-if="dropdownMenu['更新物资材质']?.roles"
											:disabled="
												checkPermission(dropdownMenu['更新物资材质']?.roles) ||
												dropdownMenu['更新物资材质']?.disabled
											"
											@click="
												handleChangeStatus(dropdownMenu['更新物资材质']?.name)
											"
										>
											更新物资材质
										</el-dropdown-item>
									</el-dropdown-menu>
								</template>
							</el-dropdown>
						</ButtonList>
					</template>
				</PitayaTable>

				<!-- 冻结、作废、解冻 -->
				<Drawer
					:size="modalSize.xl"
					v-model:drawer="matStatusEditorVisible"
					destroyOnClose
				>
					<mat-manual-status-editor
						:title="matStatusEditorTitle"
						:matCode="first(selectedTableList)?.code"
						:matId="first(selectedTableList)?.id"
						:operate-type="matOperateType"
						@close="matStatusEditorVisible = false"
						@update="fetchTableData"
					/>
				</Drawer>

				<!--维护采购信息-->
				<Drawer
					:size="modalSize.xxl"
					v-model:drawer="editorVisible"
					:destroyOnClose="true"
				>
					<mat-manual-editor
						:id="editorId"
						:mode="editorMode"
						@close="editorVisible = false"
						@update="fetchTableData"
					/>
				</Drawer>

				<!-- 更新物资性质/查看  -->
				<Drawer
					:size="modalSize.xl"
					v-model:drawer="changeAttrVisible"
					:destroyOnClose="true"
				>
					<mat-manual-attr-editor
						:sel-mat-list="selectedTableList"
						@close="changeAttrVisible = false"
						@update="fetchTableData"
					/>
				</Drawer>

				<!-- 更新物资材质 -->
				<Drawer
					:size="modalSize.sm"
					v-model:drawer="matManualQualityEditorVisible"
					:destroyOnClose="true"
				>
					<mat-manual-quality-editor
						:sel-mat-list="selectedTableList"
						@close="matManualQualityEditorVisible = false"
						@update="fetchTableData"
					/>
				</Drawer>

				<Drawer
					:size="modalSize.lg"
					v-model:drawer="matManualRecipientConfEditorVisible"
					:destroyOnClose="true"
				>
					<mat-manual-recipient-conf-editor
						:sel-mat-list="selectedTableList"
						@close="matManualRecipientConfEditorVisible = false"
						@update="fetchTableData"
					/>
				</Drawer>

				<Drawer
					:size="800"
					v-model:drawer="updatePriceEditorVisible"
					:destroyOnClose="true"
				>
					<matManualImportDrawer
						:fileTxtConf="fileTxtConf"
						:checkUrl="uploadValidateUrl"
						:saveApi="MatApplyApi.getExcelUpload"
						:downloadApi="downloadExcle"
						@onClose="updatePriceEditorVisible = false"
						@onUpdateList="fetchTableData"
					/>
				</Drawer>
			</ModelFrame>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
:deep(.el-button-group .el-button--primary:last-child) {
	margin-left: 0 !important;
	border-radius: 0 !important;
	outline: none !important;
}
:deep(.el-button-group .el-button--primary:first-child) {
	border-radius: 0 !important;
}
</style>
