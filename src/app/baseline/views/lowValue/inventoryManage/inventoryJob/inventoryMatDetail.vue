<script setup lang="ts">
import {
	MatLowValueCheckMaterialPageVo,
	MatLowValueCheckTaskPageVo
} from "@/app/baseline/utils/types/lowValue-inventory-manage"
import { useTbInit } from "../../../components/tableBase"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import pitayaTableLazy from "../../../components/PitayaTableLazy/index.vue"
import {
	listLowvalueCheckMaterialPaged,
	listLowvaluecheckAllocationList
} from "@/app/baseline/api/lowValue/inventoryJob"
import { getCheckResultStatus } from "./inventoryJob"
import { diffLabel, fontColorFromInventoryResultType } from "./inventoryJob"
import { toFixedTwo } from "@/app/baseline/utils"

const props = defineProps<{ id?: any; taskRow?: MatLowValueCheckTaskPageVo }>() // taskId

const emits = defineEmits<{
	(e: "close"): void
}>()

/**
 * title 配置
 */
const titleConf = computed(() => {
	return {
		name: ["盘点物资明细"],
		icon: ["fas", "square-share-nodes"]
	}
})

/**
 * 按钮 配置
 */
const formBtnList = [{ name: "取消", icon: ["fas", "circle-minus"] }]

const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange,
	onDataSelected
} = useTbInit<MatLowValueCheckMaterialPageVo, Record<string, any>>()

tableProp.value = [
	{ prop: "code", label: "物资编码", fixed: "left" },
	{ prop: "label", label: "物资名称" },
	{ prop: "materialTypeCode", label: "物资分类编码" },
	{ prop: "materialTypeLabel", label: "物资分类名称" },
	{ prop: "version", label: "规格型号" },
	{
		prop: "allocationCount",
		label: "应盘数量",
		needSlot: true,
		align: "right",
		fixed: "right"
	},
	{
		prop: "completedCount",
		label: "实盘数量",
		needSlot: true,
		align: "right",
		fixed: "right"
	},
	{ prop: "checkResult", label: "盘点结果", needSlot: true, fixed: "right" },
	{
		prop: "diffNum",
		label: "差异",
		needSlot: true,
		align: "right",
		fixed: "right"
	}
]

fetchFunc.value = listLowvalueCheckMaterialPaged

/**
 * 展开
 * @param row
 * @param expandedRows
 */
const expands = ref<any[]>([])
async function handleExpandChange(row: any, expandedRows: any) {
	if (expandedRows.length) {
		expands.value = []
		if (row) {
			try {
				expands.value.push(row.id)
				row.childLoading = true
				row.children = await listLowvaluecheckAllocationList(row.id)
			} finally {
				row.childLoading = false
			}
		}
	} else {
		expands.value = []
	}
}

onMounted(() => {
	fetchParam.value.sidx = "createdDate"
	fetchParam.value.sord = "desc"

	fetchParam.value.taskId = props.id
	fetchTableData()
})
</script>
<template>
	<div class="drawer-container">
		<div class="drawer-column" style="width: 100%">
			<div class="rows">
				<Title :title="titleConf" />
			</div>
			<div class="bottom">
				<el-scrollbar class="rows" style="height: 100%">
					<el-descriptions
						:column="4"
						size="small"
						border
						style="margin: 0 10px"
					>
						<el-descriptions-item>
							<template #label>
								<div class="cell-item">盘点类型</div>
							</template>
							{{ props.taskRow?.planType_view }}
						</el-descriptions-item>
						<el-descriptions-item>
							<template #label>
								<div class="cell-item">盘点范围</div>
							</template>
							{{ props.taskRow?.planScope_view }}
						</el-descriptions-item>
						<el-descriptions-item>
							<template #label>
								<div class="cell-item">盘点部门</div>
							</template>
							{{ props.taskRow?.checkOrgId_view }}
						</el-descriptions-item>
						<el-descriptions-item>
							<template #label>
								<div class="cell-item">盘点负责人</div>
							</template>
							{{ props.taskRow?.chargeUsername_view }}
						</el-descriptions-item>
						<el-descriptions-item>
							<template #label>
								<div class="cell-item">盘点人员</div>
							</template>
							{{ props.taskRow?.checkUserName_view }}
						</el-descriptions-item>
						<el-descriptions-item>
							<template #label>
								<div class="cell-item">监盘人员</div>
							</template>
							{{ props.taskRow?.superviseCheckUserName_view }}
						</el-descriptions-item>
						<el-descriptions-item>
							<template #label>
								<div class="cell-item">物资品类（项）</div>
							</template>
							{{ props.taskRow?.materialTypeNum }}
						</el-descriptions-item>
					</el-descriptions>
					<pitaya-table-lazy
						ref="tableRef"
						:columns="tableProp"
						:table-data="tableData"
						type="expand"
						:need-index="true"
						:single-select="false"
						:need-pagination="true"
						:total="pageTotal"
						:table-loading="tableLoading"
						:expand-row-keys="expands"
						@on-selection-change="onDataSelected"
						@on-current-page-change="
							($event) => {
								onCurrentPageChange($event)
								expands = []
							}
						"
						@on-expand-change="handleExpandChange"
					>
						<template #expandSlot="{ rowData }">
							<el-table
								border
								:data="rowData.children"
								v-loading="rowData.childLoading"
								style="width: auto; margin: 0 36px"
							>
								<el-table-column
									label="使用人"
									align="center"
									prop="allocationUsername_view"
								/>
								<el-table-column
									label="部门"
									align="center"
									prop="allocationSysOrgId_view"
								/>
								<el-table-column
									label="手机号"
									align="center"
									prop="allocationUserPhone"
								/>
								<el-table-column
									label="分配时间"
									align="center"
									min-width="100"
									prop="allocationDate"
								/>
								<el-table-column
									label="存放位置"
									align="center"
									min-width="100"
									prop="storeLocation"
								/>
								<el-table-column
									label="分配数量"
									align="center"
									prop="allocationNum"
								/>
								<el-table-column
									label="盘点数量"
									align="center"
									prop="checkNum"
								/>
								<el-table-column
									label="可用数量"
									align="center"
									prop="availableNum"
								/>
								<el-table-column
									label="不可用数量"
									align="center"
									prop="unavailableNum"
								/>
								<el-table-column
									label="情况说明"
									align="center"
									prop="remark"
								/>
							</el-table>
						</template>

						<!-- 实盘数量 -->
						<template #allocationCount="{ rowData }">
							{{ toFixedTwo(rowData.allocationCount) }}
						</template>

						<!-- 实盘数量 -->
						<template #completedCount="{ rowData }">
							{{ toFixedTwo(rowData.completedCount) }}
						</template>

						<!-- 盘点结果 -->
						<template #checkResult="{ rowData }">
							<dict-tag
								:options="getCheckResultStatus()"
								:value="rowData.checkResult"
							/>
						</template>

						<template #diffNum="{ rowData }">
							<span
								:style="fontColorFromInventoryResultType(rowData.checkResult)"
							>
								{{ diffLabel(toFixedTwo(rowData.diffNum) as any) }}
							</span>
						</template>
					</pitaya-table-lazy>
				</el-scrollbar>
			</div>

			<div class="row">
				<ButtonList
					class="footer"
					:button="formBtnList"
					@on-btn-click="emits('close')"
				/>
			</div>
		</div>
	</div>
</template>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.drawer-container {
	.bottom {
		display: flex;
		height: calc(100% - 80px);
	}
	.el-descriptions__label.el-descriptions__cell.is-bordered-label {
		background: #f6f6f6 !important;
	}
	:deep(.el-table__placeholder) {
		display: none !important;
	}
}
</style>
