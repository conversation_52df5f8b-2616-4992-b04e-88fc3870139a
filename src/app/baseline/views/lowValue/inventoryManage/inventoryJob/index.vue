<!-- 低值 - 低值盘点 - 盘点任务 -->
<script lang="ts" setup>
import {
	checkPermission,
	isCheckPermission
} from "@/app/platform/utils/permission"
import { powerList } from "../../../components/define.d"
import { computed, onMounted, ref } from "vue"
import { useTbInit } from "../../../components/tableBase"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import LinkTag from "@/app/baseline/views/components/linkTag.vue"
import { modalSize } from "@/app/baseline/utils/layout-config"
import {
	IIdempotentTokenType,
	IIdempotentTokenTypePre,
	IModalType
} from "../../../../utils/types/common"
import { BaseLineSysApi } from "@/app/baseline/api/system"
import { useDictInit } from "../../../components/dictBase"
import {
	ILowValueInventoryCheckJobStatus,
	MatLowValueCheckTaskPageVo,
	MatLowValueCheckTaskPageVoRequest
} from "@/app/baseline/utils/types/lowValue-inventory-manage"
import { progressColor } from "../../../store/inventoryManage/inventoryPlan/utils"
import { first, omit } from "lodash-es"
import { useUserStore } from "@/app/platform/store/modules/user"
import inventoryReport from "./inventoryReport.vue"
import {
	endCheckTask,
	getLowvalueCheckTaskBpmStatusCnt,
	issueCheckTask,
	listLowvalueCheckTaskPage,
	startCheckTask
} from "@/app/baseline/api/lowValue/inventoryJob"
import { getCheckResultStatus, getCheckJobStatus } from "./inventoryJob"
import inventoryUserEditor from "./inventoryUserEditor.vue"
import inventoryPlanDetail from "../inventoryPlan/inventoryPlanDetail.vue"
import inventoryJobEditor from "./inventoryJobEditor.vue"
import inventoryJobDetail from "./inventoryJobDetail.vue"
import { useMessageBoxInit } from "@/app/baseline/views/components/messageBox"
import { getIdempotentToken } from "@/app/baseline/utils/validate"

const { showWarnConfirm } = useMessageBoxInit()

const { dictOptions, getDictByCodeList } = useDictInit()
const { userInfo } = storeToRefs(useUserStore())

/**
 * 查询条件 配置
 */
const queryArrList = computed(() => {
	return [
		{
			name: "盘点任务编号",
			key: "taskCode",
			placeholder: "请输入盘点任务编号",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "盘点计划号",
			key: "planCode",
			placeholder: "请输入盘点计划号",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "盘点计划名称",
			key: "planLabel",
			placeholder: "请输入盘点计划名称",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "盘点负责人",
			key: "chargeRealname",
			placeholder: "请输入盘点负责人",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "盘点人",
			key: "checkRealname",
			placeholder: "请输入盘点负责人",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "监管人",
			key: "superviseRealname",
			placeholder: "请输入盘点负责人",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "盘点类型",
			key: "checkType",
			type: "select",
			placeholder: "请选择",
			children: dictOptions.value["LOW_VALUE_CHECK_TYPE"]
		},
		{
			name: "盘点范围",
			key: "checkScope",
			type: "select",
			placeholder: "请选择",
			children: dictOptions.value["LOW_VALUE_CHECK_RANGE"]
		},
		{
			name: "盘点部门",
			key: "sysOrgId",
			type: "treeSelect",
			placeholder: "请选择",
			treeApi: () =>
				BaseLineSysApi.getDepartmentTreeWithCompanyDisabled(
					userInfo.value.companyId
				)
		}
	]
})

/**
 * Title 配置
 */
const rightTitle = {
	name: ["盘点任务"],
	icon: ["fas", "square-share-nodes"]
}

/**
 * tab 配置项
 */
const statusCnt = ref<number[]>([])
const tabList = [
	{ name: "待开始", value: ILowValueInventoryCheckJobStatus.noStart },
	{ name: "进行中", value: ILowValueInventoryCheckJobStatus.startTask },
	{ name: "已完成", value: ILowValueInventoryCheckJobStatus.endTask }
]
const tabStatus = ref(tabList[0].value)
const activeName = ref(tabList[0].name)
const handleTabsClick = async (tab: any) => {
	activeName.value = tab.paneName
	tabStatus.value = tabList[tab.index].value as any
	getTableData()
}

const {
	tableData,
	tableProp,
	tableRef,
	tableLoading,
	currentPage,
	pageTotal,
	fetchParam,
	selectedTableList,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = useTbInit<MatLowValueCheckTaskPageVo, MatLowValueCheckTaskPageVoRequest>()

/**
 * table 列配置
 */
tableProp.value = [
	{ label: "盘点任务单号", prop: "code", width: 200, fixed: "left" },
	{ label: "盘点计划号", prop: "planCode", width: 200, needSlot: true },
	{ label: "盘点计划名称", prop: "planLabel", width: 200 },
	{ label: "盘点类型", prop: "checkType_view", width: 180 },
	{ label: "盘点范围", prop: "checkScope_view" },
	{ label: "盘点部门", prop: "checkOrgId_view" },
	{ label: "盘点负责人", prop: "chargeUsername_view", width: 120 },
	{ label: "盘点人", prop: "checkUserName_view" },
	{ label: "监盘人", prop: "superviseCheckUserName_view" },
	{ label: "物资品类（项）", prop: "typeNum", width: 120 },
	{ label: "盘点状态", prop: "status", needSlot: true, width: 100 },
	{ label: "盘点进度", prop: "processRate", needSlot: true, width: 150 },
	{ label: "盘点结果", prop: "checkResult", needSlot: true },
	{ label: "更新时间", prop: "lastModifiedDate", width: 150, sortable: true },
	{
		label: "操作",
		width: 200,
		prop: "operations",
		needSlot: true,
		fixed: "right"
	}
]
fetchFunc.value = listLowvalueCheckTaskPage

/**
 * tab 切换数据源
 */
const getTableData = (data?: any) => {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		sidx: "createdDate",
		sord: "desc",
		...fetchParam.value,
		status: tabStatus.value,
		...data
	}
	handleUpdate()
}

const curRowId = ref<any>("")
const curRowData = ref<MatLowValueCheckTaskPageVo>({})
const viewVisible = ref<boolean>(false)
const editorVisible = ref(false)
const editorMode = ref(IModalType.edit)

const tbBtnLoading = ref(false)

const tbBtnConf = computed(() => {
	const notSelected = selectedTableList.value?.length < 1
	const curUserName = userInfo.value.userName

	const {
		issueFlag,
		chargeUsername,
		checkUserName,
		superviseCheckUserName,
		reportStatus
	} = (first(selectedTableList.value) ?? {}) as MatLowValueCheckTaskPageVo
	const ls = [
		[
			{
				name: "指定盘点人员",
				roles: powerList.lowValueInventoryTaskBtnUser,
				icon: ["fas", "user"],
				disabled:
					notSelected || issueFlag == "1" || curUserName != chargeUsername // 待开始 && 当前用户 == 盘点负责人 可操作
			},
			{
				name: "任务下发",
				roles: powerList.lowValueInventoryTaskBtnRelease,
				icon: ["fas", "circle-check"],
				disabled:
					notSelected ||
					issueFlag == "1" ||
					curUserName != chargeUsername ||
					!checkUserName ||
					!superviseCheckUserName // 待开始 && 当前用户 == 盘点负责人 && 存在盘点人员 && 存在监盘人员
			},
			{
				name: "开始盘点",
				roles: powerList.lowValueInventoryTaskBtnStart,
				icon: ["fas", "circle-check"],
				disabled:
					notSelected || issueFlag != "1" || curUserName != checkUserName // 待开始 && 已下发任务 && 当用用户==盘点人员
			}
		],
		[
			{
				name: "结束盘点",
				roles: powerList.lowValueInventoryTaskBtnFinish,
				icon: ["fas", "circle-minus"],
				disabled: notSelected || curUserName != checkUserName // 进行中 && 当前用户 == 盘点人员
			}
		],
		[
			{
				name: "生成报告",
				roles: powerList.lowValueInventoryTaskBtnReport,
				icon: ["fas", "file-alt"],
				disabled:
					notSelected || curUserName != chargeUsername || reportStatus == "1" // 已完成 && 当前用户 == 盘点负责人
			}
		]
	]
	return ls[tabStatus.value] || []
})

async function handleTbBtnClick(name?: string) {
	const jobData = first(selectedTableList.value)
	tbBtnLoading.value = true

	try {
		switch (name) {
			case "指定盘点人员":
				handleSelectUser()
				break
			case "任务下发":
				await handleDistributeJob(jobData)
				break
			case "开始盘点":
				await handleStartJob(jobData)
				break
			case "结束盘点":
				await handleEndJob(jobData)
				break
			default:
				// 生成报告
				handleReport()
				break
		}
	} finally {
		tbBtnLoading.value = false
	}
}

/**
 * 指定盘点人员 操作
 */
const userCheckVisible = ref(false)
function handleSelectUser() {
	userCheckVisible.value = true
}

/**
 * 任务下发 操作
 * @param jobData
 */
async function handleDistributeJob(jobData?: MatLowValueCheckTaskPageVo) {
	await showWarnConfirm("请确认是否任务下发？")

	const idempotentToken = getIdempotentToken(
		IIdempotentTokenTypePre.other,
		IIdempotentTokenType.lowValueInventoryManageTask,
		jobData?.id
	)

	await issueCheckTask(jobData?.id as number, idempotentToken)
	ElMessage.success("操作成功")
	handleUpdate()
}

/**
 * 开始盘点
 * @param jobData
 */
async function handleStartJob(jobData?: MatLowValueCheckTaskPageVo) {
	await showWarnConfirm("请确认是否开始盘点？")

	const idempotentToken = getIdempotentToken(
		IIdempotentTokenTypePre.other,
		IIdempotentTokenType.lowValueInventoryManageTask,
		jobData?.id
	)

	await startCheckTask(jobData?.id as number, idempotentToken)
	ElMessage.success("操作成功")
	handleUpdate()
}

/**
 * 结束盘点
 * @param jobData
 */
async function handleEndJob(jobData?: MatLowValueCheckTaskPageVo) {
	await showWarnConfirm("请确认是否结束盘点？")

	const idempotentToken = getIdempotentToken(
		IIdempotentTokenTypePre.other,
		IIdempotentTokenType.lowValueInventoryManageTask,
		jobData?.id
	)

	await endCheckTask(jobData?.id as number, idempotentToken)
	ElMessage.success("操作成功")
	handleUpdate()
}

/**
 * 生成报告 操作
 * @param item
 */
const reportVisible = ref(false)
function handleReport() {
	reportVisible.value = true
}

/**
 * 是否可以 盘点
 * 1. 当前用户 == 盘点人员
 * 2. status = 1； 进行中
 * @param row
 */
function isCanCheckJob(row: MatLowValueCheckTaskPageVo) {
	return (
		row.checkUserName === userInfo.value.userName &&
		row.status == ILowValueInventoryCheckJobStatus.startTask
	)
}
/**
 * 盘点 操作
 * @param row
 */
const onRowEdit = (row: MatLowValueCheckTaskPageVo) => {
	curRowId.value = row.id
	curRowData.value = row
	editorVisible.value = true
	editorMode.value = IModalType.edit
}

/**
 * 查看
 * @param row
 */
const onRowView = (row: MatLowValueCheckTaskPageVo) => {
	curRowId.value = row.id
	curRowData.value = row
	viewVisible.value = true
	editorMode.value = IModalType.view
}

/**
 * 查看 关联盘点任务
 * @param row
 */
const checkPlanDetailVisible = ref(false)
function handleViewCheckPlanDetail(row: MatLowValueCheckTaskPageVo) {
	curRowData.value = row
	checkPlanDetailVisible.value = true
}
/**
 * 更新主表/statusCnt
 */
const handleUpdate = async () => {
	statusCnt.value = await getLowvalueCheckTaskBpmStatusCnt(
		omit(
			{
				...fetchParam.value
			},
			"sidx",
			"sord",
			"status",
			"currentPage",
			"pageSize"
		) as any
	)
	fetchTableData()
}
onMounted(() => {
	getDictByCodeList(["LOW_VALUE_CHECK_TYPE", "LOW_VALUE_CHECK_RANGE"])
	getTableData()
})

/**
 * 排序
 * @param column
 * @param prop 字段名称
 * @param order ascending 升序 descending 降序
 */
function handleSortChange(column: any, prop: any, order: any) {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		sord:
			order === "descending" ? "desc" : order === "ascending" ? "asc" : "desc",
		sidx: order ? prop : "createdDate" // 排序字段
	}

	fetchTableData()
}
</script>
<template>
	<div class="app-container">
		<div class="whole-frame">
			<ModelFrame class="top-frame">
				<Query
					:queryArrList="queryArrList"
					@getQueryData="getTableData"
					class="ml10"
				/>
			</ModelFrame>

			<ModelFrame class="bottom-frame">
				<Title :title="rightTitle">
					<div class="app-tabs-wrapper">
						<el-tabs v-model="activeName" @tab-click="handleTabsClick">
							<el-tab-pane
								v-for="(tab, index) in tabList"
								:key="index"
								:label="`${tab.name}（${statusCnt[index] ?? 0}）`"
								:name="tab.name"
								:index="tab.name"
							/>
						</el-tabs>
					</div>
				</Title>

				<pitaya-table
					ref="tableRef"
					:columns="tableProp"
					:tableData="tableData"
					:total="pageTotal"
					:need-selection="true"
					:single-select="true"
					:need-index="true"
					:need-pagination="true"
					@on-current-page-change="onCurrentPageChange"
					@on-selection-change="selectedTableList = $event"
					:table-loading="tableLoading"
					@on-table-sort-change="handleSortChange"
				>
					<!-- 盘点计划号 -->
					<template #planCode="{ rowData }">
						<link-tag
							:value="rowData.planCode"
							@click.stop="handleViewCheckPlanDetail(rowData)"
						/>
					</template>

					<!-- 盘点状态 -->
					<template #status="{ rowData }">
						<dict-tag :options="getCheckJobStatus()" :value="rowData.status" />
					</template>

					<!-- 盘点进度 -->
					<template #processRate="{ rowData }">
						<el-progress
							:show-text="false"
							:stroke-width="14"
							:percentage="rowData.processRate"
							:color="progressColor"
						/>
					</template>

					<!-- 盘点结果 -->
					<template #checkResult="{ rowData }">
						<dict-tag
							:options="getCheckResultStatus()"
							:value="rowData.checkResult"
						/>
					</template>

					<template #operations="{ rowData }">
						<slot
							v-if="
								(tabStatus == tabList[1].value &&
									isCheckPermission(powerList.lowValueInventoryTaskBtnEdit)) ||
								isCheckPermission(powerList.lowValueInventoryTaskBtnPreview)
							"
						>
							<el-button
								v-btn
								link
								@click.stop="onRowEdit(rowData)"
								:disabled="
									!isCanCheckJob(rowData) ||
									checkPermission(powerList.lowValueInventoryTaskBtnEdit)
								"
								v-if="
									tabStatus == tabList[1].value &&
									isCheckPermission(powerList.lowValueInventoryTaskBtnEdit)
								"
							>
								<font-awesome-icon :icon="['fas', 'pen-to-square']" />
								<span class="table-inner-btn">盘点</span>
							</el-button>
							<el-button
								v-btn
								link
								@click.stop="onRowView(rowData)"
								v-if="
									isCheckPermission(powerList.lowValueInventoryTaskBtnPreview)
								"
								:disabled="
									checkPermission(powerList.lowValueInventoryTaskBtnPreview)
								"
							>
								<font-awesome-icon :icon="['fas', 'eye']" />
								<span class="table-inner-btn">查看</span>
							</el-button>
						</slot>
						<slot v-else>---</slot>
					</template>
					<template #footerOperateLeft>
						<button-list
							v-if="tbBtnConf.length > 0"
							class="footer"
							:is-not-radius="true"
							:button="tbBtnConf"
							:loading="tbBtnLoading"
							@on-btn-click="handleTbBtnClick"
						/>
					</template>
				</pitaya-table>

				<!-- 盘点  -->
				<Drawer
					:size="modalSize.xl"
					v-model:drawer="editorVisible"
					:destroyOnClose="true"
				>
					<inventory-job-editor
						:id="curRowData.id"
						@close="editorVisible = false"
						@update="handleUpdate"
					/>
				</Drawer>

				<!-- 查看  -->
				<Drawer
					:size="modalSize.xl"
					v-model:drawer="viewVisible"
					:destroyOnClose="true"
				>
					<inventory-job-detail
						:id="curRowData.id"
						@close="viewVisible = false"
					/>
				</Drawer>

				<!-- 查看关联盘点计划详情  -->
				<Drawer
					:size="modalSize.xxl"
					v-model:drawer="checkPlanDetailVisible"
					:destroyOnClose="true"
				>
					<inventory-plan-detail
						:id="curRowData.checkPlanId"
						@close="checkPlanDetailVisible = false"
					/>
				</Drawer>

				<!-- 生成报告  -->
				<Drawer
					:size="modalSize.xl"
					v-model:drawer="reportVisible"
					:destroyOnClose="true"
				>
					<inventory-report
						:reportId="first(selectedTableList)?.reportId"
						:taskId="first(selectedTableList)?.id"
						:checkPlanId="first(selectedTableList)?.checkPlanId"
						:reportStatus="first(selectedTableList)?.reportStatus"
						@close="reportVisible = false"
						@update="handleUpdate"
					/>
				</Drawer>

				<!-- 指定盘点人员  -->
				<Drawer
					:size="modalSize.sm"
					v-model:drawer="userCheckVisible"
					:destroyOnClose="true"
				>
					<inventory-user-editor
						:check-task-row="(first(selectedTableList) as MatLowValueCheckTaskPageVo)"
						@close="userCheckVisible = false"
						@update="handleUpdate"
					/>
				</Drawer>
			</ModelFrame>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
