<!-- 盘点计划 盘点 盘点明细 -->
<script setup lang="ts">
import { computed, onMounted, ref } from "vue"
import { getNumDefByNumKey, toFixedTwo } from "@/app/baseline/utils"
import GridPanel from "../../../store/components/gridPanel.vue"
import { useTbInit } from "../../../components/tableBase"
import {
	MatLowValueCheckAllocationPageVo,
	MatLowValueCheckMaterialDetailVo
} from "@/app/baseline/utils/types/lowValue-inventory-manage"
import {
	listLowvalueCheckMaterialById,
	listLowvaluecheckAllocationPaged,
	updateLowvalueCheckAllocation
} from "@/app/baseline/api/lowValue/inventoryJob"
import { findIndex, round, toNumber } from "lodash-es"
import { validateAndCorrectInput } from "@/app/baseline/utils/validate"
import { useMessageBoxInit } from "../../../components/messageBox"

const props = defineProps<{
	id: any // 物资 id
}>()

const emits = defineEmits<{
	(e: "close"): void
	(e: "update"): void
}>()

const { showWarnConfirm } = useMessageBoxInit()

const drawerLoading = ref(false)

/**
 * 左侧title 配置
 */
const leftTitleConf = {
	name: ["物资信息"],
	icon: ["fas", "square-share-nodes"]
}

const drawerBtnRightConf = computed(() => [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "保存",
		icon: ["fas", "circle-check"],
		disabled: editedTableRowStack.value.length > 0 ? false : true
	}
])
const formBtnLoading = ref(false) // 表单按钮loading

/**
 * 右侧 title 配置
 */
const extraTitleConf = {
	name: ["盘点明细"],
	icon: ["fas", "square-share-nodes"]
}

/**
 * 左侧表单 配置
 */
const descData = ref<MatLowValueCheckMaterialDetailVo>({})
/**
 * 领用物资编码 & 领用物资数量
 */
const gridPanelOptions = computed(() => {
	return [
		{
			label: "待盘点",
			value: descData.value.waitingCheckNum ?? 0
		},
		{
			label: "已盘点",
			value: descData.value.completedCheckNum ?? 0
		}
	]
})

const descOptions = [
	{ label: "低值类型", key: "lowValueType_view" },
	{ label: "物资编码", key: "materialCode" },
	{ label: "物资名称", key: "materialLabel" },
	{ label: "物资分类编码", key: "materialTypeCode" },
	{ label: "物资分类名称", key: "materialTypeLabel" },
	{ label: "规格型号", key: "version" },
	{ label: "应盘数量", key: "checkNum" },
	{ label: "盘点人", key: "checkUserName_view" },
	{ label: "监盘人", key: "superviseCheckUserName_view" }
]

const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = useTbInit<MatLowValueCheckAllocationPageVo, Record<string, any>>()

tableProp.value = [
	{
		prop: "allocationUsername_view",
		label: "使用人",
		fixed: "left"
	},
	{ prop: "allocationSysOrgId_view", label: "部门" },
	{ prop: "allocationUserPhone", label: "手机号", width: 120 },
	{ prop: "allocationDate", label: "分配时间", width: 150 },
	{ prop: "storeLocation", label: "存放位置" },
	{
		prop: "allocationNum",
		label: "实际持有数量",
		needSlot: true,
		align: "right",
		width: 120
	},
	{
		prop: "checkNum",
		label: "盘点数量",
		needSlot: true,
		width: 120,
		fixed: "right"
	},
	{
		prop: "availableNum",
		label: "可用数量",
		needSlot: true,
		width: 120,
		fixed: "right"
	},
	{
		prop: "unavailableNum",
		label: "不可用数量",
		needSlot: true,
		width: 120,
		fixed: "right"
	},
	{
		prop: "remark",
		label: "情况说明",
		needSlot: true,
		width: 120,
		fixed: "right"
	}
]

fetchFunc.value = listLowvaluecheckAllocationPaged

/**
 * 编辑后的table row 数据
 */
const editedTableRowStack = ref<any[]>([])

function validateNum(e: any, type: string) {
	if (type == "checkNum") {
		// 较验盘点 是否大于分配数量
		const canGetNum = toNumber(e.allocationNum) ?? 0
		const num = toNumber(e.checkNum)

		if (num > canGetNum) {
			e.checkNum = canGetNum

			ElMessage.warning("盘点数量不能大于分配数量！")
		} else if (num > 0) {
			/* else if (num <= 0) {
			ElMessage.warning("盘点数量不能小于等于0！")
			return
		} */
			e.checkNum = num
		}

		e.availableNum = null
		e.unavailableNum = null
	} else if (type == "availableNum" || type == "unavailableNum") {
		// 可用数量/不可用数量
		// 可用数量+不可用数量 <= 盘点数量
		const canGetNum = toNumber(e.checkNum) ?? 0 // 盘点数量
		const availableNum = toNumber(e.availableNum) ?? 0 // 可用数量
		const unavailableNum = toNumber(e.unavailableNum) ?? 0 // 不可用数量

		e[type] = toNumber(e[type])
		if (type == "availableNum") {
			// 可用数量
			if (availableNum > canGetNum) {
				e[type] = canGetNum
				e.unavailableNum = 0
			} else if (availableNum < 0) {
				ElMessage.warning("可用数量不能小于0！")
				e.availableNum = 0
			} else if (
				availableNum >= 0 &&
				availableNum + unavailableNum != canGetNum
			) {
				e.unavailableNum = round(canGetNum - availableNum, 4)
			} else if (
				availableNum >= 0 &&
				availableNum + unavailableNum == canGetNum
			) {
				e.unavailableNum = round(canGetNum - availableNum, 4)
			}
		} else {
			// unavailableNum 不可用
			if (unavailableNum > canGetNum) {
				e[type] = canGetNum
				e.availableNum = 0
			} else if (unavailableNum < 0) {
				ElMessage.warning("不可用数量不能小于0！")
				e.unavailableNum = 0
			} else if (
				unavailableNum >= 0 &&
				availableNum + unavailableNum != canGetNum
			) {
				e.availableNum = round(canGetNum - unavailableNum, 4)
			} else if (
				unavailableNum >= 0 &&
				availableNum + unavailableNum == canGetNum
			) {
				e.availableNum = round(canGetNum - unavailableNum, 4)
			}
		}
	}

	handleInputBlur(e)
}

/**
 * 输入框失焦 handler
 *
 * 失焦后，记录编辑过的数据，用于保存任务数据
 */
function handleInputBlur(e: any) {
	if (!editedTableRowStack.value.length) {
		editedTableRowStack.value.push({
			...e,
			availableNum: Number(e.availableNum)
		})
		return
	}

	/**
	 * 队列存在数据，验证重复
	 *
	 * - 不重复->push
	 * - 重复->更新该条数据
	 */
	const rowIdx = findIndex(editedTableRowStack.value, (v) => v.id === e.id)
	if (rowIdx !== -1) {
		editedTableRowStack.value.splice(rowIdx, 1, { ...e })
		return
	}

	editedTableRowStack.value.push({ ...e })
}

/**
 * 翻页 回显已编辑的数据
 */
watch(
	() => tableData.value,
	() => {
		if (editedTableRowStack.value.length) {
			editedTableRowStack.value.map((e) => {
				const rowIdx = findIndex(tableData.value, (v) => v.id === e.id)
				if (rowIdx !== -1) {
					tableData.value.splice(rowIdx, 1, { ...e })
				}
			})
		}
	}
)

function handleFormBtnClick(btnName?: string) {
	if (btnName == "保存") {
		handleSave()
	} else {
		emits("close")
	}
}

async function handleSave() {
	try {
		// 盘点数量不能大于分配数量！
		const isValidateCheck = editedTableRowStack.value.every((v) => {
			return toNumber(v.checkNum) > toNumber(v.allocationNum)
		})

		if (isValidateCheck) {
			return ElMessage.warning("盘点数量不能大于分配数量！")
		}

		// 盘点数量不能小于等于0！
		/* const isValidateCheck0 = editedTableRowStack.value.every((v) => {
			return toNumber(v.checkNum) <= 0
		})

		if (isValidateCheck0) {
			return ElMessage.warning("盘点数量不能小于等于0！")
		} */

		// 盘点数量 = 可用数量 + 不可用数量！
		const isValidateEditorNum = editedTableRowStack.value.every((v) => {
			const availableNum = toNumber(v.availableNum) ?? 0 // 可用数量
			const unavailableNum = toNumber(v.unavailableNum) ?? 0 // 不可用数量
			const canGetNum = toNumber(v.checkNum) ?? 0 // 盘点数量

			return round(availableNum + unavailableNum, 4) != round(canGetNum, 4)
		})

		if (isValidateEditorNum) {
			return ElMessage.warning("盘点数量需等于 可用数量 + 不可用数量")
		}

		await showWarnConfirm("请确认是否保存本次数据？")

		formBtnLoading.value = true

		drawerLoading.value = true

		await updateLowvalueCheckAllocation({
			checkMaterialId: props.id,
			allocations: editedTableRowStack.value
		})

		ElMessage.success("操作成功")

		emits("update")
		emits("close")
		editedTableRowStack.value = []
	} finally {
		formBtnLoading.value = false
		drawerLoading.value = false
	}
}
/**
 * 获取详情数据
 */
async function getDetail() {
	drawerLoading.value = true
	try {
		const r = await listLowvalueCheckMaterialById(props.id)
		descData.value = { ...r }
	} finally {
		drawerLoading.value = false
	}
}

onMounted(() => {
	fetchParam.value.sidx = "createdDate"
	fetchParam.value.sord = "desc"

	if (props.id) {
		fetchParam.value.id = props.id

		getDetail()
		fetchTableData()
	}
})
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<div class="drawer-column left" style="width: 380px">
			<Title :title="leftTitleConf" />

			<el-scrollbar>
				<grid-panel :options="gridPanelOptions" />
				<!-- 物资信息 -->
				<el-descriptions size="small" :column="1" border class="content">
					<el-descriptions-item
						v-for="desc in descOptions"
						:key="desc.label"
						:label="desc.label"
					>
						<span>
							{{ getNumDefByNumKey(desc.key, descData[desc.key]) }}
						</span>
					</el-descriptions-item>
				</el-descriptions>
			</el-scrollbar>
		</div>
		<div class="drawer-column right" style="width: calc(100% - 380px)">
			<Title :title="extraTitleConf" />

			<el-scrollbar class="rows">
				<pitaya-table
					ref="tableRef"
					:columns="tableProp"
					:table-data="tableData"
					:need-selection="false"
					:multi-select="false"
					:need-index="true"
					:need-pagination="true"
					:total="pageTotal"
					:table-loading="tableLoading"
					@on-selection-change="selectedTableList = $event"
					@on-current-page-change="onCurrentPageChange"
				>
					<template #allocationNum="{ rowData }">
						{{ toFixedTwo(rowData.allocationNum) }}
					</template>
					<!-- 盘点数量 -->
					<template #checkNum="{ rowData }">
						<el-input
							class="no-arrows"
							v-model="rowData.checkNum"
							@click.stop
							@input="rowData.checkNum = validateAndCorrectInput($event)"
							@change="validateNum(rowData, 'checkNum')"
						/>
					</template>

					<!-- 可用数量 -->
					<template #availableNum="{ rowData }">
						<el-input
							class="no-arrows"
							v-model="rowData.availableNum"
							:disabled="!rowData.checkNum"
							@click.stop
							@input="rowData.availableNum = validateAndCorrectInput($event)"
							@change="validateNum(rowData, 'availableNum')"
						/>
					</template>

					<!-- 不可用数量 -->
					<template #unavailableNum="{ rowData }">
						<el-input
							class="no-arrows"
							v-model="rowData.unavailableNum"
							:disabled="!rowData.checkNum"
							@click.stop
							@input="rowData.unavailableNum = validateAndCorrectInput($event)"
							@change="validateNum(rowData, 'unavailableNum')"
						/>
					</template>

					<!-- 情况说明 -->
					<template #remark="{ rowData }">
						<el-input
							v-model="rowData.remark"
							type="input"
							@click.stop
							@change="handleInputBlur(rowData)"
						/>
					</template>
				</pitaya-table>
			</el-scrollbar>
			<button-list
				class="footer"
				:button="drawerBtnRightConf"
				:loading="formBtnLoading"
				@on-btn-click="handleFormBtnClick"
			/>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.content {
	.btn-group {
		width: 100%;
		.el-button--warning {
			background-color: #e6a23c !important;
		}
		.el-button--success {
			background-color: #67c23a !important;
		}
		.el-button {
			width: 50% !important;
		}
	}
}
</style>
