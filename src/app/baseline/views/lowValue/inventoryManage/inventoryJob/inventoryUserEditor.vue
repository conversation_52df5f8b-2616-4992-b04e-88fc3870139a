<!-- 盘点任务-指定盘点人员 -->
<template>
	<div class="drawer-container">
		<div class="drawer-column" style="width: 310px">
			<Title :title="titleConf" />

			<el-form
				class="content"
				:model="formData"
				:rules="formRules"
				ref="formRef"
				label-position="top"
				label-width="100px"
			>
				<form-element :form-element="formEls" :form-data="formData" />
			</el-form>

			<button-list
				class="footer"
				:button="btnConf"
				:loading="btnLoading"
				@on-btn-click="handleBtnClick"
			/>
		</div>

		<Drawer
			v-model:drawer="userSelectorVisible"
			:size="modalSize.md"
			destroy-on-close
		>
			<user-selector
				v-if="editingFormItemProp == 'checkRealName'"
				:sysOrgId="props.checkTaskRow.checkOrgId!"
				:sysOrgIdView="props.checkTaskRow.checkOrgId_view!"
				:table-fetch-params="{ parentOrgId: props.checkTaskRow.checkOrgId }"
				@close="userSelectorVisible = false"
				@save="handleSelectUser"
				:selected-ids="[formData.checkUserName]"
			/>
			<user-selector
				v-else
				:tableFetchParams="{
					filterParentOrgId: props.checkTaskRow.checkOrgId,
					sysCommunityId: userInfo.companyId
				}"
				@close="userSelectorVisible = false"
				@save="handleSelectUser"
				:selected-ids="[formData.superviseCheckUserName]"
			/>
		</Drawer>
	</div>
</template>

<script setup lang="ts">
import { requiredValidator } from "@/app/baseline/utils/validate"
import FormElement from "../../../components/formElement.vue"
import { FormElementType } from "../../../components/define"
import UserSelector from "../../../store/components/userSelector.vue"
import { modalSize } from "@/app/baseline/utils/layout-config"

import { first } from "lodash-es"
import { SystemUserVo } from "@/app/baseline/utils/types/system"
import { FormInstance } from "element-plus"
import {
	MatLowValueCheckTaskAssignUserDTO,
	MatLowValueCheckTaskPageVo
} from "@/app/baseline/utils/types/lowValue-inventory-manage"
import { useUserStore } from "@/app/platform/store/modules/user"
import { updateLowvalueCheckTaskAssignUser } from "@/app/baseline/api/lowValue/inventoryJob"

const props = defineProps<{
	/**
	 * 盘点任务主表数据
	 */
	checkTaskRow: MatLowValueCheckTaskPageVo
}>()

const emit = defineEmits<{
	(e: "close"): void

	/**
	 * 保存人员后
	 */
	(e: "update"): void
}>()

const { userInfo } = storeToRefs(useUserStore())

const titleConf = computed(() => ({
	name: ["指定盘点人员"],
	icon: ["fas", "square-share-nodes"]
}))

const userSelectorVisible = ref(false)

const formRef = ref<FormInstance>()

const formData = ref<Record<string, any>>({})

const formRules = {
	checkRealName: requiredValidator("盘点人员"),
	superviseCheckRealName: requiredValidator("监盘人员")
}

/**
 * 正在编辑中的表单项目 prop
 */
const editingFormItemProp = ref<"checkRealName" | "superviseCheckRealName">()

const formEls: FormElementType[][] = [
	[
		{
			label: "盘点人员",
			name: "checkRealName",
			type: "drawer",
			clickApi: () => {
				userSelectorVisible.value = true
				editingFormItemProp.value = "checkRealName"
			}
		},
		{
			label: "监盘人员",
			name: "superviseCheckRealName",
			type: "drawer",
			clickApi: () => {
				userSelectorVisible.value = true
				editingFormItemProp.value = "superviseCheckRealName"
			}
		}
	]
]

const btnLoading = ref(false)

const btnConf = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "保存",
		icon: ["fas", "floppy-disk"]
	}
]

onBeforeMount(() => {
	formData.value.checkRealName = props.checkTaskRow.checkUserName_view
	formData.value.checkUserName = props.checkTaskRow.checkUserName
	formData.value.superviseCheckRealName =
		props.checkTaskRow.superviseCheckUserName_view
	formData.value.superviseCheckUserName =
		props.checkTaskRow.superviseCheckUserName
})

onMounted(() => {
	formRef.value?.clearValidate()
})

function handleBtnClick(name?: string) {
	if (name === "取消") {
		emit("close")
		return
	}

	// 保存人员编辑
	if (!formRef.value) return

	formRef.value.validate(async (valid) => {
		if (!valid) return

		btnLoading.value = true

		try {
			await updateLowvalueCheckTaskAssignUser({
				id: props.checkTaskRow.id,
				...formData.value
			} as MatLowValueCheckTaskAssignUserDTO)

			ElMessage.success("操作成功")
			emit("close")
			emit("update")
		} finally {
			btnLoading.value = false
		}
	})
}

/**
 * 人员选择 handler
 */
function handleSelectUser(e?: SystemUserVo[]) {
	const { realname, username } = first(e) ?? {}

	if (editingFormItemProp.value === "checkRealName") {
		// 盘点人指定
		formData.value.checkUserName = username
		formData.value.checkRealName = realname
	} else {
		// 监盘人指定
		formData.value.superviseCheckUserName = username
		formData.value.superviseCheckRealName = realname
	}
	userSelectorVisible.value = false
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
