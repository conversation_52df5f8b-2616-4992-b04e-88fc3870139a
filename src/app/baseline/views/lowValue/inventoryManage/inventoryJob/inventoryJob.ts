import {
	ILowValueInventoryCheckJobStatus,
	ILowValueInventoryCheckStatus
} from "@/app/baseline/utils/types/lowValue-inventory-manage"
import { tagColorTheme } from "@/app/baseline/utils/colors"
import { StyleValue } from "vue"

/**
 * 根据盘点结果类型返回文本样式
 */
export function fontColorFromInventoryResultType(
	checkResult: ILowValueInventoryCheckStatus
) {
	const defStyle: StyleValue = {}

	switch (checkResult) {
		case ILowValueInventoryCheckStatus.default:
			// 正常
			return {
				...defStyle,
				color: tagColorTheme.success
			}

		case ILowValueInventoryCheckStatus.danger:
			// 异常
			return {
				...defStyle,
				color: tagColorTheme.danger
			}

		default:
			return defStyle
	}
}

/**
 * 差异 label
 */
export function diffLabel(num?: number | null) {
	if (num === null || num === undefined) {
		return "---"
	}

	return num > 0 ? `+${num}` : num
}

export function getCheckResultStatus() {
	return [
		{
			label: "正常",
			value: "0",
			raw: { class: "success" }
		},
		{
			label: "异常",
			value: "1",
			raw: { class: "danger" }
		}
	]
}

export function getCheckReportStatus() {
	return [
		{
			label: "已完成",
			value: "1",
			raw: { class: "success" }
		},
		{
			label: "待提交",
			value: "0",
			raw: { class: "primary" }
		}
	]
}

export function getCheckJobStatus() {
	return [
		{
			label: "待开始",
			value: ILowValueInventoryCheckJobStatus.noStart,
			raw: { class: "warning" }
		},
		{
			label: "进行中",
			value: ILowValueInventoryCheckJobStatus.startTask,
			raw: { class: "primary" }
		},
		{
			label: "已完成",
			value: ILowValueInventoryCheckJobStatus.endTask,
			raw: { class: "success" }
		}
	]
}
