<!-- 盘点计划 盘点 -->
<script setup lang="ts">
import { computed, onMounted, ref } from "vue"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import LinkTag from "@/app/baseline/views/components/linkTag.vue"
import {
	getNumDefByNumKey,
	tableColFilter,
	toFixedTwo
} from "@/app/baseline/utils"
import GridPanel from "../../../store/components/gridPanel.vue"
import { useTbInit } from "../../../components/tableBase"
import {
	ILowValueInventoryCheckJobStatus,
	ILowValueInventoryType,
	MatLowValueCheckMaterialPageVo,
	MatLowValueCheckTaskDetailVo
} from "@/app/baseline/utils/types/lowValue-inventory-manage"
import { useDictInit } from "../../../components/dictBase"
import { lowValueInventoryCheckTypeColorConf } from "@/app/baseline/utils/colors"
import ColorTag from "../../../store/components/colorTag.vue"
import {
	listLowvalueCheckMaterialPaged,
	listLowvalueCheckTaskDetail
} from "@/app/baseline/api/lowValue/inventoryJob"
import { getCheckResultStatus } from "./inventoryJob"
import { diffLabel, fontColorFromInventoryResultType } from "./inventoryJob"
import inventoryJobMatEditor from "./inventoryJobMatEditor.vue"
import { modalSize } from "@/app/baseline/utils/layout-config"

const { dictFilter, getDictByCodeList } = useDictInit()

const props = defineProps<{
	id: any // 任务 id
}>()

const emits = defineEmits<{
	(e: "close"): void
	(e: "update"): void
}>()

const drawerLoading = ref(false)

/**
 * 左侧title 配置
 */
const leftTitleConf = {
	name: ["盘点任务"],
	icon: ["fas", "square-share-nodes"]
}

const drawerBtnRightConf = computed(() => [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	}
])

/**
 * 右侧 title 配置
 */
const extraTitleConf = {
	name: ["盘点物资"],
	icon: ["fas", "square-share-nodes"]
}

/**
 * 左侧表单 配置
 */
const descData = ref<MatLowValueCheckTaskDetailVo>({})
/**
 * 领用单 & 物资品类 & 盘点总量
 */
const gridPanelOptions = computed(() => {
	return [
		{
			label: "领用单",
			value: descData.value.receiveNum ?? 0
		},
		{
			label: "物资品类",
			value: descData.value.materialTypeNum ?? 0
		},
		{
			label: "盘点总量",
			value: parseInt(descData.value.checkSumNum as any) || 0
		}
	]
})

const descOptions = [
	{ label: "盘点任务编号", key: "code" },
	{ label: "盘点计划号", key: "planCode" },
	{ label: "盘点计划名称", key: "planLabel" },
	{ label: "盘点类型", key: "planType" },
	{ label: "盘点范围", key: "planScope_view" },
	{ label: "盘点部门", key: "checkOrgId_view" },
	{ label: "盘点负责人", key: "chargeUsername_view" },
	{ label: "盘点人", key: "checkUserName_view" },
	{ label: "监盘人", key: "superviseCheckUserName_view" },
	{ label: "关联领用单", key: "receiveNum" },
	{ label: "物资品类（项）", key: "materialTypeNum" },
	{ label: "盘点物资总量", key: "materialCodeNum" },
	{ label: "盘点时间", key: "lastModifiedDate" }
]

const tbInit = useTbInit<MatLowValueCheckMaterialPageVo, Record<string, any>>()
const {
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = tbInit

tbInit.tableProp = computed(() => {
	const defTableColumns: TableColumnType[] = [
		{ prop: "code", label: "物资编码", fixed: "left" },
		{ prop: "label", label: "物资名称" },
		{ prop: "materialTypeCode", label: "物资分类编码" },
		{ prop: "materialTypeLabel", label: "物资分类名称" },
		{ prop: "version", label: "规格型号" },
		{
			prop: "allocationCount",
			label: "应盘数量",
			needSlot: true,
			align: "right",
			fixed: "right"
		},
		{
			prop: "completedCount",
			label: "实盘数量",
			needSlot: true,
			align: "right",
			fixed: "right"
		},
		{ prop: "checkResult", label: "盘点结果", needSlot: true, fixed: "right" },
		{
			prop: "diffNum",
			label: "差异",
			needSlot: true,
			fixed: "right",
			align: "right"
		}
	]

	if (descData.value.status == ILowValueInventoryCheckJobStatus.endTask) {
		return defTableColumns
	} else {
		return tableColFilter(defTableColumns, ["差异"])
	}
})

fetchFunc.value = listLowvalueCheckMaterialPaged

/**
 * 获取详情数据
 */
async function getDetail() {
	drawerLoading.value = true
	try {
		const r = await listLowvalueCheckTaskDetail(props.id)
		descData.value = { ...r }
	} finally {
		drawerLoading.value = false
	}
}

/**
 * 盘点物资明细 ->应盘数量
 * @param e
 */
const jobMatEditorVisible = ref(false)
const editJobMatRow = ref<MatLowValueCheckMaterialPageVo>({})
async function handleCheckMatItem(e: MatLowValueCheckMaterialPageVo) {
	editJobMatRow.value = e
	jobMatEditorVisible.value = true
}

/**
 * 更新 操作
 */
function handleJobUpdate() {
	fetchTableData()
	getDetail()
	emits("update")
}
onMounted(() => {
	getDictByCodeList(["LOW_VALUE_CHECK_TYPE"])

	fetchParam.value.sidx = "createdDate"
	fetchParam.value.sord = "desc"

	if (props.id) {
		fetchParam.value.taskId = props.id

		getDetail()
		fetchTableData()
	}
})
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<div class="drawer-column left" style="width: 380px">
			<Title :title="leftTitleConf" />

			<el-scrollbar>
				<grid-panel :options="gridPanelOptions" />
				<!-- 领用信息 -->
				<el-descriptions size="small" :column="1" border class="content">
					<el-descriptions-item
						v-for="desc in descOptions"
						:key="desc.label"
						:label="desc.label"
					>
						<!-- 盘点类型 -->
						<color-tag
							v-if="desc.key === 'planType'"
							border
							v-bind="lowValueInventoryCheckTypeColorConf[descData.planType as ILowValueInventoryType]"
						>
							{{
								dictFilter("LOW_VALUE_CHECK_TYPE", descData.planType as any)
									?.label
							}}
						</color-tag>
						<span v-else>
							{{ getNumDefByNumKey(desc.key, descData[desc.key]) }}
						</span>
					</el-descriptions-item>
				</el-descriptions>
			</el-scrollbar>
		</div>
		<div class="drawer-column right" style="width: calc(100% - 380px)">
			<Title :title="extraTitleConf" />

			<el-scrollbar class="rows">
				<pitaya-table
					ref="tableRef"
					:columns="(tbInit.tableProp as any)"
					:table-data="tableData"
					:need-selection="false"
					:multi-select="false"
					:need-index="true"
					:need-pagination="true"
					:total="pageTotal"
					:table-loading="tableLoading"
					@on-selection-change="selectedTableList = $event"
					@on-current-page-change="onCurrentPageChange"
				>
					<!-- 应盘数量 -->
					<template #allocationCount="{ rowData }">
						<link-tag
							:value="toFixedTwo(rowData.allocationCount)"
							@click="handleCheckMatItem(rowData)"
						/>
					</template>

					<!-- 实盘数量 -->
					<template #completedCount="{ rowData }">
						{{ toFixedTwo(rowData.completedCount) }}
					</template>

					<!-- 盘点结果 -->
					<template #checkResult="{ rowData }">
						<dict-tag
							:options="getCheckResultStatus()"
							:value="rowData.checkResult"
						/>
					</template>

					<template #diffNum="{ rowData }">
						<span :style="fontColorFromInventoryResultType(rowData.diffNum)">
							{{ diffLabel(toFixedTwo(rowData.diffNum) as any) }}
						</span>
					</template>
				</pitaya-table>
			</el-scrollbar>
			<button-list
				class="footer"
				:button="drawerBtnRightConf"
				@on-btn-click="emits('close')"
			/>
		</div>

		<!-- 应盘数量 下钻；盘点明细  -->
		<Drawer
			:size="modalSize.lg"
			v-model:drawer="jobMatEditorVisible"
			:destroyOnClose="true"
		>
			<inventory-job-mat-editor
				:id="editJobMatRow.id"
				@close="jobMatEditorVisible = false"
				@update="handleJobUpdate"
			/>
		</Drawer>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.content {
	.btn-group {
		width: 100%;
		.el-button--warning {
			background-color: #e6a23c !important;
		}
		.el-button--success {
			background-color: #67c23a !important;
		}
		.el-button {
			width: 50% !important;
		}
	}
}
</style>
