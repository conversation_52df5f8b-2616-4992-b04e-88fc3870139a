<script setup lang="ts">
import { FormInstance, FormRules } from "element-plus"
import ColorTag from "../../../store/components/colorTag.vue"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import { lowValueInventoryCheckStatusColorConf } from "@/app/baseline/utils/colors"
import {
	getIdempotentToken,
	requiredValidator
} from "@/app/baseline/utils/validate"
import {
	ILowValueInventoryCheckStatus,
	MatLowValueCheckReportDTO,
	MatLowValueCheckTaskDetailVo
} from "@/app/baseline/utils/types/lowValue-inventory-manage"
import { modalSize } from "@/app/baseline/utils/layout-config"
import inventoryMatDetail from "./inventoryMatDetail.vue"
import {
	addLowvalueCheckReport,
	listLowvalueCheckReportDetail,
	submitLowvalueCheckReport,
	updateLowvalueCheckReport
} from "@/app/baseline/api/lowValue/inventoryReport"
import { getCheckResultStatus } from "./inventoryJob"
import { listLowvalueCheckTaskDetail } from "@/app/baseline/api/lowValue/inventoryJob"
import { omit } from "lodash-es"
import { useMessageBoxInit } from "../../../components/messageBox"
import {
	IIdempotentTokenType,
	IIdempotentTokenTypePre
} from "@/app/baseline/utils/types/common"

const props = defineProps<{
	/**
	 * 报告Id
	 */
	reportId?: any

	/**
	 * 任务Id
	 */
	taskId?: any

	/**
	 * 盘点计划Id
	 */
	checkPlanId?: any

	/**
	 * 报告状态 0:未提交；1:已提交
	 */
	reportStatus?: any
}>()

const emits = defineEmits<{
	(e: "close"): void
	(e: "update"): void
}>()

const { showWarnConfirm } = useMessageBoxInit()

const drawerLoading = ref(false)
const formBtnLoading = ref(false) // 表单按钮loading

const taskInfo = ref<MatLowValueCheckTaskDetailVo>({})
/**
 * title 配置
 */
const titleConf = computed(() => {
	return {
		name: [`${props.reportId || formData.value.id ? "编辑" : "新建"}盘点报告`],
		icon: ["fas", "square-share-nodes"]
	}
})

/**
 * 按钮 配置
 */
const formBtnList = computed(() => {
	if (props.reportStatus == "1") {
		// 已提交
		return [{ name: "取消", icon: ["fas", "circle-minus"] }]
	} else {
		return [
			{ name: "取消", icon: ["fas", "circle-minus"] },
			{ name: "保存", icon: ["fas", "file-alt"] },
			{
				name: "确认提交",
				icon: ["fas", "circle-check"],
				disabled: !(props.reportId || formData.value.id)
			}
		]
	}
})

const formData = ref<Record<string, any>>({})
const formRefLeft = ref<FormInstance>()
const formRefRight = ref<FormInstance>()
const formRulesLeft = reactive<FormRules>({
	reason: requiredValidator("物资信息及原因不能为空"),
	disposalDes: requiredValidator("处置差异不能为空")
})
const formRulesRight = reactive<FormRules>({
	checkResult: requiredValidator("盘点情况总结")
})

function onFormBtnList(btnName?: string) {
	if (btnName === "保存") {
		handleSaveReport()
	} else if (btnName === "确认提交") {
		handleSubmitReport()
	} else {
		emits("close")
	}
}

/**
 * 保存 操作
 */
async function handleSaveReport() {
	if (!formRefLeft.value && !formRefRight.value) {
		return
	}
	Promise.all([formRefLeft.value?.validate(), formRefRight.value?.validate()])
		.then(async () => {
			formBtnLoading.value = true

			try {
				const api =
					props.reportId || formData.value.id
						? updateLowvalueCheckReport
						: addLowvalueCheckReport

				let idempotentToken = ""
				if (!(props.reportId || formData.value.id)) {
					idempotentToken = getIdempotentToken(
						IIdempotentTokenTypePre.apply,
						IIdempotentTokenType.lowValueInventoryManageTaskReport
					)
				}
				const r = await api(
					omit(
						{
							...formData.value,
							checkTaskId: props.taskId,
							checkPlanId: props.checkPlanId
						},
						"createdBy_view",
						"createdDate"
					) as MatLowValueCheckReportDTO,
					idempotentToken
				)
				formData.value.id = r.id

				ElMessage.success("操作成功")
				emits("update")
			} finally {
				formBtnLoading.value = false
			}
		})
		.catch(() => {
			console.log("Error Info")
		})
}

/**
 * 确认提交 操作
 */
async function handleSubmitReport() {
	await showWarnConfirm("请确认是否提交本次数据？")

	try {
		formBtnLoading.value = true
		drawerLoading.value = true

		const idempotentToken = getIdempotentToken(
			IIdempotentTokenTypePre.other,
			IIdempotentTokenType.lowValueInventoryManageTask,
			formData.value.id
		)

		await submitLowvalueCheckReport(formData.value.id, idempotentToken)
		emits("update")
		ElMessage.success("操作成功")
		emits("close")
	} finally {
		formBtnLoading.value = false
		drawerLoading.value = false
	}
}

/**
 * 查看物资明细
 */
const matVisible = ref(false)
function handleMatDetail() {
	matVisible.value = true
}

/**
 * 报告 详情
 */
async function getReportDetail() {
	drawerLoading.value = true
	try {
		const r = await listLowvalueCheckReportDetail(props.reportId)
		formData.value = { ...r }
	} finally {
		drawerLoading.value = false
	}
}

/**
 * 盘点任务 详情
 */
async function getTaskDetail() {
	drawerLoading.value = true
	try {
		const r = await listLowvalueCheckTaskDetail(props.taskId)
		taskInfo.value = { ...r }
	} finally {
		drawerLoading.value = false
	}
}
onMounted(() => {
	if (props.reportId) {
		getReportDetail()
	}

	if (props.taskId) {
		getTaskDetail()
	}
})
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<div class="drawer-column" style="width: 100%">
			<div class="rows">
				<Title :title="titleConf" />
			</div>
			<div class="bottom">
				<div class="left">
					<div>
						<font-awesome-icon
							:icon="['fas', 'exclamation-circle']"
							class="mr5"
							style="color: #e6a23c; font-size: 12px"
						/>
						<el-text class="mx-1" size="small" v-if="props.reportId">
							报告编号：{{ formData.code || "---" }}
						</el-text>
						<el-text class="mx-1" size="small">
							盘点任务编号：{{ taskInfo.code }}
						</el-text>
						<dict-tag
							class="ml10"
							style="display: inline-block"
							:options="getCheckResultStatus()"
							:value="taskInfo.checkResult!"
						/>
						<color-tag
							border
							class="ml10"
							v-bind="
								lowValueInventoryCheckStatusColorConf[
									ILowValueInventoryCheckStatus.default
								]
							"
							@click="handleMatDetail"
							style="cursor: pointer"
						>
							<font-awesome-icon
								:icon="['fas', 'file-contract']"
								class="mr5"
								style="color: #4bae89; font-size: 12px"
							/>
							物资品类 {{ taskInfo.materialTypeNum }}
						</color-tag>
					</div>

					<el-scrollbar class="row">
						<el-form
							:model="formData"
							:rules="formRulesLeft"
							label-position="top"
							label-width="420"
							class="form"
							ref="formRefLeft"
						>
							<el-form-item class="mt10" prop="reason">
								<el-input
									v-model.trim="formData.reason"
									type="textarea"
									rows="15"
									placeholder="请填写差异物资信息及原因"
									maxlength="200"
									show-word-limit
								/>
							</el-form-item>
							<el-form-item class="mt10" prop="advice">
								<el-input
									v-model.trim="formData.advice"
									type="textarea"
									rows="15"
									placeholder="请填写处置差异"
									maxlength="200"
									show-word-limit
								/>
							</el-form-item>
						</el-form>
					</el-scrollbar>
				</div>
				<div class="right">
					<div>
						<font-awesome-icon
							:icon="['fas', 'exclamation-circle']"
							class="mr5"
							style="color: #e6a23c; font-size: 12px"
						/>
						<el-text class="mx-1" size="small">盘点情况总结</el-text>
					</div>

					<el-scrollbar class="row">
						<el-form
							:model="formData"
							:rules="formRulesRight"
							label-position="top"
							label-width="420"
							class="form"
							ref="formRefRight"
						>
							<el-form-item class="mt10" prop="summary">
								<el-input
									v-model.trim="formData.summary"
									type="textarea"
									rows="30"
									placeholder="请填写盘点情况总结"
									maxlength="500"
									show-word-limit
								/>
							</el-form-item>
						</el-form>
					</el-scrollbar>
				</div>
			</div>
			<div class="row">
				<ButtonList
					class="footer"
					:loading="formBtnLoading"
					:button="formBtnList"
					@on-btn-click="onFormBtnList"
				/>
			</div>
		</div>

		<Drawer
			:size="modalSize.lg"
			v-model:drawer="matVisible"
			:destroyOnClose="true"
		>
			<inventory-mat-detail
				:id="props.taskId"
				:taskRow="taskInfo"
				@close="matVisible = false"
			/>
		</Drawer>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.drawer-container {
	.bottom {
		display: flex;
		height: calc(100% - 80px);
	}
	.left {
		width: 50%;
		height: calc(100% - 10px);
		.txt-color {
			color: #666;
			font-size: 12px;
		}
	}

	.right {
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		width: 50%;
	}
}
</style>
