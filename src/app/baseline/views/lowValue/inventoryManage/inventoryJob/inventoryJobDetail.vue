<!-- 盘点计划 查看 -->
<script setup lang="ts">
import { computed, onMounted, ref } from "vue"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import {
	batchFormatterNumView,
	getNumDefByNumKey,
	tableColFilter,
	toFixedTwo
} from "@/app/baseline/utils"
import GridPanel from "../../../store/components/gridPanel.vue"
import { useTbInit } from "../../../components/tableBase"
import {
	ILowValueInventoryCheckJobStatus,
	ILowValueInventoryType,
	MatLowValueCheckMaterialPageVo,
	MatLowValueCheckTaskDetailVo
} from "@/app/baseline/utils/types/lowValue-inventory-manage"
import { useDictInit } from "../../../components/dictBase"
import { lowValueInventoryCheckTypeColorConf } from "@/app/baseline/utils/colors"
import ColorTag from "../../../store/components/colorTag.vue"
import {
	listLowvalueCheckMaterialPaged,
	listLowvalueCheckTaskDetail,
	listLowvaluecheckAllocationList
} from "@/app/baseline/api/lowValue/inventoryJob"
import { getCheckResultStatus } from "./inventoryJob"
import { diffLabel, fontColorFromInventoryResultType } from "./inventoryJob"
import pitayaTableLazy from "../../../components/PitayaTableLazy/index.vue"

const { dictFilter, getDictByCodeList } = useDictInit()

const props = withDefaults(
	defineProps<{
		id?: any // id
		footerBtnVisible?: boolean
	}>(),
	{ footerBtnVisible: true }
)

const emits = defineEmits<{
	(e: "close"): void
}>()

const drawerLoading = ref(false)

/**
 * 左侧title 配置
 */
const leftTitleConf = {
	name: ["盘点任务"],
	icon: ["fas", "square-share-nodes"]
}

const drawerBtnRightConf = computed(() => [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	}
])

/**
 * 右侧 title 配置
 */
const extraTitleConf = {
	name: ["盘点物资"],
	icon: ["fas", "square-share-nodes"]
}

/**
 * 左侧表单 配置
 */
const descData = ref<MatLowValueCheckTaskDetailVo>({})
/**
 * 领用单 & 物资品类 & 盘点总量
 */
const gridPanelOptions = computed(() => {
	return [
		{
			label: "领用单",
			value: descData.value.receiveNum ?? 0
		},
		{
			label: "物资品类",
			value: descData.value.materialTypeNum ?? 0
		},
		{
			label: "盘点总量",
			value: parseInt(descData.value.checkSumNum) || 0
		}
	]
})

const descOptions = [
	{ label: "盘点任务编号", key: "code" },
	{ label: "盘点计划号", key: "planCode" },
	{ label: "盘点计划名称", key: "planLabel" },
	{ label: "盘点类型", key: "planType" },
	{ label: "盘点范围", key: "planScope_view" },
	{ label: "盘点部门", key: "checkOrgId_view" },
	{ label: "盘点负责人", key: "chargeUsername_view" },
	{ label: "盘点人", key: "checkUserName_view" },
	{ label: "关联领用单", key: "receiveNum" },
	{ label: "物资品类（项）", key: "materialTypeNum" },
	{ label: "盘点物资总量", key: "materialCodeNum" },
	{ label: "盘点时间", key: "lastModifiedDate" },
	{ label: "盘点结果", key: "checkResult" }
]

const tbInit = useTbInit<MatLowValueCheckMaterialPageVo, Record<string, any>>()
const {
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = tbInit

tbInit.tableProp = computed(() => {
	const defTableColumns: TableColumnType[] = [
		{ prop: "code", label: "物资编码", width: 130 },
		{ prop: "label", label: "物资名称" },
		{ prop: "materialTypeCode", label: "物资分类编码" },
		{ prop: "materialTypeLabel", label: "物资分类名称" },
		{ prop: "version", label: "规格型号" },
		{
			prop: "allocationCount",
			needSlot: true,
			align: "right",
			label: "应盘数量"
		},
		{
			prop: "completedCount",
			label: "实盘数量",
			needSlot: true,
			align: "right"
		},
		{ prop: "checkResult", label: "盘点结果", needSlot: true },
		{ prop: "diffNum", label: "差异", needSlot: true, align: "right" }
	]

	if (descData.value.status == ILowValueInventoryCheckJobStatus.endTask) {
		return defTableColumns
	} else {
		return tableColFilter(defTableColumns, ["差异"])
	}
})

fetchFunc.value = listLowvalueCheckMaterialPaged

/**
 * 展开
 * @param row
 * @param expandedRows
 */
const expands = ref<any[]>([])
async function handleExpandChange(row: any, expandedRows: any) {
	if (expandedRows.length) {
		expands.value = []
		if (row) {
			try {
				expands.value.push(row.id)
				row.childLoading = true
				row.children = await listLowvaluecheckAllocationList(row.id)
				batchFormatterNumView(row.children)
			} finally {
				row.childLoading = false
			}
		}
	} else {
		expands.value = []
	}
}

/**
 * 获取详情数据
 */
async function getDetail() {
	drawerLoading.value = true
	try {
		const r = await listLowvalueCheckTaskDetail(props.id)
		descData.value = { ...r }
	} finally {
		drawerLoading.value = false
	}
}

onMounted(() => {
	getDictByCodeList(["LOW_VALUE_CHECK_TYPE"])

	fetchParam.value.sidx = "createdDate"
	fetchParam.value.sord = "desc"

	if (props.id) {
		fetchParam.value.taskId = props.id

		getDetail()
		fetchTableData()
	}
})
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<div class="drawer-column left" style="width: 380px">
			<Title :title="leftTitleConf" />

			<el-scrollbar>
				<grid-panel :options="gridPanelOptions" />

				<!-- 盘点任务信息 -->
				<el-descriptions size="small" :column="1" border class="content">
					<el-descriptions-item
						v-for="desc in descOptions"
						:key="desc.label"
						:label="desc.label"
					>
						<!-- 盘点类型 -->
						<color-tag
							v-if="desc.key === 'planType'"
							border
							v-bind="lowValueInventoryCheckTypeColorConf[descData.planType as ILowValueInventoryType]"
						>
							{{
								dictFilter("LOW_VALUE_CHECK_TYPE", descData.planType as any)
									?.label
							}}
						</color-tag>
						<dict-tag
							v-else-if="desc.key === 'checkResult'"
							:options="getCheckResultStatus()"
							:value="(descData.checkResult as any)"
						/>
						<span v-else>
							{{ getNumDefByNumKey(desc.key, descData[desc.key]) }}
						</span>
					</el-descriptions-item>
				</el-descriptions>
			</el-scrollbar>
		</div>
		<div class="drawer-column right" style="width: calc(100% - 380px)">
			<Title :title="extraTitleConf" />

			<el-scrollbar class="rows">
				<pitaya-table-lazy
					ref="tableRef"
					type="expand"
					:columns="(tbInit.tableProp as any)"
					:table-data="tableData"
					:need-selection="false"
					:multi-select="false"
					:need-index="true"
					:need-pagination="true"
					:total="pageTotal"
					:table-loading="tableLoading"
					:expand-row-keys="expands"
					@on-selection-change="selectedTableList = $event"
					@on-current-page-change="
						($event) => {
							onCurrentPageChange($event)
							expands = []
						}
					"
					@on-expand-change="handleExpandChange"
				>
					<template #expandSlot="{ rowData }">
						<el-table
							border
							:data="rowData.children"
							v-loading="rowData.childLoading"
							style="width: auto; margin: 0 36px"
						>
							<el-table-column
								label="使用人"
								align="center"
								prop="allocationUsername_view"
							/>
							<el-table-column
								label="部门"
								align="center"
								prop="allocationSysOrgId_view"
							/>
							<el-table-column
								label="手机号"
								align="center"
								prop="allocationUserPhone"
							/>
							<el-table-column
								label="分配时间"
								align="center"
								min-width="150"
								prop="allocationDate"
							/>
							<el-table-column
								label="存放位置"
								align="center"
								min-width="100"
								prop="storeLocation"
							/>
							<el-table-column
								label="分配数量"
								align="right"
								prop="allocationNum_view"
							/>
							<el-table-column
								label="盘点数量"
								align="right"
								prop="checkNum_view"
							/>
							<el-table-column
								label="可用数量"
								align="right"
								prop="availableNum_view"
							/>
							<el-table-column
								label="不可用数量"
								align="right"
								prop="unavailableNum_view"
							/>
							<el-table-column label="情况说明" align="center" prop="remark" />
						</el-table>
					</template>

					<!-- 盘点结果 -->
					<template #checkResult="{ rowData }">
						<dict-tag
							:options="getCheckResultStatus()"
							:value="rowData.checkResult"
						/>
					</template>

					<template #allocationCount="{ rowData }">
						{{ toFixedTwo(rowData.allocationCount) }}
					</template>

					<template #completedCount="{ rowData }">
						{{ toFixedTwo(rowData.completedCount) }}
					</template>

					<template #diffNum="{ rowData }">
						<span
							:style="fontColorFromInventoryResultType(rowData.checkResult)"
						>
							{{ diffLabel(toFixedTwo(rowData.diffNum) as any) }}
						</span>
					</template>
				</pitaya-table-lazy>
			</el-scrollbar>
			<button-list
				v-if="props.footerBtnVisible"
				class="footer"
				:button="drawerBtnRightConf"
				@on-btn-click="emits('close')"
			/>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.drawer-container {
	:deep(.el-table__placeholder) {
		display: none !important;
	}
}
</style>
