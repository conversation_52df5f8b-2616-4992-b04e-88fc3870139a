<!-- 盘点计划 查看 -->
<script setup lang="ts">
import { computed, onMounted, ref } from "vue"
import { IModalType } from "../../../../utils/types/common"
import { getModalTypeLabel } from "@/app/baseline/utils"
import { getNumDefByNumKey } from "@/app/baseline/utils"
import GridPanel from "../../../store/components/gridPanel.vue"
import { useTbInit } from "../../../components/tableBase"
import {
	listLowvalueCheckPlanDetail,
	listLowvalueCheckPlanOrgPaged
} from "@/app/baseline/api/lowValue/inventoryPlan"
import {
	ILowValueInventoryType,
	MatLowValueCheckPlanDetailVo,
	MatLowValueCheckTaskPageVo
} from "@/app/baseline/utils/types/lowValue-inventory-manage"
import { useDictInit } from "../../../components/dictBase"
import useApplyTable from "./useApplyTable.vue"
import { lowValueInventoryCheckTypeColorConf } from "@/app/baseline/utils/colors"
import ColorTag from "../../../store/components/colorTag.vue"
import { getRealLength, setString } from "@/app/baseline/utils/validate"

const { dictFilter, getDictByCodeList } = useDictInit()

const props = withDefaults(
	defineProps<{
		id?: any // id
		mode?: IModalType // create || edit || create
	}>(),
	{ mode: IModalType.view, footerBtnVisible: true }
)

const emits = defineEmits<{
	(e: "close"): void
}>()

const drawerLoading = ref(false)

/**
 * 左侧title 配置
 */
const leftTitleConf = computed(() => ({
	name: [getModalTypeLabel(props.mode, "盘点计划")],
	icon: ["fas", "square-share-nodes"]
}))

const drawerBtnRightConf = computed(() => [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	}
])
const formBtnLoading = ref(false) // 表单按钮loading

/**
 * 右侧 title 配置
 */
const extraTitleConf = {
	name: ["相关信息"],
	icon: ["fas", "square-share-nodes"]
}
const tabsConf = ["盘点部门", "领用单"]
const activatedTab = ref(0)

/**
 * 左侧表单 配置
 */
const descData = ref<MatLowValueCheckPlanDetailVo>({})
/**
 * 领用物资编码 & 领用物资数量
 */
const gridPanelOptions = computed(() => {
	console.log(descData.value)
	return [
		{
			label: "领用单",
			value: descData.value.receiveNum ?? 0 // TODO
		},
		{
			label: "物资分类",
			value: descData.value.materialTypeNum ?? 0
		},
		{
			label: "物资编码",
			value: descData.value.materialCodeNum ?? 0 // TODO
		}
	]
})

const descOptions = [
	{ label: "盘点计划编码", key: "code" },
	{ label: "盘点计划名称", key: "label" },
	{ label: "盘点类型", key: "type" },
	{ label: "物资分类数量", key: "materialTypeNum" },
	{ label: "物资编码数量", key: "materialCodeNum" },
	{ label: "盘点开始时间", key: "beginDate" },
	{ label: "盘点结束时间", key: "endDate" },
	{ label: "盘点计划说明", key: "remark", needTooltip: true }
]

const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = useTbInit<MatLowValueCheckTaskPageVo, Record<string, any>>()

tableProp.value = [
	{ prop: "checkOrgId_view", label: "盘点部门" },
	{ prop: "chargeUsername_view", label: "盘点负责人" }
]

fetchFunc.value = listLowvalueCheckPlanOrgPaged

/**
 * 获取详情数据
 */
async function getDetail() {
	drawerLoading.value = true
	try {
		const r = await listLowvalueCheckPlanDetail(props.id)
		descData.value = { ...r }
	} finally {
		drawerLoading.value = false
	}
}

onMounted(async () => {
	getDictByCodeList(["LOW_VALUE_CHECK_TYPE", "LOW_VALUE_CHECK_RANGE"])

	fetchParam.value.sidx = "createdDate"
	fetchParam.value.sord = "desc"

	if (props.id) {
		fetchParam.value.planId = props.id

		await getDetail()
		await fetchTableData()
	}
})
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<div class="drawer-column left" style="width: 380px">
			<Title :title="leftTitleConf" />

			<el-scrollbar>
				<grid-panel :options="gridPanelOptions" />
				<!-- 领用信息 -->
				<el-descriptions size="small" :column="1" border class="content">
					<el-descriptions-item
						v-for="desc in descOptions"
						:key="desc.label"
						:label="desc.label"
					>
						<!-- 盘点类型 -->
						<color-tag
							v-if="desc.key === 'type'"
							border
							v-bind="lowValueInventoryCheckTypeColorConf[descData.type as ILowValueInventoryType]"
						>
							{{
								dictFilter("LOW_VALUE_CHECK_TYPE", descData.type as any)?.label
							}}
						</color-tag>
						<span v-else-if="desc.needTooltip">
							<el-tooltip
								effect="dark"
								:content="descData?.[desc.key]"
								:disabled="
									getRealLength(descData?.[desc.key]) <= 100 ? true : false
								"
							>
								{{
									getRealLength(descData?.[desc.key]) > 100
										? setString(descData?.[desc.key], 100)
										: descData?.[desc.key] || "---"
								}}
							</el-tooltip>
						</span>
						<span v-else>
							{{ getNumDefByNumKey(desc.key, descData[desc.key]) }}
						</span>
					</el-descriptions-item>
				</el-descriptions>
			</el-scrollbar>
		</div>
		<div
			class="drawer-column right"
			style="width: calc(100% - 380px)"
			:class="{ pdr10: !props.footerBtnVisible }"
		>
			<Title :title="extraTitleConf">
				<Tabs
					style="margin-right: auto; margin-left: 30px"
					:tabs="tabsConf"
					@on-tab-change="activatedTab = $event"
				/>
			</Title>
			<el-scrollbar class="rows">
				<pitaya-table
					v-if="activatedTab === 0"
					ref="tableRef"
					:columns="tableProp"
					:table-data="tableData"
					:need-selection="false"
					:multi-select="false"
					:need-index="true"
					:need-pagination="true"
					:total="pageTotal"
					:table-loading="tableLoading"
					@on-selection-change="selectedTableList = $event"
					@on-current-page-change="onCurrentPageChange"
				/>

				<use-apply-table v-else :id="props.id" />
			</el-scrollbar>
			<button-list
				v-if="props.footerBtnVisible"
				class="footer"
				:button="drawerBtnRightConf"
				:loading="formBtnLoading"
				@on-btn-click="emits('close')"
			/>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.content {
	.btn-group {
		width: 100%;
		.el-button--warning {
			background-color: #e6a23c !important;
		}
		.el-button--success {
			background-color: #67c23a !important;
		}
		.el-button {
			width: 50% !important;
		}
	}
}
</style>
