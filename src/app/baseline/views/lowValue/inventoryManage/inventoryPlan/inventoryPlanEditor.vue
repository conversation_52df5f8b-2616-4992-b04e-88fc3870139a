<!-- 盘点计划 编辑 -->
<script setup lang="ts">
import { computed, onMounted, ref } from "vue"
import {
	IIdempotentTokenType,
	IIdempotentTokenTypePre,
	IModalType
} from "../../../../utils/types/common"
import { ElMessage, FormInstance, FormItemRule } from "element-plus"
import { inputMaxLength, modalSize } from "@/app/baseline/utils/layout-config"
import { getModalTypeLabel } from "@/app/baseline/utils"
import { FormElementType } from "../../../components/define"
import formElement from "../../../components/formElement.vue"

import { useTbInit } from "../../../components/tableBase"
import { useMessageBoxInit } from "../../../components/messageBox"
import userSelector from "../../../store/components/userSelector.vue"
import {
	getIdempotentToken,
	requiredValidator
} from "@/app/baseline/utils/validate"
import {
	addLowvalueCheckPlan,
	listLowvalueCheckPlanDetail,
	updateLowvalueCheckPlan,
	listOrgTree,
	listSysOrgId,
	listLowvalueCheckPlanOrgPaged,
	delLowvalueCheckPlanOrgId,
	updateLowvalueCheckPlanOrgUsername,
	batchAddLowvalueCheckPlanOrgIds,
	submitCheckPlan
} from "@/app/baseline/api/lowValue/inventoryPlan"
import {
	MatLowValueCheckPlanAddDTO,
	MatLowValueCheckPlanDetailVo,
	MatLowValueCheckPlanEditDTO,
	MatLowValueCheckTaskPageVo
} from "@/app/baseline/utils/types/lowValue-inventory-manage"
import { useDictInit } from "../../../components/dictBase"
import { SystemUserVo } from "@/app/baseline/utils/types/system"
import useApplyTable from "./useApplyTable.vue"
import { findIndex, first, map, filter } from "lodash-es"
import { wrapDeptTreeData } from "@/app/baseline/api/system"
import { useUserStore } from "@/app/platform/store/modules/user"

const { showDelConfirm, showWarnConfirm } = useMessageBoxInit()
const { dictOptions, getDictByCodeList } = useDictInit()
const { userInfo } = storeToRefs(useUserStore())

const props = defineProps<{
	id?: any // id
	mode: IModalType // create || edit || create
}>()

const emits = defineEmits<{
	(e: "close"): void
	(e: "update"): void
}>()

/**
 * 保存旧的表单数据
 */
const oldFormData = ref<string>()

const drawerLoading = ref(false)

/**
 * 左侧title 配置
 */
const leftTitleConf = computed(() => ({
	name: [
		getModalTypeLabel(
			canEditExtra.value ? IModalType.edit : IModalType.create,
			"盘点计划"
		)
	],
	icon: ["fas", "square-share-nodes"]
}))

const leftBtnConf = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "保存草稿",
		icon: ["fas", "floppy-disk"]
	}
]
const drawerBtnRightConf = computed(() => [
	{
		name: "提交审核",
		icon: ["fas", "circle-check"],
		disabled: tableData.value.length < 1
	}
])
const formBtnLoading = ref(false) // 表单按钮loading

/**
 * 右侧 title 配置
 */
const extraTitleConf = {
	name: ["相关信息"],
	icon: ["fas", "square-share-nodes"]
}
const tabsConf = ["盘点部门", "领用单"]
const activatedTab = ref(0)

const handleTabChange = (index: number) => {
	activatedTab.value = index

	if (index === 0) {
		currentPage.value = 1
		pageSize.value = 20
		fetchTableData()
	}
}

const canEditExtra = computed(() => Boolean(formData.value?.id || props.id))

/**
 * 左侧表单 配置
 */
const formData = ref<MatLowValueCheckPlanDetailVo>({})
const formRef = ref<FormInstance>()
const formElBase = computed<FormElementType[][]>(() => [
	[
		{ label: "盘点计划名称", name: "label", maxlength: inputMaxLength.input },
		{
			label: "盘点类型",
			name: "type",
			type: "select",
			disabled: canEditExtra.value,
			data: dictOptions.value["LOW_VALUE_CHECK_TYPE"]
		},
		{
			label: "盘点范围",
			name: "scope",
			type: "select",
			disabled: canEditExtra.value,
			data: dictOptions.value["LOW_VALUE_CHECK_RANGE"]
		},
		{
			label: "盘点日期",
			name: "dateArray",
			type: "daterange",
			startPlaceholder: "开始日期",
			endPlaceholder: "结束日期",
			valueFormat: "YYYY-MM-DD",
			disabledDate: (time: Date) => {
				return time.getTime() < Date.now() - 8.64e7
			}
		},

		/* {
			label: "盘点开始日期",
			name: "beginDate",
			type: "date",
			disabledDate: (time: Date) => {
				const endDate = new Date(formData.value.endDate!).getTime()
				if (endDate) {
					return (
						time.getTime() < Date.now() - 8.64e7 || time.getTime() > endDate
					)
				} else {
					return time.getTime() < Date.now() - 8.64e7
				}
			},
			valueFormat: "YYYY-MM-DD"
		},
		{
			label: "盘点结束日期",
			name: "endDate",
			type: "date",
			valueFormat: "YYYY-MM-DD",
			disabledDate: (time: Date) => {
				const beginDate = new Date(formData.value.beginDate!).getTime()

				if (beginDate) {
					return time.getTime() < beginDate - 8.64e7
				} else {
					return time.getTime() < Date.now() - 8.64e7
				}
			}
		}, */
		{
			label: "盘点计划说明",
			name: "remark",
			type: "textarea",
			maxlength: inputMaxLength.textarea
		}
	]
])

// 左侧表单校验
const formRules: Record<string, FormItemRule> = {
	label: requiredValidator("盘点计划名称"),
	type: requiredValidator("盘点类型"),
	scope: requiredValidator("盘点范围"),
	dateArray: requiredValidator("盘点日期"),
	// endDate: requiredValidator("盘点结束日期"),
	remark: requiredValidator("盘点计划说明")
}

/**
 * 表单按钮 操作
 * @param btnName 保存草稿 | 取消
 */
const handleFormBtnClick = (btnName?: string) => {
	if (btnName == "取消") {
		emits("close")
	} else if (btnName == "保存草稿") {
		handleSaveDraft()
	} else if (btnName == "提交审核") {
		handleSubmit()
	}
}

/**
 * 保存草稿 handler
 */
async function handleSaveDraft() {
	if (!formRef.value) {
		return
	}

	formRef.value.validate(async (valid) => {
		if (!valid) {
			return
		}
		formBtnLoading.value = true
		// 表单校验通过
		try {
			const api = canEditExtra.value
				? updateLowvalueCheckPlan
				: addLowvalueCheckPlan

			formData.value.beginDate = formData.value.dateArray[0]
			formData.value.endDate = formData.value.dateArray[1]

			let idempotentToken = ""
			if (!canEditExtra.value) {
				idempotentToken = getIdempotentToken(
					IIdempotentTokenTypePre.apply,
					IIdempotentTokenType.lowValueInventoryManagePlan
				)
			}

			const r = await api(
				formData.value as
					| MatLowValueCheckPlanEditDTO
					| MatLowValueCheckPlanAddDTO,
				idempotentToken
			)
			formData.value.id = r.id
			fetchParam.value.planId = r.id

			oldFormData.value = JSON.stringify(formData.value)

			ElMessage.success("操作成功")

			getTableData()
			emits("update")
		} finally {
			formBtnLoading.value = false
		}
	})
}

/**
 * 提交 handler
 */
async function handleSubmit() {
	if (!formRef.value) {
		return
	}

	formRef.value.validate(async (valid) => {
		if (!valid) {
			return
		}
		await showWarnConfirm("请确认是否提交本次数据？")

		formBtnLoading.value = true
		drawerLoading.value = true

		try {
			// 如果主表有修改，则先更新主表数据
			if (oldFormData.value != JSON.stringify(formData.value)) {
				await updateLowvalueCheckPlan(
					formData.value as MatLowValueCheckPlanEditDTO
				)
			}

			const idempotentToken = getIdempotentToken(
				IIdempotentTokenTypePre.other,
				IIdempotentTokenType.lowValueInventoryManagePlan,
				formData.value.id
			)

			await submitCheckPlan(props.id || formData.value.id, idempotentToken)

			ElMessage.success("操作成功")

			emits("update")
			emits("close")
		} finally {
			formBtnLoading.value = false
			drawerLoading.value = false
		}
	})
}

const getTableData = () => {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value
	}
	fetchTableData()
}

const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	pageSize,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	currentPage,
	onCurrentPageChange
} = useTbInit<MatLowValueCheckTaskPageVo, Record<string, any>>()

tableProp.value = [
	{ prop: "checkOrgId_view", label: "盘点部门" },
	{ prop: "chargeUsername_view", label: "盘点负责人", needSlot: true },
	{ prop: "actions", label: "操作", width: 150, needSlot: true, fixed: "right" }
]

fetchFunc.value = listLowvalueCheckPlanOrgPaged

const orgVisible = ref(false)
const tbBtnConf = computed(() => [
	{
		name: "添加部门",
		icon: ["fas", "circle-plus"]
	},
	{
		name: "批量移除",
		icon: ["fas", "trash-can"],
		disabled: selectedTableList.value.length < 1
	}
])

/**
 * 添加部门  handle TODO
 * @param btnName
 */
const selectedOrgIds = ref()
async function handleTableBtnClick(btnName?: string) {
	if (btnName == "添加部门") {
		selectedOrgIds.value = map(
			(await listSysOrgId(props.id || formData.value.id)) ?? [],
			(v) => v
		).toString()

		orgVisible.value = true
	} else {
		// 批量删除
		const ids = map(selectedTableList.value, ({ id }) => id)
		await showDelConfirm()
		await delLowvalueCheckPlanOrgId(ids)
		ElMessage.success("操作成功")
		fetchTableData()
	}
}

/**
 * 获取部门列表树（公司禁止选择）
 */
async function getDepartmentTreeWithCompanyDisabled(): Promise<any> {
	const r = await listOrgTree({ selectedIds: selectedOrgIds.value })
	wrapDeptTreeData(r as any)
	return filter(r, (v: any) => v.companyId === userInfo.value.companyId)
}

/**
 * 保存部门
 * TODO selectIds类型应该是Record<string,any>，框架封装的DrawerTree 返回类型有误
 * @param selectIds
 * @param selectNodes
 */
async function handleSaveOrg(selectIds: string[] | undefined) {
	orgVisible.value = false

	const sysOrgId = map(selectIds as Record<string, any>, ({ id }) => id) || []

	if (sysOrgId && sysOrgId.length > 0) {
		const idempotentToken = getIdempotentToken(
			IIdempotentTokenTypePre.other,
			IIdempotentTokenType.lowValueInventoryManagePlan,
			formData.value.id
		)

		const { deleteIds } = await batchAddLowvalueCheckPlanOrgIds(
			{
				planId: props.id || formData.value.id,
				sysOrgId
			},
			idempotentToken
		)

		if (deleteIds) {
			ElMessage.warning("您已选择父级部门，页面数据已刷新！")
		} else {
			ElMessage.success("操作成功")
		}

		getTableData()
	}
}

/**
 * 选择盘点负责人
 */
const editOrgRow = ref()
const userVisible = ref(false)
function handleSelectUser(e: any) {
	editOrgRow.value = { ...e }
	userVisible.value = true
}

/**
 * 盘点负责人 回存 handler
 */
async function handleSaveUser(e?: SystemUserVo[], callback?: any) {
	const selectRow = first(e)
	if (!selectRow?.username) {
		callback()
		return ElMessage.warning("请选择盘点负责人")
	}

	editOrgRow.value.chargeUsername = selectRow.username
	editOrgRow.value.chargeUsername_view = selectRow.realname

	// 双向绑定 盘点负责人
	const rowIdx = findIndex(tableData.value, (v) => v.id === editOrgRow.value.id)
	if (rowIdx !== -1) {
		tableData.value.splice(rowIdx, 1, { ...editOrgRow.value })
	}
	await updateLowvalueCheckPlanOrgUsername({
		taskId: editOrgRow.value.id,
		chargeUsername: selectRow.username,
		chargeRealname: selectRow.realname
	})
	ElMessage.success("操作成功")
	userVisible.value = false
}

/**
 * 盘点部门 - 删除部门
 * @param e
 */
async function handleDelSysOrg(e: any) {
	await showDelConfirm()
	await delLowvalueCheckPlanOrgId([e.id])
	ElMessage.success("操作成功")
	fetchTableData()
}

/**
 * 获取详情数据
 */
async function getDetail() {
	drawerLoading.value = true
	try {
		const r = await listLowvalueCheckPlanDetail(props.id || formData.value.id)
		formData.value = {
			...r,
			dateArray: r.beginDate && r.endDate ? [r.beginDate, r.endDate] : []
		}

		oldFormData.value = JSON.stringify(formData.value)
	} finally {
		drawerLoading.value = false
	}
}

onMounted(async () => {
	getDictByCodeList(["LOW_VALUE_CHECK_TYPE", "LOW_VALUE_CHECK_RANGE"])

	fetchParam.value.sidx = "createdDate"
	fetchParam.value.sord = "desc"

	if (props.id) {
		fetchParam.value.planId = props.id

		await getDetail()
		await fetchTableData()
	}
})
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<div class="drawer-column left" style="width: 310px">
			<Title :title="leftTitleConf" />

			<!-- <el-scrollbar> -->
			<el-form
				class="content"
				:model="formData"
				ref="formRef"
				label-position="top"
				label-width="100px"
				:rules="formRules"
			>
				<form-element :form-element="formElBase" :form-data="formData" />
			</el-form>
			<!-- </el-scrollbar> -->
			<button-list
				class="footer"
				:button="leftBtnConf"
				:loading="formBtnLoading"
				@on-btn-click="handleFormBtnClick"
			/>
		</div>
		<div
			class="drawer-column right"
			:class="!canEditExtra ? 'disabled' : ''"
			style="width: calc(100% - 310px)"
		>
			<Title :title="extraTitleConf">
				<Tabs
					style="margin-right: auto; margin-left: 30px"
					:tabs="tabsConf"
					@on-tab-change="handleTabChange"
				/>
			</Title>
			<el-scrollbar class="rows">
				<pitaya-table
					v-if="activatedTab === 0"
					ref="tableRef"
					:columns="tableProp"
					:table-data="tableData"
					:need-selection="true"
					:multi-select="true"
					:need-index="true"
					:need-pagination="true"
					:total="pageTotal"
					:table-loading="tableLoading"
					@on-selection-change="selectedTableList = $event"
					@on-current-page-change="onCurrentPageChange"
				>
					<!-- 盘点负责人 -->
					<template #chargeUsername_view="{ rowData }">
						<el-input
							style="width: 200px"
							v-model.trim="rowData.chargeUsername_view"
							@click.stop="handleSelectUser(rowData)"
							readonly
						>
							<template #append>
								<font-awesome-icon
									:icon="['fas', 'layer-group']"
									style="color: #ccc"
									@click="handleSelectUser(rowData)"
								/>
							</template>
						</el-input>
					</template>

					<template #actions="{ rowData }">
						<el-button v-btn link @click.stop="handleDelSysOrg(rowData)">
							<font-awesome-icon
								:icon="['fas', 'trash-can']"
								style="color: var(--pitaya-btn-background)"
							/>
							<span class="table-inner-btn">移除</span>
						</el-button>
					</template>

					<template #footerOperateLeft>
						<button-list
							:button="tbBtnConf"
							:is-not-radius="true"
							@on-btn-click="handleTableBtnClick"
						/>
					</template>
				</pitaya-table>

				<use-apply-table v-else :id="props.id || formData.id" />
			</el-scrollbar>
			<button-list
				class="footer"
				:button="drawerBtnRightConf"
				:loading="formBtnLoading"
				@on-btn-click="handleFormBtnClick"
			/>
		</div>

		<!-- 盘点负责人 -->
		<Drawer v-model:drawer="userVisible" :size="modalSize.lg" destroy-on-close>
			<user-selector
				:sysOrgId="editOrgRow.checkOrgId!"
				:sysOrgIdView="editOrgRow.checkOrgId_view!"
				:table-fetch-params="{
					parentOrgId: editOrgRow.checkOrgId
					//addOrgAuthorityFalg: true // 加部门权限
				}"
				@save="handleSaveUser"
				@close="userVisible = false"
				:selected-ids="[editOrgRow.chargeUsername]"
			/>
		</Drawer>

		<!-- 盘点负责人 -->
		<Drawer
			class="inner-drawer drawer-hidden-box"
			:size="modalSize.sm"
			v-model:drawer="orgVisible"
			destroy-on-close
		>
			<Title
				:title="{
					name: ['选择部门'],
					icon: ['fas', 'square-share-nodes']
				}"
			/>
			<DrawerTree
				ref="drawerTree"
				:drawerState="orgVisible"
				@onBtnClick="handleSaveOrg"
				:multi-select="true"
				:apifunc="getDepartmentTreeWithCompanyDisabled"
				:checkStrictly="true"
				:needSingleSelect="false"
				:checkedKeys="[]"
				confirmBtnText="确定"
				cancelBtnText="取消"
			/>
		</Drawer>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.content {
	.btn-group {
		width: 100%;
		.el-button--warning {
			background-color: #e6a23c !important;
		}
		.el-button--success {
			background-color: #67c23a !important;
		}
		.el-button {
			width: 50% !important;
		}
	}
}
</style>
