<!-- 低值 - 低值盘点 - 盘点计划 -->
<script lang="ts" setup>
import {
	checkPermission,
	isCheckPermission
} from "@/app/platform/utils/permission"
import { powerList } from "../../../components/define.d"
import { computed, onMounted, ref } from "vue"
import { useMessageBoxInit } from "../../../components/messageBox"
import { useTbInit } from "../../../components/tableBase"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import ColorTag from "../../../store/components/colorTag.vue"
import { modalSize } from "@/app/baseline/utils/layout-config"
import { IModalType } from "../../../../utils/types/common"
import { DictApi } from "@/app/baseline/api/dict"

import { appStatus } from "@/app/baseline/api/dict"
import { useDictInit } from "../../../components/dictBase"
import { progressColor } from "../../../store/inventoryManage/inventoryPlan/utils"
import {
	listLowvalueCheckPlanPage,
	getLowvalueCheckPlanBpmStatusCnt,
	delLowvalueCheckPlan
} from "@/app/baseline/api/lowValue/inventoryPlan"
import {
	ILowValueInventoryType,
	MatLowValueCheckPlanPageVo,
	MatLowValueCheckPlanPageVoRequest
} from "@/app/baseline/utils/types/lowValue-inventory-manage"
import inventoryPlanEditor from "./inventoryPlanEditor.vue"
import inventoryPlanDetail from "./inventoryPlanDetail.vue"
import { lowValueInventoryCheckTypeColorConf } from "@/app/baseline/utils/colors"
import { tableColFilter, hasPermi } from "@/app/baseline/utils"
import { omit } from "lodash-es"
import ApprovedDrawer from "@/app/baseline/views/components/approvedDrawer.vue"
import { getTaskByBusinessIds } from "@/app/baseline/api/system"

const { showDelConfirm } = useMessageBoxInit()
const { dictOptions, dictFilter, getDictByCodeList } = useDictInit()

/**
 * 查询条件 配置
 */
const queryArrList = computed(() => {
	return [
		{
			name: "盘点计划号",
			key: "code",
			placeholder: "请输入盘点计划号",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "盘点计划名称",
			key: "label",
			placeholder: "请输入盘点计划名称",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "申请人",
			key: "createdBy",
			placeholder: "请输入申请人",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "盘点类型",
			key: "type",
			type: "select",
			placeholder: "请选择",
			children: dictOptions.value["LOW_VALUE_CHECK_TYPE"]
		},
		{
			name: "盘点范围",
			key: "scope",
			type: "select",
			placeholder: "请选择",
			children: dictOptions.value["LOW_VALUE_CHECK_RANGE"]
		}
	]
})

/**
 * Title 配置
 */
const rightTitle = {
	name: ["盘点计划"],
	icon: ["fas", "square-share-nodes"]
}
const titleBtnConf = [
	{
		name: "新建盘点计划",
		roles: powerList.lowValueInventoryPlanBtnCreate,
		icon: ["fas", "circle-plus"]
	}
]

/**
 * tab 配置项
 */
const statusCnt = ref<number[]>([])
const tabList = [
	{ name: "待开始", value: 0 },
	{ name: "进行中", value: 1 },
	{ name: "已完成", value: 2 }
]
const tabStatus = ref(tabList[0].value)
const activeName = ref(tabList[0].name)
const handleTabsClick = async (tab: any) => {
	activeName.value = tab.paneName
	tabStatus.value = tabList[tab.index].value as any
	getTableData()
}

const tbInit = useTbInit<
	MatLowValueCheckPlanPageVo,
	MatLowValueCheckPlanPageVoRequest
>()
const {
	tableData,
	tableRef,
	tableLoading,
	currentPage,
	pageTotal,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = tbInit

/**
 * table 列配置
 */
tbInit.tableProp = computed(() => {
	const defCols: TableColumnType[] = [
		{ label: "盘点计划号", prop: "code", width: 200 },
		{ label: "盘点计划名称", prop: "label" },
		{ label: "盘点类型", prop: "type", needSlot: true, width: 180 },
		{ label: "盘点范围", prop: "scope_view" },
		{ label: "盘点开始时间", prop: "beginDate" },
		{ label: "盘点结束时间", prop: "endDate" },
		{ label: "审批状态", prop: "bpmStatus", needSlot: true },
		{ label: "盘点进度", prop: "processRate", needSlot: true },
		{ label: "申请人", prop: "createdBy_view" },
		{ label: "创建时间", prop: "createdDate", width: 150, sortable: true },
		{
			label: "操作",
			width: 200,
			prop: "operations",
			needSlot: true,
			fixed: "right"
		}
	]

	switch (tabStatus.value) {
		case 0:
			// 待开始
			return tableColFilter(defCols, ["盘点进度"])

		default:
			// 已完成
			return defCols
	}
})

fetchFunc.value = listLowvalueCheckPlanPage

/**
 * tab 切换数据源
 */
const getTableData = (data?: any) => {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		sidx: "createdDate",
		sord: "desc",
		...fetchParam.value,
		status: tabStatus.value,
		...data
	}
	handleUpdate()
}

const curRowId = ref<any>("")
const curRowData = ref<MatLowValueCheckPlanPageVo>({})
const viewVisible = ref<boolean>(false)
const editorVisible = ref(false)
const editorMode = ref(IModalType.edit)
const approvedVisible = ref(false)
const editTask = ref<Record<string, any>>()

/**
 * 新建 操作
 */
const handleClickAddBtn = () => {
	editorMode.value = IModalType.create
	editorVisible.value = true
	curRowId.value = ""
	curRowData.value = {}
}

/**
 * 编辑 操作
 * @param row
 */
const onRowEdit = (row: MatLowValueCheckPlanPageVo) => {
	curRowId.value = row.id
	curRowData.value = row
	editorVisible.value = true
	editorMode.value = IModalType.edit
}

/**
 * 查看
 * @param row
 */
const onRowView = async (row: MatLowValueCheckPlanPageVo) => {
	curRowId.value = row.id
	curRowData.value = row
	/* viewVisible.value = true */
	editorMode.value = IModalType.view

	if (row.bpmStatus == appStatus.pendingApproval) {
		viewVisible.value = true
	} else {
		const res = await getTaskByBusinessIds({
			businessIds: [row?.id],
			camundaKey: "low_value_check"
		})

		if (Array.isArray(res) && res.length > 0) {
			editTask.value = { ...res[res.length - 1] }
			approvedVisible.value = true
		} else {
			viewVisible.value = true
		}
	}
}

/**
 * 删除 操作
 * @param row
 */
const onRowDel = async (row: MatLowValueCheckPlanPageVo) => {
	await showDelConfirm()
	await delLowvalueCheckPlan(row.id)
	ElMessage.success("操作成功")
	handleUpdate()
}

/**
 * 更新主表/statusCnt
 */
const handleUpdate = async () => {
	statusCnt.value = await getLowvalueCheckPlanBpmStatusCnt(
		omit(
			{
				...fetchParam.value
			},
			"sidx",
			"sord",
			"status",
			"currentPage",
			"pageSize"
		) as any
	)
	fetchTableData()
}
onMounted(() => {
	getDictByCodeList(["LOW_VALUE_CHECK_TYPE", "LOW_VALUE_CHECK_RANGE"])
	getTableData()
})
/**
 * 排序
 * @param column
 * @param prop 字段名称
 * @param order ascending 升序 descending 降序
 */
function handleSortChange(column: any, prop: any, order: any) {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		sord:
			order === "descending" ? "desc" : order === "ascending" ? "asc" : "desc",
		sidx: order ? prop : "createdDate" // 排序字段
	}

	fetchTableData()
}
</script>
<template>
	<div class="app-container">
		<div class="whole-frame">
			<ModelFrame class="top-frame">
				<Query
					:queryArrList="queryArrList"
					@getQueryData="getTableData"
					class="ml10"
				/>
			</ModelFrame>

			<ModelFrame class="bottom-frame">
				<Title
					:title="rightTitle"
					:button="(titleBtnConf as any)"
					@on-btn-click="handleClickAddBtn"
				>
					<div class="app-tabs-wrapper">
						<el-tabs v-model="activeName" @tab-click="handleTabsClick">
							<el-tab-pane
								v-for="(tab, index) in tabList"
								:key="index"
								:label="`${tab.name}（${statusCnt[index] ?? 0}）`"
								:name="tab.name"
								:index="tab.name"
							/>
						</el-tabs>
					</div>
				</Title>

				<pitaya-table
					ref="tableRef"
					:columns="(tbInit.tableProp as any)"
					:tableData="tableData"
					:total="pageTotal"
					:need-index="true"
					@on-current-page-change="onCurrentPageChange"
					:table-loading="tableLoading"
					@on-table-sort-change="handleSortChange"
				>
					<!-- 审批状态 -->
					<template #bpmStatus="{ rowData }">
						<dict-tag
							:options="DictApi.getBpmStatus()"
							:value="rowData.bpmStatus"
						/>
					</template>

					<template #type="{ rowData }">
						<!-- 盘点类型 -->
						<color-tag
							border
							v-bind="lowValueInventoryCheckTypeColorConf[rowData.type as ILowValueInventoryType]"
						>
							{{
								dictFilter("LOW_VALUE_CHECK_TYPE", rowData.type as any)?.label
							}}
						</color-tag>
					</template>

					<!-- 盘点进度 -->
					<template #processRate="{ rowData }">
						<el-progress
							:show-text="false"
							:stroke-width="14"
							:percentage="rowData.processRate"
							:color="progressColor"
						/>
					</template>

					<template #operations="{ rowData }">
						<slot
							v-if="
								(tabStatus == tabList[0].value &&
									(rowData.bpmStatus == appStatus.rejected ||
										rowData.bpmStatus == appStatus.pendingApproval) &&
									isCheckPermission(powerList.lowValueInventoryPlanBtnEdit) &&
									hasPermi(rowData.createdBy)) ||
								isCheckPermission(powerList.lowValueInventoryPlanBtnPreview) ||
								(tabStatus == tabList[0].value &&
									(rowData.bpmStatus == appStatus.rejected ||
										rowData.bpmStatus == appStatus.pendingApproval) &&
									isCheckPermission(powerList.lowValueInventoryPlanBtnDrop) &&
									hasPermi(rowData.createdBy))
							"
						>
							<el-button
								v-btn
								link
								@click="onRowEdit(rowData)"
								v-if="
									tabStatus == tabList[0].value &&
									(rowData.bpmStatus == appStatus.rejected ||
										rowData.bpmStatus == appStatus.pendingApproval) &&
									isCheckPermission(powerList.lowValueInventoryPlanBtnEdit) &&
									hasPermi(rowData.createdBy)
								"
								:disabled="
									checkPermission(powerList.lowValueInventoryPlanBtnEdit)
								"
							>
								<font-awesome-icon :icon="['fas', 'pen-to-square']" />
								<span class="table-inner-btn">编辑</span>
							</el-button>
							<el-button
								v-btn
								link
								@click="onRowView(rowData)"
								v-if="
									isCheckPermission(powerList.lowValueInventoryPlanBtnPreview)
								"
								:disabled="
									checkPermission(powerList.lowValueInventoryPlanBtnPreview)
								"
							>
								<font-awesome-icon :icon="['fas', 'eye']" />
								<span class="table-inner-btn">查看</span>
							</el-button>
							<el-button
								v-btn
								link
								@click="onRowDel(rowData)"
								v-if="
									tabStatus == tabList[0].value &&
									(rowData.bpmStatus == appStatus.rejected ||
										rowData.bpmStatus == appStatus.pendingApproval) &&
									isCheckPermission(powerList.lowValueInventoryPlanBtnDrop) &&
									hasPermi(rowData.createdBy)
								"
								:disabled="
									checkPermission(powerList.lowValueInventoryPlanBtnDrop)
								"
							>
								<font-awesome-icon :icon="['fas', 'trash-can']" />
								<span class="table-inner-btn">移除</span>
							</el-button>
						</slot>
						<slot v-else>---</slot>
					</template>
				</pitaya-table>

				<!-- 编辑/新建  -->
				<Drawer
					:size="modalSize.xxl"
					v-model:drawer="editorVisible"
					:destroyOnClose="true"
				>
					<inventory-plan-editor
						:id="curRowId"
						:mode="editorMode"
						@close="editorVisible = false"
						@update="handleUpdate"
					/>
				</Drawer>

				<!-- 查看  -->
				<Drawer
					:size="modalSize.xxl"
					v-model:drawer="viewVisible"
					:destroyOnClose="true"
				>
					<inventory-plan-detail
						:id="curRowId"
						:mode="editorMode"
						@close="viewVisible = false"
					/>
				</Drawer>

				<!-- 审批、查看弹窗 -->
				<Drawer
					:size="modalSize.xxl"
					v-model:drawer="approvedVisible"
					:destroyOnClose="true"
				>
					<approved-drawer
						:mod="editorMode"
						:task-value="editTask"
						@on-save-or-close="approvedVisible = false"
					/>
				</Drawer>
			</ModelFrame>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
