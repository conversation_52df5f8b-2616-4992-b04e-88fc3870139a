<!-- 领用单 table -->
<script setup lang="ts">
import { modalSize } from "@/app/baseline/utils/layout-config"
import { defineProps, onMounted } from "vue"
import { useTbInit } from "../../../components/tableBase"
import { BaseLineSysApi } from "@/app/baseline/api/system"
import { listLowvalueCheckPlanUseApplyPaged } from "@/app/baseline/api/lowValue/inventoryPlan"
import { MatLowValueApplyVO } from "@/app/baseline/utils/types/lowValue-requisition-apply"
import { toFixedTwo } from "@/app/baseline/utils"
import requisitionApplyDetail from "../../requisitionApply/requisitionApplyDetail.vue"
import { useUserStore } from "@/app/platform/store/modules/user"

export interface Props {
	id: string | number // 盘点计划Id
}

const { userInfo } = storeToRefs(useUserStore())

const props = defineProps<Props>()

/**
 * 查询条件 配置
 */
const queryArrList = [
	{
		name: "领用部门",
		key: "sysOrgId",
		type: "treeSelect",
		placeholder: "请选择",
		treeApi: () =>
			BaseLineSysApi.getDepartmentTreeWithCompanyDisabled(
				userInfo.value.companyId
			)
	}
]

const {
	tableData,
	tableLoading,
	tableProp,
	tableRef,
	currentPage,
	pageTotal,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = useTbInit<MatLowValueApplyVO, Record<string, any>>()

/**
 * table 列配置
 */
tableProp.value = [
	{ label: "领用部门", prop: "sysOrgId_view", width: 150, fixed: "left" },
	{ label: "领用单号", prop: "code", width: 180 },
	{ label: "领用业务名称", prop: "label", minWidth: 120 },
	{
		label: "领用物资编码",
		prop: "materialNum",
		minWidth: 120
	},
	{
		label: "领用物资数量",
		prop: "useOutNum",
		needSlot: true,
		align: "right",
		minWidth: 120
	},
	{ label: "领用人", prop: "createdBy_view", minWidth: 120 },
	{ label: "申请时间", prop: "createdDate", width: 150 },
	{
		label: "操作",
		prop: "actions",
		width: 150,
		needSlot: true,
		fixed: "right"
	}
]

/**
 * table 数据源
 * @param data
 */
const getTableData = (data?: any) => {
	if (props.id) {
		fetchFunc.value = listLowvalueCheckPlanUseApplyPaged

		currentPage.value = 1
		tableRef.value?.resetCurrentPage()

		fetchParam.value = {
			planId: props.id,
			sidx: "createdDate",
			sord: "desc",
			...data
		}
		fetchTableData()
	}
}

const useApplyDetailVisible = ref(false)
const editMatRow = ref()
function handleMatDetail(e?: any) {
	useApplyDetailVisible.value = true
	editMatRow.value = { ...e }
}

onMounted(() => {
	getTableData()
})
</script>
<template>
	<div class="tab-mat">
		<div class="tab-left">
			<Query
				:queryArrList="queryArrList"
				@getQueryData="getTableData"
				class="custom-q"
			/>
			<PitayaTable
				ref="tableRef"
				:columns="tableProp"
				:table-data="tableData"
				:need-index="true"
				:need-pagination="true"
				:single-select="false"
				:need-selection="false"
				:total="pageTotal"
				@on-current-page-change="onCurrentPageChange"
				:table-loading="tableLoading"
			>
				<template #useOutNum="{ rowData }">
					{{ toFixedTwo(rowData.useOutNum) }}
				</template>
				<template #actions="{ rowData }">
					<el-button v-btn link @click="handleMatDetail(rowData)">
						<font-awesome-icon
							:icon="['fas', 'eye']"
							style="color: var(--pitaya-btn-background)"
						/>
						<span class="table-inner-btn">查看物资明细</span>
					</el-button>
				</template>
			</PitayaTable>
		</div>

		<Drawer
			v-model:drawer="useApplyDetailVisible"
			:size="modalSize.xl"
			destroy-on-close
		>
			<!-- 领用申请单id -->
			<requisition-apply-detail
				:id="editMatRow.id"
				@close="useApplyDetailVisible = false"
			/>
		</Drawer>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.custom-q {
	margin: 10px 0px -10px 10px !important;
}

.el-input-number {
	width: 95%;
}

.tab-mat {
	display: flex;
	justify-content: space-between;
	flex-direction: row;
	height: 100%;
	width: 100%;

	.tab-left {
		width: calc(100% - 500px);
		flex: 1;
		height: 100%;
	}
}
</style>
