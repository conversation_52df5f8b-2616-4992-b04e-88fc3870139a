<!-- 低值 - 低值盘点 - 盘点报告 -->
<script lang="ts" setup>
import {
	checkPermission,
	isCheckPermission
} from "@/app/platform/utils/permission"
import { powerList } from "../../../components/define.d"
import { computed, onMounted, ref } from "vue"
import { useTbInit } from "../../../components/tableBase"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import LinkTag from "@/app/baseline/views/components/linkTag.vue"
import ColorTag from "../../../store/components/colorTag.vue"
import { modalSize } from "@/app/baseline/utils/layout-config"
import { IModalType } from "../../../../utils/types/common"
import { useDictInit } from "../../../components/dictBase"
import {
	ILowValueInventoryType,
	MatLowValueCheckReportPageVo
} from "@/app/baseline/utils/types/lowValue-inventory-manage"
import { lowValueInventoryCheckTypeColorConf } from "@/app/baseline/utils/colors"
import { listLowvalueCheckReportPaged } from "@/app/baseline/api/lowValue/inventoryReport"
import { getCheckReportStatus } from "../inventoryJob/inventoryJob"
import inventoryPlanDetail from "../inventoryPlan/inventoryPlanDetail.vue"
import { useUserStore } from "@/app/platform/store/modules/user"
import inventoryReport from "../inventoryJob/inventoryReport.vue"
import inventoryReportDetail from "./inventoryReportDetail.vue"

const { userInfo } = storeToRefs(useUserStore())
const { dictFilter, getDictByCodeList } = useDictInit()

/**
 * 查询条件 配置
 */
const queryArrList = computed(() => {
	return [
		{
			name: "报告编号",
			key: "code",
			placeholder: "请输入报告编号",
			enableFuzzy: true,
			type: "input"
		}
	]
})

/**
 * Title 配置
 */
const rightTitle = {
	name: ["盘点报告"],
	icon: ["fas", "square-share-nodes"]
}

const {
	tableData,
	tableProp,
	tableRef,
	tableLoading,
	currentPage,
	pageTotal,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = useTbInit<MatLowValueCheckReportPageVo, Record<string, any>>()

/**
 * table 列配置
 */
tableProp.value = [
	{ label: "报告编号", prop: "code", width: 180, fixed: "left" },
	{ label: "盘点任务编号", prop: "taskCode", width: 180, fixed: "left" },
	{ label: "盘点计划号", prop: "planCode", needSlot: true, width: 180 },
	{ label: "盘点类型", prop: "checkType", needSlot: true, width: 120 },
	{ label: "盘点范围", prop: "checkScope_view" },
	{ label: "盘点开始日期", prop: "beginDate", width: 150, sortable: true },
	{ label: "盘点结束日期", prop: "endDate", width: 150, sortable: true },
	{ label: "盘点负责人", prop: "chargeUsername_view", width: 120 },
	{ label: "盘点部门", prop: "checkOrgId_view" },
	{ label: "物资品类（项目）", prop: "materialTypeNum", width: 130 },
	{ label: "报告状态", prop: "status", needSlot: true, width: 120 },
	{ label: "编制人", prop: "createdBy_view", width: 120 },
	{ label: "更新时间", prop: "lastModifiedDate", width: 150, sortable: true },
	{
		label: "操作",
		width: 200,
		prop: "operations",
		needSlot: true,
		fixed: "right"
	}
]
fetchFunc.value = listLowvalueCheckReportPaged

const curRowId = ref<any>("")
const curRowData = ref<MatLowValueCheckReportPageVo>({})
const viewVisible = ref<boolean>(false)
const editorVisible = ref(false)
const editorMode = ref(IModalType.edit)

/**
 * tab 切换数据源
 */
const getTableData = (data?: any) => {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		sidx: "createdDate",
		sord: "desc",
		...fetchParam.value,
		...data
	}
	fetchTableData()
}
/**
 * 盘点计划 查看
 * @param row
 */
const checkPlanVisible = ref(false)
const handlePlanDetail = (row: MatLowValueCheckReportPageVo) => {
	curRowData.value = row
	checkPlanVisible.value = true
}

/**
 * 编辑 操作
 * @param row
 */
const onRowEdit = (row: MatLowValueCheckReportPageVo) => {
	curRowId.value = row.id
	curRowData.value = row
	editorVisible.value = true
	editorMode.value = IModalType.edit
}

/**
 * 查看
 * @param row
 */
const onRowView = (row: MatLowValueCheckReportPageVo) => {
	curRowId.value = row.id
	curRowData.value = row
	viewVisible.value = true
	editorMode.value = IModalType.view
}

const isCanEdit = (row: MatLowValueCheckReportPageVo) => {
	return row.chargeUsername === userInfo.value.userName
}

onMounted(() => {
	getDictByCodeList(["LOW_VALUE_CHECK_TYPE", "LOW_VALUE_CHECK_RANGE"])
	getTableData()
})

/**
 * 排序
 * @param column
 * @param prop 字段名称
 * @param order ascending 升序 descending 降序
 */
function handleSortChange(column: any, prop: any, order: any) {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		sord:
			order === "descending" ? "desc" : order === "ascending" ? "asc" : "desc",
		sidx: order
			? prop == "beginDate"
				? " checkPlan.beginDate"
				: prop == "endDate"
				? "checkPlan.endDate"
				: prop
			: "createdDate" // 排序字段
	}

	fetchTableData()
}
</script>
<template>
	<div class="app-container">
		<div class="whole-frame">
			<ModelFrame class="top-frame">
				<Query
					:queryArrList="queryArrList"
					@getQueryData="getTableData"
					class="ml10"
				/>
			</ModelFrame>

			<ModelFrame class="bottom-frame">
				<Title :title="rightTitle" />

				<pitaya-table
					ref="tableRef"
					:columns="tableProp"
					:tableData="tableData"
					:total="pageTotal"
					:need-index="true"
					@on-current-page-change="onCurrentPageChange"
					:table-loading="tableLoading"
					@on-table-sort-change="handleSortChange"
				>
					<!-- 盘点计划 -->
					<template #planCode="{ rowData }">
						<link-tag
							:value="rowData.planCode"
							@click="handlePlanDetail(rowData)"
						/>
					</template>

					<!-- 报告状态 -->
					<template #status="{ rowData }">
						<dict-tag
							:options="getCheckReportStatus()"
							:value="rowData.status"
						/>
					</template>

					<template #checkType="{ rowData }">
						<!-- 盘点类型 -->
						<color-tag
							border
							v-bind="lowValueInventoryCheckTypeColorConf[rowData.checkType as ILowValueInventoryType]"
						>
							{{
								dictFilter("LOW_VALUE_CHECK_TYPE", rowData.checkType as any)
									?.label
							}}
						</color-tag>
					</template>

					<template #operations="{ rowData }">
						<slot
							v-if="
								(rowData.status == '0' &&
									isCheckPermission(
										powerList.lowValueInventoryReportBtnEdit
									)) ||
								isCheckPermission(powerList.lowValueInventoryReportBtnPreview)
							"
						>
							<el-button
								v-btn
								link
								@click="onRowEdit(rowData)"
								v-if="
									rowData.status == '0' &&
									isCheckPermission(powerList.lowValueInventoryReportBtnEdit)
								"
								:disabled="
									!isCanEdit(rowData) ||
									checkPermission(powerList.lowValueInventoryReportBtnEdit)
								"
							>
								<font-awesome-icon :icon="['fas', 'pen-to-square']" />
								<span class="table-inner-btn">编辑</span>
							</el-button>
							<el-button
								v-btn
								link
								@click="onRowView(rowData)"
								v-if="
									isCheckPermission(powerList.lowValueInventoryReportBtnPreview)
								"
								:disabled="
									checkPermission(powerList.lowValueInventoryReportBtnPreview)
								"
							>
								<font-awesome-icon :icon="['fas', 'eye']" />
								<span class="table-inner-btn">查看</span>
							</el-button>
						</slot>
						<slot v-else>---</slot>
					</template>
				</pitaya-table>

				<!-- 编辑/新建  -->
				<Drawer
					:size="modalSize.xl"
					v-model:drawer="editorVisible"
					:destroyOnClose="true"
				>
					<inventory-report
						:reportId="curRowData?.id"
						:taskId="curRowData.checkTaskId"
						:check-plan-id="curRowData.planId"
						:reportStatus="curRowData.status"
						@close="editorVisible = false"
						@update="fetchTableData"
					/>
				</Drawer>

				<!-- 查看  -->
				<Drawer
					:size="modalSize.xl"
					v-model:drawer="viewVisible"
					:destroyOnClose="true"
				>
					<inventory-report-detail
						:reportId="curRowData?.id"
						:taskId="curRowData.checkTaskId"
						:reportStatus="curRowData.status"
						@close="viewVisible = false"
					/>
				</Drawer>

				<!-- 查看关联盘点计划详情  -->
				<Drawer
					:size="modalSize.xxl"
					v-model:drawer="checkPlanVisible"
					:destroyOnClose="true"
				>
					<inventory-plan-detail
						:id="curRowData.planId"
						@close="checkPlanVisible = false"
					/>
				</Drawer>
			</ModelFrame>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
