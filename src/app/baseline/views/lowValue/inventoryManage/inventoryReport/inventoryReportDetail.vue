<script setup lang="ts">
import { FormInstance } from "element-plus"
import ColorTag from "../../../store/components/colorTag.vue"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import { lowValueInventoryCheckStatusColorConf } from "@/app/baseline/utils/colors"
import {
	ILowValueInventoryCheckStatus,
	MatLowValueCheckTaskDetailVo
} from "@/app/baseline/utils/types/lowValue-inventory-manage"
import { modalSize } from "@/app/baseline/utils/layout-config"
import inventoryMatDetail from "../inventoryJob/inventoryMatDetail.vue"
import { listLowvalueCheckReportDetail } from "@/app/baseline/api/lowValue/inventoryReport"
import { getCheckResultStatus } from "../inventoryJob/inventoryJob"
import { listLowvalueCheckTaskDetail } from "@/app/baseline/api/lowValue/inventoryJob"

const props = defineProps<{
	/**
	 * 报告Id
	 */
	reportId?: any

	/**
	 * 任务Id
	 */
	taskId?: any

	/**
	 * 报告状态 0:未提交；1:已提交
	 */
	reportStatus?: any
}>()

const emits = defineEmits<{
	(e: "close"): void
	(e: "update"): void
}>()

const drawerLoading = ref(false)
const formBtnLoading = ref(false) // 表单按钮loading

const taskInfo = ref<MatLowValueCheckTaskDetailVo>({})
/**
 * title 配置
 */
const titleConf = computed(() => {
	return {
		name: ["查看盘点报告"],
		icon: ["fas", "square-share-nodes"]
	}
})

/**
 * 按钮 配置
 */
const formBtnList = [{ name: "取消", icon: ["fas", "circle-minus"] }]

const formData = ref<Record<string, any>>({})
const formRefLeft = ref<FormInstance>()
const formRefRight = ref<FormInstance>()

/**
 * 查看物资明细
 */
const matVisible = ref(false)
function handleMatDetail() {
	matVisible.value = true
}

/**
 * 报告 详情
 */
async function getReportDetail() {
	drawerLoading.value = true
	try {
		const r = await listLowvalueCheckReportDetail(props.reportId)
		formData.value = { ...r }
	} finally {
		drawerLoading.value = false
	}
}

/**
 * 盘点任务 详情
 */
async function getTaskDetail() {
	drawerLoading.value = true
	try {
		const r = await listLowvalueCheckTaskDetail(props.taskId)
		taskInfo.value = { ...r }
	} finally {
		drawerLoading.value = false
	}
}
onMounted(() => {
	if (props.reportId) {
		getReportDetail()
	}

	if (props.taskId) {
		getTaskDetail()
	}
})
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<div class="drawer-column" style="width: 100%">
			<div class="rows">
				<Title :title="titleConf" />
			</div>
			<div class="bottom">
				<div class="left">
					<div>
						<font-awesome-icon
							:icon="['fas', 'exclamation-circle']"
							class="mr5"
							style="color: #e6a23c; font-size: 12px"
						/>
						<el-text class="mx-1" size="small" v-if="props.reportId">
							报告编号：{{ formData.code || "---" }}
						</el-text>
						<el-text class="mx-1" size="small">
							盘点任务编号：{{ taskInfo.code }}
						</el-text>
						<dict-tag
							class="ml10"
							style="display: inline-block"
							:options="getCheckResultStatus()"
							:value="taskInfo.checkResult!"
						/>
						<color-tag
							border
							class="ml10"
							v-bind="
								lowValueInventoryCheckStatusColorConf[
									ILowValueInventoryCheckStatus.default
								]
							"
							@click="handleMatDetail"
							style="cursor: pointer"
						>
							<font-awesome-icon
								:icon="['fas', 'file-contract']"
								class="mr5"
								style="color: #4bae89; font-size: 12px"
							/>
							物资品类 {{ taskInfo.materialTypeNum }}
						</color-tag>
					</div>

					<el-scrollbar class="row">
						<el-form
							:model="formData"
							label-position="top"
							label-width="420"
							class="form"
							ref="formRefLeft"
						>
							<el-form-item class="mt10" prop="reason">
								<el-input
									v-model.trim="formData.reason"
									type="textarea"
									rows="15"
									placeholder="请填写差异物资信息及原因"
									maxlength="200"
									show-word-limit
									:disabled="true"
								/>
							</el-form-item>
							<el-form-item class="mt10" prop="advice">
								<el-input
									v-model.trim="formData.advice"
									type="textarea"
									rows="15"
									placeholder="请填写处置差异"
									maxlength="200"
									show-word-limit
									:disabled="true"
								/>
							</el-form-item>
						</el-form>
					</el-scrollbar>
				</div>
				<div class="right">
					<div>
						<font-awesome-icon
							:icon="['fas', 'exclamation-circle']"
							class="mr5"
							style="color: #e6a23c; font-size: 12px"
						/>
						<el-text class="mx-1" size="small">盘点情况总结</el-text>
					</div>

					<el-scrollbar class="row">
						<el-form
							:model="formData"
							label-position="top"
							label-width="420"
							class="form"
							ref="formRefRight"
						>
							<el-form-item class="mt10" prop="summary">
								<el-input
									v-model.trim="formData.summary"
									type="textarea"
									rows="30"
									placeholder="请填写盘点情况总结"
									maxlength="500"
									show-word-limit
									:disabled="true"
								/>
							</el-form-item>
						</el-form>
					</el-scrollbar>
				</div>
			</div>
			<div class="row">
				<ButtonList
					class="footer"
					:loading="formBtnLoading"
					:button="formBtnList"
					@on-btn-click="emits('close')"
				/>
			</div>
		</div>

		<Drawer
			:size="modalSize.lg"
			v-model:drawer="matVisible"
			:destroyOnClose="true"
		>
			<inventory-mat-detail
				:id="props.taskId"
				:taskRow="taskInfo"
				@close="matVisible = false"
			/>
		</Drawer>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.drawer-container {
	.bottom {
		display: flex;
		height: calc(100% - 80px);
	}
	.left {
		width: 50%;
		height: calc(100% - 10px);
		.txt-color {
			color: #666;
			font-size: 12px;
		}
	}

	.right {
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		width: 50%;
	}
}
</style>
