<!-- 分配记录 -->
<template>
	<div
		style="
			height: calc(100vh - 90px);
			width: 100%;
			display: flex;
			flex-direction: row;
		"
	>
		<!-- 设备list -->
		<el-row
			style="
				border-right: 1px solid #ccc;
				height: 100%;
				padding-bottom: 20px;
				width: 270px;
			"
			id="scrollContent"
		>
			<el-scrollbar class="date-log-list-wrap mt10">
				<div v-if="dateLogList?.length > 0">
					<div class="header-wrap">
						<span class="header-item" style="width: 150px">分配时间</span>
						<span class="header-item" style="width: 80px; text-align: right">
							分配数量
						</span>
					</div>
					<div
						class="item"
						v-for="item in dateLogList"
						:key="item.allocationDate!"
						@click="handleClickDateLogTag(item)"
						:class="{
							active: selectedDateLog.allocationDate == item?.allocationDate
						}"
					>
						<p class="item-info">
							<span class="label" style="width: 150px">
								<font-awesome-icon
									:icon="['far', 'fa-clock']"
									style="color: #666"
								/>
								{{ item.allocationDate || "---" }}
							</span>
							<span class="label" style="width: 80px; text-align: right">
								{{ toFixedTwo(item.num) }}
							</span>
						</p>
					</div>
				</div>

				<div class="empty_content" v-else style="width: 230px">
					<div class="empty_table">
						<EmptyData class="empty_img" />
						<p>未查询到相关数据</p>
					</div>
				</div>
			</el-scrollbar>
		</el-row>
		<!-- 库存事务 table -->
		<el-row style="width: calc(100% - 270px)">
			<pitaya-table
				style="width: 100%"
				ref="tableRef"
				:columns="tableProp"
				:table-data="tableData"
				:need-index="true"
				:single-select="false"
				:need-pagination="false"
				:total="pageTotal"
				:table-loading="tableLoading"
				:max-height="maxTableHeight"
				@on-selection-change="selectedTableList = $event"
				@on-current-page-change="onCurrentPageChange"
			>
				<!-- 性别 -->
				<template #sex="{ rowData }">
					{{ rowData.sex == 1 ? "男" : "女" }}
				</template>
			</pitaya-table>
		</el-row>
	</div>
</template>
<script lang="ts" setup>
import { MatLowValueAllocationVO } from "@/app/baseline/utils/types/lowValue-ledger"
import { useTbInit } from "../../components/tableBase"
import {
	batchFormatterNumView,
	maxTableHeight,
	toFixedTwo
} from "@/app/baseline/utils"

import {
	getListAllocation,
	getListAllocationDetail
} from "@/app/baseline/api/lowValue/requisitionApply"
import { MatLowValueAllocationLogDateVO } from "@/app/baseline/utils/types/lowValue-requisition-apply"
import EmptyData from "@/assets/images/empty/empty_data.svg?component"

const props = defineProps<{
	/**
	 * 领用物资明细id
	 */
	applyItemId: number
}>()

const {
	fetchParam,
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	selectedTableList,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = useTbInit<MatLowValueAllocationVO, Record<string, any>>()

fetchFunc.value = getListAllocationDetail

tableProp.value = [
	{ prop: "realname", label: "使用人", fixed: "left", width: 120 },
	{ prop: "username", label: "账号", width: 120 },
	{ prop: "sex", label: "性别", needSlot: true, width: 100 },
	{ prop: "phone", label: "手机号", width: 120 },
	{ prop: "sysOrgId_view", label: "部门", minWidth: 120 },
	{ prop: "num_view", label: "分配数量", align: "right", width: 120 },
	{ prop: "returnNum_view", label: "归还数量", align: "right", width: 120 },
	{ prop: "wasteOldNum_view", label: "交旧数量", align: "right", width: 120 },
	{ prop: "storeLocation", label: "存放位置", minWidth: 120 }
]

watch(tableData, () => {
	batchFormatterNumView(tableData.value)
})

const dateLogList = ref<MatLowValueAllocationLogDateVO[]>([])
const selectedDateLog = ref<MatLowValueAllocationLogDateVO>({})
async function loadMoreData() {
	const res = await getListAllocation(props.applyItemId)

	dateLogList.value = [...res]

	selectedDateLog.value = res?.[0] || {}
	handleClickDateLogTag(selectedDateLog.value)
}

function handleClickDateLogTag(item?: MatLowValueAllocationLogDateVO) {
	if (!item?.allocationDate) return
	selectedDateLog.value = { ...item }
	fetchParam.value.allocationDate = item.allocationDate
	fetchTableData()
}
onMounted(() => {
	fetchParam.value.applyItemId = props.applyItemId
	loadMoreData()
})
</script>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.empty_content {
	display: flex;
	justify-content: center;
	align-items: center;
}

.empty_table {
	line-height: 1rem;
	padding-bottom: 40px;
	.empty_img {
		width: 150px;
	}
	p {
		color: #666666;
		font-size: 12px;
		text-align: center;
	}
}

.date-log-list-wrap {
	height: 100%;
	margin: 0 20px 0 10px;
	font-size: 12px;
	width: 100%;
	.header-wrap {
		color: #666;
		width: 100%;
		.header-item {
			display: inline-block;
			text-align: center;
		}
		padding-bottom: 10px;
	}
	.item {
		color: #666;
		border-bottom: 1px solid #ccc;
		padding: 10px;
		cursor: pointer;

		.item-info {
			padding: 5px 0;
			display: flex;
			flex-direction: row;
			.label {
				width: 70px;
			}
		}
	}
	.item:hover {
		background: var(--el-fill-color-light);
	}
	.item.active {
		background: var(--el-fill-color-light);
	}
}
</style>
