<script setup lang="ts">
import { computed, onMounted, ref } from "vue"
import { DictApi } from "@/app/baseline/api/dict"
import DictTag from "../../components/dictTag.vue"

import {
	IIdempotentTokenType,
	IIdempotentTokenTypePre,
	IModalType
} from "../../../utils/types/common"
import { ElMessage, FormInstance, FormItemRule } from "element-plus"
import { inputMaxLength, modalSize } from "@/app/baseline/utils/layout-config"
import { batchFormatterNumView, getModalTypeLabel } from "@/app/baseline/utils"
import { FormElementType } from "../../components/define"
import formElement from "../../components/formElement.vue"

import { useTbInit } from "../../components/tableBase"
import { debounce, findIndex, first, map, includes, toNumber } from "lodash-es"
import { useMessageBoxInit } from "../../components/messageBox"
import { fileBusinessType } from "@/app/baseline/api/dict"
import TableFile from "../../components/tableFile.vue"
import matSelector from "../../store/components/matSelector.vue"
import userSelector from "../../store/components/userSelector.vue"
import { useUserStore } from "@/app/platform/store/modules/user"
import {
	addUseApply,
	listUseApplyDetail,
	listUseApplyPageItem,
	updateUseApply,
	listApplyItemSelect,
	addApplyItemSelect,
	updateApplyItemUseLowNum,
	delApplyItem,
	publishUseApply
} from "@/app/baseline/api/lowValue/requisitionApply"
import {
	MatLowValueApplyDTO,
	MatLowValueApplyItemVO,
	MatLowValueApplyItemVORequest,
	MatLowValueApplyVO
} from "@/app/baseline/utils/types/lowValue-requisition-apply"
import {
	getIdempotentToken,
	requiredValidator,
	validateAndCorrectInput
} from "@/app/baseline/utils/validate"
import { SystemUserVo } from "@/app/baseline/utils/types/system"
import storeTable from "../../store/components/storeTable.vue"
import { IWarehouseType } from "@/app/baseline/utils/types/store-manage"
import { BaseLineSysApi } from "@/app/baseline/api/system"
import { ICostCategoryStatus } from "@/app/baseline/utils/types/system-cost-category"
import { useDictInit } from "../../components/dictBase"

const { showDelConfirm, showWarnConfirm } = useMessageBoxInit()
const { userInfo } = storeToRefs(useUserStore())

const { dictOptions, getDictByCodeList } = useDictInit()

const props = defineProps<{
	id?: any // id
	mode: IModalType // create || edit || create
}>()

const emits = defineEmits<{
	(e: "close"): void
	(e: "update"): void
	(e: "save"): void
}>()

const drawerLoading = ref(false)

const lineOptions = ref()
const depotOptions = ref()

/**
 * 保存旧的表单数据
 */
const oldFormData = ref<string>()

/**
 * 左侧title 配置
 */
const leftTitleConf = computed(() => ({
	name: [
		getModalTypeLabel(
			canEditExtra.value ? IModalType.edit : IModalType.create,
			"领用申请"
		)
	],
	icon: ["fas", "square-share-nodes"]
}))

const leftBtnConf = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "保存草稿",
		icon: ["fas", "floppy-disk"]
	}
]
const drawerBtnRightConf = computed(() => [
	{
		name: "提交审核",
		icon: ["fas", "circle-check"],
		disabled: submitBtnDisabled.value || tableData.value.length < 1
	}
])
const formBtnLoading = ref(false) // 表单按钮loading

/**
 * 右侧 title 配置
 */
const extraTitleConf = {
	name: ["相关信息"],
	icon: ["fas", "square-share-nodes"]
}
const tabsConf = ["领用物资", "相关附件"]
const activatedTab = ref(0)

const handleTabChange = (index: number) => {
	activatedTab.value = index

	if (index === 0) {
		fetchParam.value = {
			applyId: props.id || formData.value.id,
			sord: "desc",
			sidx: "createdDate"
		}

		pageSize.value = 20

		getTableData()
	}
}

const canEditExtra = computed(() => Boolean(formData.value?.id || props.id))

/**
 * 左侧表单 配置
 */
const formData = ref<MatLowValueApplyVO>({})
const formRef = ref<FormInstance>()
const formElBase = computed<FormElementType[][]>(() => [
	[
		{ label: "领用申请名称", name: "label", maxlength: inputMaxLength.input },
		{
			label: "领用用途",
			name: "illustrate",
			type: "textarea",
			maxlength: inputMaxLength.textarea
		},
		{
			label: "费用类别",
			name: "expenseCategory_view",
			vname: "expenseCategory",
			type: "treeSelect",
			treeApi: () =>
				BaseLineSysApi.getCostCategoryTree({
					status: ICostCategoryStatus.Started
				})
		},
		{
			label: "线路",
			name: "lineNoId",
			type: "select",
			data: lineOptions.value,
			placeholder: "请选择线路",
			disabled: canEditExtra.value,
			change: () => {
				formData.value.storeId = undefined
				formData.value.storeLabel = undefined
				setTimeout(() => {
					formRef.value?.clearValidate()
				})
			}
		},
		{
			label: "段区",
			name: "depotId",
			type: "select",
			data: depotOptions.value,
			placeholder: "请选择段区",
			disabled: canEditExtra.value,
			change: () => {
				formData.value.storeId = undefined
				formData.value.storeLabel = undefined
				setTimeout(() => {
					formRef.value?.clearValidate()
				})
			}
		},
		{
			label: "出库仓库",
			name: "storeLabel",
			type: "drawer",
			disabled: canEditExtra.value,
			placeholder: "请选择",
			clear: false,
			clickApi: () => (storeVisible.value = true)
		},
		{
			label: "专业",
			name: "majorId_view",
			vname: "majorId",
			type: "treeSelect",
			treeApi: BaseLineSysApi.getProfessionTree,
			placeholder: "请选择专业"
		},
		{
			label: "负责人（部门负责人）",
			name: "pickUserId_view",
			type: "drawer",
			disabled: canEditExtra.value,
			placeholder: "请选择",
			clear: false,
			clickApi: () => (userSelectorVisible.value = true)
		},
		{ label: "领用人", name: "createdBy_view", disabled: true },
		{ label: "领用部门", name: "pickSysOrgId_view", disabled: true },
		{
			label: "备注说明",
			name: "remark",
			type: "textarea",
			maxlength: inputMaxLength.textarea
		}
	]
])

// 左侧表单校验
const formRules: Record<string, FormItemRule> = {
	label: requiredValidator("领用申请名称"),
	illustrate: requiredValidator("领用用途"),
	lineNoId: requiredValidator("线路"),
	pickUserId_view: requiredValidator("负责人（部门负责人）"),
	storeLabel: requiredValidator("出库仓库"),
	expenseCategory_view: requiredValidator("费用类别")
}

/**
 * 部门负责人 visible
 */
const userSelectorVisible = ref(false)

/**
 * 出库仓库 visible
 */
const storeVisible = ref(false)

/**
 * 部门负责人 handler
 */
const handleUserSelect = (e?: SystemUserVo[], callback?: any) => {
	const selectRow = first(e)
	if (!selectRow?.username) {
		callback()
		return ElMessage.warning("请选择负责人！")
	}

	formData.value.pickUserId_view = selectRow.realname
	formData.value.pickUserId = selectRow.username
	userSelectorVisible.value = false
}

/**
 * 选择仓库 保存
 * @param btnName
 * @param e
 */
function handleSaveStore(btnName: string, e?: any) {
	if (btnName === "保存") {
		formData.value.storeLabel = e?.label
		formData.value.storeId = e?.id
	}
	storeVisible.value = false
}

/**
 * 初始化领用信息
 */
function initMatPickApplyUserInfo() {
	// 领用人
	formData.value.createdBy_view = userInfo.value.realName as any
	formData.value.createdBy = userInfo.value.userName as any
	formData.value.sysOrgId = userInfo.value.orgId as any

	// 领用 部门
	formData.value.pickSysOrgId = userInfo.value.orgId as any
	formData.value.pickSysOrgId_view = userInfo.value.orgName
}

/**
 * 表单按钮 操作
 * @param btnName 保存草稿 | 取消
 */
const handleFormBtnClick = (btnName?: string) => {
	if (btnName == "取消") {
		emits("close")
	} else if (btnName == "保存草稿") {
		handleSaveDraft()
	} else if (btnName == "提交审核") {
		handleSubmit()
	}
}

/**
 * 保存草稿 handler
 */
async function handleSaveDraft() {
	if (!formRef.value) {
		return
	}

	formRef.value.validate(async (valid) => {
		if (!valid) {
			return
		}
		formBtnLoading.value = true
		// 表单校验通过
		try {
			const api = canEditExtra.value ? updateUseApply : addUseApply

			let idempotentToken = ""
			if (!canEditExtra.value) {
				idempotentToken = getIdempotentToken(
					IIdempotentTokenTypePre.apply,
					IIdempotentTokenType.lowValueUseApply
				)
			}

			const r = await api(
				formData.value as MatLowValueApplyDTO,
				idempotentToken
			)
			formData.value.id = r.id
			fetchParam.value.applyId = r.id

			oldFormData.value = JSON.stringify(formData.value)

			ElMessage.success("操作成功")
			getTableData()
			emits("save")
		} finally {
			formBtnLoading.value = false
		}
	})
}

/**
 * 提交 handler
 */
async function handleSubmit() {
	if (!formRef.value) {
		return
	}

	formRef.value.validate(async (valid) => {
		if (!valid) {
			return
		}
		await showWarnConfirm("请确认是否提交本次数据？")

		formBtnLoading.value = true
		drawerLoading.value = true

		try {
			// 如果主表有修改，则先更新主表数据
			if (oldFormData.value != JSON.stringify(formData.value)) {
				await updateUseApply(formData.value as MatLowValueApplyDTO)
			}

			const idempotentToken = getIdempotentToken(
				IIdempotentTokenTypePre.other,
				IIdempotentTokenType.lowValueUseApply,
				formData.value.id
			)

			const { code, msg, data } = await publishUseApply(
				props.id || formData.value.id,
				idempotentToken
			)

			if (data && code != 200) {
				errorGoodsIdList.value = data || []
				ElMessage.error(msg)
				getTableData()
			} else {
				ElMessage.success("操作成功")

				emits("save")
				emits("close")
			}
		} finally {
			formBtnLoading.value = false
			drawerLoading.value = false
		}
	})
}

const queryConf = computed(() => {
	return [
		{
			name: "物资编码",
			key: "materialCode",
			type: "input",
			placeholder: "请输入物资编码"
		},
		{
			name: "物资名称",
			key: "materialLabel",
			type: "input",
			placeholder: "请输入物资名称"
		},
		{
			name: "规格型号",
			key: "version",
			placeholder: "请输入规格型号",
			type: "input"
		},
		{
			name: "物资性质",
			key: "attribute",
			type: "select",
			children: dictOptions.value.MATERIAL_NATURE,
			placeholder: "请选择物资性质"
		}
	]
})

const {
	currentPage,
	tableProp,
	tableCache,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	pageSize,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = useTbInit<MatLowValueApplyItemVO, MatLowValueApplyItemVORequest>()

tableProp.value = [
	{ prop: "materialCode", label: "物资编码", width: 130, fixed: "left" },
	{ prop: "materialLabel", label: "物资名称", width: 130 },
	{ prop: "materialTypeCode", label: "物资分类编码", width: 130 },
	{ prop: "materialTypeLabel", label: "物资分类名称", width: 130 },
	{ prop: "version", label: "规格型号" },
	{ prop: "attribute", label: "物资性质", needSlot: true, width: 120 },
	{ prop: "lowValueType_view", label: "低值类型" },
	{ prop: "lowValueBookNum_view", label: "台账数量", align: "right" },
	{ prop: "hasUsedNum_view", label: "已领用", width: 130, align: "right" },
	{ prop: "canUseNum_view", label: "可领用", width: 100, align: "right" },
	{ prop: "freezeNum_view", label: "冻结量", align: "right" },
	{
		prop: "num",
		label: "本次领用",
		width: 120,
		needSlot: true,
		fixed: "right"
	},
	/* { prop: "storeName", label: "仓库名称", width: 100, fixed: "right" }, */
	{ prop: "actions", label: "操作", width: 100, needSlot: true, fixed: "right" }
]

fetchFunc.value = listUseApplyPageItem

watch(tableData, () => {
	batchFormatterNumView(tableData.value)
})

const goodsSelectorVisible = ref(false)
const tbBtnConf = [
	{
		name: "添加物资",
		icon: ["fas", "circle-plus"]
	}
]

/**
 * 删除物资
 */
async function handleDelGoods(e: any) {
	await showDelConfirm()
	await delApplyItem([e.id])
	ElMessage.success("操作成功")
	fetchTableData()
	emits("update")
}

/**
 * 较验：本次领用 < 可领用
 *
 */
const submitBtnDisabled = ref(false)
function validateNum(e: any) {
	const canGetNum = toNumber(e.canUseNum) ?? 0
	const num = toNumber(e.num) ?? 0

	const oldRow = tableCache.find((v) => v.id == e.id)
	if (num > canGetNum) {
		ElMessage.warning("本次领用数量不能大于可领用数量")
		e.num = canGetNum
	} else if (num <= 0) {
		ElMessage.warning("领用数量不能小于等于0！")
		e.num = oldRow.num
		return
	} else if (num > 0) {
		e.num = num
	}

	submitBtnDisabled.value = true
	updateMatGetNum(e)
}

/**
 * 更新领用物资数量 api
 */
const updateMatGetNum = debounce(async (e: any) => {
	try {
		await updateApplyItemUseLowNum({
			id: e.id,
			num: e.num ?? 0
		})

		//  更新 tableCache
		const rowIdx = findIndex(tableCache, (v) => v.id === e.id)
		if (rowIdx !== -1) {
			tableCache.splice(rowIdx, 1, { ...e })
		}

		emits("update")
		ElMessage.success("操作成功")
	} finally {
		submitBtnDisabled.value = false
	}
}, 300)

/**
 * 添加物资 table 列
 */
const matSelectColumns: TableColumnType[] = [
	{ prop: "materialCode", label: "物资编码", width: 130, fixed: "left" },
	{ prop: "materialLabel", label: "物资名称" },
	/* { prop: "storeName", label: "仓库名称" }, */
	{ prop: "materialTypeCode", label: "物资分类编码" },
	{ prop: "materialTypeLabel", label: "物资分类名称" },
	{ prop: "version", label: "规格型号" },
	{ prop: "attribute", label: "物资性质", needSlot: true, width: 120 },
	{ prop: "lowValueType_view", label: "低值类型" },
	{ prop: "lowValueBookNum_view", label: "台账数量", align: "right" },
	{ prop: "hasUsedNum_view", label: "已领用", align: "right" },
	/* { prop: "freezeNum_view", label: "已冻结", align: "right" }, */
	{ prop: "canUseNum_view", label: "可领用", align: "right" }
]

const handleAddGoods = async (params: any[]) => {
	const itemList = map(
		params,
		({ materialId, materialCode, materialLabel, version, attribute }) => ({
			materialId,
			materialCode,
			materialLabel,
			version,
			attribute
		})
	)

	const idempotentToken = getIdempotentToken(
		IIdempotentTokenTypePre.other,
		IIdempotentTokenType.lowValueUseApply,
		formData.value.id
	)
	await addApplyItemSelect(
		{
			applyId: props.id || formData.value.id,
			itemList
		},
		idempotentToken
	)
	goodsSelectorVisible.value = false
	ElMessage.success("操作成功")
	getTableData()
	emits("update")
}
/**
 * 获取详情数据
 */
async function getDetail() {
	drawerLoading.value = true
	try {
		const r = await listUseApplyDetail(props.id || formData.value.id)
		formData.value = r

		//fetchParam.value.sysOrgId = formData.value.sysOrgId

		oldFormData.value = JSON.stringify(formData.value)
		setTimeout(() => {
			formRef.value?.clearValidate()
		})
	} finally {
		drawerLoading.value = false
	}
}

/**
 * 物资明细 table 行样式
 *
 * 库存异常物资，用红色标记该行
 */
const errorGoodsIdList = ref<number[]>([])
function tbCellClassName({ row }: any) {
	return includes(errorGoodsIdList.value, row.id) ? "error" : ""
}

/**
 * table 数据源
 * @param data
 */
const getTableData = (data?: any) => {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()
	fetchParam.value = {
		...fetchParam.value,
		...data
	}

	fetchTableData()
}

onMounted(async () => {
	fetchParam.value.sidx = "createdDate"
	fetchParam.value.sord = "desc"

	lineOptions.value = await BaseLineSysApi.getLineOptions()
	depotOptions.value = await BaseLineSysApi.getDepotList()

	await getDictByCodeList(["MATERIAL_NATURE"])
	if (props.id) {
		fetchParam.value.applyId = props.id

		await getDetail()
		getTableData()
	} else {
		initMatPickApplyUserInfo()
	}
})
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<div class="drawer-column left" style="width: 310px">
			<Title :title="leftTitleConf" />

			<el-scrollbar>
				<el-form
					class="content"
					:model="formData"
					ref="formRef"
					label-position="top"
					label-width="100px"
					:rules="formRules"
				>
					<form-element :form-element="formElBase" :form-data="formData" />
				</el-form>
			</el-scrollbar>
			<button-list
				class="footer"
				:button="leftBtnConf"
				:loading="formBtnLoading"
				@on-btn-click="handleFormBtnClick"
			/>
		</div>
		<div
			class="drawer-column right"
			:class="!canEditExtra ? 'disabled' : ''"
			style="width: calc(100% - 310px)"
		>
			<Title :title="extraTitleConf">
				<Tabs
					style="margin-right: auto; margin-left: 30px"
					:tabs="tabsConf"
					@on-tab-change="handleTabChange"
				/>
			</Title>

			<el-scrollbar class="rows requisition-apply-editor-table-wrapper">
				<Query
					:query-arr-list="(queryConf as any)"
					style="margin: 10px 10px -10px"
					@get-query-data="getTableData"
					v-if="activatedTab === 0"
				/>
				<pitaya-table
					v-if="activatedTab === 0"
					ref="tableRef"
					:columns="tableProp"
					:table-data="tableData"
					:need-selection="false"
					:single-select="false"
					:need-index="true"
					:need-pagination="true"
					:total="pageTotal"
					:table-loading="tableLoading"
					:cell-class-name="tbCellClassName"
					@on-selection-change="selectedTableList = $event"
					@on-current-page-change="onCurrentPageChange"
				>
					<!-- 物资性质 -->
					<template #attribute="{ rowData }">
						<dict-tag
							:options="DictApi.getMatAttr()"
							:value="rowData.attribute"
						/>
					</template>

					<!-- 本次领用 -->
					<template #num="{ rowData }">
						<el-input
							class="no-arrows"
							v-model="rowData.num"
							@click.stop
							@input="rowData.num = validateAndCorrectInput($event)"
							@change="validateNum(rowData)"
						/>
					</template>

					<template #actions="{ rowData }">
						<el-button v-btn link @click.stop="handleDelGoods(rowData)">
							<font-awesome-icon
								:icon="['fas', 'trash-can']"
								style="color: var(--pitaya-btn-background)"
							/>
							<span class="table-inner-btn">移除</span>
						</el-button>
					</template>

					<template #footerOperateLeft>
						<button-list
							:button="tbBtnConf"
							:is-not-radius="true"
							:loading="formBtnLoading"
							@on-btn-click="goodsSelectorVisible = true"
						/>
					</template>
				</pitaya-table>

				<table-file
					v-else
					:business-type="fileBusinessType.requisitionApply"
					:business-id="props.id || formData.id"
					:mod="canEditExtra ? IModalType.edit : IModalType.view"
				/>
			</el-scrollbar>
			<button-list
				class="footer"
				:button="drawerBtnRightConf"
				:loading="formBtnLoading"
				@on-btn-click="handleFormBtnClick"
			/>
		</div>

		<!-- 部门负责人 -->
		<Drawer
			v-model:drawer="userSelectorVisible"
			:size="modalSize.lg"
			destroy-on-close
		>
			<user-selector
				:sysOrgId="formData.pickSysOrgId"
				:sysOrgIdView="formData.pickSysOrgId_view"
				:table-fetch-params="{ addOrgAuthorityFalg: true }"
				@save="handleUserSelect"
				@close="userSelectorVisible = false"
				:selected-ids="[formData.pickUserId]"
			/>
		</Drawer>

		<!-- 仓库选择 -->
		<Drawer v-model:drawer="storeVisible" :size="modalSize.lg" destroy-on-close>
			<store-table
				:selected-ids="[formData.storeId]"
				:table-api-params="{
					lineId: formData.lineNoId,
					depotId: formData.depotId,
					type: `${IWarehouseType.default},${IWarehouseType.dangerousWarehouse}`
				}"
				@on-save="handleSaveStore"
			/>
		</Drawer>

		<!-- 物资编码手册选择器 -->
		<Drawer
			v-model:drawer="goodsSelectorVisible"
			:size="modalSize.lg"
			destroy-on-close
		>
			<mat-selector
				:table-req-params="{
					id: formData.id,
					sord: 'asc',
					sidx: 'code'
				}"
				:table-api="listApplyItemSelect"
				:columns="matSelectColumns"
				:multiple="true"
				@save="handleAddGoods"
				@close="goodsSelectorVisible = false"
			/>
		</Drawer>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.content {
	.btn-group {
		width: 100%;
		.el-button--warning {
			background-color: #e6a23c !important;
		}
		.el-button--success {
			background-color: #67c23a !important;
		}
		.el-button {
			width: 50% !important;
		}
	}
}

.requisition-apply-editor-table-wrapper {
	&:deep(.pitaya-table) {
		.error {
			.column-inner-item,
			.cell,
			.el-input__inner {
				color: red;
			}
		}
	}
}
</style>
