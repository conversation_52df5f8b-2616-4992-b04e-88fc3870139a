<!-- 项目专用： 生成批次码 -->
<template>
	<div class="drawer-container">
		<div class="drawer-column" style="width: 100%">
			<Title :title="titleConf" />

			<div class="rows">
				<pitaya-table
					ref="tableRef"
					:columns="tableProp"
					:table-data="tableData"
					:need-index="true"
					:need-selection="true"
					:customize-height-number="160"
					:table-loading="tableLoading"
					selectKey="id"
					@onSelectionChange="selectedTableList = $event"
				>
					<template #batchNoNum="{ rowData }">
						<el-input
							class="no-arrows"
							v-model="rowData.batchNoNum"
							@click.stop
							@input="rowData.batchNoNum = validateAndCorrectInput($event, 0)"
							@change="validateBarCodeNum(rowData)"
						/>
					</template>
					<!-- 批次码 -->
					<template #qrCode="{ rowData }">
						<div class="qr-code-wrap" style="width: 85mm; padding: 2mm">
							<div class="qr-code-info">
								<div class="qr-code-info-left">
									<p class="info-text">
										低值类型:{{ rowData?.lowValueType_view }}
									</p>
									<p class="info-text">物资编码:{{ rowData?.materialCode }}</p>
									<p class="info-text">
										领用时间:{{ rowData?.outStoreTime || "---" }}
									</p>
								</div>
								<div class="qr-code-info-right">
									<vue-qrcode :value="rowData?.qrCode" class="label-qrcode" />
								</div>
							</div>
							<p class="store-label-wrap">
								物资名称:{{ rowData?.materialLabel }}
							</p>
						</div>
					</template>
				</pitaya-table>
			</div>

			<button-list
				class="footer"
				:button="btnConf"
				:loading="btnLoading"
				@on-btn-click="handleBtnClick"
			/>
		</div>
	</div>

	<div style="height: 0; overflow: hidden">
		<div
			id="print-wrap"
			style="
				position: absolute;
				z-index: -1;
				top: 0;
				left: 0;
				width: 500px;
				height: 100%;
				background: #fff;
			"
		>
			<div
				v-for="rowData in printList"
				:key="rowData.id!"
				style="padding-left: 20px"
			>
				<div
					class="qr-code-wrap"
					style="
						page-break-after: always;
						width: 90mm;
						padding: 0;
						height: 50mm;
						page-break-after: always;
						border: none;
					"
				>
					<div class="qr-code-info">
						<div class="qr-code-info-left" style="max-height: 35mm">
							<p class="info-text" style="max-width: 100%">
								低值类型:{{ rowData?.lowValueType_view }}
							</p>
							<p class="info-text" style="max-width: 100%">
								物资编码:{{ rowData?.materialCode }}
							</p>
							<p class="info-text" style="max-width: 100%">
								领用时间:{{ rowData?.outStoreTime || "---" }}
							</p>
						</div>
						<div class="qr-code-info-right" style="max-height: 35mm">
							<vue-qrcode
								:value="rowData?.qrCode"
								class="label-qrcode-print"
								style="border: none"
							/>
						</div>
					</div>
					<p class="store-label-wrap" style="max-width: 100%">
						物资名称:{{ rowData?.materialLabel }}
					</p>
				</div>
			</div>
		</div>
	</div>
</template>
<script setup lang="ts">
import printJS from "print-js"
import { validateAndCorrectInput } from "@/app/baseline/utils/validate"
import { debounce, map, toNumber } from "lodash-es"
import { useTbInit } from "../../components/tableBase"
import { MatLowValueApplyItemVO } from "@/app/baseline/utils/types/lowValue-requisition-apply"
import VueQrcode from "@chenfengyuan/vue-qrcode"

const emit = defineEmits<{
	(e: "close"): void
	(e: "save", v: any[]): void
}>()

const props = withDefaults(
	defineProps<{
		title?: string

		tableList?: any[]
	}>(),
	{
		title: "标签码"
	}
)

const titleConf = computed(() => ({
	name: [props.title],
	icon: ["fas", "square-share-nodes"]
}))

const { tableProp, tableData, tableRef, selectedTableList } = useTbInit<
	MatLowValueApplyItemVO,
	Record<string, any>
>()

const btnLoading = ref(false)

tableProp.value = [
	{
		prop: "materialCode",
		label: "物资编码",
		width: 130
	},
	{
		prop: "materialLabel",
		label: "物资名称",
		minWidth: 150
	},
	{
		prop: "lowValueType_view",
		label: "低值类型",
		width: 120
	},
	{
		prop: "outStoreTime",
		label: "领用时间",
		width: 180
	},
	{
		prop: "batchNoNum",
		label: "打印数量",
		needSlot: true,
		width: 200
	},
	{
		prop: "qrCode",
		label: "标签码预览",
		needSlot: true,
		width: 350,
		showOverflowTooltip: false
	}
]

const validateBarCodeNum = debounce((e: any) => {
	let batchNoNum = toNumber(e.batchNoNum)

	if (batchNoNum > 100) {
		batchNoNum = 100
		ElMessage.warning("打印数量不能大于100")
	} else if (batchNoNum < 1) {
		batchNoNum = 1
		ElMessage.warning("打印数量不能小于1")
	}
	e.batchNoNum = batchNoNum

	setTimeout(() => {
		e.batchNoNumList = Array.from({ length: batchNoNum }, (v, i) => i + 1)
	}, 0)
}, 300)

const btnConf = computed(() => {
	return [
		{
			name: "取消",
			icon: ["fas", "circle-minus"]
		},
		{
			name: "打印",
			icon: ["fas", "print"]
		}
	]
})

const tableLoading = ref(false)
async function getBatchByItemIds() {
	tableData.value = map(props.tableList || [], (v) => ({
		...v,
		batchNoNum: 1,
		batchNoNumList: [1]
	}))
}

onMounted(() => {
	getBatchByItemIds()
})

const loadingText = ref<string>("")

const printList = ref<Record<string, any>[]>([])

async function handleBtnClick(name?: string) {
	if (name === "取消") {
		return emit("close")
	}

	if (!selectedTableList.value.length) {
		return ElMessage.warning("请选择要打印的批次码")
	}

	printList.value = []

	selectedTableList.value.forEach((item) => {
		if (item.batchNoNumList.length > 0) {
			item.batchNoNumList.forEach((i: number) => {
				printList.value.push({ ...item, id: `${item.id}-${i}` })
			})
		}
	})

	btnLoading.value = true
	try {
		loadingText.value = "正在唤起浏览器打印界面，请等待..."
		btnLoading.value = true
		setTimeout(async () => {
			await nextTick()
			printJS({
				printable: "print-wrap",
				type: "html",
				targetStyles: ["*"],
				showModal: true,
				modalMessage: "正在打开打印界面...",
				onLoadingEnd: () => {
					loadingText.value = ""
					btnLoading.value = false
				},
				onPdfOpen: () => {
					console.log("更新列表数据")
				}
			})
		}, 200)

		//emit("save", selectedTableList.value)
	} finally {
		btnLoading.value = false
	}
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.qr-code-wrap {
	font-size: 12px;
	border: 1px solid #ccc;

	display: flex;
	flex-direction: column;
	justify-content: center;
	width: 80mm;
	padding: 1mm;
	height: 40mm;
	font-family: "Microsoft YaHei", "Microsoft YaHei UI", "PingFang SC", "Arial",
		"sans-serif";
	color: var(--color-text);
	.qr-code-info {
		display: flex;
		justify-content: space-between;
		.qr-code-info-left {
			display: flex;
			flex-direction: column;
			justify-content: space-around;
			max-height: 25mm;
			width: 60mm;
			.info-text {
				text-align: left;
				max-width: 60mm;
				/* 中文自动换行，英文单词尽量保持完整，数字不被拆分 */
				word-wrap: break-word;
				word-break: normal;
				white-space: normal;
				display: -webkit-box;
				-webkit-box-orient: vertical;
				overflow: hidden;
				text-overflow: ellipsis;
				margin-top: 5px;
				line-height: 18px;
				max-height: 36px;
			}
			.label-logo {
				width: 25mm;
				height: 6.3mm;
			}
		}

		.qr-code-info-right {
			display: flex;
			max-height: 25mm;
			position: relative;
			text-align: center;
			.label-type {
				background-color: #000;
				color: #fff;
				max-height: 25mm;
				// 上下边距 固定参数
				padding: 6mm 1mm;
			}
			.type-text {
				font-size: 12pt;
				font-family: 思源黑;
				font-weight: bold;
				line-height: 1;
				writing-mode: vertical-rl;
			}

			.label-qrcode {
				width: 25mm !important;
				height: 25mm !important;
				border: 1px solid #ccc;
			}

			.label-qrcode-print {
				width: 40mm !important;
				height: 40mm !important;
				margin-top: -2mm;
				margin-right: -6mm;
				border: 1px solid #ccc;
			}
		}
	}

	.store-label-wrap {
		text-align: left;
		max-width: 80mm;
		/* 中文自动换行，英文单词尽量保持完整，数字不被拆分 */
		word-wrap: break-word;
		word-break: normal;
		white-space: normal;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		overflow: hidden;
		text-overflow: ellipsis;
		margin-top: 5px;
		line-height: 18px;
		max-height: 36px;
	}
}
</style>
