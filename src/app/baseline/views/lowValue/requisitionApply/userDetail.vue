<!-- 低值 - 领用申请 - 分配物资信息 详情 -->
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<div class="drawer-column left" style="width: 310px">
			<Title :title="titleConf" />
			<el-scrollbar>
				<!-- 分配信息 -->
				<el-descriptions size="small" :column="1" border class="content">
					<el-descriptions-item
						v-for="desc in descOptions"
						:key="desc.label"
						:label="desc.label"
					>
						<dict-tag
							v-if="desc.key === 'attribute'"
							:options="DictApi.getMatAttr()"
							:value="matItemData.attribute"
						/>
						<span v-else>
							{{ getNumDefByNumKey(desc.key, matItemData[desc.key]) }}
						</span>
					</el-descriptions-item>
				</el-descriptions>
			</el-scrollbar>
		</div>
		<div class="drawer-column right" style="width: calc(100% - 310px)">
			<div class="rows">
				<Title :title="extTitleConf">
					<Tabs
						style="margin-right: auto"
						:tabs="['分配明细', '分配记录', '相关附件']"
						@on-tab-change="handleTabChange"
					/>
				</Title>

				<pitaya-table
					v-if="activatedTab === 0"
					ref="tableRef"
					:columns="tableProp"
					:table-data="tableData"
					:need-selection="false"
					:single-select="false"
					:need-index="true"
					:need-pagination="false"
					:total="pageTotal"
					:table-loading="tableLoading"
					:max-height="maxTableHeight"
					@on-selection-change="selectedTableList = $event"
					@on-current-page-change="onCurrentPageChange"
				>
					<!-- 性别 -->
					<template #sex="{ rowData }">
						{{ rowData.sex == 1 ? "男" : "女" }}
					</template>

					<!-- <template #actions="{ rowData }">
						<el-button v-btn link @click="handlePrint(rowData)">
							<font-awesome-icon
								:icon="['fas', 'print']"
								style="color: var(--pitaya-btn-background)"
							/>
							<span class="table-inner-btn">标签打印</span>
						</el-button>
					</template> -->
				</pitaya-table>

				<allocationTable
					v-else-if="activatedTab === 1"
					:applyItemId="props.applyItemId"
				/>

				<table-file
					v-else
					:business-type="fileBusinessType.requisitionApplyAllocation"
					:business-id="props.applyItemId"
					:mod="IModalType.edit"
				/>
			</div>

			<button-list
				class="footer"
				:button="btnConf"
				@on-btn-click="emit('close')"
			/>
		</div>
	</div>
</template>

<script setup lang="ts">
import { DictApi, fileBusinessType } from "@/app/baseline/api/dict"
import dictTag from "@/app/baseline/views/components/dictTag.vue"
import {
	batchFormatterNumView,
	getNumDefByNumKey,
	maxTableHeight
} from "@/app/baseline/utils"
import { useTbInit } from "../../components/tableBase"
import {
	MatLowValueAllocationVO,
	MatLowValueApplyItemVO
} from "@/app/baseline/utils/types/lowValue-requisition-apply"
import { listUseAllocation } from "@/app/baseline/api/lowValue/requisitionApply"
import { IModalType } from "@/app/baseline/utils/types/common"
import TableFile from "../../components/tableFile.vue"
import allocationTable from "./allocationTable.vue"

const props = defineProps<{
	/**
	 * 领用物资明细id
	 */
	applyItemId: number
	row: MatLowValueApplyItemVO

	/**
	 * 主表详情
	 */
	descData: Record<string, any>
}>()

const emit = defineEmits<{
	(e: "close"): void
}>()

const descOptions = [
	{ label: "低值类型", key: "lowValueType_view" },
	{ label: "物资编码", key: "materialCode" },
	{ label: "物资名称", key: "materialLabel" },
	{ label: "物资分类编码", key: "materialTypeCode" },
	{ label: "物资分类名称", key: "materialTypeLabel" },
	{ label: "规格型号", key: "version" },
	{ label: "物资性质", key: "attribute" },
	{ label: "领用数量", key: "num_view" },
	{ label: "已交旧数量", key: "wasteOldNum_view" },
	{ label: "已归还数量", key: "returnNum_view" },
	{ label: "冻结量", key: "allocationFreezeNum_view" },
	{ label: "领用人", key: "createdBy_view" },
	{ label: "领用部门", key: "sysOrgId_view" },
	{ label: "申请时间", key: "createdDate" }
]

const drawerLoading = ref(false)

const titleConf = {
	name: ["物资信息"],
	icon: ["fas", "square-share-nodes"]
}

const extTitleConf = {
	name: ["扩展信息"],
	icon: ["fas", "square-share-nodes"]
}

const btnConf = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	}
]

const activatedTab = ref(0)
const handleTabChange = (index: number) => {
	activatedTab.value = index
}

const matItemData = ref<any>({})

const {
	pageSize,
	fetchParam,
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	selectedTableList,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = useTbInit<MatLowValueAllocationVO, Record<string, any>>()

fetchFunc.value = listUseAllocation

tableProp.value = [
	{ prop: "realname", label: "使用人", fixed: "left", width: 120 },
	{ prop: "username", label: "账号", width: 120 },
	{ prop: "sex", label: "性别", needSlot: true, width: 100 },
	{ prop: "phone", label: "手机号", width: 120 },
	{ prop: "sysOrgId_view", label: "部门", minWidth: 120 },
	{ prop: "wasteOldNum_view", label: "已交旧数量", align: "right", width: 120 },
	{ prop: "returnNum_view", label: "已归还数量", align: "right", width: 120 },
	{ prop: "freezeNum_view", label: "冻结量", align: "right", width: 120 },
	{ prop: "canNum_view", label: "分配数量", align: "right", width: 120 },
	{ prop: "storeLocation", label: "存放位置", minWidth: 120 }
	/* { prop: "actions", label: "操作", width: 100, needSlot: true, fixed: "right" } */
]

watch(tableData, () => {
	batchFormatterNumView(tableData.value)
})

onMounted(() => {
	pageSize.value = 9999
	fetchParam.value.applyItemId = props.applyItemId
	fetchParam.value.sidx = "createdDate"
	fetchParam.value.sord = "desc"

	fetchTableData()
	matItemData.value = {
		...props.row,
		createdBy_view: props.descData.createdBy_view,
		sysOrgId_view: props.descData.sysOrgId_view,
		createdDate: props.descData.createdDate
	}
})

/**
 * 标签打印 TODO:暂时不做
 */
async function handlePrint(e?: any) {
	console.log("e", e)
	ElMessage.info("该功能建设中")
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
