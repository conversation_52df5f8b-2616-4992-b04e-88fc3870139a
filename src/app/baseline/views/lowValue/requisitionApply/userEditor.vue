<!-- 低值 - 领用申请 - 分配物资信息 编辑 -->
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<div class="drawer-column left" style="width: 310px">
			<Title :title="titleConf" />
			<el-scrollbar>
				<grid-panel :options="gridPanelOptions" />
				<!-- 分配信息 -->
				<el-descriptions size="small" :column="1" border class="content">
					<el-descriptions-item
						v-for="desc in descOptions"
						:key="desc.label"
						:label="desc.label"
					>
						<dict-tag
							v-if="desc.key === 'attribute'"
							:options="DictApi.getMatAttr()"
							:value="matItemData.attribute"
						/>
						<span v-else>
							{{ getNumDefByNumKey(desc.key, matItemData[desc.key]) }}
						</span>
					</el-descriptions-item>
				</el-descriptions>
			</el-scrollbar>
		</div>
		<div class="drawer-column right" style="width: calc(100% - 310px)">
			<div class="rows">
				<Title :title="extTitleConf">
					<Tabs
						style="margin-right: auto"
						:tabs="['分配使用人', '分配记录', '相关附件']"
						@on-tab-change="handleTabChange"
					/>
				</Title>

				<pitaya-table
					v-if="activatedTab === 0"
					ref="tableRef"
					:columns="tableProp"
					:table-data="tableData"
					:need-selection="false"
					:single-select="false"
					:need-index="true"
					:need-pagination="false"
					:total="pageTotal"
					:table-loading="tableLoading"
					:max-height="maxTableHeight"
					@on-selection-change="selectedTableList = $event"
					@on-current-page-change="onCurrentPageChange"
				>
					<!-- 性别 -->
					<template #sex="{ rowData }">
						{{ rowData.sex == 1 ? "男" : "女" }}
					</template>

					<!-- 分配数量 -->
					<template #num="{ rowData }">
						<el-input
							class="no-arrows"
							v-model="rowData.num"
							@click.stop
							@input="rowData.num = validateAndCorrectInput($event)"
							@change="validateNum(rowData)"
						/>
					</template>

					<!-- 存放位置 -->
					<template #storeLocation="{ rowData }">
						<el-input
							v-model="rowData.storeLocation"
							type="text"
							:maxlength="inputMaxLength.input"
							show-word-limit
							@click.stop
							@change="handleInputBlur(rowData)"
						/>
					</template>

					<template #actions="{ rowData }">
						<el-button v-btn link @click.stop="handleDelUser(rowData)">
							<font-awesome-icon
								:icon="['fas', 'trash-can']"
								style="color: var(--pitaya-btn-background)"
							/>
							<span class="table-inner-btn">移除</span>
						</el-button>
					</template>
					<template #footerOperateLeft>
						<button-list
							:is-not-radius="true"
							:button="tbBtnConf"
							:loading="formBtnLoading"
							@on-btn-click="userVisible = true"
						/>
					</template>
				</pitaya-table>

				<allocationTable
					v-else-if="activatedTab === 1"
					:applyItemId="props.applyItemId"
				/>

				<table-file
					v-else
					:business-type="fileBusinessType.requisitionApplyAllocation"
					:business-id="props.applyItemId"
					:mod="IModalType.edit"
				/>
			</div>

			<button-list
				class="footer"
				:loading="formBtnLoading"
				:button="btnConf"
				@on-btn-click="handleBtnClick"
			/>
		</div>
	</div>

	<Drawer v-model:drawer="userVisible" :size="modalSize.md" destroy-on-close>
		<user-selector
			:tableFetchParams="{
				applyItemId: props.applyItemId
			}"
			:table-api="listOrgUserSelect"
			:tableProps="userSelectorTableProps"
			:multiple="true"
			@save="handleSaveUser"
			@close="userVisible = false"
		/>
	</Drawer>
</template>

<script setup lang="ts">
import { DictApi, fileBusinessType } from "@/app/baseline/api/dict"
import dictTag from "@/app/baseline/views/components/dictTag.vue"
import { inputMaxLength, modalSize } from "@/app/baseline/utils/layout-config"
import {
	batchFormatterNumView,
	getNumDefByNumKey,
	maxTableHeight
} from "@/app/baseline/utils"
import { useTbInit } from "../../components/tableBase"
import GridPanel from "../../store/components/gridPanel.vue"
import userSelector from "../../store/components/userSelector.vue"
import { map, round, sumBy, toNumber, findIndex } from "lodash-es"
import {
	MatLowValueAllocationVO,
	MatLowValueApplyItemVO
} from "@/app/baseline/utils/types/lowValue-requisition-apply"
import {
	listUseAllocation,
	listOrgUserSelect,
	addOrgUserSelect,
	delUseAllocation,
	addUseAllocation,
	checkUseAllocation
} from "@/app/baseline/api/lowValue/requisitionApply"
import { useMessageBoxInit } from "../../components/messageBox"
import {
	getIdempotentToken,
	validateAndCorrectInput
} from "@/app/baseline/utils/validate"
import {
	IIdempotentTokenType,
	IIdempotentTokenTypePre,
	IModalType
} from "@/app/baseline/utils/types/common"
import TableFile from "../../components/tableFile.vue"
import allocationTable from "./allocationTable.vue"

const props = defineProps<{
	/**
	 * 领用物资明细id
	 */
	applyItemId: number
	row: MatLowValueApplyItemVO

	/**
	 * 主表详情
	 */
	descData: Record<string, any>
}>()

const emit = defineEmits<{
	(e: "close"): void
	(e: "update"): void
}>()

const tbBtnConf = [
	{
		name: "添加使用人",
		icon: ["fas", "circle-plus"]
	}
]

const formBtnLoading = ref(false)

const { showDelConfirm, showWarnConfirm } = useMessageBoxInit()

/**
 * 可分配 & 已分配
 */
const gridPanelOptions = computed(() => {
	return [
		{
			label: "可分配",
			value: parseInt(canAllocationNum.value as any) || 0
		}
		/* {
			label: "已分配",
			value: parseInt(allocationNum.value as any) || 0
		} */
	]
})

/**
 * 计算 可分配 数量
 */
const canAllocationNum = computed(() => {
	if (tableData.value.length > 0) {
		const total = sumBy(tableData.value, function (item) {
			return toNumber(item.num)
		})

		if (matItemData.value.canAllocationNum - total >= 0) {
			return matItemData.value.canAllocationNum - total
		} else {
			return 0
		}
	} else {
		return matItemData.value.canAllocationNum ?? 0
	}
})

/**
 * 计算 已分配 数量
 */
const allocationNum = computed(() => {
	if (tableData.value.length > 0) {
		return sumBy(tableData.value, function (item) {
			return toNumber(item.canAllocationNum)
		})
	} else {
		return matItemData.value.allocationNum
	}
})

const descOptions = [
	{ label: "低值类型", key: "lowValueType_view" },
	{ label: "物资编码", key: "materialCode" },
	{ label: "物资名称", key: "materialLabel" },
	{ label: "物资分类编码", key: "materialTypeCode" },
	{ label: "物资分类名称", key: "materialTypeLabel" },
	{ label: "规格型号", key: "version" },
	{ label: "物资性质", key: "attribute" },
	{ label: "领用数量", key: "num_view" },
	{ label: "已交旧数量", key: "wasteOldNum_view" },
	{ label: "已归还数量", key: "returnNum_view" },
	{ label: "冻结量", key: "allocationFreezeNum_view" },
	{ label: "领用人", key: "createdBy_view" },
	{ label: "领用部门", key: "sysOrgId_view" },
	{ label: "申请时间", key: "createdDate" }
]

const userVisible = ref(false)

const drawerLoading = ref(false)

const titleConf = {
	name: ["分配物资信息"],
	icon: ["fas", "square-share-nodes"]
}

const extTitleConf = {
	name: ["扩展信息"],
	icon: ["fas", "square-share-nodes"]
}

const btnConf = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "保存",
		icon: ["fas", "circle-check"]
	}
]

const matItemData = ref<any>({})

const activatedTab = ref(0)
const handleTabChange = (index: number) => {
	activatedTab.value = index
}

const {
	currentPage,
	pageSize,
	tableCache,
	fetchParam,
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	selectedTableList,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = useTbInit<MatLowValueAllocationVO, Record<string, any>>()

fetchFunc.value = listUseAllocation

tableProp.value = [
	{ prop: "realname", label: "使用人", fixed: "left", width: 120 },
	{ prop: "username", label: "账号", width: 120 },
	{ prop: "sex", label: "性别", needSlot: true, width: 100 },
	{ prop: "phone", label: "手机号", width: 120 },
	{ prop: "sysOrgId_view", label: "部门", minWidth: 120 },
	{ prop: "wasteOldNum_view", label: "已交旧数量", align: "right", width: 120 },
	{ prop: "returnNum_view", label: "已归还数量", align: "right", width: 120 },
	{ prop: "freezeNum_view", label: "冻结量", align: "right", width: 120 },
	{
		prop: "num",
		label: "可分配数量",
		needSlot: true,
		width: 120,
		fixed: "right"
	},
	{
		prop: "storeLocation",
		label: "存放位置",
		needSlot: true,
		width: 200,
		fixed: "right"
	},
	{ prop: "actions", label: "操作", width: 100, needSlot: true, fixed: "right" }
]

watch(tableData, () => {
	batchFormatterNumView(tableData.value)
})

const userSelectorTableProps = [
	{
		prop: "username_view",
		label: "姓名"
	},
	{
		prop: "username",
		label: "用户账号"
	},
	{
		prop: "sex",
		label: "性别",
		needSlot: true
	},
	{
		prop: "phone",
		label: "手机号"
	},
	{
		prop: "sysOrgId_view",
		label: "部门"
	}
]

/**
 * 分配数量 > 0
 * @param e
 */
function validateNum(e: any) {
	const num = toNumber(e.num)
	e.num = num

	const totalExcludeSelf = sumBy(tableData.value, (item: any) => {
		return e.id != item.id ? item.num : 0
	})

	// 已交旧数量 + 已归还数量 + 冻结数量
	const useNum = round(
		toNumber(e.wasteOldNum ?? 0) +
			toNumber(e.returnNum ?? 0) +
			toNumber(e.freezeNum ?? 0),
		4
	)
	const oldRow = tableCache.find((r) => r.id == e.id)
	if (num <= 0) {
		e.num = oldRow.canNum
		return ElMessage.warning("分配数量不能小于等于0！")
	} else if (num < useNum) {
		e.num = oldRow.canNum
		return ElMessage.warning(
			"分配数量必须大于已交旧数量 + 已归还数量 + 冻结量！"
		)
	} else if (num > matItemData.value.canAllocationNum - totalExcludeSelf) {
		ElMessage.warning("分配数量不能大于可分配数量！")
		e.num = round(matItemData.value.canAllocationNum - totalExcludeSelf, 4)
	}

	handleInputBlur(e)
}

/**
 * 编辑后的table row 数据
 */
const editedTableRowStack = ref<any[]>([])
/**
 * 输入框失焦 handler
 *
 * 失焦后，记录编辑过的数据，用于保存任务数据
 */
function handleInputBlur(e: any) {
	//  更新 tableCache
	const tableRowIdx = findIndex(tableCache, (v) => v.id === e.id)
	if (tableRowIdx !== -1) {
		tableCache.splice(tableRowIdx, 1, { ...e })
	}
	if (!editedTableRowStack.value.length) {
		editedTableRowStack.value.push({ ...e })
		return
	}
	/**
	 * 队列存在数据，验证重复
	 *
	 * - 不重复->push
	 * - 重复->更新该条数据
	 */
	const rowIdx = findIndex(editedTableRowStack.value, (v) => v.id === e.id)
	if (rowIdx !== -1) {
		editedTableRowStack.value.splice(rowIdx, 1, { ...e })
		return
	}
	editedTableRowStack.value.push({ ...e })
}

/**
 * 翻页 回显已编辑的数据
 */
watch(
	() => tableData.value,
	() => {
		tableData.value.forEach((item) => {
			item.num = item.canNum
		})

		if (editedTableRowStack.value.length) {
			editedTableRowStack.value.map((e) => {
				const rowIdx = findIndex(tableData.value, (v) => v.id === e.id)
				if (rowIdx !== -1) {
					tableData.value.splice(rowIdx, 1, { ...e })
				}
			})
		}
	}
)

onMounted(() => {
	pageSize.value = 9999
	fetchParam.value.applyItemId = props.applyItemId
	fetchParam.value.sidx = "createdDate"
	fetchParam.value.sord = "desc"

	fetchTableData()
	matItemData.value = {
		...props.row,
		createdBy_view: props.descData.createdBy_view,
		sysOrgId_view: props.descData.sysOrgId_view,
		createdDate: props.descData.createdDate
	}
})
/**
 * 按钮操作
 */
async function handleBtnClick(btnName?: string) {
	if (btnName === "保存") {
		formBtnLoading.value = true
		drawerLoading.value = true
		try {
			if (!tableData.value?.length) {
				return ElMessage.warning("请添加使用人")
			}
			// 存放位置不能为空！
			const isStoreLocation = tableData.value.every((v) => {
				return v.storeLocation
			})

			if (!isStoreLocation) {
				return ElMessage.warning("存放位置不能为空！")
			}

			// 分配数量不能小于等于0
			const isNum0 = (tableData.value || []).every((v: any) => {
				return v.num > 0
			})

			if (!isNum0) {
				return ElMessage.warning("分配数量不能小于等于0")
			}

			const formateParams = map(tableData.value, (v) => ({
				allocationId: v.id,
				num: v.num,
				storeLocation: v.storeLocation
			}))

			if (formateParams.length < 1) {
				ElMessage.warning("当前分配列表未修改")
				return false
			}

			if (canAllocationNum.value < 0) {
				ElMessage.warning("分配数量不能大于领用数量")
				return false
			}

			const res = await checkUseAllocation(formateParams as any)

			if (res) {
				await showWarnConfirm(
					`剩余${res}个物资未分配，确定保留在领用人名下吗？`
				)
			}

			await addUseAllocation(formateParams as any)
			ElMessage.success("操作成功")
			emit("update")
		} finally {
			formBtnLoading.value = false
			drawerLoading.value = false
		}
	}
	emit("close")
}
/**
 * 移除 分配人
 */
async function handleDelUser(e?: any) {
	await showDelConfirm()

	await delUseAllocation(e.id)

	ElMessage.success("操作成功")
	await fetchTableData()

	editedTableRowStack.value.map((e) => {
		const rowIdx = findIndex(tableData.value, (v) => v.id == e.id)
		if (rowIdx !== -1) {
			editedTableRowStack.value.splice(rowIdx, 1)
		}
	})
	emit("update")
}

const getTableData = () => {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value
	}
	fetchTableData()
}

/**
 *  保存 添加使用人 操作
 */
async function handleSaveUser(e?: any[], callback?: any) {
	if (e && e.length < 1) {
		callback()
		return ElMessage.warning("请选择使用人")
	}
	const usernameList = map(e, ({ username, sysOrgId }) => {
		return { username, sysOrgId }
	})

	try {
		const idempotentToken = getIdempotentToken(
			IIdempotentTokenTypePre.other,
			IIdempotentTokenType.lowValueUseApply,
			props.row.applyId
		)

		await addOrgUserSelect(
			{
				applyId: props.row.applyId!,
				materialId: props.row.materialId!,
				applyItemId: props.applyItemId,
				usernameList
			},
			idempotentToken
		)

		userVisible.value = false

		getTableData()
	} finally {
		callback()
	}
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
