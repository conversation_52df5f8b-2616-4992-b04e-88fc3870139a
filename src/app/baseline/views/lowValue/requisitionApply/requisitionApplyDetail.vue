<!-- 低值 - 领用申请 - 详情 -->
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<div class="drawer-column left" style="width: 310px">
			<Title :title="titleConf" />
			<el-scrollbar>
				<grid-panel :options="gridPanelOptions" />
				<!-- 领用信息 -->
				<el-descriptions size="small" :column="1" border class="content">
					<el-descriptions-item
						v-for="desc in descOptions"
						:key="desc.label"
						:label="desc.label"
					>
						<span>
							{{
								getNumDefByNumKey(desc.key, descData[desc.key], /Cnt$|Num$/i)
							}}
						</span>
					</el-descriptions-item>
				</el-descriptions>
			</el-scrollbar>
		</div>
		<div
			class="drawer-column right"
			style="width: calc(100% - 310px)"
			:class="{ pdr10: !footerBtnVisible }"
		>
			<div class="rows">
				<Title :title="extTitleConf">
					<Tabs
						style="margin-right: auto"
						:tabs="['领用物资', '相关附件']"
						@on-tab-change="handleTabChange"
					/>
				</Title>

				<Query
					:query-arr-list="(queryConf as any)"
					style="margin: 10px 10px -10px"
					@get-query-data="getTableData"
					v-if="activatedTab === 0"
				/>

				<pitaya-table
					v-if="activatedTab === 0"
					ref="tableRef"
					:columns="(tbInit.tableProp as any)"
					:table-data="tableData"
					:need-selection="descData.status == OutStoreStatus.out ? true : false"
					:single-select="false"
					:need-index="true"
					:need-pagination="true"
					:total="pageTotal"
					:table-loading="tableLoading"
					@on-selection-change="selectedTableList = $event"
					@on-current-page-change="onCurrentPageChange"
				>
					<!-- 物资性质 -->
					<template #attribute="{ rowData }">
						<dict-tag
							:options="DictApi.getMatAttr()"
							:value="rowData.attribute"
						/>
					</template>

					<template #actions="{ rowData }">
						<el-button v-btn link @click="handlSelectUser(rowData)">
							<font-awesome-icon
								:icon="['fas', 'user']"
								style="color: var(--pitaya-btn-background)"
							/>
							<span class="table-inner-btn">分配明细</span>
						</el-button>
					</template>

					<template
						#footerOperateLeft
						v-if="descData.status == OutStoreStatus.out"
					>
						<button-list
							:button="tbBtnConf"
							:is-not-radius="true"
							@on-btn-click="qrCodeVisible = true"
						/>
					</template>
				</pitaya-table>

				<table-file
					v-else
					:business-type="fileBusinessType.requisitionApply"
					:business-id="props.id"
					:mod="
						descData.bpmStatus === appStatus.approved
							? IModalType.edit
							: IModalType.view
					"
				/>
			</div>

			<button-list
				v-if="footerBtnVisible"
				class="footer"
				:button="btnConf"
				@on-btn-click="emit('close')"
			/>
		</div>
	</div>

	<Drawer
		v-model:drawer="selectUserVisible"
		:size="modalSize.lg"
		destroy-on-close
	>
		<user-detail
			:applyItemId="editingTableRow.id"
			:row="editingTableRow"
			:desc-data="descData"
			@close="selectUserVisible = false"
		/>
	</Drawer>

	<Drawer v-model:drawer="qrCodeVisible" :size="modalSize.lg" destroy-on-close>
		<matQrCode
			:tableList="selectedTableList"
			:row="editingTableRow"
			@close="qrCodeVisible = false"
		/>
	</Drawer>
</template>

<script setup lang="ts">
import { IModalType } from "@/app/baseline/utils/types/common"
import { DictApi, appStatus, fileBusinessType } from "@/app/baseline/api/dict"
import DictTag from "../../components/dictTag.vue"
import userDetail from "./userDetail.vue"
import { modalSize } from "@/app/baseline/utils/layout-config"
import TableFile from "../../components/tableFile.vue"
import {
	batchFormatterNumView,
	getNumDefByNumKey,
	tableColFilter,
	toFixedTwo
} from "@/app/baseline/utils"
import { useTbInit } from "../../components/tableBase"
import GridPanel from "../../store/components/gridPanel.vue"
import {
	listUseApplyDetail,
	listUseApplyPageItem
} from "@/app/baseline/api/lowValue/requisitionApply"
import {
	MatLowValueApplyItemVO,
	MatLowValueApplyItemVORequest
} from "@/app/baseline/utils/types/lowValue-requisition-apply"
import { useDictInit } from "../../components/dictBase"
import { OutStoreStatus } from "../../store/outbound/outbound"
import matQrCode from "./matQrCode.vue"

const props = withDefaults(
	defineProps<{
		/**
		 * 领用申请id
		 */
		id?: number
		footerBtnVisible?: boolean
	}>(),
	{ mode: IModalType.view, footerBtnVisible: true }
)

const emit = defineEmits<{
	(e: "close"): void
}>()

const { dictOptions, getDictByCodeList } = useDictInit()

/**
 * 领用物资编码 & 领用物资数量
 */
const gridPanelOptions = computed(() => {
	return [
		{
			label: "领用物资编码",
			value: descData.value.materialCodeNum ?? 0
		},
		{
			label: "领用物资数量",
			value: parseInt(descData.value.useOutNum) || 0
		}
	]
})

const editingTableRow = ref()

const activatedTab = ref(0)

const descOptions = [
	{ label: "领用单号", key: "code" },
	{ label: "领用业务名称", key: "label" },
	{ label: "领用物资编码", key: "materialCodeNum" },
	{ label: "领用物资数量", key: "useOutNum_view" },
	{ label: "费用类别", key: "expenseCategory_view" },
	{ label: "段区", key: "depotId_view" },
	{ label: "线路", key: "lineNoId_view" },
	{ label: "专业", key: "majorId_view" },
	{ label: "出库仓库名称", key: "storeLabel" },
	{ label: "领用部门", key: "sysOrgId_view" },
	{ label: "领用人", key: "createdBy_view" },
	{ label: "申请时间", key: "createdDate" }
]

const selectUserVisible = ref(false)

const drawerLoading = ref(false)

const titleConf = {
	name: ["领用信息"],
	icon: ["fas", "square-share-nodes"]
}

const extTitleConf = {
	name: ["扩展信息"],
	icon: ["fas", "square-share-nodes"]
}

const handleTabChange = (index: number) => {
	activatedTab.value = index

	if (index === 0) {
		fetchParam.value = {
			applyId: props.id || descData.value.id,
			sord: "desc",
			sidx: "createdDate"
		}

		pageSize.value = 20

		getTableData()
	}
}

const btnConf = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	}
]

const descData = ref<any>({})

const queryConf = computed(() => {
	return [
		{
			name: "物资编码",
			key: "materialCode",
			type: "input",
			placeholder: "请输入物资编码"
		},
		{
			name: "物资名称",
			key: "materialLabel",
			type: "input",
			placeholder: "请输入物资名称"
		},
		{
			name: "规格型号",
			key: "version",
			placeholder: "请输入规格型号",
			type: "input"
		},
		{
			name: "物资性质",
			key: "attribute",
			type: "select",
			children: dictOptions.value.MATERIAL_NATURE,
			placeholder: "请选择物资性质"
		}
	]
})

const tbInit = useTbInit<
	MatLowValueApplyItemVO,
	MatLowValueApplyItemVORequest
>()

const {
	currentPage,
	fetchParam,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	pageSize,
	selectedTableList,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = tbInit

fetchFunc.value = listUseApplyPageItem

tbInit.tableProp = computed(() => {
	const defColunmns: TableColumnType[] = [
		{ prop: "materialCode", label: "物资编码", width: 140, fixed: "left" },
		{ prop: "materialLabel", label: "物资名称", minWidth: 120 },
		{ prop: "materialTypeCode", label: "物资分类编码", minWidth: 120 },
		{ prop: "materialTypeLabel", label: "物资分类名称", minWidth: 120 },
		{ prop: "version", label: "规格型号", minWidth: 150 },
		{ prop: "attribute", label: "物资性质", needSlot: true, width: 120 },
		{ prop: "lowValueType_view", label: "低值类型" },
		{ prop: "num_view", label: "领用数量", align: "right" },
		{ prop: "allocationNum_view", label: "已分配", align: "right" },
		{
			prop: "actions",
			label: "操作",
			width: 100,
			needSlot: true,
			fixed: "right"
		}
	]

	if (descData.value.bpmStatus == appStatus.approved) {
		return defColunmns
	} else {
		return tableColFilter(defColunmns, ["操作", "已分配"])
	}
})

watch(tableData, () => {
	batchFormatterNumView(tableData.value)
})

/**
 * table 数据源
 * @param data
 */
const getTableData = (data?: any) => {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()
	fetchParam.value = {
		...fetchParam.value,
		...data
	}

	fetchTableData()
}

const qrCodeVisible = ref(false)
const tbBtnConf = computed(() => {
	return [
		{
			name: "打印标签码",
			icon: ["fas", "qrcode"],
			disabled: selectedTableList.value.length > 0 ? false : true // 生成标签码
		}
	]
})

onMounted(async () => {
	fetchParam.value.sidx = "createdDate"
	fetchParam.value.sord = "desc"

	await getDictByCodeList(["MATERIAL_NATURE"])

	getDetail()
})

function getDetail() {
	if (!props.id) {
		return
	}
	drawerLoading.value = true
	listUseApplyDetail(props.id!)
		.then((r) => {
			descData.value = r
			fetchParam.value.applyId = props.id

			descData.value.useOutNum_view = toFixedTwo(r.useOutNum)
			fetchTableData()
		})
		.finally(() => (drawerLoading.value = false))
}

/**
 * 使用分配
 */
function handlSelectUser(e: MatLowValueApplyItemVO) {
	selectUserVisible.value = true
	editingTableRow.value = { ...e }
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
