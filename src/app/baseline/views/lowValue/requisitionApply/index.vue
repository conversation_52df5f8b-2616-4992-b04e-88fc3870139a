<!-- 领用申请 -->
<script lang="ts" setup>
import {
	checkPermission,
	isCheckPermission
} from "@/app/platform/utils/permission"
import { powerList } from "../../components/define.d"
import { computed, onMounted, ref } from "vue"
import { useMessageBoxInit } from "../../components/messageBox"
import { useTbInit } from "../../components/tableBase"
import { BaseLineSysApi, getTaskByBusinessIds } from "@/app/baseline/api/system"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import { modalSize } from "@/app/baseline/utils/layout-config"
import { IModalType } from "../../../utils/types/common"
import { DictApi } from "@/app/baseline/api/dict"

import requisitionApplyEditor from "./requisitionApplyEditor.vue"
import selectUserEditor from "./selectUserEditor.vue"
import requisitionApplyDetail from "./requisitionApplyDetail.vue"

import { appStatus } from "@/app/baseline/api/dict"
import {
	delUseApplyApply,
	listLowValueUseApplyPage,
	getLowValueApplyStatusCnt
} from "@/app/baseline/api/lowValue/requisitionApply"
import {
	LowValueApplyBpmStatusVo,
	MatLowValueApplyVO,
	MatLowValueApplyVORequest
} from "@/app/baseline/utils/types/lowValue-requisition-apply"
import { listMatStoragePaged } from "@/app/baseline/api/store/manage-api"
import { batchFormatterNumView, tableColFilter } from "@/app/baseline/utils"
import {
	getOutStoreStatus,
	OutStoreStatus
} from "../../store/outbound/outbound"
import { omit } from "lodash-es"
import { useUserStore } from "@/app/platform/store/modules/user"
import { SystemDepotVo } from "@/app/baseline/utils/types/system-depot"
import { hasPermi } from "@/app/baseline/utils"
import ApprovedDrawer from "@/app/baseline/views/components/approvedDrawer.vue"

const { showDelConfirm } = useMessageBoxInit()
const { userInfo } = storeToRefs(useUserStore())

/**
 * 查询条件 配置
 */
const queryArrList = computed(() => {
	return [
		{
			name: "领用单号",
			key: "code",
			placeholder: "请输入领用单号",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "领用业务名称",
			key: "label",
			placeholder: "请输入领用业务名称",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "线路",
			key: "lineNoId",
			type: "select",
			placeholder: "请选择线路",
			children: lineOptions.value
		},
		{
			name: "专业",
			key: "majorId",
			type: "treeSelect",
			treeApi: () => BaseLineSysApi.getProfessionTree(),
			placeholder: "请选择专业"
		},
		{
			name: "段区",
			key: "depotId",
			type: "select",
			children: depotList.value,
			placeholder: "请选择段区"
		},
		{
			name: "出库仓库",
			key: "storeId",
			placeholder: "请选择出库仓库",
			type: "tableSelect",
			tableInfo: [
				{
					title: "请选择出库仓库",
					tableApi: listMatStoragePaged, //表格接口
					tableColumns: [
						{ label: "仓库编码", prop: "code", width: 120 },
						{ label: "仓库名称", prop: "label" },
						{
							label: "仓库级别",
							prop: "level_view",
							width: 100
						},
						{
							label: "仓库类型",
							prop: "type_view",
							width: 120
						},
						{
							label: "所属段区",
							prop: "depotId_view",
							width: 150
						}
					],
					queryArrList: [
						{
							name: "仓库编码",
							key: "code",
							type: "input",
							placeholder: "请输入仓库编码"
						},
						{
							name: "仓库名称",
							key: "label",
							type: "input",
							placeholder: "请输入仓库名称"
						}
					],
					params: {
						currentPage: 1,
						pageSize: 20
					}, //表格接口参数

					labelName: {
						key: "id", //回显标识
						label: "label", //input框要展示的字段
						value: "id" //要给后台传的字段
					}
				}
			]
		},
		{
			name: "领用人",
			key: "realname",
			placeholder: "请输入领用人",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "领用部门",
			key: "sysOrgId",
			type: "treeSelect",
			placeholder: "请选择",
			treeApi: () =>
				BaseLineSysApi.getDepartmentTreeWithCompanyDisabled(
					userInfo.value.companyId
				)
		}
	]
})

/**
 * Title 配置
 */
const rightTitle = {
	name: ["领用申请"],
	icon: ["fas", "square-share-nodes"]
}
const titleBtnConf = [
	{
		name: "新建领用申请",
		roles: powerList.lowValueRequisitionApplyBtnCreate,
		icon: ["fas", "circle-plus"]
	}
]

/**
 * tab 配置项
 */
const statusCnt = ref<LowValueApplyBpmStatusVo>({})
const tabList = computed(() => {
	return [
		{
			name: "草稿箱",
			value: `${appStatus.pendingApproval},${appStatus.rejected}`,
			count: statusCnt.value[0] ?? 0
		},
		{
			name: "审批中",
			value: appStatus.underApproval,
			count: statusCnt.value[1] ?? 0
		},
		{
			name: "已审批",
			value: appStatus.approved,
			count: statusCnt.value[2] ?? 0
		}
	]
})
const tabStatus = ref(tabList.value[0].value)
const activeName = ref(tabList.value[0].name)
const handleTabsClick = async (tab: any) => {
	activeName.value = tab.paneName
	tabStatus.value = tabList.value[tab.index].value as any
	getTableData()
}
const lineOptions = ref([])

const depotList = ref<SystemDepotVo[]>([])

const tbInit = useTbInit<MatLowValueApplyVO, MatLowValueApplyVORequest>()
const {
	tableData,
	tableRef,
	tableLoading,
	currentPage,
	pageTotal,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = tbInit

/**
 * table 列配置
 */
tbInit.tableProp = computed(() => {
	const defColunmns: TableColumnType[] = [
		{ label: "领用单号", prop: "code", width: 180, fixed: "left" },
		{ label: "领用业务名称", prop: "label", minWidth: 130 },
		{ prop: "depotId_view", label: "段区" },
		{ prop: "lineNoId_view", label: "线路", width: 70 },
		{ prop: "majorId_view", label: "专业", width: 120 },
		{ label: "出库仓库名称", prop: "storeLabel", minWidth: 150 },
		{ label: "费用类别", prop: "expenseCategory_view", width: 120 },
		{ label: "负责人", prop: "pickUserId_view", minWidth: 120 },
		{ label: "领用物资编码", prop: "materialCodeNum", width: 120 },
		{
			label: "领用物资数量",
			prop: "useOutNum_view",
			align: "right",
			width: 120
		},
		{ label: "审批状态", prop: "bpmStatus", needSlot: true, width: 120 },
		{ label: "出库状态", prop: "status", needSlot: true, width: 120 },
		{
			label: "分配情况",
			prop: "allocationNum_view",
			needSlot: true,
			minWidth: 120
		},
		{ label: "领用部门", prop: "sysOrgId_view" },
		{ label: "领用人", prop: "createdBy_view", minWidth: 120 },
		{ label: "申请时间", prop: "createdDate", width: 150, sortable: true },
		{
			label: "操作",
			width: 200,
			prop: "operations",
			needSlot: true,
			fixed: "right"
		}
	]

	if (tabStatus.value == appStatus.approved) {
		return defColunmns
	} else {
		return tableColFilter(defColunmns, ["分配情况", "出库状态"])
	}
})

fetchFunc.value = listLowValueUseApplyPage

watch(tableData, () => {
	batchFormatterNumView(tableData.value)
})

/**
 * tab 切换数据源
 */
const getTableData = (data?: any) => {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		sidx: "createdDate",
		sord: "desc",
		...fetchParam.value,
		bpmStatus: tabStatus.value,
		...data
	}
	handleUpdate()
}

const curRowId = ref<any>("")
const curRowData = ref<MatLowValueApplyVO>({})
const viewVisible = ref<boolean>(false)
const editorVisible = ref(false)
const editorMode = ref(IModalType.edit)
const approvedVisible = ref(false)
const editTask = ref<Record<string, any>>()

/**
 * 新建 操作
 */
const handleClickAddBtn = () => {
	editorMode.value = IModalType.create
	editorVisible.value = true
	curRowId.value = ""
	curRowData.value = {}
}

/**
 * 编辑 操作
 * @param row
 */
const onRowEdit = (row: MatLowValueApplyVO) => {
	curRowId.value = row.id
	curRowData.value = row
	editorVisible.value = true
	editorMode.value = IModalType.edit
}

/**
 * 查看
 * @param row
 */
const onRowView = async (row: MatLowValueApplyVO) => {
	curRowId.value = row.id
	curRowData.value = row
	/* viewVisible.value = true */
	editorMode.value = IModalType.view
	if (row?.bpmStatus == appStatus.pendingApproval) {
		viewVisible.value = true
	} else {
		const res = await getTaskByBusinessIds({
			businessIds: [row?.id],
			camundaKey: "low_value_apply"
		})

		if (Array.isArray(res) && res.length > 0) {
			editTask.value = { ...res[res.length - 1] }
			approvedVisible.value = true
		} else {
			viewVisible.value = true
		}
	}
}

/**
 * 删除 操作
 * @param row
 */
const onRowDel = async (row: MatLowValueApplyVO) => {
	await showDelConfirm()
	await delUseApplyApply(row.id)
	ElMessage.success("操作成功")
	handleUpdate()
}

const selectUserVisible = ref(false)
const onRowUser = (row: MatLowValueApplyVO) => {
	curRowId.value = row.id
	curRowData.value = row
	selectUserVisible.value = true
}

/**
 * 更新主表/statusCnt
 */
const handleUpdate = async () => {
	fetchTableData()
	statusCnt.value = await getLowValueApplyStatusCnt(
		omit(
			{
				...fetchParam.value
			},
			"sidx",
			"sord",
			"bpmStatus",
			"currentPage",
			"pageSize"
		) as any
	)
}
/**
 * 获取线路配置
 */
function getLineOptions() {
	BaseLineSysApi.getLineOptions().then((r) => (lineOptions.value = r))
}

/**
 * 获取段区配置
 */
function getDepotList() {
	BaseLineSysApi.getDepotList().then((res) => {
		depotList.value = res
	})
}

onMounted(async () => {
	getTableData()
	getLineOptions()
	getDepotList()
})

/**
 * 排序
 * @param column
 * @param prop 字段名称
 * @param order ascending 升序 descending 降序
 */
function handleSortChange(column: any, prop: any, order: any) {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		sord:
			order === "descending" ? "desc" : order === "ascending" ? "asc" : "desc",
		sidx: order ? prop : "createdDate" // 排序字段
	}

	fetchTableData()
}
</script>
<template>
	<div class="app-container">
		<div class="whole-frame">
			<ModelFrame class="top-frame">
				<Query
					:queryArrList="queryArrList"
					@getQueryData="getTableData"
					class="ml10"
				/>
			</ModelFrame>

			<ModelFrame class="bottom-frame">
				<Title
					:title="rightTitle"
					:button="(titleBtnConf as any)"
					@on-btn-click="handleClickAddBtn"
				>
					<div class="app-tabs-wrapper">
						<el-tabs v-model="activeName" @tab-click="handleTabsClick">
							<el-tab-pane
								v-for="(tab, index) in tabList"
								:key="index"
								:label="`${tab.name}（${tab.count ?? 0}）`"
								:name="tab.name"
								:index="tab.name"
							/>
						</el-tabs>
					</div>
				</Title>

				<pitaya-table
					ref="tableRef"
					:columns="(tbInit.tableProp as any)"
					:tableData="tableData"
					:total="pageTotal"
					:need-index="true"
					@on-current-page-change="onCurrentPageChange"
					:table-loading="tableLoading"
					@on-table-sort-change="handleSortChange"
				>
					<!-- 审批状态 -->
					<template #bpmStatus="{ rowData }">
						<dict-tag
							:options="DictApi.getBpmStatus()"
							:value="rowData.bpmStatus"
						/>
					</template>

					<!-- 出库状态 -->
					<template #status="{ rowData }">
						<dict-tag :options="getOutStoreStatus()" :value="rowData.status" />
					</template>

					<!-- 分配状态 已分配/领用数量 -->
					<template #allocationNum_view="{ rowData }">
						{{ `${rowData.allocationNum_view} / ${rowData.useOutNum_view}` }}
					</template>

					<template #operations="{ rowData }">
						<slot
							v-if="
								(tabStatus == tabList[0].value &&
									isCheckPermission(
										powerList.lowValueRequisitionApplyBtnEdit
									) &&
									hasPermi(rowData.createdBy)) ||
								(tabStatus == tabList[2].value &&
									isCheckPermission(
										powerList.lowValueRequisitionApplyBtnDistribution
									)) ||
								isCheckPermission(
									powerList.lowValueRequisitionApplyBtnPreview
								) ||
								(tabStatus == tabList[0].value &&
									isCheckPermission(
										powerList.lowValueRequisitionApplyBtnDrop
									) &&
									hasPermi(rowData.createdBy))
							"
						>
							<el-button
								v-btn
								link
								@click="onRowEdit(rowData)"
								v-if="
									tabStatus == tabList[0].value &&
									isCheckPermission(
										powerList.lowValueRequisitionApplyBtnEdit
									) &&
									hasPermi(rowData.createdBy)
								"
								:disabled="
									checkPermission(powerList.lowValueRequisitionApplyBtnEdit)
								"
							>
								<font-awesome-icon :icon="['fas', 'pen-to-square']" />
								<span class="table-inner-btn">编辑</span>
							</el-button>

							<!-- checkedPlan == 1 即盘点中 不可分配 -->

							<el-button
								v-btn
								link
								@click="onRowUser(rowData)"
								v-if="
									tabStatus == tabList[2].value &&
									isCheckPermission(
										powerList.lowValueRequisitionApplyBtnDistribution
									)
								"
								:disabled="
									rowData.status == OutStoreStatus.noOut ||
									rowData.status == OutStoreStatus.close ||
									rowData.checkedPlan == 1 ||
									rowData.canAllocationNum <= 0 ||
									checkPermission(
										powerList.lowValueRequisitionApplyBtnDistribution
									)
								"
							>
								<font-awesome-icon :icon="['fas', 'user']" />
								<span class="table-inner-btn">分配</span>
							</el-button>
							<el-button
								v-btn
								link
								@click="onRowView(rowData)"
								v-if="
									isCheckPermission(
										powerList.lowValueRequisitionApplyBtnPreview
									)
								"
								:disabled="
									checkPermission(powerList.lowValueRequisitionApplyBtnPreview)
								"
							>
								<font-awesome-icon :icon="['fas', 'eye']" />
								<span class="table-inner-btn">查看</span>
							</el-button>
							<el-button
								v-btn
								link
								@click="onRowDel(rowData)"
								v-if="
									tabStatus == tabList[0].value &&
									isCheckPermission(
										powerList.lowValueRequisitionApplyBtnDrop
									) &&
									hasPermi(rowData.createdBy)
								"
								:disabled="
									checkPermission(powerList.lowValueRequisitionApplyBtnDrop)
								"
							>
								<font-awesome-icon :icon="['fas', 'trash-can']" />
								<span class="table-inner-btn">移除</span>
							</el-button>
						</slot>
						<slot v-else>---</slot>
					</template>
				</pitaya-table>

				<!-- 编辑/新建  -->
				<Drawer
					:size="modalSize.xl"
					v-model:drawer="editorVisible"
					:destroyOnClose="true"
				>
					<requisition-apply-editor
						:id="curRowId"
						:mode="editorMode"
						@close="editorVisible = false"
						@save="handleUpdate"
						@update="fetchTableData"
					/>
				</Drawer>

				<!-- 查看  -->
				<Drawer
					:size="modalSize.xl"
					v-model:drawer="viewVisible"
					:destroyOnClose="true"
				>
					<requisition-apply-detail
						:id="curRowId"
						:mode="editorMode"
						@close="viewVisible = false"
					/>
				</Drawer>

				<!-- 分配  -->
				<Drawer
					:size="modalSize.xl"
					v-model:drawer="selectUserVisible"
					:destroyOnClose="true"
				>
					<select-user-editor
						:id="curRowId"
						@update="handleUpdate"
						@close="selectUserVisible = false"
					/>
				</Drawer>

				<!-- 审批、查看弹窗 -->
				<Drawer
					:size="modalSize.xxl"
					v-model:drawer="approvedVisible"
					:destroyOnClose="true"
				>
					<approved-drawer
						:mod="editorMode"
						:task-value="editTask"
						@on-save-or-close="approvedVisible = false"
					/>
				</Drawer>
			</ModelFrame>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
