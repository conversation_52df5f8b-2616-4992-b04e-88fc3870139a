<!-- 低值台账 - 详情 - 分配记录 -->
<template>
	<pitaya-table
		ref="tableRef"
		:columns="tableProp"
		:table-data="tableData"
		:need-index="true"
		:single-select="false"
		:need-pagination="true"
		:total="pageTotal"
		:table-loading="tableLoading"
		:max-height="maxTableHeight"
		@on-selection-change="selectedTableList = $event"
		@on-current-page-change="onCurrentPageChange"
		@on-table-sort-change="handleSortChange"
	>
		<!-- 业务 -->
		<template #code="{ rowData }">
			<link-tag
				:value="rowData.code"
				@on-click="showBusinessComponent(rowData)"
			/>
		</template>

		<!-- 分配数量 -->
		<template #num="{ rowData }">
			{{ toFixedTwo(rowData.num) }}
		</template>
	</pitaya-table>

	<!-- 业务组件详情 -->
	<Drawer
		v-model:drawer="businessComponentVisible"
		:size="modalSize.xl"
		destroy-on-close
	>
		<component
			:is="businessComponent"
			:id="editingTableRow?.applyId"
			@close="businessComponentVisible = false"
		/>
	</Drawer>
</template>
<script lang="ts" setup>
import { modalSize } from "@/app/baseline/utils/layout-config"
import { listMatLowValueBookPageDetailAllocation } from "@/app/baseline/api/lowValue/ledger"
import {
	IMatLowValueBookDetailVOSourceType,
	MatLowValueAllocationVO,
	MatLowValueBookDetailVORequest
} from "@/app/baseline/utils/types/lowValue-ledger"
import { useTbInit } from "../../components/tableBase"
import { maxTableHeight, toFixedTwo } from "@/app/baseline/utils"
import linkTag from "@/app/baseline/views/components/linkTag.vue"
import { IInventoryBusinessType } from "@/app/baseline/utils/types/store-inventory"
import businessComponentMap from "../../store/business/inventory/business-component-map"

const props = defineProps<{
	id: any // 物资Id
	sourceTypes: string | IMatLowValueBookDetailVOSourceType
}>()

const {
	currentPage,
	tableData,
	tableRef,
	tableProp,
	tableLoading,
	pageTotal,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = useTbInit<
	MatLowValueAllocationVO,
	Omit<MatLowValueBookDetailVORequest, "sourceTypes">
>()

fetchFunc.value = listMatLowValueBookPageDetailAllocation

tableProp.value = [
	{ prop: "createdDate", label: "分配时间", sortable: true },
	{ prop: "code", label: "领用单号", width: 180, needSlot: true },
	{ prop: "realname", label: "使用人" },
	{ prop: "username", label: "账号" },
	{ prop: "phone", label: "手机号" },
	{ prop: "sysOrgId_view", label: "部门" },
	{ prop: "num", label: "分配数量", needSlot: true, align: "right" },
	{ prop: "storeLocation", label: "存放位置" }
]

const editingTableRow = ref<MatLowValueAllocationVO>()

/**
 * 业务组件
 *
 * 根据不同事务类型，展示事务的前置业务
 */
const businessComponent = computed(() => {
	return businessComponentMap[
		editingTableRow.value?.applyType as IInventoryBusinessType
	]
})
const businessComponentVisible = ref(false)
function showBusinessComponent(e?: MatLowValueAllocationVO) {
	businessComponentVisible.value = true
	editingTableRow.value = e
}

/**
 * 排序
 * @param column
 * @param prop 字段名称
 * @param order ascending 升序 descending 降序
 */
function handleSortChange(column: any, prop: any, order: any) {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		sord:
			order === "descending" ? "desc" : order === "ascending" ? "asc" : "desc",
		sidx: order ? prop : "createdDate" // 排序字段
	}

	fetchTableData()
}

onMounted(() => {
	fetchParam.value.sourceTypes = props.sourceTypes
	fetchParam.value.materialId = props.id
	fetchParam.value.sord = "desc"
	fetchParam.value.sidx = "createdDate"

	fetchTableData()
})
</script>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
