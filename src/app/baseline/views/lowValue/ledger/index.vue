<!-- 低值物资台账 -->
<script lang="ts" setup>
import {
	checkPermission,
	isCheckPermission
} from "@/app/platform/utils/permission"
import { powerList } from "../../components/define.d"
import { computed, onMounted, ref } from "vue"
import { useDictInit } from "../../components/dictBase"
import { useTbInit } from "../../components/tableBase"
import costTag from "@/app/baseline/views/components/costTag.vue"
import dictTag from "@/app/baseline/views/components/dictTag.vue"
import linkTag from "@/app/baseline/views/components/linkTag.vue"
import { modalSize } from "@/app/baseline/utils/layout-config"
import { IModalType } from "../../../utils/types/common"
import { DictApi } from "@/app/baseline/api/dict"
import { listMatLowValueBookPage } from "@/app/baseline/api/lowValue/ledger"
import {
	MatLowValueBookVO,
	MatLowValueBookVORequest
} from "@/app/baseline/utils/types/lowValue-ledger"
import ledgerDetail from "./ledgerDetail.vue"
import { batchFormatterNumView } from "@/app/baseline/utils"
import freezeTable from "./freezeTable.vue"

const { dictOptions, getDictByCodeList, dictFilter } = useDictInit()

/**
 * 查询条件 配置
 */
const queryArrList = computed(() => {
	return [
		{
			name: "物资编码",
			key: "materialCode",
			placeholder: "请输入物资编码",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "物资名称",
			key: "materialLabel",
			placeholder: "请输入物资名称",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "规格型号",
			key: "version",
			placeholder: "请输入规格型号",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "物资性质",
			key: "attribute",
			type: "select",
			children: dictOptions.value.MATERIAL_NATURE,
			placeholder: "请选择物资性质"
		},
		{
			name: "物资分类编码",
			key: "materialTypeCode",
			placeholder: "请输入物资分类编码",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "物资分类名称",
			key: "materialTypeLabel",
			placeholder: "请输入物资分类名称",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "低值类型",
			key: "lowValueType",
			placeholder: "请选择低值类型",
			type: "select",
			children: dictOptions.value["LOW_VALUE_TYPE"]
		}
	]
})

/**
 * Title 配置
 */
const rightTitle = {
	name: ["低值台账"],
	icon: ["fas", "square-share-nodes"]
}

const {
	tableData,
	tableProp,
	tableRef,
	tableLoading,
	currentPage,
	pageTotal,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = useTbInit<MatLowValueBookVO, MatLowValueBookVORequest>()

/**
 * table 列配置
 */
tableProp.value = [
	{
		label: "物资编码",
		prop: "materialCode",
		width: 130,
		fixed: "left",
		sortable: true
	},
	{ label: "物资名称", prop: "materialLabel" },
	{ label: "物资分类编码", prop: "materialTypeCode", width: 130 },
	{ label: "物资分类名称", prop: "materialTypeLabel", width: 130 },
	{ label: "规格型号", prop: "version" },
	{ label: "技术参数", prop: "technicalParameter", minWidth: 120 },
	{ label: "物资性质", prop: "attribute", needSlot: true, width: 120 },
	{ label: "低值类型", prop: "lowValueType_view" },
	{ label: "库存单位", prop: "useUnit", needSlot: true },
	{
		label: "标准成本(元)",
		prop: "standardCost",
		needSlot: true,
		align: "right",
		width: 120
	},
	{ label: "台账数量", prop: "bookNum_view", align: "right" },
	{ label: "已领用数量", prop: "useNum_view", align: "right" },
	{ label: "可领用数量", prop: "canUseNum_view", align: "right" },
	{ label: "冻结量", prop: "freezeNum_view", needSlot: true, align: "right" },
	{
		label: "操作",
		width: 150,
		prop: "operations",
		needSlot: true,
		fixed: "right"
	}
]

/**
 * 数值格式化数量
 */
watch(tableData, () => {
	batchFormatterNumView(tableData.value)
})

fetchFunc.value = listMatLowValueBookPage

const getTableData = (data?: any) => {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		...data
	}
	fetchTableData()
}

const curRowId = ref<any>("")
const curRowData = ref<MatLowValueBookVO>({})
const viewVisible = ref<boolean>(false)
const editorMode = ref(IModalType.edit)

/**
 * 查看
 * @param row
 */
const onRowView = (row: MatLowValueBookVO) => {
	curRowId.value = row.materialId
	curRowData.value = row
	viewVisible.value = true
	editorMode.value = IModalType.view
}

/**
 * 查看 冻结数量
 */
const freezeNumVisible = ref(false)
function showFreezeNumDetail(e: any) {
	freezeNumVisible.value = true
	curRowId.value = e.materialId
}

onMounted(() => {
	fetchParam.value.sord = "asc"
	fetchParam.value.sidx = "code"
	getDictByCodeList(["LOW_VALUE_TYPE", "INVENTORY_UNIT", "MATERIAL_NATURE"])
	getTableData()
})
/**
 * 排序
 * @param column
 * @param prop 字段名称
 * @param order ascending 升序 descending 降序
 */
function handleSortChange(column: any, prop: any, order: any) {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		sord:
			order === "descending" ? "desc" : order === "ascending" ? "asc" : "asc",
		sidx: order ? (prop === "materialCode" ? "code" : prop) : "code" // 排序字段
	}

	fetchTableData()
}
</script>
<template>
	<div class="app-container">
		<div class="whole-frame">
			<ModelFrame class="top-frame">
				<Query
					:queryArrList="queryArrList"
					@getQueryData="getTableData"
					class="ml10"
				/>
			</ModelFrame>

			<ModelFrame class="bottom-frame">
				<Title :title="rightTitle" />

				<pitaya-table
					ref="tableRef"
					:columns="tableProp"
					:tableData="tableData"
					:total="pageTotal"
					:need-index="true"
					@on-current-page-change="onCurrentPageChange"
					:table-loading="tableLoading"
					@on-table-sort-change="handleSortChange"
				>
					<!-- 物资性质 -->
					<template #attribute="{ rowData }">
						<dict-tag
							:options="DictApi.getMatAttr()"
							:value="rowData.attribute"
						/>
					</template>

					<!-- 库存单位 -->
					<template #useUnit="{ rowData }">
						{{
							dictFilter("INVENTORY_UNIT", rowData.useUnit)?.subitemName ||
							"---"
						}}
					</template>

					<!-- 标准成本 -->
					<template #standardCost="{ rowData }">
						<cost-tag :value="rowData.standardCost" />
					</template>

					<!-- freezeNum 冻结数量 -->
					<template #freezeNum_view="{ rowData }">
						<link-tag
							:value="rowData.freezeNum_view"
							@on-click="showFreezeNumDetail(rowData)"
						/>
					</template>

					<template #operations="{ rowData }">
						<slot v-if="isCheckPermission(powerList.lowValueLedgerBtnPreview)">
							<el-button
								v-btn
								link
								@click="onRowView(rowData)"
								v-if="isCheckPermission(powerList.lowValueLedgerBtnPreview)"
								:disabled="checkPermission(powerList.lowValueLedgerBtnPreview)"
							>
								<font-awesome-icon :icon="['fas', 'eye']" />
								<span class="table-inner-btn">查看</span>
							</el-button>
						</slot>
						<slot v-else>---</slot>
					</template>
				</pitaya-table>

				<!-- 查看  -->
				<Drawer
					:size="modalSize.xxl"
					v-model:drawer="viewVisible"
					:destroyOnClose="true"
				>
					<ledger-detail
						:row="curRowData"
						:id="curRowId"
						@close="viewVisible = false"
					/>
				</Drawer>

				<Drawer
					v-model:drawer="freezeNumVisible"
					:size="modalSize.xl"
					destroy-on-close
				>
					<freeze-table :id="curRowId" @close="freezeNumVisible = false" />
				</Drawer>
			</ModelFrame>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
