<!-- 冻结信息 -->
<template>
	<div class="drawer-container">
		<div class="drawer-column" style="width: 100%">
			<Title :title="titleConf" />

			<el-scrollbar class="rows">
				<!-- 明细信息 -->
				<pitaya-table
					ref="tableRef"
					:columns="tableProp"
					:table-data="tableData"
					:need-selection="false"
					:single-select="false"
					:need-index="true"
					:need-pagination="true"
					:total="pageTotal"
					:table-loading="tableLoading"
					:max-height="maxTableHeight"
					@on-selection-change="selectedTableList = $event"
					@on-current-page-change="onCurrentPageChange"
				>
					<template #code="{ rowData }">
						<link-tag
							:value="rowData.code"
							@on-click="showBusinessComponent(rowData)"
						/>
					</template>

					<!-- 冻结数量 -->
					<template #freezeNum="{ rowData }">
						{{ toFixedTwo(rowData.freezeNum) }}
					</template>
				</pitaya-table>
			</el-scrollbar>

			<button-list
				class="footer"
				:button="btnConf"
				@on-btn-click="emit('close')"
			/>
		</div>

		<!-- 业务组件详情 -->
		<Drawer
			v-model:drawer="businessComponentVisible"
			:size="modalSize.lg"
			destroy-on-close
		>
			<component
				:is="businessComponent"
				:id="editingTableRow?.businessId"
				@close="businessComponentVisible = false"
			/>
		</Drawer>
	</div>
</template>

<script setup lang="ts">
import { useTbInit } from "../../components/tableBase"
import LinkTag from "../../components/linkTag.vue"
import businessComponentMap from "../../store/business/inventory/business-component-map"
import {
	IInventoryBusinessType,
	MatStoreFreezeVo
} from "@/app/baseline/utils/types/store-inventory"
import { modalSize } from "@/app/baseline/utils/layout-config"
import { toFixedTwo, maxTableHeight } from "@/app/baseline/utils"
import { listMatLowValueBookFreezePaged } from "@/app/baseline/api/lowValue/ledger"

const props = defineProps<{
	/**
	 * 物资id
	 */
	id: any
}>()

const emit = defineEmits<{
	(e: "close"): void
}>()

const titleConf = computed(() => ({
	name: ["冻结信息"],
	icon: ["fas", "square-share-nodes"]
}))

const btnConf = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	}
]

const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = useTbInit()

fetchFunc.value = listMatLowValueBookFreezePaged

tableProp.value = [
	{ label: "业务类型", prop: "type_view", width: 180 },
	{ label: "业务单据号", prop: "code", needSlot: true },
	{ label: "冻结量", prop: "freezeNum", needSlot: true, align: "right" },
	{ label: "申请人", prop: "createdBy_view" },
	{ label: "申请部门", prop: "sysOrgId_view" },
	{ label: "申请时间", prop: "createdDate" }
]

onMounted(() => {
	fetchParam.value = {
		...fetchParam.value,
		sord: "desc",
		sidx: "createdDate",
		materialId: props.id
	}

	fetchTableData()
})

const businessComponentVisible = ref(false)
/**
 * 要查看的当前行 Id
 */
const editingTableRow = ref<MatStoreFreezeVo>()

/**
 * 业务组件
 *
 * 根据不同事务类型，展示事务的当前业务
 */
const businessComponent = computed(() => {
	return businessComponentMap[
		editingTableRow.value?.type as IInventoryBusinessType
	]
})

function showBusinessComponent(e?: MatStoreFreezeVo) {
	businessComponentVisible.value = true
	editingTableRow.value = e
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
