<!-- 低值台账 详情 -->
<script setup lang="ts">
import { computed, onMounted, ref } from "vue"
import { DictApi } from "@/app/baseline/api/dict"
import {
	batchFormatterNumView,
	maxTableHeight,
	getNumDefByNumKey
} from "@/app/baseline/utils"
import { useDictInit } from "../../components/dictBase"
import costTag from "@/app/baseline/views/components/costTag.vue"
import linkTag from "@/app/baseline/views/components/linkTag.vue"
import dictTag from "@/app/baseline/views/components/dictTag.vue"
import { useTbInit } from "../../components/tableBase"
import { listMatLowValueBookPageDetail } from "@/app/baseline/api/lowValue/ledger"
import {
	IMatLowValueBookDetailVOSourceType,
	MatLowValueBookDetailVO,
	MatLowValueBookDetailVORequest,
	MatLowValueBookVO
} from "@/app/baseline/utils/types/lowValue-ledger"
import { modalSize } from "@/app/baseline/utils/layout-config"
import businessComponentMap from "../../store/business/inventory/business-component-map"
import { IInventoryBusinessType } from "@/app/baseline/utils/types/store-inventory"
import GridPanel from "../../store/components/gridPanel.vue"
import ledgerAllocationTable from "./ledgerAllocationTable.vue"
import { getRealLength, setString } from "@/app/baseline/utils/validate"

const { dictFilter, getDictByCodeList } = useDictInit()

const props = withDefaults(
	defineProps<{
		id: any // 物资ID
		row: MatLowValueBookVO
		footerBtnVisible?: boolean
	}>(),
	{ footerBtnVisible: true }
)

const emits = defineEmits<{
	(e: "close"): void
}>()

const drawerLoading = ref(false)

/**
 * 左侧title 配置
 */
const leftTitleConf = computed(() => ({
	name: ["低值物资信息"],
	icon: ["fas", "square-share-nodes"]
}))

const drawerBtnRightConf = computed(() => [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	}
])

/**
 * 右侧 title 配置
 */
const extraTitleConf = {
	name: ["扩展信息"],
	icon: ["fas", "square-share-nodes"]
}

/**
 * 来源类型 0入库 1领用 2归还 3交旧 5退货 6盘盈 7盘亏
 * */
const tabsConf = [
	{ name: "入库记录", value: IMatLowValueBookDetailVOSourceType.toWarehouse },
	{ name: "退货记录", value: IMatLowValueBookDetailVOSourceType.goodsReturn },
	{ name: "领用记录", value: IMatLowValueBookDetailVOSourceType.lowValueUse },
	{
		name: "分配记录",
		value: IMatLowValueBookDetailVOSourceType.lowValueAllocation
	},
	{
		name: "盘点记录",
		value: `${
			IMatLowValueBookDetailVOSourceType.inventoryLoss +
			"," +
			IMatLowValueBookDetailVOSourceType.inventoryProfit
		}`
	},
	{
		name: "归还记录",
		value: IMatLowValueBookDetailVOSourceType.lowValueReturn
	},
	{ name: "交旧记录", value: IMatLowValueBookDetailVOSourceType.wasteHandover },
	{
		name: "调拨记录",
		value: `${
			IMatLowValueBookDetailVOSourceType.transferOut +
			"," +
			IMatLowValueBookDetailVOSourceType.transferIn
		}`
	}
]
/**
 * 左侧表单 配置
 */
const formData = ref<Record<string, any>>({})

/**
 * 台账数量 & 已领用 & 可领用
 */
const gridPanelOptions = computed(() => {
	return [
		{
			label: "台账数量",
			value: parseInt(formData.value.bookNum) || 0
		},
		{
			label: "已领用",
			value: parseInt(formData.value.useNum) || 0
		},
		{
			label: "可领用",
			value: parseInt(formData.value.canUseNum) || 0
		}
	]
})

/**
 * 关联业务单数据
 */
const descList = ref([
	{ label: "物资编码", key: "materialCode" },
	{ label: "物资名称", key: "materialLabel" },
	{ label: "物资分类编码", key: "materialTypeCode" },
	{ label: "物资分类名称", key: "materialTypeLabel" },
	{ label: "规格型号", key: "version" },
	{ label: "技术参数", key: "technicalParameter", needTooltip: true },
	{ label: "物资性质", key: "attribute" }, // 字典
	{ label: "低值类型", key: "lowValueType_view" }, // 字典
	{ label: "库存单位", key: "useUnit" }, // 字典
	{ label: "标准成本(元)", key: "standardCost" }, // 格式化
	{ label: "台账数量", key: "bookNum_view" },
	{ label: "已领用", key: "useNum_view" },
	{ label: "可领用", key: "canUseNum_view" },
	{ label: "冻结量", key: "freezeNum_view" }
])

const tagType = ref<IMatLowValueBookDetailVOSourceType | string>(
	tabsConf[0].value
)
const activeName = ref(tabsConf[0].name)
const handleTabsClick = (tab: any) => {
	activeName.value = tab.paneName
	tagType.value = tabsConf[tab.index].value

	if (tagType.value != IMatLowValueBookDetailVOSourceType.lowValueAllocation) {
		getTableData()
	}
}

const tbInit = useTbInit<
	MatLowValueBookDetailVO,
	MatLowValueBookDetailVORequest
>()
const {
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange,
	currentPage
} = tbInit

fetchFunc.value = listMatLowValueBookPageDetail

tbInit.tableProp = computed(() => {
	let ls: TableColumnType[] = []
	switch (tagType.value) {
		case IMatLowValueBookDetailVOSourceType.toWarehouse:
			// 入库记录
			ls = [
				{ prop: "createdDate", label: "入库时间", sortable: true },
				{ prop: "businessCode", label: "入库单号", width: 180, needSlot: true },
				{
					prop: "preBusinessCode",
					label: "关联业务单号",
					needSlot: true,
					width: 180
				},
				{ prop: "storeLabel", label: "入库仓库名称" },
				{ prop: "roomCode", label: "入库货位编码" },
				{ prop: "num_view", align: "right", label: "入库数量" },
				{ prop: "storeManage", label: "库管员" }
			]
			return ls
		case IMatLowValueBookDetailVOSourceType.goodsReturn:
			// 退货记录
			ls = [
				{ prop: "createdDate", label: "出库时间", sortable: true },
				{ prop: "businessCode", label: "出库单号", width: 180, needSlot: true },
				{
					prop: "preBusinessCode",
					label: "关联业务单号",
					needSlot: true,
					width: 180
				},
				{ prop: "storeLabel", label: "出库仓库名称" },
				{ prop: "roomCode", label: "出库货位编码" },
				{ prop: "num_view", align: "right", label: "出库数量" }
			]
			return ls
		case IMatLowValueBookDetailVOSourceType.lowValueUse:
			// 领用记录
			ls = [
				{ prop: "createdDate", label: "领用时间", sortable: true },
				{ prop: "businessCode", label: "领用单号", width: 180, needSlot: true },
				{ prop: "illustrate", label: "领用用途" },
				{ prop: "num_view", align: "right", label: "领用数量" },
				{ prop: "storeLabel", label: "出库仓库名称" },
				{ prop: "sysOrgId_view", label: "领用部门" },
				{ prop: "createdBy_view", label: "领用人" }
			]
			return ls
		case `${
			IMatLowValueBookDetailVOSourceType.inventoryLoss +
			"," +
			IMatLowValueBookDetailVOSourceType.inventoryProfit
		}`:
			// 盘点记录
			ls = [
				{ prop: "createdDate", label: "出入库时间", sortable: true },
				{ prop: "businessType_view", label: "出入库类型" },
				{
					prop: "businessCode",
					label: "出入库单号",
					width: 180,
					needSlot: true
				},
				{
					prop: "preBusinessCode",
					label: "关联盘点计划",
					needSlot: true,
					width: 180
				},
				{ prop: "storeLabel", label: "出入库仓库名称" },
				{ prop: "roomCode", label: "出入库货位编码" },
				{ prop: "num_view", align: "right", label: "出入库数量" }
			]
			return ls
		case IMatLowValueBookDetailVOSourceType.lowValueReturn:
			// 归还记录
			ls = [
				{ prop: "createdDate", label: "归还时间", sortable: true },
				{
					prop: "businessCode",
					label: "归还业务单号",
					width: 180,
					needSlot: true
				},
				{ prop: "businessLabel", label: "归还业务名称" },
				{
					prop: "preBusinessCode",
					label: "关联领用单",
					needSlot: true,
					width: 180
				},
				{ prop: "username_view", label: "使用人" },
				{ prop: "num_view", align: "right", label: "归还数量" },
				{ prop: "sysOrgId_view", label: "归还部门" },
				{ prop: "createdBy_view", label: "归还人" }
			]
			return ls
		case IMatLowValueBookDetailVOSourceType.wasteHandover:
			// 交旧
			ls = [
				{ prop: "createdDate", label: "交旧申请时间", sortable: true },
				{ prop: "businessCode", label: "交旧单号", width: 180, needSlot: true },
				{ prop: "businessLabel", label: "交旧名称" },
				{
					prop: "preBusinessCode",
					label: "关联业务单号",
					needSlot: true,
					width: 180
				},
				{ prop: "username_view", label: "使用人" },
				{ prop: "num_view", align: "right", label: "交旧数量" },
				{ prop: "sysOrgId_view", label: "交旧申请部门" },
				{ prop: "createdBy_view", label: "申请人" }
			]
			return ls
		case `${
			IMatLowValueBookDetailVOSourceType.transferOut +
			"," +
			IMatLowValueBookDetailVOSourceType.transferIn
		}`:
			// 调拨记录
			ls = [
				{ prop: "createdDate", label: "出入库时间", sortable: true },
				{ prop: "preBusinessType_view", label: "移库类型" },
				{ prop: "businessType_view", label: "出入库类型" },
				{
					prop: "businessCode",
					label: "出入库单号",
					width: 180,
					needSlot: true
				},
				{
					prop: "preBusinessCode",
					label: "关联调拨单号",
					needSlot: true,
					width: 180
				},
				{ prop: "storeLabel", label: "出入库仓库名称", minWidth: 120 },
				{ prop: "num_view", align: "right", label: "出入库数量" }
			]
			return ls
		default:
			return ls
	}
})

const editingTableRow = ref<MatLowValueBookDetailVO>()

/**
 * 业务组件
 *
 * 根据不同事务类型，展示事务的前置业务
 */
const businessComponent = computed(() => {
	return businessComponentMap[
		editingTableRow.value?.businessType as IInventoryBusinessType
	]
})
const businessComponentVisible = ref(false)
function showBusinessComponent(e?: MatLowValueBookDetailVO) {
	businessComponentVisible.value = true
	editingTableRow.value = e
}

/**
 * 关联前置 组件
 */

const businessPreComponentVisible = ref(false)
function showBusinessPreComponent(e?: MatLowValueBookDetailVO) {
	businessPreComponentVisible.value = true
	editingTableRow.value = e
}
const businessPreComponent = computed(() => {
	return businessComponentMap[
		editingTableRow.value?.preBusinessType as IInventoryBusinessType
	]
})

/**
 * tab 切换数据源
 */
const getTableData = (data?: any) => {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		sourceTypes: tagType.value,
		materialId: props.id,
		...data
	}
	fetchTableData()
}

/**
 * 格式化数量
 */
watch(tableData, () => {
	batchFormatterNumView(tableData.value)
})

/**
 * 排序
 * @param column
 * @param prop 字段名称
 * @param order ascending 升序 descending 降序
 */
function handleSortChange(column: any, prop: any, order: any) {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		sord:
			order === "descending" ? "desc" : order === "ascending" ? "asc" : "desc",
		sidx: order ? prop : "createdDate" // 排序字段
	}

	fetchTableData()
}

onMounted(() => {
	formData.value = { ...props.row }
	getDictByCodeList(["LOW_VALUE_TYPE", "INVENTORY_UNIT"])
	if (props.id) {
		fetchParam.value.materialId = props.id
		getTableData()
	}
})
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<div class="drawer-column left" style="width: 380px">
			<Title :title="leftTitleConf" />

			<el-scrollbar>
				<grid-panel :options="gridPanelOptions" />
				<el-descriptions size="small" :column="1" border class="content">
					<template>
						<el-descriptions-item
							v-for="desc in descList"
							:key="desc.key"
							:label="desc.label"
						>
							<span v-if="desc.key === 'applyType'">
								{{
									dictFilter("LOW_VALUE_TYPE", formData?.[desc.key])
										?.subitemName || "---"
								}}
							</span>
							<dict-tag
								v-else-if="desc.key === 'attribute'"
								:options="DictApi.getMatAttr()"
								:value="formData.attribute"
							/>
							<span v-else-if="desc.key === 'useUnit'">
								{{
									dictFilter("INVENTORY_UNIT", formData?.[desc.key])
										?.subitemName || "---"
								}}
							</span>
							<span v-else-if="desc.key === 'standardCost'">
								<cost-tag :value="formData?.[desc.key]" />
							</span>
							<span v-else-if="desc.needTooltip">
								<el-tooltip
									effect="dark"
									:content="formData?.[desc.key]"
									:disabled="
										getRealLength(formData?.[desc.key]) <= 100 ? true : false
									"
								>
									{{
										getRealLength(formData?.[desc.key]) > 100
											? setString(formData?.[desc.key], 100)
											: formData?.[desc.key] || "---"
									}}
								</el-tooltip>
							</span>
							<span v-else>
								{{ getNumDefByNumKey(desc.key, formData?.[desc.key]) }}
							</span>
						</el-descriptions-item>
					</template>
				</el-descriptions>
			</el-scrollbar>
		</div>
		<div class="drawer-column right" style="width: calc(100% - 380px)">
			<Title :title="extraTitleConf">
				<div class="app-tabs-wrapper" style="left: 110px">
					<el-tabs @tab-click="handleTabsClick" v-model="activeName">
						<el-tab-pane
							v-for="(tab, index) in tabsConf"
							:key="index"
							:label="tab.name"
							:name="tab.name"
							:index="tab.name"
						/>
					</el-tabs>
				</div>
			</Title>
			<el-scrollbar class="rows" v-if="activeName != '分配记录'">
				<pitaya-table
					ref="tableRef"
					:columns="(tbInit.tableProp as any)"
					:table-data="tableData"
					:need-index="true"
					:single-select="false"
					:need-pagination="true"
					:total="pageTotal"
					:table-loading="tableLoading"
					:max-height="maxTableHeight"
					@on-selection-change="selectedTableList = $event"
					@on-current-page-change="onCurrentPageChange"
					@on-table-sort-change="handleSortChange"
				>
					<!-- 业务 -->
					<template #businessCode="{ rowData }">
						<link-tag
							:value="rowData.businessCode"
							@on-click="showBusinessComponent(rowData)"
						/>
					</template>

					<!-- 前置 -->
					<template #preBusinessCode="{ rowData }">
						<link-tag
							:value="rowData.preBusinessCode"
							@on-click="showBusinessPreComponent(rowData)"
						/>
					</template>
				</pitaya-table>
			</el-scrollbar>

			<el-scrollbar class="rows" v-else>
				<ledger-allocation-table :id="props.id" :source-types="tagType" />
			</el-scrollbar>
			<button-list
				v-if="footerBtnVisible"
				class="footer"
				:button="drawerBtnRightConf"
				@on-btn-click="emits('close')"
			/>

			<!-- 业务组件详情 -->
			<Drawer
				v-model:drawer="businessComponentVisible"
				:size="modalSize.xl"
				destroy-on-close
			>
				<component
					:is="businessComponent"
					:id="editingTableRow?.businessId"
					@close="businessComponentVisible = false"
				/>
			</Drawer>

			<!-- 前置 业务组件详情 -->
			<Drawer
				v-model:drawer="businessPreComponentVisible"
				:size="modalSize.xl"
				destroy-on-close
			>
				<component
					:is="businessPreComponent"
					:id="editingTableRow?.preBusinessId"
					@close="businessPreComponentVisible = false"
				/>
			</Drawer>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
