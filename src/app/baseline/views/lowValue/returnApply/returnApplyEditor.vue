<script setup lang="ts">
import { computed, onMounted, ref } from "vue"
import { DictApi } from "@/app/baseline/api/dict"
import DictTag from "../../components/dictTag.vue"

import {
	IIdempotentTokenType,
	IIdempotentTokenTypePre,
	IModalType
} from "../../../utils/types/common"
import { ElMessage, FormInstance, FormItemRule } from "element-plus"
import { inputMaxLength, modalSize } from "@/app/baseline/utils/layout-config"
import { batchFormatterNumView, getModalTypeLabel } from "@/app/baseline/utils"
import { FormElementType } from "../../components/define"
import formElement from "../../components/formElement.vue"

import { useTbInit } from "../../components/tableBase"
import { findIndex, first, map, includes, toNumber, debounce } from "lodash-es"
import { useMessageBoxInit } from "../../components/messageBox"
import { fileBusinessType } from "@/app/baseline/api/dict"
import TableFile from "../../components/tableFile.vue"
import matSelector from "../../store/components/matSelector.vue"
import { listSelectLowValueUseApplyPage } from "@/app/baseline/api/lowValue/requisitionApply"
import { MatLowValueApplyVO } from "@/app/baseline/utils/types/lowValue-requisition-apply"
import {
	getIdempotentToken,
	requiredValidator,
	validateAndCorrectInput
} from "@/app/baseline/utils/validate"
import { IInventoryBusinessType } from "@/app/baseline/utils/types/store-inventory"
import { listSelectLowValueUseApplyItemPage } from "@/app/baseline/api/lowValue/requisitionApply"
import {
	addReturnApply,
	updateReturnApply,
	addReturnApplyItemSelect,
	listReturnApplyPageItem,
	delReturnApplyItem,
	listReturnApplyDetail,
	publishReturnApply,
	updateReturnApplyItemSelect
} from "@/app/baseline/api/lowValue/returnApply"
import {
	MatLowValueReturnApplyDTO,
	MatLowValueReturnApplyItemVO,
	MatLowValueReturnApplyVO
} from "@/app/baseline/utils/types/lowValue-return-apply"
import { useDictInit } from "../../components/dictBase"
import { useUserStore } from "@/app/platform/store/modules/user"

const { userInfo } = storeToRefs(useUserStore())
const { dictOptions, getDictByCodeList } = useDictInit()

const { showDelConfirm, showWarnConfirm } = useMessageBoxInit()

const props = defineProps<{
	id?: any // id
	mode: IModalType // create || edit || create
}>()

const emits = defineEmits<{
	(e: "close"): void
	(e: "update"): void
	(e: "save"): void
}>()

/**
 * 保存旧的表单数据
 */
const oldFormData = ref<string>()

const drawerLoading = ref(false)

/**
 * 左侧title 配置
 */
const leftTitleConf = computed(() => ({
	name: [
		getModalTypeLabel(
			canEditExtra.value ? IModalType.edit : IModalType.create,
			"归还申请"
		)
	],
	icon: ["fas", "square-share-nodes"]
}))

const matTitle = computed(() => {
	return `选择可归还物资【领用单号：${formData.value.preApplyCode ?? "---"}】`
})

const leftBtnConf = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "保存草稿",
		icon: ["fas", "floppy-disk"]
	}
]
const drawerBtnRightConf = computed(() => [
	{
		name: "提交审核",
		icon: ["fas", "circle-check"],
		disabled: tableData.value.length < 1
	}
])
const formBtnLoading = ref(false) // 表单按钮loading

/**
 * 右侧 title 配置
 */
const extraTitleConf = {
	name: ["相关信息"],
	icon: ["fas", "square-share-nodes"]
}
const tabsConf = ["归还物资", "相关附件"]
const activatedTab = ref(0)

const handleTabChange = (index: number) => {
	activatedTab.value = index

	if (index === 0) {
		fetchParam.value = {
			applyId: props.id || formData.value.id,
			sord: "desc",
			sidx: "createdDate"
		}

		pageSize.value = 20

		getTableData()
	}
}

const canEditExtra = computed(() => Boolean(formData.value?.id || props.id))

/**
 * 左侧表单 配置
 */
const formData = ref<MatLowValueReturnApplyVO>({})
const formRef = ref<FormInstance>()
const formElBase = computed<FormElementType[][]>(() => [
	[
		{ label: "归还业务名称", name: "label", maxlength: inputMaxLength.input },
		{ label: "归还人", name: "createdBy_view", disabled: true },
		{ label: "归还部门", name: "sysOrgId_view", disabled: true },
		{
			label: "选择领用单号",
			name: "preApplyCode",
			type: "drawer",
			disabled: canEditExtra.value,
			placeholder: "请选择",
			clear: false,
			clickApi: () => (preApplyVisible.value = true)
		},
		{ label: "入库仓库", name: "storeLabel", disabled: true },
		{
			label: "归还原因",
			name: "illustrate",
			type: "textarea",
			maxlength: inputMaxLength.textarea
		},
		{
			label: "备注说明",
			name: "remark",
			type: "textarea",
			maxlength: inputMaxLength.textarea
		}
	]
])

/**
 * 左侧表单校验
 */
const formRules: Record<string, FormItemRule> = {
	label: requiredValidator("归还业务名称"),
	preApplyCode: requiredValidator("领用单号"),
	illustrate: requiredValidator("归还原因")
}

/**
 * 领用单号 visible
 */
const preApplyVisible = ref(false)

/**
 * 领用单 table 列
 */
const preApplySelectColumns = [
	{ prop: "code", label: "领用单号" },
	{ prop: "label", label: "领用单名称" },
	{ prop: "sysOrgId_view", label: "领用部门" },
	{ prop: "createdBy_view", label: "领用人" },
	{ prop: "createdDate", label: "申请时间" }
]

/**
 * 领用单 查询条件
 */
const preApplyQueryArrList = [
	{
		name: "领用单号",
		key: "code",
		type: "input",
		placeholder: "请输入领用单号"
	},
	{
		name: "领用单名称",
		key: "label",
		type: "input",
		placeholder: "请输入领用单名称"
	}
]

/**
 * 领用单  保存 操作
 * @param e
 */
const handlePreApplySelect = (e: MatLowValueApplyVO[]) => {
	const row = first(e)

	formData.value.preApplyCode = row?.code
	formData.value.preApplyId = row?.id
	formData.value.storeId = row?.storeId
	formData.value.storeLabel = row?.storeLabel
	preApplyVisible.value = false
}

/**
 * 表单按钮 操作
 * @param btnName 保存草稿 | 取消 | 提交审核
 */
const handleFormBtnClick = (btnName?: string) => {
	if (btnName == "取消") {
		emits("close")
	} else if (btnName == "保存草稿") {
		handleSaveDraft()
	} else if (btnName == "提交审核") {
		handleSubmit()
	}
}

/**
 * 保存草稿 handler
 */
async function handleSaveDraft() {
	if (!formRef.value) {
		return
	}

	formRef.value.validate(async (valid) => {
		if (!valid) {
			return
		}
		formBtnLoading.value = true

		try {
			const api = canEditExtra.value ? updateReturnApply : addReturnApply

			let idempotentToken = ""
			if (!canEditExtra.value) {
				idempotentToken = getIdempotentToken(
					IIdempotentTokenTypePre.apply,
					IIdempotentTokenType.lowValueReturnApply
				)
			}

			const r = await api(
				formData.value as MatLowValueReturnApplyDTO,
				idempotentToken
			)

			formData.value.applyId = r.id // 编辑保存时 需要 applyId => id
			formData.value.id = r.id
			fetchParam.value.applyId = r.id

			oldFormData.value = JSON.stringify(formData.value)

			ElMessage.success("操作成功")
			getTableData()
			emits("save")
		} finally {
			formBtnLoading.value = false
		}
	})
}

/**
 * 提交 handler
 */
async function handleSubmit() {
	if (!formRef.value) {
		return
	}

	formRef.value.validate(async (valid) => {
		if (!valid) {
			return
		}

		await showWarnConfirm("请确认是否提交本次数据？")

		formBtnLoading.value = true
		drawerLoading.value = true

		try {
			// 如果主表有修改，则先更新主表数据
			if (oldFormData.value != JSON.stringify(formData.value)) {
				await updateReturnApply(formData.value as MatLowValueReturnApplyDTO)
			}

			const idempotentToken = getIdempotentToken(
				IIdempotentTokenTypePre.other,
				IIdempotentTokenType.lowValueReturnApply,
				formData.value.id
			)

			const { code, msg, data } = await publishReturnApply(
				props.id || formData.value.id,
				idempotentToken
			)

			if (data && code != 200) {
				errorGoodsIdList.value = data || []
				ElMessage.error(msg)
				getTableData()
			} else {
				ElMessage.success("操作成功")

				emits("save")
				emits("close")
			}
		} finally {
			formBtnLoading.value = false
			drawerLoading.value = false
		}
	})
}

const queryConf = computed(() => {
	return [
		{
			name: "物资编码",
			key: "materialCode",
			type: "input",
			placeholder: "请输入物资编码"
		},
		{
			name: "物资名称",
			key: "materialLabel",
			type: "input",
			placeholder: "请输入物资名称"
		},
		{
			name: "规格型号",
			key: "version",
			placeholder: "请输入规格型号",
			type: "input"
		},
		{
			name: "物资性质",
			key: "attribute",
			type: "select",
			children: dictOptions.value.MATERIAL_NATURE,
			placeholder: "请选择物资性质"
		}
	]
})

const {
	currentPage,
	tableCache,
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	pageSize,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = useTbInit<MatLowValueReturnApplyItemVO, Record<string, any>>()

tableProp.value = [
	{ prop: "materialCode", label: "物资编码", width: 130, fixed: "left" },
	{ prop: "materialLabel", label: "物资名称" },
	{ prop: "materialTypeCode", label: "物资分类编码", width: 130 },
	{ prop: "materialTypeLabel", label: "物资分类名称", width: 120 },
	{ prop: "version", label: "规格型号" },
	{ prop: "attribute", label: "物资性质", needSlot: true, width: 120 },
	{ prop: "lowValueType_view", label: "低值类型" },
	{ prop: "username_view", label: "使用人", width: 120 },
	{ prop: "allocationNum_view", align: "right", label: "分配数量", width: 100 },
	{
		prop: "hasReturnNum_view",
		align: "right",
		label: "已归还数量",
		width: 100
	},
	{
		prop: "canNum_view",
		label: "可归还数量",
		width: 100,
		align: "right",
		fixed: "right"
	},
	{
		prop: "num",
		label: "本次归还数量",
		align: "right",
		width: 100,
		needSlot: true,
		fixed: "right"
	},
	/* {
		prop: "storeLabel",
		label: "归还仓库名称",
		width: 200,
		needSlot: true,
		fixed: "right"
	}, */
	{ prop: "actions", label: "操作", width: 100, needSlot: true, fixed: "right" }
]

fetchFunc.value = listReturnApplyPageItem

watch(tableData, () => {
	batchFormatterNumView(tableData.value)
})
const goodsSelectorVisible = ref(false)
const tbBtnConf = computed(() => {
	return [
		{
			name: "添加物资",
			icon: ["fas", "circle-plus"]
		},
		{
			name: "批量移除",
			icon: ["fas", "trash-can"],
			disabled: selectedTableList.value.length > 0 ? false : true
		}
	]
})

async function handleTabsClick(btnName?: string) {
	if (btnName == "添加物资") {
		goodsSelectorVisible.value = true
	} else if (btnName == "批量移除") {
		const ids = map(selectedTableList.value, ({ id }) => id)

		await showDelConfirm()
		formBtnLoading.value = true

		try {
			await delReturnApplyItem(ids)
			ElMessage.success("操作成功")
			fetchTableData()
			emits("update")
		} finally {
			formBtnLoading.value = false
		}
	}
}

/**
 * 较验：本次归还 < 可归还
 *
 */
function validateNum(e: any) {
	const canGetNum = toNumber(e.canNum) || 0
	const num = toNumber(e.num)

	if (num > canGetNum) {
		ElMessage.warning("本次归还数量不能大于可归还数量！")
		e.num = canGetNum
	} else if (num <= 0) {
		const oldRow = tableCache.find((r) => r.id == e.id)
		e.num = oldRow.num
		ElMessage.warning("本次归还数量不能小于等于0！")
		return false

		// e.num = 1
	} else {
		e.num = num
	}

	handleInputBlur(e)
}

/**
 * 输入框失焦 handler
 * 失焦后 更新归还数量 api
 */
const handleInputBlur = debounce(async (e: MatLowValueReturnApplyItemVO) => {
	formBtnLoading.value = true
	try {
		await updateReturnApplyItemSelect({ id: e.id, num: e.num })

		//  更新 tableCache
		const rowIdx = findIndex(tableCache, (v) => v.id === e.id)
		if (rowIdx !== -1) {
			tableCache.splice(rowIdx, 1, { ...e })
		}

		ElMessage.success("操作成功")
	} finally {
		formBtnLoading.value = false
	}
}, 300)

/**
 * 删除物资
 */
async function handleDelGoods(e: any) {
	await showDelConfirm()

	await delReturnApplyItem([e.id])
	ElMessage.success("操作成功")
	fetchTableData()
	emits("update")
}

/**
 * 物资分页 查询条件
 */
const matQueryArrList = computed(() => [
	{
		name: "物资编码",
		key: "code",
		type: "input",
		placeholder: "请输入物资编码"
	},
	{
		name: "物资名称",
		key: "label",
		type: "input",
		placeholder: "请输入物资名称"
	},
	{
		name: "规格型号",
		key: "version",
		placeholder: "请输入规格型号",
		type: "input"
	},
	{
		name: "物资性质",
		key: "attribute",
		type: "select",
		children: dictOptions.value.MATERIAL_NATURE,
		placeholder: "请选择物资性质"
	},
	{
		name: "使用人",
		key: "realname",
		type: "input",
		placeholder: "请输入使用人"
	}
])

/**
 * 添加物资 table 列
 */
const matSelectColumns: TableColumnType[] = [
	{ prop: "materialCode", label: "物资编码", width: "130", fixed: "left" },
	{ prop: "materialLabel", label: "物资名称" },
	{ prop: "materialTypeCode", label: "物资分类编码" },
	{ prop: "materialTypeLabel", label: "物资分类名称" },
	{ prop: "version", label: "规格型号" },
	{ prop: "technicalParameter", label: "技术参数" },
	{ prop: "attribute", label: "物资性质", needSlot: true, width: 120 },
	{ prop: "lowValueType_view", label: "低值类型" },
	{ prop: "useUnit", label: "库存单位", needSlot: true },
	{ prop: "username_view", label: "使用人", width: 120 },
	{ prop: "num_view", label: "分配数量", align: "right" },
	{ prop: "storeLocation", label: "存放位置" },
	{ prop: "returnNum_view", label: "已归还数量", align: "right" }
]

const handleAddGoods = async (params: any[]) => {
	const idempotentToken = getIdempotentToken(
		IIdempotentTokenTypePre.other,
		IIdempotentTokenType.lowValueReturnApply,
		formData.value.id
	)

	await addReturnApplyItemSelect(
		{
			applyId: props.id || formData.value.id,
			itemList:
				map(params, (v) => ({
					materialId: v.materialId,
					batchId: v.id,
					materialCode: v.materialCode,
					materialLabel: v.materialLabel,
					version: v.version,
					attribute: v.attribute
				})) || []
		},
		idempotentToken
	)
	goodsSelectorVisible.value = false
	ElMessage.success("操作成功")
	getTableData()
	emits("update")
}

/**
 * 获取详情数据
 */
async function getDetail() {
	drawerLoading.value = true
	try {
		const r = await listReturnApplyDetail(props.id || formData.value.id)
		formData.value = { ...r, applyId: r.id }

		oldFormData.value = JSON.stringify(formData.value)
	} finally {
		drawerLoading.value = false
	}
}

/**
 * 初始化领用信息
 */
function initMatPickApplyUserInfo() {
	// 领用人
	formData.value.createdBy_view = userInfo.value.realName as any
	formData.value.createdBy = userInfo.value.userName as any
	formData.value.sysOrgId = userInfo.value.orgId as any
	formData.value.sysOrgId_view = userInfo.value.orgName as any
}

/**
 * 物资明细 table 行样式
 *
 * 库存异常物资，用红色标记该行
 */
const errorGoodsIdList = ref<number[]>([])
function tbCellClassName({ row }: any) {
	return includes(errorGoodsIdList.value, row.id) ? "error" : ""
}

/**
 * table 数据源
 * @param data
 */
const getTableData = (data?: any) => {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()
	fetchParam.value = {
		...fetchParam.value,
		...data
	}

	fetchTableData()
}

onMounted(async () => {
	getDictByCodeList(["MATERIAL_NATURE"])

	fetchParam.value.sord = "desc"
	fetchParam.value.sidx = "createdDate"

	if (props.id) {
		fetchParam.value.applyId = props.id
		await getDetail()
		getTableData()
	} else {
		initMatPickApplyUserInfo()
	}
})
</script>
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<div class="drawer-column left" style="width: 310px">
			<Title :title="leftTitleConf" />

			<el-scrollbar>
				<el-form
					class="content"
					:model="formData"
					ref="formRef"
					label-position="top"
					label-width="100px"
					:rules="formRules"
				>
					<form-element :form-element="formElBase" :form-data="formData" />
				</el-form>
			</el-scrollbar>
			<button-list
				class="footer"
				:button="leftBtnConf"
				:loading="formBtnLoading"
				@on-btn-click="handleFormBtnClick"
			/>
		</div>
		<div
			class="drawer-column right"
			:class="!canEditExtra ? 'disabled' : ''"
			style="width: calc(100% - 310px)"
		>
			<Title :title="extraTitleConf">
				<Tabs
					style="margin-right: auto; margin-left: 30px"
					:tabs="tabsConf"
					@on-tab-change="handleTabChange"
				/>
			</Title>

			<el-scrollbar class="rows return-apply-editor-table-wrapper">
				<Query
					:query-arr-list="(queryConf as any)"
					style="margin: 10px 10px -10px"
					@get-query-data="getTableData"
					v-if="activatedTab === 0"
				/>
				<pitaya-table
					v-if="activatedTab === 0"
					ref="tableRef"
					:columns="tableProp"
					:table-data="tableData"
					:need-selection="true"
					:single-select="false"
					:need-index="true"
					:need-pagination="true"
					:total="pageTotal"
					:table-loading="tableLoading"
					:cell-class-name="tbCellClassName"
					@on-selection-change="selectedTableList = $event"
					@on-current-page-change="onCurrentPageChange"
				>
					<!-- 物资性质 -->
					<template #attribute="{ rowData }">
						<dict-tag
							:options="DictApi.getMatAttr()"
							:value="rowData.attribute"
						/>
					</template>

					<!-- 本次归还数量 -->
					<template #num="{ rowData }">
						<el-input
							class="no-arrows"
							v-model="rowData.num"
							@click.stop
							@input="rowData.num = validateAndCorrectInput($event)"
							@change="validateNum(rowData)"
						/>
					</template>

					<template #actions="{ rowData }">
						<el-button v-btn link @click.stop="handleDelGoods(rowData)">
							<font-awesome-icon
								:icon="['fas', 'trash-can']"
								style="color: var(--pitaya-btn-background)"
							/>
							<span class="table-inner-btn">移除</span>
						</el-button>
					</template>

					<template #footerOperateLeft>
						<button-list
							:button="tbBtnConf"
							:is-not-radius="true"
							:loading="formBtnLoading"
							@on-btn-click="handleTabsClick"
						/>
					</template>
				</pitaya-table>

				<table-file
					v-else
					:business-type="fileBusinessType.returnApply"
					:business-id="props.id || formData.id"
					:mod="canEditExtra ? IModalType.edit : IModalType.view"
				/>
			</el-scrollbar>
			<button-list
				class="footer"
				:button="drawerBtnRightConf"
				:loading="formBtnLoading"
				@on-btn-click="handleFormBtnClick"
			/>
		</div>

		<!-- 选择领用单 -->
		<Drawer
			v-model:drawer="preApplyVisible"
			:size="modalSize.lg"
			destroy-on-close
		>
			<mat-selector
				:table-api="listSelectLowValueUseApplyPage"
				:table-req-params="{ sidx: 'createdDate', sord: 'desc' }"
				:query-arr-list="preApplyQueryArrList"
				:columns="preApplySelectColumns"
				:selectedIds="[formData.preApplyId]"
				title="选择领用单"
				@save="handlePreApplySelect"
				@close="preApplyVisible = false"
			/>
		</Drawer>

		<!-- 物资编码手册选择器 -->
		<Drawer
			v-model:drawer="goodsSelectorVisible"
			:size="modalSize.lg"
			destroy-on-close
		>
			<mat-selector
				:table-req-params="{
					applyId: formData.id,
					useApplyId: formData.preApplyId,
					type: IInventoryBusinessType.lowValueReturnApply
				}"
				:table-api="listSelectLowValueUseApplyItemPage"
				:columns="matSelectColumns"
				:multiple="true"
				:title="matTitle"
				:query-arr-list="matQueryArrList"
				@save="handleAddGoods"
				@close="goodsSelectorVisible = false"
			/>
		</Drawer>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.content {
	.btn-group {
		width: 100%;
		.el-button--warning {
			background-color: #e6a23c !important;
		}
		.el-button--success {
			background-color: #67c23a !important;
		}
		.el-button {
			width: 50% !important;
		}
	}
}

.return-apply-editor-table-wrapper {
	&:deep(.pitaya-table) {
		.error {
			.column-inner-item,
			.cell,
			.el-input__inner {
				color: red;
			}
		}
	}
}
</style>
