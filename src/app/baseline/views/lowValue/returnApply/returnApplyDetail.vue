<!-- 低值 - 归还申请 - 详情 -->
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<div class="drawer-column left" style="width: 310px">
			<Title :title="titleConf" />
			<el-scrollbar>
				<grid-panel :options="gridPanelOptions" />
				<!-- 归还信息 -->
				<el-descriptions size="small" :column="1" border class="content">
					<el-descriptions-item
						v-for="desc in descOptions"
						:key="desc.label"
						:label="desc.label"
					>
						<span v-if="desc.needTooltip">
							<el-tooltip
								effect="dark"
								:content="descData?.[desc.key]"
								:disabled="
									getRealLength(descData?.[desc.key]) <= 100 ? true : false
								"
							>
								{{
									getRealLength(descData?.[desc.key]) > 100
										? setString(descData?.[desc.key], 100)
										: descData?.[desc.key] || "---"
								}}
							</el-tooltip>
						</span>
						<span v-else>
							{{
								getNumDefByNumKey(desc.key, descData[desc.key], /Cnt$|Num$/i)
							}}
						</span>
					</el-descriptions-item>
				</el-descriptions>
			</el-scrollbar>
		</div>
		<div
			class="drawer-column right"
			style="width: calc(100% - 310px)"
			:class="{ pdr10: !footerBtnVisible }"
		>
			<div class="rows">
				<Title :title="extTitleConf">
					<Tabs
						style="margin-right: auto"
						:tabs="['归还物资', '相关附件']"
						@on-tab-change="handleTabChange"
					/>
				</Title>

				<Query
					:query-arr-list="(queryConf as any)"
					style="margin: 10px 10px -10px"
					@get-query-data="getTableData"
					v-if="activatedTab === 0"
				/>

				<pitaya-table
					v-if="activatedTab === 0"
					ref="tableRef"
					:columns="(tbInit.tableProp as any)"
					:table-data="tableData"
					:need-selection="false"
					:single-select="false"
					:need-index="true"
					:need-pagination="true"
					:total="pageTotal"
					:table-loading="tableLoading"
					@on-selection-change="selectedTableList = $event"
					@on-current-page-change="onCurrentPageChange"
				>
					<!-- 物资性质 -->
					<template #attribute="{ rowData }">
						<dict-tag
							:options="DictApi.getMatAttr()"
							:value="rowData.attribute"
						/>
					</template>
				</pitaya-table>

				<table-file
					v-else
					:business-type="fileBusinessType.returnApply"
					:business-id="props.id"
					:mod="IModalType.view"
				/>
			</div>

			<button-list
				v-if="footerBtnVisible"
				class="footer"
				:button="btnConf"
				@on-btn-click="emit('close')"
			/>
		</div>
	</div>
</template>

<script setup lang="ts">
import { IModalType } from "@/app/baseline/utils/types/common"
import DictTag from "../../components/dictTag.vue"
import { DictApi, appStatus, fileBusinessType } from "@/app/baseline/api/dict"
import TableFile from "../../components/tableFile.vue"
import {
	batchFormatterNumView,
	getNumDefByNumKey,
	tableColFilter,
	toFixedTwo
} from "@/app/baseline/utils"
import { useTbInit } from "../../components/tableBase"
import GridPanel from "../../store/components/gridPanel.vue"

import { MatLowValueReturnApplyItemVO } from "@/app/baseline/utils/types/lowValue-return-apply"
import {
	listReturnApplyDetail,
	listReturnApplyPageItem
} from "@/app/baseline/api/lowValue/returnApply"
import { getRealLength, setString } from "@/app/baseline/utils/validate"
import { useDictInit } from "../../components/dictBase"

const props = withDefaults(
	defineProps<{
		/**
		 * 领用申请id
		 */
		id?: number
		footerBtnVisible?: boolean
	}>(),
	{ mode: IModalType.view, footerBtnVisible: true }
)

const emit = defineEmits<{
	(e: "close"): void
}>()

const { dictOptions, getDictByCodeList } = useDictInit()

/**
 * 领用物资编码 & 领用物资数量
 */
const gridPanelOptions = computed(() => {
	return [
		{
			label: "领用数量",
			value: parseInt(descData.value.useOutNum) || 0
		},
		{
			label: "已归还数量",
			value: parseInt(descData.value.returnInNum) || 0
		}
	]
})

const activatedTab = ref(0)

const descOptions = [
	{ label: "归还业务单号", key: "code" },
	{ label: "归还业务名称", key: "label" },
	{ label: "关联领用单号", key: "preApplyCode" },
	{ label: "入库仓库", key: "storeLabel" },
	{ label: "归还物资编码", key: "materialCodeNum" },
	{ label: "归还物资数量", key: "returnInNum_view" },
	{ label: "归还人", key: "createdBy_view" },
	{ label: "归还部门", key: "sysOrgId_view" },
	{ label: "申请时间", key: "createdDate" },
	{ label: "归还原因", key: "illustrate", needTooltip: true }
]

const drawerLoading = ref(false)

const titleConf = {
	name: ["归还信息"],
	icon: ["fas", "square-share-nodes"]
}

const extTitleConf = {
	name: ["扩展信息"],
	icon: ["fas", "square-share-nodes"]
}

const handleTabChange = (index: number) => {
	activatedTab.value = index

	if (index === 0) {
		fetchParam.value = {
			applyId: props.id || descData.value.id,
			sord: "desc",
			sidx: "createdDate"
		}

		pageSize.value = 20

		getTableData()
	}
}

const btnConf = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	}
]

const descData = ref<any>({})

const queryConf = computed(() => {
	return [
		{
			name: "物资编码",
			key: "materialCode",
			type: "input",
			placeholder: "请输入物资编码"
		},
		{
			name: "物资名称",
			key: "materialLabel",
			type: "input",
			placeholder: "请输入物资名称"
		},
		{
			name: "规格型号",
			key: "version",
			placeholder: "请输入规格型号",
			type: "input"
		},
		{
			name: "物资性质",
			key: "attribute",
			type: "select",
			children: dictOptions.value.MATERIAL_NATURE,
			placeholder: "请选择物资性质"
		}
	]
})

const tbInit = useTbInit<MatLowValueReturnApplyItemVO, Record<string, any>>()
const {
	currentPage,
	fetchParam,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	pageSize,
	selectedTableList,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = tbInit

fetchFunc.value = listReturnApplyPageItem

tbInit.tableProp = computed(() => {
	const defTableProp: TableColumnType[] = [
		{ prop: "materialCode", label: "物资编码", fixed: "left", width: 130 },
		{ prop: "materialLabel", label: "物资名称", width: 100 },
		{ prop: "materialTypeCode", label: "物资分类编码", width: 100 },
		{ prop: "materialTypeLabel", label: "物资分类名称", width: 100 },
		{ prop: "version", label: "规格型号", width: 100 },
		{ prop: "attribute", label: "物资性质", needSlot: true, width: 120 },
		{ prop: "lowValueType_view", label: "低值类型" },
		{ prop: "username_view", label: "使用人" },
		{ prop: "allocationNum_view", label: "分配数量", align: "right" },
		{
			prop: "hasReturnNum_view",
			label: "已归还数量",
			align: "right",
			width: 100
		},
		{
			prop: "canNum_view",
			label: "可归还数量",
			align: "right",
			width: 100
		},
		{
			prop: "num_view",
			label: "本次归还数量",
			align: "right",
			width: 100
		}
	]

	if (descData.value.bpmStatus == appStatus.approved) {
		return tableColFilter(defTableProp, ["已归还数量", "可归还数量"])
	} else {
		return defTableProp
	}
})

watch(tableData, () => {
	batchFormatterNumView(tableData.value)
})

/**
 * table 数据源
 * @param data
 */
const getTableData = (data?: any) => {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()
	fetchParam.value = {
		...fetchParam.value,
		...data
	}

	fetchTableData()
}

onMounted(async () => {
	await getDictByCodeList(["MATERIAL_NATURE"])
	getDetail()
})

function getDetail() {
	fetchParam.value.sord = "desc"
	fetchParam.value.sidx = "createdDate"

	if (!props.id) {
		return
	}
	drawerLoading.value = true
	listReturnApplyDetail(props.id!)
		.then((r) => {
			descData.value = r
			descData.value.returnInNum_view = toFixedTwo(r.returnInNum)

			fetchParam.value.applyId = r.id

			fetchTableData()
		})
		.finally(() => (drawerLoading.value = false))
}
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
