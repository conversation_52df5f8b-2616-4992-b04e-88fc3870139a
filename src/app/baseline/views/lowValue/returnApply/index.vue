<!-- 归还申请 -->
<script lang="ts" setup>
import {
	checkPermission,
	isCheckPermission
} from "@/app/platform/utils/permission"
import { powerList } from "../../components/define.d"
import { computed, onMounted, ref } from "vue"
import { useMessageBoxInit } from "../../components/messageBox"
import { useTbInit } from "../../components/tableBase"
import { BaseLineSysApi, getTaskByBusinessIds } from "@/app/baseline/api/system"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import LinkTag from "@/app/baseline/views/components/linkTag.vue"
import { modalSize } from "@/app/baseline/utils/layout-config"
import { IModalType } from "../../../utils/types/common"
import { DictApi } from "@/app/baseline/api/dict"

import returnApplyEditor from "./returnApplyEditor.vue"
import returnApplyDetail from "./returnApplyDetail.vue"
import requisitionApplyDetail from "../requisitionApply/requisitionApplyDetail.vue"

import { appStatus } from "@/app/baseline/api/dict"
import { LowValueApplyBpmStatusVo } from "@/app/baseline/utils/types/lowValue-requisition-apply"
import {
	listLowValueReturnApplyPage,
	getLowValueReturnApplyStatusCnt,
	delReturnApplyApply
} from "@/app/baseline/api/lowValue/returnApply"
import {
	MatLowValueReturnApplyVO,
	MatLowValueReturnApplyVORequest
} from "@/app/baseline/utils/types/lowValue-return-apply"

import { toFixedTwo } from "@/app/baseline/utils"
import { omit } from "lodash-es"
import { useUserStore } from "@/app/platform/store/modules/user"
import ApprovedDrawer from "@/app/baseline/views/components/approvedDrawer.vue"

const { userInfo } = storeToRefs(useUserStore())

const { showDelConfirm } = useMessageBoxInit()

/**
 * 查询条件 配置
 */
const queryArrList = computed(() => {
	return [
		{
			name: "归还业务单号",
			key: "code",
			placeholder: "请输入归还业务单号",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "归还业务名称",
			key: "label",
			placeholder: "请输入归还业务名称",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "关联领用单号",
			key: "preBusinessCode",
			placeholder: "请输入关联领用单号",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "归还人",
			key: "realname",
			placeholder: "请输入领用人",
			enableFuzzy: true,
			type: "input"
		},
		{
			name: "归还部门",
			key: "sysOrgId",
			type: "treeSelect",
			placeholder: "请选择",
			treeApi: () =>
				BaseLineSysApi.getDepartmentTreeWithCompanyDisabled(
					userInfo.value.companyId
				)
		}
	]
})

/**
 * Title 配置
 */
const rightTitle = {
	name: ["归还申请"],
	icon: ["fas", "square-share-nodes"]
}
const titleBtnConf = [
	{
		name: "新建归还申请",
		roles: powerList.lowValueReturnApplyBtnCreate,
		icon: ["fas", "circle-plus"]
	}
]

/**
 * tab 配置项
 */
const statusCnt = ref<LowValueApplyBpmStatusVo>({})
const tabList = computed(() => {
	return [
		{
			name: "草稿箱",
			value: `${appStatus.pendingApproval},${appStatus.rejected}`,
			count: statusCnt.value[0] ?? 0
		},
		{
			name: "审批中",
			value: appStatus.underApproval,
			count: statusCnt.value[1] ?? 0
		},
		{
			name: "已审批",
			value: appStatus.approved,
			count: statusCnt.value[2] ?? 0
		}
	]
})
const tabStatus = ref(tabList.value[0].value)
const activeName = ref(tabList.value[0].name)
const handleTabsClick = async (tab: any) => {
	activeName.value = tab.paneName
	tabStatus.value = tabList.value[tab.index].value as any
	getTableData()
}

const {
	tableData,
	tableProp,
	tableRef,
	tableLoading,
	currentPage,
	pageTotal,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = useTbInit<MatLowValueReturnApplyVO, MatLowValueReturnApplyVORequest>()

/**
 * table 列配置
 */
tableProp.value = [
	{ label: "归还业务单号", prop: "code", width: 180 },
	{ label: "归还业务名称", prop: "label" },
	{ label: "归还部门", prop: "sysOrgId_view" },
	{
		label: "关联领用业务单号",
		prop: "preApplyCode",
		needSlot: true,
		width: 180
	},
	{
		label: "入库仓库",
		prop: "storeLabel",
		width: 180
	},
	{ label: "归还物资编码", prop: "materialCodeNum", width: 150 },
	{
		label: "归还物资数量",
		prop: "returnInNum",
		needSlot: true,
		align: "right"
	},
	{ label: "审批状态", prop: "bpmStatus", needSlot: true },

	{ label: "归还人", prop: "createdBy_view" },
	{ label: "申请时间", prop: "createdDate", width: 150, sortable: true },
	{
		label: "操作",
		width: 200,
		prop: "operations",
		needSlot: true,
		fixed: "right"
	}
]
fetchFunc.value = listLowValueReturnApplyPage

/**
 * tab 切换数据源
 */
const getTableData = (data?: any) => {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		sidx: "createdDate",
		sord: "desc",
		...fetchParam.value,
		bpmStatus: tabStatus.value,
		...data
	}
	handleUpdate()
}

const curRowId = ref<any>("")
const curRowData = ref<MatLowValueReturnApplyVO>({})
const viewVisible = ref<boolean>(false)
const editorVisible = ref(false)
const editorMode = ref(IModalType.edit)
const approvedVisible = ref(false)
const editTask = ref<Record<string, any>>()

/**
 * 新建 操作
 */
const handleClickAddBtn = () => {
	editorMode.value = IModalType.create
	editorVisible.value = true
	curRowId.value = ""
	curRowData.value = {}
}

/**
 * 查看 关联领用单号 操作
 * @param row
 */
const useApplyDetailVisible = ref(false)
const onRowViewUseApplyDetail = (row: MatLowValueReturnApplyVO) => {
	curRowData.value = row
	useApplyDetailVisible.value = true
	editorMode.value = IModalType.view
}

/**
 * 编辑 操作
 * @param row
 */
const onRowEdit = (row: MatLowValueReturnApplyVO) => {
	curRowId.value = row.id
	curRowData.value = row
	editorVisible.value = true
	editorMode.value = IModalType.edit
}

/**
 * 查看
 * @param row
 */
const onRowView = async (row: MatLowValueReturnApplyVO) => {
	curRowId.value = row.id
	curRowData.value = row
	/* viewVisible.value = true */
	editorMode.value = IModalType.view

	if (row?.bpmStatus == appStatus.pendingApproval) {
		viewVisible.value = true
	} else {
		const res = await getTaskByBusinessIds({
			businessIds: [row?.id],
			camundaKey: "low_value_return_apply"
		})

		if (Array.isArray(res) && res.length > 0) {
			editTask.value = { ...res[res.length - 1] }
			approvedVisible.value = true
		} else {
			viewVisible.value = true
		}
	}
}

/**
 * 删除 操作
 * @param row
 */
const onRowDel = async (row: MatLowValueReturnApplyVO) => {
	await showDelConfirm()
	await delReturnApplyApply(row.id)
	ElMessage.success("操作成功")
	handleUpdate()
}

/**
 * 更新主表/statusCnt
 */
const handleUpdate = async () => {
	fetchTableData()
	statusCnt.value = await getLowValueReturnApplyStatusCnt(
		omit(
			{
				...fetchParam.value
			},
			"sidx",
			"sord",
			"bpmStatus",
			"currentPage",
			"pageSize"
		) as any
	)
}
onMounted(() => {
	getTableData()
})

/**
 * 排序
 * @param column
 * @param prop 字段名称
 * @param order ascending 升序 descending 降序
 */
function handleSortChange(column: any, prop: any, order: any) {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		sord:
			order === "descending" ? "desc" : order === "ascending" ? "asc" : "desc",
		sidx: order ? prop : "createdDate" // 排序字段
	}

	fetchTableData()
}
</script>
<template>
	<div class="app-container">
		<div class="whole-frame">
			<ModelFrame class="top-frame">
				<Query
					:queryArrList="queryArrList"
					@getQueryData="getTableData"
					class="ml10"
				/>
			</ModelFrame>

			<ModelFrame class="bottom-frame">
				<Title
					:title="rightTitle"
					:button="(titleBtnConf as any)"
					@on-btn-click="handleClickAddBtn"
				>
					<div class="app-tabs-wrapper">
						<el-tabs v-model="activeName" @tab-click="handleTabsClick">
							<el-tab-pane
								v-for="(tab, index) in tabList"
								:key="index"
								:label="`${tab.name}（${tab.count ?? 0}）`"
								:name="tab.name"
								:index="tab.name"
							/>
						</el-tabs>
					</div>
				</Title>

				<pitaya-table
					ref="tableRef"
					:columns="tableProp"
					:tableData="tableData"
					:total="pageTotal"
					:need-index="true"
					@on-current-page-change="onCurrentPageChange"
					:table-loading="tableLoading"
					@on-table-sort-change="handleSortChange"
				>
					<!-- 审批状态 -->
					<template #bpmStatus="{ rowData }">
						<dict-tag
							:options="DictApi.getBpmStatus()"
							:value="rowData.bpmStatus"
						/>
					</template>

					<!-- 关联领用业务单号 -->
					<template #preApplyCode="{ rowData }">
						<link-tag
							:value="rowData.preApplyCode"
							@click="onRowViewUseApplyDetail(rowData)"
						/>
					</template>

					<template #returnInNum="{ rowData }">
						{{ toFixedTwo(rowData.returnInNum) }}
					</template>

					<template #operations="{ rowData }">
						<slot
							v-if="
								(tabStatus == tabList[0].value &&
									isCheckPermission(powerList.lowValueReturnApplyBtnEdit)) ||
								isCheckPermission(powerList.lowValueReturnApplyBtnPreview) ||
								(tabStatus == tabList[0].value &&
									isCheckPermission(powerList.lowValueReturnApplyBtnDrop))
							"
						>
							<el-button
								v-btn
								link
								@click="onRowEdit(rowData)"
								v-if="
									tabStatus == tabList[0].value &&
									isCheckPermission(powerList.lowValueReturnApplyBtnEdit)
								"
								:disabled="
									checkPermission(powerList.lowValueReturnApplyBtnEdit)
								"
							>
								<font-awesome-icon :icon="['fas', 'pen-to-square']" />
								<span class="table-inner-btn">编辑</span>
							</el-button>
							<el-button
								v-btn
								link
								@click="onRowView(rowData)"
								v-if="
									isCheckPermission(powerList.lowValueReturnApplyBtnPreview)
								"
								:disabled="
									checkPermission(powerList.lowValueReturnApplyBtnPreview)
								"
							>
								<font-awesome-icon :icon="['fas', 'eye']" />
								<span class="table-inner-btn">查看</span>
							</el-button>
							<el-button
								v-btn
								link
								@click="onRowDel(rowData)"
								v-if="
									tabStatus == tabList[0].value &&
									isCheckPermission(powerList.lowValueReturnApplyBtnDrop)
								"
								:disabled="
									checkPermission(powerList.lowValueReturnApplyBtnDrop)
								"
							>
								<font-awesome-icon :icon="['fas', 'trash-can']" />
								<span class="table-inner-btn">移除</span>
							</el-button>
						</slot>
						<slot v-else>---</slot>
					</template>
				</pitaya-table>

				<!-- 编辑/新建  -->
				<Drawer
					:size="modalSize.xl"
					v-model:drawer="editorVisible"
					:destroyOnClose="true"
				>
					<return-apply-editor
						:id="curRowId"
						:mode="editorMode"
						@close="editorVisible = false"
						@save="handleUpdate"
						@update="fetchTableData"
					/>
				</Drawer>

				<!-- 查看  -->
				<Drawer
					:size="modalSize.xl"
					v-model:drawer="viewVisible"
					:destroyOnClose="true"
				>
					<return-apply-detail
						:id="curRowId"
						:mode="editorMode"
						@close="viewVisible = false"
					/>
				</Drawer>

				<!-- 关联领用业务单号 -->
				<Drawer
					:size="modalSize.xl"
					v-model:drawer="useApplyDetailVisible"
					:destroyOnClose="true"
				>
					<requisition-apply-detail
						:id="curRowData.preApplyId!"
						:mode="editorMode"
						@close="useApplyDetailVisible = false"
					/>
				</Drawer>

				<!-- 审批、查看弹窗 -->
				<Drawer
					:size="modalSize.xxl"
					v-model:drawer="approvedVisible"
					:destroyOnClose="true"
				>
					<approved-drawer
						:mod="editorMode"
						:task-value="editTask"
						@on-save-or-close="approvedVisible = false"
					/>
				</Drawer>
			</ModelFrame>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
</style>
