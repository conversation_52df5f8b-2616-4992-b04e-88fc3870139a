import ReqUtil from "../utils/request-util"
import { PageResVo } from "../utils/types/common"
import {
	SystemCostCategoryDTO,
	systemCostCategoryPagedRequest,
	SystemCostCategoryTreeVo,
	SystemCostCategoryVo
} from "../utils/types/system-cost-category"

/**
 * 查询费用类别树
 */
export function getSystemCostCategoryTree(params: SystemCostCategoryDTO = {}) {
	return ReqUtil.get<SystemCostCategoryTreeVo[]>(
		"/baseline/system/costCategory/tree",
		{ params }
	)
}

/**
 * 费用类别-分页查询
 */
export function getSystemCostCategoryPaged(
	params: systemCostCategoryPagedRequest = {}
) {
	return ReqUtil.get<PageResVo<SystemCostCategoryVo>>(
		"/baseline/system/costCategory/page",
		{ params }
	)
}

/**
 * 费用类别-新增
 */
export function addSystemCostCategory(
	data: SystemCostCategoryDTO,
	idempotentToken?: string
) {
	return ReqUtil.post<SystemCostCategoryVo>("/baseline/system/costCategory", {
		data,
		headers: { "idempotent-token": idempotentToken }
	})
}

/**
 * 费用类别-编辑
 */
export function updateSystemCostCategory(
	data: SystemCostCategoryDTO,
	_idempotentToken?: string
) {
	return ReqUtil.put<SystemCostCategoryVo>("/baseline/system/costCategory", {
		data
	})
}

/**
 * 费用类别-删除
 */
export function delSystemCostCategory(id: any) {
	return ReqUtil.delete<boolean | null>(`/baseline/system/costCategory/${id}`)
}

/**
 * 费用类别-查询详情
 */
export function getSystemCostCategoryById(id: any) {
	return ReqUtil.get<SystemCostCategoryVo>(
		`/baseline/system/costCategory/${id}`
	)
}

/**
 * 费用类别-批量启用
 */
export function batchEnableCostCategory(idList: number[] | null) {
	return ReqUtil.post<null>("/baseline/system/costCategory/batchEnable", {
		data: { idList }
	})
}

/**
 * 费用类别-批量冻结
 */
export function batchFreezeCostCategory(idList: number[] | null) {
	return ReqUtil.post<null>("/baseline/system/costCategory/batchFreeze", {
		data: { idList }
	})
}

/**
 * 费用类别-批量解冻
 */
export function batchThawCostCategory(idList: number[] | null) {
	return ReqUtil.post<null>("/baseline/system/costCategory/batchThaw", {
		data: { idList }
	})
}
