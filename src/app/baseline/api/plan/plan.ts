import { request } from "@/app/platform/utils/service"
import {
	AddPlanItemMonthlyRequest,
	AddPlanItemRequest,
	AddPlanRequest,
	GetMatListByPlanIdRequest,
	GetPlanItemMonthlyListByPlanIdAndMaterialIdRequest,
	GetPlanItemMonthlyListRequest,
	GetPlanListRequest,
	UpdPlanItemMonthlyRequest,
	UpdPlanItemRequest,
	UpdPlanRequest
} from "@/app/baseline/api/defines"

// 获取列表
function getList(data: GetPlanListRequest) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/plan/list",
		method: "get",
		params: data
	})
}

// 需求计划数量统计
function getPlanCnt(data: GetPlanListRequest): Promise<any> {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/plan/planTypeStatistics",
		method: "get",
		params: data
	})
}

// 查询需求计划详细
function getInfoById(id: string) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/plan/" + id,
		method: "get"
	})
}

// 新增需求计划
function addPlan(data: AddPlanRequest, idempotentToken?: string) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/plan",
		method: "post",
		data,
		headers: { "idempotent-token": idempotentToken }
	})
}

// 修改需求计划
function updatePlan(data: UpdPlanRequest, _idempotentToken?: string) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/plan",
		method: "put",
		data
	})
}

// 需求计划提交审核
function publishPlan(id: string, idempotentToken?: string) {
	return request<SystemApiResponseData>({
		headers: {
			"Content-Type": "multipart/form-data",
			"idempotent-token": idempotentToken
		},
		url: "/baseline/plan/plan/publish",
		method: "post",
		data: { id }
	})
}

// 删除需求计划
function deletePlan(id: string) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/plan/" + id,
		method: "delete"
	})
}

/**
 * 根据计划编号获取物资列表
 * @param id
 */
function getMatListByPlanId(query: GetMatListByPlanIdRequest) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/planItem/list",
		method: "get",
		params: query
	})
}

// 新增需求计划物资明细
function addPlanItem(data: AddPlanItemRequest) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/planItem",
		method: "post",
		data
	})
}

// 新增需求计划物资明细V2.0 - new
function addPlanItemBatch(data: AddPlanItemRequest, idempotentToken?: string) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/planItem/addBatch",
		method: "post",
		data,
		headers: { "idempotent-token": idempotentToken }
	})
}

// 通过需求清单新增需求计划物资明细
function addPlanItemFromNeedList(
	planId: string,
	planNeedIds: string[],
	idempotentToken?: string
) {
	return request<SystemApiResponseData>({
		headers: {
			"Content-Type": "multipart/form-data",
			"idempotent-token": idempotentToken
		},
		url: "/baseline/plan/planItem/addPlanNeed",
		method: "post",
		data: {
			planId,
			planNeedIds: planNeedIds.join(",")
		}
	})
}

// 删除需求计划物资明细
function deletePlanItem(id: string) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/planItem/" + id,
		method: "delete"
	})
}

// 查询需求计划物资明细详细
function getPlanItem(id: string) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/planItem/" + id,
		method: "get"
	})
}

// 修改需求计划物资明细
function updatePlanItem(data: UpdPlanItemRequest) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/planItem",
		method: "put",
		data
	})
}

// 新增需求计划物资明细-月度
function addPlanItemMonthly(
	data: AddPlanItemMonthlyRequest,
	idempotentToken?: string
) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/planItemMonthly",
		method: "post",
		data,
		headers: { "idempotent-token": idempotentToken }
	})
}

// 修改需求计划物资明细-月度
function updatePlanItemMonthly(data: UpdPlanItemMonthlyRequest) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/planItemMonthly",
		method: "put",
		data
	})
}

// 查询需求计划物资明细-月度列表
function getPlanItemMonthlyList(query: GetPlanItemMonthlyListRequest) {
	query.pageSize = 99
	return request<SystemApiResponseData>({
		url: "/baseline/plan/planItemMonthly/list",
		method: "get",
		params: query
	})
}

// 查询需求计划物资明细-月度列表
function getPlanItemMonthlyListByPlanIdAndMaterialId(
	query: GetPlanItemMonthlyListByPlanIdAndMaterialIdRequest
) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/planItemMonthly/listByPlanIdAndMaterialId",
		method: "get",
		params: query
	})
}

// 查询需求计划物资明细-月度详细
function getPlanItemMonthly(id: string) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/planItemMonthly/" + id,
		method: "get"
	})
}

/**
 * 合并计划选择需求计划
 */

function getListCanPlan(data: GetPlanListRequest) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/plan/listCanPlan",
		method: "get",
		params: data
	})
}
export const PlanApi = {
	getListCanPlan,
	getPlanCnt,
	getList,
	getInfoById,
	addPlan,
	updatePlan,
	deletePlan,
	getMatListByPlanId,
	addPlanItem,
	addPlanItemFromNeedList,
	deletePlanItem,
	getPlanItem,
	updatePlanItem,
	addPlanItemMonthly,
	updatePlanItemMonthly,
	getPlanItemMonthly,
	getPlanItemMonthlyList,
	publishPlan,
	getPlanItemMonthlyListByPlanIdAndMaterialId,
	addPlanItemBatch
}
