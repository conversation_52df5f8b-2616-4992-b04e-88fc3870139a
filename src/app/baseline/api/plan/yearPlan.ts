import { request } from "@/app/platform/utils/service"
import {
	AddYearPlanRequest,
	GetYearPlanListRequest
} from "@/app/baseline/api/defines"

// 获取列表
function getYearPlanList(query: GetYearPlanListRequest) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/planYear/list",
		method: "get",
		params: query
	})
}

// 新建
function addPlan(data: AddYearPlanRequest, idempotent?: string) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/planYear",
		method: "post",
		data,
		headers: { "idempotent-token": idempotent }
	})
}

// 更新
function updatePlan(params: AddYearPlanRequest) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/planYear",
		method: "put",
		data: params
	})
}

// 移除
function deletePlan(id: string) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/planYear/" + id,
		method: "delete"
	})
}
// 启动
function startPlan(id: any) {
	return request<SystemApiResponseData>({
		headers: { "Content-Type": "multipart/form-data" },
		url: "/baseline/plan/planYear/startPlanYear",
		method: "post",
		data: { id }
	})
}
// 结束
function endPlan(id: any) {
	return request<SystemApiResponseData>({
		headers: { "Content-Type": "multipart/form-data" },
		url: "/baseline/plan/planYear/finishPlanYear",
		method: "post",
		data: { id }
	})
}

export const YearPlanApi = {
	getYearPlanList,
	deletePlan,
	updatePlan,
	addPlan,
	startPlan,
	endPlan
}
