import { request } from "@/app/platform/utils/service"
import {
	AddPlanPurchaseRequest,
	GetPlanPurchaseListRequest,
	UpdatePushEcommerceStatusRequest,
	UpdPlanPurchaseRequest
} from "@/app/baseline/api/defines"

// 查询采购计划列表
function getPlanPurchaseList(query: GetPlanPurchaseListRequest) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/planPurchase/list",
		method: "get",
		params: query
	})
}

// 查询采购计划详细
function getPlanPurchase(id: string) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/planPurchase/" + id,
		method: "get"
	})
}

// 新增采购计划
function addPlanPurchase(
	data: AddPlanPurchaseRequest,
	idempotentToken?: string
) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/planPurchase",
		method: "post",
		data,
		headers: { "idempotent-token": idempotentToken }
	})
}

// 修改采购计划
function updatePlanPurchase(
	data: UpdPlanPurchaseRequest,
	_idempotentToken?: string
) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/planPurchase",
		method: "put",
		data
	})
}
// 删除采购计划
function deletePlanPurchase(id: string) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/planPurchase/" + id,
		method: "delete"
	})
}

// 发送电商 - 目前不做
/* function updatePushEcommerceStatus(data: UpdatePushEcommerceStatusRequest) {
	return request<SystemApiResponseData>({
		headers: { "Content-Type": "multipart/form-data" },
		url: "/baseline/plan/planPurchase/sendEcommerce",
		method: "post",
		data
	})
} */
//获取审批状态统计
function getBmpStatusStatistics(query: GetPlanPurchaseListRequest) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/planPurchase/bmpStatusStatistics",
		method: "get",
		params: query
	})
}

//发送审批
function publishApply(data: any, idempotentToken?: string) {
	return request<SystemApiResponseData>({
		headers: {
			"Content-Type": "multipart/form-data",
			"idempotent-token": idempotentToken
		},
		url: "/baseline/plan/planPurchase/publish",
		method: "post",
		data
	})
}

/* 校验采购数量是否等于需求数量 */
function updatePlanPurchaseCheckNum(id: any) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/planPurchase/checkNum",
		method: "post",
		params: { id }
	})
}
export const PlanPurchaseApi = {
	getPlanPurchaseList,
	getPlanPurchase,
	addPlanPurchase,
	updatePlanPurchase,
	deletePlanPurchase,
	//updatePushEcommerceStatus,
	getBmpStatusStatistics,
	publishApply,
	updatePlanPurchaseCheckNum
}
