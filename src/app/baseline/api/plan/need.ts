import { request } from "@/app/platform/utils/service"
import {
	AddNeedRequest,
	AddPlanNeedItemRequest,
	GetMatListByAppIdRequest,
	GetPlanNeedListRequest,
	UpdPlanNeedItemRequest
} from "@/app/baseline/api/defines"

// 获取列表
function getPlanNeedList(query: GetPlanNeedListRequest) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/planNeed/list",
		method: "get",
		params: query
	})
}

function getInfoById(id: string) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/planNeed/" + id,
		method: "get"
	})
}
// 新建
function addNeed(data: AddNeedRequest, idempotentToken?: string) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/planNeed",
		method: "post",
		data,
		headers: { "idempotent-token": idempotentToken }
	})
}

// 更新
function updateNeed(params: AddNeedRequest, _idempotentToken?: string) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/planNeed",
		method: "put",
		data: params
	})
}
// 批量状态更新
function updateNeedStatus(ids: string, status: string) {
	return request<SystemApiResponseData>({
		headers: { "Content-Type": "multipart/form-data" },
		url: "/baseline/plan/planNeed/updateStatusBatch",
		method: "post",
		data: {
			ids,
			status
		}
	})
}

// 移除
function deleteNeed(id: string) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/planNeed/" + id,
		method: "delete"
	})
}

/**
 * 根据需求编号获取物资列表
 * @param id
 */
function getMatListByAppId(query: GetMatListByAppIdRequest) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/planNeedItem/list",
		method: "get",
		params: query
	})
}

/**
 * 新增需求清单物资（单条）
 * @param data
 */
function addPlanNeedItem(data: AddPlanNeedItemRequest) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/planNeedItem",
		method: "post",
		data
	})
}

/**
 * 新增需求清单物资（单条）
 * @param data
 */
function addPlanNeedItemList(
	data: AddPlanNeedItemRequest[],
	idempotentToken?: string
) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/planNeedItem/addBatch",
		method: "post",
		data,
		headers: { "idempotent-token": idempotentToken }
	})
}

// 删除需求清单物资明细
function deletePlanNeedItem(id: string) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/planNeedItem/" + id,
		method: "delete"
	})
}

// 修改需求清单物资明细
function updatePlanNeedItem(data: UpdPlanNeedItemRequest) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/planNeedItem",
		method: "put",
		data
	})
}

/**
 * 复制需求清单
 * @param id
 * @returns
 */
function copyPlanNeed(id: string, idempotentToken?: string) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/planNeed/copy",
		method: "post",
		data: { id },
		headers: { "idempotent-token": idempotentToken }
	})
}

export const NeedApi = {
	getPlanNeedList,
	deleteNeed,
	updateNeed,
	getMatListByAppId,
	addNeed,
	getInfoById,
	addPlanNeedItem,
	addPlanNeedItemList,
	deletePlanNeedItem,
	updatePlanNeedItem,
	updateNeedStatus,
	copyPlanNeed
}
