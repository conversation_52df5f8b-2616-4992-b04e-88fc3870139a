import { request } from "@/app/platform/utils/service"
import {
	AddPlanPurchaseMergeItemRequest,
	GetPlanPurchaseMergeItemListRequest,
	UpdPlanPurchaseMergeItemRequest
} from "@/app/baseline/api/defines"

// 查询采购计划合并计划明细列表
function getPlanPurchaseMergeItemList(
	query: GetPlanPurchaseMergeItemListRequest
) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/planPurchaseMergeItem/list",
		method: "get",
		params: query
	})
}

// 查询采购计划合并计划明细详细
function getPlanPurchaseMergeItem(id: string) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/planPurchaseMergeItem/" + id,
		method: "get"
	})
}

// 新增采购计划合并计划明细
function addPlanPurchaseMergeItem(data: AddPlanPurchaseMergeItemRequest) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/planPurchaseMergeItem",
		method: "post",
		data
	})
}

// 修改采购计划合并计划明细
function updatePlanPurchaseMergeItem(data: UpdPlanPurchaseMergeItemRequest) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/planPurchaseMergeItem",
		method: "put",
		data
	})
}
// 删除采购计划合并计划明细
function deletePlanPurchaseMergeItem(id: string) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/planPurchaseMergeItem/" + id,
		method: "delete"
	})
}
function addBatchPlanPurchaseMergeItem(
	data: AddPlanPurchaseMergeItemRequest[],
	idempotentToken?: string
) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/planPurchaseMergeItem/addBatch",
		method: "post",
		data,
		headers: { "idempotent-token": idempotentToken }
	})
}
export const PlanPurchaseMergeItemAPi = {
	getPlanPurchaseMergeItemList,
	getPlanPurchaseMergeItem,
	addPlanPurchaseMergeItem,
	updatePlanPurchaseMergeItem,
	deletePlanPurchaseMergeItem,
	addBatchPlanPurchaseMergeItem
}
