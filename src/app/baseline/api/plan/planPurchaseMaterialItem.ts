import { request } from "@/app/platform/utils/service"
import {
	BalancePurchaseMaterialItemResponse,
	GetPlanPurchaseMaterialItemListRequest,
	getPlanPurchaseMaterialItemNeedPlanListRequest,
	GetPlanPurchaseNeedPlanListRequest,
	updateBalancePurchaseMaterialItemRequest
} from "@/app/baseline/api/defines"
import { getByPlanIdAndMaterialIdRequest } from "../../utils/types/plan-purchase"

// 查询采购计划物资明细列表
function getPlanPurchaseMaterialItemList(
	query: GetPlanPurchaseMaterialItemListRequest
) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/planPurchaseMaterialItem/list",
		method: "get",
		params: query
	})
}

// 查询采购计划物资明细详细
function getPlanPurchaseMaterialItem(id: string) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/planPurchaseMaterialItem/" + id,
		method: "get"
	})
}

// 查询采购计划的需求计划列表
function getPlanPurchaseNeedPlanList(
	query: GetPlanPurchaseNeedPlanListRequest
) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/planPurchaseMaterialItem/planList",
		method: "get",
		params: query
	})
}
//查询采购计划物资关联需求计划列表
function getPlanPurchaseMaterialItemNeedPlanList(
	query: getPlanPurchaseMaterialItemNeedPlanListRequest
) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/planPurchaseMaterialItem/materialPlanList",
		method: "get",
		params: query
	})
}
// 查询物资对应的 合并计划
function getPlanPurchaseMaterialItemMergePlanList(
	query: getPlanPurchaseMaterialItemNeedPlanListRequest
) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/planPurchaseMaterialItem/materialMergePlanList",
		method: "get",
		params: query
	})
}

//平衡利库
function balancePurchaseMaterialItem(ids: number[]) {
	return request<any>({
		//headers: { "Content-Type": "multipart/form-data" },
		url: "/baseline/plan/planPurchaseMaterialItem/balance",
		method: "post",
		data: { ids }
	})
}

/**
 * 更新 采购计划物资明细
 * @param data
 * @returns
 */
function updateBalancePurchaseMaterialItem(
	data: updateBalancePurchaseMaterialItemRequest
) {
	return request<any>({
		url: "/baseline/plan/planPurchaseMaterialItem",
		method: "put",
		data
	})
}

/**
 * 根据需求计划ID和物资ID获取需求计划物资明细详细信息
 * @param params
 * @returns
 */
function getByPlanIdAndMaterialId(params: getByPlanIdAndMaterialIdRequest) {
	return request<any>({
		url: "/baseline/plan/planItem/getByPlanIdAndMaterialId",
		method: "get",
		params
	})
}
export const PlanPurchaseMaterialItemApi = {
	getPlanPurchaseMaterialItemList,
	getPlanPurchaseMaterialItem,
	getPlanPurchaseNeedPlanList,
	getPlanPurchaseMaterialItemNeedPlanList,
	balancePurchaseMaterialItem,
	getPlanPurchaseMaterialItemMergePlanList,
	updateBalancePurchaseMaterialItem,
	getByPlanIdAndMaterialId
}
