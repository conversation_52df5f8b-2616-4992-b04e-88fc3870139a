import { request } from "@/app/platform/utils/service"
import {
	AddPlanMergeRequest,
	GetPlanMergeListRequest,
	PublishApplyRequest,
	UpdPlanMergeRequest
} from "@/app/baseline/api/defines"

// 查询合并计划列表
function getPlanMergeList(query: GetPlanMergeListRequest) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/planMerge/list",
		method: "get",
		params: query
	})
}

// 查询合并计划详细
function getPlanMerge(id: string) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/planMerge/" + id,
		method: "get"
	})
}

// 新增合并计划
function addPlanMerge(data: AddPlanMergeRequest, idempotentToken?: string) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/planMerge",
		method: "post",
		data,
		headers: { "idempotent-token": idempotentToken }
	})
}

// 修改合并计划
function updatePlanMerge(data: UpdPlanMergeRequest, _idempotentToken?: string) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/planMerge",
		method: "put",
		data
	})
}
// 删除合并计划
function deletePlanMerge(id: string) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/planMerge/" + id,
		method: "delete"
	})
}
//获取审批状态统计
function getBmpStatusStatistics(query: GetPlanMergeListRequest) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/planMerge/bmpStatusStatistics",
		method: "get",
		params: query
	})
}

//发送合并计划审批
function publishApply(data: PublishApplyRequest, idempotentToken?: string) {
	return request<SystemApiResponseData>({
		headers: {
			"Content-Type": "multipart/form-data",
			"idempotent-token": idempotentToken
		},
		url: "/baseline/plan/planMerge/publish",
		method: "post",
		data
	})
}

/**
 * 合并计划选择需求计划
 */

function getListCanMergePlan(data: GetPlanMergeListRequest) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/planMerge/listCanMergePlan",
		method: "get",
		params: data
	})
}
export const PlanMergeApi = {
	getListCanMergePlan,
	getPlanMergeList,
	getPlanMerge,
	addPlanMerge,
	updatePlanMerge,
	deletePlanMerge,
	getBmpStatusStatistics,
	publishApply
}
