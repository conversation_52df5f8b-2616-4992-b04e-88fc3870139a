import { request } from "@/app/platform/utils/service"
import {
	AddPlanMergeItemRequest,
	GetPlanMergeItemListRequest,
	GetPlanMergeItemMaterialListRequest,
	GetPlanMergeItemMaterialNeedPlanListRequest,
	UpdPlanMergeItemRequest
} from "@/app/baseline/api/defines"

// 查询合并计划需求计划明细列表
function getPlanMergeItemList(query: GetPlanMergeItemListRequest) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/planMergeItem/list",
		method: "get",
		params: query
	})
}

// 查询合并计划需求计划明细详细
function getPlanMergeItem(id: string) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/planMergeItem/" + id,
		method: "get"
	})
}

// 新增合并计划需求计划明细
function addPlanMergeItem(data: AddPlanMergeItemRequest) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/planMergeItem",
		method: "post",
		data
	})
}

// 修改合并计划需求计划明细
function updatePlanMergeItem(data: UpdPlanMergeItemRequest) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/planMergeItem",
		method: "put",
		data
	})
}

// 删除合并计划需求计划明细
function deletePlanMergeItem(id: string) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/planMergeItem/" + id,
		method: "delete"
	})
}

// 查询合并计划物资明细列表
function getPlanMergeItemMaterialList(
	query: GetPlanMergeItemMaterialListRequest
) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/planMergeItem/materialList",
		method: "get",
		params: query
	})
}
//查询合并计划物资关联的需求计划列表
function getPlanMergeItemMaterialNeedPlanList(
	query: GetPlanMergeItemMaterialNeedPlanListRequest
) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/planMergeItem/planList",
		method: "get",
		params: query
	})
}

// 新增合并计划需求计划明细
function addBatchPlanMergeItem(
	data: AddPlanMergeItemRequest[],
	idempotentToken?: string
) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/planMergeItem/addBatch",
		method: "post",
		data,
		headers: { "idempotent-token": idempotentToken }
	})
}

export const PlanMergeItemApi = {
	getPlanMergeItemList,
	getPlanMergeItem,
	addPlanMergeItem,
	updatePlanMergeItem,
	deletePlanMergeItem,
	getPlanMergeItemMaterialList,
	getPlanMergeItemMaterialNeedPlanList,
	addBatchPlanMergeItem
}
