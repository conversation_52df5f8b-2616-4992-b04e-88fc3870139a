/**
 * @description 退库申请 api
 */

import { PageResVo } from "../../utils/types/common"

import { MatInStoreApplyInspectDTO } from "../../utils/types/other-warehousing"
import {
	MatReturnStoreApplyAddItemDTO,
	MatReturnStoreApplyBatchInfoVo,
	MatReturnStoreApplyBatchpageReqParams,
	MatReturnStoreApplyBatchpageVo,
	MatReturnStoreApplyDTO,
	MatReturnStoreApplyEditItemDTO,
	MatReturnStoreApplyItemReqParams,
	MatReturnStoreApplyItemVo,
	MatReturnStoreApplyReqParams,
	MatReturnStoreApplyVo
} from "../../utils/types/warehouse-return-apply"

/**
 * 获取状态统计
 */
export function getOutStoreReturnApplyBmpStatusCnt(
	params: MatReturnStoreApplyReqParams
) {
	return request<number[]>({
		url: "/baseline/store/outStoreReturn/bmpStatusStatistics",
		method: "get",
		params
	})
}

/**
 * 退库申请分页
 */
export function listWarehouseReturnApplyPaged(
	params: MatReturnStoreApplyReqParams
) {
	return request<PageResVo<MatReturnStoreApplyVo>>({
		url: "/baseline/store/outStoreReturn/page",
		method: "get",
		params
	})
}

/**
 * 退库申请详情
 */
export function getWarehouseReturnApply(id: any) {
	return request<MatReturnStoreApplyVo>({
		url: "/baseline/store/outStoreReturn/getById",
		method: "get",
		params: { id }
	})
}

/**
 * 退库申请明细分页
 */
export function listWarehouseReturnApplyGoodsPaged(
	params: MatReturnStoreApplyItemReqParams
) {
	return request<PageResVo<MatReturnStoreApplyItemVo>>({
		url: "/baseline/store/outStoreReturn/pageDetail",
		method: "get",
		params
	})
}

/**
 * 新增退库申请
 */
export function saveWarehouseReturnApply(
	data: MatReturnStoreApplyDTO,
	idempotentToken?: string
) {
	return request<MatReturnStoreApplyVo>({
		url: "/baseline/store/outStoreReturn/add",
		method: "post",
		data,
		headers: { "idempotent-token": idempotentToken }
	})
}

/**
 * 更新退库申请
 */
export function updateWarehouseReturnApply(
	data: MatReturnStoreApplyDTO,
	_idempotentToken?: string
) {
	return request<MatReturnStoreApplyVo>({
		url: "/baseline/store/outStoreReturn/edit",
		method: "put",
		data
	})
}

/**
 * 删除退库申请
 */
export function deleteWarehouseReturnApply(ids: string) {
	return request<boolean>({
		url: "/baseline/store/outStoreReturn/delete",
		method: "delete",
		params: { ids }
	})
}

/**
 * 退库申请添加物资
 */
export function addWarehouseReturnApplyGoods(
	data: MatReturnStoreApplyAddItemDTO,
	idempotentToken?: string
) {
	return request<PageResVo<MatReturnStoreApplyItemVo>>({
		url: "/baseline/store/outStoreReturn/addMat",
		method: "post",
		data,
		headers: { "idempotent-token": idempotentToken }
	})
}

/**
 * 退库申请批量编辑物资明细数量
 */
export function updateWarehouseReturnApplyGoods(
	data: MatReturnStoreApplyEditItemDTO
) {
	return request<PageResVo<MatReturnStoreApplyItemVo>>({
		url: "/baseline/store/outStoreReturn/editMat",
		method: "put",
		data
	})
}

/**
 * 退库申请明细移除物资
 */
export function deleteWarehouseReturnApplyGoods(ids: string) {
	return request<boolean>({
		url: "/baseline/store/outStoreReturn/deleteMat",
		method: "delete",
		params: { ids }
	})
}

/**
 * 退库申请 分配质检员
 */
export function addWarehouseReturnApplyInspector(
	data: MatInStoreApplyInspectDTO
) {
	return request<boolean>({
		url: "/baseline/store/outStoreReturn/pushInspectPerson",
		method: "post",
		data
	})
}

/**
 * 推送质检
 */
export function pushWarehouseReturnInspect(id: any, idempotentToken?: string) {
	return request<any>({
		url: "/baseline/store/outStoreReturn/pushInspect",
		method: "post",
		params: { id },
		headers: { "idempotent-token": idempotentToken }
	})
}

/**
 * 退库申请 提交审核
 */
export function submitWarehouseReturnApply(id: any, idempotentToken?: string) {
	return request<any>({
		url: "/baseline/store/outStoreReturn/publish",
		method: "post",
		params: { id },
		headers: { "idempotent-token": idempotentToken }
	})
}

/**
 * 退库申请明细批次分页
 */
export function listOutStoreReturnPageBatchInfoPage(
	params: MatReturnStoreApplyBatchpageReqParams
) {
	return request<PageResVo<MatReturnStoreApplyBatchpageVo>>({
		url: "/baseline/store/outStoreReturn/pageBatchInfo",
		method: "get",
		params
	})
}

/**
 * 退库申请明细批次详情
 */
export function listOutStoreReturnPageBatchInfo(id: number) {
	return request<MatReturnStoreApplyBatchInfoVo>({
		url: "/baseline/store/outStoreReturn/getBatchByItemId",
		method: "get",
		params: { id }
	})
}
