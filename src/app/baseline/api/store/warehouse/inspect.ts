/**
 * @description 入库管理-质量检验 api
 */

import ReqUtil from "../../../utils/request-util"
import { PageResVo } from "../../../utils/types/common"

import {
	EditInspectRequestParams,
	MatInStoreInspectItemRequestParams,
	MatInStoreInspectItemVO,
	MatInStoreInspectRecordVO,
	MatInStoreInspectRecordVORequestParams,
	MatInStoreInspectRequestParams,
	MatInStoreInspectVO,
	TransferInspectRequest
} from "../../../utils/types/store-warehouse-inspect"

/**
 * 获取状态统计
 * @param params
 * @returns
 */
export async function getInStoreInspectBpmStatusCnt(
	params: MatInStoreInspectRequestParams
) {
	return ReqUtil.get<number[]>("/baseline/store/inspect/bmpStatusStatistics", {
		params
	})
}

/**
 * 入库管理 - 质量检验 - 获取列表
 * @param params
 * @returns
 */
export async function getInspectList(params: MatInStoreInspectRequestParams) {
	return ReqUtil.get<PageResVo<MatInStoreInspectVO>>(
		"/baseline/store/inspect/page",
		{ params }
	)
}

/**
 * 获取质检查询详情信息
 */
export const getInspectById = (id: number) => {
	return ReqUtil.get<MatInStoreInspectVO | undefined>(
		"/baseline/store/inspect/getById",
		{ params: { id } }
	)
}

/**
 * 入库管理 - 质量检验 - 详情 - 物资明细
 * @param params
 * @returns
 */
export async function getInspectDetail(
	params: MatInStoreInspectItemRequestParams
) {
	return ReqUtil.get<PageResVo<MatInStoreInspectItemVO>>(
		"/baseline/store/inspect/pageDetail",
		{ params }
	)
}

/**
 * 入库管理 - 质量检验 - 检验- 保存
 * @param data
 * @returns
 */
export async function editInspectMat(data: EditInspectRequestParams) {
	return ReqUtil.put<any>("/baseline/store/inspect/editMat", {
		data
	})
}

/**
 * 入库管理 - 质量检验 - 一键质检- 保存
 * @param data
 * @returns
 */
export async function editInspectMatList(data: EditInspectRequestParams[]) {
	return ReqUtil.put<any>("/baseline/store/inspect/editMatList", {
		data
	})
}

/**
 * 入库管理 - 质量检验 - 检验- 推送入库
 * @param data
 * @returns
 */
export async function pushInspectStore(id: any, idempotentToken?: string) {
	return ReqUtil.post<any>("/baseline/store/inspect/pushInStore", {
		params: { id },
		headers: { "idempotent-token": idempotentToken }
	})
}

/**
 * 入库管理 - 质验记录明细--分页
 * @param params
 * @returns
 */
export async function getInspectRecordList(
	params: MatInStoreInspectRecordVORequestParams
) {
	return ReqUtil.get<PageResVo<MatInStoreInspectRecordVO>>(
		"/baseline/store/inspect/pageRecord",
		{ params }
	)
}

/**
 * 入库管理 - 质量检验 -转派
 * @param data
 * @returns
 */
export async function transferInspect(
	data: TransferInspectRequest,
	idempotentToken?: string
) {
	return ReqUtil.post<any>("/baseline/store/inspect/transfer", {
		params: { ...data },
		headers: { "idempotent-token": idempotentToken }
	})
}

/**
 * 入库管理 - 质量检验--转派记录分页查询
 * @param params
 * @returns
 */
export async function getInspectTransferPage(
	params: MatInStoreInspectRequestParams
) {
	return ReqUtil.get<PageResVo<MatInStoreInspectVO>>(
		"/baseline/store/inspect/pageTransfer",
		{ params }
	)
}
