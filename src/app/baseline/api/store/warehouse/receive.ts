/**
 * @description 入库管理-接收入库 api
 */
import ReqUtil from "../../../utils/request-util"
import { PageResVo } from "../../../utils/types/common"

import {
	EditMatInStoreReceiveRequestParams,
	MatInStoreReceiveBatchRequestParams,
	MatInStoreReceiveBatchVO,
	MatInStoreReceiveDetailRequestParams,
	MatInStoreReceiveDetailVO,
	MatInStoreReceiveRequestParams,
	MatInStoreReceiveVO
} from "../../../utils/types/store-warehouse-receive"

/**
 * 获取状态统计
 * @param params
 * @returns
 */
export async function getInStoreReceiveBpmStatusCnt(
	params: MatInStoreReceiveRequestParams
) {
	return ReqUtil.get<number[]>("/baseline/store/receive/bmpStatusStatistics", {
		params
	})
}

/**
 * 接收入库 - 获取列表
 * @param params
 * @returns
 */
export async function getReceiveList(params: MatInStoreReceiveRequestParams) {
	return ReqUtil.get<PageResVo<MatInStoreReceiveVO>>(
		"/baseline/store/receive/page",
		{ params }
	)
}

/**
 * 获取查询详情信息
 */
export const getReceiveById = (id: number) => {
	return ReqUtil.get<MatInStoreReceiveVO | undefined>(
		"/baseline/store/receive/getById",
		{ params: { id } }
	)
}

/**
 * 接收入库 - 详情 - 物资明细
 * @param params
 * @returns
 */
export async function getReceiveDetail(
	params: MatInStoreReceiveDetailRequestParams
) {
	return ReqUtil.get<PageResVo<MatInStoreReceiveDetailVO>>(
		"/baseline/store/receive/pageDetail",
		{ params }
	)
}

/**
 * 接收入库 - 详情 - 入库 保存
 * @param data
 * @returns
 */
export async function editReceiveInStore(
	data: EditMatInStoreReceiveRequestParams,
	idempotentToken?: string
) {
	return ReqUtil.put<any>("/baseline/store/receive/inStore", {
		data,
		headers: { "idempotent-token": idempotentToken }
	})
}

/**
 * 接收入库 - 详情 - 查询批次
 * @param params
 * @returns
 */
export async function getReceiveBatch(
	params: MatInStoreReceiveBatchRequestParams
) {
	return ReqUtil.get<PageResVo<MatInStoreReceiveBatchVO>>(
		"/baseline/store/receive/searchBatch",
		{ params }
	)
}

/**
 * 接收入库 批量入库
 * @param data
 * @returns
 */
export async function updateReceiveInStoreBatch(
	data: any,
	idempotentToken?: string
) {
	return ReqUtil.post<any>("/baseline/store/receive/inStoreBatch", {
		data,
		headers: { "idempotent-token": idempotentToken }
	})
}
