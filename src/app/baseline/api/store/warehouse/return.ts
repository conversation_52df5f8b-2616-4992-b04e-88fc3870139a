/**
 * @description 入库管理-退库入库 api
 */

import ReqUtil from "../../../utils/request-util"
import { PageResVo } from "../../../utils/types/common"
import {
	EditMatInStoreReturnRequestParams,
	MatInStoreReturnSearchRoomRequestParams,
	MatReturnStoreItemRequestParams,
	MatReturnStoreItemVO,
	MatReturnStoreRequestParams,
	MatReturnStoreVO
} from "../../../utils/types/store-warehouse-return"

/**
 * 获取状态统计
 * @param params
 * @returns
 */
export async function getInStoreReturnBpmStatusCnt(
	params: MatReturnStoreRequestParams
) {
	return ReqUtil.get<number[]>("/baseline/store/return/bmpStatusStatistics", {
		params
	})
}

/**
 * 退库入库 - 获取列表
 * @param params
 * @returns
 */
export async function getReturnList(params: MatReturnStoreRequestParams) {
	return ReqUtil.get<PageResVo<MatReturnStoreVO>>(
		"/baseline/store/return/page",
		{ params }
	)
}

/**
 * 获取查询详情信息
 */
export const getReturnById = (id: number) => {
	return ReqUtil.get<MatReturnStoreVO | undefined>(
		"/baseline/store/return/getById",
		{ params: { id } }
	)
}

/**
 * 退库入库 - 详情 - 物资明细
 * @param params
 * @returns
 */
export async function getReturnDetail(params: MatReturnStoreItemRequestParams) {
	return ReqUtil.get<PageResVo<MatReturnStoreItemVO>>(
		"/baseline/store/return/pageDetail",
		{ params }
	)
}

/**
 * 退库入库 - 详情 - 入库 保存
 * @param data
 * @returns
 */
export async function editReturnInStore(
	data: EditMatInStoreReturnRequestParams,
	idempotentToken?: string
) {
	return ReqUtil.put<any>("/baseline/store/return/inStore", {
		data,
		headers: { "idempotent-token": idempotentToken }
	})
}

/**
 * 退库入库 批量入库
 * @param data
 * @returns
 */
export async function updateReturnInStoreBatch(
	data: any,
	idempotentToken?: string
) {
	return ReqUtil.post<any>("/baseline/store/return/inStoreBatch", {
		data,
		headers: { "idempotent-token": idempotentToken }
	})
}

/**
 * 退库入库 - 详情 - 查询货位
 * @param params
 * @returns
 */
export async function getReturnSearchRoom(
	params: MatInStoreReturnSearchRoomRequestParams
) {
	return ReqUtil.get<PageResVo<MatReturnStoreItemVO>>(
		"/baseline/store/return/searchRoom",
		{ params }
	)
}
/**
 * 退库入库 - 详情 - 查询批次号
 * @param params
 * @returns
 */
export async function getReturnBatchNo(
	params: MatInStoreReturnSearchRoomRequestParams
) {
	return ReqUtil.get<PageResVo<MatReturnStoreItemVO>>(
		"/baseline/store/return/searchBatch",
		{ params }
	)
}
