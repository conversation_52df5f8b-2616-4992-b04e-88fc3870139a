/**
 * @description 入库管理-交旧入库 api
 */
import {
	MatInStoreReceiveItemDTO,
	MatInStoreWasteOldApplyDetailVo,
	MatInStoreWasteOldApplyItemPageVo,
	MatInStoreWasteOldApplyItemPageVoRequest,
	MatInStoreWasteOldApplyPageVo,
	MatInStoreWasteOldApplyPageVoRequest,
	MatInStoreWasteOldItemRoomPageVo,
	MatInStoreWasteOldItemRoomPageVoRequest
} from "@/app/baseline/utils/types/warehouse-handover"
import ReqUtil from "../../../utils/request-util"
import { PageResVo } from "../../../utils/types/common"

/**
 * 获取状态统计
 * @param params
 * @returns
 */
export async function getInStoreWasteOldBpmStatusCnt(
	params: MatInStoreWasteOldApplyPageVoRequest
) {
	return ReqUtil.get<number[]>(
		"/baseline/store/matInStoreWasteOldApply/bmpStatusStatistics",
		{
			params
		}
	)
}

/**
 * 交旧入库 - 获取列表
 * @param params
 * @returns
 */
export async function listMatInStoreWasteOldApplyPage(
	params: MatInStoreWasteOldApplyPageVoRequest
) {
	return ReqUtil.get<PageResVo<MatInStoreWasteOldApplyPageVo>>(
		"/baseline/store/matInStoreWasteOldApply/page",
		{ params }
	)
}

/**
 * 获取查询详情信息
 */
export const matInStoreWasteOldApplyGetById = (id: number) => {
	return ReqUtil.get<MatInStoreWasteOldApplyDetailVo>(
		"/baseline/store/matInStoreWasteOldApply/getById",
		{ params: { id } }
	)
}

/**
 * 交旧入库 - 详情 - 物资明细
 * @param params
 * @returns
 */
export async function matInStoreWasteOldApplyPageDetail(
	params: MatInStoreWasteOldApplyItemPageVoRequest
) {
	return ReqUtil.get<PageResVo<MatInStoreWasteOldApplyItemPageVo>>(
		"/baseline/store/matInStoreWasteOldApply/pageDetail",
		{ params }
	)
}

/**
 * 交旧入库 - 详情 - 入库 保存
 * @param data
 * @returns
 */
export async function updateMatInStoreWasteOldApplyInStore(
	data: MatInStoreReceiveItemDTO,
	idempotentToken?: string
) {
	return ReqUtil.put<any>("/baseline/store/matInStoreWasteOldApply/inStore", {
		data,
		headers: { "idempotent-token": idempotentToken }
	})
}

/**
 * 交旧入库 批量入库
 * @param data
 * @returns
 */
export async function updateMatInStoreWasteOldApplyInStoreBatch(
	data: any,
	idempotentToken?: string
) {
	return ReqUtil.post<any>(
		"/baseline/store/matInStoreWasteOldApply/inStoreBatch",
		{
			data,
			headers: { "idempotent-token": idempotentToken }
		}
	)
}

/**
 * 接收入库 - 详情 - 查询货位
 * @param params
 * @returns
 */
export async function getWasteOldItemRoomInfoPage(
	params: MatInStoreWasteOldItemRoomPageVoRequest
) {
	return ReqUtil.get<PageResVo<MatInStoreWasteOldItemRoomPageVo>>(
		"/baseline/store/matInStoreWasteOldApply/pageItemBatch",
		{ params }
	)
}
