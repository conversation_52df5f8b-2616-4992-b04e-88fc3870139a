/**
 * @description 领料申请 api
 */

import ReqUtil from "../../utils/request-util"
import { PageResVo } from "../../utils/types/common"
import {
	CanPickApplyMaterialCodeParams,
	CanPickApplyMaterialCodeVo,
	MatOutStorePickApplyDTO,
	MatOutStorePickApplyItemBatchDTO,
	MatOutStorePickApplyItemBatchVo,
	MatOutStorePickApplyItemDTO,
	MatOutStorePickApplyItemEditDTO,
	MatOutStorePickApplyItemVo,
	MatOutStorePickApplyVo
} from "../../utils/types/mat-get-apply"
import { MatInStoreOtherApplyItemAddDTO } from "../../utils/types/other-warehousing"

//localhost:9700/baseline/store/matPickApply/publishOver?id=231&code=YY1-SQLL-202405-00040

/**
 * 获取状态统计
 * @param params
 * @returns
 */
export async function getMatPickApplyBpmStatusCnt(
	params: MatOutStorePickApplyDTO
) {
	return ReqUtil.get<number[]>(
		"/baseline/store/matPickApply/bmpStatusStatistics",
		{
			params
		}
	)
}

/**
 * 获取领料申请结果 暂时无用
 */
export const getMatGetApplyResultRollPolling = (params: {
	id: any
	code: string
}) => {
	return ReqUtil.get("/baseline/store/matPickApply/publishOver", { params })
}

/**
 * 获取领料申请明细错误数据
 *
 * @param id 领料申请 id
 */
export function listMatGetApplyErrorGoods(id: number) {
	return request<MatOutStorePickApplyItemVo[]>({
		url: "/baseline/store/matPickApply/getErrorData",
		method: "get",
		params: { id }
	})
}

/**
 * 获取领料申请 物资批次详情
 */
export function getMatPickApplyGoodsBatch(id: number) {
	return request<MatOutStorePickApplyItemVo | undefined>({
		url: "/baseline/store/matPickApply/getBatchByItemId",
		method: "get",
		params: { id }
	})
}

/**
 * 领料申请 出库批次信息分页
 */
export function listMatPickApplyDeliveryBatchPaged(
	params: MatOutStorePickApplyItemBatchDTO
) {
	return request<PageResVo<MatOutStorePickApplyItemBatchVo>>({
		url: "/baseline/store/matPickApply/pageBatch",
		method: "get",
		params
	})
}

/**
 * 领料申请分页
 */
export function listMatPickApplyPaged(params: MatOutStorePickApplyDTO) {
	return request<PageResVo<MatOutStorePickApplyVo>>({
		url: "/baseline/store/matPickApply/page",
		method: "get",
		params
	})
}

/**
 * 获取领料申请详情
 */
export function getMatPickApply(id: number) {
	return request<MatOutStorePickApplyVo | undefined>({
		url: "/baseline/store/matPickApply/getById",
		method: "get",
		params: { id }
	})
}

/**
 * 新增领料申请
 */
export function saveMatPickApply(
	data: MatOutStorePickApplyDTO,
	idempotentToken?: string
) {
	return request<MatOutStorePickApplyVo | undefined>({
		url: "/baseline/store/matPickApply/add",
		method: "post",
		data,
		headers: { "idempotent-token": idempotentToken }
	})
}

/**
 * 编辑领料申请
 */
export function updateMatPickApply(
	data: MatOutStorePickApplyDTO,
	_idempotentToken?: string
) {
	return request<MatOutStorePickApplyVo | undefined>({
		url: "/baseline/store/matPickApply/edit",
		method: "put",
		data
	})
}

/**
 * 删除领料申请
 */
export function deleteMatPickApply(ids: string) {
	return request<MatOutStorePickApplyVo | undefined>({
		url: "/baseline/store/matPickApply/delete",
		method: "delete",
		params: { ids }
	})
}

/**
 * 领料申请明细分页
 */
export function listMatPickApplyGoodsPaged(
	params: MatOutStorePickApplyItemDTO
) {
	return request<PageResVo<MatOutStorePickApplyItemVo>>({
		url: "/baseline/store/matPickApply/pageDetail",
		method: "get",
		params
	})
}

/**
 * 领料申请添加物资
 */
export function addMatPickApplyGoods(
	data: MatInStoreOtherApplyItemAddDTO,
	idempotentToken?: string
) {
	return request<MatOutStorePickApplyItemVo>({
		url: "/baseline/store/matPickApply/addMat",
		method: "post",
		data,
		headers: { "idempotent-token": idempotentToken }
	})
}

/**
 * 领料申请批量编辑物资明细数量
 */
export function updateMatPickApplyGoodsBatch(
	data: MatOutStorePickApplyItemEditDTO
) {
	return request<MatOutStorePickApplyItemVo[] | undefined>({
		url: "/baseline/store/matPickApply/eidtMat",
		method: "put",
		data
	})
}

/**
 * 领料申请明细移除物资
 */
export function deleteMatPickApplyGoods(ids: string) {
	return request<boolean | undefined>({
		url: "/baseline/store/matPickApply/deleteMat",
		method: "delete",
		params: { ids }
	})
}

/**
 * 领料申请 提交审核
 */
export function submitMatPickApply(id: number, idempotentToken?: string) {
	return request<any>({
		url: "/baseline/store/matPickApply/publish",
		method: "post",
		params: { id },
		headers: { "idempotent-token": idempotentToken }
	})
}

/**
 * 是否可以领料交旧
 */
export function canWasteOld(pickApplyId: number) {
	return request<boolean | null>({
		url: "/baseline/store/matPickApply/canWasteOld",
		method: "get",
		params: { pickApplyId }
	})
}

/**
 * 可选领料单分页列表
 */
export function listCanPickApplyPaged(params: MatOutStorePickApplyDTO) {
	return request<PageResVo<MatOutStorePickApplyVo>>({
		url: "/baseline/store/matPickApply/pageCanPickApply",
		method: "get",
		params
	})
}

/**
 * 可选领料申请明细分页
 */
export function listCanPickApplyItemPaged(
	params: CanPickApplyMaterialCodeParams
) {
	return request<PageResVo<CanPickApplyMaterialCodeVo>>({
		url: "/baseline/store/matPickApply/pageCanPickApplyItem",
		method: "get",
		params
	})
}

/**
 * 领料申请添加物资分页
 */
export function listMatPickApplyPageMaterialStore(params: Record<string, any>) {
	return request<PageResVo<MatOutStorePickApplyItemVo>>({
		url: "/baseline/store/matPickApply/pageMaterialStore",
		method: "get",
		params
	})
}
