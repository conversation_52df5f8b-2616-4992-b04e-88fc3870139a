/**
 * @description 库存管理-业务管理-库存查询 api
 */

import ReqUtil from "../../utils/request-util"
import { PageResVo } from "../../utils/types/common"
import {
	MatStoreInTransOrderVo,
	MatStoreScreenAffairVo,
	MatStoreScreenBatchVo,
	MatStoreScreenHisPricerVo,
	MatStoreScreenCommonListReqParams,
	MatStoreScreenListVo,
	MatStoreScreenNumVo,
	MatStoreScreenTreeVo,
	MatStoreScreenListReqParams,
	MatGetInventoryParams,
	ListWarehouseBatchInventoryBatchParams,
	MatStoreScreenBatchDetailVo,
	MatStoreFreezeVoReqParams,
	MatStoreFreezeVo,
	MatStoreScreenStoreVo,
	MatStoreApplyBusinessVo,
	MatStoreScreenAgeVo
} from "../../utils/types/store-inventory"

/**
 * 获取仓库批次中的库存批次(废弃)
 */
export const listWarehouseBatchInventoryBatch = (
	p: ListWarehouseBatchInventoryBatchParams
) => {
	return ReqUtil.get<PageResVo<MatStoreScreenBatchDetailVo>>(
		"/baseline/store/storeScreen/getStoreBatchDetailInfo",
		{ params: p }
	)
}

/**
 * 获取仓库 tree（包含成本中心数据）
 */
export const listStoreWithCostCenterTree = () => {
	return ReqUtil.get<MatStoreScreenTreeVo[]>("/baseline/store/storeScreen/tree")
}

/**
 * 库存查询分页列表
 */
export const listInventoryPaged = (params: MatStoreScreenListReqParams) => {
	return ReqUtil.get<PageResVo<MatStoreScreenListVo>>(
		"/baseline/store/storeScreen/page",
		{ params }
	)
}

/**
 * 获取库存查询详情信息
 */
export const getInventory = (params: MatGetInventoryParams) => {
	return ReqUtil.get<MatStoreScreenListVo | undefined>(
		"/baseline/store/storeScreen/getById",
		{ params }
	)
}

/**
 * 获取库存数量分页
 */
export const listInventoryNumberPaged = (
	params: MatStoreScreenCommonListReqParams
) => {
	return ReqUtil.get<PageResVo<MatStoreScreenNumVo>>(
		"/baseline/store/storeScreen/getStoreNum",
		{ params }
	)
}

/**
 * 获取仓库物资信息
 */
export const listStoreScreenStorePaged = (
	params: MatStoreScreenCommonListReqParams
) => {
	return ReqUtil.get<PageResVo<MatStoreScreenBatchVo>>(
		"/baseline/store/storeScreen/getStore",
		{ params }
	)
}

/**
 * 获取库存事务分页
 */
export const listInventoryBusinessPaged = (
	params: MatStoreScreenCommonListReqParams
) => {
	return ReqUtil.get<PageResVo<MatStoreScreenAffairVo>>(
		"/baseline/store/storeScreen/getStoreAffair",
		{ params }
	)
}

/**
 * 获取库存物资在途订单分页
 */
export const listInventoryOrderInTransitPaged = (
	params: MatStoreScreenCommonListReqParams
) => {
	return ReqUtil.get<PageResVo<MatStoreInTransOrderVo>>(
		"/baseline/store/storeScreen/getStoreInTransOrder",
		{ params }
	)
}

/**
 * 获取库存历史价格分页
 */
export const listInventoryHistoryPricePaged = (
	params: MatStoreScreenCommonListReqParams
) => {
	return ReqUtil.get<PageResVo<MatStoreScreenHisPricerVo>>(
		"/baseline/store/storeScreen/getStoreHisPrice",
		{ params }
	)
}

/**
 * 物资冻结信息查询
 */
export const listInventoryFreezePaged = (params: MatStoreFreezeVoReqParams) => {
	return ReqUtil.get<PageResVo<MatStoreFreezeVo>>(
		"/baseline/store/storeScreen/listFreeze",
		{ params }
	)
}

/**
 * 获取货位物资信息
 */
export const listStoreScreenRoomPaged = (
	params: MatStoreScreenCommonListReqParams
) => {
	return ReqUtil.get<PageResVo<MatStoreScreenStoreVo>>(
		"/baseline/store/storeScreen/getRoom",
		{ params }
	)
}

/**
 * 获取批次物资信息
 */
export const listStoreScreenBatchPaged = (
	params: MatStoreScreenCommonListReqParams
) => {
	return ReqUtil.get<PageResVo<MatStoreScreenStoreVo>>(
		"/baseline/store/storeScreen/getBatch",
		{ params }
	)
}

/**
 * 物资业务单据查询
 */
export const listStoreScreenApplyBusinessPaged = (
	params: MatStoreScreenCommonListReqParams
) => {
	return ReqUtil.get<PageResVo<MatStoreApplyBusinessVo>>(
		"/baseline/store/storeScreen/listApplyBusiness",
		{ params }
	)
}

/**
 * 物资业务单据批次查询
 */
export const listStoreScreenApplyBusinessBatchPaged = (
	params: MatStoreScreenCommonListReqParams
) => {
	return ReqUtil.get<PageResVo<MatStoreScreenBatchVo>>(
		"/baseline/store/storeScreen/listApplyBusinessBatch",
		{ params }
	)
}

/**
 * 物资库存事务批次查询
 */
export const listStoreScreenStoreAffairBatchPaged = (
	params: MatStoreScreenCommonListReqParams
) => {
	return ReqUtil.get<PageResVo<MatStoreScreenBatchVo>>(
		"/baseline/store/storeScreen/listStoreAffairBatch",
		{ params }
	)
}

/**
 * 获取物资库龄
 */
export const listInventoryStoreAgePaged = (
	params: MatStoreScreenCommonListReqParams
) => {
	return ReqUtil.get<PageResVo<MatStoreScreenAgeVo>>(
		"/baseline/store/storeScreen/getStoreAge",
		{ params }
	)
}
