import ReqUtil from "../../utils/request-util"
import { PageResVo } from "../../utils/types/common"
import {
	MatStoreCheckPlanTaskDetailVo,
	MatStoreCheckPlanTaskMaterialDetailPageQueryParams,
	MatStoreCheckPlanTaskMaterialDetailPageVo,
	MatStoreCheckPlanTaskPageSearchResultVo,
	MatStoreInventoryJobCheckMaterialUpdateDTO,
	MatStorePlanTaskResultQueryParams
} from "../../utils/types/inventory-manage"

/**
 * 获取状态统计
 */
export function getStoreCheckPlanTaskBmpStatusCnt(
	params: MatStorePlanTaskResultQueryParams
) {
	return request<number[]>({
		url: "/baseline/store/checkPlanTask/statusStatistics",
		method: "get",
		params
	})
}

/**
 * 盘点任务分页搜索
 */
export const listInventoryJobPaged = (
	params: MatStorePlanTaskResultQueryParams
) => {
	return ReqUtil.get<PageResVo<MatStoreCheckPlanTaskPageSearchResultVo>>(
		"/baseline/store/checkPlanTask/pageSearch",
		{ params }
	)
}

/**
 * 盘点任务详情
 *
 * @param taskId 任务id
 */
export const getInventoryJob = (taskId: any) => {
	return ReqUtil.get<MatStoreCheckPlanTaskDetailVo>(
		"/baseline/store/checkPlanTask/getDetailById",
		{
			params: { taskId }
		}
	)
}

/**
 * 盘点任务 物料明细分页
 *
 * @param taskId 任务id
 */
export const listInventoryJobGoodsPaged = (
	params: MatStoreCheckPlanTaskMaterialDetailPageQueryParams
) => {
	return ReqUtil.get<PageResVo<MatStoreCheckPlanTaskMaterialDetailPageVo>>(
		"/baseline/store/checkPlanTask/pageMaterial",
		{
			params
		}
	)
}

/**
 * 盘点任务 开始盘点任务
 *
 * @param taskId 任务id
 */
export const batchStartInventoryJob = (
	taskId: number,
	idempotentToken?: string
) => {
	return ReqUtil.post("/baseline/store/checkPlanTask/startCheck", {
		data: { taskId },
		headers: { "idempotent-token": idempotentToken }
	})
}

/**
 * 盘点任务 开始复核
 *
 * @param taskId 任务id
 */
export const batchStartSecondCheckInventoryJob = (
	taskId: number,
	idempotentToken?: string
) => {
	return ReqUtil.post("/baseline/store/checkPlanTask/startSecondCheck", {
		data: { taskId },
		headers: { "idempotent-token": idempotentToken }
	})
}

/**
 * 盘点任务 结束盘点任务
 *
 * @param taskId 任务id
 */
export const batchEndInventoryJob = (
	taskId: number,
	idempotentToken?: string
) => {
	return ReqUtil.post("/baseline/store/checkPlanTask/endCheck", {
		data: { taskId },
		headers: { "idempotent-token": idempotentToken }
	})
}

/**
 * 盘点任务 批量财务过账
 *
 * @param data 任务id集合
 */
export const batchInventoryJobFinancialPosting = (data: any[]) => {
	return ReqUtil.post(
		"/baseline/store/checkPlanTask/batchPassFinancialPosting",
		{
			data
		}
	)
}

/**
 * 盘点任务 差异复盘
 *
 * @param taskId 任务 id
 */
export const inventoryJobDiffRecheck = (taskId: any) => {
	return ReqUtil.post("/baseline/store/checkPlanTask/diffRecheck", {
		data: { taskId }
	})
}

/**
 * 盘点任务 更新盘点物料
 */
export const updateInventoryJobGoods = (
	data: MatStoreInventoryJobCheckMaterialUpdateDTO
) => {
	return ReqUtil.post("/baseline/store/checkPlanTask/updateCheckMaterial", {
		data
	})
}

/**
 * 盘点任务 盘点物料提交
 */
export const submitInventoryJobGoods = (
	taskId: number,
	idempotentToken?: string
) => {
	return ReqUtil.post<any>("/baseline/store/checkPlanTask/submitTask", {
		data: { taskId },
		headers: { "idempotent-token": idempotentToken }
	})
}
