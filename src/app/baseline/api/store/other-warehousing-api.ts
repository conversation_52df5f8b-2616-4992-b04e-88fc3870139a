/**
 * @description 其他入库相关接口
 */

import { PageResVo } from "../../utils/types/common"
import {
	MatInStoreApplyInspectDTO,
	MatInStoreOtherApplyDTO,
	MatInStoreOtherApplyItemAddDTO,
	MatInStoreOtherApplyItemDTO,
	MatInStoreOtherApplyItemVo,
	MatInStoreOtherApplyVo
} from "../../utils/types/other-warehousing"

/**
 * 获取状态统计
 */
export function getMatInStoreOtherApplyBmpStatusCnt(
	params: MatInStoreOtherApplyDTO
) {
	return request<number[]>({
		url: "/baseline/store/matInStoreOtherApply/bmpStatusStatistics",
		method: "get",
		params
	})
}

/**
 * 获取其他入库详情
 */
export function getMatInStoreOtherApply(id: any) {
	return request<MatInStoreOtherApplyVo | undefined>({
		url: "/baseline/store/matInStoreOtherApply/getById",
		method: "get",
		params: { id }
	})
}

/**
 * 其他入库分页
 */
export function listMatInStoreOtherApplyPaged(data: MatInStoreOtherApplyDTO) {
	return request<PageResVo<MatInStoreOtherApplyVo>>({
		url: "/baseline/store/matInStoreOtherApply/page",
		method: "get",
		params: data
	})
}
// 获取返修申请物资
export function listRepairAddPageItem<T>(data: T) {
	return request<PageResVo<any>>({
		url: "/baseline/store/wasteRepairApply/pageCanRepairApplyItem",
		method: "get",
		params: data
	})
}

/**
 * 新增其他入库（保存草稿)
 */
export function saveMatInStoreOtherApply(
	data: MatInStoreOtherApplyDTO,
	idempotentToken?: string
) {
	return request<MatInStoreOtherApplyVo>({
		url: "/baseline/store/matInStoreOtherApply/add",
		method: "post",
		data,
		headers: { "idempotent-token": idempotentToken }
	})
}

/**
 * 更新其他入库
 */
export function updateMatInStoreOtherApply(
	data: MatInStoreOtherApplyDTO,
	_idempotentToken?: string
) {
	return request<MatInStoreOtherApplyVo>({
		url: "/baseline/store/matInStoreOtherApply/edit",
		method: "put",
		data
	})
}

/**
 * 删除其他入库
 */
export function deleteMatInStoreOtherApply(ids: string) {
	return request<boolean | undefined>({
		url: "/baseline/store/matInStoreOtherApply/delete",
		method: "delete",
		params: { ids }
	})
}

/**
 * 其他入库物资明细分页
 */
export function listMatInStoreOtherApplyGoodsPaged(
	params?: MatInStoreOtherApplyItemDTO
) {
	return request<PageResVo<MatInStoreOtherApplyItemVo[] | undefined>>({
		url: "/baseline/store/matInStoreOtherApply/pageDetail",
		method: "get",
		params
	})
}

/**
 * 其他入库申请添加物资
 */
export function addMatInStoreOtherApplyGoods(
	data: MatInStoreOtherApplyItemAddDTO,
	idempotentToken?: string
) {
	return request<MatInStoreOtherApplyItemVo | undefined>({
		url: "/baseline/store/matInStoreOtherApply/addMat",
		method: "post",
		data,
		headers: { "idempotent-token": idempotentToken }
	})
}

/**
 * 其他入库批量编辑物资明细数量
 */
export function updateMatInStoreOtherApplyGoodsBatch(data: {
	/**
	 * ID
	 * id
	 */
	id?: number | null
	/**
	 * 数量
	 */
	num?: number | null

	warrantyPeriod: any
}) {
	return request<MatInStoreOtherApplyItemVo>({
		url: "/baseline/store/matInStoreOtherApply/eidtMat",
		method: "put",
		data
	})
}

/**
 * 其他入库申请明细移除物资
 */
export function deleteMatInStoreOtherApplyGoods(idList: Array<any>) {
	return request<MatInStoreOtherApplyItemVo>({
		url: "/baseline/store/matInStoreOtherApply/deleteMat",
		method: "post",
		data: { idList }
	})
}

/**
 * 其他入库申请 分配质检员
 */
export function addMatInStoreOtherApplyInspector(
	data: MatInStoreApplyInspectDTO
) {
	return request<boolean | undefined>({
		url: "/baseline/store/matInStoreOtherApply/pushInspectPerson",
		method: "post",
		data
	})
}

/**
 * 其他入库申请 推送质检
 */
export function pushMatInStoreOtherApplyInspect(
	id: any,
	idempotentToken?: string
) {
	return request<MatInStoreOtherApplyItemVo>({
		url: "/baseline/store/matInStoreOtherApply/pushInspect",
		method: "post",
		params: { id },
		headers: { "idempotent-token": idempotentToken }
	})
}

/**
 * 其他入库申请 提交审核
 *
 * @param id
 */
export function submitMatInStoreOtherApply(
	id: string | number,
	idempotentToken?: string
) {
	return request<MatInStoreOtherApplyItemVo>({
		url: "/baseline/store/matInStoreOtherApply/publish",
		method: "post",
		params: { id },
		headers: { "idempotent-token": idempotentToken }
	})
}

/**
 * 备品备件|随车配件入库可选物资分页查询
 * @param params
 * @returns
 */
export async function listMaterialCodePaged(params: Record<string, any>) {
	return request<PageResVo<any>>({
		url: "/baseline/store/matInStoreOtherApply/listMaterialCode",
		method: "post",
		data: params
	})
}
