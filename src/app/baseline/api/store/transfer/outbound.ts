/**
 * @description 调拨管理-调拨出库 api
 */

import ReqUtil from "@/app/baseline/utils/request-util"
import { PageResVo } from "@/app/baseline/utils/types/common"
import {
	MatStoreAllocationApplyItemRequestParams,
	MatStoreAllocationApplyItemVO
} from "@/app/baseline/utils/types/store-transfer-apply"
import {
	MatStoreAllocationOutRequestParams,
	MatStoreAllocationOutVO
} from "@/app/baseline/utils/types/store-transfer-outbound"

/**
 * 获取状态统计
 */
export function getOutAllocationBmpStatusCnt(
	params: MatStoreAllocationOutRequestParams = {}
) {
	return request<number[]>({
		url: "/baseline/store/allocation/out/bmpStatusStatistics",
		method: "get",
		params
	})
}

/**
 * 获取列表
 * @param params
 */
export async function getAllocationOutPage(
	params: MatStoreAllocationOutRequestParams
) {
	return ReqUtil.get<PageResVo<MatStoreAllocationOutVO>>(
		"/baseline/store/allocation/out/page",
		{ params }
	)
}

/**
 * 调拨出库 - 列表详情
 * @param params
 */
export async function getAllocationOutById(id: number) {
	return ReqUtil.get<PageResVo<MatStoreAllocationOutVO>>(
		"/baseline/store/allocation/out/getById",
		{ params: { id } }
	)
}

/**
 * 物资明细 列表
 * @param params
 */
export async function getAllocationOutPageBatch(
	params: MatStoreAllocationApplyItemRequestParams
) {
	return ReqUtil.get<PageResVo<MatStoreAllocationApplyItemVO>>(
		"/baseline/store/allocation/out/pageBatch",
		{ params }
	)
}

/**
 * 出库
 * @param data
 */
export async function updateAllocationOutAllocation(id: number) {
	return ReqUtil.put<MatStoreAllocationOutVO>(
		"/baseline/store/allocation/out/outAllocation",
		{
			data: { id }
		}
	)
}

/**
 * 关闭
 * @param data
 */
export async function closeAllocationOutAllocation(id: number) {
	return ReqUtil.put<MatStoreAllocationOutVO>(
		"/baseline/store/allocation/out/close",
		{
			data: { id }
		}
	)
}
