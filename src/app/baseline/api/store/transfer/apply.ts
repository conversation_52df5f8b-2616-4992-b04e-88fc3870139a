/**
 * @description 调拨管理-调拨申请 api
 */

import {
	MatStoreRoomQueryParams,
	MatStoreRoomVo
} from "@/app/baseline/utils/types/store-manage"
import ReqUtil from "../../../utils/request-util"
import { PageResVo } from "../../../utils/types/common"

import {
	BatchMaterialRequestParams,
	DelMatTransferApplyRequestParams,
	DelTransferApplyRequestParams,
	MatStoreAllocationApplyByIdRequestParams,
	MatStoreAllocationApplyDTO,
	MatStoreAllocationApplyItemAddResp,
	MatStoreAllocationApplyItemDTORequestParams,
	MatStoreAllocationApplyItemRequestParams,
	MatStoreAllocationApplyItemVO,
	MatStoreAllocationApplyRequestParams,
	MatStoreAllocationApplyVO,
	MatStoreFreezeQueryParams
} from "../../../utils/types/store-transfer-apply"
import { MatStoreFreezeVo } from "@/app/baseline/utils/types/store-inventory"

/**
 * 获取状态统计
 */
export function getAllocationBmpStatusCnt(
	params: MatStoreAllocationApplyRequestParams = {}
) {
	return request<number[]>({
		url: "/baseline/store/allocation/apply/bmpStatusStatistics",
		method: "get",
		params
	})
}

/**
 * 调拨管理 - 调拨申请 - 获取列表
 * @param params
 */
export async function getTransferApplyList(
	params: MatStoreAllocationApplyRequestParams
) {
	return ReqUtil.get<PageResVo<MatStoreAllocationApplyVO>>(
		"/baseline/store/allocation/apply/page",
		{ params }
	)
}

/**
 * 调拨管理 - 调拨申请 - 列表详情
 * @param params
 */
export async function getTransferApplyById(
	params: MatStoreAllocationApplyByIdRequestParams
) {
	return ReqUtil.get<PageResVo<MatStoreAllocationApplyVO>>(
		"/baseline/store/allocation/apply/getById",
		{ params }
	)
}

/**
 * 调拨管理 - 调拨申请 - 详情- 物资明细列表
 * @param params
 */
export async function getTransferApplyDetail(
	params: MatStoreAllocationApplyItemRequestParams
) {
	return ReqUtil.get<PageResVo<MatStoreAllocationApplyItemVO>>(
		"/baseline/store/allocation/apply/pageBatch",
		{ params }
	)
}

/**
 * 调拨管理 - 调拨申请 - 详情- 物资列表 根据出库ID查物资列表
 * @param params {storeId}
 */
export async function getMatTransferApplyList(
	params: BatchMaterialRequestParams
) {
	return ReqUtil.get<PageResVo<MatStoreAllocationApplyItemVO>>(
		"/baseline/store/allocation/apply/getBatchMaterial",
		{ params }
	)
}

/**
 * 调拨管理 - 调拨申请 - 添加 -保存草稿
 * @param data
 */
export async function saveTransferApply(
	data: MatStoreAllocationApplyDTO,
	idempotentToken?: string
) {
	return ReqUtil.post<MatStoreAllocationApplyVO>(
		"/baseline/store/allocation/apply/add",
		{ data, headers: { "idempotent-token": idempotentToken } }
	)
}

/**
 * 调拨管理 - 调拨申请 - 编辑 -保存草稿
 * @param data
 */
export async function updateTransferApply(
	data: MatStoreAllocationApplyDTO,
	_idempotentToken?: string
) {
	return ReqUtil.post<MatStoreAllocationApplyVO>(
		"/baseline/store/allocation/apply/edit",
		{ data }
	)
}

/**
 * 调拨管理 - 调拨申请 - 删除
 * @param data {id: '1,2,3'}
 */
export async function delTransferApply(data: DelTransferApplyRequestParams) {
	return ReqUtil.delete<boolean>("/baseline/store/allocation/apply/delete", {
		data
	})
}

/**
 * 调拨管理 - 调拨申请 明细 - 删除
 * @param data {ids: '1,2,3'}
 */
export async function delMatTransferApply(
	data: DelMatTransferApplyRequestParams
) {
	return ReqUtil.delete<boolean>(
		"/baseline/store/allocation/apply/deleteDetail",
		{
			data
		}
	)
}

/**
 * 调拨管理 - 调拨申请 - 提交审批
 * @param data {id: '1}
 */
export async function publishTransferApply(id: any, idempotentToken?: string) {
	return ReqUtil.post<any>("/baseline/store/allocation/apply/publish", {
		params: { id },
		headers: { "idempotent-token": idempotentToken }
	})
}

/**
 * 调拨管理 - 调拨申请 - 添加物资
 * @param data
 * {isOutOfStore:true, ids:[],item:[]}
 */
export async function addMatTransferApply(
	data: MatStoreAllocationApplyItemDTORequestParams,
	idempotentToken?: string
) {
	return ReqUtil.post<MatStoreAllocationApplyItemAddResp>(
		"/baseline/store/allocation/apply/addDetail",
		{
			data,
			headers: { "idempotent-token": idempotentToken }
		}
	)
}

/**
 * 调拨管理 - 调拨申请 - 编辑物资即添加货位 & 修改移库数量
 * @param data
 */
export async function editMatTransferApply(data: Record<string, any>) {
	return ReqUtil.put<MatStoreAllocationApplyItemVO>(
		"/baseline/store/allocation/apply/editDetail",
		{
			data
		}
	)
}

/**
 *	调拨申请 -详细 添加货位 过滤来源货位 (废弃)
 */
export async function getTransferApplyStoreRoomPage(
	params: MatStoreRoomQueryParams
) {
	return ReqUtil.get<PageResVo<MatStoreRoomVo>>(
		"/baseline/store/allocation/apply/storeRoomPage",
		{ params }
	)
}

/**
 *	调拨申请--物资明细--冻结数量下钻
 */
export async function getTransferApplyListFreezePage(
	params: MatStoreFreezeQueryParams
) {
	return ReqUtil.get<PageResVo<MatStoreFreezeVo>>(
		"/baseline/store/allocation/apply/listFreeze",
		{ params }
	)
}
