/**
 * @description 调拨管理-调拨入库 api
 */

import ReqUtil from "@/app/baseline/utils/request-util"
import { PageResVo } from "@/app/baseline/utils/types/common"
import {
	MatStoreAllocationApplyItemRequestParams,
	MatStoreAllocationApplyItemVO
} from "@/app/baseline/utils/types/store-transfer-apply"
import {
	MatStoreAllocationEditRoomDTO,
	MatStoreAllocationInRequestParams,
	MatStoreAllocationInVO
} from "@/app/baseline/utils/types/store-transfer-receipt"

/**
 * 获取状态统计
 */
export function getInAllocationBmpStatusCnt(
	params: MatStoreAllocationInRequestParams = {}
) {
	return request<number[]>({
		url: "/baseline/store/allocation/in/bmpStatusStatistics",
		method: "get",
		params
	})
}

/**
 * 获取列表
 * @param params
 */
export async function getAllocationInPage(
	params: MatStoreAllocationInRequestParams
) {
	return ReqUtil.get<PageResVo<MatStoreAllocationInVO>>(
		"/baseline/store/allocation/in/page",
		{ params }
	)
}

/**
 * 调拨入库 - 列表详情
 * @param params
 */
export async function getAllocationInById(id: number) {
	return ReqUtil.get<PageResVo<MatStoreAllocationInVO>>(
		"/baseline/store/allocation/in/getById",
		{ params: { id } }
	)
}

/**
 * 物资明细 列表
 * @param params
 */
export async function getAllocationInPageBatch(
	params: MatStoreAllocationApplyItemRequestParams
) {
	return ReqUtil.get<PageResVo<MatStoreAllocationApplyItemVO>>(
		"/baseline/store/allocation/in/pageBatch",
		{ params }
	)
}

/**
 * 入库
 * @param data
 */
export async function updateAllocationInAllocation(
	id: number,
	idempotentToken?: string
) {
	return ReqUtil.put<any>("/baseline/store/allocation/in/inAllocation", {
		data: { id },
		headers: { "idempotent-token": idempotentToken }
	})
}

/**
 * 关闭
 * @param data{ids:number[], reason:string}
 */
export async function closeAllocationInAllocation(
	data: Record<string, any>,
	idempotentToken?: string
) {
	return ReqUtil.post<any>("/baseline/store/allocation/in/close", {
		data,
		headers: { "idempotent-token": idempotentToken }
	})
}

/**
 * 调拨入库--更新货位
 * @param data
 */
export async function editRoomAllocationInAllocation(
	data: MatStoreAllocationEditRoomDTO
) {
	return ReqUtil.post<any>("/baseline/store/allocation/in/editRoom", {
		data
	})
}
