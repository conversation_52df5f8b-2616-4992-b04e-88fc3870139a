/**
 * @description 退货申请 api
 */

import ReqUtil from "../../utils/request-util"
import { PageResVo } from "../../utils/types/common"
import {
	MatOutStoreRemoveApplyDTO,
	MatOutStoreRemoveApplyItemAddDTO,
	MatOutStoreRemoveApplyItemReqParams,
	MatOutStoreRemoveApplyItemVo,
	MatOutStoreRemoveApplyReqParams,
	MatOutStoreRemoveApplyVo,
	MatReturnStoreApplyEditItemDTO
} from "../../utils/types/goods-return-apply"

/**
 * 获取状态统计
 */
export function getMatOutStoreRemoveApplyBmpStatusCnt(
	params: MatOutStoreRemoveApplyReqParams
) {
	return request<number[]>({
		url: "/baseline/store/outStoreRemove/bmpStatusStatistics",
		method: "get",
		params
	})
}

/**
 * 新增退货申请
 */
export async function saveGoodsReturnApply(
	data: MatOutStoreRemoveApplyDTO,
	idempotentToken?: string
) {
	return ReqUtil.post<MatOutStoreRemoveApplyVo>(
		"/baseline/store/outStoreRemove/add",
		{ data, headers: { "idempotent-token": idempotentToken } }
	)
}

/**
 * 删除退货申请
 */
export async function deleteGoodsReturnApply(ids: any) {
	return ReqUtil.delete<boolean>("/baseline/store/outStoreRemove/delete", {
		params: { ids }
	})
}

/**
 * 更新退货申请
 */
export async function updateGoodsReturnApply(
	data: MatOutStoreRemoveApplyDTO,
	_idempotentToken?: string
) {
	return ReqUtil.put<MatOutStoreRemoveApplyVo>(
		"/baseline/store/outStoreRemove/edit",
		{ data }
	)
}

/**
 * 获取退货申请详情
 */
export async function getGoodsReturnApply(id: any) {
	return ReqUtil.get<MatOutStoreRemoveApplyVo>(
		"/baseline/store/outStoreRemove/getById",
		{ params: { id } }
	)
}

/**
 * 退货申请分页
 */
export async function listGoodsReturnApplyPaged(
	params: MatOutStoreRemoveApplyReqParams
) {
	return ReqUtil.get<PageResVo<MatOutStoreRemoveApplyVo>>(
		"/baseline/store/outStoreRemove/page",
		{ params }
	)
}

/**
 * 新增退货申请物资
 */
export async function addGoodsReturnApplyGoods(
	data: MatOutStoreRemoveApplyItemAddDTO,
	idempotentToken?: string
) {
	return ReqUtil.post<MatOutStoreRemoveApplyItemVo>(
		"/baseline/store/outStoreRemove/addMat",
		{ data, headers: { "idempotent-token": idempotentToken } }
	)
}

/**
 * 删除退货申请物资
 */
export async function deleteGoodsReturnApplyGoods(idList: any[]) {
	return ReqUtil.delete<boolean>("/baseline/store/outStoreRemove/deleteMat", {
		data: { idList }
	})
}

/**
 * 编辑退货申请物资数量
 */
export async function updateGoodsReturnApplyGoodsNum(
	data: MatReturnStoreApplyEditItemDTO
) {
	return ReqUtil.put<boolean>("/baseline/store/outStoreRemove/eidtMat", {
		data
	})
}

/**
 * 获取退货申请物资分页
 */
export async function listGoodsReturnApplyGoodsPaged(
	params: MatOutStoreRemoveApplyItemReqParams
) {
	return ReqUtil.get<PageResVo<MatOutStoreRemoveApplyItemVo>>(
		"/baseline/store/outStoreRemove/pageDetail",
		{
			params
		}
	)
}

/**
 * 获取退货申请物资选择器 表格数据分页
 */
export async function listGoodsReturnApplyGoodsOptionsPaged(
	params: MatOutStoreRemoveApplyItemReqParams
) {
	return ReqUtil.get<PageResVo<MatOutStoreRemoveApplyItemVo>>(
		"/baseline/store/outStoreRemove/pageDetailBatch",
		{
			params
		}
	)
}

/**
 * 退货申请 提交审核
 */
export async function submitGoodsReturnApply(
	id: any,
	idempotentToken?: string
) {
	return ReqUtil.post<any>("/baseline/store/outStoreRemove/publish", {
		params: { id },
		headers: { "idempotent-token": idempotentToken }
	})
}
