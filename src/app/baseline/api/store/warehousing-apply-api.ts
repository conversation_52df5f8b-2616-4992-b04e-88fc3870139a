/**
 * @description 入库申相关接口
 */

import { PageResVo } from "../../utils/types/common"
import {
	BatchPushWarehousingInspectRequest,
	CloseMatInStoreApplyRequest,
	MatInStoreApplyItemListQueryParams,
	MatInStoreApplyItemVo,
	MatInStoreApplyRequestParams,
	MatInStoreApplyVo,
	PushWarehousingInspectDto
} from "../../utils/types/warehousing-apply"

/**
 * 获取状态统计
 */
export function getMatInStoreApplyBmpStatusCnt(
	params: MatInStoreApplyRequestParams = {}
) {
	return request<number[]>({
		url: "/baseline/store/matInStoreApply/bmpStatusStatistics",
		method: "get",
		params
	})
}

/**
 * 查询入库申请分页
 */
export function listMatInStoreApplyPaged(
	params: MatInStoreApplyRequestParams = {}
) {
	return request<PageResVo<MatInStoreApplyVo>>({
		url: "/baseline/store/matInStoreApply/page",
		method: "get",
		params
	})
}

/**
 * 查询入库申请详情
 */
export function getMatInStoreApply(id: any) {
	return request<MatInStoreApplyVo | undefined>({
		url: "/baseline/store/matInStoreApply/getById",
		method: "get",
		params: { id }
	})
}

/**
 * 查询入库申请物资明细分页
 */
export function listMatInStoreApplyGoodsPaged(
	params: MatInStoreApplyItemListQueryParams = {}
) {
	return request<PageResVo<MatInStoreApplyItemVo>>({
		url: "/baseline/store/matInStoreApply/pageDetail",
		method: "get",
		params
	})
}

/**
 * 分配质检员
 */
export function addWarehousingInspector(data: {
	/**
	 * 明细id
	 */
	itemIds?: number[] | null
	/**
	 * 用户id
	 */
	userId?: null | string
}) {
	return request<boolean | undefined>({
		url: "/baseline/store/matInStoreApply/pushInspectPerson",
		method: "post",
		data
	})
}

/**
 * 推送质检
 */
export function pushWarehousingInspect(
	data: PushWarehousingInspectDto = {},
	idempotentToken?: string
) {
	return request<PageResVo<MatInStoreApplyVo>>({
		url: "/baseline/store/matInStoreApply/pushInspect",
		method: "post",
		params: data,
		headers: { "idempotent-token": idempotentToken }
	})
}

/**
 * 批量推送质检
 * @param data
 * @param idempotentToken
 * @returns
 */
export function batchPushWarehousingInspect(
	data: BatchPushWarehousingInspectRequest = {},
	idempotentToken?: string
) {
	return request<PageResVo<null>>({
		url: "/baseline/store/matInStoreApply/batchPushInspect",
		method: "post",
		data,
		headers: { "idempotent-token": idempotentToken }
	})
}

/**
 * 关闭
 * @param data
 * @returns
 */
export function closeMatInStoreApply(
	data: CloseMatInStoreApplyRequest,
	idempotentToken?: string
) {
	return request<null>({
		url: "/baseline/store/matInStoreApply/close",
		method: "post",
		data,
		headers: { "idempotent-token": idempotentToken }
	})
}
