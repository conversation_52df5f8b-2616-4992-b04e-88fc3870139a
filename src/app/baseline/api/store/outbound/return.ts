/**
 * @description 出库管理-退货出库 api
 */

import ReqUtil from "../../../utils/request-util"
import { PageResVo } from "../../../utils/types/common"

import {
	EditMatOutStoreOutRequestParams,
	MatOutStoreRemoveItemRequestParams,
	MatOutStoreRemoveItemVO,
	MatOutStoreRemoveRequestParams,
	MatOutStoreRemoveVO
} from "../../../utils/types/store-outbound-return"

/**
 * 获取状态统计
 * @param params
 * @returns
 */
export async function getStoreRemoveBpmStatusCnt(
	params: MatOutStoreRemoveRequestParams
) {
	return ReqUtil.get<number[]>("/baseline/store/remove/bmpStatusStatistics", {
		params
	})
}

/**
 * 退货出库 - 获取列表
 * @param params
 * @returns
 */
export async function getStoreRemoveList(
	params: MatOutStoreRemoveRequestParams
) {
	return ReqUtil.get<PageResVo<MatOutStoreRemoveVO>>(
		"/baseline/store/remove/page",
		{ params }
	)
}

/**
 * 获取查询详情信息
 */
export const getStoreRemoveById = (id: number) => {
	return ReqUtil.get<MatOutStoreRemoveVO | undefined>(
		"/baseline/store/remove/getById",
		{ params: { id } }
	)
}

/**
 * 退货出库 - 详情 - 物资明细
 * @param params
 * @returns
 */
export async function getStoreRemoveDetail(
	params: MatOutStoreRemoveItemRequestParams
) {
	return ReqUtil.get<PageResVo<MatOutStoreRemoveItemVO>>(
		"/baseline/store/remove/pageDetail",
		{ params }
	)
}

/**
 * 退货出库 - 出库 保存
 * @param data
 * @returns
 */
export async function updateStoreRemove(
	data: EditMatOutStoreOutRequestParams,
	idempotentToken?: string
) {
	return ReqUtil.put<any>("/baseline/store/remove/out", {
		data,
		headers: { "idempotent-token": idempotentToken }
	})
}

/**
 * 退货出库 - 出库 关闭
 * @param data
 * @returns
 */
export async function updateStoreRemoveClose(
	data: EditMatOutStoreOutRequestParams,
	idempotentToken?: string
) {
	return ReqUtil.put<MatOutStoreRemoveVO>("/baseline/store/remove/close", {
		data,
		headers: { "idempotent-token": idempotentToken }
	})
}
