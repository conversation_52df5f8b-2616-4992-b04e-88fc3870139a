/**
 * @description 出库管理 - 领料出库 api
 */

import ReqUtil from "../../../utils/request-util"
import { PageResVo } from "../../../utils/types/common"

import {
	BatchMatOutStorePickCloseRequestParams,
	MatOutStorePickBatchVO,
	MatOutStorePickCloseRequestParams,
	MatOutStorePickItemRequestParams,
	MatOutStorePickItemVO,
	MatOutStorePickOutRequestParams,
	MatOutStorePickRequestParams,
	MatOutStorePickRoomInfoVo,
	MatOutStorePickRoompageDto,
	MatOutStorePickRoompageVo,
	MatOutStorePickSearchBatchRequestParams,
	MatOutStorePickVO,
	MatPickRegionRoomVO
} from "../../../utils/types/store-outbound-material"

/**
 * 获取状态统计
 * @param params
 * @returns
 */
export async function getMatPickOutBpmStatus(
	params: MatOutStorePickRequestParams
) {
	return ReqUtil.get<number[]>("/baseline/store/matPick/bmpStatusStatistics", {
		params
	})
}
/**
 * 领料出库 - 获取列表
 * @param params
 * @returns
 */
export async function getMatPickApplyList(
	params: MatOutStorePickRequestParams
) {
	return ReqUtil.get<PageResVo<MatOutStorePickVO>>(
		"/baseline/store/matPick/page",
		{ params }
	)
}

/**
 * 获取查询详情信息
 */
export const getMatPickById = (id: number) => {
	return ReqUtil.get<MatOutStorePickVO | undefined>(
		"/baseline/store/matPick/getById",
		{ params: { id } }
	)
}

/**
 * 领料出库- 详情 - 物资明细
 * @param params
 * @returns
 */
export async function getMatPickApplyDetail(
	params: MatOutStorePickItemRequestParams
) {
	return ReqUtil.get<PageResVo<MatOutStorePickItemVO>>(
		"/baseline/store/matPick/pageDetail",
		{ params }
	)
}

/**
 * 一键出库 - 单个
 * @param data
 * @returns
 */
export async function matPickOutStoreAll(
	applyId: number | null,
	idempotentToken?: string
) {
	return ReqUtil.put<any>("/baseline/store/matPick/outStoreAll", {
		data: { applyId },
		headers: { "idempotent-token": idempotentToken }
	})
}

/**
 * 一键出库 - 批量
 * @param data
 * @returns
 */
export async function batchMatPickOutStoreAll(
	ids: string,
	idempotentToken?: string
) {
	return ReqUtil.put<any>("/baseline/store/matPick/batchOutStoreAll", {
		data: { ids },
		headers: { "idempotent-token": idempotentToken }
	})
}

/**
 * 部分出库 保存
 * @param data
 * @returns
 */
export async function matPickOutStoreHalf(
	data: MatOutStorePickOutRequestParams,
	idempotentToken?: string
) {
	return ReqUtil.put<MatOutStorePickItemVO>(
		"/baseline/store/matPick/outStoreHalf",
		{
			data,
			headers: { "idempotent-token": idempotentToken }
		}
	)
}

/**
 * 领料出库 - 详情 - 查询批次
 * @param params
 * @returns
 */
export async function getMatPickSearchBatch(
	params: MatOutStorePickSearchBatchRequestParams
) {
	return ReqUtil.get<PageResVo<MatOutStorePickItemVO>>(
		"/baseline/store/matPick/searchBatch",
		{ params }
	)
}

/**
 * 领料出库 - 详情 - 查询货位
 * @param params
 * @returns
 */
export async function getMatPickSearchRoomInfoPage(
	params: MatOutStorePickRoompageDto
) {
	return ReqUtil.get<PageResVo<MatOutStorePickRoompageVo>>(
		"/baseline/store/matPick/pageRoomInfo",
		{ params }
	)
}

/**
 * 领料出库 - 详情 - 查询货位
 * @param params
 * @returns
 */
export async function getMatPickRoomInfo(id: number) {
	return ReqUtil.get<PageResVo<MatOutStorePickRoomInfoVo>>(
		"/baseline/store/matPick/getRoomInfo",
		{ params: { id } }
	)
}

/**
 * 领料出库 - 关闭 - 单个
 * @param data
 * @returns
 */
export async function updateMatPickClose(
	data: MatOutStorePickCloseRequestParams
) {
	return ReqUtil.put<MatOutStorePickVO>("/baseline/store/matPick/close", {
		data
	})
}

/**
 * 领料出库 - 关闭 - 批量
 * @param data
 * @returns
 */
export async function batchUpdateMatPickClose(
	data: BatchMatOutStorePickCloseRequestParams,
	idempotentToken?: string
) {
	return ReqUtil.put<MatOutStorePickVO>("/baseline/store/matPick/batchClose", {
		data,
		headers: { "idempotent-token": idempotentToken }
	})
}

/**
 * 领料出库--查询本次出库的区域和货位
 * @param params
 * @returns
 */
export async function getMatPickRegionRoom(
	params: MatOutStorePickOutRequestParams
) {
	return ReqUtil.get<MatPickRegionRoomVO>(
		"/baseline/store/matPick/getRegionRoom",
		{ params }
	)
}

/**
 * 领料出库--查询本次出库的区域和货位的分页列表
 * @param params
 * @returns
 */
export async function getRegionRoomListPaged(
	params: MatOutStorePickOutRequestParams
) {
	return ReqUtil.get<PageResVo<MatOutStorePickBatchVO>>(
		"/baseline/store/matPick/pageRegionRoomList",
		{ params }
	)
}
