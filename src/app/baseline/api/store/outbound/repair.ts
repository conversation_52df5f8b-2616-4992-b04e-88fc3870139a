/**
 * @description 出库管理-返修出库 api
 */

import {
	MatOutStoreRepairDetailVO,
	MatOutStoreRepairItemBatchPageVo,
	MatOutStoreRepairItemPageVO,
	MatOutStoreRepairPageVO,
	MatOutStoreRepairPageVORequest
} from "@/app/baseline/utils/types/store-outbound-repair"
import ReqUtil from "../../../utils/request-util"
import { PageResVo } from "../../../utils/types/common"

/**
 * 获取状态统计
 * @param params
 * @returns
 */
export async function getRepairOutBpmStatusCnt(
	params: MatOutStoreRepairPageVORequest
) {
	return ReqUtil.get<number[]>(
		"/baseline/store/repairOut/bmpStatusStatistics",
		{
			params
		}
	)
}

/**
 * 返修出库 - 获取列表
 * @param params
 * @returns
 */
export async function listRepairOutPaged(
	params: MatOutStoreRepairPageVORequest
) {
	return ReqUtil.get<PageResVo<MatOutStoreRepairPageVO>>(
		"/baseline/store/repairOut/page",
		{ params }
	)
}

/**
 * 获取查询详情信息
 * id:返修出库 id
 */
export const getStoreRepairOutById = async (id: number) => {
	return ReqUtil.get<MatOutStoreRepairDetailVO>(
		"/baseline/store/repairOut/getById",
		{ params: { id } }
	)
}

/**
 * 退货出库 - 详情 - 物资明细
 * @param params
 * @returns
 */
export async function listRepairOutItemPaged(params: Record<string, any>) {
	return ReqUtil.get<PageResVo<MatOutStoreRepairItemPageVO>>(
		"/baseline/store/repairOut/pageDetail",
		{ params }
	)
}

/**
 * 退货出库 - 出库 保存
 * @param data
 * @returns
 */
export async function updateStoreRepairOut(
	id: number,
	idempotentToken?: string
) {
	return ReqUtil.put<any>("/baseline/store/repairOut/out", {
		data: { id },
		headers: { "idempotent-token": idempotentToken }
	})
}

/**
 * 返修出库 - 货位 - 货位 分页
 * @param params {id: 明细id, ...分页相关}
 * @returns
 */
export async function listRepairOutItemRoomPaged(params: Record<string, any>) {
	return ReqUtil.get<PageResVo<MatOutStoreRepairItemBatchPageVo>>(
		"/baseline/store/repairOut/pageBatch",
		{ params }
	)
}

/**
 * 返修出库 - 货位 - 明细详情
 * id: 返修出库明细id
 */
export const getStoreRepairOutItemById = async (id: number) => {
	return ReqUtil.get<MatOutStoreRepairItemPageVO>(
		"/baseline/store/repairOut/getItemById",
		{ params: { id } }
	)
}

/**
 * 返修出库 - 出库 关闭
 * @param data {ids: string,reason:string}
 * @returns
 */
export async function updateStoreRepairOutClose(
	data: Record<string, any>,
	idempotentToken?: string
) {
	return ReqUtil.put<undefined>("/baseline/store/repairOut/close", {
		data,
		headers: { "idempotent-token": idempotentToken }
	})
}
