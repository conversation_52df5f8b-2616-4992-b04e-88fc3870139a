/**
 * @description 出库管理 - 其他出库 api
 */

import ReqUtil from "../../../utils/request-util"
import { PageResVo } from "../../../utils/types/common"

import {
	MatOutStorePickItemRequestParams,
	MatOutStorePickItemVO,
	MatOutStorePickRequestParams,
	MatOutStorePickRoomInfoVo,
	MatOutStorePickRoompageDto,
	MatOutStorePickRoompageVo,
	MatOutStorePickSearchBatchRequestParams,
	MatOutStorePickVO
} from "../../../utils/types/store-outbound-material"

/**
 * 获取状态统计
 * @param params
 * @returns
 */
export async function getMatOutOtherBpmStatus(
	params: MatOutStorePickRequestParams
) {
	return ReqUtil.get<number[]>(
		"/baseline/store/matOutStoreOther/bmpStatusStatistics",
		{
			params
		}
	)
}
/**
 * 其他出库 - 获取列表
 * @param params
 * @returns
 */
export async function getMatOutOtherList(params: MatOutStorePickRequestParams) {
	return ReqUtil.get<PageResVo<MatOutStorePickVO>>(
		"/baseline/store/matOutStoreOther/page",
		{ params }
	)
}

/**
 * 获取查询详情信息
 */
export function getMatOutOtherById(id: number) {
	return ReqUtil.get<MatOutStorePickVO | undefined>(
		"/baseline/store/matOutStoreOther/getById",
		{ params: { id } }
	)
}

/**
 * 其他出库- 详情 - 物资明细
 * @param params
 * @returns
 */
export async function getMatOutOtherDetail(
	params: MatOutStorePickItemRequestParams
) {
	return ReqUtil.get<PageResVo<MatOutStorePickItemVO>>(
		"/baseline/store/matOutStoreOther/pageDetail",
		{ params }
	)
}

/**
 * 其他出库 - 详情 - 查询批次
 * @param params
 * @returns
 */
export async function getMatOutOtherSearchBatch(
	params: MatOutStorePickSearchBatchRequestParams
) {
	return ReqUtil.get<PageResVo<MatOutStorePickItemVO>>(
		"/baseline/store/matOutStoreOther/searchBatch",
		{ params }
	)
}

/**
 * 其他出库 - 详情 - 查询货位
 * @param params
 * @returns
 */
export async function getMatOutOtherSearchRoomInfoPage(
	params: MatOutStorePickRoompageDto
) {
	return ReqUtil.get<PageResVo<MatOutStorePickRoompageVo>>(
		"/baseline/store/matOutStoreOther/pageRoomInfo",
		{ params }
	)
}

/**
 * 其他出库 - 详情 - 查询货位
 * @param params
 * @returns
 */
export async function getMatPickRoomInfo(id: number) {
	return ReqUtil.get<PageResVo<MatOutStorePickRoomInfoVo>>(
		"/baseline/store/matOutStoreOther/getRoomInfo",
		{ params: { id } }
	)
}
