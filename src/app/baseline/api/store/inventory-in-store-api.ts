/**
 * @description 盘点管理-盘盈入库-api
 */

import ReqUtil from "../../utils/request-util"
import { PageResVo } from "../../utils/types/common"
import {
	CheckProfitInStorePageVo,
	CheckProfitInStorePageVoQuery,
	CheckTaskProfitInStoreDetailVo,
	CheckTaskProfitInStoreMaterialPageVo,
	CheckTaskProfitInStoreMaterialPageVoQuery
} from "../../utils/types/store-inventory-in-store"

/**
 * 盘盈入库分页搜索
 */

export const listCheckPlanTaskPaged = (
	params: CheckProfitInStorePageVoQuery
) => {
	return ReqUtil.get<PageResVo<CheckProfitInStorePageVo>>(
		"/baseline/store/checkPlanTask/pageProfitInStore",
		{ params }
	)
}

/**
 * 盘盈入库详情
 *
 * @param taskId 任务id
 */
export const getCheckPlanTask = (taskId: any) => {
	return ReqUtil.get<CheckTaskProfitInStoreDetailVo>(
		"/baseline/store/checkPlanTask/getProfitInStoreDetail",
		{
			params: { taskId }
		}
	)
}

/**
 * 盘盈入库 物料明细分页
 *
 * @param taskId 任务id
 */
export const listCheckPlanTaskGoodsPaged = (
	params: CheckTaskProfitInStoreMaterialPageVoQuery
) => {
	return ReqUtil.get<PageResVo<CheckTaskProfitInStoreMaterialPageVo>>(
		"/baseline/store/checkPlanTask/pageProfitInStoreMaterial",
		{
			params
		}
	)
}
