import { request } from "@/app/platform/utils/service"
import {
	AddBatchMatStoreUserRequest,
	IListStoreManagerPagedParams,
	MatStoreDTO,
	MatStoreRegionDTO,
	MatStoreRegionQueryParam,
	MatStoreRegionTreeParams,
	MatStoreRegionTreeVo,
	MatStoreRegionVo,
	MatStoreRequestParams,
	MatStoreRoomDTO,
	MatStoreRoomQueryParams,
	MatStoreRoomRequest,
	MatStoreRoomVo,
	MatStoreUserDTO,
	MatStoreUserQueryParams,
	MatStoreUserVo,
	MatStoreVo
} from "../../utils/types/store-manage"
import { PageResVo } from "../../utils/types/common"
import { map } from "lodash-es"
import ReqUtil from "../../utils/request-util"
import { SystemUserVo } from "../../utils/types/system"

/**
 * 查询盘点计划仓库分页列表
 */
export async function listInventoryPlanStoreSelectorOptions(
	params?: IListStoreManagerPagedParams
) {
	return ReqUtil.get<PageResVo<SystemUserVo>>(
		"/baseline/store/matStore/pageStoreCheck",
		{ params }
	)
}

/**
 * 查询库管员分页
 */
export async function listStoreManagerPaged(
	params?: IListStoreManagerPagedParams
) {
	return ReqUtil.get<PageResVo<SystemUserVo>>(
		"/baseline/system/user/pageStoreUser",
		{ params }
	)
}

/**
 * 仓库列表（无分页，格式化返回值）
 */
export async function fmtListMatStoreApi() {
	const r = await listMatStorage({})
	return map(r, (v) => ({ ...v, name: v.label, value: v.id, allName: v.label }))
}

/**
 * 查询仓库分页列表
 */
export function listMatStoragePaged(params: MatStoreRequestParams = {}) {
	return request<SystemApiResponseData>({
		url: "/baseline/store/matStore/page",
		method: "get",
		params
	})
}

/**
 * 查询仓库列表
 */
export function listMatStorage(params: any) {
	return request<MatStoreVo[] | undefined>({
		url: "/baseline/store/matStore/list",
		method: "get",
		params
	})
}

/**
 * 新增仓库
 */
export function saveMatStorage(data: MatStoreDTO, idempotentToken?: string) {
	return request<MatStoreVo | undefined>({
		url: "/baseline/store/matStore/add",
		method: "post",
		data,
		headers: { "idempotent-token": idempotentToken }
	})
}

/**
 * 修改仓库
 */
export function updateMatStorage(data: MatStoreDTO, _idempotentToken?: string) {
	return request<MatStoreVo | undefined>({
		url: "/baseline/store/matStore/edit",
		method: "put",
		data
	})
}

/**
 * 删除仓库
 */
export async function deleteMatStorage(id: any) {
	return ReqUtil.delete<boolean>(`/baseline/store/matStore/${id}`)
}

/**
 * 提交审批
 */
export function submitMatStorageApproval(id: number) {
	return request<boolean | undefined>({
		url: "/baseline/store/matStore/publish?id=" + id,
		method: "post"
	})
}

/**
 * 修改仓库状态
 */
export function updateMatStorageStatusBatch(ids: string, status: string) {
	return request<SystemApiResponseData>({
		url: "/baseline/store/matStore/updateStatusBatch",
		method: "post",
		params: {
			ids,
			status
		}
	})
}

/**
 * 获取仓库详情
 *
 * @param id 仓库id
 */
export function getMatStorage(id: number) {
	return request<MatStoreVo | undefined>({
		url: "/baseline/store/matStore/getById",
		method: "get",
		params: {
			id
		}
	})
}

/**
 * 新增仓库区域
 *
 * @param data
 */
export function saveMatStoreRegion(
	data: MatStoreRegionDTO,
	idempotentToken?: string
) {
	return request<ApiResponseData<MatStoreVo>>({
		url: "/baseline/store/matStoreRegion/add",
		method: "post",
		data,
		headers: { "idempotent-token": idempotentToken }
	})
}

/**
 * 删除仓库区域
 *
 * @param ids id集合
 */
export function deleteMatStoreRegion(ids: string) {
	return request<ApiResponseData<boolean>>({
		url: "/baseline/store/matStoreRegion/delete",
		method: "delete",
		params: {
			ids
		}
	})
}

/**
 * 更新仓库区域
 *
 */
export function updateMatStoreRegion(data: MatStoreRegionDTO) {
	return request<MatStoreRegionVo | undefined>({
		url: "/baseline/store/matStoreRegion/edit",
		method: "put",
		data
	})
}

/**
 * 查询仓库区域分页列表
 *
 * @param params
 */
export function listMatStoreRegionPaged(params: MatStoreRegionQueryParam) {
	return request<PageResVo<MatStoreRegionVo>>({
		url: "/baseline/store/matStoreRegion/page",
		method: "get",
		params
	})
}

/**
 * 查询仓库区域列表
 *
 * @param params
 */
export function listMatStoreRegion(params: MatStoreRegionQueryParam) {
	return request<MatStoreRegionVo[]>({
		url: "/baseline/store/matStoreRegion/list",
		method: "get",
		params
	})
}

/**
 * 新增货位
 *
 * @param params
 */
export function saveMatStoreRoom(
	data: MatStoreRoomDTO,
	idempotentToken?: string
) {
	return request<ApiResponseData<MatStoreRoomVo>>({
		url: "/baseline/store/matStoreRoom/add",
		method: "post",
		data,
		headers: { "idempotent-token": idempotentToken }
	})
}

/**
 * 删除货位
 *
 * @param ids
 */
export function deleteMatStoreRoom(ids: string) {
	return request<ApiResponseData<MatStoreRoomVo>>({
		url: `/baseline/store/matStoreRoom/${ids}`,
		method: "delete"
	})
}

/**
 * 更新货位
 *
 * @param ids
 */
export function updateMatStoreRoom(data: MatStoreRoomDTO) {
	return request<ApiResponseData<MatStoreRoomVo>>({
		url: `/baseline/store/matStoreRoom/edit`,
		method: "put",
		data
	})
}

/**
 * 更新货位
 *
 * @param ids
 */
export function listMatStoreRoomPaged(params: MatStoreRoomQueryParams) {
	return request<PageResVo<MatStoreRoomVo>>({
		url: `/baseline/store/matStoreRoom/page`,
		method: "get",
		params
	})
}

/**
 * 查询仓库人员分页列表
 */
export function listMatStoreUserPaged(params: MatStoreUserQueryParams) {
	return request<PageResVo<MatStoreUserVo>>({
		url: `/baseline/store/matStoreUser/page`,
		method: "get",
		params
	})
}

/**
 * 新增仓库人员 （废弃）
 */
export function saveMatStoreUser(data: MatStoreUserDTO) {
	return request<PageResVo<MatStoreUserVo>>({
		url: `/baseline/store/matStoreUser`,
		method: "post",
		data
	})
}

/**
 * 批量新增仓库人员
 * @param data
 * @returns
 */
export function addBatchMatStoreUser(
	data: AddBatchMatStoreUserRequest,
	idempotentToken?: string
) {
	return request<PageResVo<MatStoreUserVo>>({
		url: `/baseline/store/matStoreUser/addBatch`,
		method: "post",
		data,
		headers: { "idempotent-token": idempotentToken }
	})
}

/**
 * 修改仓库人员
 */
export function updateMatStoreUser(data: MatStoreUserDTO) {
	return request<PageResVo<MatStoreUserVo>>({
		url: `/baseline/store/matStoreUser/edit`,
		method: "put",
		data
	})
}

/**
 * 删除仓库人员
 */
export function deleteMatStoreUser(ids: string) {
	return request<ApiResponseData<boolean>>({
		url: `/baseline/store/matStoreUser/${ids}`,
		method: "delete"
	})
}

/**
 * 查询区域树列表
 */
export function listMatStoreRegionTree(params?: MatStoreRegionTreeParams) {
	return request<MatStoreRegionTreeVo[] | undefined>({
		url: `/baseline/store/matStoreRegion/tree`,
		method: "get",
		params
	})
}

/**
 * 查询货位不分页列表
 */
export function listMatStoreRoomList(params?: MatStoreRoomRequest) {
	return request<MatStoreRoomVo[] | undefined>({
		url: `/baseline/store/matStoreRoom/list`,
		method: "get",
		params
	})
}
