/**
 * @description 盘点计划 api
 */

import ReqUtil from "../../utils/request-util"
import { PageResVo } from "../../utils/types/common"
import {
	MatStoreCheckMaterialAddDTO,
	MatStoreCheckPlanAllMaterialQueryParams,
	MatStoreCheckPlanDetaiAddlVoResponse,
	MatStoreCheckPlanDTO,
	MatStoreCheckPlanEditMaterialQueryParams,
	MatStoreCheckPlanMaterialQueryParams,
	MatStoreCheckPlanMaterialVo,
	MatStoreCheckPlanTaskAddStoreListParams,
	MatStoreCheckPlanTaskPageReqParams,
	MatStoreCheckPlanTaskPageVo,
	MatStoreCheckPlanTaskUserUpdateDTO,
	MatStoreCheckPlanVo,
	MatStoreCheckPlanVoQueryParams
} from "../../utils/types/inventory-manage"

/**
 * 获取状态统计
 */
export function getStoreCheckPlanBmpStatusCnt(
	params: MatStoreCheckPlanVoQueryParams
) {
	return request<number[]>({
		url: "/baseline/store/checkPlan/statusStatistics",
		method: "get",
		params
	})
}

/**
 * 新增盘点计划
 */
export const addInventoryPlan = (
	data: MatStoreCheckPlanDTO,
	idempotentToken?: string
) => {
	return ReqUtil.post<MatStoreCheckPlanVo>("/baseline/store/checkPlan/add", {
		data,
		headers: { "idempotent-token": idempotentToken }
	})
}

/**
 * 盘点计划提交审核
 *
 * @param planId 计划id
 */
export const submitInventoryPlan = (planId: any, idempotentToken?: string) => {
	return ReqUtil.post<any>("/baseline/store/checkPlan/publish", {
		params: { planId },
		headers: { "idempotent-token": idempotentToken }
	})
}

/**
 * 盘点计划差异处理 提交审核
 *
 * @param planId 计划id
 */
export const submitUploadInventoryPlan = (
	planId: any,
	idempotentToken?: string
) => {
	return ReqUtil.post("/baseline/store/checkPlan/publishUpload", {
		params: { planId },
		headers: { "idempotent-token": idempotentToken }
	})
}

/**
 * 盘点计划差异处理 提交审核
 *
 * @param planId 计划id
 */
export const diffHandlingInventoryPlan = (planId: any) => {
	return ReqUtil.post("/baseline/store/checkPlan/diffHandling", {
		data: { planId }
	})
}

/**
 * 更新盘点计划
 */
export const updateInventoryPlan = (
	data: MatStoreCheckPlanDTO,
	_idempotentToken?: string
) => {
	return ReqUtil.post<MatStoreCheckPlanVo>("/baseline/store/checkPlan/edit", {
		data
	})
}

/**
 * 查看盘点计划
 */
export const getInventoryPlan = (id: any) => {
	return ReqUtil.get<MatStoreCheckPlanVo>(
		"/baseline/store/checkPlan/getCheckPlanById",
		{
			params: { id }
		}
	)
}

/**
 * 盘点计划分页
 */
export const listInventoryPlanPaged = (
	params?: MatStoreCheckPlanVoQueryParams
) => {
	return ReqUtil.get<PageResVo<MatStoreCheckPlanVo>>(
		"/baseline/store/checkPlan/page",
		{
			params
		}
	)
}

/**
 * 获取仓库下所有物资分页 (过滤已选择的物资)
 */
export const listInventoryPlanWarehouseAllMaterialPaged = (
	params?: MatStoreCheckPlanAllMaterialQueryParams
) => {
	return ReqUtil.get<PageResVo<MatStoreCheckPlanMaterialVo>>(
		"/baseline/store/checkPlan/selectMaterialsByTaskId",
		{
			params
		}
	)
}

/**
 * 盘点任务编辑物料分页
 */
export const listInventoryPlanEditWarehouseGoodsPaged = (
	params?: MatStoreCheckPlanEditMaterialQueryParams
) => {
	return ReqUtil.get("/baseline/store/checkPlan/pageTaskEditMaterials", {
		params
	})
}

/**
 * 新增盘点仓库
 */
export const addInventoryPlanWarehouses = (
	data: {
		/**
		 * 盘点计划id
		 */
		checkPlanId?: number | null
		/**
		 * 仓库id列表
		 */
		storeList: MatStoreCheckPlanTaskAddStoreListParams[]
	},
	idempotentToken?: string
) => {
	return ReqUtil.post("/baseline/store/checkPlan/addPlanTasks", {
		data,
		headers: { "idempotent-token": idempotentToken }
	})
}

/**
 * 删除盘点仓库
 *
 * @param data 盘点仓库id集合
 */
export const deleteInventoryPlanWarehousesBatch = (idList: number[]) => {
	return ReqUtil.post("/baseline/store/checkPlan/batchDeleteCheckPlanTask", {
		data: { idList }
	})
}

/**
 * 删除盘点计划
 *
 * @param data 盘点计划id集合
 */
export const deleteInventoryPlanBatch = (idList: number[]) => {
	return ReqUtil.post("/baseline/store/checkPlan/batchDeleteCheckPlan", {
		data: { idList }
	})
}

/**
 * 新增盘点仓库物资
 */
export const addInventoryPlanWarehouseGoods = (
	data: MatStoreCheckMaterialAddDTO,
	idempotentToken?: string
) => {
	return ReqUtil.post("/baseline/store/checkPlan/addCheckMaterial", {
		data,
		headers: { "idempotent-token": idempotentToken }
	})
}

/**
 * 更新盘点仓库的盘点人员
 */
export const updateInventoryPlanWarehouseUsers = (
	data: MatStoreCheckPlanTaskUserUpdateDTO
) => {
	return ReqUtil.post("/baseline/store/checkPlan/updateCheckUser", { data })
}

/**
 * 查看盘点仓库中已有的物资分页 - 货位维度
 */
export const listInventoryPlanWarehouseGoodsPaged = (
	params?: MatStoreCheckPlanMaterialQueryParams
) => {
	return ReqUtil.get("/baseline/store/checkPlan/pageTaskMaterials", { params })
}

/**
 * 批量删除盘点仓库物资
 *
 * @param data 盘点仓库中物资的id集合
 */
export const deleteInventoryPlanWarehouseGoodsBatch = (
	data: MatStoreCheckMaterialAddDTO
) => {
	return ReqUtil.post("/baseline/store/checkPlan/batchDeleteCheckMaterial", {
		data
	})
}

/**
 * 盘点计划 仓库明细分页
 */
export const listInventoryPlanWarehousePaged = (
	params: MatStoreCheckPlanTaskPageReqParams
) => {
	return ReqUtil.get<PageResVo<MatStoreCheckPlanTaskPageVo>>(
		"/baseline/store/checkPlan/pagePlanTask",
		{
			params
		}
	)
}

/**
 * 查询循环盘点的物资种类
 */
export const getCirculateMaterialNum = (data: {
	/**
	 * 盘点计划id
	 */
	checkPlanId?: number | null
	/**
	 * 仓库id列表
	 */
	storeList: MatStoreCheckPlanTaskAddStoreListParams[]
}) => {
	return ReqUtil.post<MatStoreCheckPlanDetaiAddlVoResponse>(
		"/baseline/store/checkPlan/getCirculateMaterialNum",
		{
			data
		}
	)
}
