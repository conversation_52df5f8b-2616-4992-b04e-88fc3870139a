/**
 * @description 盘点管理-盘亏出库-api
 */

import ReqUtil from "../../utils/request-util"
import { PageResVo } from "../../utils/types/common"
import {
	CheckLossesOutStorePageVo,
	CheckLossesOutStorePageVoQuery,
	CheckTaskLossesOutStoreMaterialPageVo,
	CheckTaskLossesOutStoreMaterialPageVoQuery,
	CheckTaskProfitOutStoreDetailVo
} from "../../utils/types/store-inventory-out-store"

/**
 * 盘亏出库分页搜索
 */

export const listCheckPlanTaskOutStorePaged = (
	params: CheckLossesOutStorePageVoQuery
) => {
	return ReqUtil.get<PageResVo<CheckLossesOutStorePageVo>>(
		"/baseline/store/checkPlanTask/pageLossesOutStore",
		{ params }
	)
}

/**
 * 盘亏出库详情
 *
 * @param taskId 任务id
 */
export const getCheckPlanTaskOutStore = (taskId: any) => {
	return ReqUtil.get<CheckTaskProfitOutStoreDetailVo>(
		"/baseline/store/checkPlanTask/getLossesOutStoreDetail",
		{
			params: { taskId }
		}
	)
}

/**
 * 盘盈入库 物料明细分页
 *
 * @param taskId 任务id
 */
export const listCheckPlanTaskGoodsOutStorePaged = (
	params: CheckTaskLossesOutStoreMaterialPageVoQuery
) => {
	return ReqUtil.get<PageResVo<CheckTaskLossesOutStoreMaterialPageVo>>(
		"/baseline/store/checkPlanTask/pageLossesOutStoreMaterial",
		{
			params
		}
	)
}
