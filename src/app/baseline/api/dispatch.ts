/*
 * @Author: liulianming
 * @Date: 2025-07-12 00:06:51
 * @LastEditors: liulianming
 * @LastEditTime: 2025-07-30 09:27:36
 * @Description:
 */

import { request } from "@/app/platform/utils/service"

export interface FaultItem {
	id: string
	name: string
	code: string
	location: string
	reportTime: string
	status: string
}

interface ApiResponse<T> {
	msg: string
	code: string
	data: T
	alertType: string
}

/**
 * 获取故障报告状态统计
 */
export function getFaultReportStatusStatistics(params: {
	key?: string
	lineId?: string
	reportTime?: string[]
}) {
	return request<any>({
		url: "/baseline/system/faultReport/statusStatistics",
		method: "get",
		params
	})
}

/**
 * 获取故障列表
 */
export function getFaultList(params: {
	statusType?: string | string[]
	key?: string
	lineId?: string
	reportTime?: string[]
}) {
	return request<any>({
		url: "/baseline/system/faultReport/list",
		method: "get",
		params
	})
}
/**
 * 故障上报详情
 */
export function getFaultReportDetail(id: string) {
	return request<any>({
		url: `/baseline/system/faultReport/${id}`,
		method: "get"
	})
}
/**
 * 故障上报详情
 */
export function startStandardProcess(data: {
	faultReportId: string
	setMainId: string
	mainTitle: string
	disposalLevel: string
}) {
	return request<any>({
		url: `/baseline/system/faultReport/${data.faultReportId}/startStandardProcess`,
		method: "post",
		params: data
	})
}

/**
 * 获取标准处置流程列表
 */
export function getStandardProcessList(params: {
	locComposeNo?: string
	disposalLevel?: string
	lineId?: string
	status?: string
	mainTitle?: string
	key?: string
}) {
	return request<any>({
		url: "/baseline/system/rpstssProcessTreeFlowSetMain/list",
		method: "get",
		params
	})
}
/**
 * 获取位置
 */
export function getLocComposeTreeApi(params: anyKey) {
	return request<SystemApiResponseData>({
		url: `/positions/tree`,
		method: "get",
		params
	})
}

/**
 * 获取标准流程步骤信息
 */
export function getProcessStepsApi(processId: string | number) {
	return request<any>({
		url: `/baseline/system/standardProcess/${processId}/steps`,
		method: "get"
	})
}

/**
 * 更新流程步骤状态
 */
export function updateProcessStepStatus(data: {
	processId: string | number
	stepId: string
	status: string
	operator?: string
	remark?: string
}) {
	return request<any>({
		url: `/baseline/system/standardProcess/step/updateStatus`,
		method: "post",
		data
	})
}

/**
 * 根据故障ID获取处置流程详情
 */
export function getProcessDetailByFault(faultId: string) {
	return request<any>({
		url: `/baseline/system/rpstssProcessTreeFlowExecuteMain/getDetailByFault/${faultId}`,
		method: "get"
	})
}

/**
 * 流程执行人员-分页查询
 */
export async function listProcessTreeFlowExecuteUserPage(params: any) {
	return request<any>({
		url: "/baseline/system/processTreeFlowExecuteUser/page",
		method: "get",
		params
	})
}
