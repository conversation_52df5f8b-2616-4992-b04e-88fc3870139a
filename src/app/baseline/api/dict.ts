import {
	getDictionaryTerm,
	listTreeDictionaryByCode
} from "@/app/platform/api/system/dictionary"
import XEUtils from "xe-utils"
import { request } from "@/app/platform/utils/service"
import {
	IMateOperateStatus,
	IMaterialBusinessType,
	ISimilarityType
} from "../utils/types/material"
import { ICostCategoryStatus } from "../utils/types/system-cost-category"
import { IDepotStatus } from "../utils/types/system-depot"

//相关附件业务类型
export enum fileBusinessType {
	"matCodePhoto" = 1, //物资编码的照片
	"matCode" = 2, //物资编码相关附件

	"needList" = 3, //需求清单
	"needPlan" = 4, //需求计划
	"mergePlan" = 5, //合并计划相关附件
	"purchasePlan" = 6, //采购计划相关附件

	"purchaseDistribution" = 20, //采购分包
	"purchaseProject" = 21, //采购项目
	"orderPlan" = 22, //订货计划
	"purchaseOrder" = 23, //采购订单
	"purchaseContract" = 24, //采购合同
	"supplierInfo" = 25, //供应商信息
	"invoiceInfo" = 26, //发票信息

	warehousingApply = 30, // 入库申请
	otherWarehousingApply = 31, // 其他入库申请
	matGetApply = 32, // 领料申请

	qualityInspect = 33, // 质量检验
	outStorePick = 34, // 领料出库

	warehouseReturnApply = 35, // 退库申请
	goodsReturnApply = 36, // 退货申请

	returnOutbound = 37, // 退货出库

	transferApply = 38, // 调拨申请
	transferReceipt = 39, // 调拨入库
	transferOutbound = 40, // 调拨出库

	inventoryJob = 41, // 盘点任务
	inventoryPlan = 42, // 盘点计划
	wasteHandoverApply = 43, //交旧申请
	wasteScrapApply = 44, //报废申请
	wasteScrapDisposalApply = 46, // 报废处置申请
	assessmentReport = 47, //待询价
	disposalReport = 48, //待处置

	repairOutbound = 49, // 返修出库
	repairApply = 50, // 返修申请
	requisitionApply = 51, // 领用申请
	returnApply = 52, // 归还申请
	inventoryPlanDiff = 53, // 盘点计划差异处理

	matMaterialFreeze = 54, // 编码手册 冻结
	matMaterialThawing = 55, // 编码手册 解冻
	matMaterialCancel = 56, // 编码手册 作废

	requisitionApplyAllocation = 57, // 领用申请 - 分配
	receivedStored = 58 // 接收入库
}

/**
 * 相似物资查询类型
 * similarityType:  0-一期接口  1-对接丰数接口
 */
export const tableSimilarTypeDefault = ref(ISimilarityType.otherSimilar)

/**
 * 发票管理 蓝色发票/红色发票
 */
export enum IInvoiceColor {
	blue = "0",
	red = "1"
}
/**
 * 返修状态 字典配置
 */
export enum RepairStatus {
	"pendingOutStore" = "1", // 待出库
	"repairing" = "2", // 维修中
	"finishRepair" = "4", // 已完修
	"close" = "3" // 已关闭
}

/**
 * 返修状态 样式
 * @returns
 */
function getRepairStatus() {
	return [
		{
			label: "待出库",
			value: RepairStatus.pendingOutStore,
			raw: { class: "success" }
		},
		{
			label: "维修中",
			value: RepairStatus.repairing,
			raw: { class: "warning" }
		},
		{
			label: "已完修",
			value: RepairStatus.finishRepair,
			raw: { class: "danger-less" }
		},
		{
			label: "已关闭",
			value: RepairStatus.close,
			raw: { class: "danger" }
		}
	]
}

/**
 * 领料用途 字典配置
 */
export enum MaterialPurpose {
	"quotaMaterial" = "1",
	"dailyMaterial" = "2",
	"lowvalueMaterial" = "3"
}
//通用类型定义
export enum commonBoolan {
	"false" = "0", //否
	"true" = "1" //是
}

/*物资分类编码状态 0草稿、1正常、2冻结、3作废*/
export enum matStatus {
	"normal" = "1", //正常
	"drafted" = "0", //草稿
	"canceled" = "3", //作废
	"freeze" = "2" //冻结
	//"inventoryInProgress" = "4" // 盘点中
}

/**
 * 需求清单 状态
 * 启用 1；草稿0 2:停用
 */
export enum needListStatus {
	"drafted" = "0", //草稿
	"started" = "1", //启用
	"stopped" = "2" //停用
}

/**
 * 年度计划 状态
 * 启用 2；草稿1 3:停用
 */
export enum yearPlanListStatus {
	"drafted" = "1", //草稿
	"started" = "2", //启用
	"stopped" = "3" //停用
}

/**
 * 审批状态
 */
export enum appStatus {
	/**
	 * 待提交
	 */
	"pendingApproval" = "0",

	/**
	 * 审批中
	 */
	"underApproval" = "1",

	/**
	 * 已审批
	 */
	"approved" = "2",

	/**
	 * 已驳回
	 */
	"rejected" = "3"
}

const DictCache = new Map()

/* export enum needStatus {
	"draft", //草稿
	"started", //启用
	"stopped" //停用
} */

export enum dealStatus {
	"generated" = "1", // 已生成
	"notGenerated" = "0" //未生成
}

//订单来源
export enum purchaseOrderSource {
	/**
	 * 年度需求计划
	 */
	"annualDemandPlan" = "1",

	/**
	 * 临时计划
	 */
	"emergencyPlan" = "2"
}

const getDictByCode = (code: string) => {
	return new Promise((resolve) => {
		if (DictCache.has(code)) resolve(DictCache.get(code))
		else {
			getDictionaryTerm({ dataDictionaryCode: code, containDisable: false })
				.then((_r) => {
					XEUtils.eachTree(_r, (item: any) => {
						item.label = item.subitemName
						item.value = item.subitemValue
					})
					DictCache.set(code, _r)
					resolve(_r)
				})
				.catch(() => {
					resolve([])
				})
		}
	})
}
const getDictByCodeList = (dictOptions: any): Promise<anyKey> => {
	return new Promise((resolve) => {
		const keys = Object.keys(dictOptions.value)
		const codes = keys.join(",")
		getDictionaryTermList({
			dataDictionaryCodeList: codes,
			containDisable: false
		})
			.then((_r) => {
				XEUtils.eachTree(_r, (item: any) => {
					item.label = item.subitemName
					item.value = item.subitemValue
				})
				const groupedData = _r.reduce(
					(acc: Record<string, any[]>, obj: any) => {
						const key = obj.dataDictionaryCode
						if (!acc[key]) {
							acc[key] = []
						}
						acc[key].push(obj)
						return acc
					},
					{}
				)
				// 对每个聚合数组按照 sortedBy 进行升序排序
				for (const key in groupedData) {
					groupedData[key].sort((a: any, b: any) => a.sortedBy - b.sortedBy)
				}
				resolve(groupedData)
			})
			.catch(() => {
				resolve([])
			})
	})
}

function getDictionaryTermList(params: any) {
	return request<any>({
		//url: `/pitaya/system/dataDictionary/listByDictionaryCodeList`,
		url: `/pitaya/system/dataDictionary/listByDictionaryCodeEnhanceList`,
		method: "get",
		params
	})
}

function getWasteMaterialType() {
	return [
		{
			label: "A类",
			value: "0",
			raw: { class: "success" }
		},
		{
			label: "B类",
			value: "1",
			raw: { class: "danger" }
		},
		{
			label: "C类",
			value: "2",
			raw: { class: "warning" }
		}
	]
}

/**
 * 物资分类状态
 * @returns
 */
function getMatStatus() {
	return [
		{
			label: "草稿",
			value: matStatus.drafted,
			raw: { class: "info" }
		},
		{
			label: "正常",
			value: matStatus.normal,
			raw: { class: "success" }
		},
		{
			label: "冻结",
			value: matStatus.freeze,
			raw: { class: "warning" }
		},
		{
			label: "作废",
			value: matStatus.canceled,
			raw: { class: "danger" }
		}
		/* {
			label: "盘点中",
			value: matStatus.inventoryInProgress,
			raw: { class: "primary" }
		} */
	]
}

/**
 * 操作状态: 0正常 1正在冻结中 2正在作废中 3正在解冻中 4正在更新中  手册显示时，正常的显示空
 * 编码手册 - 物资状态变更审批状态
 * @returns
 */
function getMatOperateTypeStatus() {
	return [
		{
			label: "正在冻结中",
			value: IMateOperateStatus.freeze,
			raw: { class: "purple" }
		},
		{
			label: "解冻审批中",
			value: IMateOperateStatus.thawing,
			raw: { class: "primary" }
		},
		{
			label: "更新审批中",
			value: IMateOperateStatus.update,
			raw: { class: "warning" }
		},
		{
			label: "作废审批中",
			value: IMateOperateStatus.cancel,
			raw: { class: "danger" }
		}
	]
}

function getBpmStatus() {
	return [
		{
			label: "待提交",
			value: appStatus.pendingApproval,
			raw: { class: "primary" }
		},
		{
			label: "审批中",
			value: appStatus.underApproval,
			raw: { class: "warning" }
		},
		{
			label: "已审批",
			value: appStatus.approved,
			raw: { class: "success" }
		},
		{
			label: "已驳回",
			value: appStatus.rejected,
			raw: { class: "danger" }
		}
		/* {
			label: "审批中",
			value: appStatus.needApproval,
			raw: { class: "warning" }
		} */
	]
}

/**
 * 库存 - 盘点 差异处理状态
 * @returns
 */
function getBpmDiffStatus() {
	return [
		{
			label: "待提交",
			value: appStatus.pendingApproval,
			raw: { class: "primary" }
		},
		{
			label: "审批中",
			value: appStatus.underApproval,
			raw: { class: "warning" }
		},
		{
			label: "已完成",
			value: appStatus.approved,
			raw: { class: "success" }
		},
		{
			label: "已驳回",
			value: appStatus.rejected,
			raw: { class: "danger" }
		}
		/* {
			label: "审批中",
			value: appStatus.needApproval,
			raw: { class: "warning" }
		} */
	]
}

/**
 * 查询条件有审批状态列表时使用
 */
function getQueryBpmStatus() {
	return [
		{
			label: "待提交",
			value: appStatus.pendingApproval,
			raw: { class: "primary" }
		},
		{
			label: "审批中",
			value: appStatus.underApproval, //+ "," + appStatus.needApproval,
			raw: { class: "warning" }
		},
		{
			label: "已审批",
			value: appStatus.approved,
			raw: { class: "success" }
		},
		{
			label: "已驳回",
			value: appStatus.rejected,
			raw: { class: "danger" }
		}
	]
}

/**
 * 订单状态
 */
function getDealStatus() {
	return [
		{
			label: "已生成",
			value: dealStatus.generated,
			raw: { class: "primary" }
		},
		{
			label: "未生成",
			value: dealStatus.notGenerated,
			raw: { class: "warning" }
		}
	]
}
/**
 * 采购订单-通知供应商状态
 */
function getNotifySuppliersStatus() {
	return [
		{
			label: "已通知",
			value: "1",
			raw: { class: "primary" }
		},
		{
			label: "待通知",
			value: "0",
			raw: { class: "warning" }
		}
	]
}
/**
 * 采购订单-到货状态
 */
function getArrivalStatus() {
	return [
		{
			label: "未到货",
			value: "0",
			raw: { class: "primary" }
		},
		{
			label: "部分到货",
			value: "1",
			raw: { class: "warning" }
		},
		{
			label: "已到货",
			value: "2",
			raw: { class: "success" }
		},
		{
			label: "已关闭",
			value: "3",
			raw: { class: "danger" }
		}
	]
}

/**
 * 物资性质
 */
export enum IMaterialNature {
	/**
	 * A类-替换件
	 */
	"A" = "1",

	/**
	 * A类-维修件
	 */
	"ARepair" = "2",

	/**
	 * B类-专用
	 */
	"B" = "3",

	/**
	 * C类-通用
	 */
	"C" = "4"
}

/**
 * 物资性质颜色
 */
function getMatAttr() {
	return [
		{
			label: "A类-替换件",
			value: IMaterialNature.A,
			raw: { cssClass: "warning_bg" }
		},
		{
			label: "A类-维修件",
			value: IMaterialNature.ARepair,
			raw: { cssClass: "success_bg" }
		},
		{
			label: "B类-专用",
			value: IMaterialNature.B,
			raw: { cssClass: "primary_bg" }
		},
		{
			label: "C类-通用",
			value: IMaterialNature.C,
			raw: { cssClass: "successLess_bg" }
		}
	]
}

function getYesNo() {
	return [
		{
			label: "是",
			value: "1"
		},
		{
			label: "否",
			value: "0"
		}
	]
}
function getTrueFalse() {
	return [
		{
			label: "是",
			value: true
		},
		{
			label: "否",
			value: false
		}
	]
}

/**
 * 需求清单  按钮颜色
 * @returns
 */
function getStpStatus() {
	return [
		{
			label: "草稿",
			value: needListStatus.drafted,
			raw: { class: "warning" }
		},
		{
			label: "启用",
			value: needListStatus.started,
			raw: { class: "success" }
		},
		{
			label: "停用",
			value: needListStatus.stopped,
			raw: { class: "danger" }
		}
	]
}

function getFeeType() {
	return [
		{
			label: "维修费",
			value: "0",
			raw: { class: "success" }
		},
		{
			label: "运营费用",
			value: "1",
			raw: { class: "warning" }
		},
		{
			label: "劳保费",
			value: "2",
			raw: { class: "info" }
		}
	]
}

function getSendPlatStatus() {
	return [
		{
			label: "已推送",
			value: "1",
			raw: { class: "success" }
		},
		{
			label: "未推送",
			value: "0",
			raw: { class: "primary" }
		}
	]
}

function getInvoiceStatus(color?: string) {
	if (color == "1") {
		return [
			{
				label: "草稿",
				value: appStatus.pendingApproval,
				raw: { class: "info" }
			},
			{
				label: "红冲审批",
				value: appStatus.underApproval,
				raw: { class: "danger-less" }
			},
			{
				label: "已红冲",
				value: appStatus.approved,
				raw: { class: "danger" }
			},
			{
				label: "草稿",
				value: appStatus.rejected,
				raw: { class: "info" }
			}
			/* {
				label: "红冲审批",
				value: appStatus.needApproval,
				raw: { class: "danger-less" }
			} */
		]
	} else {
		return [
			{
				label: "草稿",
				value: appStatus.pendingApproval,
				raw: { class: "info" }
			},
			{
				label: "待确认",
				value: appStatus.underApproval,
				raw: { class: "success-less" }
			},
			{
				label: "已开票",
				value: appStatus.approved,
				raw: { class: "success" }
			},
			{
				label: "草稿",
				value: appStatus.rejected,
				raw: { class: "info" }
			}
			/* {
				label: "待确认",
				value: appStatus.needApproval,
				raw: { class: "warning" }
			} */
		]
	}
}
function getPurchaseOrderSource() {
	return [
		{
			label: "月度订货计划", // 年度需求计划
			value: purchaseOrderSource.annualDemandPlan,
			raw: { class: "success-less" }
		},
		{
			label: "临时计划",
			value: purchaseOrderSource.emergencyPlan,
			raw: { class: "warning" }
		}
	]
}

/**
 * 费用类别状态
 * @returns
 */
export function getCostCategoryStatus() {
	return [
		{
			label: "待启用",
			value: ICostCategoryStatus.Drafted,
			raw: { class: "info" }
		},
		{
			label: "已启用",
			value: ICostCategoryStatus.Started,
			raw: { class: "success" }
		},
		{
			label: "冻结",
			value: ICostCategoryStatus.Freeze,
			raw: { class: "warning" }
		}
	]
}

/**
 * 段区状态
 * @returns
 */
export function getDepotStatus() {
	return [
		{
			label: "待启用",
			value: IDepotStatus.Drafted,
			raw: { class: "info" }
		},
		{
			label: "已启用",
			value: IDepotStatus.Started,
			raw: { class: "success" }
		},
		{
			label: "已停用",
			value: IDepotStatus.Disabled,
			raw: { class: "danger" }
		}
	]
}
/**
 * 获取年份
 */
function getFutureYears(pass = 0, future = 4) {
	const currentYear = new Date().getFullYear()
	const futureYears = []
	for (let i = pass; i <= future; i++) {
		const year = currentYear + i
		futureYears.push({
			label: year.toString(),
			value: year.toString()
		})
	}
	return futureYears
}

/**
 * 获取年份
 */
function getMonthList(pass = 0, future = 4) {
	const _rst = []
	for (let _i = 1; _i <= 12; _i++) {
		_rst.push({
			label: `${_i.toString()} 月`,
			value: _i.toString()
		})
	}
	return _rst
}

/**
 * 获取物资性质
 */
function getMaterialAttribute(): Promise<any> {
	return new Promise((resolve) => {
		listTreeDictionaryByCode({ treeDictionaryCode: "MATERIAL_NATURE" })
			.then((_r) => {
				XEUtils.eachTree(_r, (item: any) => {
					item.allName = item.subitemName
					item.name = item.subitemName
					item.label = item.subitemName
					item.value = item.subitemValue
				})
				resolve(_r)
			})
			.catch(() => {
				resolve([])
			})
	})
}

/**
 * 获取报表
 */
function getComponentReport(): Promise<any> {
	return new Promise((resolve) => {
		listTreeDictionaryByCode({ treeDictionaryCode: "COMPANY_REPORT" })
			.then((_r) => {
				XEUtils.eachTree(_r, (item: any) => {
					item.allName = item.subitemName
					item.name = item.subitemName
					item.label = item.subitemName
					item.value = item.subitemValue
				})
				resolve(_r)
			})
			.catch(() => {
				resolve([])
			})
	})
}

export const DictApi = {
	getRepairStatus,
	getTrueFalse,
	getYesNo,
	getDictByCode,
	getMatStatus,
	getBpmStatus,
	getBpmDiffStatus,
	getStpStatus,
	getFeeType,
	getMaterialAttribute,
	getDictByCodeList,
	getMatAttr,
	getFutureYears,
	getMonthList,
	getSendPlatStatus,
	getQueryBpmStatus,
	getPurchaseOrderSource,
	getDealStatus,
	getNotifySuppliersStatus,
	getInvoiceStatus,
	getArrivalStatus,
	getWasteMaterialType,
	getComponentReport,
	getMatOperateTypeStatus
}
