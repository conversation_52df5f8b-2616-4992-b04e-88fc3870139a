export interface AddNeedRequest {
	/**
	 * 费用类别
	 */
	expenseCategory?: null | string
	/**
	 * 需求清单名称
	 */
	label?: null | string
	/**
	 * 专业ID
	 */
	majorId?: number | null
	/**
	 * 需求计划类型;1年度计划
	 */
	planType?: null | string
	/**
	 * 备注
	 */
	remark?: null | string
	[property: string]: any
}
export interface MatApplyListRequest {
	/**
	 * 物资性质
	 */
	attribute?: string
	/**
	 * 审批状态;0待提交，1审批中，2已审批，3已驳回
	 */
	bpmStatus?: string
	/**
	 * 采购单位
	 */
	buyUnit?: string
	/**
	 * 物资编码
	 */
	code?: string
	/**
	 * 库存转换比例
	 */
	conversionUnit?: string
	currentPage?: number
	/**
	 * 是否包含危险废物
	 */
	dangerousWasteFlag?: string
	/**
	 * 是否可维修
	 */
	fixFlag?: string
	/**
	 * id列表
	 */
	ids?: string
	/**
	 * 编码名称
	 */
	label?: string
	/**
	 * 物资分类ID
	 */
	materialTypeId?: number
	pageSize?: number
	/**
	 * 主要材质
	 */
	quality?: string
	/**
	 * 是否可回收
	 */
	recoveryFlag?: string
	/**
	 * 预估回收重量
	 */
	recoveryWeight?: string
	sidx?: string
	sord?: string
	/**
	 * 物资编码状态
	 */
	status?: string
	/**
	 * 所属公司ID
	 */
	sysCommunityId?: number
	/**
	 * 所属部门ID
	 */
	sysOrgId?: number
	/**
	 * 技术参数
	 */
	technicalParameter?: string
	/**
	 * 库存单位
	 */
	useUnit?: string
	/**
	 * 规格型号
	 */
	version?: string
	/**
	 * 废旧物资类型
	 */
	wasteMaterialType?: string
}

export interface MatApplyListUserRequest {
	/**
	 * 物资性质
	 */
	attribute?: string
	/**
	 * 审批状态;0待提交，1审批中，2已审批，3已驳回
	 */
	bpmStatus?: string
	/**
	 * 采购单位
	 */
	buyUnit?: string
	/**
	 * 物资编码
	 */
	code?: string
	/**
	 * 库存转换比例
	 */
	conversionUnit?: string
	currentPage?: number
	/**
	 * 是否包含危险废物
	 */
	dangerousWasteFlag?: string
	/**
	 * 是否可维修
	 */
	fixFlag?: string
	/**
	 * id列表
	 */
	ids?: string
	/**
	 * 编码名称
	 */
	label?: string
	/**
	 * 物资分类ID
	 */
	materialTypeId?: number
	pageSize?: number
	/**
	 * 主要材质
	 */
	quality?: string
	/**
	 * 是否可回收
	 */
	recoveryFlag?: string
	/**
	 * 预估回收重量
	 */
	recoveryWeight?: string
	sidx?: string
	sord?: string
	/**
	 * 物资编码状态
	 */
	status?: string
	/**
	 * 技术参数
	 */
	technicalParameter?: string
	/**
	 * 库存单位
	 */
	useUnit?: string
	/**
	 * 规格型号
	 */
	version?: string
	/**
	 * 废旧物资类型
	 */
	wasteMaterialType?: string
}

export interface PublishRequest {
	id: number
}

export interface UpdateMatStatusRequest {
	ids: string
	status: string
}

export interface UpdateMatAttributeRequest {
	attribute: string
	ids: string
}

export interface addMatProcureRequest {
	/**
	 * 预估采购单价
	 */
	evaluation?: number | null
	/**
	 * 预估采购周期
	 */
	evaluationCycle?: number | null
	/**
	 * 物资ID
	 */
	materialId?: number | null
	/**
	 * 最小订货数量
	 */
	minBuyNum?: number | null
	/**
	 * 安全库存
	 */
	safeStock?: number | null
}

export interface updMatProcureRequest {
	/**
	 * 预估采购单价
	 */
	evaluation?: number | null
	/**
	 * 预估采购周期
	 */
	evaluationCycle?: number | null
	/**
	 * 物资ID
	 */
	materialId?: number | null
	/**
	 * 最小订货数量
	 */
	minBuyNum?: number | null
	/**
	 * 安全库存
	 */
	safeStock?: number | null
}

export interface getTaskByBusinessIdsRequest {
	/**
	 * 业务ids
	 */
	businessIds: string[]
	/**
	 * 业务流程编码
	 */
	camundaKey: string
	/**
	 * 当前节点key
	 */
	nodeKey: string
}

export interface GetMatTypeTreeRequest {
	/**
	 * 物资分类编码
	 */
	code?: string
	/**
	 * 物资分类组合名称
	 */
	combineLabel?: string
	/**
	 * 上级物资分类ID
	 */
	fid?: number
	/**
	 * 物资分类名称
	 */
	label?: string
	/**
	 * 是否包含下级分类
	 */
	leafFlag?: string
	/**
	 * 低值易耗台账类型
	 */
	lowValueType?: string
	/**
	 * 实物资产台账类型
	 */
	propertyType?: string
	/**
	 * 物资分类状态;0正常 1冻结 2作废
	 */
	status?: string
}

export interface GetMatTypeListRequest {
	/**
	 * 物资分类编码
	 */
	code?: string
	/**
	 * 物资分类组合名称
	 */
	combineLabel?: string
	currentPage?: number
	/**
	 * 上级物资分类ID
	 */
	fid?: number
	/**
	 * 物资分类名称
	 */
	label?: string
	/**
	 * 是否包含下级分类
	 */
	leafFlag?: string
	/**
	 * 低值易耗台账类型
	 */
	lowValueType?: string
	pageSize?: number
	/**
	 * 实物资产台账类型
	 */
	propertyType?: string
	sidx?: string
	sord?: string
	/**
	 * 物资分类状态;0正常 1冻结 2作废
	 */
	status?: string
}

export interface AddMatTypeRequest {
	/**
	 * 物资分类编码
	 */
	code?: null | string
	/**
	 * 上级物资分类ID
	 */
	fid?: number | null
	/**
	 * 物资分类名称
	 */
	label?: null | string
	/**
	 * 是否包含下级分类
	 */
	leafFlag?: null | string
	/**
	 * 低值易耗台账类型
	 */
	lowValueType?: null | string
	/**
	 * 实物资产台账类型
	 */
	propertyType?: null | string
	/**
	 * 备注
	 */
	remark?: null | string
}
export interface UpdMatTypeRequest {
	/**
	 * ID
	 */
	id: number
	/**
	 * 物资分类编码
	 */
	code?: null | string
	/**
	 * 上级物资分类ID
	 */
	fid?: number | null
	/**
	 * 物资分类名称
	 */
	label?: null | string
	/**
	 * 是否包含下级分类
	 */
	leafFlag?: null | string
	/**
	 * 低值易耗台账类型
	 */
	lowValueType?: null | string
	/**
	 * 实物资产台账类型
	 */
	propertyType?: null | string
	/**
	 * 备注
	 */
	remark?: null | string
	[property: string]: any
}

export interface UpdateStatusBatchRequest {
	ids: string
	status: string
}

export interface GetMatApplyListRequest {
	currentPage?: number
	pageSize?: number
	planId: number
	sidx?: string
	sord?: string
}

export interface GetPlanNeedListRequest {
	/**
	 * 需求清单编码
	 */
	code?: string
	currentPage?: number
	/**
	 * 费用类别
	 */
	expenseCategory?: string
	/**
	 * 需求清单名称
	 */
	label?: string
	/**
	 * 专业ID
	 */
	majorId?: number
	pageSize?: number
	/**
	 * 需求计划类型;1年度计划
	 */
	planType?: string
	sidx?: string
	sord?: string
	/**
	 * 状态类型;0草稿 1 已启用 2已关闭
	 */
	status?: string
	/**
	 * 公司ID
	 */
	sysCommunityId?: number
}

export interface ModNeedRequest {
	/**
	 * 费用类别
	 */
	expenseCategory?: null | string
	/**
	 * 需求清单名称
	 */
	label?: null | string
	/**
	 * 专业ID
	 */
	majorId?: number | null
	/**
	 * 需求计划类型;1年度计划
	 */
	planType?: null | string
	/**
	 * 备注
	 */
	remark?: null | string
}

export interface GetMatListByAppIdRequest {
	currentPage?: number
	/**
	 * 物资ID
	 */
	materialId?: number
	pageSize?: number
	/**
	 * 需求清单ID
	 */
	planNeedId?: number
	sidx?: string
	sord?: string
}

export interface AddPlanNeedItemRequest {
	/**
	 * 物资ID
	 */
	materialId?: number | null
	/**
	 * 需求清单物资数量
	 */
	num?: number | null
	/**
	 * 需求清单ID
	 */
	planNeedId?: number | null
}

export interface UpdPlanNeedItemRequest {
	/**
	 * ID
	 */
	id: number
	/**
	 * 物资ID
	 */
	materialId?: number | null
	/**
	 * 需求清单物资数量
	 */
	num?: number | null
	/**
	 * 需求清单ID
	 */
	planNeedId?: number | null
}

export interface GetPlanListRequest {
	/**
	 * 审批状态;0待提交 1审批中 2已审批 3已驳回
	 */
	bpmStatus?: string
	/**
	 * 需求计划号
	 */
	code?: string
	currentPage?: number
	/**
	 * 段区ID
	 */
	depotId?: number
	/**
	 * 费用类别
	 */
	expenseCategory?: string
	/**
	 * 需求计划名称
	 */
	label?: string
	/**
	 * 线路ID
	 */
	lineNoId?: string
	/**
	 * 专业ID
	 */
	majorId?: number
	pageSize?: number
	/**
	 * 需求计划类型;1年度需求计划 2紧急计划 3社会化电商续采计划 4框架协议续采计划
	 */
	planType?: string
	sidx?: string
	sord?: string
	/**
	 * 公司ID
	 */
	sysCommunityId?: number
	/**
	 * 部门ID
	 */
	sysOrgId?: number
	/**
	 * 计划年度
	 */
	year?: number
}

export interface AddPlanRequest {
	/**
	 * 段区ID
	 */
	depotId?: number | null
	/**
	 * 费用类别
	 */
	expenseCategory?: null | string
	/**
	 * 需求计划名称
	 */
	label?: null | string
	/**
	 * 线路ID
	 */
	lineNoId?: null | string
	/**
	 * 专业ID
	 */
	majorId?: number | null
	/**
	 * 需求计划类型;1年度需求计划 2紧急计划 3社会化电商续采计划 4框架协议续采计划
	 */
	planType?: null | string
	/**
	 * 备注
	 */
	remark?: null | string
	/**
	 * 部门ID
	 */
	sysOrgId?: number | null
	/**
	 * 计划年度
	 */
	year?: number | null
}

export interface UpdPlanRequest {
	/**
	 * ID
	 */
	id: number
	/**
	 * 段区ID
	 */
	depotId?: number | null
	/**
	 * 费用类别
	 */
	expenseCategory?: null | string
	/**
	 * 需求计划名称
	 */
	label?: null | string
	/**
	 * 线路ID
	 */
	lineNoId?: null | string
	/**
	 * 专业ID
	 */
	majorId?: number | null
	/**
	 * 需求计划类型;1年度需求计划 2紧急计划 3社会化电商续采计划 4框架协议续采计划
	 */
	planType?: null | string
	/**
	 * 备注
	 */
	remark?: null | string
	/**
	 * 部门ID
	 */
	sysOrgId?: number | null
	/**
	 * 计划年度
	 */
	year?: number | null
}

export interface GetMatListByPlanIdRequest {
	currentPage?: number
	/**
	 * 物资ID
	 */
	materialId?: number
	pageSize?: number
	/**
	 * 需求计划ID
	 */
	planId?: number
	sidx?: string
	sord?: string
	/**
	 * 公司ID
	 */
	sysCommunityId?: number
}

export interface AddPlanItemRequest {
	/**
	 * 物资ID
	 */
	materialId?: number | null
	/**
	 * 数量
	 */
	num?: number | null
	/**
	 * 需求计划ID
	 */
	planId?: number | null
}

export interface UpdPlanItemRequest {
	/**
	 * ID
	 */
	id: number
	/**
	 * 物资ID
	 */
	materialId?: number | null
	/**
	 * 数量
	 */
	num?: number | null
	/**
	 * 需求计划ID
	 */
	planId?: number | null
}

export interface AddPlanItemMonthlyRequest {
	/**
	 * 月份
	 */
	month?: number | null
	/**
	 * 数量
	 */
	num?: number | null
	/**
	 * 需求计划物资ID
	 */
	planItemId?: number | null
	/**
	 * 需求计划年度
	 */
	year?: number | null
}

export interface UpdPlanItemMonthlyRequest {
	/**
	 * ID
	 */
	id: number
	/**
	 * 月份
	 */
	month?: number | null
	/**
	 * 数量
	 */
	num?: number | null
	/**
	 * 需求计划物资ID
	 */
	planItemId?: number | null
	/**
	 * 需求计划年度
	 */
	year?: number | null
}

export interface GetPlanItemMonthlyListRequest {
	currentPage?: number
	/**
	 * 月份
	 */
	month?: number
	pageSize?: number
	/**
	 * 需求计划物资ID
	 */
	planItemId: number
	sidx?: string
	sord?: string
	/**
	 * 需求计划年度
	 */
	year?: number
}

export interface GetPlanItemMonthlyListByPlanIdAndMaterialIdRequest {
	materialId?: number
	planId?: number
}

export interface GetPlanMergeListRequest {
	/**
	 * 审批状态;0待提交 1审批中 2已审批 3已驳回
	 */
	bpmStatus?: string
	/**
	 * 合并计划号
	 */
	code?: string
	currentPage?: number
	/**
	 * 段区ID
	 */
	depotId?: number
	/**
	 * 费用类别
	 */
	expenseCategory?: string
	/**
	 * 需求计划名称
	 */
	label?: string
	/**
	 * 线路ID
	 */
	lineNoId?: string
	/**
	 * 专业ID
	 */
	majorId?: number
	pageSize?: number
	/**
	 * 备注
	 */
	remark?: string
	sidx?: string
	sord?: string
	/**
	 * 公司ID
	 */
	sysCommunityId?: number
	/**
	 * 部门ID
	 */
	sysOrgId?: number
	/**
	 * 计划年度
	 */
	year?: number
}

export interface AddPlanMergeRequest {
	/**
	 * 段区ID
	 */
	depotId?: number | null
	/**
	 * 费用类别
	 */
	expenseCategory?: null | string
	/**
	 * 合并计划名称
	 */
	label?: null | string
	/**
	 * 线路ID
	 */
	lineNoId?: null | string
	/**
	 * 专业ID
	 */
	majorId?: number | null
	/**
	 * 备注
	 */
	remark?: null | string
	/**
	 * 计划年度
	 */
	year?: number | null
}

export interface UpdPlanMergeRequest {
	/**
	 * ID
	 */
	id: number
	/**
	 * 段区ID
	 */
	depotId?: number | null
	/**
	 * 费用类别
	 */
	expenseCategory?: null | string
	/**
	 * 合并计划名称
	 */
	label?: null | string
	/**
	 * 线路ID
	 */
	lineNoId?: null | string
	/**
	 * 专业ID
	 */
	majorId?: number | null
	/**
	 * 备注
	 */
	remark?: null | string
	/**
	 * 计划年度
	 */
	year?: number | null
}

export interface PublishApplyRequest {
	id: number
}

export interface GetPlanMergeItemListRequest {
	currentPage?: number
	/**
	 * 合并计划ID
	 */
	mergePlanId?: number
	pageSize?: number
	/**
	 * 需求计划ID
	 */
	planId?: number
	sidx?: string
	sord?: string
}

export interface AddPlanMergeItemRequest {
	/**
	 * 合并计划ID
	 */
	mergePlanId?: number | null
	/**
	 * 需求计划ID
	 */
	planId?: number | null
}

export interface UpdPlanMergeItemRequest {
	/**
	 * ID
	 */
	id: number
	/**
	 * 合并计划ID
	 */
	mergePlanId?: number | null
	/**
	 * 需求计划ID
	 */
	planId?: number | null
}

export interface GetPlanMergeItemMaterialListRequest {
	currentPage?: number
	/**
	 * 合并计划ID
	 */
	mergePlanId?: number
	pageSize?: number
	/**
	 * 需求计划ID
	 */
	planId?: number
	sidx?: string
	sord?: string
}

export interface GetPlanMergeItemMaterialNeedPlanListRequest {
	currentPage?: number
	/**
	 * 物资ID
	 */
	materialId?: number
	/**
	 * 合并计划ID
	 */
	mergePlanId?: number
	pageSize?: number
	sidx?: string
	sord?: string
}

export interface GetPlanPurchaseListRequest {
	/**
	 * 审批状态;0待提交 1审批中 2已审批 3已驳回
	 */
	bpmStatus?: string
	/**
	 * 采购计划号
	 */
	code?: string
	currentPage?: number
	/**
	 * 已分包物资编码数量
	 */
	distributionNum?: number
	/**
	 * 采购计划名称
	 */
	label?: string
	pageSize?: number
	/**
	 * 需求计划类型;1年度需求计划
	 */
	planType?: string
	/**
	 * 推送电商状态;0未推送 1已推送
	 */
	pushEcommerceStatus?: string
	sidx?: string
	sord?: string
	/**
	 * 公司ID
	 */
	sysCommunityId?: number
	/**
	 * 计划年度
	 */
	year?: number
}

export interface AddPlanPurchaseRequest {
	/**
	 * 采购计划名称
	 */
	label?: null | string
	/**
	 * 需求计划类型;1年度需求计划
	 */
	planType?: null | string
	/**
	 * 备注
	 */
	remark?: null | string
	/**
	 * 计划年度
	 */
	year?: number | null
}

export interface UpdPlanPurchaseRequest {
	/**
	 * ID
	 */
	id: number
	/**
	 * 采购计划名称
	 */
	label?: null | string
	/**
	 * 需求计划类型;1年度需求计划
	 */
	planType?: null | string
	/**
	 * 备注
	 */
	remark?: null | string
	/**
	 * 计划年度
	 */
	year?: number | null

	/**
	 *	原因
	 */
	reason?: null | string
}

export interface UpdatePushEcommerceStatusRequest {
	id: number
}

export interface PublishApplyRequest {
	id: number
}

export interface GetPlanPurchaseMaterialItemListRequest {
	currentPage?: number
	/**
	 * 物资ID
	 */
	materialId?: number
	pageSize?: number
	/**
	 * 采购计划ID
	 */
	purchasePlanId?: number
	sidx?: string
	sord?: string
}

export interface GetPlanPurchaseNeedPlanListRequest {
	currentPage?: number
	/**
	 * 物资ID
	 */
	materialId?: number
	pageSize?: number
	/**
	 * 采购计划ID
	 */
	purchasePlanId?: number
	sidx?: string
	sord?: string
}

export interface getPlanPurchaseMaterialItemNeedPlanListRequest {
	currentPage?: number
	/**
	 * 物资ID
	 */
	materialId?: number
	pageSize?: number
	/**
	 * 采购计划ID
	 */
	purchasePlanId?: number
	sidx?: string
	sord?: string
}

export interface BalancePurchaseMaterialItemResponse {
	/**
	 * 状态码
	 */
	code?: null | string
	/**
	 * 返回数据
	 */
	data?: null
	/**
	 * 接口描述
	 */
	msg?: null | string
}

export interface GetPlanPurchaseMergeItemListRequest {
	currentPage?: number
	/**
	 * 合并计划ID
	 */
	mergePlanId?: number
	pageSize?: number
	/**
	 * 采购计划ID
	 */
	purchasePlanId?: number
	sidx?: string
	sord?: string
}

export interface AddPlanPurchaseMergeItemRequest {
	/**
	 * 合并计划ID
	 */
	mergePlanId?: number | null
	/**
	 * 采购计划ID
	 */
	purchasePlanId?: number | null
}

export interface UpdPlanPurchaseMergeItemRequest {
	/**
	 * ID
	 */
	id: number
	/**
	 * 合并计划ID
	 */
	mergePlanId?: number | null
	/**
	 * 采购计划ID
	 */
	purchasePlanId?: number | null
}

export interface GetPurchaseContractListRequest {
	/**
	 * 合同金额
	 */
	contractAmount: string
	/**
	 * 合同编号
	 */
	contractCode: string
	/**
	 * 合同截止日期
	 */
	contractEndDate: string
	/**
	 * 合同名称
	 */
	contractLabel: string
	/**
	 * 合同签订日期
	 */
	contractSigningDate: string
	/**
	 * 合同类型
	 */
	contractType: string
	currentPage?: number
	/**
	 * 有效截止日期
	 */
	expireDate?: string
	pageSize?: number
	/**
	 * 创建时间
	 */
	purchaseCreateDate?: string
	sidx?: string
	sord?: string
	/**
	 * 供应商id
	 */
	supplierId: number
}

export interface AddPurchaseContractRequest {
	/**
	 * 合同金额
	 */
	contractAmount: number | null
	/**
	 * 合同编号
	 */
	contractCode: null | string
	/**
	 * 合同截止日期
	 */
	contractEndDate: null | string
	/**
	 * 合同名称
	 */
	contractLabel: null | string
	/**
	 * 合同签订日期
	 */
	contractSigningDate: null | string
	/**
	 * 合同类型
	 */
	contractType: null | string
	/**
	 * 有效截止日期
	 */
	expireDate?: null | string
	/**
	 * 创建时间
	 */
	purchaseCreateDate?: null | string
	/**
	 * 备注
	 */
	remark?: null | string
	/**
	 * 供应商id
	 */
	supplierId: number | null
}

export interface UpdPurchaseContractRequest {
	/**
	 * ID
	 */
	id: number
	/**
	 * 合同金额
	 */
	contractAmount: number | null
	/**
	 * 合同编号
	 */
	contractCode: null | string
	/**
	 * 合同截止日期
	 */
	contractEndDate: null | string
	/**
	 * 合同名称
	 */
	contractLabel: null | string
	/**
	 * 合同签订日期
	 */
	contractSigningDate: null | string
	/**
	 * 合同类型
	 */
	contractType: null | string
	/**
	 * 有效截止日期
	 */
	expireDate?: null | string
	/**
	 * 创建时间
	 */
	purchaseCreateDate?: null | string
	/**
	 * 备注
	 */
	remark?: null | string
	/**
	 * 供应商id
	 */
	supplierId: number | null
}

export interface GetPurchaseDistributionListRequest {
	/**
	 * 审批状态;0待提交 1审批中 2已审批 3已驳回
	 */
	bpmStatus?: string
	/**
	 * 分包编码
	 */
	code?: string
	/**
	 * 合同状态
	 */
	contractStatus?: string
	currentPage?: number
	/**
	 * 分包名称
	 */
	label?: string
	/**
	 * 线路ID
	 */
	lineId?: string
	/**
	 * 专业ID
	 */
	majorId?: number
	pageSize?: number
	/**
	 * 采购计划编码
	 */
	planPurchaseCode?: string
	/**
	 * 采购计划ID
	 */
	planPurchaseId?: number
	/**
	 * 采购计划名称
	 */
	planPurchaseLabel?: string
	/**
	 * 采购计划年度
	 */
	planPurchaseYear?: number
	/**
	 * 采购员ID
	 */
	purchaseUserId?: string
	sidx?: string
	sord?: string
	/**
	 * 公司ID
	 */
	sysCommunityId?: number
}

export interface AddPurchaseDistributionRequest {
	/**
	 * 分包名称
	 */
	label?: null | string
	/**
	 * 线路ID
	 */
	lineId?: null | string
	/**
	 * 专业ID
	 */
	majorId?: number | null
	/**
	 * 采购计划ID
	 */
	planPurchaseId?: number | null
	/**
	 * 采购员id
	 */
	purchaseUserId?: null | string
	/**
	 * 备注
	 */
	remark?: null | string
}

export interface UpdPurchaseDistributionRequest {
	/**
	 * ID
	 */
	id: number
	/**
	 * 分包名称
	 */
	label?: null | string
	/**
	 * 线路ID
	 */
	lineId?: null | string
	/**
	 * 专业ID
	 */
	majorId?: number | null
	/**
	 * 采购计划ID
	 */
	planPurchaseId?: number | null
	/**
	 * 采购员id
	 */
	purchaseUserId?: null | string
	/**
	 * 备注
	 */
	remark?: null | string
}

export interface PublishPurchaseDistributionRequest {
	id: number
}

export interface GetPurchaseDistributionItemListRequest {
	currentPage?: number
	/**
	 * 分包ID
	 */
	distributionId?: number
	pageSize?: number
	/**
	 * 采购计划物资明细ID
	 */
	planPurchaseMaterialItemId?: number
	sidx?: string
	sord?: string
}

export interface AddPurchaseDistributionItemRequest {
	/**
	 * 分包ID
	 */
	distributionId?: number | null
	/**
	 * 采购计划物资明细ID
	 */
	planPurchaseMaterialItemId?: number | null
}

export interface UpdPurchaseDistributionItemRequest {
	/**
	 * ID
	 */
	id: number
	/**
	 * 分包ID
	 */
	distributionId?: number | null
	/**
	 * 采购计划物资明细ID
	 */
	planPurchaseMaterialItemId?: number | null
}

export interface GetPurchasePlanItemListRequest {
	/**
	 * 审批状态;0待提交 1审批中 2已审批 3已驳回
	 */
	bpmStatus?: string
	/**
	 * 分包编码
	 */
	code?: string
	currentPage?: number
	/**
	 * 分包名称
	 */
	label?: string
	/**
	 * 线路ID
	 */
	lineId?: string
	/**
	 * 专业ID
	 */
	majorId?: number
	pageSize?: number
	/**
	 * 采购计划编码
	 */
	planPurchaseCode?: string
	/**
	 * 采购计划ID
	 */
	planPurchaseId: number
	/**
	 * 采购计划名称
	 */
	planPurchaseLabel?: string
	/**
	 * 采购计划年度
	 */
	planPurchaseYear?: number
	/**
	 * 采购员id
	 */
	purchaseUserId?: string
	/**
	 * 备注
	 */
	remark?: string
	sidx?: string
	sord?: string
	/**
	 * 公司ID
	 */
	sysCommunityId?: number
}

export interface GetPurchaseInvoiceListRequest {
	/**
	 * 开票金额
	 */
	amount?: string
	/**
	 * 冲红对应蓝字发票ID
	 */
	blueInvoiceId?: number
	/**
	 * 审批状态;0待提交 1审批中 2已审批 3已驳回
	 */
	bpmStatus?: string
	/**
	 * 发票号码
	 */
	code?: string
	/**
	 * 合同编号
	 */
	contractCode?: string
	/**
	 * 创建人真实姓名
	 */
	createdUserRealname?: string
	currentPage?: number
	/**
	 * 发票颜色
	 */
	invoiceColor?: string
	pageSize?: number
	/**
	 * 采购订单ID
	 */
	purchaseOrderCode?: string
	sidx?: string
	sord?: string
	/**
	 * 发票状态;0待确认 1已开票 2已红冲
	 */
	status?: string
	/**
	 * 供货商ID
	 */
	supplierId?: number
	/**
	 * 所属公司ID
	 */
	sysCommunityId?: number
	/**
	 * 所属部门ID
	 */
	sysOrgId?: number
	/**
	 * 税率
	 */
	taxRate?: string
	/**
	 * 发票类型
	 */
	type?: string
}

export interface GetPurchaseInvoiceListByContractRequest {
	contractId: number
	currentPage?: number
	pageSize?: number
	sidx?: string
	sord?: string
}

export interface AddPurchaseInvoiceRequest {
	/**
	 * 冲红对应蓝字发票ID
	 */
	blueInvoiceId?: number | null
	/**
	 * 发票号码
	 */
	code?: null | string
	/**
	 * 合同编号
	 */
	contractCode?: null | string
	/**
	 * 发票颜色
	 */
	invoiceColor?: null | string
	/**
	 * 开票日期
	 */
	invoiceDate?: null | string
	/**
	 * 采购订单ID
	 */
	purchaseOrderCode?: null | string
	/**
	 * 备注
	 */
	remark?: null | string
	/**
	 * 供货商ID
	 */
	supplierId?: number | null
	/**
	 * 税率
	 */
	taxRate?: number | null
	/**
	 * 发票类型
	 */
	type?: null | string
}

export interface UpdPurchaseInvoiceRequest {
	/**
	 * ID
	 */
	id: number

	/**
	 * 冲红对应蓝字发票ID
	 */
	blueInvoiceId?: number | null
	/**
	 * 发票号码
	 */
	code?: null | string
	/**
	 * 合同编号
	 */
	contractCode?: null | string
	/**
	 * 发票颜色
	 */
	invoiceColor?: null | string
	/**
	 * 开票日期
	 */
	invoiceDate?: null | string
	/**
	 * 采购订单ID
	 */
	purchaseOrderCode?: null | string
	/**
	 * 备注
	 */
	remark?: null | string
	/**
	 * 供货商ID
	 */
	supplierId?: number | null
	/**
	 * 税率
	 */
	taxRate?: number | null
	/**
	 * 发票类型
	 */
	type?: null | string
}

export interface GetPurchaseInvoiceItemListRequest {
	currentPage?: number
	/**
	 * 发票ID
	 */
	invoiceId?: number
	pageSize?: number
	/**
	 * 采购订单编号
	 */
	purchaseOrderCode?: string
	/**
	 * 采购订单ID
	 */
	purchaseOrderId?: number
	/**
	 * 采购订单物资明细ID
	 */
	purchaseOrderItemId?: number
	sidx?: string
	sord?: string
}

export interface GetPurchaseInvoiceAddItemRequest {
	currentPage?: number
	/**
	 * 发票ID
	 */
	id: number
	pageSize?: number
	/**
	 * 采购订单ID
	 */
	purchaseOrderCode?: string
	sidx?: string
	sord?: string
}

export interface AddPurchaseInvoiceItemRequest {
	/**
	 * 发票ID
	 */
	invoiceId?: string
	/**
	 * 物资ID
	 */
	materialId: string
	/**
	 * 采购订单ID
	 */
	purchaseOrderId?: string
	/**
	 * 库存批次明细ID（可添加物资列表中的id）
	 */
	storeBatchItemId?: string
}

export interface updatePurchaseInvoiceItemRequest {
	/**
	 * ID
	 */
	id: number
	/**
	 * 开票数量
	 */
	num?: number | null
}

export interface GetPurchaseInvoiceOrderListRequest {
	currentPage?: number
	/**
	 * 发票ID
	 */
	invoiceId?: number
	/**
	 * 订单ID
	 */
	orderId?: number
	pageSize?: number
	sidx?: string
	sord?: string
}

export interface GetPurchaseInvoiceOrderListCanAddRequest {
	currentPage?: number
	/**
	 * 发票ID
	 */
	id: number
	pageSize?: number
	sidx?: string
	sord?: string
}

export interface AddPurchaseInvoiceOrderRequest {
	/**
	 * 发票ID
	 */
	invoiceId?: number | null
	/**
	 * 订单ID
	 */
	orderIdList: number[] | null
}

export interface GetPurchaseOrderListRequest {
	/**
	 * 发票ID
	 */
	invoiceId?: number | null
	/**
	 * 订单ID
	 */
	orderId?: number | null
}

export interface AddPurchaseOrderRequest {
	/**
	 * 合同ID
	 */
	contractId?: number | null
	/**
	 * 外部单号
	 */
	externalOrder?: null | string
	/**
	 * 采购订单名称
	 */
	label?: null | string
	/**
	 * 最晚送货日期
	 */
	latestDeliveryDate?: null | string
	/**
	 * 月份
	 */
	month?: number | null
	/**
	 * 采购计划ID
	 */
	planPurchaseId?: number | null
	/**
	 * 采购项目ID
	 */
	projectId?: number | null
	/**
	 * 采购员ID
	 */
	purchaseUserId?: null | string
	/**
	 * 备注
	 */
	remark?: null | string
	/**
	 * 订单来源
	 */
	source?: null | string
	/**
	 * 供货商ID
	 */
	supplierId?: number | null
	/**
	 * 计划年度
	 */
	year?: number | null
}

export interface updatePurchaseOrderRequest {
	/**
	 * 采购订单号
	 */
	code?: null | string
	/**
	 * 合同ID
	 */
	contractId?: number | null
	/**
	 * 外部单号
	 */
	externalOrder?: null | string
	/**
	 * ID
	 */
	id: number
	/**
	 * 采购订单名称
	 */
	label?: null | string
	/**
	 * 最晚送货日期
	 */
	latestDeliveryDate?: null | string
	/**
	 * 月份
	 */
	month?: number | null
	/**
	 * 采购计划ID
	 */
	planPurchaseId?: number | null
	/**
	 * 采购项目ID
	 */
	projectId?: number | null
	/**
	 * 采购员ID
	 */
	purchaseUserId?: null | string
	/**
	 * 备注
	 */
	remark?: null | string
	/**
	 * 订单来源
	 */
	source?: null | string
	/**
	 * 供货商ID
	 */
	supplierId?: number | null
	/**
	 * 计划年度
	 */
	year?: number | null
}

/**
 * 更新采购员 入参
 */
export interface UpdatePurchaseUserRequest {
	/**
	 * id集合
	 */
	idList?: number[] | null
	/**
	 * 采购员ID
	 */
	purchaseUserId?: null | string
	[property: string]: any
}

export interface PublishRequest {
	id: number
}

export interface PublishChangeRequest {
	id: number
}

export interface ConfirmArrivalRequest {
	/**
	 * 采购订单ID
	 */
	id: number
	/**
	 * 采购订单物资明细
	 */
	purchaseOrderItemDTOList: {
		/**
		 * 本次到货量
		 */
		completedArrivedThisNum: number
		/**
		 * 采购订单物资明细ID
		 */
		id: string
	}[]
}

export interface GetPurchaseOrderItemListRequest {
	currentPage?: number
	/**
	 * 物资ID
	 */
	materialId?: number
	/**
	 * 订单ID
	 */
	orderId?: number
	pageSize?: number
	sidx?: string
	sord?: string
	/**
	 * 指定仓库ID
	 */
	storeId?: number
	[property: string]: any
}

export interface AddPurchaseOrderItemRequest {
	/**
	 * 物资ID
	 */
	materialId?: number | null
	/**
	 * 订单ID
	 */
	orderId?: number | null
}

export interface UpdatePurchaseOrderItemRequest {
	/**
	 * ID
	 */
	id: number
	/**
	 * 订货数量
	 */
	orderingNum: number
	/**
	 * 变更的采购单价（待审批）
	 */
	purchaseUnitPriceChange: number
	/**
	 * 指定仓库ID
	 */
	storeId?: number | null
}

export interface GetPurchasePlanListRequest {
	/**
	 * 审批状态;0待提交，1审批中，2已审批，3已驳回
	 */
	bpmStatus?: string
	/**
	 * 月度订货计划号
	 */
	code?: string
	currentPage?: number
	/**
	 * 生成采购订单状态;0未生成 1已生成
	 */
	generatePurchaseStatus?: string
	/**
	 * 月份
	 */
	month?: number
	pageSize?: number
	/**
	 * 采购计划编码
	 */
	planPurchaseCode?: string
	/**
	 * 采购计划ID
	 */
	planPurchaseId?: number
	/**
	 * 采购计划名称
	 */
	planPurchaseLabel?: string
	/**
	 * 采购计划年度
	 */
	planPurchaseYear?: number
	/**
	 * 采购项目ID
	 */
	projectId?: number
	/**
	 * 采购计划编码
	 */
	purchaseProjectCode?: string
	/**
	 * 采购计划名称
	 */
	purchaseProjectLabel?: string
	sidx?: string
	sord?: string
	/**
	 * 状态;0未完成 1已完成 2已取消
	 */
	status?: string
	/**
	 * 供货商ID
	 */
	supplierId?: number
	/**
	 * 公司ID
	 */
	sysCommunityId?: number
	/**
	 * 部门ID
	 */
	sysOrgId?: number
	/**
	 * 计划年度
	 */
	year?: number
}

export interface GetPurchasePlanItemListRequest {
	/**
	 * 审批状态
	 */
	bpmStatus?: string
	/**
	 * 订货计划号
	 */
	code?: string
	currentPage?: number
	/**
	 * 用于查询物资的需求计划
	 */
	materialId?: number
	/**
	 * 月份
	 */
	month?: number
	pageSize?: number
	/**
	 * 需求计划ID
	 */
	planId?: number
	/**
	 * 采购计划编码
	 */
	planPurchaseCode?: string
	/**
	 * 采购计划ID
	 */
	planPurchaseId: number
	/**
	 * 采购计划名称
	 */
	planPurchaseLabel?: string
	/**
	 * 采购计划年度
	 */
	planPurchaseYear?: number
	/**
	 * 采购项目ID
	 */
	projectId?: number
	/**
	 * 订货计划ID
	 */
	purchasePlanId?: number
	/**
	 * 采购计划编码
	 */
	purchaseProjectCode?: string
	/**
	 * 采购计划名称
	 */
	purchaseProjectLabel?: string
	sidx?: string
	sord?: string
	/**
	 * 供货商ID
	 */
	supplierId?: number
	/**
	 * 公司ID
	 */
	sysCommunityId?: number
	/**
	 * 部门ID
	 */
	sysOrgId?: number
	/**
	 * 计划年度
	 */
	year?: number
}

export interface GetPurchasePlanItemByMaterialRequest {
	/**
	 * 提前订货标记
	 */
	advanceFlag?: string
	/**
	 * 确认状态
	 */
	confirmStatus?: string
	currentPage?: number
	/**
	 * 预估采购单价
	 */
	evaluation?: string
	/**
	 * 预估采购周期
	 */
	evaluationCycle?: number
	/**
	 * 物资ID
	 */
	materialId?: number
	/**
	 * 月份
	 */
	month?: number
	pageSize?: number
	/**
	 * 需求计划ID
	 */
	planId?: number
	/**
	 * 需求计划物资ID
	 */
	planItemId?: number
	/**
	 * 订货计划ID
	 */
	purchasePlanId?: number
	/**
	 * 订货计划需求计划明细ID
	 */
	purchasePlanItemId?: number
	sidx?: string
	sord?: string
	/**
	 * 计划年度
	 */
	year?: number
}

export interface GetPurchasePlanMaterialItemListRequest {
	/**
	 * 提前订货标记
	 */
	advanceFlag?: string
	/**
	 * 确认状态
	 */
	confirmStatus?: string
	currentPage?: number
	/**
	 * 预估采购周期
	 */
	evaluationCycle?: number
	/**
	 * 物资ID
	 */
	materialId?: number
	/**
	 * 月份
	 */
	month?: number
	pageSize?: number
	/**
	 * 需求计划ID
	 */
	planId?: number
	/**
	 * 需求计划物资ID
	 */
	planItemId?: number
	/**
	 * 订货计划ID
	 */
	purchasePlanId?: number
	/**
	 * 订货计划需求计划明细ID
	 */
	purchasePlanItemId?: number
	sidx?: string
	sord?: string
	/**
	 * 计划年度
	 */
	year?: number
}

export interface GetPurchaseAdvanceOrderItemRequest {
	currentPage?: number
	pageSize?: number
	/**
	 * 需求计划ID
	 */
	planId: number
	/**
	 * 订货计划需求计划明细ID
	 */
	purchasePlanItemId: number
	sidx?: string
	sord?: string
}

export interface AddPurchasePlanMaterialItemRequest {
	/**
	 * 提前订货标记
	 */
	advanceFlag?: null | string
	/**
	 * 物资ID
	 */
	materialId?: number | null
	/**
	 * 需求计划ID
	 */
	planId?: number | null
	/**
	 * 需求计划物资ID
	 */
	planItemId?: number | null
	/**
	 * 订货计划ID
	 */
	purchasePlanId?: number | null
	/**
	 * 订货计划需求计划明细ID
	 */
	purchasePlanItemId?: number | null
}

export interface UpdatePurchasePlanMaterialItemRequest {
	/**
	 * ID
	 */
	id: number
	/**
	 * 本月订货数量
	 */
	num?: number | null
}

export interface GetPurchaseProjectListRequest {
	/**
	 * 集采项目编号
	 */
	centralizePurchaseProjectCode?: string
	/**
	 * 采购项目编号
	 */
	code?: string
	/**
	 * 合同截止日期-结束
	 */
	contractEndDate_end?: string
	/**
	 * 合同截止日期-开始
	 */
	contractEndDate_start?: string
	/**
	 * 合同ID
	 */
	contractId?: number
	/**
	 * 合同签订日期-结束
	 */
	contractSigningDate_end?: string
	/**
	 * 合同签订日期-开始
	 */
	contractSigningDate_start?: string
	/**
	 * 合同类型
	 */
	contractType?: string
	currentPage?: number
	/**
	 * 有效截止日期
	 */
	expireDate?: string
	/**
	 * 采购项目名称
	 */
	label?: string
	pageSize?: number
	/**
	 * 采购计划编码
	 */
	planPurchaseCode?: string
	/**
	 * 采购计划ID
	 */
	planPurchaseId?: number
	/**
	 * 采购计划名称
	 */
	planPurchaseLabel?: string
	/**
	 * 采购计划年度
	 */
	planPurchaseYear?: number
	/**
	 * 创建时间
	 */
	purchaseCreateDate?: string
	/**
	 * 采购方式
	 */
	purchaseType?: string
	/**
	 * 采购员id
	 */
	purchaseUserId?: string
	/**
	 * 备注
	 */
	remark?: string
	sidx?: string
	sord?: string
	/**
	 * 供货商id
	 */
	supplierId?: number
	/**
	 * 年度
	 */
	year?: number
}

export interface AddPurchaseProjectRequest {
	/**
	 * 合同ID
	 */
	contractId?: number | null
	/**
	 * 有效截止日期
	 */
	expireDate?: null | string
	/**
	 * 采购方式
	 */
	purchaseType?: null | string
	/**
	 * 采购员id
	 */
	purchaseUserId?: null | string
	/**
	 * 备注
	 */
	remark?: null | string
}

export interface UpdatePurchaseProjectRequest {
	/**
	 * 合同ID
	 */
	contractId?: number | null
	/**
	 * 有效截止日期
	 */
	expireDate?: null | string
	/**
	 * ID
	 */
	id: number
	/**
	 * 采购方式
	 */
	purchaseType?: null | string
	/**
	 * 采购员id
	 */
	purchaseUserId?: null | string
	/**
	 * 备注
	 */
	remark?: null | string
}

export interface AddPurchaseSupplierRequest {
	/**
	 * 供应商编码
	 */
	code?: null | string
	/**
	 * 联系人
	 */
	contact?: null | string
	/**
	 * 联系地址
	 */
	contactAddress?: null | string
	/**
	 * 邮箱（Email）
	 */
	contactEmail?: null | string
	/**
	 * 联系人手机号
	 */
	contactPhoneNumber?: null | string
	/**
	 * 供应商名称
	 */
	label?: null | string
	/**
	 * 法定代表人
	 */
	legalRepresentative?: null | string
	/**
	 * 备注
	 */
	remark?: null | string
}

export interface UpdatePurchaseSupplierRequest {
	/**
	 * 供应商编码
	 */
	code?: null | string
	/**
	 * 联系人
	 */
	contact?: null | string
	/**
	 * 联系地址
	 */
	contactAddress?: null | string
	/**
	 * 邮箱（Email）
	 */
	contactEmail?: null | string
	/**
	 * 联系人手机号
	 */
	contactPhoneNumber?: null | string
	/**
	 * ID
	 */
	id: number
	/**
	 * 供应商名称
	 */
	label?: null | string
	/**
	 * 法定代表人
	 */
	legalRepresentative?: null | string
	/**
	 * 备注
	 */
	remark?: null | string
}

export interface GenerateMonthlyPurchasePlanRequest {
	id: number
	month: number
}

export interface GetPurchaseProjectItemListRequest {
	currentPage?: number
	pageSize?: number
	/**
	 * 采购计划物资明细ID
	 */
	planPurchaseMaterialItemId?: number
	/**
	 * 采购项目ID
	 */
	projectId?: number
	sidx?: string
	sord?: string
}

export interface GetPurchaseSupplierListRequest {
	/**
	 * 供应商编码
	 */
	code?: string
	/**
	 * 联系人
	 */
	contact?: string
	/**
	 * 联系地址
	 */
	contactAddress?: string
	/**
	 * 邮箱（Email）
	 */
	contactEmail?: string
	/**
	 * 联系人手机号
	 */
	contactPhoneNumber?: string
	currentPage?: number
	/**
	 * 供应商名称
	 */
	label?: string
	/**
	 * 法定代表人
	 */
	legalRepresentative?: string
	pageSize?: number
	/**
	 * 备注
	 */
	remark?: string
	sidx?: string
	sord?: string
}

export interface MaterialCode {
	/**
	 * 物资性质
	 */
	attribute?: null | string
	/**
	 * 审批状态;0待提交，1审批中，2已审批，3已驳回
	 */
	bpmStatus?: null | string
	/**
	 * 采购单位
	 */
	buyUnit?: number | null
	/**
	 * 物资编码
	 */
	code?: null | string
	/**
	 * 库存转换比例
	 */
	conversionUnit?: number | null
	createdBy?: null | string
	createdDate?: null | string
	/**
	 * 是否包含危险废物
	 */
	dangerousWasteFlag?: null | string
	/**
	 * 预估采购单价
	 */
	evaluation?: number | null
	/**
	 * 是否可维修
	 */
	fixFlag?: null | string
	/**
	 * ID
	 */
	id?: number | null
	/**
	 * 编码名称
	 */
	label?: null | string
	lastModifiedBy?: null | string
	lastModifiedDate?: null | string
	lastModifiedRealName?: null | string
	materialType?: MaterialType
	/**
	 * 物资分类ID
	 */
	materialTypeId?: number | null
	/**
	 * 图片
	 */
	photoName?: null | string
	/**
	 * 审批实例ID
	 */
	procInsId?: null | string
	/**
	 * 主要材质;{"铜":0.5，"铁":0.8}
	 */
	quality?: null | string
	/**
	 * 是否可回收
	 */
	recoveryFlag?: null | string
	/**
	 * 预估回收重量
	 */
	recoveryWeight?: number | null
	/**
	 * 物资编码状态;0正常 1作废 2冻结 3新增
	 */
	status?: null | string
	/**
	 * 所属公司ID
	 */
	sysCommunityId?: number | null
	/**
	 * 所属部门ID
	 */
	sysOrgId?: number | null
	/**
	 * 技术参数
	 */
	technicalParameter?: null | string
	/**
	 * 库存单位
	 */
	useUnit?: number | null
	/**
	 * 规格型号
	 */
	version?: null | string
	/**
	 * 废旧物资类型
	 */
	wasteMaterialType?: null | string
}

/**
 * MaterialType
 *
 * com.bmzymtr.baseline.mms.material.entity.MaterialType
 */
export interface MaterialType {
	ancestors?: null | string
	children?: MaterialType[] | null
	/**
	 * 物资分类编码
	 */
	code?: null | string
	/**
	 * 物资分类组合名称
	 */
	combineLabel?: null | string
	createdBy?: null | string
	createdDate?: null | string
	fid?: number | null
	id?: number | null
	/**
	 * 物资分类名称
	 */
	label?: null | string
	lastModifiedBy?: null | string
	lastModifiedDate?: null | string
	/**
	 * 是否包含下级分类
	 */
	leafFlag?: null | string
	/**
	 * 低值易耗台账类型
	 */
	lowValueType?: null | string
	/**
	 * 实物资产台账类型
	 */
	propertyType?: null | string
	/**
	 * 备注
	 */
	remark?: null | string
	/**
	 * 物资分类状态;0正常 1冻结 2作废
	 */
	status?: null | string
}

export interface GetYearPlanListRequest {
	currentPage?: number
	pageSize?: number
	status?: number | string
	year?: string | string
}
export interface AddYearPlanRequest {
	/**
	 * 计划填报开始日期
	 */
	beginDate?: string
	/**
	 * 计划填报截止日期
	 */
	endDate?: string
	/**
	 * 年度计划名称
	 */
	label?: string | string
	/**
	 * 计划年度
	 */
	year?: number | string
	id?: number | string
}

/**
 * 年度计划类型 枚举值
 */
export enum IEndWindowPhase {
	NeedPlan = "1",
	PurchasePlan = "2"
}
/**
 * 更新 采购计划物资明细 入参
 */
export interface updateBalancePurchaseMaterialItemRequest {
	/**
	 * 采购计划物资明细ID
	 */
	id: number
	/**
	 * 采购数量
	 */
	purchaseNum: number
	[property: string]: any
}
