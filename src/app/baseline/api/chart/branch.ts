/**
 * @description 废旧 - 交旧申请
 */

import ReqUtil from "../../utils/request-util"

import {
	Datum,
	StatementVO,
	StatementVORequest
} from "../../utils/types/chart-branch"

/**
 * 交旧申请 分页
 */
export async function listFindReportGroup(params: StatementVORequest) {
	return ReqUtil.get<any>("/baseline/system/statement/findReportGroup", {
		params
	})
}

/**
 * 新建
 */

export async function addReportInfo(data: Record<string, any>) {
	return ReqUtil.post<any>("/baseline/system/statement/addReportInfo", {
		data
	})
}

/**
 * 编辑
 */
export async function updateReportInfo(data: Record<string, any>) {
	return ReqUtil.put<any>("/baseline/system/statement/updateReportInfo", {
		data
	})
}

/**
 * 删除
 */
export async function delReportInfo(id: number) {
	return ReqUtil.delete<any>("/baseline/system/statement/deleteReportInfo", {
		params: { id }
	})
}
