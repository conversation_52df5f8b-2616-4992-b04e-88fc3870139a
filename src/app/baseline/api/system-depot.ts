/**
 * 段区
 */
import ReqUtil from "../utils/request-util"
import { PageResVo } from "../utils/types/common"

import {
	SystemDepotDTO,
	systemDepotPagedRequest,
	SystemDepotVo
} from "../utils/types/system-depot"

/**
 * 段区-分页查询
 */
export function getSystemDepotPaged(params: systemDepotPagedRequest = {}) {
	return ReqUtil.get<PageResVo<SystemDepotVo>>("/baseline/system/depot/page", {
		params
	})
}

/**
 * 段区-新增
 */
export function addSystemDepot(data: SystemDepotDTO, idempotentToken?: string) {
	return ReqUtil.post<SystemDepotVo>("/baseline/system/depot", {
		data,
		headers: { "idempotent-token": idempotentToken }
	})
}

/**
 * 段区-编辑
 */
export function updateSystemDepot(
	data: SystemDepotDTO,
	_idempotentToken?: string
) {
	return ReqUtil.put<SystemDepotVo>("/baseline/system/depot", {
		data
	})
}

/**
 * 段区-删除
 */
export function delSystemDepot(id: any) {
	return ReqUtil.delete<boolean | null>(`/baseline/system/depot/${id}`)
}

/**
 * 段区-查询详情
 */
export function getSystemDepotById(id: any) {
	return ReqUtil.get<SystemDepotVo>(`/baseline/system/depot/${id}`)
}

/**
 * 段区-批量启用
 */
export function batchEnableDepot(idList: number[] | null) {
	return ReqUtil.post<null>("/baseline/system/depot/batchEnable", {
		data: { idList }
	})
}

/**
 * 段区-批量停用
 */
export function batchDisableDepot(idList: number[] | null) {
	return ReqUtil.post<null>("/baseline/system/depot/batchDisable", {
		data: { idList }
	})
}
