/**
 * @description 低值 - 盘点管理/盘点任务 Api
 */

import { PageResVo } from "../../utils/types/common"
import ReqUtil from "../../utils/request-util"
import {
	MatLowValueCheckAllocationPageVo,
	MatLowValueCheckMaterialDetailVo,
	MatLowValueCheckMaterialPageVo,
	MatLowValueCheckTaskAssignUserDTO,
	MatLowValueCheckTaskDetailVo,
	MatLowValueCheckTaskPageVo,
	MatLowValueCheckTaskPageVoRequest
} from "../../utils/types/lowValue-inventory-manage"

/**
 * 获取状态统计
 * @param params
 * @returns
 */
export async function getLowvalueCheckTaskBpmStatusCnt(
	params: MatLowValueCheckTaskPageVoRequest
) {
	return ReqUtil.get<number[]>(
		"/baseline/lowvalue/checkTask/statusStatusStatistics",
		{
			params
		}
	)
}

/**
 * 盘点任务 主列表  分页
 */
export async function listLowvalueCheckTaskPage(
	params: MatLowValueCheckTaskPageVoRequest
) {
	return ReqUtil.get<PageResVo<MatLowValueCheckTaskPageVo>>(
		"/baseline/lowvalue/checkTask/page",
		{ params }
	)
}

/**
 * 盘点任务 - 指定盘点人员
 * @param data
 */
export async function updateLowvalueCheckTaskAssignUser(
	data: MatLowValueCheckTaskAssignUserDTO
) {
	return ReqUtil.put<null>("/baseline/lowvalue/checkTask/assignUser", {
		data
	})
}

/**
 * 盘点任务 - 任务下发
 * @param data
 */
export async function issueCheckTask(id: number, idempotentToken?: string) {
	return ReqUtil.put<null>("/baseline/lowvalue/checkTask/issue", {
		data: { id },
		headers: { "idempotent-token": idempotentToken }
	})
}

/**
 * 盘点任务 - 开始盘点
 * @param data
 */
export async function startCheckTask(id: number, idempotentToken?: string) {
	return ReqUtil.put<null>("/baseline/lowvalue/checkTask/startTask", {
		data: { id },
		headers: { "idempotent-token": idempotentToken }
	})
}

/**
 * 盘点任务 - 结束盘点
 * @param data
 */
export async function endCheckTask(id: number, idempotentToken?: string) {
	return ReqUtil.put<null>("/baseline/lowvalue/checkTask/endTask", {
		data: { id },
		headers: { "idempotent-token": idempotentToken }
	})
}

/**
 * 盘点任务 获取详情
 * @param id
 */
export async function listLowvalueCheckTaskDetail(id: any) {
	return ReqUtil.get<MatLowValueCheckTaskDetailVo>(
		"/baseline/lowvalue/checkTask/getById",
		{ params: { id } }
	)
}

/**
 * 低值 盘点任务 盘点结果 - 分页
 * @param params {taskId, ... 分页相关}
 */
export async function listLowvalueCheckMaterialPaged(
	params: Record<string, any>
) {
	return ReqUtil.get<PageResVo<MatLowValueCheckMaterialPageVo>>(
		"/baseline/lowvalue/checkMaterial/page",
		{ params }
	)
}

/**
 * 低值 盘点任务 盘点结果 物资详情
 * @param params {taskId, ... 分页相关}
 */
export async function listLowvalueCheckMaterialById(id: number) {
	return ReqUtil.get<PageResVo<MatLowValueCheckMaterialDetailVo>>(
		"/baseline/lowvalue/checkMaterial/getById",
		{ params: { id } }
	)
}

/**
 * 低值 盘点任务 盘点结果 盘点明细 - 分页
 * @param params {id盘点物资Id, ... 分页相关}
 */
export async function listLowvaluecheckAllocationPaged(
	params: Record<string, any>
) {
	return ReqUtil.get<PageResVo<MatLowValueCheckAllocationPageVo>>(
		"/baseline/lowvalue/checkAllocation/page",
		{ params }
	)
}

/**
 * 低值 盘点任务 盘点结果 盘点明细 - 不分页
 * @param params {id盘点物资Id, ... 分页相关}
 */
export async function listLowvaluecheckAllocationList(materialId: number) {
	return ReqUtil.get<MatLowValueCheckAllocationPageVo[]>(
		"/baseline/lowvalue/checkAllocation/list",
		{ params: { materialId, sidx: "createdDate", sord: "desc" } }
	)
}

/**
 * 低值 盘点任务 盘点结果 盘点明细 - 编辑
 * @param data
 */
export async function updateLowvalueCheckAllocation(data: Record<string, any>) {
	return ReqUtil.put<null>("/baseline/lowvalue/checkAllocation/batchUpdate", {
		data
	})
}
