/**
 * @description 低值 - 领用申请 Api
 */

import { PageResVo } from "../../utils/types/common"
import ReqUtil from "../../utils/request-util"
import {
	AddApplyItemSelectDTO,
	EditApplyItemUseLowNumDTO,
	LowValueApplyBpmStatusVo,
	MatCanUseApplyItemVO,
	MatLowValueAllocationDTO,
	MatLowValueAllocationEditDTO,
	MatLowValueAllocationVO,
	MatLowValueAllocationVORequest,
	MatLowValueApplyDTO,
	MatLowValueApplyItemVO,
	MatLowValueApplyItemVORequest,
	MatLowValueApplyVO,
	MatLowValueApplyVORequest,
	MatCanUseApplyItemSelectRequest,
	MatLowValueAllocationLogDateVO,
	MatLowValueAllocationLogVO
} from "../../utils/types/lowValue-requisition-apply"

/**
 * 低值-归还/交旧申请：选择领用单号
 * code?: string;label?: string;createBy?: string;
 */
export async function listSelectLowValueUseApplyPage(
	params: Record<string, any>
) {
	return ReqUtil.get<PageResVo<MatLowValueApplyVO>>(
		"/baseline/lowvalue/useApply/pageCanUseApply",
		{ params }
	)
}

/**
 * 归还/交旧申请 - 添加物资(分页)
 * @param params
 * @returns
 */
export async function listSelectLowValueUseApplyItemPage(
	params: MatCanUseApplyItemSelectRequest
) {
	return ReqUtil.get<PageResVo<MatCanUseApplyItemVO>>(
		"/baseline/lowvalue/useApply/pageCanUseApplyItem",
		{ params }
	)
}

/**
 * 交旧申请低值领用单分页  分页
 */
export async function listLowValueUseApplyPageWasteOld(
	params: MatLowValueApplyVORequest
) {
	return ReqUtil.get<PageResVo<MatLowValueApplyVO>>(
		"/baseline/lowvalue/useApply/pageWasteOld",
		{ params }
	)
}

/**
 * 领用申请-审核状态数量
 */
export async function getLowValueApplyStatusCnt(
	params: MatLowValueApplyVORequest
) {
	return ReqUtil.get<LowValueApplyBpmStatusVo>(
		"/baseline/lowvalue/useApply/bmpStatusStatistics",
		{ params }
	)
}

/**
 * 领用申请 主列表  分页
 */
export async function listLowValueUseApplyPage(
	params: MatLowValueApplyVORequest
) {
	return ReqUtil.get<PageResVo<MatLowValueApplyVO>>(
		"/baseline/lowvalue/useApply/page",
		{ params }
	)
}

/**
 * 领用申请 添加 -保存草稿
 * @param data
 */
export async function addUseApply(
	data: MatLowValueApplyDTO,
	idempotentToken?: string
) {
	return ReqUtil.post<MatLowValueApplyVO>("/baseline/lowvalue/useApply/add", {
		data,
		headers: { "idempotent-token": idempotentToken }
	})
}

/**
 * 领用申请 编辑 - 草稿
 * @param data
 */
export async function updateUseApply(
	data: MatLowValueApplyDTO,
	_idempotentToken?: string
) {
	return ReqUtil.put<MatLowValueApplyVO>("/baseline/lowvalue/useApply/edit", {
		data
	})
}

/**
 * 领用申请 获取详情
 * @param id
 */
export async function listUseApplyDetail(id: any) {
	return ReqUtil.get<MatLowValueApplyVO>(
		"/baseline/lowvalue/useApply/getApply",
		{ params: { id } }
	)
}

/**
 * 领用申请 删除
 * @param id
 */
export async function delUseApplyApply(id: any) {
	return ReqUtil.delete<any>("/baseline/lowvalue/useApply/del", {
		params: { id }
	})
}

/**
 * 领用申请-扩展信息-领用物资(分页)
 * {id: 领用单id}
 */
export async function listUseApplyPageItem(params: Record<string, any>) {
	return ReqUtil.get<PageResVo<MatLowValueApplyItemVO>>(
		"/baseline/lowvalue/useApply/pageApplyItem",
		{ params }
	)
}

/**
 *领用申请-扩展信息-领用物资-添加物资(分页)
 * @param params
 * @returns
 */
export async function listApplyItemSelect(
	params: MatLowValueApplyItemVORequest
) {
	return ReqUtil.get<PageResVo<MatLowValueApplyItemVO>>(
		"/baseline/lowvalue/useApply/pageApplyItemSelect",
		{ params }
	)
}

/**
 * 领用申请-扩展信息-领用物资-添加物资-保存
 * @param data
 * @returns
 */
export async function addApplyItemSelect(
	data: AddApplyItemSelectDTO,
	idempotentToken?: string
) {
	return ReqUtil.post<boolean | null>(
		"/baseline/lowvalue/useApply/addApplyItemSelect",
		{
			data,
			headers: { "idempotent-token": idempotentToken }
		}
	)
}

/**
 *领用申请-扩展信息-领用物资-(本次领用-编辑)
 * @param data
 * @returns
 */
export async function updateApplyItemUseLowNum(
	data: EditApplyItemUseLowNumDTO
) {
	return ReqUtil.post<boolean | null>(
		"/baseline/lowvalue/useApply/editApplyItemUseLowNum",
		{
			data
		}
	)
}

/**
 * 领用申请-扩展信息-领用物资-移除
 * @param applyItemId
 */
export async function delApplyItem(idList: number[]) {
	return ReqUtil.delete<any>("/baseline/lowvalue/useApply/delApplyItem", {
		data: { idList }
	})
}

/**
 * 领用申请-分配-扩展信息-领用物资-使用分配(列表)
 * {applyItemId:itemId}
 */
export async function listUseAllocation(params: Record<string, any>) {
	return ReqUtil.get<PageResVo<MatLowValueAllocationVO>>(
		"/baseline/lowvalue/allocation/pageUseAllocation",
		{ params }
	)
}

/**
 * 领用申请-分配-扩展信息-领用物资-使用分配-添加使用人(列表)
 * {applyItemId:itemId}
 */
export async function listOrgUserSelect(
	params: MatLowValueAllocationVORequest
) {
	return ReqUtil.get<PageResVo<MatLowValueAllocationVO>>(
		"/baseline/lowvalue/allocation/pageOrgUserSelect",
		{ params }
	)
}

/**
 * 领用申请-分配-扩展信息-领用物资-使用分配-添加使用人-保存
 * @param data
 * @returns
 */
export async function addOrgUserSelect(
	data: MatLowValueAllocationDTO,
	idempotentToken?: string
) {
	return ReqUtil.post<boolean | null>(
		"/baseline/lowvalue/allocation/addOrgUserSelect",
		{
			data,
			headers: { "idempotent-token": idempotentToken }
		}
	)
}

/**
 * 领用申请 --- 物资明细 --- 分配 --- 校验是否分配
 * @param data
 * @returns
 */
export async function checkUseAllocation(data: MatLowValueAllocationEditDTO[]) {
	return ReqUtil.post<string | null>(
		"/baseline/lowvalue/allocation/checkUseAllocation",
		{
			data
		}
	)
}

/**
 * 领用申请-分配-扩展信息-领用物资-使用分配-保存
 * @param data
 * @returns
 */
export async function addUseAllocation(data: MatLowValueAllocationEditDTO[]) {
	return ReqUtil.post<boolean | null>(
		"/baseline/lowvalue/allocation/addUseAllocation",
		{
			data
		}
	)
}

/**
 * 领用申请 删除
 * @param id
 */
export async function delUseAllocation(allocationId: any) {
	return ReqUtil.delete<any>("/baseline/lowvalue/allocation/delUseAllocation", {
		params: { allocationId }
	})
}

/**
 * 领用申请-提交审核
 * @param data
 * @returns
 */
export async function publishUseApply(id: number, idempotentToken?: string) {
	return ReqUtil.post<any>("/baseline/lowvalue/useApply/publish", {
		params: { id },
		headers: { "idempotent-token": idempotentToken }
	})
}

/**
 * 领用分配记录分配查询
 */
export async function getListAllocation(applyItemId: number) {
	return ReqUtil.get<MatLowValueAllocationLogDateVO[]>(
		"/baseline/lowvalue/allocation/listAllocation",
		{ params: { applyItemId } }
	)
}

/**
 * 领用分配记录分配明细查询
 */
export async function getListAllocationDetail(params: Record<string, any>) {
	return ReqUtil.get<MatLowValueAllocationLogVO[]>(
		"/baseline/lowvalue/allocation/listAllocationDetail",
		{ params }
	)
}
