/**
 * @description 低值 - 盘点管理/盘点报告 Api
 */

import { PageResVo } from "../../utils/types/common"
import ReqUtil from "../../utils/request-util"
import {
	MatLowValueCheckReportDTO,
	MatLowValueCheckReportPageVo,
	MatLowValueCheckReportVo
} from "../../utils/types/lowValue-inventory-manage"

/**
 * 盘点报告 分页
 * @param id
 */
export async function listLowvalueCheckReportPaged(
	params: Record<string, any>
) {
	return ReqUtil.get<PageResVo<MatLowValueCheckReportPageVo>>(
		"/baseline/lowvalue/checkReport/page",
		{ params }
	)
}

/**
 * 盘点报告 获取详情
 * @param id
 */
export async function listLowvalueCheckReportDetail(id: any) {
	return ReqUtil.get<MatLowValueCheckReportVo>(
		"/baseline/lowvalue/checkReport/getById",
		{ params: { id } }
	)
}

/**
 * 盘点报告 添加
 * @param id
 */
export async function addLowvalueCheckReport(
	data: MatLowValueCheckReportDTO,
	idempotentToken?: string
) {
	return ReqUtil.post<MatLowValueCheckReportVo>(
		"/baseline/lowvalue/checkReport/add",
		{ data, headers: { "idempotent-token": idempotentToken } }
	)
}

/**
 * 盘点报告 编辑
 * @param id
 */
export async function updateLowvalueCheckReport(
	data: MatLowValueCheckReportDTO
) {
	return ReqUtil.put<MatLowValueCheckReportVo>(
		"/baseline/lowvalue/checkReport/update",
		{ data }
	)
}

/**
 * 盘点报告 提交
 * @param id
 */
export async function submitLowvalueCheckReport(
	id: number,
	idempotentToken?: string
) {
	return ReqUtil.put<MatLowValueCheckReportVo>(
		"/baseline/lowvalue/checkReport/submit",
		{ params: { id }, headers: { "idempotent-token": idempotentToken } }
	)
}
