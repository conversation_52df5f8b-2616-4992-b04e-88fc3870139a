/**
 * @description 低值 - 低值台账
 */

import { PageResVo } from "../../utils/types/common"
import ReqUtil from "../../utils/request-util"
import {
	MatLowValueAllocationVO,
	MatLowValueBookDetailVO,
	MatLowValueBookDetailVORequest,
	MatLowValueBookVO,
	MatLowValueBookVORequest
} from "../../utils/types/lowValue-ledger"
import { MatStoreFreezeVo } from "../../utils/types/store-inventory"

/**
 * 低值台账 主列表  分页
 */
export async function listMatLowValueBookPage(
	params: MatLowValueBookVORequest
) {
	return ReqUtil.get<PageResVo<MatLowValueBookVO>>(
		"/baseline/lowvalue/matLowValueBook/page",
		{ params }
	)
}

/**
 * 低值台账记录分页查询（除分配记录外）
 */
export async function listMatLowValueBookPageDetail(
	params: MatLowValueBookDetailVORequest
) {
	return ReqUtil.get<PageResVo<MatLowValueBookDetailVO>>(
		"/baseline/lowvalue/matLowValueBook/pageDetail",
		{ params }
	)
}

/**
 * 低值台账记录分页查询（分配记录）
 */
export async function listMatLowValueBookPageDetailAllocation(
	params: Omit<MatLowValueBookDetailVORequest, "sourceTypes">
) {
	return ReqUtil.get<PageResVo<MatLowValueAllocationVO>>(
		"/baseline/lowvalue/matLowValueBook/pageDetailAllocation",
		{ params }
	)
}

/**
 * 低值物资冻结信息查询
 */
export async function listMatLowValueBookFreezePaged(params: any) {
	return ReqUtil.get<PageResVo<MatStoreFreezeVo>>(
		"/baseline/lowvalue/matLowValueBook/listFreeze",
		{ params }
	)
}
