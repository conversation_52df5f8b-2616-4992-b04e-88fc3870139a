/**
 * @description 低值 - 归还申请 Api
 */

import { PageResVo } from "../../utils/types/common"
import ReqUtil from "../../utils/request-util"
import {
	AddReturnApplyItemSelectDTO,
	EditReturnApplyItemDTO,
	MatLowValueReturnApplyDTO,
	MatLowValueReturnApplyItemVO,
	MatLowValueReturnApplyVO,
	MatLowValueReturnApplyVORequest
} from "../../utils/types/lowValue-return-apply"
import { LowValueApplyBpmStatusVo } from "../../utils/types/lowValue-requisition-apply"

/**
 * 归还申请-审核状态数量
 */
export async function getLowValueReturnApplyStatusCnt(
	params: MatLowValueReturnApplyVORequest
) {
	return ReqUtil.get<LowValueApplyBpmStatusVo>(
		"/baseline/lowvalue/returnApply/bmpStatusStatistics",
		{ params }
	)
}

/**
 * 归还申请 主列表  分页
 */
export async function listLowValueReturnApplyPage(
	params: MatLowValueReturnApplyVORequest
) {
	return ReqUtil.get<PageResVo<MatLowValueReturnApplyVO>>(
		"/baseline/lowvalue/returnApply/page",
		{ params }
	)
}

/**
 * 归还申请 添加 -保存草稿
 * @param data
 */
export async function addReturnApply(
	data: MatLowValueReturnApplyDTO,
	idempotentToken?: string
) {
	return ReqUtil.post<MatLowValueReturnApplyVO>(
		"/baseline/lowvalue/returnApply/add",
		{
			data,
			headers: { "idempotent-token": idempotentToken }
		}
	)
}

/**
 * 归还申请 编辑 草稿
 * @param data
 */
export async function updateReturnApply(
	data: MatLowValueReturnApplyDTO,
	_idempotentToken?: string
) {
	return ReqUtil.put<MatLowValueReturnApplyVO>(
		"/baseline/lowvalue/returnApply/edit",
		{
			data
		}
	)
}

/**
 * 归还申请 获取详情
 * @param id
 */
export async function listReturnApplyDetail(id: any) {
	return ReqUtil.get<MatLowValueReturnApplyVO>(
		"/baseline/lowvalue/returnApply/getApply",
		{ params: { id } }
	)
}

/**
 * 归还申请 删除
 * @param id
 */
export async function delReturnApplyApply(id: any) {
	return ReqUtil.delete<any>("/baseline/lowvalue/returnApply/del", {
		params: { id }
	})
}

/**
 * 归还申请-扩展信息-归还物资(分页)
 * @param params
 * @returns
 */
export async function listReturnApplyPageItem(params: Record<string, any>) {
	return ReqUtil.get<PageResVo<MatLowValueReturnApplyItemVO>>(
		"/baseline/lowvalue/returnApply/pageApplyItem",
		{ params }
	)
}

/**
 * 归还申请-扩展信息-归还物资-添加物资-保存
 * @param data
 * @returns
 */
export async function addReturnApplyItemSelect(
	data: {
		applyId: number
		itemList: AddReturnApplyItemSelectDTO[]
	},
	idempotentToken?: string
) {
	return ReqUtil.post<boolean | null>(
		"/baseline/lowvalue/returnApply/addApplyItem",
		{
			data,
			headers: { "idempotent-token": idempotentToken }
		}
	)
}

/**
 * 归还申请-扩展信息-归还物资-(本次归还数量+归还仓库-编辑)
 * @param data
 * @returns
 */
export async function updateReturnApplyItemSelect(
	data: EditReturnApplyItemDTO
) {
	return ReqUtil.post<boolean | null>(
		"/baseline/lowvalue/returnApply/editApplyItem",
		{
			data
		}
	)
}

/**
 * 归还申请-扩展信息-归还物资-移除
 * @param applyItemId
 */
export async function delReturnApplyItem(data: number[]) {
	return ReqUtil.delete<any>("/baseline/lowvalue/returnApply/delApplyItem", {
		data
	})
}

/**
 * 领用申请-提交审核
 * @param data
 * @returns
 */
export async function publishReturnApply(id: number, idempotentToken?: string) {
	return ReqUtil.post<any>("/baseline/lowvalue/returnApply/publish", {
		params: { id },
		headers: { "idempotent-token": idempotentToken }
	})
}
