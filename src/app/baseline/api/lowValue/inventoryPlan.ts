/**
 * @description 低值 - 盘点管理/盘点计划 Api
 */

import { PageResVo } from "../../utils/types/common"
import ReqUtil from "../../utils/request-util"
import {
	MatLowValueCheckPlanAddDTO,
	MatLowValueCheckPlanDetailVo,
	MatLowValueCheckPlanPageVo,
	MatLowValueCheckPlanPageVoRequest,
	MatLowValueCheckTaskBatchAddDTO,
	MatLowValueCheckTaskOrgPageVo,
	MatLowValueCheckTaskUpdateChargeUserDTO
} from "../../utils/types/lowValue-inventory-manage"
import { MatLowValueApplyVO } from "../../utils/types/lowValue-requisition-apply"

/**
 * 获取状态统计
 * @param params
 * @returns
 */
export async function getLowvalueCheckPlanBpmStatusCnt(
	params: MatLowValueCheckPlanPageVoRequest
) {
	return ReqUtil.get<number[]>(
		"/baseline/lowvalue/checkPlan/statusStatusStatistics",
		{
			params
		}
	)
}

/**
 * 盘点计划 主列表  分页
 */
export async function listLowvalueCheckPlanPage(
	params: MatLowValueCheckPlanPageVoRequest
) {
	return ReqUtil.get<PageResVo<MatLowValueCheckPlanPageVo>>(
		"/baseline/lowvalue/checkPlan/page",
		{ params }
	)
}

/**
 * 盘点计划 获取详情
 * @param id
 */
export async function listLowvalueCheckPlanDetail(planId: any) {
	return ReqUtil.get<MatLowValueCheckPlanDetailVo>(
		"/baseline/lowvalue/checkPlan/getDetail",
		{ params: { planId } }
	)
}

/**
 * 盘点计划 添加
 * @param data
 */
export async function addLowvalueCheckPlan(
	data: MatLowValueCheckPlanAddDTO,
	idempotentToken?: string
) {
	return ReqUtil.post<MatLowValueCheckPlanDetailVo>(
		"/baseline/lowvalue/checkPlan/add",
		{
			data,
			headers: { "idempotent-token": idempotentToken }
		}
	)
}

/**
 * 盘点计划 编辑
 * @param data
 */
export async function updateLowvalueCheckPlan(
	data: MatLowValueCheckPlanAddDTO,
	_idempotentToken?: string
) {
	return ReqUtil.put<MatLowValueCheckPlanDetailVo>(
		"/baseline/lowvalue/checkPlan/edit",
		{
			data
		}
	)
}

/**
 * 盘点计划 删除
 * @param id
 */
export async function delLowvalueCheckPlan(planId: any) {
	return ReqUtil.delete<any>("/baseline/lowvalue/checkPlan/delete", {
		data: { planId }
	})
}

/**
 * 查询当前用户组织架构
 * @param selectedIds:number[]
 * @returns
 */
export async function listOrgTree(params: { selectedIds?: number[] }) {
	return ReqUtil.get<any>("/baseline/system/org/listTree", {
		params
	})
}

/**
 * 添加部门 前 获取计划已选择部门Ids
 * @param planId
 * @returns
 */
export async function listSysOrgId(planId: number) {
	return ReqUtil.get<number[] | null>(
		"/baseline/lowvalue/checkPlan/listSysOrgId",
		{
			params: { planId }
		}
	)
}

/**
 * 盘点计划 - 盘点部门 分页
 * @param params planId及分页相关信息
 * @returns
 */
export async function listLowvalueCheckPlanOrgPaged(
	params: Record<string, any>
) {
	return ReqUtil.get<PageResVo<MatLowValueCheckTaskOrgPageVo>>(
		"/baseline/lowvalue/checkTask/pageOrg",
		{ params }
	)
}

/**
 * 盘点计划 - 盘点部门 - 批量添加
 * @param data
 * @returns
 */
export async function batchAddLowvalueCheckPlanOrgIds(
	data: MatLowValueCheckTaskBatchAddDTO,
	idempotentToken?: string
) {
	return ReqUtil.post<any>("/baseline/lowvalue/checkTask/batchAdd", {
		data,
		headers: { "idempotent-token": idempotentToken }
	})
}

/**
 * 盘点计划 - 盘点部门 - 更新盘点负责人
 * @param data
 * @returns
 */
export async function updateLowvalueCheckPlanOrgUsername(
	data: MatLowValueCheckTaskUpdateChargeUserDTO
) {
	return ReqUtil.put<null>("/baseline/lowvalue/checkTask/updateChargeUser", {
		data
	})
}

/**
 * 盘点计划 - 盘点部门 - 删除
 * @param ids
 * @returns
 */
export async function delLowvalueCheckPlanOrgId(ids: number[]) {
	return ReqUtil.delete<any>("/baseline/lowvalue/checkTask/batchDelete", {
		data: { ids }
	})
}

/**
 * 盘点计划 - 领用单 分页
 * @param params
 * @returns
 */
export async function listLowvalueCheckPlanUseApplyPaged(
	params: Record<string, any>
) {
	return ReqUtil.get<PageResVo<MatLowValueApplyVO>>(
		"/baseline/lowvalue/checkTask/pageApply",
		{ params }
	)
}

/**
 * 盘点计划 审批
 * @param data
 */
export async function submitCheckPlan(
	planId: number,
	idempotentToken?: string
) {
	return ReqUtil.post<any>("/baseline/lowvalue/checkPlan/publish", {
		params: { planId },
		headers: { "idempotent-token": idempotentToken }
	})
}
