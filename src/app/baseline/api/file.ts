import { request } from "@/app/platform/utils/service"

// 获取列表
function getAllFileList(query: any) {
	return request<SystemApiResponseData>({
		url: "/pitaya/system/common/attachment/list",
		method: "get",
		params: query
	})
}
function getFileList(query: any) {
	return request<SystemApiResponseData>({
		url: "/pitaya/system/common/attachment/pageList",
		method: "get",
		params: query
	})
}
function getFileListByBusinessIds(query: any) {
	return request<SystemApiResponseData>({
		url: "/pitaya/system/common//attachment/listByBusinessIdsAndbusinessType",
		method: "get",
		params: query
	})
}


// 移除
function deleteFile(data: any) {
	return request<SystemApiResponseData>({
		url: "/pitaya/system/common/attachment/delete",
		method: "post",
		data
	})
}

function getFile(filePath: any) {
	return request({
		url: "/pitaya/system/common/static/" + filePath,
		method: "get",
		responseType: "blob"
	})
}
const downloadFile = (filePath: string) => {
	const url = buildPreviewUrl() + filePath
	const link = document.createElement("a")
	link.href = url
	link.download = url.split("/").pop() || ""
	document.body.appendChild(link)
	link.click()
	document.body.removeChild(link)
}
function buildPreviewUrl() {
	const baseApi = import.meta.env.VITE_BASE_API
	const prefix = baseApi.endsWith("/") ? baseApi : `${baseApi}/`
	const previewUrl = prefix + "pitaya/system/common/static/"
	return previewUrl
}
export const FileApi = {
	getAllFileList,
	getFileList,
	deleteFile,
	downloadFile,
	buildPreviewUrl,
	getFileListByBusinessIds
}
