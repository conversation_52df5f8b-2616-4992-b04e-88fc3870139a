/**
 * 相似物资阈值配置 状态
 * 启用 1；草稿0 2:停用
 */
export enum IMaterialThresholdStatus {
	drafted = "0", //草稿
	started = "1", //启用
	close = "2" //关闭
}

export function getMaterialThresholdStatus() {
	return [
		{
			label: "草稿",
			value: IMaterialThresholdStatus.drafted,
			raw: { class: "warning" }
		},
		{
			label: "已启用",
			value: IMaterialThresholdStatus.started,
			raw: { class: "success" }
		},
		{
			label: "已关闭",
			value: IMaterialThresholdStatus.close,
			raw: { class: "danger" }
		}
	]
}
