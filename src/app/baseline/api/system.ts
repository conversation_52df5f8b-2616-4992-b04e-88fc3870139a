import XEUtils from "xe-utils"
import { ProfessionApi } from "@/app/platform/api/system/profession"
import { DepartmentApi } from "@/app/platform/api/system/department"
import { LineApi } from "@/app/platform/api/system/line"
import { appStatus, DictApi } from "@/app/baseline/api/dict"
import { baseUserApi } from "@/app/platform/api/system/baseUser"
import { useUserStore } from "@/app/platform/store/modules/user"
import { PurchaseSupplierApi } from "@/app/baseline/api/purchase/purchaseSupplier"
import {
	IInventoryUserSelectorParams,
	MatStoreJobOrderDetailVo,
	MatStoreJobOrderDTO,
	MatStoreJobOrderGoodsDTO,
	MatStoreJobOrderVo,
	SystemUserDTO,
	SystemUserVo
} from "../utils/types/system"
import { PageResVo } from "../utils/types/common"
import { filter, forEach, map } from "lodash-es"
import ReqUtil from "../utils/request-util"
import { getSystemCostCategoryTree } from "./system-cost-category"
import { platformApi } from "@/app/platform/api/platform"
import { IDepotStatus } from "../utils/types/system-depot"

const userStore = useUserStore()
const { userInfo } = storeToRefs(userStore)

/**
 * 接口幂等性能 - 获取Token (废弃)
 * @param params
 * @returns
 */
export function getBaselineToken(data?: any) {
	return request<any>({
		url: "/baseline/system/token/getToken",
		method: "post",
		data: data
	})
}

export function getTaskByBusinessIds(data: any) {
	return request<SystemApiResponseData>({
		url: "pitaya/system/procTask/getTaskByBusinessIds",
		method: "post",
		data: data
	})
}

/**
 * 获取线路列表
 * @param params
 * @returns
 */
export function getSystemLineList(params: any) {
	return request<ApiResponseData<Record<string, any>>>({
		url: "/baseline/system/line/list",
		method: "get",
		params
	})
}

/**
 * 获取段区
 */
export function getSystemDepotList(params?: any) {
	return request<ApiResponseData<Record<string, any>>>({
		url: "/baseline/system/depot/list",
		method: "get",
		params
	})
}

/**
 * 获取成本中心
 */
export function getSystemCostCenterList(params?: any) {
	return request<ApiResponseData<Record<string, any>>>({
		url: "/baseline/system/costCenter/list",
		method: "get",
		params
	})
}

/**
 * 公司列表
 */
export const listCompanyWithFormat = async () => {
	const r = await baseUserApi.getAllCompany()
	return map(r, (v: any) => ({
		...v,
		label: v.companyName,
		value: v.id
	}))
}

/**
 * 查询质检人角色用户列表
 */
export function listInspectorPaged(params: SystemUserDTO = {}) {
	return ReqUtil.get<PageResVo<SystemUserVo>>(
		"/baseline/system/user/inspectPage",
		{ params }
	)
}

/**
 * 查询人员分页列表
 */
export function listSysUserPaged(params: SystemUserDTO = {}) {
	return request<PageResVo<SystemUserVo>>({
		url: "/baseline/system/user/page",
		method: "get",
		params
	})
}

/**
 * 查询盘点任务人员分页列表
 */
export function listInventoryJobUserPaged(
	params: IInventoryUserSelectorParams = {}
) {
	return request<SystemUserVo[]>({
		url: "/baseline/system/user/storeCheckPage",
		method: "get",
		params
	})
}

/**
 * 查询人员分页列表
 */
export function listSysUser(params: SystemUserDTO = {}) {
	return request<SystemUserVo[]>({
		url: "/baseline/system/user/listAll",
		method: "post",
		data: params
	})
}

/**
 * 查询工单分页数据
 */
export function listJobOrderPaged(params: MatStoreJobOrderDTO = {}) {
	return request<PageResVo<MatStoreJobOrderVo>>({
		url: "/baseline/system/jobOrder/page",
		method: "get",
		params
	})
}

/**
 * 查询工单不分页数据
 */
export function listJobOrder(params: MatStoreJobOrderDTO = {}) {
	return request<MatStoreJobOrderVo[]>({
		url: "/baseline/system/jobOrder/listAll",
		method: "post",
		data: params
	})
}

/**
 * 查询工单明细分页数据
 */
export function listJobOrderGoodsPaged(params: MatStoreJobOrderGoodsDTO = {}) {
	return request<PageResVo<MatStoreJobOrderDetailVo>>({
		url: "/baseline/system/jobOrder/pageDetail",
		method: "get",
		params
	})
}

/**
 * 附件上传 更改fileName
 * /pitaya/system/common/attachment/update
 * {"businessId":341,"versionControlId":1780,"fileIds":[1109],"customFileName":"test"}
 */
export function attachmentUpdate(data: Record<string, any>) {
	return request<Record<string, any>>({
		url: "/pitaya/system/common/attachment/update",
		method: "post",
		data
	})
}

/**
 * 获取所属专业
 * @param companyId
 */
function getProfessionTree(companyId?: string): Promise<any> {
	return new Promise((resolve) => {
		let curCompanyId: any = ""
		if (userInfo.value.companyType === 1) {
			// 集团
			curCompanyId = "" //userInfo.value.companyCode
		} else {
			// 公司
			curCompanyId = companyId || userInfo.value.companyCode
		}

		ProfessionApi.getProfessionTreeV2(curCompanyId)
			.then((_r) => {
				XEUtils.eachTree(_r, (item: any) => {
					item.value = item.id
					item.allName = item.composeName
				})
				resolve(_r)
			})
			.catch(() => {
				resolve([])
			})
	})
}

/**
 * 获取部门列表树（公司禁止选择）
 * @param companyId
 */
function getCostCategoryTree(params?: Record<string, any>): Promise<any> {
	return new Promise((resolve) => {
		getSystemCostCategoryTree(params)
			.then((_r) => {
				XEUtils.eachTree(_r, (item: any) => {
					item.disabled = item.isCompany
					item.value = item.id
					item.name = item.label
					item.allName = item.label
				})
				resolve(_r)
			})
			.catch(() => {
				resolve([])
			})
	})
}

/**
 * 获取费用科目
 */
function getCostSubject(): Promise<any> {
	return new Promise((resolve) => {
		DictApi.getDictByCode("COST_CATEGORY")
			.then((_r) => {
				XEUtils.eachTree(_r, (item: any) => {
					item.value = item.subitemValue
					item.label = item.subitemName
					item.allName = item.subitemName
					item.name = item.subitemName
				})
				resolve(_r)
			})
			.catch(() => {
				resolve([])
			})
	})
}

/**
 * 获取公司列表
 * @param companyId
 */
function getCompanyAllList(): Promise<any> {
	return new Promise((resolve) => {
		//CompanyApi.getCompanyAllList(data)
		baseUserApi
			.getAllCompany()
			.then((_r) => {
				XEUtils.eachTree(_r, (item: any) => {
					item.value = item.id
					item.label = item.companyName
					item.allName = item.companyName
					item.name = item.companyName
				})
				resolve(_r)
			})
			.catch(() => {
				resolve([])
			})
	})
}

/**
 * 获取部门列表树
 * @param companyId
 */
function getDepartmentTree(companyId: any): Promise<any> {
	return new Promise((resolve) => {
		DepartmentApi.getDepartmentTreeWithRole({})
			.then((_r: any) => {
				let _tmp = _r
				if (companyId && JSON.stringify(companyId) != "{}") {
					_tmp = []
					_r.forEach((item: any) => {
						if (item.companyId == companyId) {
							_tmp.push(item)
						}
					})
				}
				resolve(_tmp)
			})
			.catch(() => {
				resolve([])
			})
	})
}

/**
 * 获取部门列表树（公司禁止选择）
 * @param companyId
 */
async function getDepartmentTreeWithCompanyDisabled(
	companyId: any
): Promise<any> {
	const r = await DepartmentApi.getDepartmentTreeWithRole({ companyId })
	wrapDeptTreeData(r as any)

	return r
	//return filter(r, (v: any) => v.companyId === companyId)
}

/**
 * 向部门 tree 数据添加 disabled 字段
 */
export function wrapDeptTreeData(list?: any[]) {
	if (list?.length) {
		forEach(list, (v) => {
			v.disabled = v.company
			if (v.children?.length) {
				wrapDeptTreeData(v.children)
			}
		})
	}
}

/**
 * 获取线路
 *
 * el-select options 兼容处理
 * @param data
 */
async function getLineOptions(): Promise<any> {
	let companyId: any = ""
	if (userInfo.value.companyType === 1) {
		// 集团
		companyId = "" //userInfo.value.companyCode
	} else {
		// 公司
		companyId = userInfo.value.companyCode
	}

	const res = await getSystemLineList({ companyId })
	return map(res, (v: any) => ({ ...v, label: v.name, value: v.id }))
}

/**
 * 获取线路
 * @param data
 */
function getLineList(): Promise<any> {
	let companyId: any = ""
	if (userInfo.value.companyType === 1) {
		// 集团
		companyId = "" //userInfo.value.companyCode
	} else {
		// 公司
		companyId = userInfo.value.companyCode
	}

	return new Promise((resolve) => {
		getSystemLineList({ companyId })
			.then((_r) => {
				XEUtils.eachTree(_r, (item: any) => {
					item.allName = item.name
				})
				resolve(_r)
			})
			.catch(() => {
				resolve([])
			})
	})
}

function getSegmentList(): Promise<any> {
	// return Promise.resolve([
	// 	{ id: 1, value: "1", name: "段区1", label: "段区1", allName: "段区1" },
	// 	{ id: 2, value: "2", name: "段区2", label: "段区2", allName: "段区2" },
	// 	{ id: 3, value: "3", name: "段区3", label: "段区3", allName: "段区3" },
	// 	{ id: 4, value: "4", name: "段区4", label: "段区4", allName: "段区4" }
	// ])
	return new Promise((resolve) => {
		DictApi.getDictByCode("DEPOT")
			.then((_r) => {
				XEUtils.eachTree(_r, (item: any) => {
					item.value = item.subitemValue
					item.label = item.subitemName
					item.allName = item.subitemName
					item.name = item.subitemName
					item.id = item.subitemValue
				})
				resolve(_r)
			})
			.catch(() => {
				resolve([])
			})
	})
}

function getPurchaseSupplierTree(): Promise<any> {
	return new Promise<any>((resolve) => {
		PurchaseSupplierApi.getPurchaseSupplierList({
			currentPage: 0,
			pageSize: 9999,
			bpmStatus: appStatus.approved
		} as any).then((_r) => {
			XEUtils.map(_r.rows, (_d: any) => {
				_d.value = _d.id
				_d.name = _d.label
				_d.allName = _d.label
			})

			resolve(_r.rows)
		})
	})
}

// 根据当前登录人关联的专业查询设备分级树
// 目前用到的是 报表分类
function listSubitemLikeComposeCodes(data: any) {
	return request<SystemApiResponseData>({
		url: `/pitaya/system/treeDictionary/listSubitemLikeComposeCodes`,
		method: "post",
		data
	})
}

/**
 * 获取段区
 */
function getDepotList(): Promise<any> {
	return new Promise((resolve) => {
		getSystemDepotList({ status: IDepotStatus.Started })
			.then((_r) => {
				XEUtils.eachTree(_r, (item: any) => {
					item.allName = item.label
					item.value = item.id
					item.name = item.label
				})
				resolve(_r)
			})
			.catch(() => {
				resolve([])
			})
	})
}

/**
 * 获取成本中心
 */
function getCostCenterList(): Promise<any> {
	return new Promise((resolve) => {
		getSystemCostCenterList({ status: IDepotStatus.Started })
			.then((_r) => {
				XEUtils.eachTree(_r, (item: any) => {
					item.allName = item.label
					item.value = item.id
					item.name = item.label
				})
				resolve(_r)
			})
			.catch(() => {
				resolve([])
			})
	})
}

export const BaseLineSysApi = {
	listSubitemLikeComposeCodes,
	getCostCategoryTree,
	getProfessionTree,
	getCostSubject,
	getCompanyAllList,
	getLineOptions,
	getDepartmentTree,
	getDepartmentTreeWithCompanyDisabled,
	getLineList,
	getSegmentList,
	getPurchaseSupplierTree,
	getDepotList,
	getCostCenterList
}
