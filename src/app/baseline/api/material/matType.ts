import { request } from "@/app/platform/utils/service"
import {
	GetMatTypeListRequest,
	GetMatTypeTreeRequest,
	UpdateStatusBatchRequest,
	AddMatTypeRequest,
	UpdMatTypeRequest
} from "@/app/baseline/api/defines"
import { MaterialTypePropertyUpdateDTO } from "../../utils/types/material"

// 获取分类树
function getMatTypeTree(query: GetMatTypeTreeRequest) {
	return request<SystemApiResponseData>({
		url: "/baseline/material/materialType/tree",
		method: "get",
		params: query
	})
}

// 获取分类树
function getMatTypeChild(query: GetMatTypeTreeRequest) {
	return request<SystemApiResponseData>({
		url: "/baseline/material/materialType/child",
		method: "get",
		params: query
	})
}
// 获取分类下的子分类
function getMatTypeList(query: GetMatTypeListRequest) {
	return request<SystemApiResponseData>({
		url: "/baseline/material/materialType/list",
		method: "get",
		params: query
	})
}
function getInfo(id: string) {
	return request<SystemApiResponseData>({
		url: "/baseline/material/materialType/" + id,
		method: "get"
	})
}
// 新建
function addMatType(data: AddMatTypeRequest, idempotentToken?: string) {
	return request<SystemApiResponseData>({
		url: "/baseline/material/materialType",
		method: "post",
		data,
		headers: { "idempotent-token": idempotentToken }
	})
}

// 更新
function updateMatType(data: UpdMatTypeRequest) {
	return request<SystemApiResponseData>({
		url: "/baseline/material/materialType",
		method: "put",
		data
	})
}
// 移除
function deleteMatType(ids: string) {
	return request<SystemApiResponseData>({
		url: "/baseline/material/materialType/" + ids,
		method: "delete"
	})
}
function updateStatusBatch(data: UpdateStatusBatchRequest) {
	return request<SystemApiResponseData>({
		headers: { "Content-Type": "multipart/form-data" },
		url: "/baseline/material/materialType/updateStatusBatch",
		method: "post",
		data
	})
}
/**
 * 物资分类扩展属性-不分页查询
 */
function getMatTypeProperties(query: any) {
	return request<SystemApiResponseData>({
		url: "/baseline/material/materialTypeProperty/list",
		method: "get",
		params: query
	})
}

/**
 * 物资分类扩展属性-保存
 * @param data
 * @returns
 */
function updateMaterialTypeProperty(data: MaterialTypePropertyUpdateDTO) {
	return request<any>({
		url: "/baseline/material/materialTypeProperty/updateMaterialTypeProperty",
		method: "post",
		data
	})
}
export const MatTypeApi = {
	getMatTypeTree,
	getMatTypeList,
	getInfo,
	addMatType,
	updateMatType,
	deleteMatType,
	updateStatusBatch,
	getMatTypeChild,
	getMatTypeProperties,
	updateMaterialTypeProperty
}
