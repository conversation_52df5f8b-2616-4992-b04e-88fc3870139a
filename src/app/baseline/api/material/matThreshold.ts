/**
 * 物资 - 阈值管理 Api
 */
import ReqUtil from "../../utils/request-util"
import { PageResVo } from "../../utils/types/common"
import {
	MaterialThresholdDTO,
	MaterialThresholdVo
} from "../../utils/types/material-threshold"

/**
 * 阈值管理 列表
 */
export async function getMaterialThresholdPaged(params: any) {
	return ReqUtil.get<PageResVo<MaterialThresholdVo>>(
		"/baseline/material/materialThreshold/list",
		{
			params: {
				...params
			}
		}
	)
}

/**
 * 物资阈值配置-详细信息查询
 * @param id
 * @returns
 */
export async function getMaterialThresholdById(id: number) {
	return ReqUtil.get<MaterialThresholdVo>(
		`/baseline/material/materialThreshold/${id}`
	)
}

/**
 * 物资阈值配置-新增
 * @param data
 * @returns
 */
export async function addMaterialThreshold(
	data: MaterialThresholdDTO,
	idempotentToken?: string
) {
	return ReqUtil.post<MaterialThresholdVo>(
		"/baseline/material/materialThreshold",
		{ data, headers: { "idempotent-token": idempotentToken } }
	)
}

/**
 * 物资阈值配置-修改
 * @param data
 * @returns
 */
export async function updateMaterialThreshold(data: MaterialThresholdDTO) {
	return ReqUtil.put<MaterialThresholdVo>(
		"/baseline/material/materialThreshold",
		{ data }
	)
}

/**
 * 物资阈值配置-删除
 * @param id
 * @returns
 */
export async function delMaterialThreshold(id: number) {
	return ReqUtil.delete<boolean | null>(
		`/baseline/material/materialThreshold/${id}`
	)
}

/**
 * 物资阈值配置-启用
 * @param id
 * @returns
 */
export async function enableMaterialThreshold(id: number) {
	return ReqUtil.post<null>("/baseline/material/materialThreshold/enable", {
		params: { id }
	})
}

/**
 * 物资阈值配置-关闭
 * @param id
 * @returns
 */
export async function closeMaterialThreshold(id: number) {
	return ReqUtil.post<null>("/baseline/material/materialThreshold/close", {
		params: { id }
	})
}
