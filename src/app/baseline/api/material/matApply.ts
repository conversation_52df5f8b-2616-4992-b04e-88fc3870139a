import { request } from "@/app/platform/utils/service"
import {
	addMatProcureRequest,
	getTaskByBusinessIdsRequest,
	MatApplyListRequest,
	MatApplyListUserRequest,
	MaterialCode,
	PublishRequest,
	UpdateMatAttributeRequest,
	UpdateMatStatusRequest,
	updMatProcureRequest
} from "@/app/baseline/api/defines"
import {
	MaterialOperateDTO,
	MessageReceiveUserVo,
	MessageReceiveUserVoRequest,
	SimilarityMaterialCodeVo,
	SimilarityMaterialCodeVoRequest,
	MessageReceiveUserDTO
} from "../../utils/types/material"
import { PageResVo } from "../../utils/types/common"

// 获取列表
function getMatApplyList(query: MatApplyListRequest) {
	return request<SystemApiResponseData>({
		url: "/baseline/material/materialCode/list",
		method: "post",
		data: query
	})
}
function getMatApplyListUser(query: MatApplyListUserRequest) {
	return request<SystemApiResponseData>({
		url: "/baseline/material/materialCode/listUser",
		method: "get",
		params: query
	})
}
function getMatApplyById(id: any) {
	return request<SystemApiResponseData>({
		url: "/baseline/material/materialCode/" + id,
		method: "get"
	})
}
// 新建
function addMatApply(data: MaterialCode, idempotentToken?: string) {
	return request<SystemApiResponseData>({
		url: "/baseline/material/materialCode",
		method: "post",
		data,
		headers: { "idempotent-token": idempotentToken }
	})
}

// 更新
function updateMatApply(data: MaterialCode) {
	return request<SystemApiResponseData>({
		url: "/baseline/material/materialCode",
		method: "put",
		data
	})
}
// 移除
function deleteMatApply(ids: any) {
	return request<SystemApiResponseData>({
		url: "/baseline/material/materialCode/" + ids,
		method: "delete"
	})
}
//提交审核
function publishApply(data: PublishRequest, idempotentToken?: string) {
	return request<SystemApiResponseData>({
		headers: {
			"Content-Type": "multipart/form-data",
			"idempotent-token": idempotentToken
		},
		url: "/baseline/material/materialCode/publish",
		method: "post",
		data
	})
}

// 批量更新物资状态
function updateMatStatusBatch(data: UpdateMatStatusRequest) {
	return request<SystemApiResponseData>({
		headers: { "Content-Type": "multipart/form-data" },
		url: "/baseline/material/materialCode/updateStatusBatch",
		method: "post",
		data
	})
}
// 批量更新物资性质
function updateMatAttributeBatch(data: UpdateMatAttributeRequest) {
	return request<SystemApiResponseData>({
		headers: { "Content-Type": "multipart/form-data" },
		url: "/baseline/material/materialCode/updateAttributeBatch",
		method: "post",
		data
	})
}

// 查询采购信息列表
function getMatProcureInfoList(query: any) {
	return request<any>({
		url: "/baseline/material/materialProcureInfo/materialList",
		method: "get",
		params: query
	})
}
//获取采购信息详细信息
function getMatProcureInfoById(id: any) {
	return request<SystemApiResponseData>({
		url: "/baseline/material/materialProcureInfo/" + id,
		method: "get"
	})
}
//新增采购信息
function addMatProcureInfo(
	data: addMatProcureRequest,
	idempotentToken?: string
) {
	return request<SystemApiResponseData>({
		url: "/baseline/material/materialProcureInfo",
		method: "post",
		data,
		headers: { "idempotent-token": idempotentToken }
	})
}
//修改采购信息
function updateMatProcureInfo(data: updMatProcureRequest) {
	return request<SystemApiResponseData>({
		url: "/baseline/material/materialProcureInfo",
		method: "put",
		data
	})
}
// 删除采购信息
function deleteMatProcureInfo(ids: string) {
	return request<SystemApiResponseData>({
		url: "/baseline/material/materialProcureInfo/" + ids,
		method: "delete"
	})
}
//根据物资编码ID获取物本公司的采购信息详细信息
function getMatProcureInfoByMatId(materialCodeId: string) {
	return request<SystemApiResponseData>({
		url: "/baseline/material/materialProcureInfo/getByMaterialCodeId",
		method: "get",
		params: {
			materialCodeId: materialCodeId
		}
	})
}

//获取审批状态统计
function getBmpStatusStatistics(query: MatApplyListRequest) {
	return request<SystemApiResponseData>({
		url: "/baseline/material/materialCode/bmpStatusStatistics",
		method: "get",
		params: query
	})
}
function getTaskByBusinessIds(data: getTaskByBusinessIdsRequest) {
	return request<SystemApiResponseData>({
		url: "pitaya/system/procTask/getTaskByBusinessIds",
		method: "post",
		data
	})
}
function publishApplyProcureInfo(data: PublishRequest) {
	return request<SystemApiResponseData>({
		url: "/baseline/material/materialProcureInfo/publish",
		method: "post",
		data
	})
}

// 获取不在当前计划中的物资列表
function getMatApplyListV2(query: MatApplyListRequest) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/planNeedItem/pageMaterial",
		method: "post",
		data: query
	})
}

/**
 * 相似物资
 * @param query
 * @returns
 */
function getSimilarityMatPaged(data: SimilarityMaterialCodeVoRequest) {
	return request<PageResVo<SimilarityMaterialCodeVo>>({
		url: "/baseline/material/materialCode/pageSimilarityMaterial",
		method: "post",
		data
	})
}

/**
 * 相似物资物资分类查询
 * @param query
 * @returns
 */
function getSimilarityMatTypePaged(data: SimilarityMaterialCodeVoRequest) {
	return request<PageResVo<SimilarityMaterialCodeVo>>({
		url: "/baseline/material/materialCode/getSimilarityMaterialType",
		method: "post",
		data
	})
}

/**
 * 物资编码-冻结|解冻|作废|更新物资性质提交校验
 * @param query
 * @returns
 */
function matManualCheckOperate(data: MaterialOperateDTO) {
	return request<SystemApiResponseData>({
		url: "/baseline/material/materialOperate/checkOperate",
		method: "post",
		data
	})
}

/**
 * 物资编码-冻结|解冻|作废|更新物资性质提交校验
 * @param query
 * @returns
 */
function matManualPublishOperate(
	data: MaterialOperateDTO,
	idempotentToken?: string
) {
	return request<SystemApiResponseData>({
		url: "/baseline/material/materialOperate/publish",
		method: "post",
		data,
		headers: { "idempotent-token": idempotentToken }
	})
}

/**
 * 编码手册-物资操作记录分页查询
 * @param query
 * @returns
 */
function getMatOperateItemPage(params: any) {
	return request<PageResVo<any>>({
		url: "/baseline/material/materialOperate/pageOperate",
		method: "get",
		params
	})
}

/**
 * 编码手册-物资操作明细不分页查询
 * @param query
 * @returns
 */
function getMatOperateItemList(params: any) {
	return request<any>({
		url: "/baseline/material/materialOperate/listOperateItem",
		method: "get",
		params
	})
}

/**
 * 物资编码-查询物资操作记录详情
 * @param params
 * @returns
 */
function getMatOperateInfo(data: any) {
	return request<PageResVo<any>>({
		url: "/baseline/material/materialOperate/getInfo",
		method: "post",
		data
	})
}

/**
 * 物资编码-查询物资操作记录详情
 * @param params
 * @returns
 */
function saveMatOperateInfo(data: any) {
	return request<PageResVo<any>>({
		url: "/baseline/material/materialOperate/save",
		method: "post",
		data
	})
}

/**
 * 物资编码-批量更新物资材质
 * @param params {auxiliaryQualityAfter?: string,materialIdList: number[]; 物资id集合, qualityAfter: string; 主要材质}
 * @returns
 */
function updateQuality(data: any) {
	return request<any>({
		url: "/baseline/material/materialOperate/updateQuality",
		method: "post",
		data
	})
}

/**
 * 业务消息接收人配置-分页查询
 * @param params
 * @returns
 */
function getMessageReceiveUserPaged(params: MessageReceiveUserVoRequest) {
	return request<PageResVo<MessageReceiveUserVo>>({
		url: "/baseline/system/messageReceiveUser/page",
		method: "get",
		params
	})
}

/**
 * 业务消息接收人-新增
 * @param params
 * @returns
 */
function addMessageReceiveUser(data: MessageReceiveUserDTO) {
	return request<any>({
		url: "/baseline/system/messageReceiveUser/add",
		method: "post",
		data
	})
}

/**
 * 业务消息接收人-删除
 * @param params
 * @returns
 */
function removeMessageReceiveUser(idList: number[]) {
	return request<any>({
		url: "/baseline/system/messageReceiveUser/remove",
		method: "post",
		data: { idList }
	})
}

/**
 * 添加接收人 列表
 * @param params
 * @returns
 */
function getSelectedMessageReceiveUserPaged(
	params: MessageReceiveUserVoRequest
) {
	return request<any>({
		url: "/baseline/system/messageReceiveUser/pageUserSelect",
		method: "get",
		params
	})
}

/**
 * 不分页物资分类属性转换物资编码属性查询
 */
function getMatMaterialProperties(query: any) {
	return request<SystemApiResponseData>({
		url: "/baseline/material/materialCode/listMaterialProperty",
		method: "get",
		params: query
	})
}

/**
 * 下载更新预估采购单价导入模板
 * @param query
 * @returns
 */
function getExcelModel(query: any) {
	return request<any>({
		url: "/baseline/material/materialProcureInfo/download",
		method: "get",
		responseType: "blob",
		params: query
	})
}

/**
 * 导出更新预估采购单价错误信息
 * @param query
 * @returns
 */
function getExcelError(data: any) {
	return request<any>({
		url: "/baseline/material/materialProcureInfo/excelError",
		method: "post",
		headers: { "Content-Type": "multipart/form-data" },
		responseType: "blob",
		data
	})
}

/**
 * 导入更新预估采购单价
 * @param query
 * @returns
 */
function getExcelUpload(data: any) {
	return request<any>({
		url: "/baseline/material/materialProcureInfo/upload",
		method: "post",
		headers: { "Content-Type": "multipart/form-data" },
		data
	})
}
export const MatApplyApi = {
	getMatApplyList,
	getMatApplyListV2,
	getMatApplyById,
	addMatApply,
	updateMatApply,
	deleteMatApply,
	updateMatStatusBatch,
	updateMatAttributeBatch,
	getMatProcureInfoList,
	getMatProcureInfoById,
	addMatProcureInfo,
	updateMatProcureInfo,
	deleteMatProcureInfo,
	getMatProcureInfoByMatId,
	getBmpStatusStatistics,
	publishApply,
	getMatApplyListUser,
	getTaskByBusinessIds,
	publishApplyProcureInfo,
	getSimilarityMatPaged,
	getSimilarityMatTypePaged,
	matManualCheckOperate,
	matManualPublishOperate,
	getMatOperateItemList,
	getMatOperateItemPage,
	getMatOperateInfo,
	saveMatOperateInfo,
	updateQuality,
	getMessageReceiveUserPaged,
	addMessageReceiveUser,
	removeMessageReceiveUser,
	getSelectedMessageReceiveUserPaged,
	getMatMaterialProperties,
	getExcelModel,
	getExcelError,
	getExcelUpload
}
