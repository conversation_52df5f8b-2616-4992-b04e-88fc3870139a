/*
 * @Author: liulianming
 * @Date: 2025-07-12 00:06:51
 * @LastEditors: liulianming
 * @LastEditTime: 2025-07-14 12:15:33
 * @Description:
 */

import ReqUtil from "../utils/request-util"
import { PageResVo } from "../utils/types/common"
import { configurationGisCateVo, configurationGisInformationPagedRequest, configurationGisInformationVo } from "../utils/types/configuration"


/**
 * 配置-GIS信息  分页查询
 */
export function getConfigurationGisInformationPaged(
	params: configurationGisInformationPagedRequest = {}
) {
	return ReqUtil.get<PageResVo<configurationGisInformationVo>>(
		"/baseline/system/surveyingMappingObject/page",
		{ params }
	)
}
/**
 * 配置-GIS信息  分页查询
 */
export function getConfigurationGisInformationCate(

) {
	return ReqUtil.get<configurationGisCateVo[]>(
		"/baseline/system/surveyingMappingObjectCate/all"
	)
}
/**
 * 配置-GIS信息  批量导入文件
 */
export function globalUploadApi(fileList: any[], data: any, url: string = '', idempotentToken?: string) {
  const formData = new FormData()
  fileList.forEach((item: any) => {
    // formData.append('file', item.raw)
		formData.append("file", item.raw, item.raw?.name)
  })
  //遍历data  append 到formData
  Object.keys(data).forEach((key: string) => {
    formData.append(key, data[key])
  })
  return request({
    url,
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
			"idempotent-token": idempotentToken
    },
  })
}
