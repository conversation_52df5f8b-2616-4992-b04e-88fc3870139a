/**
 * @description 废旧 - 返修申请
 */

import { PageResVo } from "../../utils/types/common"
import ReqUtil from "../../utils/request-util"
import {
	MatWasteRepairApplyPageQuery,
	MatWasteRepairApplyVo,
	MatWasteRepairApplyAddVo,
	MatWasteRepairApplyResVo,
	repairApplyItemVo,
	WasteRepairMaterialStoreVo
} from "../../utils/types/waste-repair-apply"

/**
 * 获取状态统计
 */
export function getWasteRepairApplyBmpStatusCnt(
	params: MatWasteRepairApplyPageQuery
) {
	return request<number[]>({
		url: "/baseline/store/wasteRepairApply/bmpStatusStatistics",
		method: "get",
		params
	})
}

/**
 * 返修申请 分页
 */
export async function listWasteRepairApplyPaged(
	params: MatWasteRepairApplyPageQuery
) {
	return ReqUtil.get<PageResVo<MatWasteRepairApplyVo>>(
		"/baseline/store/wasteRepairApply/page",
		{ params }
	)
}

// 其他入库可选返修申请
export async function listWasteRepairApplySelectPaged(
	params: MatWasteRepairApplyPageQuery
) {
	return ReqUtil.get<PageResVo<MatWasteRepairApplyVo>>(
		"/baseline/store/wasteRepairApply/pageCanRepairApply",
		{ params }
	)
}

/**
 * 返修申请 删除
 * @param id
 */
export async function delWasteRepairApply(id: any) {
	return ReqUtil.delete<any>("/baseline/store/wasteRepairApply/del", {
		params: { id }
	})
}

/**
 * 返修申请 添加 -保存草稿
 * @param data
 */
export async function addWasteRepairApply(
	data: MatWasteRepairApplyAddVo,
	idempotentToken?: string
) {
	return ReqUtil.post<MatWasteRepairApplyResVo>(
		"/baseline/store/wasteRepairApply/add",
		{ data, headers: { "idempotent-token": idempotentToken } }
	)
}

/**
 * 返修申请 编辑 - 草稿
 * @param data
 */
export async function updateWasteRepairApply(
	data: MatWasteRepairApplyAddVo,
	_idempotentToken?: string
) {
	return ReqUtil.put<MatWasteRepairApplyResVo>(
		"/baseline/store/wasteRepairApply/edit",
		{ data }
	)
}

/**
 * 返修申请 获取返修申请详情
 * @param id
 */
export async function getRepairApplyDetail(id: any) {
	return ReqUtil.get<MatWasteRepairApplyVo>(
		"/baseline/store/wasteRepairApply/getApply",
		{ params: { id } }
	)
}

/**
 * 返修申请 提交审批
 * @param data
 */
export async function submitWasteRepairApply(
	id: number,
	idempotentToken?: string
) {
	return ReqUtil.post<any>("/baseline/store/wasteRepairApply/publish", {
		params: { id },
		headers: { "idempotent-token": idempotentToken }
	})
}

/**
 * 返修申请明细 分页
 */
export async function repairApplyPageItem(params: Record<string, any>) {
	return ReqUtil.get<PageResVo<repairApplyItemVo>>(
		"/baseline/store/wasteRepairApply/pageApplyItem",
		{ params }
	)
}

/**
 * 批量新增 - 返修申请新增物资
 * @param data
 */
export async function addRepairApplyBatchItem(
	data: any,
	idempotentToken?: string
) {
	return ReqUtil.post<any>("/baseline/store/wasteRepairApply/batchAddItem", {
		data,
		headers: { "idempotent-token": idempotentToken }
	})
}

/**
 * 编辑 - 返修数量
 * @param data
 */
export async function updateRepairApplyItem<T>(data: T) {
	return ReqUtil.put<any>("/baseline/store/wasteRepairApply/batchEditItem", {
		data
	})
}

/**
 * 批量删除 - 申请明细
 * @param ids
 */
export async function deleteRepiarApplyBatchItem(idList: any) {
	return ReqUtil.delete<any>("/baseline/store/wasteRepairApply/batchDelItem", {
		data: { idList }
	})
}

/**
 * 选择物资 分页
 * 物资 drawer
 */
export async function listRepairAddPageItem(params: any) {
	return ReqUtil.get<PageResVo<WasteRepairMaterialStoreVo>>(
		"/baseline/store/wasteRepairApply/pageMaterialStore",
		{ params }
	)
}

/**
 * 交旧申请单号添加 分页
 * 物资 drawer
 */
export async function listWasteOldApplyPaged(params: any) {
	return ReqUtil.get<PageResVo<WasteRepairMaterialStoreVo>>(
		"/baseline/store/wasteRepairApply/getOldApplyList",
		{ params }
	)
}
