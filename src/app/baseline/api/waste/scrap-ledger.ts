/**
 * @description 废旧 - 废旧台账
 */

import { PageResVo } from "../../utils/types/common"
import ReqUtil from "../../utils/request-util"
import {
	ScrapBookDetailVo,
	ScrapBookInStoreAgePageVo,
	ScrapBookItemRequest,
	ScrapBookPageVo,
	ScrapBookPageVoQuery,
	ScrapBookScrapRecordPageVo,
	ScrapBookStorePageVo,
	ScrapBookWasteOldRecordPageVo
} from "../../utils/types/waste-scrap-ledger"

/**
 * 报废台账分页搜索 分页
 */
export async function listWasteScrapBookPaged(params: ScrapBookPageVoQuery) {
	return ReqUtil.get<PageResVo<ScrapBookPageVo>>(
		"/baseline/wasteold/wasteBook/pageScrapBook",
		{ params }
	)
}

/**
 * 报废台账详情
 */
export async function listWasteScrapBookDetail(bookId: number) {
	return ReqUtil.get<ScrapBookDetailVo>(
		"/baseline/wasteold/wasteBook/getScrapBookDetail",
		{ params: { bookId } }
	)
}

/**
 * 报废台账 库存查询 分页 TODO
 */
export async function listWasteBookStorePaged(params: ScrapBookItemRequest) {
	return ReqUtil.get<PageResVo<ScrapBookStorePageVo>>(
		"/baseline/wasteold/wasteBook/pageScrapBookStore",
		{ params }
	)
}

/**
 * 报废台账 交旧记录 分页 TODO
 */
export async function listWasteBookWasteOldPaged(params: ScrapBookItemRequest) {
	return ReqUtil.get<PageResVo<ScrapBookWasteOldRecordPageVo>>(
		"/baseline/wasteold/wasteBook/pageScrapBookWasteOldRecord",
		{ params }
	)
}

/**
 * 报废台账 报废记录 分页
 */
export async function listWasteScrapBookScrapPaged(
	params: ScrapBookItemRequest
) {
	return ReqUtil.get<PageResVo<ScrapBookScrapRecordPageVo>>(
		"/baseline/wasteold/wasteBook/pageScrapBookScrapRecorde",
		{ params }
	)
}

/**
 * 报废台账 在库时长 分页
 */
export async function listWasteScrapBookInStorePaged(
	params: ScrapBookItemRequest
) {
	return ReqUtil.get<PageResVo<ScrapBookInStoreAgePageVo>>(
		"/baseline/wasteold/wasteBook/pageScrapBookInStoreAge",
		{ params }
	)
}
