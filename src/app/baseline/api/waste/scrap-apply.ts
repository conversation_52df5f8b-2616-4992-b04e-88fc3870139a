/**
 * @description 废旧 - 报废申请
 */

import { PageResVo } from "../../utils/types/common"
import ReqUtil from "../../utils/request-util"
import {
	CkWasteStoreMaterialVo,
	CkWasteStoreMaterialVoQuery,
	MatWasteScrapApplyDTO,
	MatWasteScrapApplyItemBatchDelDTO,
	MatWasteScrapApplyItemDTOQuery,
	MatWasteScrapApplyItemVo,
	MatWasteScrapApplyItemVoQuery,
	MatWasteScrapApplyPageVo,
	MatWasteScrapApplyPageVoQuery,
	MatWasteScrapApplyVo,
	WasteMaterialStoreVo,
	WasteMaterialStoreVoQuery,
	WasteStoreVo
} from "../../utils/types/waste-scrap-apply"
import { LowValueApplyBpmStatusVo } from "../../utils/types/lowValue-requisition-apply"

/**
 * 报废申请-状态统计
 */
export async function getWasteScrapApplyStatusCnt(
	params: MatWasteScrapApplyPageVoQuery
) {
	return ReqUtil.get<LowValueApplyBpmStatusVo>(
		"/baseline/wasteold/wasteScrapApply/bmpStatusStatistics",
		{ params }
	)
}

/**
 * 报废申请 分页
 */
export async function listWasteScrapApplyPaged(
	params: MatWasteScrapApplyPageVoQuery
) {
	return ReqUtil.get<PageResVo<MatWasteScrapApplyPageVo>>(
		"/baseline/wasteold/wasteScrapApply/page",
		{ params }
	)
}

/**
 * 报废申请-查看-报废单信息
 */
export async function listWasteScrapApplyDetail(scrapId: number) {
	return ReqUtil.get<MatWasteScrapApplyVo>(
		"/baseline/wasteold/wasteScrapApply/getApply",
		{ params: { scrapId } }
	)
}

/**
 * 报废申请 新建
 */
export async function addWasteScrapApply(
	data: MatWasteScrapApplyDTO,
	idempotentToken?: string
) {
	return ReqUtil.post<MatWasteScrapApplyVo>(
		"/baseline/wasteold/wasteScrapApply/add",
		{ data, headers: { "idempotent-token": idempotentToken } }
	)
}

/**
 * 报废申请 编辑
 */
export async function updateWasteScrapApply(
	data: MatWasteScrapApplyDTO,
	_idempotentToken?: string
) {
	return ReqUtil.put<MatWasteScrapApplyVo>(
		"/baseline/wasteold/wasteScrapApply/edit",
		{ data }
	)
}

/**
 * 报废申请 删除
 */
export async function delWasteScrapApply(scrapId: number) {
	return ReqUtil.delete<MatWasteScrapApplyVo>(
		"/baseline/wasteold/wasteScrapApply/del",
		{ params: { scrapId } }
	)
}

/**
 * 报废申请-扩展信息-物资明细 分页
 */
export async function listWasteScrapApplyItemPaged(
	params: MatWasteScrapApplyItemVoQuery
) {
	return ReqUtil.get<PageResVo<MatWasteScrapApplyItemVo>>(
		"/baseline/wasteold/wasteScrapApply/pageApplyItem",
		{ params }
	)
}

/**
 * 报废申请-扩展信息-物资明细-存放仓库
 */

export async function listShowStoresByStoreIdsAndMaterialIdPaged(
	params: CkWasteStoreMaterialVoQuery
) {
	return ReqUtil.post<PageResVo<CkWasteStoreMaterialVo>>(
		"/baseline/wasteold/wasteScrapApply/pageStoresByStoreIdsAndMaterialId",
		{ params }
	)
}

/**
 * 报废申请-扩展信息-物资明细-添加物资 分页
 */

export async function listWasteMaterialStorePage(
	params: WasteMaterialStoreVoQuery
) {
	return ReqUtil.get<PageResVo<WasteMaterialStoreVo>>(
		"/baseline/wasteold/wasteScrapApply/pageWasteMaterialStore",
		{ params }
	)
}

/**
 * 报废申请-扩展信息-物资明细-添加物资
 */

export async function addWasteScrapApplyBatchItem(
	data: MatWasteScrapApplyItemDTOQuery,
	idempotentToken?: string
) {
	return ReqUtil.post<boolean | null>(
		"/baseline/wasteold/wasteScrapApply/batchAddItem",
		{
			data,
			headers: { "idempotent-token": idempotentToken }
		}
	)
}
/**
 * 报废申请-扩展信息-物资明细-批量移除物资
 */
export async function delWasteScrapApplyBatchItem(
	data: MatWasteScrapApplyItemBatchDelDTO
) {
	return ReqUtil.delete<boolean | null>(
		"/baseline/wasteold/wasteScrapApply/batchDelItem",
		{ data }
	)
}

/**
 * 报废申请-扩展信息-仓库明细 TODO入参
 * {scrapId: number}
 */
export async function listShowStoreByMaterialIds(params: Record<string, any>) {
	return ReqUtil.get<PageResVo<WasteStoreVo>>(
		"/baseline/wasteold/wasteScrapApply/pageStoreByMaterialIds",
		{ params }
	)
}

/**
 * 报废申请-扩展信息-仓库明细-查看物资明细 TODO入参
 * {scrapId: number}
 */
export async function listShowMaterialByStoreIdAndMaterialIds(
	params: Record<string, any>
) {
	return ReqUtil.get<PageResVo<WasteMaterialStoreVo>>(
		"/baseline/wasteold/wasteScrapApply/pageMaterialByStoreIdAndMaterialIds",
		{ params }
	)
}

/**
 *
 */
export async function submitWasteScrapApplyPublish(
	id: number,
	idempotentToken?: string
) {
	return ReqUtil.post<any>("/baseline/wasteold/wasteScrapApply/publish", {
		params: { id },
		headers: { "idempotent-token": idempotentToken }
	})
}
