/**
 * 废旧 - 厂商管理 Api
 */
import ReqUtil from "../../utils/request-util"
import { PageResVo } from "../../utils/types/common"
import {
	MatWasteRepairCompanyRequest,
	MatWasteRepairCompanyVo,
	MatWasteRepairCompanyDto
} from "../../utils/types/waste-supplier"

/**
 * 厂商管理 列表
 */
export async function listWasteRepairCompanyPaged(
	params: MatWasteRepairCompanyRequest
) {
	return ReqUtil.get<PageResVo<MatWasteRepairCompanyVo>>(
		"/baseline/wasteold/repairCompany/page",
		{ params }
	)
}

/**
 * 维修公司管理-获取维修公司详细信息
 * @param id
 * @returns
 */
export async function getWasteRepairCompanyDetail(id: number) {
	return ReqUtil.get<MatWasteRepairCompanyVo>(
		`/baseline/wasteold/repairCompany/${id}`
	)
}

/**
 *维修公司管理-新增维修公司
 * @param data
 * @returns
 */
export async function addWasteRepairCompany(
	data: MatWasteRepairCompanyDto,
	idempotentToken?: string
) {
	return ReqUtil.post<MatWasteRepairCompanyVo>(
		"/baseline/wasteold/repairCompany",
		{ data, headers: { "idempotent-token": idempotentToken } }
	)
}

/**
 *维修公司管理-编辑维修公司
 * @param data
 * @returns
 */
export async function updateWasteRepairCompany(
	data: MatWasteRepairCompanyDto,
	_idempotentToken?: string
) {
	return ReqUtil.put<MatWasteRepairCompanyVo>(
		"/baseline/wasteold/repairCompany",
		{ data }
	)
}

/**
 *维修公司管理-批量删除维修公司
 * @param id
 * @returns
 */
export async function delWasteRepairCompany(id: number) {
	return ReqUtil.delete<boolean | null>(
		`/baseline/wasteold/repairCompany/${id}`
	)
}

/**
 *维修公司管理-提交审批
 * @param id
 * @returns
 */
export async function submitWasteRepairCompany(
	id: number,
	idempotentToken?: string
) {
	return ReqUtil.post<null>("/baseline/wasteold/repairCompany/publish", {
		params: { id },
		headers: { "idempotent-token": idempotentToken }
	})
}
