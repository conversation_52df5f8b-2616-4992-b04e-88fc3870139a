/**
 * @description 废旧 - 报废处置
 */

import { PageResVo } from "../../utils/types/common"
import ReqUtil from "../../utils/request-util"
import {
	CkWasteDisposalStoreMaterialVoQuery,
	MatWasteScrapDisposalApplyDTO,
	MatWasteScrapDisposalApplyItemVoQuery,
	MatWasteScrapDisposalApplyStatusVo,
	MatWasteScrapDisposalApplyVo,
	MatWasteScrapDisposalApplyVoRequest,
	MatWasteScrapDisposalReportVo
} from "../../utils/types/waste-scrap-disposal-apply"
import {
	CkWasteStoreMaterialVo,
	MatWasteScrapApplyItemVo,
	MatWasteScrapApplyVo,
	WasteStoreVo
} from "../../utils/types/waste-scrap-apply"

/**
 * 主页 分页
 */
export async function listDisposalApplyPaged(
	params: MatWasteScrapDisposalApplyVoRequest
) {
	return ReqUtil.get<PageResVo<MatWasteScrapDisposalApplyVo>>(
		"/baseline/wasteold/disposalApply/page",
		{ params }
	)
}

/**
 * 报废处置-状态数量
 */
export async function getStatusCnt(
	params: MatWasteScrapDisposalApplyVoRequest
) {
	return ReqUtil.get<MatWasteScrapDisposalApplyStatusVo>(
		"/baseline/wasteold/disposalApply/getStatusCnt",
		{ params }
	)
}

/**
 * 报废处置单 详情
 */
export async function listDisposalApplyDetail(id: number) {
	return ReqUtil.get<Record<string, any>>(
		"/baseline/wasteold/disposalApply/getApply",
		{ params: { id } }
	)
}

/**
 * 报废处置单 新建
 */
export async function addDisposalApply(
	data: MatWasteScrapDisposalApplyDTO,
	idempotentToken?: string
) {
	return ReqUtil.post<MatWasteScrapDisposalApplyVo>(
		"/baseline/wasteold/disposalApply/add",
		{ data, headers: { "idempotent-token": idempotentToken } }
	)
}

/**
 * 报废处置单 编辑
 */
export async function updateDisposalApply(
	data: MatWasteScrapDisposalApplyDTO,
	_idempotentToken?: string
) {
	return ReqUtil.put<MatWasteScrapDisposalApplyVo>(
		"/baseline/wasteold/disposalApply/edit",
		{ data }
	)
}

/**
 * 报废处置 删除
 */
export async function delDisposalApply(id: number) {
	return ReqUtil.delete<boolean | null>(
		"/baseline/wasteold/disposalApply/del",
		{ params: { id } }
	)
}

/**
 * 报废处置-扩展信息-报废申请 分页 {id}
 */
export async function listScrapApplyByDisposalApplyIdPaged(
	params: MatWasteScrapDisposalApplyItemVoQuery
) {
	return ReqUtil.get<PageResVo<MatWasteScrapApplyVo>>(
		"/baseline/wasteold/disposalApply/pageScrapApplyByDisposalApplyId",
		{ params }
	)
}

/**
 * 报废处置-扩展信息-报废申请 添加报废申请 分页 {id}
 */

export async function listScrapApplyByDisposalApplyIdOthersPaged(
	params: MatWasteScrapDisposalApplyItemVoQuery
) {
	return ReqUtil.get<PageResVo<MatWasteScrapApplyVo>>(
		"/baseline/wasteold/disposalApply/pageScrapApplyByDisposalApplyIdOthers",
		{ params }
	)
}

/**
 * 报废处置-扩展信息-报废申请 添加报废申请 新建
 */
export async function addBatchAddDisposalApplyItem(
	data: {
		disposalApplyId?: number | null
		scrapIdList?: number[] | null
	},
	idempotentToken?: string
) {
	return ReqUtil.post<boolean | null>(
		"/baseline/wasteold/disposalApply/batchAddDisposalApplyItem",
		{ data, headers: { "idempotent-token": idempotentToken } }
	)
}

/**
 * 报废处置-扩展信息-报废申请 删除 {id}
 */
export async function delScrapApplyByDisposalApplyIdPaged(data: {
	disposalApplyId?: number | null
	scrapIdList?: number[] | null
}) {
	return ReqUtil.delete<boolean | null>(
		"/baseline/wasteold/disposalApply/batchDelDisposalApplyItem",
		{ data }
	)
}

/**
 * 报废处置-扩展信息-物资明细 分页 {id}
 */
export async function listWasteMatByDisposalApplyIdPaged(
	params: MatWasteScrapDisposalApplyItemVoQuery
) {
	return ReqUtil.get<PageResVo<MatWasteScrapApplyItemVo>>(
		"/baseline/wasteold/disposalApply/pageWasteMaterialByDisposalApplyId",
		{ params }
	)
}
/**
 * 报废申请-扩展信息-物资明细-存放仓库
 */

export async function listWasteStoresByDisposalApplyIdAndMaterialIdPaged(
	params: CkWasteDisposalStoreMaterialVoQuery
) {
	return ReqUtil.get<PageResVo<CkWasteStoreMaterialVo>>(
		"/baseline/wasteold/disposalApply/pageWasteStoreByDisposalApplyIdAndMaterialId",
		{ params }
	)
}

/**
 * 报废处置-扩展信息-仓库明细
 */
export async function listWasteStoreByDisposalApplyIdPaged(
	params: MatWasteScrapDisposalApplyItemVoQuery
) {
	return ReqUtil.get<PageResVo<WasteStoreVo>>(
		"/baseline/wasteold/disposalApply/pageWasteStoreByDisposalApplyId",
		{ params }
	)
}

/**
 * 报废处置-扩展信息-仓库明细-查看物资明细
 */
export async function listWasteStoreByDisposalApplyIdInfo(
	params: Record<string, any>
) {
	return ReqUtil.get<PageResVo<MatWasteScrapApplyItemVo>>(
		"/baseline/wasteold/disposalApply/pageWasteMaterialByDisposalApplyIdAndStoreId",
		{ params }
	)
}

/**
 * 报废处置-待询价-报废询价报告-添加报废询价报告
 */
export async function addScrapDisposalReport(
	data: MatWasteScrapDisposalApplyDTO,
	idempotentToken?: string
) {
	return ReqUtil.post<boolean | null>(
		"/baseline/wasteold/disposalApply/addScrapDisposalReport",
		{ data, headers: { "idempotent-token": idempotentToken } }
	)
}

/**
 * 报废处置-待询价-报废询价报告
 */
export async function listScrapDisposalReportPaged(
	params: MatWasteScrapDisposalApplyItemVoQuery
) {
	return ReqUtil.get<PageResVo<MatWasteScrapDisposalReportVo>>(
		"/baseline/wasteold/disposalApply/pageScrapDisposalReport",
		{ params }
	)
}

/**
 * 报废处置-待询价-报废询价报告 删除
 */
export async function delScrapDisposalReport(idScrapDisposalReport: number) {
	return ReqUtil.delete<boolean | null>(
		"/baseline/wasteold/disposalApply/delScrapDisposalReport",
		{ params: { idScrapDisposalReport } }
	)
}

/**
 * 报废处置-待处置-处置结果
 */
export async function listScrapDisposalResultPaged(
	params: MatWasteScrapDisposalApplyItemVoQuery
) {
	return ReqUtil.get<PageResVo<MatWasteScrapDisposalReportVo>>(
		"/baseline/wasteold/disposalApply/pageScrapDisposalResult",
		{ params }
	)
}

/**
 * 报废处置-待处置-处置结果-移除
 */
export async function delScrapDisposalResult(idScrapDisposalResult: number) {
	return ReqUtil.delete<boolean | null>(
		"/baseline/wasteold/disposalApply/delScrapDisposalResult",
		{ params: { idScrapDisposalResult } }
	)
}

/**
 * 报废处置-待询价-处置结果-添加处置结果
 */
export async function addScrapDisposalResult(
	data: MatWasteScrapDisposalApplyDTO,
	idempotentToken?: string
) {
	return ReqUtil.post<boolean | null>(
		"/baseline/wasteold/disposalApply/addScrapDisposalResult",
		{ data, headers: { "idempotent-token": idempotentToken } }
	)
}

/**
 * 报废处置-处置申请-提交审批
 */
export async function pubishDisposalApply(id: any, idempotentToken?: string) {
	return ReqUtil.post<any>("/baseline/wasteold/disposalApply/publish", {
		params: { id },
		headers: { "idempotent-token": idempotentToken }
	})
}

/**
 * 报废处置-待询价-报废询价报告-确认提交
 */
export async function submitScrapDisposalReport(
	id: any,
	idempotentToken?: string
) {
	return ReqUtil.post<boolean | null>(
		"/baseline/wasteold/disposalApply/submitScrapDisposalReport",
		{ params: { id }, headers: { "idempotent-token": idempotentToken } }
	)
}

/**
 * 报废处置-待处置-处置结果-确认提交
 */
export async function submitScrapDisposalResult(
	id: any,
	idempotentToken?: string
) {
	return ReqUtil.post<any>(
		"/baseline/wasteold/disposalApply/submitScrapDisposalResult",
		{ params: { id }, headers: { "idempotent-token": idempotentToken } }
	)
}
