/**
 * @description 废旧 - 交旧申请
 */

import { PageResVo } from "../../utils/types/common"
import ReqUtil from "../../utils/request-util"
import {
	MatWasteOldApplyAddDTO,
	MatWasteOldApplyItemBatchAddDTO,
	MatWasteOldApplyItemBatchUpdateDTO,
	MatWasteOldApplyItemBatchUpdateStoreDTO,
	MatWasteOldApplyItemPageVo,
	MatWasteOldApplyPageQuery,
	MatWasteOldApplyPageVo,
	MatWasteOldApplyVo,
	MatWasteOldDetailVo
} from "../../utils/types/waste-handover-apply"

/**
 * 获取状态统计
 */
export function getWasteOldApplyBmpStatusCnt(
	params: MatWasteOldApplyPageQuery
) {
	return request<number[]>({
		url: "/baseline/wasteold/wasteOldApply/bpmStatusStatistics",
		method: "get",
		params
	})
}

/**
 * 交旧申请 分页
 */
export async function listWasteOldApplyPaged(
	params: MatWasteOldApplyPageQuery
) {
	return ReqUtil.get<PageResVo<MatWasteOldApplyPageVo>>(
		"/baseline/wasteold/wasteOldApply/page",
		{ params }
	)
}

/**
 * 交旧申请 添加 -保存草稿
 * @param data
 */
export async function addWasteOldApply(
	data: MatWasteOldApplyAddDTO,
	idempotentToken?: string
) {
	return ReqUtil.post<MatWasteOldApplyVo>(
		"/baseline/wasteold/wasteOldApply/add",
		{ data, headers: { "idempotent-token": idempotentToken } }
	)
}

/**
 * 交旧申请 编辑 - 草稿
 * @param data
 */
export async function updateWasteOldApply(
	data: MatWasteOldApplyAddDTO,
	_idempotentToken?: string
) {
	return ReqUtil.put<MatWasteOldApplyVo>(
		"/baseline/wasteold/wasteOldApply/update",
		{ data }
	)
}

/**
 * 交旧申请 删除
 * @param id
 */
export async function delWasteOldApply(applyId: any) {
	return ReqUtil.delete<any>("/baseline/wasteold/wasteOldApply/delete", {
		data: { applyId }
	})
}

/**
 * 交旧申请 获取交旧申请详情
 * @param id
 */
export async function listWasteOldApplyDetail(id: any) {
	return ReqUtil.get<MatWasteOldDetailVo>(
		"/baseline/wasteold/wasteOldApply/getApplyDetail",
		{ params: { id } }
	)
}

/**
 * 批量新增 - 交旧申请明细
 * @param data
 */
export async function addWasteOldApplyBatchItem(
	data: MatWasteOldApplyItemBatchAddDTO,
	idempotentToken?: string
) {
	return ReqUtil.post<any>(
		"/baseline/wasteold/wasteOldApplyItem/batchAddApplyItem",
		{
			data,
			headers: { "idempotent-token": idempotentToken }
		}
	)
}

/**
 * 批量编辑 - 交旧申请明细
 * @param data
 */
export async function updateWasteOldApplyBatchItem(
	data: MatWasteOldApplyItemBatchUpdateDTO
) {
	return ReqUtil.put<any>(
		"/baseline/wasteold/wasteOldApplyItem/batchUpdateApplyItem",
		{ data }
	)
}

/**
 * 交旧申请明细-批量更新仓库
 * @param data
 */
export async function batchUpdateStore(
	data: MatWasteOldApplyItemBatchUpdateStoreDTO
) {
	return ReqUtil.post<any>(
		"/baseline/wasteold/wasteOldApplyItem/batchUpdateStore",
		{ data }
	)
}

/**
 * 批量删除 - 交旧申请明细
 * @param itemIds
 */
export async function deleteWasteOldApplyBatchItem(itemIds: string) {
	return ReqUtil.delete<any>(
		"/baseline/wasteold/wasteOldApplyItem/batchDeleteApplyItem",
		{ data: { itemIds } }
	)
}

/**
 * 交旧申请明细 分页
 */
export async function listWasteOldApplyPageItem(params: Record<string, any>) {
	return ReqUtil.get<PageResVo<MatWasteOldApplyItemPageVo>>(
		"/baseline/wasteold/wasteOldApplyItem/pageItem",
		{ params }
	)
}

/**
 * 交旧申请 审批
 * @param data
 */
export async function submitWasteOldApply(
	applyId: number,
	idempotentToken?: string
) {
	return ReqUtil.post<any>("/baseline/wasteold/wasteOldApply/publish", {
		params: { applyId },
		headers: { "idempotent-token": idempotentToken }
	})
}

/**
 * 历史交旧(物资编码交旧)可选物资列表
 * @param params
 * @returns
 */
export async function listOldMaterialCodePaged(params: Record<string, any>) {
	return ReqUtil.post<PageResVo<any>>(
		"/baseline/wasteold/wasteOldApplyItem/listMaterialCode",
		{ data: params }
	)
}

/**
 * 在库物资交旧可选物资列表
 * @param params
 * @returns
 */
export async function listMatInStoreSelectorPaged(params: Record<string, any>) {
	return ReqUtil.post<PageResVo<any>>(
		"/baseline/wasteold/wasteOldApplyItem/listInStore",
		{ data: params }
	)
}
