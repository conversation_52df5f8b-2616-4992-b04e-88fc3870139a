/**
 * @description 废旧 - 返修台账
 */

import { PageResVo } from "../../utils/types/common"
import ReqUtil from "../../utils/request-util"

import {
	RepairBookDetailVo,
	RepairBookItemRequest,
	RepairBookPageVo,
	RepairBookPageVoRequest,
	RepairBookRepairRecordPageVo,
	RepairBookStorePageVo,
	RepairBookWasteOldRecordPageVo
} from "../../utils/types/warehouse-repair-ledger"
import {
	ScrapBookInStoreAgePageVo,
	ScrapBookItemRequest
} from "../../utils/types/waste-scrap-ledger"

/**
 * 主列表  分页
 */
export async function listPageRepairBookPaged(params: RepairBookPageVoRequest) {
	return ReqUtil.get<PageResVo<RepairBookPageVo>>(
		"/baseline/wasteold/wasteBook/pageRepairBook",
		{ params }
	)
}

/**
 * 详情
 */
export async function getRepairBookDetail(bookId: number) {
	return ReqUtil.get<RepairBookDetailVo>(
		"/baseline/wasteold/wasteBook/getRepairBookDetail",
		{ params: { bookId } }
	)
}

/**
 * 库存查询 分页
 */
export async function listRepairBookStorePaged(params: RepairBookItemRequest) {
	return ReqUtil.get<PageResVo<RepairBookStorePageVo>>(
		"/baseline/wasteold/wasteBook/pageRepairBookStore",
		{ params }
	)
}

/**
 * 交旧记录 分页
 */
export async function listRepairBookWasteOldRecordPaged(
	params: RepairBookItemRequest
) {
	return ReqUtil.get<PageResVo<RepairBookWasteOldRecordPageVo>>(
		"/baseline/wasteold/wasteBook/pageRepairBookWasteOldRecord",
		{ params }
	)
}

/**
 * 报废记录 分页
 */
export async function listRepairBookRepairRecordePaged(
	params: RepairBookItemRequest
) {
	return ReqUtil.get<PageResVo<RepairBookRepairRecordPageVo>>(
		"/baseline/wasteold/wasteBook/pageRepairBookRepairRecorde",
		{ params }
	)
}

/**
 * 报废台账 在库时长 分页查询
 */
export async function listWasteRepairBookInStorePaged(
	params: ScrapBookItemRequest
) {
	return ReqUtil.get<PageResVo<ScrapBookInStoreAgePageVo>>(
		"/baseline/wasteold/wasteBook/pageRepairBookInStoreAge",
		{ params }
	)
}
