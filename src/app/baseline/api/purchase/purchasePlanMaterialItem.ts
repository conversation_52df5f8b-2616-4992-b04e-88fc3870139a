import { request } from "@/app/platform/utils/service"
import {
	AddPurchasePlanMaterialItemRequest,
	GetPurchaseAdvanceOrderItemRequest,
	GetPurchasePlanMaterialItemListRequest,
	UpdatePurchasePlanMaterialItemRequest
} from "@/app/baseline/api/defines"

// 查询订货计划明细列表
function getPurchasePlanMaterialItemList(
	query: GetPurchasePlanMaterialItemListRequest
) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchasePlanMaterialItem/list",
		method: "get",
		params: query
	})
}
// 查询订货计划提前订货列表
function getPurchaseAdvanceOrderItem(
	query: GetPurchaseAdvanceOrderItemRequest
) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchasePlanMaterialItem/listAdvanceOrderItem",
		method: "get",
		params: query
	})
}

// 查询订货计划明细详细
function getPurchasePlanMaterialItem(id: string) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchasePlanMaterialItem/" + id,
		method: "get"
	})
}

// 新增订货计划明细
function addPurchasePlanMaterialItem(data: AddPurchasePlanMaterialItemRequest) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchasePlanMaterialItem",
		method: "post",
		data
	})
}

// 批量新增 订货计划明细
function addBatchPurchasePlanMaterialItem(
	data: AddPurchasePlanMaterialItemRequest[],
	idempotentToken?: string
) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchasePlanMaterialItem/addBatch",
		method: "post",
		data,
		headers: { "idempotent-token": idempotentToken }
	})
}

// 修改订货计划明细
function updatePurchasePlanMaterialItem(
	data: UpdatePurchasePlanMaterialItemRequest
) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchasePlanMaterialItem",
		method: "put",
		data
	})
}
// 删除订货计划明细
function deletePurchasePlanMaterialItem(data: any) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchasePlanMaterialItem/remove",
		method: "delete",
		data
	})
}

// 获取订货计划物资详细信息
function getPurchasePlanItem(id: string) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchasePlanMaterialItem/" + id,
		method: "get"
	})
}

export const PurchasePlanMaterialItemApi = {
	getPurchasePlanMaterialItemList,
	getPurchasePlanMaterialItem,
	addPurchasePlanMaterialItem,
	addBatchPurchasePlanMaterialItem,
	updatePurchasePlanMaterialItem,
	deletePurchasePlanMaterialItem,
	getPurchaseAdvanceOrderItem,
	getPurchasePlanItem
}
