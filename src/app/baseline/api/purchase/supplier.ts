import { request } from "@/app/platform/utils/service"

// 获取供应商列表
function getSupplierList(data: any) {
	let tmp = {
		"code": "5363",
		"name": "北京易成永泰电气有限公司",
		"person": "许士华",
		"contactPerson": "刘立杰",
		"mobile": "13800138000",
		"address": "北京市昌平区崔村镇真顺文化园1区28号",
		"createdBy_view":"范遥",
		"createdDate": "2023-09-21 13:28:29",
		"email": "<EMAIL>"
	};
	function genGood(id: any) {
		return {
			id: id + 1,
			bpmStatus: Math.floor(Math.random() * 5),
			...tmp
		};
	}
	return Promise.resolve( {
		currentPage: 1,
		pageSize: 20,
		total: 2,
		records: 2,
		rows: new Array(10).fill(0).map((_, idx) => genGood(idx))
	} );
	return request<SystemApiResponseData>({
		url: "",
		method: "get",
		data
	})
}

// 新建供应商
function addSupplier(data: any) {
	return Promise.resolve( {
		code: 200,
		msg: '成功'
	} );
	return request<SystemApiResponseData>({
		url: "",
		method: "post",
		data
	})
}

// 更新供应商
function updateSupplier(data: any) {
	return Promise.resolve( {
		code: 200,
		msg: '成功'
	} );
	return request<SystemApiResponseData>({
		url: "",
		method: "put",
		data
	})
}



// 需求计划数量统计
function getPlanCnt( data : anyKey ) : Promise<any>{
	return request<SystemApiResponseData>({
		url: "/baseline/plan/plan/planTypeStatistics",
		method: "get",
		params: data
	})
}


// 查询需求计划详细
function getInfoById(id: any) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/plan/" + id,
		method: "get"
	})
}

// 新增需求计划
function addPlan(data: any) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/plan",
		method: "post",
		data
	})
}

// 修改需求计划
function updatePlan(data: any) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/plan",
		method: "put",
		data
	})
}

// 需求计划提交审核
function publishPlan( id:string ) {
	return request<SystemApiResponseData>({
		headers: { "Content-Type": "multipart/form-data" },
		url: "/baseline/plan/plan/publish",
		method: "post",
		data:{id}
	})
}

// 删除需求计划
function deletePlan(id: any) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/plan/" + id,
		method: "delete"
	})
}

/**
 * 根据计划编号获取物资列表
 * @param id
 */
function getMatListByPlanId(query: any) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/planItem/list",
		method: "get",
		params: query
	})
}

// 新增需求计划物资明细
function addPlanItem(data: any) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/planItem",
		method: "post",
		data
	})
}

// 删除需求计划物资明细
function deletePlanItem(id: any) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/planItem/" + id,
		method: "delete"
	})
}

// 查询需求计划物资明细详细
function getPlanItem(id: any) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/planItem/" + id,
		method: "get"
	})
}

// 修改需求计划物资明细
function updatePlanItem(data: any) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/planItem",
		method: "put",
		data
	})
}

// 新增需求计划物资明细-月度
function addPlanItemMonthly(data: any) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/planItemMonthly",
		method: "post",
		data
	})
}

// 修改需求计划物资明细-月度
function updatePlanItemMonthly(data: any) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/planItemMonthly",
		method: "put",
		data
	})
}

// 查询需求计划物资明细-月度列表
function getPlanItemMonthlyList(query: any) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/planItemMonthly/list",
		method: "get",
		params: query
	})
}

// 查询需求计划物资明细-月度详细
function getPlanItemMonthly(id: any) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/planItemMonthly/" + id,
		method: "get"
	})
}

export const SupplierApi = {
	getSupplierList,
	addSupplier,
	updateSupplier
}
