import { request } from "@/app/platform/utils/service"
import {
	AddPurchaseProjectRequest,
	GenerateMonthlyPurchasePlanRequest,
	GetPurchaseProjectListRequest,
	UpdatePurchaseProjectRequest
} from "@/app/baseline/api/defines"

// 查询采购项目列表
function getPurchaseProjectList(query: GetPurchaseProjectListRequest) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchaseProject/list",
		method: "get",
		params: query
	})
}

// 查询采购项目详细
function getPurchaseProject(id: string) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchaseProject/" + id,
		method: "get"
	})
}

// 新增采购项目
function addPurchaseProject(data: AddPurchaseProjectRequest) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchaseProject",
		method: "post",
		data
	})
}

// 修改采购项目
function updatePurchaseProject(data: UpdatePurchaseProjectRequest) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchaseProject",
		method: "put",
		data
	})
}
// 删除采购项目
function deletePurchaseProject(id: string) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchaseProject/" + id,
		method: "delete"
	})
}

//生成月度订货计划
function generateMonthlyPurchasePlan(
	data: GenerateMonthlyPurchasePlanRequest,
	idempotentToken?: string
) {
	return request<SystemApiResponseData>({
		headers: {
			"Content-Type": "multipart/form-data",
			"idempotent-token": idempotentToken
		},
		url: "/baseline/purchase/purchaseProject/generateMonthlyPurchasePlan",
		method: "post",
		data
	})
}

/**
 * 批量生成月度订货计划
 * @param data
 * @returns
 */
function batchGenerateMonthlyPurchasePlan(
	idList: number[],
	idempotentToken?: string
) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchaseProject/generateMonthlyPurchasePlanBatch",
		method: "post",
		data: { idList },
		headers: {
			"idempotent-token": idempotentToken
		}
	})
}

function getStatusStatistics(query: GetPurchaseProjectListRequest) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchaseProject/statusStatistics",
		method: "get",
		params: query
	})
}

/**
 * 关闭采购项目
 * @param data closeReason?: null | string; id: number;
 * @returns
 */
function closePurchaseProject(data: any, idempotentToken?: string) {
	return request<any>({
		url: "/baseline/purchase/purchaseProject/close",
		method: "post",
		data,
		headers: {
			"idempotent-token": idempotentToken
		}
	})
}

/**
 * 关闭项目校验
 * @param data closeReason?: null | string; id: number;
 * @returns
 */
function closeCheckPurchaseProject(data: any) {
	return request<ApiResponseData<boolean | null>>({
		url: "/baseline/purchase/purchaseProject/closeCheck",
		method: "post",
		data
	})
}

export const PurchaseProjectApi = {
	getPurchaseProjectList,
	getPurchaseProject,
	addPurchaseProject,
	updatePurchaseProject,
	deletePurchaseProject,
	generateMonthlyPurchasePlan,
	batchGenerateMonthlyPurchasePlan,
	getStatusStatistics,
	closePurchaseProject,
	closeCheckPurchaseProject
}
