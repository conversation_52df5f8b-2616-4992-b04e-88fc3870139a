import { request } from "@/app/platform/utils/service"
import {
	AddPurchaseContractRequest,
	GetPurchaseContractListRequest,
	UpdPurchaseContractRequest
} from "@/app/baseline/api/defines"

// 查询合同列表
function getPurchaseContractList(query: GetPurchaseContractListRequest) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchaseContract/list",
		method: "get",
		params: query
	})
}

// 查询合同详细
function getPurchaseContract(id: string) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchaseContract/" + id,
		method: "get"
	})
}

// 新增合同
function addPurchaseContract(
	data: AddPurchaseContractRequest,
	idempotentToken?: string
) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchaseContract",
		method: "post",
		data,
		headers: { "idempotent-token": idempotentToken }
	})
}

// 修改合同
function updatePurchaseContract(
	data: UpdPurchaseContractRequest,
	_idempotentToken?: string
) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchaseContract",
		method: "put",
		data
	})
}
// 删除合同
function deletePurchaseContract(id: string) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchaseContract/" + id,
		method: "delete"
	})
}

// 合同提交审核
function publishContract(id: string, idempotentToken?: string) {
	return request<SystemApiResponseData>({
		headers: {
			"Content-Type": "multipart/form-data",
			"idempotent-token": idempotentToken
		},
		url: "/baseline/purchase/purchaseContract/publish",
		method: "post",
		data: { id }
	})
}

export const PurchaseContractApi = {
	getPurchaseContractList,
	getPurchaseContract,
	addPurchaseContract,
	updatePurchaseContract,
	deletePurchaseContract,
	publishContract
}
