import { request } from "@/app/platform/utils/service"
import { GetPurchasePlanListRequest } from "@/app/baseline/api/defines"

// 查询订货计划列表
function getPurchasePlanList(query: GetPurchasePlanListRequest) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchasePlan/list",
		method: "get",
		params: query
	})
}

// 查询订货计划详细
function getPurchasePlan(id: string) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchasePlan/" + id,
		method: "get"
	})
}

// 删除订货计划
function deletePurchasePlan(id: string) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchasePlan/" + id,
		method: "delete"
	})
}

function generateOrder(
	id: string,
	latestDeliveryDate: string,
	idempotentToken?: string
) {
	return request<SystemApiResponseData>({
		headers: {
			"Content-Type": "multipart/form-data",
			"idempotent-token": idempotentToken
		},
		url: "/baseline/purchase/purchasePlan/generatePurchaseOrder",
		method: "post",
		data: { id, latestDeliveryDate }
	})
}

function getPurchasePlanCnt(data: GetPurchasePlanListRequest): Promise<any> {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchasePlan/statusStatistics",
		method: "get",
		params: data
	})
}

/**
 * 批量生成采购订单
 * @param idList
 * @param latestDeliveryDate
 * @returns
 */
function generateOrderBatch(
	idList?: number[] | null,
	latestDeliveryDate?: null | string,
	idempotentToken?: string
) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchasePlan/generatePurchaseOrderBatch",
		method: "post",
		data: { idList, latestDeliveryDate },
		headers: {
			"idempotent-token": idempotentToken
		}
	})
}

export const PurchasePlanApi = {
	getPurchasePlanList,
	getPurchasePlan,
	deletePurchasePlan,
	generateOrder,
	getPurchasePlanCnt,
	generateOrderBatch
}
