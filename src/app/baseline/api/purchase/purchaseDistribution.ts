import { request } from "@/app/platform/utils/service"
import {
	AddPurchaseDistributionRequest,
	GetPurchaseDistributionListRequest,
	PublishPurchaseDistributionRequest,
	UpdPurchaseDistributionRequest
} from "@/app/baseline/api/defines"

// 查询采购分包列表
function getPurchaseDistributionList(
	query: GetPurchaseDistributionListRequest
) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchaseDistribution/list",
		method: "get",
		params: query
	})
}

// 查询采购分包详细
function getPurchaseDistribution(id: string) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchaseDistribution/" + id,
		method: "get"
	})
}

// 新增采购分包
function addPurchaseDistribution(
	data: AddPurchaseDistributionRequest,
	idempotentToken?: string
) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchaseDistribution",
		method: "post",
		data,
		headers: { "idempotent-token": idempotentToken }
	})
}

// 修改采购分包
function updatePurchaseDistribution(
	data: UpdPurchaseDistributionRequest,
	_idempotentToken?: string
) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchaseDistribution",
		method: "put",
		data
	})
}
// 删除采购分包
function deletePurchaseDistribution(id: string) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchaseDistribution/" + id,
		method: "delete"
	})
}

// 采购分包需求计划数量统计
function getPurchaseDistributionCnt(
	data: GetPurchaseDistributionListRequest
): Promise<any> {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchaseDistribution/bmpStatusStatistics",
		method: "get",
		params: data
	})
}

// 采购分包提交审核
function publishPurchaseDistribution(
	data: PublishPurchaseDistributionRequest,
	idempotentToken?: string
) {
	return request<SystemApiResponseData>({
		headers: {
			"Content-Type": "multipart/form-data",
			"idempotent-token": idempotentToken
		},
		url: "/baseline/purchase/purchaseDistribution/publish",
		method: "post",
		data
	})
}

export const PurchaseDistributionApi = {
	getPurchaseDistributionList,
	getPurchaseDistribution,
	addPurchaseDistribution,
	updatePurchaseDistribution,
	deletePurchaseDistribution,
	getPurchaseDistributionCnt,
	publishPurchaseDistribution
}
