import { request } from "@/app/platform/utils/service"
import {
	AddPurchaseSupplierRequest,
	GetPurchaseSupplierListRequest,
	UpdatePurchaseSupplierRequest
} from "@/app/baseline/api/defines"

// 查询供应商列表
function getPurchaseSupplierList(query: GetPurchaseSupplierListRequest) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchaseSupplier/list",
		method: "get",
		params: query
	})
}

// 查询供应商详细
function getPurchaseSupplier(id: string) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchaseSupplier/" + id,
		method: "get"
	})
}

// 新增供应商
function addPurchaseSupplier(
	data: AddPurchaseSupplierRequest,
	idempotentToken?: string
) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchaseSupplier",
		method: "post",
		data,
		headers: { "idempotent-token": idempotentToken }
	})
}

// 修改供应商
function updatePurchaseSupplier(
	data: UpdatePurchaseSupplierRequest,
	_idempotentToken?: string
) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchaseSupplier",
		method: "put",
		data
	})
}
// 删除供应商
function deletePurchaseSupplier(id: string) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchaseSupplier/" + id,
		method: "delete"
	})
}

// 供应商提交审核
function publishSupplier(id: string, idempotentToken?: string) {
	return request<SystemApiResponseData>({
		headers: {
			"Content-Type": "multipart/form-data",
			"idempotent-token": idempotentToken
		},
		url: "/baseline/purchase/purchaseSupplier/publish",
		method: "post",
		data: { id }
	})
}

export const PurchaseSupplierApi = {
	getPurchaseSupplierList,
	getPurchaseSupplier,
	addPurchaseSupplier,
	updatePurchaseSupplier,
	deletePurchaseSupplier,
	publishSupplier
}
