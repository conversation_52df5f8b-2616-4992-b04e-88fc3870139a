import { request } from "@/app/platform/utils/service"
import {
	AddPurchaseInvoiceOrderRequest,
	GetPurchaseInvoiceOrderListCanAddRequest,
	GetPurchaseInvoiceOrderListRequest
} from "@/app/baseline/api/defines"

// 查询发票订单明细列表
function getPurchaseInvoiceOrderList(
	query: GetPurchaseInvoiceOrderListRequest
) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchaseInvoiceOrder/list",
		method: "get",
		params: query
	})
}

// 查询发票可添加采购订单列表
function getPurchaseInvoiceOrderListCanAdd(
	query: GetPurchaseInvoiceOrderListCanAddRequest
) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchaseInvoice/listAddOrder",
		method: "get",
		params: query
	})
}

// 查询发票订单明细详细
function getPurchaseInvoiceOrder(id: string) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchaseInvoiceOrder/" + id,
		method: "get"
	})
}

// 新增发票订单明细
function addPurchaseInvoiceOrder(
	data: AddPurchaseInvoiceOrderRequest,
	idempotentToken?: string
) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchaseInvoiceOrder",
		method: "post",
		data,
		headers: { "idempotent-token": idempotentToken }
	})
}

// 删除发票订单明细
function deletePurchaseInvoiceOrder(id: string) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchaseInvoiceOrder/" + id,
		method: "delete"
	})
}

export const PurchaseInvoiceOrderApi = {
	getPurchaseInvoiceOrderList,
	getPurchaseInvoiceOrder,
	addPurchaseInvoiceOrder,
	deletePurchaseInvoiceOrder,
	getPurchaseInvoiceOrderListCanAdd
}
