import { request } from "@/app/platform/utils/service"
import { GetPurchaseProjectItemListRequest } from "@/app/baseline/api/defines"

// 查询采购项目明细列表
function getPurchaseProjectItemList(query: GetPurchaseProjectItemListRequest) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchaseProjectItem/list",
		method: "get",
		params: query
	})
}

// 查询采购项目明细详细
function getPurchaseProjectItem(id: string) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchaseProjectItem/" + id,
		method: "get"
	})
}

// 删除采购项目明细
function deletePurchaseProjectItem(id: string) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchaseProjectItem/" + id,
		method: "delete"
	})
}

/**
 * 更新 明细 质保期
 * @param data
 * @returns
 */
function updatePurchaseProjectItem(data: any) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchaseProjectItem/saveItem",
		method: "post",
		data
	})
}

export const PurchaseProjectItemApi = {
	getPurchaseProjectItemList,
	getPurchaseProjectItem,
	deletePurchaseProjectItem,
	updatePurchaseProjectItem
}
