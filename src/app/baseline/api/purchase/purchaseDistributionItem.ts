import { request } from "@/app/platform/utils/service"
import {
	AddPurchaseDistributionItemRequest,
	GetPurchaseDistributionItemListRequest,
	GetPurchasePlanItemListRequest,
	UpdPurchaseDistributionItemRequest
} from "@/app/baseline/api/defines"

// 查询采购分包物资明细列表
function getPurchaseDistributionItemList(
	query: GetPurchaseDistributionItemListRequest
) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchaseDistributionItem/list",
		method: "get",
		params: query
	})
}

// 查询采购分包物资明细详细
function getPurchaseDistributionItem(id: string) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchaseDistributionItem/" + id,
		method: "get"
	})
}

// 删除采购分包物资明细
function deletePurchaseDistributionItem(id: string) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchaseDistributionItem/" + id,
		method: "delete"
	})
}

// 批量新增采购分包物资明细
function addBatchPurchaseDistributionItem(
	data: AddPurchaseDistributionItemRequest[],
	idempotentToken?: string
) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchaseDistributionItem/addBatch",
		method: "post",
		data,
		headers: { "idempotent-token": idempotentToken }
	})
}

function getPurchasePlanItemList(query: GetPurchasePlanItemListRequest) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchaseDistributionItem/pagePlanPurchaseMaterialItemOutside",
		method: "get",
		params: query
	})
}
export const PurchaseDistributionItemApi = {
	getPurchaseDistributionItemList,
	getPurchaseDistributionItem,
	deletePurchaseDistributionItem,
	addBatchPurchaseDistributionItem,
	getPurchasePlanItemList
}
