import { request } from "@/app/platform/utils/service"
import {
	AddPurchaseOrderRequest,
	ConfirmArrivalRequest,
	GetPurchaseOrderListRequest,
	PublishChangeRequest,
	PublishRequest,
	updatePurchaseOrderRequest,
	UpdatePurchaseUserRequest
} from "@/app/baseline/api/defines"

// 查询采购订单列表
function getPurchaseOrderList(query: GetPurchaseOrderListRequest) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchaseOrder/list",
		method: "get",
		params: query
	})
}

// 查询采购订单详细
function getPurchaseOrder(id: string) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchaseOrder/" + id,
		method: "get"
	})
}

// 新增采购订单
function addPurchaseOrder(data: AddPurchaseOrderRequest) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchaseOrder",
		method: "post",
		data
	})
}

// 修改采购订单
function updatePurchaseOrder(data: updatePurchaseOrderRequest) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchaseOrder",
		method: "put",
		data
	})
}

/**
 * 指定采购员
 * @param data
 * @returns
 */
function updatePurchaseUser(data: UpdatePurchaseUserRequest) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchaseOrder/updatePurchaseUser",
		method: "post",
		data
	})
}

// 删除采购订单
function deletePurchaseOrder(id: string) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchaseOrder/" + id,
		method: "delete"
	})
}
function publish(data: PublishRequest, idempotentToken?: string) {
	return request<SystemApiResponseData>({
		headers: {
			"Content-Type": "multipart/form-data",
			"idempotent-token": idempotentToken
		},
		url: "/baseline/purchase/purchaseOrder/publish",
		method: "post",
		data: data
	})
}
function publishChange(data: PublishChangeRequest) {
	return request<SystemApiResponseData>({
		headers: { "Content-Type": "multipart/form-data" },
		url: "/baseline/purchase/purchaseOrder/publishChange",
		method: "post",
		data: data
	})
}

/**
 * 批量确认到货： 已废弃
 * @param data
 * @returns
 */
function confirmArrival(data: ConfirmArrivalRequest) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchaseOrder/confirmArrival",
		method: "post",
		data: data
	})
}

/**
 * 批量更新物资 采购单价、订货数量
 * @param data
 * @returns
 */
function updateBatchPurchaseOrderItem(data: Record<string, any>[]) {
	return request<any>({
		url: "/baseline/purchase/purchaseOrderItem/updateBatch",
		method: "post",
		data: data
	})
}

// 获取状态统计
function getPurchaseOrderStatusCnt(
	data: GetPurchaseOrderListRequest
): Promise<any> {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchaseOrder/statusStatistics",
		method: "get",
		params: data
	})
}

// 关闭采购订单
function closeOurchaseOrder(
	data: Record<string, any>,
	idempotentToken?: string
): Promise<any> {
	return request<any>({
		url: "/baseline/purchase/purchaseOrder/close",
		method: "post",
		data,
		headers: { "idempotent-token": idempotentToken }
	})
}

/**
 * 确认本次到货
 * @param data
 * @returns
 */
function confirmPurchaseOrderItemThisArrival(
	data: any,
	idempotentToken?: string
) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchaseOrder/confirmThisArrival",
		method: "post",
		data,
		headers: { "idempotent-token": idempotentToken }
	})
}

/**
 * 确认采购订单
 * @param id
 * @returns
 */
function confirmPurchaseOrder(id: any, idempotentToken?: string) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchaseOrder/confirm",
		method: "post",
		params: { id },
		headers: {
			"idempotent-token": idempotentToken
		}
	})
}

/**
 * 通知供应商
 * @param id
 * @returns
 */
function notifySuppliersPurchaseOrder(id: any) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchaseOrder/notifySuppliers",
		method: "post",
		params: { id }
	})
}

/**
 * 批量通知供应商
 * @param idList
 * @returns
 */
function batchNotifySuppliersPurchaseOrder(idList: number[]) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchaseOrder/notifySuppliersBatch",
		method: "post",
		data: { idList }
	})
}

/**
 * 通知供应商 下载PDF
 * @param id
 * @returns
 */
function exportPDFPurchaseOrder(id: any) {
	return request<any>({
		url: "/baseline/purchase/purchaseOrder/exportPDF",
		headers: { "Content-Type": "multipart/form-data" },
		responseType: "blob",
		method: "get",
		params: { id }
	})
}
export const PurchaseOrderApi = {
	getPurchaseOrderList,
	getPurchaseOrder,
	addPurchaseOrder,
	updatePurchaseOrder,
	updatePurchaseUser,
	deletePurchaseOrder,
	publish,
	publishChange,
	confirmArrival,
	updateBatchPurchaseOrderItem,
	getPurchaseOrderStatusCnt,
	closeOurchaseOrder,
	confirmPurchaseOrderItemThisArrival,
	confirmPurchaseOrder,
	notifySuppliersPurchaseOrder,
	batchNotifySuppliersPurchaseOrder,
	exportPDFPurchaseOrder
}
