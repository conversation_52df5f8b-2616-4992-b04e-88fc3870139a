import { request } from "@/app/platform/utils/service"
import {
	AddPurchaseInvoiceRequest,
	GetPurchaseInvoiceListByContractRequest,
	GetPurchaseInvoiceListRequest,
	UpdPurchaseInvoiceRequest
} from "@/app/baseline/api/defines"

// 查询发票列表
function getPurchaseInvoiceList(query: GetPurchaseInvoiceListRequest) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchaseInvoice/list",
		method: "get",
		params: query
	})
}
// 发票状态数量统计
function getPurchaseInvoiceStatusCount(query: GetPurchaseInvoiceListRequest) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchaseInvoice/bmpStatusStatistics",
		method: "get",
		params: query
	})
}
// 根据合同查询发票列表
function getPurchaseInvoiceListByContract(
	query: GetPurchaseInvoiceListByContractRequest
) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchaseInvoice/listByContract",
		method: "get",
		params: query
	})
}

// 查询发票详细
function getPurchaseInvoice(id: string) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchaseInvoice/" + id,
		method: "get"
	})
}

// 新增发票
function addPurchaseInvoice(
	data: AddPurchaseInvoiceRequest,
	idempotentToken?: string
) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchaseInvoice",
		method: "post",
		data,
		headers: { "idempotent-token": idempotentToken }
	})
}

// 修改发票
function updatePurchaseInvoice(
	data: UpdPurchaseInvoiceRequest,
	_idempotentToken?: string
) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchaseInvoice",
		method: "put",
		data
	})
}
// 删除发票
function deletePurchaseInvoice(id: string) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchaseInvoice/" + id,
		method: "delete"
	})
}

// 发票提交审核
function publishPurchaseInvoice(id: string, idempotentToken?: string) {
	return request<SystemApiResponseData>({
		headers: {
			"Content-Type": "multipart/form-data",
			"idempotent-token": idempotentToken
		},
		url: "/baseline/purchase/purchaseInvoice/publish",
		method: "post",
		data: { id }
	})
}

export const PurchaseInvoiceApi = {
	getPurchaseInvoiceList,
	getPurchaseInvoice,
	addPurchaseInvoice,
	updatePurchaseInvoice,
	deletePurchaseInvoice,
	getPurchaseInvoiceStatusCount,
	publishPurchaseInvoice,
	getPurchaseInvoiceListByContract
}
