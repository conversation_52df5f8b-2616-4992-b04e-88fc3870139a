import { request } from "@/app/platform/utils/service"
import {
	GetPurchasePlanItemByMaterialRequest,
	GetPurchasePlanItemListRequest
} from "@/app/baseline/api/defines"

// 查询订货计划明细列表
function getPurchasePlanItemList(query: GetPurchasePlanItemListRequest) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/plan/list",
		method: "get",
		params: query
	})
}

// 查询订货计划部门列表
function getPurchasePlanDeptList(query: GetPurchasePlanItemListRequest) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchasePlanItem/list",
		method: "get",
		params: query
	})
}

// 查询订货计划明细详细
function getPurchasePlanItem(id: string) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchasePlanItem/" + id,
		method: "get"
	})
}

// 查询订货计划物资合并列表
function getPurchasePlanItemByMaterial(
	query: GetPurchasePlanItemByMaterialRequest
) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchasePlanMaterialItem/listByMaterial",
		method: "get",
		params: query
	})
}
// 查询修改日志
function getListUpdateRecord(query: any) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchasePlanMaterialItem/listUpdateRecord",
		method: "get",
		params: query
	})
}
// 查询订货计划物资
function getPurchasePlanMaterialItem(
	query: GetPurchasePlanItemByMaterialRequest
) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchasePlanMaterialItem/list",
		method: "get",
		params: query
	})
}

// 查询提前订货物资
function listAdvanceOrderItem(query: GetPurchasePlanItemByMaterialRequest) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchasePlanMaterialItem/listAdvanceOrderItem",
		method: "get",
		params: query
	})
}

// 删除订货计划明细
function deletePurchasePlanItem(id: any) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchasePlanItem/" + id,
		method: "delete"
	})
}

// 查询需求计划物资明细-月度列表
function getPlanItemMonthlyList(query: any) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchasePlanMaterialItem/listByPlanItemId",
		method: "get",
		params: query
	})
}

function getPlanMonthlyList(query: any) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/planItemMonthly/listByPurchaseItemId",
		method: "get",
		params: query
	})
}

/**
 * 订货计划 本月应订货 下钻
 * 根据 根据采购项目年份查看月度需求量
 * @param query
 * @returns
 */
function getByPlanItemIdAndYearList(query: any) {
	return request<SystemApiResponseData>({
		url: "/baseline/plan/planItemMonthly/listByPlanItemIdAndYear",
		method: "get",
		params: query
	})
}

/***
 * 订货计划明细提交审核 前较验
 * @param id
 */
function checkPublishPurchasePlan(id: string) {
	return request<any>({
		headers: { "Content-Type": "multipart/form-data" },
		url: "/baseline/purchase/purchasePlan/checkPublish",
		method: "post",
		data: { id }
	})
}

/***
 * 订货计划明细提交审核
 * @param id
 */
function publishPurchasePlanItem(id: string, idempotentToken?: string) {
	return request<SystemApiResponseData>({
		headers: {
			"Content-Type": "multipart/form-data",
			"idempotent-token": idempotentToken
		},
		// url: "/baseline/purchase/purchasePlanItem/publish",
		url: "/baseline/purchase/purchasePlan/publish",
		method: "post",
		data: { id }
	})
}

// 批量更新物资仓库
function updateStore(ids: string, storeId: string) {
	return request<SystemApiResponseData>({
		headers: { "Content-Type": "multipart/form-data" },
		url: "/baseline/purchase/purchasePlanMaterialItem/updateStore",
		method: "post",
		data: { ids, storeId }
	})
}

export const PurchasePlanItemApi = {
	getPurchasePlanItemList,
	getPurchasePlanDeptList,
	getPurchasePlanItem,
	deletePurchasePlanItem,
	getPlanItemMonthlyList,
	checkPublishPurchasePlan,
	publishPurchasePlanItem,
	getPurchasePlanItemByMaterial,
	getListUpdateRecord,
	getPurchasePlanMaterialItem,
	listAdvanceOrderItem,
	getPlanMonthlyList,
	getByPlanItemIdAndYearList,
	updateStore
}
