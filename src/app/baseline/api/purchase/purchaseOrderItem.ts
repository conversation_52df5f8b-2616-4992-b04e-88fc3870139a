import { request } from "@/app/platform/utils/service"
import {
	AddPurchaseOrderItemRequest,
	GetPurchaseOrderItemListRequest,
	UpdatePurchaseOrderItemRequest
} from "@/app/baseline/api/defines"

// 查询采购订单明细列表
function getPurchaseOrderItemList(query: GetPurchaseOrderItemListRequest) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchaseOrderItem/list",
		method: "get",
		params: query
	})
}

// 查询采购订单明细列表
function getPurchaseOrderItemListNoPage(
	query: GetPurchaseOrderItemListRequest
) {
	return request<any>({
		url: "/baseline/purchase/purchaseOrderItem/listNoPage",
		method: "get",
		params: query
	})
}

// 查询采购订单明细详细
function getPurchaseOrderItem(id: string) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchaseOrderItem/" + id,
		method: "get"
	})
}

// 新增采购订单明细
function addPurchaseOrderItem(data: AddPurchaseOrderItemRequest) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchaseOrderItem",
		method: "post",
		data
	})
}

// 修改采购订单明细
function updatePurchaseOrderItem(data: UpdatePurchaseOrderItemRequest) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchaseOrderItem",
		method: "put",
		data
	})
}
// 删除采购订单明细
function deletePurchaseOrderItem(id: string) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchaseOrderItem/" + id,
		method: "delete"
	})
}

// 新增合并计划需求计划明细
function addBathPurchaseOrderItem(data: AddPurchaseOrderItemRequest[]) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchaseOrderItem/addBatch",
		method: "post",
		data
	})
}

// 批量更新物资仓库
function updateStore(ids: string, storeId: string) {
	return request<SystemApiResponseData>({
		headers: { "Content-Type": "multipart/form-data" },
		url: "/baseline/purchase/purchaseOrderItem/updateStore",
		method: "post",
		data: { ids, storeId }
	})
}

/**
 * 保存本次到货数量
 * @param data
 * @returns
 */
function savePurchaseOrderItemThisNum(data: any) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchaseOrderItem/saveThisNum",
		method: "post",
		data
	})
}

export const PurchaseOrderItemApi = {
	savePurchaseOrderItemThisNum,
	getPurchaseOrderItemList,
	getPurchaseOrderItemListNoPage,
	getPurchaseOrderItem,
	addPurchaseOrderItem,
	updatePurchaseOrderItem,
	deletePurchaseOrderItem,
	addBathPurchaseOrderItem,
	updateStore
}
