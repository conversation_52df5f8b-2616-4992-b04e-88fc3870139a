import { request } from "@/app/platform/utils/service"
import {
	AddPurchaseInvoiceItemRequest,
	GetPurchaseInvoiceAddItemRequest,
	GetPurchaseInvoiceItemListRequest,
	updatePurchaseInvoiceItemRequest
} from "@/app/baseline/api/defines"

// 查询发票物资明细列表
function getPurchaseInvoiceItemList(query: GetPurchaseInvoiceItemListRequest) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchaseInvoiceItem/list",
		method: "get",
		params: query
	})
}

// 查询发票物资明细详细
function getPurchaseInvoiceItem(id: string) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchaseInvoiceItem/" + id,
		method: "get"
	})
}

// 查询发票可添加物资列表
function getPurchaseInvoiceAddItem(query: GetPurchaseInvoiceAddItemRequest) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchaseInvoice/listAddMaterial",
		method: "get",
		params: query
	})
}

// 新增发票物资明细
function addPurchaseInvoiceItem(
	data: AddPurchaseInvoiceItemRequest[],
	idempotentToken?: string
) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchaseInvoiceItem",
		method: "post",
		data,
		headers: { "idempotent-token": idempotentToken }
	})
}

// 修改发票物资明细
function updatePurchaseInvoiceItem(data: updatePurchaseInvoiceItemRequest) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchaseInvoiceItem",
		method: "put",
		data
	})
}
// 删除发票物资明细 - remove
function deletePurchaseInvoiceItem(id: string) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchaseInvoiceItem/" + id,
		method: "delete"
	})
}

/**
 * 批量删除 开票物资明细
 * @param idList
 * @returns
 */
function deleteAllPurchaseInvoiceItem(idList: number[]) {
	return request<SystemApiResponseData>({
		url: "/baseline/purchase/purchaseInvoiceItem/remove",
		method: "delete",
		data: { idList }
	})
}

export const PurchaseInvoiceItemApi = {
	getPurchaseInvoiceItemList,
	getPurchaseInvoiceItem,
	addPurchaseInvoiceItem,
	updatePurchaseInvoiceItem,
	deletePurchaseInvoiceItem,
	getPurchaseInvoiceAddItem,
	deleteAllPurchaseInvoiceItem
}
