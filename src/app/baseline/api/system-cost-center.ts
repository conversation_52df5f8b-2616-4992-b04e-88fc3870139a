/**
 * 成本中心 - api
 */
import ReqUtil from "../utils/request-util"
import { PageResVo } from "../utils/types/common"
import {
	SystemCostCenterDTO,
	systemCostCenterPagedRequest,
	SystemCostCenterVo
} from "../utils/types/system-cost-center"

/**
 * 成本中心-分页查询
 */
export function getSystemCostCenterPaged(
	params: systemCostCenterPagedRequest = {}
) {
	return ReqUtil.get<PageResVo<SystemCostCenterVo>>(
		"/baseline/system/costCenter/page",
		{
			params
		}
	)
}

/**
 * 成本中心-新增
 */
export function addSystemCostCenter(
	data: SystemCostCenterDTO,
	idempotentToken?: string
) {
	return ReqUtil.post<SystemCostCenterVo>("/baseline/system/costCenter", {
		data,
		headers: { "idempotent-token": idempotentToken }
	})
}

/**
 * 成本中心-编辑
 */
export function updateSystemCostCenter(
	data: SystemCostCenterDTO,
	_idempotentToken?: string
) {
	return ReqUtil.put<SystemCostCenterVo>("/baseline/system/costCenter", {
		data
	})
}

/**
 * 成本中心-删除
 */
export function delSystemCostCenter(id: any) {
	return ReqUtil.delete<boolean | null>(`/baseline/system/costCenter/${id}`)
}

/**
 * 成本中心-查询详情
 */
export function getSystemCostCenterById(id: any) {
	return ReqUtil.get<SystemCostCenterVo>(`/baseline/system/costCenter/${id}`)
}

/**
 * 成本中心-批量启用
 */
export function batchEnableCostCenter(idList: number[] | null) {
	return ReqUtil.post<null>("/baseline/system/costCenter/batchEnable", {
		data: { idList }
	})
}

/**
 * 成本中心-批量停用
 */
export function batchDisableCostCenter(idList: number[] | null) {
	return ReqUtil.post<null>("/baseline/system/costCenter/batchDisable", {
		data: { idList }
	})
}
