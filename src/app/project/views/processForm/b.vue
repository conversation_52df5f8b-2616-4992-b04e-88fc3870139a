<script setup lang="ts">
import { reactive, ref,onMounted,inject } from 'vue'
import type { FormProps } from 'element-plus'

const labelPosition = ref<FormProps['labelPosition']>('right')
const formLabelAlign = reactive({
  name: '',
  region: '',
  type: '',
})

const props = defineProps({
	formValue: {
		type: Object,
		default: () => {},
		requeired: false
	},
})

const { updateGreet, greet} = inject('greet');  
onMounted(() => {
	//
	updateGreet(()=>{
		return new Promise((resolve, reject) => {  
        // 假设这是一个模拟的异步操作  
        setTimeout(() => {  
          if (Math.random() > 0.5) {  
            resolve('success');  
          } else {  
            reject('失败');  
          }  
        }, 1000);  
      });  
	})	
})

</script>
<template>
	<div class="box" >
		project页面bbb{{ props.formValue }}
		<el-form
			:label-position="labelPosition"
			label-width="100px"
			:model="formLabelAlign"
			style="max-width: 460px"
		>
			<el-form-item label="名称">
				<el-input v-model="props.formValue.businessTitle" />
			</el-form-item>
			<el-form-item label="组建taskId">
				<el-input v-model="props.formValue.processDefinitionName" />
			</el-form-item>
		</el-form>
	</div>
</template>
<style scoped lang="scss">
.box{
	padding: 20px ;
}

</style>
