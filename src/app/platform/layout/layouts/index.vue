<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { AppMain, NavigationBar, Logo } from "./components"
import NoticeDetail from "@/app/platform/components/NoticeDetail.vue"
import { CompanyApi } from "../../api/system/notice"
import { useUserStoreHook } from "../../store/modules/user"
import { WarningFilled } from '@element-plus/icons-vue'
import WebSocket from "@/app/platform/hooks/pfWebsocket.js"
import router from "../../../../router"

interface MenuItem {
  name?: string
  path: string
  children?: MenuItem[]
}

interface NoticeItem {
  id: string
  noticeTitle: string
  read: boolean
}

type SystemApiResponse<T = any> = {
  data: T
  code: number
  message: string
  success: boolean
}

type NoticeApiResponse = SystemApiResponse<NoticeItem[]> | NoticeItem[]

const { userInfo } = useUserStoreHook()

// 获取当前用户名确保唯一
const getCurrentUsername = () => {
  try {
    const userInfoId = userInfo.id
    return userInfoId || 'default'
  } catch {
    return 'default'
  }
}

const NOTICE_KEY = `system_notices_${getCurrentUsername()}`
const dialogEmits = ref<NoticeItem[]>([])
const drawerDetail = ref(false)
const currentNotice = ref<any>({})

// 从localStorage获取当前用户的通知(包含已读状态)
const getStoredNotices = (): NoticeItem[] => {
  const notices = localStorage.getItem(NOTICE_KEY)
  return notices ? JSON.parse(notices) : []
}

// 保存当前用户的通知到localStorage
const saveNotices = (notices: NoticeItem[]) => {
  localStorage.setItem(NOTICE_KEY, JSON.stringify(notices))
}

const socket = new WebSocket()
const toGetNoticeDialog = () => {
  (CompanyApi.getNoticeDialog() as unknown as Promise<NoticeApiResponse>).then((res) => {
    const notices = Array.isArray(res) ? res : (res?.data || [])
    const storedNotices = getStoredNotices()
    // 只保留存在于后端返回列表中的通知，并添加新通知
    const allNotices = storedNotices
      .filter(s => notices.some(n => n.id == s.id)) // 过滤掉已被删除的通知
      .concat(
        notices.filter(n => 
          !storedNotices.some(s => s.id == n.id)
        ).map(n => ({...n, read: n.read||false}))
      )
    dialogEmits.value = allNotices
    saveNotices(allNotices)
  })
	socket.subscribeEvent(
		{},
		`/topic/notice`,
		(state: string, res: any) => {
			if (state == "success") {
				const data = JSON.parse(res.body)
				const noticeCont = [...dialogEmits.value,data]
				saveNotices(noticeCont)
				dialogEmits.value = noticeCont
			}
		}
	)
}

// 标记通知为已读
const toGetNotice = (item: NoticeItem) => {
  if(!item?.id) return
  
  const notices = getStoredNotices()
  const updatedNotices = notices.map(n => 
    n.id === item.id ? {...n, read: true} : n
  )
  
  saveNotices(updatedNotices)
  dialogEmits.value = updatedNotices
  
  CompanyApi.getNoticeGet({ id: item.id }).then(res => {
    currentNotice.value = res
    drawerDetail.value = true
  })
}

// 标记通知为不再提醒
const toDismissNotice = (item: NoticeItem) => {
  if(!item?.id) return
  
  const notices = getStoredNotices()
  const updatedNotices = notices.map(n => 
    n.id === item.id ? {...n, read: true} : n
  )
  
  saveNotices(updatedNotices)
  dialogEmits.value = updatedNotices
}
onMounted(() => {
  // 初始化时显示所有通知(包括已读)
  dialogEmits.value = getStoredNotices()
  toGetNoticeDialog()
})
onUnmounted(() => {
	// 销毁websocket
	socket.destory()
})
</script>

<template>
	<div class="app-wrapper">
		<!-- 通知公告弹窗 -->
		<div class="noticAlert" v-if="dialogEmits.some(n => !n.read)">
			<div class="alertContent" v-for="item in dialogEmits.filter(n => !n.read)" :key="item.id">
				<el-icon color="#E37318" size="18">
					<WarningFilled />
				</el-icon>
				<div class="viewDetailsNoticeTitle">{{ item.noticeTitle }}</div>
				<div class="viewDetails" @click="toGetNotice(item)">查看详情</div>
				<div class="viewDetails noRemindAgain" @click="toDismissNotice(item)">不再提醒</div>
			</div>
		</div>
		<!-- 头部导航栏和标签栏 -->
		<div class="fixed-header layout-header">
			<Logo class="logo" />
			<div class="content">
				<NavigationBar />
		</div>
		<UserInfoBar />
	</div>
	<!-- 引入公告查看详情弹窗 -->
	<NoticeDetail v-model="drawerDetail" :notice-data="currentNotice" @close="drawerDetail = false" />
		<!-- 主容器 -->
		<div class="main-container">
			<!-- 左侧边栏 -->
			<Sidebar class="sidebar-container" />
			<!-- 页面主体内容 -->
			<AppMain class="app-main" />
		</div>
	</div>
</template>

<style lang="scss" scoped>
@import "@/styles/mixins.scss";
$transition-time: 0.35s;

.noticAlert {
	position: fixed;
	top: 0;
	left: 0;
	width: 100vw;
	height: 100vh;
	background: transparent;
	pointer-events: auto;
	z-index: 1003;

	/* 确保在页面内容上方 */
	.alertContent {
		width: calc(100% - 20px);
		height: 54px;
		background-color: rgba(252, 243, 230, 0.95);
		border-width: 1px;
		border-color: rgba(245, 155, 34,0.95);
		border-style: solid;
		border-radius: 3.5px;
		transition: unset;
		top: 60px;
		margin-bottom: 10px;
		left: 10px;
		z-index: 1003;
		display: flex;
		align-items: center;
		font-size: 14px;
		color: rgb(51, 51, 51);
		position: relative;
		i{
			margin:0 8px 0 15px;
		}
		.viewDetailsNoticeTitle{
			max-width: calc(100% - 220px);
		}
		.viewDetails {
			font-size: 14px;
			font-family: "PingFang SC", BlinkMacSystemFont, -apple-system;
			text-align: left;
			color: rgb(0, 82, 217);
			margin-left: 20px;
			cursor: pointer;
		}

		.noRemindAgain {
			position: absolute;
			right: 20px;
		}
	}
}

.app-wrapper {
	@include clearfix;
	width: 100%;
}

.fixed-header {
	position: fixed;
	top: 0;
	z-index: 1004;
	width: 100%;
	display: flex;

	.content {
		flex: 1;
		margin-left: 15px;
	}
}

.layout-header {
	padding: 0 10px;
	box-sizing: border-box;
	width: 100%;
	background: var(--pitaya-header-bg-color);
}

.main-container {
	min-height: 100%;
}

.sidebar-container {
	transition: width $transition-time;
	width: var(--pitaya-sidebar-width) !important;
	position: fixed;
	top: 0;
	left: 0;
	bottom: 0;
	z-index: 1001;
	overflow: hidden;
	padding-top: var(--pitaya-navigationbar-height);
}

.app-main {
	transition: padding-left $transition-time;
	padding-top: var(--pitaya-navigationbar-height);
	padding-left: var(--pitaya-sidebar-width);
	height: 100vh;
	overflow: auto;
}

.hideSidebar {
	.sidebar-container {
		width: var(--pitaya-sidebar-hide-width) !important;
	}

	.app-main {
		padding-left: var(--pitaya-sidebar-hide-width);
	}
}

.hasTagsView {
	.sidebar-container {
		padding-top: var(--pitaya-header-height);
	}

	.app-main {
		padding-top: var(--pitaya-header-height);
	}
}
</style>
