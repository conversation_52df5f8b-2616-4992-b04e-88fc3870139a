<script lang="ts">
export default {
	name: "TopBar"
}
</script>
<script lang="ts" setup>
import { usePermissionStore } from "@/app/platform/store/modules/permission"

const permissionStore = usePermissionStore()

const route = useRoute()
const router = useRouter()

const redirectedRoute = ref("")
const onHorizontalMenuClick = (e: string) => {
	const curRoute = permissionStore.routes.find((route) => {
		return route.path === e
	})
	if (curRoute?.children && curRoute?.children.length > 0) {
		//二级菜单处理
		if (
			curRoute?.children[0].children &&
			curRoute?.children[0].children.length
		) {
			//如果index=0，有三级菜单
			redirectedRoute.value = curRoute?.children[0].children[0].name
		} else {
			//如果没有三级菜单
			redirectedRoute.value = curRoute.children[0].name
		}

		router.push({
			name: redirectedRoute.value
		})
	}
}

const activeMenuTop = computed(() => {
	const menuNow = "/" + route.meta.activeMenuTop
	return menuNow ? menuNow : route.path
})
</script>

<template>
	<div class="navigation-bar">
		<el-menu
			:default-active="activeMenuTop"
			class="el-menu-horizontal"
			mode="horizontal"
			background-color="#1d4a98"
			text-color="#fff"
			active-text-color="#1d4a98"
			:ellipsis="false"
			@select="onHorizontalMenuClick"
		>
			<BarItem
				v-for="item in permissionStore.routes"
				:key="item.path"
				:base-path="route.path"
				:item="item"
			/>
		</el-menu>
	</div>
</template>

<style lang="scss" scoped>
.navigation-bar {
	height: var(--pitaya-navigationbar-height);
	overflow: hidden;
	.el-menu-horizontal {
		height: 100%;
		border-bottom: none;
	}
	::v-deep(.el-menu) {
		.el-menu-item:not(.is-active):hover {
			background-color: var(--pitaya-header-bg-color);
		}
		.el-menu-item {
			border-bottom: none;
		}
		.is-active {
			background: #fff;
		}
	}
}
</style>
@/app/platform/store/modules/permission
