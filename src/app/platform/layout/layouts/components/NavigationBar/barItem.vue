<script lang="ts">
export default {
	name: "TopBarItem"
}
</script>
<script lang="ts" setup>
import { type RouteRecordRaw } from "vue-router"
import path from "path-browserify"
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome"

interface Props {
	item: RouteRecordRaw
	basePath: string
}
const props = defineProps<Props>()

const resolvePath = (routePath: string) => {
	switch (true) {
		case isExternal(routePath):
			return routePath
		case isExternal(props.basePath):
			return props.basePath
		default:
			return path.resolve(props.basePath, routePath)
	}
}
</script>
<template>
	<el-menu-item
		v-if="!props.item.meta?.hidden"
		:index="resolvePath(props.item.path)"
	>
		<font-awesome-icon
			v-if="props.item.meta?.awesomeIcon"
			:icon="props.item.meta.awesomeIcon"
		/>
		<SvgIcon
			v-else-if="props.item.meta?.svgIcon"
			:name="props.item.meta.svgIcon"
		/>
		<component
			v-else-if="props.item.meta?.elIcon"
			:is="props.item.meta.elIcon"
			class="el-icon"
		/>
		<template v-if="props.item.meta?.title" #title>
			<span class="meta-title">
				{{ props.item.meta.title }}
			</span>
		</template>
	</el-menu-item>
</template>

<style lang="scss" scoped>
.meta-title {
	margin-left: 10px;
	font-weight: bold;
}
</style>
