<script lang="ts" setup>

const title = import.meta.env.VITE_SYS_TITLE
const logoSrc = import.meta.env.VITE_SYS_LOGO

</script>

<template>
	<div class="layout-logo-container">
		<span class="logo" v-if="logoSrc">
			<img class="logo-img" :src="logoSrc" />
		</span>
		<span class="logo" v-else>LOGO</span>
		<span style="font-weight: bold;">{{ title }}</span>
	</div>
</template>

<style lang="scss" scoped>
.layout-logo-container {
	display: flex;
	align-items: center;
	position: relative;
	height: var(--pitaya-header-height);
	text-align: center;
	overflow: hidden;
	font-size: 14px!important;
	font-weight: bold!important;
	color: #fff;
	.logo {
		font-size:26px;
		margin-right: 10px;
		font-weight: bolder;
		display: flex;
		align-items: center;
		.logo-img {
			width: auto;
			height: 100%;
			max-height: 37px;
			max-width: 200px;
			border-radius:3px;
		}
	}
}
</style>
@/app/platform/store/modules/app
