<script lang="ts" setup>
import { computed } from "vue"
import { useRoute } from "vue-router"
import { useSettingsStore } from "@/app/platform/store/modules/settings"
import place from "@/app/platform/layout/layouts/components/Place/index.vue"
const settingsStore = useSettingsStore()
const { showPlace } = storeToRefs(settingsStore)

const route = useRoute()

const key = computed(() => {
	// 返回 route.path 和 route.fullPath 有着不同的效果，大多数时候 path 更通用
	return route.path
})

const loading = ref(false)

// watchEffect(() => {
// 	if (key.value) {
// 		loading.value = true
// 		// 菜单切换时，延迟 500ms 关闭 loading
// 		// 防止切换过程后，内容区域白屏影响观感
// 		setTimeout(() => {
// 			loading.value = false
// 		}, 300)
// 	}
// })
</script>

<template>
	<section class="app-main">
		<place v-if="showPlace" />
		<div class="app-scrollbar" v-loading="loading">
			<router-view v-slot="{ Component }">
				<transition name="fade" mode="out-in">
					<component :is="Component" :key="key" />
				</transition>
			</router-view>
		</div>
	</section>
</template>

<style lang="scss" scoped>
@import "@/styles/mixins.scss";

.app-main {
	width: 100%;
	background-color: var(--pitaya-body-bg-color);
	display: flex;
	flex-direction: column;
}

.app-scrollbar {
	height: 0;
	flex: 1;
	height: 100%;
	overflow: auto;
	@include scrollbar;
	display: flex;
	flex-direction: column;
}
</style>
@/app/platform/store/modules/settings
