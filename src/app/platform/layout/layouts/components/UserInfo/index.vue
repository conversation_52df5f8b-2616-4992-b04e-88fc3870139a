<script lang="ts" setup>
import { storeToRefs } from "pinia"
import { useUserStore } from "@/app/platform/store/modules/user"

const userStore = useUserStore()
const { userInfo } = storeToRefs(userStore)

// #region 修改权限
const showChangeRoleDrawer = ref<boolean>(false)
const changeRDrawerTitle = {
	name: ["个人信息"],
	icon: ["fas", "square-share-nodes"]
}

</script>

<template>
	<div class="common-from-wrapper common-from-only">
		<Title :title="changeRDrawerTitle" />
		<div class="common-from-group">
			<el-scrollbar>
				<ul class="drawer-select-role-wrapper">
					<li class="drawer-li">
						<span class="c-3 w40">姓名：</span>
						<span class="c-6"
							>{{ userInfo.realName || "---" }}&nbsp;&nbsp;</span
						>
						<el-tag
							v-if="userInfo?.outsourcedPersonnel"
							class="state-txt"
							:class="[
								userInfo.outsourcedPersonnel ? 'is-enable' : 'no-enable'
							]"
						>
							{{ userInfo.outsourcedPersonnel ? "委外" : "否" }}
						</el-tag>
					</li>
					<li class="drawer-li">
						<span class="c-3 w40">公司：</span>
						<span class="c-6">{{ userInfo.companyName || "---" }}</span>
					</li>
					<li class="drawer-li">
						<span class="c-3 w40">部门：</span>
						<span class="c-6">{{ userInfo.orgAllName || "---" }}</span>
					</li>
					<li class="drawer-li">
						<span class="c-3 w40">岗位：</span>
						<span class="c-6">{{ userInfo.station || "---" }}</span>
					</li>
					<li class="drawer-li">
						<span class="c-3 w40">工号：</span>
						<span class="c-6">{{ userInfo.employeeNumber || "---" }}</span>
					</li>
					<li class="drawer-li">
						<span class="c-3 w40">手机：</span>
						<span class="c-6">{{ userInfo.phone || "---" }}</span>
					</li>

					<li
						class="drawer-li"
						v-if="userInfo && userInfo?.roleListAll.length > 0"
					>
						<span class="c-3 w40">角色：</span>
						<span class="role">
							<span
								class="c-6"
								style="text-align: left"
								v-for="item in userInfo.roleListAll"
								>{{ item.roleName || "---" }}</span
							>
						</span>
					</li>
					<li
						class="drawer-li"
						v-if="userInfo && userInfo?.teamdList.length > 0"
					>
						<span class="c-3 w40">班组：</span>
						<span class="role">
							<span
								class="c-6"
								style="text-align: left"
								v-for="item in userInfo.teamdList"
								>{{ item.teamName || "---" }}</span
							>
						</span>
					</li>
				</ul>
			</el-scrollbar>
		</div>
	</div>
</template>

<style lang="scss" scoped>

.drawer-select-role-wrapper {
	padding: 10px 10px 0 10px;
	.drawer-li {
		padding: 10px 0;
		width: 100%;
		line-height: 20px;
		display: flex;
		align-items: baseline;
		border-bottom: 1px solid #f4f4f4;
		span {
			font-size: var(--pitaya-fs-12);
		}
		.w40 {
			width: 40px;
			min-width:40px;
		}
		.c-3 {
			color: #333;
		}
		.c-6 {
			color: #666;
		}
		.role {
			display: flex;
			flex-direction: column;
		}
	}
	.drawer-li:nth-child(1) {
		margin-top: -10px;
	}
}
</style>
@/app/platform/store/modules/user
