<script lang="ts" setup>
import { storeToRefs } from "pinia"
import { useUserStore } from "@/app/platform/store/modules/user"
import UserinfoDrawer from "../UserInfo/index.vue"

const userStore = useUserStore()
const { userInfo } = storeToRefs(userStore)

// #region 修改权限
const showChangeRoleDrawer = ref<boolean>(false)
const changeRDrawerTitle = {
	name: ["个人信息"],
	icon: ["fas", "square-share-nodes"]
}

const changeUserRole = () => {
	showChangeRoleDrawer.value = true
}
</script>

<template>
	<div class="layout-logo-container" @click="changeUserRole">
		<div class="logo">
			<img
				v-if="userInfo.sex == 1"
				class="avatar-img"
				src="@/assets/images/defaultHeadBoy.png"
			/>
			<img
				v-else
				class="avatar-img"
				src="@/assets/images/defaultHeadGirl.png"
			/>
		</div>
		<div class="logo-txt txt-1">
			{{ userInfo.station || "---" }}
		</div>
		<div class="logo-txt">{{ userInfo.realName || "" }}</div>
	</div>
	<Drawer
		:size="310"
		:destroyOnClose="true"
		v-model:drawer="showChangeRoleDrawer"
	>
		<UserinfoDrawer></UserinfoDrawer>
	</Drawer>
</template>

<style lang="scss" scoped>
.layout-logo-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	position: relative;
	text-align: center;
	overflow: hidden;
	font-size: 16px;
	font-weight: 600;
	color: #fff;
	padding: 22px 5px 19px;
	cursor: pointer;
	border-bottom: 1px solid var(--pitaya-border-color);
	.logo {
		width: 80px;
		height: 80px;
		border-radius: 100%;
		overflow: hidden;
		display: flex;
		align-items: center;
		justify-content: center;
		.avatar-img {
			width: 100%;
			height: 100%;
		}
		margin-bottom: 7px;
	}
	.logo-txt {
		line-height: 20px;
		font-size: var(--pitaya-fs-12);
		color: #1c4a98;
		&.txt-1 {
			font-size: 14px;
		}
	}
}
.drawer-select-role-wrapper {
	padding: 10px 10px 0 10px;
	.drawer-li {
		padding: 10px 0;
		width: 100%;
		line-height: 20px;
		display: flex;
		align-items: baseline;
		border-bottom: 1px solid #f4f4f4;
		span {
			font-size: var(--pitaya-fs-12);
		}
		.w40 {
			width: 40px;
		}
		.c-3 {
			color: #333;
		}
		.c-6 {
			color: #666;
		}
		.role {
			display: flex;
			flex-direction: column;
		}
	}
	.drawer-li:nth-child(1) {
		margin-top: -10px;
	}
}
</style>
@/app/platform/store/modules/user
