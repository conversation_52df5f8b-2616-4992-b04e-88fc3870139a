<script lang="ts">
export default {
	name: "SideBarItem"
}
</script>
<script lang="ts" setup>
import { computed } from "vue"
import { type RouteRecordRaw } from "vue-router"
import SidebarItemLink from "./SidebarItemLink.vue"
import { isExternal } from "@/app/platform/utils/validate"
import path from "path-browserify"
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome"

interface Props {
	item: RouteRecordRaw
	isCollapse?: boolean
	isFirstLevel?: boolean
	basePath?: string
}

const props = withDefaults(defineProps<Props>(), {
	isCollapse: false,
	isFirstLevel: true,
	basePath: ""
})

/** 解析路径 */
const resolvePath = (routePath: string) => {
	switch (true) {
		case isExternal(routePath):
			return routePath
		case isExternal(props.basePath):
			return props.basePath
		default:
			return routePath
	}
}
</script>

<template>
	<div
		v-if="props.item.meta?.alwaysShow"
		:class="{
			'simple-mode': props.isCollapse,
			'first-level': props.isFirstLevel
		}"
	>
		<template
			v-if="
				!item.children ||
				item.children.filter((child) => child.meta?.alwaysShow).length === 0
			"
		>
			<SidebarItemLink v-if="item.meta" :to="resolvePath(item.path)">
				<el-menu-item :index="resolvePath(item.path)">
					<span class="slide-icon-wrapper">
						<font-awesome-icon
							v-if="item.meta.awesomeIcon"
							:icon="item.meta.awesomeIcon"
						/>
						<SvgIcon v-else-if="item.meta.svgIcon" :name="item.meta.svgIcon" />
						<component
							v-else-if="item.meta.elIcon"
							:is="item.meta.elIcon"
							class="el-icon"
						/>
					</span>
					<template v-if="item.meta.title" #title>
						{{ item.meta.title }}
					</template>
				</el-menu-item>
			</SidebarItemLink>
		</template>
		<el-sub-menu v-else :index="resolvePath(props.item.path)" teleported>
			<template #title>
				<span class="slide-icon-wrapper">
					<font-awesome-icon
						v-if="
							props.item.meta &&
							props.item.meta.awesomeIcon &&
							props.item.meta.awesomeIcon.length > 0
						"
						:icon="props.item.meta.awesomeIcon"
					/>
					<SvgIcon
						v-else-if="props.item.meta?.svgIcon"
						:name="props.item.meta.svgIcon"
					/>
					<component
						v-else-if="props.item.meta?.elIcon"
						:is="props.item.meta.elIcon"
						class="el-icon"
					/>
				</span>
				<span v-if="props.item.meta?.title">{{ props.item.meta.title }}</span>
			</template>
			<template v-if="props.item.children">
				<sidebar-item
					v-for="child in props.item.children"
					:key="child.path"
					:item="child"
					:is-collapse="props.isCollapse"
					:is-first-level="false"
					:base-path="resolvePath(child.path)"
				/>
			</template>
		</el-sub-menu>
	</div>
</template>

<style lang="scss" scoped>
.svg-icon {
	min-width: 1em;
	margin-right: var(--pitaya-fs-12);
	font-size: 18px;
}

.el-icon {
	width: 1em;
	margin-right: var(--pitaya-fs-12);
	font-size: 18px;
}

:deep(.el-menu-item) {
	position: relative;
	padding: 0 10px 0 40px !important;
	font-size: var(--pitaya-fs-12);
}

:deep(.el-sub-menu__title) {
	padding: 0 10px 0 40px !important;
	font-size: var(--pitaya-fs-12);
	.el-sub-menu__icon-arrow {
		right: 15px;
	}
}

.simple-mode {
	&.first-level {
		:deep(.el-sub-menu) {
			.el-sub-menu__icon-arrow {
				display: none;
			}
			span {
				visibility: hidden;
			}
		}
	}
}
.slide-icon-wrapper {
	display: flex;
	align-items: center;
	justify-content: center;
	position: absolute;
	left: 15px;
	top: 50%;
	transform: translateY(-50%);
	width: 15px;
	height: 100%;
	font-size: var(--pitaya-fs-12);
}

.el-sub-menu__title * {
	vertical-align: unset !important;
}
</style>
