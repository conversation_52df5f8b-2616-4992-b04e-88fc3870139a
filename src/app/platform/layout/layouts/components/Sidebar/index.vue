<script lang="ts">
export default {
	name: "Sidebar"
}
</script>
<script lang="ts" setup>
import { computed } from "vue"
import {
	useRoute,
	useRouter,
	type RouteLocationNormalizedLoaded
} from "vue-router"
import { storeToRefs } from "pinia"
import { usePermissionStore } from "@/app/platform/store/modules/permission"
import { useSettingsStore } from "@/app/platform/store/modules/settings"
import SidebarItem from "./SidebarItem.vue"
import Avatar from "../Avatar/index.vue"
import { getCssVariableValue } from "@/app/platform/utils/common"
import { getVersion } from "@/app/platform/api/login"

const pitayaSidebarMenuBgColor = getCssVariableValue(
	"--pitaya-sidebar-menu-bg-color"
)
const pitayaSidebarMenuTextColor = getCssVariableValue(
	"--pitaya-sidebar-menu-text-color"
)
const pitayaSidebarMenuActiveTextColor = getCssVariableValue(
	"--pitaya-sidebar-menu-active-text-color"
)

const route = useRoute()
const router = useRouter()
const permissionStore = usePermissionStore()
const settingsStore = useSettingsStore()
const versionList = ref({})

const { layoutMode, showAvatar } = storeToRefs(settingsStore)

const activeMenuLeft = computed(() => {
	const {
		meta: { activeMenuLeft },
		path
	} = route
	return activeMenuLeft ? activeMenuLeft : path
})

const isCollapse = ref<boolean>(false)
const isLeft = computed(() => layoutMode.value === "left")
const isAvatar = computed(() => isLeft.value && showAvatar.value)
const backgroundColor = computed(() =>
	isLeft.value ? pitayaSidebarMenuBgColor : undefined
)
const textColor = computed(() =>
	isLeft.value ? pitayaSidebarMenuTextColor : undefined
)
const activeTextColor = computed(() =>
	isLeft.value ? pitayaSidebarMenuActiveTextColor : undefined
)

let sidebarList = reactive<any>([])
/**
 * 初始化时加载版本号
 *
 * 为基线时，解开注释功能
 */
onMounted(() => {
	getVersion().then((res: any) => {
		versionList.value = res
	})
})

watch(
	() => router.currentRoute.value,
	(nVal: RouteLocationNormalizedLoaded) => {
		const activeMenuTop = "/" + nVal.fullPath.split("/")[1]
		sidebarList = permissionStore.routes.filter((item: { path: string }) => {
			return item.path === activeMenuTop
		})
	},
	{
		immediate: true
	}
)
</script>

<template>
	<div class="sidebar-wrapper">
		<Avatar v-if="isAvatar" />
		<div class="sidebar-group">
			<el-scrollbar wrap-class="scrollbar-wrapper">
				<el-menu
					:default-active="activeMenuLeft"
					:collapse="isCollapse"
					:background-color="backgroundColor"
					:text-color="textColor"
					:active-text-color="activeTextColor"
					:unique-opened="true"
					:collapse-transition="false"
					mode="vertical"
				>
					<SidebarItem
						v-for="route in sidebarList[0].children"
						:key="route.path"
						:item="route"
						:base-path="
							route.meta ? `${route.meta.activeMenuTop}/${route.path}` : ''
						"
						:is-collapse="isCollapse"
					/>
				</el-menu>
			</el-scrollbar>
		</div>
		<div class="version-txt" v-if="versionList && versionList?.project">Version {{ versionList?.project }}</div>
	</div>
</template>

<style lang="scss" scoped>
@mixin tip-line {
	&::before {
		content: "";
		position: absolute;
		top: 0;
		left: 0;
		width: 3px;
		height: 100%;
		background-color: var(--pitaya-sidebar-menu-tip-line-bg-color);
	}
}

.sidebar-wrapper {
	display: flex;
	flex-direction: column;
	border-right: 1px solid var(--pitaya-border-color);
	background-color: var(--pitaya-model-bg-color);
	.sidebar-group {
		height: 0;
		flex: 1;
		overflow: hidden;
	}
	.version-txt {
		font-size: 10px;
		font-weight: bold;
		line-height: 30px;
		color: var(--pitaya-place-icon-color);
		display: flex;
		align-items: center;
		justify-content: center;
	}
}

.el-scrollbar {
	height: 100%;
	:deep(.scrollbar-wrapper) {
		// 限制水平宽度
		overflow-x: hidden !important;
		.el-scrollbar__view {
			padding-top: 0px;
		}
	}
	// 滚动条
	:deep(.el-scrollbar__bar) {
		&.is-horizontal {
			// 隐藏水平滚动条
			display: none;
		}
	}
}

.el-menu {
	border: none;
	min-height: 100%;
	width: 100% !important;
	margin-top: 10px;
}

:deep(.el-menu-item),
:deep(.el-sub-menu__title),
:deep(.el-sub-menu .el-menu-item) {
	height: var(--pitaya-sidebar-menu-item-height);
	line-height: var(--pitaya-sidebar-menu-item-height);
	&.is-active,
	&:hover {
		background-color: var(--pitaya-sidebar-menu-hover-bg-color);
	}
	display: block;
	* {
		vertical-align: middle;
	}
}

:deep(.el-menu-item) {
	&.is-active {
		@include tip-line;
	}
}

.el-menu--collapse {
	:deep(.el-sub-menu) {
		&.is-active {
			.el-sub-menu__title {
				@include tip-line;
			}
		}
	}
}
</style>
@/app/platform/store/modules/permission@/app/platform/store/modules/settings
