<script lang="ts">
export default {
	name: "place"
}
</script>
<script lang="ts" setup>
import { type RouteLocationNormalizedLoaded } from "vue-router"
import { usePermissionStore } from "@/app/platform/store/modules/permission"
import XEUtils from "xe-utils"
import {useBackBtnStore} from "@/app/platform/store/modules/backBtn";

const backBtnStore  = useBackBtnStore();
let { backBtnState } = storeToRefs(backBtnStore)
const permissionStore = usePermissionStore()
const router = useRouter()
const placeDesc = ref<string[]>([])
watch(
	() => router.currentRoute.value,
	(nVal: RouteLocationNormalizedLoaded) => {
		const currentRouteFind = XEUtils.findTree(
			permissionStore.routes,
			(item) => item.name === nVal.name
		)
		if (currentRouteFind && currentRouteFind.nodes) {
			placeDesc.value = currentRouteFind.nodes.map(
				(item) => item.meta?.title || ""
			)
			return
		}
	},
	{
		immediate: true
	}
)
let showBack = ref(false);
const awesomeHomeIcon = ["fas", "house"]
const awesomeBackIcon = ["fas", "reply"]

watchEffect(() => {
	if(backBtnState.value.name){
		showBack.value = true;
	}else{
		showBack.value = false;
	}
})

const goBack = () => {
	if(backBtnState.value.name !== ""){
		backBtnState.value.state = true;
		console.log(new Date().getTime());
		return;
	}
	router.go(-1)
}
</script>

<template>
	<div class="place-wrapper">
		<font-awesome-icon class="place-icon" :icon="awesomeHomeIcon" />
		<span>当前位置：</span>
		<div class="place-list" v-if="placeDesc && placeDesc.length">
			<template v-for="(place, index) in placeDesc" :key="index">
				<span>{{ place }}</span>
				<span class="place-space" v-if="index < placeDesc.length - 1">></span>
			</template>
		</div>
		<div class="place-go-back" @click="goBack" v-if="showBack">
			<font-awesome-icon class="place-icon" :icon="awesomeBackIcon" />
			<span>返回上级</span>
		</div>
	</div>
</template>

<style lang="scss" scoped>
.place-wrapper {
	height: var(--pitaya-place-height);
	background-color: var(--pitaya-model-bg-color);
	display: flex;
	align-items: center;
	font-size: var(--pitaya-place-font-size);
	color: var(--pitaya-place-font-color);
	border-bottom: 1px solid #ccc;
	.place-icon {
		color: var(--pitaya-place-icon-color);
		margin: 0 10px;
	}
	.place-space {
		padding: 0 5px;
	}
	.place-go-back {
		position: absolute;
		right: 10px;
		top: 50%;
		transform: translateY(-50%);
		z-index: 1;
		display: flex;
		align-items: center;
		justify-content: flex-end;
		cursor: pointer;
		color: var(--pitaya-place-icon-color);
	}
}
</style>
@/app/platform/store/modules/permission
