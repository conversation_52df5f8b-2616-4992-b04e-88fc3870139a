import { request } from "@/app/platform/utils/service"
import { baselineApi } from "@/app/platform/api/baseline"
import { platformApi } from "@/app/platform/api/platform"

import type * as Login from "./types/login"
import { userInfo } from "./userInfoDemo"

/** 获取验证码key */
export function getLoginCodeApi() {
	return request<Login.LoginCodeResponseData>({
		url: platformApi.LoginCode,
		method: "get"
	})
}

/** 获取验证码图片 */
export function getCodeImgApi(params: anyKey) {
	return request<BlobPart>({
		responseType: "arraybuffer",
		url: platformApi.restCaptcha,
		method: "get",
		params
	})
}

/** 登录并返回 Token */
export function loginApi({
	username,
	password,
	code,
	unionCode,
	clientType,
	wxcpCode
}: Login.LoginRequestData) {
	return request<SystemApiResponseData>({
		url: platformApi.login,
		method: "post",
		data: { username, password, clientType, wxcpCode },
		headers: {
			"BASE-REST-CAPTCHA-UNION-CODE": unionCode,
			"BASE-REST-CAPTCHA-STRING": code,
			"Client-Type": clientType
		}
	})
}
/** 获取用户详情 */
export function getUserInfoApi() {
	return request<SystemApiResponseData>({
		url: platformApi.getUserInfo,
		method: "get"
	})
}

/** 退出登录 */
export function logoutApi(params:anyKey) {
	return request<SystemApiResponseData>({
		url: platformApi.logout,
		method: "get",
		params
	})
}

/** 修改密码 */
export function changePasswordApi(data: anyKey) {
	return request<SystemApiResponseData>({
		url: platformApi.updatePassword,
		headers: { "Content-Type": "multipart/form-data" },
		method: "post",
		data
	})
}

/** 流程新建实例  该接口后续放到流程新建页面*/
export function startProcessInstance(data: any) {
	return request({
		url: platformApi.startProcessInstance,
		method: "post",
		data
	})
}

/** 版本号接口*/
export function getVersion() {
	return request({
		url: baselineApi.getVersion,
		method: "get"
	})
}

/** 验证二维码是否展示接口*/
export function systemParamenter(params:anyKey) {
	return request({
		url: platformApi.systemParamenter,
		method: "get",
		params
	})
}
/** 关闭浏览器*/
export function closeTimeLog() {
	return request({
		url: platformApi.closeTimeLog,
		method: "get",
	})
}
