export interface LoginRequestData {
	/** admin 或 editor */
	username: string
	/** 密码 */
	password: string
	/** 验证码 */
	code: string
	/** 验证码key */
	unionCode: string
	/** 登陆接口 必须添加 clientType字段,1表示PC客户端;2表示DPA客户端; */
	clientType: number
	/** 企业微信code */
	wxcpCode:string
}

export type LoginCodeResponseData = { unionCode: string }

export type LoginResponseData = ApiResponseLoginData<{}>

export type UserInfoResponseData = ApiResponseData<{
	username: string
	roles: string[]
	menuRoles: string[]
	clientType: string
}>
