export const userInfo = {
	id: "4028098189d4cd990189d4cdba240000",
	realName: "管理员",
	userName: "admin",
	companyId: 16,
	companyName: "集团公司",
	companyType: 1,
	orgId: null,
	defaultRoleId: "ROLE_ADMIN",
	station: null,
	admin: true,
	orgName: null,
	teamOrgId: null,
	majorId: null,
	locationId: null,
	teamdList: [],
	roleList: [
		{
			roloId: "ROLE_ADMIN",
			roleName: "超级管理员",
			privillegesList: [
				{
					privillegId: 136,
					privillegName: "终端授权-编辑",
					privillegPurview: "btn-role:auth-编辑",
					privilegeType: "visible"
				},
				{
					privillegId: 137,
					privillegName: "线路管理-移除",
					privillegPurview: "btn-role:-移除",
					privilegeType: "visible"
				},
				{
					privillegId: 138,
					privillegName: "线路管理-新增线路",
					privillegPurview: "btn-role:line-新增线路",
					privilegeType: "visible"
				},
				{
					privillegId: 139,
					privillegName: "线路管理-编辑",
					privillegPurview: "btn-role:line-编辑",
					privilegeType: "visible"
				},
				{
					privillegId: 183,
					privillegName: "公司管理-新增公司",
					privillegPurview: "btn-role:-新增公司",
					privilegeType: "invisible"
				},
				{
					privillegId: 192,
					privillegName: "专业管理-新增专业",
					privillegPurview: "btn-role:-新增专业",
					privilegeType: "invisible"
				},
				{
					privillegId: 193,
					privillegName: "专业管理-新增专业",
					privillegPurview: "btn-role:-新增专业",
					privilegeType: "invisible"
				}
			]
		}
	],
	menus: [
		{
			path: "/platform",
			name: "platform",
			component: "/layout/layouts/index.vue",
			meta: {
				title: "系统",
				awesomeIcon: ["fas", "gear"],
				roles: ["top-menu-role:"],
				alwaysShow: true
			},
			children: [
				{
					path: "/platform/system/line",
					name: "systemLine",
					component: "/views/system/line.vue",
					meta: {
						title: "线路管理",
						awesomeIcon: ["fas", "rectangle-list"],
						activeMenuTop: "platform",
						roles: ["first-menu-role:"],
						alwaysShow: true
					}
				},
				{
					path: "/platform/system/company",
					name: "systemCompany",
					component: "/views/system/company.vue",
					meta: {
						title: "公司管理",
						awesomeIcon: ["fas", "city"],
						activeMenuTop: "platform",
						roles: ["first-menu-role:"],
						alwaysShow: true
					}
				},
				{
					path: "/platform/system/profession",
					name: "systemProfession",
					component: "/views/system/profession.vue",
					meta: {
						title: "专业管理",
						awesomeIcon: ["fas", "glasses"],
						activeMenuTop: "platform",
						roles: ["first-menu-role:"],
						alwaysShow: true
					}
				},
				{
					path: "/platform/system/department",
					name: "systemDepartment",
					component: "/views/system/department.vue",
					meta: {
						title: "部门管理",
						awesomeIcon: ["fas", "sitemap"],
						activeMenuTop: "platform",
						roles: ["first-menu-role:"],
						alwaysShow: true
					}
				},
				{
					path: "/platform/system/baseUser",
					name: "systemBaseUser",
					component: "/views/system/account.vue",
					meta: {
						title: "账号管理",
						awesomeIcon: ["fas", "user"],
						activeMenuTop: "system",
						roles: ["first-menu-role:"],
						alwaysShow: true
					}
				},
				{
					path: "/platform/system/baseRole",
					name: "systemBaseRole",
					component: "/views/system/baseRole.vue",
					meta: {
						title: "角色管理",
						awesomeIcon: ["fas", "user-check"],
						activeMenuTop: "system",
						roles: ["first-menu-role:"],
						alwaysShow: true
					}
				},
				{
					path: "/platform/system/requestMap",
					name: "systemRequestMap",
					component: "/views/system/requestMap.vue",
					meta: {
						title: "权限管理",
						awesomeIcon: ["fas", "copyright"],
						activeMenuTop: "system",
						roles: ["first-menu-role:"],
						alwaysShow: true
					}
				},
				{
					path: "/platform/system/device",
					name: "systemDevice",
					component: "",
					meta: {
						title: "终端管理",
						awesomeIcon: ["fas", "robot"],
						activeMenuTop: "platform",
						roles: ["first-menu-role:"],
						alwaysShow: true
					},
					children: [
						{
							path: "/platform/system/auth",
							name: "systemDeviceuth",
							component: "/views/system/auth.vue",
							meta: {
								title: "终端授权",
								awesomeIcon: [],
								activeMenuTop: "platform",
								roles: ["second-menu-role:"],
								alwaysShow: true
							}
						},
						{
							path: "/platform/system/version",
							name: "systemDeviceVersion",
							component: "/views/system/version.vue",
							meta: {
								title: "版本更新",
								awesomeIcon: [],
								activeMenuTop: "platform",
								roles: ["second-menu-role:"],
								alwaysShow: true
							}
						}
					]
				},
				{
					path: "/platform/system/dictionary",
					name: "systemDictionary",
					component: "/views/system/dictionary.vue",
					meta: {
						title: "数据字典",
						awesomeIcon: ["fas", "book"],
						activeMenuTop: "platform",
						roles: ["first-menu-role:"],
						alwaysShow: true
					}
				},
				{
					path: "/platform/system/parameter",
					name: "systemParameter",
					component: "/views/system/parameter.vue",
					meta: {
						title: "系统参数",
						awesomeIcon: ["fas", "sliders"],
						activeMenuTop: "platform",
						roles: ["first-menu-role:parameter"],
						alwaysShow: true
					}
				},
				{
					path: "/system/processDesign",
					name: "ProcessDesign",
					component: "/views/system/bpmn/processDesign.vue",
					meta: {
						title: "流程设计",
						awesomeIcon: ["fas", "code-compare"],
						activeMenuTop: "platform",
						roles: ["first-menu-role:"],
						alwaysShow: true
					}
				},
				{
					path: "/system/processCallback",
					name: "ProcessCallback",
					component: "/views/system/bpmn/processCallback.vue",
					meta: {
						title: "流程回调",
						awesomeIcon: ["fas", "rectangle-list"],
						activeMenuTop: "platform",
						roles: ["first-menu-role:"],
						alwaysShow: false
					}
				},
				{
					path: "/system/manufacturer",
					name: "Manufacturer",
					component: "/views/system/manufacturer.vue",
					meta: {
						title: "厂商管理",
						awesomeIcon: ["fas", "industry"],
						activeMenuTop: "platform",
						roles: ["first-menu-role:"],
						alwaysShow: true
					}
				},
				{
					path: "/system/bpmn",
					name: "bpmn",
					component: "/views/system/bpmn/index.vue",
					meta: {
						title: "流程图",
						awesomeIcon: [],
						activeMenuTop: "platform",
						roles: ["second-menu-role:"],
						alwaysShow: false
					}
				}
			]
		},
		{
			path: "/platform/approved",
			name: "Approved",
			component: "/layout/layouts/index.vue",
			meta: {
				title: "审批",
				awesomeIcon: ["fas", "gear"],
				roles: ["top-menu-role:approved"],
				alwaysShow: true
			},
			children: [
				{
					path: "/platform/approved/myInitiate",
					name: "MyInitiate",
					component: "/views/examineApprove/initiate.vue",
					meta: {
						title: "我发起的",
						awesomeIcon: ["fas", "rectangle-list"],
						activeMenuTop: "/platform/approved",
						roles: ["first-menu-role:myInitiate"],
						alwaysShow: true
					}
				},
				{
					path: "/platform/approved/myApproved",
					name: "MyApproved",
					component: "/views/examineApprove/approved.vue",
					meta: {
						title: "我审批的",
						awesomeIcon: ["fas", "city"],
						activeMenuTop: "/platform/approved/",
						roles: ["first-menu-role:myApproved"],
						alwaysShow: true
					}
				}
			]
		}
	],
	organizationAuthority: [],
	teamAuthority: []
}
