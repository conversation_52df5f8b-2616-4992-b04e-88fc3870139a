export interface ApprovedRequest {
	currentPage?: number
	pageSize?: number
}
export interface ApprovedResponse {
	/**
	 * 编码
	 */
	code: string
	data: Data
	/**
	 * 中文信息
	 */
	msg: string
}

export interface Data {
	/**
	 * 数量
	 */
	count: number
	rows: Row[]
}

export interface Row {
	/**
	 * 处理人
	 */
	assignee?: null
	/**
	 * 描述
	 */
	description?: null
	/**
	 * 自定义参数
	 */
	formData?: FormData
	/**
	 * 业务id
	 */
	id?: string
	/**
	 * 流程节点名称
	 */
	name?: string
	/**
	 * 流程定义id
	 */
	processDefinitionId?: string
	/**
	 * 流程实例id
	 */
	processInstanceId?: string
	/**
	 * 代办key
	 */
	taskDefinitionKey?: string
	/**
	 * 任务id
	 */
	taskId?: string
	/**
	 * 耗时时间
	 */
	timeConsuming?: null
	/**
	 * 版本
	 */
	version?: number
}

/**
 * 自定义参数
 */
export interface FormData {
	/**
	 * 是否同意
	 */
	agree: boolean
	/**
	 * 发起人
	 */
	applyer: string
	/**
	 * 开始时间
	 */
	beginDate: string
	/**
	 * 是否供电人员
	 */
	hasGd: boolean
	/**
	 * 是否机电人员
	 */
	hasJd: boolean
	/**
	 * 模块编码
	 */
	moduleCode: string
	/**
	 * 模块名称
	 */
	moduleName: string
	/**
	 * 流程定义id
	 */
	processDefinitionId: string
	/**
	 * 流程定义key
	 */
	processDefinitionKey: string
	/**
	 * 流程实例id
	 */
	processInstanceId: string
	/**
	 * 状态
	 */
	status: string
	/**
	 * 任务id
	 */
	taskId: string
	/**
	 * 业务标题
	 */
	taskTitle: string
}
