import { request } from "@/app/platform/utils/service"
import { AxiosPromise } from "axios"
import { ApprovedRequest, ApprovedResponse } from "./types/Approved"
import { platformApi } from "@/app/platform/api/platform"

/**
 * 审批列表
 */
export function getApprovedTableData(
	data: ApprovedRequest
): AxiosPromise<ApprovedResponse> {
	return request({
		url: platformApi.getApprovedTableData,
		method: "post",
		params: data
	})
}
export function getProcessListData(
	data: ApprovedRequest
	): AxiosPromise<ApprovedResponse> {
	return request({
		url: platformApi.getProcessListData,
		method: "post",
		data
	})
}
export function getMyTask(params: any) {
	return request({
		url: platformApi.getMyTask,
		method: "post",
		data: params
	})
}

export function getMyHisTasks(params: any) {
	return request({
		url: platformApi.getMyHisTasks,
		method: "post",
		data: params
	})
}
export function getTasks(params: any) {
	return request({
		url: platformApi.getTasks,
		method: "get",
		params
	})
}

/**
 * 审批列表
 */
import { mapKeys } from "lodash-es"
export function dealwithComplete(data: any) {
	const formdata = new FormData()

	mapKeys(data, (value, key) => {
		formdata.append(key, value)
	})
	return request({
		headers: { "Content-Type": "multipart/form-data" },
		url: platformApi.dealwithComplete,
		method: "post",
		data: formdata
	})
}

/**
 * 审批历史意见列表
 */
export function getHistoryApprovedData(params: any) {
	return request({
		url: platformApi.getHistoryApprovedData,
		method: "get",
		params
	})
}
/**
 * 历史流程跟踪
 */
export function getHistoryProcessTrackDown(params: any) {
	return request({
		url: platformApi.getHistoryProcessTrackDown,
		method: "get",
		params
	})
}
/**
 * 我的待办-撤回
 */
export function getRevocationHook(data: any,url:any) {
	return request({
		url: url,
		method: "post",
		data
	})
}



/** 流程 发起审批的接口*/
export function approvalDemo(data: any) {
	return request({
		url: platformApi.approvalDemo,
		method: "post",
		data
	})
}