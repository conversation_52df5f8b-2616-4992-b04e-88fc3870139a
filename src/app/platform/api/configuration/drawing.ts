/* 权限管理相关接口 */
import { request } from "@/app/platform/utils/service"
import { platformApi } from "@/app/platform/api/platform"

/**
 * @description 获取专业类别树
 */
export function getProfessionTreeApi(params: anyKey) {
  return request<SystemApiResponseData>({
    url: platformApi.getProfessionTree,
    method: "get",
    params
  })
}

/**
 * @description 查询设备分级
 */
export function getVEquipmentClassApi(params: any) {
  return request<SystemApiResponseData>({
    url: `/vEquipmentClass/getChildrenAll`,
    method: "get",
    params
  })
}

/**
 * @description 获取关联位置树
 */
export function getPositionTreeApi(params: anyKey) {
  return request<SystemApiResponseData>({
    url: `/positions/tree`,
    method: "get",
    params
  })
}

/**
 * @description 获取所有图纸资料
 */
export function getDrawListApi(data: anyKey) {
  return request<SystemApiResponseData>({
    url: `/draw/getList`,
    method: "post",
    data
  })
}

/**
 * @description 新增图纸资料
 */
export function addDrawApi(data: anyKey) {
  return request<SystemApiResponseData>({
    url: `/draw/addDraw`,
    method: "post",
    data
  })
}

/**
 * @description 编辑图纸资料
 */
export function editDrawApi(data: anyKey) {
  return request<SystemApiResponseData>({
    url: `/draw/updateDraw`,
    method: "put",
    data
  })
}

/**
 * @description 查询图纸资料-根据id
 */
export function getDrawByIdApi(params: anyKey) {
  return request<SystemApiResponseData>({
    url: `/draw/getById/${params.id}`,
    method: "get"
  })
}

/**
 * @description 删除图纸资料
 */
export function deleteDrawApi(params: anyKey) {
  return request<SystemApiResponseData>({
    url: `/draw/deleteDraw/${params.id}`,
    method: "delete"
  })
}

export const drawingsApi = {
  getProfessionTreeApi,
  getPositionTreeApi,
  getVEquipmentClassApi,
  getDrawListApi,
  addDrawApi,
  editDrawApi,
  getDrawByIdApi,
  deleteDrawApi
}
