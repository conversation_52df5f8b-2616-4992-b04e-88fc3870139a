/* 权限管理相关接口 */
import { request } from "@/app/platform/utils/service"
import { platformApi } from "@/app/platform/api/platform"

/**
 * @description 获取专业类别树
 */
export function getProfessionTreeApi(params: anyKey) {
  return request<SystemApiResponseData>({
    url: platformApi.getProfessionTree,
    method: "get",
    params
  })
}

/**
 * @description 获取组织架构树
 */
export function getOrganListApi(params: anyKey) {
  return request<SystemApiResponseData>({
    url: `/organ/getChildrenAll`,
    method: "get",
    params
  })
}

/**
 * @description 获取可选专家列表
 */
export function getExpertListApi(params: anyKey) {
  return request<SystemApiResponseData>({
    url: `/expert/getList`,
    method: "get",
    params
  })
}

/**
 * @description 确认选中专家
 */
function confirmSelectExpertManagementApi(data: any) {
	return request<SystemApiResponseData>({
		url: "/expertInfo/addList",
		method: "post",
		data
	})
}

/**
 * @description 获取专家列表
 */
export function getExpertInfoListApi(data: any) {
  return request<SystemApiResponseData>({
    url: `/expertInfo/getList`,
    method: "post",
    data
  })
}

/**
 * @description 查询专家信息
 */
export function getExpertInfoApi(params: any) {
  return request<SystemApiResponseData>({
    url: `/expertInfo/${params.id}`,
    method: "get"
  })
}

/**
 * @description 删除专家
 */
export function deleteExpertInfoApi(data: any) {
  return request<SystemApiResponseData>({
    url: `/expertInfo/${data.empNo}`,
    method: "delete"
  })
}

/**
 * @description 获取线路列表
 */
function getLineListApi(data: any) {
	return request<SystemApiResponseData>({
		url: platformApi.getLineList,
		method: "post",
		headers: {
			"Content-Type": "multipart/form-data"
		},
		data
	})
}

/**
 * @description 查询设备分级
 */
export function getVEquipmentClassApi(params: any) {
  return request<SystemApiResponseData>({
    url: `/vEquipmentClass/getChildrenAll`,
    method: "get",
    params
  })
}

/**
 * @description 查询专家证书
 */
export function getCertificateListApi(params: any) {
  return request<SystemApiResponseData>({
    url: `/expertCertificate/expert/${params.empNo}`,
    method: "get"
  })
}

/**
 * @description 查询专家证书
 */
export function addCertificateApi(data: any) {
  return request<SystemApiResponseData>({
    url: `/expertCertificate/addCertificate`,
    method: "post",
    data
  })
}

/**
 * @description 删除专家证书
 */
export function deleteCertificateApi(data: any) {
  return request<SystemApiResponseData>({
    url: `/expertCertificate/${data.empNo}/${data.id}`,
    method: "delete"
  })
}

/**
 * @description 更新专家信息
 */
export function updateExpertApi(data: any) {
  return request<SystemApiResponseData>({
    url: `/expertInfo/updateExpert`,
    method: "put",
    data
  })
}

/**
 * @description 修改专家状态
 */
export function updateExpertStatusApi(data: any) {
  return request<SystemApiResponseData>({
    url: `/expertInfo/statusUpdate`,
    method: "post",
    data
  })
}

export const expertManagementApi = {
  getProfessionTreeApi,
  getOrganListApi,
  getExpertListApi,
  confirmSelectExpertManagementApi,
  getExpertInfoListApi,
  getExpertInfoApi,
  deleteExpertInfoApi,
  getLineListApi,
  getVEquipmentClassApi,
  getCertificateListApi,
  addCertificateApi,
  deleteCertificateApi,
  updateExpertApi,
  updateExpertStatusApi
}
