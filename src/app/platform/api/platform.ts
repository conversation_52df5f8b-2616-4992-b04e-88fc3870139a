// 框架版本
const api = "/pitaya" //pitaya
export const platformApi = {
	//
	// 登录

	/** 获取验证码key */
	LoginCode: `${api}/system/login/restCaptchaUnionCode`,
	/** 获取验证码图片 */
	restCaptcha: `${api}/system/login/restCaptchaImg`,
	/** 登录并返回 Token */
	login: `${api}/system/login`,
	/** 获取用户详情 */
	getUserInfo: `${api}/system/user/getUserInfo`,
	/** 退出登录 */
	logout: `${api}/system/user/logout`,
	/** 修改密码 */
	updatePassword: `${api}/system/user/updatePassword`,
	/** 流程新建实例  该接口后续放到流程新建页面*/
	startProcessInstance: `${api}/system/procInst/startProcessInstance`,
	// 验证二维码是否展示接口
	systemParamenter: `${api}/system/systemParamenter/getSystemParameter`,
	//关闭浏览器调用接口
	closeTimeLog: `${api}/system/LogUserCalculation/closeTimeLog`,

	//ststem
	//系统
	/** 树 */
	getTreeData: `${api}/system/assetType/tree`,
	/** 列表 */
	getAssetList: `${api}/system/assetType/list`,
	/** 列表 */
	getAuthList: `${api}/system/device/list`,
	/** 编辑保存 */
	changeAuth: `${api}/system/device/save`,
	/** 移除 */
	deleteAuth: `${api}/system/device/remove`,
	/**  启用、停用*/
	batchRelease: `${api}/system/device/batchEnable`,
	getAuthUserLog: `${api}/system/device/listLog`,
	//强制下线
	setLogoutPDA: `${api}/system/device/logoutPDA`,

	//角色管理相关接口

	/** 新增/修改角色 */
	saveOrUpdateBaseRoleApi: `${api}/system/role/save`,
	/** 角色列表 */
	getBaseRoleList: `${api}/system/role/pageRoles`,
	/** 关联详情 */
	getBaseRoleDetail: `${api}/system/role/getById`,
	/** 所有账号列表 */
	getAllPageUsers: `${api}/system/user/pageUsers`,
	/** 移除 */
	removeRoleItem: `${api}/system/role/delete`,
	/** 复制角色信息 */
	roleReplication: `${api}/system/role/roleReplication`,

	//账号管理相关接口

	/** 新增/修改账号 */
	saveOrUpdateBaseUserApi: `${api}/system/user/save`,
	/** 根据部门查询账号列表 */
	getBaseUserListApi: `${api}/system/user/pageUsersByOrganizationId`,
	/** 移除账号 */
	removeBaseUserApi: `${api}/system/user/delete`,
	/** 启用/停用账号 */
	enabledOrNotApi: `${api}/system/user/changeState`,
	/** 修改账号密码 */
	resetPasswordApi: `${api}/system/user/resetPassword`,
	/** 根据账号id查询角色 */
	getRoleByUserIdApi: `${api}/system/user/findRoleByUserId`,
	/** 根据账号id查询班组 */
	getTeamByUserIdApi: `${api}/system/user/findTeamByUserId`,
	/** 获取公司树列表 */
	getAllCompany: `${api}/system/company/findAllNoPage`,
	/** 获取部门列表 */
	getAllOrgList: `${api}/system/organization/findAllByCompanyIdNoPage`,
	/** 切换角色 */
	changeUserRole: `${api}/system/user/changeRole`,

	/** 上传文件 */
	uploadFileServe: `${api}/system/camunda/develop`,

	//公司管理

	/** 获取公司列表 */
	getCompanyList: `${api}/system/company/findAllByPage`,
	getCompanyAllList: `${api}/system/company/findAll`,

	/** 保存公司 */
	saveCompany: `${api}/system/company/save`,
	/** 获取公司详情 */
	getCompanyInfo: `${api}/system/company/get`,
	/** 移除公司 */
	deleteCompany: `${api}/system/company/delete`,
	/** 启用停用 */
	startOrEndCompany: `${api}/system/company/statusChange`,

	//部门管理

	/** 获取部门树 */
	getDepartmentTree: `${api}/system/organization/findAllForTree`,
	/** 获取部门树-权限 */
	getDepartmentTreeWithRole: `${api}/system/organization/findAllForTreeAndData`,
	/** 获取部门下的列表 */
	getDepartmentTable: `${api}/system/organization/findAllByIdByPage`,
	/** 保存部门 */
	saveDepartment: `${api}/system/organization/save`,
	/** 部门关联的专业 */
	getProfessionForDepartment: `${api}/system/major/findTreeByOrgId`,
	/** 移除部门 */
	deleteDepartment: `${api}/system/organization/delete`,

	/** 获取关联的线路 */
	getDepartmentLine: `${api}/system/line/listByOrgId`,
	/** 部门或班组关联位置 */
	getDepartmentOrTeamPosition: `${api}/location/info/findAllByOrgIdToTree`,

	//数据字典
	/** 列表 */
	getDictionaryList: `${api}/system/dataDictionary/list`,
	getTreeDictnaryList: `${api}/system/treeDictionary/list`,
	saveOrUpdateTreeDictionary: `${api}/system/treeDictionary/save`,
	listTreeDictionaryByCode: `${api}/system/treeDictionary/listByDataDictionaryCode`,
	deleteTreeDictnary: `${api}/system/treeDictionary/remove`,
	/**  新增、编辑保存 */
	changeDictionary: `${api}/system/dataDictionary/save`,
	/** 移除 */
	deleteDictionary: `${api}/system/dataDictionary/remove`,
	/** 字典项 */
	getDictionaryTerm: `${api}/system/dataDictionary/listByDataDictionaryCode`,

	//标签管理相关接口

	/** 获取标签列表 */
	getLabelCodeListApi: `${api}/system/labelCode/list`,
	/** 新增标签 */
	createLabelCodeApi: `${api}/system/labelCode/save`,
	/** 标签码基本详情 */
	getLabelCodeDetailApi: `${api}/system/labelCode/get`,
	/** 标签码详情列表 */
	getLabelCodeDetailListApi: `${api}/system/labelCode/bindingList`,
	/** 标签码打印 */
	labelCodePrintApi: `${api}/system/labelCode/print`,

	//线路

	/** 获取线路列表 */
	getLineList: `${api}/system/line/list`,
	/** 线路保存 */
	saveAndUpdateLine: `${api}/system/line/save`,
	/** 线路移除 */
	deleteLine: `${api}/system/line/delete`,

	/** 列表 */
	getManufacturerPage: `${api}/system/manufacturer/list`,
	/** 保存厂商的接口 */
	saveManufacturer: `${api}/system/manufacturer/saveOrUpdate`,
	deleteManufacturer: `${api}/system/manufacturer/remove`,
	/** 消息列表 */
	getMsgListApi: `${api}/system/message/list`,
	/** 一键已读 */
	readAll: `${api}/system/message/batchRead`,

	/** 列表 */
	getSystemParameterList: `${api}/system/systemParamenter/list`,
	/** 编辑保存  */
	changeSystemParameter: `${api}/system/systemParamenter/save`,
	/** 从缓存中获取全部系统参数数据  */
	getBatchSystemParameters:`${api}/system/systemParamenter/getBatchSystemParameters`,
	//parameter.ts

	/** 列表  */
	getProcessCallbackList: `${api}/system/callbackInfo/findAllByPage`,
	/** 新增、编辑保存  */
	changeProcessCallback: `${api}/system/callbackInfo/save`,
	/**  移除 */
	deleteProfession: `${api}/system/callbackInfo/delete`,

	//processDesign.ts
	createProcessInfo: `${api}/system/process/save`,
	editProcessInfo: `${api}/system/process/updateInstance`,

	deleteProcessInfo: `${api}/system/process/delete`,
	/**  列表 */
	getProcessDesignList: `${api}/system/process/list`,
	/** 流程版本发布 **/
	processVersionDeploy: `${api}/system/process/version/publish`,
	/** 流程版本新增 */
	processVersionAdd: `${api}/system/process/version/add`,
	/** 流程版本移除 */
	processVersionDelete: `${api}/system/process/version/delete`,
	/**   */
	deployProcessDef: `${api}/system/procDef/deploy`,

	deployProcessDefNew: `${api}/system/procDef/deploy`,
	/** 获取流程xml  */
	getProcessXML: `${api}/system/procDef/processDefinitionXml`,
	/** 获取流程xml 高亮节点 */
	getProcessXMLHeightLight: `${api}/system/camunda/processDiagram`,
	/** 移除 */
	deleteProcessCallback: `${api}/system/callbackInfo/delete`,

	//profession.ts  专业管理
	/** 获取专业树  */
	getProfessionTree: `${api}/system/major/rootTree`,
	/**  获取专业树 */
	getProfessionTreeV2: `${api}/system/major/tree`,
	/**  获取专业树 */
	getProfessionListPidTree: `${api}/system/major/listPidTree`,
	/**  专业保存 */
	saveProfession: `${api}/system/major/save`,
	/**  查看关联部门 */
	getOrgInfo: `${api}/system/major/orgInfo`,
	/** 获取专业列表 */
	getProfessionTable: `${api}/system/major/page`,
	/**  移除专业 */
	getDeleteProfession: `${api}/system/major/delete`,
	// 根据专业id获取子专业
	getMajorChildren: `${api}/system/major/major/getChildren`,

	//requestMap.ts 权限管理

	/** 获取权限树  */
	getBaseSystemTreeApi: `${api}/system/menu/listWithTree`,
	getBaseSystemLazyTreeApi: `${api}/system/menu/listLazyLoadWithTree`,
	getListRoleALLWithTree:`${api}/system/menu/listRoleALLWithTree`,

	/**  根据权限树查询权限列表 */
	getBaseSystemListApi: `${api}/system/menu/list`,
	/**  新增/修改权限 */
	saveOrUpdateBaseSystemApi: `${api}/system/menu/save`,
	/** 移除权限  */
	removeBaseSystemApi: `${api}/system/menu/delete`,

	//teamGroup.ts
	/** 获取班组树-权限  */
	getTeamGroupTreeWithRole: `${api}/system/teamd/findAllForTreeAndData`,
	/** 获取班组下的列表  */
	getTeamGroupTable: `${api}/system/teamd/findAllByIdByPage`,
	/** 保存班组  */
	saveTeamGroup: `${api}/system/teamd/save`,
	/** 班组关联的专业  */
	getProfessionForTeamGroup: `${api}/system/teamd/getMajor`,
	/** 移除班组  */
	deleteTeamGroup: `${api}/system/teamd/delete`,
	/**  班组关联的用户 */
	getUserByTeamGroupId: `${api}/system/teamd/getBaseUser`,
	/**   */
	getSharedUser: `${api}/system/teamd/sharedUser`,

	//teamGroup.ts
	/**  列表 */
	getSdkManagerList: `${api}/system/sdkManager/list`,
	/**  新增、编辑保存 */
	changeSdkManager: `${api}/system/sdkManager/save`,
	/**  移除 */
	deleteSdkManager: `${api}/system/sdkManager/remove`,
	/**  发布 */
	getBatchRelease: `${api}/system/sdkManager/release`,
	/**  回滚 */
	allowRollback: `${api}/system/sdkManager/allowRollback`,
	/**  sdk上传 */
	uploadFile: `${api}/system/sdkManager/uploadFile`,
	/** sdk下载  */
	download: `${api}/system/sdkManager/download`,

	//审批

	/** 审批列表  */
	getApprovedTableData: `${api}/system/procTask/myTaskJson`,
	/**   */
	getProcessListData: `${api}/system/procInst/getAllProcessInstanceByApplyer`,
	/**   */
	getMyTask: `${api}/system/procTask/getMyTasks`,

	getMyHisTasks: `${api}/system/procTask/getMyHisTasks`,
	getTasks: `${api}/system/procTask/getTasks`,
	/** 审批列表  */
	dealwithComplete: `${api}/system/procTask/complete`,
	/** 审批历史意见列表  */
	getHistoryApprovedData: `${api}/system/procTask/showCommentsJson`,
	/** 历史流程跟踪  */
	getHistoryProcessTrackDown: `${api}/system/procTask/showTasksJson`,

	deleteProcessDef: `${api}/system/procDef/delete`,
	//通用版本列表
	getVersion: `${api}/system/common/version/list`,

	//获取版本下的附件
	getProcessAttachment: `${api}/system/common/attachment/listByVersionControlIdAndBusinessType`,

	commonUploadFile: `${api}/system/common/upload`,

	commonDownloadFile: `${api}/system/common/static`,

	//获取版本下的附件
	approvalDemo: `${api}/system/approvalDemo/startProcessInstance`,

	/** 消息设置列表  */
	messageList: `${api}/system/message/manager/pagelist`,

	messageUpdate: `${api}/system/message/manager/update`,

	/** 用户日志统计  */
	LogUserCalculation: `${api}/system/LogUserCalculation/list`,

	/** 公告列表  */
	noticeList: `${api}/system/notice/list`,

	noticeGet:`${api}/system/notice/get`,

	noticeSave:`${api}/system/notice/save`,

	noticeDialog:`${api}/system/notice/getNoticeDialog`,

	noticePublish:`${api}/system/notice/noticePublish`,

	noticeOffline:`${api}/system/notice/noticeOffline`,

	noticeDelete:`${api}/system/notice/delete`,
}
