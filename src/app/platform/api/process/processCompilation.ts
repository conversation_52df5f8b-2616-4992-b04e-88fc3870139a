/* 角色管理相关接口 */
import { request } from "@/app/platform/utils/service"
import { platformApi } from "@/app/platform/api/platform"

/**
 *  @description 获取关联角色
 */
export function getRoleListApi(params: anyKey) {
  return request<SystemApiResponseData>({
    url: platformApi.getBaseRoleList,
    method: "post",
    params
  })
}

/**
 * @description 获取关联位置树
 */
export function getPositionTreeApi(params: anyKey) {
  return request<SystemApiResponseData>({
    url: `/positions/tree`,
    method: "get",
    params
  })
}

/**
 * @description 获取流程列表
 */
export function getProcessListApi(data: anyKey) {
  return request<SystemApiResponseData>({
    url: `/baseline/system/rpstssProcessTreeFlowSetMain/pageByNewDate`,
    method: "post",
    data
  })
}

/**
 * @description 获取流程各状态个数
 */
export function getProcessStatusNumApi() {
  return request<SystemApiResponseData>({
    url: `/baseline/system/rpstssProcessTreeFlowSetMain/countStatus`,
    method: "get"
  })
}

/**
 *  @description 添加流程基本信息
 */
export function addFlowApi(data: anyKey) {
  return request<SystemApiResponseData>({
    url: '/baseline/system/rpstssProcessTreeFlowSetMain/addFlow',
    method: "post",
    data
  })
}

/**
 *  @description 发布流程
 */
export function updateFlowStatusApi(data: anyKey) {
  return request<SystemApiResponseData>({
    url: '/baseline/system/rpstssProcessTreeFlowSetMain/updateFlowStatus',
    method: "put",
    data
  })
}

/**
 * @description 获取基本流程详情
 */
export function getProcessDetailApi(id: string) {
  return request<SystemApiResponseData>({
    url: `/baseline/system/rpstssProcessTreeFlowSetMain/${id}`,
    method: "get"
  })
}

/**
 * @description 获取流程任务详情
 */
export function getProcessTaskDetailApi(id: string) {
  return request<SystemApiResponseData>({
    url: `/baseline/system/rpstssProcessTreeFlowSetMain/flow/${id}`,
    method: "get"
  })
}


/**
 *  @description 添加流程基本信息
 */
export function updateFlowApi(data: anyKey) {
  return request<SystemApiResponseData>({
    url: '/baseline/system/rpstssProcessTreeFlowSetMain/updateFlow',
    method: "put",
    data
  })
}

/**
 *  @description 删除流程
 */
export function deleteFlowApi(id: string | number) {
  return request<SystemApiResponseData>({
    url: `/baseline/system/rpstssProcessTreeFlowSetMain/deleteFlow/${id}`,
    method: "delete"
  })
}

/**
 * @description 新增任务
 */
export function createTaskApi(data: anyKey) {
  return request<SystemApiResponseData>({
    url: '/baseline/system/rpstssProcessTreeFlowSetTask/createTask',
    method: "post",
    data
  })
}

/**
 * @description 编辑任务
 */
export function updateTaskApi(data: anyKey) {
  return request<SystemApiResponseData>({
    url: '/baseline/system/rpstssProcessTreeFlowSetTask/updateTask',
    method: "put",
    data
  })
}

/**
 * @description 删除任务
 */
export function deleteTaskApi(id: any) {
  return request<SystemApiResponseData>({
    url: `/baseline/system/rpstssProcessTreeFlowSetTask/deleteTask/${id}`,
    method: "delete"
  })
}

/**
 * @description 任务详情
 */
export function getTaskDetailApi(id: any) {
  return request<SystemApiResponseData>({
    url: `/baseline/system/rpstssProcessTreeFlowSetTask/getTaskList/${id}`,
    method: "get"
  })
}

/**
 * @description 获取图纸已选择的列表
 */
export function getSelectDrawingsListApi(mainId: any) {
  return request<SystemApiResponseData>({
    url: `/baseline/system/draw/${mainId}`,
    method: "get"
  })
}

/**
 * @description 新增图纸关联
 */
export function addDrawApi(data: anyKey) {
  return request<SystemApiResponseData>({
    url: '/baseline/system/draw',
    method: "post",
    data
  })
}

/**
 * @description 删除图纸关联
 */
export function deleteDrawApi(id: anyKey) {
  return request<SystemApiResponseData>({
    url: `/baseline/system/draw/${id}`,
    method: "delete"
  })
}

/**
 * @description 获取专家/领导已选择的列表
 */
export function getSelectExpertManagementListApi(mainId: any, roleType: any) {
  return request<SystemApiResponseData>({
    url: `/baseline/system/setUser/${mainId}/${roleType}`,
    method: "get"
  })
}

/**
 * @description 新增专家/领导关联
 */
export function addExpertApi(data: anyKey) {
  return request<SystemApiResponseData>({
    url: '/baseline/system/setUser',
    method: "post",
    data
  })
}

/**
 * @description 删除专家/领导关联
 */
export function deleteExpertApi(id: anyKey) {
  return request<SystemApiResponseData>({
    url: `/baseline/system/setUser/${id}`,
    method: "delete"
  })
}

export const processApi = {
  getRoleListApi,
  getPositionTreeApi,
  getProcessListApi,
  getProcessStatusNumApi,
  addFlowApi,
  updateFlowStatusApi,
  getProcessDetailApi,
  getProcessTaskDetailApi,
  updateFlowApi,
  deleteFlowApi,
  createTaskApi,
  updateTaskApi,
  deleteTaskApi,
  getTaskDetailApi,
  getSelectDrawingsListApi,
  addDrawApi,
  deleteDrawApi,
  getSelectExpertManagementListApi,
  addExpertApi,
  deleteExpertApi
}