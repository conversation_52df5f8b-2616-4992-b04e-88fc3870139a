import { request } from "@/app/platform/utils/service"
import { platformApi } from "@/app/platform/api/platform"


// 列表
export function getManufacturerPage(params: any) {
	return request<SystemApiResponseData>({
		url: platformApi.getManufacturerPage,
		method: "get",
		params
	})
}
// 保存厂商的接口
export function saveManufacturer(data: any) {
	return request<SystemApiResponseData>({
		url: platformApi.saveManufacturer,
		method: "post",
		data
	})
}
export function deleteManufacturer(id: string) {
	return request<SystemApiResponseData>({
		url: platformApi.deleteManufacturer,
		method: "post",
		params: {
			id
		}
	})
}
