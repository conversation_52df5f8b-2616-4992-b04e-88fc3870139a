import { request } from "@/app/platform/utils/service"
import { platformApi } from "@/app/platform/api/platform"

// 获取部门树
function getDepartmentTree() {
	return request<SystemApiResponseData>({
		url: platformApi.getDepartmentTree,
		method: "post"
	})
}

// 获取部门树-权限
function getDepartmentTreeWithRole(params: any) {
	return request<SystemApiResponseData>({
		url: platformApi.getDepartmentTreeWithRole,
		method: "get",
		params
	})
}

// 获取部门下的列表
function getDepartmentTable(data: any) {
	return request<SystemApiResponseData>({
		url: platformApi.getDepartmentTable,
		method: "post",
		headers: {
			"Content-Type": "multipart/form-data"
		},
		data
	})
}

// 保存部门
// function saveDepartment(data: any) {
// 	return request<SystemApiResponseData>({
// 		url: platformApi.saveDepartment,
// 		method: "post",
// 		data
// 	})
// }

function saveDepartment(data: any) {
	return request<SystemApiResponseData>({
		url: platformApi.saveDepartment,
		method: "post",
		data
	})
}

// 部门关联的专业
function getProfessionForDepartment(orgId: any) {
	return request<SystemApiResponseData>({
		url: platformApi.getProfessionForDepartment,
		method: "get",
		params: {
			orgId
		}
	})
}

// 移除部门
function deleteDepartment(id: any) {
	return request<SystemApiResponseData>({
		url: platformApi.deleteDepartment,
		method: "get",
		params: {
			id
		}
	})
}

// 获取关联的线路
function getDepartmentLine(data: any) {
	return request<SystemApiResponseData>({
		url: platformApi.getDepartmentLine,
		method: "get",
		params: {
			orgId: data.orgId ?? "",
			companyId: data.companyId ?? ""
		}
	})
}

// 部门或班组关联位置
// function getDepartmentOrTeamPosition(id: any) {
// 	return request<SystemApiResponseData>({
// 		url: platformApi.getDepartmentOrTeamPosition,
// 		method: "get",
// 		params: {
// 			orgId: id
// 		}
// 	})
// }

// // 部门或班组关联位置
// function getDepartmentOrTeamPosition(id: any, lineIds: any) {
// 	return request<SystemApiResponseData>({
// 		url: baselineApi.getDepartmentOrTeamPosition,
// 		method: "get",
// 		params: {
// 			orgId: id,
// 			lineIds: lineIds
// 		}
// 	})
// }

// 查询关联的位置
function getPositionDetail(id: any) {
	return request<SystemApiResponseData>({
		url: "/baseline/system/organization/findAllByOrgIdForLocationTree",
		method: "get",
		params: {
			orgId: id
		}
	})
}

// 查询关联的位置
// function getPositionDetail(id: any) {
// 	return new Promise((resolve, reject) => {
// 		resolve({})
// 	})
// }

//部门停用/启用
function getStatusChange(params: any) {
	return request<SystemApiResponseData>({
		url: "/pitaya/system/organization/statusChange",
		method: "get",
		params
	})
}
// function getStatusChange(data: any) {
// 	return request<SystemApiResponseData>({
// 		url: "/pitaya/system/organization/statusChange",
// 		method: "get",
// 		data
// 	})
// }

export const DepartmentApi = {
	getDepartmentTree,
	getDepartmentTable,
	saveDepartment,
	getProfessionForDepartment,
	deleteDepartment,
	getDepartmentTreeWithRole,
	getDepartmentLine,
	getPositionDetail,
	getStatusChange
}
