import { request } from "@/app/platform/utils/service"
import { platformApi } from "@/app/platform/api/platform"

// 获取专业树
function getProfessionTree() {
	return request<SystemApiResponseData>({
		url: platformApi.getProfessionTree,
		method: "get"
	})
}
// 获取专业树
function getProfessionTreeV2(companyId: any) {
	return request<SystemApiResponseData>({
		url: platformApi.getProfessionTreeV2,
		method: "get",
		params: {
			companyId
		}
	})
}
// 获取专业树
function getProfessionListPidTree(params: any) {
	return request<SystemApiResponseData>({
		url: platformApi.getProfessionListPidTree,
		method: "get",
		params
	})
}
// 专业保存
function saveProfession(data: any) {
	return request<SystemApiResponseData>({
		url: platformApi.saveProfession,
		method: "post",
		data
	})
}

// 查看关联部门
function getOrgInfo(subjectId: any) {
	return request<SystemApiResponseData>({
		url: platformApi.getOrgInfo,
		method: "get",
		params: {
			subjectId
		}
	})
}

// 获取专业列表
function getProfessionTable(data: any) {
	return request<SystemApiResponseData>({
		url: platformApi.getProfessionTable,
		method: "post",
		headers: {
			"Content-Type": "multipart/form-data"
		},
		data
	})
}

// 移除专业
function deleteProfession(id: any) {
	return request<SystemApiResponseData>({
		url: platformApi.getDeleteProfession,
		method: "get",
		params: {
			id
		}
	})
}

export const ProfessionApi = {
	getProfessionTree,
	saveProfession,
	getProfessionTable,
	getOrgInfo,
	deleteProfession,
	getProfessionTreeV2
}
