import { request } from "@/app/platform/utils/service"
import { platformApi } from "@/app/platform/api/platform"

// 树
export function getTreeData(data: any) {
	return request<SystemApiResponseData>({
		url: platformApi.getTreeData,
		headers: { "Content-Type": "multipart/form-data" },
		method: "post",
		data
	})
}
// 列表
export function getAssetList(data: any) {
	return request<SystemApiResponseData>({
		url: platformApi.getAssetList,
		headers: { "Content-Type": "multipart/form-data" },
		method: "post",
		data
	})
}
