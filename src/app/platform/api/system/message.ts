import { request } from "@/app/platform/utils/service"
import { platformApi } from "@/app/platform/api/platform"

// 列表
export function getMessageList(params: anyKey) {
	return request<any>({
		url: platformApi.messageList,
		method: "get",
		params
	})
}

//修改消息设置
export function getMessageUpdate(data: any) {
	return request<SystemApiResponseData>({
		url: platformApi.messageUpdate,
		method: "post",
		data
	})
}


// 用户日志统计列表
export function LogUserCalculation(params: anyKey) {
	return request<any>({
		url: platformApi.LogUserCalculation,
		method: "get",
		params
	})
}