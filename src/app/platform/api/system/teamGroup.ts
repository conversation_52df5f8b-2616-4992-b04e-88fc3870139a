import { request } from "@/app/platform/utils/service"
import { platform<PERSON>pi } from "@/app/platform/api/platform"


// 获取班组树-权限
function getTeamGroupTreeWithRole(params: anyKey) {
	return request<SystemApiResponseData>({
		url: platformApi.getTeamGroupTreeWithRole,
		method: "get",
		params
	})
}

// 获取班组下的列表
function getTeamGroupTable(data: any) {
	return request<SystemApiResponseData>({
		url: platformApi.getTeamGroupTable,
		method: "post",
		headers: {
			"Content-Type": "multipart/form-data"
		},
		data
	})
}

// 保存班组
function saveTeamGroup(data: any) {
	return request<SystemApiResponseData>({
		url: platformApi.saveTeamGroup,
		method: "post",
		data
	})
}

// 班组关联的专业
function getProfessionForTeamGroup(orgId: any) {
	return request<SystemApiResponseData>({
		url: platformApi.getProfessionForTeamGroup,
		method: "get",
		params: {
			orgId
		}
	})
}

// 删除班组
function deleteTeamGroup(id: any) {
	return request<SystemApiResponseData>({
		url: platformApi.deleteTeamGroup,
		method: "get",
		params: {
			id
		}
	})
}

// 班组关联的用户
function getUserByTeamGroupId(id: any) {
	return request<SystemApiResponseData>({
		url: platformApi.getUserByTeamGroupId,
		method: "get",
		params: {
			id
		}
	})
}
export function getSharedUser(params: anyKey) {
	return request<SystemApiResponseData>({
		url: platformApi.getSharedUser,
		method: "get",
		params
	})
}

export const TeamGroupApi = {
	getTeamGroupTable,
	saveTeamGroup,
	getProfessionForTeamGroup,
	deleteTeamGroup,
	getUserByTeamGroupId,
	getTeamGroupTreeWithRole,
	getSharedUser
}
