import { request } from "@/app/platform/utils/service"
import { platform<PERSON>pi } from "@/app/platform/api/platform"

// 列表
export function getProcessDesignList(params: any) {
	return request<SystemApiResponseData>({
		url: platformApi.getProcessDesignList,
		method: "get",
		params
	})
}
//删除流程
export function deleteProcessDesign(data: any) {
	return request<SystemApiResponseData>({
		url: platformApi.deleteProcessInfo,
		method: "post",
		data
	})
}
//删除流程版本
export function deleteDraft(data: any) {
	return request<SystemApiResponseData>({
		url: platformApi.processVersionDelete,
		method: "post",
		data
	})
}

// 版本更新
export function deployProcessDef(formData: any) {
	return request({
		url: platformApi.deployProcessDef,
		data: formData,
		headers: { "Content-Type": "multipart/form-data" },
		method: "post"
	})
}
// 新版本
export function deployProcessDefNew(formData: any) {
	return request({
		url: platformApi.deployProcessDefNew,
		data: formData,
		headers: { "Content-Type": "multipart/form-data" },
		method: "post"
	})
}

// 获取流程xml
export function getProcessXML(params: any) {
	return request<SystemApiResponseData>({
		url: platformApi.getProcessXML,
		method: "get",
		params,
		responseType: "blob"
	})
}
// 获取流程xml 高亮节点
export function getProcessXMLHeightLight(params: any) {
	return request({
		url: platformApi.getProcessXMLHeightLight,
		method: "get",
		params
	})
}
// 移除
export function deleteProcessCallback(data: any) {
	return request<SystemApiResponseData>({
		url: platformApi.deleteProcessCallback,
		headers: { "Content-Type": "multipart/form-data" },
		method: "post",
		data
	})
}

// 移除
export function deleteProcessDef(params: any) {
	return request<SystemApiResponseData>({
		url: platformApi.deleteProcessDef,
		method: "post",
		params
	})
}
//新增
export function createProcessInfo(data: any) {
	return request<SystemApiResponseData>({
		url: platformApi.createProcessInfo,
		method: "post",
		data
	})
}
//编辑
export function editProcessInfo(data: any) {
	return request<SystemApiResponseData>({
		url: platformApi.editProcessInfo,
		method: "post",
		data
	})
}
//通用版本列表
export function getVersion(params: any) {
	return request<SystemApiResponseData>({
		url: platformApi.getVersion,
		method: "get",
		params
	})
}

export function getProcessAttachment(params: any) {
	return request<SystemApiResponseData>({
		url: platformApi.getProcessAttachment,
		method: "get",
		params
	})
}
export function commonUploadFile(data: any) {
	return request<SystemApiResponseData>({
		url: platformApi.commonUploadFile,
		headers: { "Content-Type": "multipart/form-data" },
		method: "post",
		data
	})
}

export function commonDownloadFile(path: string) {
	return request<SystemApiResponseData>({
		url: platformApi.commonDownloadFile + "/" + path,
		headers: { "Content-Type": "multipart/form-data" },
		method: "get",
		responseType: "blob"
	})
}

export function processVersionDeploy(data: any) {
	return request<SystemApiResponseData>({
		url: platformApi.processVersionDeploy,
		method: "post",
		data
	})
}

export function processVersionAdd(data: any) {
	return request<SystemApiResponseData>({
		url: platformApi.processVersionAdd,
		method: "post",
		data
	})
}
