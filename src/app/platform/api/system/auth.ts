import { request } from "@/app/platform/utils/service"
import { platformApi } from "@/app/platform/api/platform"

// 列表
export function getAuthList(data: any) {
	return request<SystemApiResponseData>({
		url: platformApi.getAuthList,
		headers: { "Content-Type": "multipart/form-data" },
		method: "post",
		data
	})
}
// 编辑保存
export function changeAuth(data: any) {
	return request<SystemApiResponseData>({
		url: platformApi.changeAuth,
		method: "post",
		data
	})
}
// 移除
export function deleteAuth(data: any) {
	return request<SystemApiResponseData>({
		url: platformApi.deleteAuth,
		headers: { "Content-Type": "multipart/form-data" },
		method: "post",
		data
	})
}
// 启用、停用
export function batchRelease(data: any) {
	return request<SystemApiResponseData>({
		url: platformApi.batchRelease,
		headers: { "Content-Type": "multipart/form-data" },
		method: "post",
		data
	})
}

export function getAuthUserLog(data: any){
	return request<SystemApiResponseData>({
		url: platformApi.getAuthUserLog,
		method: "get",
		params: {
			...data
		}
	})
}

export function setLogoutPDA(data: any){
	return request<SystemApiResponseData>({
		url: platformApi.setLogoutPDA,
		method: "get",
		params: {
			...data
		}
	})
}