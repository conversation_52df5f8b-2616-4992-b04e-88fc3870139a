import { request } from "@/app/platform/utils/service"
import { platformApi } from "@/app/platform/api/platform"

// 列表
export function getDictionaryList(params: any) {
	return request<SystemApiResponseData>({
		url:platformApi.getDictionaryList,
		method: "post",
		params
	})
}

export function getTreeDictnaryList(params: any) {
	return request<SystemApiResponseData>({
		url: platformApi.getTreeDictnaryList,
		method: "get",
		params
	})
}
export function saveOrUpdateTreeDictionary(data: any) {
	return request<SystemApiResponseData>({
		url: platformApi.saveOrUpdateTreeDictionary,
		method: "post",
		data
	})
}

export function listTreeDictionaryByCode(params: any,){
	return request<SystemApiResponseData>({
		url: platformApi.listTreeDictionaryByCode,
		method: "get",
		params
	})
}
export function deleteTreeDictnary(id: any) {
	return request<SystemApiResponseData>({
		url: platformApi.deleteTreeDictnary,
		method: "post",
		params: {
			id
		}
	})
}

// 新增、编辑保存
export function changeDictionary(data: any) {
	return request<SystemApiResponseData>({
		url: platformApi.changeDictionary,
		method: "post",
		data
	})
}
// 移除
export function deleteDictionary(data: any) {
	return request<SystemApiResponseData>({
		url: platformApi.deleteDictionary,
		headers: { "Content-Type": "multipart/form-data" },
		method: "post",
		data
	})
}
// 字典项
export function getDictionaryTerm(params: any) {
	return request<any>({
		url: platformApi.getDictionaryTerm,
		method: "get",
		params
	})
}
