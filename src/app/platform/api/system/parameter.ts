import { request } from "@/app/platform/utils/service"
import { platformApi } from "@/app/platform/api/platform"

// 列表
export function getSystemParameterList(params: any) {
	return request<SystemApiResponseData>({
		url: platformApi.getSystemParameterList,
		method: "post",
		params
	})
}
// 编辑保存
export function changeSystemParameter(data: any) {
	return request<SystemApiResponseData>({
		url: platformApi.changeSystemParameter,
		method: "post",
		data
	})
}
// 列表
export function getBatchSystemParameters(params: any) {
	return request<SystemApiResponseData>({
		url: platformApi.getBatchSystemParameters,
		method: "get",
		params
	})
}