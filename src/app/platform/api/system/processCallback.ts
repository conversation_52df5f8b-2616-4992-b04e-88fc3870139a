import { request } from "@/app/platform/utils/service"
import { platformApi } from "@/app/platform/api/platform"

// 列表
export function getProcessCallbackList(params: any) {
	return request<SystemApiResponseData>({
		url:platformApi.getProcessCallbackList ,
		method: "get",
		params
	})
}
// 新增、编辑保存
export function changeProcessCallback(data: any) {
	return request<SystemApiResponseData>({
		url:platformApi.changeProcessCallback ,
		method: "post",
		data
	})
}
// 移除
export function deleteProcessCallback(data: any) {
	return request<SystemApiResponseData>({
		url: platformApi.deleteProcessCallback,
		headers: { "Content-Type": "multipart/form-data" },
		method: "post",
		data
	})
}
