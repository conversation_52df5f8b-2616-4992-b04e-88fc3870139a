/* 账号管理相关接口 */
import { request } from "@/app/platform/utils/service"
import { platformApi } from "@/app/platform/api/platform"

/**
 * 新增/修改账号
 * @date 2023-08-08
 */
function saveOrUpdateBaseUserApi(data: anyKey) {
	return request<SystemApiResponseData>({
		url: platformApi.saveOrUpdateBaseUserApi,
		method: "post",
		data
	})
}

/**
 * 根据部门查询账号列表
 * @date 2023-08-08
 */
function getBaseUserListApi(params: anyKey) {
	return request<SystemApiResponseData>({
		url: platformApi.getBaseUserListApi,
		method: "get",
		params
	})
}

/**
 * 移除账号
 * @date 2023-08-08
 */
function removeBaseUserApi(params: anyKey) {
	return request<SystemApiResponseData>({
		url: platformApi.removeBaseUserApi,
		method: "post",
		params
	})
}

/**
 * 启用/停用账号
 * @date 2023-08-08
 */
function enabledOrNotApi(data: anyKey) {
	return request<SystemApiResponseData>({
		url: platformApi.enabledOrNotApi,
		method: "post",
		data
	})
}

/**
 * 修改账号密码
 * @date 2023-08-08
 */
function resetPasswordApi(data: anyKey) {
	return request<SystemApiResponseData>({
		url: platformApi.resetPasswordApi,
		method: "post",
		data
	})
}

/**
 * 根据账号id查询角色
 * @date 2023-08-10
 */
function getRoleByUserIdApi(params: anyKey) {
	return request<SystemApiResponseData>({
		url: platformApi.getRoleByUserIdApi,
		method: "get",
		params
	})
}

/**
 * 根据账号id查询班组
 * @date 2023-08-10
 */
function getTeamByUserIdApi(params: anyKey) {
	return request<SystemApiResponseData>({
		url: platformApi.getTeamByUserIdApi,
		method: "post",
		params
	})
}

/**
 * 获取公司树列表
 */
function getAllCompany() {
	return request<SystemApiResponseData>({
		url: platformApi.getAllCompany,
		method: "get"
	})
}

/**
 * 获取部门列表
 */
function getAllOrgList(params: anyKey) {
	return request<SystemApiResponseData>({
		url: platformApi.getAllOrgList,
		method: "get",
		params
	})
}

// 切换角色
function changeUserRole(roleId: string) {
	return request<SystemApiResponseData>({
		url: platformApi.changeUserRole,
		method: "get",
		params: {
			roleId
		}
	})
}

export const baseUserApi = {
	saveOrUpdateBaseUserApi,
	getBaseUserListApi,
	removeBaseUserApi,
	enabledOrNotApi,
	resetPasswordApi,
	getRoleByUserIdApi,
	getTeamByUserIdApi,
	getAllCompany,
	changeUserRole,
	getAllOrgList
}
