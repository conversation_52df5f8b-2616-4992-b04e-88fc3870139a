import { request } from "@/app/platform/utils/service"
import { AxiosPromise } from "axios"
import { MsgListRequest, MsgListResponse } from "./types/msg"
import { platformApi } from "@/app/platform/api/platform"

/**
 * 消息列表
 */
export function getMsgListApi(
	data: MsgListRequest
): AxiosPromise<MsgListResponse> {
	return request({
		url: platformApi.getMsgListApi,
		method: "post",
		params: data
	})
}

/**
 * 一键已读
 */
export function readAll() {
	return request({
		url: platformApi.readAll,
		method: "post"
	})
}
