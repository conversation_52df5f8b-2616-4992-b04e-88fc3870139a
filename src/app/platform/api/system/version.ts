import { request } from "@/app/platform/utils/service"
import { platformApi } from "@/app/platform/api/platform"

// 列表
export function getSdkManagerList(params: any) {
	return request<SystemApiResponseData>({
		url: platformApi.getSdkManagerList,
		method: "get",
		params
	})
}
// 新增、编辑保存
export function changeSdkManager(data: any) {
	return request<SystemApiResponseData>({
		url: platformApi.changeSdkManager,
		method: "post",
		data
	})
}
// 移除
export function deleteSdkManager(data: any) {
	return request<SystemApiResponseData>({
		url: platformApi.deleteSdkManager,
		headers: { "Content-Type": "multipart/form-data" },
		method: "post",
		data
	})
}
// 发布
export function batchRelease(data: any) {
	return request<SystemApiResponseData>({
		url: platformApi.getBatchRelease,
		headers: { "Content-Type": "multipart/form-data" },
		method: "post",
		data
	})
}
// 回滚
export function allowRollback(data: any) {
	return request<SystemApiResponseData>({
		url: platformApi.allowRollback,
		headers: { "Content-Type": "multipart/form-data" },
		method: "post",
		data
	})
}
// sdk上传
export function uploadFile(data: any) {
	return request<SystemApiResponseData>({
		url: platformApi.uploadFile,
		headers: { "Content-Type": "multipart/form-data" },
		method: "post",
		data
	})
}
// sdk下载
export function download(params: any) {
	return request<SystemApiResponseData>({
		url: platformApi.download,
		method: "get",
		params
	})
}
