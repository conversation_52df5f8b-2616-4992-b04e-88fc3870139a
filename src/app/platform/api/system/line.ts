import { request } from "@/app/platform/utils/service"
import { platformApi } from "@/app/platform/api/platform"

// 获取线路列表
function getLineList(data: any) {
	return request<SystemApiResponseData>({
		url: platformApi.getLineList,
		method: "post",
		headers: {
			"Content-Type": "multipart/form-data"
		},
		data
	})
}

// 线路保存
function saveAndUpdateLine(data: any) {
	return request<SystemApiResponseData>({
		url:platformApi.saveAndUpdateLine,
		method: "post",
		data
	})
}

// 线路移除
function deleteLine(data: any) {
	return request<SystemApiResponseData>({
		url:platformApi.deleteLine ,
		method: "post",
		headers: {
			"Content-Type": "multipart/form-data"
		},
		data
	})
}

export const LineApi = {
	getLineList,
	saveAndUpdateLine,
	deleteLine
}
