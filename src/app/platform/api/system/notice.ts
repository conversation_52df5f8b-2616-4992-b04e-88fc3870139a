import { request } from "@/app/platform/utils/service"
import { platformApi } from "@/app/platform/api/platform"

// 请求公告列表
function getNoticeList(data: any) {
	return request<SystemApiResponseData>({
		url: platformApi.noticeList,
		method: "post",
		data,
		headers: {
			"Content-Type": "multipart/form-data"
		}
	})
}
// 请求公告详情
function getNoticeGet(data: any) {
	return request<SystemApiResponseData>({
		url: platformApi.noticeGet,
		method: "post",
		data,
		headers: {
			"Content-Type": "multipart/form-data"
		}
	})
}
// 新增/发布/更新公告
function getNoticeSave(data: any) {
	return request<SystemApiResponseData>({
		url: platformApi.noticeSave,
		method: "post",
		data
	})
}
// 公告弹窗
function getNoticeDialog() {
	return request<SystemApiResponseData>({
		url: platformApi.noticeDialog,
		method: "post",
		headers: {
			"Content-Type": "multipart/form-data"
		}
	})
}
// 公告发布
function getNoticePublish(data: any) {
	return request<SystemApiResponseData>({
		url: platformApi.noticePublish,
		method: "post",
		data,
		headers: {
			"Content-Type": "multipart/form-data"
		}
	})
}
// 公告下线
function getNoticeOffline(data: any) {
	return request<SystemApiResponseData>({
		url: platformApi.noticeOffline,
		method: "post",
		data,
		headers: {
			"Content-Type": "multipart/form-data"
		}
	})
}
// 公告移除
function noticeDelete(params: any) {
	return request<SystemApiResponseData>({
		url: platformApi.noticeDelete,
		method: "get",
		params
	})
}
export const CompanyApi = {
    getNoticeList,
    getNoticeGet,
    getNoticeSave,
	getNoticeDialog,
	getNoticePublish,
	getNoticeOffline,
	noticeDelete
}