import { request } from "@/app/platform/utils/service"
import { platformApi } from "@/app/platform/api/platform"

// 获取公司列表
function getCompanyList(data: any) {
	return request<SystemApiResponseData>({
		url: platformApi.getCompanyList,
		method: "post",
		headers: {
			"Content-Type": "multipart/form-data"
		},
		data
	})
}

function getCompanyAllList(data: any) {
	return request<SystemApiResponseData>({
		url: platformApi.getCompanyAllList,
		method: "post",
		headers: {
			"Content-Type": "multipart/form-data"
		},
		data
	})
}

// 保存公司
function saveCompany(data: any) {
	return request<SystemApiResponseData>({
		url: platformApi.saveCompany,
		method: "post",
		data
	})
}

// 获取公司详情
function getCompanyInfo(id: any) {
	return request<SystemApiResponseData>({
		url: platformApi.getCompanyInfo,
		method: "get",
		params: {
			id
		}
	})
}

// 移除公司
function deleteCompany(companyId: any) {
	return request<SystemApiResponseData>({
		url: platformApi.deleteCompany,
		method: "get",
		params: {
			companyId
		}
	})
}
// 启用停用
function startOrEndCompany(companyId: any, status: any) {
	return request<SystemApiResponseData>({
		url: platformApi.startOrEndCompany,
		method: "get",
		params: {
			companyId,
			status
		}
	})
}

export const CompanyApi = {
	getCompanyList,
	getCompanyAllList,
	saveCompany,
	getCompanyInfo,
	deleteCompany,
	startOrEndCompany
}
