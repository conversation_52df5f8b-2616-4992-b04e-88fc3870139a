/* 角色管理相关接口 */
import { request } from "@/app/platform/utils/service"
import { platformApi } from "@/app/platform/api/platform"

/**
 *	新增/修改角色
 * @date 2023-08-08
 */
export function saveOrUpdateBaseRoleApi(data: anyKey) {
	return request<SystemApiResponseData>({
		url: platformApi.saveOrUpdateBaseRoleApi,
		method: "post",
		data
	})
}

/**
 *	角色列表
 * @date 2023-08-09
 */
export function getBaseRoleList(params: anyKey) {
	return request<SystemApiResponseData>({
		url: platformApi.getBaseRoleList,
		method: "post",
		params
	})
}

/**
 *	关联详情
 * @date 2023-08-09
 */
export function getBaseRoleDetail(params: anyKey) {
	return request<any>({
		url: platformApi.getBaseRoleDetail,
		method: "get",
		params
	})
}

/**
 * 所有账号列表
 * @date 2023-08-10
 */
export function getAllPageUsers(params: anyKey) {
	return request<SystemApiResponseData>({
		url: platformApi.getAllPageUsers,
		method: "get",
		params
	})
}

/**
 * 移除
 * @date 2023-08-10
 */
export function removeRoleItem(params: anyKey) {
	return request<SystemApiResponseData>({
		url: platformApi.removeRoleItem,
		method: "get",
		params
	})
}

/**
 * 复制
 * @date 2023-08-10
 */
export function roleReplication(data: anyKey) {
	return request<SystemApiResponseData>({
		url: platformApi.roleReplication,
		method: "post",
		data
	})
}
