/* 标签管理相关接口 */
import { request } from "@/app/platform/utils/service"
import { platformApi } from "@/app/platform/api/platform"

/**
 * 获取标签列表
 * @date 2023-08-07
 */
export function getLabelCodeListApi(data: anyKey) {
	return request<SystemApiResponseData>({
		url: platformApi.getLabelCodeListApi,
		method: "post",
		data,
		headers: { "Content-Type": "multipart/form-data" }
	})
}

/**
 * 新增标签
 * @date 2023-08-07
 */
export function createLabelCodeApi(data: anyKey) {
	return request<SystemApiResponseData>({
		url:platformApi.createLabelCodeApi ,
		method: "post",
		data
	})
}

/**
 * 标签码基本详情
 * @date 2023-08-07
 */
export function getLabelCodeDetailApi(data: anyKey) {
	return request<SystemApiResponseData>({
		url:platformApi.getLabelCodeDetailApi ,
		method: "post",
		data,
		headers: { "Content-Type": "multipart/form-data" }
	})
}

/**
 * 标签码详情列表
 * @date 2023-08-07
 */
export function getLabelCodeDetailListApi(data: anyKey) {
	return request<SystemApiResponseData>({
		url:platformApi.getLabelCodeDetailListApi ,
		method: "post",
		data,
		headers: { "Content-Type": "multipart/form-data" }
	})
}

/**
 * 标签码打印
 * @date 2023-08-07
 */
export function labelCodePrintApi(data: anyKey) {
	return request<SystemApiResponseData>({
		url:platformApi.labelCodePrintApi ,
		method: "post",
		data,
		headers: { "Content-Type": "multipart/form-data" }
	})
}
