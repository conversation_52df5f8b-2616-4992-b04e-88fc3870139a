export interface MsgListRequest {
	/**
	 * 当前页数
	 */
	currentPage?: number
	/**
	 * id，查询该id下的子级
	 */
	id?: number | null
	/**
	 * 每页条数
	 */
	pageSize?: number
}
export interface MsgListResponse {
	code: string
	data: Data
	msg: string
}

export interface Data {
	currentPage: number
	pageSize: number
	records: number
	rows: Row[]
	total: number
}

export interface Row {
	createdBy: string
	createdDate: string
	deleted: boolean
	id: number
	lastModifiedBy: string
	lastModifiedDate: string
	message: string
	receiver: string
	sender: string
	status: string
	title: string
	type: string
}
