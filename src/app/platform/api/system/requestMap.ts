/* 权限管理相关接口 */
import { request } from "@/app/platform/utils/service"
import { platformApi } from "@/app/platform/api/platform"

/**
 * 获取权限树
 * @date 2023-08-08
 */
export function getBaseSystemTreeApi(params: anyKey) {
	return request<SystemApiResponseData>({
		url: platformApi.getBaseSystemTreeApi,
		method: "get",
		params
	})
}
/**
 * 获取当前用户所有角色的菜单权限树（关联权限）
 * @date 2025-05-21
 */
export function getListRoleALLWithTree(params: anyKey) {
	return request<SystemApiResponseData>({
		url: platformApi.getListRoleALLWithTree,
		method: "get",
		params
	})
}

export function getBaseSystemLazyTreeApi(params: anyKey) {
	return request<SystemApiResponseData>({
		url: platformApi.getBaseSystemLazyTreeApi,
		method: "get",
		params
	})
}

/**
 * 根据权限树查询权限列表
 * @date 2023-08-08
 */
export function getBaseSystemListApi(params: anyKey) {
	return request<SystemApiResponseData>({
		url: platformApi.getBaseSystemListApi,
		method: "get",
		params
	})
}

/**
 * 新增/修改权限
 * @date 2023-08-08
 */
export function saveOrUpdateBaseSystemApi(data: anyKey) {
	return request<SystemApiResponseData>({
		url: platformApi.saveOrUpdateBaseSystemApi,
		method: "post",
		data
	})
}

/**
 * 移除权限
 * @date 2023-08-08
 */
export function removeBaseSystemApi(params: anyKey) {
	return request<SystemApiResponseData>({
		url: platformApi.removeBaseSystemApi,
		method: "post",
		params
	})
}

export const requestMapApi = {
	getBaseSystemTreeApi,
	getBaseSystemListApi,
	getBaseSystemLazyTreeApi,
	getListRoleALLWithTree,
	saveOrUpdateBaseSystemApi,
	removeBaseSystemApi
}
