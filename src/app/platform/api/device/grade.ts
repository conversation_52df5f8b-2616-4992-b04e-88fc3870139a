import { request } from "@/app/platform/utils/service"

// 设备分级树
function getGradeTree(data = {}) {
	return request<SystemApiResponseData>({
		headers: {
			"Content-Type": "multipart/form-data"
		},
		url: "/api/device/level/tree",
		method: "post",
		data
	})
}
// 分级列表
function getGradeTable(data: any) {
	return request<SystemApiResponseData>({
		url: "/api/device/level/list",
		method: "post",
		headers: {
			"Content-Type": "multipart/form-data"
		},
		data
	})
}
// 获取节点信息
function getNodeInfo(id: any) {
	return request<SystemApiResponseData>({
		url: "/api/device/level/get",
		method: "get",
		params: {
			id
		}
	})
}
// 查看关联专业
function getProfessionTreeDetail(id: any) {
	return request<SystemApiResponseData>({
		url: "/api/device/level/getMajorTree",
		method: "get",
		params: {
			id
		}
	})
}
// 设备分级保存
function saveGrade(data: any) {
	return request<SystemApiResponseData>({
		url: "/api/device/level/save",
		method: "post",
		data
	})
}
// 获取关联设备型号
function getDeviceDetail(data: any) {
	return request<SystemApiResponseData>({
		url: "/api/device/level/deviceModelList",
		method: "post",
		headers: {
			"Content-Type": "multipart/form-data"
		},
		data
	})
}
// 获取关联专业
function getSubjectTree(id: any) {
	return request<SystemApiResponseData>({
		url: "/api/device/level/getMajorTree",
		method: "get",
		params: {
			id
		}
	})
}
// 移除专业和设备
function deleteSubjectOrDevice(data: any) {
	return request<SystemApiResponseData>({
		url: "/api/device/level/removeSub",
		method: "post",
		headers: {
			"Content-Type": "multipart/form-data"
		},
		data
	})
}
// 移除设备分级
function removeGrade(data: any) {
	return request<SystemApiResponseData>({
		url: "/api/device/level/remove",
		method: "post",
		headers: {
			"Content-Type": "multipart/form-data"
		},
		data
	})
}

export const GradeApi = {
	getGradeTree,
	getGradeTable,
	getNodeInfo,
	getProfessionTreeDetail,
	saveGrade,
	getDeviceDetail,
	getSubjectTree,
	deleteSubjectOrDevice,
	removeGrade
}
