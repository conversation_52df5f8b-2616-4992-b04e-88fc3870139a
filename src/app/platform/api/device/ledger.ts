import { request } from "@/app/platform/utils/service"

// 获取分级信息
export function idGetLevelInfo(params: anyKey) {
	return request<SystemApiResponseData>({
		url: "/api/device/info/getDeviceLevelById",
		method: "get",
		params
	})
}

// 保存设备台账
export function saveLedger(data: anyKey) {
	return request<SystemApiResponseData>({
		url: "/api/device/info/save",
		method: "post",
		data
	})
}

// 设备台账类列表
export function ledgerList(data: anyKey) {
	return request<SystemApiResponseData>({
		url: "/api/device/info/list",
		method: "post",
		headers: { "Content-Type": "multipart/form-data" },
		data
	})
}

// 设备装配日志
export function ledgerLog(data: anyKey) {
	return request<SystemApiResponseData>({
		url: "/api/device/info/logs",
		method: "post",
		headers: { "Content-Type": "multipart/form-data" },
		data
	})
}

// 获取设备台账详情信息
export function ledgerDetailsInfo(params: anyKey) {
	return request<SystemApiResponseData>({
		url: "/api/device/info/getDeviceInfoById",
		method: "get",
		params
	})
}

// 设备装配列表
export function packList(data: anyKey) {
	return request<SystemApiResponseData>({
		url: "/api/device/info/packList",
		method: "post",
		headers: { "Content-Type": "multipart/form-data" },
		data
	})
}

// 设备装配
export function deviceAssemble(data: anyKey) {
	return request<SystemApiResponseData>({
		url: "/api/device/info/deviceAssemble",
		method: "post",
		data
	})
}

// 设备拆卸
export function deviceDisassemble(data: anyKey) {
	return request<SystemApiResponseData>({
		url: "/api/device/info/deviceDisassemble",
		method: "post",
		data
	})
}

// 移除设备台账
export function deleteById(params: anyKey) {
	return request<SystemApiResponseData>({
		url: "/api/device/info/deleteById",
		method: "get",
		params
	})
}
