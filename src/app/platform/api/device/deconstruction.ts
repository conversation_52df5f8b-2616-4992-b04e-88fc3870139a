import { request } from "@/app/platform/utils/service"

/**
 * 获取设备解构列表
 * @date 2023-08-18
 */
function getDecList(data: anyKey) {
	return request<SystemApiResponseData>({
		headers: { "Content-Type": "multipart/form-data" },
		url: "/api/device/structure/findAllModel",
		method: "post",
		data
	})
}

/**
 * 获取子级设备解构列表
 */
function getChildDecList(params: anyKey = {}) {
	return request<SystemApiResponseData>({
		headers: { "Content-Type": "multipart/form-data" },
		url: "/api/device/structure/page",
		method: "post",
		params
	})
}

/**
 * 获取关联装配列表
 * @date 2023-08-28
 */
function getRelevanceDecList(data: anyKey) {
	return request<SystemApiResponseData>({
		headers: { "Content-Type": "multipart/form-data" },
		url: "/api/device/structureDetail/list",
		method: "post",
		data
	})
}

/**
 * 获取设备解构树
 */
function getDecTree(data: anyKey = {}) {
	return request<SystemApiResponseData>({
		headers: { "Content-Type": "multipart/form-data" },
		url: "/api/device/level/listByType",
		method: "post",
		data
	})
}

/**
 * 获取子级设备解构树
 */
function getChildDecTree(params: anyKey = {}) {
	return request<SystemApiResponseData>({
		url: "/api/device/structure/tree",
		method: "get",
		params
	})
}

/**
 *新增/编辑设备解构
 */
function addOrUpdateDec(data: anyKey = {}) {
	return request<SystemApiResponseData>({
		url: "/api/device/structure/save",
		method: "post",
		data
	})
}

/**
 * 新增/编辑关联装配设备
 */
function addOrUpdateRelevance(data: anyKey = {}) {
	return request<SystemApiResponseData>({
		url: "/api/device/structureDetail/save",
		method: "post",
		data
	})
}

/**
 * 获取设备解构详情
 */
function getDecDetail(params: anyKey = {}) {
	return request<SystemApiResponseData>({
		url: "/api/device/structure/get",
		method: "get",
		params
	})
}

/**
 * 获取关联装配设备详情
 */
function getRelevanceDetail(data: anyKey = {}) {
	return request<SystemApiResponseData>({
		headers: { "Content-Type": "multipart/form-data" },
		url: "/api/device/structureDetail/get",
		method: "post",
		data
	})
}

/**
 * 移除设备解构
 */
function delDec(params: anyKey = {}) {
	return request<SystemApiResponseData>({
		url: "/api/device/structure/delete",
		method: "get",
		params
	})
}

/**
 * 移除关联装配设备
 */
function delRelevance(data: anyKey = {}) {
	return request<SystemApiResponseData>({
		headers: { "Content-Type": "multipart/form-data" },
		url: "/api/device/structureDetail/delete",
		method: "post",
		data
	})
}

export const deconstructionApi = {
	getDecList,
	getRelevanceDecList,
	getDecTree,
	getChildDecTree,
	addOrUpdateDec,
	addOrUpdateRelevance,
	getChildDecList,
	getDecDetail,
	getRelevanceDetail,
	delDec,
	delRelevance
}
