/**
 * 新建位置分级
 */
export interface LevelForm {
	[key: string]: any
	/**
	 * 唯一标识
	 */
	id?: number | null
	/**
	 * 位置分级编码
	 */
	code: string
	/**
	 * 是否连续
	 */
	continuous?: boolean
	/**
	 * 是否目录
	 */
	directory?: boolean
	/**
	 * 位置分级名称
	 */
	name: string
	/**
	 * 备注
	 */
	note?: string
	/**
	 * 父级
	 */
	parentLevel?: ParentLevel
	/**
	 * 排序
	 */
	sortNum: number
}

/**
 * 父级
 */
export interface ParentLevel {
	/**
	 * 父级id
	 */
	id: number | null
}
