export interface LevelTableRequest {
	/**
	 * 当前页数
	 */
	currentPage?: number
	/**
	 * id，查询该id下的子级
	 */
	id?: number | null
	/**
	 * 每页条数
	 */
	pageSize?: number
}

export interface LevelTableResponse {
	/**
	 * 当前页
	 */
	currentPage: number
	/**
	 * 每页条数
	 */
	pageSize: number
	/**
	 * 总条数
	 */
	records: number
	rows: Row[]
	/**
	 * 总页数
	 */
	total: number
}

export interface Row {
	/**
	 * 分级编码
	 */
	code: string
	/**
	 * 是否继续
	 */
	continuous: boolean
	/**
	 * 创建人
	 */
	createdBy: string
	/**
	 * 创建时间
	 */
	createdDate: string
	deleted: boolean
	/**
	 * 是否目录
	 */
	directory: boolean
	/**
	 * id
	 */
	id: number
	/**
	 * 修改人
	 */
	lastModifiedBy: string
	/**
	 * 修改时间
	 */
	lastModifiedDate: string
	/**
	 * 分级名称
	 */
	name: string
	/**
	 * 备注
	 */
	note: string
	/**
	 * 父级信息
	 */
	parentLevel: ParentLevel
	/**
	 * 排序
	 */
	sortNum: number
}

/**
 * 父级信息
 */
export interface ParentLevel {
	code: string
	continuous: boolean
	createdBy: null
	createdDate: string
	deleted: boolean
	directory: boolean
	id: number
	lastModifiedBy: null
	lastModifiedDate: string
	name: string
	note: null
	parentLevel: null
	sortNum: number
}
