/**
 * 新建位置信息
 */
export interface InfoForm {
	[key: string]: any
	/**
	 * 锚段信息，当连续位置类型为接触网时，可传锚段信息
	 */
	ahrSectionList?: AhrSectionList[]
	/**
	 * 起始KM，当连续位置类轨道为时，可传起始、结束信息
	 */
	beginKm?: number
	/**
	 * 起始M，当连续位置类轨道为时，可传起始、结束信息
	 */
	beginMetre?: number
	/**
	 * 起始前缀，当连续位置类轨道为时，可传起始、结束信息
	 */
	beginPrefix?: string
	/**
	 * bim信息
	 */
	bimInfo?: string
	/**
	 * 位置信息编码，如果位置分级为目录，则名称和编码都和位置分级的一致且不可修改
	 */
	code: string
	/**
	 * 是否连续
	 */
	continuous?: boolean
	/**
	 * 连续位置类型，从数据字典取值，使用value值
	 */
	dictionarySubitemValue: string
	/**
	 * 结束KM，当连续位置类轨道为时，可传起始、结束信息
	 */
	endKm?: number
	/**
	 * 结束M，当连续位置类轨道为时，可传起始、结束信息
	 */
	endMetre?: number
	/**
	 * 结束前缀，当连续位置类轨道为时，可传起始、结束信息
	 */
	endPrefix?: string
	/**
	 * id，新增不传，修改传
	 */
	id?: number
	/**
	 * 纬度
	 */
	latitude?: string
	/**
	 * 位置分级
	 */
	locationLevel: LocationLevel
	/**
	 * 关联的部门id，传多个id，用逗号分隔
	 */
	locOrgIds?: number[]
	/**
	 * 经度
	 */
	longitude?: string
	/**
	 * 绑定线路
	 */
	line?: Line
	bindLindOptions?: any[]
	/**
	 * 合成编码
	 */
	mixCode: string
	/**
	 * 合成名称
	 */
	mixName: string
	/**
	 * 位置信息名称
	 */
	name: string
	/**
	 * 备注
	 */
	note?: string
	/**
	 * 父级信息
	 */
	parentInfo?: ParentInfo
	/**
	 * 排序
	 */
	sortNum: number
}

export interface AhrSectionList {
	code?: string
	/**
	 * 锚段Id，新增锚段无Id，修改的需要传id
	 */
	id?: number
	num?: number
}

export interface Line {
	/**
	 * 线路Id
	 */
	id: string | undefined
}

/**
 * 位置分级
 */
export interface LocationLevel {
	/**
	 * 位置分级id
	 */
	id?: number
	options?: any[]
	directory?: boolean
	continuous?: boolean
	resource?: any[]
}

/**
 * 父级信息
 */
export interface ParentInfo {
	/**
	 * 父级id
	 */
	id: number | null
	name: string
	code?: string
}
