export interface InfoTableRequest {
	/**
	 * 当前页数
	 */
	currentPage?: number
	/**
	 * id，查询该id下的子级
	 */
	id?: number | null
	/**
	 * 每页条数
	 */
	pageSize?: number
}
export interface InfoTableResponse {
	/**
	 * 当前页
	 */
	currentPage: number
	/**
	 * 每页条数
	 */
	pageSize: number
	/**
	 * 总条数
	 */
	records: number
	rows: Row[]
	/**
	 * 总页数
	 */
	total: number
}

export interface Row {
	ahrSectionList?: string[]
	beginKm?: number
	beginMetre?: number
	beginPrefix?: string
	bimInfo?: string
	/**
	 * 分级编码
	 */
	code?: string
	/**
	 * 是否继续
	 */
	continuous?: boolean
	/**
	 * 创建人
	 */
	createdBy?: string
	/**
	 * 创建时间
	 */
	createdDate?: string
	deleted?: boolean
	dictionarySubitemValue?: string
	endKm?: number
	endMetre?: number
	endPrefix?: string
	/**
	 * id
	 */
	id?: number
	/**
	 * 修改人
	 */
	lastModifiedBy?: string
	/**
	 * 修改时间
	 */
	lastModifiedDate?: string
	latitude?: null
	locationLevel?: LocationLevel
	longitude?: null
	mixCode?: string
	mixName?: string
	/**
	 * 分级名称
	 */
	name?: string
	/**
	 * 备注
	 */
	note?: string
	parentInfo?: ParentInfo
	parentCode: string
	parentName: string
	/**
	 * 排序
	 */
	sortNum?: number
}

export interface LocationLevel {
	code: string
	continuous: boolean
	createdBy: string
	createdDate: string
	deleted: boolean
	directory: boolean
	id: number
	lastModifiedBy: string
	lastModifiedDate: string
	name: string
	note: string
	parentLevel: ParentLevel
	sortNum: number
}

export interface ParentLevel {
	code: string
	continuous: boolean
	createdBy: null
	createdDate: string
	deleted: boolean
	directory: boolean
	id: number
	lastModifiedBy: null
	lastModifiedDate: string
	name: string
	note: null
	parentLevel: null
	sortNum: number
}

export interface ParentInfo {
	ahrSectionList: string[]
	beginKm: null
	beginMetre: null
	beginPrefix: null
	bimInfo: null
	code: string
	continuous: boolean
	createdBy: null
	createdDate: string
	deleted: boolean
	dictionarySubitemValue: string
	endKm: null
	endMetre: null
	endPrefix: null
	id: number
	lastModifiedBy: null
	lastModifiedDate: string
	latitude: null
	locationLevel: null
	longitude: null
	mixCode: string
	mixName: string
	name: string
	note: null
	parentInfo: null
	sortNum: number
}
