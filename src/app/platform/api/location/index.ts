import { request } from "@/app/platform/utils/service"
import { AxiosPromise } from "axios"
import { LevelForm } from "./types/LevelForm"
import { LevelTreeResponse } from "./types/LevelTree"
import { LevelTableRequest, LevelTableResponse } from "./types/LevelTable"
import { InfoTreeResponse } from "./types/InfoTree"
import { InfoTableRequest, InfoTableResponse } from "./types/InfoTable"
import { LevelOptionsRequest, LevelOptionsResponse } from "./types/LevelOptions"
import { InfoForm } from "./types/InfoForm"
import { LevelDeleteRequest } from "./types/LevelDelete"
import {
	InfoBindLineOptionsRequest,
	InfoBindLineOptionsResponse
} from "./types/InfoBindLineOptions"
import { InfoDeleteRequest } from "./types/InfoDelete"

/**
 * 信息分级树
 */
export function getInfoTreeApi(): AxiosPromise<InfoTreeResponse> {
	return request({
		url: "/api/location/info/findAllForTree",
		method: "get"
	})
}

/**
 * 信息表格
 */
export function getInfoTableApi(
	data: InfoTableRequest
): AxiosPromise<InfoTableResponse> {
	return request({
		url: "/api/location/info/findAllByIdByPage",
		method: "get",
		params: data
	})
}

/**
 * 信息表格
 */
export function getInfoDetailApi(data: {
	id: number | undefined
}): AxiosPromise<InfoForm> {
	return request({
		url: "/api/location/info/get",
		method: "get",
		params: data
	})
}

/**
 * 新增位置信息
 *
 * @param data {InfoForm}
 * @returns
 */
export function saveInfoApi(data: InfoForm) {
	if (data.organizationTreeVo)
		data.organizationTreeVo = data.organizationTreeVo[0]
	if (!data.parentInfo || !data.parentInfo.id) data.parentInfo = undefined
	return request({
		url: "/api/location/info/saveOrUpdate",
		method: "post",
		data: data
	})
}

/**
 * 发起流程
 *
 * @param data {InfoForm} 比保存多了一个processDefinitionId
 * @returns
 */
export function startProcess(data: any) {
	if (data.organizationTreeVo)
		data.organizationTreeVo = data.organizationTreeVo[0]
	if (!data.parentInfo || !data.parentInfo.id) data.parentInfo = undefined
	return request({
		url: "/api/location/info/startProcess",
		method: "post",
		data: data
	})
}

/**
 * 移除位置信息
 *
 * @param data {LevelForm}
 * @returns
 */
export function deleteInfoApi(data: InfoDeleteRequest) {
	return request({
		url: "/api/location/info/delete",
		method: "get",
		params: data
	})
}
/**
 * 新增位置信息 绑定线路下拉选项
 *
 * @param data {InfoBindLineOptionsRequest}
 * @returns
 */
export function getInfoBindLineOptionsApi(
	data: InfoBindLineOptionsRequest
): AxiosPromise<InfoBindLineOptionsResponse> {
	return request({
		url: "/api/system/line/listLineForLocInfo",
		method: "post",
		data: data
	})
}

/**
 * 位置分级树
 */
export function getLevelTreeApi(): AxiosPromise<LevelTreeResponse> {
	return request({
		url: "/api/location/level/findAllForTree",
		method: "get"
	})
}

/**
 * 位置分级选项
 */
export function getLevelOptionsApi(
	data: LevelOptionsRequest
): AxiosPromise<LevelOptionsResponse> {
	return request({
		url: "/api/location/level/findTreeByParentLevelId",
		method: "get",
		params: data
	})
}

/**
 * 分级表格
 */
export function getLevelTableApi(
	data: LevelTableRequest
): AxiosPromise<LevelTableResponse> {
	return request({
		url: "/api/location/level/findAllByIdByPage",
		method: "get",
		params: data
	})
}

/**
 * 新增位置分级
 *
 * @param data {LevelForm}
 * @returns
 */
export function saveLevelApi(operation: string, data: LevelForm) {
	if (!data.parentLevel || !data.parentLevel.id) data.parentLevel = undefined

	let url
	if (operation === "create") {
		url = "/api/location/level/save"
	} else if (operation === "update") {
		url = "/api/location/level/update"
	}
	return request({
		url,
		method: "post",
		data: data
	})
}

/**
 * 移除位置分级
 *
 * @param data {LevelForm}
 * @returns
 */
export function deleteLevelApi(data: LevelDeleteRequest) {
	return request({
		url: "/api/location/level/delete",
		method: "get",
		params: data
	})
}

/**
 * 合并位置保存
 */
export function mergeLocationApi(data: any) {
	return request({
		url: "/api/location/info/mergeLocation",
		method: "post",
		data
	})
}
