import { type Directive } from "vue"
import { useUserStoreHook } from "@/app/platform/store/modules/user"

/**
 * 按钮权限全局指令
 * 使用示例：<el-button v-has="'system:device:auth:btn:edit'"></el-button>
 */

// 权限对象类型
type PermissionObject = {
	privilegeType: "invisible" | "disable" | "visible" // invisible-直接不可见；disable-可见但是不可用；visible-可见又可用；
	privillegId: number
	privillegName: string // 例："终端授权-编辑"
	privillegPurview: string // 例："system:device:auth:btn:edit"
}

// 接口类型
interface DirectiveOptions {
	value: string // 按钮权限标识：例："system:device:auth:btn:edit"
	arg?: any
	modifiers?: any // 一个包含修饰符的对象
	oldValue?: any // 之前的值，仅在 beforeUpdate 和 updated 中可用。无论值是否更改，它都可用。
	instance?: any // 使用该指令的组件实例
	dir?: any // 指令的定义对象
}

// 获取登录用户所有按钮权限
const getPermissionList: (arg: any) => PermissionObject[] = (userInfo: any) => {
	if (userInfo && userInfo.roleList && userInfo.roleList.length) {
		const target = userInfo.roleList[0].privillegesList
		if (target && target.length) {
			return target
		}
	}
}

export const btnPermission: Directive = {
	beforeMount(el: HTMLElement, binding: DirectiveOptions) {
		// 登录用户信息
		const { userInfo } = useUserStoreHook()
		const list = getPermissionList(userInfo)
		const { value: btn } = binding
		if (btn) {
			const target = list.find(
				(item: PermissionObject) => item.privillegPurview === btn
			)
			if (target) {
				switch (target.privilegeType) {
					case "invisible":
						el.style.display = "none"
						break
					case "disable":
						el.classList.add("unusableBtn")
						break
					case "visible":
						el.classList.remove("unusableBtn")
						break
				}
			} else {
				el.style.display = "none"
			}
		} else {
			el.style.display = "none"
		}
	}
}
