import { type Directive } from "vue"
// import { useUserStoreHook } from "@/app/platform/store/modules/user"

/** 权限指令，和权限判断函数 checkPermission 功能类似 */
export const permission: Directive = {
	mounted(el, binding) {
		const { value: permissionRoles } = binding

		// const { roles } = useUserStoreHook()
		if (Array.isArray(permissionRoles) && permissionRoles.length > 0) {
			// const hasPermission = roles.some((role: any) =>
			// 	permissionRoles.includes(role)
			// )
			// hasPermission || (el.style.display = "none") // 隐藏
			// hasPermission || el.parentNode?.removeChild(el) // 销毁
			// hasPermission || el.classList.add("unusableBtn") // 销毁

			// hasPermission &&
			permissionRoles.forEach((item: string) => {
				const roleType = item.split(":")[1].split("-")[2]
				if (roleType === "invisible") {
					el.classList.add("permission-hidden-btn")
					el.disabled = true
				} else if (roleType === "disable") {
					el.classList.add("unusableBtn")
					el.disabled = true
				}
			})
		} else {
			return false
			// throw new Error(`need roles!`)
		}
	}
}
