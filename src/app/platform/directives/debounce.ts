/**
 * 全局防抖指令
 * 使用方法示例：<el-button v-debounce:500.leading.trailing="{ cb: function, extArgs: [参数1, ...其他参数] }"></el-button>
 */
import { debounce } from "lodash-es"

type BindingObject = {
	cb: <T>() => T // 回调函数
	extArgs?: any[] // 额外参数数组
}

// 防抖参数配置
type debounceOptions = {
	leading?: boolean // 指定在延迟开始前调用（默认值：false）
	trailing?: boolean // 指定在延迟结束后调用（默认值：true）
}

// 接口类型
interface DirectiveOptions {
	value: BindingObject // 传递给指令的对象
	arg?: any // 传递给指令的参数：delay：延迟时间（ms），【默认延迟300ms】
	modifiers?: debounceOptions // 一个包含修饰符的对象
	// oldValue?: any // 之前的值，仅在 beforeUpdate 和 updated 中可用。无论值是否更改，它都可用。
	// instance?: any // 使用该指令的组件实例
	// dir: any // 指令的定义对象
}

export const debounceDirective = {
	// 在元素被插入到 DOM 前调用
	beforeMount(el: HTMLElement, binding: DirectiveOptions) {
		const { value, arg, modifiers } = binding
		const { cb, extArgs } = value
		const debounceFn: (...args: any[]) => void = debounce(
			cb,
			arg ? number(arg) : 300,
			{
				...modifiers
			}
		)

		el.addEventListener("click", () => {
			if (extArgs && extArgs.length) {
				debounceFn(...extArgs)
			} else {
				debounceFn()
			}
		})
	},
	// 绑定元素的父组件卸载后调用
	unmounted(el: HTMLElement, binding: DirectiveOptions) {
		const { value, arg, modifiers } = binding
		const { cb } = value
		const debounceFn: (args: any) => void = debounce(
			cb,
			arg ? number(arg) : 300,
			{ ...modifiers }
		)
		el.removeEventListener("click", debounceFn)
	}
}
