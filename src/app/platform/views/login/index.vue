<script lang="ts" setup>
import { reactive, ref } from "vue"
import JSEncrypt from "jsencrypt"
import { useRouter } from "vue-router"
import { useUserStore } from "@/app/platform/store/modules/user"
import { type FormInstance, ElMessage } from "element-plus"
import { getCodeImgApi, getLogin<PERSON>ode<PERSON>pi } from "@/app/platform/api/login"
import { type LoginRequestData } from "@/app/platform/api/login/types/login"
import { useUserStoreHook } from "@/app/platform/store/modules/user"
import { usePermissionStoreHook } from "@/app/platform/store/modules/permission"
import { getVersion, systemParamenter } from "@/app/platform/api/login"
import { debounce } from "lodash-es"
import * as ww from "@wecom/jssdk"

const router = useRouter()
const title = import.meta.env.VITE_SYS_TITLE
const enTitle = import.meta.env.VITE_SYS_ENTITLE
const dialogVisible = ref(false)

/** 登录表单元素的引用 */
const loginFormRef = ref<FormInstance | null>(null)
const activeIndex = ref(0)

/** 登录按钮 Loading */
const loading = ref(false)
/** 验证码图片 URL */
const codeUrl = ref("")
/** 登录表单数据 */
const loginFormData: LoginRequestData = reactive({
	username: "",
	password: "",
	code: "",
	unionCode: "",
	clientType: 1 //1表示PC客户端;2表示PDA客户端;
})
/** 登录表单校验规则 */

/** 登录加密*/
const doRSA = (pwd: string) => {
	const publicKey =
		"MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDCNWMv9Jv1APv8mQyagbHsZ+NAdstANZ0gox1ncXNec1aFSKnlG7vJNK9fwkVH4FvIGwDJH481uhk8mokEcxvtlvwCYGlZXYJaxo0s3sdiSJtyBaEmOoCU7Ni6z8oq3hxBqYfCeO+iKqEcySIJqrBmnaV8qrfDhhoSfGkqpvp+CwIDAQAB"
	const encryptor = new JSEncrypt()
	encryptor.setPublicKey(publicKey)
	const encryptPassword = encryptor.encrypt(pwd)
	if (!encryptPassword) {
		ElMessage.warning("登录密码加密错误")
		return ""
	} else {
		return encryptPassword
	}
}

/** 登录逻辑 */
const handleKeydown = (event: any) => {
	if (event.key === "Enter") {
		nextTick(() => {
			if (loginFormData.username !== "") {
				debouncedLogin()
			}
		})
	}
}

const handleLogin = () => {
	//参数验证
	if (loginFormData.username == "") {
		ElMessage.warning("请输入用户名")
		return
	} else if (loginFormData.password == "") {
		ElMessage.warning("请输入密码")
		return
	}
	// else if (
	// 	loginFormData.password.length < 5 ||
	// 	loginFormData.password.length > 16
	// ) {
	// 	ElMessage.warning("长度在 5 到 16 个字符")
	// 	return
	// }
	if (systemParamenterData.value) {
		if (loginFormData.code == "") {
			ElMessage.warning("请输入验证码")
			return
		}
	}
	const encryptPassword = doRSA(loginFormData.password)
	if (!encryptPassword) {
		return
	}
	loading.value = true
	useUserStore()
		.login({ ...loginFormData, password: encryptPassword })
		.then(async () => {
			const userStore = useUserStoreHook()
			const permissionStore = usePermissionStoreHook()
			try {
				await permissionStore.setRoutes()
				router.replace({ path: "/" })
			} catch (err: any) {
				// 过程中发生任何错误，都直接重置 Token，并重定向到登录页面
				userStore.resetToken()
				if (systemParamenterData.value) createCode()
				loginFormData.password = ""
			}
		})
		.catch((error) => {
			if (
				error.code === "ECONNABORTED" ||
				error.message === "Network Error" ||
				error.message.includes("timeout")
			) {
				loginFormData.password = ""
			} else {
				if (systemParamenterData.value) createCode()
				loginFormData.password = ""
			}
		})
		.finally(() => {
			loading.value = false
		})
}

// 防抖登录
const debouncedLogin = debounce(handleLogin, 1000, {
	leading: true,
	trailing: false
})

/** 获取验证码key */
const createCode = () => {
	// 先清空验证码的输入
	loginFormData.unionCode = ""
	loginFormData.code = ""
	codeUrl.value = ""
	getLoginCodeApi()
		.then((res) => {
			if (res && res.unionCode) {
				loginFormData.unionCode = res.unionCode
				getCodeImg(res.unionCode)
			}
		})
		.catch((err) => {
			throw new Error("getLoginCodeApi():::" + err)
		})
}

/**
 * 获取验证码图片
 */
const getCodeImg = (unionCode: string) => {
	getCodeImgApi({ unionCode })
		.then((res) => {
			const blob = new Blob([res], { type: "image/*" })
			const reader = new FileReader()
			reader.readAsDataURL(blob)
			reader.onload = () => {
				codeUrl.value = reader.result as string
			}
		})
		.catch((err) => {
			throw new Error("getCodeImgApi():::" + err)
		})
}

//获取版本号
let versionList = reactive({
	framework: "0.0.0",
	baseline: "0.0.0",
	project: "0.0.0"
})
//基线方法：  登录页面获取 版本号
const getVersionData = () => {
	getVersion().then((res: any) => {
		versionList = Object.assign(versionList, res)
	})
}
getVersionData()

//验证二维码是否展示接口
const systemParamenterData = ref(false)
const qwSystemParamenter = ref(false)
const getSystemParamenter = () => {
	const data = { key: "CAPTCHA" }
	systemParamenter(data).then((res: any) => {
		systemParamenterData.value = res == "false" ? false : true
		if (res == "true") createCode()
	})
	const qiwei = { key: "ENTERPRISE_SCAN" }
	systemParamenter(qiwei).then((res: any) => {
		qwSystemParamenter.value = res == "false" ? false : true
	})
}
getSystemParamenter()

const year = ref(0)
const getCurrentDate = () => {
	const date = new Date()
	year.value = date.getFullYear()
}
getCurrentDate()

let loginCard = ref<any>()
const handleLoginMethodChange = (index: number) => {
	activeIndex.value = index
	if (index === 1) {
		dialogVisible.value = true
		nextTick(() => {
			loginCard = ww.createWWLoginPanel({
				el: "#ww_login",
				params: {
					login_type: import.meta.env.VITE_WW_LOGIN_TYPE,
					appid: import.meta.env.VITE_WW_LOGIN_APP_ID,
					agentid: import.meta.env.VITE_WW_LOGIN_AGENTID,
					redirect_uri: import.meta.env.VITE_WW_LOGIN_REDIRECT_URI,
					state: import.meta.env.VITE_WW_LOGIN_STATE,
					redirect_type: import.meta.env.VITE_WW_LOGIN_REDIRECT_TYPE,
					panel_size: import.meta.env.VITE_WW_LOGIN_PANEL_SIZE
				},
				onCheckWeComLogin({ isWeComLogin }) {
					console.log("企业微信1：", isWeComLogin)
				},
				onLoginSuccess({ code }) {
					console.log(
						"企业微信2：",
						{ code },
						{ wxcpCode: code, clientType: 5 }
					)
					useUserStore()
						.login({ wxcpCode: code, clientType: 5 })
						.then(async () => {
							console.log("====3")
							const userStore = useUserStoreHook()
							const permissionStore = usePermissionStoreHook()
							try {
								await permissionStore.setRoutes()
								router.replace({ path: "/" })
							} catch (err: any) {
								// 过程中发生任何错误，都直接重置 Token，并重定向到登录页面
								userStore.resetToken()
								if (systemParamenterData.value) createCode()
								loginFormData.password = ""
							}
						})
						.catch((error) => {
							console.log("====4", error)
							if (
								error.code === "ECONNABORTED" ||
								error.message === "Network Error" ||
								error.message.includes("timeout")
							) {
								loginFormData.password = ""
							} else {
								if (systemParamenterData.value) createCode()
								loginFormData.password = ""
							}
						})
						.finally(() => {
							console.log("====5")
							loading.value = false
						})
				},
				onLoginFail(err) {
					console.log("企业微信3：", err)
				}
			})
			console.log("登录组件4", loginCard)
		})
	}
}
const handleClose = (done: () => void) => {
	loginCard.unmount()
	done()
	dialogVisible.value = false
	activeIndex.value = 0
}
</script>

<template>
	<div class="login-container">
		<div class="login-banner">
			<div class="login-card">
				<div class="title">
					<h1>{{ title }}</h1>
					<h2>{{ enTitle }}</h2>
					<h2
						class="version"
						v-if="versionList.framework"
						style="font-size: var(--pitaya-fs-12)"
					>
						Platform v{{ versionList.framework }} - Baseline v{{
							!!versionList.baseline ? versionList.baseline : "0.0.0"
						}}
					</h2>
					<h2 class="copyright" style="font-size: var(--pitaya-fs-12)">
						Copyright © {{ year }} All rights reserved.
					</h2>
				</div>
				<div class="f-content">
					<div class="f-contentTabTittle" v-if="qwSystemParamenter">
						<div
							class="accountPassword"
							:class="{ activeAccount: activeIndex === 0 }"
						>
							<div @click="handleLoginMethodChange(0)">
								<img
									v-if="activeIndex === 0"
									src="@/assets/images/yaoshi.png"
								/>
								<img v-else src="@/assets/images/yaoshi.png" />
								账号密码登录
							</div>
							<span class="item-selected-markerStrip"></span>
						</div>
						<div
							class="accountPassword"
							:class="{ activeAccount: activeIndex === 1 }"
						>
							<div @click="handleLoginMethodChange(1)">
								<img v-if="activeIndex === 1" src="@/assets/images/qiwei.png" />
								<img v-else src="@/assets/images/qiwei1.png" />
								企业微信登录
							</div>
							<span class="item-selected-markerStrip"></span>
						</div>
					</div>
					<div
						class="content"
						:class="{ contentpadding: !qwSystemParamenter }"
						v-if="!activeIndex"
					>
						<el-form ref="loginFormRef" :model="loginFormData">
							<el-form-item>
								<el-input
									class="login-input"
									v-model.trim="loginFormData.username"
									placeholder="请输入员工账号"
									type="text"
									tabindex="1"
									size="large"
									@keydown="handleKeydown"
									style="width: 320px; --el-input-height: 42px"
								>
									<!-- 使用插槽插入自定义图标 -->
									<template #prefix>
										<font-awesome-icon
											:icon="['fas', 'user']"
											style="margin-right: 8px; color: #666"
										/>
									</template>
								</el-input>
							</el-form-item>
							<el-form-item>
								<el-input
									class="login-input"
									v-model.trim="loginFormData.password"
									placeholder="请输入登录密码"
									type="password"
									tabindex="2"
									size="large"
									show-password
									@keydown="handleKeydown"
									style="width: 320px; --el-input-height: 42px"
								>
									<!-- 使用插槽插入自定义图标 -->
									<template #prefix>
										<font-awesome-icon
											:icon="['fas', 'fa-lock']"
											style="margin-right: 8px; color: #666"
										/>
									</template>
								</el-input>
							</el-form-item>
							<el-form-item v-if="systemParamenterData">
								<el-input
									class="login-input"
									v-model.trim="loginFormData.code"
									placeholder="请输入验证码"
									type="text"
									tabindex="3"
									maxlength="7"
									size="large"
									@keydown="handleKeydown"
									style="width: 320px; --el-input-height: 42px"
								>
									<!-- 使用插槽插入自定义图标 -->
									<template #prefix>
										<font-awesome-icon
											:icon="['fas', 'fa-tag']"
											style="margin-right: 8px; color: #666"
										/>
									</template>
									<template #append>
										<el-image
											:src="codeUrl"
											@click="createCode"
											draggable="false"
										>
											<template #placeholder>
												<el-icon>
													<Picture />
												</el-icon>
											</template>
											<template #error>
												<el-icon>
													<Loading />
												</el-icon>
											</template>
										</el-image>
									</template>
								</el-input>
							</el-form-item>
							<el-button
								v-btn
								:loading="loading"
								type="primary"
								size="large"
								@click.prevent="debouncedLogin"
								><span style="font-size: var(--pitaya-fs-14)"
									>登录</span
								></el-button
							>
						</el-form>
					</div>
					<div
						class="content-qw"
						v-if="activeIndex === 1"
						:class="{ hasData: systemParamenterData }"
					>
						加载中～
					</div>
				</div>
			</div>
		</div>
		<!-- 弹窗企业微信二维码 -->
		<el-dialog v-model="dialogVisible" width="520" :before-close="handleClose">
			<div id="ww_login" />
		</el-dialog>
	</div>
</template>

<style lang="scss" scoped>
.login-container {
	.el-input__wrapper {
		box-shadow: none;
	}

	.el-input-group__append {
		background: #fff;
		padding-right: 5px !important;
		box-shadow: none !important;
	}

	.el-input__wrapper:hover {
		box-shadow: none;
	}
	:deep(.el-dialog__header) {
		margin-right: 0px !important;
	}
	:deep(.el-dialog__headerbtn) {
		top: -5px !important;
	}
	:deep(.el-dialog__body) {
		padding: 0 20px 40px 20px;
	}
}
</style>
<style lang="scss" scoped>
.login-container {
	display: flex;
	justify-content: center;
	align-items: center;
	position: relative;
	width: 100%;
	height: 100%;
	background: url("@/assets/images/bg.png") repeat;

	.login-banner {
		width: 100%;
		height: 580px;
		background: url("@/assets/images/bg-banner.png") no-repeat;
		background-size: cover;
		box-shadow: 0px 0px 4px 2px #999;
	}

	.login-card {
		height: 580px;
		display: flex;
		justify-content: center;
		align-items: center;

		.title {
			width: 354px;

			h1 {
				color: #fff;
				font-size: 39px;
				font-weight: bold;
			}

			h2 {
				color: #ffffff;
				font-size: 12px;
				width: 345px;
				margin-top: 5px;
			}
		}

		.f-contentTab {
			width: 236px;
			margin-top: 10px;

			.contentTab-tabs {
				.custom-tabs-label {
					color: #fff;
					padding: 0;
					margin: 0;
				}
			}
		}

		// 添加企业微信扫码
		.content-qw {
			text-align: center;
			height: 175px;
			color: #fff;
			line-height: 175px;
		}

		.hasData {
			height: 237px;
			/* 存在数据时的高度 */
			line-height: 237px;
		}

		.f-contentTabTittle {
			display: flex;
			flex-direction: row;
			margin-top: 15px;

			.accountPassword {
				width: 157px;
				padding: 0 25px;
				font-size: 14px;
				font-family: "Microsoft YaHei", 微软雅黑;
				text-align: left;
				color: rgb(153, 153, 153);
				line-height: 34px;
				top: 0px;
				display: flex;
				flex-direction: column;
				align-items: center;
				img {
					width: 15px;
					height: 15px;
					margin-right: 2px;
					margin-bottom: 3px;
					vertical-align: middle;
				}
			}

			.activeAccount {
				color: rgba(255, 255, 255, 1);

				.item-selected-markerStrip {
					background-color: rgb(255, 255, 255);
					width: 104px;
					height: 4px;
				}
			}
		}

		.f-content {
			display: flex;
			flex-direction: column;
			align-items: center;
			width: 445px;
			// height: 358px;
			background-color: rgba(0, 0, 0, 0.29);
			filter: drop-shadow(rgb(6, 47, 93) 0px 0px 10px);
			border-radius: 5.5px;
			transition: unset;
			padding-bottom: 40px;
			.contentpadding {
				padding-top: 25px;
			}
			.content {
				// padding: 50px;
				border-radius: 5px;
				margin-top: 15px;

				:deep(.el-form-item) {
					margin-bottom: 20px;
				}

				:deep(.el-input-group__append) {
					padding: 0 !important;
					overflow: hidden;
					.el-image {
						width: 132px;
						height: 42px;
						border-left: 0px;
						user-select: none;
						cursor: pointer;
						text-align: center;
					}
				}

				.el-button {
					width: 320px;
					// color: #26529d !important;
					background: #26529d;
					--el-button-bg-color: rgba(182, 232, 253, 0.9) !important;
					--el-button-hover-bg-color: rgba(182, 232, 253, 0.7) !important;
					--el-button-active-bg-color: rgba(182, 232, 253, 1) !important;
					--el-button-size: 36px;
				}
			}
		}
	}

	.login-input {
		:deep(input.el-input__inner) {
			box-shadow: inset 0 70px #ffffff;
		}
	}
}
</style>
@/app/platform/store/modules/user@/app/platform/store/modules/user@/app/platform/store/modules/permission
@/app/platform/api/login@/app/platform/api/login/types/login
