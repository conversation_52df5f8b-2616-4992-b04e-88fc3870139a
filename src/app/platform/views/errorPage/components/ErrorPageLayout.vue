<script lang="ts" setup>
import { useUserStore } from "@/app/platform/store/modules/user"
const router = useRouter()
const userStore = useUserStore()
function handleSwitchAccount() {
	userStore.logout()
}
</script>
<template>
	<div class="error-page">
		<div class="error-page-svg">
			<slot />
		</div>
		<div class="btn-list">
			<el-button v-btn type="primary" @click="router.push('/')"
				>回到首页</el-button
			>
			<el-button v-btn type="primary" @click="handleSwitchAccount"
				>重新登录</el-button
			>
		</div>
	</div>
</template>

<style lang="scss" scoped>
.error-page {
	height: 100%;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	&-svg {
		width: 400px;
		margin-bottom: 50px;
	}
}

.btn-list {
	display: flex;
}
</style>
@/app/platform/store/modules/user
