/**
 * 标准流程相关类型定义
 */

// 流程步骤状态枚举
export enum ProcessStepStatus {
  COMPLETED = 'completed',
  CURRENT = 'current', 
  PENDING = 'pending',
  ERROR = 'error'
}

// 主流程步骤接口
export interface ProcessStep {
  /** 步骤ID */
  id: string
  /** 步骤标题 */
  title: string
  /** 步骤时间 */
  time: string
  /** 步骤状态 */
  status: ProcessStepStatus
  /** 子步骤列表 */
  subSteps: SubStep[]
  /** 步骤描述 */
  description?: string
  /** 负责人 */
  assignee?: string
}

// 子步骤接口
export interface SubStep {
  /** 子步骤ID */
  id: string
  /** 子步骤标题 */
  title: string
  /** 子步骤时间 */
  time: string
  /** 子步骤内容 */
  content: string
  /** 操作人员 */
  operator?: string
  /** 子步骤状态 */
  status: ProcessStepStatus
  /** 备注 */
  remark?: string
}

// 流程信息接口
export interface ProcessInfo {
  /** 流程ID */
  id: string | number
  /** 流程名称 */
  name: string
  /** 流程描述 */
  description?: string
  /** 创建时间 */
  createTime: string
  /** 更新时间 */
  updateTime?: string
  /** 流程状态 */
  status: ProcessStepStatus
  /** 流程步骤列表 */
  steps: ProcessStep[]
}

// API响应接口
export interface ProcessApiResponse<T = any> {
  code: string
  msg: string
  data: T
  alertType?: string
}

// 更新步骤状态请求参数
export interface UpdateStepStatusRequest {
  /** 流程ID */
  processId: string | number
  /** 步骤ID */
  stepId: string
  /** 新状态 */
  status: ProcessStepStatus
  /** 操作人员 */
  operator?: string
  /** 备注 */
  remark?: string
}

// 流程查询参数
export interface ProcessQueryParams {
  /** 流程ID */
  processId?: string | number
  /** 流程名称 */
  name?: string
  /** 状态 */
  status?: ProcessStepStatus
  /** 创建时间范围 */
  createTimeRange?: [string, string]
  /** 页码 */
  page?: number
  /** 每页大小 */
  pageSize?: number
}
