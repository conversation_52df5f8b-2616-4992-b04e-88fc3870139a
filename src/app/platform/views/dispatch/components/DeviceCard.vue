<!--
 * @Author: liulianming
 * @Date: 2025-07-15 10:00:00
 * @LastEditors: liulianming
 * @LastEditTime: 2025-07-30 22:38:23
 * @Description: 设备卡片组件
-->
<script lang="ts" setup>
// 定义设备状态类型
type DeviceStatus = 0 | 1 | 2 | 3

// 定义设备类型
interface Device {
	id: number
	deviceName: string
	deviceCode: string
	locationDesc: string
	reportTime: string
	faultDesc: string
	faultCode: string
	deviceGrade: DeviceStatus
	faultLevel: number
	isDrill: number
}

// 定义props
const props = defineProps<{
	device: Device
}>()
watch(()=>props.device, (newVal) => {
	console.log(newVal)
})
// 定义事件
const emit = defineEmits<{
	(e: 'fault', device: Device): void
	(e: 'itemClick', device: Device): void
}>()

// 获取状态对应的样式
const getStatusStyle = (status: number) => {
	const styles = {
		0: {
			background: '#FFF7F7',
			borderColor: '#FFCCC7',
			color: 'rgb(229, 112, 107)'
		},
		1: {
			background: '#FFF7E6',
			borderColor: '#FFE7BA',
			color: 'rgb(245, 155, 34)'
		},
		2: {
			background: '#E6F7FF',
			borderColor: '#BAE7FF',
			color: 'rgb(0, 161, 252)'
		},
		3: {
			background: '#F6FFED',
			borderColor: '#B7EB8F',
			color: 'rgb(75, 174, 137)'
		}
	} as const

	return styles[status as keyof typeof styles] || {}
}

// 获取故障等级对应的颜色
const getGradeColor = (grade: number) => {
	const colors = {
		0: 'rgb(229, 112, 107)',
		1: 'rgb(245, 155, 34)',
		2: 'rgb(0, 161, 252)',
		3: 'rgb(75, 174, 137)'
	} as const

	return colors[grade as keyof typeof colors] || 'rgb(229, 112, 107)'
}

// 故障停按钮点击事件
const handleFaultClick = () => {
	// emit('fault', props.device)
}

const handleItemClick = () => {
	emit('itemClick', props.device)
}
</script>

<template>
	<div class="device-card" :style="getStatusStyle(device.faultLevel)" @click="handleItemClick">
		<!-- 新增“演”字icon，绝对定位在右上角 -->
		<div class="yan-icon" v-if="device.isDrill === 1">演</div>
		<div class="card-top">
			<div class="device-icon" :style="{ color: getStatusStyle(device.faultLevel).color }">
				<font-awesome-icon :icon="['fas', 'bell']" />
			</div>
			<div class="device-info">
				<div class="device-name">{{ device.faultDesc }}</div>
				<div class="device-code">{{ device.faultCode }}</div>
			</div>
			<div class="status-grade" :style="{ color: getGradeColor(device.faultLevel) }">
				{{ device.faultLevel==0 ? 'Ⅰ' : device.faultLevel==1 ? 'Ⅱ' : device.faultLevel==2 ? 'Ⅲ' : '' }}
			</div>
		</div>
		<div class="card-bottom">
			<div class="info-section">
				<div class="location">
					<span class="label">位置信息：</span>
					<span>{{ device.locationDesc }}</span>
				</div>
				<div class="report-time">
					<span class="label">上报时间：</span>
					<span>{{ device.reportTime }}</span>
				</div>
			</div>
			<div class="action-section">
				<el-tag
					size="small"
					class="fault-btn"
					:type="device.faultLevel === 0 ? 'danger' : device.faultLevel === 1 ? 'warning' : device.faultLevel === 2 ? 'info' : 'success'"
				>故障</el-tag>
			</div>
		</div>
	</div>
</template>

<style lang="scss" scoped>
.device-card {
	position: relative;
	border-radius: 4px;
	border: 1px solid;
	transition: all 0.3s;
	overflow: hidden;
	height: 120px;
	cursor: pointer;

	.card-top {
		display: flex;
		align-items: center;
		padding: 12px 16px;
		gap: 12px;
		background: white;

		.device-icon {
			font-size: 24px;
		}

		.device-info {
			flex: 1;
			min-width: 0;
			color: #666;
			.device-name {
				font-size: 12px;
				font-weight: 500;
				margin-bottom: 4px;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
			}

			.device-code {
				font-size: 12px;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
			}
		}

		.status-grade {
			font-size: 24px;
			flex-shrink: 0;
			min-width: 20px;
			text-align: center;
			font-family: '微软雅黑';
			font-variant-numeric: oldstyle-nums;
		}
	}

	.yan-icon {
		position: absolute;
		top: 2px;
		right: 4px;
		z-index: 2;
		font-size: 12px;
		color: rgb(0, 161, 252);
		width: 16px;
		height: 16px;
	}

	.card-bottom {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 12px 16px;
		border-top: 1px solid rgba(0, 0, 0, 0.06);
		background: rgba(255, 255, 255, 0.4);

		.info-section {
			flex: 1;
			min-width: 0;

			.location, .report-time {
				font-size: 12px;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
				color: #333;

			}

			.location {
				margin-bottom: 4px;
			}
		}

		.action-section {
			margin-left: 16px;

			:deep(.fault-btn) {
				font-size: 12px;
				height: 24px;
				padding: 0 8px;
				border-radius: 2px;
				background-color: transparent;

				&.info {
					border: 1px solid rgb(0, 161, 252) !important;
					color: rgb(0, 161, 252) !important;
				}

				&.warning {
					border: 1px solid rgb(245, 155, 34) !important;
					color: rgb(245, 155, 34) !important;
				}

				&.success {
					border: 1px solid rgb(75, 174, 137) !important;
					color: rgb(75, 174, 137) !important;
				}

				&.danger {
					border: 1px solid rgb(229, 112, 107) !important;
					color: rgb(229, 112, 107) !important;
				}

			}
		}
	}
}


</style>
