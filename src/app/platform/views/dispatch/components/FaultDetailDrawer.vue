<!--
 * @Author: liulianming
 * @Date: 2025-07-29 09:33:47
 * @LastEditors: liulianming
 * @LastEditTime: 2025-07-30 09:24:39
 * @Description:
-->
<!--
 * @Author: liulianming
 * @Description: 故障详情抽屉
-->
<script lang="ts" setup>
import { ref, watch } from "vue"
import FaultDetail from "./FaultDetail.vue"
const props = defineProps<{
	id?: string | number
	visible: boolean
	detail: any
	dictOptions: any
}>()
const drawerLoading = ref(false)

const emit = defineEmits(["update:visible", "clickBtn"])
const leftTitle = computed(() => ({
	name: ["故障信息【" + props.detail.faultCode+"】"],
	icon: ["fas", "square-share-nodes"]
}))
// 监听visible变化
watch(
	() => props.visible,
	(val) => {
		if (!val) {
			emit("update:visible", false)
		}
	}
)

// 监听id变化，获取故障详情
watch(
	() => props.id,
	async (id) => {
		if (id) {
			// TODO: 调用接口获取故障详情
			// const detail = await getFaultDetail(id)
			// faultDetail.value = detail
		}
	},
	{ immediate: true }
)
const formBtnList = [
	{
		name: "标准处置流程选择",
		icon: ["fas", "file"]
	}
]
defineOptions({
	name: "FaultDetailDrawer"
})

const handleClickBtn = (btnName: string | undefined) => {
	emit("clickBtn", btnName)
}
</script>

<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<div class="drawer-column">
			<div class="rows">
				<Title :title="leftTitle" />
				<el-scrollbar>
					<FaultDetail :detail="detail" :dictOptions="dictOptions" :showProgressInfo="false" :loading="drawerLoading"/>
				</el-scrollbar>
			</div>
			<ButtonList
				class="footer"
				:button="formBtnList"
				@onBtnClick="handleClickBtn"
			/>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.drawer-column {
	width: 100%;
}

.label-text {
	width: 120px;
}
</style>
