<!-- 需求清单 新建/编辑页 V2.0-->
<template>
	<div class="drawer-container" v-loading="drawerLoading">
		<!-- 左侧表单区域 -->
		<div class="drawer-column left">
			<div class="rows">
				<Title :title="drawerLeftTitle" />
				<el-scrollbar>
					<FaultDetail
						:detail="detail"
						:processDetail="processFlowDetail"
						:loading="processFlowLoading"
						:loading1="loading1"
						:showProgressInfo="true"
						:dictOptions="dictOptions"
					/>
				</el-scrollbar>
			</div>
		</div>
		<div class="drawer-column center">
			<div class="rows">
				<Title :title="drawerCenterTitle"> </Title>
				<el-scrollbar
					v-loading="processFlowLoading"
					element-loading-text="加载中..."
				>
					<StandardProcessInfoCenter :processDetail="processFlowDetail" />
				</el-scrollbar>
				<div class="flex-row-center">
					<el-button type="primary" class="play-btn" :icon="VideoCameraFilled"
						>观看执行视频记录</el-button
					>
				</div>
			</div>
		</div>
		<!-- 右侧table区域 -->
		<div class="drawer-column right">
			<div class="rows">
				<Title :title="drawerRightTitle">
					<Tabs class="tabs" :tabs="tabList" @on-tab-change="handleTabChange" />
				</Title>
				<el-scrollbar>
					<pitaya-table
						ref="tableRef"
						:columns="tableProp"
						:table-data="tableData"
						:need-selection="false"
						:single-select="false"
						:need-index="false"
						:need-pagination="true"
						:total="pageTotal"
						:table-loading="tableLoading"
						@on-selection-change="selectedTableList = $event"
						@on-current-page-change="onCurrentPageChange"
					>
					</pitaya-table>
				</el-scrollbar>
			</div>
			<button-list
				class="footer"
				:button="btnRightConf"
				@on-btn-click="handleFormAction"
			/>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref } from "vue"
import { FormElementType } from "@/app/baseline/views/components/define"
import { BaseLineSysApi } from "@/app/baseline/api/system"
import { NeedApi } from "@/app/baseline/api/plan/need"
import {
	getProcessDetailByFault,
	listProcessTreeFlowExecuteUserPage
} from "@/app/baseline/api/dispatch"
import { VideoCameraFilled } from "@element-plus/icons-vue"
import { useTbInit } from "@/app/baseline/views/components/tableBase"
import { useDictInit } from "@/app/baseline/views/components/dictBase"
import { useMessageBoxInit } from "@/app/baseline/views/components/messageBox"
import { ICostCategoryStatus } from "@/app/baseline/utils/types/system-cost-category"
import FaultDetail from "./FaultDetail.vue"
import StandardProcessInfoCenter from "@/app/platform/views/dispatch/components/standardProcessInfoCenter.vue"
import { formatSecondsToTime } from "@/app/baseline/utils"

const props = defineProps<{
	detail: any
	dictOptions: any
	loading1: boolean
}>()

// 处置流程信息
const processFlowDetail = ref<any>({})
const processFlowLoading = ref(false)

watch(
	() => props.detail,
	(val) => {
		console.log("🚀 ~ val:", val)
		// 当detail变化时，获取处置流程信息
		if (val && val.id) {
			getProcessFlowDetail(val.id)
		}
	},
	{
		deep: true,
		immediate: true
	}
)
const tabList = ["参与人员"]
const emit = defineEmits<{
	(e: "update"): void
	(e: "close"): void
}>()

const { dictFilter, getDictByCodeList } = useDictInit()
const { showDelConfirm, showWarnConfirm } = useMessageBoxInit()

const drawerLeftTitle = computed(() => ({
	name: ["标准处置流程执行信息【" + props.detail.faultCode + "】"],
	icon: ["fas", "square-share-nodes"]
}))

const drawerCenterTitle = computed(() => {
	return {
		name: [
			"标准处置流程计时【" +
				(processFlowDetail.value.executeTime
					? formatSecondsToTime(processFlowDetail.value.executeTime)
					: "-") +
				"】"
		],
		icon: ["fas", "square-share-nodes"]
	}
})
const drawerRightTitle = {
	name: ["扩展信息"],
	icon: ["fas", "square-share-nodes"]
}

const drawerLoading = ref(false)
const btnRightConf = [
	{
		name: "关闭",
		icon: ["fas", "circle-check"]
	}
]
const activeTab = ref<number>(0)
const handleTabChange = (index: number) => {
	activeTab.value = index
}
/**
 * 保存旧的表单数据
 */
const oldFormData = ref<string>()

const formData = ref<Record<string, any>>({})

/**
 * 表单项配置
 */
const formEl: FormElementType[][] = [
	[{ label: "姓名", name: "label", maxlength: 50 }],
	[
		{
			label: "职位",
			name: "majorId_view",
			vname: "majorId",
			type: "treeSelect",
			treeApi: BaseLineSysApi.getProfessionTree
		}
	],
	[
		{
			label: "所属部门",
			name: "expenseCategory_view",
			vname: "expenseCategory",
			type: "treeSelect",
			treeApi: () =>
				BaseLineSysApi.getCostCategoryTree({
					status: ICostCategoryStatus.Started
				})
		}
	],
	[
		{
			label: "用户操作",
			name: "remark",
			maxlength: 200,
			rows: "3",
			type: "textarea"
		}
	]
]

/**
 * 获取处置流程详情
 */
async function getProcessFlowDetail(faultId: string) {
	if (!faultId) return

	try {
		processFlowLoading.value = true
		const res = await getProcessDetailByFault(faultId)
		processFlowDetail.value = res
		console.log("🚀 ~ 处置流程详情:", res)
	} catch (error) {
		console.error("获取处置流程详情失败:", error)
	} finally {
		processFlowLoading.value = false
	}
}

function handleFormAction(btnName?: string) {
	emit("close")
}

const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	pageTotal,
	selectedTableList,
	fetchParam,
	currentPage,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = useTbInit()
tableProp.value = [
	{ label: "姓名", prop: "empName", width: "100px" },
	{ label: "职位", prop: "postName", width: "120px" },
	{
		label: "所属部门",
		prop: "roleName"
	},
	{ label: "用户操作", prop: "operate" },
	{
		label: "操作时间",
		prop: "updateTime",
		width: "150px"
	}
]
fetchFunc.value = listProcessTreeFlowExecuteUserPage
function getTableData(data?: { [propName: string]: any }) {
	currentPage.value = 1
	tableRef.value?.resetCurrentPage()
	console.log(props.detail)

	fetchParam.value = {
		executeMainId: props.detail.executeMainId,
		...fetchParam.value,
		...data
	}
	fetchTableData()
}
/**
 * 左侧详情
 */
function getDetail() {
	if (props.detail.id) {
		drawerLoading.value = true
		NeedApi.getInfoById(props.detail.id)
			.then((res: any) => {
				formData.value = { ...res }

				oldFormData.value = JSON.stringify(formData.value)
			})
			.finally(() => {
				drawerLoading.value = false
			})
	}
}
onMounted(async () => {
	await getDictByCodeList(["INVENTORY_UNIT"])
	// getDetail()
	// if (props.detail.id) {
	// 	fetchTableData()
	// }
	getTableData()
})
</script>

<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";
.custom-q {
	margin: 10px 0px -10px 10px !important;
}
.drawer-container {
	.left {
		flex: 0 0 570px;
		padding: 0 15px 0 5px;
	}
	.center {
		padding: 0 15px;
		flex: 0 0 500px;
		&::after {
			content: "";
			position: absolute;
			right: 0;
			top: -10px;
			width: 1px;
			height: calc(100% + 20px);
			background-color: #ccc;
		}
	}
	.right {
		flex: 1;
		padding: 0 15px;
	}

	.el-scrollbar {
		height: calc(100% - 85px);
	}
}
.flex-row-center {
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: center;
}
.play-btn {
	height: 45px;
	width: 468px;
	background-color: transparent !important;
	border: 1px dashed #409eff !important;
	color: #409eff !important;
	&:hover {
		background-color: #409eff !important;
		color: #fff !important;
	}
}
.common-title-wrapper {
	padding: 0 15px 15px 0;
}
</style>
