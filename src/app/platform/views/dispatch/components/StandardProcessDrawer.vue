<!--
 * @Author: liulianming
 * @Description: 故障详情抽屉
-->
<script lang="ts" setup>
import { ref, watch, computed } from "vue"
import {
	getLocComposeTreeApi,
	getStandardProcessList
} from "@/app/baseline/api/dispatch"
import { useDictInit } from "@/app/baseline/views/components/dictBase"
import XEUtils from "xe-utils"
const { dictOptions, getDictByCodeList, dictFilter } = useDictInit()
const locComposeTree = ref<any[]>([])
const documentListLoading = ref(false)
/**
 * 查询条件 配置
 */
const queryArrList = ref<any[]>([
	{
		name: "故障位置",
		key: "locComposeNo",
		placeholder: "请选择",
		type: "elTreeSelect",
		checkStrictly: true,
		width: 200,
		children: []
	},
	{
		name: "处置级别",
		key: "disposalLevel",
		placeholder: `请选择`,
		enableFuzzy: true,
		type: "select",
		width: 120,
		children: [] //字典值
	},
	{
		name: "关键字",
		key: "mainTitle",
		type: "input",
		width: 120,
		placeholder: "请输入"
	}
])
const props = defineProps<{
	id?: string | number
	visible: boolean
	lineId: string
	loading: boolean
}>()

const queryForm = ref<{
	locComposeNo: string //故障位置
	disposalLevel: string //处置级别
	lineId: string //线路Id
	mainTitle: string //流程标题 关键字
	status: string //发布状态:0草稿箱、1已发布、2已废弃
}>({
	locComposeNo: "",
	disposalLevel: "",
	lineId: "",
	status: "",
	mainTitle: ""
})

const drawerLoading = ref(false)

const emit = defineEmits(["update:visible", "clickBtn"])
const leftTitle = computed(() => ({
	name: ["标准处置流程选择"],
	icon: ["fas", "square-share-nodes"]
}))
// 监听visible变化
watch(
	() => props.visible,
	(val) => {
		if (!val) {
			emit("update:visible", false)
		}
	}
)

// 监听id变化，获取故障详情
watch(
	() => props.id,
	async (id) => {
		if (id) {
			// TODO: 调用接口获取故障详情
			// const detail = await getFaultDetail(id)
			// faultDetail.value = detail
		}
	},
	{ immediate: true }
)
// 文档列表数据
const documentList = ref()

defineOptions({
	name: "StandardProcessDrawer"
})

const handleClickBtn = (btnName: string | undefined) => {
	if (btnName === "启动标准处置流程") {
		emit("clickBtn", selectedDocument.value)
	}
}
const getQueryData = async (data: { [propName: string]: any }) => {
	documentListLoading.value = true
	queryForm.value = {
		...queryForm.value,
		...data
	}
	try {
		const res = await getStandardProcessList(queryForm.value)
		documentList.value = res
	} catch (error) {
		console.error("获取标准处置流程失败:", error)
	} finally {
		documentListLoading.value = false
	}
}
const formBtnList = computed(() => [
	{
		name: "启动标准处置流程",
		icon: ["fas", "file"],
		disabled:
			!documentList.value ||
			documentList.value.length === 0 ||
			!selectedDocument.value
	}
])
// 选中的文档
const selectedDocument = ref<any>()

// 处理文档点击
const handleDocumentClick = (doc: any) => {
	selectedDocument.value = doc
}

/**
 * @description 获取关联位置树的数据
 */
const getLocComposeTree = () => {
	getLocComposeTreeApi(
		objectToFormData({
			currentPage: 1,
			pageSize: 500
		})
	).then((res: any) => {
		queryArrList.value[0].children = []
		const map = new Map(Object.entries(res))
		map.forEach((value: any, key) => {
			queryArrList.value[0].children = addValueAndLabelForTree(value)
			console.log("queryArrList", queryArrList.value[0].children)
		})
	})
}
/**
 * @description 递归处理数据, 为 cascade 的每个 options 添加 label 和 value 属性
 */
const addValueAndLabelForTree = (arr: any[]): any => {
	arr = arr.map((node) => {
		if (node.hasOwnProperty("children")) {
			node.children = addValueAndLabelForTree(node.children)
		}
		return {
			...node,
			label: `${node.locComposeNameBelong}(${node.locComposeNoBeLong})`,
			value: node["locComposeNo"]
		}
	})
	return arr
}

// onMounted(() => {
// 	getLocComposeTree()
// })
const getDictByCodeListFun = async () => {
	await getDictByCodeList(["DISPOSAL_LEVEL"])
	queryArrList.value[1].children = dictOptions.value.DISPOSAL_LEVEL
}

defineExpose({
	getQueryData,
	getLocComposeTree,
	getDictByCodeListFun
})
</script>

<template>
	<div
		class="drawer-container"
		v-loading="props.loading"
		element-loading-text="启动中..."
	>
		<div class="drawer-column">
			<div class="rows">
				<Title :title="leftTitle" />
				<div style="height: 10px"></div>
				<Query
					:queryArrList="queryArrList"
					class="ml10"
					@getQueryData="getQueryData"
				/>
				<el-scrollbar height="calc(100vh - 160px)">
					<div v-loading="documentListLoading" element-loading-text="加载中...">
						<div class="document-list" v-if="documentList?.length > 0">
							<div
								v-for="doc in documentList"
								:key="doc.id"
								class="document-item"
								:class="{ 'is-selected': selectedDocument?.id === doc.id }"
								@click="handleDocumentClick(doc)"
							>
								<div class="document-icon">
									<font-awesome-icon :icon="['fas', 'file-lines']" />
								</div>
								<div class="document-info">
									<div class="document-title">{{ doc.mainTitle }}</div>
									<div class="document-meta">
										<span class="document-id"
											>预案编号: {{ doc.mainCode }}</span
										>
										<span class="document-time"
											>发布时间: {{ doc.createdDate }}</span
										>
									</div>
								</div>
							</div>
						</div>
						<div v-if="documentList?.length === 0" class="no-data">
							暂无数据
						</div>
					</div>
				</el-scrollbar>
			</div>
			<ButtonList
				class="footer"
				:button="formBtnList"
				@onBtnClick="handleClickBtn"
			/>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.drawer-column {
	width: 100%;
}
.fault-detail-content {
	padding: 20px;

	.section {
		margin-bottom: 24px;

		h3 {
			margin: 0 0 16px;
			font-size: 16px;
			font-weight: 500;
			color: #303133;
		}
	}

	:deep(.el-descriptions__label) {
		width: 120px;
		background-color: #f5f7fa;
		font-size: 12px;
	}
}
.label-text {
	width: 120px;
}
.document-list {
	padding: 0 10px 16px;
	height: 100%;

	.document-item {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 16px;
		background: #f8f9fe;
		border-radius: 4px;
		margin-bottom: 12px;
		cursor: pointer;
		transition: all 0.3s;
		border: 1px solid transparent;
		&:hover {
			background: #eef1fd;
		}

		&.is-selected {
			background: #e6f1fe;
			border: 1px solid #409eff;
		}

		.document-icon {
			margin-right: 12px;
			color: #409eff;
			font-size: 32px;
		}

		.document-info {
			flex: 1;

			.document-title {
				font-size: 14px;
				color: #303133;
				margin-bottom: 8px;
				line-height: 1.4;
			}

			.document-meta {
				font-size: 12px;
				color: #909399;

				.document-id {
					margin-right: 16px;
				}
			}
		}
	}
}
.no-data {
	text-align: center;
	padding: 32px;
	color: #909399;
	font-size: 14px;
}
</style>
