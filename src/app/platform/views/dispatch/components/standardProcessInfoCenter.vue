<script setup lang="ts">
import { ref, computed, onMounted, watch } from "vue"
import { formatSecondsToTime } from "@/app/baseline/utils"
// 组件属性
interface Props {
	processId?: string | number
	visible?: boolean
	processDetail?: any
	loading?: boolean
}

const formatDateTime = (dateStr: string) => {
	if (!dateStr) return ""
	return dateStr.split(" ")[1] || dateStr
}

const props = withDefaults(defineProps<Props>(), {
	visible: true,
	loading: false
})

const getBeforeSpace = (str: string) => {
	if (!str) return ''
	const idx = str.indexOf(' ')
	return idx === -1 ? str : str.slice(0, idx)
}
</script>

<template>
	<div class="standard-process-info-center">
		<div class="process-content">
			<el-scrollbar height="100%">
				<div class="process-steps-container">
					<!-- 主步骤卡片 -->
					<div
						v-for="step in processDetail.processVoList"
						:key="step.id"
						class="process-step-card"
						:class="`step-${step.processStatus}`"
					>
						<!-- 主步骤标题 -->
						<div class="step-header">
							<span class="step-title">{{ step.name }}</span>
							<span class="step-time"
								>&nbsp;&nbsp;|&nbsp;&nbsp;{{
									formatSecondsToTime(step.executeTime)
								}}</span
							>
						</div>

						<!-- 子步骤列表 -->
						<div class="sub-steps-list">
							<div
								v-for="subStep in step.taskVoList"
								:key="subStep.id"
								class="sub-step-item"
							>
								<div class="sub-step-content">
									<el-text class="sub-step-title">{{
										subStep.content
									}}</el-text>
									<el-text class="sub-step-title"
										>&nbsp;&nbsp;|&nbsp;&nbsp;</el-text
									>
									<el-text class="sub-step-time">{{
										formatSecondsToTime(subStep.executeTime)
									}}</el-text>
									<div
										class="sub-step-sub-content"
										v-for="subSubStep in subStep.stepVoList"
										:key="subSubStep.id"
									>
									<div style="display: flex; align-items: center;">
										<el-text truncated class="sub-step-sub-title">{{
											subSubStep.content
										}}</el-text>
										<el-text class="sub-step-sub-title"
											>&nbsp;&nbsp;|&nbsp;&nbsp;</el-text
										>
										<el-text class="sub-step-sub-time">{{
											formatSecondsToTime(subSubStep.executeTime)
										}}</el-text>
									</div>

										<div style="display: flex; align-items: center;">
											<el-text truncated class="sub-step-sub-title">{{
												getBeforeSpace(subSubStep.content)
											}}&nbsp;</el-text>
												<el-text truncated class="sub-step-sub-title">{{
												subSubStep.executeStatusName
											}}:&nbsp;</el-text>
											<el-text truncated class="sub-step-sub-title"
												>{{ subSubStep.roleName }}
											</el-text>
											<el-text class="sub-step-sub-title"
												>&nbsp;&nbsp;|&nbsp;&nbsp;</el-text
											>
											<el-text class="sub-step-sub-time">{{
												formatSecondsToTime(subSubStep.executeTime)
											}}</el-text>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</el-scrollbar>
		</div>
	</div>
</template>

<style scoped lang="scss">
.standard-process-info-center {
	height: 100%;

	.process-content {
		height: 100%;
		padding: 15px 0;

		.process-steps-container {
			display: flex;
			flex-direction: column;
			gap: 12px;

			.process-step-card {
				//background: #e8f4fd;
				border-radius: 8px;
				//box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

				//&.step-completed {
				//	background: #e8f5e8;
				//}

				//&.step-current {
				//	background: #e8f4fd;
				//	border: 2px solid #409eff;
				//}

				//&.step-pending {
				//	background: #fdf6ec;
				//}

				.step-header {
					display: flex;
					align-items: center;
					background: #e8f4fe;
					padding: 6px 8px;
					color: #409eff;
					border: 1px dashed #afd4f9;
					border-radius: 4px;
					.step-title {
						font-size: 14px;
						font-weight: 600;
					}

					.step-time {
						font-size: 14px;
						font-weight: 500;
					}
				}

				.sub-steps-list {
					padding: 8px;
					.sub-step-item {
						margin-bottom: 4px;
						.sub-step-title,
						.sub-step-time {
							color: #409eff !important;
							font-size: 14px !important;
						}
						.sub-step-content {
							.sub-step-title {
								font-size: 12px;
								color: #666;
							}

							.sub-step-time {
								font-size: 12px;
								color: #666;
							}
							.sub-step-sub-content {
								.sub-step-sub-title {
									max-width: 200px;
									font-size: 12px;
									color: #666;
								}
							}
						}

						.sub-step-description {
							font-size: 12px;
							color: #666;
							line-height: 1.4;
							margin-bottom: 4px;
							padding-left: 0;
						}

						.sub-step-operator {
							font-size: 12px;
							color: #888;
							font-style: italic;
						}
					}
				}
			}
		}
	}
}
</style>
