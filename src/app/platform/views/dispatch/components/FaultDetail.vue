<template>
	<div class="fault-detail-content">
		<div class="section">
			<h3>基本信息</h3>
			<el-descriptions
				:column="2"
				border
				size="small"
				v-loading="loading1"
				element-loading-text="加载中..."
			>
				<el-descriptions-item>
					<template #label>
						<div class="label-text">故障编号</div>
					</template>
					<template #default>
						<div style="width: 150px">
							<el-text size="small">{{ detail.faultCode || "--" }}</el-text>
						</div>
					</template>
				</el-descriptions-item>
				<el-descriptions-item>
					<template #label>
						<div class="label-text">故障来源</div>
					</template>
					<template #default>
						<el-text size="small">{{ faultSourceDisplay }}</el-text>
					</template>
				</el-descriptions-item>
				<el-descriptions-item>
					<template #label>
						<div class="label-text">关闭方式</div>
					</template>
					<template #default>
						<el-text size="small">{{ closeTypeDisplay }}</el-text>
					</template>
				</el-descriptions-item>
				<el-descriptions-item>
					<template #label>
						<div class="label-text">处理状态</div>
					</template>
					<template #default>
						<el-tag
							style="
								background-color: rgba(75, 174, 137, 0.2);
								border: 1px solid rgba(75, 174, 137);
								width: 50px;
							"
							size="small"
							><el-text style="color: rgba(75, 174, 137)" size="small">{{
								detail.stsName || "--"
							}}</el-text>
						</el-tag>
					</template>
				</el-descriptions-item>
				<el-descriptions-item>
					<template #label>
						<div class="label-text">故障发生时间</div>
					</template>
					<template #default>
						<el-text size="small">{{ detail.faultTime || "--" }}</el-text>
					</template>
				</el-descriptions-item>
				<el-descriptions-item>
					<template #label>
						<div class="label-text">故障持续时长</div>
					</template>
					<template #default>
						<el-text size="small">{{ detail.durationTime || "--" }}</el-text>
					</template>
				</el-descriptions-item>
				<el-descriptions-item :span="2">
					<template #label>
						<div class="label-text">线路</div>
					</template>
					<template #default>
						<el-text size="small">{{ detail.lineName || "--" }}</el-text>
					</template>
				</el-descriptions-item>
				<el-descriptions-item :span="2">
					<template #label>
						<div class="label-text">故障位置</div>
					</template>
					<template #default>
						<el-text size="small">{{ detail.locationName || "--" }}</el-text>
					</template>
				</el-descriptions-item>
				<el-descriptions-item :span="2">
					<template #label>
						<div class="label-text">故障位置说明</div>
					</template>
					<template #default>
						<el-text size="small">{{ detail.locationDesc || "--" }}</el-text>
					</template>
				</el-descriptions-item>
				<el-descriptions-item :span="2">
					<template #label>
						<div class="label-text">故障设备分级</div>
					</template>
					<template #default>
						<el-text size="small">{{ detail.deviceLevelName || "--" }}</el-text>
					</template>
				</el-descriptions-item>
				<!--				<el-descriptions-item :span="2">-->
				<!--					<template #label>-->
				<!--						<div class="label-text">故障设备</div>-->
				<!--					</template>-->
				<!--					<el-text size="small">{{ detail.deviceName || "&#45;&#45;" }}</el-text>-->
				<!--				</el-descriptions-item>-->
			</el-descriptions>
		</div>

		<div class="section">
			<h3>故障信息</h3>
			<el-descriptions
				:column="2"
				border
				size="small"
				v-loading="loading1"
				element-loading-text="加载中..."
			>
				<el-descriptions-item :span="2">
					<template #label>
						<div class="label-text">设备重要分级</div>
					</template>
					<el-tag
						class="device-grade"
						:class="detail.deviceGrade"
						size="small"
						:style="getDeviceGradeColor"
						><el-text
							:style="{ color: getDeviceGradeColor.color }"
							size="small"
							>{{ detail.deviceGrade || "--" }}</el-text
						>
					</el-tag>
				</el-descriptions-item>
				<!--				<el-descriptions-item>-->
				<!--					<template #label>-->
				<!--						<div class="label-text">常见故障</div>-->
				<!--					</template>-->
				<!--					<el-text size="small">{{ detail.faultNormalInfoId || "&#45;&#45;" }}</el-text>-->
				<!--				</el-descriptions-item>-->
				<el-descriptions-item>
					<template #label>
						<div class="label-text">影响正线</div>
					</template>
					<template #default>
						<div style="width: 150px">
							<el-text size="small">{{ hinderLineDisplay }}</el-text>
						</div>
					</template>
				</el-descriptions-item>
				<el-descriptions-item>
					<template #label>
						<div class="label-text">影响安全</div>
					</template>
					<template #default>
						<div>
							<el-text size="small">{{ hinderSafeDisplay }}</el-text>
						</div>
					</template>
				</el-descriptions-item>
				<el-descriptions-item>
					<template #label>
						<div class="label-text">影响设备</div>
					</template>
					<template #default>
						<div>
							<el-text size="small">{{ hinderEqDisplay }}</el-text>
						</div>
					</template>
				</el-descriptions-item>
				<el-descriptions-item>
					<template #label>
						<div class="label-text">影响客运服务</div>
					</template>
					<template #default>
						<div>
							<el-text size="small">{{ hinderPassengerDisplay }}</el-text>
						</div>
					</template>
				</el-descriptions-item>
				<el-descriptions-item>
					<template #label>
						<div class="label-text">故障影响程度</div>
					</template>
					<el-tag
						:style="{
							backgroundColor: getUrgentLevelColor,
							border: `1px solid ${getUrgentLevelColor}`
						}"
						size="small"
						><el-text style="color: #fff" size="small">{{
							urgentLevelDisplay
						}}</el-text>
					</el-tag>
				</el-descriptions-item>
				<el-descriptions-item>
					<template #label>
						<div class="label-text">故障分类</div>
					</template>
					<el-tag
						:style="{
							backgroundColor: getFaultLevelColor,
							border: `1px solid ${getFaultLevelColor}`
						}"
						size="small"
						><el-text style="color: #fff" size="small">{{
							faultLevelDisplay
						}}</el-text>
					</el-tag>
				</el-descriptions-item>
				<el-descriptions-item :span="2">
					<template #label>
						<div class="label-text">故障现象</div>
					</template>
					<template #default>
						<div>
							<el-text size="small">{{ detail.faultDesc || "--" }}</el-text>
						</div>
					</template>
				</el-descriptions-item>
				<!--				<el-descriptions-item>-->
				<!--					<template #label>-->
				<!--						<div class="label-text">故障图片或视频</div>-->
				<!--					</template>-->
				<!--					<el-text size="small">{{ detail.faultImg || "&#45;&#45;" }}</el-text>-->
				<!--				</el-descriptions-item>-->
			</el-descriptions>
		</div>

		<div class="section">
			<h3>其他信息</h3>
			<el-descriptions
				:column="2"
				border
				size="small"
				v-loading="loading1"
				element-loading-text="加载中..."
			>
				<el-descriptions-item>
					<template #label>
						<div class="label-text">故障上报时间</div>
					</template>
					<template #default>
						<div style="width: 150px">
							<el-text size="small">{{ detail.reportTime || "--" }}</el-text>
						</div>
					</template>
				</el-descriptions-item>
				<el-descriptions-item>
					<template #label>
						<div class="label-text">来源工单</div>
					</template>
					<el-text size="small">{{ detail.fromJobId || "--" }}</el-text>
				</el-descriptions-item>
				<el-descriptions-item>
					<template #label>
						<div class="label-text">上报部门</div>
					</template>
					<template #default>
						<div>
							<el-text size="small">{{
								detail.reportOrganizationName || "--"
							}}</el-text>
						</div>
					</template>
				</el-descriptions-item>
				<el-descriptions-item>
					<template #label>
						<div class="label-text">上报人</div>
					</template>
					<template #default>
						<div>
							<el-text size="small">{{
								detail.reportRealUsername || "--"
							}}</el-text>
						</div>
					</template>
				</el-descriptions-item>
				<el-descriptions-item>
					<template #label>
						<div class="label-text">故障关闭时间</div>
					</template>
					<template #default>
						<div>
							<el-text size="small">{{
								detail.faultCloseTime || "--"
							}}</el-text>
						</div>
					</template>
				</el-descriptions-item>
				<el-descriptions-item>
					<template #label>
						<div class="label-text">关闭人</div>
					</template>
					<template #default>
						<div>
							<el-text size="small">{{
								detail.faultCloseUser || "--"
							}}</el-text>
						</div>
					</template>
				</el-descriptions-item>
				<el-descriptions-item>
					<template #label>
						<div class="label-text">关闭故障说明</div>
					</template>
					<template #default>
						<div>
							<el-text size="small">{{
								detail.faultCloseRemark || "--"
							}}</el-text>
						</div>
					</template>
				</el-descriptions-item>
			</el-descriptions>
		</div>

		<div class="section" v-if="showProgressInfo">
			<h3>处置流程信息</h3>
			<el-descriptions
				:column="2"
				border
				size="small"
				v-loading="loading"
				element-loading-text="加载中..."
			>
				<el-descriptions-item :span="2">
					<template #label>
						<div class="label-text">处置流程级别</div>
					</template>
					<template #default>
						<div>
							<el-text size="small">{{
								processDetail.disposalLevel || "--"
							}}</el-text>
						</div>
					</template>
				</el-descriptions-item>
				<el-descriptions-item :span="2">
					<template #label>
						<div class="label-text">处置流程编号</div>
					</template>
					<template #default>
						<div>
							<el-text size="small">{{
								processDetail.mainCode || "--"
							}}</el-text>
						</div>
					</template>
				</el-descriptions-item>
				<el-descriptions-item :span="2">
					<template #label>
						<div class="label-text">处置流程名称</div>
					</template>
					<template #default>
						<div>
							<el-text size="small">{{
								processDetail.mainTitle || "--"
							}}</el-text>
						</div>
					</template>
				</el-descriptions-item>
				<el-descriptions-item>
					<template #label>
						<div class="label-text">调度指挥人员</div>
					</template>
					<template #default>
						<div style="width: 150px">
							<el-text size="small">{{
								processDetail.dispatchName || "--"
							}}</el-text>
						</div>
					</template>
				</el-descriptions-item>
				<el-descriptions-item>
					<template #label>
						<div class="label-text">技术支持专家</div>
					</template>
					<template #default>
						<div>
							<el-text size="small">{{
								processDetail.expertEmpName || "--"
							}}</el-text>
						</div>
					</template>
				</el-descriptions-item>
				<el-descriptions-item>
					<template #label>
						<div class="label-text">开始时间</div>
					</template>
					<template #default>
						<div>
							<el-text size="small">{{
								processDetail.startTime || "--"
							}}</el-text>
						</div>
					</template>
				</el-descriptions-item>
				<el-descriptions-item>
					<template #label>
						<div class="label-text">结束时间</div>
					</template>
					<template #default>
						<div>
							<el-text size="small">{{
								processDetail.endTime || "--"
							}}</el-text>
						</div>
					</template>
				</el-descriptions-item>
				<el-descriptions-item>
					<template #label>
						<div class="label-text">累计用时</div>
					</template>
					<template #default>
						<div>
							<el-text size="small">{{
								formatSecondsToTime(processDetail.executeTime) || "--"
							}}</el-text>
						</div>
					</template>
				</el-descriptions-item>
				<el-descriptions-item>
					<template #label>
						<div class="label-text">定额用时</div>
					</template>
					<template #default>
						<div>
							<el-text size="small">{{
								formatSecondsToTime(processDetail.limitedTime) || "--"
							}}</el-text>
						</div>
					</template>
				</el-descriptions-item>
				<el-descriptions-item>
					<template #label>
						<div class="label-text">参与人员</div>
					</template>
					<template #default>
						<div>
							<el-text size="small">{{
								processDetail.executeUserNames || "--"
							}}</el-text>
						</div>
					</template>
				</el-descriptions-item>
			</el-descriptions>
		</div>
	</div>
</template>
<script lang="ts" setup>
import { computed, onMounted } from "vue"
import { useDictInit } from "@/app/baseline/views/components/dictBase"
import { formatSecondsToTime } from "@/app/baseline/utils"
// const { dictOptions, getDictByCodeList, dictFilter } = useDictInit()
const props = defineProps<{
	detail: any
	dictOptions: {
		CLOSE_WAY: any[]
		HINDER_LINE: any[]
		HINDER_SAFE: any[]
		HINDER_EQ: any[]
		HINDER_PASSENGER: any[]
		URGENT_LEVEL: any[]
		FAULT_LEVEL: any[]
		GZX: any[]
	}
	showProgressInfo?: boolean
	processDetail?: any
	loading: boolean
	loading1: boolean
}>()

// 获取故障来源的显示名称
const faultSourceDisplay = computed(() => {
	// 从第四位开始截取（索引从0开始计算的话是第3位）
	const natureCodePart = props.detail.natureCode.slice(4) || ""
	const data = props.dictOptions.GZX.find((item) => {
		return item.subitemValue == natureCodePart
	})
	console.log(data)

	return data?.subitemName || "--"
})

// 获取关闭方式的显示名称
const closeTypeDisplay = computed(() => {
	const data = props.dictOptions.CLOSE_WAY.find(
		(item) => item.subitemValue == props.detail.closeType
	)
	return data?.subitemName || "--"
})
// 获取影响正线的显示名称
const hinderLineDisplay = computed(() => {
	console.log("dictOptions", props.dictOptions)
	const data = props.dictOptions.HINDER_LINE.find(
		(item) => item.subitemValue == props.detail.hinderLine
	)
	return data?.subitemName || "--"
})
//获取影响安全的显示名称
const hinderSafeDisplay = computed(() => {
	const data = props.dictOptions.HINDER_SAFE.find(
		(item) => item.subitemValue == props.detail.hinderSafe
	)
	return data?.subitemName || "--"
})
//获取影响设备的显示名称
const hinderEqDisplay = computed(() => {
	const data = props.dictOptions.HINDER_EQ.find(
		(item) => item.subitemValue == props.detail.hinderEq
	)
	return data?.subitemName || "--"
})
//获取影响乘客的显示名称
const hinderPassengerDisplay = computed(() => {
	const data = props.dictOptions.HINDER_PASSENGER.find(
		(item) => item.subitemValue == props.detail.hinderPassenger
	)
	return data?.subitemName || "--"
})
//获取故障影响程度的显示名称
const urgentLevelDisplay = computed(() => {
	const data = props.dictOptions.URGENT_LEVEL.find(
		(item) => item.subitemValue == props.detail.urgentLevel
	)
	return data?.subitemName || "--"
})
//获取故障分类的显示名称
const faultLevelDisplay = computed(() => {
	const data = props.dictOptions.FAULT_LEVEL.find(
		(item) => item.subitemValue == props.detail.faultLevel
	)
	return data?.subitemName || "--"
})

// 根据故障影响程度返回对应的颜色
const getUrgentLevelColor = computed(() => {
	const levelName = props.detail?.urgentLevelName
	if (!levelName) return "rgb(229, 112, 107)" // 默认极高颜色

	switch (levelName) {
		case "极高":
			return "rgb(229, 112, 107)"
		case "高":
			return "rgb(245, 155, 34)"
		case "中":
			return "rgb(0, 161, 252)"
		case "低":
			return "rgb(75, 174, 137)"
		default:
			return "rgb(229, 112, 107)" // 默认极高颜色
	}
})
// 根据设备重要分级返回对应的颜色
const getDeviceGradeColor = computed(() => {
	const levelName = props.detail?.deviceGrade
	if (!levelName)
		return {
			backgroundColor: "rgb(229, 112, 107,0.3)",
			border: "1px solid rgb(229, 112, 107)",
			color: "rgb(229, 112, 107)"
		} // 默认极高颜色

	switch (levelName) {
		case "A":
			return {
				backgroundColor: "rgb(229, 112, 107,0.3)",
				border: "1px solid rgb(229, 112, 107)",
				color: "rgb(229, 112, 107)"
			}
		case "B":
			return {
				backgroundColor: "rgb(245, 155, 34,0.3)",
				border: "1px solid rgb(245, 155, 34)",
				color: "rgb(245, 155, 34)"
			}
		case "C":
			return {
				backgroundColor: "rgb(0, 161, 252,0.3)",
				border: "1px solid rgb(0, 161, 252)",
				color: "rgb(0, 161, 252)"
			}
		case "D":
			return {
				backgroundColor: "rgb(75, 174, 137,0.3)",
				border: "1px solid rgb(75, 174, 137)",
				color: "rgb(75, 174, 137)"
			}
		default:
			return {
				backgroundColor: "rgb(229, 112, 107,0.3)",
				border: "1px solid rgb(229, 112, 107)",
				color: "rgb(229, 112, 107)"
			}
	}
})

// 根据故障分类返回对应的颜色
const getFaultLevelColor = computed(() => {
	const faultLevel = props.detail?.faultLevel
	if (!faultLevel) return "rgb(229, 112, 107)" // 默认I类故障颜色

	// 处理包含"I类"或"II类"的字符串
	// if (faultLevel.includes("I类") && !faultLevel.includes("II类")) {
	// 	return "rgb(229, 112, 107)" // I类故障
	// } else if (faultLevel.includes("II类")) {
	// 	return "rgb(245, 155, 34)" // II类故障
	// } else if (faultLevel.includes("2") || faultLevel === "I类故障：2") {
	// 	return "rgb(0, 161, 252)" // I类故障：2
	// }

	// 精确匹配
	switch (faultLevel) {
		case 0:
			return "rgb(229, 112, 107)"
		case 1:
			return "rgb(245, 155, 34)"
		case 2:
			return "rgb(0, 161, 252)"
		default:
			return "rgb(229, 112, 107)" // 默认I类故障颜色
	}
})
</script>
<style lang="scss" scoped>
.fault-detail-content {
	padding: 10px 0;

	.section {
		margin-bottom: 24px;

		h3 {
			margin: 0 0 16px;
			font-size: 14px;
			font-weight: 500;
			color: #303133;
		}
	}

	:deep(.el-descriptions__label) {
		background-color: #f5f7fa;
		font-size: 12px;
		text-align: center;
		font-weight: bold !important;
		width: 80px !important;
	}
	:deep(.el-descriptions__label.el-descriptions__cell.is-bordered-label) {
		width: 80px !important;
	}
	:deep(.el-descriptions__content.el-descriptions__cell.is-bordered-content) {
		width: 120px !important;
	}

	.label-text {
		font-weight: bold;
	}
	:deep(.el-descriptions__body .el-descriptions__table .el-descriptions__cell) {
	}
}
.device-grade {
	&.info {
		border: 1px solid rgb(0, 161, 252) !important;
		color: rgb(0, 161, 252) !important;
	}

	&.warning {
		border: 1px solid rgb(245, 155, 34) !important;
		color: rgb(245, 155, 34) !important;
	}

	&.success {
		border: 1px solid rgb(75, 174, 137) !important;
		color: rgb(75, 174, 137) !important;
	}

	&.danger {
		border: 1px solid rgb(229, 112, 107) !important;
		color: rgb(229, 112, 107) !important;
	}
}
</style>
