<!--
 * @Author: liulianming
 * @Date: 2025-07-14 23:42:19
 * @LastEditors: liulianming
 * @LastEditTime: 2025-07-31 17:27:27
 * @Description: 应急指挥
-->
<script lang="ts" setup>
import { computed, onMounted, ref, watch } from "vue"
import { modalSize } from "@/app/baseline/utils/layout-config"
import { ElMessage } from "element-plus"
import { LineVo } from "@/app/baseline/utils/types/common"
import StandardProcessDrawer from "./components/StandardProcessDrawer.vue"
import {
	getFaultList,
	getFaultReportDetail,
	getFaultReportStatusStatistics,
	getLocComposeTreeApi,
	startStandardProcess
} from "@/app/baseline/api/dispatch"
import DeviceCard from "./components/DeviceCard.vue"
import FaultDetailDrawer from "./components/FaultDetailDrawer.vue"
import { LineApi } from "../../api/system/line"
import { usePagination } from "../../hooks/usePagination"
import { getToken } from "../../utils/cache/cookies"
import StandardProcessInfoDrawer from "./components/StandardProcessInfoDrawer.vue"
import { useDictInit } from "@/app/baseline/views/components/dictBase"

const { dictOptions, getDictByCodeList, dictFilter } = useDictInit()
/**
 * 公司列表
 */
const companyOptions = ref<any>([])

/**
 * 查询条件 配置
 */
const queryArrList = ref(<any[]>[
	{
		name: "故障位置",
		key: "locComposeNo",
		placeholder: "请选择",
		type: "elTreeSelect",
		width: 200,
		checkStrictly: true,
		children: []
	},
	// {
	// 	name: "所属线路",
	// 	key: "lineId",
	// 	type: "select",
	// 	children: lineList.value.map((item) => ({
	// 		label: item.name,
	// 		value: item.id
	// 	})),
	// 	placeholder: "请选择"
	// },
	// {
	// 	name: "所属车站",
	// 	key: "code",
	// 	placeholder: `请选择`,
	// 	enableFuzzy: true,
	// 	type: "select"
	// },
	{
		name: "上报时间",
		key: "reportTime",
		placeholder: `请选择`,
		enableFuzzy: true,
		type: "startAndEndTime"
	},
	{
		name: "关键字",
		key: "mainTitle",
		type: "input",
		placeholder: "请输入"
	}
])

const tabList = [
	{
		name: "待执行",
		icon: ["fas", "square-plus"]
	},
	{
		name: "执行中",
		icon: ["fas", "square-plus"]
	},
	{
		name: "已关闭",
		icon: ["fas", "square-plus"]
	}
]
const tabNum = ref(Array.from({ length: tabList.length }, () => 0))
const activeName = ref(tabList[0].name)
const activeIndex = ref(0)

/**
 * 查询条件
 */
const queryForm = ref({
	mainTitle: "",
	lineId: "",
	reportTime: []
})

/**
 * 故障列表
 */
const faultList = ref<any>()

/**
 * 获取故障报告状态统计数据
 */
const fetchStatusStatistics = async () => {
	try {
		const params = {
			...queryForm.value
		}
		const data = await getFaultReportStatusStatistics(params)
		// 更新每个tab的数量
		tabNum.value[0] = data["待执行"] || 0 // 待执行
		tabNum.value[1] = data["执行中"] || 0 // 执行中
		tabNum.value[2] = data["已关闭"] || 0 // 已关闭
	} catch (error) {
		console.error("获取故障报告状态统计失败:", error)
	}
}

/**
 * 获取故障列表
 */
const fetchFaultList = async () => {
	faultListLoading.value = true
	try {
		const params = {
			...queryForm.value,
			statusType: activeName.value
		}
		const res = await getFaultList(params)
		faultList.value = res || []
	} catch (error) {
		console.error("获取故障列表失败:", error)
		ElMessage.error("获取故障列表失败")
	} finally {
		faultListLoading.value = false
	}
}

// 监听tab切换和查询条件变化
watch(
	() => [activeIndex.value, queryForm.value],
	() => {
		fetchFaultList()
		fetchStatusStatistics()
	},
	{ deep: true }
)
const faultListLoading = ref(false)
// 线路列表
const lineList = ref<LineVo[]>([])
const usePaginationStore = usePagination()
const usePaginationStoreForData = usePaginationStore.paginationData
const currentPage = ref<number>(usePaginationStoreForData.currentPage)
const pageSize = ref<number>(usePaginationStoreForData.pageSize)
/**
 * 获取线路配置
 */
function getLineList() {
	LineApi.getLineList(
		objectToFormData({
			currentPage: currentPage.value,
			pageSize: 9999
		})
	).then((res: any) => {
		lineList.value = res.rows ?? []
	})
}
/**
 * 页面标题
 */
const titleConf = {
	name: ["应急指挥"],
	icon: ["fas", "square-share-nodes"]
}

/**********************tab 相关操作 *********************************/
/**
 * 单击 操作
 * @param tab
 */
const handleTabsClick = (tab: any) => {
	activeName.value = tab.paneName
	activeIndex.value = tab.index
	faultList.value = null
	faultListLoading.value = true
}

// 故障详情抽屉相关
const faultDetailVisible = ref(false)
const currentFaultId = ref<string | number>()
const faultDetail = ref<any>(null)
const standardProcessVisible = ref(false)
const standardProcessInfoVisible = ref(false)
const standardProcessDrawerRef = ref<any>(null)
const standardProcessBtnLoading = ref(false)
const loading1 = ref(false)
// 处理故障停按钮点击
const handleFault = async (device: any) => {
	currentFaultId.value = device.id
	try {
		loading1.value = true
		faultDetail.value = await getFaultReportDetail(device.id)
		loading1.value = false
	} catch (error) {
		loading1.value = false
		console.error("获取故障详情失败:", error)
		ElMessage.error("获取故障详情失败")
	}
}
const handleItemClick = (device: any) => {
	console.log("device", device)
	faultDetail.value = device
	if (device.executeMainStatus === "0") {
		faultDetailVisible.value = true
		handleFault(device)
	} else if (device.executeMainStatus === "1") {
		//跳转录屏软件地址
		const RPSTSS_APP_PATH = import.meta.env.VITE_RPSTSS_APP_PATH
		window.open(
			`${RPSTSS_APP_PATH}?id=${device.executeMainId}&token=${
				"Bearer " + getToken()
			}`,
			"_blank"
		)
	} else if (device.executeMainStatus === "2") {
		standardProcessInfoVisible.value = true
		handleFault(device)
	}
}
const handleStartProcess = async (doc: any) => {
	standardProcessBtnLoading.value = true
	try {
		const params = {
			faultReportId: faultDetail.value.id,
			setMainId: doc.id,
			mainTitle: doc.mainTitle,
			disposalLevel: doc.disposalLevel
		}
		const res = await startStandardProcess(params)
		ElMessage.success("启动标准处置流程成功")
		standardProcessVisible.value = false
		faultDetailVisible.value = false
		fetchFaultList()
		setTimeout(() => {
			//跳转app
			const RPSTSS_APP_PATH = import.meta.env.VITE_RPSTSS_APP_PATH
			window.open(
				`${RPSTSS_APP_PATH}?id=${res.id}&token=${"Bearer " + getToken()}`,
				"_blank"
			)
		}, 500)
	} catch (error) {
		console.error("启动标准处置流程失败:", error)
		// ElMessage.error("启动标准处置流程失败")
	} finally {
		standardProcessBtnLoading.value = false
	}
}

const handleClickBtn = (btnName: string | undefined) => {
	if (btnName === "标准处置流程选择") {
		standardProcessVisible.value = true
		const params = {
			lineId: faultDetail.value.lineId,
			status: 1 // 已发布
		}
		setTimeout(() => {
			standardProcessDrawerRef?.value?.getLocComposeTree()
			standardProcessDrawerRef?.value?.getDictByCodeListFun()
			standardProcessDrawerRef?.value?.getQueryData(params)
		}, 100)
	}
}
/**
 * @description 获取关联位置树的数据
 */
const getLocComposeTree = () => {
	getLocComposeTreeApi(
		objectToFormData({
			currentPage: 1,
			pageSize: 500
		})
	).then((res: any) => {
		queryArrList.value[0].children = []
		const map = new Map(Object.entries(res))
		map.forEach((value: any, key) => {
			queryArrList.value[0].children = addValueAndLabelForTree(value)
			console.log("queryArrList", queryArrList.value[0].children)
		})
	})
}
/**
 * @description 递归处理数据, 为 cascade 的每个 options 添加 label 和 value 属性
 */
const addValueAndLabelForTree = (arr: any[]): any => {
	arr = arr.map((node) => {
		if (node.hasOwnProperty("children")) {
			node.children = addValueAndLabelForTree(node.children)
		}
		return {
			...node,
			label: `${node.locComposeNameBelong}(${node.locComposeNoBeLong})`,
			value: node["locComposeNo"]
		}
	})
	return arr
}
onMounted(async () => {
	// getLineList()
	getLocComposeTree()
	// 获取故障报告状态统计
	await fetchStatusStatistics()
	// 获取故障列表
	await fetchFaultList()
	//获取字典
	await getDictByCodeList([
		"CLOSE_WAY",
		"HINDER_LINE",
		"HINDER_SAFE",
		"HINDER_EQ",
		"HINDER_PASSENGER",
		"URGENT_LEVEL",
		"FAULT_LEVEL",
		"GZX"
	])
})
const getQueryData = (data: { [propName: string]: any }) => {
	const { reportTime } = data
	data.reportTime_start = reportTime ? `${reportTime[0]} 00:00:00` : ""
	data.reportTime_end = reportTime ? `${reportTime[1]} 23:59:59` : ""

	delete data.reportTime
	currentPage.value = 1
	queryForm.value = {
		...queryForm.value,
		...data
	}
	fetchFaultList()
}
defineOptions({
	name: "EmergencyCommand"
})
</script>
<template>
	<div class="app-container">
		<div
			class="whole-frame"
			v-loading="faultListLoading"
			element-loading-text="加载中..."
		>
			<ModelFrame class="top-frame">
				<Query
					:queryArrList="queryArrList"
					class="ml10"
					@getQueryData="getQueryData"
				/>
			</ModelFrame>
			<ModelFrame class="bottom-frame">
				<Title :title="titleConf">
					<div class="app-tabs-wrapper">
						<el-tabs v-model="activeName" @tab-click="handleTabsClick">
							<el-tab-pane
								v-for="(tab, index) in tabList"
								:key="index"
								:label="`${tab.name}（${tabNum[index]}）`"
								:name="tab.name"
								:index="tab.name"
							/>
						</el-tabs>
					</div>
				</Title>
				<el-scrollbar height="calc(100vh - 200px)">
					<div style="height: 100%">
						<div class="device-list" v-if="faultList && faultList.length > 0">
							<DeviceCard
								v-for="device in faultList"
								:key="device.id"
								:device="device"
								@fault="handleFault"
								@itemClick="handleItemClick"
							/>
						</div>
						<div v-if="faultList && faultList.length === 0" class="no-data">
							暂无数据
						</div>
					</div>
				</el-scrollbar>
			</ModelFrame>
		</div>
		<Drawer
			:size="modalSize.md"
			v-model:drawer="faultDetailVisible"
			:destroyOnClose="true"
		>
			<FaultDetailDrawer
				:visible="faultDetailVisible"
				:detail="faultDetail"
				:dictOptions="dictOptions"
				@clickBtn="handleClickBtn"
			/>
		</Drawer>
		<Drawer
			:size="940"
			v-model:drawer="standardProcessVisible"
			:destroyOnClose="true"
		>
			<StandardProcessDrawer
				ref="standardProcessDrawerRef"
				:lineId="faultDetail?.lineId"
				:visible="standardProcessVisible"
				:loading="standardProcessBtnLoading"
				@update:visible="standardProcessVisible = $event"
				@clickBtn="handleStartProcess"
			/>
		</Drawer>
		<!-- 标准处置流程执行信息 -->
		<Drawer
			:size="modalSize.xl"
			v-model:drawer="standardProcessInfoVisible"
			:destroyOnClose="true"
			:show-close="false"
		>
			<StandardProcessInfoDrawer
				:detail="faultDetail"
				:loading1="loading1"
				:dictOptions="dictOptions"
				@close="standardProcessInfoVisible = false"
			/>
		</Drawer>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.device-list {
	display: grid;
	grid-template-columns: repeat(5, 1fr);
	gap: 16px;
	padding: 16px;
}

.no-data {
	text-align: center;
	padding: 32px;
	color: #909399;
	font-size: 14px;
	height: 100%;
}

.underline-link {
	text-decoration: none;
	border-bottom: 1px solid;
	display: inline-block;
	transition: border-bottom-color 0.2s;
}

.underline-link:hover {
	border-bottom-color: transparent;
}

.dialog-box {
	:deep(.el-dialog__body) {
		margin: 10px;
		padding: 20px 0 10px !important;
		border-top: 1px solid #ddd;
		border-bottom: 1px solid #ddd;
	}
}

.fault-detail-dialog {
	:deep(.el-dialog__body) {
		padding: 20px;
	}

	.fault-detail-content {
		.section {
			margin-bottom: 24px;

			h3 {
				margin: 0 0 16px;
				font-size: 16px;
				font-weight: 500;
				color: #303133;
			}
		}
	}

	:deep(.el-descriptions__label) {
		width: 120px;
		background-color: #f5f7fa;
	}
}

.dialog-footer {
	text-align: right;
}
</style>
