<!--
 * @Author: liulianming
 * @Date: 2025-07-14 23:42:19
 * @LastEditors: liulianming
 * @LastEditTime: 2025-07-27 08:33:45
 * @Description: 应急指挥
-->
<script lang="ts" setup>
import { ref, onMounted, reactive, computed, watch } from "vue"
import { modalSize } from "@/app/baseline/utils/layout-config"
import { ElMessage } from "element-plus"
import { LineVo } from "@/app/baseline/utils/types/common"
import StandardProcessDrawer from "./components/StandardProcessDrawer.vue"
import {
	getFaultReportStatusStatistics,
	getFaultList,
	getFaultReportDetail,
	type FaultItem,
	startStandardProcess
} from "@/app/baseline/api/dispatch"
import { listCompanyWithFormat } from "@/app/baseline/api/system"
import DeviceCard from "./components/DeviceCard.vue"
import FaultDetailDrawer from "./components/FaultDetailDrawer.vue"
import { LineApi } from "../../api/system/line"
import { usePagination } from "../../hooks/usePagination"
import { getToken } from "../../utils/cache/cookies"
import StandardProcessInfoDrawer from "./components/StandardProcessInfoDrawer.vue"
const { dictOptions, getDictByCodeList, dictFilter } = useDictInit()
import { IModalType } from "@/app/baseline/utils/types/common"
import { useDictInit } from "@/app/baseline/views/components/dictBase"
/**
 * 公司列表
 */
const companyOptions = ref<any>([])

/**
 * 查询条件 配置
 */
const queryArrList = computed(() => [
	{
		name: "所属线路",
		key: "lineId",
		type: "select",
		children: lineList.value.map((item) => ({
			label: item.name,
			value: item.id
		})),
		placeholder: "请选择"
	},
	{
		name: "所属车站",
		key: "code",
		placeholder: `请选择`,
		enableFuzzy: true,
		type: "select"
	},
	{
		name: "上报时间",
		key: "reportTime",
		placeholder: `请选择`,
		enableFuzzy: true,
		type: "startAndEndTime"
	},
	{
		name: "关键字",
		key: "key",
		type: "input",
		placeholder: "请输入"
	}
])

const tabList = [
	{
		name: "待执行",
		icon: ["fas", "square-plus"]
	},
	{
		name: "执行中",
		icon: ["fas", "square-plus"]
	},
	{
		name: "已关闭",
		icon: ["fas", "square-plus"]
	}
]
const tabNum = ref(Array.from({ length: tabList.length }, () => 0))
const activeName = ref(tabList[0].name)
const activeIndex = ref(0)

/**
 * 查询条件
 */
const queryForm = ref({
	key: "",
	lineId: "",
	reportTime: []
})

/**
 * 故障列表
 */
const faultList = ref<any>()

/**
 * 获取故障报告状态统计数据
 */
const fetchStatusStatistics = async () => {
	try {
		const params = {
			...queryForm.value
		}
		const data = await getFaultReportStatusStatistics(params)
		// 更新每个tab的数量
		tabNum.value[0] = data["待执行"] || 0 // 待执行
		tabNum.value[1] = data["执行中"] || 0 // 执行中
		tabNum.value[2] = data["已关闭"] || 0 // 已关闭
	} catch (error) {
		console.error("获取故障报告状态统计失败:", error)
	}
}

/**
 * 获取故障列表
 */
const fetchFaultList = async () => {
	faultListLoading.value = true
	try {
		const params = {
			...queryForm.value,
			statusType: activeName.value
		}
		const res = await getFaultList(params)
		faultList.value = res || []
	} catch (error) {
		console.error("获取故障列表失败:", error)
		ElMessage.error("获取故障列表失败")
	} finally {
		faultListLoading.value = false
	}
}

// 监听tab切换和查询条件变化
watch(
	() => [activeIndex.value, queryForm.value],
	() => {
		fetchFaultList()
		fetchStatusStatistics()
	},
	{ deep: true }
)
const faultListLoading = ref(false)
// 线路列表
const lineList = ref<LineVo[]>([])
const usePaginationStore = usePagination()
const usePaginationStoreForData = usePaginationStore.paginationData
const currentPage = ref<number>(usePaginationStoreForData.currentPage)
const pageSize = ref<number>(usePaginationStoreForData.pageSize)
/**
 * 获取线路配置
 */
function getLineList() {
	LineApi.getLineList(
		objectToFormData({
			currentPage: currentPage.value,
			pageSize: 9999
		})
	).then((res: any) => {
		lineList.value = res.rows ?? []
	})
}
/**
 * 页面标题
 */
const titleConf = {
	name: ["应急指挥"],
	icon: ["fas", "square-share-nodes"]
}

/**********************tab 相关操作 *********************************/
/**
 * 单击 操作
 * @param tab
 */
const handleTabsClick = (tab: any) => {
	activeName.value = tab.paneName
	activeIndex.value = tab.index
	faultList.value = null
	faultListLoading.value = true
}

// 故障详情抽屉相关
const faultDetailVisible = ref(false)
const currentFaultId = ref<string | number>()
const faultDetail = ref<any>(null)
const standardProcessVisible = ref(false)
const standardProcessInfoVisible = ref(false)
const standardProcessDrawerRef = ref<any>(null)
// 处理故障停按钮点击
const handleFault = async (device: any) => {
	currentFaultId.value = device.id
	try {
		const detail = await getFaultReportDetail(device.id)
		faultDetailVisible.value = true
		faultDetail.value = detail
	} catch (error) {
		console.error("获取故障详情失败:", error)
		ElMessage.error("获取故障详情失败")
	}
}
const handleItemClick = (device: any) => {
	console.log("device", device)
	faultDetail.value = device
	if (device.sts === 2) {
		handleFault(device)
	} else if (device.sts === 3) {
		window.open(
			`http://192.168.31.10:9000/?id=${device.executeMainId}&token=${
				"Bearer " + getToken()
			}`,
			"_blank"
		)
	} else if (device.sts === 4) {
		standardProcessInfoVisible.value = true
	}
}
const handleStartProcess = async (doc: any) => {
	const params = {
		faultReportId: faultDetail.value.id,
		setMainId: doc.id,
		mainTitle: doc.mainTitle,
		disposalLevel: doc.disposalLevel
	}
	const res = await startStandardProcess(params)
	ElMessage.success("启动标准处置流程成功")
	standardProcessVisible.value = false
	faultDetailVisible.value = false
	fetchFaultList()
	setTimeout(() => {
		//跳转外链
		window.open(
			`http://192.168.31.10:9000/?id=${res.id}&token=${"Bearer " + getToken()}`,
			"_blank"
		)
	}, 500)
}

const handleClickBtn = (btnName: string | undefined) => {
	if (btnName === "标准处置流程选择") {
		standardProcessVisible.value = true
		const params = {
			lineId: faultDetail.value.lineId,
			status: 1 // 已发布
		}
		setTimeout(() => {
			standardProcessDrawerRef?.value?.getLocComposeTree()
			standardProcessDrawerRef?.value?.getDictByCodeListFun()
			standardProcessDrawerRef?.value?.getQueryData(params)
		}, 100)
	}
}
onMounted(async () => {
	getLineList()

	// 获取故障报告状态统计
	await fetchStatusStatistics()
	// 获取故障列表
	await fetchFaultList()
	//获取字典
	await getDictByCodeList([
		"CLOSE_WAY",
		"HINDER_LINE",
		"HINDER_SAFE",
		"HINDER_EQ",
		"HINDER_PASSENGER",
		"URGENT_LEVEL",
		"FAULT_LEVEL",
		"GZX"
	])
})
const getQueryData = (data: { [propName: string]: any }) => {
	const { reportTime } = data
	data.reportTime_start = reportTime ? `${reportTime[0]} 00:00:00` : ""
	data.reportTime_end = reportTime ? `${reportTime[1]} 23:59:59` : ""

	delete data.reportTime
	currentPage.value = 1
	queryForm.value = {
		...queryForm.value,
		...data
	}
	fetchFaultList()
}
defineOptions({
	name: "EmergencyCommand"
})
</script>
<template>
	<div class="app-container">
		<div class="whole-frame">
			<ModelFrame class="top-frame">
				<Query
					:queryArrList="queryArrList"
					class="ml10"
					@getQueryData="getQueryData"
				/>
			</ModelFrame>
			<ModelFrame class="bottom-frame">
				<Title :title="titleConf">
					<div class="app-tabs-wrapper">
						<el-tabs v-model="activeName" @tab-click="handleTabsClick">
							<el-tab-pane
								v-for="(tab, index) in tabList"
								:key="index"
								:label="`${tab.name}（${tabNum[index]}）`"
								:name="tab.name"
								:index="tab.name"
							/>
						</el-tabs>
					</div>
				</Title>
				<div
					v-loading="faultListLoading"
					element-loading-text="加载中..."
					style="height: 100%"
				>
					<div class="device-list" v-if="faultList && faultList.length > 0">
						<DeviceCard
							v-for="device in faultList"
							:key="device.id"
							:device="device"
							@fault="handleFault"
							@itemClick="handleItemClick"
						/>
					</div>
					<div v-if="faultList && faultList.length === 0" class="no-data">
						暂无数据
					</div>
				</div>
			</ModelFrame>
		</div>
		<Drawer
			:size="modalSize.md"
			v-model:drawer="faultDetailVisible"
			:destroyOnClose="true"
		>
			<FaultDetailDrawer
				:visible="faultDetailVisible"
				:detail="faultDetail"
				:dictOptions="dictOptions"
				@clickBtn="handleClickBtn"
			/>
		</Drawer>
		<Drawer
			:size="modalSize.lg"
			v-model:drawer="standardProcessVisible"
			:destroyOnClose="true"
		>
			<StandardProcessDrawer
				ref="standardProcessDrawerRef"
				:lineId="faultDetail?.lineId"
				:visible="standardProcessVisible"
				@update:visible="standardProcessVisible = $event"
				@clickBtn="handleStartProcess"
			/>
		</Drawer>
		<!-- 标准处置流程执行信息 -->
		<Drawer
			:size="modalSize.xl"
			v-model:drawer="standardProcessInfoVisible"
			:destroyOnClose="true"
			:show-close="false"
		>
			<StandardProcessInfoDrawer
				:detail="faultDetail"
				@close="standardProcessInfoVisible = false"
			/>
		</Drawer>
	</div>
</template>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

.device-list {
	display: grid;
	grid-template-columns: repeat(5, 1fr);
	gap: 16px;
	padding: 16px;
}

.no-data {
	text-align: center;
	padding: 32px;
	color: #909399;
	font-size: 14px;
	height: 100%;
}

.underline-link {
	text-decoration: none;
	border-bottom: 1px solid;
	display: inline-block;
	transition: border-bottom-color 0.2s;
}

.underline-link:hover {
	border-bottom-color: transparent;
}

.dialog-box {
	:deep(.el-dialog__body) {
		margin: 10px;
		padding: 20px 0 10px !important;
		border-top: 1px solid #ddd;
		border-bottom: 1px solid #ddd;
	}
}

.fault-detail-dialog {
	:deep(.el-dialog__body) {
		padding: 20px;
	}

	.fault-detail-content {
		.section {
			margin-bottom: 24px;

			h3 {
				margin: 0 0 16px;
				font-size: 16px;
				font-weight: 500;
				color: #303133;
			}
		}
	}

	:deep(.el-descriptions__label) {
		width: 120px;
		background-color: #f5f7fa;
	}
}

.dialog-footer {
	text-align: right;
}
</style>
