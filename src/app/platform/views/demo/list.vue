<template>
	<div class="app-container">
		<ModelFrame style="padding: 20px; height: 260px">
			<div style="margin-bottom: 10px">
				<el-button @click="autoUload = true">裁剪</el-button>
				<el-button @click="autoUload = false">不裁剪</el-button>
			</div>
			<Upload
				:list-type="''"
				:maxCount="9"
				:file-list="files"
				:accept="assetFileType"
				:autoUload="autoUload"
				:data="{
					businessType: 7
				}"
				:multiple="false"
			/>
		</ModelFrame>
		<!-- 自动上传 -->
		<ModelFrame style="padding:20px; margin-top: 20px;">
			<el-row :gutter="20">
				<el-col :span="24">
					<div style="display: flex; align-items: center; gap: 20px">
						<div style="font-weight: bold; white-space: nowrap">自动上传（文件选择后立即上传）</div>
						<UploadFile 
							style="width: 30%"
							ref="uploadRef"
							class="jumper-slot-btn"
							:list-type="''"
							:action="`/pitaya/system/common/upload`"
							:file-list="files"
							:max-count="10"
							:accept="assetFileTypes"
							:allExtensions="allExtensions"
							:businessType="2"
							@onSuccess="handleUpload"
							@handleRemove="()=>{}"
							@update:files="handleFilesUpdate" 
							:autoUpload="true"
							:data="{
								businessType: 20,
								businessId: 1
							}"
							:multiple="true"
							:fileListShow="false"
						/>
					</div>
				</el-col>
			</el-row>
		</ModelFrame>
		<!-- 自定义上传 -->
		 <ModelFrame style="padding: 20px; margin-top: 20px;">
			<el-row :gutter="20">
				<el-col :span="24">
					<div style="display: flex; align-items: center; gap: 20px">
						<div style="font-weight: bold; white-space: nowrap">自定义上传（手动触发上传）</div>
						<UploadFile 
							style="width: 30%"
							ref="uploadRef"
							class="jumper-slot-btn"
							:list-type="''"
							:action="`/pitaya/system/common/upload`"
							:file-list="files"
							:max-count="10"
							:accept="assetFileTypes"
							:allExtensions="allExtensions"
							:businessType="2"
							@onSuccess="handleUpload"
							@handleRemove="()=>{}"
							@update:files="handleFilesUpdate" 
							:autoUpload="false"
							:data="{
								businessType: 20,
								businessId: 1
							}"
							:multiple="true"
							:fileListShow="false"
						/>
						<el-button @click="onFormBtnClick">上传</el-button>
					</div>
				</el-col>
			</el-row>
		</ModelFrame>
		<ModelFrame style="padding: 20px; height: 260px; margin-top: 20px;">
			<div style="margin-bottom: 10px">二维码展示</div>
			<QRCodeVue
				value="789452b7-9809-462c-ac42-60a547685d49"
				:size="130"
				level="H"
			></QRCodeVue>
		</ModelFrame>
		<!-- 搜索选择人员 -->
		<ModelFrame style="padding: 20px; margin-top: 20px;">
			<el-row :gutter="20">
				<el-col :span="10">
					<RealnameSearch 
						v-model="searchValue" 
						:options="userOptions" 
						placeholder="非query请输入拼音/中文，可以自定义文案，宽度"
						width="100%"
						@select="handleSelect" 
					/>
				</el-col>
				<el-col :span="14">
					<Query
						:queryArrList="queryArrList"
						@getQueryData="getQueryData"
						:numInRow="3"
						:queryBtnColSpan="6"
					/>
				</el-col>
			</el-row>
		</ModelFrame>
		<ModelFrame style="padding: 20px; margin-top: 20px;">
			<el-row :gutter="20">
				<el-col :span="10">
					<RealnameSearch 
						v-model="searchValueMore" 
						:options="userOptions" 
						placeholder="多选的"
						width="100%"
						multiple
						collapseTags
						:max-display-count="2"
						@select="handleSelectMore" 
					/>
				</el-col>
				<el-col :span="14">
					<Query
						:queryArrList="queryArrListMore"
						@getQueryData="getQueryDataMore"
						:numInRow="3"
						:queryBtnColSpan="6"
					/>
				</el-col>
			</el-row>
		</ModelFrame>
	</div>
</template>
<script setup lang="ts">
import { ref, nextTick, toRaw } from 'vue'
import QRCodeVue from "qrcode.vue"
// @ts-ignore
import UploadFile from "@/compontents/UploadFile.vue"

const ButtonLoading = ref(false)

defineOptions({
	name: "demoList"
})

const autoUload = ref(false)
/**
 * 上传组件相关参数
 */
const files = ref<any[]>([])
// 限制文件上传类型
const assetFileType = "image/jpg, image/jpeg, image/png, video/*"
// 批量上传文件，可以立即上传，也可以自定义上传事件
const uploadRef = ref<any>(null);
const assetFileTypes = "image/jpg, image/jpeg, image/png,image/webp, application/pdf,"
const allExtensions = [
	".jpeg",
	".pdf",
	".png",
	".jpg",
	".webp"
] // 允许上传的文件扩展名
// 自定义文件上传回调
const handleFilesUpdate = (file:any) =>{
	console.log('===file自定义文件上传回调',file)
}
// 上传文件回调
const handleUpload = (fileValue: any) => {
    console.log('上传文件回调fileValue', fileValue)
    files.value.push(fileValue.data)
	ButtonLoading.value = false
}
const onFormBtnClick = async () =>{
	const result = await uploadRef.value?.handleSubmit()
}
// 实现拼音，中文都可以搜索选中回显搜索功能
// 单选默认选中张三(value='1')
const searchValue = ref('1')
// 多选默认选中张三(value='1')和王五(value='3')  
const searchValueMore = ref<string[]>(['1', '3'])
const userOptions = ref([ 
		{ value: '1', label: '张三' }, 
		{ value: '2', label: '李四' }, 
		{ value: '3', label: '王五' },     
		{ value: '4', label: '秦韬' },
		{ value: '5', label: '吴文姣' },
        { value: '6', label: '李孟飞' },
		{ value: '7', label: '崔文姣' }
]) 
const handleSelect = (item:any) => {
	searchValue.value = item.label
}
const handleSelectMore = (items:any[]) => {
	console.log('==items',items)
}
// query中使用
const queryArrList = ref<any[]>([{
    name: "用户姓名",
    key: "realname", 
    type: "inputRealname",
    enableFuzzy: false,
    placeholder: "输入中文或拼音",
    children:[
        { value: '赵宇宁', label: '赵宇宁' },
        { value: '李文姣', label: '李文姣' },
		{ value: '闫海波', label: '李海波' },
        { value: '李海波', label: '李海波' },
        { value: '秦韬', label: '秦韬' },
		{ value: '吴文姣', label: '吴文姣' },
        { value: '李孟飞', label: '李孟飞' },
		{ value: '崔文姣', label: '崔文姣' },
        { value: '何宇宇', label: '何宇宇' },
		{ value: '许婕', label: '许婕' },
        { value: '海萍', label: '海萍' },
    ]
}])
const queryArrListMore = ref<any[]>([{
    name: "用户姓名",
    key: "realname", 
    type: "inputRealname",
    enableFuzzy: false,
	collapseTags:true,
	multiple:true,
    placeholder: "输入中文或拼音",
    children:[
        { value: '赵宇宁', label: '赵宇宁' },
        { value: '李文姣', label: '李文姣' },
		{ value: '闫海波', label: '李海波' },
        { value: '李海波', label: '李海波' },
        { value: '秦韬', label: '秦韬' },
		{ value: '吴文姣', label: '吴文姣' },
        { value: '李孟飞', label: '李孟飞' },
		{ value: '崔文姣', label: '崔文姣' },
        { value: '何宇宇', label: '何宇宇' },
		{ value: '许婕', label: '许婕' },
        { value: '海萍', label: '海萍' },
    ]
}])
const getQueryData = (queryParams?: any) => {
	console.log('==queryParams',queryParams)
}


const getQueryDataMore = (queryParams?: any) => {
	console.log('==queryParams', toRaw(queryParams.realname))
}
</script>
<style lang="scss" scoped>
:deep(.el-tag__content){
	line-height: 10px;
}
</style>
