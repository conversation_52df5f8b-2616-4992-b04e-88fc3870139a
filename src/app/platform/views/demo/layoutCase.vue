<template>
	<!-- 搜索框 + 表格框  -->
	<div class="app-container" v-if="true">
		<ModelFrame>
			<Query :queryArrList="queryArrList" @getQueryData="getQueryData" />
		</ModelFrame>
		<ModelFrame class="content">
			<Title :title="title" :button="button" @onBtnClick="onBtnClick" />
			<PitayaTable
				ref="tableRef"
				:table-data="testTableData"
				:columns="testColumns"
				:table-tree-props="testTableTreeProps"
				:need-selection="true"
			>
				<template #address="data"> {{ data.rowData }} </template>
			</PitayaTable>
		</ModelFrame>
	</div>

	<!--侧边栏+ 搜索框 + 表格框  -->
	<div class="app-container-row" v-if="false">
		<ModelFrame class="left-model-frame">
			<Title :title="title" />
			<PitayaTree
				:treeData="departmentTreeData"
				:treeProps="departmentTreeProp"
				:needCheckBox="false"
				:defaultExpandedKeys="['1']"
				@onTreeClick="departmentTreeClick"
			/>
		</ModelFrame>
		<div class="right-model-frame">
			<ModelFrame>
				<Query :queryArrList="queryArrList" @getQueryData="getQueryData" />
			</ModelFrame>
			<ModelFrame class="content">
				<Title :title="title" :button="button" @onBtnClick="onBtnClick" />
				<PitayaTable
					ref="tableRef"
					:table-data="testTableData"
					:columns="testColumns"
					:table-tree-props="testTableTreeProps"
					:need-selection="true"
				>
					<template #address="data"> {{ data.rowData }} </template>
				</PitayaTable>
			</ModelFrame>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

const queryArrList = ref<any[]>([
	{
		name: "参数名称",
		key: "name",
		type: "input",
		placeholder: "请输入查询关键字"
	},
	{
		name: "重要级别",
		key: "grade",
		code: "DEVICE_LEVEL",
		type: "select",
		placeholder: "请输入查询关键字",
		children: [
			{
				label: "全部",
				value: ""
			}
		]
	},
])

const getQueryData = (queryData: anyKey) => {
	console.log("queryData", queryData)
}

const testColumns = ref<TableColumnType[]>([
	{ prop: "date", label: "时间", align: "left" },
	{ prop: "name", label: "姓名" },
	{ prop: "address", label: "地址", needSlot: true }
])
const testTableData = ref<any[]>([
	{
		id: 1,
		date: "2016-05-02",
		name: "王小虎",
		address: "上海市普陀区金沙江路 1518 弄"
	},
	{
		id: 2,
		date: "2016-05-04",
		name: "王小虎",
		address: "上海市普陀区金沙江路 1517 弄"
	},
	{
		id: 3,
		date: "2016-05-01",
		name: "王小虎",
		address: "上海市普陀区金沙江路 1519 弄"
	}
])
const tableRef = ref<PitayaTableRef>()
const clearAllSelect = () => {
	tableRef.value?.pitayaTableRef.clearSelection()
}

const title = {
	name: ["数据字典"],
	icon: ["fas", "square-share-nodes"]
}

const button = [
	{
		name: "新增字典",
		icon: ["fas", "square-plus"]
	}
]

const onBtnClick = (e: string | undefined) => {
	console.log(e)
}

const departmentTreeData = ref<any[]>([
	{
		id: "1",
		label: "机电分公司",
		children: [
			{
				id: "1",
				label: "安全质量部(0101)",
				name: "安全质量部",
				fatherRoot: "机电分公司"
			},
			{
				id: "3",
				label: "第八项目部(02)",
				name: "第八项目部",
				fatherRoot: "机电分公司",
				children: [
					{
						id: "3-1",
						label: "安全质量室(0201)",
						name: "安全质量室",
						fatherRoot: "第八项目部"
					},
					{
						id: "3-2",
						label: "综合管理室(0202)",
						name: "综合管理室",
						fatherRoot: "第八项目部",
						children: [
							{
								id: "3-2",
								label: "综合管理室(0202)",
								name: "综合管理室",
								fatherRoot: "第八项目部",
								iconConfig: {
									icon: ["fas", "user-group"],
									iconStyle: {
										color: "#f08c34"
									}
								}
							}
						]
					}
				]
			}
		]
	}
])
const departmentTreeProp = {
	children: "children",
	label: "label"
}
// 树点击
const departmentTreeClick = (data: any, nodeData: any) => {
	console.log(data, nodeData)
}
</script>
<style lang="scss" scoped></style>
