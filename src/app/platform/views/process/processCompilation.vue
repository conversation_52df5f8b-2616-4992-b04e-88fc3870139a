<template>
	<div class="app-container">
		<ModelFrame>
			<Query
				class="ml10"
				:queryArrList="searchArrList"
				@getQueryData="getSearchData"
			/>
		</ModelFrame>
    <ModelFrame class="content">
		  <Title
		  	:title="processTitle"
		  	:button="processTitleBtn"
		  	@onBtnClick="showProcessDrawer"
		  >
				<Tabs
					style="position: absolute; left: 140px"
					:tabs="tabsArr"
					@on-tab-change="changeTabs"
				/>
			</Title>
		  <el-scrollbar>
		  	<PitayaTable
		  		ref="processTableRef"
					:customizeHeightNumber="0"
		  		:columns="tableColumns"
		  		:tableData="tableData"
		  		:total="total"
		  		:single-select="true"
		  		:need-selection="true"
		  		@on-current-page-change="onChangeCurrentPageChange"
		  		:table-loading="tableLoading"
		  	>
					<template #lineName="{ rowData }">
						<div>
							<el-tag
								class="line-item-btn"
								:style="{
									color: '#FFFFFF',
									backgroundColor: 'rgb(245, 155, 34)',
									borderRadius: '4px !important'
								}"
							>
								{{ rowData.lineName }}
							</el-tag>
						</div>
					</template>
					<template #disposalLevel="{ rowData }">
						<div>
							<el-tag
								class="line-item-btn"
								:style="{
									color: '#FFFFFF',
									backgroundColor: rowData.disposalLevelColor,
									border: '0',
									borderRadius: '4px !important'
								}"
							>
								{{ rowData.disposalLevel }}
							</el-tag>
						</div>
					</template>
					<template #version="{ rowData }">
						<div>
							<el-tag
								class="line-item-btn"
								:style="{
									color: '#FFFFFF',
									backgroundColor: 'rgb(40, 193, 193)',
									borderRadius: '4px !important'
								}"
							>
								{{ rowData.version }}
							</el-tag>
						</div>
					</template>
					<template #bmpStatusName="{ rowData }">
						<div>
							<el-tag
								class="line-item-btn"
								:type="rowData.statusType"
								:style="{
									width: '50px',
									height: '22px',
									color: rowData.statusColor,
									borderRadius: '4px !important',
									backgroundColor: rowData.statusBgColor,
									borderColor: rowData.statusBorderColor
								}"
							>
								{{ rowData.bmpStatusName }}
							</el-tag>
						</div>
					</template>
		  		<template #operations="{ rowData }">
            <el-button v-btn link @click="viewLine(rowData)">
		  				<font-awesome-icon
		  					:icon="['fas', 'eye']"
		  					style="color: var(--pitaya-btn-background)"
		  				/>
		  				<span class="table-inner-btn">查看</span>
		  			</el-button>
		  			<el-button v-btn link @click="editLine(rowData)">
		  				<font-awesome-icon
		  					:icon="['fas', 'pen-to-square']"
		  					style="color: var(--pitaya-btn-background)"
		  				/>
		  				<span class="table-inner-btn">编辑</span>
		  			</el-button>
		  			<el-button v-btn link @click="deleteLine(rowData)" v-if="rowData.status == 0">
		  				<font-awesome-icon
		  					:icon="['fas', 'trash-can']"
		  					style="color: var(--pitaya-btn-background)"
		  				/>
		  				<span class="table-inner-btn">移除</span>
		  			</el-button>
		  		</template>
		  		<template #footerOperateLeft>
		  			<ButtonList
		  				class="btn-list"
		  				:is-not-radius="true"
		  				:button="bottomBtnArray"
		  				:loading="bottomBtnLoading"
		  				@on-btn-click="clickBottomBtn"
		  			/>
		  		</template>
		  	</PitayaTable>
		  </el-scrollbar>

			<!-- 标准流程配置 -->
			<baseProcessFormDrawer ref="baseProcessFormDrawerRef" @submit-success="getList" />

			<!-- 编辑/查看标准处置流程信息 -->
			<Drawer
				size="1230"
				v-model:drawer="showEditProcessDrawer"
				:destroyOnClose="true"
			>
				<editProcessDrawer ref="editProcessDrawerRef" :processId="processId" :isView="isView" @hideEditDrawer="hideEditDrawer" @re-get-list="getList" />
			</Drawer>
	  </ModelFrame>
	</div>
</template>

<script lang="ts" setup>
import { usePagination } from "@/app/platform/hooks/usePagination"
import { _matchFormProp } from "@/app/platform/utils/permission"
import { CustomMessageBox } from "@/app/platform/utils/message"
import baseProcessFormDrawer from "./components/baseProcessFormDrawer.vue"
import editProcessDrawer from "./components/editProcessDrawer.vue"
import { DictApi } from "@/app/baseline/api/dict"
import { useMajor } from "@/app/platform/hooks/useMajor"
import { processApi } from "../../api/process/processCompilation"

// 检索项相关
const searchArrList = ref<any[]>([
	{
		name: "流程位置",
		key: "locComposeNo",
		placeholder: "请选择流程位置",
		type: "elTreeSelect",
		children: []
	},
	{
		name: "流程级别",
		key: "disposalLevel",
		placeholder: "请选择流程级别",
		enableFuzzy: false,
		type: "select",
		children: []
	},
  {
		name: "所属专业",
		key: "majorId",
		placeholder: "请选择所属专业",
		enableFuzzy: false,
		type: "select",
		children: []
	},
  {
		name: "",
		key: "mainTitle",
		placeholder: "请输入查询关键字",
		enableFuzzy: false,
		type: "input"
	},
])
const searchData = ref<any>({}) // 检索项
const useMajorStore = useMajor(true)
const bmpStatus = ref<number>(0) //状态

// 标签栏相关
const tabsArr = ref(['草稿箱(0)', '审批中(0)', '已发布(0)'])

// 表格相关
const processTitle = {
	name: ['流程编制'],
	icon: ["fas", "square-share-nodes"]
} // 表格title 
const processTitleBtn = [
	{
		name: "新增标准处置流程",
		roles: "process:processCompilation:btn:add",
		icon: ["fas", "square-plus"]
	}
] // 表格交互按钮
const baseProcessFormDrawerRef = ref<any>(null)
const tableColumns: TableColumnType[] = [
	{ label: "流程编号", prop: "mainCode", width: 165 },
	{ label: "所属专业", prop: "majorName" },
	{ label: "所属线路", prop: "lineName", needSlot: true },
	{ label: "关联位置", prop: "locComposeName", width: 185 },
	{ label: "流程标题", prop: "mainTitle", width: 220 },
	{ label: "流程级别", prop: "disposalLevel", needSlot: true, width: 120 },
	{ label: "版本号", prop: "version", needSlot: true, width: 120 },
	{ label: "发布状态", prop: "bmpStatusName", needSlot: true, width: 120 },
	{ label: "创建人", prop: "createdByName", width: 120 },
	{ label: "更新时间", prop: "createdDate", width: 150 },
	{
		label: "操作",
		prop: "operations",
		fixed: "right",
		needSlot: true,
		width: 200
	}
] //表格列
const tableData = ref<any[]>([]) //表格数据
const total = ref<number>(0)
// 表格配置项
const tableLoading = ref<boolean>(false)
const usePaginationStore = usePagination()
const usePaginationStoreForData = usePaginationStore.paginationData
const pageSize = ref<number>(usePaginationStoreForData.pageSize)
const currentPage = ref<number>(usePaginationStoreForData.currentPage)
const processTableRef = ref<any>(null)
const isView = ref<boolean>(false)

// 导入按钮
const bottomBtnArray = ref([
	{
		name: "流程导入",
		roles: "configuration:process:btn:enable",
		icon: ["fas", "power-off"],
		disabled: true
	}
])
const bottomBtnLoading = ref<boolean>(false) //启用停用按钮状态
const showEditProcessDrawer = ref<boolean>(false) //控制编辑/查看标准处置流程弹窗
const processId = ref<string>('') //要编辑的流程id

onMounted(() => {
	getDisposalLevel()
	getLocationTreeData()
	getProcessList()
	getProcessStatusNum()
})

/**
 * @description 选择搜索参数回显
 * @param queryParams 检索参数
 */
const getSearchData = (queryParams?: any) => {
	searchData.value.locComposeNo = queryParams.locComposeNo
		? queryParams.locComposeNo
		: undefined
	searchData.value.disposalLevel = queryParams.disposalLevel
		? queryParams.disposalLevel
		: undefined
	searchData.value.majorId = queryParams.majorId
		? queryParams.majorId
		: undefined
	searchData.value.mainTitle = queryParams.mainTitle
		? queryParams.mainTitle
		: undefined
	getProcessList()
	getProcessStatusNum()
}

/**
 * @description 递归处理数据, 为 cascade 的每个 options 添加 label 和 value 属性
 */
const addValueAndLabelForTree = (arr: any[]): any => {
  arr = arr.map(node => {
    if (node.hasOwnProperty('children')) {
      node.children = addValueAndLabelForTree(node.children)
    }
    return {
      ...node,
      label: `${node.locComposeNameBelong}(${node.locComposeNoBeLong})`,
      value: node['locComposeNo'],
    }
  })
  return arr
}

/**
 * @description 获取流程级别
 */
const getDisposalLevel = () => {
	DictApi.getDictByCode("DISPOSAL_LEVEL")
	.then((res) => {
		searchArrList.value[1].children = res as any
		searchArrList.value[2].children = useMajorStore.majorArr
	})
}

/**
 * @description 获取关联位置树的数据
 */
const getLocationTreeData = () => {
	processApi.getPositionTreeApi(
		objectToFormData({
			currentPage: 1,
			pageSize: 500
		})
	)
		.then((res: any) => {
			searchArrList.value[0].children = []
			const map = new Map(Object.entries(res))
			map.forEach((value: any, key) => {
				searchArrList.value[0].children = addValueAndLabelForTree(value)
			})
		})
}

/**
 * @description 获取流程列表
 */
const getProcessList = () => {
	tableLoading.value = true
	processApi
		.getProcessListApi({ bmpStatus: bmpStatus.value, currentPage: currentPage.value - 1, pageSize: pageSize.value, ...searchData.value })
		.then((res: any) => {
			tableData.value = res.rows.map((item: any) => ({ ...item, 
				disposalLevelColor: item.disposalLevel === '一级' ? 'rgb(226, 94, 89)' : item.disposalLevel === '二级' ? 'rgb(245, 155, 34)' : item.disposalLevel === '三级' ? 'rgb(0, 157, 255)' : '',
				statusType: item.bmpStatusName === '草稿箱' ? 'danger' : item.bmpStatusName === '审批中' ? 'warning' : item.bmpStatusName === '已发布' ? 'success' : '',
				statusBorderColor: item.bmpStatusName === '草稿箱' ? 'rgb(210, 102, 94)' : item.bmpStatusName === '审批中' ? 'rgb(232, 159, 66)' : item.bmpStatusName === '已发布' ? 'rgb(102, 172, 139)' : '',
				statusBgColor: item.bmpStatusName === '草稿箱' ? 'rgba(226, 94, 89, 0.2)' : item.bmpStatusName === '审批中' ? 'rgb(232, 159, 66)' : item.bmpStatusName === '已发布' ? 'rgba(75, 174, 137, 0.2)' : '',
				statusColor: item.bmpStatusName === '草稿箱' ? 'rgb(226, 94, 89)' : item.bmpStatusName === '审批中' ? 'rgb(232, 159, 66)' : item.bmpStatusName === '已发布' ? 'rgb(75, 174, 137)' : ''
			})),
			total.value = res.records
		})
		.catch((err) => {
			throw new Error("getProcessListApi():::" + err)
		})
		.finally(() => {
			tableLoading.value = false
		})
}

/**
 * @description 获取流程各状态个数
 */
const getProcessStatusNum = () => {
	processApi
		.getProcessStatusNumApi()
		.then((res: any) => {
			tabsArr.value = [`草稿箱(${res[0]})`, `审批中(${res[1]})`, `已发布(${res[2]})`]
		})
		.catch((err) => {
			throw new Error("getProcessStatusNumApi():::" + err)
		})
}

/**
 * @description 切换tab栏
 */
const changeTabs = (index: number) => {
	bmpStatus.value = index
	getProcessList()
	getProcessStatusNum()
}

/**
 * @description 展示新增流程弹窗
 */
const showProcessDrawer = () => {
  baseProcessFormDrawerRef.value.showAddProcessDrawer = true
}

/**
 * @description 添加标准处置流程后重新获取列表
 */
const getList = () => {
	getProcessList()
	getProcessStatusNum()
}

/**
 * @description 切换分页
 * @param pageData 
 */
const onChangeCurrentPageChange = (pageData: any) => {
	pageSize.value = pageData.pageSize
	currentPage.value = pageData.currentPage
	getProcessList()
}


/**
 * @description 查看数据
 */
const viewLine = (row: any) => {
	isView.value = true
	processId.value = row.id
	showEditProcessDrawer.value = true
}

/**
 * @description 编辑数据
 * @param row 选择的行
 */
const editLine = (row: any) => {
	isView.value = false
	processId.value = row.id
	showEditProcessDrawer.value = true
}

/**
 * @description 删除数据
 * @param row 选择的行
 */
const deleteLine = (row: any) => {
	CustomMessageBox({ message: "确定要移除这行数据吗？" }, (res: boolean) => {
		if (res) {
			processApi.
			deleteFlowApi(row.id)
				.then((res: any) => {
					ElMessage.success('删除成功')
				})
				.catch((err) => {
					throw new Error("deleteFlowApi():::" + err)
				})
				.finally(() => {
					getProcessList()
					getProcessStatusNum()
				})
		}
	})
}

/**
 * @description 导入
 * @param btnName 
 */
const clickBottomBtn = (btnName: any) => {
	showEditProcessDrawer.value = true
}

/**
 * @description 隐藏编辑弹窗
 */
const hideEditDrawer = () => {
	getProcessList()
	getProcessStatusNum()
	showEditProcessDrawer.value = false
}
</script>

<style lang="scss" scoped>
.app-container-row {
	flex-direction: row;
}

.border-bottom-text {
	position: relative;
	color: #204a9c;
	text-decoration: underline;
	cursor: pointer;
}

.form-container {
  padding: 6px;
  box-sizing: border-box;
}
</style>
