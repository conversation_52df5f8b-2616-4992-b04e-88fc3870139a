<template>
  <PitayaTable
  	ref="selectedLeaderTableRef"
		:customizeHeightNumber="0"
  	:columns="selectedTableColumns"
  	:tableData="selectedTableData"
  	:single-select="false"
  	:need-selection="false"
		:table-loading="selectTableLoading"
  >
    <template #empName="{ rowData }">
			<div>{{ rowData.empName }}</div>
		</template>
		<template #postName="{ rowData }">
			<div>{{ rowData.postName || '-' }}</div>
		</template>
		<template #orgName="{ rowData }">
			<div>{{ rowData.expertInfoVo ? rowData.expertInfoVo.vsystemOrganization?.orgName : '-' }}</div>
		</template>
		<template #fcell="{ rowData }">
			<div>{{ rowData.expertInfoVo ? rowData.expertInfoVo.vsystemBaseUser.fcell : '-' }}</div>
		</template>
  	<template #operations="{ rowData }">
  		<el-button v-btn link @click="deleteLine(rowData)" v-if="(!props.isView && showAddBtn) || showAddBtn">
  			<font-awesome-icon
  				:icon="['fas', 'trash-can']"
  				style="color: var(--pitaya-btn-background)"
  			/>
  			<span class="table-inner-btn">移除</span>
  		</el-button>
			<p v-else>不可操作</p>
  	</template>
  	<template #footerOperateLeft>
  		<ButtonList
			 	v-if="(!props.isView && showAddBtn) || showAddBtn"
  			:is-not-radius="true"
  			:button="selectedBottomBtnArray"
  			@on-btn-click="clickBottomBtn"
  		/>
  	</template>
  </PitayaTable>

  <Drawer
		size="1230"
		v-model:drawer="showChooseLeadersDrawer"
	>
    <div class="ChooseLeader_container">
	  	<div class="ChooseLeader-left">
	  		<Title :title="treeTitle" />
	  		<div class="app-el-scrollbar-wrapper">
	  			<el-scrollbar>
	  				<PitayaTree
	  					:treeData="organizationTreeData"
	  					:treeProps="organizationTreeProps"
	  					:needCheckBox="false"
	  					@onTreeClick="clickTreeNode"
	  					:tree-loading="organizationTreeLoading"
	  				/>
	  			</el-scrollbar>
	  		</div>
	  	</div>
	  	<div class="ChooseLeader-right">
	  		<Title :title="chooseLeadersTitle" />
		    <section class="table_container">
          <PitayaTable
		      	ref="leaderTableRef"
		      	:columns="tableColumns"
						:customizeHeightNumber="0"
		      	:tableData="tableData"
		      	:total="total"
						select-key="empNo"
		      	:single-select="false"
		      	:need-selection="true"
		      	@on-current-page-change="onChangeCurrentPageChange"
		      	:table-loading="tableLoading"
		    		:selectedTableData="selectedLeader"
		    		@onSelectionChange="selectLeader"
		      >
						<template #postName="{ rowData }">
							<div>{{ rowData.postName || '-' }}</div>
						</template>
		      </PitayaTable>
        </section>
        <div class="btn-list">
		    	<ButtonList :button="chooseLeadersBtn" @onBtnClick="chooseLeaders" />
		    </div>
	  	</div>
	  </div>
	</Drawer>
</template>

<script lang="ts" setup>
import { usePagination } from "@/app/platform/hooks/usePagination"
import { expertManagementApi } from "@/app/platform/api/configuration/expertManagement"
import { processApi } from "@/app/platform/api/process/processCompilation"

const props = defineProps({
  isView: {
    type: Boolean,
    default: false
  },
	showAddBtn: {
    type: Boolean,
    default: false
  },
	mainId: {
		type: String,
		default: ''
	}
})

watch(
	() => props.showAddBtn,
	(val: any) => {
		setTimeout(() => {
			showAddBtn.value = val
		});
	},
	{
		deep: true,
	}
)

// 选择回显表格
const selectedTableColumns: TableColumnType[] = [
	{ label: "领导姓名", prop: "empName", needSlot: true },
	{ label: "领导职位", prop: "postName", needSlot: true },
	{ label: "所属部门", prop: "orgName",needSlot: true },
	{ label: "联系电话", prop: "fcell", needSlot: true },
	{
		label: "操作",
		prop: "operations",
		fixed: "right",
		needSlot: true,
		width: 150
	}
]
const selectedTableData = ref<any[]>([]) //表格数据
const selectedBottomBtnArray = ref([
	{
		name: "新增领导",
		icon: ["fas", "square-plus"]
	}
])

// 所有领导表格
const showChooseLeadersDrawer = ref<boolean>(false)
const tableColumns: TableColumnType[] = [
	{ label: "姓名", prop: "empName" },
	{ label: "职位", prop: "postName", needSlot: true },
	{ label: "所属部门", prop: "orgName", width: 220 },
	{ label: "联系电话", prop: "fcell" }
] //表格列
const chooseLeadersTitle = {
	name: ["选择领导"],
	icon: ["fas", "square-share-nodes"]
}

const tableData = ref<any[]>([]) //表格数据
const total = ref<number>(0)
// 表格配置项
const tableLoading = ref<boolean>(false)
const selectTableLoading = ref<boolean>(false)
const usePaginationStore = usePagination()
const usePaginationStoreForData = usePaginationStore.paginationData
const pageSize = ref<number>(usePaginationStoreForData.pageSize)
const currentPage = ref<number>(usePaginationStoreForData.currentPage)
const selectedLeaderTableRef = ref<any>(null)
const leaderTableRef = ref<any>(null)
const selectIds = ref<string[]>([])
const selectedLeader = ref<any[]>([]) //已选中表格数据
const showAddBtn = ref<boolean>(true)

const chooseLeadersBtn = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "确定",
		icon: ["fas", "floppy-disk"]
	}
]

// 树状结构
const treeTitle = {
	name: ["组织架构"],
	icon: ["fas", "square-share-nodes"]
} // 树状图标题
const organizationTreeData = ref<any[]>([]) // 树状图数据
const organizationTreeProps = {
	children: "children",
	label: "orgName"
} // 树状图参数配置
const organizationTreeLoading = ref(false) // 控制树的loading状态
const orgCode = ref<any>('') //选择的部门

onMounted(() => {
	getTree()
})

/**
 * @description 新增领导
 */
const clickBottomBtn = () => {
	getLeaderList()
  showChooseLeadersDrawer.value = true
}

/**
 * @description 获取已选择的专家列表
 */
const getSelectList = () => {
	selectTableLoading.value = true
	processApi.
		getSelectExpertManagementListApi(props.mainId, 3)
		.then((res: any) => {
			selectedTableData.value = res
			// selectedLeader.value = res
			// selectIds.value = res.map((item: any) => item.id)
		})
		.catch((err) => {
			throw new Error("getSelectExpertManagementListApi():::" + err)
		})
		.finally(() => {
			selectTableLoading.value = false
		})
}

/**
 * @description 删除已选择数据
 * @param row 选择的行
 */
const deleteLine = (row: any) => {
	CustomMessageBox({ message: "确定要移除这行数据吗？" }, (res: boolean) => {
		if (res) {
			processApi.
				deleteExpertApi(row.id)
				.then((res: any) => {
					ElMessage.success('删除成功')
				})
				.catch((err) => {
					throw new Error("deleteLeaderApi():::" + err)
				})
				.finally(() => {
					getSelectList()
				})
		}
	})
}

/**
 * @description 获取树的数据
 */
const getTree = () => {
	organizationTreeLoading.value = true
	expertManagementApi
		.getOrganListApi({})
		.then((res: any) => {
			organizationTreeData.value = res.filter((item: any) => item.orgName != '供电分公司')
		})
		.catch((err) => {
			throw new Error("getOrganListApi():::" + err)
		})
		.finally(() => {
			organizationTreeLoading.value = false
		})
}

/**
 * @description 获取领导列表
 */
const getLeaderList = () => {
	tableLoading.value = true
	expertManagementApi
		.getExpertListApi({ currentPage: currentPage.value - 1, pageSize: pageSize.value, orgCode: orgCode.value || undefined })
		.then((res: any) => {
			tableData.value = res.rows
			total.value = res.total
		})
		.catch((err) => {
			throw new Error("getExpertListApi():::" + err)
		})
		.finally(() => {
			tableLoading.value = false
		})
}

/**
 * @description 切换分页
 * @param pageData 
 */
const onChangeCurrentPageChange = (pageData: any) => {
	pageSize.value = pageData.pageSize
	currentPage.value = pageData.currentPage
	getLeaderList()
}

/**
 * @description 点击树状节点
 * @param selectData 选中的节点
 * @param nodeData 节点信息
 */
const clickTreeNode = (selectData: any, nodeData: any) => {
  orgCode.value = selectData.orgCode
	getLeaderList()
}

/**
 * @description 选择领导
 */
const selectLeader = (rowList: any[]) => {
	selectedLeader.value = rowList
}

/**
 * @description 确定选择领导
 */
const chooseLeaders = (btnName: string | undefined) => {
	if (btnName === "确定") {
    if(selectedLeader.value && selectedLeader.value.length === 0){
      ElMessage.warning('请选择关联领导')
      return false
    }
		const data = selectedLeader.value.map(item => ({ 
			empNo: item.empNo, 
			mainId: props.mainId, 
			roleType: 3,
			postId: item.postId || item.vsystemBaseUser.postId,
			// roleName: item.fempTypeName || item.vsystemBaseUser.fempTypeName,
			empName: item.empName || item.vsystemBaseUser.empName
		}))
		processApi.
			addExpertApi(data)
			.then((res: any) => {
				ElMessage.success('添加成功')
				leaderTableRef.value.clearSelectedTableData()
			})
			.catch((err) => {
				throw new Error("addLeaderApi():::" + err)
			})
			.finally(() => {
				getSelectList()
			})
	}
	showChooseLeadersDrawer.value = false
}

defineExpose({
  selectedLeader: selectedTableData,
	getSelectList: getSelectList
})
</script>

<style lang="scss" scoped>

.ChooseLeader_container {
	display: flex;
	align-items: center;
	height: 100%;
	.ChooseLeader-left {
		width: 310px;
		height: 100%;
		position: relative;
		padding-right: 10px;
		box-sizing: border-box;

		.app-el-scrollbar-wrapper{
			height: 98%;
		}
	}
	.ChooseLeader-left::after {
		content: "";
		position: absolute;
		right: 0;
		top: -10px;
		width: 1px;
		height: calc(100% + 20px);
		background-color: #ccc;
	}
	.ChooseLeader-right {
		width: calc(100% - 320px);
		height: 100%;
		position: relative;
		// margin-top: 15px;
		margin-left: 10px;
		.table_container{
      padding-top: 10px;
    }
		.btn-list {
    	width: 900px;
    	display: flex;
    	justify-content: flex-end;
    	position: fixed;
    	right: 10px;
    	bottom: 0px;
    	z-index: 99;
    	padding: 10px 10px 10px 0;
    	border-top: 1px solid #ccc;
    	box-sizing: border-box;
    	background-color: #fff;
    }
	}
}
</style>
