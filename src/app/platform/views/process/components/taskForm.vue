<template>
  <Drawer
		size="1130"
		v-model:drawer="showTaskFormDrawer"
		:destroyOnClose="true"
	>
    <div class="taskForm_container">
      <div class="taskForm-left">
	  		<Title :title="leftTitle" />
        <section class="form_container">
          <el-form
		      	class="form-container"
		      	ref="taskFormRef"
		      	label-position="top"
		      	label-width="100px"
		      	:model="taskForm"
		      	:rules="rules"
						v-loading="taskLoading"
		      >
            <el-form-item label="调度任务排序（T）" prop="number">
			    		<el-input
			    			v-model.trim="taskForm.number"
			    			:formatter="(value: string) => value.trim()"
			    			placeholder="请输入调度任务排序"
			    		/>
			    	</el-form-item>
            <el-form-item label="任务内容" prop="content">
			    		<el-input
                type="textarea"
                :rows="5"
                resize="none"
			    			v-model.trim="taskForm.content"
			    			:formatter="(value: string) => value.trim()"
			    			placeholder="请输入任务内容"
			    		/>
			    	</el-form-item>
          </el-form>
        </section>
        <div class="btn-list">
			  	<ButtonList :loading="btnLoading" :button="addTaskBtn" @onBtnClick="addTask" />
			  </div>
	  	</div>
	  	<div class="taskForm-right">
        <Title :title="rightTitle">
			  	<Tabs
			  		style="position: absolute; left: 140px"
			  		:tabs="['任务步骤', '任务要素']"
			  		@on-tab-change="changeTabs"
			  	/>
			  </Title>
			  <div v-show="activeTab === 0" class="company-drawer-table" v-loading="taskLoading">
			  	<associationTaskTable ref="associationTaskTableRef" :processId="processIdData" :mainId="mainIdData" :associationTaskTableArr="associationTaskTableArr" />
			  </div>
			  <div v-show="activeTab === 1" class="company-drawer-table" v-loading="taskLoading">
          <associationessentialFactorTable ref="associationessentialFactorTableRef" :processId="processIdData" :mainId="mainIdData" :associationessentialFactorTableArr="associationessentialFactorTableArr" />
			  </div>
	  	</div>
    </div>
  </Drawer>
</template>

<script lang="ts" setup>
import { FormInstance, FormRules } from "element-plus"
import associationTaskTable from "./associationTaskTable.vue"
import associationessentialFactorTable from "./associationessentialFactorTable.vue"
import { cloneDeep } from "lodash-es"
import { processApi } from "@/app/platform/api/process/processCompilation"

// 控制弹窗展示
const showTaskFormDrawer = ref<boolean>(false)
// 要被添加任务的id-后续改名字
const processIdData = ref<any>(0)
const mainIdData = ref<string>('')
// 标题
const leftTitle = {
	name: ["新建任务"],
	icon: ["fas", "square-share-nodes"]
}
// 表单相关
const taskFormRef = ref<FormInstance>()
const taskForm: anyKey = reactive({
	number: null,
  content: null
})
const rules = reactive<FormRules<typeof taskForm>>({
	number: [
	  { required: true, message: "请输入调度任务排序", trigger: "blur" }
	],
  content: [
	  { required: true, message: "请输入任务内容", trigger: "blur" }
	]
})

const isAdd = ref<boolean>(true)
const taskLoading = ref<boolean>(false)
const emit = defineEmits(['reGetProcessTaskList'])

// 底部操作按钮
const addTaskBtn = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "保存",
		icon: ["fas", "floppy-disk"]
	}
]

// tab栏
const rightTitle = {
	name: ["任务信息"],
	icon: ["fas", "square-share-nodes"]
}
//扩展栏标签页切换
const activeTab = ref<number>(0)
const associationTaskTableRef = ref<any>(null)
const associationessentialFactorTableRef = ref<any>(null)
const associationTaskTableArr = ref<any>([])
const associationessentialFactorTableArr = ref<any>([])
const btnLoading = ref<boolean>(false)

/**
 * @description 展示弹窗并存储id
 */
const showTaskForm = (processId: any, mainId: string) => {
  processIdData.value = processId
	mainIdData.value = mainId
	Object.assign(taskForm, {
		number: null,
	  content: null
	})
	delete taskForm.id
	isAdd.value = true
	activeTab.value = 0
  showTaskFormDrawer.value = true
}

/**
 * @description 展示弹窗并需要编辑
 */
const editTaskForm = (taskId: any) => {
	taskLoading.value = true
	processApi
	.getTaskDetailApi(taskId)
	.then((res: any) => {
		processIdData.value = res.processId
		mainIdData.value = res.mainId
		Object.assign(taskForm, {...res, taskId: taskId})
		activeTab.value = 0
		associationTaskTableArr.value = res.taskStep
		associationessentialFactorTableArr.value = res.taskPoint
	})
	.catch((err) => {
		throw new Error("getTaskDetailApi():::" + err)
	})
	.finally(() => {
		taskLoading.value = false
	})
	isAdd.value = false
	showTaskFormDrawer.value = true
}

/**
 * @description 添加/编辑任务
 */
const addTask = (btnName: string | undefined) => {
  if(btnName === '保存'){
    taskFormRef.value?.validate((valid) => {
      if(valid) {
				const associationTaskTable = associationTaskTableRef.value.taskTableData
				const associationessentialFactorTable = associationessentialFactorTableRef.value.essentialFactorTableData
				if(associationTaskTable && associationTaskTable.length == 0) {
					ElMessage.warning('请选择任务步骤')
					return false
				}
        const data: any = {
					...cloneDeep(taskForm),
					processId: processIdData.value,
					mainId: mainIdData.value,
					rpstssProcessTreeFlowSetTaskPointList: associationessentialFactorTable,
					rpstssProcessTreeFlowSetTaskStepList: associationTaskTable
				}
				if(!isAdd.value){
					updateTask(data)
					return false;
				}
				createTask(data)
      }
    })
  }else{
    taskFormRef.value?.clearValidate()
    taskFormRef.value?.resetFields()
    showTaskFormDrawer.value = false
  }
}

/**
 * @description 新增任务
 */
const createTask = (data: any) => {
	if(data.createdDate){
		delete data.createdDate
	}
	if(data.taskStep){
		delete data.taskStep
	}
	if(data.taskPoint){
		delete data.taskPoint
	}
	btnLoading.value = true
	processApi
	.createTaskApi(data)
	.then((res: any) => {
		ElMessage.success('保存成功')
		taskFormRef.value?.resetFields()
		emit('reGetProcessTaskList')
  	showTaskFormDrawer.value = false
	})
	.catch((err) => {
		throw new Error("createTaskApi():::" + err)
	})
	.finally(() => {
		btnLoading.value = false
	})
}


/**
 * @description 更新任务
 */
const updateTask = (data: any) => {
	data.id = data.taskId
	delete data.taskStep
	delete data.taskPoint
	if(data.createdDate){
		delete data.createdDate
	}
	btnLoading.value = true
	processApi
	.updateTaskApi(data)
	.then((res: any) => {
		ElMessage.success('更新成功')
		taskFormRef.value?.resetFields()
		emit('reGetProcessTaskList')
  	showTaskFormDrawer.value = false
	})
	.catch((err) => {
		throw new Error("updateTaskApi():::" + err)
	})
	.finally(() => {
		btnLoading.value = false
	})
}

/**
 * @description 切换tab页
 * @param index 选择的下标
 */
const changeTabs = (index: number) => {
	activeTab.value = index
}

defineExpose({
  showTaskForm: showTaskForm,
	editTaskForm: editTaskForm
})
</script>

<style lang="scss" scoped>
.taskForm_container {
	height: 100%;
	display: flex;
	align-items: center;
	.taskForm-left {
		width: 300px;
		height: 100%;
		position: relative;
		padding-left: 10px;
		padding-right: 20px;
		box-sizing: border-box;

    .form_container{
      padding-top: 10px;
    }

		.btn-list {
			width: 300px;
			display: flex;
			justify-content: flex-end;
			position: absolute;
			right: calc(100% - 300px);
			bottom: 0px;
			z-index: 99;
			padding: 10px 10px 0px 0;
			box-sizing: border-box;
			border-top: 1px solid #ccc;
		}

		.common-title-wrapper{
			padding-left: 0;
			padding-right: 0;

			::v-deep .common-title-right{
				.btn-item{
					margin-right: 0;

					svg,
					span{
						font-size: 12px;
					}
				}
			}
		}
	}
	.taskForm-left::after {
		content: "";
		position: absolute;
		right: 0;
		top: -10px;
		width: 1px;
		height: calc(100% + 20px);
		background-color: #ccc;
	}
	.taskForm-right {
		width: calc(100% - 300px);
		height: 100%;
		// margin-top: 15px;
		margin-left: 10px;
	}
	.info_table{
		width: 100%;
		margin-top: 10px;
		border: 1px solid #ccc;

		tr {
			box-sizing: border-box;
			td{
				font-size: 12px;
				height: 30px;
				color: #222;
				border-bottom: 1px solid #ccc;
				padding-left: 4px;
				box-sizing: border-box;
				text-align: center;
			}
			td:first-child{
				width: 25%;
				background: #f9f9f9;
				font-weight: bold;
				box-sizing: border-box;
				border-right: 1px solid #ccc;
				padding-left: 0;
			}
			&:last-child{
				td {
					border-bottom: 0;
				}
			}
		}
	}
}
</style>