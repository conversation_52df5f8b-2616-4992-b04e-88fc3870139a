<template>
  <div class="editProcess_container">
		<div class="editProcess-left">
			<Title :title="leftTitle" :button="leftBtn" @on-btn-click="editProcessBaseInfo" v-if="!props.isView" />
			<Title :title="leftTitle" v-if="props.isView" />
			<table class="info_table" cellspacing="0" v-loading="baseInfoLoading">
				<tr>
					<td>流程编号</td>
					<td>{{ processData.mainCode || '-' }}</td>
				</tr>
				<tr>
					<td>处置级别</td>
					<td>{{ processData.disposalLevel }}</td>
				</tr>
				<tr>
					<td>所属专业</td>
					<td>{{ processData.majorName || '-' }}</td>
				</tr>
				<tr>
					<td>所属线路</td>
					<td>{{ processData.lineName || '-' }}</td>
				</tr>
        <tr>
					<td>关联位置</td>
					<td>{{ processData.locComposeName || '-' }}</td>
				</tr>
        <tr>
					<td>定额时间</td>
					<td>{{ formatSecond(parseInt(processData.limitedTime)) || '-' }}</td>
				</tr>
        <tr>
					<td>流程标题</td>
					<td>{{ processData.mainTitle || '-' }}</td>
				</tr>
        <tr>
					<td>创建人</td>
					<td>{{ processData.createdByName || '-' }}</td>
				</tr>
        <tr>
					<td>备注</td>
					<td>{{ processData.remark || '-' }}</td>
				</tr>
        <tr>
					<td>更新时间</td>
					<td>{{ processData.createdDate || '-' }}</td>
				</tr>
			</table>
			<section class="add_version_box" v-if="!hideAddVersion">
				<el-button v-btn link @click="addVersion">
					<font-awesome-icon :icon="['fas', 'square-plus']" style="color: var(--pitaya-btn-background)" />
					<span class="table-inner-btn">新建版本</span>
				</el-button>
			</section>
			<section class="version_list" v-loading="stepLoading">
				<div
					v-for="(item, index) in versionList"
					:key="index"
					:class="`version_line ${selectVersionId == item.id ? 'active' : ''}`"
					@click="changeVersion(item, versionList)"
				>
					<article class="left_info">
						<p class="version">版本号：{{ item.version }}</p>
						<p class="time">发布时间：{{ item.createdDate }}</p>
					</article>
					<el-tag :type="tagTypeArr[parseInt(item.status)]">{{ stateArr[parseInt(item.status)] }}</el-tag>
				</div>
			</section>
			<div class="btn-list" v-if="!props.isView && publishTaskBtn.length > 0">
				<ButtonList :button="publishTaskBtn" @onBtnClick="publishTask" />
			</div>
		</div>
		<div class="editProcess-right">
			<Title :title="rightTitle">
				<Tabs
					style="position: absolute; left: 140px"
					:tabs="['调度步骤', '关联图纸', '关联专家', '关联领导']"
					@on-tab-change="changeTabs"
				/>
			</Title>
			<div v-show="activeTab === 0" class="company-drawer-table" v-loading="stepLoading">
				<schedulingSteps
					ref="schedulingStepsRef"
					:isView="props.isView"
					:mainId="mainId"
					:stepsArr="stepsArr"
					:status="status"
					@re-get-process-task-list="reGetProcess"
				/>
			</div>
			<div v-show="activeTab === 1" class="company-drawer-table">
				<associationDrawingsTable
					ref="associationDrawingsTableRef"
					:isView="props.isView"
					:mainId="mainId"
					:showAddBtn="showAddBtn"
					:processData="processData"
					@re-get-process="reGetProcess"
				/>
			</div>
			<div v-show="activeTab === 2" class="company-drawer-table">
				<associationExpertManagementTable
					ref="associationExpertManagementTableRef"
					:isView="props.isView"
					:mainId="mainId"
					:showAddBtn="showAddBtn"
					:processData="processData"
					@re-get-process="reGetProcess"
				/>
			</div>
			<div v-show="activeTab === 3" class="company-drawer-table">
				<associationLeaderTable
					ref="associationLeaderTableRef"
					:isView="props.isView"
					:mainId="mainId"
					:showAddBtn="showAddBtn"
					@re-get-process="reGetProcess"
				/>
			</div>
		</div>
	
		<!-- 标准流程配置 -->
		<baseProcessFormDrawer ref="baseProcessFormDrawerRef" :processData="processData" @submit-success="reGetProcessDetail" />
	</div>
</template>

<script lang="ts" setup>
import schedulingSteps from './schedulingSteps.vue'
import associationDrawingsTable from './associationDrawingsTable.vue'
import associationExpertManagementTable from './associationExpertManagementTable.vue'
import associationLeaderTable from './associationLeaderTable.vue'
import { processApi } from "@/app/platform/api/process/processCompilation"
import baseProcessFormDrawer from './baseProcessFormDrawer.vue'
import { cloneDeep } from 'lodash-es'

const emit = defineEmits(['hideEditDrawer', 'reGetList'])

const props = defineProps({
	processId: {
		type: String,
		default: ''
	},
	isView: {
		type: Boolean,
		default: false
	}
})

watch(
	() => props.processId,
	(val: any) => {
		setTimeout(() => {
			mainId.value = val
		});
	},
	{ deep: true,
		immediate: true
	}
)

watch(
	() => props.isView,
	(val: any) => {
		setTimeout(() => {
			hideAddVersion.value = val
		});
	},
	{ deep: true,
		immediate: true
	}
)

// 标题
const leftTitle = {
	name: ["标准处置流程信息"],
	icon: ["fas", "square-share-nodes"]
}

const leftBtn = [
	{
		name: "修改基本信息",
		roles: "process:processCompilation:btn:edit",
		icon: ["fas", "pen-to-square"]
	}
]

// 版本列表- 状态0草稿箱、1已发布、2已废弃
let versionList = reactive<any>([])
// 标签类别数组
const tagTypeArr = ref<any>(['danger', 'success', 'info'])
const stateArr = ref<any>(['草稿箱', '已发布', '已废弃'])

// 底部操作按钮
const publishTaskBtn = ref<any>([
	{
		name: "移除",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "发布",
		icon: ["fas", "floppy-disk"]
	}
])
// 选择的版本
const selectVersionId = ref<number>(1)

// tab栏
const rightTitle = {
	name: ["调度指挥"],
	icon: ["fas", "square-share-nodes"]
}
//扩展栏标签页切换
const activeTab = ref<number>(0)
const schedulingStepsRef = ref<any>(null)
const stepsArr = ref<any>([])
const status = ref<any>('')
const associationDrawingsTableRef = ref<any>(null)
const associationExpertManagementTableRef = ref<any>(null)
const associationLeaderTableRef =  ref<any>(null)

const processData = reactive<anyKey>({})
const baseProcessFormDrawerRef = ref<any>(null)
const hideAddVersion = ref<boolean>(false)
const mainId = ref<any>('')
const baseInfoLoading = ref<boolean>(false)
const stepLoading = ref<boolean>(false)
const showAddBtn = ref<boolean>(true)
onMounted(() => {
	setTimeout(() => {
		getProcessDetail()
		getProcessTaskDetail()	
	});
})

/**
 * @description 格式化秒数为时分秒
 * @param seconds 
 */
const formatSecond = (seconds: number) => {
  let a: number | string = ~~(seconds / 3600)
  let b: number | string = ~~(seconds / 60) - a * 60
  let c: number | string = seconds % 60 || 0
 
  a = String(a).padStart(2, "0")
  b = String(b).padStart(2, "0")
  c = String(c).padStart(2, "0")
 
  return `${a}:${b}:${c}`
}

/**
 * @description 切换tab页
 * @param index 选择的下标
 */
const changeTabs = (index: number) => {
	if(index == 1){
		associationDrawingsTableRef.value.getSelectList()
	}

	if(index == 2){
		associationExpertManagementTableRef.value.getSelectList()
	}

	if(index == 3){
		associationLeaderTableRef.value.getSelectList()
	}
	activeTab.value = index
}

/**
 * @description 获取标准流程详细信息
 */
const getProcessDetail = () => {
	baseInfoLoading.value = true
	processApi.getProcessDetailApi(mainId.value)
		.then((res: any) => {
			Object.assign(processData, res)
		})
		.catch((err: string) => {
			throw new Error("getProcessDetailApi():::" + err)
		})
		.finally(() => {
			baseInfoLoading.value = false
		})
}

/**
 * @description 获取流程任务列表信息
 */
const getProcessTaskDetail = () => {
	stepLoading.value = true
	processApi.getProcessTaskDetailApi(mainId.value)
		.then((res: any) => {
			versionList = res.rpstssProcessTreeFlowSetMainVos
			if(res.rpstssProcessTreeFlowSetMainVos && res.rpstssProcessTreeFlowSetMainVos.length > 0) {
				selectVersionId.value = res.rpstssProcessTreeFlowSetMainVos[0].id || ''
				stepsArr.value = res.rpstssProcessTreeFlowSetMainVos[0].processTreeFlow || []
				status.value = res.rpstssProcessTreeFlowSetMainVos[0].status
				// 选中第一个版本并判断是否隐藏移除按钮
				changeVersion(res.rpstssProcessTreeFlowSetMainVos[0], res.rpstssProcessTreeFlowSetMainVos)
				// 有草稿箱状态隐藏新增按钮
				if(res.rpstssProcessTreeFlowSetMainVos.filter((item: any) => item.statusName == '草稿箱').length > 0){
					hideAddVersion.value = true
				}
			}
		})
		.catch((err: string) => {
			throw new Error("getProcessTaskDetailApi():::" + err)
		})
		.finally(() => {
			stepLoading.value = false
		})
}

/**
 * @description 编辑后重新获取标准流程信息
 */
const reGetProcessDetail = () => {
	setTimeout(() => {
		emit('reGetList')	
	});
	getProcessDetail()
}

/**
 * @description 修改基本信息
 */
const editProcessBaseInfo = () => {
	baseProcessFormDrawerRef.value.showAddProcessDrawer = true
}

/**
 * @description 新建版本
 */
const addVersion = () => {
	CustomMessageBox({ message: "确定要新增版本吗？" }, (res: boolean) => {
	if (res) {
		const versionData = cloneDeep(processData)
		delete versionData.id
		delete versionData.createdDate
		delete versionData.id
		stepLoading.value = true
		processApi
		  .addFlowApi({ ...versionData, version: `V${ parseInt(versionList.length) + 1 }`, status: 0 })
		  .then((res: any) => {
				ElMessage.success('新增成功')
		  	getProcessTaskDetail()
				emit('reGetList')
		  })
		  .catch((err) => {
		  	throw new Error("addFlowApi():::" + err)
		  })
			.finally(() => {
				// stepLoading.value = false
			})
		}
	})
}

/**
 * @description 切换版本
 */
const changeVersion = (data: any, versionList: any) => {
	selectVersionId.value = data.id
	mainId.value = data.id
	stepsArr.value = data.processTreeFlow
	status.value = data.status
	showAddBtn.value = data.status == '0'
	// 草稿箱、只有一条
	if(data.status === '0' && versionList && versionList.length === 1){
		publishTaskBtn.value = [{ name: "发布", icon: ["fas", "floppy-disk"]}]
	}
	// 草稿箱、多条
	if(data.status === '0' && versionList && versionList.length > 1){
		publishTaskBtn.value = data.version != 'v1' || data.version != 'V1' ? [{ name: "移除", icon: ["fas", "circle-minus"]}, { name: "发布", icon: ["fas", "floppy-disk"]}] : [{ name: "发布", icon: ["fas", "floppy-disk"]}]
	}
	// 已废弃、已废弃不可发布 不可移除
	if(data.status === '1' || data.status === '2'){
		publishTaskBtn.value = []
	}
}

/**
 * @description 重新获取流程详细信息
 */
const reGetProcess = () => {
	getProcessTaskDetail()
}

/**
 * @description 任务发布
 */
const publishTask = (btnName: string | undefined) => {
	
	if(btnName === '发布'){
		CustomMessageBox({ message: "确定要发布吗？" }, (res: boolean) => {
		if (res) {
			stepLoading.value = true
			processApi
				.updateFlowStatusApi({ id: mainId.value, status: 1, bmpStatus: 2 })
				.then((res: any) => {
					ElMessage.success('发布成功')
					emit('reGetList')
					emit('hideEditDrawer')
				})
				.catch((err) => {
					throw new Error("updateFlowStatusApi():::" + err)
				})
				.finally(() => {
					// stepLoading.value = false
				})
			}
		})
	}else{
		CustomMessageBox({ message: "确定要移除吗？" }, (res: boolean) => {
		if (res) {
			stepLoading.value = true
			processApi
				.deleteFlowApi(selectVersionId.value)
				.then((res: any) => {
					ElMessage.success('移除成功')
					const newVersionList = versionList.filter((item: any) => item.id != selectVersionId.value)
					// 移除草稿箱后获取最新版本列表 并取最顶的一条获取详情，出现新增版本按钮
					mainId.value = newVersionList[0].id
					getProcessTaskDetail()
					changeVersion(newVersionList[0], newVersionList)
					hideAddVersion.value = false
					emit('reGetList')
				})
				.catch((err) => {
					throw new Error("deleteFlowApi():::" + err)
				})
				.finally(() => {
					// stepLoading.value = false
				})
			}
		})
	}
}
</script>

<style lang="scss" scoped>
.editProcess_container {
	height: 100%;
	display: flex;
	align-items: center;
	.editProcess-left {
		width: 360px;
		height: 100%;
		position: relative;
		padding-left: 10px;
		padding-right: 20px;
		box-sizing: border-box;

		.btn-list {
			width: 360px;
			display: flex;
			justify-content: flex-end;
			position: absolute;
			right: calc(100% - 360px);
			bottom: 0px;
			z-index: 99;
			padding: 10px 10px 0px 0;
			box-sizing: border-box;
			border-top: 1px solid #ccc;
		}

		.common-title-wrapper{
			padding-left: 0;
			padding-right: 0;

			::v-deep .common-title-right{
				.btn-item{
					margin-right: 0;

					svg,
					span{
						font-size: 12px;
					}
				}
			}
		}
	}
	.editProcess-left::after {
		content: "";
		position: absolute;
		right: 0;
		top: -10px;
		width: 1px;
		height: calc(100% + 20px);
		background-color: #ccc;
	}
	.editProcess-right {
		width: calc(100% - 360px);
		height: 100%;
		// margin-top: 15px;
		margin-left: 10px;
	}
	.info_table{
		width: 100%;
		margin-top: 10px;
		border: 1px solid #ccc;

		tr {
			box-sizing: border-box;
			td{
				font-size: 12px;
				height: 30px;
				color: #222;
				border-bottom: 1px solid #ccc;
				padding-left: 4px;
				box-sizing: border-box;
				text-align: center;
			}
			td:first-child{
				width: 25%;
				background: #f9f9f9;
				font-weight: bold;
				box-sizing: border-box;
				border-right: 1px solid #ccc;
				padding-left: 0;
			}
			&:last-child{
				td {
					border-bottom: 0;
				}
			}
		}
	}
	.version_list{
		width: 100%;
		height: calc(100% - 480px);
		margin-top: 10px;
		overflow: auto;

		.version_line{
			width: 100%;
			height: 70px;
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 12px 10px;
			box-sizing: border-box;
			border-bottom: 1px dashed #ccc;
			cursor: pointer;

			&.active{
				background-color: rgb(229, 245, 255);
			}

			.left_info{
				width: fit-content;
				height: 100%;
				display: flex;
				flex-direction: column;
				justify-content: space-between;

				.version{
					font-size: 14px;
					color: #222;
				}

				.time{
					font-size: 12px;
					color: #888;
				}
			}
		}
	}
	.add_version_box{
		width: 100%;
		height: 50px;
		border: 1px dashed #ccc;
		box-sizing: border-box;
		display: flex;
		justify-content: center;
		align-items: center;
		margin-top: 20px;
		cursor: pointer;

		svg,
		span{
			font-size: 14px;
		}
	}
}
</style>