<template>
	<div class="task-table">
		<PitayaTable
			:need-index="false"
			:customizeHeightNumber="0"
			:table-data="taskTableData"
			:columns="taskTableColumn"
			:need-pagination="false"
			:table-loading="tableLoading"
		>
			<template #operations="{ rowData, $index }">
        <el-button v-btn link @click="editRow(rowData, $index)">
					<font-awesome-icon :icon="['fas', 'pen-to-square']" style="color: var(--pitaya-btn-background)" />
					<span class="table-inner-btn">编辑</span>
				</el-button>
				<el-button v-btn link @click="deleteRow(rowData, $index)">
					<font-awesome-icon :icon="['fas', 'trash-can']" style="color: var(--pitaya-btn-background)" />
					<span class="table-inner-btn">移除</span>
				</el-button>
			</template>
			<template #footerOperateLeft>
				<el-button v-btn style="border-radius: 0" @click="showChooseTask">
					<font-awesome-icon
						:icon="['fas', 'square-plus']"
						style="color: #fff"
					/>
					<span class="choose-task-btn">新增步骤</span>
				</el-button>
			</template>
		</PitayaTable>
		<Drawer
			v-show="showChooseTaskDrawer"
			class="inner-drawer drawer-hidden-box"
			:size="300"
			v-model:drawer="showChooseTaskDrawer"
		>
			<Title :title="chooseTaskTitle" />
			<section class="form_container">
        <el-form
		    	class="form-container"
		    	ref="stepFormRef"
		    	label-position="top"
		    	label-width="100px"
		    	:model="taskForm"
		    	:rules="rules"
		    >
          <el-form-item label="步骤排序（S）" prop="number">
			  		<el-input
			  			v-model.trim="taskForm.number"
			  			:formatter="(value: string) => value.trim()"
			  			placeholder="请输入步骤排序"
			  		/>
			  	</el-form-item>
          <el-form-item label="关联角色" prop="baseRoleId">
			    	<el-select
			    		style="width: 100%"
			    		v-model="taskForm.baseRoleId"
			    		placeholder="请选择关联角色"
							@change="changeRole"
							v-loading="roleLoading"
			    	>
			    		<el-option
			    			v-for="item in roleList"
			    			:key="item.value"
			    			:label="item.label"
			    			:value="item.value"
			    		/>
			    	</el-select>
			    </el-form-item>
          <el-form-item label="步骤内容" prop="content">
			  		<el-input
              type="textarea"
              :rows="5"
              resize="none"
			  			v-model.trim="taskForm.content"
			  			:formatter="(value: string) => value.trim()"
			  			placeholder="请输入步骤内容"
			  		/>
			  	</el-form-item>
        </el-form>
      </section>
			<div class="btn-list">
				<ButtonList :button="chooseTaskBtn" @onBtnClick="chooseTask" />
			</div>
		</Drawer>
	</div>
</template>

<script lang="ts" setup>
import { FormInstance, FormRules } from "element-plus"
import { CustomMessageBox } from "@/app/platform/utils/message"
import { processApi } from "@/app/platform/api/process/processCompilation"
import { cloneDeep } from "lodash-es"

const props = defineProps({
	associationTaskTableArr: {
		type: Array,
		default: []
	}
})

watch(
	() => props.associationTaskTableArr,
	(val: any) => {
		setTimeout(() => {
			taskTableData.value = val.map((item: any) => 
			({ ...item, baseRoleId: item.roleId, content: item.stepContent, roleName: item.roleName }))
		});
	},
	{ deep: true }
)

const taskTableColumn: TableColumnType[] = [
	{ label: "序号", prop: "number" },
	{ label: "角色名称", prop: "roleName" },
	{ label: "步骤内容", prop: "content" },
	{
		label: "操作",
		prop: "operations",
		fixed: "right",
		width: 200,
		needSlot: true
	}
]
const chooseTaskTitle = {
	name: ["任务步骤"],
	icon: ["fas", "square-share-nodes"]
}
const chooseTaskBtn = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "保存",
		icon: ["fas", "floppy-disk"]
	}
]
const taskTableData = ref<any[]>([])
const showChooseTaskDrawer = ref<boolean>(false)
const tableLoading = ref(false)

// 表单相关
const stepFormRef = ref<FormInstance>()
const roleList = ref([{label: '', value: ''}])
const taskForm: anyKey = reactive({
	number: null,
  baseRoleId: null,
	roleName: null,
  content: null
})
const rules = reactive<FormRules<typeof taskForm>>({
	number: [
	  { required: true, message: "请输入步骤排序", trigger: "blur" }
	],
  baseRoleId: [
	  { required: true, message: "请选择关联角色", trigger: "blur" }
	],
  content: [
	  { required: true, message: "请输入步骤内容", trigger: "blur" }
	]
})
const roleLoading = ref<boolean>(false)

onMounted(() => {
  getRolesList()
})

/**
 * @description 获取关联角色列表
 */
const getRolesList = () => {
	roleLoading.value = true
  processApi
	.getRoleListApi({ currentPage: 1, pageSize: 99 })
	.then((res: any) => {
		roleList.value = res.rows.map((item: { roleName: string; id: string }) => ({ label: item.roleName, value: item.id }))
	})
	.catch((err) => {
		throw new Error("getProfessionTreeApi():::" + err)
	})
	.finally(() => {
		roleLoading.value = false
	})
}

/**
 * @description 切换角色
 */
const changeRole = () => {
	taskForm.roleName = roleList.value.find((item: any) => item.value == taskForm.baseRoleId)?.label
}

/**
 * @description 编辑选择的任务
 */
const editRow = (row: any, index: any) => {
	taskForm.number = row.number
	taskForm.baseRoleId = row.roleId || row.baseRoleId
	taskForm.roleName = row.roleName
	taskForm.content = row.stepContent || row.content
	taskForm.index = index
  showChooseTaskDrawer.value = true
}

/**
 * @description 删除选择的任务
 * @param row 选择的行
 */
const deleteRow = (row: any, index: any) => {
	CustomMessageBox({ message: "确定要移除吗?" }, (res: boolean) => {
		if (res) {
			taskTableData.value.splice(index, 1)
		}
	})
}

/**
 * @description 展示新建步骤弹窗
 */
const showChooseTask = () => {
	stepFormRef.value?.resetFields()
	taskForm.number = taskForm.baseRoleId = taskForm.content = null
	delete taskForm.index
	showChooseTaskDrawer.value = true
}

/**
 * @description 新建步骤
 */
const chooseTask = (btnName: string | undefined) => {
	if (btnName === "保存") {
		stepFormRef.value?.validate((valid) => {
      if(valid) {
        const taskData = cloneDeep(taskForm)
        if(taskData.index || taskData.index == 0){
          taskTableData.value[taskData.index] = taskData
        }else{
          taskTableData.value.push(taskData)
        }
        taskTableData.value = taskTableData.value.sort((a, b) => a.number - b.number)
        stepFormRef.value?.resetFields()
        showChooseTaskDrawer.value = false
      }
    })
	}else{
    stepFormRef.value?.clearValidate()
    stepFormRef.value?.resetFields()
    showChooseTaskDrawer.value = false
  }
}

defineExpose({
	taskTableData: taskTableData
})
defineOptions({
	name: "associationTaskTable"
})
</script>

<style lang="scss" scoped>
:deep(.el-tree-node__content) {
	height: 32px;
}
:deep(.el-tree-node__label) {
	font-size: var(--pitaya-fs-12);
}
.choose-task-btn {
	margin-left: 5px;
	color: #fff;
	font-weight: 500;
}
.inner-drawer {
	position: relative;
	height: 100%;
  .form_container{
    padding: 10px 10px 0;
    box-sizing: border-box;
  }
	.btn-list {
		display: flex;
		justify-content: flex-end;
		position: fixed;
		right: 10px;
		bottom: 0px;
		z-index: 99;
		padding: 10px 10px 10px 0;
		box-sizing: border-box;
		width: 290px;
		border-top: 1px solid #ccc;
		background-color: #fff;
	}
}
</style>