<template>
  <PitayaTable
  	ref="selectedExpertManagementTableRef"
		:customizeHeightNumber="0"
  	:columns="selectedTableColumns"
  	:tableData="selectedTableData"
  	:single-select="false"
  	:need-selection="false"
		:table-loading="selectTableLoading"
  >
    <template #empName="{ rowData }">
			<div>{{ rowData.expertInfoVo ? rowData.expertInfoVo.vsystemBaseUser.empName  : '-'}}</div>
		</template>
		<template #postName="{ rowData }">
			<div>{{ rowData.postName || '-' }}</div>
		</template>
		<template #orgName="{ rowData }">
			<div>{{ rowData.expertInfoVo ? rowData.expertInfoVo.vsystemOrganization?.orgName : '-' }}</div>
		</template>
		<template #fcell="{ rowData }">
			<div>{{ rowData.expertInfoVo ? rowData.expertInfoVo.vsystemBaseUser.fcell : '-' }}</div>
		</template>
		<template #majorName="{ rowData }">
			<div>{{ rowData.expertInfoVo ? rowData.expertInfoVo.majorName  : '-'}}</div>
		</template>
  	<template #operations="{ rowData }">
  		<el-button v-btn link @click="deleteLine(rowData)" v-if="(!props.isView && showAddBtn) || showAddBtn">
  			<font-awesome-icon
  				:icon="['fas', 'trash-can']"
  				style="color: var(--pitaya-btn-background)"
  			/>
  			<span class="table-inner-btn">移除</span>
  		</el-button>
			<p v-else>不可操作</p>
  	</template>
  	<template #footerOperateLeft>
  		<ButtonList
			 	v-if="(!props.isView && showAddBtn) || showAddBtn"
  			:is-not-radius="true"
  			:button="selectedBottomBtnArray"
  			@on-btn-click="clickBottomBtn"
  		/>
  	</template>
  </PitayaTable>

  <Drawer
		size="1230"
		v-model:drawer="showChooseExpertManagementsDrawer"
	>
		<Title :title="chooseExpertManagementsTitle" />
		<section class="table_container">
      <Query
		  	class="ml10"
				ref="queryRef"
		  	:queryArrList="searchArrList"
		  	@getQueryData="getSearchData"
		  />
      <PitayaTable
		  	ref="expertManagementTableRef"
		  	:columns="tableColumns"
				:customizeHeightNumber="0"
		  	:tableData="tableData"
		  	:total="total"
		  	:single-select="false"
		  	:need-selection="true"
		  	@on-current-page-change="onChangeCurrentPageChange"
		  	:table-loading="tableLoading"
				@onSelectionChange="selectExpertManagement"
		  >
        <template #empName="{ rowData }">
					<div>{{ rowData.vsystemBaseUser.empName }}</div>
				</template>
				<template #orgName="{ rowData }">
					<div>{{ rowData.vsystemOrganization?.orgName || '-' }}</div>
				</template>
        <template #lines="{ rowData }">
					<div>{{ rowData.lines && rowData.lines.length > 0 ? rowData.lines.map((item:any) => item.name).join(',') : '、' }}</div>
				</template>
				<template #fcell="{ rowData }">
					<div>{{ rowData.vsystemBaseUser.fcell }}</div>
				</template>
		  </PitayaTable>
    </section>
    <div class="btn-list">
			<ButtonList :button="chooseExpertManagementsBtn" @onBtnClick="chooseExpertManagements" />
		</div>
	</Drawer>
</template>

<script lang="ts" setup>
import { usePagination } from "@/app/platform/hooks/usePagination"
import { expertManagementApi } from "@/app/platform/api/configuration/expertManagement"
import { processApi } from "@/app/platform/api/process/processCompilation"
import { useMajor } from "@/app/platform/hooks/useMajor"

const props = defineProps({
  isView: {
    type: Boolean,
    default: false
  },
	showAddBtn: {
    type: Boolean,
    default: false
  },
	mainId: {
		type: String,
		default: ''
	},
	processData: {
		default: {}
	}
})

watch(
	() => props.showAddBtn,
	(val: any) => {
		setTimeout(() => {
			showAddBtn.value = val
		});
	},
	{
		deep: true,
	}
)

// 选择回显表格
const selectedTableColumns: TableColumnType[] = [
	{ label: "专家姓名", prop: "empName", needSlot: true },
	{ label: "专家职位", prop: "postName", needSlot: true },
	{ label: "所属部门", prop: "orgName",needSlot: true },
	{ label: "联系电话", prop: "fcell", needSlot: true },
	{ label: "关联专业", prop: "majorName", needSlot: true },
	{
		label: "操作",
		prop: "operations",
		fixed: "right",
		needSlot: true,
		width: 150
	}
]
const selectedTableData = ref<any[]>([]) //表格数据
const selectedBottomBtnArray = ref([
	{
		name: "新增专家",
		icon: ["fas", "square-plus"]
	}
])

// 所有专家表格
const showChooseExpertManagementsDrawer = ref<boolean>(false)
const tableColumns: TableColumnType[] = [
	{ label: "姓名", prop: "empName", needSlot: true },
	{ label: "所属部门", prop: "orgName",needSlot: true },
	{ label: "关联专业", prop: "majorName" },
	{ label: "关联线路", prop: "lines", needSlot: true },
	{ label: "联系电话", prop: "fcell", needSlot: true }
] //表格列
const chooseExpertManagementsTitle = {
	name: ["选择专家"],
	icon: ["fas", "square-share-nodes"]
}
// 检索项列表
const searchArrList = ref<any[]>([
	{
		name: "所属部门",
		key: "orgCode",
		placeholder: "请选择所属部门",
		type: "elTreeSelect",
		children: []
	},
	{
		name: "所属专业",
		key: "majorCode",
		placeholder: "请选择所属专业",
		enableFuzzy: false,
		type: "select",
		children: []
	},
  {
		name: "",
		key: "empName",
		placeholder: "请输入查询关键字",
		enableFuzzy: false,
		type: "input"
	},
])
const useMajorStore = useMajor(true)

const tableData = ref<any[]>([]) //表格数据
const total = ref<number>(0)
// 表格配置项
const selectTableLoading = ref<boolean>(false)
const tableLoading = ref<boolean>(false)
const usePaginationStore = usePagination()
const usePaginationStoreForData = usePaginationStore.paginationData
const pageSize = ref<number>(usePaginationStoreForData.pageSize)
const currentPage = ref<number>(usePaginationStoreForData.currentPage)
const selectedExpertManagementTableRef = ref<any>(null)
const expertManagementTableRef = ref<any>(null)
const searchData = ref<any>({}) // 检索项数据
const selectedExpertManagement = ref<any[]>([]) //已选中表格数据
const showAddBtn = ref<boolean>(true)
const queryRef = ref<any>(null)

const chooseExpertManagementsBtn = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "确定",
		icon: ["fas", "floppy-disk"]
	}
]

/**
 * @description 递归处理数据, 为 cascade 的每个 options 添加 label 和 value 属性
 */
const addValueAndLabelForTree = (arr: any[]): any => {
  arr = arr.map(node => {
    if (node.hasOwnProperty('children')) {
      node.children = addValueAndLabelForTree(node.children)
    }
    return {
      ...node,
      label: node['orgName'],
      value: node['orgCode'],
    }
  })
  return arr
}

/**
 * @description 获取已选择的专家列表
 */
const getSelectList = () => {
	selectTableLoading.value = true
	processApi.
		getSelectExpertManagementListApi(props.mainId, 2)
		.then((res: any) => {
			selectedTableData.value = res
			// selectedExpertManagement.value = res
			// selectIds.value = res.map((item: any) => item.id)
		})
		.catch((err) => {
			throw new Error("getSelectExpertManagementListApi():::" + err)
		})
		.finally(() => {
			selectTableLoading.value = false
		})
}

/**
 * @description 获取树的数据
 */
const getTree = () => {
	expertManagementApi
		.getOrganListApi({})
		.then((res: any) => {
			searchArrList.value[0].children = addValueAndLabelForTree(res.filter((item: any) => item.orgName != '供电分公司'))
		})
		.catch((err) => {
			throw new Error("getOrganListApi():::" + err)
		})
}

/**
 * @description 新增专家
 */
const clickBottomBtn = () => {
	const processData: any = props.processData
	getSearchData({ majorCode: processData.majorId })
	getTree()
	// searchArrList.value[0].children = useLineStore.linesArr
	searchArrList.value[1].children = useMajorStore.majorArr
  showChooseExpertManagementsDrawer.value = true
	nextTick(() => {
		queryRef.value.queryData['majorCode'] = Number(processData.majorId)
	})
}

/**
 * @description 删除已选择数据
 * @param row 选择的行
 */
const deleteLine = (row: any) => {
	CustomMessageBox({ message: "确定要移除这行数据吗？" }, (res: boolean) => {
		if (res) {
			processApi.
				deleteExpertApi(row.id)
				.then((res: any) => {
					ElMessage.success('删除成功')
				})
				.catch((err) => {
					throw new Error("deleteExpertApi():::" + err)
				})
				.finally(() => {
					getSelectList()
				})
		}
	})
}

/**
 * @description 获取专家列表
 */
const getExpertManagementList = () => {
	tableLoading.value = true
	expertManagementApi
		.getExpertInfoListApi({ currentPage: currentPage.value - 1, pageSize: pageSize.value, ...searchData.value, expertStatus: 'open' })
		.then((res: any) => {
			tableData.value = res.rows
			total.value = res.total
		})
		.catch((err) => {
			throw new Error("getExpertInfoListApi():::" + err)
		})
		.finally(() => {
			tableLoading.value = false
		})
}

/**
 * @description 切换分页
 * @param pageData 
 */
const onChangeCurrentPageChange = (pageData: any) => {
	pageSize.value = pageData.pageSize
	currentPage.value = pageData.currentPage
	getExpertManagementList()
}

/**
 * @description 选择搜索参数回显
 * @param queryParams 检索参数
 */
const getSearchData = (queryParams?: any) => {
	searchData.value.orgCode = queryParams.orgCode
		? queryParams.orgCode
		: undefined
	searchData.value.majorCode = queryParams.majorCode
			? queryParams.majorCode
			: undefined
	searchData.value.empName = queryParams.empName
		? queryParams.empName
		: undefined
	getExpertManagementList()
}

/**
 * @description 选择专家
 */
const selectExpertManagement = (rowList: any[]) => {
	selectedExpertManagement.value = rowList
}

/**
 * @description 确定选择专家
 */
const chooseExpertManagements = (btnName: string | undefined) => {
	if (btnName === "确定") {
    if(selectedExpertManagement.value && selectedExpertManagement.value.length === 0){
      ElMessage.warning('请选择关联专家')
      return false
    }
		const data = selectedExpertManagement.value.map(item => ({ 
			empNo: item.empNo, 
			mainId: props.mainId, 
			roleType: 2,
			postId: item.postId || item.vsystemBaseUser.postId,
			// roleName: item.fempTypeName || item.vsystemBaseUser.fempTypeName,
			empName: item.empName || item.vsystemBaseUser.empName
		}))
		processApi.
			addExpertApi(data)
			.then((res: any) => {
				ElMessage.success('添加成功')
				expertManagementTableRef.value.clearSelectedTableData()
			})
			.catch((err) => {
				throw new Error("addExpertApi():::" + err)
			})
			.finally(() => {
				getSelectList()
			})
	}
	showChooseExpertManagementsDrawer.value = false
}

defineExpose({
  selectedExpertManagement: selectedTableData,
	getSelectList: getSelectList
})
</script>

<style lang="scss" scoped>
.btn-list {
	width: 1220px;
	display: flex;
	justify-content: flex-end;
	position: fixed;
	right: 10px;
	bottom: 0px;
	z-index: 99;
	padding: 10px 10px 10px 0;
	border-top: 1px solid #ccc;
	box-sizing: border-box;
	background-color: #fff;
}

.table_container{
  padding-top: 10px;
}
</style>
