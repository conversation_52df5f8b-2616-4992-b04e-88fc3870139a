<template>
  <PitayaTable
  	ref="selectedDrawingTableRef"
		:customizeHeightNumber="0"
  	:columns="selectedTableColumns"
  	:tableData="selectedTableData"
  	:single-select="false"
  	:need-selection="false"
		:table-loading="selectTableLoading"
  >
  	<template #operations="{ rowData }">
  		<el-button v-btn link @click="deleteLine(rowData)" v-if="(!props.isView && showAddBtn) || showAddBtn">
  			<font-awesome-icon
  				:icon="['fas', 'trash-can']"
  				style="color: var(--pitaya-btn-background)"
  			/>
  			<span class="table-inner-btn">移除</span>
  		</el-button>
			<p v-else>不可操作</p>
  	</template>
  	<template #footerOperateLeft>
  		<ButtonList
			 	v-if="(!props.isView && showAddBtn) || showAddBtn"
  			:is-not-radius="true"
  			:button="selectedBottomBtnArray"
  			@on-btn-click="clickBottomBtn"
  		/>
  	</template>
  </PitayaTable>

  <Drawer
		size="1230"
		v-model:drawer="showChooseDrawingsDrawer"
	>
		<Title :title="chooseDrawingsTitle" />
		<section class="table_container">
      <Query
		  	class="ml10"
				ref="queryRef"
		  	:queryArrList="searchArrList"
		  	@getQueryData="getSearchData"
		  />
      <PitayaTable
		  	ref="drawingTableRef"
		  	:columns="tableColumns"
				:customizeHeightNumber="0"
		  	:tableData="tableData"
		  	:total="total"
				select-key="drawId"
		  	:single-select="false"
		  	:need-selection="true"
		  	@on-current-page-change="onChangeCurrentPageChange"
		  	:table-loading="tableLoading"
				:selectedTableData="selectedDrawing"
				@onSelectionChange="selectDrawing"
		  >
		  </PitayaTable>
    </section>
    <div class="btn-list">
			<ButtonList :button="chooseDrawingsBtn" @onBtnClick="chooseDrawings" />
		</div>
	</Drawer>
</template>

<script lang="ts" setup>
import { usePagination } from "@/app/platform/hooks/usePagination"
import { drawingsApi } from "@/app/platform/api/configuration/drawing"
import { processApi } from "@/app/platform/api/process/processCompilation"
import { remove } from "lodash-es"
import { useLine } from "@/app/platform/hooks/useLine"
import { useMajor } from "@/app/platform/hooks/useMajor"

const props = defineProps({
  isView: {
    type: Boolean,
    default: false
  },
	showAddBtn: {
    type: Boolean,
    default: false
  },
	mainId: {
		type: String,
		default: ''
	},
	processData: {
		default: {}
	}
})

watch(
	() => props.showAddBtn,
	(val: any) => {
		setTimeout(() => {
			showAddBtn.value = val
		});
	},
	{
		deep: true,
	}
)

// 选择回显表格
const selectedTableColumns: TableColumnType[] = [
	{ label: "资料编号", prop: "drawCode" },
	{ label: "资料标题", prop: "title"},
	{ label: "关联时间", prop: "createdDate" },
	{
		label: "操作",
		prop: "operations",
		fixed: "right",
		needSlot: true,
		width: 150
	}
]
const selectedTableData = ref<any[]>([]) //表格数据
const selectedBottomBtnArray = ref([
	{
		name: "新增图纸",
		icon: ["fas", "square-plus"]
	}
])

// 所有图纸资料表格
const showChooseDrawingsDrawer = ref<boolean>(false)
const tableColumns: TableColumnType[] = [
	{ label: "资料编号", prop: "drawCode" },
	{ label: "资料标题", prop: "title"},
	{ label: "关联专业", prop: "majorName" },
	{ label: "关联线路", prop: "lineName" }
] //表格列
const chooseDrawingsTitle = {
	name: ["选择图纸"],
	icon: ["fas", "square-share-nodes"]
}
// 检索项列表
const searchArrList = ref<any[]>([
	{
		name: "所属线路",
		key: "lineCode",
		placeholder: "请选择所属线路",
		enableFuzzy: false,
		type: "select",
		children: []
	},
	{
		name: "所属专业",
		key: "majorCode",
		placeholder: "请选择所属专业",
		enableFuzzy: false,
		type: "select",
		children: []
	},
  {
		name: "",
		key: "title",
		placeholder: "请输入查询关键字",
		enableFuzzy: false,
		type: "input"
	},
])
const useLineStore = useLine()
const useMajorStore = useMajor(true)

const tableData = ref<any[]>([]) //表格数据
const total = ref<number>(0)
// 表格配置项
const tableLoading = ref<boolean>(false)
const selectTableLoading = ref<boolean>(false)
const usePaginationStore = usePagination()
const usePaginationStoreForData = usePaginationStore.paginationData
const pageSize = ref<number>(usePaginationStoreForData.pageSize)
const currentPage = ref<number>(usePaginationStoreForData.currentPage)
const selectedDrawingTableRef = ref<any>(null)
const drawingTableRef = ref<any>(null)
const searchData = ref<any>({}) // 检索项数据
const selectIds = ref<string[]>([])
const selectedDrawing = ref<any[]>([]) //已选中表格数据
const showAddBtn = ref<boolean>(true)
const queryRef = ref<any>(null)

const chooseDrawingsBtn = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "确定",
		icon: ["fas", "floppy-disk"]
	}
]


/**
 * @description 获取已选择的图纸列表
 */
const getSelectList = () => {
	selectTableLoading.value = true
	processApi.
		getSelectDrawingsListApi(props.mainId)
		.then((res: any) => {
			selectedTableData.value = res
			selectedDrawing.value = res
			selectIds.value = res.map((item: any) => item.drawId)
		})
		.catch((err) => {
			throw new Error("getSelectDrawingsListApi():::" + err)
		})
		.finally(() => {
			selectTableLoading.value = false
		})
}

/**
 * @description 新增图纸
 */
const clickBottomBtn = () => {
	const processData: any = props.processData
	getSearchData({ lineCode: String(processData.lineId), majorCode: Number(processData.majorId) })
	searchArrList.value[0].children = useLineStore.linesArr
	searchArrList.value[1].children = useMajorStore.majorArr
  showChooseDrawingsDrawer.value = true
	nextTick(() => {
		queryRef.value.queryData['lineCode'] = processData.lineId
		queryRef.value.queryData['majorCode'] = Number(processData.majorId)
	})
}

/**
 * @description 删除已选择数据
 * @param row 选择的行
 */
const deleteLine = (row: any) => {
	CustomMessageBox({ message: "确定要移除这行数据吗？" }, (res: boolean) => {
		if (res) {
			processApi.
				deleteDrawApi(row.id)
				.then((res: any) => {
					ElMessage.success('删除成功')
					const newKeysArr = remove(selectIds.value, (id) => row.id !== id)
					const newSelectNodes = remove(
						selectedTableData.value,
						(node) => row.id !== node.id
					)
					selectIds.value = newKeysArr
					selectedTableData.value = newSelectNodes
				})
				.catch((err) => {
					throw new Error("deleteDrawApi():::" + err)
				})
				.finally(() => {
					getSelectList()
				})
		}
	})
}

/**
 * @description 获取图纸资料列表
 */
const getDrawingList = () => {
	tableLoading.value = true
	drawingsApi
		.getDrawListApi({ currentPage: currentPage.value - 1, pageSize: pageSize.value, ...searchData.value })
		.then((res: any) => {
			tableData.value = res.rows.map((item: {
				id: any, line: { name: any } 
			}) => ({ ...item, lineName: item.line ? item.line.name : '', drawId: item.id }))
			total.value = res.total
		})
		.catch((err) => {
			throw new Error("getDrawListApi():::" + err)
		})
		.finally(() => {
			tableLoading.value = false
		})
}

/**
 * @description 切换分页
 * @param pageData 
 */
const onChangeCurrentPageChange = (pageData: any) => {
	pageSize.value = pageData.pageSize
	currentPage.value = pageData.currentPage
	getDrawingList()
}

/**
 * @description 选择搜索参数回显
 * @param queryParams 检索参数
 */
const getSearchData = (queryParams?: any) => {
	searchData.value.lineCode = queryParams.lineCode
		? queryParams.lineCode
		: ""
	searchData.value.majorCode = queryParams.majorCode
			? queryParams.majorCode
			: ""
	searchData.value.title = queryParams.title
		? queryParams.title
		: ""
	getDrawingList()
}

/**
 * @description 选择图纸
 */
const selectDrawing = (rowList: any[]) => {
	selectedDrawing.value = rowList
}

/**
 * @description 确定选择图纸
 */
const chooseDrawings = (btnName: string | undefined) => {
	if (btnName === "确定") {
    if(selectedDrawing.value && selectedDrawing.value.length === 0){
      ElMessage.warning('请选择关联图纸')
      return false
    }
		const data = selectedDrawing.value.map(item => ({ drawId: item.drawId ? item.drawId : item.id, mainId: props.mainId }))
		processApi.
			addDrawApi(data)
			.then((res: any) => {
				ElMessage.success('添加成功')
				drawingTableRef.value.clearSelectedTableData()
			})
			.catch((err) => {
				throw new Error("addDrawApi():::" + err)
			})
			.finally(() => {
				getSelectList()
			})
	}
	showChooseDrawingsDrawer.value = false
}

defineExpose({
	selectedDrawing: selectedTableData,
	getSelectList: getSelectList
})
</script>

<style lang="scss" scoped>
.btn-list {
	width: 1220px;
	display: flex;
	justify-content: flex-end;
	position: fixed;
	right: 10px;
	bottom: 0px;
	z-index: 99;
	padding: 10px 10px 10px 0;
	border-top: 1px solid #ccc;
	box-sizing: border-box;
	background-color: #fff;
}

.table_container{
  padding-top: 10px;
}
</style>
