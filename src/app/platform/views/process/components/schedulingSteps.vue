<template>
  <div class="schedulingSteps_container">
    <el-row>
      <el-col :span="4">
        <section class="steps_list">
          <div
            v-for="(item, index) in stepsArr"
            :key="index"
            :class="`steps_box ${ item.taskTreeVos && item.taskTreeVos.length > 0 ? 'active' : '' }`">
            <header class="steps_title">{{ `P${index + 1} ${item.name}` }}</header>
            <div class="bottom_info_box">
              <article>
                <p class="number">{{ item.taskTreeVos ? item.taskTreeVos.length : 0 }}</p>
                <p class="describe">任务</p>
              </article>
              <article>
                <p class="number">{{ item.stepNumber ? item.stepNumber : 0 }}</p>
                <p class="describe">步骤</p>
              </article>
            </div>
          </div>
        </section>
      </el-col>
      <el-col :span="20">
        <section class="task_container">
          <div :class="`task_detail ${ item.taskTreeVos && item.taskTreeVos.length > 0 ? 'active' : '' }`" v-for="(item, index) in stepsArr" :key="index">
            <header>
              <p class="title">{{ `P${item.number} ${item.name}` }}</p>
              <el-button v-btn link @click="addTask(item.processId, props.mainId)" v-if="props.status == '0' && !props.isView">
						  	<font-awesome-icon :icon="['fas', 'square-plus']" style="color: #999;font-size: 16px;" />
						  </el-button>
            </header>
            <main class="task_info" v-for="(task, taskIndex) in item.taskTreeVos" :key="taskIndex">
              <section class="task_title">
                <section class="flex_child">
                  <p>{{ `T${task.number} ${task.taskContent}` }}</p>
                  <div class="essentialFactor_box" v-if="task.taskPoint && task.taskPoint.length > 0">任务要素{{ task.taskPoint.length }}项</div>
                </section>
                <section class="flex_child" v-if="props.status == '0' && !props.isView">
                  <el-button v-btn link @click="editTask(task.taskId)">
						      	<font-awesome-icon :icon="['fas', 'pen-to-square']" style="color: rgb(4, 157, 255);font-size: 12px;" />
						      </el-button>
                  <el-button v-btn link @click="deleteTask(task.taskId)">
						      	<font-awesome-icon :icon="['fas', 'trash-can']" style="color: rgb(4, 157, 255);font-size: 12px;" />
						      </el-button>
                </section>
              </section>
              <section class="task_children" v-for="(step, stepIndex) in task.taskStep" :key="stepIndex">
                <el-button v-btn link v-if="task.taskStep[stepIndex].roleName != task.taskStep[stepIndex - 1]?.roleName">
				        	<font-awesome-icon :icon="['fas', 'user']" style="color: rgb(4, 157, 255)" />
				        	<span class="table-inner-btn" style="color: rgb(4, 157, 255);">{{ step.roleName }}</span>
				        </el-button>
                <p class="task_line">{{ `S${step.number} ${step.stepContent}` }}</p>
              </section>
            </main>
          </div>
        </section>
      </el-col>
    </el-row>
    <taskForm ref="taskFormRef" @re-get-process-task-list="reGetProcessTaskList"></taskForm>
  </div>
</template>

<script lang="ts" setup>
import taskForm from './taskForm.vue';
import { processApi } from "@/app/platform/api/process/processCompilation"

const props = defineProps({
  isView: {
    type: Boolean,
    default: false
  },
  stepsArr: {
    type: Array,
    default: []
  },
  mainId: {
    type: String,
    default: ''
  },
  status: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['reGetProcessTaskList'])

watch(
	() => props.stepsArr,
	(val: any) => {
		setTimeout(() => {
			stepsArr.value = val.map((item: any) => ({ ...item, stepNumber: addNumber(item.taskTreeVos || []) }))
		});
	},
	{ deep: true }
)

// 模拟流程任务数据
const stepsArr = ref<any>([
  { name: '首报启动', number: 1, processId: 1, processStatus: null, processTime: null, taskTreeVos: [] },
  { name: '现场赶赴', number: 2, processId: 2, processStatus: null, processTime: null, taskTreeVos: [] },
  { name: '出动到位', number: 3, processId: 3, processStatus: null, processTime: null, taskTreeVos: [] },
  { name: '应急处置', number: 4, processId: 4, processStatus: null, processTime: null, taskTreeVos: [] },
  { name: '事件终报', number: 5, processId: 5, processStatus: null, processTime: null, taskTreeVos: [] }
])
const taskFormRef = ref<any>(null)

/**
 * @description 计算步骤数量
 * @param arr 被计算的数组
 */
const addNumber = (arr: any) => {
  let number = 0
  arr.forEach((element: any) => {
    const stepNumber = element.taskStep.length || 0
    number += stepNumber
  })

  return number
}

/**
 * @description 添加任务-展示任务弹窗
 */
const addTask = (processId: any, mainId: string) => {
  taskFormRef.value.showTaskForm(processId, mainId)
}

/**
 * @description 编辑任务
 */
const editTask = (taskId: any) => {
  taskFormRef.value.editTaskForm(taskId)
}

/**
 * @description 删除任务
 */
const deleteTask = (id: any) => {
  CustomMessageBox({ message: "确定要移除这行数据吗？" }, (res: boolean) => {
		if (res) {
			processApi.
			deleteTaskApi(id)
			.then((res: any) => {
				ElMessage.success('删除成功')
			})
			.catch((err) => {
				throw new Error("deleteTaskApi():::" + err)
			})
			.finally(() => {
				emit('reGetProcessTaskList')
			})
		}
	})
}

/**
 * @description 重新获取流程任务列表
 */
const reGetProcessTaskList = () => {
  emit('reGetProcessTaskList')
}
</script>

<style lang="scss" scoped>
.schedulingSteps_container{
  width: 100%;
  padding-top: 20px;
  padding-left: 20px;
  box-sizing: border-box;

  .steps_list{
    width: 86%;

    .steps_box{
      width: 100%;
      // height: 118px;
      background-color: rgb(246, 246, 246);
      border: 1px dashed #ccc;
      padding: 0 10px 10px;
      box-sizing: border-box;
      margin-bottom: 20px;
      border-radius: 2px;

      &.active{
        background-color: rgb(229, 245, 255);
        border: 1px dashed rgb(175, 223, 255);

        .steps_title{
          color: rgb(4, 157, 255);
          border-bottom: 1px dashed rgb(175, 223, 255);
        }

        .bottom_info_box{
          article{
            &:first-child{
              border-right: 1px dashed rgb(175, 223, 255);
            }
          }

          p{
            color: rgb(4, 157, 255);
          }
        }
      }

      .steps_title{
        width: 100%;
        height: 45px;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 14.5px;
        font-weight: bold;
        color: #888;
        border-bottom: 1px dashed #ccc;
        margin-bottom: 10px;
      }

      .bottom_info_box{
        width: 100%;
        // height: calc(50% - 10px - 10px);
        display: flex;
        justify-content: space-between;

        article{
          width: 50%;
          box-sizing: border-box;
          display: flex;
          flex-wrap: wrap;
          justify-content: center;
          align-items: center;
          
          &:first-child{
            padding-right: 10px;
            border-right: 1px dashed #ccc;
          }

          &:last-child{
            padding-left: 10px;
          }

          p{
            width: 100%;
            text-align: center;
            color: #999;
          }

          .number{
            font-size: 28px;
            font-weight: bold;
          }

          .describe{
            font-size: 12px;
          }
        }
      }
    }
  }

  .task_container{
    width: 100%;
    padding-right: 10px;
    box-sizing: border-box;

    .task_detail{
      width: 100%;
      margin-top: 10px;
      padding-bottom: 20px;
      border-bottom: 1px dashed #ccc;

      &:last-child{
        border-bottom: 0;
      }

      &.active{
        .title{
          color: rgb(35, 106, 166);
        }
      }

      header{
        width: 100%;
        display: flex;
        justify-content: space-between;
        padding-left: 10px;
        box-sizing: border-box;

        .title{
          font-size: 14px;
          font-weight: bold;
          color: #999;
        }
      }

      .task_info{
        width: 100%;

        .task_title{
          width: 100%;
          // height: 40px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          background-color: rgb(229, 245, 255);
          border: 1px dashed rgb(175, 223, 255);
          padding: 5px 10px;
          box-sizing: border-box;
          margin-top: 10px;
          border-radius: 4px;

          .flex_child{
            display: flex;
            align-items: center;

            p{
              font-size: 12px;
              font-weight: bold;
              color: rgb(4, 157, 255);
            }

            .essentialFactor_box{
              width: fit-content;
              height: 25px;
              display: flex;
              align-items: center;
              padding: 0 10px;
              border: 1px dashed rgb(4, 157, 255);
              box-sizing: border-box;
              font-size: 12px;
              color: rgb(4, 157, 255);
              margin-left: 20px;
              margin-right: 10px;
              border-radius: 4px;
              flex-shrink: 0;
            }

            .el-button{
              margin: 2px !important;
            }
          }
        }

        .task_children{
          width: 100%;
          padding: 5px 0 0 10px;
          box-sizing: border-box;

          .task_line{
            width: 100%;
            padding-top: 8px;
            white-space: wrap;
            font-size: 12px;
            color: #999;
          }
        }
      }
    }
  }
}
</style>