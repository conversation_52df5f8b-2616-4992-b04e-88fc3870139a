<template>
  <Drawer
		size="420"
		v-model:drawer="showAddProcessDrawer"
		:destroyOnClose="true"
	>
    <Title :title="baseProcessFormTitle" />
    <section class="form_container">
      <el-form
	    	class="form-container"
	    	ref="baseProcessFormRef"
	    	label-position="top"
	    	label-width="100px"
	    	:model="baseProcessForm"
	    	:rules="rules"
	    >
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="处置级别" prop="disposalLevel">
	    	    	<el-select
	    	    		style="width: 100%"
	    	    		v-model="baseProcessForm.disposalLevel"
	    	    		placeholder="请选择处置级别"
	    	    	>
	    	    		<el-option
	    	    			v-for="item in levelArr"
	    	    			:key="item.value"
	    	    			:label="item.label"
	    	    			:value="item.label"
	    	    		/>
	    	    	</el-select>
	    	    </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属专业" prop="majorId">
	    	    	<el-select
	    	    		style="width: 100%"
	    	    		v-model="baseProcessForm.majorId"
	    	    		placeholder="请选择所属专业"
	    	    	>
	    	    		<el-option
	    	    			v-for="item in majorArr"
	    	    			:key="item.value"
	    	    			:label="item.label"
	    	    			:value="String(item.value)"
	    	    		/>
	    	    	</el-select>
	    	    </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="所属线路" prop="lineId">
	    	    	<el-select
	    	    		style="width: 100%"
	    	    		v-model="baseProcessForm.lineId"
	    	    		placeholder="请选择所属线路"
	    	    	>
	    	    		<el-option
	    	    			v-for="item in lineArr"
	    	    			:key="item.value"
	    	    			:label="item.label"
	    	    			:value="item.value"
	    	    		/>
	    	    	</el-select>
	    	    </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="关联位置" prop="locComposeNoName">
							<el-input
								@click="showChooseLocation"
								v-model="baseProcessForm.locComposeNoName"
								readonly
								placeholder="请选择关联位置"
							>
								<template #append>
									<font-awesome-icon
										:icon="['fas', 'location-dot']"
										style="color: #ccc"
										@click="showChooseLocation"
									/>
								</template>
							</el-input>
	  		    </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="定额时间" prop="limitedTime">
							<el-time-picker style="width: 100%;" v-model="baseProcessForm.limitedTime" placeholder="请选择定额时间" value-format="HH:mm:ss" />
	  	  	  </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="标准处置流程标题" prop="mainTitle">
	  	  	  	<el-input
	  	  	  		v-model.trim="baseProcessForm.mainTitle"
	  	  	  		:formatter="(value: string) => value.trim()"
	  	  	  		placeholder="请输入标准处置流程标题"
	  	  	  	/>
	  	  	  </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注说明" prop="remark">
	  	  	  	<el-input
	  	  	  		v-model.trim="baseProcessForm.remark"
	  	  	  		:formatter="(value: string) => value.trim()"
                :rows="5"
                type="textarea"
                resize="none"
	  	  	  		placeholder="请输入备注说明"
                style="width: 100%;"
	  	  	  	/>
	  	  	  </el-form-item>
          </el-col>
        </el-row>
	    </el-form>
			<div class="btn-list">
	  		<ButtonList :button="submitFormBtn" @onBtnClick="submitForm" />
	  	</div>
    </section>

    <!-- 选择关联位置弹窗 -->
    <Drawer
			size="420"
			v-model:drawer="showChooseLocationDrawer"
			:destroyOnClose="true"
		>
			<Title :title="chooseLocationTitle" />
			<section class="tree_container">
				<PitayaTree
					ref="treeRef"
					:need-check-box="false"
					:single-select="true"
					:tree-data="locationTreeData"
					:tree-props="locationTreeProps"
					:tree-loading="locationTreeLoading"
					nodeKey="locComposeNo"
				/>
			</section>
			<div class="btn-list">
				<ButtonList :loading="btnLoading" :button="chooseLocationBtn" @onBtnClick="chooseLocation" />
			</div>
		</Drawer>
  </Drawer>
</template>

<script lang="ts" setup>
import { FormInstance, FormRules, ElMessage } from "element-plus"
import { processApi } from "@/app/platform/api/process/processCompilation"
import { drawingsApi } from "@/app/platform/api/configuration/drawing"
import { _matchFormProp } from "@/app/platform/utils/permission"
import { useMajor } from "@/app/platform/hooks/useMajor"
import { useLine } from "@/app/platform/hooks/useLine"
import { DictApi } from "@/app/baseline/api/dict"
import { cloneDeep } from "lodash-es"


const showAddProcessDrawer = ref<boolean>(false) //控制新增标准处置流程弹窗
const submitFormBtn = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "保存",
		icon: ["fas", "floppy-disk"]
	}
]
// 标题
const baseProcessFormTitle = {
	name: ["新增标准处置流程"],
	icon: ["fas", "square-share-nodes"]
}

const props = defineProps({
	processData: {
		default: {}
	}
})

watch(
	() => props.processData,
	async (val: any) => {
		const editData = cloneDeep(val)
		editData.limitedTime = formatSecond(editData.limitedTime)
		editData.locComposeNoName = editData.locComposeName
		Object.assign(baseProcessForm, editData)
	},
	{ deep: true }
)

// 表单相关
const baseProcessFormRef = ref<any>(null)
let baseProcessForm: anyKey = reactive({
	disposalLevel: null,
  majorId: null,
  lineId: null,
	locComposeNo: null,
	locComposeNoName: null,
  limitedTime: null,
  mainTitle: null,
	remark: null
})
const btnLoading = ref<boolean>(false)
const rules = reactive<FormRules<typeof baseProcessForm>>({
	disposalLevel: [
	  { required: true, message: "请选择处置级别", trigger: "change" }
	],
  majorId: [
	  { required: true, message: "请选择所属专业", trigger: "change" }
	],
  lineId: [
    { required: true, message: "请选择所属线路", trigger: "change" }
  ],
  locComposeNoName: [
    { required: true, message: "请选择关联位置", trigger: "change" }
  ],
  limitedTime: [
    { required: true, message: "请输入定额时间", trigger: "blur" }
  ],
  mainTitle: [
    { required: true, message: "请输入标准处置流程标题", trigger: "blur" }
  ]
})
// 处置级别数据源
const levelArr = ref<any[]>([])
// 所属专业数据源
const useMajorForData = useMajor(true)
const majorArr = useMajorForData.majorArr
// 所属线路数据源
const useLineForData = useLine()
const lineArr = useLineForData.linesArr

const emit = defineEmits(['submitSuccess'])

// 选择关联位置弹窗
interface Tree {
	id: number | string
	label: string
	children?: Tree[]
}

const treeRef = ref<any>()
const showChooseLocationDrawer = ref<boolean>(false)
const chooseLocationTitle = {
	name: ["选择关联位置"],
	icon: ["fas", "square-share-nodes"]
}
const chooseLocationBtn = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "保存",
		icon: ["fas", "floppy-disk"]
	}
]
const locationTreeLoading = ref(false)
const locationTreeData = ref<Tree[]>([])
const locationTreeProps = {
	children: "children",
	label: "newLocComposeName"
}

onMounted(() => {
	getDisposalLevel()
})

/**
 * @description 递归处理数据, 为 cascade 的每个 options 添加 label 和 value 属性
 */
const addValueAndLabelForTree = (arr: any[]): any => {
  arr = arr.map(node => {
    if (node.hasOwnProperty('children')) {
      node.children = addValueAndLabelForTree(node.children)
    }
    return {
      ...node,
      newLocComposeName: `${node.locComposeNameBelong}(${node.locComposeNoBeLong})`
    }
  })
  return arr
}

/**
 * @description 时间转换为秒
 * @param timeString 时间字符串
 */
const timeToSeconds = (timeString: string): number => {
  const [hours, minutes, seconds] = timeString.split(':').map(Number)
  return hours * 3600 + minutes * 60 + seconds;
}

/**
 * @description 格式化秒数为时分秒
 * @param seconds 
 */
const formatSecond = (seconds: number) => {
  let a: number | string = ~~(seconds / 3600)
  let b: number | string = ~~(seconds / 60) - a * 60
  let c: number | string = seconds % 60 || 0
 
  a = String(a).padStart(2, "0")
  b = String(b).padStart(2, "0")
  c = String(c).padStart(2, "0")
 
  return `${a}:${b}:${c}`
}

/**
 * @description 获取处置级别
 */
const getDisposalLevel = () => {
	DictApi.getDictByCode("DISPOSAL_LEVEL")
	.then((res) => {
		levelArr.value = res as any
	})
}

/**
 * @description 关联位置弹窗展示
 */
const showChooseLocation = () => {
	if(baseProcessForm.lineId == null){
		ElMessage.warning('请先选择线路')
		return
	}else{
		getLocationTreeData(baseProcessForm.lineId)
	}
	showChooseLocationDrawer.value = true
}

/**
 * @description 获取关联位置树的数据
 */
const getLocationTreeData = (lineId: any) => {
	locationTreeLoading.value = true
	drawingsApi.getPositionTreeApi({ lineId })
		.then((res: any) => {
			locationTreeData.value = []
			const map = new Map(Object.entries(res))
			map.forEach((value: any, key) => {
				const list: any[] = []
				locationTreeData.value = locationTreeData.value.concat(addValueAndLabelForTree(value))
			})
		})
		.finally(() => {
			locationTreeLoading.value = false
		})
}

/**
 * @description 选择关联位置
 */
const chooseLocation = (btnName: string | undefined) => {
	if (btnName === "保存") {
		// 将勾选的key和节点存入
		const currentSelectKeys = treeRef.value.PitayaTreeRef.getCurrentKey()
		const currentSelectNodes = treeRef.value.PitayaTreeRef.getCurrentNode()
		baseProcessForm.locComposeNo = currentSelectKeys
		baseProcessForm.locComposeNoName = currentSelectNodes.locComposeName
	}
	showChooseLocationDrawer.value = false
}

/**
 * @description 提交表单
 */
const submitForm = (btnName: string | undefined) => {
	if (btnName === "保存") {
		baseProcessFormRef.value?.validate((valid: any) => {
      if(valid){
				const data = cloneDeep(baseProcessForm)
				data.limitedTime = timeToSeconds(data.limitedTime)
				if(data.id){
					btnLoading.value = true
					processApi
						.updateFlowApi({ ...data })
						.then((res: any) => {
							ElMessage.success("编辑成功")
							emit('submitSuccess')
							baseProcessFormRef.value?.resetFields()
    					showAddProcessDrawer.value = false
						})
						.catch((err) => {
							throw new Error("updateFlowApi():::" + err)
						})
						.finally(() => {
							btnLoading.value = false
						})
					return false
				}

				btnLoading.value = true
				processApi
				.addFlowApi({ ...data, bmpStatus: 0 })
				.then((res: any) => {
					ElMessage.success("添加成功")
					emit('submitSuccess')
					baseProcessFormRef.value?.resetFields()
    			showAddProcessDrawer.value = false
				})
				.catch((err) => {
					throw new Error("addFlowApi():::" + err)
				})
				.finally(() => {
					btnLoading.value = false
				})
      }
    })
	}else{
    baseProcessFormRef.value?.resetFields()
    showAddProcessDrawer.value = false
  }
}

defineExpose({
	showAddProcessDrawer
})
</script>

<style lang="scss" scoped>
.form_container{
  width: 100%;
  padding: 10px;
  box-sizing: border-box;
}
.btn-list {
	width: 400px;
	display: flex;
	justify-content: flex-end;
	position: fixed;
	right: 10px;
	bottom: 0px;
	z-index: 99;
	padding: 10px 10px 10px 0;
	box-sizing: border-box;
	border-top: 1px solid #ccc;
	background-color: #fff;
}
</style>