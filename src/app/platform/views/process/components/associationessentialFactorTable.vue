<template>
	<div class="essentialFactor-table">
		<PitayaTable
			:need-index="false"
			:customizeHeightNumber="0"
			:table-data="essentialFactorTableData"
			:columns="essentialFactorTableColumn"
			:need-pagination="false"
			:table-loading="tableLoading"
		>
			<template #operations="{ rowData, $index }">
        <el-button v-btn link @click="editRow(rowData, $index)">
					<font-awesome-icon :icon="['fas', 'pen-to-square']" style="color: var(--pitaya-btn-background)" />
					<span class="table-inner-btn">编辑</span>
				</el-button>
				<el-button v-btn link @click="deleteRow(rowData, $index)">
					<font-awesome-icon :icon="['fas', 'trash-can']" style="color: var(--pitaya-btn-background)" />
					<span class="table-inner-btn">移除</span>
				</el-button>
			</template>
			<template #footerOperateLeft>
				<el-button v-btn style="border-radius: 0" @click="showChooseEssentialFactor">
					<font-awesome-icon
						:icon="['fas', 'square-plus']"
						style="color: #fff"
					/>
					<span class="choose-essentialFactor-btn">新增要素</span>
				</el-button>
			</template>
		</PitayaTable>
		<Drawer
			v-show="showChooseEssentialFactorDrawer"
			class="inner-drawer drawer-hidden-box"
			:size="300"
			v-model:drawer="showChooseEssentialFactorDrawer"
		>
			<Title :title="chooseEssentialFactorTitle" />
			<section class="form_container">
        <el-form
		    	class="form-container"
		    	ref="essentialFactorFormRef"
		    	label-position="top"
		    	label-width="100px"
		    	:model="essentialFactorForm"
		    	:rules="rules"
		    >
          <el-form-item label="要素排序" prop="number">
			  		<el-input
			  			v-model.trim="essentialFactorForm.number"
			  			:formatter="(value: string) => value.trim()"
			  			placeholder="请输入要素排序"
			  		/>
			  	</el-form-item>
          <el-form-item label="要素内容" prop="content">
			  		<el-input
              type="textarea"
              :rows="5"
              resize="none"
			  			v-model.trim="essentialFactorForm.content"
			  			:formatter="(value: string) => value.trim()"
			  			placeholder="请输入要素内容"
			  		/>
			  	</el-form-item>
        </el-form>
      </section>
			<div class="btn-list">
				<ButtonList :button="chooseEssentialFactorBtn" @onBtnClick="chooseEssentialFactor" />
			</div>
		</Drawer>
	</div>
</template>

<script lang="ts" setup>
import { FormInstance, FormRules } from "element-plus"
import { CustomMessageBox } from "@/app/platform/utils/message"
import { cloneDeep } from "lodash-es"
import { processApi } from "@/app/platform/api/process/processCompilation"

const props = defineProps({
	associationessentialFactorTableArr: {
		type: Array,
		default: []
	}
})

watch(
	() => props.associationessentialFactorTableArr,
	(val: any) => {
		setTimeout(() => {
			essentialFactorTableData.value = val.map((item: any) => ({ ...item, content: item.pointContent }))
		});
	},
	{ deep: true }
)

const essentialFactorTableColumn: TableColumnType[] = [
	{ label: "序号", prop: "number" },
	{ label: "任务要素", prop: "content" },
	{
		label: "操作",
		prop: "operations",
		fixed: "right",
		width: 200,
		needSlot: true
	}
]
const chooseEssentialFactorTitle = {
	name: ["任务要素"],
	icon: ["fas", "square-share-nodes"]
}
const chooseEssentialFactorBtn = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "保存",
		icon: ["fas", "floppy-disk"]
	}
]
const essentialFactorTableData = ref<any[]>([])
const showChooseEssentialFactorDrawer = ref<boolean>(false)
const tableLoading = ref(false)

// 表单相关
const essentialFactorFormRef = ref<FormInstance>()
const essentialFactorForm: anyKey = reactive({
	number: null,
  content: null
})
const rules = reactive<FormRules<typeof essentialFactorForm>>({
	number: [
	  { required: true, message: "请输入要素排序", trigger: "blur" }
	],
  content: [
	  { required: true, message: "请输入要素内容", trigger: "blur" }
	]
})

/**
 * @description 编辑选择的任务
 */
const editRow = (row: any, index: any) => {
  Object.assign(essentialFactorForm, {...row, index})
  showChooseEssentialFactorDrawer.value = true
}

/**
 * @description 删除选择的任务
 * @param row 选择的行
 */
const deleteRow = (row: any, index: any) => {
	CustomMessageBox({ message: "确定要移除吗?" }, (res: boolean) => {
		if (res) {
			essentialFactorTableData.value.splice(index, 1)
		}
	})
}

/**
 * @description 展示新建要素弹窗
 */
const showChooseEssentialFactor = () => {
	essentialFactorFormRef.value?.resetFields()
	Object.assign(essentialFactorForm, {
		number: null,
  	content: null
	})
	delete essentialFactorForm.index
	showChooseEssentialFactorDrawer.value = true
}

/**
 * @description 新建要素
 */
const chooseEssentialFactor = (btnName: string | undefined) => {
	if (btnName === "保存") {
		essentialFactorFormRef.value?.validate((valid) => {
      if(valid) {
      	const essentialFactorData = cloneDeep(essentialFactorForm)
        if(essentialFactorData.index || essentialFactorData.index == 0){
          essentialFactorTableData.value[essentialFactorData.index] = essentialFactorData
        }else{
          essentialFactorTableData.value.push(essentialFactorData)
        }
        essentialFactorTableData.value = essentialFactorTableData.value.sort((a, b) => a.number - b.number)
        essentialFactorFormRef.value?.resetFields()
        showChooseEssentialFactorDrawer.value = false
      }
    })
	}else{
    essentialFactorFormRef.value?.clearValidate()
    essentialFactorFormRef.value?.resetFields()
    showChooseEssentialFactorDrawer.value = false
  }	
}

defineExpose({
	essentialFactorTableData: essentialFactorTableData
})
defineOptions({
	name: "associationEssentialFactorTable"
})
</script>

<style lang="scss" scoped>
:deep(.el-tree-node__content) {
	height: 32px;
}
:deep(.el-tree-node__label) {
	font-size: var(--pitaya-fs-12);
}
.choose-essentialFactor-btn {
	margin-left: 5px;
	color: #fff;
	font-weight: 500;
}
.inner-drawer {
	position: relative;
	height: 100%;
  .form_container{
    padding: 10px 10px 0;
    box-sizing: border-box;
  }
	.btn-list {
		display: flex;
		justify-content: flex-end;
		position: fixed;
		right: 10px;
		bottom: 0px;
		z-index: 99;
		padding: 10px 10px 10px 0;
		box-sizing: border-box;
		width: 290px;
		border-top: 1px solid #ccc;
		background-color: #fff;
	}
}
</style>