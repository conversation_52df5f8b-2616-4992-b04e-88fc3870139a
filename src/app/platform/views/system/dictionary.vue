<script lang="ts" setup>
import dictListTab from "@/app/platform/views/system/components/dictListTab.vue"
import dictTreeTab from "@/app/platform/views/system/components/dictTreeTab.vue"
interface queryObj {
	/**
	 * 字典编码
	 */
	dataDictionaryCode?: string
	/**
	 * 字典名称
	 */
	dataDictionaryName?: string
}

const paramObj = ref<queryObj>({
	dataDictionaryCode: "",
	dataDictionaryName: ""
})

const getQueryData = (queryData: queryObj) => {
	paramObj.value = Object.assign(paramObj.value, queryData)
	if (activedTab.value === 0) {
		listDictTabRef.value.handleQuery()
	}
	if (activedTab.value === 1) {
		treeDictTabRef.value.handleQuery()
	}
}
const activedTab = ref<number>(0)
const listDictTabRef = ref<any>()
const treeDictTabRef = ref<any>()

const queryArrList = [
	{
		name: "字典名称",
		key: "dataDictionaryName",
		type: "input",
		placeholder: "请输入查询关键字",
		enableFuzzy: false
	},
	{
		name: "字典编码",
		key: "dataDictionaryCode",
		type: "input",
		placeholder: "请输入编码",
		enableFuzzy: false
	}
]

const title = {
	name: ["数据字典"],
	icon: ["fas", "square-share-nodes"]
}

const button = [
	{
		name: "新增列表字典",
		icon: ["fas", "square-plus"],
		roles: "system:dictionary:btn:add",
		tab: 0
	},
	{
		name: "新增树形字典",
		icon: ["fas", "square-plus"],
		roles: "system:dictionary:btn:add",
		tab: 1
	}
]

const onBtnClick = (e: string | undefined) => {
	if (activedTab.value === 0) {
		listDictTabRef.value.handleAdd()
	}
	if (activedTab.value === 1) {
		treeDictTabRef.value.handleAdd()
	}
}

const titleTabs = ["列表字典", "树形字典"]

const onTabChange = (value: number) => {
	activedTab.value = value
}
</script>

<template>
	<div class="app-container">
		<ModelFrame>
			<Query class="ml10"  :queryArrList="queryArrList" @getQueryData="getQueryData" />
		</ModelFrame>
		<div class="app-content-wrapper">
			<div class="app-content-group">
				<ModelFrame>
					<Title
						:title="title"
						:button="button.filter((item:any)=>item.tab === activedTab)"
						@onBtnClick="onBtnClick"
					>
						<Tabs
							@onTabChange="onTabChange"
							class="abs-tab"
							:tabs="titleTabs"
						/>
					</Title>
					<dict-list-tab
						ref="listDictTabRef"
						v-if="activedTab === 0"
						:paramObj="paramObj"
					/>
					<dict-tree-tab
						ref="treeDictTabRef"
						v-if="activedTab === 1"
						:paramObj="paramObj"
					/>
				</ModelFrame>
			</div>
		</div>
	</div>
</template>

<style lang="scss" scoped>
.btn-groups-box {
	padding: 0 10px;
}

.abs-tab {
	position: absolute;
	left: 140px;
}

.app-container {
	:deep(.model-frame-wrapper) {
		padding-bottom: 0px;
	}
}

.app-content-wrapper {
	height: 0;
	flex: 1;
	margin-top: 10px;

	.app-content-group {
		height: 100%;

		:deep(.model-frame-wrapper) {
			height: 100%;
			padding-bottom: 10px;
			overflow: hidden;
			display: flex;
			flex-direction: column;
		}
	}
}
</style>
