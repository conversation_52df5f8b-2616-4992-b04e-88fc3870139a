<script lang="ts" setup>
import { ElMessage } from "element-plus"
import type { FormInstance, FormRules } from "element-plus"
import { usePagination } from "@/app/platform/hooks/usePagination"
import {
	getMessageList,
	getMessageUpdate
} from "@/app/platform/api/system/message"

interface queryObj {
	/**
	 * 页码
	 */
	currentPage?: number
	/**
	 * 每页条数
	 */
	pageSize?: number
}

const tableLoading = ref(false)
const drawerLoading = ref(false)

const usePaginationStore = usePagination()
const paginationData = ref<PaginationData>(usePaginationStore.paginationData)

const paramObj = ref<queryObj>({
	currentPage: paginationData.value.currentPage,
	pageSize: paginationData.value.pageSize,
})

const getTableList = (pd: PaginationData) => {
	paramObj.value.currentPage = pd.currentPage
	paramObj.value.pageSize = pd.pageSize
	systemParameterListInit()
}

//消息设置列表
const drawerState = ref(false)
const drawerSize = 310
const dataTotal = ref(0)
const baseTableData = ref<any[]>([])
const baseColumns = ref<TableColumnType[]>([
	{ prop: "eventName", label: "事件", needSlot: true, minWidth: 200 },
	// {
	// 	prop: "receiverObjectList",
	// 	label: "通知对象",
	// 	needSlot: true,
	// 	align: "left"
	// },
	{ prop: "internalMessage", label: "系统消息", width: 100, needSlot: true },
	{ prop: "wechatMessage", label: "企业微信", width: 100, needSlot: true },
	{ prop: "noticeType", label: "通知方式", needSlot: true, width: 350 },
	{ prop: "noticeDatetime", label: "通知时间", needSlot: true, width: 200 },
	{ prop: "combined", label: "是否合并发送", needSlot: true, width: 100 },
	{
		prop: "operations",
		fixed: "right",
		label: "操作",
		needSlot: true,
		width: 180
	}
])
const systemParameterListInit = () => {
	tableLoading.value = true
	getMessageList(paramObj.value)
		.then((res: any) => {
			if (res.rows && res.rows.length) {
				baseTableData.value = JSON.parse(JSON.stringify(res.rows))
				baseTableData.value.map((item) => {
					item.noticeDatetime = item.noticeDatetime
						? item.noticeDatetime
						: "00:00"
				})
				dataTotal.value = res.records
			} else {
				baseTableData.value = []
				dataTotal.value = 0
			}
		})
		.finally(() => {
			tableLoading.value = false
		})
}

onMounted(() => {
	systemParameterListInit()
})

const title = {
	name: ["消息设置"],
	icon: ["fas", "square-share-nodes"]
}

const drawerTitle = {
	name: ["消息文案"],
	icon: ["fas", "square-share-nodes"]
}
const btnList = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "保存",
		icon: ["fas", "floppy-disk"]
	}
]
const baseFormRef = ref<FormInstance>()
const baseFormData = reactive<anyKey>({})
const formRules = reactive<FormRules<typeof baseFormData>>({
	messageTitle: [
		{ required: true, message: "请输入消息标题", trigger: "blur" }
	],
	messageBody: [
		{ required: true, message: "请输入参消息内容", trigger: "blur" }
	]
})
//编辑
const onEdit = (rowData: anyKey) => {
	Object.keys(rowData).forEach((key) => {
		baseFormData[key] = rowData[key] ? rowData[key] : ""
	})
	drawerState.value = true
}
//保存
const onFormBtnClick = (btnName: string | undefined) => {
	if (!baseFormRef.value) return
	if (btnName === "保存") {
		baseFormRef.value.validate((valid) => {
			if (valid) {
				getChange(baseFormData)
			} else {
				return false
			}
		})
		return
	}

	if (btnName === "取消") {
		drawerState.value = false
		return
	}
}

const onClose = () => {
	Object.keys(baseFormData).map((key) => (baseFormData[key] = ""))
	nextTick(() => {
		baseFormRef.value?.clearValidate()
	})
}


//保存接口
const numFlag = ref(1)
const numTwo  = ref(2)
const num = ref(0)
const getChange = (data: any) => {
	if (data.noticeType == 0) {
		data.noticeDatetime = "00:00"
	}
	tableLoading.value = true
	if (drawerLoading.value) return
	drawerLoading.value = true
	getMessageUpdate(data)
		.then(() => {
			ElMessage({
				message: "操作成功",
				type: "success",
				duration: 1000
			})
			drawerState.value = false
		})
		.finally(() => {
			systemParameterListInit()
			drawerLoading.value = false
			tableLoading.value = false
		})
}
</script>

<template>
	<div class="app-container">
		<div class="app-content-wrapper" style="margin-top: 0">
			<div class="app-content-group">
				<ModelFrame>
					<Title :title="title" />
					<div class="app-el-scrollbar-wrapper">
						<el-scrollbar>
							<PitayaTable
								@onCurrentPageChange="getTableList"
								:table-data="baseTableData"
								:columns="baseColumns"
								:total="dataTotal"
								:pageSize="paramObj.pageSize"
								:table-loading="tableLoading"
							>
								<template #eventName="{ rowData }">
									<div>{{ rowData.eventName }}</div>
								</template>
								<!-- 通知对象 -->
								<template #receiverObjectList="{ rowData }">
									<el-checkbox
										size="small"
										:true-label="numFlag"
										:false-label="num"
										@change="getChange(rowData)"
										v-for="item in rowData.receiverObjectList"
										v-model="item.receiverObjectValue"
										>{{ item.receiverObjectName }}</el-checkbox
									>
								</template>

								<!-- 系统消息 -->
								<template #internalMessage="{ rowData }">
									<el-checkbox
										size="small"
										:true-label="numFlag"
										:false-label="num"
										@change="getChange(rowData)"
										v-model="rowData.internalMessage"
									></el-checkbox>
								</template>
								<!-- 企业微信 -->
								<template #wechatMessage="{ rowData }">
									<el-checkbox
										size="small"
										:true-label="numFlag"
										:false-label="num"
										@change="getChange(rowData)"
										v-model="rowData.wechatMessage"
									></el-checkbox>
								</template>
								<!-- 通知方式 -->
								<template #noticeType="{ rowData }">
									<!-- 0:立即通知 1：定时通知 2:循环通知 -->
									<el-radio-group
										v-model="rowData.noticeType"
										@change="getChange(rowData)"
									>
										<el-radio size="small" :label="num">立即通知</el-radio>
										<el-radio size="small" :label="numFlag">定时通知</el-radio>
										<el-radio size="small" :label="numTwo">自定义</el-radio>

									</el-radio-group>
								</template>
								<!-- 通知时间 -->
								<template #noticeDatetime="{ rowData }">
									<el-time-picker
										v-if="rowData.noticeType == 1"
										style="width: 150px"
										@change="getChange(rowData)"
										v-model="rowData.noticeDatetime"
										placeholder="请选择"
										value-format="HH:mm"
										format="HH:mm"
										size="small"
									/>
									<div v-else>---</div>
								</template>
								<!-- 是否合并发送 0：不合并 1：合并-->
								<template #combined="{ rowData }">
									<el-checkbox
										size="small"
										:true-label="numFlag"
										:false-label="num"
										@change="getChange(rowData)"
										v-model="rowData.combined"
									></el-checkbox>
								</template>

								<template #operations="{ rowData }">
									<el-button
										v-btn
										link
										@click="onEdit(rowData)"
									>
										<font-awesome-icon
											:icon="['fas', 'pen-to-square']"
											style="color: var(--pitaya-btn-background)"
										/>
										<span class="table-inner-btn"> 编辑 </span>
									</el-button>
								</template>
							</PitayaTable>
						</el-scrollbar>
					</div>
				</ModelFrame>
			</div>
		</div>
		<Drawer
			:size="drawerSize"
			:destroyOnClose="true"
			v-model:drawer="drawerState"
			@close="onClose"
		>
			<div class="common-from-wrapper" v-loading="drawerLoading">
				<Title :title="drawerTitle" />
				<div class="common-from-group">
					<el-scrollbar>
						<el-form
							class="el-form-wrapper"
							ref="baseFormRef"
							:model="baseFormData"
							:rules="formRules"
							label-width="auto"
							label-position="top"
						>
							<el-form-item label="消息标题" prop="messageTitle">
								<el-input
									v-model.trim="baseFormData.messageTitle"
									type="textarea"
									:autosize="{ minRows: 8, maxRows: 8 }"
									resize="none"
									show-word-limit
								/>
							</el-form-item>
							<el-form-item label="消息内容" prop="messageBody">
								<el-input
									v-model.trim="baseFormData.messageBody"
									type="textarea"
									:autosize="{ minRows: 8, maxRows: 8 }"
									resize="none"
									show-word-limit
								/>
							</el-form-item>
						</el-form>
					</el-scrollbar>
				</div>
				<div class="btn-groups">
					<ButtonList :button="btnList" @onBtnClick="onFormBtnClick" />
				</div>
			</div>
		</Drawer>
	</div>
</template>

<style lang="scss" scoped>
.app-container {
	:deep(.model-frame-wrapper) {
		padding-bottom: 0px;
	}
}
.app-content-wrapper {
	height: 0;
	flex: 1;
	margin-top: 10px;
	.app-content-group {
		height: 100%;
		:deep(.model-frame-wrapper) {
			height: 100%;
			padding-bottom: 10px;
			overflow: hidden;
			display: flex;
			flex-direction: column;
		}
		.app-el-scrollbar-wrapper {
			height: 0;
			flex: 1;
			.table-inner-btn {
				margin-left: 5px;
				color: #204a9c;
			}
		}
	}
}
.common-from-wrapper {
	height: 100%;
	width: 100%;
	display: flex;
	flex-direction: column;
	.common-from-group {
		height: 0;
		flex: 1;
		padding: 0 10px;
	}
	.btn-groups {
		display: flex;
		justify-content: flex-end;
		padding: 10px 10px 0 0;
		border-top: 1px solid var(--pitaya-border-color);
	}
}
:deep(.el-textarea__inner) {
	/* 针对 WebKit 浏览器隐藏滚动条 */
	-webkit-scrollbar: none;
	/* Firefox */
	scrollbar-width: none; /* Firefox 64+ */
}
</style>
@/app/platform/hooks/usePagination @/app/platform/api/system/parameter
