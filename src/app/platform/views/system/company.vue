<script lang="ts" setup>
import CompanyDrawer from "./components/companyDrawer.vue"
import { usePagination } from "@/app/platform/hooks/usePagination"
import { CompanyApi } from "@/app/platform/api/system/company"
import { ElMessage } from "element-plus"
import {
	checkPermission,
	isCheckPermission
} from "@/app/platform/utils/permission"
import XEUtils from "xe-utils"
import { CustomMessageBox } from "@/app/platform/utils/message"
import { getDictionaryTerm } from "@/app/platform/api/system/dictionary"

const containerTitle = {
	name: ["公司管理"],
	icon: ["fas", "square-share-nodes"]
}
const companyContainerBtn = [
	{
		name: "新增公司",
		roles: "system:company:btn:add",
		icon: ["fas", "square-plus"]
	}
]
const startEndLoading = ref<boolean>(false)
const startEndBtn = ref([
	{
		name: "启用",
		roles: "system:company:btn:enable",
		icon: ["fas", "power-off"],
		disabled: true
	},
	{
		name: "停用",
		roles: "system:company:btn:disable",
		icon: ["fas", "circle-minus"],
		disabled: true
	}
])

const queryArrList = ref<any[]>([
	{
		name: "公司名称",
		key: "companyName",
		placeholder: "请输入查询关键字",
		enableFuzzy: false,
		type: "input"
	},
	{
		name: "公司编码",
		key: "companyCode",
		placeholder: "请输入查询关键字",
		enableFuzzy: false,
		type: "input"
	},
	{
		name: "公司状态",
		key: "companyStatus",
		placeholder: "请选择",
		enableFuzzy: false,
		type: "select",
		children: [
			{
				label: "已停用",
				value: 0
			},
			{
				label: "已启用",
				value: 1
			}
		]
	}
])

const usePaginationStore = usePagination()
const usePaginationStoreForData = usePaginationStore.paginationData

const drawerSize = 1230
const tableColumnType: TableColumnType[] = [
	{ label: "公司编码", prop: "companyCode", width: 85 },
	{ label: "公司名称", prop: "companyName", width: 130 },
	{ label: "公司简称", prop: "companyShortName", width: 95 },
	{ label: "公司类型", prop: "companyType", needSlot: true, width: 120 },
	{ label: "管辖线路", prop: "lines", needSlot: true, minWidth: 120 },
	{ label: "关联专业", prop: "majors", needSlot: true, width: 120 },
	{ label: "关联部门", prop: "org", needSlot: true, width: 120 },
	{ label: "状态", prop: "companyStatus", needSlot: true, width: 105 },
	{ label: "更新时间", prop: "lastModifiedDate", width: 160 },
	{
		label: "操作",
		prop: "operations",
		fixed: "right",
		needSlot: true,
		width: 150
	}
]
const tableData = ref<any[]>([])
const lineTableData = ref<any[]>([])
const showDrawer = ref<boolean>(false)
const tableLoading = ref<boolean>(false)
const pageSize = ref<number>(usePaginationStoreForData.pageSize)
const pageTotal = ref<number>(0)
const currentPage = ref<number>(usePaginationStoreForData.currentPage)
const editCompanyId = ref<any>("")
const lineTableRef = ref<any>()

/**
 * 关联专业
 */
const relatedMajorDrawerSize = ref(620)
const showRelatedMajorDrawer = ref(false)
const relevanceMajor = {
	name: ["关联专业"],
	icon: ["fas", "square-share-nodes"]
}
const relatedMajorTableColumn: TableColumnType[] = [
	{ label: "专业", prop: "name", align: "left", class: "tree-cell-flex" },
	{
		label: "专业编码",
		prop: "code",
		width: 120
	}
]
const relatedMajorDrawerTableData = ref<any[]>([])

const relatedOrgDrawerSize = ref(620)
const showRelatedOrgDrawer = ref(false)
const relevanceOrg = {
	name: ["关联部门"],
	icon: ["fas", "square-share-nodes"]
}
const relatedOrgTableColumn: TableColumnType[] = [
	{
		label: "部门",
		prop: "name",
		align: "left",
		class: "tree-cell-flex",
		minWidth: 100
	},
	{
		label: "部门编码",
		prop: "code"
	}
]
const relatedOrgDrawerTableData = ref<any[]>([])

const onBtnClick = () => {
	showDrawer.value = true
}
// table操作
const onRowEdit = (row: any) => {
	editCompanyId.value = row.id
	showDrawer.value = true
}
const onRowDelete = (row: any) => {
	CustomMessageBox({ message: "确定要移除吗？" }, (res: boolean) => {
		if (res) {
			CompanyApi.deleteCompany(row.id).then(() => {
				ElMessage.success("移除成功")
				getTableData()
			})
		}
	})
}
// 获取表格
const getTableData = () => {
	tableLoading.value = true
	CompanyApi.getCompanyList(
		objectToFormData({
			currentPage: currentPage.value,
			pageSize: pageSize.value,
			...queryData.value
		})
	)
		.then((res: any) => {
			pageTotal.value = res.records
			tableData.value = res.rows ?? []
			lineTableRef.value.clearSelectedTableData()
		})
		.finally(() => {
			tableLoading.value = false
		})
}
// 分页
const onCurrentPageChange = (pageData: any) => {
	pageSize.value = pageData.pageSize
	currentPage.value = pageData.currentPage
	getTableData()
}
// 弹窗关闭
const onCloseDrawer = (key: string) => {
	if (key === "success") {
		getTableData()
	}
	showDrawer.value = false
}
// 启用停用
const onStartEndBtn = (btnName: any) => {
	if (startEndLoading.value) return
	const selectNodes = lineTableRef.value.pitayaTableRef.getSelectionRows()
	const selectIds = selectNodes[0].id
	const flag = btnName === "启用" ? 1 : 0
	if (flag === selectNodes[0].companyStatus) {
		ElMessage.error(`该公司已${btnName}`)
		return
	}
	startEndLoading.value = true
	CompanyApi.startOrEndCompany(selectIds, flag)
		.then(() => {
			ElMessage.success(`${btnName}成功`)
			getTableData()
		})
		.finally(() => {
			startEndLoading.value = false
		})
}

// 关联专业详情
const getRelatedMajorData = (data: any) => {
	let list = XEUtils.sortBy(data.majors, (item) => item.id)
	relatedMajorDrawerTableData.value = XEUtils.toArrayTree(list, {
		parentKey: "pid"
	})
	showRelatedMajorDrawer.value = true
}

// 关联部门详情
const getRelatedOrgData = (data: any) => {
	let list = XEUtils.sortBy(data.organizations, (item) => item.id)
	relatedOrgDrawerTableData.value = XEUtils.toArrayTree(list, {
		parentKey: "pid"
	})
	showRelatedOrgDrawer.value = true
}

const companyTypeOptions = ref([])
const _getCompanyType = (type) => {
	let data = ""
	companyTypeOptions.value.map((item) => {
		if (item.subitemValue == type) {
			data = item?.subitemName
		}
	})
	return data
}

// 获取公司类型字典
const getCompanyTypeSelects = () => {
	getDictionaryTerm({ dataDictionaryCode: "COMPANY_TYPE" }).then((res: any) => {
		companyTypeOptions.value = res
	})
}
getCompanyTypeSelects()

const queryData = ref<any>({})
const getQueryData = (queryParams?: any) => {
	queryData.value.companyCode = queryParams.companyCode
		? queryParams.companyCode
		: ""
	queryData.value.companyName = queryParams.companyName
		? queryParams.companyName
		: ""
	queryData.value.companyStatus =
		queryParams.companyStatus || queryParams.companyStatus == 0
			? queryParams.companyStatus
			: ""
	getTableData()
}

watch(
	() => showDrawer.value,
	(val: boolean) => {
		if (!val) {
			editCompanyId.value = ""
		}
	},
	{ immediate: true }
)

watchEffect(() => {
	if (lineTableRef.value?.pitayaTableRef) {
		const selectNodes = lineTableRef.value?.pitayaTableRef.getSelectionRows()
		if (selectNodes.length) {
			if (selectNodes[0].companyStatus === 1) {
				startEndBtn.value[0].disabled = true
				startEndBtn.value[1].disabled = false
			} else if (selectNodes[0].companyStatus === 0) {
				startEndBtn.value[0].disabled = false
				startEndBtn.value[1].disabled = true
			}
		} else {
			startEndBtn.value[0].disabled = true
			startEndBtn.value[1].disabled = true
		}
	}
})

onMounted(() => {
	getTableData()
})

defineOptions({
	name: "CompanyManagement"
})
</script>
<template>
	<div class="app-container">
		<ModelFrame>
			<Query
				class="ml10"
				:numInRow="4"
				:queryBtnColSpan="6"
				:queryArrList="queryArrList"
				@getQueryData="getQueryData"
			/>
		</ModelFrame>
		<ModelFrame class="content">
			<Title
				:title="containerTitle"
				:button="companyContainerBtn"
				@onBtnClick="onBtnClick"
			/>
			<el-scrollbar>
				<PitayaTable
					ref="lineTableRef"
					:columns="tableColumnType"
					:tableData="tableData"
					:total="pageTotal"
					:single-select="true"
					:need-selection="true"
					@on-current-page-change="onCurrentPageChange"
					:table-loading="tableLoading"
				>
					<template #companyType="{ rowData }">
						<div>{{ _getCompanyType(rowData.companyType) }}</div>
					</template>
					<template #lines="{ rowData }">
						<div class="jurisdiction-line-container">
							<el-tag
								class="line-item"
								v-for="(item, index) in rowData.lines"
								:key="index"
								:style="{
									background: item.colour,
									color: '#FFF',
									backgroundColor: rowData.colour,
									borderColor: rowData.colour
								}"
								>{{ item.name ?? "---" }}</el-tag
							>
							<div v-if="rowData.lines.length === 0">---</div>
						</div>
					</template>
					<template #majors="{ rowData }">
						<div
							class="border-bottom-text"
							@click="getRelatedMajorData(rowData)"
						>
							{{ rowData.majors.length || 0 }}
						</div>
					</template>

					<template #org="{ rowData }">
						<div class="border-bottom-text" @click="getRelatedOrgData(rowData)">
							{{ rowData.organizations.length || 0 }}
						</div>
					</template>

					<template #companyStatus="{ rowData }">
						<el-tag
							:class="[
								'state-txt',
								rowData.companyStatus === 1
									? 'status-item-use'
									: 'status-item-stop'
							]"
							>{{ rowData.companyStatus === 1 ? "已启用" : "已停用" }}</el-tag
						>
					</template>
					<template #operations="{ rowData }">
						<el-button
							v-btn
							link
							@click="onRowEdit(rowData)"
							:disabled="checkPermission('system:company:btn:edit')"
							v-if="isCheckPermission('system:company:btn:edit')"
						>
							<font-awesome-icon
								:icon="['fas', 'pen-to-square']"
								:class="
									checkPermission('system:company:btn:edit') ? 'disabled' : ''
								"
								style="color: var(--pitaya-btn-background)"
							/>
							<span
								class="table-inner-btn"
								:class="
									checkPermission('system:company:btn:edit') ? 'disabled' : ''
								"
								>编辑</span
							>
						</el-button>
						<el-button
							v-btn
							link
							@click="onRowDelete(rowData)"
							:disabled="checkPermission('system:company:btn:delete')"
							v-if="isCheckPermission('system:company:btn:delete')"
						>
							<font-awesome-icon
								:icon="['fas', 'trash-can']"
								:class="
									checkPermission('system:company:btn:delete') ? 'disabled' : ''
								"
								style="color: var(--pitaya-btn-background)"
							/>
							<span
								class="table-inner-btn"
								:class="
									checkPermission('system:company:btn:delete') ? 'disabled' : ''
								"
								>移除</span
							>
						</el-button>
						<div
							v-if="
								!isCheckPermission('system:company:btn:edit') &&
								!isCheckPermission('system:company:btn:delete')
							"
						>
							---
						</div>
					</template>
					<template #footerOperateLeft>
						<ButtonList
							class="btn-list"
							:is-not-radius="true"
							:button="startEndBtn"
							:loading="startEndLoading"
							@on-btn-click="onStartEndBtn"
						/>
					</template>
				</PitayaTable>
			</el-scrollbar>
		</ModelFrame>
		<!-- 新增公司弹窗 -->
		<Drawer
			:size="drawerSize"
			v-model:drawer="showDrawer"
			:destroyOnClose="true"
		>
			<CompanyDrawer
				:id="editCompanyId"
				:table-data="lineTableData"
				@on-save-or-close="onCloseDrawer"
			/>
		</Drawer>
		<!-- 关联专业 -->
		<Drawer
			:size="relatedMajorDrawerSize"
			v-model:drawer="showRelatedMajorDrawer"
		>
			<Title :title="relevanceMajor" />
			<PitayaTable
				:columns="relatedMajorTableColumn"
				:table-data="relatedMajorDrawerTableData"
				:need-pagination="false"
				:need-index="true"
			/>
		</Drawer>
		<!-- 关联部门 -->
		<Drawer :size="relatedOrgDrawerSize" v-model:drawer="showRelatedOrgDrawer">
			<Title :title="relevanceOrg" />
			<PitayaTable
				:columns="relatedOrgTableColumn"
				:table-data="relatedOrgDrawerTableData"
				:need-pagination="false"
				:need-index="true"
			/>
		</Drawer>
	</div>
</template>
<style lang="scss" scoped>
.app-container {
	::v-deep(.model-frame-wrapper) {
		display: flex;
		flex-direction: column;
		// height: 100%;
	}
	.app-el-scrollbar-wrapper {
		flex: 1;
		height: 0;
	}

	.jurisdiction-line-container {
		display: flex;
		align-items: center;
		overflow: hidden;
		.line-item {
			margin-right: 10px;
			height: 22px;
			display: inline-table;
			align-items: center;
			justify-content: center;
			padding: 0 10px;
			border-radius: 3px;
			font-size: var(--pitaya-fs-12);
			color: #fff;
			&:last-child {
				margin-right: 0;
			}
		}
	}

	.status-item-use {
		color: #00942a;
		background-color: #e0ffef;
		border: 1px solid #00942a;
	}
	.status-item-stop {
		color: #ff0000;
		background-color: #fff2f1;
		border: 1px solid #ff0000;
	}

	.table-inner-btn {
		margin-left: 5px;
		color: var(--pitaya-btn-background);
	}

	.border-bottom-text {
		position: relative;
		color: #204a9c;
		text-decoration: underline;
		cursor: pointer;
	}
}
</style>
@/app/platform/hooks/usePagination @/app/platform/api/system/company
