<script lang="ts" setup>
import { ElMessage } from "element-plus"
import type { FormInstance, FormRules } from "element-plus"
import { usePagination } from "@/app/platform/hooks/usePagination"
import {
	getAuthList,
	changeAuth,
	batchRelease,
	setLogoutPDA
} from "@/app/platform/api/system/auth"
import { getDictionaryTerm } from "@/app/platform/api/system/dictionary"
import DeviceUserLogDrawer from "@/app/platform/views/system/components/deviceUserLogDrawer.vue"
import {
	checkPermission,
	isCheckPermission
} from "@/app/platform/utils/permission"
import { baseUserApi } from "@/app/platform/api/system/baseUser"
import { DepartmentApi } from "@/app/platform/api/system/department"

interface queryObj {
	/**
	 * 在线状态 0:离线 1:在线
	 */
	onLine?: string
	/**
	 * 授权状态 0:待启用 1:已启用 2:已停用
	 */
	authorizationStatus?: string
	/**
	 * 终端型号
	 */
	deviceModel?: string
	/**
	 * 使用部门
	 */
	organizationName?: string
	/**
	 * 使用人
	 */
	userName?: string
	/**
	 * 页码
	 */
	currentPage?: number
	/**
	 * 每页条数
	 */
	pageSize?: number
	/**
	 * 排序名称
	 */
	sidx?: string
	/**
	 * 排序字段
	 */
	sord?: string
}

const tableLoading = ref(false)
const drawerLoading = ref(false)

const usePaginationStore = usePagination()
const paginationData = ref<PaginationData>(usePaginationStore.paginationData)

const paramObj = ref<queryObj>({
	onLine: "",
	authorizationStatus: "",
	deviceModel: "",
	organizationName: "",
	userName: "",
	currentPage: paginationData.value.currentPage,
	pageSize: paginationData.value.pageSize,
	sidx: "",
	sord: ""
})

const getTableList = (pd: PaginationData) => {
	paramObj.value.currentPage = pd.currentPage
	paramObj.value.pageSize = pd.pageSize
	AuthListInit()
}

const getQueryData = (queryData: queryObj) => {
	paramObj.value.currentPage = 1
	PitayaTableRef.value.resetCurrentPage()
	paramObj.value = Object.assign(paramObj.value, queryData)
	AuthListInit()
}

const AuthListInit = () => {
	tableLoading.value = true
	getAuthList(paramObj.value)
		.then((res: any) => {
			if (res.rows && res.rows.length) {
				baseTableData.value = res.rows
				dataTotal.value = res.records
				PitayaTableRef.value.clearSelectedTableData()
			} else {
				baseTableData.value = []
				dataTotal.value = 0
			}
		})
		.finally(() => {
			tableLoading.value = false
		})
}

onMounted(() => {
	AuthListInit()
	getDictionaryTerm({
		dataDictionaryCode: "DEVICE_MODEL"
	}).then((res: any) => {
		if (res && res.length) {
			const children = [
				{
					label: "全部",
					value: ""
				}
			]
			res.forEach((item: any) => {
				children.push({
					label: item.subitemName,
					value: item.subitemValue
				})
			})
			queryArrList.value[1].children = children
		}
	})
})

const queryArrList = ref([
	{
		name: "在线状态",
		key: "onLine",
		type: "select",
		placeholder: "请选择",
		children: [
			{
				label: "全部",
				value: ""
			},
			{
				label: "在线",
				value: "1"
			},
			{
				label: "离线",
				value: "0"
			}
		]
	},
	{
		name: "终端序列号",
		key: "deviceSeq",
		type: "input",
		enableFuzzy: false,
		placeholder: "请输入查询关键字"
	},
	{
		name: "所属公司",
		key: "companyId",
		placeholder: "请选择",
		enableFuzzy: false,
		type: "select",
		children: []
	},
	// {
	// 	name: "部门",
	// 	key: "organizationName",
	// 	type: "input",
	// 	enableFuzzy: false,
	// 	placeholder: "请输入查询关键字"
	// },
	{
		name: "部门",
		key: "organizationName",
		type: "treeSelect",
		enableFuzzy: false,
		treeApi: DepartmentApi.getDepartmentTreeWithRole,
		replaceIdTo: "id",
		placeholder: "请选择部门"
	},
	{
		name: "用户",
		key: "userName",
		type: "input",
		enableFuzzy: false,
		placeholder: "请输入查询关键字"
	},
	{
		name: "授权状态",
		key: "authorizationStatus",
		type: "select",
		placeholder: "请选择",
		children: [
			{
				label: "全部",
				value: ""
			},
			{
				label: "待启用",
				value: "0"
			},
			{
				label: "已启用",
				value: "1"
			},
			{
				label: "已停用",
				value: "2"
			}
		]
	},
	{
		name: "终端型号",
		key: "deviceModel",
		type: "input",
		enableFuzzy: false,
		placeholder: "请选择"
	},
	{
		name: "备注",
		key: "notes",
		type: "input",
		enableFuzzy: false,
		placeholder: "请输入查询关键字"
	}
])

const title = {
	name: ["终端授权"],
	icon: ["fas", "square-share-nodes"]
}

const drawerTitle = {
	name: ["设备信息"],
	icon: ["fas", "square-share-nodes"]
}

const drawerState = ref(false)
const drawerSize = 310
const userLogDrawerStatus = ref(false)
const userLogRef = ref<any>()
const userLogDrawerSize = 920

const dataTotal = ref(0)
const baseTableData = ref<any[]>([])
const baseColumns = ref<TableColumnType[]>([
	{ prop: "onLine", label: "在线状态", needSlot: true, width: 90 },
	{ prop: "deviceSeq", label: "终端设备序列号", minWidth: 200 },
	{ prop: "deviceModel", label: "终端型号", minWidth: 100 },
	// { prop: "deviceNetworkCardNo", label: "4G网卡号", width: 120 },
	{
		prop: "deviceVersion",
		label: "当前应用版本号",
		width: 140,
		sortable: "custom"
	},
	{ prop: "companyId_view", label: "公司", width: 100 },
	{ prop: "userName_view", label: "上一次登录用户", width: 120 },
	{ prop: "organizationName_view", label: "上一次登录部门", minWidth: 200 },
	{
		prop: "usageDuration",
		label: "最近30天使用时长",
		needSlot: true,
		minWidth: 200,
		sortable: "custom"
	},
	{ prop: "notes", label: "备注", width: 200 },
	{
		prop: "authorizationStatus",
		label: "授权状态",
		needSlot: true,
		width: 100
	},
	{
		prop: "operations",
		fixed: "right",
		label: "操作",
		needSlot: true,
		width: 80
	}
])

const selectionTableList = ref<any[]>([])

const getSelectionTableList = (rowList: any) => {
	selectionTableList.value = rowList
}
/**
 *
 *
 * 列表排序
 *
 * ***/
const onTableSortChange = (column: any, prop: any, order: any) => {
	paramObj.value.sidx = prop
	paramObj.value.sord = order == "ascending" ? "asc" : "desc"
	AuthListInit()
}

const baseFormData = reactive<anyKey>({
	id: "",
	deviceVersion: "",
	organizationName: "",
	deviceModel: "",
	deviceSeq: "",
	userName: "",
	notes: "",
	onLine: "",
	authorizationStatus: "",
	companyId: ""
})

const onEdit = (rowData: anyKey) => {
	Object.keys(baseFormData).forEach((key) => {
		baseFormData[key] = rowData[key] ? rowData[key] : ""
	})
	drawerState.value = true
}

const baseFormRef = ref<FormInstance>()
	const rules = reactive<FormRules<typeof baseFormData>>({
		companyId: [{ required: true, message: "请选择所属公司", trigger: "blur" }]
})
const onFormBtnClick = (btnName: string | undefined) => {
	if (!baseFormRef.value) return
	if (btnName === "保存") {
		baseFormRef.value.validate((valid) => {
			if (valid) {
				drawerLoading.value = true
				changeAuth(baseFormData)
					.then(() => {
						AuthListInit()
						drawerState.value = false
						ElMessage.success("操作成功")
					})
					.finally(() => {
						drawerLoading.value = false
					})
			} else {
				return false
			}
		})
		return
	}

	if (btnName === "取消") {
		drawerState.value = false
		return
	}
}

const onClose = () => {
	Object.keys(baseFormData).map((key) => (baseFormData[key] = ""))
	baseFormRef.value?.clearValidate()
}

const PitayaTableRef = ref()

function checkState(
	selected: anyKey[] | Ref<anyKey[]>,
	state: string | number
) {
	selected = unref(selected)
	state = +state

	if (selected.length === 1) {
		if (state == 1) {
			if (selectionTableList.value[0].authorizationStatus == "1") {
				ElMessage.error("该终端已启用")
				return false
			}
		}
		if (state == 2) {
			if (selectionTableList.value[0].authorizationStatus == "2") {
				ElMessage.error("该终端已停用")
				return false
			}
		}
	}

	if (selected.length > 1) {
		if (state == 1) {
			if (
				selectionTableList.value.some((item) => item.authorizationStatus == "1")
			) {
				ElMessage.error("存在已启用终端")
				return false
			}
		}
		if (state == 2) {
			if (
				selectionTableList.value.some((item) => item.authorizationStatus == "2")
			) {
				ElMessage.error("存在已停用终端")
				return false
			}
		}
	}

	return true
}
const onPaginationBtnClick = (btnName: string | undefined) => {
	const sledTlId: anyKey[] = []
	if (selectionTableList.value && selectionTableList.value.length) {
		selectionTableList.value.forEach((item: any) => {
			sledTlId.push(item.id)
		})
	}
	let authorizationStatus = "0"
	if (btnName === "启用") {
		if (!sledTlId.length) {
			ElMessage({
				message: "请选择需要启用的终端信息！",
				type: "warning"
			})
			return
		}
		authorizationStatus = "1"
		batchStatusUpdate(sledTlId, authorizationStatus)
	}
	if (btnName === "停用") {
		if (!sledTlId.length) {
			ElMessage({
				message: "请选择需要停用的终端信息！",
				type: "warning"
			})
			return
		}
		authorizationStatus = "2"
		batchStatusUpdate(sledTlId, authorizationStatus)
	}
	if (btnName === "使用日志") {
		if (!sledTlId.length || sledTlId.length != 1) {
			ElMessage({
				message: "请选择一条需要查看的终端信息！",
				type: "warning"
			})
			return
		}
		userLogDrawerStatus.value = true
		nextTick(() => {
			userLogRef.value?.init(sledTlId[0])
		})
	}

	if (btnName === "强制下线") {
		if (!sledTlId.length) {
			ElMessage({
				message: "请选择一条需要强制下线的终端信息！",
				type: "warning"
			})
			return
		}
		ElMessageBox.confirm(
			`您确定要强制下线此PDA用户吗？这将立即使用户从PDA端登出。`,
			"确认强制下线",
			{
				confirmButtonText: "确定",
				cancelButtonText: "取消",
				autofocus: false,
				type: "warning"
			}
		).then(() => {
			setLogoutPDA({ ids: sledTlId.join(",") }).then((res: any) => {
				AuthListInit()
				ElMessage({
					message: res.data,
					type: "success"
				})
			})
		})
	}
}

const batchStatusUpdate = (sledTlId: anyKey[], authorizationStatus: string) => {
	// 状态校验
	if (!checkState(sledTlId, authorizationStatus)) return false
	if (startEndLoading.value) return
	startEndLoading.value = true
	batchRelease({
		ids: sledTlId.join(","),
		authorizationStatus
	})
		.then(() => {
			AuthListInit()
			ElMessage.success(`${authorizationStatus === "1" ? "启用" : "停用"}成功`)
			PitayaTableRef.value?.clearSelectedTableData()
		})
		.finally(() => {
			startEndLoading.value = false
		})
}

const onBtnClick = (e: string | undefined) => {
	Object.keys(baseFormData).map((key) => (baseFormData[key] = ""))
	baseFormRef.value?.clearValidate()
	drawerState.value = true
}

const btnList = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "保存",
		icon: ["fas", "floppy-disk"]
	}
]
const startEndLoading = ref<boolean>(false)
const paginationBtnList = ref([
	{
		name: "启用",
		icon: ["fas", "power-off"],
		roles: "system:device:auth:btn:enable",
		disabled: true
	},
	{
		name: "停用",
		roles: "system:device:auth:btn:disable",
		icon: ["fas", "circle-stop"],
		disabled: true
	},
	{
		name: "使用日志",
		roles: "system:device:auth:btn:logs",
		icon: ["fas", "user"],
		disabled: true
	},
	{
		name: "强制下线",
		roles: "system:device:auth:btn:Offline",
		icon: ["fas", "copy"],
		disabled: true
	}
])

const getDicMatchTxt = (code: string, val: string) => {
	let MTxt = ""
	const Mdic = queryArrList.value.filter((item: any) => {
		return item.code === code
	})
	if (Mdic && Mdic.length && Mdic[0].children && Mdic[0].children.length) {
		const MItem = Mdic[0].children.filter((citem: any) => {
			return citem.value === val
		})
		if (MItem && MItem.length) {
			MTxt = MItem[0].label
		}
	}
	return MTxt
}

// #region 获取公司列表相关
const companyList = ref<any[]>([])
// 获取全部公司列表
function getAllCompany() {
	baseUserApi
		.getAllCompany()
		.then((res: any) => {
			if (res && res.length > 0) {
				companyList.value = res.map((item: anyKey) => {
					return {
						label: item.companyName,
						value: item.id,
						companyStatus: item.companyStatus
					}
				})
				queryArrList.value[2].children = companyList.value
			} else {
				companyList.value = []
			}
		})
		.catch((err) => {
			throw new Error("getAllCompany():::" + err)
		})
}
getAllCompany()

const convertSecondsToTime = (milliseconds: number) => {
	if (milliseconds) {
		// 将毫秒转换成秒
		const totalSeconds = Math.floor(milliseconds / 1000)

		const days = Math.floor(totalSeconds / (24 * 60 * 60))
		const remainingSecondsForHoursAndMinutes = totalSeconds % (24 * 60 * 60)

		const hours = Math.floor(remainingSecondsForHoursAndMinutes / (60 * 60))
		const remainingSecondsForMinutes =
			remainingSecondsForHoursAndMinutes % (60 * 60)

		// 判断是否有额外的秒数需要加到分钟上
		let minutes = Math.floor(remainingSecondsForMinutes / 60)
		const seconds = remainingSecondsForMinutes % 60
		if (seconds > 0 && seconds < 59) {
			minutes += 1 // 将有秒数的那一分钟视为“+1 分钟”
		}

		// 构建时间字符串
		const timeString = `${days}天 ${hours}小时 ${minutes}分钟`
		return timeString
	}
}

watchEffect(() => {
	if (selectionTableList.value.length) {
		// 使用日志
		if (selectionTableList.value.length === 1) {
			paginationBtnList.value[2].disabled = false
		} else {
			paginationBtnList.value[2].disabled = true
		}
		// 启用
		if (
			selectionTableList.value.some((item) => item.authorizationStatus === "1")
		) {
			paginationBtnList.value[0].disabled = true
		} else {
			paginationBtnList.value[0].disabled = false
		}
		// 停用
		if (
			selectionTableList.value.some((item) => item.authorizationStatus === "2")
		) {
			paginationBtnList.value[1].disabled = true
		} else {
			paginationBtnList.value[1].disabled = false
		}
		// 强制下线
		if (
			selectionTableList.value.some((item) => item.authorizationStatus === "3")
		) {
			paginationBtnList.value[3].disabled = true
		} else {
			paginationBtnList.value[3].disabled = false
		}
	} else {
		paginationBtnList.value[0].disabled = true
		paginationBtnList.value[1].disabled = true
		paginationBtnList.value[2].disabled = true
		paginationBtnList.value[3].disabled = true
	}
})
</script>

<template>
	<div class="app-container">
		<ModelFrame>
			<Query
				class="ml10"
				:queryArrList="queryArrList"
				@getQueryData="getQueryData"
			/>
		</ModelFrame>
		<div class="app-content-wrapper">
			<div class="app-content-group">
				<ModelFrame>
					<Title :title="title" @onBtnClick="onBtnClick" />
					<div class="app-el-scrollbar-wrapper">
						<el-scrollbar>
							<PitayaTable
								ref="PitayaTableRef"
								@onTableSortChange="onTableSortChange"
								@onSelectionChange="getSelectionTableList"
								@onCurrentPageChange="getTableList"
								:table-data="baseTableData"
								:columns="baseColumns"
								:needSelection="true"
								:total="dataTotal"
								:table-loading="tableLoading"
							>
								<template #onLine="{ rowData }">
									<el-tag :class="'state' + rowData.onLine">
										{{ rowData.onLine === "0" ? "离线" : "在线" }}
									</el-tag>
								</template>
								<template #usageDuration="{ rowData }">
									<span>{{
										convertSecondsToTime(rowData.usageDuration) || "---"
									}}</span>
								</template>
								<template #authorizationStatus="{ rowData }">
									<el-tag :class="'authState' + rowData.authorizationStatus">
										{{
											rowData.authorizationStatus === "0"
												? "待启用"
												: rowData.authorizationStatus === "1"
												? "已启用"
												: "已停用"
										}}
									</el-tag>
								</template>
								<template #operations="{ rowData }">
									<el-button
										v-btn
										link
										@click="onEdit(rowData)"
										:disabled="checkPermission('system:device:auth:btn:edit')"
										v-if="isCheckPermission('system:device:auth:btn:edit')"
									>
										<font-awesome-icon
											:icon="['fas', 'pen-to-square']"
											:class="
												checkPermission('system:device:auth:btn:edit')
													? 'disabled'
													: ''
											"
											style="color: var(--pitaya-btn-background)"
										/>
										<span
											class="table-inner-btn"
											:class="
												checkPermission('system:device:auth:btn:edit')
													? 'disabled'
													: ''
											"
											>编辑</span
										>
									</el-button>
									<div v-else>---</div>
								</template>
								<template #footerOperateLeft>
									<ButtonList
										:button="paginationBtnList"
										:isNotRadius="true"
										:loading="startEndLoading"
										@onBtnClick="onPaginationBtnClick"
									/>
								</template>
							</PitayaTable>
						</el-scrollbar>
					</div>
				</ModelFrame>
			</div>
		</div>
		<Drawer :size="drawerSize" v-model:drawer="drawerState" @close="onClose">
			<div class="common-from-wrapper" v-loading="drawerLoading">
				<Title :title="drawerTitle" />
				<div class="common-from-group">
					<el-scrollbar>
						<el-form
							class="el-form-wrapper"
							ref="baseFormRef"
							:model="baseFormData"
							:rules="rules"
							label-width="auto"
							label-position="top"
						>
							<el-form-item label="终端设备序列号">
								<el-input v-model="baseFormData.deviceSeq" disabled />
							</el-form-item>
							<el-form-item label="终端型号">
								<el-input v-model="baseFormData.deviceModel" disabled />
							</el-form-item>
							<el-form-item label="所属公司" prop="companyId" required>
								<el-select
									filterable
									style="width: 100%"
									v-model="baseFormData.companyId"
									clearable
								>
									<el-option
										v-for="item in companyList"
										:key="item.value"
										:label="`${item.label} ${
											item.companyStatus == 0 ? '（停用）' : ''
										}`"
										:value="item.value"
										:disabled="item.companyStatus === 0"
									/>
								</el-select>
							</el-form-item>
							<el-form-item label="备注信息">
								<el-input
									maxlength="200"
									:show-word-limit="true"
									v-model="baseFormData.notes"
									type="textarea"
									:autosize="{ minRows: 8 }"
									resize="none"
								/>
							</el-form-item>
						</el-form>
					</el-scrollbar>
				</div>
				<div class="btn-groups">
					<ButtonList :button="btnList" @onBtnClick="onFormBtnClick" />
				</div>
			</div>
		</Drawer>
		<Drawer :size="userLogDrawerSize" v-model:drawer="userLogDrawerStatus">
			<DeviceUserLogDrawer ref="userLogRef" />
		</Drawer>
	</div>
</template>

<style lang="scss" scoped>
.app-container {
	:deep(.model-frame-wrapper) {
		padding-bottom: 0px;
	}
}
.app-content-wrapper {
	height: 0;
	flex: 1;
	margin-top: 10px;
	.app-content-group {
		height: 100%;
		:deep(.model-frame-wrapper) {
			height: 100%;
			padding-bottom: 10px;
			overflow: hidden;
			display: flex;
			flex-direction: column;
		}
		.app-el-scrollbar-wrapper {
			height: 0;
			flex: 1;
			.table-inner-btn {
				margin-left: 5px;
				color: #204a9c;
			}
		}
	}
}
.common-from-wrapper {
	height: 100%;
	width: 100%;
	display: flex;
	flex-direction: column;
	.common-from-group {
		height: 0;
		flex: 1;
		padding: 0 10px;
	}
	.btn-groups {
		display: flex;
		justify-content: flex-end;
		padding: 10px 10px 0 0;
		border-top: 1px solid var(--pitaya-border-color);
	}
}

.state0 {
	color: #999999;
	border-color: #d0d0d0;
	background-color: #f5f7fb;
}
.state1 {
	color: #ffffff;
	border-color: #4bae89;
	background-color: #4bae89;
}
.authState0 {
	color: #f59b22;
	border-color: #f59b22;
	background-color: #fcf3e6;
}
.authState1 {
	color: #4bae89;
	border-color: #4bae89;
	background-color: #e0ffef;
}
.authState2 {
	color: #e25e59;
	border-color: #e25e59;
	background-color: #f9e7e7;
}

.file-view-wrapper {
	display: flex;
	align-items: center;
	padding: 0 5px;
	font-size: var(--pitaya-fs-12);
	color: var(--pitaya-header-bg-color);
	.file-name-text {
		width: 0;
		flex: 1;
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
		padding: 0 5px;
	}
	.awesom-del-icon {
		display: none;
		cursor: pointer;
	}
	&:hover {
		.awesom-del-icon {
			display: block;
		}
	}
}
</style>
@/app/platform/hooks/usePagination
@/app/platform/api/system/auth@/app/platform/api/system/dictionary
