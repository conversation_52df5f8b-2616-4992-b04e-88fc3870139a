<script lang="ts" setup>
import { ref, reactive, watchEffect, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import type { TableColumnType } from '@/app/platform/types'
import type { anyKey } from '@/app/platform/types'
import {
	checkPermission,
	isCheckPermission
} from "@/app/platform/utils/permission"
import AddNotice from "./components/addNotice.vue"
import NoticeDetail from "@/app/platform/components/NoticeDetail.vue"
import { CompanyApi } from "@/app/platform/api/system/notice"
import { usePagination } from "@/app/platform/hooks/usePagination"
import { getDictionaryTerm } from "@/app/platform/api/system/dictionary"
import { ElMessage } from "element-plus"
import type { FormInstance, FormRules } from "element-plus"
import { CustomMessageBox } from "@/app/platform/utils/message"

const usePaginationStore = usePagination()
const usePaginationStoreForData = usePaginationStore.paginationData
const tableLoading = ref<boolean>(false)
const drawerDetail = ref(false)   // 详情弹窗
const drawerState = ref(false)
const curDataId = ref("")
const editModel = ref("edit")
const drawerLoading = ref(false)		// 弹窗loading
const tableBtnLoading = ref<boolean>(false)  // 发布加载
const pageSize = ref<number>(usePaginationStoreForData.pageSize)
const pageTotal = ref<number>(0)
const currentPage = ref<number>(usePaginationStoreForData.currentPage)
const queryData = ref<any>({})
const tableData = ref<any[]>([])
const drawerSize = 310
const lineTableRef = ref()
// 暂存表格选中数据
const selectedTableData = ref<any[]>([])
const startEndBtn = ref([
	{
		name: "发布",
		roles: "system:notice:btn:release",
		icon: ["fas", "check"],
		disabled: true
	}, {
		name: "下线",
		icon: ["fas", "down-long"],
		disabled: true,
		roles: 'system:notice:btn:offline'
	}
])
// 字典项获取，通知公告状态
const dictionaryList = ref<any[]>([
	{
		label: "发布状态",
		code: "NOTICE_STATUS",
		children: []
	}
])
const queryArrList = ref<any[]>([
	{
		name: "标题",
		key: "noticeTitle",
		placeholder: "请输入查询关键字",
		enableFuzzy: false,
		type: "input"
	},
	{
		name: "发布时间",
		key: "publishTime",
		placeholder: "请选择时间",
		enableFuzzy: false,
		type: "dateTime"
	},
	{
		name: "状态",
		key: "noticeStatus",
		placeholder: "请选择",
		code: 'NOTICE_STATUS',
		enableFuzzy: false,
		type: "select",
		children: []
	}
])
// 表格选中回调
const getSelectedTableData = (rowList: any[]) => {
	selectedTableData.value = rowList
}
const getQueryData = (querylist?: any) => {
	queryData.value = querylist
	currentPage.value = 1
	lineTableRef.value.resetCurrentPage()
	getTableData()
}
const containerTitle = {
	name: ["通知公告"],
	icon: ["fas", "square-share-nodes"]
}
const companyContainerBtn = [
	{
		name: "新增通知",
		roles: "system:notice:btn:add",
		icon: ["fas", "square-plus"]
	}
]
//新增按钮
function onCreate(btnName: string) {
	if (btnName == "新增通知") {
		editModel.value = "add"
		curDataId.value = ""
		drawerState.value = true
	}
}
const onRowEdit = (row: any) => {
	editModel.value = "edit"
	curDataId.value = row.id
	drawerState.value = true
}
//新增成功回调
function onCreateSuccess() {
	drawerState.value = false
	getTableData()
}
const tableColumnType: TableColumnType[] = [
	{ label: "标题", prop: "noticeTitle" },
	{ label: "状态", prop: "noticeStatus", needSlot: true, width: 105 },
	{ label: "创建人", prop: "createdByName", width: 130 },
	{ label: "发布时间", prop: "publishTime", width: 160 },
	{ label: "下线时间", prop: "endTime", width: 160 },
	{
		label: "操作",
		prop: "operations",
		fixed: "right",
		needSlot: true,
		width: 200
	}
]
// table操作
const onRowDelete = (row: any) => {
	CustomMessageBox({ message: "确定要移除吗？" }, (res: boolean) => {
		if (res) {
			CompanyApi.deleteCompany(row.id).then(() => {
				ElMessage.success("移除成功")
				getTableData()
			})
		}
	})
}
// 分页
const onCurrentPageChange = (pageData: any) => {
	pageSize.value = pageData.pageSize
	currentPage.value = pageData.currentPage
	getTableData()
}
// 获取表格
const getTableData = () => {
	tableLoading.value = true
	CompanyApi.getNoticeList(
		{
			currentPage: currentPage.value,
			pageSize: pageSize.value,
			...queryData.value
		}
	)
		.then((res: any) => {
			pageTotal.value = res.records
			tableData.value = res.rows ?? []
			// lineTableRef.value.clearSelectedTableData()
		})
		.finally(() => {
			tableLoading.value = false
		})
}
const getDictionaryList = () => {
	dictionaryList.value.forEach((dItem: any) => {
		getDictionaryTerm({
			dataDictionaryCode: dItem.code
		}).then((dItemList: any) => {
			if (dItemList && dItemList.length) {
				const children: Array<{label: string, value: string}> = []
				dItemList.forEach((item: any) => {
					children.push({
						label: item.subitemName,
						value: item.subitemValue
					})
				})
				dItem.children = children
				if (dItem.code === "NOTICE_STATUS") {
					queryArrList.value.forEach((item: any) => {
						if (item.code === "NOTICE_STATUS") {
							item.children = dItem.children
						}
					})
				}
			}
		})
	})
}

onMounted(() => {
	getTableData()
	getDictionaryList()
})
const getDicMatchTxt = (code: string, val: string) => {
	let MTxt = ""
	const Mdic = dictionaryList.value.filter((item: any) => {
		return item.code === code
	})
	if (Mdic && Mdic.length && Mdic[0].children && Mdic[0].children.length) {
		const MItem = Mdic[0].children.filter((citem: any) => {
			return citem.value === val
		})
		if (MItem && MItem.length) {
			MTxt = MItem[0].label
		}
	}
	return MTxt
}
// 发布
const getGroupStartUse = () => {
	tableLoading.value = true
	tableBtnLoading.value = true
	let params = {
		id: selectedTableData.value[0].id,
	}
	CompanyApi.getNoticePublish(params)
		.then((res: any) => {
			ElMessage.success("发布成功")
			lineTableRef.value.clearSelectedTableData()
			getTableData()
		})
		.finally(() => {
			tableLoading.value = false
			tableBtnLoading.value = false
		})
}
// 下线
const getGroupStopUse = () => {
	tableBtnLoading.value = true
	CustomMessageBox({ message: "确定要下线吗？" }, (res: boolean) => {
		if (res) {
			tableLoading.value = true
			let params = {
				id: selectedTableData.value[0].id,
			}
			CompanyApi.getNoticeOffline(params)
				.then((res: any) => {
					ElMessage.success("下线成功")
					lineTableRef.value.clearSelectedTableData()
					getTableData()
				})
				.finally(() => {
					tableLoading.value = false
					tableBtnLoading.value = false
				})
		} else {
			tableBtnLoading.value = false
		}
	})
}
// 表格底部按钮操作回调
const onTableBtn = (btnName?: string | undefined) => {
	switch (btnName) {
		case "发布":
			getGroupStartUse()
			break
		case "下线":
			getGroupStopUse()
			break
	}
}
// 点击查看详情关闭弹窗
function onBtnClick(btnName: string) {
	drawerDetail.value = false
}
// 查看详情
const currentData = ref<any>({})
const onPreview = async (data: anyKey) => {
	CompanyApi.getNoticeGet({
		id: data.id
	}).then((_r) => {
		console.log('===_r',_r)
		currentData.value = _r
	}).finally(() => {
		drawerDetail.value = true
	})
}
const route = useRoute()

// 实时监测路由参数变化
watchEffect(() => {
	if (route.query.showDialog === 'true' && route.query.noticeId) {
		onPreview({ id: route.query.noticeId })
	}
})
// 移除
function onRemove(data: any) {
	CustomMessageBox({ message: "确定要移除吗？" }, (res: boolean) => {
		if (res) {
			CompanyApi.noticeDelete({ id: String(data.id) }).then(res => {
				ElMessage.success('删除成功')
				getTableData()
			})
		} else {
			return false
		}
	})
}
/**
 * 钩子函数
 */

watchEffect(() => {
	// 启用/停用按钮控制
	if (selectedTableData.value.length) {
		if (
			selectedTableData.value[0].noticeStatus === 0
		) {
			//sts字段:  草稿：0，已发布：1，未发布：2 草稿和未发布,可以启用;
			startEndBtn.value[0].disabled = false
			startEndBtn.value[1].disabled = true
		} else if (selectedTableData.value[0].noticeStatus === 1) {
			startEndBtn.value[0].disabled = true
			startEndBtn.value[1].disabled = false
		}
	} else {
		startEndBtn.value[0].disabled = true
		startEndBtn.value[1].disabled = true
	}
})
</script>
<template>
	<div class="app-container">
		<ModelFrame>
			<Query class="ml10" :numInRow="4" :queryBtnColSpan="6" :queryArrList="queryArrList"
				@getQueryData="getQueryData" />
		</ModelFrame>
		<ModelFrame class="content">
			<Title :title="containerTitle" :button="companyContainerBtn" @onBtnClick="onCreate('新增通知')" />
			<el-scrollbar>
				<PitayaTable ref="lineTableRef" :columns="tableColumnType" :tableData="tableData" :total="pageTotal"
					:single-select="true" :need-selection="true" :selected-table-data="selectedTableData"
					@on-current-page-change="onCurrentPageChange" :table-loading="tableLoading"
					:disabledIds="tableData.filter(row => row.noticeStatus === 2).map(row => row.id)"
					@onSelectionChange="getSelectedTableData">
					<template #noticeStatus="{ rowData }">
						<el-tag v-if="
							getDicMatchTxt('NOTICE_STATUS', rowData.noticeStatus + '')
						" :class="'state' + rowData.noticeStatus">
							{{
								getDicMatchTxt("NOTICE_STATUS", rowData.noticeStatus + '')
							}}
						</el-tag>
						<span v-else>---</span>
					</template>
					<template #operations="{ rowData }">
						<el-button v-btn link @click="onRowEdit(rowData)"
							:disabled="checkPermission('system:notice:btn:edit')"
							v-if="isCheckPermission('system:notice:btn:edit') && !rowData.noticeStatus">
							<font-awesome-icon :icon="['fas', 'pen-to-square']" :class="checkPermission('system:notice:btn:edit') ? 'disabled' : ''
								" style="color: var(--pitaya-btn-background)" />
							<span class="table-inner-btn" :class="checkPermission('system:notice:btn:edit') ? 'disabled' : ''
								">编辑</span>
						</el-button>
						<el-button v-btn link
							v-if="isCheckPermission('system:notice:btn:remove') && !rowData.noticeStatus"
							class="btn-success" @click="onRemove(rowData)">
							<font-awesome-icon :icon="['fas', 'trash-can']" style="color: #204a9c"
								:class="checkPermission('system:notice:btn:remove') ? 'disabled' : ''" />
							<span class="table-inner-btn"
								:class="checkPermission('system:notice:btn:remove') ? 'disabled' : ''">移除</span>
						</el-button>
						<el-button v-btn link class="btn-success" @click="onPreview(rowData)">
							<font-awesome-icon :icon="['fas', 'eye']" style="color: #204a9c" />
							<span class="table-inner-btn">查看</span>
						</el-button>
					</template>
					<template #footerOperateLeft>
						<ButtonList class="btn-list" :is-not-radius="true" :button="startEndBtn"
							:loading="tableBtnLoading" @on-btn-click="onTableBtn" />
					</template>
				</PitayaTable>
			</el-scrollbar>
		</ModelFrame>
		<!-- 新建编辑弹窗 -->
		<Drawer class="common-from-wrapper" :destroy-on-close="true" v-model="drawerState" size="450" drawer
		@close="drawerState = false">
			<AddNotice v-if="drawerState" :dataId="curDataId" :model="editModel" @onClosed="drawerState = false"
				@onSuccess="onCreateSuccess" />
		</Drawer>
		<!-- 通知内容详情弹窗 -->
		<NoticeDetail v-model="drawerDetail" :notice-data="currentData" @close="drawerDetail = false" />
	</div>
</template>
<style lang="scss" scoped>
.app-container {}
.common-from-wrapper {
	height: 100%;
	width: 100%;
	display: flex;
	flex-direction: column;
	.common-from-group {
		height: 0;
		flex: 1;
		padding: 0 10px;
	}
	.btn-groups {
		display: flex;
		justify-content: flex-end;
		padding: 10px 10px 0 0;
		border-top: 1px solid var(--pitaya-border-color);
	}
}
.state0 {
	color: #e25e59;
	border-color: #e25e59;
	background-color: #f9e7e7;
}

.state1 {
	color: #4bae89;
	border-color: #4bae89;
	background-color: #e0ffef;
}

.state2 {
	color: #999999;
	border-color: #d0d0d0;
	background-color: #f5f7fb;
}
</style>
