<script lang="ts" setup>
import { ElMessage, type FormInstance, type FormRules } from "element-plus"
import { usePagination } from "@/app/platform/hooks/usePagination"
import {
	getLabelCodeListApi,
	getLabelCodeDetailApi,
	getLabelCodeDetailListApi,
	createLabelCodeApi,
	labelCodePrintApi
} from "@/app/platform/api/system/labelCode"
import { baseUserApi } from "@/app/platform/api/system/baseUser"
import {
	checkPermission,
	isCheckPermission
} from "@/app/platform/utils/permission"
import { storeToRefs } from "pinia"
import { useUserStore } from "@/app/platform/store/modules/user"
import Cookies from "js-cookie"
import CacheKey from "@/constants/cacheKey"

const userStore = useUserStore()
const { userInfo } = storeToRefs(userStore)

const usePaginationStore = usePagination()
const paginationData = ref<PaginationData>(usePaginationStore.paginationData)

const queryArrList = [
	{
		name: "申请时间",
		key: "startAndEndTime",
		type: "startAndEndTime"
	},
	{
		name: "打印状态",
		key: "status",
		type: "select",
		placeholder: "请选择",
		children: [
			{
				label: "已打印",
				value: "已打印"
			},
			{
				label: "未打印",
				value: "未打印"
			}
		]
	},
	{
		name: "申请人",
		key: "createdByName",
		type: "input",
		enableFuzzy: false,
		placeholder: "请输入查询关键字"
	},
	{
		name: "申请公司",
		key: "companyId",
		type: "select",
		placeholder: "请选择"
	}
]

const title = {
    name: ["标签管理"],
    icon: ["fas", "square-share-nodes"]
}

const drawerTitle = {
    name: ["新增标签"],
    icon: ["fas", "square-share-nodes"]
}

const tabList = [
    {
        name: "位置码",
        icon: ["fas", "square-plus"]
    },
    {
        name: "设备码",
        icon: ["fas", "square-plus"]
    }
]

const button = ref([
    {
        name: "新增位置码",
        icon: ["fas", "square-plus"],
        roles: "system:labelcode:btn:add"
    }
])

const currentId = ref("")
const activeName = ref("")
activeName.value = tabList[0].name

const tremFormData = ref<anyKey>({})

const getQueryData = (queryData: anyKey) => {
    termFormData.currentPage = 1
    formData.currentPage = 1
    pitayaTableRef.value.resetCurrentPage()
    // termTableRef.value.resetCurrentPage()
    const { startAndEndTime } = queryData
    formData.beginDate =
        startAndEndTime && startAndEndTime[0] ? startAndEndTime[0] : ""
    formData.endDate =
        startAndEndTime && startAndEndTime[1] ? startAndEndTime[1] : ""

    formData.status = queryData.status ? queryData.status : ""
    formData.createdByName = queryData.createdByName
        ? queryData.createdByName
        : ""
    formData.companyId = queryData.companyId ? queryData.companyId : ""
}

const formData = reactive<anyKey>({
    currentPage: paginationData.value.currentPage || 1,
    pageSize: paginationData.value.pageSize || 10,
    type: activeName.value || "位置码"
})

watch(formData, getBaseList)

// 获取标签码列表
function getBaseList() {
    const data = objectToFormData(formData)
    tableLoading.value = true
    getLabelCodeListApi(data)
        .then((res: anyKey) => {
            if (res.rows && res.rows.length > 0) {
                const { rows, records } = res
                baseTableData.value = rows
                dataTotal.value = records
                pitayaTableRef.value.clearSelectedTableData()
            } else {
                baseTableData.value = []
            }
        })
        .catch((err) => {
            throw new Error("getLabelCodeListApi():::" + err)
        })
        .finally(() => {
            tableLoading.value = false
        })
}

onMounted(() => {
    getBaseList()
})

const termFormData = reactive<anyKey>({
    currentPage: 1,
    pageSize: 20
})

watch(termFormData, getTermList)

const tableDataLoading = ref(false)
function getTermList() {
    tableDataLoading.value = true
    const data = objectToFormData({
        ...termFormData,
        id: currentId.value
    })
    getLabelCodeDetailListApi(data)
        .then((res: anyKey) => {
            if (res.rows && res.rows.length > 0) {
                const { rows, records } = res
                termTableData.value = rows
                termDataTotal.value = records
            } else {
                termTableData.value = []
            }
        })
        .catch((err) => {
            throw new Error("getLabelCodeDetailListApi():::" + err)
        })
        .finally(() => {
            tableDataLoading.value = false
        })
}

function getBaseDetails() {
    const data = objectToFormData({
        id: currentId.value
    })

    getLabelCodeDetailApi(data)
        .then((res) => (tremFormData.value = res))
        .catch((err) => {
            throw new Error("getLabelCodeDetailApi():::" + err)
        })

    getTermList()
}

const onBtnClick = () => {
    Object.keys(baseFormData).map((key) => (baseFormData[key] = ""))
    baseFormRef.value?.clearValidate()
    baseFormData.type = activeName.value
    baseFormData.labelType = "二维码"
    baseFormData.sharedTag = 0
    // 所属公司
    baseFormData.companyName = userInfo.value.companyName
    drawerState.value = true
}

const drawerState = ref(false)
const drawerSize = 310

const detailsDrawerState = ref(false)
const detailsDrawerSize = 1240

const dataTotal = ref(0)
const termDataTotal = ref(0)
const baseTableData = ref([])
const tableLoading = ref<boolean>(false)
const baseColumns = ref<TableColumnType[]>([
    { prop: "labelType", label: "标签码类型", width: 100 },
    { prop: "num", label: "标签码数量", width: 100 },
    { prop: "status", label: "打印状态", needSlot: true, width: 100 },
    { prop: "notBindingNum", label: "未绑定标签码", width: 100 },
    { prop: "bindingNum", label: "已绑定标签码", width: 100 },
    { prop: "expireNum", label: "已失效标签码", width: 100 },
    { prop: "createdByName", label: "申请人", width: 100 },
    { prop: "companyName", label: "申请公司", width: 150 },
    { prop: "orgName", label: "申请部门", minWidth: 300 },
    { prop: "createdDate", label: "申请时间", width: 170 },
    { prop: "sharedTag", label: "共享标签",needSlot: true, width: 100 },
    {
        prop: "operations",
        fixed: "right",
        label: "操作",
        needSlot: true,
        width: 100
    }
])

const getTableList = (pd: PaginationData) => {
    paginationData.value = pd
    if (detailsDrawerState.value) {
        termFormData.currentPage = pd.currentPage
        termFormData.pageSize = pd.pageSize
    } else {
        formData.currentPage = pd.currentPage
        formData.pageSize = pd.pageSize
    }
}

const baseFormData = reactive<anyKey>({
    type: "",
    labelType: "",
    value: "",
    companyName: "",
    sharedTag:0
})

const formRules = reactive<FormRules<typeof baseFormData>>({
    value: [{ required: true, message: "请输入标签码数量", trigger: "blur" }],
    companyName: [{ required: true, message: "请选择所属公司", trigger: "blur" }],
    labelType: [{ required: true, message: "请选择标签码类型", trigger: "blur" }]
})

const onEdit = (rowData: anyKey) => {
    currentId.value = rowData.id
    getBaseDetails()

    if (activeName.value == "位置码") {
        termColumns.value = [
            { prop: "code", label: "位置码", width: 270 },
            { prop: "status", label: "状态", needSlot: true, width: 80 },
            { prop: "objectName", label: "绑定位置名称", minWidth: 250 },
            { prop: "objectCode", label: "绑定位置编码", width: 160 },
            { prop: "usingCompanyName", label: "使用公司", width: 160, needSlot: true }
        ]
    } else if (activeName.value == "设备码") {
        termColumns.value = [
            { prop: "code", label: "设备码", width: 270 },
            { prop: "status", label: "状态", needSlot: true, width: 80 },
            { prop: "objectName", label: "绑定设备名称", minWidth: 250 },
            { prop: "objectCode", label: "绑定编码", width: 160 },
            { prop: "usingCompanyName", label: "使用公司", width: 160, needSlot: true }
        ]
    }

    detailsDrawerState.value = true
    setTimeout(() => {
        termTableRef.value.handleSizeChange(
            usePaginationStore.paginationData.pageSize
        )
    }, 200)
}

const baseFormRef = ref<FormInstance>()
const tremFormRef = ref<FormInstance>()

function saveLabelCode() {
    if (submitLoading.value) return
    const data = {
        labelType: baseFormData.labelType,
        type: activeName.value,
        num: baseFormData.value,
        sharedTag:baseFormData.sharedTag?1:0
    }
    submitLoading.value = true
    createLabelCodeApi(data)
        .then(() => {
            ElMessage.success("新增成功")
            getBaseList()
            drawerState.value = false
        })
        .catch((err) => {
            throw new Error("createLabelCodeApi():::" + err)
        })
        .finally(() => {
            submitLoading.value = false
        })
}
const onFormBtnClick = (btnName: string | undefined) => {
    if (!baseFormRef.value) return
    if (btnName === "保存") {
        baseFormRef.value.validate((valid) => {
            if (valid) {
                saveLabelCode()
            } else {
                return false
            }
        })
        return
    }

    if (btnName === "取消") {
        drawerState.value = false
        return
    }
}

const onClose = () => {
    Object.keys(baseFormData).map((key) => (baseFormData[key] = ""))
    baseFormRef.value?.clearValidate()
}

const onLookBtnClick = () => {
    detailsDrawerState.value = false
}

const onDetailsClose = () => {
    tremFormData.value = {}
    tremFormRef.value?.clearValidate()
}

const submitLoading = ref(false)
const btnList = [
    {
        name: "取消",
        icon: ["fas", "circle-minus"]
    },
    {
        name: "保存",
        icon: ["fas", "floppy-disk"]
    }
]

const lookBtnList = [
    {
        name: "取消",
        icon: ["fas", "circle-minus"]
    }
]
const buttonLoading = ref(false)
const paginationBtnList = ref([
    {
        name: "打印",
        icon: ["fas", "print"],
        roles: "system:labelcode:btn:print",
        disabled: true
    },
    {
        name: "导出",
        icon: ["fas", "cloud-arrow-down"],
        disabled: true
    }
])

const selectionTableList = ref<any[]>([])

const getSelectionTableList = (rowList: any) => {
    selectionTableList.value = rowList
}

function handlerPrintLabelCode() {
    if (selectionTableList.value && selectionTableList.value.length > 0) {
        const idStr = (selectionTableList.value as any[])
            .reduce((pre: anyKey, cur: anyKey) => {
                pre.push(cur.id)
                return pre
            }, [])
            .join(",")
        if (buttonLoading.value) return
        buttonLoading.value = true
        labelCodePrintApi(objectToFormData({ id: idStr }))
            .then(() => {
                ElMessage.success("打印成功")
                getBaseList()
                pitayaTableRef.value?.clearSelectedTableData()
            })
            .finally(() => {
                buttonLoading.value = false
            })
    } else ElMessage.warning("请选择标签")
}

const onPaginationBtnClick = (btnName: string | undefined) => {
    if (btnName === "打印") {
        handlerPrintLabelCode()
    }
    if (btnName === "导出") {
        ExportLabelCode()
    }
}
const ExportLabelCode = () => {
    if (selectionTableList.value && selectionTableList.value.length > 0) {
        const idStr = (selectionTableList.value as any[])
            .reduce((pre: anyKey, cur: anyKey) => {
                pre.push(cur.id)
                return pre
            }, [])
            .join(",")
        downloadFile(
            `/pitaya/system/labelCode/export?id=${idStr}&codeType=${formData.type}`
        )
    } else ElMessage.warning("请选择标签")
}
const headers = reactive({
    Authorization: "Bearer " + Cookies.get(CacheKey.TOKEN),
    responseType: "blob"
})
const downloadFile = async (url: string) => {
    const response = await fetch(url, { headers })
    if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
    }
    const contentDisposition = response.headers.get("content-disposition")
    const filename = contentDisposition
        ? contentDisposition.split("=")[1].trim().replace(/"/g, "")
        : "default-filename.ext" // 如果没有提供文件名，则使用默认名
    const blob = await response.blob()
    const fileUrl = URL.createObjectURL(blob)
    const link = document.createElement("a")
    link.href = fileUrl
    link.download = decodeURIComponent(filename || "")
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(fileUrl)
}
const pitayaTableRef = ref()
const termTableRef = ref()

const handleClick = () => {
    pitayaTableRef.value.currentPagechange(1)
    setTimeout(() => {
        formData.type = activeName.value
        if (activeName.value === "位置码") {
            button.value[0].name = "新增位置码"
        }
        if (activeName.value === "设备码") {
            button.value[0].name = "新增设备码"
        }
    }, 200)
}

const detailsDrawerTitle = {
    name: ["标签查看"],
    icon: ["fas", "square-share-nodes"]
}

const detailsDrawerTitle2 = {
    name: ["扩展信息"],
    icon: ["fas", "square-share-nodes"]
}

const termTableData = ref<any[]>([])
const termColumns = ref<TableColumnType[]>([
    { prop: "code", label: "标签码", width: 160 },
    { prop: "status", label: "状态", needSlot: true, width: 90 },
    { prop: "objectName", label: "绑定位置名称", minWidth: 250 },
    { prop: "objectCode", label: "绑定位置编码", width: 160 },
    { prop: "usingCompanyName", label: "使用公司", width: 160, needSlot:true }
])

function _matchTagClass(value: string) {
    switch (value) {
        case "已绑定":
            return "termState1"
        case "未绑定":
            return "termState0"
        case "已失效":
            return "termState2"
    }
}

// #region 获取公司列表相关
const companyList = ref<any[]>([])
// 获取全部公司列表
function getAllCompany() {
    baseUserApi
        .getAllCompany()
        .then((res: any) => {
            if (res && res.length > 0) {
                companyList.value = res.map((item: anyKey) => {
                    return {
                        label: item.companyName,
                        value: item.id
                    }
                })
                queryArrList[3].children = companyList.value
            } else {
                companyList.value = []
            }
        })
        .catch((err) => {
            throw new Error("getAllCompany():::" + err)
        })
}
getAllCompany()

watchEffect(() => {
    if (selectionTableList.value.length === 1) {
        paginationBtnList.value[0].disabled = false
        paginationBtnList.value[1].disabled = false
    } else {
        paginationBtnList.value[0].disabled = true
        paginationBtnList.value[1].disabled = true
    }
})
</script>

<template>
    <div class="app-container">
        <ModelFrame>
            <Query
                class="ml10"
                :queryArrList="queryArrList"
                @getQueryData="getQueryData"
            />
        </ModelFrame>
        <div class="app-content-wrapper">
            <div class="app-content-group">
                <ModelFrame>
                    <Title :title="title" :button="button" @onBtnClick="onBtnClick">
                        <div class="app-tabs-wrapper">
                            <el-tabs v-model="activeName" @tab-click="handleClick">
                                <el-tab-pane
                                    v-for="(tab, index) in tabList"
                                    :key="index"
                                    :label="tab.name"
                                    :name="tab.name"
                                    :index="tab.name"
                                />
                            </el-tabs>
                        </div>
                    </Title>
                    <div class="app-el-scrollbar-wrapper">
                        <el-scrollbar>
                            <PitayaTable
                                ref="pitayaTableRef"
                                @onSelectionChange="getSelectionTableList"
                                @onCurrentPageChange="getTableList"
                                :table-data="baseTableData"
                                :columns="baseColumns"
                                :total="dataTotal"
                                :needSelection="true"
                                :single-select="true"
                                :table-loading="tableLoading"
                            >
                                <template #status="{ rowData }">
                                    <el-tag
                                        :class="rowData.status === '未打印' ? 'state0' : 'state1'"
                                        >{{ rowData.status }}</el-tag
                                    >
                                </template>
                                <template #usingCompanyName="{ rowData }">
                                    {{ rowData.usingCompanyName || (rowData.status === "已绑定"? tremFormData.companyName : '---')}}
                                </template>
                                <template #sharedTag="{ rowData }">
                                    {{ rowData.sharedTag ? "是":"否" }}
                                </template>
                                <template #operations="{ rowData }">
                                    <el-button
                                        v-btn
                                        link
                                        @click="onEdit(rowData)"
                                        v-if="
                                            rowData.state !== '0' &&
                                            isCheckPermission('system:labelcode:btn:view')
                                        "
                                        :disabled="checkPermission('system:labelcode:btn:view')"
                                    >
                                        <font-awesome-icon
                                            :icon="['fas', 'eye']"
                                            style="color: var(--pitaya-btn-background)"
                                            :class="
                                                checkPermission('system:labelcode:btn:view')
                                                    ? 'disabled'
                                                    : ''
                                            "
                                        />
                                        <span
                                            class="table-inner-btn"
                                            :class="
                                                checkPermission('system:labelcode:btn:view')
                                                    ? 'disabled'
                                                    : ''
                                            "
                                            >查看</span
                                        >
                                    </el-button>
                                    <span v-else>--</span>
                                </template>
                                <template #footerOperateLeft>
                                    <ButtonList
                                        :button="paginationBtnList"
                                        :loading="buttonLoading"
                                        :isNotRadius="true"
                                        @onBtnClick="onPaginationBtnClick"
                                    />
                                </template>
                            </PitayaTable>
                        </el-scrollbar>
                    </div>
                </ModelFrame>
            </div>
        </div>
        <Drawer :size="drawerSize" v-model:drawer="drawerState" @close="onClose">
            <div class="common-from-wrapper common-from-only">
                <Title :title="drawerTitle" />
                <div class="common-from-group">
                    <el-scrollbar>
                        <el-form
                            class="el-form-wrapper"
                            ref="baseFormRef"
                            :model="baseFormData"
                            :rules="formRules"
                            label-width="auto"
                            label-position="top"
                        >
                            <el-form-item label="类型">
                                <el-input v-model="baseFormData.type" disabled />
                            </el-form-item>
                            <el-form-item label="标签码类型" prop="labelType">
                                <!-- <el-input v-model="baseFormData.codeType"  /> -->
                                <el-select
                                    v-model="baseFormData.labelType"
                                    style="width: 100%"
                                    placeholder="请选择"
                                    clearable
                                >
                                    <el-option
                                        key="二维码"
                                        value="二维码"
                                        label="二维码"
                                    ></el-option>
                                    <el-option key="NFC" value="NFC" label="NFC"></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="标签码数量" prop="value">
                                <el-input-number
                                    style="width: 100%"
                                    controls-position="right"
                                    v-model="baseFormData.value"
                                    :min="1"
                                    placeholder="请输入"
                                />
                            </el-form-item>
                            <el-form-item label="所属公司" prop="companyName">
                                <el-input v-model="baseFormData.companyName" disabled />
                            </el-form-item>
                            <el-form-item label="" prop="sharedTag">
                                <span slot="label" class="item-label" >
                                    共享标签
                                    <el-tooltip
                                        effect="dark"
                                        content="共享标签可由其他公司绑定使用。"
                                        placement="top"
                                    >
                                            <font-awesome-icon
                                                :icon="['far', 'fa-question-circle']"
                                                style="color: #666;"
                                            />
                                    </el-tooltip>
                                </span>
                                <el-select
                                    v-model="baseFormData.sharedTag"
                                    style="width: 100%"
                                    placeholder="请选择"
                                >
                                    <el-option key="是" :value="1" label="是"></el-option>
                                    <el-option key="否" :value="0" label="否"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-form>
                    </el-scrollbar>
                </div>
                <div class="btn-groups">
                    <ButtonList
                        :button="btnList"
                        :loading="submitLoading"
                        @onBtnClick="onFormBtnClick"
                    />
                </div>
            </div>
        </Drawer>
        <Drawer
            :size="detailsDrawerSize"
            v-model:drawer="detailsDrawerState"
            @close="onDetailsClose"
        >
            <div class="drawer-lr-layout-wrapper">
                <div class="common-from-wrapper common-from-left">
                    <Title :title="detailsDrawerTitle" />
                    <div class="common-from-group">
                        <el-scrollbar>
                            <el-form
                                class="el-form-wrapper"
                                ref="tremFormRef"
                                :model="tremFormData"
                                label-width="auto"
                                label-position="top"
                            >
                                <el-form-item label="类型">
                                    <el-input v-model="tremFormData.type" disabled />
                                </el-form-item>
                                <el-form-item label="标签码类型">
                                    <el-input v-model="tremFormData.labelType" disabled />
                                </el-form-item>
                                <el-form-item label="标签码数量">
                                    <el-input v-model="tremFormData.num" disabled />
                                </el-form-item>
                                <el-form-item label="申请人">
                                    <el-input v-model="tremFormData.createdByName" disabled />
                                </el-form-item>
                                <el-form-item label="申请部门">
                                    <el-input v-model="tremFormData.orgName" disabled />
                                </el-form-item>
                                <el-form-item label="申请时间">
                                    <el-input v-model="tremFormData.createdDate" disabled />
                                </el-form-item>
                                <el-form-item label="未绑定">
                                    <el-input v-model="tremFormData.notBindingNum" disabled />
                                </el-form-item>
                                <el-form-item label="已绑定">
                                    <el-input v-model="tremFormData.bindingNum" disabled />
                                </el-form-item>
                                <el-form-item label="已失效">
                                    <el-input v-model="tremFormData.expireNum" disabled />
                                </el-form-item>
                                <el-form-item label="">
                                    <span slot="label" class="item-label" >
                                        共享标签
                                        <el-tooltip
                                            effect="dark"
                                            content="共享标签可由其他公司绑定使用。"
                                            placement="top"
                                        >
                                            <font-awesome-icon
                                                :icon="['far', 'fa-question-circle']"
                                                style="color: #666;"
                                            />
                                        </el-tooltip>
                                    </span>
                                    <el-select
                                        v-model="tremFormData.sharedTag"
                                        style="width: 100%"
                                        placeholder="请选择"
                                        disabled
                                    >
                                        <el-option key="true" :value="true" label="是"></el-option>
                                        <el-option key="false" :value="false" label="否"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-form>
                        </el-scrollbar>
                    </div>
                    <div class="btn-groups">
                        <ButtonList :button="lookBtnList" @onBtnClick="onLookBtnClick" />
                    </div>
                </div>
                <div class="common-from-wrapper">
                    <Title :title="detailsDrawerTitle2">
                        <div class="drawer-content-wrapper">
                            {{ activeName === "位置码" ? "位置码列表" : "设备码列表" }}
                        </div>
                    </Title>
                    <div class="common-from-group">
                        <el-scrollbar>
                            <PitayaTable
                                ref="termTableRef"
                                :table-data="termTableData"
                                :table-loading="tableDataLoading"
                                :columns="termColumns"
                                :total="termDataTotal"
                                :max-height="693"
                                @onCurrentPageChange="getTableList"
                            >
                                <template #status="{ rowData }">
                                    <el-tag :class="_matchTagClass(rowData.status)">{{
                                        rowData.status
                                    }}</el-tag>
                                </template>
                                 <template #usingCompanyName="{ rowData }">
                                    {{ rowData.usingCompanyName || (rowData.status === "已绑定"? tremFormData.companyName : '---')}}
                                </template>
                            </PitayaTable>
                        </el-scrollbar>
                    </div>
                </div>
            </div>
        </Drawer>
    </div>
</template>

<style lang="scss" scoped>
.state0 {
    color: #f59b22;
    border-color: #f59b22;
    background-color: #fcf3e6;
}
.state1 {
    color: #4bae89;
    border-color: #4bae89;
    background-color: #e0ffef;
}
.termState0 {
    color: #009dff;
    border-color: #009dff;
    background-color: #dff3ff;
}
.termState1 {
    color: #4bae89;
    border-color: #4bae89;
    background-color: #e0ffef;
}
.termState2 {
    color: #999;
    border-color: #d0d0d0;
    background-color: #f5f7fb;
}
.app-container {
    :deep(.model-frame-wrapper) {
        padding-bottom: 0px;
    }
}
.app-content-wrapper {
    height: 0;
    flex: 1;
    margin-top: 10px;
    .app-content-group {
        height: 100%;
        :deep(.model-frame-wrapper) {
            height: 100%;
            padding-bottom: 10px;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }
        .app-el-scrollbar-wrapper {
            height: 0;
            flex: 1;
            .table-inner-btn {
                margin-left: 5px;
                color: #204a9c;
            }
        }
    }
}

.drawer-lr-layout-wrapper {
    display: flex;
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    overflow: hidden;
    &::after {
        content: "";
        width: 1px;
        height: 120%;
        position: absolute;
        left: 310px;
        top: -15px;
        background-color: var(--pitaya-border-color);
    }
}

.common-from-wrapper {
    height: 100%;
    width: 0;
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 10px;
    .drawer-content-wrapper {
        position: absolute;
        left: 130px;
        &::after {
            content: "";
            width: 100%;
            height: 2px;
            background-color: var(--pitaya-btn-background);
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
        }
    }
    &.common-from-left {
        width: 310px;
        flex: 0 0 310px;
    }
    &.common-from-only {
        width: 100%;
        padding: 0;
    }
    .common-from-group {
        height: 0;
        flex: 1;
        .el-form-wrapper {
            padding-left: 10px;
            padding-right: 10px;
        }
    }
    .btn-groups {
        display: flex;
        justify-content: flex-end;
        padding: 10px 10px 0 0;
        border-top: 1px solid var(--pitaya-border-color);
    }
}
.item-label {
    height: 22px;
    margin-bottom: 5px;
    line-height: 26px;
    display: block;
    font-size: 12px;
    color: var(--pitaya-place-font-color);
}
</style>
@/app/platform/hooks/usePagination @/app/platform/api/system/labelCode
