<script lang="ts" setup>
import type { FormInstance, FormRules } from "element-plus"
import { usePagination } from "@/app/platform/hooks/usePagination"
import { LogUserCalculation } from "@/app/platform/api/system/message"
import { getDictionaryTerm } from "@/app/platform/api/system/dictionary"

interface queryObj {
	/**
	 * 页码
	 */
	currentPage?: number
	/**
	 * 每页条数
	 */
	pageSize?: number
	/**
	 * 客户端类型
	 */
	clientType?: string
	/**
	 * 名称
	 */
	userName?: string

	/**
	 * 名称
	 */
	beginDate?: string
	/**
	 * 统计日期
	 */
	endDate?: string
}

const tableLoading = ref(false)

const usePaginationStore = usePagination()
const paginationData = ref<PaginationData>(usePaginationStore.paginationData)

//初始化查询日期
const todayDate = ref("")
const yesterdayDate = ref("")

const formatDate = (date: any) => {
	const year = date.getFullYear()
	const month = String(date.getMonth() + 1).padStart(2, "0") // 月份从0开始，需要加1并补零
	const day = String(date.getDate()).padStart(2, "0") // 补零
	return `${year}-${month}-${day}`
}
const getTodayAndYesterdayDates = () => {
	const today = new Date()
	const yesterday = new Date(today)
	yesterday.setDate(today.getDate() - 1)
	todayDate.value = formatDate(today)
	yesterdayDate.value = formatDate(yesterday)
}
getTodayAndYesterdayDates()

const paramObj = ref<queryObj>({
	currentPage: paginationData.value.currentPage,
	pageSize: paginationData.value.pageSize,
	clientType: "",
	userName: "",
	beginDate: yesterdayDate.value,
	endDate: todayDate.value
})

const getTableList = (pd: PaginationData) => {
	paramObj.value.currentPage = pd.currentPage
	paramObj.value.pageSize = pd.pageSize
	systemParameterListInit()
}

const tableRef = ref()
const getQueryData = (queryData: queryObj) => {
	paramObj.value.currentPage = 1
	tableRef.value.resetCurrentPage()
	paramObj.value.userName = queryData.userName
	paramObj.value.clientType = queryData.clientType
	paramObj.value.beginDate =
		queryData?.calDate && queryData?.calDate[0] ? queryData.calDate[0] : ""
	paramObj.value.endDate =
		queryData?.calDate && queryData?.calDate[1] ? queryData.calDate[1] : ""
	systemParameterListInit()
}

const systemParameterListInit = () => {
	tableLoading.value = true
	LogUserCalculation(paramObj.value)
		.then((res: any) => {
			if (res.rows && res.rows.length) {
				tableData.value = res.rows
				dataTotal.value = res.records
			} else {
				tableData.value = []
				dataTotal.value = 0
			}
		})
		.finally(() => {
			tableLoading.value = false
		})
}
const loginType = ref<any>([])
onMounted(() => {
	systemParameterListInit()
	getDictionaryTerm({
		dataDictionaryCode: "LOGIN_TYPE"
	}).then((res: any) => {
		if (res && res.length) {
			const children: any[] = []
			res.forEach((item: any) => {
				children.push({
					label: item.subitemName,
					value: item.subitemValue
				})
			})
			queryArrList[1].children = children
			loginType.value = children
		}
	})
})

const queryArrList = [
	{
		name: "用户账号",
		key: "userName",
		type: "input",
		placeholder: "请输入查询关键字",
		enableFuzzy: false
	},
	{
		name: "客户端类型",
		key: "clientType",
		type: "select",
		placeholder: "请输入查询关键字",
		enableFuzzy: false,
		children: []
	},
	{
		name: "统计日期",
		key: "calDate",
		type: "startAndEndTime",
		placeholder: "请输入查询关键字",
		enableFuzzy: false,
		paramsData: [yesterdayDate.value, todayDate.value]
	}
]

const title = {
	name: ["用户日志"],
	icon: ["fas", "square-share-nodes"]
}

const dataTotal = ref(0)
const tableData = ref<any[]>([])
const columns = ref<TableColumnType[]>([
	{ prop: "userName", label: "用户账号" },
	{ prop: "loginCount", label: " 登录次数", needSlot: true },
	{ prop: "clientType", label: "客户端类型", needSlot: true },
	{ prop: "duration", label: "在线时长", needSlot: true },
	{ prop: "calDate", label: "统计日期", needSlot: true }
])

// 列表-返回客户类型对应label
const getClientType = (clientType: string | undefined) => {
	if (!Array.isArray(loginType.value)) return '---'
	let data = loginType.value.filter(
		(item: { value: string | undefined }) => item.value == clientType
	)
	return data[0]?.label || '---'
}
//
const convertMinutesToTime = (minutes: any) => {
	const hours = Math.floor(minutes / 60)
	const mins = minutes % 60
	return `${hours}小时${String(mins).padStart(2, "0")}分钟`
}

// const formatDate =(date) =>{
//   const year = date.getFullYear();
//   const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从 0 开始，需要加 1
//   const day = String(date.getDate()).padStart(2, '0');
//   return `${year}-${month}-${day}`;
// }
</script>

<template>
	<div class="app-container">
		<ModelFrame>
			<Query
				ref="queryRef"
				class="ml10"
				:queryArrList="queryArrList"
				@getQueryData="getQueryData"
			/>
		</ModelFrame>
		<div class="app-content-wrapper">
			<div class="app-content-group">
				<ModelFrame>
					<Title :title="title" />
					<div class="app-el-scrollbar-wrapper">
						<el-scrollbar>
							<PitayaTable
								ref="tableRef"
								@onCurrentPageChange="getTableList"
								:table-data="tableData"
								:columns="columns"
								:total="dataTotal"
								:table-loading="tableLoading"
							>
								<template #clientType="{ rowData }">
									<span>{{ getClientType(rowData.clientType) }}</span>
								</template>
								<template #loginCount="{ rowData }">
									<span>{{
										rowData.loginCount ? rowData.loginCount : "---"
									}}</span>
								</template>
								<template #duration="{ rowData }">
									<span>{{
										rowData.duration > 0
											? convertMinutesToTime(rowData.duration)
											: "---"
									}}</span>
								</template>
								<template #calDate="{ rowData }">
									{{ rowData.calDate?rowData.calDate.split(' ')[0] :'---'}}
								</template>
							</PitayaTable>
						</el-scrollbar>
					</div>
				</ModelFrame>
			</div>
		</div>
	</div>
</template>

<style lang="scss" scoped>
.app-container {
	:deep(.model-frame-wrapper) {
		padding-bottom: 0px;
	}
}
.app-content-wrapper {
	height: 0;
	flex: 1;
	margin-top: 10px;
	.app-content-group {
		height: 100%;
		:deep(.model-frame-wrapper) {
			height: 100%;
			padding-bottom: 10px;
			overflow: hidden;
			display: flex;
			flex-direction: column;
		}
		.app-el-scrollbar-wrapper {
			height: 0;
			flex: 1;
		}
	}
}
</style>
@/app/platform/hooks/usePagination @/app/platform/api/system/parameter
