<script lang="ts" setup>
import { ProfessionApi } from "@/app/platform/api/system/profession"
import ProfessionDrawer from "./components/professionDrawer.vue"
import { usePagination } from "@/app/platform/hooks/usePagination"
import { ElMessage } from "element-plus"
import {
	checkPermission,
	isCheckPermission
} from "@/app/platform/utils/permission"
import { CustomMessageBox } from "@/app/platform/utils/message"

const relevanceDep = {
	name: ["关联部门"],
	icon: ["fas", "square-share-nodes"]
}
const containerLeftTitle = {
	name: ["专业管理"],
	icon: ["fas", "square-share-nodes"]
}
const professionContainerBtn = [
	{
		name: "新增专业",
		icon: ["fas", "square-plus"],
		roles: "system:profession:btn:add"
	}
]
const professionTreeProp = {
	children: "children",
	label: (data: any) => {
		if (data.pid !== null && data?.code !== null) {
			return `${data.name}（${data.code}）`
		} else {
			return `${data.name}`
		}
	}
}
const professionTableProp: TableColumnType[] = [
	{ label: "专业编码", prop: "code", width: 200 },
	{ label: "专业名称", prop: "name", width: 200 },
	{ label: "备注", prop: "remark", minWidth: 200},
	{
		label: "关联的部门",
		prop: "orgCount",
		width: 120,
		needSlot: true
	},
	{
		label: "操作",
		prop: "operations",
		fixed: "right",
		width: 150,
		needSlot: true
	}
]
const relatedDepartmentTableColumn: TableColumnType[] = [
	{ label: "部门", prop: "name", align: "left", class: "tree-cell-flex" },
	{
		label: "部门编码",
		prop: "code",
		width: 260
	}
]
const drawerSize = 310
const relatedDepartmentDrawerSize = 620

const usePaginationStore = usePagination()
const usePaginationStoreForData = usePaginationStore.paginationData

const containerRightTitle = ref<any>({
	name: ["专业管理"],
	icon: ["fas", "square-share-nodes"]
})

const professionTreeData = ref<any[]>([])
const professionTableData = ref<any[]>([])
const showDrawer = ref<boolean>(false)
const tableLoading = ref<boolean>(false)
const treeLoading = ref<boolean>(false)
const relatedDepartmentTableData = ref<any[]>([])
const showRelatedDepartmentDrawer = ref<boolean>(false)
const relatedDepartmentDrawerTableData = ref<any[]>([])
const relatedDepLoading = ref<boolean>(false)
// 分页
const pageSize = ref<number>(usePaginationStoreForData.pageSize)
const pageTotal = ref<number>(0)
const currentPage = ref<number>(usePaginationStoreForData.currentPage)
// tree当前选中的节点
const currentTreeNode = ref<any>({})
// 编辑的信息
const editData = ref<any>({})

// #region 10.14bug修复
const treeBizId = ref()
const defaultExpandedKeys = ref<any[]>([])
// #endregion

// 获取树
const getProfessionTreeData = () => {
	treeLoading.value = true
	ProfessionApi.getProfessionTree()
		.then((res: any) => {
			professionTreeData.value = res
			// professionTreeData.value = [
			// 	{ children: res, name: "全部专业", code: "", id: 0, pid: null }
			// ]
			if (!currentTreeNode.value.id) {
				currentTreeNode.value = res[0]
			}

			defaultExpandedKeys.value = treeBizId.value
				? [treeBizId.value]
				: [res[0].id]
			treeBizId.value = treeBizId.value || res[0].id
			// 更新面包屑
			const titleObj = JSON.parse(JSON.stringify(containerLeftTitle))
			titleObj.name.push(res[0].name)
			containerRightTitle.value = titleObj
			getProfessionTableData()
		})
		.finally(() => {
			treeLoading.value = false
		})
}
// 专业树点击
const refTable = ref()
const professionTreeClick = (data: any, treeData: any) => {
	currentPage.value = 1
	refTable.value.resetCurrentPage()
	// 更新面包屑
	containerRightTitle.value.name = JSON.parse(
		JSON.stringify(containerLeftTitle)
	).name.concat(getTreeTitle(treeData, "name"))
	currentTreeNode.value = data
	console.log('===currentTreeNode.value',currentTreeNode.value)
	getProfessionTableData()
}
// 获取表格数据
const getProfessionTableData = () => {
	tableLoading.value = true
	ProfessionApi.getProfessionTable(
		objectToFormData({
			...queryData.value,
			currentPage: currentPage.value,
			pageSize: pageSize.value,
			id: currentTreeNode.value.id,
		})
	)
		.then((res: any) => {
			pageTotal.value = res.records
			professionTableData.value = res.rows
		})
		.finally(() => {
			tableLoading.value = false
		})
}
// 表格编辑
const onRowEdit = (row: any) => {
	editData.value = row
	showDrawer.value = true
}
// 表格移除
const onRowDelete = (row: any) => {
	CustomMessageBox({ message: "确定要移除吗?" }, (res: boolean) => {
		if (res) {
			ProfessionApi.deleteProfession(row.id).then(() => {
				// getProfessionTableData()
				ElMessage.success("移除成功")
				getProfessionTreeData()
			})
		}
	})
}
const onBtnClick = () => {
	editData.value = {}
	showDrawer.value = true
}
// 获取部门关联弹窗数据
const getRelatedDepartmentDrawerTableData = (data: any) => {
	relatedDepLoading.value = true
	ProfessionApi.getOrgInfo(data.id)
		.then((res: any) => {
			relatedDepartmentDrawerTableData.value = res
		})
		.finally(() => {
			relatedDepLoading.value = false
		})
}
// 关联部门详情
const getRelatedDepartmentData = (data: any) => {
	getRelatedDepartmentDrawerTableData(data)
	showRelatedDepartmentDrawer.value = true
}

// 分页
const onCurrentPageChange = (pageData: any) => {
	pageSize.value = pageData.pageSize
	currentPage.value = pageData.currentPage
	getProfessionTableData()
}

// 新增、编辑 弹窗回调
const closeAddDrawer = (flag: any) => {
	if (flag === "success") {
		ElMessage.success("保存成功")
		getProfessionTreeData()
		getProfessionTableData()
	}
	showDrawer.value = false
}

//查询

const queryArrList = ref<any[]>([
	{
		name: "专业名称",
		key: "name",
		type: "input",
		enableFuzzy: false,
		placeholder: "请输入查询关键字"
	},
	{
		name: "专业编码",
		key: "code",
		type: "input",
		enableFuzzy: false,
		placeholder: "请输入查询关键字"
	}
])
const queryData = ref<any>({})
const getQueryData = (queryParams?: any) => {
	currentPage.value = 1
	refTable.value.resetCurrentPage()
	queryData.value.name = queryParams.name ? queryParams.name : ""
	queryData.value.code = queryParams.code ? queryParams.code : ""
	getProfessionTableData()
}

watch(
	() => showRelatedDepartmentDrawer.value,
	(val: boolean) => {
		if (!val) {
			relatedDepartmentDrawerTableData.value = []
		}
	},
	{ immediate: true }
)

onMounted(() => {
	getProfessionTreeData()
})

defineOptions({
	name: "ProfessionManagement"
})
</script>
<template>
	<div class="app-container-row">
		<ModelFrame class="left-model-frame">
			<Title :title="containerLeftTitle" />
			<div class="left-model-group">
				<PitayaTree
					:treeData="professionTreeData"
					:treeProps="professionTreeProp"
					:needCheckBox="false"
					v-model:treeBizId="treeBizId"
					:defaultExpandedKeys="defaultExpandedKeys"
					@onTreeClick="professionTreeClick"
					:tree-loading="treeLoading"
				/>
			</div>
		</ModelFrame>
		<div class="right-model-frame">
			<ModelFrame>
				<Query
					class="ml10"
					:queryArrList="queryArrList"
					@getQueryData="getQueryData"
				/>
			</ModelFrame>
			<ModelFrame class="content">
				<Title
					:title="containerRightTitle"
					:button="currentTreeNode.levelNum&&currentTreeNode.levelNum<2?professionContainerBtn:undefined"
					@onBtnClick="onBtnClick"
				/>
				<el-scrollbar>
					<PitayaTable
						ref="refTable"
						:columns="professionTableProp"
						:table-data="professionTableData"
						:need-index="true"
						:need-pagination="true"
						:total="pageTotal"
						@on-current-page-change="onCurrentPageChange"
						:table-loading="tableLoading"
					>
						<template #orgCount="{ rowData }">
							<div
								class="border-bottom-text"
								@click="getRelatedDepartmentData(rowData)"
							>
								{{ rowData.orgCount }}
							</div>
						</template>
						<template #operations="{ rowData }">
							<el-button
								v-btn
								link
								@click="onRowEdit(rowData)"
								:disabled="checkPermission('system:profession:btn:edit')"
								v-if="isCheckPermission('system:profession:btn:edit')"
							>
								<font-awesome-icon
									:icon="['fas', 'pen-to-square']"
									:class="
										checkPermission('system:profession:btn:edit')
											? 'disabled'
											: ''
									"
									style="color: var(--pitaya-btn-background)"
								/>
								<span
									class="table-inner-btn"
									:class="
										checkPermission('system:profession:btn:edit')
											? 'disabled'
											: ''
									"
									>编辑</span
								>
							</el-button>
							<el-button
								v-btn
								color="var(--pitaya-btn-background)"
								link
								@click="onRowDelete(rowData)"
								:disabled="checkPermission('system:profession:btn:delete')"
								v-if="isCheckPermission('system:profession:btn:delete')"
							>
								<font-awesome-icon
									:icon="['fas', 'trash-can']"
									:class="
										checkPermission('system:profession:btn:delete')
											? 'disabled'
											: ''
									"
									style="color: var(--pitaya-btn-background)"
								/>
								<span
									class="table-inner-btn"
									:class="
										checkPermission('system:profession:btn:delete')
											? 'disabled'
											: ''
									"
									>移除</span
								>
							</el-button>
							<div v-if="!isCheckPermission('system:profession:btn:edit') && !isCheckPermission('system:profession:btn:delete')">---</div>
						</template>
					</PitayaTable>
				</el-scrollbar>
				<Drawer
					:size="drawerSize"
					v-model:drawer="showDrawer"
					:destroy-on-close="true"
				>
					<ProfessionDrawer
						v-if="showDrawer"
						:table-data="relatedDepartmentTableData"
						:current-tree="currentTreeNode"
						:form="editData"
						@on-close="closeAddDrawer"
					/>
				</Drawer>
				<Drawer
					:size="relatedDepartmentDrawerSize"
					v-model:drawer="showRelatedDepartmentDrawer"
				>
					<Title :title="relevanceDep" />
					<PitayaTable
						:columns="relatedDepartmentTableColumn"
						:table-data="relatedDepartmentDrawerTableData"
						:need-pagination="false"
						:need-index="true"
						:table-loading="relatedDepLoading"
					/>
				</Drawer>
			</ModelFrame>
		</div>
	</div>
</template>
<style lang="scss" scoped>
.app-container-row {
	align-items: center;
	justify-content: space-between;
	flex-direction: row;
	.left-model-frame {
		width: 20%;
		height: 100%;
		display: flex;
		flex-direction: column;
		.left-model-group {
			height: 0;
			flex: 1;
			overflow: hidden;
		}
	}
	.right-model-frame {
		width: calc(100% - 20% - 10px);
		display: flex;
		flex-direction: column;
		height: 100%;
	}
	.border-bottom-text {
		position: relative;
		color: #204a9c;
		text-decoration: underline;
		cursor: pointer;
	}
}
</style>
@/app/platform/hooks/usePagination @/app/platform/api/system/profession
