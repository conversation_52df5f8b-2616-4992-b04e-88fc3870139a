<script setup lang="ts">
import XEUtils from "xe-utils"
import { ProfessionApi } from "@/app/platform/api/system/profession"
import { CustomMessageBox } from "@/app/platform/utils/message"

interface Props {
	tableData: any[]
	tableLoading: boolean
}
const selectIds = ref<string[]>([])
const tableColumn: TableColumnType[] = [
	{
		label: "专业",
		align: "left",
		prop: "name",
		class: "tree-cell-flex"
	},
	{ label: "专业编码", prop: "code", width: "100px" },
	{
		label: "操作",
		prop: "operations",
		fixed: "right",

		needSlot: true,
		width: "100px"
	}
]
const containerTitle = {
	name: ["选择专业"],
	icon: ["fas", "square-share-nodes"]
}
const props = defineProps<Props>()
const tableValue = ref<any[]>([])
const handleDelete = (rowData: any) => {
	CustomMessageBox({ message: "确定要移除吗?" }, (res: boolean) => {
		if (res) {
			if (rowData.pid === 0) {
				tableValue.value = tableValue.value.filter(
					(item) => item.id !== rowData.id
				)
			} else {
				const resultArr = removeTreeItem(
					tableValue.value,
					rowData.id,
					"id",
					"pid",
					"children"
				)
				tableValue.value = resultArr
			}
			selectIds.value = XEUtils.toTreeArray(tableValue.value).map(
				(item) => item.id
			)
		}
	})
}
const chooseSubjects = () => {
	subjectDrawerState.value = true
	treeLoading.value=true
	ProfessionApi.getProfessionTree().then((res: any) => {
		depTreeData.value = res[0].children
		treeLoading.value=false
		checkRelevanceNode()
	})
}
const subjectDrawerState = ref(false)
const drawerSize = 310
const treeRef = ref<any>()
const treeLoading = ref<boolean>(false)
const depTreeData = ref<any[]>([])
const depTreeProp = {
	children: "children",
	label: "name"
}
const treeBtnList = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "确定",
		icon: ["fas", "floppy-disk"]
	}
]
const onTreeBtnClick = (btnName: string | undefined) => {
	if (btnName === "确定") {
		const currentSelectKeys = treeRef.value.PitayaTreeRef.getCheckedKeys()
		const result = treeRef.value.getDataObjIncludeCheckedNodes()
		tableValue.value = result
		tableSelect.value = currentSelectKeys
		selectIds.value = XEUtils.toTreeArray(result).map((item) => item.id)
		console.log(result, "!!!")
	}
	subjectDrawerState.value = false
}
const tableSelect = ref<any[]>([])
// 勾选相关节点
const checkRelevanceNode = () => {
	nextTick(() => {
		tableSelect.value = extractIds(tableValue.value)
		// tableSelect.value = tableValue.value
		// 	.flatMap((item: any) => item.children)
		// 	.map((item: any) => item.id)
		// 	console.log('===tableSelect.value',tableSelect.value)
		treeRef.value.setCheckedKeys(tableSelect.value)
	})
}
// 递归函数提取所有id
const extractIds = (items:any) => {
	// console.log('===items',items)
    let ids:any = [];
    items.forEach((item:any)=> {
        if (item.children && item.children.length > 0) {
            // 如果有子项，递归处理子项
            ids = ids.concat(extractIds(item.children));
        } else {
            // 如果没有子项，添加当前项的id
            ids.push(item.id);
        }
    });
    return ids;
};
// 树选择
const onTreeChange = (data: any, checked: boolean) => {}
watch(
	() => props.tableData,
	(val: any) => {
		tableValue.value = val
		if (val) {
			selectIds.value = XEUtils.toTreeArray(val).map((item) => item.id)
		} else {
			selectIds.value = []
		}
	},
	{ immediate: true }
)
defineExpose({
	selectIds: selectIds
})
</script>
<template>
<div>
	<PitayaTable
		:need-index="true"
		:table-data="tableValue"
		:columns="tableColumn"
		:need-pagination="false"
		:table-loading="props.tableLoading"
	>
		<template #operations="{ rowData }">
			<el-button v-btn color="var(--pitaya-btn-background)" link>
				<font-awesome-icon
					:icon="['fas', 'trash-can']"
					style="color: var(--pitaya-btn-background)"
				/>
				<span class="table-inner-btn" @click="handleDelete(rowData)">
					移除
				</span>
			</el-button>
		</template>
		<template #footerOperateLeft>
			<el-button v-btn style="border-radius: 0" @click="chooseSubjects">
				<font-awesome-icon :icon="['fas', 'square-plus']" style="color: #fff" />
				<span class="choose-line-btn"> 选择专业 </span>
			</el-button>
		</template>
	</PitayaTable>
	<Drawer
		class="drawer-hidden-box drawer-box"
		:size="drawerSize"
		v-model:drawer="subjectDrawerState"
		destroy-on-close
	>
		<Title :title="containerTitle" />
		<PitayaTree
			class="dep-tree"
			ref="treeRef"
			:need-check-box="true"
			:tree-data="depTreeData"
			:tree-props="depTreeProp"
			:tree-loading="treeLoading"
			:on-tree-change="onTreeChange"
			:defaultCheckedKeys="extractIds(tableValue)"
		/>
		<div class="btn-groups">
			<ButtonList :button="treeBtnList" @onBtnClick="onTreeBtnClick" />
		</div>
	</Drawer>
</div>
</template>
<style lang="scss" scoped>
.choose-line-btn {
	margin-left: 5px;
	color: #fff;
	font-weight: 500;
}
.inner-drawer {
	height: 100% !important;
	overflow: hidden !important;
}
.drawer-box {
	height: 100%;
	display: flex;
	flex-direction: column;
	.dep-tree {
		padding: 0 10px;
		height: calc(100% - 75px);
		display: flex;
		flex-direction: column;
	}

	.btn-groups {
		margin-top: 10px;
		bottom: 10px;
		right: 0;
		display: flex;
		justify-content: flex-end;
		padding: 10px 10px 0 0;
		box-sizing: border-box;
		width: 100%;
		border-top: 1px solid #ccc;
	}
}
</style>
