<script lang="ts">
export default {
	name: "teamGroupDrawer"
}
</script>
<script lang="ts" setup>
import { type FormInstance, type FormRules } from "element-plus"
import { DepartmentApi } from "@/app/platform/api/system/department"
import { TeamGroupApi } from "@/app/platform/api/system/teamGroup"
import { getAllPageUsers } from "@/app/platform/api/system/baseRole"
import { usePagination } from "@/app/platform/hooks/usePagination"
import { remove, reduce, map, cloneDeep } from "lodash-es"
import { ElMessage } from "element-plus"
import { CustomMessageBox } from "@/app/platform/utils/message"

import XEUtils from "xe-utils"

const drawerLoading = ref(false)
const treeLoading = ref(false)
const userTableDataLoading = ref(false)
const tableLoading = ref(false)

const leftContainerTitle = {
	name: ["班组信息"],
	icon: ["fas", "square-share-nodes"]
}
const rightContainerTitle = {
	name: ["扩展信息"],
	icon: ["fas", "square-share-nodes"]
}
const btnList = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "保存",
		icon: ["fas", "floppy-disk"]
	}
]
const tableColumns = {
	user: [
		{ label: "用户账户", prop: "username", width: 150 },
		{ label: "用户姓名", prop: "realname", needSlot: true, width: 200 },
		{ label: "部门", prop: "orgName" },
		{
			label: "操作",
			prop: "operations",
			fixed: "right",
			width: 120,
			needSlot: true
		}
	],
	profession: [
		{ label: "专业", prop: "name", align: "left", class: "tree-cell-flex" },
		{ label: "专业编码", prop: "code", width: 90 },
		{
			label: "操作",
			prop: "operations",
			fixed: "right",
			width: 90,
			needSlot: true
		}
	],
	location: [
		{ label: "专业", prop: "name", align: "left", class: "tree-cell-flex" },
		{ label: "专业编码", prop: "code", width: 90 },
		{
			label: "操作",
			prop: "operations",
			fixed: "right",
			width: 90,
			needSlot: true
		}
	],
	line: [
		{ label: "线路编码", prop: "code" },
		{ label: "线路名称", prop: "name", needSlot: true, width: 120 },
		{ label: "线路里程(KM)", prop: "length", width: 120 },
		{ label: "车站数量", prop: "stationNumber", width: 120 },
		{ label: "起始站", prop: "firstStation", width: 120 },
		{ label: "终点站", prop: "lastStation", width: 120 },
		{ label: "更新时间", prop: "lastModifiedDate", width: 280 },
		{
			label: "操作",
			prop: "operations",
			fixed: "right",
			width: 90,
			needSlot: true
		}
	]
}

const depTreeProp = {
	children: "children",
	label: "name"
}
const treeBtnList = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "确定",
		icon: ["fas", "floppy-disk"]
	}
]

const formModelData = reactive<anyKey>({
	orgCode: "",
	orgName: "",
	companyId: "",
	parentOrgId: "",
	parentOrgName: "",
	sortNum: "1000"
})
const rules = reactive<FormRules<typeof formModelData>>({
	orgName: [{ required: true, message: "请输入班组名称", trigger: "blur" }],
	parentOrgId: [
		{ required: true, message: "请选择班组归属部门", trigger: "blur" }
	]
})

const usePaginationStore = usePagination()
const usePaginationStoreForData = usePaginationStore.paginationData

const depTreeDrawerSize = ref<number>(310)
const tableColumnTypes = ref<any[]>([
	{ label: "专业", prop: "name", align: "left", class: "tree-cell-flex" },
	{ label: "专业编码", prop: "code", width: 90 },
	{
		label: "操作",
		prop: "operations",
		fixed: "right",
		width: 90,
		needSlot: true
	}
])
const chooseDepBtnList = ref<any[]>([
	{
		name: "选择专业",
		icon: ["fas", "circle-plus"]
	}
])
const ruleFormRef = ref<FormInstance>()
const drawerTableRef = ref<any>()
const tableDataSelf = ref<any[]>([])
const showChooseDepDrawer = ref<boolean>(false)
const showDepartDrawer = ref<boolean>(false)
const params = ref({ companyStatus: 0 }) //，表示是否显示公司停用  companyStatus ：0不显示
const depTreeData = ref<any[]>([])
const extendInfoType = ref<number>(0)
// const chooseResult = reactive({})
const pageSize = ref<number>(usePaginationStoreForData.pageSize)
const pageTotal = ref<number>(0)
const currentPage = ref<number>(usePaginationStoreForData.currentPage)
const treeRef = ref<any>()
// 编辑时的data
const currentDetailEdit = ref<any>()
const relatedIdObj = reactive<anyKey>({
	relatedLineIds: [], // 关联线路的id
	relatedPositionIds: [], // 关联位置的id
	relatedProfessionIds: [], // 关联专业的id
	relatedUserIds: [] //关联的用户id
})
// 关联的数组
const relatedObj = reactive<anyKey>({
	lines: [],
	profession: [],
	users: [],
	postion: []
})
// 用户列表
const queryArrList = [
	{
		name: "用户名",
		key: "realname",
		enableFuzzy: false,
		placeholder: "请输入查询关键字",
		type: "input"
	}
	// {
	// 	name: "用户账号",
	// 	key: "username",
	// 	placeholder: "请输入查询关键字",
	// 	type: "input"
	// }
]
const userTableData = ref<any[]>([])
const userTableColumn: TableColumnType[] = [
	{ label: "账号", prop: "username" },
	{ label: "姓名", prop: "realname" },
	{ label: "部门", prop: "orgName" }
]
const teamGroupTableRef = ref<any>()
const relatedDrawerTitle = ref<string>("")

const emits = defineEmits(["onCloseDrawer"])

// 保存
const onFormBtnClick = (btnName: string | undefined) => {
	if (btnName === "保存") {
		if (drawerLoading.value) return
		if (currentDetailEdit.value.companyId === formModelData.parentOrgId) {
			return ElMessage.error("当前级别不可新建班组")
		}
		ruleFormRef.value?.validate((valid) => {
			if (valid) {
				const linesArr = reduce(
					relatedIdObj.relatedLineIds,
					(result: any, value: string) => {
						result.push({
							id: value
						})
						return result
					},
					[]
				)
				// #endregion
				// #region 专业
				const idList = getIdList(relatedObj.profession, "id", "children")
				const subjectArr = idList
					.filter((id) => id != 0)
					.map((id) => {
						return { id: id }
					})
				// #endregion
				// #region 用户
				const baseUsersArr = relatedObj.users.map((user: any) => {
					return {
						id: user.id,
						teamUserType: user.teamUserType || "member",
						teamSharedType: user.teamSharedType || "member"
					}
				})
				// #endregion
				// #region 位置
				const idPostionList = getIdList(relatedObj.postion, "id", "children")
				const locationVosArr = idPostionList
					.filter((id) => id != 0)
					.map((id) => {
						return {
							id: id
						}
					})
				// #endregion
				const params = {
					...formModelData,
					parentOrg: {
						id: formModelData.parentOrgId
					},
					company: {
						id: formModelData.companyId
					},
					lines: linesArr,
					majors: subjectArr,
					baseUsers: baseUsersArr,
					locationVos: locationVosArr,
					teamed: true,
					id: currentDetailEdit.value?.id ?? ""
				}
				drawerLoading.value = true
				TeamGroupApi.saveTeamGroup(params)
					.then(() => {
						emits("onCloseDrawer", "success")
					})
					.finally(() => {
						drawerLoading.value = false
					})
			} else {
				return false
			}
		})
		return
	}
	if (btnName === "取消") {
		Object.keys(formModelData).map((key) => (formModelData[key] = ""))
		ruleFormRef.value?.clearValidate()
		emits("onCloseDrawer", "cancel")
		return
	}
}
// 移除
const onRowDelete = (row: any) => {
	CustomMessageBox({ message: "确定要移除吗?" }, (res: boolean) => {
		if (res) {
			if (extendInfoType.value === 0) {
				const resultArr = removeTreeItem(
					tableDataSelf.value,
					row.id,
					"id",
					"pid",
					"children"
				)
				const resultIds = remove(
					relatedIdObj.relatedProfessionIds,
					(id) => id !== row.id
				)
				tableDataSelf.value = resultArr
				relatedIdObj.relatedProfessionIds = resultIds
				relatedObj.profession = resultArr
				if (row.pid === null) {
					tableDataSelf.value = []
					relatedIdObj.relatedProfessionIds = []
					relatedObj.profession = []
				}
			}
			if (extendInfoType.value === 1) {
				const resultArr = removeTreeItem(
					tableDataSelf.value,
					row.id,
					"id",
					"pid",
					"children"
				)
				const resultIds = remove(
					relatedIdObj.relatedPositionIds,
					(id) => id !== row.id
				)
				tableDataSelf.value = resultArr
				relatedIdObj.relatedPositionIds = resultIds
				relatedObj.postion = resultArr
				if (row.pid === null) {
					tableDataSelf.value = []
					relatedIdObj.relatedPositionIds = []
					relatedObj.postion = []
				}
			}
			if (extendInfoType.value === 2) {
				const findLocation = relatedObj.postion.find(
					(item: any) => item.lineId === row.id
				)
				if (findLocation) {
					ElMessage.warning(
						"该线路已关联位置[" + findLocation.allName + "],请先移除位置"
					)
					return
				}
				// 线路
				relatedIdObj.relatedLineIds = remove(
					relatedIdObj.relatedLineIds,
					(id) => id !== row.id
				)
				const newSelect = remove(
					tableDataSelf.value,
					(item) => item.id !== row.id
				)
				tableDataSelf.value = newSelect
				relatedObj.lines = newSelect
			}

			if (extendInfoType.value === 3) {
				relatedIdObj.relatedUserIds = remove(
					relatedIdObj.relatedUserIds,
					(id) => id !== row.id
				)
				const newSelect = remove(
					tableDataSelf.value,
					(item) => item.id !== row.id
				)
				tableDataSelf.value = newSelect
				relatedObj.users = newSelect
				if (row.pid === null) {
					tableDataSelf.value = []
					relatedObj.users = []
				}
				drawerTableRef.value.clearSelectedTableData()
			}
		}
	})
}
// 获取专业表
const getProfessionTableData = () => {
	tableColumnTypes.value = tableColumns.profession
	chooseDepBtnList.value = tabBtnList.majorTab
	tableDataSelf.value = relatedObj.profession
}

// 获取管辖线路表
const getJurisdictionalLineTableData = () => {
	tableColumnTypes.value = tableColumns.line
	chooseDepBtnList.value = tabBtnList.lineTab
	tableDataSelf.value = cloneDeep(relatedObj.lines) ?? []
}
const tabBtnList = {
	lineTab: [
		{
			name: "选择线路",
			icon: ["fas", "circle-plus"]
		}
	],
	majorTab: [
		{
			name: "选择专业",
			icon: ["fas", "circle-plus"]
		}
	],
	memberTab: [
		{
			name: "选择本部门人员",
			icon: ["fas", "circle-plus"]
		},
		{
			name: "添加外部人员",
			icon: ["fas", "circle-plus"]
		},
		{
			name: "设为组长",
			icon: ["fas", "user"]
		},
		// {
		// 	name: "设为共享",
		// 	icon: ["fas", "share-nodes"]
		// }
	]
}
const currentTableSelect = ref<any>()
const onTableSelectionChange = (selection: any) => {
	if (extendInfoType.value !== 3) {
		return
	}
	chooseDepBtnList.value[2].name = "设为组长"
	chooseDepBtnList.value[2].class = ""
	chooseDepBtnList.value[3].name = "设为共享"
	chooseDepBtnList.value[3].class = ""
	currentTableSelect.value = {}

	if (selection && selection.length > 0 && tableDataSelf.value.length > 0) {
		currentTableSelect.value = selection[0]
		if (currentTableSelect.value.teamUserType === "manager") {
			chooseDepBtnList.value[2].name = "取消组长"
			chooseDepBtnList.value[2].class = "red-btn"
		}
		if (currentTableSelect.value.teamSharedType === "shared") {
			chooseDepBtnList.value[3].name = "取消共享"
			chooseDepBtnList.value[3].class = "red-btn"
		}
	}
}

// 选择关联账户表格内容
const getAssociatedAccountTableData = () => {
	tableColumnTypes.value = tableColumns.user
	chooseDepBtnList.value = tabBtnList.memberTab
	tableDataSelf.value = relatedObj.users ?? []
}
// 选择专业树
const getProfessionTreeData = () => {
	treeLoading.value = true
	DepartmentApi.getProfessionForDepartment(formModelData.parentOrgId)
		.then((res: any) => {
			depTreeData.value = res
			if (relatedIdObj.relatedProfessionIds.length > 0) {
				nextTick(() => {
					treeRef.value.PitayaTreeRef.setCheckedKeys(
						relatedIdObj.relatedProfessionIds
					)
				})
			}
		})
		.finally(() => {
			treeLoading.value = false
		})
}
// 选择线路树
const getLineTreeData = () => {
	const params = {
		orgId: formModelData.parentOrgId
	}
	treeLoading.value = true
	DepartmentApi.getDepartmentLine(params)
		.then((res: any) => {
			depTreeData.value = res
			nextTick(() => {
				if (relatedIdObj.relatedLineIds.length > 0) {
					treeRef.value.PitayaTreeRef.setCheckedKeys(
						relatedIdObj.relatedLineIds,
						true
					)
				}
			})
		})
		.finally(() => {
			treeLoading.value = false
		})
}
const queryUserName = ref<string|undefined>('');
const queryRealname = ref<string|undefined>('')
const queryGetSelectUsers = (params?: any) =>{
	currentPage.value = 1
	queryUserName.value = params?.username
	queryRealname.value = params?.realname
	teamGroupTableRef.value.resetCurrentPage()
	getSelectUsers(params)
}
// 选择用户
const getSelectUsers = (params?: any) => {
	userTableDataLoading.value = true
	getAllPageUsers({
		username: params?.username || queryUserName.value,
		realname: params?.realname || queryRealname.value,
		shared: sharedUserParams.value,
		currentPage: currentPage.value,
		pageSize: pageSize.value,
		orgId: formModelData?.parentOrgId,
		companyId: formModelData?.companyId
	})
		.then((res: any) => {
			pageTotal.value = res.records
			userTableData.value = res.rows
			nextTick(() => {
				if (relatedIdObj.relatedUserIds.length > 0) {
					treeRef.value.PitayaTreeRef.setCheckedKeys(
						relatedIdObj.relatedUserIds,
						true
					)
				}
			})
		})
		.finally(() => {
			userTableDataLoading.value = false
		})
}
const sharedUserParams = ref<boolean>(false)
// 选择树 回调
const chooseDepBtnClick = (btnName: any) => {
	depTreeData.value = []
	relatedDrawerTitle.value = btnName
	if (!formModelData.parentOrgId) {
		ElMessage.error("请选择班组归属部门")
		return
	}
	if (btnName === "选择专业") {
		depTreeDrawerSize.value = 310
		getProfessionTreeData()
		showChooseDepDrawer.value = true
	} else if (btnName === "选择线路") {
		depTreeDrawerSize.value = 310
		getLineTreeData()
		showChooseDepDrawer.value = true
	} else if (btnName === "选择本部门人员") {
		depTreeDrawerSize.value = 750
		sharedUserParams.value = false
		getSelectUsers()
		showChooseDepDrawer.value = true
	} else if (btnName === "添加外部人员") {
		//班组用户类型：member普通，manager组长,shared共享,outMember外部门成员
		depTreeDrawerSize.value = 750
		sharedUserParams.value = true
		getSelectUsers()
		showChooseDepDrawer.value = true
	} else {
		const selected = drawerTableRef.value.getSelectedTable()
		if (selected.length === 0) {
			ElMessage.error("请选择要操作的用户")
			return
		}
		if (selected.length > 1) {
			ElMessage.error(`每次只能选择一个用户${btnName}!`)
			return
		}
		if (btnName === "设为组长") {
			CustomMessageBox(
				{ message: `确定要将${selected[0].realname}设为组长吗?` },
				(res: boolean) => {
					if (res) {
						selected[0].teamUserType = "manager"
						onTableSelectionChange(selected)
					}
				}
			)
		} else if (btnName === "设为共享") {
			CustomMessageBox(
				{ message: `确定要将${selected[0].realname}设为共享吗?` },
				(res: boolean) => {
					if (res) {
						selected[0].teamSharedType = "shared"
						onTableSelectionChange(selected)
					}
				}
			)
		} else if (btnName === "取消组长") {
			CustomMessageBox(
				{ message: `确定要将${selected[0].realname}取消组长吗?` },
				(res: boolean) => {
					if (res) {
						selected[0].teamUserType = "member"
						onTableSelectionChange(selected)
					}
				}
			)
		} else if (btnName === "取消共享") {
			CustomMessageBox(
				{ message: `确定要将${selected[0].realname}取消共享吗?` },
				(res: boolean) => {
					if (res) {
						selected[0].teamSharedType = "member"
						onTableSelectionChange(selected)
					}
				}
			)
		}
	}
}
// 二级抽屉提交
const onTreeBtnClick = (btnName: string | undefined) => {
	if (btnName === "确定") {
		// 关联专业
		if (extendInfoType.value === 0) {
			const selectIds = treeRef.value.PitayaTreeRef.getCheckedKeys()
			relatedIdObj.relatedProfessionIds = selectIds
			const result = treeRef.value.getDataObjIncludeCheckedNodes()
			tableDataSelf.value = result
			relatedObj.profession = result
		}
		// 管辖线路
		if (extendInfoType.value === 2) {
			const currentSelectKeys = treeRef.value.PitayaTreeRef.getCheckedKeys()
			const currentSelectNodes = treeRef.value.PitayaTreeRef.getCheckedNodes()
			relatedIdObj.relatedLineIds = currentSelectKeys
			tableDataSelf.value = currentSelectNodes
			tableColumnTypes.value = tableColumns.line
			relatedObj.lines = cloneDeep(currentSelectNodes)
		}
		// 关联用户
		if (extendInfoType.value === 3) {
			tableColumnTypes.value = tableColumns.user
			const currentSelectRows = teamGroupTableRef.value.getSelectedTable()
			const newUsers = currentSelectRows.filter(
				(item: any) => !relatedIdObj.relatedUserIds.includes(item.id)
			)
			tableDataSelf.value.push(...newUsers)
			relatedObj.users = tableDataSelf.value
			relatedIdObj.relatedUserIds = map(currentSelectRows, "id")
		}
	}
	beforeClose()
}
// 扩展信息类型切换
const changeExtendInfoType = (type: number) => {
	extendInfoType.value = type
	if (type === 0) {
		getProfessionTableData()
	} else if (type === 2) {
		getJurisdictionalLineTableData()
	} else if (type === 3) {
		getAssociatedAccountTableData()
	}
}
// 获取班组关联的专业
const getProfessionByDepId = (id: any) => {
	tableLoading.value = true
	DepartmentApi.getProfessionForDepartment(id)
		.then((res: any) => {
			const temp = findAllEndNode(res, "id", "children")
			relatedIdObj.relatedProfessionIds.push(...temp)
			relatedObj.profession = res
			tableDataSelf.value = res
		})
		.finally(() => {
			tableLoading.value = false
		})
}
// 班组关联用户
const getUserForTeamGroup = (id: any) => {
	tableLoading.value = true
	TeamGroupApi.getUserByTeamGroupId(id)
		.then((res: any) => {
			relatedObj.users = res
			relatedIdObj.relatedUserIds = map(res, "id")
		})
		.finally(() => {
			tableLoading.value = false
		})
}
// 分页
const onCurrentPageChange = (pageData: any) => {
	currentPage.value = pageData.currentPage
	pageSize.value = pageData.pageSize
	getSelectUsers()
}
// 树选择
const onTreeChange = (data: any, checked: boolean) => {
	if (extendInfoType.value !== 1) return
	selectTreeNodeByNoRelevance(
		treeRef.value.PitayaTreeRef,
		depTreeData.value,
		data,
		checked
	)
}
const handleShowDepartment = () => {
	showDepartDrawer.value = true
}
const onDepartBtnClick = (selected: any) => {
	if (selected) {
		// 保存
		//选择的是部门
		if (selected.disabled) return
		if (!selected.company) {
			formModelData.parentOrgId = selected.id
			formModelData.parentOrgName = selected.allName
			formModelData.companyId = selected.companyId
			showDepartDrawer.value = false
			ruleFormRef.value?.clearValidate("parentOrgId")
		} else {
			ElMessage.error("当前级别不可新建班组")
		}
	} else {
		//取消
		showDepartDrawer.value = false
	}
}
const init = (val: any) => {
	currentDetailEdit.value = val
	if (val.id) {
		relatedIdObj.relatedLineIds = map(val.lines, (line) => line.id)
		relatedObj.lines = val.lines
		val.majorNum > 0 && getProfessionByDepId(val.id)
		val.userNum > 0 && getUserForTeamGroup(val.id)
	}
	formModelData.orgCode = val.orgCode
	formModelData.orgName = val.orgName
	formModelData.parentOrgId = val.parentOrgId
	formModelData.companyId = val.companyId
	formModelData.parentOrgName = val.orgAllName.replace("-" + val.orgName, "")
	formModelData.sortNum = val.sortNum
}


const beforeClose = () => {
	currentPage.value = 1
	pageSize.value = 20
	queryUserName.value = undefined
	queryRealname.value = undefined
	showChooseDepDrawer.value = false
}
// 递归函数提取所有id
const extractIds = (items:any) => {
    let ids:any = [];
    items.forEach((item:any)=> {
        if (item.children && item.children.length > 0) {
            // 如果有子项，递归处理子项
            ids = ids.concat(extractIds(item.children));
        } else {
            // 如果没有子项，添加当前项的id
            ids.push(item.id);
        }
    });
    return ids;
}
// 计算默认展开节点
const expandedKeys = computed(() => {
  // 优先使用筛选后的父节点，其次使用历史记录
  return [formModelData.companyId,formModelData.parentOrgId]
});

// 计算默认选中节点
const selectedKeys = computed(() => {
  // 类型转换确保字符串类型
  const currentId = formModelData.parentOrgId?.toString();
  
  return currentId ? 
    [currentId] : 
    ([]);
});
onMounted(() => {
	getProfessionTableData()
})
defineOptions({
	name: "DepartmentDrawer"
})
defineExpose({ init })
</script>
<template>
	<div class="department-drawer-container" v-loading="drawerLoading">
		<div class="drawer-left">
			<Title :title="leftContainerTitle" />
			<div class="drawer-left-form">
				<el-form
					ref="ruleFormRef"
					:model="formModelData"
					:rules="rules"
					label-position="top"
				>
					<el-form-item label="归属部门" prop="parentOrgId">
						<el-input
							@click="handleShowDepartment"
							:disabled="currentDetailEdit && currentDetailEdit.id"
							v-model="formModelData.parentOrgName"
							readonly
							placeholder="请输入归属部门"
						>
							<template #append>
								<font-awesome-icon
									:icon="['fas', 'layer-group']"
									style="color: #ccc"
									@click="handleShowDepartment"
								/>
							</template>
						</el-input>
					</el-form-item>
					<el-form-item label="班组编码">
						<el-input
							disabled
							v-model.trim="formModelData.orgCode"
							placeholder="班组编码自动生成"
							maxlength="50"
							:show-word-limit="true"
						/>
					</el-form-item>
					<el-form-item label="班组名称" prop="orgName">
						<el-input
							v-model.trim="formModelData.orgName"
							placeholder="请输入班组名称"
							maxlength="50"
							:show-word-limit="true"
						/>
					</el-form-item>
					<el-form-item label="排序" prop="sortNum">
						<el-input-number
							style="width: 100%"
							controls-position="right"
							v-model.trim="formModelData.sortNum"
							:min="1"
							:max="10000"
						/>
					</el-form-item>
				</el-form>
			</div>
			<div class="btn-groups">
				<ButtonList
					:button="btnList"
					:loading="drawerLoading"
					@onBtnClick="onFormBtnClick"
				/>
			</div>
		</div>
		<div class="drawer-right">
			<Title :title="rightContainerTitle">
				<div class="type-choose">
					<div
						class="drawer-right-subtitle"
						:class="{ 'type-is-active': extendInfoType === 0 }"
						@click="changeExtendInfoType(0)"
					>
						关联专业
					</div>
					<div
						class="drawer-right-subtitle"
						:class="{ 'type-is-active': extendInfoType === 2 }"
						@click="changeExtendInfoType(2)"
					>
						管辖线路
					</div>
					<div
						class="drawer-right-subtitle"
						:class="{ 'type-is-active': extendInfoType === 3 }"
						@click="changeExtendInfoType(3)"
					>
						关联账户
					</div>
				</div>
			</Title>
			<PitayaTable
				ref="drawerTableRef"
				:table-data="tableDataSelf"
				:columns="tableColumnTypes"
				:need-index="true"
				:need-selection="extendInfoType === 3"
				:table-loading="tableLoading"
				single-select
				@on-selection-change="onTableSelectionChange"
			>
				<template #name="{ rowData }">
					<div class="line-name-container">
						<el-tag
							:style="{
								backgroundColor: rowData.colour,
								color: '#FFF',
								borderColor: rowData.colour
							}"
						>
							{{ rowData.name }}
						</el-tag>
					</div>
				</template>
				<template #realname="{ rowData }">
					{{ rowData.realname }}
					<el-tag v-if="rowData.teamUserType === 'manager'">组长</el-tag>
					<el-tag v-if="rowData.teamSharedType === 'shared'" type="info"
						><font-awesome-icon
							:icon="['fas', 'share-nodes']"
							style="color: var(--pitaya-btn-background)"
					/></el-tag>
					<el-tag v-if="rowData.teamSharedType === 'outMember'" type="warning"
						>外部</el-tag
					>
				</template>
				<template #operations="{ rowData }">
					<el-button v-btn link @click="onRowDelete(rowData)">
						<font-awesome-icon
							:icon="['fas', 'trash-can']"
							style="color: var(--pitaya-btn-background)"
						/>
						<span class="table-inner-btn"> 移除 </span>
					</el-button>
				</template>
				<template #footerOperateLeft>
					<ButtonList
						:button="chooseDepBtnList"
						:isNotRadius="true"
						@onBtnClick="chooseDepBtnClick"
					/>
				</template>
			</PitayaTable>
		</div>
		<Drawer
			:size="depTreeDrawerSize"
			:destroyOnClose="true"
			:isBeforeClose="true"
			v-if="showChooseDepDrawer"
			v-model:drawer="showChooseDepDrawer"
			@beforeClose="beforeClose"

		>
			<div class="drawer-box">
				<Title
					:title="{
						name: [relatedDrawerTitle],
						icon: ['fas', 'square-share-nodes']
					}"
				>
					<template #title>
						<div v-if="relatedDrawerTitle == '选择专业'">
							<el-tooltip
								class="box-item"
								effect="dark"
								content="仅显示上级部门关联的专业"
								placement="bottom"
							>
								<font-awesome-icon
									:icon="['far', 'fa-question-circle']"
									style="color: #666; margin: 0 10px"
								/>
							</el-tooltip>
						</div>
						<div v-if="relatedDrawerTitle == '选择线路'">
							<el-tooltip
								class="box-item"
								effect="dark"
								content="仅显示上级部门关联的线路"
								placement="bottom"
							>
								<font-awesome-icon
									:icon="['far', 'fa-question-circle']"
									style="color: #666; margin: 0 10px"
								/>
							</el-tooltip>
						</div>
					</template>
				</Title>
				<PitayaTree
					v-if="extendInfoType !== 3"
					ref="treeRef"
					:need-check-box="true"
					:tree-data="depTreeData"
					:tree-props="depTreeProp"
					:check-strictly="extendInfoType === 1"
					:tree-loading="treeLoading"
					:defaultCheckedKeys="extractIds(relatedObj.profession)"
				/>
				<Query
					v-if="extendInfoType == 3"
					style="margin: 10px 0 -10px 10px"
					:queryArrList="queryArrList"
					@getQueryData="queryGetSelectUsers"
				/>
				<PitayaTable
					v-if="extendInfoType == 3"
					ref="teamGroupTableRef"
					:table-data="userTableData"
					:columns="userTableColumn"
					:total="pageTotal"
					:need-selection="true"
					:need-index="true"
					@on-current-page-change="onCurrentPageChange"
					:table-loading="userTableDataLoading"
				/>
				<div class="btn-groups">
					<ButtonList :button="treeBtnList" @onBtnClick="onTreeBtnClick" />
				</div>
			</div>
		</Drawer>
		<Drawer :size="depTreeDrawerSize" v-model:drawer="showDepartDrawer">
			<div class="drawer-box">
				<Title
					:title="{
						name: ['选择部门'],
						icon: ['fas', 'square-share-nodes']
					}"
				/>
				<DepartmentTree
					:params="params"
					:multiSelect="false"
					:drawerState="showDepartDrawer"
					:default-expanded-keys="expandedKeys"
					:defaultSelectedKeys="selectedKeys"
					@onBtnClick="onDepartBtnClick"
				/>
			</div>
		</Drawer>
	</div>
</template>
<style lang="scss" scoped>
:deep(.red-btn) {
	background-color: #e25e59 !important;
}
.department-drawer-container {
	display: flex;
	align-items: center;
	position: relative;
	height: 100%;
	:deep(.el-input-group__append) {
		padding: 0 9px !important;
	}
	.drawer-left {
		position: relative;
		padding-right: 10px;
		box-sizing: border-box;
		width: 300px;
		height: 100%;
		.drawer-left-form {
			padding: 0 10px;
			margin-top: 10px;
		}
		.btn-groups {
			display: flex;
			justify-content: flex-end;
			position: absolute;
			bottom: 0;
			padding: 10px 10px 0 0;
			left: 0;
			right: 10px;
			width: auto;
			border-top: 1px solid #ccc;
		}
	}
	.drawer-left::after {
		content: "";
		position: absolute;
		right: 0;
		top: -15px;
		width: 1px;
		height: calc(100% + 25px);
		background: #ccc;
	}
	.drawer-right {
		position: relative;
		padding-left: 10px;
		box-sizing: border-box;
		width: 0;
		flex: 1;
		height: 100%;
		.type-choose {
			display: flex;
			position: absolute;
			left: 130px;
			right: 0;
			.drawer-right-subtitle {
				margin-right: 40px;

				color: #666666;
				cursor: pointer;
			}
			.drawer-right-subtitle.type-is-active {
				color: var(--pitaya-btn-background);
			}
			.drawer-right-subtitle.type-is-active::after {
				content: "";
				position: absolute;
				left: 0;
				bottom: -10px;
				width: 100%;
				height: 2px;
				background-color: var(--pitaya-btn-background);
			}
		}
		.line-name-container {
			display: flex;
			align-items: center;
			justify-content: center;
			.line-item {
				height: 22px;
				display: flex;
				align-items: center;
				justify-content: center;
				padding: 0 10px;
				border-radius: 3px;
				font-size: var(--pitaya-fs-12);
				color: #fff;
			}
		}
	}
}
.drawer-box {
	height: 100%;
	display: flex;
	flex-direction: column;
	.dep-tree {
		flex: 1;
	}

	.btn-groups {
		width: 100%;
		position: absolute;
		margin-top: 10px;
		bottom: 0px;
		right: 0px;
		display: flex;
		justify-content: flex-end;
		padding: 10px 10px 0 0;
		box-sizing: border-box;
		border-top: 1px solid #ccc;
	}
}
</style>
