<script lang="ts" setup>
import { ElMessage, type FormInstance, type FormRules } from "element-plus"
import CompanyLineTable from "./companyLineTable.vue"
import CompanySubjectTable from "./companySubjectTable.vue"
import { CompanyApi } from "@/app/platform/api/system/company"
import { getDictionaryTerm } from "@/app/platform/api/system/dictionary"
import { CustomMessageBox } from "@/app/platform/utils/message"

import XEUtils from "xe-utils"
const loading = ref(false)
const drawerLeftTitle = {
	name: ["公司信息"],
	icon: ["fas", "square-share-nodes"]
}
const drawerRightTitle = {
	name: ["扩展信息"],
	icon: ["fas", "square-share-nodes"]
}
const leftBtns = [
	{ name: "取消", icon: ["fas", "circle-minus"] },
	{ name: "保存", icon: ["fas", "floppy-disk"] }
]
const props = defineProps({
	id: {
		type: [String, Number],
		required: false
	}
})
const formModal = reactive({
	id: props.id,
	companyCode: "",
	companyName: "",
	companyShortName: "",
	sortNum: "1000",
	lines: [],
	majors: [],
	companyType: ""
})
const formRules = reactive<FormRules<typeof formModal>>({
	companyCode: [
		{ required: true, message: "公司编码不能为空", trigger: "blur" },
		{ validator: validateCodeName, required: true, trigger: "blur" }
	],
	companyName: [
		{ required: true, message: "公司名称不能为空", trigger: "blur" }
	],
	companyShortName: [
		{ required: true, message: "公司简称不能为空", trigger: "blur" }
	],
	companyType: [
		{ required: true, message: "公司类型不能为空", trigger: "blur" }
	]
})
const drawerLoading = ref(false)
const companyLineTableLoading = ref(false)
const ruleFormRef = ref<FormInstance>()
const lineTableData = ref<any[]>([])
const subjectTableData = ref<any[]>([])
const companyLineTableRef = ref<any>()
const compySubjectTableRef = ref<any>()
const emits = defineEmits(["onSaveOrClose"])
const companyTypeOptions = ref<any[]>([])

const companyId = toRef(props, "id")
const getLineTableData = () => {
	companyLineTableLoading.value = true
	CompanyApi.getCompanyInfo(companyId.value)
		.then((res: any) => {
			lineTableData.value = res.lines
			if (res.majors) {
				subjectTableData.value = XEUtils.toArrayTree(res.majors, {
					parentKey: "pid"
				})
				console.log(subjectTableData.value)
			}
			formModal.companyCode = res.companyCode
			formModal.companyName = res.companyName
			formModal.companyShortName = res.companyShortName
			formModal.sortNum = res.sortNum
			formModal.companyType = res.companyType
		})
		.finally(() => {
			companyLineTableLoading.value = false
		})
}
// 左侧按钮点击
const leftBtnClick = (btnName: string | undefined) => {
	if (!ruleFormRef.value) return
	if (btnName === "保存") {
		ruleFormRef.value.validate((valid) => {
			if (valid) {
				const lines = companyLineTableRef.value.selectIds
				// 线路保存为[{id:}]的形式
				formModal.lines = lines.map((lineId: string) => {
					return { id: lineId }
				})
				const majors = compySubjectTableRef.value.selectIds
				formModal.majors = majors.map((majorId: string) => {
					return { id: majorId }
				})
				loading.value = true
				drawerLoading.value = true
				CompanyApi.saveCompany({ ...formModal })
					.then(() => {
						const msg = companyId.value ? "修改成功" : "保存成功"
						ElMessage.success(msg)
						emits("onSaveOrClose", "success")
					})
					.catch((err) => {
						if (Object.prototype.toString.call(err) !== "[object Error]") {
							CustomMessageBox({
								type: "error",
								title: "更新失败",
								message: err,
								showCancelButton: false
							})
						}
					})
					.finally(() => {
						loading.value = false
						drawerLoading.value = false
					})
			} else {
				return false
			}
		})
		return
	}
	if (btnName === "取消") {
		ruleFormRef.value?.clearValidate()
		emits("onSaveOrClose", "cacenl")
		return
	}
}

// 获取公司类型字典
const getCompanyTypeSelects = () => {
	getDictionaryTerm({ dataDictionaryCode: "COMPANY_TYPE" }).then((res: any) => {
		companyTypeOptions.value = res
	})
}
//扩展栏标签页切换
const activeTab = ref<number>(0)
const handleTabChange = (index: number) => {
	activeTab.value = index
}

getCompanyTypeSelects()

watch(
	() => props.id,
	(id: any) => {
		if (id) {
			getLineTableData()
		}
	},
	{ immediate: true }
)

defineOptions({
	name: "CompanyDrawer"
})
</script>
<template>
	<div class="company-drawer-container" v-loading="drawerLoading">
		<div class="company-drawer-left">
			<Title :title="drawerLeftTitle" />
			<el-form
				class="form-container"
				:model="formModal"
				:rules="formRules"
				ref="ruleFormRef"
				label-position="top"
				label-width="100px"
			>
				<el-form-item label="公司编码" prop="companyCode">
					<el-input
						v-model.trim="formModal.companyCode"
						maxlength="50"
						:show-word-limit="true"
					/>
				</el-form-item>
				<el-form-item label="公司名称" prop="companyName">
					<el-input
						v-model.trim="formModal.companyName"
						maxlength="50"
						:show-word-limit="true"
					/>
				</el-form-item>
				<el-form-item label="公司简称" prop="companyShortName">
					<el-input
						v-model.trim="formModal.companyShortName"
						maxlength="50"
						:show-word-limit="true"
					/>
				</el-form-item>
				<el-form-item label="公司类型" prop="companyType">
					<el-select
						style="width: 100%"
						v-model="formModal.companyType"
						placeholder="请选择公司类型"
					>
						<el-option
							v-for="item in companyTypeOptions"
							:key="item.id"
							:label="item.subitemName"
							:value="Number(item.subitemValue)"
						/>
					</el-select>
				</el-form-item>
				<el-form-item label="排序" prop="sortNum">
					<el-input-number
						style="width: 100%"
						controls-position="right"
						v-model="formModal.sortNum"
						:min="1"
						:max="10000"
					/>
				</el-form-item>
			</el-form>
			<ButtonList
				class="btn-list"
				:button="leftBtns"
				:loading="loading"
				@on-btn-click="leftBtnClick"
			/>
		</div>
		<div class="company-drawer-right">
			<Title :title="drawerRightTitle">
				<Tabs
					style="position: absolute; left: 140px"
					:tabs="['管辖线路', '关联专业']"
					@on-tab-change="handleTabChange"
				/>
			</Title>
			<div v-show="activeTab === 0" class="company-drawer-table">
				<CompanyLineTable
					ref="companyLineTableRef"
					:table-data="lineTableData"
					:table-loading="companyLineTableLoading"
				/>
			</div>
			<div v-show="activeTab === 1" class="company-drawer-table">
				<CompanySubjectTable
					ref="compySubjectTableRef"
					:table-data="subjectTableData"
					:table-loading="companyLineTableLoading"
				/>
			</div>
		</div>
	</div>
</template>
<style lang="scss" scoped>
.company-drawer-container {
	display: flex;
	align-items: center;
	height: 100%;
	.company-drawer-left {
		display: flex;
		flex-direction: column;
		position: relative;
		padding-right: 10px;
		box-sizing: border-box;
		width: 310px;
		height: 100%;
		.form-container {
			flex: 1;
			margin-top: 10px;
			padding: 10px;
			box-sizing: border-box;
		}
		.btn-list {
			display: flex;
			justify-content: flex-end;
			border-top: 1px solid #ccc;
			padding: 10px 10px 0 0;
			box-sizing: border-box;
		}
	}
	.company-drawer-left::after {
		content: "";
		position: absolute;
		right: 0;
		top: -10px;
		width: 1px;
		height: calc(100% + 20px);
		background-color: #ccc;
	}
	.company-drawer-right {
		position: relative;
		// margin-top: 15px;
		margin-left: 10px;
		height: 100%;
		width: calc(100% - 320px);
		.title-tabs {
			position: absolute;
			left: 130px;
			cursor: pointer;
		}
		.title-tabs::after {
			content: "";
			position: absolute;
			left: 0;
			bottom: -10px;
			height: 2px;
			width: 100%;
			background-color: var(--pitaya-btn-background);
		}
		// .company-drawer-table {
		// }
	}
}
</style>
