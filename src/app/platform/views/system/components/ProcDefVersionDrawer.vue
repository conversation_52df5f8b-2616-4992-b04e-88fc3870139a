<script lang="ts" setup>
import { getProcessDesignList } from "@/app/platform/api/system/processDesign"
import BPMNPreview from "@/app/platform/views/system/bpmn/preview.vue"
const props = defineProps({
	procDefKey: {
		type: [String, Number],
		required: false
	}
})
const title = {
	name: ["流程版本"],
	icon: ["fas", "square-share-nodes"]
}
const previewTitle = {
	name: ["流程图"],
	icon: ["fas", "square-share-nodes"]
}

const tableLoading = ref(false)

const tableData = ref<any[]>([])
const tableColumn = ref<any[]>([
	{ prop: "key", label: "流程定义编码", width: 200 },
	{ prop: "name", label: "流程名称" },
	{ prop: "version", label: "版本号",		needSlot: true, },
	{
		prop: "operations",
		fixed: "right",
		label: "操作",
		needSlot: true,
		width: 140
	}
])
const pageInfo = ref<any>({
	currentPage: 1,
	pageSize: 20,
	total: 0
})
const showPreview = ref(false)
const procDefKey = ref<string>("")

function getProcessDesignData(queryData: any) {
	tableLoading.value = true
	getProcessDesignList({
		...queryData,
		pageSize: pageInfo.value.pageSize,
		currentPage: pageInfo.value.currentPage
	})
		.then((res) => {
			console.log(res)
			tableData.value = res.rows
			pageInfo.value.total = res.records
		})
		.finally(() => {
			tableLoading.value = false
		})
}
const onCurrentPageChange = (pageData: any) => {
	pageInfo.value.pageSize = pageData.pageSize
	pageInfo.value.currentPage = pageData.currentPage
	getProcessDesignData()
}
const onRowShow = (rowData: any) => {
	console.log(rowData)
	showPreview.value = true
	procDefKey.value = rowData.id
}

watch(
	() => props.procDefKey,
	(procDefKey: any) => {
		if (procDefKey) {
			getProcessDesignData({ procDefKey: procDefKey })
		}
	},
	{ immediate: true }
)

defineOptions({
	name: "ProcDefVersionDrawer"
})
</script>

<template>
	<Title :title="title" />
	<PitayaTable2
		ref="tableInner"
		:table-data="tableData"
		:columns="tableColumn"
		:need-index="true"
		:need-pagination="true"
		:total="pageInfo.total"
		@on-current-page-change="onCurrentPageChange"
		:table-loading="tableLoading"
	>
	<template #version="{ rowData }">
			V{{rowData.version}}
		</template>
		<template #operations="{ rowData }">
			<el-button v-btn link @click="onRowShow(rowData)">
				<font-awesome-icon
					:icon="['fas', 'eye']"
					style="color: var(--pitaya-btn-background)"
				/>
				<span class="table-inner-btn"> 查看 </span>
			</el-button>
		</template>
	</PitayaTable2>
	<Drawer :size="650" :destroyOnClose="true" v-model:drawer="showPreview">
		<Title :title="previewTitle" />
		<BPMNPreview :processDefinitionId="procDefKey" />
	</Drawer>
</template>

<style scoped lang="scss"></style>
@/app/platform/api/system/processDesign
