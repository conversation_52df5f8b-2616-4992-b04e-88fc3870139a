<script lang="ts" setup>
import { LineApi } from "@/app/platform/api/system/line"
import { remove, map } from "lodash-es"
import { CustomMessageBox } from "@/app/platform/utils/message"

interface Props {
	tableData: any[]
	tableLoading: boolean
}
interface Tree {
	id: number | string
	label: string
	children?: Tree[]
}
const depTreeProp = {
	children: "children",
	label: "name"
}
const tableColumn: TableColumnType[] = [
	{ label: "线路编码", prop: "code" },
	{ label: "线路名称", prop: "name", needSlot: true, minWidth: 150 },
	{ label: "线路里程(KM)", prop: "length", width: 120 },
	{ label: "车站数量", prop: "stationNumber", width: 100 },
	{ label: "起始站", prop: "firstStation", width: 120 },
	{ label: "终点站", prop: "lastStation", width: 120 },
	{
		label: "操作",
		prop: "operations",
		fixed: "right",
		width: 100,
		needSlot: true
	}
]
const drawerSize = 310
const innerDrawerTitle = {
	name: ["选择线路"],
	icon: ["fas", "square-share-nodes"]
}
const btnList = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "确定",
		icon: ["fas", "floppy-disk"]
	}
]
const treeLoading = ref(false)
const props = defineProps<Props>()
const tableValue = ref<any[]>([])
const showChooseLineDrawer = ref<boolean>(false)
const lineTreeData = ref<Tree[]>([])
const treeRef = ref<any>()
const selectIds = ref<string[]>([])

const onRowDelete = (row: any) => {
	CustomMessageBox({ message: "确定要移除吗?" }, (res: boolean) => {
		if (res) {
			const newKeysArr = remove(selectIds.value, (id) => row.id !== id)
			const newSelectNodes = remove(
				tableValue.value,
				(node) => row.id !== node.id
			)
			selectIds.value = newKeysArr
			tableValue.value = newSelectNodes
		}
	})
}
// 选择线路
const chooseLine = () => {
	getLineTreeData()
	showChooseLineDrawer.value = true
	// 重新打开选择线路勾选回显
	nextTick(() => {
		if (selectIds.value.length > 0) {
			treeRef.value.PitayaTreeRef.setCheckedKeys(selectIds.value, true)
		}
	})
}
// 获取线路树
const getLineTreeData = () => {
	treeLoading.value = true
	LineApi.getLineList(
		objectToFormData({
			currentPage: 1,
			pageSize: 500
		})
	)
		.then((res: any) => {
			lineTreeData.value = res.rows ?? []
		})
		.finally(() => {
			treeLoading.value = false
		})
}
// 弹窗按钮
const onBtnClick = (btnName: string | undefined) => {
	if (btnName === "确定") {
		// 将勾选的key和节点存入
		const currentSelectKeys = treeRef.value.PitayaTreeRef.getCheckedKeys()
		const currentSelectNodes = treeRef.value.PitayaTreeRef.getCheckedNodes()
		selectIds.value = currentSelectKeys
		tableValue.value = currentSelectNodes
	}
	showChooseLineDrawer.value = false
}
// 树过滤
const filterNode = (value: string, data: anyKey) => {
	if (!value) return true
	return data.label.includes(value)
}

watch(
	() => props.tableData,
	(val: any) => {
		tableValue.value = val
		selectIds.value = val && val.length > 0 ? map(val, (item) => item.id) : []
	},
	{ immediate: true }
)
defineExpose({
	selectIds: selectIds
})
defineOptions({
	name: "CompanyLineTable"
})
</script>
<template>
	<div class="company-line-table">
		<PitayaTable
			:need-index="true"
			:table-data="tableValue"
			:columns="tableColumn"
			:need-pagination="false"
			:table-loading="props.tableLoading"
		>
			<template #name="{ rowData }">
				<div>
					<el-tag
						class="line-item-btn"
						:style="{
							backgroundColor: rowData.colour,
							color: '#FFF',
							border: 'unset'
						}"
						>{{ rowData.name }}</el-tag
					>
				</div>
			</template>
			<template #operations="{ rowData }">
				<el-button v-btn color="var(--pitaya-btn-background)" link>
					<font-awesome-icon
						:icon="['fas', 'trash-can']"
						style="color: var(--pitaya-btn-background)"
					/>
					<span class="table-inner-btn" @click="onRowDelete(rowData)"
						>移除</span
					>
				</el-button>
			</template>
			<template #footerOperateLeft>
				<el-button v-btn style="border-radius: 0" @click="chooseLine">
					<font-awesome-icon
						:icon="['fas', 'square-plus']"
						style="color: #fff"
					/>
					<span class="choose-line-btn">选择线路</span>
				</el-button>
			</template>
		</PitayaTable>
		<Drawer
			v-if="showChooseLineDrawer"
			class="inner-drawer drawer-hidden-box"
			:size="drawerSize"
			v-model:drawer="showChooseLineDrawer"
		>
			<Title :title="innerDrawerTitle" />
			<PitayaTree
				ref="treeRef"
				:need-check-box="true"
				:tree-data="lineTreeData"
				:tree-props="depTreeProp"
				:filter-node-method="filterNode"
				:tree-loading="treeLoading"
			/>
			<div class="btn-list">
				<ButtonList :button="btnList" @onBtnClick="onBtnClick" />
			</div>
		</Drawer>
	</div>
</template>
<style lang="scss" scoped>
:deep(.el-tree-node__content) {
	height: 32px;
}
:deep(.el-tree-node__label) {
	font-size: var(--pitaya-fs-12);
}
.choose-line-btn {
	margin-left: 5px;
	color: #fff;
	font-weight: 500;
}
.inner-drawer {
	position: relative;
	height: 100%;
	.tree-search {
		margin-top: 10px;
		padding: 0 10px;
	}
	.el-tree {
		margin-top: 10px;
	}
	.pitaya-tree-container {
		padding-bottom: 53px;
	}
	.btn-list {
		display: flex;
		justify-content: flex-end;
		position: fixed;
		right: 10px;
		bottom: 0px;
		z-index: 99;
		padding: 10px 10px 10px 0;
		box-sizing: border-box;
		width: 290px;
		border-top: 1px solid #ccc;
		background-color: #fff;
	}
}
</style>
@/app/platform/api/system/line
