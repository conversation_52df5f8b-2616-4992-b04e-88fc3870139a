<script lang="ts" setup>
import type { FormInstance, FormRules } from "element-plus"
import { getDictionaryTerm } from "@/app/platform/api/system/dictionary"
const formLabelProp: anyKey[] = [
	{
		label: "回调地址",
		prop: "url",
		type: "input",
		placeholder: "请输入回调地址"
	},
	{
		label: "调用方式",
		prop: "callMethod",
		type: "select",
		dictionaryType: "CALL_METHOD",
		placeholder: "请选择",
		children: [
			{
				label: "全部",
				value: ""
			},
		]
	},
	{
		label: "回调接口名称",
		prop: "name",
		type: "input",
		placeholder: "请输入回调接口名称"
	},
	{
		label: "所属模块",
		prop: "module",
		type: "select",
		dictionaryType: "module",
		placeholder: "请选择",
		children: [
			{
				label: "全部",
				value: ""
			}
		]
	},
	{
		label: "回调类型",
		prop: "callType",
		type: "select",
		dictionaryType: "CALL_TYPE",
		placeholder: "请选择",
		children: [
			{
				label: "全部",
				value: ""
			}
		]
	},
	{
		label: "监听类型",
		prop: "listenerType",
		type: "select",
		dictionaryType: "LISTENTER_TYPE",
		placeholder: "请选择",
		children: [
			{
				label: "全部",
				value: ""
			}
		]
	},
	{
		label: "事件类型",
		prop: "eventType",
		type: "select",
		dictionaryType: "TaskListener",
		placeholder: "请选择",
		children: [
			{
				label: "全部",
				value: ""
			}
		]
	},
	{
		label: "备注说明",
		prop: "notes",
		type: "textarea",
		placeholder: "请输入备注说明"
	}
]
const props = defineProps({
	formValue: {
		type: Object,
		default: () => {},
		requeired: false
	}
})

const dictionaryTypeList = ref<any[]>([
	{
		label: "调用方式",
		code: "CALL_METHOD",
		children: [
			{
				label: "全部",
				value: ""
			}
		]
	},
	{
		label: "所属模块",
		code: "module",
		children: [
			{
				label: "全部",
				value: ""
			}
		]
	},
	{
		label: "回调类型",
		code: "CALL_TYPE",
		children: [
			{
				label: "全部",
				value: ""
			}
		]
	},
	{
		label: "监听类型",
		code: "LISTENTER_TYPE",
		children: [
			{
				label: "全部",
				value: ""
			}
		]
	},
	{
		label: "事件类型",
		code: "TaskListener",
		children: [
			{
				label: "全部",
				value: ""
			}
		]
	},
	{
		label: "事件类型",
		code: "ExecutionListener",
		children: [
			{
				label: "全部",
				value: ""
			}
		]
	}
])

const getAllDictionaryList = () => {
	dictionaryTypeList.value.forEach((dItem: any) => {
		getDictionaryTerm({
			dataDictionaryCode: dItem.code
		}).then((dItemList: any) => {
			if (dItemList && dItemList.length) {
				const children = [
					{
						label: "全部",
						value: ""
					}
				]
				dItemList.forEach((item: any) => {
					children.push({
						label: item.subitemName,
						value: item.subitemValue
					})
				})
				dItem.children = children
			}
			console.log('dictionaryTypeList===', dictionaryTypeList)
		})
	})
}
const getOneDic = (code: string) => {
	let ret: any[] = []
	const curDic = dictionaryTypeList.value.filter((item: any) => {
		return item.code === code
	})
	if (curDic && curDic.length) {
		ret = curDic[0].children.slice(1, curDic[0].length)
	}
	return ret
}
const getTwoDic = (code: string, listenerType: any) => {
	console.log('getTwoDic====', code, listenerType)
	if(listenerType != '') {
		let ret: any[] = []
		const curDic = dictionaryTypeList.value.filter((item: any) => {
			return item.code === code
		})
		if (curDic && curDic.length) {
			ret = curDic[0].children.slice(1, curDic[0].length)
		}
		return ret
	} else {
		return []
	}

}
onMounted(() => {
	console.log('onMounted=====')
	getAllDictionaryList()
})
const formData: anyKey = reactive({
	id: "",
	url: "",
  name: "",
	module: "",
	callMethod: "",
	callType:"",
	listenerType: "",
	eventType:"",
	notes:""
})

const formRules = reactive<FormRules<typeof formData>>({
	url: [{ validator: validateEmpty, required: true, trigger: "blur" }],
	callMethod: [{ validator: validateEmpty, required: true, trigger: "blur" }],
	name: [{ validator: validateEmpty, required: true, trigger: "blur" }],
	module: [{ validator: validateEmpty, required: true, trigger: "blur" }],
	callType: [{ validator: validateEmpty, required: true, trigger: "blur" }],
	listenerType: [{ validator: validateEmpty, required: true, trigger: "blur" }],
	eventType: [{ validator: validateEmpty, required: true, trigger: "blur" }]
})

const emit = defineEmits(["onSubmit", "onCancel"])

const ruleFormRef = ref<FormInstance>()

//
function onChangeSelect(val: any) {
	console.log('val=====', val)
	formLabelProp.forEach((item: any) => {
		if (item.label === "事件类型") {
			item.dictionaryType = val
			formData.eventType = ''
		}
		return item
	})
	console.log('formLabelProp=====', formLabelProp)
	// formModelData.value.orgId = ""
}
// 表单提交
const onFormSubmt = (formEl: FormInstance | undefined) => {
	if (!formEl) return
	formEl.validate((valid) => {
		if (valid) {
			const params = {
				...formData
			}
			emit("onSubmit", { ...params })
		} else {
			console.log("error submit!")
			return false
		}
	})
}
// 取消
const onFormCancel = () => {
	emit("onCancel")
}
watch(
	() => props.formValue,
	(val: any) => {
		if (val.id) {
			formData.id = val.id
			formData.url = val.url
			formData.callMethod = val.callMethod
			formData.name = val.name
			formData.module = val.module
			formData.callType = val.callType
			formData.listenerType = val.listenerType
			formData.eventType = val.eventType
			formData.notes = val.notes
		}
	},
	{ immediate: true }
)
defineOptions({
	name: "CallbackForm"
})

</script>
<template>
	<div class="line-form">
		<div class="line-form-container">
			<el-form
				class="form-container"
				ref="ruleFormRef"
				label-position="top"
				label-width="100px"
				:model="formData"
				:rules="formRules"
			>

				<el-form-item
					v-for="(item, index) in formLabelProp"
					:key="index"
					:label="item.label"
					:prop="item.prop"
				>
					<el-input
						v-if="item.type === 'input'"
						v-model="formData[item.prop]"
						:placeholder="item.placeholder"
						clearable
					/>
					<el-select
						v-if="item.type === 'select' && item.prop === 'eventType'"
						style="width: 100%;"
						v-model="formData[item.prop]"
						:placeholder="item.placeholder"
						clearable
					>
						<el-option
							v-for="(item, index) in getTwoDic(item.dictionaryType, formData.listenerType)"
							:key="index"
							:label="item.label"
							:value="item.value"
						/>
					</el-select>
					<el-select
						v-if="item.type === 'select' && item.prop === 'listenerType'"
						style="width: 100%;"
						v-model="formData[item.prop]"
						:placeholder="item.placeholder"
						@change="onChangeSelect"
						clearable
					>
						<el-option
							v-for="(item, index) in getOneDic(item.dictionaryType)"
							:key="index"
							:label="item.label"
							:value="item.value"
						/>
					</el-select>
					<el-select
						v-if="item.type === 'select' && item.prop != 'listenerType' && item.prop != 'eventType'"
						style="width: 100%;"
						v-model="formData[item.prop]"
						:placeholder="item.placeholder"
						clearable
					>
						<el-option
							v-for="(item, index) in getOneDic(item.dictionaryType)"
							:key="index"
							:label="item.label"
							:value="item.value"
						/>
					</el-select>
					<el-input
							v-if="item.type === 'textarea'"
							v-model="formData[item.prop]"
							:rows="3"
							type="textarea"
							maxlength="200"
							:show-word-limit="true"
							:placeholder="item.placeholder"
						/>
				</el-form-item>
			</el-form>
		</div>
		<div class="btn-groups">
			<el-button
				v-btn
				type="primary"
				color="var(--pitaya-btn-background)"
				@click="onFormCancel"
			>
				<font-awesome-icon :icon="['fas', 'circle-minus']" />
				<span class="btn-text">取消</span>
			</el-button>
			<el-button
				v-btn
				type="primary"
				color="var(--pitaya-btn-background)"
				@click="onFormSubmt(ruleFormRef)"
			>
				<font-awesome-icon :icon="['fas', 'floppy-disk']" />
				<span class="btn-text">保存</span>
			</el-button>
		</div>
	</div>
</template>
<style lang="scss" scoped>
.line-form {
	display: flex;
	flex-direction: column;
	height: calc(100% - 45px);
	.line-form-container {
		flex: 1;
		display: flex;
		flex-direction: column;
		margin-top: 10px;
		padding: 0 10px;
		.form-container {
			flex: 1;
			.color-input-form {
				width: 100%;
				.color-input {
					position: relative;
				}
				.color-show {
					position: absolute;
					right: 5px;
					top: 50%;
					transform: translateY(-50%);
					width: 25px;
					height: 25px;
					border-radius: 2px;
				}
			}
			::v-deep(.el-form-item) {
				.el-form-item__label::before {
					font-size: 16px;
				}
			}
		}
	}
}
.btn-groups {
	display: flex;
	justify-content: flex-end;
	padding-top: 10px;
	box-sizing: border-box;
	border-top: 1px solid #ccc;
	.btn-text {
		margin-left: 5px;
	}
}
</style>
@/app/platform/api/system/dictionary
