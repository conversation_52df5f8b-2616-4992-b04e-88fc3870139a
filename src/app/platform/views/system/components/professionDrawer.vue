<script lang="ts" setup>
import { type FormInstance, type FormRules } from "element-plus"
import { DepartmentApi } from "@/app/platform/api/system/department"
import { ProfessionApi } from "@/app/platform/api/system/profession"
import { CustomMessageBox } from "@/app/platform/utils/message"
// import { reduce, union } from "lodash-es"

interface Props {
	tableData: any[]
	currentTree?: any
	form?: any
}

const drawerLoading = ref(false)

const leftContainerTitle = {
	name: ["专业信息"],
	icon: ["fas", "square-share-nodes"]
}
const _rightContainerTitle = {
	name: ["扩展信息"],
	icon: ["fas", "square-share-nodes"]
}
const btnList = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "保存",
		icon: ["fas", "floppy-disk"]
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      	}
]
const _chooseDepBtnList = [
	{
		name: "选择部门",
		icon: ["fas", "circle-plus"]
	}
]
const _tableColumnType: TableColumnType[] = [
	{ label: "部门", prop: "name", align: "left", class: "tree-cell-flex" },
	{ label: "部门编码", prop: "code", width: 90 },
	{
		label: "操作",
		prop: "operations",
		fixed: "right",
		width: 90,
		needSlot: true
	}
]
const _depTreeProp = {
	children: "children",
	label: "name"
}
const _depTreeDrawerSize = 310
const _treeBtnList = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "保存",
		icon: ["fas", "floppy-disk"]
	}
]

const emits = defineEmits(["onClose"])
const formModelData = reactive<anyKey>({
	code: "",
	name: "",
	remark: "",
	id: "",
	sortNum: 1000,
})
const rules = reactive<FormRules<typeof formModelData>>({
	code: [
		{ required: true, message: "请输入专业编码", trigger: "blur" },
		{ validator: validateCodeName, required: true, trigger: "blur" }
	],
	name: [{ required: true, message: "请输入专业名称", trigger: "blur" }]
})

const props = defineProps<Props>()
const ruleFormRef = ref<FormInstance>()
const tableDataSelf = ref<any[]>([])
const showChooseDepDrawer = ref<boolean>(false)
const depTreeData = ref<any[]>([])
// 部门树ref
const depTreeRef = ref<any>()
// 勾选的部门id
const depSelectIds = ref<any[]>([])
// 部门回显
const editDepObj = ref<any>([])

// 按钮点击
const onFormBtnClick = (btnName: string | undefined) => {
	if (btnName === "保存") {
		ruleFormRef.value?.validate((valid) => {
			if (valid) {
				// 关联部门
				// let newOrgIds = reduce(
				// 	depSelectIds.value,
				// 	(result: any, value: string) => {
				// 		result.push(value)
				// 		return result
				// 	},
				// 	[]
				// )
				// const tempId: any[] = []
				// editDepObj.value.forEach((pro: any) => {
				// 	if (pro.pid == 0) {
				// 		// newOrgIds.push(pro.id)
				// 		editDepObj.value[0].children.forEach((item: any) => {
				// 			if (item.children && item.children.length > 0) {
				// 				newOrgIds.push(item.id)
				// 				if (pro.id === item.id) {
				// 					tempId.push(pro.id)
				// 				}
				// 			}
				// 		})
				// 	} else {
				// 		newOrgIds.push(pro.id)
				// 		pro.children.forEach((item: any) => {
				// 			newOrgIds.push(item.id)
				// 			if (pro.id === item.id) {
				// 				tempId.push(pro.id)
				// 			}
				// 		})
				// 	}
				// })
				// newOrgIds = union(newOrgIds, "id")
				const params = {
					...formModelData,
					// orgIds: newOrgIds,
					parent: { id: props.currentTree.id }
				}
				drawerLoading.value = true
				ProfessionApi.saveProfession(params)
					.then(() => {
						emits("onClose", "success")
					})
					.finally(() => {
						drawerLoading.value = false
					})
			} else {
				return false
			}
		})
		return
	}
	if (btnName === "取消") {
		Object.keys(formModelData).map((key) => (formModelData[key] = ""))
		ruleFormRef.value?.clearValidate()
		emits("onClose", "cancel")
		return
	}
}
// 移除
const _onRowDelete = (row: any) => {
	CustomMessageBox({ message: "确定要移除吗?" }, (res: boolean) => {
		if (res) {
			const resultArr = removeTreeItem(
				tableDataSelf.value,
				row.id,
				"id",
				"pid",
				"children"
			)
			const allResultTree = getIdList(resultArr)
			const resultIds = secIdList(allResultTree, depSelectIds.value)
			tableDataSelf.value = resultArr
			depSelectIds.value = resultIds
			editDepObj.value = resultIds
		}
	})
}
// 部门树
const getDepTreeData = () => {
	DepartmentApi.getDepartmentTree().then((res: any) => {
		depTreeData.value = [res]
	})
}
// 选择部门
const _chooseDepBtnClick = (btnName: string | undefined) => {
	if (btnName === "选择部门") {
		getDepTreeData()
		showChooseDepDrawer.value = true
		nextTick(() => {
			depSelectIds.value.length > 0 &&
				depTreeRef.value.PitayaTreeRef.setCheckedKeys(depSelectIds.value)
		})
	}
}
// 部门树选择
const _onTreeBtnClick = (btnName: string | undefined) => {
	if (btnName === "保存") {
		// 获取当前 选中id
		const selectIds = depTreeRef.value.PitayaTreeRef.getCheckedKeys()
		depSelectIds.value = selectIds
		getNodeList(
			depTreeRef.value.PitayaTreeRef,
			(resultTree: any) => {
				tableDataSelf.value = resultTree
				editDepObj.value = resultTree
			},
			"id",
			"pid",
			"children"
		)
	}
	showChooseDepDrawer.value = false
}
// 获取关联的部门
const getOrgById = (id: any) => {
	ProfessionApi.getOrgInfo(id).then((res: any) => {
		tableDataSelf.value = [res]
		editDepObj.value = [res]
	})
}

watch(
	() => props.tableData,
	(val: any) => {
		tableDataSelf.value = val
	},
	{ immediate: true }
)

watch(
	() => props.form,
	(val: any) => {
		if (val) {
			formModelData.code = val.code
			formModelData.name = val.name
			formModelData.remark = val.remark
			formModelData.id = val.id
			formModelData.sortNum = val.sortNum ? val.sortNum : 1000

			if (val.orgCount > 0) {
				getOrgById(val.id)
			}
		}
	},
	{ immediate: true }
)

defineOptions({
	name: "ProfessionDrawer"
})
</script>
<template>
	<div class="profession-drawer-container" v-loading="drawerLoading">
		<div class="drawer-left">
			<Title :title="leftContainerTitle" />
			<div class="drawer-left-form">
				<el-form
					ref="ruleFormRef"
					:model="formModelData"
					:rules="rules"
					label-position="top"
				>
					<el-form-item label="专业编码" prop="code">
						<el-input
							v-model.trim="formModelData.code"
							placeholder="请输入专业编码"
							maxlength="50"
							:show-word-limit="true"
						/>
					</el-form-item>
					<el-form-item label="专业名称" prop="name">
						<el-input
							v-model.trim="formModelData.name"
							placeholder="请输入专业名称"
							maxlength="50"
							:show-word-limit="true"
						/>
					</el-form-item>
					<el-form-item label="备注信息" :prop="formModelData.remark">
						<el-input
							v-model.trim="formModelData.remark"
							:rows="3"
							type="textarea"
							maxlength="200"
							:show-word-limit="true"
							placeholder="请输入备注"
						/>
					</el-form-item>
					<el-form-item label="排序">
						<el-input-number
							v-model="formModelData.sortNum"
							style="width: 100%"
							controls-position="right"
							:min="1"
							:max="10000"
						/>
					</el-form-item>
				</el-form>
			</div>
			<div class="btn-groups">
				<ButtonList :button="btnList" @onBtnClick="onFormBtnClick" />
			</div>
		</div>
		<!-- <div class="drawer-right">
			<Title :title="rightContainerTitle">
				<div class="drawer-right-subtitle">关联部门</div>
			</Title>
			<PitayaTable
				:table-data="tableDataSelf"
				:columns="tableColumnType"
				:need-index="true"
			>
				<template #operations="{ rowData }">
					<el-button v-btn link @click="onRowDelete(rowData)">
						<font-awesome-icon
							:icon="['fas', 'trash-can']"
							style="color: var(--pitaya-btn-background)"
						/>
						<span class="table-inner-btn"> 移除 </span>
					</el-button>
				</template>
				<template #footerOperateLeft>
					<ButtonList
						:button="chooseDepBtnList"
						:isNotRadius="true"
						@onBtnClick="chooseDepBtnClick"
					/>
				</template>
			</PitayaTable>
		</div>
		<Drawer :size="depTreeDrawerSize" v-model:drawer="showChooseDepDrawer">
			<Title
				:title="{
					name: ['选择部门'],
					icon: ['fas', 'square-share-nodes']
				}"
			/>
			<PitayaTree
				ref="depTreeRef"
				:need-check-box="true"
				:tree-data="depTreeData"
				:tree-props="depTreeProp"
			/>
			<div class="btn-groups">
				<ButtonList :button="treeBtnList" @onBtnClick="onTreeBtnClick" />
			</div>
		</Drawer> -->
	</div>
</template>
<style lang="scss" scoped>
.profession-drawer-container {
	display: flex;
	align-items: center;
	position: relative;
	height: 100%;
	overflow: hidden;
	.drawer-left {
		position: relative;
		box-sizing: border-box;
		width: 300px;
		height: 100%;
		.drawer-left-form {
			padding: 0 10px;
			margin-top: 10px;
		}
		.btn-groups {
			display: flex;
			justify-content: flex-end;
			position: absolute;
			bottom: 0;
			padding: 10px 10px 0 0;
			left: 0;
			right: 0;
			width: auto;
			border-top: 1px solid #ccc;
		}
	}
	// .drawer-left::after {
	// 	content: "";
	// 	position: absolute;
	// 	right: 0;
	// 	top: -15px;
	// 	width: 1px;
	// 	height: calc(100% + 30px);
	// 	background: #ccc;
	// }
	.drawer-right {
		position: relative;
		padding-left: 10px;
		box-sizing: border-box;
		width: 0;
		flex: 1;
		height: 100%;
		.drawer-right-subtitle {
			position: absolute;
			left: 130px;
		}
		.drawer-right-subtitle::after {
			content: "";
			position: absolute;
			left: 0;
			bottom: -10px;
			width: 100%;
			height: 2px;
			background-color: var(--pitaya-btn-background);
		}
	}
}
.btn-groups {
	position: absolute;
	bottom: 10px;
	left: 10px;
	right: 10px;
	display: flex;
	justify-content: flex-end;
	padding-top: 10px;
	box-sizing: border-box;
	width: auto;
	border-top: 1px solid #ccc;
}
:deep(.el-textarea__inner) {
	/* 针对 WebKit 浏览器隐藏滚动条 */
	-webkit-scrollbar: none;
	/* Firefox */
	scrollbar-width: none; /* Firefox 64+ */
}
</style>
@/app/platform/api/system/department@/app/platform/api/system/profession
