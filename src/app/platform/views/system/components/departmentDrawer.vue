<script lang="ts" setup>
import type { FormInstance, FormRules } from "element-plus"
import { DepartmentApi } from "@/app/platform/api/system/department"
import { ProfessionApi } from "@/app/platform/api/system/profession"
import { remove, reduce, map, cloneDeep } from "lodash-es"
import { getValueArrarByKey } from "@/app/platform/utils/tree"
import { validateCodeName } from "@/app/platform/utils/validate"
import { CustomMessageBox } from "@/app/platform/utils/message"
import { baseUserApi } from "@/app/platform/api/system/baseUser"
interface Props {
	currentNode: any
	fomrData: any
}

const drawerLoading = ref(false)
const treeLoading = ref(false)

const leftContainerTitle = {
	name: ["部门信息"],
	icon: ["fas", "square-share-nodes"]
}
const rightContainerTitle = {
	name: ["扩展信息"],
	icon: ["fas", "square-share-nodes"]
}
const btnList = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "保存",
		icon: ["fas", "floppy-disk"]
	}
]
const depTreeProp = {
	children: "children",
	label: "name"
}
const depTreeDrawerSize = 310
const treeBtnList = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "确定",
		icon: ["fas", "floppy-disk"]
	}
]

const formModelData = reactive<anyKey>({
	orgId:"",
	orgCode: "",
	orgName: "",
	sortNum: "1000"
})
const rules = reactive<FormRules<typeof formModelData>>({
	companyId: [{ required: true, message: "请选择所属公司", trigger: "blur" }],
	orgCode: [
		{ required: true, message: "请输入部门编码", trigger: "blur" },
		{
			validator: validateCodeName,
			trigger: "blur"
		}
	],
	orgName: [{ required: true, message: "请输入部门名称", trigger: "blur" }]
})
const tableColumnTypes = ref<TableColumnType[]>([
	{ label: "专业", prop: "name", align: "left", class: "tree-cell-flex" },
	{ label: "专业编码", prop: "code", width: 90 },
	{
		label: "操作",
		prop: "operations",
		fixed: "right",
		width: 90,
		needSlot: true
	}
])
const chooseDepBtnList = ref<any[]>([
	{
		name: "选择专业",
		icon: ["fas", "circle-plus"]
	}
])
const props = defineProps<Props>()
const ruleFormRef = ref<FormInstance>()
const tableDataSelf = ref<any[]>([])
const showChooseDepDrawer = ref<boolean>(false)
const depTreeData = ref<any[]>([])
const extendInfoType = ref<number>(0)
// const chooseResult = reactive({})
const treeRef = ref<any>()
// 编辑时的data
const currentDetailEdit = ref<any>()
const relatedIdObj = reactive<anyKey>({
	relatedLineIds: [], // 关联线路的id
	relatedPositionIds: [], // 关联位置的id
	relatedProfessionIds: [] //关联专业的id
})
// 关联的数组
const relatedObj = reactive<anyKey>({
	lines: [],
	postion: [],
	profession: []
})
const relatedDrawerTitle = ref<string>("")

const emits = defineEmits(["onCloseDrawer"])

// 提交保存
const onFormBtnClick = (btnName: string | undefined) => {
	if (btnName === "保存") {
		ruleFormRef.value?.validate((valid) => {
			if (valid) {
				const currentNode = props.currentNode
				// #region 线路
				const linesArr = reduce(
					relatedIdObj.relatedLineIds,
					(result: any, value: string) => {
						result.push({
							id: value
						})
						return result
					},
					[]
				)
				// #endregion
				// #region 专业
				const idList = getIdList(relatedObj.profession, "id", "children")
				const idListResult = remove(idList, (id) => id != 0)
				const majorArr = idListResult.map((id) => {
					return { id: id }
				})
				// #endregion
				// #region 位置
				const idPostionList = getIdList(relatedObj.postion, "id", "children")
				const idPostionListResult = remove(idPostionList, (id) => id != 0)
				const locationVosArr = reduce(
					idPostionListResult,
					(result: any, value: string) => {
						result.push({
							id: value
						})
						return result
					},
					[]
				)
				// return console.log('===currentNode.pid',currentNode,'formModelData==',formModelData)
				// #endregion
				const params = {
					...formModelData,
					parentOrg:
					formModelData.orgId?
					{
						id: formModelData.orgId?formModelData.orgId:currentNode.id
					}: null,
					company: {
						id: formModelData.companyId
					},
					lines: linesArr,
					majors: majorArr,
					locationVos: locationVosArr,
					id: currentDetailEdit.value?.id ?? ""
				}
				drawerLoading.value = true
				DepartmentApi.saveDepartment(params)
					.then(() => {
						emits("onCloseDrawer", "success")
					})
					.catch((err) => {
						if (Object.prototype.toString.call(err) !== "[object Error]") {
							CustomMessageBox({
								type: "error",
								title: "更新失败",
								message: err,
								showCancelButton: false
							})
						}
					})
					.finally(() => {
						drawerLoading.value = false
					})
				return true
			} else {
				return false
			}
		})
		return
	}
	if (btnName === "取消") {
		Object.keys(formModelData).map((key) => (formModelData[key] = ""))
		ruleFormRef.value?.clearValidate()
		emits("onCloseDrawer", "cacenl")
		return
	}
}
// 移除
const onRowDelete = (row: any) => {
	CustomMessageBox({ message: "确定要移除吗?" }, (res: boolean) => {
		if (res) {
			if (extendInfoType.value === 0) {
				const resultArr = removeTreeItem(
					tableDataSelf.value,
					row.id,
					"id",
					"pid",
					"children"
				)
				const resultIds = getIdList(resultArr)
				tableDataSelf.value = resultArr
				relatedIdObj.relatedPositionIds = resultIds
				relatedObj.profession = resultArr
			}
			if (extendInfoType.value === 1) {
				const resultArr = removeTreeItem(
					tableDataSelf.value,
					row.id,
					"id",
					"pid",
					"children"
				)
				const resultIds = getIdList(resultArr)
				tableDataSelf.value = resultArr
				relatedIdObj.relatedPositionIds = resultIds
				relatedObj.postion = resultArr
			}
			if (extendInfoType.value === 2) {
				relatedIdObj.relatedLineIds = remove(
					relatedIdObj.relatedLineIds,
					(id) => id !== row.id
				)
				const newSelect = remove(
					tableDataSelf.value,
					(item) => item.id !== row.id
				)
				tableDataSelf.value = newSelect
				relatedObj.lines = newSelect
			}
		}
	})
}
// 获取专业表
const getProfessionTableData = () => {
	tableColumnTypes.value = [
		{
			label: "专业",
			prop: "name",
			align: "left",
			class: "tree-cell-flex"
		},
		{ label: "专业编码", prop: "code", width: 90 },
		{
			label: "操作",
			prop: "operations",
			fixed: "right",
			width: 90,
			needSlot: true
		}
	]
	chooseDepBtnList.value = [
		{
			name: "选择专业",
			icon: ["fas", "circle-plus"]
		}
	]
	tableDataSelf.value = relatedObj.profession
}
// 获取管辖线路表
const getJurisdictionalLineTableData = () => {
	tableColumnTypes.value = [
		{ label: "线路编码", prop: "code", width: 90 },
		{ label: "线路名称", prop: "name", needSlot: true },
		{ label: "线路里程(KM)", prop: "length", width: 130 },
		{ label: "车站数量", prop: "stationNumber", width: 80 },
		{ label: "起始站", prop: "firstStation", width: 80 },
		{ label: "结束站", prop: "lastStation", width: 80 },
		{
			label: "操作",
			prop: "operations",
			fixed: "right",
			width: 90,
			needSlot: true
		}
	]
	chooseDepBtnList.value = [
		{
			name: "选择线路",
			icon: ["fas", "circle-plus"]
		}
	]
	tableDataSelf.value = cloneDeep(relatedObj.lines) ?? []
}
// 选择专业树
const getProfessionTreeData = () => {
	const curNode = props.currentNode
	if (curNode.pid === null) {
		treeLoading.value = true
		ProfessionApi.getProfessionTreeV2(curNode.companyId)
			.then((res: any) => {
				depTreeData.value = res || []
			})
			.finally(() => {
				treeLoading.value = false
			})
	} else {
		treeLoading.value = true
		DepartmentApi.getProfessionForDepartment(curNode.id)
			.then((res: any) => {
				depTreeData.value = res || []
			})
			.finally(() => {
				treeLoading.value = false
			})
	}
}
// 选择线路树
const getLineTreeData = () => {
	const params = {
		orgId: props.currentNode.pid === null ? "" : props.currentNode.id,
		companyId: props.currentNode.pid === null ? props.currentNode.companyId : ""
	}
	treeLoading.value = true
	DepartmentApi.getDepartmentLine(params)
		.then((res: any) => {
			depTreeData.value = res || []
			relatedIdObj.relatedProfessionIds.push(
				...getValueArrarByKey(res, "id", "children")
			)
		})
		.finally(() => {
			treeLoading.value = false
		})
}
// 选择树 回调
const chooseDepBtnClick = (btnName: any) => {
	depTreeData.value = []
	relatedDrawerTitle.value = btnName
	if (btnName === "选择专业") {
		getProfessionTreeData()
	} else if (btnName === "选择线路") {
		getLineTreeData()
	}
	showChooseDepDrawer.value = true
}
// 二级抽屉提交
const onTreeBtnClick = (btnName: string | undefined) => {
	console.log("ddd", btnName, extendInfoType.value)
	if (btnName === "确定") {
		// 关联专业
		if (extendInfoType.value === 0) {
			const selectIds = treeRef.value.PitayaTreeRef.getCheckedKeys()
			relatedIdObj.relatedProfessionIds = selectIds
			const result = treeRef.value.getDataObjIncludeCheckedNodes()
			tableDataSelf.value = result
			relatedObj.profession = result
		}
		// 管辖线路
		if (extendInfoType.value === 2) {
			const currentSelectKeys = treeRef.value.PitayaTreeRef.getCheckedKeys()
			const currentSelectNodes = treeRef.value.PitayaTreeRef.getCheckedNodes()
			relatedIdObj.relatedLineIds = currentSelectKeys
			tableDataSelf.value = currentSelectNodes
			tableColumnTypes.value = [
				{ label: "线路编码", prop: "code", width: 90 },
				{ label: "线路名称", prop: "name", needSlot: true },
				{ label: "线路里程(KM)", prop: "length", width: 130 },
				{ label: "车站数量", prop: "stationNumber", width: 80 },
				{
					label: "操作",
					prop: "operations",
					fixed: "right",
					width: 90,
					needSlot: true
				}
			]
			relatedObj.lines = cloneDeep(currentSelectNodes)
		}
	}
	showChooseDepDrawer.value = false
}
// 扩展信息类型切换
const changeExtendInfoType = (type: number) => {
	extendInfoType.value = type
	tableDataSelf.value = []
	if (type === 0) {
		getProfessionTableData()
	} else if (type === 2) {
		getJurisdictionalLineTableData()
	}
}
// 获取部门关联的专业
const tableDataLosding = ref(false)
const getProfessionByDepId = (id: any) => {
	tableDataLosding.value = true
	DepartmentApi.getProfessionForDepartment(id)
		.then((res: any) => {
			relatedIdObj.relatedProfessionIds.push(
				...getValueArrarByKey(res, "id", "children")
			)
			relatedObj.profession = res
			tableDataSelf.value = res
		})
		.finally(() => {
			tableDataLosding.value = false
		})
}
// 树选择
const onTreeChange = (data: any, checked: boolean) => {
	if (extendInfoType.value !== 1) return
	selectTreeNodeByNoRelevance(
		treeRef.value.PitayaTreeRef,
		depTreeData.value,
		data,
		checked
	)
}
// 递归函数提取所有id
const extractIds = (items:any) => {
    let ids:any = [];
    items.forEach((item:any)=> {
        if (item.children && item.children.length > 0) {
            // 如果有子项，递归处理子项
            ids = ids.concat(extractIds(item.children));
        } else {
            // 如果没有子项，添加当前项的id
            ids.push(item.id);
        }
    });
    return ids;
};
const isCompDisabled = ref(false)
watch(
	() => props.fomrData,
	(val: any) => {
		formModelData.companyId = val.companyId
		// 不是公司第一层级，不给部门进行赋值
		if(!val.company){
			if(!!val.orgCode){
				const parts = val.orgAllName.split('-');
				formModelData.orgAllName = parts.length > 1 ? parts.slice(0, -1).join('-') : val.orgAllName;
			}else{
				formModelData.orgAllName = !!val.orgCode?val.orgAllName:val.orgAllName
			}
			formModelData.orgId = val.orgId
		}
		if (val.id) {
			currentDetailEdit.value = val
			formModelData.orgCode = val.orgCode
			formModelData.orgName = val.orgName
			formModelData.sortNum = val.sortNum
			relatedIdObj.relatedLineIds = map(val.lines, (line) => line.id)
			relatedObj.lines = val.lines
			isCompDisabled.value = !!val.orgCode
			val.majorNum > 0 && getProfessionByDepId(val.id)
		}
	},
	{ immediate: true }
)

onMounted(() => {
	getProfessionTableData()
	getAllCompany()
})
defineOptions({
	name: "DepartmentDrawer"
})
// 所属公司，所属部门
const showDepartDrawer = ref<boolean>(false)
const companyList = ref<any[]>([])
const handleShowDepartment = () => {
	showDepartDrawer.value = true
}
function onCompanyIdChange(val: any) {
	formModelData.orgId  =  ''
}
// 计算默认选中节点
const selectedKeys = computed(() => {
  // 类型转换确保字符串类型
  const currentId = formModelData.orgId?.toString();
  
  return currentId ? 
    [currentId] : 
    ([]);
});
// 计算默认展开节点
const expandedKeys = computed(() => {
  // 优先使用筛选后的父节点，其次使用历史记录
  return props.fomrData.parentId ? 
    [props.fomrData.parentId] : 
    (formModelData.orgId ? [formModelData.orgId] : []);
});
// 获取全部公司列表
function getAllCompany() {
	baseUserApi
		.getAllCompany()
		.then((res: any) => {
			if (res && res.length > 0) {
				companyList.value = res.map((item: anyKey) => {
					return {
						label: item.companyName,
						value: item.id
					}
				})
				// queryArrList.value[0].children = companyList.value.filter((item=>item.value===props.currentNodeData.companyId))
			} else {
				companyList.value = []
			}
		})
		.catch((err) => {
			throw new Error("getAllCompany():::" + err)
		})
}
const onDepartBtnClick = (selected: any, value2: any) => {
	console.log('===selected',selected)
	if (selected) {
		//选择的是部门
		formModelData.orgId = selected.id
		formModelData.orgAllName = selected.allName
		showDepartDrawer.value = false
		ruleFormRef.value?.clearValidate("orgAllName")
	} else {
		//取消
		showDepartDrawer.value = false
	}
}
// 默认选中
const defaultCheckedKeys = computed(() => {
  if(relatedDrawerTitle.value === "选择专业"){
	return extractIds(relatedObj.profession)
  }else if(relatedDrawerTitle.value === "选择线路"){
	return extractIds(relatedObj.lines)
  }
});
</script>
<template>
	<div class="department-drawer-container" v-loading="drawerLoading">
		<div class="drawer-left">
			<Title :title="leftContainerTitle" />
			<div class="drawer-left-form">
				<el-form
					ref="ruleFormRef"
					:model="formModelData"
					:rules="rules"
					label-position="top"
				>
					<el-form-item label="所属公司" prop="companyId" class="select-item">
						<el-select
							filterable
							:disabled="isCompDisabled"
							style="width: 100%"
							v-model.trim="formModelData.companyId"
							placeholder="请选择所属公司"
							clearable
							@change="onCompanyIdChange"
						>
							<el-option
								v-for="item in companyList"
								:key="item.value"
								:label="item.label"
								:value="item.value"
							/>
						</el-select>
					</el-form-item>

					<el-form-item
						label="上级部门"
						prop="orgAllName"
						class="select-item"
						:disabled="isCompDisabled"
						clearable
					>
						<el-input
							@click="handleShowDepartment"
							v-model="formModelData.orgAllName"
							readonly
							placeholder="请输入归属部门"
							:disabled="isCompDisabled"
						>
							<template #append>
								<font-awesome-icon
									:icon="['fas', 'layer-group']"
									style="color: #ccc"
									@click="handleShowDepartment"
								/>
							</template>
						</el-input>
					</el-form-item>
					<el-form-item label="部门编码" prop="orgCode">
						<el-input
							v-model="formModelData.orgCode"
							placeholder="请输入部门编码"
							maxlength="50"
							:show-word-limit="true"
						/>
					</el-form-item>
					<el-form-item label="部门名称" prop="orgName">
						<el-input
							v-model="formModelData.orgName"
							placeholder="请输入部门名称"
							maxlength="50"
							:show-word-limit="true"
						/>
					</el-form-item>
					<el-form-item label="排序" prop="sortNum">
						<el-input-number
							style="width: 100%"
							controls-position="right"
							v-model="formModelData.sortNum"
							:min="1"
							:max="10000"
						/>
					</el-form-item>
				</el-form>
			</div>
			<div class="btn-groups">
				<ButtonList :button="btnList" @onBtnClick="onFormBtnClick" />
			</div>
		</div>
		<div class="drawer-right">
			<Title :title="rightContainerTitle">
				<div class="type-choose">
					<div
						class="drawer-right-subtitle"
						:class="{ 'type-is-active': extendInfoType === 0 }"
						@click="changeExtendInfoType(0)"
					>
						关联专业
					</div>
					<!-- <div
						class="drawer-right-subtitle"
						:class="{ 'type-is-active': extendInfoType === 1 }"
						@click="changeExtendInfoType(1)"
					>
						关联位置
					</div>-->
					<div
						class="drawer-right-subtitle"
						:class="{ 'type-is-active': extendInfoType === 2 }"
						@click="changeExtendInfoType(2)"
					>
						管辖线路
					</div>
				</div>
			</Title>
			<PitayaTable
				:table-data="tableDataSelf"
				:columns="tableColumnTypes"
				:table-loading="tableDataLosding"
				:need-index="true"
			>
				<template #name="{ rowData }">
					<div>
						<el-tag
							class="line-name-container"
							:style="{
								backgroundColor: rowData.colour,
								color: '#FFF',
								borderColor: rowData.colour
							}"
							>{{ rowData.name }}</el-tag
						>
					</div>
				</template>
				<template #operations="{ rowData }">
					<el-button v-btn link @click="onRowDelete(rowData)">
						<font-awesome-icon
							:icon="['fas', 'trash-can']"
							style="color: var(--pitaya-btn-background)"
						/>
						<span class="table-inner-btn">移除</span>
					</el-button>
				</template>
				<template #footerOperateLeft>
					<ButtonList
						:button="chooseDepBtnList"
						:isNotRadius="true"
						@onBtnClick="chooseDepBtnClick"
					/>
				</template>
			</PitayaTable>
		</div>
		<!-- 关联专业 位置 线路 公用一个树结构 -->
		<Drawer
			class="drawer-hidden-box"
			:size="depTreeDrawerSize"
			v-model:drawer="showChooseDepDrawer"
		>
			<div class="drawer-box" v-if="showChooseDepDrawer">
				<Title
					:title="{
						name: [relatedDrawerTitle],
						icon: ['fas', 'square-share-nodes']
					}"
				>
					<template #title>
						<div v-if="relatedDrawerTitle == '选择专业'">
							<el-tooltip
								class="box-item"
								effect="dark"
								content="仅显示上级部门关联的专业"
								placement="bottom"
							>
								<font-awesome-icon
									:icon="['far', 'fa-question-circle']"
									style="color: #666; margin: 0 10px"
								/>
							</el-tooltip>
						</div>
						<div v-if="relatedDrawerTitle == '选择线路'">
							<el-tooltip
								class="box-item"
								effect="dark"
								content="仅显示上级部门关联的线路"
								placement="bottom"
							>
								<font-awesome-icon
									:icon="['far', 'fa-question-circle']"
									style="color: #666; margin: 0 10px"
								/>
							</el-tooltip>
						</div>
					</template>
				</Title>
				<PitayaTree
					class="dep-tree"
					ref="treeRef"
					:need-check-box="true"
					:tree-data="depTreeData"
					:tree-props="depTreeProp"
					:check-strictly="extendInfoType === 1"
					@on-tree-change="onTreeChange"
					:defaultCheckedKeys="defaultCheckedKeys"
					:tree-loading="treeLoading"
				/>
				<div class="btn-groups">
					<ButtonList :button="treeBtnList" @onBtnClick="onTreeBtnClick" />
				</div>
			</div>
		</Drawer>
		<!-- 选择部门 -->
		<Drawer
			:size="depTreeDrawerSize"
			v-model:drawer="showDepartDrawer"
			class="drawer-hidden-box"
		>
			<div class="drawer-box">
				<Title
					:title="{
						name: ['选择部门'],
						icon: ['fas', 'square-share-nodes']
					}"
				/>
				<DepartmentTree
					:multiSelect="false"
					:drawerState="showDepartDrawer"
					:companyId="formModelData.companyId"
					:default-expanded-keys="expandedKeys"
					:defaultSelectedKeys="selectedKeys"
					@onBtnClick="onDepartBtnClick"
				/>
			</div>
		</Drawer>
	</div>
</template>
<style lang="scss" scoped>
.department-drawer-container {
	display: flex;
	align-items: center;
	position: relative;
	height: 100%;
	.drawer-left {
		position: relative;
		padding-right: 10px;
		box-sizing: border-box;
		width: 300px;
		height: 100%;
		.drawer-left-form {
			padding: 0 10px;
			margin-top: 10px;
		}
		.btn-groups {
			display: flex;
			justify-content: flex-end;
			position: absolute;
			bottom: 0;
			padding: 10px 10px 0 0;
			left: 0;
			right: 10px;
			width: auto;
			border-top: 1px solid #ccc;
		}
	}
	.drawer-left::after {
		content: "";
		position: absolute;
		right: 0;
		top: -15px;
		width: 1px;
		height: calc(100% + 25px);
		background: #ccc;
	}
	.drawer-right {
		position: relative;
		padding-left: 10px;
		box-sizing: border-box;
		width: 0;
		flex: 1;
		height: 100%;
		.type-choose {
			display: flex;
			position: absolute;
			left: 130px;
			right: 0;
			.drawer-right-subtitle {
				margin-right: 40px;

				color: #666666;
				cursor: pointer;
			}
			.drawer-right-subtitle.type-is-active {
				color: var(--pitaya-btn-background);
			}
			.drawer-right-subtitle.type-is-active::after {
				content: "";
				position: absolute;
				left: 0;
				bottom: -10px;
				width: 100%;
				height: 2px;
				background-color: var(--pitaya-btn-background);
			}
		}
		.line-name-container {
			margin: 0 auto;
			height: 22px;
			padding: 0 10px;
			border-radius: 3px;
			font-size: var(--pitaya-fs-12);
			color: #fff;
		}
	}
}
.drawer-box {
	height: 100%;
	display: flex;
	flex-direction: column;
	.dep-tree {
		padding: 0 10px;
		height: calc(100% - 75px);
		display: flex;
		flex-direction: column;
	}

	.btn-groups {
		width: 290px;
		margin-top: 10px;
		position: fixed;
		bottom: 10px;
		right: 10px;
		display: flex;
		justify-content: flex-end;
		padding: 10px 10px 0 0;
		box-sizing: border-box;
		border-top: 1px solid #ccc;
	}
}
</style>
@/app/platform/api/system/department@/app/platform/api/system/profession@/app/platform/api/location/index
