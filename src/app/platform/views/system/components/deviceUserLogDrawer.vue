<script setup lang="ts">
import { getAuthUserLog } from "@/app/platform/api/system/auth"
import { usePagination } from "@/app/platform/hooks/usePagination"
const usePaginationStore = usePagination({})
const paginationData = ref<PaginationData>(usePaginationStore.paginationData)
const dataTotal = ref(0)
const tableData = ref<any[]>([])
const columns = ref<TableColumnType[]>([
	{
		label: "用户名",
		prop: "userName_view",
		width: 250
	},
	{
		label: "使用时长",
		prop: "durationTime",
		width: 350
	},
	{
		label: "登录时间",
		prop: "loginTime",
	}
])
const authDeviceId = ref<any>()
const queryTableData = () => {
	getAuthUserLog({
		sord: "desc",
		sidx: "loginTime",
		deviceId: authDeviceId.value,
		...paginationData.value
	}).then((res: any) => {
		console.log(res)
		if (res.rows && res.rows.length > 0) {
			dataTotal.value = res.records
			tableData.value = res.rows
		} else {
			dataTotal.value = 0
			tableData.value = []
		}
	})
}
const onCurrentPageChange = (pd: PaginationData) => {
	paginationData.value = pd
	queryTableData()
}

const init = (id: any) => {
	authDeviceId.value = id
	paginationData.value.currentPage=1
	console.log("user log init! ->",	paginationData.value
, authDeviceId.value)
	queryTableData()
}
defineExpose({
	init
})
</script>
<template>
	<div>
		<Title
			:title="{ name: ['设备使用日志'], icon: ['fas', 'square-share-nodes'] }"
		/>
		<PitayaTable
			:total="dataTotal"
			:table-data="tableData"
			:columns="columns"
			:max-height="672"
			@onCurrentPageChange="onCurrentPageChange"
		/>
	</div>
</template>
@/app/platform/hooks/usePagination @/app/platform/api/system/auth
