<script lang="ts" setup>
import type { FormInstance, FormRules } from "element-plus"
import { LineApi } from "@/app/platform/api/system/line"

const emit = defineEmits(["onSubmit"])

/**
 * 表单相关参数
 */
const ruleFormRef = ref<FormInstance>()


const formRules = reactive<FormRules<typeof formData>>({
	code: [
	{ required: true, message: "线路编码不能为空", trigger: "blur" },
		{ validator: validateCodeName, required: true, trigger: "blur" }
	],
	name: [
	{ required: true, message: "线路名称不能为空", trigger: "blur" }
	],
	colour: [	{ required: true, message: "线路颜色不能为空", trigger: "blur" }],
	length: [	{ required: true, message: "线路里程不能为空", trigger: "blur" },],
	stationNumber: [
	{ required: true, message: "车站数量不能为空", trigger: "blur" },
	],
	firstStation: [	{ required: true, message: "起始站不能为空", trigger: "blur" },],
	lastStation: [	{ required: true, message: "终点站不能为空", trigger: "blur" },]
})
const formLabelProp: anyKey[] = [
	{ label: "线路编码", prop: "code", type: "input", required: true },
	{ label: "线路名称", prop: "name", type: "input", required: true },
	{ label: "线路颜色", prop: "colour", type: "color", required: true },
	{
		label: "线路里程(KM)",
		prop: "length",
		type: "input",
		onlyNumber: true,
		max: 10000,
		required: true
	},
	{
		label: "车站数量",
		prop: "stationNumber",
		type: "input",
		onlyNumber: true,
		max: 100,
		required: true
	},
	{ label: "起始站", prop: "firstStation", type: "input", required: true },
	{ label: "终点站", prop: "lastStation", type: "input", required: true },
	{
		label: "初次运营日期",
		prop: "firstEnableTime",
		type: "date",
		required: false
	},
	{
		label: "排序值",
		prop: "sortedBy",
		type: "input",
		onlyNumber: true,
		max: 10000
	}
]
const formData: anyKey = reactive({
	id: null,
	code: null,
	name: null,
	colour: "000000",
	length: null,
	stationNumber: null,
	firstStation: null,
	lastStation: null,
	sortedBy: "1000",
	firstEnableTime: null
})

const drawerLoading = ref<boolean>(false)
const drawerState = ref<boolean>(false)
const btnLoading = ref<boolean>(false)
const drawerTitle = {
	name: ["线路信息"],
	icon: ["fas", "square-share-nodes"]
}
const btnList = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "保存",
		icon: ["fas", "circle-check"]
	}
]

// 回调：底部按钮操作
const onBtnClick = (btnName: string | undefined) => {
	if (btnName === "保存") {
		ruleFormRef.value?.validate((valid) => {
			if (valid) {
				formData.firstEnableTime = formData.firstEnableTime
					? formData.firstEnableTime
					: ""
				const params = {
					...formData,
					colour: "#" + formData.colour
				}
				drawerLoading.value = true
				btnLoading.value = true
				LineApi.saveAndUpdateLine(objectToFormData(params))
					.then(() => {
						ElMessage.success(`${formData.id ? "修改成功" : "新增成功"}`)
						emit("onSubmit")
						drawerState.value = false
					})
					.finally(() => {
						drawerLoading.value = false
						btnLoading.value = false
					})
			}
		})
	} else {
		drawerState.value = false
	}
}

// 表单初始化
const formDataInit = (rowData?: any) => {
	open()
	if (rowData) {
		for (const attr in formData) {
			if (attr === "colour") {
				formData[attr] = rowData[attr].replace("#", "")
			} else {
				formData[attr] = rowData[attr]
			}
		}
	} else {
		for (const attr in formData) {
			formData[attr] = null
		}
		// 设备默认值
		formData.colour = "000000"
		formData.sortedBy = "1000"
	}
}

// 打开
const open = () => {
	drawerState.value = true
}

/**
 * 钩子函数
 */

defineExpose({
	formDataInit
})

defineOptions({
	name: "LineForm"
})
</script>
<template>
	<NewDrawer
		v-model:drawer="drawerState"
		:destroyOnClose="true"
		size="310"
		:loading="drawerLoading"
		:title="drawerTitle"
	>
		<template #content>
			<el-form
				class="form-container"
				ref="ruleFormRef"
				label-position="top"
				label-width="100px"
				:model="formData"
				:rules="formRules"
			>
				<el-form-item
					v-for="(item, index) in formLabelProp"
					:key="index"
					:label="item.label"
					:prop="item.prop"
				>
					<el-input
						v-if="item.type === 'input' && !item.onlyNumber"
						v-model.trim="formData[item.prop]"
						:formatter="(value: string) => value.trim()"
						maxlength="50"
						:show-word-limit="true"
						placeholder="请输入"
					/>
					<el-input-number
						style="width: 100%; text-align: left"
						controls-position="right"
						v-if="item.type === 'input' && item.onlyNumber "
						v-model="formData[item.prop]"
						:min="1"
						:max="item.max"
						placeholder="请输入"
					/>
					<el-input-number
						style="width: 100%; text-align: left"
						controls-position="right"
						v-if="item.type === 'length'"
						v-model="formData[item.prop]"
						:min="1"
						:max="item.max"
						placeholder="请输入"
						:precision="3"
					/>
					<div class="color-input-form" v-if="item.type === 'color'">
						<el-input
							v-model.trim="formData[item.prop]"
							:formatter="(value: string) => `# ${value.trim()}`"
							maxlength="8"
							:parser="(value: string) => value.replace(/\#\s?|(,*)/g, '')"
							class="color-input"
						/>
						<div
							class="color-show"
							:style="{ backgroundColor: `#${formData[item.prop]}` }"
						/>
					</div>
					<div v-if="item.type === 'date'" style="width: 100%">
						<el-date-picker
							class="date-picker-wrapper"
							v-model.trim="formData[item.prop]"
							format="YYYY-MM-DD "
							value-format="YYYY-MM-DD "
							type="date"
							placeholder="请选择"
						/>
						<el-input v-model.trim="formData[item.prop]" readonly>
							<template #append>
								<el-button v-btn link>
									<font-awesome-icon
										:icon="['fas', 'calendar-check']"
										style="color: var(--pitaya-place-font-color)"
									/>
								</el-button>
							</template>
						</el-input>
					</div>
				</el-form-item>
			</el-form>
		</template>
		<template #footer>
			<ButtonList
				:loading="btnLoading"
				:button="btnList"
				@onBtnClick="onBtnClick"
			/>
		</template>
	</NewDrawer>
</template>
<style lang="scss" scoped>
:deep(.date-picker-wrapper) {
	position: absolute;
	left: 0;
	top: 0;
	right: 0;
	bottom: 0;
	z-index: 1;
	opacity: 1;
	cursor: pointer;
	width: 88%;
}
:deep(.el-input__prefix){
	display: none;
}
.color-input-form {
	width: 100%;
	position: relative;
	.color-input {
		position: relative;
	}
	.color-show {
		position: absolute;
		right: 5px;
		top: 3.5px;
		width: 25px;
		height: 25px;
		border-radius: 2px;
	}
}
</style>
