<template>
	<div class="app-el-scrollbar-wrapper">
		<el-scrollbar>
			<PitayaTable
				ref="tableRef"
				@onCurrentPageChange="getTableList"
				:table-data="baseTableData"
				:columns="baseColumns"
				:total="dataTotal"
				:table-loading="tableLoading"
			>
				<template #operations="{ rowData }">
					<el-button
						v-btn
						link
						@click="onEdit(rowData)"
						:disabled="checkPermission('system:dictionary:btn:edit')"
						v-if="isCheckPermission('system:dictionary:btn:edit')"
					>
						<font-awesome-icon
							:icon="['fas', 'pen-to-square']"
							style="color: var(--pitaya-btn-background)"
							:class="
								checkPermission('system:dictionary:btn:edit') ? 'disabled' : ''
							"
						/>
						<span
							class="table-inner-btn"
							:class="
								checkPermission('system:dictionary:btn:edit') ? 'disabled' : ''
							"
							>编辑</span
						>
					</el-button>
					<div v-else>---</div>
					<!-- <el-button
						v-btn
						color="var(--pitaya-btn-background)"
						link
						@click="onDelete(rowData)"
						:disabled="checkPermission('system:dictionary:btn:delete')"
						v-if="isCheckPermission('system:dictionary:btn:delete')"
					>
						<font-awesome-icon
							:icon="['fas', 'trash-can']"
							style="color: var(--pitaya-btn-background)"
						/>
						<span class="table-inner-btn">移除</span>
					</el-button> -->
				</template>
			</PitayaTable>
		</el-scrollbar>
	</div>
	<Drawer :size="drawerSize1" v-model:drawer="drawerState1" @close="onClose1"  :destroyOnClose="true">
		<div class="drawer-lr-layout-wrapper" v-loading="drawerLoading">
			<div class="common-from-wrapper common-from-left">
				<Title :title="drawer1Title" />
				<div class="common-from-group">
					<el-scrollbar>
						<el-form
							class="el-form-wrapper"
							ref="baseFormRef"
							:model="baseFormData"
							:rules="formRules"
							label-width="auto"
							label-position="top"
						>
							<el-form-item label="字典类型">
								<el-input maxlength="50" value="列表字典" disabled />
							</el-form-item>
							<el-form-item label="字典编码" prop="dataDictionaryCode">
								<el-input
									maxlength="100"
									:show-word-limit="true"
									v-model.trim="baseFormData.dataDictionaryCode"
									:disabled="!!baseFormData.id"
								/>
							</el-form-item>
							<el-form-item label="字典名称" prop="dataDictionaryName">
								<el-input
									maxlength="100"
									:show-word-limit="true"
									v-model.trim="baseFormData.dataDictionaryName"
								/>
							</el-form-item>
							<el-form-item label="备注说明">
								<el-input
									maxlength="200"
									:show-word-limit="true"
									v-model="baseFormData.notes"
									type="textarea"
									:autosize="{ minRows: 8 }"
									resize="none"
								/>
							</el-form-item>
						</el-form>
					</el-scrollbar>
				</div>
				<div class="btn-groups">
					<ButtonList
						:button="btnList"
						:loading="drawerLoading"
						@onBtnClick="onFormBtnClick"
					/>
				</div>
			</div>
			<div class="common-from-wrapper">
				<Title :title="drawer2Title">
					<div class="drawer-content-wrapper">字典项</div>
				</Title>
				<div class="common-from-group">
					<el-scrollbar>
						<PitayaTable
							:tableData="termTableData"
							:columns="termColumns"
							:need-index="true"
							:tableLoading="termTableDataLoading"
						>
							<template #disable="{ rowData }">
								<el-switch
									size="small"
									v-model="rowData.disable"
									:active-value="false"
									:inactive-value="true"
								/>
							</template>
							<template #operations="{ rowData }">
								<el-button v-btn link @click="onTremEdit(rowData)">
									<font-awesome-icon
										:icon="['fas', 'pen-to-square']"
										style="color: var(--pitaya-btn-background)"
									/>
									<span class="table-inner-btn">编辑</span>
								</el-button>
								<!-- <el-button v-btn color="var(--pitaya-btn-background)" link>
									<font-awesome-icon
										:icon="['fas', 'trash-can']"
										style="color: var(--pitaya-btn-background)"
									/>
									<span class="table-inner-btn" @click="onTremDelete(rowData)"
										>移除</span
									>
								</el-button> -->
							</template>
							<template #footerOperateLeft>
								<ButtonList
									:button="addBtn"
									:isNotRadius="true"
									@onBtnClick="onAddTerm"
								/>
							</template>
						</PitayaTable>
						<!-- <div class="btn-groups-box">
							<ButtonList
								:button="addBtn"
								:isNotRadius="true"
								@onBtnClick="onAddTerm"
							/>
						</div>-->
					</el-scrollbar>
				</div>
				<Drawer
					:size="drawerSize2"
					v-model:drawer="drawerState2"
					@close="onClose2"
					:destroyOnClose="true"
				>
					<div class="common-from-wrapper common-from-only">
						<Title :title="drawerTremTitle" />
						<div class="common-from-group">
							<el-scrollbar>
								<el-form
									class="el-form-wrapper"
									ref="tremFormRef"
									:model="tremFormData"
									:rules="tremFormRules"
									label-width="auto"
									label-position="top"
								>
									<el-form-item label="字典项名称" prop="subitemName">
										<el-input v-model.trim="tremFormData.subitemName" />
									</el-form-item>
									<el-form-item label="字典项数据值" prop="subitemValue">
										<el-input v-model.trim="tremFormData.subitemValue" />
									</el-form-item>
									<el-form-item label="排序" prop="sortedBy">
										<el-input-number
											style="width: 100%"
											controls-position="right"
											v-model="tremFormData.sortedBy"
											:min="1"
											:max="10000"
										/>
									</el-form-item>
								</el-form>
							</el-scrollbar>
						</div>
						<div class="btn-groups">
							<ButtonList
								:button="btnListSubmit"
								@onBtnClick="onTremBtnClick"
							/>
						</div>
					</div>
				</Drawer>
			</div>
		</div>
	</Drawer>
</template>
<script setup lang="ts">
import { usePagination } from "@/app/platform/hooks/usePagination"
import {
	checkPermission,
	isCheckPermission
} from "@/app/platform/utils/permission"
import {
	changeDictionary,
	deleteDictionary,
	getDictionaryList
} from "@/app/platform/api/system/dictionary"
import { ElMessage, ElMessageBox, FormRules } from "element-plus"
import type { FormInstance } from "element-plus"
import { CustomMessageBox } from "@/app/platform/utils/message"

const props = defineProps({
	paramObj: {
		type: Object,
		default: () => ({
			dataDictionaryCode: "",
			dataDictionaryName: ""
		})
	}
})

const tableLoading = ref(false)
const drawerLoading = ref(false)

const baseFormData = reactive<anyKey>({
	dataDictionaryCode: "",
	dataDictionaryName: "",
	notes: "",
	dataDictionarySubitems: []
})

const formRules = reactive<FormRules<typeof baseFormData>>({
	dataDictionaryCode: [
		{ required: true, message: "请输入字典编码", trigger: "blur" },
		{ validator: validateCodeName, required: true, trigger: "blur" }
	],
	dataDictionaryName: [
		{ required: true, message: "请输入字典名称", trigger: "blur" }
	]
})
const usePaginationStore = usePagination()
const paginationData = ref<PaginationData>(usePaginationStore.paginationData)

const pageObj = ref<any>({
	currentPage: paginationData.value.currentPage,
	pageSize: paginationData.value.pageSize
})
const getTableList = (pd: PaginationData) => {
	pageObj.value.currentPage = pd.currentPage
	pageObj.value.pageSize = pd.pageSize
	dictionaryListInit()
}

const drawer1Title = {
	name: ["字典信息"],
	icon: ["fas", "square-share-nodes"]
}

const drawer2Title = {
	name: ["扩展信息"],
	icon: ["fas", "square-share-nodes"]
}

const drawerTremTitle = {
	name: ["字典信息"],
	icon: ["fas", "square-share-nodes"]
}

const drawerState1 = ref(false)
const drawerSize1 = 930
const baseFormRef = ref<FormInstance>()
const tremFormRef = ref<FormInstance>()
const drawerState2 = ref(false)
const drawerSize2 = 310
const dataTotal = ref(0)
const baseTableData = ref<any[]>([])
const baseColumns = ref<TableColumnType[]>([
	{ prop: "dataDictionaryName", label: "字典名称", width:250, align:'left'  },
	{ prop: "dataDictionaryCode", label: "字典编码", width:250, align:'left' },
	{ prop: "subitemNum", label: "字典项数量", width: 100 },
	{ prop: "notes", label: "备注说明", minWidth: 200, align:'left'},
	{ prop: "lastModifiedDate", label: "更新时间", width: 170 },
	{
		prop: "operations",
		fixed: "right",
		label: "操作",
		needSlot: true,
		width: 180
	}
])
const btnList = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "保存",
		icon: ["fas", "floppy-disk"]
	}
]
const btnListSubmit = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "确定",
		icon: ["fas", "floppy-disk"]
	}
]
const dictionaryListInit = () => {
	tableLoading.value = true
	getDictionaryList({ ...props.paramObj, ...pageObj.value })
		.then((res: any) => {
			if (res.rows && res.rows.length) {
				baseTableData.value = res.rows
				dataTotal.value = res.records
			} else {
				baseTableData.value = []
				dataTotal.value = 0
			}
		})
		.finally(() => {
			tableLoading.value = false
		})
}

const addBtn = [
	{
		name: "新增",
		icon: ["fas", "circle-plus"]
	}
]
const tremFormData = reactive<anyKey>({
	subitemName: "",
	subitemValue: "",
	sortedBy: 1000
})
const termTableData = ref<any[]>([])
const termColumns = ref<TableColumnType[]>([
	{ prop: "subitemName", label: "字典项名称" },
	{ prop: "subitemValue", label: "数据值", width: 100 },
	{ prop: "sortedBy", label: "排序", width: 100 },
	{ prop: "disable", label: "状态", width: 100, needSlot: true },
	{
		prop: "operations",
		fixed: "right",
		label: "操作",
		needSlot: true,
		width: 100
	}
])
const onAddTerm = () => {
	Object.keys(tremFormData).map((key) => (tremFormData[key] = ""))
	tremFormData.sortedBy = 1000
	tremFormData.disable = false
	tremFormRef.value?.clearValidate()
	drawerState2.value = true
}

const tremFormRules = reactive<FormRules<typeof baseFormData>>({
	subitemName: [
		{ required: true, message: "请输入字典项名称", trigger: "blur" }
	],
	subitemValue: [
		{ required: true, message: "请输入字典项数据值", trigger: "blur" }
	]
})
const termTableDataLoading = ref(false)
const onEdit = (rowData: anyKey) => {
	drawerState1.value = true
	termTableDataLoading.value = true
	Object.keys(baseFormData).forEach((key) => {
		baseFormData[key] = rowData[key] ? rowData[key] : ""
	})
	baseFormData.id = rowData.id
	termTableData.value = rowData.dataDictionarySubitems || []
	termTableDataLoading.value = false
}

const onTremEdit = (rowData: anyKey) => {
	Object.keys(tremFormData).forEach((key) => {
		if (key === "disable") {
			tremFormData[key] = rowData[key] ? rowData[key] : false
		} else if (key === "sortedBy") {
			tremFormData[key] = rowData[key]
		} else {
			tremFormData[key] = rowData[key] ? rowData[key] : ""
		}
	})
	tremFormData.id = rowData.id
	drawerState2.value = true
}

const onDelete = (rowData: anyKey) => {
	CustomMessageBox({ message: "确定要移除该条字典信息吗?" }, (res: boolean) => {
		if (res) {
			const formdata = objectToFormData({
				id: rowData.id
			})
			deleteDictionary(formdata).then(() => {
				dictionaryListInit()
				ElMessage.success("操作成功")
			})
		}
	})
}

const onTremDelete = (rowData: anyKey) => {
	CustomMessageBox(
		{ message: "确定要移除该条字典项信息吗?" },
		(res: boolean) => {
			if (res) {
				termTableData.value = termTableData.value.filter((item) => {
					return item.id !== rowData.id
				})
			}
		}
	)
}

const onFormBtnClick = (btnName: string | undefined) => {
	console.log("btnName", btnName)
	if (!baseFormRef.value) return
	if (btnName === "保存") {
		baseFormRef.value.validate((valid) => {
			console.log("valid", valid)
			if (valid) {
				baseFormData.dataDictionarySubitems = termTableData.value || []
				if (!baseFormData.id) {
					delete baseFormData.id
				}
				if (
					baseFormData.dataDictionarySubitems &&
					baseFormData.dataDictionarySubitems.length
				) {
					baseFormData.dataDictionarySubitems =
						baseFormData.dataDictionarySubitems.map((item: any) => {
							const idStr = item.id + ""
							if (idStr && idStr.indexOf("new") === 0) {
								delete item.id
							}
							return item
						})
				}
				drawerLoading.value = true
				changeDictionary(baseFormData)
					.then(() => {
						dictionaryListInit()
						ElMessage.success("操作成功")
					})
					.finally(() => {
						drawerLoading.value = false
						drawerState1.value = false
					})
			} else {
				return false
			}
		})
		return
	}

	if (btnName === "取消") {
		drawerState1.value = false
		return
	}
}

const onClose1 = () => {
	Object.keys(baseFormData).map((key) => (baseFormData[key] = ""))
	baseFormRef.value?.clearValidate()
}

const onTremBtnClick = (btnName: string | undefined) => {
	if (!tremFormRef.value) return
	if (btnName === "确定") {
		tremFormRef.value.validate((valid) => {
			if (valid) {
				if (tremFormData.id) {
					termTableData.value = termTableData.value.map((item: any) => {
						if (tremFormData.id === item.id) {
							item = { ...tremFormData }
						}
						return item
					})
					termTableData.value.sort((a: any, b: any) => {
						return a.sortedBy - b.sortedBy
					})
				} else {
					tremFormData.id =
						"new" + (Math.random() * Math.pow(10, 16)).toString().split(".")[0]
					termTableData.value.push({ ...tremFormData })
					termTableData.value.sort((a: any, b: any) => {
						return a.sortedBy - b.sortedBy
					})
				}
				drawerState2.value = false
			} else {
				return false
			}
		})
		return
	}

	if (btnName === "取消") {
		drawerState2.value = false
		return
	}
}

const onClose2 = () => {
	Object.keys(tremFormData).map((key) => (tremFormData[key] = ""))
	tremFormRef.value?.clearValidate()
}

onMounted(() => {
	dictionaryListInit()
})

const handleAdd = () => {
	Object.keys(baseFormData).map((key) => (baseFormData[key] = ""))
	delete baseFormData.id
	baseFormRef.value?.clearValidate()
	termTableData.value = []
	drawerState1.value = true
}
const tableRef = ref()
const handleQuery = () => {
	pageObj.value.currentPage = 0
	tableRef.value.resetCurrentPage()
	dictionaryListInit()
}
defineExpose({
	handleAdd,
	handleQuery
})
</script>
<style lang="scss" scoped>
.app-el-scrollbar-wrapper {
	height: 0;
	flex: 1;
}

.drawer-lr-layout-wrapper {
	display: flex;
	position: absolute;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	overflow: hidden;

	&::after {
		content: "";
		width: 1px;
		height: 120%;
		position: absolute;
		left: 310px;
		top: -15px;
		background-color: var(--pitaya-border-color);
	}
}

.common-from-wrapper {
	height: 100%;
	width: 0;
	flex: 1;
	display: flex;
	flex-direction: column;
	padding: 10px;

	.drawer-content-wrapper {
		position: absolute;
		left: 130px;

		&::after {
			content: "";
			width: 100%;
			height: 2px;
			background-color: var(--pitaya-btn-background);
			position: absolute;
			bottom: -10px;
			left: 50%;
			transform: translateX(-50%);
		}
	}

	&.common-from-left {
		width: 310px;
		flex: 0 0 310px;
	}

	&.common-from-only {
		width: 100%;
		padding: 0;
	}

	.common-from-group {
		height: 0;
		flex: 1;

		.el-form-wrapper {
			padding-left: 10px;
			padding-right: 10px;
		}
	}

	.btn-groups {
		display: flex;
		justify-content: flex-end;
		padding: 10px 10px 0 0;
		border-top: 1px solid var(--pitaya-border-color);
	}
}
</style>
@/app/platform/hooks/usePagination @/app/platform/api/system/dictionary
