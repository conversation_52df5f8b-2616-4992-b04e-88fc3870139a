<script lang="ts" setup>
import { getBaseRoleList } from "@/app/platform/api/system/baseRole"
import { baseUserApi } from "@/app/platform/api/system/baseUser"
import { TeamGroupApi } from "@/app/platform/api/system/teamGroup"
import { usePagination } from "@/app/platform/hooks/usePagination"
import { useUserStore } from "@/app/platform/store/modules/user"
import { ElMessage, type FormInstance, type FormRules, ElMessageBox } from "element-plus"
import { pullAt } from "lodash-es"
import { CustomMessageBox } from "@/app/platform/utils/message"

interface Props {
	rowData: anyKey
	currentNodeData: anyKey
}
const tableLoading = ref<boolean>(false)

const props = defineProps<Props>()
const orgId = props.rowData.orgId

const emit = defineEmits(["onClose"])
const usePaginationStore = usePagination({ currentPage: 1, pageSize: 20 })
const paginationData = ref<PaginationData>(usePaginationStore.paginationData)
const extendInfoType = ref<number>(0)

const isEdit = computed(() => !!props.rowData.id)


const leftContainerTitle = {
	name: ["账号信息"],
	icon: ["fas", "square-share-nodes"]
}
const rightContainerTitle = {
	name: ["扩展信息"],
	icon: ["fas", "square-share-nodes"]
}

const extendInfoTitle = computed(() => ({
	name: [extendInfoType.value === 0 ? "分配角色" : "关联班组"],
	icon: ["fas", "square-share-nodes"]
}))

const btnList = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "保存",
		icon: ["fas", "floppy-disk"]
	}
]
const treeBtnList = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "确定",
		icon: ["fas", "floppy-disk"]
	}
]

const formModelData = ref<anyKey>({})

const rules: FormRules = {
	username: [
		{ required: true, message: "请输入用户账号", trigger: "blur" },
		{ validator: validateCodeName, required: true, trigger: "blur" }
	],
	realname: [{ required: true, message: "请输入用户名称", trigger: "blur" }],
	employeeNumber: [
		{
			required: true,
			message: "请输入员工工号",
			trigger: "blur"
		}
	],
	sex: [{ required: true, message: "请选择性别", trigger: "blur" }],
	phone: [
		{
			pattern: /^1[3456789]\d{9}$/,
			message: "手机号码格式不正确",
			trigger: ["blur", "change"]
		}
	],
	companyId: [{ required: true, message: "请选择所属公司", trigger: "blur" }],
	orgAllName: [{ required: true, message: "请选择所属部门", trigger: "blur" }]
}

const tableColumnTypes = ref<TableColumnType[]>([
	{ label: "角色编码", prop: "roleCode" },
	{ label: "角色名称", prop: "roleName" },
	{ label: "操作", prop: "operations", width: 90, needSlot: true }
])

const chooseDepBtnList = ref<any[]>([
	{
		name: "分配角色",
		icon: ["fas", "circle-plus"]
	}
])

const ruleFormRef = ref<FormInstance>()
const showChooseDepDrawer = ref<boolean>(false)

const tableDataSelf = computed(() =>
	extendInfoType.value === 0 ? roleTDateSelf.value : groupTDateSelf.value
)

/**
 *
 */
const queryArrList = ref<any[]>([
	{
		name: "所属公司",
		key: "companyId",
		placeholder: "请选择",
		enableFuzzy: false,
		type: "select",
		children: [],
		paramsData: props.currentNodeData.companyId,
	},
	{
		name: "角色名称",
		key: "roleName",
		placeholder: "请输入查询关键字",
		enableFuzzy: false,
		type: "input"
	},
	{
		name: "角色编码",
		key: "roleCode",
		placeholder: "请输入查询关键字",
		enableFuzzy: false,
		type: "input"
	}
])
const paramObj = ref({
	roleName: "",
	companyId: props.currentNodeData.companyId,
	roleCode: ''
})
const getQueryData = (queryData: any) => {
	paramObj.value = Object.assign(paramObj.value, queryData)
	paramObj.value.companyId = props.currentNodeData.companyId
	paginationData.value.currentPage = 1
	getRolesTreeData()
}

/**
 * 新增/编辑账号
 */

const teamedIds = ref([])
function saveOrUpdateBaseUser() {
	// return console.log('isOldTeamByUser.value',isOldTeamByUser.value)
	if (buttonLoading.value) return
	const roleIds = roleTDateSelf.value.map((item) => item.id)
	const data = {
		...formModelData.value,
		oldOrgId: orgId ? orgId : "",
		id: isEdit.value ? props.rowData.id : null,
		roleIds,
		teamedIds: teamedIds.value,
		isOldTeamByUser: isOldTeamByUser.value
	}
	buttonLoading.value = true
	baseUserApi
		.saveOrUpdateBaseUserApi(data)
		.then(() => {
			ElMessage.success(isEdit.value ? "修改成功" : "新增成功")
			emit("onClose")
		})
		.catch((err) => {
			throw new Error("saveOrUpdateBaseUserApi():::" + err)
		})
		.finally(() => {
			buttonLoading.value = false
		})
}

// #region 关联班组相关
const groupTDateSelf = ref<any[]>([])
const depTreeData = ref<any[]>([])
const depTreeTotal = ref(0)
const treeTableRef = ref()
const depGroupColumnTypes = [
	{ label: "班组编码", prop: "orgCode" },
	{ label: "班组名称", prop: "orgAllName" }
]
/**
 * 获取已关联班组id
 */
function setAffiliatedGroupTableData() {
	if (isEdit.value) {
		baseUserApi
			.getTeamByUserIdApi({ baseUserId: props.rowData.id })
			.then((res: any) => {
				if (res && res.length > 0) {
					teamedIds.value = res.map((item) => item.id)
				}
			})
			.catch((err) => {
				throw new Error("getTeamByUserIdApi():::" + err)
			})
	}
}
setAffiliatedGroupTableData()

/**
 * 获取已关联班组列表
 */
const groupColumn = ref({
	tableColumnTypes: [
		{ label: "班组编码", prop: "orgCode" },
		{ label: "班组名称", prop: "orgAllName" }
	]
})
function getAffiliatedGroupTableData() {
	if (isEdit.value) {
		tableDataLoading.value = true
		baseUserApi
			.getTeamByUserIdApi({ baseUserId: props.rowData.id })
			.then((res: any) => {
				if (res && res.length > 0) {
					groupTDateSelf.value = res
				} else {
					groupTDateSelf.value = []
				}
			})
			.catch((err) => {
				throw new Error("getTeamByUserIdApi():::" + err)
			})
			.finally(() => {
				tableDataLoading.value = false
			})
	}
}

/**
 * 回显班组列表
 */
function handlerSelectedDepTree() {
	const resArr = treeTableRef.value.getSelectedTable()
	groupTDateSelf.value = JSON.parse(JSON.stringify(resArr))
}
const sexType = [
	{ label: "男", value: 1 },
	{ label: "女", value: 0 }
]
/**
 * 获取班组列表
 */
function getGroupData(id: string | number) {
	const params = objectToFormData({
		...paginationData.value,
		companyId: id,
		noShowAllTeamed: false
	})
	tableLoading.value = true
	TeamGroupApi.getTeamGroupTable(params)
		.then((res: any) => {
			if (res) {
				if (res.rows && res.rows.length > 0) {
					const { rows, records } = res
					depTreeData.value = rows
					depTreeTotal.value = records
					// 更新选中状态
					refreshGroupSelectedItem()
				} else {
					depTreeData.value = []
				}
			} else {
				depTreeData.value = []
			}
		})
		.catch((err) => {
			throw new Error("getTeamGroupTable():::" + err)
		})
		.finally(() => {
			tableLoading.value = false
		})
}

/**
 * 更新班组列表选中状态
 */
function refreshGroupSelectedItem() {
	const curKeys = groupTDateSelf.value.map((item) => item.orgCode)
	if (curKeys && curKeys.length > 0) {
		depTreeData.value.forEach((row) => {
			curKeys.forEach((key) => {
				if (row.orgCode === key) {
					setTimeout(() => {
						treeTableRef.value.pitayaTableRef!.toggleRowSelection(row, true)
					}, 100)
				}
			})
		})
	} else {
		setTimeout(() => {
			treeTableRef.value.pitayaTableRef!.clearSelection()
		}, 100)
	}
}
// #endregion

// #region 分配角色相关
const roleTDateSelf = ref<any[]>([])
const tableDataLoading = ref(false)
const depTableData = ref<anyKey[]>([])
const pitayaTableRef = ref()
const depTableTotal = ref(0)
const depTableColumnTypes = [
	{ label: "角色编码", prop: "roleCode" },
	{ label: "角色名称", prop: "roleName" },
	{ label: "所属公司", prop: "companyName" },
]
/**
 * 获取已分配角色列表
 */
const roleColumn = ref({
	tableColumnTypes: [
		{ label: "角色编码", prop: "roleCode" },
		{ label: "角色名称", prop: "roleName" },
		{ label: "所属公司", prop: "companyName" },
		{ label: "操作", prop: "operations", needSlot: true }
	],
	chooseDepBtnList: [
		{
			name: "分配角色",
			icon: ["fas", "circle-plus"]
		}
	]
})
const getRoleInfoTableData = () => {
	if (isEdit.value) {
		tableDataLoading.value = true
		baseUserApi
			.getRoleByUserIdApi({ baseUserId: props.rowData.id })
			.then((res: any) => {
				if (res && res.length > 0) {
					roleTDateSelf.value = res
				} else {
					roleTDateSelf.value = []
				}
			})
			.catch((err) => {
				throw new Error("getRoleByUserIdApi():::" + err)
			})
			.finally(() => {
				tableDataLoading.value = false
			})
	}
}

/**
 * 获取全部角色列表
 */
const getRolesTreeData = () => {
	const data = {
		...paginationData.value,
		...paramObj.value,
	}
	tableLoading.value = true
	getBaseRoleList(data)
		.then((res: any) => {
			if (res.rows && res.rows.length > 0) {
				const { rows, records } = res
				depTableData.value = rows
				depTableTotal.value = records
				// 更新选中状态
				// refreshRoleSelectedItem()
			} else {
				depTableData.value = []
			}
		})
		.catch((err) => {
			throw new Error("getBaseRoleList():::" + err)
		})
		.finally(() => {
			tableLoading.value = false
		})
}

/**
 * 回显角色列表
 */
function handlerSelectedRoleTable() {
	const resArr = pitayaTableRef.value.getSelectedTable()
	roleTDateSelf.value = JSON.parse(JSON.stringify(resArr))
}

/**
 * 更新角色列表选中状态
 */
function refreshRoleSelectedItem() {
	const curKeys = roleTDateSelf.value.map((item) => item.roleCode)
	if (curKeys && curKeys.length > 0) {
		depTableData.value.forEach((row) => {
			curKeys.forEach((key) => {
				if (row.roleCode === key) {
					setTimeout(() => {
						pitayaTableRef.value.pitayaTableRef!.toggleRowSelection(row, true)
					}, 100)
				}
			})
		})
	} else {
		setTimeout(() => {
			pitayaTableRef.value.pitayaTableRef!.clearSelection()
		}, 100)
	}
}
// #endregion

/**
 * 分配按钮事件
 */
const chooseDepBtnClick = (btnName: string | undefined) => {
	if (btnName === "分配角色") {
		paginationData.value.currentPage = 1
		getRolesTreeData()
	} else if (btnName === "关联班组") {
		getGroupData(formModelData.value.companyId)
	}
	setTimeout(() => {
		showChooseDepDrawer.value = true
	}, 200)
}

/*
 * 一级弹窗按钮事件
 */
const buttonLoading = ref(false)
const onFormBtnClick = (btnName: string | undefined) => {
	if (btnName === "保存") {
		ruleFormRef.value?.validate((valid) => {
			if (valid) {
				saveOrUpdateBaseUser()
			} else {
				return false
			}
		})
		return
	}
	if (btnName === "取消") {
		emit("onClose")
		return
	}
}

// 二级弹窗按钮操作
const onTreeBtnClick = (btnName: string | undefined) => {
	if (btnName === "确定") {
		if (extendInfoType.value === 0) {
			// 角色列表操作
			handlerSelectedRoleTable()
		} else if (extendInfoType.value === 1) {
			// 班组树操作
			handlerSelectedDepTree()
		}
	}
	showChooseDepDrawer.value = false
}

// 移除角色或班组
function onRowDelete(row: anyKey) {
	CustomMessageBox(
		{
			message: `确定要移除当前${extendInfoType.value === 0 ? "角色" : "班组"
				}吗？`
		},
		(res: boolean) => {
			if (res) {
				// 移除角色
				if (extendInfoType.value === 0) {
					const idx = roleTDateSelf.value.findIndex(
						(item) => item.roleCode === row.roleCode
					)
					pullAt(roleTDateSelf.value, idx)
				} else {
					// 移除班组
					const idx = groupTDateSelf.value.findIndex(
						(item) => item.orgCode === row.orgCode
					)
					pullAt(groupTDateSelf.value, idx)
				}
			} else {
				return false
			}
		}
	)
}

// 标签切换事件
const changeExtendInfoType = (type: number) => {
	extendInfoType.value = type
	// if (type === 0) {
	// 	getRoleInfoTableData()
	// } else if (type === 1) {
	// 	getAffiliatedGroupTableData()
	// }
}

//委外人员
const outsourcedPersonnelList = ref([
	{ value: 0, label: "否" },
	{ value: 1, label: "是" }
])
// #region 获取公司列表相关
const companyList = ref<any[]>([])

// 获取全部公司列表
function getAllCompany() {
	baseUserApi
		.getAllCompany()
		.then((res: any) => {
			if (res && res.length > 0) {
				companyList.value = res.map((item: anyKey) => {
					return {
						label: item.companyName,
						value: item.id
					}
				})
				queryArrList.value[0].children = companyList.value.filter((item => item.value === props.currentNodeData.companyId))
			} else {
				companyList.value = []
			}
		})
		.catch((err) => {
			throw new Error("getAllCompany():::" + err)
		})
}
// #endregion

// #region 获取部门选项相关
const orgList = ref<anyKey[]>([])

/**
 * 获取部门列表
 */
function getOrgTree(id: string | number) {
	baseUserApi
		.getAllOrgList({ id })
		.then((res: any) => {
			if (res && res.length > 0) {
				orgList.value = res.map((item: anyKey) => {
					return {
						label: item.orgName,
						value: item.id
					}
				})
			} else {
				formModelData.value.orgId = ""
				orgList.value = []
			}
		})
		.catch((err) => {
			throw new Error("getAllOrgList():::" + err)
		})
}
// #endregion

// #region 根据账号权限初始化状态
// 是否可选公司
const isCompDisabled = ref(false)
function initBaseData() {
	// 获取当前账号权限
	const { isSuperAdmin } = storeToRefs(useUserStore())

	// 请求公司列表
	getAllCompany()
	if (!isSuperAdmin.value) {
		isCompDisabled.value = true
	} else {
		//不允许改公司
		isCompDisabled.value = true
	}

	if (isEdit.value) {
		formModelData.value = props.rowData
		formModelData.value.outsourcedPersonnel = props.rowData.outsourcedPersonnel
			? 1
			: 0
		getRoleInfoTableData()
		getOrgTree(props.rowData.companyId)
	} else {
		formModelData.value.companyId = props.currentNodeData.companyId
		if (!props.currentNodeData.company) {
			formModelData.value.orgId = props.currentNodeData.id
			formModelData.value.orgAllName = props.currentNodeData.name
		}
	}
}

// 监听公司id变化，请求部门树
watch(
	() => formModelData.value.companyId,
	(newCompId) => {
		getOrgTree(newCompId)
	}
)

function onCompanyIdChange(val: any) {
	formModelData.value.orgId = ""
}
// #endregion

const depTreeDrawerSize = ref<number>(310)
const showDepartDrawer = ref<boolean>(false)
const isOldTeamByUser = ref<boolean>(false)  // 是否从班组中移除该账号给后端标识
const handleShowDepartment = () => {
	showDepartDrawer.value = true
}
const onDepartBtnClick = async (selected: any, value2: any) => {
	const handleDepartmentSelection = () => {
	  if (!selected) {
		// 取消选择
		showDepartDrawer.value = false;
		return;
	  }
	  // 更新表单数据
	  formModelData.value.orgId = selected.id
	  formModelData.value.orgAllName = selected.allName
	  showDepartDrawer.value = false
	  ruleFormRef.value?.clearValidate("orgAllName")
	};
	// 非编辑模式直接处理
	if (!isEdit.value) {
	  handleDepartmentSelection();
	  return;
	}
	// 两次选择的一样的部门 || 编辑但是之前不存在班组的时候可以直接保存
	if (props.currentNodeData.id === selected?.id || (isEdit.value && !groupTDateSelf.value.length)) {
	  handleDepartmentSelection();
	  isOldTeamByUser.value = false
	  return;
	}
	// 当前存在班组,提示修改操作的风险
	if(groupTDateSelf.value&&groupTDateSelf.value.length){
		const orgNames = (groupTDateSelf.value||[]).filter(n=> n.teamSharedType !== 'outMember').map(item => item.orgAllName).join('，');
		try {
		await ElMessageBox.confirm(
			`该账户已在 [${orgNames}] 班组中。如果移动到其他部门，账户将脱离该班组。确定要继续吗？`,
			{
				confirmButtonText: '确认',
				cancelButtonText: '取消',
				type: 'warning',
			}
		);
		handleDepartmentSelection();
		isOldTeamByUser.value = true
		} catch (error) {
			showDepartDrawer.value = false;
			isOldTeamByUser.value = false
		}
	}
}
// 计算默认展开节点
const expandedKeys = computed(() => {
	// 优先使用筛选后的父节点，其次使用历史记录
	return props.currentNodeData.parentId ?
		[props.currentNodeData.parentId] :
		(formModelData.value.orgId ? [formModelData.value.orgId] : []);
});

// 计算默认选中节点
const selectedKeys = computed(() => {
	// 类型转换确保字符串类型
	const currentId = props.currentNodeData.id?.toString();

	return currentId ?
		[currentId] :
		([]);
});

onMounted(() => {
	initBaseData()
	getRoleInfoTableData()
	getAffiliatedGroupTableData()
})

/**
 * 分页
 */
function handlerCurPageChanged(pd: PaginationData) {
	paginationData.value = pd
	getRolesTreeData()
}

defineOptions({
	name: "AccountDrawer"
})
</script>
<template>
	<div class="drawer-lr-layout-wrapper" v-loading="buttonLoading">
		<div class="common-from-wrapper common-from-left">
			<Title :title="leftContainerTitle" />
			<div class="common-from-group">
				<el-scrollbar>
					<el-form class="el-form-wrapper" ref="ruleFormRef" :model="formModelData" :rules="rules"
						label-position="top">
						<el-form-item label="用户姓名" prop="realname" required>
							<el-input maxlength="50" :show-word-limit="true" v-model.trim="formModelData.realname"
								placeholder="请输入用户姓名" />
						</el-form-item>
						<el-form-item label="用户账号" prop="username" required>
							<el-input :disabled="isEdit" maxlength="50" :show-word-limit="true"
								v-model.trim="formModelData.username" placeholder="请输入用户账号" />
						</el-form-item>
						<el-form-item label="员工工号" prop="employeeNumber" required>
							<el-input maxlength="100" :show-word-limit="true"
								v-model.trim="formModelData.employeeNumber" placeholder="请输入员工工号" />
						</el-form-item>
						<el-form-item label="性别" prop="sex" required>
							<el-select v-model="formModelData.sex" style="width: 100%" clearable>
								<el-option v-for="(item, index) in sexType" :key="index" :label="item.label"
									:value="item.value" />
							</el-select>
						</el-form-item>
						<el-form-item label="手机号码" prop="phone">
							<el-input maxlength="11" :show-word-limit="true" v-model.trim.number="formModelData.phone"
								placeholder="请输入手机号码" />
						</el-form-item>
						<el-form-item label="企业微信" prop="enterpriseWeChat">
							<el-input maxlength="50" :show-word-limit="true"
								v-model.trim="formModelData.enterpriseWeChat" placeholder="请输入企业微信号" />
						</el-form-item>
						<el-form-item label="所属公司" prop="companyId" class="select-item">
							<el-select filterable :disabled="isCompDisabled" style="width: 100%"
								v-model.trim="formModelData.companyId" placeholder="请选择所属公司" clearable
								@change="onCompanyIdChange">
								<el-option v-for="item in companyList" :key="item.value" :label="item.label"
									:value="item.value" />
							</el-select>
						</el-form-item>

						<el-form-item label="所属部门" prop="orgAllName" class="select-item" clearable>
							<el-input @click="handleShowDepartment" v-model="formModelData.orgAllName" readonly
								placeholder="请输入归属部门">
								<template #append>
									<font-awesome-icon :icon="['fas', 'layer-group']" style="color: #ccc"
										@click="handleShowDepartment" />
								</template>
							</el-input>
						</el-form-item>
						<el-form-item label="岗位" prop="station" class="select-item" clearable>
							<el-input maxlength="50" :show-word-limit="true" v-model.trim="formModelData.station"
								placeholder="请输入岗位" />
						</el-form-item>
						<el-form-item label="别名" prop="nickName" class="select-item" clearable>
							<el-input maxlength="50" :show-word-limit="true" v-model.trim="formModelData.nickName"
								placeholder="请输入别名" />
						</el-form-item>
						<el-form-item label="委外人员" prop="outsourcedPersonnel" class="select-item" clearable>
							<el-select filterable style="width: 100%" v-model.trim="formModelData.outsourcedPersonnel"
								placeholder="请选择" clearable>
								<el-option v-for="item in outsourcedPersonnelList" :key="item.value" :label="item.label"
									:value="item.value" />
							</el-select>
						</el-form-item>
					</el-form>
				</el-scrollbar>
			</div>
			<div class="btn-groups">
				<ButtonList :button="btnList" :loading="buttonLoading" @onBtnClick="onFormBtnClick" />
			</div>
		</div>
		<div class="common-from-wrapper">
			<Title :title="rightContainerTitle">
				<div class="app-tabs-wrapper">
					<div class="drawer-right-subtitle" :class="{ 'type-is-active': extendInfoType === 0 }"
						@click="changeExtendInfoType(0)">
						角色信息
					</div>
					<div class="drawer-right-subtitle" :class="{ 'type-is-active': extendInfoType === 1 }"
						@click="changeExtendInfoType(1)">
						所属班组
					</div>
				</div>
			</Title>
			<div class="common-from-group">
				<el-scrollbar>
					<!-- 角色 -->
					<PitayaTable v-if="extendInfoType == 0" :table-data="roleTDateSelf"
						:table-loading="tableDataLoading" :columns="roleColumn.tableColumnTypes" :need-index="true">
						<template #operations="{ rowData }">
							<el-button v-btn link @click="onRowDelete(rowData)">
								<font-awesome-icon :icon="['fas', 'trash-can']"
									style="color: var(--pitaya-btn-background)" />
								<span class="table-inner-btn">移除</span>
							</el-button>
						</template>
						<template #footerOperateLeft>
							<ButtonList :button="roleColumn.chooseDepBtnList" :isNotRadius="true"
								@onBtnClick="chooseDepBtnClick" />
						</template>
					</PitayaTable>
					<!-- 所属班组 -->
					<PitayaTable v-if="extendInfoType == 1" :table-data="groupTDateSelf"
						:table-loading="tableDataLoading" :columns="groupColumn.tableColumnTypes" :need-index="true">
						<template #operations="{ rowData }">
							<el-button v-btn link @click="onRowDelete(rowData)">
								<font-awesome-icon :icon="['fas', 'trash-can']"
									style="color: var(--pitaya-btn-background)" />
								<span class="table-inner-btn">移除</span>
							</el-button>
						</template>
					</PitayaTable>
				</el-scrollbar>
			</div>
		</div>
		<Drawer :destroyOnClose="true" :size="1120" v-model:drawer="showChooseDepDrawer">
			<div class="common-from-wrapper common-from-only">
				<Title :title="extendInfoTitle" />
				<div class="common-from-group">
					<el-scrollbar v-if="extendInfoType == 0">
						<Query class="ml10 mt10" :queryArrList="queryArrList" @getQueryData="getQueryData" :numInRow="3"
							:queryBtnColSpan="6" />
						<PitayaTable style="margin-top: 2px" ref="pitayaTableRef" :selectedTableData="roleTDateSelf"
							:table-data="depTableData" :columns="depTableColumnTypes" :need-index="true"
							:need-selection="true" :pagerCount="5" :pageSize="20" :total="depTableTotal"
							@onCurrentPageChange="handlerCurPageChanged" :table-loading="tableLoading" />
					</el-scrollbar>
					<el-scrollbar v-else>
						<PitayaTable ref="treeTableRef" :selectedTableData="groupTDateSelf" :table-data="depTreeData"
							:columns="depGroupColumnTypes" :need-index="true" :need-selection="true" :pagerCount="5"
							:total="depTreeTotal" @onCurrentPageChange="handlerCurPageChanged"
							:table-loading="tableLoading" />
					</el-scrollbar>
				</div>
				<div class="btn-groups">
					<ButtonList :button="treeBtnList" @onBtnClick="onTreeBtnClick" />
				</div>
			</div>
		</Drawer>
		<Drawer :size="depTreeDrawerSize" v-model:drawer="showDepartDrawer" class="drawer-hidden-box">
			<div class="drawer-box">
				<Title :title="{
					name: ['选择部门'],
					icon: ['fas', 'square-share-nodes']
				}" />
				<DepartmentTree :multiSelect="false" :drawerState="showDepartDrawer"
					:companyId="formModelData.companyId" :default-expanded-keys="expandedKeys"
					:defaultSelectedKeys="selectedKeys" @onBtnClick="onDepartBtnClick" />
			</div>
		</Drawer>
	</div>
</template>

<style lang="scss" scoped>
.app-container {
	:deep(.model-frame-wrapper) {
		padding-bottom: 0px;
	}
}

.app-content-wrapper {
	height: 0;
	flex: 1;
	margin-top: 10px;

	.app-content-group {
		height: 100%;

		:deep(.model-frame-wrapper) {
			height: 100%;
			padding-bottom: 10px;
			overflow: hidden;
			display: flex;
			flex-direction: column;
		}

		.app-el-scrollbar-wrapper {
			height: 0;
			flex: 1;

			.table-inner-btn {
				margin-left: 5px;
				color: #204a9c;
			}
		}
	}
}

.app-tabs-wrapper {
	position: absolute;
	display: flex;
	left: 130px;
	bottom: 9px;

	.drawer-right-subtitle {
		margin-right: 40px;

		color: #666666;
		cursor: pointer;
	}

	.drawer-right-subtitle.type-is-active {
		color: var(--pitaya-btn-background);
	}

	.drawer-right-subtitle.type-is-active::after {
		content: "";
		position: absolute;
		left: 0;
		bottom: -10px;
		width: 100%;
		height: 2px;
		background-color: var(--pitaya-btn-background);
	}
}

.drawer-lr-layout-wrapper {
	display: flex;
	position: absolute;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	overflow: hidden;

	&::after {
		content: "";
		width: 1px;
		height: 120%;
		position: absolute;
		left: 310px;
		top: -15px;
		background-color: var(--pitaya-border-color);
	}

	:deep(.el-input-group__append) {
		padding: 0 9px !important;
	}
}

.common-from-wrapper {
	height: 100%;
	width: 0;
	flex: 1;
	display: flex;
	flex-direction: column;
	padding: 10px;

	.drawer-content-wrapper {
		position: absolute;
		left: 130px;

		&::after {
			content: "";
			width: 100%;
			height: 2px;
			background-color: var(--pitaya-btn-background);
			position: absolute;
			bottom: -10px;
			left: 50%;
			transform: translateX(-50%);
		}
	}

	&.common-from-left {
		width: 310px;
		flex: 0 0 310px;
	}

	&.common-from-only {
		width: 100%;
		padding: 0;
	}

	.common-from-group {
		height: 0;
		flex: 1;

		.el-form-wrapper {
			padding-left: 10px;
			padding-right: 10px;
		}
	}

	.btn-groups {
		display: flex;
		justify-content: flex-end;
		padding: 10px 10px 0 0;
		border-top: 1px solid var(--pitaya-border-color);
	}
}

.can-click-btn {
	color: var(--pitaya-sidebar-menu-active-text-color);
	cursor: pointer;
	border-bottom: 1px solid var(--pitaya-sidebar-menu-active-text-color);

	&::after {
		content: "";
		opacity: 0;
		position: absolute;
		left: -20px;
		right: -20px;
		display: block;
		height: 100%;
		top: 0;
	}
}

.drawer-box {
	height: 100%;
	display: flex;
	flex-direction: column;

	.pitaya-table {
		margin-top: 0;
	}

	.dep-tree {
		flex: 1;
	}

	.btn-groups {
		position: absolute;
		bottom: 0;
		right: 0;
		display: flex;
		justify-content: flex-end;
		padding-top: 10px;
		padding-right: 10px;
		box-sizing: border-box;
		width: 100%;
		border-top: 1px solid #ccc;
	}
}
</style>
@/app/platform/store/modules/user @/app/platform/hooks/usePagination
