<script setup lang="ts">
import { CompanyApi } from "@/app/platform/api/system/notice"
import { useUserStore } from "@/app/platform/store/modules/user"
import { storeToRefs } from "pinia"
import { reactive, ref } from "vue"
import { FormInstance, FormRules } from "element-plus"

enum SpecialNoticeStatus {
	"草稿",
	"已发布",
	"已下线"
}
interface SpecialNoticeInfo {
	"id"?: number, //主键
	"notifyCode"?: string, //通知编码
	"noticeTitle": string, //通知名称
	"noticeContent": string, //通知内容
	"endTime": string,	//下线时间
	"sts"?: SpecialNoticeStatus,	//状态 0:草稿, 1:已发布, 2:已下线
	"sts_view"?: string,	//状态名称
	"companyId"?: number,	//公司主键
	"organizationCode"?: string,	//部门编码
	"remark"?: string, //备注
	"createdBy"?: string, //创建人
	"createdDate"?: string, //创建时间 YYYY-MM-DD HH:ii:ss
	"lastModifiedBy"?: string, //更新人
	"lastModifiedDate"?: string, //更新时间 YYYY-MM-DD HH:ii:ss
	// "planUseCount"		: null,
	// "files"						: null,
}
const detailTitle = ref<{ name?: string[]; icon?: string[] }>({
	name: ["通知公告"],
	icon: ["fas", "square-share-nodes"]
})
// 表单定义
const formData = reactive<SpecialNoticeInfo>({
	id: -1,
	noticeTitle: "",
	noticeContent: "",
	endTime: "",
})
// 表单验证规则
const formRules = reactive<FormRules<SpecialNoticeInfo>>({
	noticeTitle: [
		{ required: true, message: "通知标题不能为空", trigger: "change" }
	],
	noticeContent: [
		{ required: true, message: "通知内容不能为空", trigger: "change" },
		{
			validator: (rule, value, cb) => {
				return value.trim().length > 0
			},
			message: "通知内容不能为空",
			trigger: "change"
		}
	],
	endTime: [{ required: true, message: "下线时间不能为空", trigger: "change" }]
})
interface Props {
	dataId: string
	model: string
}
const emit = defineEmits(["onSuccess", "onClosed"])
const props = withDefaults(defineProps<Props>(), {
	dataId: "",
	model: "edit" //edit|preview
})
const { dataId, model } = toRefs(props)
const detailBtns = reactive([])

const formref = ref<FormInstance>()
const uploadFile = ref()
const isLoading = ref(true)
const isProcessing = ref(false)
const companyList = ref([])
const userStore = useUserStore()
const { userInfo } = storeToRefs(userStore)

function dateChecker(time: Date) {
	let _curDate = new Date(
		`${new Date().getFullYear()}-${new Date().getMonth() + 1
		}-${new Date().getDate()}`
	)
	return time.getTime() < _curDate.getTime() - 1
}

function onBtnClick(btnName: string) {
	if (btnName == "取消") {
		emit("onClosed")
	} else {
		formref.value.validate((valid) => {
			if (valid) {
				isProcessing.value = true
				let _reqParam = {
					noticeTitle: (typeof formData.noticeTitle === 'string' && formData.noticeTitle.trim()) || "",
					noticeContent: (typeof formData.noticeContent === 'string' && formData.noticeContent.trim()) || "",
					endTime: formData.endTime,
					id:formData.id,
					noticeStatus: btnName == "发布"?1:formData.noticeStatus||0,
				}
				CompanyApi.getNoticeSave(_reqParam)
				.then((_r) => {
					resetForm()
					emit("onSuccess", _r)
				})
				.finally(() => {
					isProcessing.value = false
				})
			}
		})
	}
}

function resetForm() {
	detailBtns.length = 0
	formData.id = -1
	formData.noticeTitle = ""
	formData.noticeContent = ""
	formData.endTime = ""
}

onMounted(() => {
	resetForm()
	isLoading.value = true
	if (dataId.value) {
		CompanyApi.getNoticeGet({
			id: dataId.value
		}).then((_r) => {
			if(_r){
				formData.id = _r.id
				formData.noticeTitle = _r.noticeTitle
				formData.noticeContent = _r.noticeContent 
				formData.endTime = _r.endTime
				formData.noticeStatus = _r.noticeStatus
				isLoading.value = false
			}
		})
	} else {
		isLoading.value = false
	}
	detailBtns.push({
		name: "取消",
		icon: ["fas", "circle-minus"]
	},{
		name: "保存",
		icon: ["fas", "floppy-disk"]
	},{
		name: "发布",
		icon: ["fas", "check"]
	})
})
</script>

<template>
	<div class="form-body">
		<div>
			<Title :title="detailTitle" />
			<el-scrollbar v-if="!isLoading">
				<el-form :model="formData" :rules="formRules" label-position="top" label-width="420" class="form"
					ref="formref">
					<el-form-item label="标题" prop="noticeTitle">
						<el-input v-model="formData.noticeTitle"
							:formatter="(value: string) => value.trim()" :parser="(value: string) => value.trim()"
							maxlength="50" show-word-limit placeholder="请输入标题" />
					</el-form-item>
					<el-form-item label="内容" prop="noticeContent">
						<el-input v-model="formData.noticeContent" type="textarea" rows="10"
							placeholder="请输入内容" maxlength="200" show-word-limit />
					</el-form-item>
					<el-form-item label="下线时间" prop="endTime">
						<el-date-picker :disabled-date="dateChecker" v-model="formData.endTime" 
							value-format="YYYY-MM-DD HH:mm:ss"
							type="datetime"
							:default-time="new Date()"
							placeholder="请选择下线时间" />
					</el-form-item>
				</el-form>
			</el-scrollbar>
		</div>
		<div class="btn-groups">
			<ButtonList :button="detailBtns" :loading="isProcessing" @on-btn-click="onBtnClick" />
		</div>
	</div>
</template>

<style lang="scss" scoped>
.form-body {
	height: 100%;
	display: flex;
	flex-direction: column;
	justify-content: space-between;

	>div:first-child {
		.el-scrollbar {
			display: flex;
			flex-direction: column;
			height: calc(100vh - 110px);
		}
	}
}

.form {
	padding: 10px;
}

.btn-groups {
	border-top: 1px solid #ccc;
	padding-top: 10px;
	padding-right: 10px;
	display: flex;
	justify-content: flex-end;
}

.el-select {
	width: 100%;
}

::v-deep .el-date-editor {
	width: 100%;
}

.upd-filelist {
	font-size: 12px;
	color: var(--pitaya-header-bg-color);
	padding-left: 10px;
}

.file-item {
	cursor: pointer;
	user-select: none;
}
</style>
