<template>
	<div class="app-el-scrollbar-wrapper">
		<el-scrollbar>
			<PitayaTable
				ref="tableRef"
				@onCurrentPageChange="getNewPage"
				:table-data="baseTableData"
				:columns="baseColumns"
				:total="dataTotal"
				:table-loading="tableLoading"
			>
				<template #operations="{ rowData }">
					<el-button
						v-btn
						link
						@click="onEdit(rowData)"
						:disabled="checkPermission('system:dictionary:btn:edit')"
						v-if="isCheckPermission('system:dictionary:btn:edit')"
					>
						<font-awesome-icon
							:icon="['fas', 'pen-to-square']"
							style="color: var(--pitaya-btn-background)"
							:class="
								checkPermission('system:dictionary:btn:edit') ? 'disabled' : ''
							"
						/>
						<span
							class="table-inner-btn"
							:class="
								checkPermission('system:dictionary:btn:edit') ? 'disabled' : ''
							"
							>编辑</span
						>
					</el-button>
					<div v-else>---</div>
					<!-- <el-button
						v-btn
						color="var(--pitaya-btn-background)"
						link
						:disabled="checkPermission('system:dictionary:btn:delete')"
						v-if="isCheckPermission('system:dictionary:btn:delete')"
					>
						<font-awesome-icon
							:icon="['fas', 'trash-can']"
							style="color: var(--pitaya-btn-background)"
						/>
						<span class="table-inner-btn" @click="onDelete(rowData)">移除</span>
					</el-button> -->
				</template>
			</PitayaTable>
		</el-scrollbar>
	</div>
	<Drawer
		:size="drawerSize"
		v-model:drawer="drawerState"
		@close="onClose"
		:destroyOnClose="true"
	>
		<div class="drawer-lr-layout-wrapper" v-loading="drawerLoading">
			<div class="common-from-wrapper common-from-left">
				<Title :title="drawerTitle.main" />
				<div class="common-from-group">
					<el-scrollbar>
						<el-form
							ref="treeDictFormRef"
							:model="treeDictForm"
							:rules="treeDictFormRules"
							class="el-form-wrapper"
							label-width="auto"
							label-position="top"
						>
							<el-form-item label="字典类型">
								<el-input maxlength="50" value="树形字典" disabled />
							</el-form-item>
							<el-form-item label="字典编码" prop="treeDictionaryCode">
								<el-input
									maxlength="100"
									:show-word-limit="true"
									v-model.trim="treeDictForm.treeDictionaryCode"
									:disabled="!!treeDictForm.id"
								/>
							</el-form-item>
							<el-form-item label="字典名称" prop="treeDictionaryName">
								<el-input
									maxlength="100"
									:show-word-limit="true"
									v-model.trim="treeDictForm.treeDictionaryName"
								/>
							</el-form-item>
							<el-form-item label="备注说明">
								<el-input
									maxlength="200"
									:show-word-limit="true"
									v-model="treeDictForm.notes"
									type="textarea"
									:autosize="{ minRows: 8 }"
									resize="none"
								/>
							</el-form-item>
						</el-form>
					</el-scrollbar>
				</div>
				<div class="btn-groups">
					<ButtonList
						:button="btnList"
						:loading="drawerLoading"
						@onBtnClick="onFormBtnClick"
					/>
				</div>
			</div>
			<div class="common-from-wrapper">
				<Title :title="drawerTitle.ext" />
				<div class="common-from-group">
					<el-scrollbar>
						<PitayaTable
							:tableData="subDictTableData"
							:columns="subDictColumns"
							:need-index="true"
							:tableLoading="subDictTableDataLoading"
						>
							<template #disable="{ rowData }">
								<el-switch
									size="small"
									@change="(event:boolean)=>onSubitemDisable(rowData,event)"
									v-model="rowData.disable"
									:active-value="false"
									:inactive-value="true"
								/>
							</template>
							<template #operations="{ rowData }">
								<el-button
									v-btn
									link
									@click="onSubitemEdit(rowData)"
									:disabled="checkPermission('system:dictionary:btn:edit')"
									v-if="isCheckPermission('system:dictionary:btn:edit')"
								>
									<font-awesome-icon
										:icon="['fas', 'pen-to-square']"
										style="color: var(--pitaya-btn-background)"
									/>
									<span class="table-inner-btn">编辑</span>
								</el-button>
								<!-- <el-button
									v-btn
									color="var(--pitaya-btn-background)"
									link
									:disabled="checkPermission('system:dictionary:btn:delete')"
									v-if="isCheckPermission('system:dictionary:btn:delete')"
								>
									<font-awesome-icon
										:icon="['fas', 'trash-can']"
										style="color: var(--pitaya-btn-background)"
									/>
									<span
										class="table-inner-btn"
										@click="onSubitemDelete(rowData)"
										>移除</span
									>
								</el-button> -->
								<el-button v-btn color="var(--pitaya-btn-background)" link>
									<font-awesome-icon
										:icon="['fas', 'add']"
										style="color: var(--pitaya-btn-background)"
									/>
									<span
										class="table-inner-btn"
										@click="onSubitemNewChild(rowData)"
										>添加下级</span
									>
								</el-button>
							</template>
							<template #footerOperateLeft>
								<ButtonList
									:button="addSubDictBtn"
									:isNotRadius="true"
									@onBtnClick="onAddSubDict"
								/>
							</template>
						</PitayaTable>
						<!-- <div class="btn-groups-box">
							<ButtonList
								:button="addSubDictBtn"
								:isNotRadius="true"
								@onBtnClick="onAddSubDict"
							/>
						</div>-->
					</el-scrollbar>
				</div>
			</div>
		</div>
	</Drawer>
	<Drawer
		:destroyOnClose="true"
		:size="subDictDrawerSize"
		v-model:drawer="subDictDrawerState"
		@close="onSubDictDrawerClose"
	>
		<div class="common-from-wrapper common-from-only">
			<Title :title="subDictDrawerTitle" />
			<div class="common-from-group">
				<el-scrollbar>
					<el-form
						class="el-form-wrapper"
						label-width="auto"
						label-position="top"
						ref="subDictFormRef"
						:model="subDictForm"
						:rules="subDictFormRules"
					>
						<el-form-item label="父级字典项" prop="parentName">
							<el-input disabled :value="subDictForm.parentName || '---'" />
						</el-form-item>
						<el-form-item label="字典项名称" prop="subitemName">
							<el-input v-model.trim="subDictForm.subitemName" />
						</el-form-item>
						<el-form-item label="数据值" prop="subitemValue">
							<el-input v-model.trim="subDictForm.subitemValue" />
						</el-form-item>
						<el-form-item label="排序" prop="sortedBy">
							<el-input-number
								style="width: 100%"
								controls-position="right"
								v-model="subDictForm.sortedBy"
								:min="1"
								:max="10000"
							/>
						</el-form-item>
					</el-form>
				</el-scrollbar>
			</div>
			<div class="btn-groups">
				<ButtonList
					:button="btnListSubmit"
					@onBtnClick="onSubDictDrawerBtnClick"
				/>
			</div>
		</div>
	</Drawer>
</template>
<script setup lang="ts">
import {
	deleteTreeDictnary,
	getTreeDictnaryList,
	listTreeDictionaryByCode,
	saveOrUpdateTreeDictionary
} from "@/app/platform/api/system/dictionary"
import {
	checkPermission,
	isCheckPermission
} from "@/app/platform/utils/permission"
import { usePagination } from "@/app/platform/hooks/usePagination"
import { FormRules } from "element-plus"
import XEUtils from "xe-utils"
import { CustomMessageBox } from "@/app/platform/utils/message"

const props = defineProps({
	paramObj: {
		type: Object,
		default: () => ({
			dataDictionaryCode: "",
			dataDictionaryName: ""
		})
	}
})

const tableLoading = ref(false)
const drawerLoading = ref(false)

//表格配置
const usePaginationStore = usePagination()
const paginationData = ref<PaginationData>(usePaginationStore.paginationData)
const pageObj = ref<any>({
	currentPage: paginationData.value.currentPage,
	pageSize: paginationData.value.pageSize
})

const dataTotal = ref(0)
const baseTableData = ref<any[]>([])
const baseColumns = ref<TableColumnType[]>([
	{ prop: "treeDictionaryName", label: "字典名称", width:250, align:'left' },
	{ prop: "treeDictionaryCode", label: "字典编码", width:250, align:'left' },
	{ prop: "subitemNum", label: "字典项数量", width: 100 },
	{ prop: "notes", label: "备注说明", minWidth: 200, align:'left' },
	{ prop: "lastModifiedDate", label: "更新时间", width: 180 },
	{
		prop: "operations",
		fixed: "right",
		label: "操作",
		needSlot: true,
		width: 150
	}
])

const dictionaryListInit = () => {
	dataTotal.value = 1
	//组装查询条件
	const queryParam = {
		currentPage: pageObj.value.currentPage,
		pageSize: pageObj.value.pageSize,
		treeDictionaryCode: props.paramObj.dataDictionaryCode,
		treeDictionaryName: props.paramObj.dataDictionaryName
	}
	tableLoading.value = true
	getTreeDictnaryList(queryParam)
		.then((res: any) => {
			if (res.rows && res.rows.length > 0) {
				baseTableData.value = res.rows
				dataTotal.value = res.records
			} else {
				baseTableData.value = []
				dataTotal.value = 0
			}
		})
		.finally(() => {
			tableLoading.value = false
		})
}

const getNewPage = (pd: PaginationData) => {
	pageObj.value.currentPage = pd.currentPage
	pageObj.value.pageSize = pd.pageSize
	dictionaryListInit()
}
//操作设置
const subDictTableDataLoading = ref(false)
const onEdit = (rowData: any) => {
	drawerState.value = true
	subDictTableDataLoading.value = true
	Object.keys(treeDictForm).forEach((key) => {
		treeDictForm[key] = rowData[key] ? rowData[key] : ""
	})
	treeDictForm.id = rowData.id

	const data = {
		treeDictionaryCode: rowData.treeDictionaryCode,
		containDisable: true
	}
	listTreeDictionaryByCode(data)
		.then((res: any) => {
			if (res && res.length > 0) {
				subDictTableData.value = res
			} else {
				subDictTableData.value = []
			}
		})
		.finally(() => {
			subDictTableDataLoading.value = false
		})
}
const onDelete = (rowData: any) => {
	console.log(rowData)
	CustomMessageBox({ message: "确定要移除该条字典信息吗?" }, (res: boolean) => {
		if (res) {
			deleteTreeDictnary(rowData.id).then(() => {
				dictionaryListInit()
				ElMessage.success("操作成功")
			})
		}
	})
}
//弹出框设置
const drawerState = ref(false)
const drawerSize = 1120
const onClose = () => {}
const drawerTitle = {
	main: {
		name: ["字典信息"],
		icon: ["fas", "square-share-nodes"]
	},
	ext: {
		name: ["扩展信息"],
		icon: ["fas", "square-share-nodes"]
	}
}
//弹出框左侧按钮
const btnList = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "保存",
		icon: ["fas", "floppy-disk"]
	}
]
const btnListSubmit = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "确定",
		icon: ["fas", "floppy-disk"]
	}
]
const onFormBtnClick = (btnName: string | undefined) => {
	console.log(btnName)
	if (!treeDictFormRef.value) return
	if (btnName === "取消") {
		drawerState.value = false
	}
	if (btnName === "保存") {
		treeDictFormRef.value.validate((valid: any) => {
			if (valid) {
				treeDictForm.treeDictionarySubitems = subDictTableData.value || []
				drawerState.value = false
				//去除id
				if (!treeDictForm.id) {
					delete treeDictForm.id
				}
				if (subDictTableData.value.length > 0) {
					XEUtils.eachTree(subDictTableData.value, (item: any) => {
						//如果是首次创建的，树节点，由于id和parentId是随机生成的，需要移除
						if (!XEUtils.isNumber(item.id)) {
							delete item.id
						}
						if (!XEUtils.isNumber(item.parentId)) {
							delete item.parentId
						}
					})
				}
				drawerLoading.value = true
				saveOrUpdateTreeDictionary(treeDictForm)
					.then(() => {
						subDictTableData.value = []
						dictionaryListInit()
						ElMessage.success("操作成功")
						drawerState.value = false
						treeDictFormRef.value?.resetFields()
					})
					.finally(() => {
						drawerLoading.value = false
					})
			} else {
				console.log("验证失败", treeDictForm)
			}
		})
	}
}

//弹出数据字典表单
const treeDictFormRef = ref<any>()
const treeDictForm = reactive<anyKey>({
	treeDictionaryCode: "",
	treeDictionaryName: "",
	notes: "",
	treeDictionarySubitems: []
})
const treeDictFormRules = reactive<FormRules<typeof treeDictForm>>({
	treeDictionaryCode: [
		{ required: true, message: "请输入字典编码", trigger: "blur" },
		{ validator: validateCodeName, required: true, trigger: "blur" }
	],
	treeDictionaryName: [
		{ required: true, message: "请输入字典名称", trigger: "blur" }
	]
})
//数据字典子项表格数据
const subDictTableData = ref<any[]>([])
const subDictColumns = ref<TableColumnType[]>([
	{
		prop: "subitemName",
		label: "字典项名称",
		align: "left",
		class: "tree-cell-flex"
	},
	{ prop: "subitemValue", label: "数据值", width: 120 },
	{ prop: "sortedBy", label: "排序", width: 80 },
	{ prop: "disable", label: "状态", needSlot: true, width: 80 },
	{
		prop: "operations",
		fixed: "right",
		label: "操作",
		needSlot: true,
		showOverflowTooltip: false,
		width: 220
	}
])
const subDictFormRef = ref<any>()
const subDictForm = reactive<anyKey>({
	subitemName: "",
	subitemValue: "",
	sortedBy: 1000,
	disable: false
})
const subDictFormRules = reactive<FormRules<typeof subDictForm>>({
	subitemName: [
		{ required: true, message: "请输入字典项名称", trigger: "blur" }
	],
	subitemValue: [{ required: true, message: "请输入数据值", trigger: "blur" }]
})

const onSubitemEdit = (rowData: any) => {
	Object.keys(subDictForm).forEach((key) => {
		if (key === "disable") {
			subDictForm[key] = rowData[key] ? rowData[key] : false
		} else {
			subDictForm[key] = rowData[key] ? rowData[key] : ""
		}
	})
	subDictForm.parentId = rowData.parentId
	const parentNode = XEUtils.findTree(
		subDictTableData.value,
		(item: any) => item.id === rowData.parentId
	)
	if (parentNode && parentNode.item) {
		subDictForm.parentName = parentNode.item.subitemName
	}
	subDictForm.id = rowData.id
	subDictDrawerState.value = true
}
const onSubitemDelete = (rowData: any) => {
	console.log(rowData)
	CustomMessageBox(
		{ message: "确定要移除该条字典项信息吗?" },
		(res: boolean) => {
			if (res) {
				if (rowData.children && rowData.children.length > 0) {
					ElMessage.error("该字典项下存在子项")
					return
				}
				if (rowData.parentId) {
					//从父节点的children里移除
					const parentNode = getTreeNode(rowData.parentId)
					if (parentNode) {
						parentNode.children = parentNode.children.filter(
							(item: any) => item.id !== rowData.id
						)
					}
				} else {
					//直接移除
					subDictTableData.value = subDictTableData.value.filter(
						(item: any) => item.id !== rowData.id
					)
				}
			}
		}
	)
}
const onSubitemNewChild = (rowData: any) => {
	console.log("添加下级", rowData)
	subDictForm.parentId = rowData.id
	subDictForm.parentName = rowData.subitemName
	subDictForm.sortedBy = 1000
	subDictForm.disable = false
	subDictForm.id = ""
	subDictForm.subitemName = ""
	subDictForm.subitemValue = ""
	subDictDrawerState.value = true
}

const onSubitemDisable = (rowData: any, disable: boolean) => {
	console.log(rowData)
	// 关父节点时，子集同时关闭
	if (disable && rowData.children.length > 0) {
		XEUtils.eachTree(rowData.children, (item: any) => {
			item.disable = true
		})
	}
	// 开子节点时，父节点同时开启
	if (!disable) {
		// 获取当前节点的父节点列表
		const itemPath = XEUtils.findTree(
			subDictTableData.value,
			(item: any) => item.id === rowData.id
		)
		console.log("itemPath", itemPath)
		//   遍历父节点列表，并将子节点的状态置为false
		if (itemPath.nodes.length > 0) {
			itemPath.nodes.forEach((item: any) => {
				item.disable = false
			})
		}
	}
}

const addSubDictBtn = [
	{
		name: "新增",
		icon: ["fas", "circle-plus"]
	}
]
const onAddSubDict = () => {
	console.log("onAddSubDict")
	subDictForm.parentId = ""
	subDictForm.parentName = ""
	subDictForm.subitemName = ""
	subDictForm.subitemValue = ""
	subDictForm.id = ""
	subDictForm.sortedBy = 1000
	subDictForm.disable = false
	subDictDrawerState.value = true
}
//子字典项弹窗(编辑、添加)
const subDictDrawerSize = 310
const subDictDrawerState = ref(false)
const subDictDrawerTitle = {
	name: ["字典信息"],
	icon: ["fas", "square-share-nodes"]
}
const onSubDictDrawerBtnClick = (btnName: string | undefined) => {
	console.log(btnName)
	if (btnName === "取消") {
		subDictDrawerState.value = false
	}
	if (btnName === "确定") {
		subDictFormRef.value.validate((valid: any) => {
			if (valid) {
				subDictForm.sortedBy = parseInt(subDictForm.sortedBy)
				console.log("验证成功", subDictForm)
				subDictDrawerState.value = false
				if (!subDictForm.id) {
					console.log("新增")
					//新增数据
					subDictForm.id =
						"new" + (Math.random() * Math.pow(10, 16)).toString().split(".")[0]
					const parentNode = getTreeNode(subDictForm.parentId)
					if (parentNode) {
						if (!parentNode.children) {
							parentNode.children = []
						}
						parentNode.children.push({ ...subDictForm })
						//按照sortBy重新排序
						doSort(parentNode.children)
					} else {
						subDictTableData.value.push({ ...subDictForm })
						//按照sortBy重新排序
						doSort(subDictTableData.value)
					}
				} else {
					//更新数据
					const treeNode = getTreeNode(subDictForm.id)
					Object.assign(treeNode, subDictForm)
					if (treeNode.parentId) {
						const parentNode = getTreeNode(treeNode.parentId)
						if (parentNode) {
							doSort(parentNode.children)
						}
					} else {
						doSort(subDictTableData.value)
					}
				}
			}
		})
	}
}
const doSort = (itemList: any[]) => {
	itemList.sort((a: any, b: any) => {
		return a.sortedBy - b.sortedBy
	})
}

const getTreeNode = (nodeId: any) => {
	const findPath = XEUtils.findTree(
		subDictTableData.value,
		(item: any) => item.id === nodeId
	)
	if (findPath && findPath.item) {
		return findPath.item
	} else {
		return null
	}
}

const onSubDictDrawerClose = () => {
	console.log("onSubDictDrawerClose")
}
//对外部提供的接口
const tableRef = ref()
const handleAdd = () => {
	console.log("handleAdd -- tree")
	treeDictForm.id = ""
	treeDictForm.treeDictionaryCode = ""
	treeDictForm.treeDictionaryName = ""
	treeDictForm.notes = ""
	treeDictForm.treeDictionarySubitems = []
	subDictTableData.value = []
	drawerState.value = true
}
const handleQuery = () => {
	pageObj.value.currentPage = 0
	tableRef.value.resetCurrentPage()
	dictionaryListInit()
}
onMounted(() => {
	dictionaryListInit()
})

defineExpose({
	handleAdd,
	handleQuery
})
</script>
<style lang="scss" scoped>
.app-el-scrollbar-wrapper {
	height: 0;
	flex: 1;
}

.drawer-lr-layout-wrapper {
	display: flex;
	position: absolute;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	overflow: hidden;

	&::after {
		content: "";
		width: 1px;
		height: 120%;
		position: absolute;
		left: 310px;
		top: -15px;
		background-color: var(--pitaya-border-color);
	}
}

.common-from-wrapper {
	height: 100%;
	width: 0;
	flex: 1;
	display: flex;
	flex-direction: column;
	padding: 10px;

	.drawer-content-wrapper {
		position: absolute;
		left: 130px;

		&::after {
			content: "";
			width: 100%;
			height: 2px;
			background-color: var(--pitaya-btn-background);
			position: absolute;
			bottom: -10px;
			left: 50%;
			transform: translateX(-50%);
		}
	}

	&.common-from-left {
		width: 310px;
		flex: 0 0 310px;
	}

	&.common-from-only {
		width: 100%;
		padding: 0;
	}

	.common-from-group {
		height: 0;
		flex: 1;

		.el-form-wrapper {
			padding-left: 10px;
			padding-right: 10px;
		}
	}

	.btn-groups {
		display: flex;
		justify-content: flex-end;
		padding: 10px 10px 0 0;
		border-top: 1px solid var(--pitaya-border-color);
	}
}
</style>
@/app/platform/hooks/usePagination @/app/platform/api/system/dictionary
