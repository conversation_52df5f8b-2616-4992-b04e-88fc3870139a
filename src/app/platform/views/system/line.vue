<script lang="ts" setup>
import { ElMessage } from "element-plus"
import LineForm from "./components/lineForm.vue"
import { usePagination } from "@/app/platform/hooks/usePagination"
import { LineApi } from "@/app/platform/api/system/line"
import {
	checkPermission,
	isCheckPermission
} from "@/app/platform/utils/permission"
import { CustomMessageBox } from "@/app/platform/utils/message"

defineOptions({
	name: "systemLine"
})
const containerTitle = {
	name: ["线路管理"],
	icon: ["fas", "square-share-nodes"]
}
const lineContainerBtn = [
	{
		name: "新增线路",
		roles: "system:line:btn:add",
		icon: ["fas", "square-plus"]
	}
]
const tableColumn: TableColumnType[] = [
	{ label: "线路编码", prop: "code", width: 150 },
	{ label: "线路名称", prop: "name", needSlot: true, width: 130 },
	{ label: "线路里程", prop: "length", needSlot: true, width: 120 },
	{ label: "车站数量", prop: "stationNumber", width: 120 },
	{ label: "起始站", prop: "firstStation",minWidth:150 },
	{ label: "终点站", prop: "lastStation",minWidth:150 },
	{ label: "初次运营日期", prop: "firstEnableTime", width: 120 },
	{ label: "更新时间", prop: "lastModifiedDate", width: 200 },
	{
		label: "操作",
		prop: "operations",
		fixed: "right",
		width: 150,
		needSlot: true
	}
]

const usePaginationStore = usePagination()
const usePaginationStoreForData = usePaginationStore.paginationData

const tableLoading = ref<boolean>(false)
const tableData = ref<any[]>([])
const pageTotal = ref<number>(0)
const currentPage = ref<number>(usePaginationStoreForData.currentPage)
const pageSize = ref<number>(usePaginationStoreForData.pageSize)

// 新增线路抽屉
const onBtnClick = () => {
	nextTick(() => {
		lineFormRef.value.formDataInit()
	})
}
const getTableData = () => {
	tableLoading.value = true
	LineApi.getLineList(
		objectToFormData({
			currentPage: currentPage.value,
			pageSize: pageSize.value
		})
	)
		.then((res: any) => {
			pageTotal.value = res.records
			tableData.value = res.rows ?? []
		})
		.finally(() => {
			tableLoading.value = false
		})
}

const lineFormRef = ref()
// 编辑
const onRowEdit = (data: any) => {
	nextTick(() => {
		lineFormRef.value.formDataInit(data)
	})
}
// 移除
const onRowDelete = (data: any) => {
	CustomMessageBox({ message: "确定要移除吗？" }, (res: boolean) => {
		if (res) {
			LineApi.deleteLine(
				objectToFormData({
					id: data.id,
					hard: true // 是否物理移除
				})
			).then(() => {
				ElMessage.success("移除成功")
				getTableData()
			})
		}
	})
}
// 回调：新增
const onAddNewSave = () => {
	getTableData()
}

// 分页回调
const onCurrentPageChange = (pageData: any) => {
	pageSize.value = pageData.pageSize
	currentPage.value = pageData.currentPage
	getTableData()
}

onMounted(() => {
	getTableData()
})
</script>

<template>
	<div class="app-container">
		<ModelFrame>
			<Title
				:title="containerTitle"
				:button="lineContainerBtn"
				@onBtnClick="onBtnClick"
			/>
			<div class="app-el-scrollbar-wrapper">
				<el-scrollbar>
					<PitayaTable
						:need-index="true"
						:table-data="tableData"
						:columns="tableColumn"
						:total="pageTotal"
						@on-current-page-change="onCurrentPageChange"
						:table-loading="tableLoading"
					>
						<template #name="{ rowData }">
							<div>
								<el-tag
									class="line-item-btn"
									:style="{
										color: '#FFFFFF',
										backgroundColor: rowData.colour,
										borderColor: rowData.colour
									}"
									>{{ rowData.name }}</el-tag
								>
							</div>
						</template>
						<template #length="{ rowData }">
							<div>{{ rowData.length }}KM</div>
						</template>
						<template #operations="{ rowData }">
							<el-button
								v-btn
								link
								@click="onRowEdit(rowData)"
								:disabled="checkPermission('system:line:btn:edit')"
								v-if="isCheckPermission('system:line:btn:edit')"
							>
								<font-awesome-icon
									:icon="['fas', 'pen-to-square']"
									:class="
										checkPermission('system:line:btn:edit') ? 'disabled' : ''
									"
									style="color: var(--pitaya-btn-background)"
								/>
								<span
									class="table-inner-btn"
									:class="
										checkPermission('system:line:btn:edit') ? 'disabled' : ''
									"
									>编辑</span
								>
							</el-button>
							<el-button
								v-btn
								color="var(--pitaya-btn-background)"
								link
								@click="onRowDelete(rowData)"
								:disabled="checkPermission('system:line:btn:delete')"
								v-if="isCheckPermission('system:line:btn:delete')"
							>
								<font-awesome-icon
									:icon="['fas', 'trash-can']"
									:class="
										checkPermission('system:line:btn:delete') ? 'disabled' : ''
									"
									style="color: var(--pitaya-btn-background)"
								/>
								<span
									class="table-inner-btn"
									:class="
										checkPermission('system:line:btn:delete') ? 'disabled' : ''
									"
									>移除</span
								>
							</el-button>
							<div
								v-if="
									!isCheckPermission('system:line:btn:edit') &&
									!isCheckPermission('system:line:btn:delete')
								"
							>
								---
							</div>
						</template>
					</PitayaTable>
				</el-scrollbar>
			</div>
		</ModelFrame>
		<!-- 线路新增/编辑 -->
		<LineForm ref="lineFormRef" @on-submit="onAddNewSave" />
	</div>
</template>
<style lang="scss" scoped>
.cancel-button-class {
	color: #fff;
}
.cancel-button-class:hover {
	color: #fff;
}
</style>
<style lang="scss" scoped>
.app-container {
	::v-deep(.model-frame-wrapper) {
		display: flex;
		flex-direction: column;
		height: 100%;
	}
	.app-el-scrollbar-wrapper {
		flex: 1;
		height: 0;

		.table-inner-btn {
			margin-left: 5px;
			color: var(--pitaya-btn-background);
		}
	}
}
</style>
@/app/platform/hooks/usePagination @/app/platform/api/system/line
