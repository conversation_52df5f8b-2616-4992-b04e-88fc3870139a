<script setup lang="ts">
import {
	deleteManufacturer,
	getManufacturerPage,
	saveManufacturer
} from "@/app/platform/api/system/manufacturer"
import { usePagination } from "@/app/platform/hooks/usePagination"
import { FormRules } from "element-plus"
import {
	checkPermission,
	isCheckPermission
} from "@/app/platform/utils/permission"
import { CustomMessageBox } from "@/app/platform/utils/message"
import { getDictionaryInfo } from "@/app/platform/utils/dicEcho"

//查询条件
const queryArrList = ref([
	{
		name: "厂商类型",
		key: "manufacturerType",
		type: "select",
		enableFuzzy: false,
		placeholder: "请选择",
		children: []
	},
	{
		name: "厂商名称",
		key: "name",
		type: "input",
		placeholder: "请输入查询关键字",
		enableFuzzy: false
	},
	{
		name: "厂商简称",
		key: "oldName",
		type: "input",
		placeholder: "请输入查询关键字",
		enableFuzzy: false
	},
	{
		name: "厂商编码",
		key: "code",
		type: "input",
		placeholder: "请输入查询关键字",
		enableFuzzy: false
	}
])
const tableRef = ref()
const manufacturerType = ref([])
const getQueryData = (data: any) => {
	tableRef.value.resetCurrentPage()
	paramObj.value.name = data.name ? "*" + data.name.trim() + "*" : ""
	paramObj.value.code = data.code ? "*" + data.code.trim() + "*" : ""
	paramObj.value.oldName = data.oldName ? "*" + data.oldName.trim() + "*" : ""
	paramObj.value.manufacturerType = data.manufacturerType
	paramObj.value.currentPage = data.currentPage
	queryTableData()
}

const tableData = ref<any[]>([])
const columns = ref<TableColumnType[]>([
	{
		prop: "manufacturerType",
		label: "厂商类型",
		minWidth: 100,
		needSlot: true
	},
	{ prop: "name", label: "厂商名称", minWidth: 200, align:'left'},
	{ prop: "oldName", label: "厂商简称", width: 140 },
	{ prop: "code", label: "厂商编码", width: 100 },
	{ prop: "contactPerson", label: "联系人", width: 100 },
	{ prop: "contactAddress", label: "联系地址", width: 200 },
	{ prop: "remark", label: "备注说明", width: 200 },
	{ prop: "lastModifiedDate", label: "更新时间", width: 160 },
	{
		prop: "operations",
		fixed: "right",
		label: "操作",
		needSlot: true,
		width: 180
	}
])
const tableLoading = ref(false)
const drawerLoading = ref(false)
const usePaginationStore = usePagination()
const paginationData = ref<PaginationData>(usePaginationStore.paginationData)
const paramObj = ref<any>({
	currentPage: paginationData.value.currentPage,
	pageSize: paginationData.value.pageSize,
	name: "",
	code: "",
	oldName: "",
	manufacturerType: ""
})
const drawerBtnList = ref([
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "保存",
		icon: ["fas", "floppy-disk"]
	}
])
const dataTotal = ref(0)
const onRowDelete = (data: any) => {
	CustomMessageBox({ message: "确定要移除吗?" }, (res: boolean) => {
		if (res) {
			deleteManufacturer(data.id).then(() => {
				ElMessage.success("移除成功")
				queryTableData()
			})
		}
	})
}
const viewFlag = ref(false)
const onRowEdit = (data: any, type: any) => {
	//查看禁止修改
	if (type == "view") {
		viewFlag.value = true
		drawerBtnList.value = [
			{
				name: "取消",
				icon: ["fas", "circle-minus"]
			}
		]
	} else {
		viewFlag.value = false
		drawerBtnList.value = [
			{
				name: "取消",
				icon: ["fas", "circle-minus"]
			},
			{
				name: "保存",
				icon: ["fas", "floppy-disk"]
			}
		]
	}
	drawerStatus.value = true
	entityForm.id = data.id
	entityForm.code = data.code
	entityForm.name = data.name
	entityForm.oldName = data.oldName
	entityForm.sortedBy = data.sortedBy
	entityForm.remark = data.remark
	entityForm.manufacturerType = data.manufacturerType
		? data.manufacturerType + ""
		: ""
	entityForm.legalRepresentative = data.legalRepresentative
	entityForm.contactPerson = data.contactPerson
	entityForm.contactPhone = data.contactPhone
	entityForm.contactAddress = data.contactAddress
	entityForm.email = data.email
}
const onCurrentPageChange = (pd: PaginationData) => {
	paginationData.value = pd
	paramObj.value.currentPage = pd.currentPage
	paramObj.value.pageSize = pd.pageSize
	queryTableData()
}
const queryTableData = () => {
	tableLoading.value = true
	getManufacturerPage({ ...paramObj.value })
		.then((res: any) => {
			if (res.rows && res.rows.length > 0) {
				dataTotal.value = res.records
				tableData.value = res.rows
			} else {
				dataTotal.value = 0
				tableData.value = []
			}
		})
		.catch((err: any) => {
			// console.log(err)
		})
		.finally(() => {
			tableLoading.value = false
		})
}
// 弹出框
const drawerSize = 310
const drawerStatus = ref(false)
const entityFormRef = ref<any>(null)
const entityForm = reactive<anyKey>({
	code: "",
	name: "",
	oldName: "",
	manufacturerType: "",
	legalRepresentative: "",
	contactPerson: "",
	contactPhone: "",
	contactAddress: "",
	email: "",
	remark: "",
	sortedBy: "1000"
})
const entityFormRules = reactive<FormRules<typeof entityForm>>({
	name: {
		required: true,
		message: "请输入厂商名称",
		trigger: "blur"
	},
	oldName: {
		required: true,
		message: "请输入厂商简称",
		trigger: "blur"
	},
	contactPhone: [
		{
			pattern: /^1[3456789]\d{9}$/,
			message: "手机号码格式不正确",
			trigger: ["blur", "change"]
		}
	],
	email: [
		{
			pattern: /^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$/,
			message: "邮箱格式不正确",
			trigger: ["blur", "change"]
		}
	]
})

const onDrawerBtnClick = (btnName: string | undefined) => {
	drawerBtnList.value = [
		{
			name: "取消",
			icon: ["fas", "circle-minus"]
		},
		{
			name: "保存",
			icon: ["fas", "floppy-disk"]
		}
	]
	if (btnName === "取消") {
		drawerStatus.value = false
	}
	if (btnName === "新增厂商") {
		entityForm.id = ""
		drawerStatus.value = true
		entityForm.code = ""
		entityForm.name = ""
		entityForm.oldName = ""
		entityForm.remark = ""
		entityForm.manufacturerType = ""
		entityForm.legalRepresentative = ""
		entityForm.contactPerson = ""
		entityForm.contactPhone = ""
		entityForm.contactAddress = ""
		entityForm.email = ""
		entityForm.sortedBy = "1000"
		viewFlag.value = false
	}
	if (btnName === "保存") {
		entityFormRef.value.validate((valid: any) => {
			if (valid) {
				if (!entityForm.id) {
					delete entityForm.id
				}
				drawerLoading.value = true
				saveManufacturer(entityForm)
					.then(() => {
						entityFormRef.value.resetFields()
						ElMessage.success("操作成功")
						drawerStatus.value = false
						queryTableData()
					})
					.finally(() => {
						drawerLoading.value = false
					})
			}
		})
	}
}
const filterManufacturerType = (_manufacturerType: any) => {
	if (manufacturerType.value.length > 0) {
		let data = manufacturerType.value.filter(
			(item: any) => item.value == _manufacturerType
		)
		return data[0]?.label
	}
}
onMounted(() => {
	queryTableData()
	getDictionaryInfo("MANUFACTURER_TYPE", "dataDictionary").then((res) => {
		manufacturerType.value = res
		queryArrList.value[0].children = res
	})
})
</script>
<template>
	<div class="app-container">
		<ModelFrame>
			<Query
				class="ml10"
				:queryArrList="queryArrList"
				@getQueryData="getQueryData"
			/>
		</ModelFrame>
		<div class="app-content-wrapper">
			<div class="app-content-group">
				<ModelFrame>
					<Title
						:title="{ name: ['厂商管理'], icon: ['fas', 'square-share-nodes'] }"
						@onBtnClick="onDrawerBtnClick"
						:button="[
							{
								name: '新增厂商',
								roles: 'system:manufacturer:btn:add',
								icon: ['fas', 'plus-square']
							}
						]"
					/>
					<div class="app-el-scrollbar-wrapper">
						<el-scrollbar>
							<PitayaTable
								ref="tableRef"
								:tableData="tableData"
								:columns="columns"
								:total="dataTotal"
								@onCurrentPageChange="onCurrentPageChange"
								:table-loading="tableLoading"
							>
								<template #updateTime="{ rowData }">{{
									rowData.updateTime
								}}</template>
								<template #manufacturerType="{ rowData }">{{
									filterManufacturerType(rowData.manufacturerType) || "---"
								}}</template>

								<template #operations="{ rowData }">
									<el-button v-btn link @click="onRowEdit(rowData, 'view')">
										<font-awesome-icon
											:icon="['fas', 'eye']"
											style="color: var(--pitaya-btn-background)"
										/>
										<span class="table-inner-btn">查看</span>
									</el-button>
									<el-button
										v-btn
										link
										@click="onRowEdit(rowData, 'edit')"
										:disabled="checkPermission('system:manufacturer:btn:edit')"
										v-if="isCheckPermission('system:manufacturer:btn:edit')"
									>
										<font-awesome-icon
											:icon="['fas', 'pen-to-square']"
											:class="
												checkPermission('system:manufacturer:btn:edit')
													? 'disabled'
													: ''
											"
											style="color: var(--pitaya-btn-background)"
										/>
										<span
											class="table-inner-btn"
											:class="
												checkPermission('system:manufacturer:btn:edit')
													? 'disabled'
													: ''
											"
											>编辑</span
										>
									</el-button>
									<el-button
										v-btn
										link
										@click="onRowDelete(rowData)"
										:disabled="
											checkPermission('system:manufacturer:btn:delete')
										"
										v-if="isCheckPermission('system:manufacturer:btn:delete')"
									>
										<font-awesome-icon
											:icon="['fas', 'trash-can']"
											:class="
												checkPermission('system:manufacturer:btn:delete')
													? 'disabled'
													: ''
											"
											style="color: var(--pitaya-btn-background)"
										/>
										<span
											class="table-inner-btn"
											:class="
												checkPermission('system:manufacturer:btn:delete')
													? 'disabled'
													: ''
											"
											>移除</span
										>
									</el-button>
								</template>
							</PitayaTable>
						</el-scrollbar>
					</div>
				</ModelFrame>
			</div>
		</div>
		<Drawer
			v-if="drawerStatus"
			:size="drawerSize"
			v-model:drawer="drawerStatus"
		>
			<div
				class="common-from-wrapper common-from-only"
				v-loading="drawerLoading"
			>
				<Title
					:title="{ name: ['厂商信息'], icon: ['fas', 'square-share-nodes'] }"
				/>
				<div class="common-from-group">
					<el-scrollbar>
						<el-form
							class="el-form-wrapper"
							label-width="auto"
							label-position="top"
							ref="entityFormRef"
							:model="entityForm"
							:rules="entityFormRules"
						>
							<el-form-item label="厂商编码">
								<el-input
									:placeholder="entityForm.code ? entityForm.code : '自动生成'"
									disabled
								/>
							</el-form-item>
							<el-form-item label="厂商名称" prop="name">
								<el-input
									v-model.trim="entityForm.name"
									placeholder="请输入厂商名称"
									:disabled="viewFlag"
								/>
							</el-form-item>
							<el-form-item label="厂商简称" prop="oldName">
								<el-input
									v-model.trim="entityForm.oldName"
									placeholder="请输入厂商简称"
									:disabled="viewFlag"
								/>
							</el-form-item>
							<el-form-item label="厂商类型">
								<el-select
									filterable
									style="width: 100%"
									v-model="entityForm.manufacturerType"
									clearable
									:disabled="viewFlag"
								>
									<el-option
										v-for="item in manufacturerType"
										:key="item.value"
										:label="item.label"
										:value="item.value"
									/>
								</el-select>
							</el-form-item>
							<el-form-item label="法定代表人">
								<el-input
									v-model.trim="entityForm.legalRepresentative"
									placeholder="请输入法定代表人"
									:disabled="viewFlag"
								/>
							</el-form-item>
							<el-form-item label="联系人">
								<el-input
									v-model.trim="entityForm.contactPerson"
									placeholder="请输入联系人"
									:disabled="viewFlag"
								/>
							</el-form-item>
							<el-form-item label="联系人手机号" prop="contactPhone">
								<el-input
									maxlength="11"
									:show-word-limit="true"
									v-model.trim="entityForm.contactPhone"
									placeholder="请输入手机号码"
									:disabled="viewFlag"
								/>
							</el-form-item>
							<el-form-item label="联系地址">
								<el-input
									v-model.trim="entityForm.contactAddress"
									:autosize="{ minRows: 3, maxRows: 3 }"
									type="textarea"
									placeholder="请输入联系地址"
									:disabled="viewFlag"
								/>
							</el-form-item>
							<el-form-item label="电子邮箱" prop="email">
								<el-input
									v-model.trim="entityForm.email"
									placeholder="请输入电子邮箱"
									:disabled="viewFlag"
								/>
							</el-form-item>
							<el-form-item label="备注说明" prop="remark">
								<el-input
									v-model.trim="entityForm.remark"
									:autosize="{ minRows: 3, maxRows: 10 }"
									type="textarea"
									placeholder="请输入备注说明"
									:disabled="viewFlag"
								/>
							</el-form-item>
							<el-form-item label="排序">
								<el-input-number
									style="width: 100%"
									controls-position="right"
									v-model="entityForm.sortedBy"
									:min="1"
									:max="10000"
									:disabled="viewFlag"
								/>
							</el-form-item>
						</el-form>
					</el-scrollbar>
				</div>
				<div class="btn-groups">
					<ButtonList :button="drawerBtnList" @onBtnClick="onDrawerBtnClick" />
				</div>
			</div>
		</Drawer>
	</div>
</template>
<style lang="scss" scoped>
.app-container {
	:deep(.model-frame-wrapper) {
		padding-bottom: 0px;
	}
}

.app-content-wrapper {
	height: 0;
	flex: 1;
	margin-top: 10px;

	.app-content-group {
		height: 100%;

		:deep(.model-frame-wrapper) {
			height: 100%;
			padding-bottom: 10px;
			overflow: hidden;
			display: flex;
			flex-direction: column;
		}

		.app-el-scrollbar-wrapper {
			height: 0;
			flex: 1;

			.table-inner-btn {
				margin-left: 5px;
				color: #204a9c;
			}
		}
	}
}
.common-from-wrapper {
	height: 100%;
	width: 0;
	flex: 1;
	display: flex;
	flex-direction: column;
	padding: 10px;

	.drawer-content-wrapper {
		position: absolute;
		left: 130px;

		&::after {
			content: "";
			width: 100%;
			height: 2px;
			background-color: var(--pitaya-btn-background);
			position: absolute;
			bottom: -10px;
			left: 50%;
			transform: translateX(-50%);
		}
	}

	&.common-from-left {
		width: 310px;
		flex: 0 0 310px;
	}

	&.common-from-only {
		width: 100%;
		padding: 0;
	}

	.common-from-group {
		height: 0;
		flex: 1;

		.el-form-wrapper {
			padding-left: 10px;
			padding-right: 10px;
		}
	}

	.btn-groups {
		display: flex;
		justify-content: flex-end;
		padding: 10px 10px 0 0;
		border-top: 1px solid var(--pitaya-border-color);
	}
}
:deep(.el-textarea__inner) {
	/* 针对 WebKit 浏览器隐藏滚动条 */
	-webkit-scrollbar: none;
	/* Firefox */
	scrollbar-width: none; /* Firefox 64+ */
}
</style>
@/app/platform/hooks/usePagination @/app/platform/api/system/manufacturer
