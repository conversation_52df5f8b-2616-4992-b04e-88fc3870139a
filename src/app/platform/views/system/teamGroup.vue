<script lang="ts" setup>
import { TeamGroupApi } from "@/app/platform/api/system/teamGroup"
import TreamGroupDrawer from "./components/treamGroupDrawer.vue"
import { usePagination } from "@/app/platform/hooks/usePagination"
import { ElMessage } from "element-plus"
import { DepartmentApi } from "@/app/platform/api/system/department"
import {
	checkPermission,
	isCheckPermission
} from "@/app/platform/utils/permission"
import { CustomMessageBox } from "@/app/platform/utils/message"

const TeamGroupTreeRef = ref<any>(null)
const containerLeftTitle = {
	name: ["班组管理"],
	icon: ["fas", "square-share-nodes"]
}
const departmentContainerBtn = [
	{
		name: "新增班组",
		roles: "system:team:btn:add",
		icon: ["fas", "square-plus"]
	}
]
const departmentTreeProp = {
	children: "children",
	label: (data: any) => {
		if (data.companyStatus !== null) {
			return `${data.name}${
				data.companyStatus == 0 || data.orgStatus == 0 || data.teamedStatus == 0
					? `<span style='color: #E25E59'>(停用)</span>`
					: ""
			}`
		} else {
			return `${data.name}`
		}
	}
}

const departmentTableProp: TableColumnType[] = [
	{ label: "班组名称", prop: "orgAllName", minWidth: 380,align:'left' },
	{ label: "班组编码", prop: "orgCode", width: 120 },
	{ label: "管辖线路", prop: "lines", needSlot: true, minWidth: 120  },
	{ label: "关联专业", prop: "majorNum", needSlot: true, width: 85 },
	{ label: "关联账户", prop: "userNum", needSlot: true, width: 85 },
	{ label: "状态", prop: "orgStatus", needSlot: true, width: 85 },
	{ label: "更新时间", prop: "lastModifiedDate",width: 160 },
	{
		label: "操作",
		prop: "operations",
		fixed: "right",
		needSlot: true,
		width: 180
	}
]

const tableLoading = ref<boolean>(false)
const treeLoading = ref<boolean>(false)

const drawerSize = 1240
const drawerSizeDetail = "620px"
const treeList = ref("") //保存用户当前点击树信息
const treeListData = ref("") //保存用户当前点击树信息
const usePaginationStore = usePagination()
const usePaginationStoreForData = usePaginationStore.paginationData

const containerRightTitle = ref<any>({
	name: ["班组管理"],
	icon: ["fas", "square-share-nodes"]
})
const departmentTreeData = ref<any[]>([])
const departmentTableData = ref<any[]>([])
const showDrawer = ref<boolean>(false)
const showDetailDrawer = ref<boolean>(false) // 关联专业、关联位置弹窗
const drawerDetailTitle = ref<any>({
	name: ["关联专业"],
	icon: ["fas", "square-share-nodes"]
})
const drawerDetailTableColumn = ref<TableColumnType[]>([
	{ label: "专业编码", prop: "professionCode" },
	{ label: "专业组合名称", prop: "professionName" }
])
const drawerDetailTableData = ref<any[]>([])
const pageSize = ref<number>(usePaginationStoreForData.pageSize)
const pageTotal = ref<number>(0)
const currentPage = ref<number>(usePaginationStoreForData.currentPage)
// 当前点击的树节点
const currentClickTreeNode = ref<any>({})
const defaultExpandedKeys = ref<any[]>([])
const treeBizId = ref()

// 获取部门树
const getDepartmentTreeData = (flag: any) => {
	treeLoading.value = true
	TeamGroupApi.getTeamGroupTreeWithRole({
		companyStatus: showChildren.value ? 1 : 0
	})
		.then((res: any) => {
			departmentTreeData.value = res
			// 更新面包屑
			if (res.length > 0) {
				const titleObj = JSON.parse(JSON.stringify(containerLeftTitle))
				titleObj.name.push(res[0].name)
				containerRightTitle.value = titleObj
				if (!currentClickTreeNode.value.id) {
					currentClickTreeNode.value = res[0]
					defaultExpandedKeys.value = [res[0].id]
					getDepartmentTableData(res[0])
				} else {
					defaultExpandedKeys.value = [currentClickTreeNode.value.id]
					getDepartmentTableData()
				}
				//如果是新增或者修改，调用点击树方法重新渲染按钮和数据
				if (flag == "success") {
					departmentTreeClick(currentClickTreeNode.value, treeListData.value)
				}
				nextTick(() => {
					TeamGroupTreeRef.value.setCurrentKey(currentClickTreeNode.value.id)
				})
			}
		})
		.finally(() => {
			treeLoading.value = false
		})
}

// 获取部门表
const getDepartmentTableData = (data = currentClickTreeNode.value) => {
	if (!(data.id && data.id === currentClickTreeNode.value.id)) {
		currentClickTreeNode.value = data
	}
	const params = objectToFormData({
		...queryData.value,
		noShowAllTeamed: false,
		currentPage: currentPage.value,
		pageSize: pageSize.value,
		orgId: data.company ? "" : data.id,
		companyId: data.companyId
	})
	tableLoading.value = true
	departmentTableData.value = []
	TeamGroupApi.getTeamGroupTable(params)
		.then((res: any) => {
			if (res) {
				pageTotal.value = res.records
				departmentTableData.value = res.rows
			} else {
				departmentTableData.value = []
			}
		})
		.finally(() => {
			tableLoading.value = false
		})
}
// 树点击
const refTable = ref()
const departmentTreeClick = (data: any, dataTree: any) => {
	treeList.value = data
	treeListData.value = dataTree
	currentPage.value = 1
	tableEditDetail.value = {}
	clearSelected()
	refTable.value.resetCurrentPage()
	// 更新面包屑
	containerRightTitle.value.name = JSON.parse(
		JSON.stringify(containerLeftTitle)
	).name.concat(getTreeTitle(dataTree, "name"))
	if (data) getDepartmentTableData(data)
}
//停用
const tableEditDetail = ref<any>({})
const setTreeClick = () => {
	if (
		// //如果班组为停用
		(tableEditDetail.value.orgStatus == 0 && tableEditDetail.value.teamed) ||
		(currentClickTreeNode.value.orgStatus == 0 &&
			currentClickTreeNode.value.teamed)
	) {
		ElMessage.warning("已停用不可操作")
		return false
	} else {
		return true
	}
}
const onBtnClick = (_btnName: string | undefined) => {
	if(currentClickTreeNode.value.company){
		return ElMessage.error("当前级别不可新建班组")
	}
	tableEditDetail.value = {}
	clearSelected()
	if (!extractPublicmMethods()) return
	if (!setTreeClick()) return
	showDrawer.value = true
	nextTick(() => {
		TeamGroupDrawerRef.value?.init({
			companyId: currentClickTreeNode.value.companyId,
			orgAllName: currentClickTreeNode.value.allName,
			parentOrgId:currentClickTreeNode.value.id
		})
	})
}
const onRowEdit = (row: any) => {
	tableEditDetail.value = row
	clearSelected()
	if (!extractPublicmMethods()) return
	if (!setTreeClick()) return
	showDrawer.value = true
	nextTick(() => {
		TeamGroupDrawerRef.value?.init(row)
	})
}

const onRowDelete = (row: any) => {
	tableEditDetail.value = row
	clearSelected()
	if (!extractPublicmMethods()) return
	if (!setTreeClick()) return
	CustomMessageBox({ message: "确定要移除吗?" }, (res: boolean) => {
		if (res) {
			TeamGroupApi.deleteTeamGroup(row.id).then((res: any) => {
				if (res != "删除成功") {
					ElMessage.error("部门下存在用户请先删除下属用户")
					return
				}
				ElMessage.success("移除成功")
				getDepartmentTreeData("success")
			})
		}
	})
}

//清空选项框
const clearSelected = () => {
	selectionTableList.value = []
	refTable.value?.clearSelectedTableData()
}

// 关联专业查询
const drawerDetailTableLoading = ref(false)
const getRelatedMajorTableData = (data: any) => {
	drawerDetailTableData.value = []
	drawerDetailTitle.value = {
		name: ["关联专业"],
		icon: ["fas", "square-share-nodes"]
	}
	drawerDetailTableColumn.value = [
		{ label: "专业编码", prop: "name", class: "tree-cell-flex", align: "left" },
		{ label: "专业名称", prop: "code" }
	]

	drawerDetailTableLoading.value = true
	DepartmentApi.getProfessionForDepartment(data.id)
		.then((res: any) => {
			drawerDetailTableData.value = res ?? []
		})
		.finally(() => {
			drawerDetailTableLoading.value = false
		})
	showDetailDrawer.value = true
}

// 关联账户
const getAssociatedAccountTableData = (data: any) => {
	drawerDetailTableData.value = []
	drawerDetailTitle.value = {
		name: ["关联账户"],
		icon: ["fas", "square-share-nodes"]
	}
	drawerDetailTableColumn.value = [
		{ label: "用户账号", prop: "username" },
		{ label: "用户名称", prop: "realname" },
		{ label: "部门名称", prop: "orgName" }
	]
	drawerDetailTableLoading.value = true
	showDetailDrawer.value = true
	TeamGroupApi.getUserByTeamGroupId(data.id)
		.then((res: any) => {
			drawerDetailTableData.value = res
		})
		.finally(() => {
			drawerDetailTableLoading.value = false
		})
}
// 分页
const onCurrentPageChange = (pageData: any) => {
	pageSize.value = pageData.pageSize
	currentPage.value = pageData.currentPage
	getDepartmentTableData()
}

const onCloseAddDrawer = (flag: string) => {
	if (flag === "success") {
		getDepartmentTreeData(flag)
		// getDepartmentTableData()
	}
	showDrawer.value = false
}
const TeamGroupDrawerRef = ref()

//查询
const queryArrList = ref<any[]>([
	{
		name: "班组名称",
		key: "orgAllName",
		type: "input",
		enableFuzzy: false,
		placeholder: "请输入查询关键字"
	},
	{
		name: "班组编码",
		key: "orgCode",
		type: "input",
		enableFuzzy: false,
		placeholder: "请输入查询关键字"
	}
])
const queryData = ref<any>({})
const getQueryData = (queryParams?: any) => {
	currentPage.value = 1
	refTable.value.resetCurrentPage()
	queryData.value.orgAllName = queryParams.orgAllName
		? queryParams.orgAllName
		: ""
	queryData.value.orgCode = queryParams.orgCode ? queryParams.orgCode : ""
	getDepartmentTableData()
}

//切换显示已停用部门
const showChildren = ref(false)
//是否显示已停用部门
const setShowChildren = () => {
	currentPage.value = 1
	getDepartmentTreeData("success")
}

//停用
const paginationBtnList = ref([
	{
		name: "启用",
		icon: ["fas", "power-off"],
		roles: "system:device:auth:btn:enable",
		disabled: true
	},
	{
		name: "停用",
		roles: "system:device:auth:btn:disable",
		icon: ["fas", "circle-stop"],
		disabled: true
	}
])
const selectionTableList = ref<any[]>([])
const getSelectionTableList = (rowList: any) => {
	tableEditDetail.value = {}
	selectionTableList.value = rowList
}
const startEndLoading = ref<boolean>(false)
//提取按钮权限公共方法
const extractPublicmMethods = () => {
	//如果公司为停用
	if (
		treeList.value.companyStatus == 0 ||
		currentClickTreeNode.value.companyStatus == 0
	) {
		ElMessage.warning("已停用不可操作")
		return false
	} else if (
		//如果部门为停用
		(treeList.value.orgStatus == 0 && !treeList.value.teamed) ||
		(currentClickTreeNode.value.orgStatus == 0 && !treeList.value.teamed)
	) {
		ElMessage.warning("已停用不可操作")
		return false
	} else {
		return true
	}
}
const onPaginationBtnClick = (btnName: string | undefined) => {
	if (!extractPublicmMethods()) return
	//如果班组为停用
	if (
		(treeList.value.orgStatus == 0 && tableEditDetail.value.teamed) ||
		(currentClickTreeNode.value.orgStatus == 0 &&
			currentClickTreeNode.value.teamed)
	) {
		ElMessage.warning("已停用不可操作")
		return
	}

	const sledTlId: anyKey[] = []
	if (selectionTableList.value && selectionTableList.value.length) {
		selectionTableList.value.forEach((item: any) => {
			sledTlId.push(item.id)
		})
	}
	let authorizationStatus = "0"
	if (btnName === "启用") {
		if (!sledTlId.length) {
			ElMessage({
				message: "请选择需要启用的班组信息！",
				type: "warning"
			})
			return
		}
		authorizationStatus = "1"
		batchStatusUpdate(sledTlId, authorizationStatus)
	}
	if (btnName === "停用") {
		if (!sledTlId.length) {
			ElMessage({
				message: "请选择需要停用的班组信息！",
				type: "warning"
			})
			return
		}
		authorizationStatus = "0"
		batchStatusUpdate(sledTlId, authorizationStatus)
	}
}
const batchStatusUpdate = (sledTlId: anyKey[], authorizationStatus: string) => {
	// 状态校验
	if (!checkState(sledTlId, authorizationStatus)) return false

	if (authorizationStatus == "0") {
		CustomMessageBox(
			{
				message: "您即将停用此班组。您确定要继续吗？"
			},
			(res: boolean) => {
				if (res) {
					getStatusChange(sledTlId, authorizationStatus)
				}
			}
		)
	} else {
		getStatusChange(sledTlId, authorizationStatus)
	}
}

const getStatusChange = (sledTlId: anyKey[], authorizationStatus: string) => {
	if (startEndLoading.value) return
	startEndLoading.value = true
	DepartmentApi.getStatusChange({
		orgIds: sledTlId.join(","),
		status: authorizationStatus
	})
		.then(() => {
			getDepartmentTableData()
			ElMessage.success(`${authorizationStatus === "1" ? "启用" : "停用"}成功`)
			refTable.value?.clearSelectedTableData()
			getDepartmentTreeData("success")
		})
		.finally(() => {
			startEndLoading.value = false
		})
}

function checkState(
	selected: anyKey[] | Ref<anyKey[]>,
	state: string | number
) {
	selected = unref(selected)
	state = +state

	if (selected.length === 1) {
		if (state == 1) {
			if (selectionTableList.value[0].orgStatus == "1") {
				ElMessage.error("该班组已启用")
				return false
			}
		}
		if (state == 0) {
			if (selectionTableList.value[0].orgStatus == "0") {
				ElMessage.error("该班组已停用")
				return false
			}
		}
	}

	if (selected.length > 1) {
		if (state == 1) {
			if (selectionTableList.value.some((item) => item.orgStatus == "1")) {
				ElMessage.error("存在已启用班组")
				return false
			}
		}
		if (state == 0) {
			if (selectionTableList.value.some((item) => item.orgStatus == "0")) {
				ElMessage.error("存在已停用班组")
				return false
			}
		}
	}

	return true
}
watchEffect(() => {
	if (selectionTableList.value.length) {
		// 启用
		if (
			selectionTableList.value.some((item) => item.authorizationStatus === "1")
		) {
			paginationBtnList.value[0].disabled = true
		} else {
			paginationBtnList.value[0].disabled = false
		}
		// 停用
		if (
			selectionTableList.value.some((item) => item.authorizationStatus === "2")
		) {
			paginationBtnList.value[1].disabled = true
		} else {
			paginationBtnList.value[1].disabled = false
		}
	} else {
		paginationBtnList.value[0].disabled = true
		paginationBtnList.value[1].disabled = true
	}
})

/***
 *
 * 按钮权限
 *
 * **/

const edit = computed(() => {
	return checkPermission("system:team:btn:edit")
})
const editShow = computed(() => {
	return isCheckPermission("system:team:btn:edit")
})

const deleteInfo = computed(() => {
	return checkPermission("system:team:btn:delete")
})
const deleteShow = computed(() => {
	return isCheckPermission("system:team:btn:delete")
})

onMounted(() => {
	getDepartmentTreeData()
})

defineOptions({
	name: "DepartmentManagement"
})
</script>
<template>
	<div class="app-container-row">
		<ModelFrame class="left-model-frame">
			<Title :title="containerLeftTitle">
				<div>
					<el-switch
						class="mr5"
						v-model="showChildren"
						size="small"
						@change="setShowChildren"
					/>
					<span class="f12 c-3">显示已停用部门</span>
				</div>
			</Title>
			<div class="left-model-group">
				<PitayaTree
					ref="TeamGroupTreeRef"
					:treeData="departmentTreeData"
					:treeProps="departmentTreeProp"
					:needCheckBox="false"
					v-model:treeBizId="treeBizId"
					:defaultExpandedKeys="defaultExpandedKeys"
					@onTreeClick="departmentTreeClick"
					:tree-loading="treeLoading"
				/>
			</div>
		</ModelFrame>
		<div class="right-model-frame">
			<ModelFrame>
				<Query
					class="ml10"
					:queryArrList="queryArrList"
					@getQueryData="getQueryData"
				/>
			</ModelFrame>
			<ModelFrame class="content">
				<Title
					:title="containerRightTitle"
					:button="departmentContainerBtn"
					@onBtnClick="onBtnClick"
				/>
				<el-scrollbar>
					<PitayaTable
						ref="refTable"
						:needSelection="true"
						:columns="departmentTableProp"
						:table-data="departmentTableData"
						:need-index="true"
						:need-pagination="true"
						:total="pageTotal"
						@onSelectionChange="getSelectionTableList"
						@on-current-page-change="onCurrentPageChange"
						:table-loading="tableLoading"
					>
						<template #lines="{ rowData }">
							<div class="line-name-container">
								<el-tag
									v-for="(item, index) in rowData.lines"
									class="line-item"
									:key="index"
									:style="{ backgroundColor: item.colour, color: '#FFF' }"
									>{{ item.name }}</el-tag
								>
								<div v-if="rowData.lines.length === 0">---</div>
							</div>
						</template>
						<template #orgStatus="{ rowData }">
							<el-tag
								:class="'authState' + rowData.orgStatus"
								v-if="rowData.orgStatus !== null"
							>
								{{ rowData.orgStatus == "0" ? "已停用" : "已启用" }}
							</el-tag>
							<div v-else>---</div>
						</template>
						<template #majorNum="{ rowData }">
							<div
								class="border-bottom-text"
								@click="getRelatedMajorTableData(rowData)"
							>
								{{ rowData.majorNum }}
							</div>
						</template>
						<template #userNum="{ rowData }">
							<div
								class="border-bottom-text"
								@click="getAssociatedAccountTableData(rowData)"
							>
								{{ rowData.userNum }}
							</div>
						</template>
						<template #operations="{ rowData }">
							<el-button
								v-btn
								link
								@click="onRowEdit(rowData)"
								:disabled="edit"
								v-if="editShow"
							>
								<font-awesome-icon
									:icon="['fas', 'pen-to-square']"
									:class="edit ? 'disabled' : ''"
									style="color: var(--pitaya-btn-background)"
								/>
								<span class="table-inner-btn" :class="edit ? 'disabled' : ''"
									>编辑</span
								>
							</el-button>
							<el-button
								v-btn
								color="var(--pitaya-btn-background)"
								link
								:disabled="deleteInfo"
								v-if="deleteShow"
								@click="onRowDelete(rowData)"
							>
								<font-awesome-icon
									:icon="['fas', 'trash-can']"
									:class="deleteInfo ? 'disabled' : ''"
									style="color: var(--pitaya-btn-background)"
								/>
								<span
									class="table-inner-btn"
									:class="deleteInfo ? 'disabled' : ''"
									>移除</span
								>
							</el-button>
							<div v-if="!editShow && !deleteShow">---</div>
						</template>
						<template #footerOperateLeft>
							<ButtonList
								:button="paginationBtnList"
								:isNotRadius="true"
								:loading="startEndLoading"
								@onBtnClick="onPaginationBtnClick"
							/>
						</template>
					</PitayaTable>
				</el-scrollbar>
				<Drawer :size="drawerSize" v-model:drawer="showDrawer">
					<TreamGroupDrawer
						v-if="showDrawer"
						ref="TeamGroupDrawerRef"
						@on-close-drawer="onCloseAddDrawer"
					/>
				</Drawer>
				<Drawer :size="drawerSizeDetail" v-model:drawer="showDetailDrawer">
					<Title :title="drawerDetailTitle" />
					<PitayaTable
						:columns="drawerDetailTableColumn"
						:tableData="drawerDetailTableData"
						:need-index="true"
						:table-loading="drawerDetailTableLoading"
					/>
				</Drawer>
			</ModelFrame>
		</div>
	</div>
</template>
<style lang="scss" scoped>
.app-container-row {
	align-items: center;
	justify-content: space-between;
	flex-direction: row;

	.el-drawer__body {
		overflow: hidden;
	}

	.left-model-frame {
		width: 20%;
		height: 100%;
		display: flex;
		flex-direction: column;

		.left-model-group {
			height: 0;
			flex: 1;
			overflow: hidden;
		}
	}

	.right-model-frame {
		width: calc(100% - 20% - 10px);
		display: flex;
		flex-direction: column;
		height: 100%;

		.line-name-container {
			display: flex;
			align-items: center;

			.line-item {
				margin-right: 10px;
				padding: 0 10px;
				height: 22px;
				border-radius: 3px;
				font-size: var(--pitaya-fs-12);
				color: #fff;
				display: flex;
				align-items: center;
				justify-content: center;
			}

			.line-item:last-child {
				margin-right: 0;
			}
		}

		.border-bottom-text {
			position: relative;
			color: #204a9c;
			text-decoration: underline;
			cursor: pointer;
		}
	}
}

.authState0 {
	color: #e25e59;
	border-color: #e25e59;
	background-color: #f9e7e7;
}

.authState1 {
	color: #4bae89;
	border-color: #4bae89;
	background-color: #e0ffef;
}

.authStatenull {
	color: #4bae89;
	border-color: #4bae89;
	background-color: #e0ffef;
}
</style>
