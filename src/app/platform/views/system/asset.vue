<script lang="ts" setup>
import { getTreeData, getAssetList } from "@/app/platform/api/system/asset"
import { usePagination } from "@/app/platform/hooks/usePagination"
import { getTreeTitle, copeCVTree } from "@/app/platform/utils/common"

interface queryObj {
	/**
	 * 上级编码
	 */
	parentCode: string
	/**
	 * 每页显示条数，默认10
	 */
	pageSize?: number
	/**
	 * 页码，默认1
	 */
	currentPage?: number
}

const usePaginationStore = usePagination({})
const paginationData = ref<PaginationData>(usePaginationStore.paginationData)

const paramObj = ref<queryObj>({
	parentCode: "",
	currentPage: paginationData.value.currentPage,
	pageSize: paginationData.value.pageSize
})

const getTableList = (pd: PaginationData) => {
	paramObj.value.currentPage = pd.currentPage
	paramObj.value.pageSize = pd.pageSize
	AssetListInit()
}
const baseTableDataLoading = ref(false)
const AssetListInit = () => {
	baseTableDataLoading.value = true
	getAssetList(paramObj.value)
		.then((res: any) => {
			if (res.rows && res.rows.length) {
				baseTableData.value = res.rows
				dataTotal.value = res.records
			} else {
				baseTableData.value = []
				dataTotal.value = 0
			}
		})
		.finally(() => {
			baseTableDataLoading.value = false
		})
}

const tableTitle = ref<anyKey>({})
const tableTitleInit = {
	name: ["资产分类"],
	icon: ["fas", "square-share-nodes"]
}

const defaultExpandedId = ref([0])
const treeLoading = ref(false)
onMounted(() => {
	tableTitle.value = JSON.parse(JSON.stringify(tableTitleInit))
	treeLoading.value = true
	getTreeData({})
		.then((res: any) => {
			if (res) {
				treeData.value = copeCVTree([res], "code", "name")
				if (treeData.value[0]) {
					defaultExpandedId.value = [treeData.value[0].id]
					treeBizId.value = treeData.value[0].id
					tableTitle.value.name.push(treeData.value[0].name)
					paramObj.value.parentCode = ""
					AssetListInit()
				}
			}
		})
		.finally(() => {
			treeLoading.value = false
		})
})

const title = {
	name: ["资产分类"],
	icon: ["fas", "square-share-nodes"]
}

const treeData = ref<any[]>([])

const treeBizId = ref<number | string>()

const defaultProps = {
	children: "children",
	label: "name"
}

const dataTotal = ref(0)
const baseTableData = ref<any[]>([])
const baseColumns = ref<TableColumnType[]>([
	{ prop: "code", label: "资产标签编码", minWidth: 200 },
	{ prop: "name", label: "资产标签名称", minWidth: 400 }
])
const tableRef =ref()
const onTreeClick = (data: any, data1: any) => {
	treeBizId.value = data.id
	paramObj.value.currentPage = 1
	tableRef.value.resetCurrentPage()
	const getTreeTitleArr = getTreeTitle(data1, "name")
	tableTitle.value.name = JSON.parse(
		JSON.stringify(tableTitleInit)
	).name.concat(getTreeTitleArr)

	paramObj.value.parentCode = data.code === "0" ? "" : data.code
	AssetListInit()
}
</script>

<template>
	<div class="app-container">
		<div class="app-left-container">
			<div class="app-content-wrapper">
				<div class="app-content-group">
					<ModelFrame>
						<Title :title="title" />
						<div class="app-el-scrollbar-wrapper">
							<PitayaTree
								:default-expanded-keys="defaultExpandedId"
								:treeData="treeData"
								:treeProps="defaultProps"
								:needCheckBox="false"
								:tree-loading="treeLoading"
								v-model:treeBizId="treeBizId"
								@onTreeClick="onTreeClick"
							/>
						</div>
					</ModelFrame>
				</div>
			</div>
		</div>
		<div class="app-right-container">
			<div class="app-content-wrapper">
				<div class="app-content-group">
					<ModelFrame>
						<Title :title="tableTitle" />
						<div class="app-el-scrollbar-wrapper">
							<el-scrollbar>
								<PitayaTable
									ref="tableRef"
									@onCurrentPageChange="getTableList"
									:table-data="baseTableData"
									:columns="baseColumns"
									:total="dataTotal"
									:max-height="700"
									:table-loading="baseTableDataLoading"
								/>
							</el-scrollbar>
						</div>
					</ModelFrame>
				</div>
			</div>
		</div>
	</div>
</template>

<style lang="scss" scoped>
.app-container {
	flex-direction: row;
	.app-left-container {
		height: 100%;
		width: 20%;
		flex: 0 0 20%;
		display: flex;
		flex-direction: column;
		margin-right: 10px;
	}
	.app-right-container {
		height: 100%;
		width: 0;
		flex: 1;
		overflow: hidden;
		display: flex;
		flex-direction: column;
	}
	:deep(.model-frame-wrapper) {
		padding-bottom: 0px;
	}
}
.app-content-wrapper {
	height: 0;
	flex: 1;
	.app-content-group {
		height: 100%;
		:deep(.model-frame-wrapper) {
			height: 100%;
			padding-bottom: 10px;
			overflow: hidden;
			display: flex;
			flex-direction: column;
		}
		.app-el-scrollbar-wrapper {
			height: 0;
			flex: 1;
			.table-inner-btn {
				margin-left: 5px;
				color: #204a9c;
			}
		}
	}
}
</style>
