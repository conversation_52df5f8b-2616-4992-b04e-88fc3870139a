<script lang="ts" setup>
import XEUtils from "xe-utils"
import { ElMessage, ElMessageBox } from "element-plus"
import type { FormInstance, FormRules } from "element-plus"
import { usePagination } from "@/app/platform/hooks/usePagination"
import {
	getBaseRoleList,
	saveOrUpdateBaseRoleApi,
	getBaseRoleDetail,
	getAllPageUsers,
	removeRoleItem,
	roleReplication
} from "@/app/platform/api/system/baseRole"
import { requestMapApi } from "@/app/platform/api/system/requestMap"
import { getDictionaryTerm } from "@/app/platform/api/system/dictionary"
import { checkTree, generateMixed } from "@/app/platform/utils/common"
import { removeTreeNode, getIdList, secIdList } from "@/app/platform/utils/tree"
import { matchPermissionBtnList } from "@/app/platform/utils/permission"
import { baseUserApi } from "@/app/platform/api/system/baseUser"
import { useUserStore } from "@/app/platform/store/modules/user"
import { watchEffect } from "vue"
import { CustomMessageBox } from "@/app/platform/utils/message"

interface queryObj {
	/**
	 * 角色名称
	 */
	roleName?: string
	/**
	 * 页码
	 */
	currentPage?: number
	/**
	 * 每页条数
	 */
	pageSize?: number
}

const tableLoading = ref(false)
const drawerLoading = ref(false)
// 关联权限表格加载状态
const termTableDataLoading = ref(false)
// 选择用户抽屉表格加载状态
const baseUserTableDataLoading = ref(false)

const usePaginationStore = usePagination()
const paginationData = ref<PaginationData>(usePaginationStore.paginationData)

const paramObj = ref<queryObj>({
	roleName: "",
	currentPage: paginationData.value.currentPage,
	pageSize: paginationData.value.pageSize
})

const getTableList = (pd: PaginationData) => {
	paramObj.value.currentPage = pd.currentPage
	paramObj.value.pageSize = pd.pageSize
	baseRoleListInit()
}

const getQueryData = (queryData: queryObj) => {
	queryUserObj.value.currentPage = 1
	tableRef.value.resetCurrentPage()
	paramObj.value = Object.assign(paramObj.value, queryData)
	baseRoleListInit()
}

const baseRoleListInit = () => {
	tableLoading.value = true
	getBaseRoleList(paramObj.value)
		.then((res: any) => {
			if (res.rows && res.rows.length) {
				baseTableData.value = res.rows
				dataTotal.value = res.records
			} else {
				baseTableData.value = []
				dataTotal.value = 0
			}
		})
		.finally(() => {
			tableLoading.value = false
		})
}

const roleType = ref<any[]>([])

const rootNode = ref<any>({})
const renderTypeList = ref([])

onMounted(() => {
	// 获取全部公司列表
	getAllCompany()
	baseRoleListInit()
	getDictionaryTerm({
		dataDictionaryCode: "MENU_LEVEL"
	}).then((res: any) => {
		if (res && res.length) {
			const children: any[] = []
			res.forEach((item: any) => {
				children.push({
					label: item.subitemName,
					value: item.subitemValue
				})
			})
			roleType.value = children
		}
	})

	//获取权限展示方式类型
	getDictionaryTerm({
		dataDictionaryCode: "MENU_DISPLAY_TYPE"
	}).then((res: any) => {
		if (res && res.length) {
			const children: any[] = []
			res.forEach((item: any) => {
				children.push({
					label: item.subitemName,
					value: item.subitemValue
				})
			})
			renderTypeList.value = children
		}
	})
	requestMapApi.getListRoleALLWithTree().then((res: anyKey) => {
		// 新增树字段
		;(function refreshMenuName(obj) {
			;[obj].flat().forEach((item) => {
				let typeName = roleType.value.find(
					(role) => role.value == item.menuLevel
				)
				typeName = typeName ? typeName.label + " : " : ""
				// 拼接按钮展示样式
				let data = renderTypeList.value.filter(
					(cur) => cur.value == item.menuDisplayType
				)
				let displayBug = ""
				if (data.length > 0) {
					displayBug = "(" + data[0].label + ")"
				}
				item.menuTypeName = typeName + item.menuName + displayBug
				if (item.chirldMenu) {
					refreshMenuName(item.chirldMenu)
				}
			})
		})(res)

		/**
		 * 处理 关联权限 不展示根节点的需求
		 * 虽然页面不展示根节点信息，但是接口中需要携带
		 * 根节点数据单独保存，树中就不展示了，最终接口中再拼
		 * 冯建凯  9.27
		 */
		treeData.value = res.chirldMenu
		rootNode.value = res
		rootNode.value.chirldMenu = []

		// treeData.value = [res]
	})
})

const baseFormData = reactive<anyKey>({
	companyId: "",
	roleName: "",
	roleCode: "",
	// chickIds: "",
	menuIds: [],
	menuTreeIds: [],
	userIds: [],
	sortNum: 1000
})

const queryUserObj = ref<anyKey>({
	currentPage: paginationData.value.currentPage,
	pageSize: paginationData.value.pageSize
})

const AllPageUsersInit = (queryParams?: any) => {
	const params = {
		...queryUserObj.value,
		companyId: baseFormData.companyId,
		station: queryParams?.station,
		realname: queryParams?.realname,
		username: queryParams?.username
	}
	baseUserTableDataLoading.value = true
	getAllPageUsers(params)
		.then((res: any) => {
			if (res.rows && res.rows.length) {
				baseUserTableData.value = res.rows
			} else {
				baseUserTableData.value = []
			}
			termDataTotal.value = res.records
		})
		.finally(() => {
			baseUserTableDataLoading.value = false
		})
}

const getUserTableList = (pd: PaginationData) => {
	queryUserObj.value.currentPage = pd.currentPage
	queryUserObj.value.pageSize = pd.pageSize
	AllPageUsersInit()
}
const userTableRef = ref()
const onUserBtnClick = (btnName: string | undefined) => {
	if (btnName === "确定") {
		const chooseDataList = userTableRef.value.getSelectedTable()
		termUserData.value = JSON.parse(JSON.stringify(chooseDataList))
		termTableData.value = JSON.parse(JSON.stringify(termUserData.value))
		baseFormData.userIds = getIdList(termUserData.value)
		drawerUserState.value = false
		return
	}

	if (btnName === "取消") {
		drawerUserState.value = false
		return
	}
}

const termDataTotal = ref(0)
const baseUserTableData = ref<any[]>([])
const userBaseColumns = ref<TableColumnType[]>([
	{ prop: "username", label: "用户账号", width: 140 },
	{ prop: "realname", label: "用户姓名", width: 140 },
	{ prop: "station", label: "岗位名称", minWidth: 200 },
	{ prop: "orgName", label: "部门名称", minWidth: 200 }
])

const getRoleTypeDesc = (type: number | string | boolean) => {
	let desc = "---"
	const ret = roleType.value.filter((item: any) => {
		return item.value == type
	})
	desc = ret && ret.length ? ret[0].label : desc
	return desc
}

const lookBaseRoleDetail = (id: string | number) => {
	return getBaseRoleDetail({
		id
	})
}

const queryArrList = ref<any[]>([
	{
		name: "所属公司",
		key: "companyId",
		placeholder: "请选择",
		enableFuzzy: false,
		type: "select",
		children: []
	},
	{
		name: "角色名称",
		key: "roleName",
		placeholder: "请输入查询关键字",
		enableFuzzy: false,
		type: "input"
	},
	{
		name: "角色编码",
		key: "roleCode",
		placeholder: "请输入查询关键字",
		enableFuzzy: false,
		type: "input"
	}
])

const title = {
	name: ["角色管理"],
	icon: ["fas", "square-share-nodes"]
}

const drawerTitle = ref<any>({
	name: ["关联权限"],
	icon: ["fas", "square-share-nodes"]
})

const tabList = [
	{
		name: "关联权限",
		icon: ["fas", "square-plus"]
	},
	{
		name: "关联账号",
		icon: ["fas", "square-plus"]
	}
]

const button = ref([
	{
		name: "新增角色",
		icon: ["fas", "square-plus"]
	}
])

const tremInit = () => {
	paginationBtnList.value[0].name = "选择权限"
	termColumns.value = JSON.parse(JSON.stringify(roleColumns))
	termTableData.value = []
	activeName.value = tabList[0].name
}

const onBtnClick = () => {
	Object.keys(baseFormData).map((key) => (baseFormData[key] = ""))
	baseFormData.menuIds = []
	baseFormData.userIds = []
	baseFormData.sortNum = 1000
	delete baseFormData.id
	baseFormRef.value?.clearValidate()
	termRoleData.value = []
	termUserData.value = []
	detailsDrawerState.value = true
	nextTick(() => {
		tremInit()

		// 生成角色编码
		const { userInfo } = storeToRefs(useUserStore())
		baseFormData.roleCode = "ROLE_" + generateMixed(6).toUpperCase()
		baseFormData.companyId = userInfo.value.companyId || ""
	})
}

const drawerState = ref(false)
const drawerSize = 410

const detailsDrawerState = ref(false)
const detailsDrawerSize = 930

const drawerUserState = ref(false)
const drawerUserSize = 1200

const dataTotal = ref(0)
const baseTableData = ref<any[]>([])
const baseColumns = ref<TableColumnType[]>([
	{ prop: "companyName", label: "所属公司", width: 180 },
	{ prop: "roleName", label: "角色名称", minWidth: 250 },
	{ prop: "roleCode", label: "角色编码", width: 150 },
	{ prop: "userCount", label: "关联账号", needSlot: true, width: 120 },
	{ prop: "menuCount", label: "关联权限", needSlot: true, width: 120 },
	{ prop: "lastModifiedDate", label: "更新时间", width: 160 },
	{
		prop: "operations",
		fixed: "right",
		label: "操作",
		needSlot: true,
		width: 140
	}
])

const formRules = reactive<FormRules<typeof baseFormData>>({
	roleCode: [{ required: true, message: "请输入角色编码", trigger: "blur" }],
	roleName: [{ required: true, message: "请输入角色名称", trigger: "blur" }],
	companyId: [
		{ required: true, message: "请选择所属公司", trigger: ["blur", "change"] }
	]
})

const BaseRoleItemDetail = ref<anyKey>({})

const isSuperAdmin = ref(false)
const onEdit = async (rowData: anyKey) => {
	detailsDrawerState.value = true
	if (rowData.id === "ROLE_ADMIN") {
		isSuperAdmin.value = true
	} else {
		isSuperAdmin.value = false
	}
	tremInit()
	Object.keys(baseFormData).forEach((key) => {
		baseFormData[key] = rowData[key] ? rowData[key] : ""
	})
	baseFormData.id = rowData.id
	// 回显公司
	baseFormData.companyId = rowData.companyId || ""
	termTableDataLoading.value = true
	BaseRoleItemDetail.value = await lookBaseRoleDetail(rowData.id)
	termTableDataLoading.value = false

	let menuTree: any[] = []
	if (BaseRoleItemDetail.value.menuTree) {
		menuTree = BaseRoleItemDetail.value.menuTree.chirldMenu
		// menuTree = [BaseRoleItemDetail.value.menuTree]
	}
	termTableData.value = checkTree(
		menuTree,
		"chirldMenu",
		"children",
		"chirldMenu"
	)
	initialTermTableData.value = JSON.parse(JSON.stringify(termTableData.value)) // 保存初始权限数据
	// console.log('===JSON.parse(JSON.stringify(termTableData.value))',JSON.parse(JSON.stringify(termTableData.value)))
	baseFormData.userIds = BaseRoleItemDetail.value.userIds
	baseFormData.menuIds = BaseRoleItemDetail.value.menuIds
	// baseFormData.chickIds = BaseRoleItemDetail.value.chickIds
	termRoleData.value = JSON.parse(JSON.stringify(termTableData.value))
	termUserData.value = JSON.parse(
		JSON.stringify(BaseRoleItemDetail.value.baseUserList)
	)

	detailsDrawerState.value = true
}

const onDelete = (rowData: anyKey) => {
	CustomMessageBox({ message: "确定要移除该角色信息吗？" }, (res: boolean) => {
		if (res) {
			removeRoleItem({
				id: rowData.id
			}).then(() => {
				baseRoleListInit()
				ElMessage.success("操作成功")
			})
		} else {
			return false
		}
	})
}

const onDeleteTermTable = (rowData: anyKey) => {
	CustomMessageBox(
		{ message: `确定要移除该${activeName.value}吗？` },
		(res: boolean) => {
			if (res) {
				if (activeName.value === "关联账号") {
					termTableData.value = termTableData.value.filter(
						(item) => item.id !== rowData.id
					)
					termUserData.value = JSON.parse(JSON.stringify(termTableData.value))
					baseFormData.userIds = getIdList(termUserData.value)
				}
				if (activeName.value === "关联权限") {
					termTableData.value = removeTreeNode(
						termTableData.value,
						rowData.id,
						"id",
						"menuParentId"
					)
					termRoleData.value = JSON.parse(JSON.stringify(termTableData.value))
					baseFormData.menuIds = secIdList(
						baseFormData.menuIds,
						getIdList(termRoleData.value)
					)
				}
			} else {
				return false
			}
		}
	)
}

const baseFormRef = ref<FormInstance>()

const onFormBtnClick = (btnName: string | undefined) => {
	if (!baseFormRef.value) return
	if (btnName === "保存") {
		baseFormRef.value.validate((valid) => {
			if (valid) {
				const params: any = {
					...baseFormData,
					company: { id: baseFormData.companyId }
				}
				/**
				 * 处理 关联权限 不展示根节点的需求
				 * 虽然页面不展示根节点信息，但是接口中需要携带
				 * 根节点数据单独保存，树中就不展示了，最终接口中再拼
				 * 冯建凯  9.27
				 */
				if (!params.menuIds.includes(rootNode.value.id)) {
					params.menuIds.push(rootNode.value.id)
				}
				drawerLoading.value = true
				saveOrUpdateBaseRoleApi(params)
					.then(() => {
						baseRoleListInit()
						ElMessage.success("保存成功")
						detailsDrawerState.value = false
					})
					.finally(() => {
						drawerLoading.value = false
					})
			} else {
				return false
			}
		})
		return
	}

	if (btnName === "取消") {
		detailsDrawerState.value = false
		return
	}
}

const onClose = () => {
	isSuperAdmin.value = false
	Object.keys(baseFormData).map((key) => (baseFormData[key] = ""))
	baseFormRef.value?.clearValidate()
}

const refPitayaTree = ref()

// 递归合并树形权限数据
const mergeTreeData = (initialNodes: any[], selectedNodes: any[]) => {
	// 递归删除initialNodes中与treeData.value重复的节点
	const removeDuplicateNodes = (nodes: any[], treeNodes: any[]) => {
		return nodes.filter((node) => {
			// 检查当前节点是否存在于treeData中
			const existsInTree = treeNodes.some((treeNode) => treeNode.id === node.id)

			// 如果节点不存在于treeData中，则保留
			if (!existsInTree) return true

			// 检查selectedNodes中是否有该节点
			const existsInSelected = selectedNodes.some(
				(selectedNode) => selectedNode.id === node.id
			)

			// 如果selectedNodes中没有该节点，则删除
			if (!existsInSelected) return false

			// 如果节点存在于treeData中且selectedNodes中有该节点，检查其子节点
			if (node.children && node.children.length > 0) {
				const treeNode = treeNodes.find((t) => t.id === node.id)
				const treeChildren = treeNode?.children || treeNode?.chirldMenu || []
				node.children = removeDuplicateNodes(node.children, treeChildren)
				return node.children.length > 0 // 如果处理后还有子节点则保留
			}

			return true
		})
	}

	// 规范化节点结构
	const normalizeNodes = (nodes: any[]) => {
		return nodes.map((node) => {
			const newNode = { ...node }
			if (newNode.chirldMenu) {
				newNode.children = [...newNode.chirldMenu]
				delete newNode.chirldMenu
			}
			if (newNode.children) {
				newNode.children = normalizeNodes(newNode.children)
			}
			return newNode
		})
	}

	// 1. 先删除initialNodes中与treeData重复的节点
	const filteredInitialNodes = removeDuplicateNodes(
		normalizeNodes(initialNodes),
		treeData.value
	)
	// 2. 将处理后的initialNodes与selectedNodes合并
	const normalizedSelected = normalizeNodes(selectedNodes)

	const merged = [...filteredInitialNodes]
	normalizedSelected.forEach((selectedNode) => {
		const existingNode = merged.find((node) => node.id === selectedNode.id)
		if (existingNode) {
			// 合并子节点到children数组
			if (selectedNode.children && selectedNode.children.length > 0) {
				existingNode.children = mergeTreeData(
					existingNode.children || [],
					selectedNode.children
				)
			}
			// 合并其他属性
			Object.keys(selectedNode).forEach((key) => {
				if (key !== "id" && key !== "children") {
					existingNode[key] = selectedNode[key]
				}
			})
		} else {
			// 添加新节点到children数组
			merged.push(selectedNode)
		}
	})
	// 转换Proxy对象为普通对象并排序每一层级
	const sortedResult = JSON.parse(JSON.stringify(merged))

	// 递归排序树结构
	const sortTree = (nodes: any[]) => {
		if (!nodes || !nodes.length) return nodes
		// 排序当前层级
		const sortedNodes = XEUtils.sortBy(nodes, "sortedBy")
		// 递归子节点
		sortedNodes.forEach((node) => {
			if (node.children && node.children.length) {
				node.children = sortTree(node.children)
			}
		})

		return sortedNodes
	}

	return sortTree(sortedResult)
}
const onLookBtnClick = (btnName: string | undefined) => {
	refPitayaTree.value.filterText = "" // 关闭弹窗时清空tree查询条件
	if (!refPitayaTree.value) return
	if (btnName === "确定") {
		/**
		 * 处理 关联权限 不展示根节点的需求
		 * 虽然页面不展示根节点信息，但是接口中需要携带
		 * 根节点数据单独保存，树中就不展示了，最终接口中再拼
		 * 冯建凯  9.27
		 */

		let nodeList = refPitayaTree.value.getDataObjIncludeCheckedNodes()
		let isAuthorize = nodeList.find(
			(item: { menuLevel: number; chirldMenu: string | any[] }) =>
				item.menuLevel == 1 && item.chirldMenu.length == 0
		)

		if (isAuthorize) {
			ElMessage.warning(`功能模块不能单独授权`)
			return
		}

		const selectedNodes = refPitayaTree.value.getDataObjIncludeCheckedNodes()
		// console.log('==selectedNodes',selectedNodes)
		// console.log('==initialTermTableData.value,',initialTermTableData.value)
		// 合并上最新选择的权限
		const mergedData = mergeTreeData(initialTermTableData.value, selectedNodes)
		// console.log('===mergedData',mergedData)
		termTableData.value = checkTree(
			mergedData,
			"children",
			"children",
			"children"
		)
		let selectIds = XEUtils.toTreeArray(mergedData, {
			children: "children"
		})
		baseFormData.menuIds = XEUtils.toTreeArray(selectIds, {
			children: "children"
		}).map((item) => item.id)

		baseFormData.menuTreeIds =
			refPitayaTree.value.PitayaTreeRef.getCheckedKeys()
		termRoleData.value = JSON.parse(JSON.stringify(termTableData.value))
		drawerState.value = false
		return
	}

	if (btnName === "取消") {
		drawerState.value = false
		return
	}
}

const btnList = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "保存",
		icon: ["fas", "floppy-disk"]
	}
]

const submitBtn = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "确定",
		icon: ["fas", "floppy-disk"]
	}
]

const activeName = ref("")
activeName.value = tabList[0].name
const handleClick = () => {
	setTimeout(() => {
		if (activeName.value === "关联账号") {
			paginationBtnList.value[0].name = "选择用户"
			termColumns.value = JSON.parse(JSON.stringify(baseUserColumns))
			termTableData.value = JSON.parse(JSON.stringify(termUserData.value))
		}
		if (activeName.value === "关联权限") {
			paginationBtnList.value[0].name = "选择权限"
			termColumns.value = JSON.parse(JSON.stringify(roleColumns))
			termTableData.value = JSON.parse(JSON.stringify(termRoleData.value))
		}
	}, 200)
}

const detailsDrawerTitle = {
	name: ["角色信息"],
	icon: ["fas", "square-share-nodes"]
}

const detailsDrawerTitle2 = {
	name: ["扩展信息"],
	icon: ["fas", "square-share-nodes"]
}

const termTableData = ref<any[]>([])
const termRoleData = ref<any[]>([])
const termUserData = ref<any[]>([])
const initialTermTableData = ref<any[]>([]) // 存储初始权限数据
const roleColumns = [
	{
		prop: "purview",
		label: "权限编码",
		minWidth: 140,
		class: "tree-cell-flex",
		align: "left"
	},
	{ prop: "menuName", label: "权限名称", minWidth: 140 },
	{ prop: "menuLevel", label: "权限类型", needSlot: true, width: 120 },
	{
		prop: "operations",
		fixed: "right",
		label: "操作",
		needSlot: true,
		width: 100
	}
]
const baseUserColumns = [
	{ prop: "username", label: "用户账号", width: 140 },
	{ prop: "realname", label: "用户姓名", width: 100 },
	{ prop: "orgName", label: "部门", minWidth: 200 },
	{
		prop: "operations",
		fixed: "right",
		label: "操作",
		needSlot: true,
		width: 100
	}
]
const termColumns = ref<TableColumnType[]>([])
termColumns.value = JSON.parse(JSON.stringify(roleColumns))

const paginationBtnList = ref([
	{
		name: "选择权限",
		icon: ["fas", "square-plus"]
	}
])
const menuIds = ref([])
const onPaginationBtnClick = async (btnName: string | undefined) => {
	drawerTitle.value.name[0] = activeName.value
	if (activeName.value === "关联权限") {
		drawerState.value = true
		checkRelevanceNode()
		return
	}

	if (activeName.value === "关联账号") {
		queryUserObj.value.currentPage = 1
		await AllPageUsersInit()
		drawerUserState.value = true
		return
	}
}

// 勾选相关节点
const checkRelevanceNode = () => {
	menuIds.value = baseFormData.menuIds
		? JSON.parse(JSON.stringify(baseFormData.menuIds))
		: []

	nextTick(() => {
		let tableSelect = XEUtils.mapTree(treeData.value, (item) => {
			if (menuIds.value.includes(item.id)) {
				return item
			}
		})
		//只获取树的最后一级
		let data: any[] = []
		XEUtils.eachTree(
			tableSelect,
			(item: any) => {
				if (item?.id && menuIds.value.includes(item.id)) {
					data.push(item)
				}
			},
			{ children: "chirldMenu" }
		)
		refPitayaTree.value.PitayaTreeRef.setCheckedKeys(
			XEUtils.map(data, (item) => item.id)
		)
	})
}

const treeData = ref<any[]>([])
treeData.value = []

const defaultProps = {
	children: "chirldMenu",
	label: "menuTypeName"
}

const tableDrawerState = ref(false)
const tableDrawerSize = 620
const tableDrawerData = ref<any[]>([])
const tableDrawerColumns = ref<any[]>([])
const tableDrawerBtnList = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	}
]

// 关联账户搜索
const queryArrAccountList = [
	{
		name: "用户账号",
		key: "username",
		placeholder: "请输入查询关键字",
		type: "input",
		enableFuzzy: false
	},
	{
		name: "用户姓名",
		key: "realname",
		placeholder: "请输入查询关键字",
		type: "input",
		enableFuzzy: false
	},
	{
		name: "岗位名称",
		key: "station",
		placeholder: "请输入查询关键字",
		type: "input",
		enableFuzzy: false
	}
]
const tableDrawerTitle = ref<any>({
	name: ["关联账号"],
	icon: ["fas", "square-share-nodes"]
})
const tableDrawerLoading = ref(false)
const lookDetail = async (rowData: anyKey, type: string) => {
	tableDrawerState.value = true
	tableDrawerLoading.value = true
	const BaseRoleDetail = await lookBaseRoleDetail(rowData.id)
	if (type === "role") {
		let menuTree: any[] = []
		if (BaseRoleDetail.menuTree) {
			// menuTree = [BaseRoleDetail.menuTree]
			menuTree = BaseRoleDetail.menuTree.chirldMenu
		}
		tableDrawerData.value = checkTree(
			menuTree,
			"chirldMenu",
			"children",
			"chirldMenu"
		)
		tableDrawerLoading.value = false
		tableDrawerColumns.value = JSON.parse(JSON.stringify(roleColumns)).slice(
			0,
			roleColumns.length - 1
		)
		tableDrawerTitle.value.name[0] = "关联权限"
	}
	if (type === "baseUser") {
		tableDrawerData.value = BaseRoleDetail.baseUserList
		tableDrawerLoading.value = false
		tableDrawerColumns.value = JSON.parse(
			JSON.stringify(baseUserColumns)
		).slice(0, baseUserColumns.length - 1)
		tableDrawerTitle.value.name[0] = "关联账号"
	}
	tableDrawerState.value = true
}

const hideTableDrawer = () => {
	tableDrawerState.value = false
}

// #region 获取公司列表相关
const companyList = ref<any[]>([])
// 获取全部公司列表
function getAllCompany() {
	baseUserApi
		.getAllCompany()
		.then((res: any) => {
			if (res && res.length > 0) {
				companyList.value = res.map((item: anyKey) => {
					return {
						label: item.companyName,
						value: item.id,
						companyStatus: item.companyStatus
					}
				})
				queryArrList.value[0].children = companyList.value
			} else {
				companyList.value = []
			}
		})
		.catch((err) => {
			throw new Error("getAllCompany():::" + err)
		})
}
// #endregion

// #region 权限树勾选逻辑
function handleTreeChange(data: anyKey, checked: boolean) {
	let getHalfCheckedKeys = refPitayaTree.value.PitayaTreeRef.getCheckedKeys()

	//如果包含当前ID，勾选上，如果取消勾选当前，清除子集所有勾选项
	if (getHalfCheckedKeys.includes(data.id)) {
		if (data.menuLevel > 1) {
			refPitayaTree.value.PitayaTreeRef.setChecked(data.menuParentId, true)
		}
	} else {
		XEUtils.eachTree(
			[data],
			(item) => {
				if (getHalfCheckedKeys.includes(item.id)) {
					refPitayaTree.value.PitayaTreeRef.setChecked(item.id, false)
				}
			},
			{ children: "chirldMenu" }
		)
	}
}
// #endregion

//复制
const copyRepairBtn = ref<any[]>([
	{
		name: "复制角色",
		icon: ["fas", "fa-copy"],
		disabled: true
	}
])

const tableRef = ref()
const copyLoading = ref<any>(false)
const onRepairBtn = (btnName: any) => {
	let data = tableRef.value.pitayaTableRef.getSelectionRows()
	if (data.length == 0) {
		ElMessage.warning(`请选择要${btnName}的修程`)
		return
	}
	if (copyLoading.value) return
	copyLoading.value = true
	if (btnName == "复制角色") {
		const roleCode = "ROLE_" + generateMixed(6).toUpperCase()
		roleReplication({ id: data[0].id, roleCode: roleCode })
			.then((res: any) => {
				ElMessage.success("复制成功")
				baseRoleListInit()
			})
			.finally(() => {
				copyLoading.value = false
				tableRef.value.clearSelectedTableData()
			})
	}
}
const selectionTableList = ref<any[]>([])
const getSelectionTableList = (rowList: any) => {
	selectionTableList.value = rowList
}
// 监听
watchEffect(() => {
	if (selectionTableList.value.length > 0) {
		copyRepairBtn.value.forEach((item) => {
			item.disabled = false
		})
	} else {
		copyRepairBtn.value.forEach((item) => {
			item.disabled = true
		})
	}
})
// 监听权限管理弹窗
watch(
	() => drawerState.value,
	(val: boolean) => {
		if (!val) refPitayaTree.value.filterText = "" // 关闭弹窗时清空tree查询条件
	}
)
</script>

<template>
	<div class="app-container">
		<ModelFrame>
			<Query
				class="ml10"
				:queryArrList="queryArrList"
				@getQueryData="getQueryData"
			/>
		</ModelFrame>
		<div class="app-content-wrapper">
			<div class="app-content-group">
				<ModelFrame>
					<Title :title="title" :button="button" @onBtnClick="onBtnClick" />
					<div class="app-el-scrollbar-wrapper">
						<el-scrollbar>
							<PitayaTable
								ref="tableRef"
								:singleSelect="true"
								:needSelection="true"
								:needPagination="true"
								@onCurrentPageChange="getTableList"
								:table-data="baseTableData"
								:columns="baseColumns"
								:total="dataTotal"
								:table-loading="tableLoading"
								@onSelectionChange="getSelectionTableList"
							>
								<template #userCount="{ rowData }">
									<span
										v-if="rowData.userCount || rowData.userCount == 0"
										class="can-click-btn"
										@click="lookDetail(rowData, 'baseUser')"
										>{{ rowData.userCount }}</span
									>
									<span v-else style="color: var(--pitaya-table-font-color)"
										>---</span
									>
								</template>
								<template #menuCount="{ rowData }">
									<span
										v-if="rowData.menuCount || rowData.menuCount == 0"
										class="can-click-btn"
										@click="lookDetail(rowData, 'role')"
										>{{ rowData.menuCount }}</span
									>
									<span v-else style="color: var(--pitaya-table-font-color)"
										>---</span
									>
								</template>
								<template #operations="{ rowData }">
									<el-button
										v-btn
										link
										@click="onEdit(rowData)"
										v-permission="matchPermissionBtnList('编辑')"
									>
										<font-awesome-icon
											:icon="['fas', 'pen-to-square']"
											style="color: var(--pitaya-btn-background)"
										/>
										<span class="table-inner-btn">编辑</span>
									</el-button>
									<el-button
										v-btn
										color="var(--pitaya-btn-background)"
										link
										v-permission="matchPermissionBtnList('移除')"
									>
										<font-awesome-icon
											:icon="['fas', 'trash-can']"
											style="color: var(--pitaya-btn-background)"
										/>
										<span class="table-inner-btn" @click="onDelete(rowData)"
											>移除</span
										>
									</el-button>
								</template>
								<template #footerOperateLeft>
									<ButtonList
										class="btn-list"
										:isNotRadius="true"
										:button="copyRepairBtn"
										:loading="copyLoading"
										@onBtnClick="onRepairBtn"
									/>
								</template>
							</PitayaTable>
						</el-scrollbar>
					</div>
				</ModelFrame>
			</div>
		</div>
		<Drawer
			:size="detailsDrawerSize"
			v-model:drawer="detailsDrawerState"
			@close="onClose"
		>
			<div class="drawer-lr-layout-wrapper" v-loading="drawerLoading">
				<div class="common-from-wrapper common-from-left">
					<Title :title="detailsDrawerTitle" />
					<div class="common-from-group">
						<el-scrollbar>
							<el-form
								class="el-form-wrapper"
								ref="baseFormRef"
								:model="baseFormData"
								:rules="formRules"
								label-width="auto"
								label-position="top"
							>
								<!-- :disabled="!!baseFormData.id" -->
								<el-form-item label="所属公司" prop="companyId" required>
									<el-select
										filterable
										style="width: 100%"
										v-model="baseFormData.companyId"
										clearable
									>
										<el-option
											v-for="item in companyList"
											:key="item.value"
											:label="`${item.label} ${
												item.companyStatus == 0 ? '（停用）' : ''
											}`"
											:value="item.value"
											:disabled="item.companyStatus === 0"
										/>
									</el-select>
								</el-form-item>
								<el-form-item label="角色编码" prop="roleCode">
									<el-input v-model="baseFormData.roleCode" :disabled="true" />
								</el-form-item>
								<el-form-item label="角色名称" prop="roleName">
									<el-input
										maxlength="50"
										:show-word-limit="true"
										v-model.trim="baseFormData.roleName"
									/>
								</el-form-item>
								<el-form-item label="排序" prop="sortNum">
									<el-input-number
										v-model="baseFormData.sortNum"
										style="width: 100%"
										controls-position="right"
										:min="1"
										:max="10000"
									/>
								</el-form-item>
							</el-form>
						</el-scrollbar>
					</div>
					<div class="btn-groups">
						<ButtonList :button="btnList" @onBtnClick="onFormBtnClick" />
					</div>
				</div>
				<div class="common-from-wrapper">
					<Title :title="detailsDrawerTitle2">
						<div class="app-tabs-wrapper">
							<el-tabs v-model="activeName" @tab-click="handleClick">
								<el-tab-pane
									v-for="(tab, index) in tabList"
									:key="index"
									:label="tab.name"
									:name="tab.name"
									:index="tab.name"
								/>
							</el-tabs>
						</div>
					</Title>
					<div class="common-from-group">
						<el-scrollbar>
							<PitayaTable
								:table-data="termTableData"
								:columns="termColumns"
								:need-index="true"
								:table-loading="termTableDataLoading"
								:max-height="672"
							>
								<template #menuLevel="{ rowData }">
									<span>{{ getRoleTypeDesc(rowData.menuLevel) }}</span>
								</template>
								<template #operations="{ rowData }">
									<el-button
										v-if="
											!(
												isSuperAdmin && paginationBtnList[0].name === '选择权限'
											)
										"
										v-btn
										color="var(--pitaya-btn-background)"
										link
									>
										<font-awesome-icon
											:icon="['fas', 'trash-can']"
											style="color: var(--pitaya-btn-background)"
										/>
										<span
											class="table-inner-btn"
											@click="onDeleteTermTable(rowData)"
											>移除</span
										>
									</el-button>
									<span v-else>---</span>
								</template>
								<template
									#footerOperateLeft
									v-if="
										!(isSuperAdmin && paginationBtnList[0].name === '选择权限')
									"
								>
									<ButtonList
										:button="paginationBtnList"
										:isNotRadius="true"
										@onBtnClick="onPaginationBtnClick"
									/>
								</template>
							</PitayaTable>
						</el-scrollbar>
					</div>
				</div>
				<!-- 关联权限 -->
				<Drawer :size="drawerSize" v-model:drawer="drawerState">
					<div class="common-from-wrapper common-from-only">
						<Title :title="drawerTitle">
							<template #title>
								<el-tooltip
									class="box-item"
									effect="dark"
									content="您只能授权当前账户已有的权限"
									placement="bottom"
								>
									<font-awesome-icon
										:icon="['far', 'fa-question-circle']"
										style="color: #666; margin: 0 10px"
									/>
								</el-tooltip>
							</template>
						</Title>
						<div class="common-from-group">
							<PitayaTree
								ref="refPitayaTree"
								:treeData="treeData"
								:treeProps="defaultProps"
								:checkStrictly="true"
								:needSingleSelect="false"
								@onTreeChange="handleTreeChange"
							/>
						</div>
						<div class="btn-groups">
							<ButtonList :button="submitBtn" @onBtnClick="onLookBtnClick" />
						</div>
					</div>
				</Drawer>
				<!-- 关联账号 -->
				<Drawer
					:size="drawerUserSize"
					v-model:drawer="drawerUserState"
					:destroyOnClose="true"
				>
					<div class="common-from-wrapper common-from-only">
						<Title :title="drawerTitle" />
						<div class="common-from-group">
							<el-scrollbar>
								<div style="margin: 10px 10px -8px 10px">
									<Query
										class="initiate-query"
										:queryArrList="queryArrAccountList"
										@getQueryData="AllPageUsersInit"
									/>
								</div>
								<PitayaTable
									ref="userTableRef"
									@onCurrentPageChange="getUserTableList"
									:table-data="baseUserTableData"
									:selectedTableData="termUserData"
									:columns="userBaseColumns"
									:total="termDataTotal"
									:pagerCount="5"
									:needSelection="true"
									:table-loading="baseUserTableDataLoading"
									:max-height="672"
								/>
							</el-scrollbar>
						</div>
						<!-- 关联账号 -->
						<div class="btn-groups">
							<ButtonList :button="submitBtn" @onBtnClick="onUserBtnClick" />
						</div>
					</div>
				</Drawer>
			</div>
		</Drawer>
		<Drawer :size="tableDrawerSize" v-model:drawer="tableDrawerState">
			<div class="common-from-wrapper common-from-only">
				<Title :title="tableDrawerTitle" />
				<div class="common-from-group">
					<el-scrollbar>
						<PitayaTable
							:table-data="tableDrawerData"
							:columns="tableDrawerColumns"
							:table-loading="tableDrawerLoading"
							:need-index="true"
							:max-height="800"
						>
							<template #menuLevel="{ rowData }">
								<span>{{ getRoleTypeDesc(rowData.menuLevel) }}</span>
							</template>
						</PitayaTable>
					</el-scrollbar>
				</div>
				<div class="btn-groups">
					<ButtonList
						:button="tableDrawerBtnList"
						@onBtnClick="hideTableDrawer"
					/>
				</div>
			</div>
		</Drawer>
	</div>
</template>

<style lang="scss" scoped>
.pitaya-table {
	overflow: unset;
}
.app-container {
	:deep(.model-frame-wrapper) {
		padding-bottom: 0px;
	}
}
.app-content-wrapper {
	height: 0;
	flex: 1;
	margin-top: 10px;
	.app-content-group {
		height: 100%;
		:deep(.model-frame-wrapper) {
			height: 100%;
			padding-bottom: 10px;
			overflow: hidden;
			display: flex;
			flex-direction: column;
		}
		.app-el-scrollbar-wrapper {
			height: 0;
			flex: 1;
			.table-inner-btn {
				margin-left: 5px;
				color: #204a9c;
			}
		}
	}
}
.app-tabs-wrapper {
	position: absolute;
	left: 150px;
	bottom: 0;
	:deep(.el-tabs__item) {
		font-size: 14px;
		color: #666;
		&.is-active {
			color: var(--pitaya-sidebar-menu-active-text-color);
		}
	}
	:deep(.el-tabs__active-bar) {
		background-color: var(--pitaya-header-bg-color);
	}
	:deep(.el-tabs__header) {
		margin-bottom: 0;
	}
	:deep(.el-tabs__nav-wrap) {
		&::after {
			width: 0;
			height: 0;
		}
	}
}
.drawer-lr-layout-wrapper {
	display: flex;
	position: absolute;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	overflow: hidden;
	&::after {
		content: "";
		width: 1px;
		height: 120%;
		position: absolute;
		left: 310px;
		top: -15px;
		background-color: var(--pitaya-border-color);
	}
}

.common-from-wrapper {
	height: 100%;
	width: 0;
	flex: 1;
	display: flex;
	flex-direction: column;
	padding: 10px;
	.drawer-content-wrapper {
		position: absolute;
		left: 130px;

		&::after {
			content: "";
			width: 100%;
			height: 2px;
			background-color: var(--pitaya-btn-background);
			position: absolute;
			bottom: -10px;
			left: 50%;
			transform: translateX(-50%);
		}
	}
	&.common-from-left {
		width: 310px;
		flex: 0 0 310px;
	}
	&.common-from-only {
		width: 100%;
		padding: 0;
	}
	.common-from-group {
		height: 0;
		flex: 1;
		.el-form-wrapper {
			padding-left: 10px;
			padding-right: 10px;
		}
	}
	.btn-groups {
		display: flex;
		justify-content: flex-end;
		padding: 10px 10px 0 0;
		border-top: 1px solid var(--pitaya-border-color);
	}
}
.can-click-btn {
	color: var(--pitaya-sidebar-menu-active-text-color);
	cursor: pointer;
	border-bottom: 1px solid var(--pitaya-sidebar-menu-active-text-color);
	&::after {
		content: "";
		opacity: 0;
		position: absolute;
		left: -20px;
		right: -20px;
		display: block;
		height: 100%;
		top: 0;
	}
}
</style>
