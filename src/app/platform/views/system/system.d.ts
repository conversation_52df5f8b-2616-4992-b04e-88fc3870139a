interface anyKey {
	[key: string]: any
}
interface options {
	label?: string
	value?: string | number | boolean | any
	disabled?: boolean
	children?: options[]
	[propName: string]: any
}

// 查询组件
interface querySetting {
	[key: string]: any
	name: string // 字段名称
	key: string // 字段接收key
	type: string // 表单类型 startAndEndTime dateTime input select cascader （有其他他特殊需求可自行扩展）
	placeholder?: string // 空选项描述
	enableFuzzy?: boolean // 是否开启模糊查询，默认开启
	children?: options[]
	disabledDate?: Function
	treeApi?: Function
	tableApi?: Function
	tableColumns?: any[]
	queryParams?: any
	replaceIdTo?: any // 默认选主键id作为key的value，特殊情况传递该值覆盖id，选择其他值作为key的value（通常适用于treeSelect）
	extraParams?: any // 查询所需额外参数（适用于treeSelect，一个查询项需要传递多个参数的情况）
	disabled?: boolean // 当前查询项是否禁用
}

interface PaginationData {
	total?: number
	currentPage?: number
	pageSize?: number
}
