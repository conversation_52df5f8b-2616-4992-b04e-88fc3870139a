<script lang="ts" setup>
import { DepartmentApi } from "@/app/platform/api/system/department"
import DepartmentDrawer from "./components/departmentDrawer.vue"
import { usePagination } from "@/app/platform/hooks/usePagination"
import { ElMessage } from "element-plus"
import {
	checkPermission,
	isCheckPermission
} from "@/app/platform/utils/permission"
import { CustomMessageBox } from "@/app/platform/utils/message"

const DepartTreeRef = ref<any>("")
const containerLeftTitle = {
	name: ["部门管理"],
	icon: ["fas", "square-share-nodes"]
}
const departmentContainerBtn = [
	{
		roles: "system:department:btn:add",
		name: "新增部门",
		icon: ["fas", "square-plus"]
	}
]

//companyStatus：0 停用  1:启用
const departmentTreeProp = {
	children: "children",
	label: (data: any) => {
		if (data.companyStatus !== null) {
			return `${data.name}${
				data.companyStatus == 0 || data.orgStatus == 0
					? `<span style='color: #E25E59'>(停用)</span>`
					: ""
			}`
		} else {
			return `${data.name}`
		}
	}
}

const departmentTableProp: TableColumnType[] = [
	{ label: "部门名称", prop: "orgName", width: 300,align:'left' },
	{ label: "部门编码", prop: "orgCode", width: 120 },
	{ label: "管辖线路", prop: "lines", needSlot: true, minWidth: 200 },
	{ label: "关联专业", prop: "majorNum", needSlot: true, width: 85 },
	{ label: "状态", prop: "orgStatus", needSlot: true, width: 85 },
	// { label: "关联位置", prop: "locationNum", needSlot: true, width: 85 },
	{ label: "更新时间", prop: "lastModifiedDate", width: 160 },
	{
		label: "操作",
		width: 150,
		prop: "operations",
		fixed: "right",
		needSlot: true
	}
]
const drawerSize = 1200
const drawerSizeDetail = "620px"

const treeList = ref("") //保存用户当前点击树信息
const treeListData = ref("") //保存用户当前点击树信息
const usePaginationStore = usePagination()
const usePaginationStoreForData = usePaginationStore.paginationData

const containerRightTitle = ref<any>({
	name: [],
	icon: ["fas", "square-share-nodes"]
})
const departmentTreeData = ref<any[]>([])
const departmentTableData = ref<any[]>([])
const showDrawer = ref<boolean>(false)
const tableLoading = ref<boolean>(false)
const treeLoading = ref<boolean>(false)
const showDetailDrawer = ref<boolean>(false) // 关联专业、关联位置弹窗
const drawerDetailTitle = ref<any>({
	name: ["关联专业"],
	icon: ["fas", "square-share-nodes"]
})
const drawerDetailTableColumn = ref<TableColumnType[]>([
	{ label: "专业编码", prop: "code" },
	{ label: "专业组合名称", prop: "name" }
])
const drawerDetailTableData = ref<any[]>([])
const pageSize = ref<number>(usePaginationStoreForData.pageSize)
const pageTotal = ref<number>(0)
const currentPage = ref<number>(usePaginationStoreForData.currentPage)
// 当前点击的树节点
const currentClickTreeNode = ref<any>({})
// 默认展开的树节点
const defaultExpandedKeys = ref<any[]>([])
const treeBizId = ref()
// 编辑的回显
const tableEditDetail = ref<any>({})

// 获取部门树
const getDepartmentTreeData = (flag: any) => {
	treeLoading.value = true
	DepartmentApi.getDepartmentTreeWithRole({
		companyStatus: showChildren.value ? 1 : 0
	})
		.then((res: any) => {
			// 更新面包屑
			const titleObj = JSON.parse(JSON.stringify(containerLeftTitle))
			titleObj.name.push(res[0].name)
			containerRightTitle.value = titleObj
			departmentTreeData.value = res
			ensureUniqueIds(departmentTreeData.value)

			if (!currentClickTreeNode.value.id) {
				currentClickTreeNode.value = res[0]
				defaultExpandedKeys.value = [res[0].idCopy]
				getDepartmentTableData(res[0])  // 直接传递 res[0] 避免响应式延迟
			} else {
				defaultExpandedKeys.value = [currentClickTreeNode.value.idCopy]
				getDepartmentTableData()
			}
			nextTick(() => {
				DepartTreeRef.value.setCurrentKey(currentClickTreeNode.value.idCopy)
			})

			//如果是新增或者修改，调用点击树方法重新渲染按钮和数据
			if (flag == "success") {
				departmentTreeClick(treeList.value, treeListData.value)
			}
		})
		.finally(() => {
			treeLoading.value = false
		})
}

const ensureUniqueIds = (
	tree: { id: number; children: any }[],
	idMap = {},
	nextId = 1
) => {
	tree.forEach((node: { id: number; children: any }) => {
		// 如果节点的id已经存在于idMap中，则为其分配一个新的唯一id
		node.idCopy = node.id + "-" + nextId++
		// 如果节点有子节点，递归地调用此函数
		if (node.children) {
			ensureUniqueIds(node.children, idMap, nextId)
		}
	})
}

// 获取部门表
const getDepartmentTableData = (data = currentClickTreeNode.value) => {
	if (!data) {
		currentClickTreeNode.value = departmentTreeData.value[0]
	} else {
		if (!(data.id && data.id === currentClickTreeNode.value.id)) {
			currentClickTreeNode.value = data
		}
	}
	const params = objectToFormData({
		...queryData.value,
		currentPage: currentPage.value,
		pageSize: pageSize.value,
		orgId: currentClickTreeNode.value.id,
		companyId:
			currentClickTreeNode.value.pid === null
				? currentClickTreeNode.value.companyId
				: ""
	})
	tableLoading.value = true
	DepartmentApi.getDepartmentTable(params)
		.then((res: any) => {
			pageTotal.value = res.records
			departmentTableData.value = res.rows
		})
		.finally(() => {
			tableLoading.value = false
		})
}
// 树点击
const refTable = ref()
const departmentTreeClick = (data: any, treeData: any) => {
	treeList.value = data
	treeListData.value = treeData
	currentPage.value = 1
	tableEditDetail.value = {}
	clearSelected()
	refTable.value.resetCurrentPage()

	// 更新面包屑
	containerRightTitle.value.name = JSON.parse(
		JSON.stringify(containerLeftTitle)
	).name.concat(getTreeTitle(treeData, "name"))
	// data.companyId = departmentTreeData.value[0].id
	getDepartmentTableData(data)
}

//停用
const setTreeClick = () => {
	if (
		treeList.value.companyStatus == 0 ||
		currentClickTreeNode.value.companyStatus == 0 ||
		tableEditDetail.value.orgStatus == 0 ||
		currentClickTreeNode.value.orgStatus == 0
	) {
		ElMessage.warning("已停用不可操作")
		return false
	} else {
		return true
	}
}
const onBtnClick = (_btnName: string | undefined) => {
	console.log('==urrentClickTreeNode.value',currentClickTreeNode.value)
	tableEditDetail.value = {
		companyId:currentClickTreeNode.value.companyId,
		orgAllName:currentClickTreeNode.value.allName,
		orgId:currentClickTreeNode.value.id,
		parentId:currentClickTreeNode.value.parentId,
		company:currentClickTreeNode.value.company
	}
	clearSelected()
	if (!setTreeClick()) return
	showDrawer.value = true
}
const onRowEdit = (row: any) => {
	tableEditDetail.value = row
	clearSelected()
	if (!setTreeClick()) return
	showDrawer.value = true
}

const onRowDelete = (row: any) => {
	tableEditDetail.value = row
	clearSelected()
	if (!setTreeClick()) return
	CustomMessageBox({ message: "确定要移除吗?" }, (res: boolean) => {
		if (res) {
			DepartmentApi.deleteDepartment(row.id)
				.then(() => {
					ElMessage.success("移除成功")
					// getDepartmentTableData()
					getDepartmentTreeData("success")
				})
				.catch(() => {
					// ElMessage.error("移除失败")
				})
		}
	})
}

//清空选项框
const clearSelected = () => {
	selectionTableList.value = []
	refTable.value?.clearSelectedTableData()
}

// 关联专业查询
const rawerDetailLoading = ref(false)
const getRelatedMajorTableData = (data: any) => {
	drawerDetailTableData.value = []
	rawerDetailLoading.value = true
	drawerDetailTitle.value = {
		name: ["关联专业"],
		icon: ["fas", "square-share-nodes"]
	}
	drawerDetailTableColumn.value = [
		{ label: "专业编码", prop: "code", class: "tree-cell-flex", align: "left" },
		{ label: "专业名称", prop: "name" }
	]
	DepartmentApi.getProfessionForDepartment(data.id)
		.then((res: any) => {
			drawerDetailTableData.value = res
		})
		.finally(() => {
			rawerDetailLoading.value = false
		})
	showDetailDrawer.value = true
}
// 关联位置查询
const getAssociatedPositionTableData = (data: any) => {
	showDetailDrawer.value = true
	drawerDetailTitle.value = {
		name: ["关联位置"],
		icon: ["fas", "square-share-nodes"]
	}
	drawerDetailTableColumn.value = [
		{
			label: "位置编码",
			prop: "allCode",
			align: "left",
			class: "tree-cell-flex"
		},
		{ label: "位置名称", prop: "allName" }
	]
	DepartmentApi.getPositionDetail(data.id).then((res: any) => {
		res.forEach((res: any) => {
			res.children = []
		})
		drawerDetailTableData.value = [res]
	})
}
// 分页
const onCurrentPageChange = (pageData: any) => {
	pageSize.value = pageData.pageSize
	currentPage.value = pageData.currentPage
	getDepartmentTableData()
}

const onCloseAddDrawer = (flag: string) => {
	if (flag === "success") {
		getDepartmentTreeData(flag)
		ElMessage.success("操作成功")
	}
	showDrawer.value = false
}

//查询

const queryArrList = ref<any[]>([
	{
		name: "部门名称",
		key: "orgName",
		type: "input",
		enableFuzzy: false,
		placeholder: "请输入查询关键字"
	},
	{
		name: "部门编码",
		key: "orgCode",
		type: "input",
		enableFuzzy: false,
		placeholder: "请输入查询关键字"
	}
])
const queryData = ref<any>({})
const getQueryData = (queryParams?: any) => {
	currentPage.value = 1
	refTable.value.resetCurrentPage()
	queryData.value.orgName = queryParams.orgName ? queryParams.orgName : ""
	queryData.value.orgCode = queryParams.orgCode ? queryParams.orgCode : ""
	getDepartmentTableData()
}

//切换显示已停用部门
const showChildren = ref(false)
//是否显示已停用部门
const setShowChildren = () => {
	currentPage.value = 1
	getDepartmentTreeData("success")
}

//停用

const paginationBtnList = ref([
	{
		name: "启用",
		icon: ["fas", "power-off"],
		roles: "system:device:auth:btn:enable",
		disabled: true
	},
	{
		name: "停用",
		roles: "system:device:auth:btn:disable",
		icon: ["fas", "circle-stop"],
		disabled: true
	}
])
const selectionTableList = ref<any[]>([])
const getSelectionTableList = (rowList: any) => {
	tableEditDetail.value = {}
	selectionTableList.value = rowList
}
const startEndLoading = ref<boolean>(false)
const onPaginationBtnClick = (btnName: string | undefined) => {
	if (!setTreeClick()) return
	const sledTlId: anyKey[] = []
	if (selectionTableList.value && selectionTableList.value.length) {
		selectionTableList.value.forEach((item: any) => {
			sledTlId.push(item.id)
		})
	}
	let authorizationStatus = "0"
	if (btnName === "启用") {
		if (!sledTlId.length) {
			ElMessage({
				message: "请选择需要启用的部门信息！",
				type: "warning"
			})
			return
		}
		authorizationStatus = "1"
		batchStatusUpdate(sledTlId, authorizationStatus)
	}
	if (btnName === "停用") {
		if (!sledTlId.length) {
			ElMessage({
				message: "请选择需要停用的部门信息！",
				type: "warning"
			})
			return
		}
		authorizationStatus = "0"
		batchStatusUpdate(sledTlId, authorizationStatus)
	}
}
const batchStatusUpdate = (sledTlId: anyKey[], authorizationStatus: string) => {
	// 状态校验
	if (!checkState(sledTlId, authorizationStatus)) return false
	if (authorizationStatus == "0") {
		CustomMessageBox(
			{
				message:
					"您即将停用此部门。请注意，将停用所有下属班组及子部门。您确定要继续吗？"
			},
			(res: boolean) => {
				if (res) {
					getStatusChange(sledTlId, authorizationStatus)
				}
			}
		)
	} else {
		getStatusChange(sledTlId, authorizationStatus)
	}
}

const getStatusChange = (sledTlId: anyKey[], authorizationStatus: string) => {
	if (startEndLoading.value) return
	startEndLoading.value = true
	DepartmentApi.getStatusChange({
		orgIds: sledTlId.join(","),
		status: authorizationStatus
	})
		.then(() => {
			getDepartmentTableData()
			ElMessage.success(`${authorizationStatus === "1" ? "启用" : "停用"}成功`)
			refTable.value?.clearSelectedTableData()
			getDepartmentTreeData("success")
		})
		.finally(() => {
			startEndLoading.value = false
		})
}

function checkState(
	selected: anyKey[] | Ref<anyKey[]>,
	state: string | number
) {
	selected = unref(selected)
	state = +state

	if (selected.length === 1) {
		if (state == 1) {
			if (selectionTableList.value[0].orgStatus == "1") {
				ElMessage.error("该部门已启用")
				return false
			}
		}
		if (state == 0) {
			if (selectionTableList.value[0].orgStatus == "0") {
				ElMessage.error("该部门已停用")
				return false
			}
		}
	}

	if (selected.length > 1) {
		if (state == 1) {
			if (selectionTableList.value.some((item) => item.orgStatus == "1")) {
				ElMessage.error("存在已启用部门")
				return false
			}
		}
		if (state == 0) {
			if (selectionTableList.value.some((item) => item.orgStatus == "0")) {
				ElMessage.error("存在已停用部门")
				return false
			}
		}
	}

	return true
}
watchEffect(() => {
	if (selectionTableList.value.length) {
		// 启用
		if (
			selectionTableList.value.some((item) => item.authorizationStatus === "1")
		) {
			paginationBtnList.value[0].disabled = true
		} else {
			paginationBtnList.value[0].disabled = false
		}
		// 停用
		if (
			selectionTableList.value.some((item) => item.authorizationStatus === "2")
		) {
			paginationBtnList.value[1].disabled = true
		} else {
			paginationBtnList.value[1].disabled = false
		}
	} else {
		paginationBtnList.value[0].disabled = true
		paginationBtnList.value[1].disabled = true
	}
})

watch(
	() => showDetailDrawer.value,
	(flag: boolean) => {
		if (!flag) {
			drawerDetailTableData.value = []
		}
	},
	{ immediate: true }
)

onMounted(() => {
	getDepartmentTreeData()
})

defineOptions({
	name: "DepartmentManagement"
})
</script>
<template>
	<div class="app-container-row">
		<ModelFrame class="left-model-frame">
			<Title :title="containerLeftTitle">
				<div>
					<el-switch
						class="mr5"
						v-model="showChildren"
						size="small"
						@change="setShowChildren"
					/>
					<span class="f12 c-3">显示已停用部门</span>
				</div>
			</Title>
			<div class="left-model-group">
				<PitayaTree
					ref="DepartTreeRef"
					nodeKey="idCopy"
					:treeData="departmentTreeData"
					:treeProps="departmentTreeProp"
					:needCheckBox="false"
					v-model:treeBizId="treeBizId"
					:defaultExpandedKeys="defaultExpandedKeys"
					@onTreeClick="departmentTreeClick"
					:tree-loading="treeLoading"
				/>
			</div>
		</ModelFrame>
		<div class="right-model-frame">
			<ModelFrame>
				<Query
					class="ml10"
					:queryArrList="queryArrList"
					@getQueryData="getQueryData"
					:numInRow="3"
					:queryBtnColSpan="6"
				/>
			</ModelFrame>
			<ModelFrame class="content">
				<Title
					:title="containerRightTitle"
					:button="departmentContainerBtn"
					@onBtnClick="onBtnClick"
				/>
				<el-scrollbar>
					<PitayaTable
						ref="refTable"
						:columns="departmentTableProp"
						:table-data="departmentTableData"
						:need-index="true"
						:needSelection="true"
						:need-pagination="true"
						:total="pageTotal"
						@onSelectionChange="getSelectionTableList"
						@on-current-page-change="onCurrentPageChange"
						:table-loading="tableLoading"
						:max-height="700"
					>
						<template #lines="{ rowData }">
							<div class="line-name-container">
								<el-tag
									class="line-item"
									v-for="(item, index) in rowData.lines"
									:key="index"
									:style="{
										backgroundColor: item.colour,
										color: '#FFF',
										borderColor: item.colour
									}"
									>{{ item.name }}</el-tag
								>
								<div v-if="rowData.lines.length === 0">---</div>
							</div>
						</template>
						<template #orgStatus="{ rowData }">
							<el-tag
								:class="'authState' + rowData.orgStatus"
								v-if="rowData.orgStatus !== null"
							>
								{{ rowData.orgStatus == "0" ? "已停用" : "已启用" }}
							</el-tag>
							<div v-else>---</div>
						</template>
						<template #majorNum="{ rowData }">
							<div
								class="border-bottom-text"
								@click="getRelatedMajorTableData(rowData)"
							>
								{{ rowData.majorNum }}
							</div>
						</template>
						<template #locationNum="{ rowData }">
							<div
								class="border-bottom-text"
								@click="getAssociatedPositionTableData(rowData)"
							>
								{{ rowData.locationNum }}
							</div>
						</template>

						<template #operations="{ rowData }">
							<el-button
								v-btn
								link
								@click="onRowEdit(rowData)"
								:disabled="checkPermission('system:department:btn:edit')"
								v-if="isCheckPermission('system:department:btn:edit')"
							>
								<font-awesome-icon
									:icon="['fas', 'pen-to-square']"
									:class="
										checkPermission('system:department:btn:edit')
											? 'disabled'
											: ''
									"
									style="color: var(--pitaya-btn-background)"
								/>
								<span
									class="table-inner-btn"
									:class="
										checkPermission('system:department:btn:edit')
											? 'disabled'
											: ''
									"
									>编辑</span
								>
							</el-button>
							<el-button
								v-btn
								color="var(--pitaya-btn-background)"
								link
								@click="onRowDelete(rowData)"
								:disabled="checkPermission('system:department:btn:delete')"
								v-if="isCheckPermission('system:department:btn:delete')"
							>
								<font-awesome-icon
									:icon="['fas', 'trash-can']"
									:class="
										checkPermission('system:department:btn:delete')
											? 'disabled'
											: ''
									"
									style="color: var(--pitaya-btn-background)"
								/>
								<span
									class="table-inner-btn"
									:class="
										checkPermission('system:department:btn:delete')
											? 'disabled'
											: ''
									"
									>移除</span
								>
							</el-button>
							<div
								v-if="
									!isCheckPermission('system:department:btn:edit') &&
									!isCheckPermission('system:department:btn:delete')
								"
							>
								---
							</div>
						</template>
						<template #footerOperateLeft>
							<ButtonList
								:button="paginationBtnList"
								:isNotRadius="true"
								:loading="startEndLoading"
								@onBtnClick="onPaginationBtnClick"
							/>
						</template>
					</PitayaTable>
				</el-scrollbar>
				<Drawer :size="drawerSize" v-model:drawer="showDrawer">
					<DepartmentDrawer
						v-if="showDrawer"
						:fomr-data="tableEditDetail"
						:current-node="currentClickTreeNode"
						@on-close-drawer="onCloseAddDrawer"
					/>
				</Drawer>
				<Drawer :size="drawerSizeDetail" v-model:drawer="showDetailDrawer">
					<Title :title="drawerDetailTitle" />
					<PitayaTable
						:columns="drawerDetailTableColumn"
						:tableData="drawerDetailTableData"
						:need-index="true"
						:table-loading="rawerDetailLoading"
					/>
				</Drawer>
			</ModelFrame>
		</div>
	</div>
</template>
<style lang="scss" scoped>
.app-container-row {
	align-items: center;
	justify-content: space-between;
	flex-direction: row;
	.left-model-frame {
		width: 20%;
		height: 100%;
		display: flex;
		flex-direction: column;
		.left-model-group {
			height: 0;
			flex: 1;
			overflow: hidden;
		}
	}
	.right-model-frame {
		width: calc(100% - 20% - 10px);
		display: flex;
		flex-direction: column;
		height: 100%;
		.line-name-container {
			display: flex;
			align-items: center;
			overflow: hidden;

			.line-item {
				margin-right: 10px;
				height: 20px;
				border-radius: 3px;
				font-weight: 600;
				font-size: var(--pitaya-fs-12);
				color: #fff;
				display: flex;
				align-items: center;
				justify-content: center;
				&:last-child {
					margin-right: 0;
				}
			}
		}
		.border-bottom-text {
			position: relative;
			color: #204a9c;
			text-decoration: underline;
			cursor: pointer;
		}
	}
}
.authState0 {
	color: #e25e59;
	border-color: #e25e59;
	background-color: #f9e7e7;
}
.authState1 {
	color: #4bae89;
	border-color: #4bae89;
	background-color: #e0ffef;
}
.authStatenull {
	color: #4bae89;
	border-color: #4bae89;
	background-color: #e0ffef;
}
</style>
