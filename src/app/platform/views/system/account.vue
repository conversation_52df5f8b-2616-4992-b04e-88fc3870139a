<script lang="ts" setup>
import { ElMessage } from "element-plus"
import { baseUserApi } from "@/app/platform/api/system/baseUser"
import { DepartmentApi } from "@/app/platform/api/system/department"
import AccountDrawer from "./components/accountDrawer.vue"
import { usePagination } from "@/app/platform/hooks/usePagination"
import {
	checkPermission,
	isCheckPermission
} from "@/app/platform/utils/permission"
import { CustomMessageBox } from "@/app/platform/utils/message"

const usePaginationStore = usePagination()
const containerLeftTitle = {
	name: ["账号管理"],
	icon: ["fas", "square-share-nodes"]
}

const containerRightTitle = ref<any>({
	name: ["账号管理"],
	icon: ["fas", "square-share-nodes"]
})

const departmentContainerBtn = [
	{
		name: "新增账号",
		roles: "system:baseuser:btn:add",
		icon: ["fas", "square-plus"]
	}
]

const drawerDetailTitle = ref<any>({
	name: ["分配角色"],
	icon: ["fas", "square-share-nodes"]
})

const departmentTreeProp = {
	children: "children",
	label: (data: any) => {
		if (data.companyStatus !== null) {
			return `${data.name}（${data.userNUm}）${
				data.companyStatus == 0 || data.orgStatus == 0
					? `<span style='color: #E25E59'>(停用)</span>`
					: ""
			}`
		} else {
			return `${data.name}（${data.userNUm}）`
		}
	}
}
const startEndLoading = ref<boolean>(false)
const paginationBtnList = ref([
	{
		name: "启用",
		roles: "system:baseuser:btn:enable",
		icon: ["fas", "power-off"],
		disabled: true
	},
	{
		name: "停用",
		roles: "system:baseuser:btn:disable",
		icon: ["fas", "circle-stop"],
		disabled: true
	},
	{
		name: "重置密码",
		roles: "system:baseuser:btn:resetting",
		icon: ["fas", "unlock-keyhole"],
		disabled: true
	}
])
const departmentTableProp: TableColumnType[] = [
	{ label: "用户姓名", prop: "realname", width: 80 },
	{ label: "用户账号", prop: "username", width: 120 },
	{ label: "手机号码", prop: "phone", width: 120 },
	{ label: "员工工号", prop: "employeeNumber", width: 120 },
	{ label: "性别", prop: "sex", width: 80, needSlot: true },
	{ label: "企业微信", prop: "enterpriseWeChat", width: 85 },
	{ label: "公司", prop: "companyName", minWidth: 120 },
	{ label: "部门", prop: "orgAllName", minWidth: 200,align:'left' },
	{ label: "岗位", prop: "station", minWidth: 120 },
	{ label: "角色信息", prop: "baseRoleNum", width: 95, needSlot: true },
	{ label: "委外人员", prop: "outsourcedPersonnel", width: 95, needSlot: true },
	{ label: "工作班组", prop: "organizationNum", width: 95, needSlot: true },
	{ label: "状态", prop: "enabled", width: 85, needSlot: true,fixed: "right" },
	{
		label: "操作",
		prop: "operations",
		width: 150,
		needSlot: true,
		fixed: "right"
	}
]

const drawerSize = 930
const drawerSizeDetail = 620

const departmentTreeData = ref<any[]>([])
const departmentTableData = ref<any[]>([])
const pageTotal = ref(0)
const showDrawer = ref(false)
const tableLoading = ref(false)
const treeLoading = ref(false)
const curRowData = ref<anyKey>({})
const showDetailDrawer = ref(false) // 关联专业、关联位置弹窗

const drawerDetailTableColumn = ref<TableColumnType[]>([
	{ label: "角色编码", prop: "professionCode" },
	{ label: "角色名称", prop: "professionName" },
	{ label: "操作", prop: "operations" }
])
const drawerDetailTableData = ref<any[]>([])
const drawerDetailTableLoading = ref(false)
const paginationData = ref<PaginationData>(usePaginationStore.paginationData)
const treeBizId = ref("")
const baseAccountTree = ref()

// #region 树相关
// 展开节点id（默认第一个）
const defaultExpandedId = ref([0])
// 当前节点
const curNodeData = ref<any>({})
const DepartTreeRef = ref<any>(null)
/**
 * 获取部门树
 */
function totalChildren(data: any, key: string): any {
	for (let i = 0; i < data.length; i++) {
		if (data[i].children) {
			totalChildren(data[i].children, key)
			data[i].userNUm = data[i].userNUm
		}
	}
	return data
}
const ensureUniqueIds = (
	tree: { id: number; children: any }[],
	idMap = {},
	nextId = 1
) => {
	tree.forEach((node: { id: number; children: any }) => {
		// 如果节点的id已经存在于idMap中，则为其分配一个新的唯一id
		node.idCopy = node.id + "-" + nextId++
		// 如果节点有子节点，递归地调用此函数
		if (node.children) {
			ensureUniqueIds(node.children, idMap, nextId)
		}
	})
}
const getDepartmentTreeData = () => {
	treeLoading.value = true
	DepartmentApi.getDepartmentTreeWithRole({
		withChildren: showChildren.value,
		companyStatus: showChildren.value ? 1 : 0
	})
		.then((res: any) => {
			if (res && res.length > 0) {
				if (showChildren.value) {
					departmentTreeData.value = upwardAccumulationByOneKey(res, "userNUm")
				} else {
					departmentTreeData.value = totalChildren(res, "userNUm")
				}
				departmentTreeData.value.forEach((item: any) => {
					item.company = true
				})
				ensureUniqueIds(departmentTreeData.value)

				if (!curNodeData.value || !curNodeData.value.idCopy) {
					curNodeData.value = res[0]
					treeBizId.value = res[0].id
					defaultExpandedId.value = [res[0].idCopy]
				} else {
					defaultExpandedId.value = [curNodeData.value.idCopy]
				}
				nextTick(() => {
					DepartTreeRef.value.setCurrentKey(curNodeData.value.idCopy)
				})
				if(curNodeData.value.companyId) getDepartmentTableData()
				// 更新面包屑
				if (!treeBizId.value) {
					const titleObj = JSON.parse(JSON.stringify(containerLeftTitle))
					titleObj.name.push(res[0].name)
					containerRightTitle.value = titleObj
				}
			}
		})
		.catch((err) => {
			throw new Error("getDepartmentTreeWithRole():::" + err)
		})
		.finally(() => {
			treeLoading.value = false
		})
}
/**
 * 树点击
 */
const departmentTreeClick = (data: any, data1: any) => {
	paginationData.value.currentPage = 1
	curNodeData.value = data
	// 更新面包屑
	containerRightTitle.value.name = JSON.parse(
		JSON.stringify(containerLeftTitle)
	).name.concat(getTreeTitle(data1, "name"))
	getDepartmentTableData()
}
// #endregion

/**
 * 根据部门ID获取账号列表
 */
const getDepartmentTableData = () => {
	tableLoading.value = true
	baseUserApi
		.getBaseUserListApi({
			...queryData.value,
			...paginationData.value,
			companyId: curNodeData.value.companyId,
			orgId: curNodeData.value.company ? "" : curNodeData.value.id,
			showChildren: showChildren.value ? 1 : 0
		})
		.then((res: anyKey) => {
			if (res.rows && res.rows.length > 0) {
				const { rows, records } = res
				departmentTableData.value = rows
				pageTotal.value = records
				baseAccountTree.value.clearSelectedTableData()
			} else {
				departmentTableData.value = []
				pageTotal.value = 0
			}
		})
		.catch((err: any) => {
			throw new Error("getBaseUserListApi():::" + err)
		})
		.finally(() => {
			tableLoading.value = false
		})
}
//停用
const setTreeClick = () => {
	if (
		curNodeData.value.companyStatus == 0 ||
		curNodeData.value.orgStatus == 0
	) {
		ElMessage.warning("已停用不可操作")
		return false
	} else {
		return true
	}
}

const onBtnClick = () => {
	if (!setTreeClick()) return
	curRowData.value = {}
	showDrawer.value = true
}

const onRowEdit = (row: any) => {
	curRowData.value = JSON.parse(JSON.stringify(row))
	if (!setTreeClick()) return
	showDrawer.value = true
}

const onRowDelete = (row: any) => {
	curRowData.value = JSON.parse(JSON.stringify(row))
	if (!setTreeClick()) return
	CustomMessageBox({ message: "确定要移除吗？" }, (res: boolean) => {
		if (res) {
			baseUserApi
				.removeBaseUserApi({ id: row.id })
				.then((res) => {
					if (res) {
						ElMessage.success("移除成功")
						getDepartmentTreeData()
					}
				})
				.catch((err) => {
					throw new Error("removeBaseUserApi():::" + err)
				})
		} else {
			return false
		}
	})
}

function onClose() {
	showDrawer.value = false
	getDepartmentTreeData()
}

/**
 * 关联角色信息查询
 */
const getRoleInfoTableData = (_data: any) => {
	showDetailDrawer.value = true
	drawerDetailTableLoading.value = true
	drawerDetailTitle.value = {
		name: ["关联角色"],
		icon: ["fas", "square-share-nodes"]
	}
	drawerDetailTableColumn.value = [
		{ label: "角色编码", prop: "roleCode" },
		{ label: "角色名称", prop: "roleName" }
	]
	baseUserApi
		.getRoleByUserIdApi({ baseUserId: _data.id })
		.then((res: any) => {
			if (res && res.length > 0) {
				drawerDetailTableData.value = res
			} else {
				drawerDetailTableData.value = []
			}
		})
		.catch((err) => {
			throw new Error("getRoleByUserIdApi():::" + err)
		})
		.finally(() => {
			drawerDetailTableLoading.value = false
		})
}
/**
 * 关联班组查询
 */
const getWorkGroupTableData = (_data: any) => {
	showDetailDrawer.value = true
	drawerDetailTableLoading.value = true
	drawerDetailTitle.value = {
		name: ["关联班组"],
		icon: ["fas", "square-share-nodes"]
	}

	drawerDetailTableColumn.value = [
		{ label: "班组编码", prop: "orgCode" },
		{ label: "班组名称", prop: "orgAllName" }
	]

	baseUserApi
		.getTeamByUserIdApi({ baseUserId: _data.id })
		.then((res: any) => {
			if (res && res.length > 0) {
				drawerDetailTableData.value = res
			} else {
				drawerDetailTableData.value = []
			}
		})
		.catch((err) => {
			throw new Error("getTeamByUserIdApi():::" + err)
		})
		.finally(() => {
			drawerDetailTableLoading.value = false
		})
}

const selectionTableList = ref<anyKey[]>([])
const selectedOrNot = computed(() =>
	selectionTableList.value.length > 0 ? true : false
)
function getSelectionTableList(rowList: anyKey[]) {
	selectionTableList.value = rowList
}
// 启用、停用、重置密码
const paginationBtnClick = (btnName: string | undefined) => {
	if (!setTreeClick()) return
	switch (btnName) {
		case "启用":
			handlerEnabledOrNot(1)
			break
		case "停用":
			handlerEnabledOrNot(0)
			break
		case "重置密码":
			handlerResetPassword()
			break
	}
}

// 校验选中状态
function checkState(
	selected: anyKey[] | Ref<anyKey[]>,
	state: string | number
) {
	selected = unref(selected)
	state = +state

	if (selected.length === 1) {
		if (state === 1) {
			if (selected[0].enabled) {
				ElMessage.error("该账号已启用")
				return false
			}
		}
		if (state === 0) {
			if (!selected[0].enabled) {
				ElMessage.error("该账号已停用")
				return false
			}
		}
	}

	if (selected.length > 1) {
		if (state === 1) {
			if (selected.some((item) => item.enabled)) {
				ElMessage.error("存在已启用账号")
				return false
			}
		}
		if (state === 0) {
			if (selected.some((item) => !item.enabled)) {
				ElMessage.error("存在已停用账号")
				return false
			}
		}
	}

	return true
}

function handlerEnabledOrNot(enabled: number | string) {
	// 校验选中状态
	if (!selectedOrNot.value) return ElMessage.warning("请选择账号")

	if (!checkState(selectionTableList, enabled)) return false

	const ids = selectionTableList.value.map((item) => item.id)
	if (startEndLoading.value) return
	startEndLoading.value = true
	baseUserApi
		.enabledOrNotApi({ ids, enabled })
		.then((res) => {
			if (res) {
				ElMessage.success(`${enabled === 1 ? "启用" : "停用"}成功`)
				getDepartmentTableData()
				baseAccountTree.value.clearSelectedTableData()
			}
		})
		.catch((err) => {
			throw new Error("enabledOrNotApi():::" + err)
		})
		.finally(() => {
			startEndLoading.value = false
		})
}

function handlerResetPassword() {
	if (startEndLoading.value) return
	if (!selectedOrNot.value) return ElMessage.warning("请选择账号")
	const ids = selectionTableList.value.map((item) => item.id)
	startEndLoading.value = true
	baseUserApi
		.resetPasswordApi({ ids })
		.then((res: any) => {
			if (res) {
				const password = res[0].password
				CustomMessageBox({
					message: `密码为：${password}`,
					showCancelButton: false
				})
				getDepartmentTableData()
				baseAccountTree.value.clearSelectedTableData()
			}
		})
		.catch((err) => {
			throw new Error("resetPasswordApi():::" + err)
		})
		.finally(() => {
			startEndLoading.value = false
		})
}

function getCurrentPageNation(pd: PaginationData) {
	paginationData.value = pd
	getDepartmentTableData()
}

//查询
const queryArrList = ref<any[]>([
	{
		name: "用户姓名",
		key: "realname",
		type: "input",
		enableFuzzy: false,
		placeholder: "请输入查询关键字"
	},
	{
		name: "用户账号",
		key: "username",
		type: "input",
		enableFuzzy: false,
		placeholder: "请输入查询关键字"
	},
	{
		name: "手机号码",
		key: "phone",
		type: "input",
		enableFuzzy: false,
		placeholder: "请输入查询关键字"
	},
	{
		name: "企业微信",
		key: "enterpriseWeChat",
		type: "input",
		enableFuzzy: false,

		placeholder: "请输入查询关键字"
	},
	{
		name: "员工工号",
		key: "employeeNumber",
		type: "input",
		enableFuzzy: false,
		placeholder: "请输入查询关键字"
	},
	{
		name: "状态",
		key: "enabled",
		type: "select",
		enableFuzzy: false,
		placeholder: "请选择",
		children: [
			{
				label: "已停用",
				value: "false"
			},
			{
				label: "已启用",
				value: "true"
			}
		]
	},
	{
		name: "委外人员",
		key: "outsourcedPersonnel",
		type: "select",
		enableFuzzy: false,
		placeholder: "请选择",
		children: [
			{
				label: "否",
				value: "0"
			},
			{
				label: "是",
				value: "1"
			}
		]
	}
])
const queryData = ref<any>({})
const getQueryData = (queryParams?: any) => {
	queryData.value.phone = queryParams?.phone
	queryData.value.realname = queryParams?.realname
	queryData.value.username = queryParams?.username
	queryData.value.enterpriseWeChat = queryParams?.enterpriseWeChat
	queryData.value.employeeNumber = queryParams?.employeeNumber
	queryData.value.enabled = queryParams?.enabled
	queryData.value.outsourcedPersonnel = queryParams?.outsourcedPersonnel
	paginationData.value.currentPage = 1
	getDepartmentTableData()
}

onMounted(() => {
	departmentTableData.value = []
	getDepartmentTreeData()
})

watchEffect(() => {
	if (selectionTableList.value.length) {
		// 重置密码
		if (selectionTableList.value.length === 1) {
			paginationBtnList.value[2].disabled = false
		} else {
			paginationBtnList.value[2].disabled = true
		}
		// 启用
		if (selectionTableList.value.some((item) => item.enabled === true)) {
			paginationBtnList.value[0].disabled = true
		} else {
			paginationBtnList.value[0].disabled = false
		}
		// 停用
		if (selectionTableList.value.some((item) => item.enabled === false)) {
			paginationBtnList.value[1].disabled = true
		} else {
			paginationBtnList.value[1].disabled = false
		}
	} else {
		paginationBtnList.value[0].disabled = true
		paginationBtnList.value[1].disabled = true
		paginationBtnList.value[2].disabled = true
	}
})

//切换展示分级
const showChildren = ref(true)
//是否展示子专业的维修建议
const setShowChildren = () => {
	paginationData.value.currentPage = 1
	getDepartmentTreeData()
}

defineOptions({
	name: "AccountManagement"
})
</script>
<template>
	<div class="app-container-row">
		<ModelFrame class="left-model-frame">
			<Title :title="containerLeftTitle">
				<div>
					<el-switch
						class="mr5"
						v-model="showChildren"
						size="small"
						@change="setShowChildren"
					/>
					<span class="f12 c-3">包含全部账号</span>
				</div>
			</Title>
			<div class="left-model-group">
				<PitayaTree
					ref="DepartTreeRef"
					nodeKey="idCopy"
					:default-expanded-keys="defaultExpandedId"
					:treeData="departmentTreeData"
					:treeProps="departmentTreeProp"
					:needCheckBox="false"
					v-model:treeBizId="treeBizId"
					@onTreeClick="departmentTreeClick"
					:tree-loading="treeLoading"
				/>
			</div>
		</ModelFrame>
		<div class="right-model-frame">
			<ModelFrame>
				<Query
					class="ml10"
					:queryArrList="queryArrList"
					@getQueryData="getQueryData"
					:numInRow="3"
					:queryBtnColSpan="6"
				/>
			</ModelFrame>
			<ModelFrame class="content">
				<Title
					:title="containerRightTitle"
					:button="departmentContainerBtn"
					@onBtnClick="onBtnClick"
				/>
				<PitayaTable
					ref="baseAccountTree"
					:columns="departmentTableProp"
					:table-data="departmentTableData"
					:needSelection="true"
					:total="pageTotal"
					@onSelectionChange="getSelectionTableList"
					@onCurrentPageChange="getCurrentPageNation"
					:table-loading="tableLoading"
				>
					<template #sex="{ rowData }">
						<div>{{ rowData.sex == 1 ? "男" : "女" }}</div>
					</template>
					<template #baseRoleNum="{ rowData }">
						<div
							class="border-bottom-text"
							@click="getRoleInfoTableData(rowData)"
						>
							{{ rowData.baseRoleNum }}
						</div>
					</template>
					<template #outsourcedPersonnel="{ rowData }">
						<div>
							{{ rowData.outsourcedPersonnel ? "是" : "否" }}
						</div>
					</template>
					<template #organizationNum="{ rowData }">
						<div
							class="border-bottom-text"
							@click="getWorkGroupTableData(rowData)"
						>
							{{ rowData.organizationNum }}
						</div>
					</template>
					<template #enabled="{ rowData }">
						<el-tag
							class="state-txt"
							:class="[rowData.enabled ? 'is-enable' : 'no-enable']"
						>
							{{ rowData.enabled ? "已启用" : "已停用" }}
						</el-tag>
					</template>
					<template #operations="{ rowData }">
						<el-button
							v-btn
							link
							@click="onRowEdit(rowData)"
							:disabled="checkPermission('system:baseuser:btn:edit')"
							v-if="isCheckPermission('system:baseuser:btn:edit')"
						>
							<font-awesome-icon
								:icon="['fas', 'pen-to-square']"
								style="color: var(--pitaya-btn-background)"
								:class="
									checkPermission('system:baseuser:btn:edit') ? 'disabled' : ''
								"
							/>
							<span
								class="table-inner-btn"
								:class="
									checkPermission('system:baseuser:btn:edit') ? 'disabled' : ''
								"
								>编辑</span
							>
						</el-button>
						<el-button
							v-btn
							color="var(--pitaya-btn-background)"
							link
							@click="onRowDelete(rowData)"
							:disabled="checkPermission('system:baseuser:btn:delete')"
							v-if="isCheckPermission('system:baseuser:btn:delete')"
						>
							<font-awesome-icon
								:icon="['fas', 'trash-can']"
								style="color: var(--pitaya-btn-background)"
								:class="
									checkPermission('system:baseuser:btn:delete')
										? 'disabled'
										: ''
								"
							/>
							<span
								class="table-inner-btn"
								:class="
									checkPermission('system:baseuser:btn:delete')
										? 'disabled'
										: ''
								"
								>移除</span
							>
						</el-button>
						<div
							v-if="
								!isCheckPermission('system:baseuser:btn:edit') &&
								!isCheckPermission('system:baseuser:btn:delete')
							"
						>
							---
						</div>
					</template>
					<template #footerOperateLeft>
						<ButtonList
							v-if="departmentTableData.length > 0"
							:isNotRadius="true"
							:button="paginationBtnList"
							:loading="startEndLoading"
							@on-btn-click="paginationBtnClick"
						/>
					</template>
				</PitayaTable>
				<Drawer
					:size="drawerSize"
					v-model:drawer="showDrawer"
					:destroy-on-close="true"
					@close="onClose"
				>
					<AccountDrawer
						:rowData="curRowData"
						:currentNodeData="curNodeData"
						@onClose="onClose"
					/>
				</Drawer>
				<Drawer :size="drawerSizeDetail" v-model:drawer="showDetailDrawer">
					<Title :title="drawerDetailTitle" />
					<PitayaTable
						:columns="drawerDetailTableColumn"
						:tableData="drawerDetailTableData"
						:table-loading="drawerDetailTableLoading"
						:need-index="true"
					/>
				</Drawer>
			</ModelFrame>
		</div>
	</div>
</template>
<style lang="scss" scoped>
.app-container-row {
	align-items: center;
	justify-content: space-between;
	flex-direction: row;
	.left-model-frame {
		width: 20%;
		height: 100%;
		display: flex;
		flex-direction: column;
		.left-model-group {
			height: 0;
			flex: 1;
			overflow: hidden;
		}
	}
	.right-model-frame {
		width: calc(100% - 20% - 10px);
		height: 100%;
		.border-bottom-text {
			position: relative;
			color: #204a9c;
			text-decoration: underline;
			cursor: pointer;
		}
		.state-txt {
			width: 50px;
			&.is-enable {
				background-color: #e0ffef;
				color: #4bae89;
				border: 1px solid #4bae89;
			}
			&.no-enable {
				background-color: #fcf3e6;
				color: #f59b22;
				border: 1px solid #f59b22;
			}
		}
	}
}
</style>
@/app/platform/hooks/usePagination
@/app/platform/api/system/baseUser@/app/platform/api/system/department
