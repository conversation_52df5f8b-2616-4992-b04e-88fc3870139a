<script lang="ts" setup>
// 框架版本
const api = "/pitaya" //pitaya
import { genFileId, ElMessage, ElMessageBox } from "element-plus"
import type {
	FormInstance,
	FormRules,
	UploadFile,
	UploadInstance,
	UploadProps,
	UploadRawFile
} from "element-plus"
import { onMounted, reactive, ref } from "vue"
import { UploadFilled, QuestionFilled } from "@element-plus/icons-vue"
import { usePagination } from "@/app/platform/hooks/usePagination"
import {
	getSdkManagerList,
	changeSdkManager,
	deleteSdkManager,
	batchRelease,
	allowRollback
} from "@/app/platform/api/system/version"
import { getDictionaryTerm } from "@/app/platform/api/system/dictionary"
import { matchPermissionBtnList } from "@/app/platform/utils/permission"
import Cookies from "js-cookie"
import { CustomMessageBox } from "@/app/platform/utils/message"

const headers = reactive({
	Authorization: "Bearer " + Cookies.get("pitaya-FB-token-key")
})
const queryDataTemp = ref()
interface queryObj {
	/**
	 * 页码
	 */
	currentPage?: number
	/**
	 * 每页条数
	 */
	pageSize?: number
}

const tableLoading = ref(false)
const drawerLoading = ref(false)

const usePaginationStore = usePagination({})
const paginationData = ref<PaginationData>(usePaginationStore.paginationData)

const paramObj = ref<queryObj>({
	currentPage: paginationData.value.currentPage,
	pageSize: paginationData.value.pageSize
})

const getTableList = (pd: PaginationData) => {
	paramObj.value.currentPage = pd.currentPage
	paramObj.value.pageSize = pd.pageSize
	sdkManagerListInit()
}

const sdkManagerListInit = () => {
	tableLoading.value = true
	const params = {
		...paramObj.value,
		...queryDataTemp.value,
	}
	getSdkManagerList(params)
		.then((res: any) => {
			if (res.rows && res.rows.length) {
				baseTableData.value = res.rows
				dataTotal.value = res.records
				PitayaTableRef.value.clearSelectedTableData()
			} else {
				baseTableData.value = []
				dataTotal.value = 0
			}
		})
		.finally(() => {
			tableLoading.value = false
		})
}

onMounted(() => {
	sdkManagerListInit()
	getDictionaryList()
})

const dictionaryList = ref<any[]>([
	{
		label: "发布状态",
		code: "DEVICE_VERSION",
		children: [
			{
				label: "全部",
				value: ""
			}
		]
	}
])

const getDictionaryList = () => {
	dictionaryList.value.forEach((dItem: any) => {
		getDictionaryTerm({
			dataDictionaryCode: dItem.code
		}).then((dItemList: any) => {
			if (dItemList && dItemList.length) {
				const children = [
					{
						label: "全部",
						value: ""
					}
				]
				dItemList.forEach((item: any) => {
					children.push({
						label: item.subitemName,
						value: item.subitemValue
					})
				})
				dItem.children = children
			}
		})
	})
}

const getDicMatchTxt = (code: string, val: string) => {
	let MTxt = ""
	const Mdic = dictionaryList.value.filter((item: any) => {
		return item.code === code
	})
	if (Mdic && Mdic.length && Mdic[0].children && Mdic[0].children.length) {
		const MItem = Mdic[0].children.filter((citem: any) => {
			return citem.value === val
		})
		if (MItem && MItem.length) {
			MTxt = MItem[0].label
		}
	}
	return MTxt
}

const title = {
	name: ["版本更新"],
	icon: ["fas", "square-share-nodes"]
}

const drawerTitle = {
	name: ["版本信息"],
	icon: ["fas", "square-share-nodes"]
}

const button = [
	{
		name: "新增版本",
		icon: ["fas", "square-plus"]
	}
]

const drawerState = ref(false)
const drawerSize = 310

const dataTotal = ref(0)
const baseTableData = ref<any[]>([])
const baseColumns = ref<TableColumnType[]>([
	{ prop: "majorVersion", label: "主版本号", width: 100 },
	{ prop: "electromechanical", label: "机电", width: 100 },
	{ prop: "powerSupply", label: "供电", width: 100 },
	{ prop: "communicationNumber", label: "通号", width: 100 },
	{ prop: "line", label: "线路", width: 100 },
	{ prop: "updateContent", label: "更新内容", minWidth: 250 },
	{ prop: "releaseNum", label: "发布号", width: 120 },
	{ prop: "releaseStatus", label: "发布状态", needSlot: true, width: 120 },
	{ prop: "releaseTime", label: "发布时间", width: 170, sortable: "custom" },
	{
		prop: "operations",
		fixed: "right",
		label: "操作",
		needSlot: true,
		width: 150
	}
])

const selectionTableList = ref<any[]>([])

const getSelectionTableList = (rowList: any) => {
	selectionTableList.value = rowList
}

const baseFormData = reactive<anyKey>({
	majorVersion: "",
	electromechanical: "",
	powerSupply: "",
	communicationNumber: "",
	line: "",
	updateContent: "",
	sdkUrl: "",
	apkList: []
})

const formRules = reactive<FormRules<typeof baseFormData>>({
	majorVersion: [
		{
			required: true,
			trigger: ["input", "blur"],
			validator: (rule: any, value: string, callback: any) => {
				if (!value) {
					callback(new Error("请输入主版本号"))
				} else if (!/^([0-9]{1,3}\.){2}[0-9]{1,3}$|^([0-9]{1,3}\.){3}[0-9]{1,3}$/.test(value)) {
					callback(new Error("版本号格式有误"))
				} else {
					callback()
				}
			}
		}
	],
	updateContent: [
		{ required: true, message: "请输入更新内容", trigger: "blur" }
	],
	electromechanical: [
		{ required: true, message: "请输入机电版本号", trigger: "blur" }
	],
	powerSupply: [{ required: true, message: "请输入供电版本号", trigger: "blur" }],
	communicationNumber: [
		{ required: true, message: "请输入通号版本号", trigger: "blur" }
	],
	line: [{ required: true, message: "请输入线路版本号", trigger: "blur" }],
	apkList: [
		{ required: true, message: "请上传apk格式的附件", trigger: "change" }
	]
})

const onEdit = (rowData: anyKey) => {
	baseFormData.apkList = []
	Object.keys(baseFormData).forEach((key) => {
		if (key === "apkList") {
			if (rowData["sdkUrl"]) {
				const sdkUrlArr = JSON.parse(JSON.stringify(rowData["sdkUrl"])).split(
					"/"
				)
				baseFormData[key].push({
					name: sdkUrlArr[sdkUrlArr.length - 1]
				})
			} else {
				baseFormData[key] = []
			}
		} else {
			baseFormData[key] = rowData[key] ? rowData[key] : ""
		}
		baseFormData.id = rowData.id
	})
	baseFormRef.value?.clearValidate()
	drawerState.value = true
}

const onDelete = (rowData: anyKey) => {
	CustomMessageBox(
		{ message: "确定要移除该条版本数据信息吗?" },
		(res: boolean) => {
			if (res) {
				deleteSdkManager({
					id: rowData.id
				}).then(() => {
					sdkManagerListInit()
					ElMessage.success("操作成功")
				})
			}
		}
	)
}

const baseFormRef = ref<FormInstance>()
const buttonLoading = ref(false)
const onFormBtnClick = (btnName: string | undefined) => {
	if (!baseFormRef.value) return
	if (btnName === "保存") {
		baseFormRef.value.validate((valid) => {
			if (valid) {
				const paramObj = JSON.parse(JSON.stringify(baseFormData))
				delete paramObj.apkList
				if (!paramObj.sdkUrl) {
					ElMessage({
						message: "附件上传中，请稍后再试",
						type: "warning"
					})
					return false
				}
				if (paramObj.sdkUrl === "error") {
					baseFormData.apkList = []
					baseFormData.sdkUrl = ""
					ElMessage({
						message: "附件上传失败",
						type: "warning"
					})
					return false
				}
				drawerLoading.value = true
				if (buttonLoading.value) return
				buttonLoading.value = true
				changeSdkManager(paramObj)
					.then(() => {
						sdkManagerListInit()
						drawerState.value = false
						ElMessage.success("操作成功")
					})
					.finally(() => {
						drawerLoading.value = false
						buttonLoading.value = false
					})
			} else {
				return false
			}
		})
		return
	}

	if (btnName === "取消") {
		drawerState.value = false
		return
	}
}

const onClose = () => {
	Object.keys(baseFormData).map((key) => (baseFormData[key] = ""))
	baseFormData["apkList"] = []
	baseFormRef.value?.clearValidate()
}

const PitayaTableRef = ref()

const onPaginationBtnClick = (btnName: string | undefined) => {
	const sledTlId: string[] = []
	const sledTlSdkUrl: string[] = []
	let releaseStatus = ""
	if (selectionTableList.value && selectionTableList.value.length) {
		selectionTableList.value.forEach((item: any) => {
			sledTlId.push(item.id)
			sledTlSdkUrl.push(item.sdkUrl)
			releaseStatus = item.releaseStatus
		})
	}

	if (btnName === "发布") {
		if (!sledTlId.length) {
			ElMessage({
				message: "请选择需要发布的版本信息！",
				type: "warning"
			})
			return
		}
		if (releaseStatus && releaseStatus !== "0" && releaseStatus !== "3") {
			if (releaseStatus === "1") {
				ElMessage({
					message: "该版本信息已发布！",
					type: "warning"
				})
			}
			if (releaseStatus === "2") {
				ElMessage({
					message: "该版本信息已过期！",
					type: "warning"
				})
			}
			return
		}
		if (paginationLoading.value) return
		paginationLoading.value = true
		batchRelease({
			id: sledTlId[0]
		})
			.then(() => {
				sdkManagerListInit()
				ElMessage.success("操作成功")
				PitayaTableRef.value?.clearSelectedTableData()
			})
			.finally(() => {
				paginationLoading.value = false
			})
		return
	}

	if (btnName === "下载") {
		if (!sledTlSdkUrl.length) {
			ElMessage({
				message: "请选择需要下载的版本信息！",
				type: "warning"
			})
			return
		}
		sledTlSdkUrl.forEach((item: string) => {
			downloadFile(`${api}/system/sdkManager/download?fileName=${item}`)
		})
		return
	}

	if (btnName === "回滚") {
		if (!sledTlSdkUrl.length) {
			ElMessage({
				message: "请选择需要回滚的版本信息！",
				type: "warning"
			})
			return
		}
		if (releaseStatus && releaseStatus !== "1") {
			if (releaseStatus === "0") {
				ElMessage({
					message: "该版本信息未发布！",
					type: "warning"
				})
			}
			if (releaseStatus === "2") {
				ElMessage({
					message: "该版本信息已过期！",
					type: "warning"
				})
			}
			if (releaseStatus === "3") {
				ElMessage({
					message: "该版本信息已回滚！",
					type: "warning"
				})
			}
			return
		}

		allowRollback({
			id: sledTlId[0]
		}).then(() => {
			sdkManagerListInit()
			ElMessage.success("操作成功")
			PitayaTableRef.value?.clearSelectedTableData()
		})
	}
}

const downloadFile = async (url: string) => {
	const response = await fetch(url, {headers});
    if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
    }
    const blob = await response.blob();
    const fileUrl = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = fileUrl;
    link.download = decodeURIComponent(url.split("/").pop() || "");
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(fileUrl);
};


const onBtnClick = (e: string | undefined) => {
	Object.keys(baseFormData).map((key) => (baseFormData[key] = ""))
	baseFormData["apkList"] = []
	delete baseFormData.id
	baseFormRef.value?.clearValidate()
	drawerState.value = true
}

const btnList = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "保存",
		icon: ["fas", "floppy-disk"]
	}
]

const paginationLoading = ref<boolean>(false)
const paginationBtnList = ref([
	{
		name: "发布",
		icon: ["fas", "paper-plane"],
		disabled: true
	},
	{
		name: "下载",
		icon: ["fas", "cloud-arrow-down"],
		disabled: true
	}

	/**
	  {
		name: "回滚",
		icon: ["fas", "share"]
	}
	 */
])

const uploadApkRef = ref<UploadInstance>()

const handleExceed: UploadProps["onExceed"] = (files) => {
	uploadApkRef.value!.clearFiles()
	const file = files[0] as UploadRawFile
	file.uid = genFileId()
	uploadApkRef.value!.handleStart(file)
	uploadApkRef.value!.submit()
}
const filesStatus = ref("")
const handleChange: UploadProps["onChange"] = (files: UploadFile) => {
	filesStatus.value = files.status
	if (files.status === "success") {
		baseFormData["sdkUrl"] = (files.response as anyKey).data
	} else if (files.status === "fail") {
		baseFormData["sdkUrl"] = "error"
	} else {
		baseFormData["sdkUrl"] = ""
	}
	setTimeout(() => {
		baseFormRef.value?.validateField("apkList")
	}, 200)
}

const handleError: UploadProps["onError"] = () => {
	ElMessage({
		message: "附件上传失败，请重新上传",
		type: "error"
	})
}

const beforeUpload: UploadProps["beforeUpload"] = (file) => {
	const typeList = ["application/vnd.android.package-archive"]
	if (typeList.find((item) => item === file.type)) {
		return true
	}
	filesStatus.value = ""
	return false
}

const switchChange = (rowData: any) => {
	changeSdkManager({
		id: rowData.id,
		allowRollback: rowData.allowRollback
	}).then(() => {
		sdkManagerListInit()
	})
}

const awesomeIcon = ["fas", "paperclip"]
const awesomeDelIcon = ["fas", "xmark"]

const handleRemove = (file: UploadFile) => {
	baseFormData["sdkUrl"] = ""
	uploadApkRef.value!.clearFiles()
}

watchEffect(() => {
	if (selectionTableList.value.length === 1) {
		if (selectionTableList.value[0].releaseStatus === "0") {
			paginationBtnList.value[0].disabled = false
		} else {
			paginationBtnList.value[0].disabled = true
		}
		paginationBtnList.value[1].disabled = false
	} else {
		paginationBtnList.value[0].disabled = true
		paginationBtnList.value[1].disabled = true
	}
})
const sortData:any = ref({})
function onSortChange(column: any, prop: any, order: any) {
        if(order == null){
            order = ""
        } else if(order == "ascending"){
            order = "asc"
        } else if(order == "descending"){
            order = "desc"
        }
		console.log('===column.order',column.order)
        queryDataTemp.value = {
            ...queryDataTemp.value,
            sidx:column.order == null?'':prop,
            sord:order
        }
        sortData.value.sidx = queryDataTemp.value.sidx
        sortData.value.sord = queryDataTemp.value?.sord
        // queryParam.sidx = prop
        sdkManagerListInit()
}
defineOptions({
	name: "sdkManager"
})
</script>

<template>
	<div class="app-container">
		<div class="app-content-wrapper">
			<div class="app-content-group">
				<ModelFrame>
					<Title :title="title" :button="button" @onBtnClick="onBtnClick" />
					<div class="app-el-scrollbar-wrapper">
						<el-scrollbar>
							<PitayaTable
								ref="PitayaTableRef"
								@onSelectionChange="getSelectionTableList"
								@onCurrentPageChange="getTableList"
								:table-data="baseTableData"
								:columns="baseColumns"
								:needSelection="true"
								:singleSelect="true"
								:total="dataTotal"
								:table-loading="tableLoading"
								:max-height="700"
								@onTableSortChange="onSortChange"
							>
								<template #allowRollback="{ rowData }">
									<el-switch
										v-if="rowData.releaseStatus === '1'"
										v-model="rowData.allowRollback"
										style="
											--el-switch-on-color: #204a9c;
											--el-switch-off-color: #cccccc;
										"
										@change="switchChange(rowData)"
									/>
									<span v-else>---</span>
								</template>
								<template #releaseStatus="{ rowData }">
									<el-tag
										v-if="
											getDicMatchTxt('DEVICE_VERSION', rowData.releaseStatus)
										"
										:class="'state' + rowData.releaseStatus"
									>
										{{
											getDicMatchTxt("DEVICE_VERSION", rowData.releaseStatus)
										}}
									</el-tag>
									<span v-else>---</span>
								</template>
								<template #operations="{ rowData }">
									<template
										v-if="
											rowData.releaseStatus === '0' ||
											rowData.releaseStatus === '3'
										"
									>
										<el-button
											v-btn
											link
											@click="onEdit(rowData)"
											v-permission="matchPermissionBtnList('编辑')"
										>
											<font-awesome-icon
												:icon="['fas', 'pen-to-square']"
												style="color: var(--pitaya-btn-background)"
											/>
											<span class="table-inner-btn">编辑</span>
										</el-button>
										<el-button
											v-btn
											color="var(--pitaya-btn-background)"
											link
											@click="onDelete(rowData)"
											v-permission="matchPermissionBtnList('移除')"
										>
											<font-awesome-icon
												:icon="['fas', 'trash-can']"
												style="color: var(--pitaya-btn-background)"
											/>
											<span class="table-inner-btn">移除</span>
										</el-button>
									</template>
									<span v-else>---</span>
								</template>
								<template #footerOperateLeft>
									<ButtonList
										:button="paginationBtnList"
										:loading="paginationLoading"
										:isNotRadius="true"
										@onBtnClick="onPaginationBtnClick"
									/>
								</template>
							</PitayaTable>
						</el-scrollbar>
					</div>
				</ModelFrame>
			</div>
		</div>
		<Drawer :size="drawerSize" v-model:drawer="drawerState" @close="onClose">
			<div class="common-from-wrapper" v-loading="drawerLoading">
				<Title :title="drawerTitle" />
				<div class="common-from-group">
					<el-scrollbar>
						<el-form
							class="el-form-wrapper"
							ref="baseFormRef"
							:model="baseFormData"
							:rules="formRules"
							label-width="auto"
							label-position="top"
						>
							<el-form-item
								class="major-version-label-init"
								label="主版本号"
								prop="majorVersion"
							>
								<el-input
									maxlength="11"
									:show-word-limit="true"
									v-model.trim="baseFormData.majorVersion"
								/>
							</el-form-item>
							<el-form-item label="机电" prop="electromechanical">
								<el-input
									maxlength="20"
									:show-word-limit="true"
									v-model.trim="baseFormData.electromechanical"
								/>
							</el-form-item>
							<el-form-item label="供电" prop="powerSupply">
								<el-input
									maxlength="20"
									:show-word-limit="true"
									v-model.trim="baseFormData.powerSupply"
								/>
							</el-form-item>
							<el-form-item label="通号" prop="communicationNumber">
								<el-input
									maxlength="20"
									:show-word-limit="true"
									v-model.trim="baseFormData.communicationNumber"
								/>
							</el-form-item>
							<el-form-item label="线路" prop="line">
								<el-input
									maxlength="20"
									:show-word-limit="true"
									v-model.trim="baseFormData.line"
								/>
							</el-form-item>
							<el-form-item label="更新内容" prop="updateContent">
								<el-input
									v-model="baseFormData.updateContent"
									type="textarea"
									:autosize="{ minRows: 8 }"
									resize="none"
								/>
							</el-form-item>
							<el-form-item label="上传包" prop="apkList">
								<el-upload
									ref="uploadApkRef"
									class="upload-apk-wrapper"
									:headers="headers"
									v-model:file-list="baseFormData.apkList"
									action="/pitaya/system/sdkManager/uploadFile"
									:limit="1"
									:on-exceed="handleExceed"
									:on-change="handleChange"
									:on-error="handleError"
									:before-upload="beforeUpload"
									:disabled="filesStatus === 'ready'"
									style="width: 100%; overflow: hidden"
								>
									<template #trigger>
										<el-button
											v-btn
											class="el-upload-button"
											type="primary"
											:loading="filesStatus === 'ready'"
										>
											<el-icon> <UploadFilled /> </el-icon>附件上传
										</el-button>
									</template>
									<template #file="{ file }">
										<div class="file-view-wrapper">
											<font-awesome-icon :icon="awesomeIcon" />
											<span class="file-name-text">{{ file.name }}</span>
											<font-awesome-icon
												class="awesom-del-icon"
												:icon="awesomeDelIcon"
												@click="handleRemove"
											/>
										</div>
									</template>
								</el-upload>
							</el-form-item>
						</el-form>
					</el-scrollbar>
				</div>
				<div class="btn-groups">
					<ButtonList
						:button="btnList"
						:loading="buttonLoading"
						@onBtnClick="onFormBtnClick"
					/>
				</div>
			</div>
		</Drawer>
	</div>
</template>

<style lang="scss" scoped>
.app-container {
	padding-top: 10px;
	:deep(.model-frame-wrapper) {
		padding-bottom: 0px;
	}
}
.app-content-wrapper {
	height: 0;
	flex: 1;
	.app-content-group {
		height: 100%;
		:deep(.model-frame-wrapper) {
			height: 100%;
			padding-bottom: 10px;
			overflow: hidden;
			display: flex;
			flex-direction: column;
		}
		.app-el-scrollbar-wrapper {
			height: 0;
			flex: 1;
			.table-inner-btn {
				margin-left: 5px;
				color: #204a9c;
			}
			:deep(.el-switch) {
				height: 20px;
			}
		}
	}
}
.common-from-wrapper {
	height: 100%;
	width: 100%;
	display: flex;
	flex-direction: column;
	.common-from-group {
		height: 0;
		flex: 1;
		padding: 0 10px;
	}
	.btn-groups {
		display: flex;
		justify-content: flex-end;
		padding: 10px 10px 0 0;
		border-top: 1px solid var(--pitaya-border-color);
	}
}

.state0 {
	color: #e25e59;
	border-color: #e25e59;
	background-color: #f9e7e7;
}
.state1 {
	color: #4bae89;
	border-color: #4bae89;
	background-color: #e0ffef;
}
.state2 {
	color: #999999;
	border-color: #d0d0d0;
	background-color: #f5f7fb;
}
.state3 {
	color: #009dff;
	border-color: #009dff;
	background-color: #dff3ff;
}

.file-view-wrapper {
	display: flex;
	align-items: center;
	padding: 0 5px;
	font-size: var(--pitaya-fs-12);
	color: var(--pitaya-header-bg-color);
	.file-name-text {
		width: 0;
		flex: 1;
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
		padding: 0 5px;
	}
	.awesom-del-icon {
		display: none;
		cursor: pointer;
	}
	&:hover {
		.awesom-del-icon {
			display: block;
		}
	}
}
</style>

<style lang="scss" scoped>
.major-version-label-init {
	label {
		display: none !important;
	}

	.major-version-label-con {
		margin-bottom: 5px;
		line-height: 22px;
		display: block;
		height: auto;
		text-align: left;
		font-size: 12px;
		color: #606266;
		display: flex;
		justify-content: center;
		align-items: center;

		&::before {
			content: "*";
			color: #f56c6c;
			margin-right: 4px;
		}
	}
}

.major-version-tooltip-item {
	margin-bottom: 5px;
}
</style>
@/app/platform/hooks/usePagination
@/app/platform/api/system/version@/app/platform/api/system/dictionary
