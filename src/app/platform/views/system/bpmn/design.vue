<template>
	<div style="padding: 0 320px 0 10px">
		<Title :title="containerDesignTitle">
			<Tabs
				@onTabChange="onTabChange"
				style="position: absolute; left: 124px"
				:tabs="['设计', '源码']"
			/>
		</Title>
		<div class="containers-bpmn" v-if="activeTab === 0">
			<div class="canvas" ref="canvas" id="bpmn-canvas" />
			<div class="panel">
				<Title :title="processTitle" />
				<el-scrollbar style="height: calc(100vh - 95px)">
					<div id="js-properties-panel" />
				</el-scrollbar>
				<div class="buttons">
					<el-button class="button" type="primary" @click="uploadServe()"
						>保存</el-button
					>
				</div>
			</div>
			<Drawer
				:size="drawerSize"
				v-model:drawer="showDrawer"
				@open="handlerOpen"
				:close-on-click-modal="closeMode"
				:destroyOnClose="true"
			>
				<UserSelectDrawerV2
					v-if="drawerTitle != '选择候选角色'"
					:idTag="idTag"
					:titel="drawerTitle"
					:selectRows="selectRows"
					@save="handleSave"
					@cancel="handlerCancel"
				/>
				<RoleSelectDrawerV2
					v-if="drawerTitle == '选择候选角色'"
					:idTag="idTag"
					:titel="drawerTitle"
					:selectRows="selectRows"
					@save="handleSave"
					@cancel="handlerCancel"
				/>
			</Drawer>
		</div>
		<div
			v-if="activeTab === 1"
			style="height: calc(100vh - 75px)"
			class="scroll_bar"
		>
			<el-input
				type="textarea"
				:autosize="{ minRows: 2, maxRows: 30 }"
				:rows="30"
				v-model="xmlStr"
			/>
		</div>
	</div>
</template>
<script setup lang="ts">
// 引入Bpmn相关的依赖
import BpmnModeler from "bpmn-js/lib/Modeler"
// 这里引入的是右侧属性栏这个框
import propertiesPanelModule from "bpmn-js-properties-panel"
// 而这个引入的是右侧属性栏里的内容
import propertiesProviderModule from "bpmn-js-properties-panel/lib/provider/camunda"
// 右侧属性栏扩展，不然报错
import camundaModdleDescriptor from "camunda-bpmn-moddle/resources/camunda"
// 汉化组件
import customTranslate from "./customTranslate/customTranslate"
import { defaultXmlStr } from "./defaultXmlStr"
import UserSelectDrawerV2 from "./UserSelectDrawerV2.vue"
import RoleSelectDrawerV2 from "./RoleSelectDrawerV2.vue"
import { map } from "lodash-es"
import {
	getProcessXML,
	deployProcessDef,
	commonUploadFile,
	commonDownloadFile,
	processVersionDeploy
} from "@/app/platform/api/system/processDesign"
import x2js from "x2js"
import { events } from "@/app/platform/utils/bus"
const emit = defineEmits(["camundaOclick", "onSuccess"])
interface Props {
	currentVersion: Object
	processData: Object
	xmlStrPath: string
}
const customTranslateModule = {
	translate: ["value", customTranslate]
}
const props = defineProps<Props>()
const containerDesignTitle = {
	name: ["流程设计"],
	icon: ["fas", "square-share-nodes"]
}
const activeTab = ref(0)
const bpmnModeler = ref({})
const container = ref({})
const row = ref([])
const shape = ref(null)
const canvas = ref({})
const xmlStr = ref("")
const drawerSize = 1050
const showDrawer = ref(false)
const drawerTitle = ref("选择")
const processTitle = {
	name: ["流程参数"],
	icon: ["fas", "square-share-nodes"]
}
const idTag = ref("")
// 传递给表格勾选使用
const selectRows = ref([])
// 存放用户节点
const userTask = ref({})
const currentTaskId = ref("")
const closeMode = ref(true)
const newProcessDefinitionKey = ref("Process")
const init = async () => {
	nextTick(() => {
		initBpmn()
		setTimeout(() => {
			if (document.getElementById("camunda-id")) {
				document.getElementById("camunda-id").classList.remove("invalid")
				document.querySelector(".bpp-error-message").style.display = "none"
			}
		}, 500)
	})
}
const initBpmn = () => {
	// 获取到属性ref为“canvas”的dom节点
	const canvasRef = canvas.value
	canvasRef.innerHTML = ""
	// 建模
	bpmnModeler.value = new BpmnModeler({
		container: canvasRef,
		//添加左侧控制板
		propertiesPanel: {
			parent: "#js-properties-panel"
		},
		additionalModules: [
			//添加右侧属性面板
			propertiesProviderModule,
			propertiesPanelModule,
			//汉化
			customTranslateModule
		],
		moddleExtensions: {
			//如果要在属性面板中修改属性，必须添加
			camunda: camundaModdleDescriptor
		}
	})
	createDiagram()
	addEventBusListener()
}

// 暂存流程名称，解决点击空白处流程名称消失的问题
const processName = ref<string>("")
const createDiagram = async () => {
	if (xmlStr.value === "") {
		if (props.xmlStrPath) {
			xmlStr.value = await getXmlUrl()
			// // 禁止修改流程id、流程名称
			setTimeout(() => {
				const itemName = document.querySelector("div#camunda-name")
				if (itemName) {
					if(processName.value){
						itemName.innerHTML = processName.value
					}
					var camundaParent =
						document.querySelector("input#camunda-id").parentElement
					if (camundaParent) {
						camundaParent
							.querySelectorAll(".action-button")
							.forEach(function (button) {
								// 添加或修改样式
								button.style.display = "none" // 示例样式
							})
					}
				}
			}, 200)
			// console.log("xmlStr load", xmlStr.value)
		} else {
			xmlStr.value = defaultXmlStr(
				props.processData.code,
				props.processData.name
			)
		}
		console.log("xmlStr ==>", xmlStr.value,'props.processData.name',props.processData.name)
	}
	try {
		const result = await bpmnModeler.value.importXML(xmlStr.value)
		console.log("bpmnResult-", result)
		// 禁止修改流程id、流程名称
		setTimeout(() => {
			const itemId = document.querySelector("input#camunda-id")
			const itemName = document.querySelector("div#camunda-name")
			if (itemId && itemName) {
				itemId.disabled = true
				itemName.contentEditable = false
				processName.value = itemName.innerHTML
				var camundaParent =
						document.querySelector("input#camunda-id").parentElement
					if (camundaParent) {
						camundaParent
							.querySelectorAll(".action-button")
							.forEach(function (button) {
								// 添加或修改样式
								button.style.display = "none" // 示例样式
							})
					}
			}
		}, 200)
	} catch (err) {
		console.error("bpmnError-", err)
	}
	// 让图能自适应屏幕
	const tempCanvas = bpmnModeler.value.get("canvas")
	tempCanvas.zoom("fit-viewport")
	bpmnModeler.value.on(
		"element.click",
		(e: { element: { id: any }; shape: any }) => {
			const elementRegistry = bpmnModeler.value.get("elementRegistry")
			shape.value = e.element ? elementRegistry.get(e.element.id) : e.shape
			setTimeout(() => {
				document.getElementById("camunda-id").classList.remove("invalid")
				newProcessDefinitionKey.value =
					document.getElementById("camunda-id").value
				//console.log("")
				document.querySelector(".bpp-error-message").style.display = "none"
				document.getElementById("camunda-id").readOnly = true
			}, 300)
		}
	)
}

// 审批人、候选用户、候选角色
const addEventBusListener_backup = () => {
	const eventBus = bpmnModeler.value.get("eventBus") // 需要使用 eventBus
	const eventType = ["element.click", "element.changed"] // 需要监听的事件集合
	eventType.forEach((eventType) => {
		eventBus.on(eventType, (e: { element: { type: string; id: any } }) => {
			if (e.element.type === "bpmn:Process") {
				console.log("监听到事件--->", e)
			}
			if (e.element.type === "bpmn:UserTask") {
				console.log("监听成功", e.element)
				currentTaskId.value = e.element.id
				setTimeout(function () {
					const buttonsOclick = document.getElementById("buttons")
					const assignee = document.getElementById("camunda-assignee")
					const candidateUsers = document.getElementById(
						"camunda-candidateUsers"
					)
					const candidateGroups = document.getElementById(
						"camunda-candidateGroups"
					)

					if (assignee) {
						assignee.parentNode.children[1].style.display = "none"
						assignee.disabled = true
					}
					if (candidateUsers) {
						candidateUsers.parentNode.children[1].style.display = "none"
						candidateUsers.disabled = true
					}
					if (candidateGroups) {
						candidateGroups.parentNode.children[1].style.display = "none"
						candidateGroups.disabled = true
					}
					// if (!buttonsOclick) {
					if (assignee) {
						if (!userTask.value[currentTaskId.value]) {
							userTask.value[currentTaskId.value] = {}
						}
						if (!userTask.value[currentTaskId.value].assigneeRows) {
							userTask.value[currentTaskId.value].assigneeRows = []
						}
						assignee.value = map(
							userTask.value[currentTaskId.value].assigneeRows,
							"realname"
						).join(",")
						assignee.classList.add("w80")
						assignee.insertAdjacentHTML(
							"afterend",
							'<button class="button" id="buttons"  style="width: 50px;border-color: #1890ff;background: #1890ff;color: #fff;">选择</button>'
						)
					}
					// 候选用户
					// const name = document.getElementById('camunda-candidateUsers');
					if (candidateUsers) {
						if (!userTask.value[currentTaskId.value]) {
							userTask.value[currentTaskId.value] = {}
						}
						if (!userTask.value[currentTaskId.value].candidateUsersRows) {
							userTask.value[currentTaskId.value].candidateUsersRows = []
						}
						candidateUsers.value = map(
							userTask.value[currentTaskId.value].candidateUsersRows,
							"realname"
						).join(",")
						candidateUsers.classList.add("w80")
						candidateUsers.insertAdjacentHTML(
							"afterend",
							'<button class="button" id="buttonsUsers"  style="width: 50px;border-color: #1890ff;background: #1890ff;color: #fff;">选择</button>'
						)
					}
					// 候选组
					if (candidateGroups) {
						console.log("candidateGroups", userTask.value, currentTaskId.value)
						if (!userTask.value[currentTaskId.value]) {
							userTask.value[currentTaskId.value] = {}
						}
						if (!userTask.value[currentTaskId.value].candidateGroupsRows) {
							userTask.value[currentTaskId.value].candidateGroupsRows = []
						}
						candidateGroups.value = map(
							userTask.value[currentTaskId.value].candidateGroupsRows,
							"roleName"
						).join(",")
						candidateGroups.classList.add("w80")
						candidateGroups.insertAdjacentHTML(
							"afterend",
							'<button class="button" id="buttonsGroups"  style="width: 50px;border-color: #1890ff;background: #1890ff;color: #fff;">选择</button>'
						)
					}
					const buttons = document.getElementById("buttons")
					const buttonsUsers = document.getElementById("buttonsUsers")
					const buttonsGroups = document.getElementById("buttonsGroups")
					// eslint-disable-next-line @typescript-eslint/no-this-alias
					if (buttons && buttonsUsers && buttonsGroups) {
						buttons.addEventListener("click", function () {
							drawerTitle.value = "选择审批人"
							idTag.value = "camunda-assignee"
							selectRows.value = JSON.parse(
								JSON.stringify(userTask.value[currentTaskId.value].assigneeRows)
							)
							showDrawer.value = true
						})
						buttonsUsers.addEventListener("click", function () {
							drawerTitle.value = "选择候选用户"
							idTag.value = "camunda-candidateUsers"
							selectRows.value = JSON.parse(
								JSON.stringify(
									userTask.value[currentTaskId.value].candidateUsersRows
								)
							)
							showDrawer.value = true
						})
						buttonsGroups.addEventListener("click", function () {
							drawerTitle.value = "选择候选角色"
							idTag.value = "camunda-candidateGroups"
							selectRows.value = JSON.parse(
								JSON.stringify(
									userTask.value[currentTaskId.value].candidateGroupsRows
								)
							)
							showDrawer.value = true
						})
					}
				}, 200)
			}
		})
	})
}

// 仅保留候选角色，禁止更改元素id、流程名称
const addEventBusListener = () => {
	const eventBus = bpmnModeler.value.get("eventBus") // 需要使用 eventBus
	const eventType = ["element.click", "element.changed"] // 需要监听的事件集合
	eventType.forEach((eventType) => {
		eventBus.on(eventType, (e: { element: { type: string; id: any } }) => {
			console.log("监听到事件--->", e)

			if (e.element.type === "bpmn:Process") {
				// // 禁止修改流程id、流程名称
				setTimeout(() => {
					const itemName = document.querySelector("div#camunda-name")
					if (itemName) {
						itemName.innerHTML = processName.value
						var camundaParent =
							document.querySelector("input#camunda-id").parentElement
						if (camundaParent) {
							camundaParent
								.querySelectorAll(".action-button")
								.forEach(function (button) {
									// 添加或修改样式
									button.style.display = "none" // 示例样式
								})
						}
					}
				}, 200)
			}
			if (e.element.type === "bpmn:UserTask") {
				console.log("监听成功", e.element)
				currentTaskId.value = e.element.id

				setTimeout(function () {
					// 获取具有 data-entry="candidateUsers" 的元素
					if (!document.getElementById("camunda-implementation-select")) {
						const candidateUsersElement = document.querySelector(
							'[data-entry="candidateUsers"]'
						)

						// 创建一个新的 div 元素
						const newDiv = document.createElement("div")
						newDiv.className = "bpp-properties-entry bpp-textfield"

						// 创建一个新的 label 元素
						const label = document.createElement("label")
						label.htmlFor = "camunda-candidateUsers" // 注意：虽然这里可以用 for，但为了避免与 JavaScript 的保留字冲突，使用 htmlFor 是更安全的做法
						label.textContent = "候选人类型"

						// 创建一个新的 select 元素
						const select = document.createElement("select")
						select.id = "camunda-implementation-select"
						select.name = "implType"
						select.setAttribute("data-value", "") // 设置自定义属性

						// 创建选项并添加到 select 中
						const options = [
							{ value: "role", text: "角色" },
							{ value: "expression", text: "表达式" }
						]
						options.forEach((option) => {
							const opt = document.createElement("option")
							opt.value = option.value
							opt.textContent = option.text
							select.appendChild(opt)
						})

						// 将 label 和 select 添加到新的 div 元素中
						newDiv.appendChild(label)
						newDiv.appendChild(select)

						// 在 candidateUsersElement 之前插入新的 div 元素
						candidateUsersElement.parentNode.insertBefore(
							newDiv,
							candidateUsersElement
						)
					}

					var selectElement = document.getElementById(
						"camunda-implementation-select"
					)

					// 隐藏审批人、候选用户
					const assignee = document.querySelector("[data-entry='assignee']")
					const candidateUsers = document.querySelector(
						"[data-entry='candidateUsers']"
					)

					if (assignee && candidateUsers) {
						assignee.style.display = "none"
						// candidateUsers.style.display = "none"
						if (selectElement.value == "role") {
							candidateUsers.style.display = "none"
						}
					}

					const candidateGroups = document.getElementById(
						"camunda-candidateGroups"
					)
					const candidateGroupsBox = document.querySelector(
						"[data-entry='candidateGroups']"
					)
					if (candidateGroups) {
						candidateGroups.parentNode.children[1].style.display = "none"
						candidateGroups.disabled = true
					}
					// 候选角色
					if (candidateGroups) {
						if (!userTask.value[currentTaskId.value]) {
							userTask.value[currentTaskId.value] = {}
						}
						if (!userTask.value[currentTaskId.value].candidateGroupsRows) {
							userTask.value[currentTaskId.value].candidateGroupsRows = []
						}
						candidateGroups.value = map(
							userTask.value[currentTaskId.value].candidateGroupsRows,
							"roleName"
						).join(",")
						candidateGroups.classList.add("w80")
						candidateGroups.insertAdjacentHTML(
							"afterend",
							'<button class="button" id="buttonsGroups"  style="width: 50px;border-color: #1890ff;background: #1890ff;color: #fff;">选择</button>'
						)

						//判断当前候选人类型
						if (e.element.businessObject.candidateUsers) {
							candidateUsers.style.display = "block"
							candidateGroupsBox.style.display = "none"
							selectElement.value = "expression"
						}

						if (e.element.businessObject.candidateGroups) {
							candidateUsers.style.display = "none"
							candidateGroupsBox.style.display = "block"
							selectElement.value = "role"
						}
					}

					// 添加 change 事件监听器
					selectElement.addEventListener("change", function (event) {
						// 获取选中的选项的 value
						var selectedValue = event.target.value
						// 在控制台打印选中的值和文本内容
						if (selectedValue == "role") {
							candidateUsers.style.display = "none"
							candidateGroupsBox.style.display = "block"
							selectElement.value = "role"
							userTask.value[currentTaskId.value].candidateUsersRows = []
							e.element.businessObject.candidateUsers = ""
						}
						if (selectedValue == "expression") {
							candidateUsers.style.display = "block"
							candidateGroupsBox.style.display = "none"
							selectElement.value = "expression"
							userTask.value[currentTaskId.value].candidateGroupsRows = []
							candidateGroups.value = ""
							e.element.businessObject.candidateGroups = ""
						}
						// 你可以在这里添加其他逻辑，比如更新页面上的其他元素
					})

					const buttonsGroups = document.getElementById("buttonsGroups")
					// eslint-disable-next-line @typescript-eslint/no-this-alias
					if (buttonsGroups) {
						buttonsGroups.addEventListener("click", function () {
							drawerTitle.value = "选择候选角色"
							idTag.value = "camunda-candidateGroups"
							selectRows.value = JSON.parse(
								JSON.stringify(
									userTask.value[currentTaskId.value].candidateGroupsRows
								)
							)
							showDrawer.value = true
						})
					}

					// }
				}, 200)
			}
			// 禁止修改流程id、流程名称
			setTimeout(() => {
				const itemId = document.querySelector("input#camunda-id")
				const itemName = document.querySelector("div#camunda-name")
				if (itemId && itemName && itemName.innerHTML === processName.value) {
					itemId.disabled = true
					// itemName.contentEditable = false
					var camundaParent =
						document.querySelector("input#camunda-id").parentElement
					if (camundaParent) {
						camundaParent
							.querySelectorAll(".action-button")
							.forEach(function (button) {
								// 添加或修改样式
								button.style.display = "none" // 示例样式
							})
					}
				}
			}, 200)
		})
	})
}
// 选人弹窗 保存
const handleSave = (inputIdTag: any, selectData: any) => {
	let tempUsers
	console.log("idTag", selectData)
	switch (inputIdTag) {
		case "camunda-assignee":
			userTask.value[currentTaskId.value].assigneeRows = selectData
			tempUsers = map(selectData, "username").join(",")
			camundaOnClick("1", selectData)
			break
		case "camunda-candidateUsers":
			userTask.value[currentTaskId.value].candidateUsersRows = selectData
			tempUsers = map(selectData, "username").join(",")
			camundaOnClick("2", selectData)
			break
		case "camunda-candidateGroups":
			userTask.value[currentTaskId.value].candidateGroupsRows = selectData
			tempUsers = map(selectData, "id").join(",")
			camundaOnClick("3", selectData)
			console.log("candidateGroupsRows", selectData, tempUsers)

			break
		default:
			break
	}
	console.log(
		"userTask.value[currentTaskId.value]",
		userTask.value[currentTaskId.value]
	)
	// document.getElementById(idTag).value = tempUsers
	showDrawer.value = false
}
const camundaOnClick = (item: any, value: any) => {
	emit("camundaOclick", value, item)
	onSelectValues(null, value, item)
}
const onSelectValues = (options: any, values: any, item: any) => {
	console.log("onSelectValues", options, values, item)
	if (item == "1") {
		const names = map(values, "username").join(",")
		document.getElementById("camunda-assignee").value = names
		const element = document.getElementById("camunda-assignee")
		// 创建一个新的事件
		const event = document.createEvent("Event")
		// 触发事件
		event.initEvent("change", true, false)
		element.dispatchEvent(event)
	} else if (item == "2") {
		const names = map(values, "username").join(",")
		document.getElementById("camunda-candidateUsers").value = names
		const element = document.getElementById("camunda-candidateUsers")
		// 创建一个新的事件
		const event = document.createEvent("Event")
		// 触发事件
		event.initEvent("change", true, false)
		element.dispatchEvent(event)
	} else {
		const names = map(values, "id").join(",")
		document.getElementById("camunda-candidateGroups").value = names
		console.log("names", names)
		const element = document.getElementById("camunda-candidateGroups")
		// 创建一个新的事件
		const event = document.createEvent("Event")
		// 触发事件
		event.initEvent("change", true, false)
		element.dispatchEvent(event)
		console.log("element.dispatchEvent(event)", element, event)
	}
}
const getXmlUrl = () => {
	return new Promise((resolve) => {
		commonDownloadFile(props.xmlStrPath).then((res: any) => {
			console.log("res", res)
			// const xml = new Blob([res], { type: "application/vnd.ms-excel" })
			// console.log("xml", xml)
			const reader = new FileReader()
			// 传入需要被转换的文本流 file,这个是转字符串的关键方法
			reader.readAsText(res)
			// onload是异步的,封装的话可以用promise
			reader.onload = () => {
				// 输出字符串
				resolve(reader.result)
				const resultJson = new x2js().xml2js(reader.result)
				let pickResult = resultJson.definitions.process.userTask
				if (!(pickResult instanceof Array)) pickResult = [pickResult]
				console.log("reader pickResult", pickResult, userTask.value)

				pickResult.forEach((item: any) => {
					userTask.value[item._id] = {}
					const jsonString = JSON.stringify(item).replace(/_camunda:/g, "")
					const jsonObj = JSON.parse(jsonString)
					dealwithSelectObject(item._id, "assigneeRows", jsonObj["assignee"])
					dealwithSelectObject(
						item._id,
						"candidateUsersRows",
						jsonObj["candidateUsers"]
					)
					dealwithSelectObject(
						item._id,
						"candidateGroupsRows",
						jsonObj["candidateGroups"]
					)
				})
			}
		})
		// setTimeout(() => {
		// 	const url = ""
		// 	resolve(url)
		// }, 1000)
	})
}
const dealwithSelectObject = (
	id: string | number,
	key: string,
	val: string
) => {
	userTask.value[id][key] = []
	if (val) {
		val.split(",").forEach((row) => {
			const temp =
				key == "candidateGroupsRows"
					? {
							id: row,
							roleName: row
					  }
					: {
							username: row,
							realname: row
					  }
			userTask.value[id][key].push(temp)
		})
	}
}
const handlerOpen = () => {
	console.log("open", selectRows.value)
}
const handlerCancel = () => {
	showDrawer.value = false
	selectRows.value = []
}
const success = () => {
	console.log("创建成功")
}
const add = () => {
	init()
}
const download = async () => {
	const result = await bpmnModeler.value.saveXML({
		format: true
	})
	const { xml } = result
	console.log(xml)
	const name = document.getElementById("camunda-name").innerHTML
	const element = document.createElement("a")
	element.setAttribute(
		"href",
		"data:text/xml;charset=utf-8," + encodeURIComponent(xml)
	)
	element.setAttribute("download", name + ".bpmn")
	element.style.display = "none"
	element.click()
}
const uploadServe = async () => {
	// 获取流程元素进行校验
	try {
		const canvas = bpmnModeler.value.get('canvas');
		const rootElement = canvas.getRootElement();
		const process = rootElement.businessObject;
		const flowElements = process.flowElements || [];

		if (flowElements.length <= 1) {
			ElMessage.error('流程不完整,请检查流程图配置');
			return;
		}else{
			const { xml } = await bpmnModeler.value.saveXML({
				format: true
			})
			console.log("上传到服务器", bpmnModeler.value, xml)
			const blob = new Blob([xml], { type: "text/plain;charset=utf-8" })
			const formData = new FormData()
			const name = document.getElementById("camunda-name").innerHTML
			formData.append(
				"file",
				new File([blob], name + ".bpmn", { type: "text/plain" })
			)
			formData.append("businessId", props.currentVersion.businessId)
			formData.append("businessType", props.currentVersion.businessType)
			formData.append("versionControlId", props.currentVersion.id)
			// Long businessId, Integer businessType, Long versionControlId
			// await commonUploadFile(formData)
			// ElMessage.success("保存成功")
			const response = await commonUploadFile(formData);
			// 假设后端返回结构为 { code: number, message: string }
			if (response) { // 根据实际成功码调整
				ElMessage.success("保存成功");
				xmlStr.value = xml
			} else {
				ElMessage.error(response.message || "保存失败，请重试");
			}
		}
	} catch (error) {
		console.error('上传流程失败:', error);
		ElMessage.error(`保存失败: ${error.message || '未知错误'}`);
  	}
}
const delploy = async () => {
	const params = {
		formData: {
			versionId: props.currentVersion.id,
			version: props.currentVersion.version,
			businessId: props.currentVersion.businessId
		}
	}
	const canvas = bpmnModeler.value.get('canvas');
	const rootElement = canvas.getRootElement();
	const process = rootElement.businessObject;
	const flowElements = process.flowElements || [];

	if (flowElements.length <= 1) {
		ElMessage.error('流程不完整,请检查流程图配置');
		return;
	}else{
		await uploadServe()
		await processVersionDeploy(params)
		ElMessage.success("发布成功")
		emit("onSuccess")
	}
}
const clearDiagram = async () => {
	try {
		const result = await bpmnModeler.value.saveXML({
			format: true
		})
		const { xml } = result
		const params = { xmlStr: xml }
		const res = null
		// if (this.row.id) {
		// 	params.id = this.row.id
		// 	res = defHttp.post({ url: "/oa/actReModel/edit", params })
		// } else {
		// 	res = defHttp.post({ url: "/oa/actReModel/add", params })
		// }
		// res.then((data) => {
		// 	console.log(data)
		// 	this.$emit("reloadList")
		// })
	} catch (err) {
		console.log("err", err)
	}
}
const updata = (row: { id: any; value: any }) => {
	const params = { id: row.id }
	row.value = row
	// const res = defHttp.get({ url: "/oa/actReModel/queryById", params })
	// res.then((data) => {
	// 	if (data) {
	// 		this.xmlStr = data.xmlStr
	// 		this.$nextTick(() => {
	// 			this.initBpmn()
	// 		})
	// 	}
	// })
}
const saveDiagram = async () => {
	try {
		const result = await bpmnModeler.value.saveXML({
			format: true
		})
		const { xml } = result
		const params = { xmlStr: xml }
		const res = null
		// if (this.row.id) {
		// 	params.id = this.row.id
		// 	res = defHttp.post({ url: "/oa/actReModel/edit", params })
		// } else {
		// 	res = defHttp.post({ url: "/oa/actReModel/add", params })
		// }
		// res.then((data) => {
		// 	console.log(data)
		// 	this.$emit("reloadList")
		// })
	} catch (err) {
		console.log("err", err)
	}
}

const onTabChange = (index: any) => {
	activeTab.value = index
	// 刷新设计页面
	if (activeTab.value === 0) {
		nextTick(() => {
			console.log("onTabChange", xmlStr.value)
			initBpmn()
		})
	}
}

/**
 * 钩子函数
 */
onMounted(() => {
	init()
	success()
})

defineOptions({
	name: "Bpmn"
})
defineExpose({
	download,
	uploadServe,
	delploy
})
</script>
<style lang="scss" scoped>
.containers-bpmn {
	position: static;
	background-color: #ffffff;
	width: 100%;
	font-size: var(--pitaya-fs-12) !important;
	height: calc(100vh - 20px) !important;
	:deep(svg) {
		position: static !important;
	}
}
.containers-bpmn::before,
.containers-bpmn::after {
	position: static !important;
}

.canvas {
	width: 100%;
	height: 100%;
}
// .panel-title {
// 	position: absolute;
// 	right: 20px;
// 	top: 0px;
// 	width: 300px;
// }
.panel {
	position: absolute;
	right: 0px;
	top: -1px;
	width: 310px;
	padding: 0 0 0 10px;
	height: calc(100vh - 10px);
	background-color: hsl(0, 0%, 100%);
	border: solid #ccc;
	border-width: 0 0 0 1px;
	overflow-y: auto;
	:deep(.bpp-properties-panel) {
		background-color: #ffffff;
	}
	:deep(a) {
		background-color: #ffffff;
	}
	:deep(.bpp-properties-group) {
		border-bottom: #eee solid;
	}

	:deep(label) {
		font-weight: normal;
		line-height: 1;
	}
	:deep(.button) {
		margin-top: 5px;
		border-radius: 4px;
		line-height: 1;
		height: 32px;
		white-space: nowrap;
		background-color: var(--pitaya-active-btn-background) !important;
		color: #ffffff !important;
	}
}

.buttons {
	display: flex;
	justify-content: flex-end;
	border-top: 1px solid #ccc;
	padding-top: 10px;
	box-sizing: border-box;
}
:deep(.bjs-powered-by) {
	display: none !important;
}
.buttons .button {
	margin: 0px 5px;
}
:deep(.djs-palette) {
	top: 10px !important;
	left: 10px !important;
}
.djs-palette .entry {
	float: none !important;
}
.djs-palette.two-column.open {
	width: 47px !important;
}
.w80 {
	width: 80% !important;
	float: left;
}

/** 美化原生滚动条 */
.scroll_bar {
	:deep(.el-textarea__inner) {
		// 整个滚动条
		&::-webkit-scrollbar {
			width: 8px;
			height: 8px;
		}
		// 滚动条上的滚动滑块
		&::-webkit-scrollbar-thumb {
			border-radius: 4px;
			background-color: #90939955;
		}
		&::-webkit-scrollbar-thumb:hover {
			background-color: #90939977;
		}
		&::-webkit-scrollbar-thumb:active {
			background-color: #90939999;
		}
		// 当同时有垂直滚动条和水平滚动条时交汇的部分
		&::-webkit-scrollbar-corner {
			background-color: transparent;
		}
	}
}
</style>
