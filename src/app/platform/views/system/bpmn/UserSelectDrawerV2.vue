<script setup lang="ts">
import { getAllPageUsers } from "@/app/platform/api/system/baseRole"
import { useUserStore } from "@/app/platform/store/modules/user"

const drawerTitle = ref({
	name: ["选人"],
	icon: ["fas", "square-share-nodes"]
})
interface Props {
	titel: any
	idTag: string
	selectRows: any[]
}
const props = defineProps<Props>()

const selectRows = toRef(props, "selectRows")

const tableData = ref<any[]>([])
const tableColumn = ref<any[]>([])
const tableLoading = ref<boolean>(false)
const tableSelectSingleMode = ref<boolean>(true)
const pageInfo = ref<any>({
	pageNum: 1,
	pageSize: 20,
	total: 0
})

const onCurrentPageChange = (pageData: any) => {
	pageInfo.value.pageSize = pageData.pageSize
	pageInfo.value.currentPage = pageData.currentPage
	getUsersTableData({})
}

const queryArrList = [
	{
		name: "用户姓名",
		key: "realname",
		placeholder: "请输入查询关键字",
		enableFuzzy: false,
		type: "input"
	},
	{
		name: "用户账号",
		key: "username",
		placeholder: "请输入查询关键字",
		enableFuzzy: false,
		type: "input"
	},
	{
		name: "所属部门",
		key: "orgName",
		placeholder: "请输入查询关键字",
		type: "input"
	},
	{
		name: "所属班组",
		key: "teamdName",
		placeholder: "请输入查询关键字",
		type: "input"
	}
]

const getQueryData = (queryData: any) => {
	getUsersTableData(queryData)
}

watch(
	() => props.titel,
	(val: any) => {
		drawerTitle.value.name = [val]
		switch (val) {
			case "选择审批人":
				tableColumn.value = [
					{ prop: "username", label: "用户账号", width: 140 },
					{ prop: "realname", label: "用户姓名", width: 100 },
					{ prop: "orgName", label: "部门", minWidth: 200 },
					{ prop: "teamdName", label: "班组", minWidth: 200 }
				]
				tableSelectSingleMode.value = true
				break
			case "选择候选用户":
				tableColumn.value = [
					{ prop: "username", label: "用户账号", width: 140 },
					{ prop: "realname", label: "用户姓名", width: 100 },
					{ prop: "orgName", label: "部门", minWidth: 200 },
					{ prop: "teamdName", label: "班组", minWidth: 200 }
				]
				tableSelectSingleMode.value = false
				break

			default:
				break
		}
	},
	{ immediate: true }
)

onMounted(() => {
	getUsersTableData({})
})

const userStore = useUserStore()
const { userInfo } = storeToRefs(userStore)
const getUsersTableData = (queryParams: any) => {
	const params = {
		...pageInfo.value,
		companyId: userInfo.value.companyId,
		...queryParams
	}
	tableLoading.value = true
	getAllPageUsers(params)
		.then((res: any) => {
			if (res.rows && res.rows.length) {
				tableData.value = res.rows
			} else {
				tableData.value = []
			}
			pageInfo.value.total = res.records
		})
		.finally(() => {
			tableLoading.value = false
		})
}

const btnList = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "保存",
		icon: ["fas", "floppy-disk"]
	}
]
const commonQueryRef = ref()
const userTableRef = ref()
const userSelect = ref([])
const emit = defineEmits(["save", "cancel"])
const onUserBtnClick = (btnName: string | undefined) => {
	// 置空勾选值
	commonQueryRef.value.handleReset()

	if (btnName === "保存") {
		userSelect.value = userTableRef.value.getSelectedTable()
		userTableRef.value.clearSelectedTableData()
		emit("save", props.idTag, userSelect.value)
		return
	}
	if (btnName === "取消") {
		userTableRef.value.clearSelectedTableData()
		emit("cancel")
		return
	}
}
</script>

<template>
	<div class="common-from-wrapper">
		<Title :title="drawerTitle" />
		<Query
			ref="commonQueryRef"
			class="my-query"
			:num-in-row="3"
			:query-btn-col-span="8"
			:queryArrList="queryArrList"
			@getQueryData="getQueryData"
		/>

		<div class="common-from-group">
			<PitayaTable
				ref="userTableRef"
				:table-data="tableData"
				:selectedTableData="selectRows"
				:columns="tableColumn"
				:needSelection="true"
				:singleSelect="tableSelectSingleMode"
				:table-loading="tableLoading"
				:total="pageInfo.total"
				selectKey="username"
				@onCurrentPageChange="onCurrentPageChange"
				style="padding-bottom: 10px"
			/>
		</div>
		<div class="btn-groups">
			<ButtonList :button="btnList" @onBtnClick="onUserBtnClick" />
		</div>
	</div>
</template>

<style scoped lang="scss">
.common-from-wrapper {
	display: flex;
	flex-direction: column;
	height: 100%;

	.my-query {
		margin: 10px 10px 0;
	}
	.common-from-group {
		flex: 1;
	}
	.btn-groups {
		padding-top: 10px;
		padding-right: 10px;
		border-top: 1px solid var(--pitaya-border-color);
		display: flex;
		justify-content: flex-end;
	}
}
</style>
@/app/platform/store/modules/user @/app/platform/api/system/baseRole
