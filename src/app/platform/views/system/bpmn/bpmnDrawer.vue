<script setup lang="ts">
import {
	getProcessAttachment,
	processVersionAdd,
	deleteDraft
} from "@/app/platform/api/system/processDesign"
import { getVersion } from "@/app/platform/api/system/processDesign"
import Bpmn from "./design.vue"
import Version from "./Version.vue"
import BPMNPreview from "./preview.vue"
import { CustomMessageBox } from "@/app/platform/utils/message"
import { resolve } from "path"
const emit = defineEmits(["editBtn"])

const containerLeftTitle = {
	name: ["流程版本"],
	icon: ["fas", "square-share-nodes"]
}
const containerRightTitle = {
	name: ["流程参数"],
	icon: ["fas", "square-share-nodes"]
}
const containerDesignTitle = {
	name: ["流程设计"],
	icon: ["fas", "square-share-nodes"]
}
const props = defineProps({
	processData: {
		type: Object,
		required: true
	}
})
const btnList = ref<any>([
	{
		name: "发布",
		icon: ["fas", "floppy-disk"],
		disabled: false
	},
	{
		name: "下载",
		icon: ["fas", "download"]
	},
	{
		name: "移除",
		icon: ["fas", "trash-can"]
	}
])
const BpmnDesignerRef = ref<any>("")
const BpmnPreviewRef = ref<any>("")
const onBtnClick = (btnName: string | undefined) => {
	//点击按钮
	if (btnName === "发布") {
		BpmnDesignerRef.value.delploy()
	} else if (btnName === "移除") {
		removeDraft()
	} else {
		if (showDesign.value) {
			BpmnDesignerRef.value.download()
		} else {
			BpmnPreviewRef.value.download()
		}
	}
}
const processData = toRef(props.processData)
const versionList = ref<any>([])
const tableLoading = ref<boolean>(false)
//查询流程版本
const getProcessDesignData = () => {
	getVersion({ businessType: 10, businessId: processData.value.id }).then(
		(res: any) => {
			versionList.value = res
			versionClick(versionList.value[0])
			resolve()
		}
	)
}
const versionAdd = () => {
	//新增版本
	//TODO 添加版本
	//versionClick(newDraft)
	//versionList.value = [newDraft, ...versionList.value]
	processVersionAdd({ businessId: props.processData.id }).then(() => {
		getProcessDesignData()
		nextTick(()=>{
			setTimeout(()=>{
				BpmnDesignerRef.value.uploadServe()
			},1000)
		})
	})
}
const addBtnDisabled = ref<boolean>(false)
const refreshBtnList = () => {
	if (versionList.value.length != 0 && versionList.value[0].isDraft) {
		addBtnDisabled.value = true
	} else {
		addBtnDisabled.value = false
	}
}
const showDesign = ref<boolean>(false)
const previewProcessDefinitionId = ref<string>("")
const currentVersion = ref<any>("")
const xmlStrPath = ref<string>("")
const versionClick = (version: any) => {
	previewProcessDefinitionId.value = ""
	currentVersion.value = version
	//从附件中获取xml
	getProcessAttachment({
		businessType: version.businessType,
		versionControlId: version.id
	}).then((res: any) => {
		//获取最新的附件 ，就是流程图的附件
		if (res && res.length > 0) {
			xmlStrPath.value = res[res.length - 1].filePath
		}
		nextTick(() => {
			refreshBtnList()
			//点击版本 草稿可以编辑
			if (version.bpmSts === 0) {
				showDesign.value = true
				previewProcessDefinitionId.value = version.formId
				btnList.value[0].disabled = false
				btnList.value[2].disabled = false
			} else {
				showDesign.value = false
				previewProcessDefinitionId.value = version.id
				btnList.value[0].disabled = true
				btnList.value[2].disabled = true
			}
		})
	})
}
const handleSuccess = (procDefKeyNew: string) => {
	//保存成功
	getProcessDesignData()
}
const editBtn =()=>{
	emit('editBtn',processData.value)

}
const removeDraft= ()=> {
	if(currentVersion.value&&currentVersion.value.bpmSts===0){
		CustomMessageBox(
			{ message: "确定要移除吗？" },
			(res: boolean) => {
				if (res) {
					deleteDraft({
						id: currentVersion.value.id,
						businessId: currentVersion.value.businessId
					}).then((res:any)=>{
						ElMessage.success('移除成功')
						getProcessDesignData()
					})
				}
			}
		)
		
	} else {
		ElMessage.warning('只能移除草稿版本')
	}
}
onMounted(() => {
	getProcessDesignData()
})
</script>

<template>
	<div class="app-container-row">
		<!-- 左侧专业树 -->
		<div class="left-model-frame" v-loading="tableLoading">
			<Title :title="containerLeftTitle">
				<div @click="editBtn" style="cursor:pointer;">
					<font-awesome-icon :icon="['fas', 'pen-to-square']" />
					修改基本信息
				</div>
			</Title>

			<div class="left-model-frame_content">
				<Button
					title="新增版本"
					@onBtnClick="versionAdd"
					v-show="!addBtnDisabled"
				/>
				<el-scrollbar>
					<Version
						:version-list="versionList"
						@onVersionClickBtn="versionClick"
					/>
				</el-scrollbar>
			</div>
			<div class="btn-groups">
				<ButtonList :button="btnList" @on-btn-click="onBtnClick" />
			</div>
		</div>
		<div class="right-model-frame">
			<Bpmn
				ref="BpmnDesignerRef"
				v-if="showDesign"
				:currentVersion="currentVersion"
				:processData="processData"
				:xmlStrPath="xmlStrPath"
				type="edit"
				@onSuccess="handleSuccess"
			/>
			<div class="bpmnPrev-container" v-else>
				<div style="width: 100%">
					<BPMNPreview
						ref="BpmnPreviewRef"
						v-if="previewProcessDefinitionId"
						:xmlStrPath="xmlStrPath"
						:currentVersion="currentVersion"
						:processData="processData"
					/>
				</div>
			</div>
		</div>
	</div>
</template>
<style lang="scss" scoped>
.left-model-frame_content {
	width: 100%;
	padding: 0 10px;
	height: 100%;
}
.app-container-row {
	padding: 0;
	align-items: center;
	flex-direction: row;
	height: calc(100vh - 20px);
	.left-model-frame {
		width: 300px;
		min-width: 300px !important;
		height: 100%;
		display: flex;
		margin-right: 0;
		flex-direction: column;
		border: #ccc solid;
		border-width: 0 1px 0 0;
		padding: 0 10px 45px 0;
		.btn-groups {
			display: flex;
			justify-content: flex-end;
			position: absolute;
			bottom: 0px;
			padding-top: 10px;
			padding-right: 10px;
			left: 0;
			right: 10px;
			width: auto;
			border-top: 1px solid #ccc;
			background-color: #fff;
		}
	}

	.right-model-frame {
		width: calc(100% - 300px);
		display: flex;
		padding: 0;
		flex-direction: column;
		height: 100%;
		.bpmnPrev-container {
			height: 100%;
			display: flex;
		}
		.panel {
			width: 310px;
			top: -1px;
			padding: 0 0 0 10px;
			border: #ccc solid;
			border-width: 0 0 0 1px;
		}
		:deep(#js-properties-panel) {
			padding: 0 10px;
		}
	}
}
.lh14 {
	line-height: 14px;
}

.f12 {
	font-size: 12px;
}

.c-9 {
	color: #999;
}

.c-6 {
	color: #666666;
}

.lh20 {
	line-height: 20px;
}

.mr10 {
	margin-right: 10px;
}
.span-text-right {
	line-height: 14px;
}
.w130 {
	width: 130px;
}
.flex-se {
	display: flex;
	justify-content: space-between;
	// height: 14px;
	margin-top: 10px;
	&:first-child {
		margin-top: 0;
	}
}
</style>
