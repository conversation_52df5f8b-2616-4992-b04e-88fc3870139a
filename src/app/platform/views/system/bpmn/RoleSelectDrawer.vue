<script setup lang="ts">
import { find, unionBy } from "lodash-es"
import { getBaseRoleList } from "@/app/platform/api/system/baseRole"
import { useUserStore } from "@/app/platform/store/modules/user"

const drawerTitle = ref({
	name: ["选择候选角色"],
	icon: ["fas", "square-share-nodes"]
})
interface Props {
	titel: any
	idTag: string
	selectRows: any[]
}
const props = defineProps<Props>()

const tableData = ref<any[]>([])
const tableColumn = ref<any[]>([
	{ prop: "roleCode", label: "角色编号", width: 140 },
	{ prop: "roleName", label: "角色名称", width: 100 },
	{ prop: "companyName", label: "所属公司", minWidth: 200 }
])
const tableSelectSingleMode = ref<boolean>(false)
const pageInfo = ref<any>({
	pageNum: 1,
	pageSize: 20,
	total: 0
})

const onCurrentPageChange = (pageData: any) => {
	pageInfo.value.pageSize = pageData.pageSize
	pageInfo.value.currentPage = pageData.currentPage
	getUsersTableData()
}

const queryArrList = [
	{
		name: "角色名称",
		key: "roleName",
		placeholder: "请输入查询关键字",
		type: "input"
	},
	{
		name: "角色编号",
		key: "roleCode",
		placeholder: "请输入查询关键字",
		type: "input"
	}
]

const getQueryData = (queryData: queryObj) => {
	console.log(queryData)
	userTableRef.value.resetPageInfo()
	getUsersTableData(queryData)
}

const selectRows = ref<any>([])

watch(
	() => props.selectRows,
	(rows: any) => {
		selectRows.value = rows || []
		selectRows.value.forEach((row: any) => {
			nextTick(() => {
				const findItem = find(tableData.value, (item) => {
					return item.id == row.id
				})
				findItem &&
					userTableRef.value!.pitayaTableRef!.toggleRowSelection(findItem, true)
			})
		})
	},
	{ immediate: true, deep: true }
)

onMounted(() => {
	getUsersTableData()
})

const userStore = useUserStore()
const { userInfo } = storeToRefs(userStore)
const getUsersTableData = (queryParams) => {
	console.log(queryParams, "queryParams")

	const params = {
		...pageInfo.value,
		companyId: userInfo.value.companyId,
		...queryParams
	}
	getBaseRoleList(params).then((res: any) => {
		if (res.rows && res.rows.length) {
			tableData.value = res.rows
		} else {
			tableData.value = []
		}
		pageInfo.value.total = res.records
		// 找到列表中勾选项
		selectRows.value.forEach((row: any) => {
			nextTick(() => {
				const findItem = find(tableData.value, (item) => {
					return item.id == row.id
				})
				findItem &&
					userTableRef.value!.pitayaTableRef!.toggleRowSelection(findItem, true)
			})
		})
	})
}

const btnList = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "保存",
		icon: ["fas", "floppy-disk"]
	}
]

const commonQueryRef = ref()
const userTableRef = ref()
const userSelect = ref([])
const emit = defineEmits(["save", "cancel"])
const onUserBtnClick = (btnName: string | undefined) => {
	// 置空勾选值
	selectRows.value = []
	pageInfo.value.currentPage = 1
	commonQueryRef.value.handleReset()

	if (btnName === "保存") {
		userSelect.value = userTableRef.value.getSelectedTable()

		userTableRef.value.clearSelectedTableData()
		emit("save", props.idTag, userSelect.value)
		return
	}
	if (btnName === "取消") {
		userTableRef.value.recoveryOperation()
		userTableRef.value.clearSelectedTableData()
		emit("cancel")
		return
	}
}

function onSelectionChange(params: any) {
	console.log("onSelectionChange", params)
}
</script>

<template>
	<div class="common-from-wrapper">
		<Title :title="drawerTitle" />

		<Query
			ref="commonQueryRef"
			class="my-query"
			:queryArrList="queryArrList"
			@getQueryData="getQueryData"
		/>

		<div class="common-from-group">
			<PitayaTable2
				ref="userTableRef"
				select-key="id"
				:table-data="tableData"
				:columns="tableColumn"
				:need-index="true"
				:need-pagination="true"
				:total="pageInfo.total"
				:single-select="tableSelectSingleMode"
				:selectedTableData="selectRows"
				:need-selection="true"
				@on-current-page-change="onCurrentPageChange"
			>
				<template #name="{ rowData }">
					<div
						class="line-name-container"
						:style="{
							backgroundColor: rowData.colour,
							borderColor: rowData.colour
						}"
					>
						{{ rowData.name }}
					</div>
				</template>
			</PitayaTable2>
		</div>
		<div class="btn-groups">
			<ButtonList :button="btnList" @onBtnClick="onUserBtnClick" />
		</div>
	</div>
</template>

<style scoped lang="scss">
.common-from-wrapper {
	display: flex;
	flex-direction: column;
	height: 100%;
	.my-query {
		margin: 10px 10px 0;
	}
	.common-from-group {
		flex: 1;
	}
	.btn-groups {
		padding-top: 10px;
		border-top: 1px solid var(--pitaya-border-color);
		display: flex;
		justify-content: flex-end;
	}
}
</style>
@/app/platform/store/modules/user @/app/platform/api/system/baseRole
