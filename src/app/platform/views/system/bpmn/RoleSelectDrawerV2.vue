<script setup lang="ts">
import { getBaseRoleList } from "@/app/platform/api/system/baseRole"
import { useUserStore } from "@/app/platform/store/modules/user"

const drawerTitle = ref({
	name: ["选择候选角色"],
	icon: ["fas", "square-share-nodes"]
})
interface Props {
	titel: any
	idTag: string
	selectRows: any[]
}
const props = defineProps<Props>()

const selectRows = toRef(props, "selectRows")

const tableData = ref<any[]>([])
const tableColumn = ref<any[]>([
	{ prop: "roleCode", label: "角色编号", width: 140 },
	{ prop: "roleName", label: "角色名称", width: 100 },
	{ prop: "companyName", label: "所属公司", minWidth: 200 }
])
const tableSelectSingleMode = ref<boolean>(false)
const pageInfo = ref<any>({
	pageNum: 1,
	pageSize: 20,
	total: 0
})

const onCurrentPageChange = (pageData: any) => {
	pageInfo.value.pageSize = pageData.pageSize
	pageInfo.value.currentPage = pageData.currentPage
	getUsersTableData({})
}

const queryArrList = [
	{
		name: "角色名称",
		key: "roleName",
		placeholder: "请输入查询关键字",
		type: "input",
		enableFuzzy: false,
	},
	{
		name: "角色编号",
		key: "roleCode",
		placeholder: "请输入查询关键字",
		type: "input",
		enableFuzzy: false,
	}
]

const getQueryData = (queryData: any) => {
	console.log(queryData)
	getUsersTableData(queryData)
}

onMounted(() => {
	getUsersTableData({})
})

const userStore = useUserStore()
const { userInfo } = storeToRefs(userStore)
const getUsersTableData = (queryParams: any) => {
	console.log(queryParams, "queryParams")

	const params = {
		...pageInfo.value,
		companyId: userInfo.value.companyId,
		...queryParams
	}
	getBaseRoleList(params).then((res: any) => {
		if (res.rows && res.rows.length) {
			tableData.value = res.rows
		} else {
			tableData.value = []
		}
		pageInfo.value.total = res.records
	})
}

const btnList = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "保存",
		icon: ["fas", "floppy-disk"]
	}
]

const commonQueryRef = ref()
const userTableRef = ref()
const userSelect = ref([])
const emit = defineEmits(["save", "cancel"])
const onUserBtnClick = (btnName: string | undefined) => {
	pageInfo.value.currentPage = 1
	commonQueryRef.value.handleReset()

	if (btnName === "保存") {
		userSelect.value = userTableRef.value.getSelectedTable()

		userTableRef.value.clearSelectedTableData()
		emit("save", props.idTag, userSelect.value)
		return
	}
	if (btnName === "取消") {
		userTableRef.value.clearSelectedTableData()
		emit("cancel")
		return
	}
}
</script>

<template>
	<div class="common-from-wrapper">
		<Title :title="drawerTitle" />

		<Query
			ref="commonQueryRef"
			class="my-query"
			:queryArrList="queryArrList"
			@getQueryData="getQueryData"
		/>

		<div class="common-from-group">
			<PitayaTable
				ref="userTableRef"
				:table-data="tableData"
				:selectedTableData="selectRows"
				:columns="tableColumn"
				:needSelection="true"
				:total="pageInfo.total"
				@onCurrentPageChange="onCurrentPageChange"
				style="padding-bottom: 10px"
			/>
		</div>
		<div class="btn-groups">
			<ButtonList :button="btnList" @onBtnClick="onUserBtnClick" />
		</div>
	</div>
</template>

<style scoped lang="scss">
.common-from-wrapper {
	display: flex;
	flex-direction: column;
	height: 100%;
	.my-query {
		margin: 10px 10px 0;
	}
	.common-from-group {
		flex: 1;
	}
	.btn-groups {
		padding-top: 10px;
		padding-right: 10px;
		border-top: 1px solid var(--pitaya-border-color);
		display: flex;
		justify-content: flex-end;
	}
}
</style>
@/app/platform/store/modules/user @/app/platform/api/system/baseRole
