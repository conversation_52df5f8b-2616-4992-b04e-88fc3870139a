<template>
	<div class="containers-preview">
		<!-- <div>
			重置
			<font-awesome-icon
				@click="onClick"
				:icon="['fas', 'rotate']"
				style="color: #ccc"
				class="icon"
			/>
		</div> -->
		<ButtonList :button="btnList" class="resetBtn" @on-btn-click="onBtnClick" />
		<div
			class="canvas-preview"
			:class="processData ? 'with-panal' : ''"
			ref="canvas"
		/>
		<div class="panel" v-if="processData">
			<Title :title="containerRightTitle" />
			<el-descriptions
				class="margin-top"
				title="基础信息"
				:column="1"
				size="small"
				border
			>
				<el-descriptions-item label="流程名称">
					{{ processData.name || "---" }}</el-descriptions-item
				>
				<el-descriptions-item label="流程版本">
					{{ "V" + currentVersion.version || "---" }}</el-descriptions-item
				>
				<el-descriptions-item label="流程定义编码">
					{{ processData.code || "---" }}</el-descriptions-item
				>
				<el-descriptions-item label="发布时间">
					{{ processData.deploymentTime || "---" }}</el-descriptions-item
				>
			</el-descriptions>
			<!-- 用户节点处理 -->
			<el-descriptions
				v-if="currentNode && currentNode.$type === 'bpmn:UserTask'"
				class="margin-top"
				title="用户节点"
				:column="2"
				size="small"
				border
			>
				<el-descriptions-item label="节点名称" :span="2">
					{{ currentNode.name || "---" }}</el-descriptions-item
				>
				<el-descriptions-item
					:span="2"
					v-if="currentNode.$attrs['camunda:assignee']"
					label="审批人"
				>
					{{
						currentNode.$attrs["camunda:assignee"] || "---"
					}}</el-descriptions-item
				>
				<el-descriptions-item
					:span="2"
					v-if="currentNode.$attrs['camunda:candidateUsers']"
					label="候选用户"
				>
					{{
						currentNode.$attrs["camunda:candidateUsers"] || "---"
					}}</el-descriptions-item
				>
				<el-descriptions-item
					:span="2"
					v-if="currentNode.$attrs['camunda:candidateGroups']"
					label="候选组"
				>
					{{
						currentNode.$attrs["camunda:candidateGroups"] || "---"
					}}</el-descriptions-item
				>
				<template v-if="currentNode.loopCharacteristics">
					<el-descriptions-item
						:span="2"
						v-if="currentNode.loopCharacteristics.$attrs['camunda:collection']"
						label="会签人员集合"
					>
						{{
							currentNode.loopCharacteristics.$attrs["camunda:collection"] ||
							"---"
						}}</el-descriptions-item
					>
					<el-descriptions-item
						:span="2"
						v-if="currentNode.loopCharacteristics.loopCardinality"
						label="循环次数"
					>
						{{
							currentNode.loopCharacteristics.loopCardinality.body || "---"
						}}</el-descriptions-item
					>
				</template>
			</el-descriptions>
			<el-descriptions
				v-if="currentNode && currentNode.$type === 'bpmn:ServiceTask'"
				class="margin-top"
				title="服务节点"
				:column="2"
				size="small"
				border
			>
				<el-descriptions-item label="节点名称" :span="2">
					{{ currentNode.name || "---" }}</el-descriptions-item
				>
				<el-descriptions-item
					:span="2"
					v-if="currentNode.$attrs['camunda:type']"
					label="类型"
				>
					{{
						currentNode.$attrs["camunda:type"] || "---"
					}}</el-descriptions-item
				>
				<el-descriptions-item
					:span="2"
					v-if="currentNode.$attrs['camunda:topic']"
					label="订阅主题"
				>
					{{
						currentNode.$attrs["camunda:topic"] || "---"
					}}</el-descriptions-item
				>
			</el-descriptions>
			<el-descriptions
				v-if="currentNode && currentNode.$type === 'bpmn:SequenceFlow'"
				class="margin-top"
				title="顺序流"
				:column="2"
				size="small"
				border
			>
				<el-descriptions-item label="节点名称" :span="2">
					{{ currentNode.name || "---" }}</el-descriptions-item
				>
				<el-descriptions-item label="节点表达式" :span="2">
					{{
						currentNode.conditionExpression?.body || "---"
					}}</el-descriptions-item
				>
			</el-descriptions>
		</div>
	</div>
</template>
<script setup>
// 引入Bpmn相关的依赖
import BpmnModeler from "bpmn-js/lib/Modeler"
import { defaultXmlStr } from "./defaultXmlStr"
import {
	getProcessXML,
	commonDownloadFile,
	getProcessXMLHeightLight
} from "@/app/platform/api/system/processDesign"

const props = defineProps({
	xmlStrPath: {
		type: String
	},
	currentVersion: {
		type: Object
	},
	processData: {
		type: Object
	},
	processInstanceId: {
		type: String
	},
	processDefinitionId: {
		type: String
	}
})
const process = toRef(props, "process")
const bpmnModeler = ref(null)
const container = ref(null)
const row = ref({})
const canvas = ref(null)
const xmlStr = ref("")
const currentNode = ref({})
const containerRightTitle = {
	name: ["流程参数"],
	icon: ["fas", "square-share-nodes"]
}
const btnList = ref([
	{
		name: "重置",
		icon: ["fas", "rotate"]
	}
])

const init = async () => {
	if (props.processDefinitionId) {
		xmlStr.value = await getXmlUrlFromProcess()
	} else {
		xmlStr.value = await getXmlUrl()
	}
	nextTick(() => {
		initBpmn()
	})
}
const onClick = () => {
	init()
}
const getXmlUrlFromProcess = async () => {
	return new Promise((resolve) => {
		getProcessXML({
			processDefinitionId: props.processDefinitionId
		}).then((res) => {
			const reader = new FileReader()
			// 传入需要被转换的文本流 file,这个是转字符串的关键方法
			reader.readAsText(res)
			// onload是异步的,封装的话可以用promise
			reader.onload = () => {
				// 输出字符串
				resolve(reader.result)
			}
		})
	})
}

const getXmlUrl = () => {
	return new Promise((resolve) => {
		commonDownloadFile(props.xmlStrPath).then((res) => {
			const reader = new FileReader()
			// 传入需要被转换的文本流 file,这个是转字符串的关键方法
			reader.readAsText(res)
			// onload是异步的,封装的话可以用promise
			reader.onload = () => {
				// 输出字符串
				resolve(reader.result)
			}
		})
	})
}
const initBpmn = () => {
	// 获取到属性ref为“canvas”的dom节点
	const tempCanvas = canvas.value
	tempCanvas.innerHTML = ""
	// 建模
	bpmnModeler.value = new BpmnModeler({
		container: tempCanvas,
		additionalModules: [
			{
				paletteProvider: ["value", ""],
				labelEditingProvider: ["value", ""], //禁用节点编辑
				contextPadProvider: ["value", ""], //禁用图形菜单
				bendpoints: ["value", {}], //禁用连线拖动
				// zoomScroll: ['value', ''], //禁用滚动
				// moveCanvas: ['value', ''], //禁用拖动整个流程图
				move: ["value", ""] //禁用单个图形拖动
			}
		]
	})
	createDiagram().then(() => {
		addEventListener()
	})
}
const createDiagram = async () => {
	if (xmlStr.value === "") {
		xmlStr.value = defaultXmlStr(props.processData.code, props.processData.name)
	}
	try {
		const result = await bpmnModeler.value.importXML(xmlStr.value)
		const { warnings } = result
	} catch (err) {
		// console.log(err.message, err.warnings)
	}
	const tempCanvas = bpmnModeler.value.get("canvas")
	if (props.processInstanceId) {
		const params = { processDefinitionId: props.processInstanceId }
		getProcessXMLHeightLight(params).then((data) => {
			//高亮线
			data.highLine.forEach((e) => {
				if (e) {
					tempCanvas.addMarker(e, "highlight-line")
				}
			})
			//高亮任务
			data.highPoint.forEach((e) => {
				if (e) {
					tempCanvas.addMarker(e, "highlight")
				}
			})
			//高亮我执行过的任务
			data.iDo.forEach((e) => {
				if (e) {
					tempCanvas.addMarker(e, "highlightIDO")
				}
			})
			//高亮下一个任务
			data.waitingToDo.forEach((e) => {
				if (e) {
					tempCanvas.addMarker(e, "highlightTODO")
				}
			})
		})
	}
	// }
	// 让图能自适应屏幕
	tempCanvas.zoom("fit-viewport")
	bpmnModeler.value.on("element.hover", (e) => {
		if (e.element.type === "bpmn:UserTask") {
		}
	})
}
const addEventListener = () => {
	const eventBus = bpmnModeler.value.get("eventBus") // 需要使用 eventBus
	const eventType = ["element.click"] //
	eventType.forEach((eventType) => {
		eventBus.on(eventType, (e) => {
			switch (e.element.type) {
				case "bpmn:Process": {
					// console.log("监听到事件--->", e)
					break
				}
				case "bpmn:UserTask": {
					currentNode.value = e.element.businessObject
					// console.log(currentNode.value, "当前userTask")
					break
				}
				case "bpmn:SequenceFlow": {
					currentNode.value = e.element.businessObject
					// console.log(currentNode.value, "当前SequenceFlow")
					break
				}
				case "bpmn:ServiceTask": {
					currentNode.value = e.element.businessObject
					// console.log(currentNode.value, "当前serviceTask")
					break
				}
				default: {
					// console.log("未识别点击事件-->", e.element.type)
					// console.log("事件-->", e.element.businessObject)
					currentNode.value = {}
				}
			}
		})
	})
}

const onBtnClick = (btnName) => {
	if (btnName) {
		init()
	}
}
const download = async () => {
	const result = await bpmnModeler.value.saveXML({
		format: true
	})
	const { xml } = result
	const name = "process"
	const element = document.createElement("a")
	element.setAttribute(
		"href",
		"data:text/xml;charset=utf-8," + encodeURIComponent(xml)
	)
	element.setAttribute("download", name + ".bpmn")
	element.style.display = "none"
	element.click()
}
onMounted(() => {
	init()
})
defineExpose({
	download
})
</script>

<style lang="scss" scoped>
.icon:hover {
	color: rgb(32, 74, 156, 0.9) !important;
	cursor: pointer;
}

.containers-preview {
	background-color: #ffffff;
	width: 100%;
	height: calc(100% - 20px);
	display: flex;
}

.canvas-preview {
	width: 100%;
	height: calc(100vh - 42px);
	margin-top: 42px;
}

.with-panal {
	width: calc(100% - 295px) !important;
}

.bjs-powered-by {
	display: none;
}

.panel {
	width: 310px;
	border: solid #ccc;
	border-width: 0 0 0 1px;
	padding: 0 0 0 10px;
}

.margin-top {
	padding: 0 10px;
}

.buttons {
	position: absolute;
	right: 420px;
	top: 55px;
}

.buttons .button {
	margin: 0px 5px;
}

:deep(.djs-palette .entry) {
	float: none !important;
}

:deep(.djs-palette.two-column.open) {
	width: 47px !important;
}

:deep(.highlight-line .djs-visual > :nth-child(1)) {
	stroke: green !important;
}

:deep(.highlight .djs-visual > :nth-child(1)) {
	stroke: green !important;
	fill: rgba(0, 80, 0, 0.4) !important;
}

:deep(.highlightIDO .djs-visual > :nth-child(1)) {
	stroke: green !important;
	fill: rgba(0, 80, 0, 0.4) !important;
}

:deep(.highlightTODO .djs-visual > :nth-child(1)) {
	stroke: rgb(255, 0, 0) !important;
	fill: rgba(255, 255, 255, 0.4) !important;
}

:deep(.bjs-powered-by) {
	display: none !important;
}

.el-descriptions {
	margin: 10px 0;
	color: var(--pitaya-table-font-color);

	:deep(.el-descriptions__header) {
		margin-bottom: 10px;
	}

	:deep(.el-descriptions__title) {
		color: var(--pitaya-table-font-color) !important;
		font-size: 14px;
	}

	:deep(.el-descriptions__cell) {
		border: 1px solid var(--pitaya-border-color) !important;
		color: var(--pitaya-table-font-color) !important;

		&.title {
			border: none !important;
			background: transparent;
			font-size: 14px;
			font-weight: bold;
			padding: 10px 0px !important;

			~ * {
				border: none !important;
				background: transparent;
			}
		}
	}

	:deep(.el-descriptions__label) {
		width: 103px;
		padding: 0 10px;
		background-color: #f6f6f6;
		height: 100%;
		min-height: 31px;
	}

	:deep(.el-descriptions__content) {
		max-width: calc(25% - 120px);
	}
}

// 重置按钮
.resetBtn {
	position: absolute;
	top: 0;
	left: 10px;
	z-index: 10;
}
</style>
