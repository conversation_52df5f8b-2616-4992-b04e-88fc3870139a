<script setup lang="ts">
import { find } from "lodash-es"
import { getAllPageUsers } from "@/app/platform/api/system/baseRole"
import { useUserStore } from "@/app/platform/store/modules/user"
import { handleClose } from "../../location/departmentShow"

const drawerTitle = ref({
	name: ["选人"],
	icon: ["fas", "square-share-nodes"]
})
interface Props {
	titel: any
	idTag: string
	selectRows: any[]
}
const props = defineProps<Props>()

const tableData = ref<any[]>([])
const tableColumn = ref<any[]>([])
const tableSelectSingleMode = ref<boolean>(true)
const pageInfo = ref<any>({
	pageNum: 1,
	pageSize: 20,
	total: 0
})

const onCurrentPageChange = (pageData: any) => {
	pageInfo.value.pageSize = pageData.pageSize
	pageInfo.value.currentPage = pageData.currentPage
	getUsersTableData()
}

const queryArrList = [
	{
		name: "用户姓名",
		key: "realname",
		placeholder: "请输入查询关键字",
		type: "input"
	},
	{
		name: "用户账号",
		key: "username",
		placeholder: "请输入查询关键字",
		type: "input"
	},
	{
		name: "所属部门",
		key: "orgName",
		placeholder: "请输入查询关键字",
		type: "input"
	},
	{
		name: "所属班组",
		key: "teamdName",
		placeholder: "请输入查询关键字",
		type: "input"
	}
]

const getQueryData = (queryData: queryObj) => {
	console.log(queryData)
	userTableRef.value.resetPageInfo()
	getUsersTableData(queryData)
}

watch(
	() => props.titel,
	(val: any) => {
		drawerTitle.value.name = [val]
		switch (val) {
			case "选择审批人":
				tableColumn.value = [
					{ prop: "username", label: "用户账号", width: 140 },
					{ prop: "realname", label: "用户姓名", width: 100 },
					{ prop: "orgName", label: "部门", minWidth: 200 },
					{ prop: "teamdName", label: "班组", minWidth: 200 }
				]
				tableSelectSingleMode.value = true
				break
			case "选择候选用户":
				tableColumn.value = [
					{ prop: "username", label: "用户账号", width: 140 },
					{ prop: "realname", label: "用户姓名", width: 100 },
					{ prop: "orgName", label: "部门", minWidth: 200 },
					{ prop: "teamdName", label: "班组", minWidth: 200 }
				]
				tableSelectSingleMode.value = false
				break

			default:
				break
		}
	},
	{ immediate: true }
)

const selectRows = ref<any>([])
watch(
	() => props.selectRows,
	(rows: any) => {
		selectRows.value = rows || []
		if (tableData.value.length == 0) {
			setTimeout(() => {
				selectRows.value.forEach((row: any) => {
					nextTick(() => {
						const findItem = find(tableData.value, (item) => {
							return item.username == row.username
						})
						findItem &&
							userTableRef.value!.pitayaTableRef!.toggleRowSelection(
								findItem,
								true
							)
					})
				})
			}, 1000)
		} else {
			selectRows.value.forEach((row: any) => {
				nextTick(() => {
					const findItem = find(tableData.value, (item) => {
						return item.username == row.username
					})
					findItem &&
						userTableRef.value!.pitayaTableRef!.toggleRowSelection(
							findItem,
							true
						)
				})
			})
		}
	},
	{ immediate: true, deep: true }
)

onMounted(() => {
	getUsersTableData()
})

const userStore = useUserStore()
const { userInfo } = storeToRefs(userStore)
const getUsersTableData = (queryParams) => {
	const params = {
		...pageInfo.value,
		companyId: userInfo.value.companyId,
		...queryParams
	}
	getAllPageUsers(params).then((res: any) => {
		if (res.rows && res.rows.length) {
			tableData.value = res.rows
		} else {
			tableData.value = []
		}
		// 找到列表中勾选项
		selectRows.value.forEach((row: any) => {
			nextTick(() => {
				const findItem = find(tableData.value, (item) => {
					return item.id == row.id
				})
				findItem &&
					userTableRef.value!.pitayaTableRef!.toggleRowSelection(findItem, true)
			})
		})
		pageInfo.value.total = res.records
	})
}

const btnList = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "保存",
		icon: ["fas", "floppy-disk"]
	}
]
const commonQueryRef = ref()
const userTableRef = ref()
const userSelect = ref([])
const emit = defineEmits(["save", "cancel"])
const onUserBtnClick = (btnName: string | undefined) => {
	// 置空勾选值
	selectRows.value = []
	commonQueryRef.value.handleReset()

	if (btnName === "保存") {
		userSelect.value = userTableRef.value.getSelectedTable()
		userTableRef.value.clearSelectedTableData()
		emit("save", props.idTag, userSelect.value)
		return
	}
	if (btnName === "取消") {
		userTableRef.value.recoveryOperation()
		userTableRef.value.clearSelectedTableData()
		emit("cancel")
		return
	}
}

function onSelectionChange(params: any) {
	console.log("onSelectionChange", params)
}
</script>

<template>
	<div class="common-from-wrapper">
		<Title :title="drawerTitle" />

		<Query
			ref="commonQueryRef"
			class="my-query"
			:queryArrList="queryArrList"
			@getQueryData="getQueryData"
		/>

		<div class="common-from-group">
			<PitayaTable2
				ref="userTableRef"
				select-key="username"
				:table-data="tableData"
				:columns="tableColumn"
				:need-index="true"
				:need-pagination="true"
				:total="pageInfo.total"
				:single-select="tableSelectSingleMode"
				:need-selection="true"
				:selectedTableData="selectRows"
				@on-current-page-change="onCurrentPageChange"
			>
				<template #name="{ rowData }">
					<div
						class="line-name-container"
						:style="{
							backgroundColor: rowData.colour,
							borderColor: rowData.colour
						}"
					>
						{{ rowData.name }}
					</div>
				</template>
			</PitayaTable2>
		</div>
		<div class="btn-groups">
			<ButtonList :button="btnList" @onBtnClick="onUserBtnClick" />
		</div>
	</div>
</template>

<style scoped lang="scss">
.common-from-wrapper {
	display: flex;
	flex-direction: column;
	height: 100%;

	.my-query {
		margin: 10px 10px 0;
	}
	.common-from-group {
		flex: 1;
	}
	.btn-groups {
		padding-top: 10px;
		border-top: 1px solid var(--pitaya-border-color);
		display: flex;
		justify-content: flex-end;
	}
}
</style>
@/app/platform/store/modules/user @/app/platform/api/system/baseRole
