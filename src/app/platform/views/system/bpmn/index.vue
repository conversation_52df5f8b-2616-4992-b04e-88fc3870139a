<template>
	<div style="padding:0 320px 0 10px">
		<Title :title="containerDesignTitle">
			<Tabs
				@onTabChange="onTabChange"
				style="position: absolute; left: 124px"
				:tabs="['设计', '源码']"
			/>
		</Title>
		<div class="containers-bpmn" v-if="activeTab === 0">
			<div class="canvas" ref="canvas" id="bpmn-canvas" />
			<div class="panel">
				<Title :title="processTitle" />
				<el-scrollbar style="height: calc(100vh - 55px)">
					<div id="js-properties-panel" />
				</el-scrollbar>
				<!-- <div class="buttons">
				<el-button class="button" type="primary" @click="download()"
					>下载</el-button
				>
				<el-button class="button" type="primary" @click="uploadServe()"
					>保存</el-button
				>
			</div> -->
			</div>
			<Drawer
				:size="drawerSize"
				v-model:drawer="showDrawer"
				@open="handlerOpen"
				:close-on-click-modal="closeMode"
				:destroyOnClose="true"
			>
				<UserSelectDrawerV2
					v-if="drawerTitle != '选择候选角色'"
					:idTag="idTag"
					:titel="drawerTitle"
					:selectRows="selectRows"
					@save="handleSave"
					@cancel="handlerCancel"
				/>
				<!-- <RoleSelectDrawer
				v-if="drawerTitle == '选择候选角色'"
				:idTag="idTag"
				:titel="drawerTitle"
				:selectRows="selectRows"
				@save="handleSave"
				@cancel="handlerCancel"
			/> -->
				<RoleSelectDrawerV2
					v-if="drawerTitle == '选择候选角色'"
					:idTag="idTag"
					:titel="drawerTitle"
					:selectRows="selectRows"
					@save="handleSave"
					@cancel="handlerCancel"
				/>
			</Drawer>
		</div>
		<div v-if="activeTab === 1" style="height: calc(100vh - 75px)" class="scroll_bar">
			<el-input
				type="textarea"
				:autosize="{ minRows: 2, maxRows: 30 }"
				:rows="30"
				v-model="xmlStr"
			/>
		</div>
	</div>
</template>
<script>
// 引入Bpmn相关的依赖
import BpmnModeler from "bpmn-js/lib/Modeler"
// 这里引入的是右侧属性栏这个框
import propertiesPanelModule from "bpmn-js-properties-panel"
// 而这个引入的是右侧属性栏里的内容
import propertiesProviderModule from "bpmn-js-properties-panel/lib/provider/camunda"
// 右侧属性栏扩展，不然报错
import camundaModdleDescriptor from "camunda-bpmn-moddle/resources/camunda"
// 汉化组件
import customTranslate from "./customTranslate/customTranslate"
const customTranslateModule = {
	translate: ["value", customTranslate]
}
import { defaultXmlStr } from "./defaultXmlStr"
import UserSelectDrawerV2 from "./UserSelectDrawerV2.vue"
import RoleSelectDrawerV2 from "./RoleSelectDrawerV2.vue"
import { map } from "lodash-es"
import {
	getProcessXML,
	deployProcessDef
} from "@/app/platform/api/system/processDesign"
import x2js from "x2js"
import { events } from "@/app/platform/utils/bus"

export default {
	name: "Bpmn",
	emits: ["camundaOclick"],
	components: {
		RoleSelectDrawerV2,
		UserSelectDrawerV2
	},
	props: {
		processDefinitionId: { default: "-1" }
	},
	data() {
		return {
			containerDesignTitle: {
				name: ["流程设计"],
				icon: ["fas", "square-share-nodes"]
			},
			activeTab: 0,
			// bpmn建模器
			bpmnModeler: null,
			container: null,
			row: {},
			canvas: null,
			xmlStr: "",
			shape: null,
			drawerSize: 930,
			showDrawer: false,
			drawerTitle: "选择",
			processTitle: {
				name: ["流程参数"],
				icon: ["fas", "square-share-nodes"]
			},
			idTag: "",
			// 传递给表格勾选使用
			selectRows: [],
			// 存放用户节点
			userTask: {},
			currentTaskId: "",
			// 存放3组被选择的数据
			// assigneeRows: [],
			// candidateUsersRows: [],
			// candidateGroupsRows: [],
			closeMode: true,
			newProcessDefinitionKey: "Process"
		}
	},
	mounted() {
		this.init()
		// setTimeout(() => {
		//   document.getElementById('camunda-id').classList.remove('invalid');
		//   document.querySelector('.bpp-error-message').style.display = 'none';
		// }, 500);
		this.success()
	},
	// 方法集合
	methods: {
		// 选人弹窗 保存
		handleSave(idTag, selectData) {
			let tempUsers
			switch (idTag) {
				case "camunda-assignee":
					this.userTask[this.currentTaskId].assigneeRows = selectData
					tempUsers = map(selectData, "username").join(",")
					this.camundaOnClick("1", selectData)
					break
				case "camunda-candidateUsers":
					this.userTask[this.currentTaskId].candidateUsersRows = selectData
					tempUsers = map(selectData, "username").join(",")
					this.camundaOnClick("2", selectData)
					break
				case "camunda-candidateGroups":
					this.userTask[this.currentTaskId].candidateGroupsRows = selectData
					tempUsers = map(selectData, "id").join(",")
					this.camundaOnClick("3", selectData)
					break
				default:
					break
			}
			
			// document.getElementById(idTag).value = tempUsers
			this.showDrawer = false
		},
		handlerOpen() {
			// console.log("open", this.selectRows)
		},
		// 选人弹窗 取消
		handlerCancel() {
			this.showDrawer = false
			this.selectRows = []
		},
		success() {
			// console.log("创建成功")
		},
		// 监听 modeler
		addEventBusListener() {
			const eventBus = this.bpmnModeler.get("eventBus") // 需要使用 eventBus
			const eventType = ["element.click", "element.changed"] // 需要监听的事件集合
			const that = this
			eventType.forEach((eventType) => {
				eventBus.on(eventType, (e) => {
					if (e.element.type === "bpmn:Process") {
						that.newProcessDefinitionKey = e.element.id
					}
					if (e.element.type === "bpmn:UserTask") {
						that.currentTaskId = e.element.id
						setTimeout(function () {
							const buttonsOclick = document.getElementById("buttons")
							const assignee = document.getElementById("camunda-assignee")
							const candidateUsers = document.getElementById(
								"camunda-candidateUsers"
							)
							const candidateGroups = document.getElementById(
								"camunda-candidateGroups"
							)

							if (assignee) {
								assignee.parentNode.children[1].style.display = "none"
								assignee.disabled = true
							}
							if (candidateUsers) {
								candidateUsers.parentNode.children[1].style.display = "none"
								candidateUsers.disabled = true
							}
							if (candidateGroups) {
								candidateGroups.parentNode.children[1].style.display = "none"
								candidateGroups.disabled = true
							}
							// if (!buttonsOclick) {
							if (assignee) {
								if (!that.userTask[that.currentTaskId]) {
									that.userTask[that.currentTaskId] = {}
								}
								if (!that.userTask[that.currentTaskId].assigneeRows) {
									that.userTask[that.currentTaskId].assigneeRows = []
								}
								assignee.value = map(
									that.userTask[that.currentTaskId].assigneeRows,
									"realname"
								).join(",")
								assignee.classList.add("w80")
								assignee.insertAdjacentHTML(
									"afterend",
									'<button class="button" id="buttons"  style="width: 50px;border-color: #1890ff;background: #1890ff;color: #fff;">选择</button>'
								)
							}
							// 候选用户
							// const name = document.getElementById('camunda-candidateUsers');
							if (candidateUsers) {
								if (!that.userTask[that.currentTaskId]) {
									that.userTask[that.currentTaskId] = {}
								}
								if (!that.userTask[that.currentTaskId].candidateUsersRows) {
									that.userTask[that.currentTaskId].candidateUsersRows = []
								}
								candidateUsers.value = map(
									that.userTask[that.currentTaskId].candidateUsersRows,
									"realname"
								).join(",")
								candidateUsers.classList.add("w80")
								candidateUsers.insertAdjacentHTML(
									"afterend",
									'<button class="button" id="buttonsUsers"  style="width: 50px;border-color: #1890ff;background: #1890ff;color: #fff;">选择</button>'
								)
							}
							// 候选组
							if (candidateGroups) {
								if (!that.userTask[that.currentTaskId]) {
									that.userTask[that.currentTaskId] = {}
								}
								if (!that.userTask[that.currentTaskId].candidateGroupsRows) {
									that.userTask[that.currentTaskId].candidateGroupsRows = []
								}
								candidateGroups.value = map(
									that.userTask[that.currentTaskId].candidateGroupsRows,
									"roleName"
								).join(",")
								candidateGroups.classList.add("w80")
								candidateGroups.insertAdjacentHTML(
									"afterend",
									'<button class="button" id="buttonsGroups"  style="width: 50px;border-color: #1890ff;background: #1890ff;color: #fff;">选择</button>'
								)
							}
							const buttons = document.getElementById("buttons")
							const buttonsUsers = document.getElementById("buttonsUsers")
							const buttonsGroups = document.getElementById("buttonsGroups")
							// eslint-disable-next-line @typescript-eslint/no-this-alias
							if (buttons && buttonsUsers && buttonsGroups) {
								buttons.addEventListener("click", function () {
									that.drawerTitle = "选择审批人"
									that.idTag = "camunda-assignee"
									that.selectRows = JSON.parse(
										JSON.stringify(
											that.userTask[that.currentTaskId].assigneeRows
										)
									)
									that.showDrawer = true
								})
								buttonsUsers.addEventListener("click", function () {
									that.drawerTitle = "选择候选用户"
									that.idTag = "camunda-candidateUsers"
									that.selectRows = JSON.parse(
										JSON.stringify(
											that.userTask[that.currentTaskId].candidateUsersRows
										)
									)
									that.showDrawer = true
								})
								buttonsGroups.addEventListener("click", function () {
									that.drawerTitle = "选择候选角色"
									that.idTag = "camunda-candidateGroups"
									that.selectRows = JSON.parse(
										JSON.stringify(
											that.userTask[that.currentTaskId].candidateGroupsRows
										)
									)
									that.showDrawer = true
								})
							}
							// }
						}, 200)
					}
				})
			})
		},
		camundaOnClick(item, value) {
			this.$emit("camundaOclick", value, item)
			this.onSelectValues(null, value, item)
		},
		async init() {
			if (this.processDefinitionId != "-1") {
				this.xmlStr = await this.getXmlUrl()
			}
			this.$nextTick(() => {
				this.initBpmn()
				setTimeout(() => {
					if (document.getElementById("camunda-id")) {
						document.getElementById("camunda-id").classList.remove("invalid")
						document.querySelector(".bpp-error-message").style.display = "none"
					}
				}, 500)
			})
		},
		getXmlUrl() {
			const that = this
			return new Promise((resolve) => {
				getProcessXML({
					processDefinitionId: that.processDefinitionId
				}).then((res) => {
					// const xml = new Blob([res], { type: "application/vnd.ms-excel" })
					// console.log("xml", xml)
					const reader = new FileReader()
					// 传入需要被转换的文本流 file,这个是转字符串的关键方法
					reader.readAsText(res)
					// onload是异步的,封装的话可以用promise
					reader.onload = () => {
						// 输出字符串
						resolve(reader.result)
						const resultJson = new x2js().xml2js(reader.result)
						let pickResult = resultJson.definitions.process.userTask
						if (!(pickResult instanceof Array)) pickResult = [pickResult]

						pickResult.forEach((item) => {
							that.userTask[item._id] = {}
							const jsonString = JSON.stringify(item).replace(/_camunda:/g, "")
							const jsonObj = JSON.parse(jsonString)
							that.dealwithSelectObject(
								item._id,
								"assigneeRows",
								jsonObj["assignee"]
							)
							that.dealwithSelectObject(
								item._id,
								"candidateUsersRows",
								jsonObj["candidateUsers"]
							)
							that.dealwithSelectObject(
								item._id,
								"candidateGroupsRows",
								jsonObj["candidateGroups"]
							)
						})
					}
				})
				// setTimeout(() => {
				// 	const url = ""
				// 	resolve(url)
				// }, 1000)
			})
		},
		/**
		 *
		 * @param id 对应流程属性id
		 * @param key 指定id下分组key值，如candidateGroupsRows，candidateUsersRows，assigneeRows
		 * @param val 逗号分隔的value值
		 */
		dealwithSelectObject(id, key, val) {
			this.userTask[id][key] = []
			if (val) {
				val.split(",").forEach((row) => {
					const temp =
						key == "candidateGroupsRows"
							? {
									id: row,
									roleName: row
							  }
							: {
									username: row,
									realname: row
							  }
					this.userTask[id][key].push(temp)
				})
			}
		},
		initBpmn() {
			// 获取到属性ref为“canvas”的dom节点
			const canvas = this.$refs.canvas
			canvas.innerHTML = ""
			// 建模
			this.bpmnModeler = new BpmnModeler({
				container: canvas,
				//添加左侧控制板
				propertiesPanel: {
					parent: "#js-properties-panel"
				},
				additionalModules: [
					//添加右侧属性面板
					propertiesProviderModule,
					propertiesPanelModule,
					//汉化
					customTranslateModule
				],
				moddleExtensions: {
					//如果要在属性面板中修改属性，必须添加
					camunda: camundaModdleDescriptor
				}
			})
			this.createDiagram()
			this.addEventBusListener()
		},
		async createDiagram() {
			if (this.xmlStr === "") {
				this.xmlStr = defaultXmlStr
					.replaceAll("#{process_def_key}", "process_def_" + Date.now())
					.replaceAll("#{start_event_id}", "start_event_" + Date.now())
			}
			try {
				const result = await this.bpmnModeler.importXML(this.xmlStr)
			} catch (err) {}
			// 让图能自适应屏幕
			const canvas = this.bpmnModeler.get("canvas")
			canvas.zoom("fit-viewport")
			const res = {
				waitingToDo: ["Activity_1q9tiem"],
				highPoint: ["Activity_1t7kzyu", "StartEvent_1"],
				iDo: ["Activity_1t7kzyu"],
				highLine: ["Flow_0zplpf5", "Flow_07hlmiu"]
			}
			res.highLine.forEach((e) => {
				if (e) {
					canvas.addMarker(e, "highlight")
					const ele = document
						.querySelector(".highlight")
						.querySelector(".djs-visual rect")
					if (ele) {
						ele.setAttribute("stroke-dasharray", "4,4")
					}
				}
			})
			//高亮任务
			res.highPoint.forEach((e) => {
				if (e) {
					canvas.addMarker(e, "highlight")
				}
			})
			//高亮我执行过的任务
			res.iDo.forEach((e) => {
				if (e) {
					canvas.addMarker(e, "highlightIDO")
				}
			})
			//高亮下一个任务
			res.waitingToDo.forEach((e) => {
				if (e) {
					canvas.addMarker(e, "highlightTODO")
				}
			})
			const isNew = this.processDefinitionId == "-1"
			this.bpmnModeler.on("element.click", (e) => {
				const elementRegistry = this.bpmnModeler.get("elementRegistry")
				this.shape = e.element ? elementRegistry.get(e.element.id) : e.shape
				setTimeout(() => {
					document.getElementById("camunda-id").classList.remove("invalid")
					this.newProcessDefinitionKey =
						document.getElementById("camunda-id").value
					document.querySelector(".bpp-error-message").style.display = "none"
					if (!isNew) {
						// 如果是历史流程，不允许修改流程定义Key
						document.getElementById("camunda-id").readOnly = true
					}
				}, 300)
			})
		},
		add() {
			this.init()
		},
		onTabChange(index) {
			this.activeTab = index
			// 刷新设计页面
			if (this.activeTab === 0) {
				this.$nextTick(() => {
					this.initBpmn()
				})
			}
		},
		onSelectValues(options, values, item) {
			if (item == "1") {
				const names = map(values, "username").join(",")
				document.getElementById("camunda-assignee").value = names
				const element = document.getElementById("camunda-assignee")
				// 创建一个新的事件
				const event = document.createEvent("Event")
				// 触发事件
				event.initEvent("change", true, false)
				element.dispatchEvent(event)
			} else if (item == "2") {
				const names = map(values, "username").join(",")
				document.getElementById("camunda-candidateUsers").value = names
				const element = document.getElementById("camunda-candidateUsers")
				// 创建一个新的事件
				const event = document.createEvent("Event")
				// 触发事件
				event.initEvent("change", true, false)
				element.dispatchEvent(event)
			} else {
				const names = map(values, "id").join(",")
				document.getElementById("camunda-candidateGroups").value = names
				console.log("names", names)
				const element = document.getElementById("camunda-candidateGroups")
				// 创建一个新的事件
				const event = document.createEvent("Event")
				// 触发事件
				event.initEvent("change", true, false)
				element.dispatchEvent(event)
			}
		},
		async download() {
			const result = await this.bpmnModeler.saveXML({
				format: true
			})
			const { xml } = result
			console.log(xml)
			const name = document.getElementById("camunda-name").innerHTML
			const element = document.createElement("a")
			element.setAttribute(
				"href",
				"data:text/xml;charset=utf-8," + encodeURIComponent(xml)
			)
			element.setAttribute("download", name + ".bpmn")
			element.style.display = "none"
			element.click()
		},
		// 上传到服务器
		async uploadServe() {
			const { xml } = await this.bpmnModeler.saveXML({
				format: true
			})
			console.log("上传到服务器", xml)
			const blob = new Blob([xml], { type: "text/plain;charset=utf-8" })
			const formData = new FormData()
			const name = document.getElementById("camunda-name").innerHTML
			formData.append(
				"multipartFile",
				new File([blob], name + ".bpmn", { type: "text/plain" })
			)
			// 流程定义新增时要判断流程定义Key是否重复
			if (this.processDefinitionId == "-1") {
				formData.append("procDefKey", this.newProcessDefinitionKey)
			}
			// if (this.$route.query.id) {
			// 	formData.append("id", this.$route.query.id)
			// }
			deployProcessDef(formData).then((res) => {
				if (res) {
					ElMessage({
						message: "上传成功",
						type: "success"
					})
					// that.$emit("refershDesignTable")
					// processDefinitionId == -1时，表示新增，需要回传procDefKey
					if (this.processDefinitionId == "-1") {
						this.$emit("onSuccess", this.newProcessDefinitionKey)
					} else {
						this.$emit("onSuccess")
					}
				} else {
					ElMessage.error("上传失败")
				}
			})
		},
		clearDiagram() {
			this.xmlStr = ""
			this.initBpmn()
		},
		updata(row) {
			const params = { id: row.id }
			this.row = row
			// const res = defHttp.get({ url: "/oa/actReModel/queryById", params })
			// res.then((data) => {
			// 	if (data) {
			// 		this.xmlStr = data.xmlStr
			// 		this.$nextTick(() => {
			// 			this.initBpmn()
			// 		})
			// 	}
			// })
		},
		// 下载为bpmn格式,done是个函数，调用的时候传入的
		async saveDiagram() {
			try {
				const result = await this.bpmnModeler.saveXML({
					format: true
				})
				const { xml } = result
				const params = { xmlStr: xml }
				const res = null
				// if (this.row.id) {
				// 	params.id = this.row.id
				// 	res = defHttp.post({ url: "/oa/actReModel/edit", params })
				// } else {
				// 	res = defHttp.post({ url: "/oa/actReModel/add", params })
				// }
				// res.then((data) => {
				// 	console.log(data)
				// 	this.$emit("reloadList")
				// })
			} catch (err) {
				console.log("err", err)
			}
		}
	}
}
</script>
<style lang="scss" scoped>
.containers-bpmn {
	position: static;
	background-color: #ffffff;
	width: 100%;
	font-size: var(--pitaya-fs-12) !important;
	height: calc(100vh - 20px) !important;
	:deep(svg) {
		position: static !important;
	}
}
.containers-bpmn::before,
.containers-bpmn::after {
	position: static !important;
}

.canvas {
	width: 100%;
	height: 100%;
}
// .panel-title {
// 	position: absolute;
// 	right: 20px;
// 	top: 0px;
// 	width: 300px;
// }
.panel {
	position: absolute;
	right: 0px;
	top: -1px;
	width: 310px;
	padding: 0 0 0 10px;
	height: calc(100vh - 10px);
	background-color: hsl(0, 0%, 100%);
	border: solid #ccc;
	border-width: 0 0 0 1px;
	overflow-y: auto;
	:deep(.bpp-properties-panel) {
		background-color: #ffffff;
	}
	:deep(a) {
		background-color: #ffffff;
	}
	:deep(.bpp-properties-group) {
		border-bottom: #eee solid;
	}

	:deep(label) {
		font-weight: normal;
		line-height: 1;
	}
	:deep(.button) {
		margin-top: 5px;
		border-radius: 4px;
		line-height: 1;
		height: 32px;
		white-space: nowrap;
		background-color: var(--pitaya-active-btn-background) !important;
		color: #ffffff !important;
	}
}

.buttons {
	display: flex;
	justify-content: flex-end;
	border-top: 1px solid #ccc;
	padding-top: 10px;
	box-sizing: border-box;
}
:deep(.bjs-powered-by) {
	display: none !important;
}
.buttons .button {
	margin: 0px 5px;
}
:deep(.djs-palette) {
	top: 10px !important;
	left: 10px !important;
}
.djs-palette .entry {
	float: none !important;
}
.djs-palette.two-column.open {
	width: 47px !important;
}
.w80 {
	width: 80% !important;
	float: left;
}

/** 美化原生滚动条 */
.scroll_bar{
	:deep(.el-textarea__inner){
			// 整个滚动条
	&::-webkit-scrollbar {
		width: 8px;
		height: 8px;
	}
	// 滚动条上的滚动滑块
	&::-webkit-scrollbar-thumb {
		border-radius: 4px;
		background-color: #90939955;
	}
	&::-webkit-scrollbar-thumb:hover {
		background-color: #90939977;
	}
	&::-webkit-scrollbar-thumb:active {
		background-color: #90939999;
	}
	// 当同时有垂直滚动条和水平滚动条时交汇的部分
	&::-webkit-scrollbar-corner {
		background-color: transparent;
	}
	}
}
</style>
