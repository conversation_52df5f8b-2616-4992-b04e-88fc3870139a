<script setup lang="ts">
import {
	createProcessInfo,
	editProcessInfo
} from "@/app/platform/api/system/processDesign"
import { baseUserApi } from "@/app/platform/api/system/baseUser"
import { useUserStore } from "@/app/platform/store/modules/user"
import {
	validateProcessCode
} from "@/app/platform/utils/validate"
interface Props {
	dictionaryList: any[]
}

const props = withDefaults(defineProps<Props>(), {
	dictionaryList: () => []
})

const { dictionaryList } = toRefs(props)

const showDrawer = ref(false)
const formRef = ref()
const emit = defineEmits(["success"])

// 公司下拉列表
const companyList = ref<any[]>([])
const footerBtnList = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "保存",
		icon: ["fas", "floppy-disk"]
	}
]
const { userInfo } = storeToRefs(useUserStore())
const footerBtnClick = (btnName: string | undefined) => {
	if (btnName === "保存") {
		formRef.value.validate((valid: any) => {
			if (valid) {
				const data = {
					id:form.value.id,
					name: form.value.name,
					code: form.value.code,
					formUrlPath: form.value.formUrlPath,
					companyId: form.value.companyId,
					processRemark: form.value.processRemark
				}
				// 流程名称加上登录用户所属公司前缀
				if (company.value.label) {
					data.name = company.value.label + "-" + data.name
					data.code = data.code + "_" + company.value.code
				}
				if (editFlag.value) {
					//编辑
					editProcessInfo(data).then((res) => {
						if (res) {
							ElMessage.success("保存成功")
							showDrawer.value = false
							returnData.value = res
						} else {
							ElMessage.error(res.msg)
						}
					})
				} else {
					//新增
					createProcessInfo(data).then((res) => {
						if (res) {
							ElMessage.success("保存成功")
							showDrawer.value = false
							returnData.value = res
						} else {
							ElMessage.error(res.msg)
						}
					})
				}
			}
		})
	} else {
		showDrawer.value = false
	}
}

const form = ref<any>({})
const rules = {
	name: [
		{
			required: true,
			message: "请输入流程名称",
			trigger: "blur"
		},
	],
	code: [
		{
			required: true,
			message: "请输入流程编码",
			trigger: "blur"
		},
		{ validator: validateProcessCode, required: true, trigger: "blur" }
	],
	formUrlPath: [
		{
			required: true,
			message: "请输入表单页面",
			trigger: "blur"
		}
	],
	companyId: [{ required: true, message: "请选择公司", trigger: "change" }]
}

// 获取全部公司列表
function getAllCompany() {
	baseUserApi.getAllCompany().then((res: any) => {
		if (res && res.length > 0) {
			companyList.value = res.map((item: anyKey) => {
				return {
					label: item.companyName,
					value: item.id,
					code: item.companyCode
				}
			})
		} else {
			companyList.value = []
		}
	})
}

// 公司暂存信息
const company = ref<any>({})
const returnData = ref({})
// 回调：公司选择
const onCompanyClick = (item: any) => {
	company.value = Object.assign({}, item)
}

const editFlag = ref(false)
const open = (data: any) => {
	if (data) {
		editFlag.value = true
		form.value = data
	}else{
		editFlag.value = false
		form.value = {}
	}
	showDrawer.value = true
	getAllCompany()
}
const handleClose = () => {
	formRef.value.resetFields()
	if (returnData.value) {
		emit("success", returnData.value)
	} else {
		emit("success")
	}
	returnData.value = {}
}

defineExpose({
	open
})
</script>
<template>
	<NewDrawer
		:size="310"
		:title="{
			name: [`${editFlag?'编辑流程':'新建流程'}`],
			icon: ['fas', 'square-share-nodes']
		}"
		v-model:drawer="showDrawer"
		:destroy-on-close="true"
		@close="handleClose"
	>
		<template #content>
			<el-form
				ref="formRef"
				class="el-form-wrapper"
				:model="form"
				:rules="rules"
				label-width="auto"
				label-position="top"
			>
				<el-form-item label="流程名称" prop="name" required>
					<el-input
						v-model.trim="form.name"
						:show-word-limit="true"
						:maxlength="50"
						placeholder="请输入"
						:disabled="editFlag"
					/>
				</el-form-item>
				<el-form-item label="流程编码" prop="code" required>
					<el-input
						v-model.trim="form.code"
						:show-word-limit="true"
						:maxlength="50"
						placeholder="请输入"
						:disabled="editFlag"
					/>
				</el-form-item>
				<el-form-item label="表单页面" prop="formUrlPath" required>
					<el-input v-model.trim="form.formUrlPath" placeholder="请输入" />
				</el-form-item>
				<el-form-item label="所属公司" prop="companyId" required>
					<el-select
						filterable
						v-model="form.companyId"
						placeholder="请选择"
						clearable
						style="width: 100%"
						:disabled="editFlag"
					>
						<el-option
							v-for="item in companyList"
							:key="item.value"
							:label="item.label"
							:value="item.value"
							@click="onCompanyClick(item)"
						/>
					</el-select>
				</el-form-item>
				<el-form-item label="备注说明">
					<el-input
						v-model.trim="form.processRemark"
						:rows="3"
						type="textarea"
						:show-word-limit="true"
						:maxlength="50"
						placeholder="请输入备注说明"
					/>
				</el-form-item>
			</el-form>
		</template>
		<template #footer>
			<ButtonList :button="footerBtnList" @on-btn-click="footerBtnClick" />
		</template>
	</NewDrawer>
</template>
