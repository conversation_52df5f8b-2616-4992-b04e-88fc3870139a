<script lang="ts" setup>
import { ElMessage, ElMessageBox } from "element-plus"
import CallbackForm from "../components/callbackForm.vue"
import { getDictionaryTerm } from "@/app/platform/api/system/dictionary"
import { usePagination } from "@/app/platform/hooks/usePagination"

import {
	getProcessCallbackList,
	changeProcessCallback,
	deleteProcessCallback
} from "@/app/platform/api/system/processCallback"
import { matchPermissionBtnList } from "@/app/platform/utils/permission"
import { CustomMessageBox } from "@/app/platform/utils/message"

interface queryObj {
	/**
	 * 模块名称
	 */
	name?: string
	/**
	 * 页码
	 */
	currentPage?: number
	/**
	 * 每页条数
	 */
	pageSize?: number
}

const usePaginationStore = usePagination({})
const paginationData = ref<PaginationData>(usePaginationStore.paginationData)

const paramObj = ref<queryObj>({
	currentPage: paginationData.value.currentPage,
	pageSize: paginationData.value.pageSize,
	name: ""
})

const getTableList = (pd: PaginationData) => {
	paramObj.value.currentPage = pd.currentPage
	paramObj.value.pageSize = pd.pageSize
	dictionaryListInit()
}

const getQueryData = (queryData: queryObj) => {
	paramObj.value = Object.assign(paramObj.value, queryData)
	dictionaryListInit()
}

const dictionaryListInit = () => {
	getProcessCallbackList(paramObj.value).then((res: any) => {
		if (res.rows && res.rows.length) {
			baseTableData.value = res.rows
			dataTotal.value = res.records
		} else {
			baseTableData.value = []
			dataTotal.value = 0
		}
	})
}

// 字段对应展示
const dictionaryList = ref<any[]>([
	{
		label: "所属模块",
		code: "module",
		children: [
			{
				label: "全部",
				value: ""
			}
		]
	},
	{
		label: "回调类型",
		code: "CALL_TYPE",
		children: [
			{
				label: "全部",
				value: ""
			}
		]
	},
	{
		label: "监听类型",
		code: "LISTENTER_TYPE",
		children: [
			{
				label: "全部",
				value: ""
			}
		]
	},
	{
		label: "事件类型",
		code: "TaskListener",
		children: [
			{
				label: "全部",
				value: ""
			}
		]
	},
	{
		label: "事件类型",
		code: "ExecutionListener",
		children: [
			{
				label: "全部",
				value: ""
			}
		]
	}
])

const getAllDictionaryList = () => {
	dictionaryList.value.forEach((dItem: any) => {
		getDictionaryTerm({
			dataDictionaryCode: dItem.code
		}).then((dItemList: any) => {
			if (dItemList && dItemList.length) {
				const children = [
					{
						label: "全部",
						value: ""
					}
				]
				dItemList.forEach((item: any) => {
					children.push({
						label: item.subitemName,
						value: item.subitemValue
					})
				})
				dItem.children = children
			}
		})
	})
}
const getDicMatchTxt = (code: string, val: string) => {
	let MTxt = ""
	const Mdic = dictionaryList.value.filter((item: any) => {
		return item.code === code
	})
	if (Mdic && Mdic.length && Mdic[0].children && Mdic[0].children.length) {
		const MItem = Mdic[0].children.filter((citem: any) => {
			return citem.value === val
		})
		if (MItem && MItem.length) {
			MTxt = MItem[0].label
		}
	}
	return MTxt
}
const queryArrList = [
	{
		name: "接口名称",
		key: "name",
		type: "input",
		placeholder: "请输入查询关键字"
	}
]

const title = {
	name: ["流程回调"],
	icon: ["fas", "square-share-nodes"]
}

const button = [
	{
		name: "新增流程回调",
		icon: ["fas", "square-plus"]
	}
]

const dataTotal = ref(0)
const baseTableData = ref<any[]>([])
const baseColumns = ref<TableColumnType[]>([
	{ prop: "url", label: "回调地址", width: 170 },
	{ prop: "callMethod", label: "调用方式", width: 200 },
	{ prop: "name", label: "回调接口名称", width: 110 },
	{ prop: "module", label: "所属模块", width: 170, needSlot: true },
	{ prop: "callType", label: "回调类型", width: 110, needSlot: true },
	{ prop: "listenerType", label: "监听类型", width: 160, needSlot: true },
	{ prop: "eventType", label: "事件类型", width: 110, needSlot: true },
	{ prop: "notes", label: "备注说明", minWidth: 210 },
	{ prop: "lastModifiedDate", label: "更新时间", width: 170 },
	{
		prop: "operations",
		fixed: "right",
		label: "操作",
		needSlot: true,
		width: 150
	}
])

// 弹框
const drawerTitle = {
	name: ["新增流程回调"],
	icon: ["fas", "square-share-nodes"]
}
const formData = ref<object>({})
const showDrawer = ref<boolean>(false)
const drawerSize = "310px"
// 打开新增弹框
const onBtnClick = () => {
	showDrawer.value = true
}
// 打开编辑弹框
const onEdit = (data: any) => {
	formData.value = data
	showDrawer.value = true
}
// 点击移除
const onDelete = (rowData: anyKey) => {
	CustomMessageBox({ message: "确定要移除该条信息吗？" }, (res: boolean) => {
		if (res) {
			const formdata = objectToFormData({
				id: rowData.id
			})
			deleteProcessCallback(formdata).then(() => {
				dictionaryListInit()
				ElMessage.success("操作成功")
			})
		}
	})
}
// 新增/修改
const onAddNewSave = (data: any) => {
	changeProcessCallback(objectToFormData(data)).then(() => {
		const msg = data.id ? "修改成功" : "新增成功"
		ElMessage.success(msg)
		dictionaryListInit()
		showDrawer.value = false
	})
}
// 取消新增
const onDrawerCancel = () => {
	showDrawer.value = false
}

watch(
	() => showDrawer.value,
	(flag: boolean) => {
		if (!flag) {
			formData.value = {}
		}
	},
	{ immediate: true }
)

onMounted(() => {
	dictionaryListInit()
	getAllDictionaryList()
})
</script>

<template>
	<div class="app-container">
		<ModelFrame>
			<Query :queryArrList="queryArrList" @getQueryData="getQueryData" />
		</ModelFrame>
		<div class="app-content-wrapper">
			<div class="app-content-group">
				<ModelFrame>
					<Title :title="title" :button="button" @onBtnClick="onBtnClick" />
					<div class="app-el-scrollbar-wrapper">
						<el-scrollbar>
							<PitayaTable
								@onCurrentPageChange="getTableList"
								:table-data="baseTableData"
								:columns="baseColumns"
								:total="dataTotal"
							>
								<template #module="{ rowData }">
									<span v-if="getDicMatchTxt('module', rowData.module)">
										{{ getDicMatchTxt("module", rowData.module) }}
									</span>
									<span v-else>---</span>
								</template>
								<template #callType="{ rowData }">
									<span v-if="getDicMatchTxt('CALL_TYPE', rowData.callType)">
										{{ getDicMatchTxt("CALL_TYPE", rowData.callType) }}
									</span>
									<span v-else>---</span>
								</template>
								<template #listenerType="{ rowData }">
									<span
										v-if="
											getDicMatchTxt('LISTENTER_TYPE', rowData.listenerType)
										"
									>
										{{ getDicMatchTxt("LISTENTER_TYPE", rowData.listenerType) }}
									</span>
									<span v-else>---</span>
								</template>
								<template #eventType="{ rowData }">
									<span
										v-if="
											getDicMatchTxt(rowData.listenerType, rowData.eventType)
										"
									>
										{{
											getDicMatchTxt(rowData.listenerType, rowData.eventType)
										}}
									</span>
									<span v-else>---</span>
								</template>
								<template #operations="{ rowData }">
									<el-button
										v-btn
										link
										@click="onEdit(rowData)"
										v-permission="matchPermissionBtnList('编辑')"
									>
										<font-awesome-icon
											:icon="['fas', 'pen-to-square']"
											style="color: var(--pitaya-btn-background)"
										/>
										<span class="table-inner-btn"> 编辑 </span>
									</el-button>
									<el-button
										v-btn
										color="var(--pitaya-btn-background)"
										link
										@click="onDelete(rowData)"
										v-permission="matchPermissionBtnList('移除')"
									>
										<font-awesome-icon
											:icon="['fas', 'trash-can']"
											style="color: var(--pitaya-btn-background)"
										/>
										<span class="table-inner-btn"> 移除 </span>
									</el-button>
								</template>
							</PitayaTable>
						</el-scrollbar>
					</div>
				</ModelFrame>
			</div>
		</div>
		<Drawer :size="drawerSize" v-model:drawer="showDrawer">
			<Title :title="drawerTitle" />
			<CallbackForm
				v-if="showDrawer"
				:form-value="formData"
				@on-submit="onAddNewSave"
				@on-cancel="onDrawerCancel"
			/>
		</Drawer>
	</div>
</template>

<style lang="scss" scoped>
.btn-groups-box {
	padding: 0 10px;
}
.app-container {
	:deep(.model-frame-wrapper) {
		padding-bottom: 0px;
	}
}
.app-content-wrapper {
	height: 0;
	flex: 1;
	margin-top: 10px;
	.app-content-group {
		height: 100%;
		:deep(.model-frame-wrapper) {
			height: 100%;
			padding-bottom: 10px;
			overflow: hidden;
			display: flex;
			flex-direction: column;
		}
		.app-el-scrollbar-wrapper {
			height: 0;
			flex: 1;
		}
	}
}
.drawer-lr-layout-wrapper {
	display: flex;
	position: absolute;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	overflow: hidden;
	&::after {
		content: "";
		width: 1px;
		height: 120%;
		position: absolute;
		left: 310px;
		top: -15px;
		background-color: var(--pitaya-border-color);
	}
}
.common-from-wrapper {
	height: 100%;
	width: 0;
	flex: 1;
	display: flex;
	flex-direction: column;
	padding: 10px;
	.drawer-content-wrapper {
		position: absolute;
		left: 130px;
		&::after {
			content: "";
			width: 100%;
			height: 2px;
			background-color: var(--pitaya-btn-background);
			position: absolute;
			bottom: -10px;
			left: 50%;
			transform: translateX(-50%);
		}
	}
	&.common-from-left {
		width: 310px;
		flex: 0 0 310px;
	}
	&.common-from-only {
		width: 100%;
		padding: 0;
	}
	.common-from-group {
		height: 0;
		flex: 1;
		.el-form-wrapper {
			padding-left: 10px;
			padding-right: 10px;
		}
	}
	.btn-groups {
		display: flex;
		justify-content: flex-end;
		padding-top: 10px;
		border-top: 1px solid var(--pitaya-border-color);
	}
}
</style>
@/app/platform/hooks/usePagination
@/app/platform/api/system/dictionary@/app/platform/api/system/processCallback
