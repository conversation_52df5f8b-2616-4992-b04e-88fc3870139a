<!-- 流程设计主页面 -->
<script setup lang="ts">
import {
	checkPermission,
	isCheckPermission
} from "@/app/platform/utils/permission"
import bpmnDrawer from "./bpmnDrawer.vue"
import NewProcessDrawer from "./newProcessDrawer.vue"
import { usePagination } from "@/app/platform/hooks/usePagination"
import {
	deleteProcessDesign,
	getProcessDesignList
} from "@/app/platform/api/system/processDesign"
import { CustomMessageBox } from "@/app/platform/utils/message"
import { getDictionaryTerm } from "@/app/platform/api/system/dictionary"

const showDrawer = ref(false)
const showDesignDrawer = ref(false)
const tableLoading = ref(false)
const usePaginationStore = usePagination()
const paginationData = ref<PaginationData>(usePaginationStore.paginationData)
// 查询条件
const queryArrList = [
	{
		name: "流程名称",
		key: "name",
		placeholder: "请输入查询关键字",
		type: "input",
		enableFuzzy: false
	},
	{
		name: "流程编码",
		key: "code",
		placeholder: "请输入查询关键字",
		type: "input",
		enableFuzzy: false
	}
]
// 字典项数据
const dictionaryList = ref<any[]>([
	{
		label: "流程类型",
		code: "PROCESS_CODE",
		children: []
	}
])
const queryDataTemp = ref()
const userTableRef = ref()
const currentRow = ref()
// 查询方法
const getQueryData = (queryData: queryObj) => {
	queryDataTemp.value = queryData
	pageInfo.value.currentPage = 1
	userTableRef.value.resetCurrentPage()
	getProcessDesignData()
}

const getDictionaryList = () => {
	dictionaryList.value.forEach((dItem: any) => {
		getDictionaryTerm({
			dataDictionaryCode: dItem.code
		}).then((dItemList: any) => {
			if (dItemList && dItemList.length) {
				const children: { label: any; value: any }[] | undefined = []
				dItemList.forEach((item: any) => {
					children.push({ label: item.subitemName, value: item.subitemValue })
				})
				dItem.children = children
			}
		})
	})
}
// 列表相关
const tableTitle = ref({
	name: ["流程设计"],
	icon: ["fas", "square-share-nodes"]
})
const tableBtns = [
	{
		name: "新建流程",
		roles: "system:processdesign:btn:add",
		icon: ["fas", "square-plus"]
	}
]
// 新建流程按钮
const newProcessRef = ref()
const handleTableBtnClick = (btnName: string | undefined) => {
	if (btnName === "新建流程") {
		newProcessRef.value.open()
	} else {
		showDesignDrawer.value = true
	}
}

const tableData = ref<any[]>([])
const preBpmSts = ["草稿", "审核中", "已发布", "已废弃"]
const tableColumn = ref<any[]>([
	{ prop: "name", label: "流程名称", minWidth: 300, align:'left'},
	{ prop: "code", label: "流程编码", width: 200 },
	{ prop: "companyId_view", label: "所属公司", width: 150 },
	{ prop: "publishTime", label: "发布时间", width: 160 },
	{ prop: "currentVersion", label: "当前版本号", width: 100, needSlot: true },
	{ prop: "preVersion", label: "预发布版本号", width: 100, needSlot: true },
	{ prop: "createdBy_view", label: "创建人", width: 100 },
	{ prop: "lastModifiedBy_view", label: "更新人", width: 100 },
	{ prop: "processRemark", label: "备注说明", width: 200, align:'left'},
	{
		prop: "operations",
		fixed: "right",
		label: "操作",
		needSlot: true,
		width: 150
	}
])
const pageInfo = ref<any>({
	currentPage: paginationData.value.currentPage,
	pageSize: paginationData.value.pageSize,
	total: 0
})
const onCurrentPageChange = (pageData: any) => {
	pageInfo.value.pageSize = pageData.pageSize
	pageInfo.value.currentPage = pageData.currentPage
	getProcessDesignData()
}
const handleEditClick = (row: any) => {
	currentRow.value = row
	showDesignDrawer.value = true
}
const handleDeleteClick = (row: any) => {
	CustomMessageBox({ message: "确定要移除吗？" }, (res: boolean) => {
		if (res) {
			deleteProcessDesign({ id: row.id }).then(() => {
				ElMessage.success("移除成功")
				pageInfo.value.currentPage = 1
				getProcessDesignData()
			})
		}
	})
}

const handleDrawerSuccess = (data: { code: any }) => {
	nextTick(() => {
		if (data && data.code) {
			currentRow.value = data
			showDesignDrawer.value = true
		} else {
			getProcessDesignData()
		}
	})
}

function getProcessDesignData() {
	tableLoading.value = true
	getProcessDesignList({
		...queryDataTemp.value,
		pageSize: pageInfo.value.pageSize,
		currentPage: pageInfo.value.currentPage
	})
		.then((res: any) => {
			console.log(res)
			tableData.value = res.rows
			pageInfo.value.total = res.records
		})
		.finally(() => {
			tableLoading.value = false
		})
}

const editBtn = (data: any) => {
	newProcessRef.value.open(data)
}

onMounted(() => {
	getProcessDesignData()
	getDictionaryList()
})
</script>

<template>
	<div class="app-container">
		<ModelFrame>
			<Query
				class="ml10 process-design-query"
				:queryArrList="queryArrList"
				@getQueryData="getQueryData"
			/>
		</ModelFrame>
		<div class="app-content-wrapper">
			<div class="app-content-group">
				<ModelFrame>
					<Title
						:title="tableTitle"
						:button="tableBtns"
						@onBtnClick="handleTableBtnClick"
					/>
					<div class="app-el-scrollbar-wrapper">
						<PitayaTable
							ref="userTableRef"
							:table-data="tableData"
							:columns="tableColumn"
							:need-index="true"
							:need-pagination="true"
							:total="pageInfo.total"
							@on-current-page-change="onCurrentPageChange"
							:table-loading="tableLoading"
						>
							<template #currentVersion="{ rowData }">
								<VersionTag
									:value="rowData.currentVersion"
									:label="'V' + rowData.currentVersion"
									:bpm-sts="rowData.bpmSts"
								/>
							</template>
							<template #preVersion="{ rowData }">
								<VersionTag
									:value="rowData.preVersion"
									:label="'V' + rowData.preVersion"
									:bpm-sts="rowData.preBpmSts"
								/>
							</template>

							<template #preBpmSts="{ rowData }">
								<VersionTag
									:value="rowData.preBpmSts"
									:label="preBpmSts[rowData.preBpmSts]"
									:bpm-sts="rowData.preBpmSts"
								/>
							</template>
							<template #operations="{ rowData }">
								<el-button
									v-btn
									link
									@click="handleEditClick(rowData)"
									:disabled="checkPermission('system:processdesign:btn:edit')"
									v-if="isCheckPermission('system:processdesign:btn:edit')"
								>
									<font-awesome-icon
										:icon="['fas', 'eye']"
										:class="
											checkPermission('system:processdesign:btn:edit')
												? 'disabled'
												: ''
										"
										style="color: var(--pitaya-btn-background)"
									/>
									<span
										class="table-inner-btn"
										:class="
											checkPermission('system:processdesign:btn:edit')
												? 'disabled'
												: ''
										"
									>
										查看
									</span>
								</el-button>
								<el-button
									v-btn
									link
									@click="handleDeleteClick(rowData)"
									:disabled="checkPermission('system:processdesign:btn:edit')"
									v-if="isCheckPermission('system:processdesign:btn:edit')"
								>
									<font-awesome-icon
										:icon="['fas', 'trash-can']"
										:class="
											checkPermission('system:processdesign:btn:edit')
												? 'disabled'
												: ''
										"
										style="color: var(--pitaya-btn-background)"
									/>
									<span
										class="table-inner-btn"
										:class="
											checkPermission('system:processdesign:btn:edit')
												? 'disabled'
												: ''
										"
									>
										移除
									</span>
								</el-button>
								<div v-if="!isCheckPermission('system:processdesign:btn:edit')&&!isCheckPermission('system:processdesign:btn:edit')">
									---
								</div>
							</template>
						</PitayaTable>
					</div>
				</ModelFrame>
			</div>
		</div>
		<Drawer
			size="90%"
			v-model:drawer="showDesignDrawer"
			:destroy-on-close="true"
			@close="getProcessDesignData"
		>
			<bpmnDrawer :processData="currentRow" @editBtn="editBtn" />
		</Drawer>
		<NewProcessDrawer
			ref="newProcessRef"
			:dictionary-list="dictionaryList"
			@success="handleDrawerSuccess"
		/>
	</div>
</template>

<style lang="scss" scoped>
.app-container {
	:deep(.model-frame-wrapper) {
		padding-bottom: 0px;
	}
}
.app-content-wrapper {
	height: 0;
	flex: 1;
	margin-top: 10px;
	.app-content-group {
		height: 100%;
		:deep(.model-frame-wrapper) {
			height: 100%;
			padding-bottom: 10px;
			overflow: hidden;
			display: flex;
			flex-direction: column;
		}
		.app-el-scrollbar-wrapper {
			height: 0;
			flex: 1;
			.table-inner-btn {
				margin-left: 5px;
				color: #204a9c;
			}
		}
	}
}
.common-from-wrapper {
	height: 100%;
	width: 100%;
	display: flex;
	flex-direction: column;
	.common-from-group {
		height: 0;
		flex: 1;
		padding: 0 10px;
	}
	.btn-groups {
		display: flex;
		justify-content: flex-end;
		padding-top: 10px;
		border-top: 1px solid var(--pitaya-border-color);
	}
}
</style>
@/app/platform/hooks/usePagination @/app/platform/api/system/parameter
