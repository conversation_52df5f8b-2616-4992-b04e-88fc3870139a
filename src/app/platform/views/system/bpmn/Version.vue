<!-- 版本列表-新建版本 -->
<template>
	<div class="left-model">
		<!-- 列表 -->
		<ul class="ul">
			<template v-if="versionList.length > 0">
				<li
					class="flex-dr li"
					:class="[id == item.id ? 'hover' : '']"
					v-for="(item, index) in versionList"
					:key="index"
					@click="onClickBtn(item)"
				>
					<div class="f12 flex-dc">
						<span class="c-3"
							>版本号：V{{ item.version ? item.version : "---" }}
							<span v-if="item.bpmSts === 4" style="color: #e25e59"
								>（{{ item.bpmSts_view }}）
								<el-tooltip
									popper-class="tooltipMaxWidth"
									class="box-item"
									effect="dark"
									:content="item.rejectReason"
									placement="top"
								>
									<!-- <el-icon :size="18" style="vertical-align: text-top"><QuestionFilled/></el-icon> -->
									<span class="cus_icon">?</span>
								</el-tooltip>
							</span>
						</span>
						<span class="c-9"
							>发布时间：{{ item.publishTime ? item.publishTime : "---" }}</span
						>
					</div>
					<el-tag class="f12 release" :class="'release-' + item.bpmSts">
						{{ item.bpmSts === 4 ? "草稿" : item.bpmSts_view }}
					</el-tag>
				</li>
			</template>
			<li v-else class="tc">
				<EmptySearch class="empty_img" />
				<p>未查询到相关信息</p>
			</li>
		</ul>
	</div>
</template>
<script lang="ts" setup>
import { toRef, ref } from "vue"
import EmptySearch from "@/assets/images/empty/empty_search.svg?component"
// import { QuestionFilled } from  '@element-plus/icons-vue'

interface Props {
	versionList: any[]
}

const props = defineProps<Props>()
const versionList = toRef(props, "versionList")

// const bpmSts = [
// 	{ name: "草稿", style: "release3" },
// 	{ name: "审核中", style: "release2" },
// 	{ name: "已发布", style: "release1" },
// 	{ name: "已废弃", style: "release" }
// ]
const emit = defineEmits<{
	(e: "onVersionClickBtn", btnName: [] | undefined): void
}>()

const id = ref("")

const onClickBtn = (item: any) => {
	id.value = item.id
	emit("onVersionClickBtn", item)
}

const initId = () => {
	nextTick(() => {
		if (versionList.value.length == 0) return
		id.value = versionList.value[0].id
	})
}
initId()
watch(
	() => versionList.value,
	(val: any) => {
		if (val) {
			initId()
		}
	},
	{ immediate: true }
)
</script>
<style>
.tooltipMaxWidth {
	max-width: 332px !important;
}
</style>
<style lang="scss" scoped>
.empty_img {
	width: 150px;
}
.cus_icon {
	display: inline-block;
	width: 16px;
	height: 16px;
	border-radius: 50%;
	line-height: 14px;
	text-align: center;
	font-size: 12px;
	border: 1px solid #e25e59;
	color: #e25e59;
	vertical-align: middle;
	transform: scale(0.8);
	margin-top: -4px;
}
.left-model {
	margin-top: var(--pitaya-fs-12);
}

.dashed-box {
	height: 35px;
	line-height: 35px;
	font-size: 14px;
	margin-top: var(--pitaya-fs-12);
	text-align: center;
	color: var(--pitaya-header-bg-color);
	border: 1px dashed var(--el-border-color);
}

.ml10 {
	margin-left: 10px;
}

.flex-dr {
	display: flex;
	flex-direction: row;
	justify-content: space-between;
	align-items: center;
}

.flex-dc {
	display: flex;
	flex-direction: column;
}

.li:hover {
	color: #666666;
	background: var(--el-fill-color-light);
	cursor: pointer;
}

.hover {
	color: #666666;
	background: var(--el-fill-color-light);
}

.li {
	line-height: 24px;
	padding: 10px;
	border-bottom: 1px dashed var(--el-border-color);

	.release {
		border-radius: 3px;
		font-size: 12px;
		text-align: center;
	}
}
.tc {
	color: #666666;
	font-size: 12px;
	text-align: center;
}
.f12 {
	font-size: 12px;
}

.c-9 {
	color: #999;
}
.c-3 {
	color: #333;
	font-size: 14px;
}

.mt20 {
	margin-top: 20px;
}

.cp {
	cursor: pointer;
}
</style>
