<script lang="ts" setup>
import { ElMessage } from "element-plus"
import type { FormInstance, FormRules } from "element-plus"
import { usePagination } from "@/app/platform/hooks/usePagination"
import {
	getSystemParameterList,
	changeSystemParameter
} from "@/app/platform/api/system/parameter"
import {
	checkPermission,
	isCheckPermission
} from "@/app/platform/utils/permission"

interface queryObj {
	/**
	 * 页码
	 */
	currentPage?: number
	/**
	 * 每页条数
	 */
	pageSize?: number
	/**
	 * 参数编码
	 */
	paramenterCode?: string
	/**
	 * 参数名称
	 */
	paramenterName?: string
}

const tableLoading = ref(false)
const drawerLoading = ref(false)

const usePaginationStore = usePagination()
const paginationData = ref<PaginationData>(usePaginationStore.paginationData)

const paramObj = ref<queryObj>({
	currentPage: paginationData.value.currentPage,
	pageSize: paginationData.value.pageSize,
	paramenterCode: "",
	paramenterName: ""
})

const getTableList = (pd: PaginationData) => {
	paramObj.value.currentPage = pd.currentPage
	paramObj.value.pageSize = pd.pageSize
	systemParameterListInit()
}

const tableRef = ref()
const getQueryData = (queryData: queryObj) => {
	paramObj.value.currentPage = 1
	tableRef.value.resetCurrentPage()
	paramObj.value = Object.assign(paramObj.value, queryData)
	systemParameterListInit()
}

const systemParameterListInit = () => {
	tableLoading.value = true
	getSystemParameterList(paramObj.value)
		.then((res: any) => {
			if (res.rows && res.rows.length) {
				baseTableData.value = res.rows
				dataTotal.value = res.records
			} else {
				baseTableData.value = []
				dataTotal.value = 0
			}
		})
		.finally(() => {
			tableLoading.value = false
		})
}

onMounted(() => {
	systemParameterListInit()
})

const queryArrList = [
	{
		name: "参数名称",
		key: "paramenterName",
		type: "input",
		placeholder: "请输入查询关键字",
		enableFuzzy: false
	},
	{
		name: "参数编码",
		key: "paramenterCode",
		type: "input",
		placeholder: "请输入查询关键字",
		enableFuzzy: false
	}
]

const title = {
	name: ["系统参数"],
	icon: ["fas", "square-share-nodes"]
}

const drawerTitle = {
	name: ["参数信息"],
	icon: ["fas", "square-share-nodes"]
}

const drawerState = ref(false)
const drawerSize = 310

const dataTotal = ref(0)
const baseTableData = ref<any[]>([])
const baseColumns = ref<TableColumnType[]>([
	{ prop: "paramenterName", label: "参数名称", width:250, align:'left'},
	{ prop: "paramenterCode", label: "参数编码", width:200, align:'left' },
	{ prop: "paramenterValue", label: "参数取值", minWidth: 200 },
	{ prop: "notes", label: "备注说明", minWidth: 200, align:'left' },
	{ prop: "lastModifiedDate", label: "更新时间", width: 170 },
	{
		prop: "operations",
		fixed: "right",
		label: "操作",
		needSlot: true,
		width: 100
	}
])

const baseFormData = reactive<anyKey>({
	id: "",
	paramenterCode: "",
	paramenterName: "",
	notes: "",
	paramenterValue: ""
})

const formRules = reactive<FormRules<typeof baseFormData>>({
	paramenterValue: [
		{ required: true, message: "请输入参数取值", trigger: "blur" }
	]
})

const onEdit = (rowData: anyKey) => {
	Object.keys(baseFormData).forEach((key) => {
		baseFormData[key] = rowData[key] ? rowData[key] : ""
	})
	drawerState.value = true
}

const baseFormRef = ref<FormInstance>()

const onFormBtnClick = (btnName: string | undefined) => {
	if (!baseFormRef.value) return
	if (btnName === "保存") {
		baseFormRef.value.validate((valid) => {
			if (valid) {
				drawerLoading.value = true
				changeSystemParameter(baseFormData)
					.then(() => {
						systemParameterListInit()
						drawerState.value = false
						ElMessage.success("操作成功")
					})
					.finally(() => {
						drawerLoading.value = false
					})
			} else {
				return false
			}
		})
		return
	}

	if (btnName === "取消") {
		drawerState.value = false
		return
	}
}

const onClose = () => {
	Object.keys(baseFormData).map((key) => (baseFormData[key] = ""))
	baseFormRef.value?.clearValidate()
}

const btnList = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "保存",
		icon: ["fas", "floppy-disk"]
	}
]
</script>

<template>
	<div class="app-container">
		<ModelFrame>
			<Query
				class="ml10"
				:queryArrList="queryArrList"
				@getQueryData="getQueryData"
			/>
		</ModelFrame>
		<div class="app-content-wrapper">
			<div class="app-content-group">
				<ModelFrame>
					<Title :title="title" />
					<div class="app-el-scrollbar-wrapper">
						<el-scrollbar>
							<PitayaTable
								ref="tableRef"
								@onCurrentPageChange="getTableList"
								:table-data="baseTableData"
								:columns="baseColumns"
								:total="dataTotal"
								:table-loading="tableLoading"
							>
								<template #operations="{ rowData }">
									<el-button
										v-btn
										link
										@click="onEdit(rowData)"
										:disabled="checkPermission('system:parameter:btn:edit')"
										v-if="isCheckPermission('system:parameter:btn:edit')"
									>
										<font-awesome-icon
											:icon="['fas', 'pen-to-square']"
											style="color: var(--pitaya-btn-background)"
											:class="
												checkPermission('system:parameter:btn:edit')
													? 'disabled'
													: ''
											"
										/>
										<span
											class="table-inner-btn"
											:class="
												checkPermission('system:parameter:btn:edit')
													? 'disabled'
													: ''
											"
										>
											编辑
										</span>
									</el-button>
									<div v-else>---</div>
								</template>
							</PitayaTable>
						</el-scrollbar>
					</div>
				</ModelFrame>
			</div>
		</div>
		<Drawer :size="drawerSize" v-model:drawer="drawerState" @close="onClose">
			<div class="common-from-wrapper" v-loading="drawerLoading">
				<Title :title="drawerTitle" />
				<div class="common-from-group">
					<el-scrollbar>
						<el-form
							class="el-form-wrapper"
							ref="baseFormRef"
							:model="baseFormData"
							:rules="formRules"
							label-width="auto"
							label-position="top"
						>
							<el-form-item label="参数编码">
								<el-input v-model.trim="baseFormData.paramenterCode" disabled />
							</el-form-item>
							<el-form-item label="参数名称">
								<el-input v-model.trim="baseFormData.paramenterName" disabled />
							</el-form-item>
							<el-form-item label="备注说明">
								<el-input
									v-model.trim="baseFormData.notes"
									type="textarea"
									:autosize="{ minRows: 8 }"
									resize="none"
									disabled
								/>
							</el-form-item>
							<el-form-item label="参数取值" prop="paramenterValue">
								<el-input v-model.trim="baseFormData.paramenterValue" />
							</el-form-item>
						</el-form>
					</el-scrollbar>
				</div>
				<div class="btn-groups">
					<ButtonList :button="btnList" @onBtnClick="onFormBtnClick" />
				</div>
			</div>
		</Drawer>
	</div>
</template>

<style lang="scss" scoped>
.app-container {
	:deep(.model-frame-wrapper) {
		padding-bottom: 0px;
	}
}
.app-content-wrapper {
	height: 0;
	flex: 1;
	margin-top: 10px;
	.app-content-group {
		height: 100%;
		:deep(.model-frame-wrapper) {
			height: 100%;
			padding-bottom: 10px;
			overflow: hidden;
			display: flex;
			flex-direction: column;
		}
		.app-el-scrollbar-wrapper {
			height: 0;
			flex: 1;
			.table-inner-btn {
				margin-left: 5px;
				color: #204a9c;
			}
		}
	}
}
.common-from-wrapper {
	height: 100%;
	width: 100%;
	display: flex;
	flex-direction: column;
	.common-from-group {
		height: 0;
		flex: 1;
		padding: 0 10px;
	}
	.btn-groups {
		display: flex;
		justify-content: flex-end;
		padding: 10px 10px 0 0;
		border-top: 1px solid var(--pitaya-border-color);
	}
}
</style>
@/app/platform/hooks/usePagination @/app/platform/api/system/parameter
