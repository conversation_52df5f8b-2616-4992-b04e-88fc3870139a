<script lang="ts" setup>
import { FormInstance, FormRules, ElMessage } from "element-plus"
import { requestMapApi } from "@/app/platform/api/system/requestMap"
import { usePagination } from "@/app/platform/hooks/usePagination"
import { getTreeTitle, sortArr } from "@/app/platform/utils/common"
import { getDicDescEcho } from "@/app/platform/utils/dicEcho"
import { _matchFormProp } from "@/app/platform/utils/permission"
import { CustomMessageBox } from "@/app/platform/utils/message"
import { getDictionaryTerm } from "@/app/platform/api/system/dictionary"

// #region 基本
const TreeProp = {
	children: "chirldMenu",
	label: "menuName"
}
const drawerLoading = ref(false)

onMounted(() => {
	baseTableData.value = []
	currentNodeObj.value = {}
	treeBizId.value = ""
	getBaseSystemTree("refreshTable")
	events.on("refreshTable", () => {
		getBaseSystemTree("refreshTable")
	})
})
// #endregion

// #region 树相关
const treeData = ref<any[]>([])
const treeBizId = ref("")
const currentNodeObj = ref<anyKey>({})
const currentNode = ref<anyKey>({})
const defaultExpandedKeys = ref([0])

const tableLoading = ref(false)
const treeLoading = ref(false)
const renderTypeList = ref([])

/**
 * 获取权限树
 */
// 当前选中的树节点
const currentTreeSelectNode = ref<any>()
function getBaseSystemTree(param?: string) {
	treeLoading.value = true
	requestMapApi
		.getBaseSystemTreeApi({ searchValue: filterText.value })
		.then((res: anyKey) => {
			// 左侧树 数据
			treeData.value = [res]
			// 这里暂时没拿到根节点 自己拼的
			currentNodeObj.value = res
			defaultExpandedKeys.value = [treeBizId.value ? treeBizId.value : ""]
			if (treeBizId.value == "") {
				currentTreeSelectNode.value = { data: treeData.value[0], parent: {} }
				if (currentTreeSelectNode.value.data.menuName !== "全部位置") {
					// 表格展示的 面包屑
					tableTitle.value.name.push(currentTreeSelectNode.value.data.menuName)
					tableTitle.value.name.push(
						currentTreeSelectNode.value.data.chirldMenu[0].menuName
					)
					treeBizId.value = res.chirldMenu[0].id
				}
			} else {
				treeBizId.value = treeBizId.value
			}
			getBaseSystemList()
		})
		.catch((err) => {
			throw new Error("getBaseSystemTreeApi():::" + err)
		})
		.finally(() => {
			treeLoading.value = false
		})
}

//查询参数
const filterText = ref<string>("")
// const onTreeSearch = (infilterText: any) => {
// 	if (infilterText && infilterText.length >= 2) {
// 		filterText.value = infilterText
// 		getBaseSystemTree()
// 	} else if (!infilterText || infilterText.length < 2) {
// 		filterText.value = ""
// 		getBaseSystemTree()
// 	}
// }

/**
 * 树点击
 */
const onTreeClick = (data: any, data1: any) => {
	treeBizId.value = data.id
	currentNodeObj.value = data
	currentNode.value = data1
	tableTitle.value.name = JSON.parse(JSON.stringify(title)).name.concat(
		getTreeTitle(data1, "menuName")
	)
	paginationData.value.currentPage = 1
	getBaseSystemList()
}
// #endregion

// #region 表格相关
const title = {
	name: ["权限管理"],
	icon: ["fas", "square-share-nodes"]
}

const tableTitle = ref({
	name: ["权限管理"],
	icon: ["fas", "square-share-nodes"]
})
const usePaginationStore = usePagination()
const paginationData = ref<PaginationData>(usePaginationStore.paginationData)
const dataTotal = ref(0)
const baseTableData = ref<any[]>([])
const baseFormRef = ref<FormInstance>()
const baseColumns: TableColumnType[] = [
	{ prop: "purview", label: "权限编码", width: 200 },
	{ prop: "menuName", label: "权限名称", width: 100 },
	{ prop: "menuLevel", label: "权限类型", needSlot: true, width: 100 },
	{ prop: "menuIcon", label: "权限图标", needSlot: true, width: 80 },
	{ prop: "path", label: "路由路径" ,minWidth:150},
	{ prop: "alwaysShow", label: "是否显示", needSlot: true, width: 100 },
	{ prop: "lastModifiedDate", label: "更新时间", width: 160 },
	{
		prop: "operations",
		fixed: "right",
		label: "操作",
		needSlot: true,
		width: 140
	}
]

// 列表渲染类型列文本展示
const _menuDisplayTypeShowText = computed(() => {
	return function (type: string) {
		// 按钮时 1 、2 表示可见否   菜单时 -- 省略
		return type === "2"
			? "未授权不可见"
			: type === "1"
			? "未授权可见不可用"
			: "---"
	}
})

/**
 * 获取权限列表
 */
function getBaseSystemList() {
	tableLoading.value = true
	requestMapApi
		.getBaseSystemListApi({
			...queryData.value,
			...paginationData.value,
			id: treeBizId.value
		})
		.then((res: anyKey) => {
			if (res.rows && res.rows.length > 0) {
				const { rows, records } = res
				baseTableData.value = rows
				dataTotal.value = records
			} else {
				baseTableData.value = []
			}
		})
		.catch((err) => {
			throw new Error("getBaseSystemListApi():::" + err)
		})
		.finally(() => {
			tableLoading.value = false
		})
}

const onEdit = (rowData: anyKey) => {
	// 按钮权限编辑回显按钮级联
	baseFormData.value = { ...rowData }
	baseFormData.value[_matchFormProp("menuParent")] =
		currentNodeObj.value.menuName
	drawerTitle.value.name[0] = "编辑权限"
	drawerState.value = true
}

const onDelete = (rowData: anyKey) => {
	CustomMessageBox({ message: "确定要移除吗?" }, (res: boolean) => {
		if (res) {
			requestMapApi
				.removeBaseSystemApi({ id: rowData.id })
				.then((res) => {
					if (res) {
						ElMessage.success("移除成功")
						getBaseSystemTree()
					}
				})
				.catch((err) => {
					throw new Error("removeBaseSystemApi():::" + err)
				})
		} else {
			return false
		}
	})
}

const handlerPaginationChanged = (pd: PaginationData) => {
	paginationData.value = pd
	getBaseSystemList()
}
// #endregion

// #region 表单相关
const baseFormData = ref<anyKey>({ alwaysShow: true })
const formRules: FormRules<typeof baseFormData> = {
	[_matchFormProp("menuName")]: [
		{ required: true, message: "请输入文字标题", trigger: ["blur", "change"] }
	],
	[_matchFormProp("purview")]: [
		{ required: true, message: "请输入权限编码", trigger: ["blur", "change"] }
	],
	[_matchFormProp("name")]: [
		{
			required: true,
			message: "请输入组件名称",
			trigger: ["blur", "change"]
		}
	],
	[_matchFormProp("path")]: [
		{
			required: true,
			message: "请输入组件路径",
			trigger: ["blur", "change"]
		}
	],
	[_matchFormProp("component")]: [
		{
			required: true,
			message: "请输入路由路径",
			trigger: ["blur", "change"]
		}
	],
	[_matchFormProp("menuBtnText")]: [
		{ required: true, message: "请选择按钮配置", trigger: "blur" }
	],
	[_matchFormProp("menuLevel")]: [
		{ required: true, message: "请选择权限类型", trigger: "change" }
	],
	[_matchFormProp("menuDisplayType")]: [
		{ required: true, message: "请选择渲染方式", trigger: "change" }
	]
}
const roleTypeList = ref([])


// #region 使用级联权限按钮相关

// #endregion
const onFormBtnClick = (btnName: string | undefined) => {
	if (!baseFormRef.value) return
	if (btnName === "保存") {
		baseFormRef.value.validate((valid) => {
			if (valid) {
				if (baseFormData.value[_matchFormProp("menuLevel")] == "4") {
					baseFormData.value[_matchFormProp("menuName")] =
						baseFormData.value[_matchFormProp("menuBtnText")]
				}
				// 按钮配置字段
				setTimeout(() => {
					const data = {
						...baseFormData.value,
						menuParentId: treeBizId.value,
						menuIcon:
							baseFormData.value[_matchFormProp("menuLevel")] == 3
								? ""
								: baseFormData.value.menuIcon
					}
					drawerLoading.value = true
					requestMapApi
						.saveOrUpdateBaseSystemApi(data)
						.then(() => {
							ElMessage.success(baseFormData.value.id ? "编辑成功" : "新增成功")

							if (baseFormData.value.id) {
								getBaseSystemTree()
							} else {
								getBaseSystemTree()
							}
							drawerState.value = false
						})
						.catch((err) => {
							throw new Error("saveOrUpdateBaseSystemApi():::" + err)
						})
						.finally(() => {
							drawerLoading.value = false
						})
				}, 200)
			} else {
				return false
			}
		})
		return
	}

	if (btnName === "取消") {
		drawerState.value = false
		return
	}
}
// #endregion

// #region 弹窗相关
const drawerTitle = ref({
	name: ["新增权限"],
	icon: ["fas", "square-share-nodes"]
})
const button = [
	{
		name: "新增权限",
		icon: ["fas", "square-plus"]
	}
]
const btnList = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "保存",
		icon: ["fas", "floppy-disk"]
	}
]
const drawerState = ref(false)
const drawerSize = 310

const onBtnClick = () => {
	nextTick(() => {
		baseFormRef.value?.resetFields()
	}) // 校验当前节点是否为按钮，是则不允许新建并给出提示
	if (
		currentNodeObj.value.menuLevel &&
		currentNodeObj.value.menuLevel === "4"
	) {
		return ElMessage.warning("按钮操作下不允许新建权限")
	}

	baseFormData.value = { alwaysShow: true }
	baseFormData.value[_matchFormProp("menuParent")] =
		currentNodeObj.value.menuName
	baseFormData.value.sortedBy = 1000
	drawerTitle.value.name[0] = "新增权限"
	drawerState.value = true
}

const onClose = () => {
	baseFormData.value = {}
	nextTick(() => {
		baseFormRef.value?.resetFields()
	})
}
// #endregion

// #region 辅助函数
function _matchIcon(menuIcon: string) {
	return ["fas", menuIcon]
}
// #endregion

//查询
const queryArrList = ref<any[]>([
	{
		name: "权限名称",
		key: "menuName",
		type: "input",
		enableFuzzy: false,
		placeholder: "请输入查询关键字"
	},
	{
		name: "权限编码",
		key: "purview",
		type: "input",
		enableFuzzy: false,
		placeholder: "请输入查询关键字"
	},
	{
		name: "权限类型",
		key: "menuLevel",
		type: "select",
		enableFuzzy: false,
		placeholder: "请选择",
		children: []
	}
])
const refTable =ref()
const queryData = ref<any>({})
const getQueryData = (queryParams?: any) => {
	paginationData.value.currentPage = 1
	refTable.value.resetCurrentPage()
	queryData.value.menuName = queryParams.menuName ? queryParams.menuName : ""
	queryData.value.purview = queryParams.purview ? queryParams.purview : ""
	queryData.value.menuLevel = queryParams.menuLevel ? queryParams.menuLevel : ""
	getBaseSystemList()
}
const getDictionaryData = () => {
	getDictionaryTerm({
		dataDictionaryCode: "MENU_LEVEL"
	}).then((res: any) => {
		if (res && res.length) {
			const children: any[] = []
			res.forEach((item: any) => {
				children.push({
					label: item.subitemName,
					value: item.subitemValue
				})
			})
			 roleTypeList.value = children
			 queryArrList.value[2].children = children
		}
	})
	getDictionaryTerm({
		dataDictionaryCode: "MENU_DISPLAY_TYPE"
	}).then((res: any) => {
		if (res && res.length) {
			const children: any[] = []
			res.forEach((item: any) => {
				children.push({
					label: item.subitemName,
					value: item.subitemValue
				})
			})
			renderTypeList.value = children
		}
	})
}
getDictionaryData()
</script>

<template>
	<div class="app-container-row">
		<div class="app-left-container">
			<div class="app-content-wrapper">
				<div class="app-content-group">
					<ModelFrame>
						<Title :title="title" />
						<div class="app-el-scrollbar-wrapper">
							<PitayaTree
								:treeData="treeData"
								:treeProps="TreeProp"
								:needCheckBox="false"
								v-model:treeBizId="treeBizId"
								:defaultExpandedKeys="defaultExpandedKeys"
								@onTreeClick="onTreeClick"
								@onTreeExpand="onTreeClick"
								:tree-loading="treeLoading"
							/>
						</div>
					</ModelFrame>
				</div>
			</div>
		</div>
		<div class="right-model-frame">
			<ModelFrame>
				<Query
					class="ml10"
					:queryArrList="queryArrList"
					@getQueryData="getQueryData"
				/>
			</ModelFrame>
			<ModelFrame class="content">
				<Title :title="tableTitle" :button="button" @onBtnClick="onBtnClick" />
				<div class="app-el-scrollbar-wrapper">
					<el-scrollbar>
						<PitayaTable
							ref="refTable"
							@onCurrentPageChange="handlerPaginationChanged"
							:table-data="baseTableData"
							:columns="baseColumns"
							:total="dataTotal"
							:table-loading="tableLoading"
						>
							<template #alwaysShow="{ rowData }">
								<span>{{ rowData.alwaysShow ? "是" : "否" }}</span>
							</template>
							<template #menuDisplayType="{ rowData }">
								<span>
									{{ _menuDisplayTypeShowText(rowData.menuDisplayType) }}
								</span>
							</template>
							<template #menuLevel="{ rowData }">
								<span>
									{{ getDicDescEcho(rowData.menuLevel, roleTypeList) }}
								</span>
							</template>
							<template #menuIcon="{ rowData }">
								<font-awesome-icon
									v-if="rowData.menuIcon"
									:icon="_matchIcon(rowData.menuIcon)"
									style="color: var(--pitaya-btn-background)"
								/>
								<span v-else>---</span>
							</template>
							<template #operations="{ rowData }">
								<el-button v-btn link @click="onEdit(rowData)">
									<font-awesome-icon
										:icon="['fas', 'pen-to-square']"
										style="color: var(--pitaya-btn-background)"
									/>
									<span class="table-inner-btn">编辑</span>
								</el-button>
								<el-button
									v-btn
									color="var(--pitaya-btn-background)"
									link
									@click="onDelete(rowData)"
								>
									<font-awesome-icon
										:icon="['fas', 'trash-can']"
										style="color: var(--pitaya-btn-background)"
									/>
									<span class="table-inner-btn">移除</span>
								</el-button>
							</template>
						</PitayaTable>
					</el-scrollbar>
				</div>
			</ModelFrame>
		</div>

		<Drawer :size="drawerSize" v-model:drawer="drawerState" @close="onClose">
			<div class="common-from-wrapper" v-loading="drawerLoading">
				<Title :title="drawerTitle" />
				<div class="common-from-group">
					<el-scrollbar>
						<el-form
							class="el-form-wrapper"
							ref="baseFormRef"
							:model="baseFormData"
							:rules="formRules"
							label-width="auto"
							label-position="top"
						>
							<el-form-item label="上级权限">
								<el-input
									v-model="baseFormData[_matchFormProp('menuParent')]"
									disabled
								/>
							</el-form-item>
							<el-form-item label="权限类型" prop="menuLevel">
								<el-select
									v-model="baseFormData[_matchFormProp('menuLevel')]"
									style="width: 100%"
									clearable
								>
									<el-option
										v-for="(item, index) in roleTypeList"
										:key="index"
										:label="item.label"
										:value="item.value"
									/>
								</el-select>
							</el-form-item>
							<el-form-item
								label="权限编码"
								prop="purview"
								:rules="[
									{ 
										required: true,
										message: '请输入权限编码', 
										trigger: ['blur', 'change'] 
									},
									{
										pattern: /^[a-zA-Z-:0-9]+$/,
										message: '权限编码只能包含英文字母大小写、-、：、和数字。',
										trigger: 'blur'
									}
								]"
							>
								<el-input v-model.trim="baseFormData.purview" />
							</el-form-item>
							<el-form-item
								label="权限名称"
								prop="menuBtnText"
								v-if="baseFormData.menuLevel === '4'"
							>
								<el-input v-model.trim="baseFormData.menuBtnText" />
							</el-form-item>
							<el-form-item
								label="渲染方式"
								prop="menuDisplayType"
								v-if="baseFormData.menuLevel === '4'"
							>
								<el-select
									v-model="baseFormData[_matchFormProp('menuDisplayType')]"
									style="width: 100%"
									clearable
								>
									<el-option
										v-for="(item, index) in renderTypeList"
										:key="index"
										:label="item.label"
										:value="item.value"
									/>
								</el-select>
							</el-form-item>

							<el-form-item
								v-if="baseFormData.menuLevel !== '4'"
								label="权限名称"
								prop="menuName"
							>
								<el-input
									maxlength="50"
									:show-word-limit="true"
									v-model.trim="baseFormData[_matchFormProp('menuName')]"
								/>
							</el-form-item>
							<el-form-item
								v-if="
									baseFormData.menuLevel !== '4' &&
									baseFormData.menuLevel !== '3'
								"
								label="图标样式"
							>
								<el-input
									maxlength="50"
									:show-word-limit="true"
									v-model.trim="baseFormData.menuIcon"
								/>
							</el-form-item>
							<el-form-item label="排序">
								<el-input-number
									style="width: 100%"
									controls-position="right"
									v-model="baseFormData.sortedBy"
									:min="1"
									:max="10000"
								/>
							</el-form-item>

							<!--  -->
							<el-form-item
								v-if="baseFormData.menuLevel !== '4'"
								label="组件名称"
								prop="name"
							>
								<el-input
									:show-word-limit="true"
									v-model.trim="baseFormData[_matchFormProp('name')]"
								/>
							</el-form-item>
							<el-form-item
								v-if="baseFormData.menuLevel !== '4'"
								label="路由路径"
								prop="path"
							>
								<el-input
									:show-word-limit="true"
									v-model.trim="baseFormData[_matchFormProp('path')]"
								/>
							</el-form-item>
							<el-form-item
								v-if="baseFormData.menuLevel !== '4'"
								label="组件路径"
								prop="component"
							>
								<el-input
									:show-word-limit="true"
									v-model.trim="baseFormData[_matchFormProp('component')]"
								/>
							</el-form-item>
							<el-form-item
								label="是否显示"
								v-if="baseFormData.menuLevel !== '4'"
							>
								<el-switch v-model="baseFormData.alwaysShow" />
							</el-form-item>
						</el-form>
					</el-scrollbar>
				</div>
				<div class="btn-groups">
					<ButtonList :button="btnList" @onBtnClick="onFormBtnClick" />
				</div>
			</div>
		</Drawer>
	</div>
</template>

<style lang="scss" scoped>
.app-container-row {
	flex-direction: row;
	.app-left-container {
		height: 100%;
		width: 20%;
		min-width: 300px;
		flex: 0 0 20%;
		display: flex;
		flex-direction: column;
		margin-right: 10px;
	}
}
.app-content-wrapper {
	height: 0;
	flex: 1;
	.app-content-group {
		height: 100%;
		:deep(.model-frame-wrapper) {
			height: 100%;
			padding-bottom: 10px;
			overflow: hidden;
			display: flex;
			flex-direction: column;
		}
		.app-el-scrollbar-wrapper {
			height: 0;
			flex: 1;
			.table-inner-btn {
				margin-left: 5px;
				color: #204a9c;
			}
		}
	}
}
.common-from-wrapper {
	height: 100%;
	width: 100%;
	display: flex;
	flex-direction: column;
	.common-from-group {
		height: 0;
		flex: 1;
		padding: 0 10px;
	}
	.btn-groups {
		display: flex;
		justify-content: flex-end;
		padding: 10px 10px 0 0;
		border-top: 1px solid var(--pitaya-border-color);
	}
}
</style>
@/app/platform/hooks/usePagination
@/app/platform/api/system/requestMap@/app/platform/api/system/dictionary
