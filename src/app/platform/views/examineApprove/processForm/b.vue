<script setup lang="ts">
import { reactive, ref } from 'vue'
import type { FormProps } from 'element-plus'

const labelPosition = ref<FormProps['labelPosition']>('right')

const formLabelAlign = reactive({
  name: '',
  region: '',
  type: '',
})

const props = defineProps({
	formValue: {
		type: Object,
		default: () => {},
		requeired: false
	},
	drawerApproved:{
		type: Object,
		default: () => {},
	}
})

</script>
<template>
	<div class="box">
		bbb
		<!-- <el-form
			:label-position="labelPosition"
			label-width="100px"
			:model="formLabelAlign"
			style="max-width: 460px"
		>
			<el-form-item label="名称">
				<el-input v-model="props.drawerApproved.formData.taskTitle" />
			</el-form-item>
			<el-form-item label="组建taskId">
				<el-input v-model="props.drawerApproved.formData.taskId" />
			</el-form-item>
			<el-form-item label="组建类型">
				<el-input v-model="props.drawerApproved.formData.formType" />
			</el-form-item>
		</el-form> -->
	</div>
</template>
<style scoped lang="scss">
.box{
	padding: 20px ;
}

</style>
