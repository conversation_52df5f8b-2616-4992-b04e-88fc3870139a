<!-- 我发起的 -->
<script setup lang="ts">
import ApprovedExtend from "@/app/platform/views/examineApprove/ApprovedExtend.vue"
import {
	getProcessListData,
	getRevocationHook,
	approvalDemo
} from "@/app/platform/api/examineApproved"
import { usePagination } from "@/app/platform/hooks/usePagination"
import { getProcessDesignList } from "@/app/platform/api/system/processDesign"
import { useUserStoreHook } from "@/app/platform/store/modules/user"
import DynamicCompont from "./dynamicCompont.vue"
import { useBackBtnStore } from "@/app/platform/store/modules/backBtn"
import { FormInstance, FormRules } from "element-plus"

const backBtnStore = useBackBtnStore()
const { backBtnState } = storeToRefs(backBtnStore)
const usePaginationStore = usePagination()
const paginationData = ref<PaginationData>(usePaginationStore.paginationData)

// 查询条件
const queryArrList = [
	{
		name: "业务标题",
		key: "businessTitle",
		placeholder: "请输入查询关键字",
		type: "input"
	},
	// {
	// 	name: "流程编号",
	// 	key: "processDefinitionId",
	// 	placeholder: "请输入查询关键字",
	// 	type: "input"
	// },
	{
		name: "流程名称",
		key: "processDefinitionName",
		placeholder: "请输入查询关键字",
		type: "input"
	}
]

const tableLoading = ref(false)

const userInfo = useUserStoreHook()
const activeName = ref("")
const processData = ref<any>()
const processFormRef = ref<any>()
const queryDataTemp = ref()
const refInitiateTable = ref()
// 查询方法
const getQueryData = (queryData: any) => {
	queryDataTemp.value = queryData
	pageInfo.value.currentPage = 1
	refInitiateTable.value.resetCurrentPage()
	getUserProcessData()
}
const getUserProcessData = () => {
	tableLoading.value = true
	getProcessListData({
		...queryDataTemp.value,
		currentPage: pageInfo.value.currentPage,
		pageSize: pageInfo.value.pageSize
		// applyer: userInfo.userInfo.userName
	})
		.then((res: any) => {
			// if (res.rows && res.rows.length > 0) {
			tableData.value = res.rows
			pageInfo.value.total = res.records
			// }
		})
		.finally(() => {
			tableLoading.value = false
		})
}
// 列表相关
const tableTitle = ref({
	name: ["我发起的"],
	icon: ["fas", "square-share-nodes"]
})
//发起审批-用于发起框架审批流程测试
// const button = [
// 	{
// 		name: "发起审批",
// 		icon: ["fas", "square-plus"]
// 	}
// ]
const tableData = ref<any[]>([])
const tableColumn = ref<any[]>([
	// { prop: "code", label: "流程编码" },
	{ prop: "processDefinitionName", label: "流程名称" },
	{ prop: "businessTitle", label: "业务标题",minWidth:150 },
	{ prop: "instStartTime", label: "开始时间", width: 150 },
	{ prop: "instEndTime", label: "结束时间", width: 150 },
	{ prop: "timeConsuming", label: "耗时", width: 120 },
	{ prop: "status", label: "状态", width: 100 },
	{
		prop: "operations",
		fixed: "right",
		label: "操作",
		needSlot: true,
		width: 140
	}
])
const pageInfo = ref<any>({
	currentPage: paginationData.value.currentPage,
	pageSize: paginationData.value.pageSize,
	total: 0
})
const onCurrentPageChange = (pageData: any) => {
	pageInfo.value.pageSize = pageData.pageSize
	pageInfo.value.currentPage = pageData.currentPage
	getUserProcessData()
}

// 流程编码对应的组件
// const codeContrast = {
// 	fault_influence_rule: "degreeApprove", //影响程度
// 	fault_level_rule: "classificationApproved", // 故障分类
// 	maint_rule: "repairProcedureApproved", //修程管理审批
// 	maint_workStandard: "standardOperationApproved", //标准工作
// 	fault_nature: "suggestApproved" // 维修建议
// }

// watchEffect(() => {
// 	if (
// 		backBtnState.value.name === "yearPlanApprovelLook" &&
// 		backBtnState.value.state
// 	) {
// 		isYearPlan.value = false
// 		backBtnState.value.name = ""
// 		backBtnState.value.state = false
// 	}
// })

const isYearPlan = ref(false)
const refApprovedExtend = ref()
const showPlanDetails = ref()
const handleSeeClick = (row: any) => {
	drawerInitiate.taskId = row.processInstanceId
	//设置组件参数
	processData.value = row
	processData.value.formData = {}
	// const key = row.processDefinitionId.split(":")[0]
	// processData.value.formData.revocationHook = codeContrast[key]
	processData.value.formData.businessId = row.businessId

	drawerInitiate.value.state = true
	processData.value.removeAdd = true
	drawerInitiate.value.showExpend = true
	//加载表单组件
	nextTick(() => {
		refApprovedExtend.value.getTaskList()
		if (row.formUrlPath !== "") {
			console.log("row.formUrlPath", row.formUrlPath)
			processFormRef.value.loadComponent(row.formUrlPath)
		} else {
			ElMessage.warning("页面路径不能为空")
		}
	})
}

// 查看审批表单弹窗 相关配置项
const drawerInitiate = ref<any>({
	size: 1750,
	title: {
		name: ["审批表单"],
		icon: ["fas", "square-share-nodes"]
	},
	state: false,
	showExpend: false,
	taskId: undefined,
	open: function handleOpenClick(id: number | null) {
		drawerInitiate.value.showExpend = true
	},
	close: function handleCloseClick() {
		drawerInitiate.value.size=1750
		drawerInitiate.value.showExpend = false
	}
})
const handleClose = (done: () => void) => {
	drawerInitiate.value.size=1750
	done()
}
//
const drawerSize = 310
const drawerState = ref(false)
const baseFormRef = ref<FormInstance>()

const drawerTitle = {
	name: ["新增审批"],
	icon: ["fas", "square-share-nodes"]
}

const formData = reactive<anyKey>({
	taskTitle: "",
	processDefKey: ""
})
const formRules = reactive<FormRules<typeof formData>>({
	taskTitle: [{ required: true, message: "请输入业务标题", trigger: "blur" }],
	processDefKey: [
		{ required: true, message: "请选择流程编码", trigger: "blur" }
	]
})

const submitLoading = ref(false)
const btnList = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "保存",
		icon: ["fas", "floppy-disk"]
	}
]
const onBtnClick = () => {
	drawerState.value = true
}
const onClose = () => {
	formData.processDefKey = ""
	formData.taskTitle = ""
	drawerState.value = false
	baseFormRef.value?.resetFields()
}
const onFormBtnClick = (btnName: string | undefined) => {
	if (!baseFormRef.value) return
	if (btnName === "保存") {
		baseFormRef.value.validate((valid) => {
			if (valid) {
				approvalDemo({
					formData: {
						taskTitle: formData.taskTitle,
						formType: 11
					},
					processDefKey: formData.processDefKey
				}).then(() => {
					drawerState.value = false
					ElMessage.success("保存成功")
					getUserProcessData()
				})
			} else {
				return false
			}
		})
		return
	}

	if (btnName === "取消") {
		drawerState.value = false
		return
	}
}

// 获取表格数据
const professionTreeData = ref([])
const getProfessionTreeData = () => {
	getProcessDesignList({
		pageSize: 1000,
		currentPage: 1
	}).then((res: any) => {
		professionTreeData.value = res.rows
	})
}

//撤回
const revokeClick = (data: any) => {
	CustomMessageBox({ message: "确定要撤回吗?" }, (res) => {
		if (res) {
			let params = {
				processInstId: data.processInstanceId,
				businessId: data.businessId,
				versionId: data.versionId
			}
			getRevocationHook(params, data.revocationHook).then((res) => {
				ElMessage({
					message: "撤回成功",
					type: "success"
				})
				getUserProcessData()
			})
		}
	})
}
//子组件通过emit调用父亲组建方法设置宽度
const sizeChange = (size: any) => {
	drawerInitiate.value.size = size
}
onMounted(() => {
	getUserProcessData()
})

/**
 *
 * 需要传递的方法
 *
 * 1.provide 定义 greet 方法
 * 2.updateGreet接受动态表单页面的方法
 * 3.审批页面点击通过或驳回触发greet方法
 * 4.动态表单页面将updateGreet返回值给审批页面
 *
 * 注意：
 * 动态表单页面需返回Promise
 * **/
const greet = ref()
const updateGreet = (fun: any) => {
	greet.value = fun //fun:Promise
}
// 使用 provide 提供方法
provide("greet", { greet, updateGreet })
</script>

<template>
	<div class="app-container">
		<ModelFrame v-show="!isYearPlan">
			<Query
				class="initiate-query ml10"
				:queryArrList="queryArrList"
				@getQueryData="getQueryData"
			/>
		</ModelFrame>
		<div class="app-content-wrapper" v-show="!isYearPlan">
			<div class="app-content-group">
				<ModelFrame>
					<Title
						:title="tableTitle"
						@onBtnClick="onBtnClick"
					/>

					<div class="app-el-scrollbar-wrapper">
						<el-scrollbar>
							<PitayaTable
								ref="refInitiateTable"
								:table-data="tableData"
								:columns="tableColumn"
								:need-index="true"
								:need-pagination="true"
								:total="pageInfo.total"
								@on-current-page-change="onCurrentPageChange"
								:table-loading="tableLoading"
							>
								<template #operations="{ rowData }">
									<el-button v-btn link @click="handleSeeClick(rowData)">
										<font-awesome-icon
											:icon="['fas', 'fa-eye']"
											style="color: var(--pitaya-btn-background)"
										/>
										<span class="table-inner-btn"> 查看 </span>
									</el-button>
									<el-button
										v-btn
										link
										@click="revokeClick(rowData)"
										v-if="rowData.revocationHook"
									>
										<font-awesome-icon
											:icon="['fas', 'undo']"
											style="color: var(--pitaya-btn-background)"
										/>
										<span class="table-inner-btn">撤回</span>
									</el-button>
								</template>
							</PitayaTable>
						</el-scrollbar>
					</div>
				</ModelFrame>
			</div>
		</div>
		<!-- 查看窗口 -->
		<Drawer
			:size="drawerInitiate.size"
			v-model:drawer="drawerInitiate.state"
			@open="drawerInitiate.open"
			@close="drawerInitiate.close"
			:before-close="handleClose"
			:destroyOnClose="true"
		>
			<div class="drawer-box">
				<div class="drawer-box-left">
					<DynamicCompont
						:form-value="processData"
						ref="processFormRef"
						:isMySatrt="true"
						@sizeChange="sizeChange"
					/>
				</div>
				<div class="drawer-box-right">
					<ApprovedExtend
						ref="refApprovedExtend"
						:isMySatrt="true"
						v-if="drawerInitiate.showExpend"
						:process-definition-id="processData.processDefinitionId"
						:process-instance-id="processData.processInstanceId"
						type="history"
					/>
				</div>
			</div>
		</Drawer>
		<!-- 发起审批 -->
		<Drawer :size="drawerSize" v-model:drawer="drawerState" @close="onClose">
			<div class="common-from-wrapper common-from-only" v-if="drawerState">
				<Title :title="drawerTitle" />
				<div class="common-from-group">
					<el-scrollbar>
						<el-form
							class="el-form-wrapper"
							ref="baseFormRef"
							:model="formData"
							:rules="formRules"
							label-width="auto"
							label-position="top"
						>
							<el-form-item label="流程编码" prop="processDefKey">
								<el-select
									v-model="formData.processDefKey"
									style="width: 100%"
									clearable
									@click="getProfessionTreeData"
								>
									<el-option
										v-for="(item, index) in professionTreeData"
										:key="index"
										:label="item.code"
										:value="item.code"
									/>
								</el-select>
							</el-form-item>
							<el-form-item label="业务标题" prop="taskTitle">
								<el-input v-model="formData.taskTitle" />
							</el-form-item>
							<!-- <el-form-item label="页面地址" prop="revocationHook">
								<el-input v-model="formData.revocationHook" />
							</el-form-item> -->
						</el-form>
					</el-scrollbar>
				</div>
				<div class="btn-groups">
					<ButtonList
						:button="btnList"
						:loading="submitLoading"
						@onBtnClick="onFormBtnClick"
					/>
				</div>
			</div>
		</Drawer>
	</div>
</template>

<style lang="scss" scoped>
.app-container {
	.el-drawer__body {
		overflow: hidden;
	}
	:deep(.model-frame-wrapper) {
		padding-bottom: 0px;
	}
	:deep(.app-container-row) {
		height: 100%;
	}

	:deep(.plan-right-model-frame .plan-right-approved-content) {
		height: 100%;
	}
}
.app-content-wrapper {
	height: 0;
	flex: 1;
	margin-top: 10px;
	.app-content-group {
		height: 100%;
		:deep(.model-frame-wrapper) {
			height: 100%;
			padding-bottom: 10px;
			overflow: hidden;
			display: flex;
			flex-direction: column;
		}
		.app-el-scrollbar-wrapper {
			height: 0;
			flex: 1;
			.table-inner-btn {
				margin-left: 5px;
				color: #204a9c;
			}
		}
	}
}
.drawer-box {
	height: 100%;
	display: flex;

	.drawer-box-left {
		display: flex;
		flex-direction: column;
		height: 100%;
		border-right: 1px solid #ccc;
		width: calc(100% - 400px);
		.drawer-content {
			flex: 1;
		}

		.btn-groups {
			margin-top: 10px;
			display: flex;
			flex-direction: row-reverse;
		}
	}

	.drawer-box-right {
		width: 400px;
		padding: 0 10px;
	}
}
.btn-groups {
	width: 290px;
	position: fixed;
	bottom: 10px;
	display: flex;
	justify-content: flex-end;
	padding: 10px 10px 0 0;
	border-top: 1px solid var(--pitaya-border-color);
}
.common-from-group {
	padding: 0 10px;
}
</style>
