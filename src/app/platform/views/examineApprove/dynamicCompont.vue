<script setup lang="ts">
//动态引入组建
const baselineComponents = import.meta.glob(
	"/src/app/baseline/views/processForm/*.vue"
)
const projectComponents = import.meta.glob(
	"/src/app/project/views/processForm/*.vue"
)

//将动态组建集合到data中
const componentsList = { ...baselineComponents, ...projectComponents }

const props = defineProps({
	formValue: {
		type: Object,
		default: () => ({})
	},
	isMySatrt: {
		type: Boolean,
		default:false
	}
})

const componentLoaded = ref(false)
const formComponent = ref<any>()

const formValue = toRef(props.formValue)
//加载组件
const loadComponent = (componentPath: string) => {
	const thisComponent: any = defineAsyncComponent(componentsList[componentPath])

	formComponent.value = thisComponent

	componentLoaded.value = true
}

defineExpose({
	loadComponent
})
</script>

<template>
	<component
		v-if="componentLoaded"
		:is="formComponent"
		:formValue="formValue"
		:isMySatrt="isMySatrt"
	/>
</template>
