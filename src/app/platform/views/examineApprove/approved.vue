<!-- 我审批的 -->
<script setup lang="ts">
import { provide } from "vue"
import ApprovedExtend from "./ApprovedExtend.vue"
import {
	dealwithComplete,
	getMyTask,
	getMyHisTasks
} from "@/app/platform/api/examineApproved/index"
import DynamicCompont from "./dynamicCompont.vue"
import { useBackBtnStore } from "@/app/platform/store/modules/backBtn"
import { usePagination } from "@/app/platform/hooks/usePagination"

const tableLoading = ref(false)
const backBtnStore = useBackBtnStore()
const usePaginationStore = usePagination()
const paginationData = ref<PaginationData>(usePaginationStore.paginationData)
// 查询条件
const queryArrList = [
	// {
	// 	name: "流程编号",
	// 	key: "processDefinitionId",
	// 	placeholder: "请输入查询关键字",
	// 	type: "input"
	// },
	{
		name: "流程名称",
		key: "processDefinitionName",
		placeholder: "请输入查询关键字",
		type: "input"
	},
	{
		name: "任务发起人",
		key: "applyer",
		placeholder: "请输入查询关键字",
		type: "input"
	}
]

const refApprovedTable = ref()
const queryDataTemp = ref()
// 查询方法
const getQueryData = (queryData: any) => {
	queryDataTemp.value = queryData
	refApprovedTable.value.resetCurrentPage()
	pageInfo.value.currentPage = 1
	getApprovedTableList()
}
// table切换tabs
const tabList = [
	{
		name: "我的待办",
		icon: ["fas", "square-plus"]
	},
	{
		name: "我的已办",
		icon: ["fas", "square-plus"]
	}
]
const isMySatrt = ref(false)
const activeName = ref(tabList[0].name)
const handleTabClick = (tab: any) => {
	drawerApproved.value.formType =
		activeName.value === "我的待办" ? "todo" : "done"
	nextTick(() => {
		pageInfo.value.currentPage = 1
		getApprovedTableList()
	})
}
// 列表相关
const tableTitle = ref({
	name: ["我审批的"],
	icon: ["fas", "square-share-nodes"]
})
const tableData = ref<any[]>([
	// {
	// 	taskTitle: "测试流程",
	// 	processName: "测试流程",
	// 	processCode: "123456",
	// 	status: "审批中",
	// 	initiator: "张三",
	// 	beginTime: "2021-12-12 12:12:12"
	// }
])
const tableColumn = ref<any[]>([
	// { prop: "code", label: "流程编码" },
	{ prop: "processDefinitionName", label: "流程名称",  },
	{ prop: "processDefinitionTitle", label: "业务标题",minWidth:150},
	{ prop: "applyer_view", label: "发起人", width: 100 },
	{ prop: "timeStart", label: "开始时间", width: 150 },
	{ prop: "timeEnd", label: "结束时间", width: 150 },
	{ prop: "timeConsuming", label: "耗时", width: 100 },
	{ prop: "name", label: "当前节点", width: 130 },
	{
		prop: "operations",
		fixed: "right",
		label: "操作",
		needSlot: true,
		width: 140
	}
])
const pageInfo = ref<any>({
	currentPage: paginationData.value.currentPage,
	pageSize: paginationData.value.pageSize,
	total: 0
})
const onCurrentPageChange = (pageData: any) => {
	pageInfo.value.pageSize = pageData.pageSize
	pageInfo.value.currentPage = pageData.currentPage
	getApprovedTableList()
}

// 操作项文字显示
const _textShow = computed((): any => {
	return function (taskDefinitionKey: string) {
		if (activeName.value === "我的已办") return "查看"
		return taskDefinitionKey == "activity_update1" ? "修改" : "审批"
	}
})

// watchEffect(() => {
// 	if (
// 		backBtnState.value.name === "yearPlanApprovel" &&
// 		backBtnState.value.state
// 	) {
// 		isYearPlan.value = false
// 		backBtnState.value.name = ""
// 		backBtnState.value.state = false
// 		getApprovedTableList()
// 	}
// })
// 查看审批表单弹窗 相关配置项
const refApprovedExtend = ref()
const isYearPlan = ref<boolean>(false)
const handleApprovedClick = (row: any) => {
	isMySatrt.value = activeName.value === "我的待办" ? false : true
	drawerApproved.value.form = row
	drawerApproved.id = row.id
	drawerApproved.value.taskId = row.taskId
	drawerApproved.value.processInstanceId = row.processInstanceId
	drawerApproved.value.processInstanceKey = row.taskDefinitionKey
	drawerApproved.value.processDefinitionId = row.processDefinitionId
	drawerApproved.value.businessId = row.businessId
	drawerApproved.value.formData = {}

	drawerApproved.value.state = true
	drawerApproved.value.showExpend = true
	drawerApproved.value.formData.businessId = row.businessId

	//row.formData.formType：组建名称
	nextTick(() => {
		refApprovedExtend.value.getTaskList()
		if (row.formUrlPath !== "") {
			formComponent.value.loadComponent(row.formUrlPath)
		} else {
			ElMessage.warning("页面路径不能为空")
		}
	})
}

const drawerApproved = ref<any>({
	size: 1750,
	title: {
		name: ["审批表单"],
		icon: ["fas", "square-share-nodes"]
	},
	state: false,
	formType: "myApproved",
	// 审批扩展信息展示否
	showExpend: false,
	// 被审批信息的id
	id: undefined,
	taskId: undefined,
	processInstanceId: undefined,
	processInstanceKey: undefined,
	processDefinitionId: undefined,
	form: {}
})
const handleOpenClick = () => {
	drawerApproved.value.showExpend = true
}
const handleCloseClick = () => {
	drawerApproved.value.size=1750
	drawerApproved.value.showExpend = false
}

const handleClose = (done: () => void) => {
	drawerApproved.value.size=1750
	done()
}
const handleComplete = (inputForm: { input: any; result: any }) => {
	drawerApproved.value.state = false
	tableLoading.value = true
	dealwithComplete({
		taskId: drawerApproved.value.taskId,
		comment: inputForm.input,
		agree: inputForm.result
	})
		.then((res: any) => {
			if (res) {
				ElMessage({
					message: "操作成功",
					type: "success"
				})
				pageInfo.value.currentPage = 1
				getApprovedTableList()
			}
		})
		.finally(() => {
			tableLoading.value = false
		})
}
//子组件通过emit调用父亲组建方法设置宽度
const sizeChange = (size: any) => {
	drawerApproved.value.size = size
}
onMounted(() => {
	getApprovedTableList()
	isMySatrt.value = false
})

function getApprovedTableList() {
	tableLoading.value = true
	let queryFunction = getMyTask
	if (activeName.value == "我的已办") {
		queryFunction = getMyHisTasks
	}
	queryFunction({
		...queryDataTemp.value,
		currentPage: pageInfo.value.currentPage,
		pageSize: pageInfo.value.pageSize
	})
		.then((res: any) => {
			tableData.value = res.rows
			pageInfo.value.total = res.records
		})
		.finally(() => {
			tableLoading.value = false
		})
}

const formComponent = ref<any>()

/**
 *
 * 需要传递的方法
 *
 * 1.provide 定义 greet 方法
 * 2.updateGreet接受动态表单页面的方法
 * 3.审批页面点击通过或驳回触发greet方法
 * 4.动态表单页面将updateGreet返回值给审批页面
 *
 * 注意：
 * 动态表单页面需返回Promise
 * **/
const greet = ref()
const updateGreet = (fun: any) => {
	greet.value = fun //fun:Promise
}
// 使用 provide 提供方法
provide("greet", { greet, updateGreet })
</script>

<template>
	<div class="app-container">
		<ModelFrame v-show="!isYearPlan">
			<Query
				class="approved-query ml10"
				:queryArrList="queryArrList"
				@getQueryData="getQueryData"
			/>
		</ModelFrame>
		<div class="app-content-wrapper" v-show="!isYearPlan">
			<div class="app-content-group">
				<ModelFrame>
					<Title :title="tableTitle">
						<div class="approved-tabs-wrapper">
							<el-tabs v-model="activeName" @tab-click="handleTabClick">
								<el-tab-pane
									v-for="(tab, index) in tabList"
									:key="index"
									:label="tab.name"
									:name="tab.name"
									:index="tab.name"
								/>
							</el-tabs>
						</div>
					</Title>
					<div class="app-el-scrollbar-wrapper">
						<el-scrollbar>
							<PitayaTable
								ref="refApprovedTable"
								:table-data="tableData"
								:columns="tableColumn"
								:need-index="true"
								:need-pagination="true"
								:total="pageInfo.total"
								@on-current-page-change="onCurrentPageChange"
								:table-loading="tableLoading"
							>
								<template #operations="{ rowData }">
									<el-button v-btn link @click="handleApprovedClick(rowData)">
										<font-awesome-icon
											v-if="_textShow(rowData.taskDefinitionKey) === '查看'"
											:icon="['fas', 'fa-eye']"
											style="color: var(--pitaya-btn-background)"
										/>
										<font-awesome-icon
											v-else
											:icon="['fas', 'pen-to-square']"
											style="color: var(--pitaya-btn-background)"
										/>

										<span class="table-inner-btn">
											{{ _textShow(rowData.taskDefinitionKey) }}
										</span>
									</el-button>
								</template>
							</PitayaTable>
						</el-scrollbar>
					</div>
				</ModelFrame>
			</div>
		</div>
		<!-- 查看按钮弹窗 -->
		<Drawer
			class="drawer-hidden-box"
			:size="drawerApproved.size"
			v-model:drawer="drawerApproved.state"
			@open="handleOpenClick"
			@close="handleCloseClick"
			:before-close="handleClose"
			show-close
			:destroyOnClose="true"
		>
			<div class="drawer-box">
				<div class="drawer-box-left">
					<!-- <Title :title="drawerApproved.title" /> -->
					<!-- <el-scrollbar class="drawer-content"> -->
					<DynamicCompont
						v-if="drawerApproved.showExpend"
						:form-value="drawerApproved"
						ref="formComponent"
						:isMySatrt="isMySatrt"
						@sizeChange="sizeChange"
					/>
					<!-- </el-scrollbar> -->
				</div>
				<div class="drawer-box-right">
					<ApprovedExtend
						:isMySatrt="isMySatrt"
						:taskId="drawerApproved.taskId"
						@on-pass="handleComplete"
						@on-reject="handleComplete"
						v-if="drawerApproved.showExpend"
						ref="refApprovedExtend"
						:processInstanceId="drawerApproved.processInstanceId"
						:processDefinitionId="drawerApproved.processDefinitionId"
						:type="drawerApproved.formType"
					/>
				</div>
			</div>
		</Drawer>
	</div>
</template>

<style scoped lang="scss">
@import url("../../../../styles/common-from-wrapper.scss");

.app-container {
	:deep(.model-frame-wrapper) {
		padding-bottom: 0px;
	}

	:deep(.app-container-row) {
		height: 100%;
	}

	:deep(.plan-right-model-frame .plan-right-approved-content) {
		height: 100%;
	}
}

.app-content-wrapper {
	height: 0;
	flex: 1;
	margin-top: 10px;

	.app-content-group {
		height: 100%;

		:deep(.model-frame-wrapper) {
			height: 100%;
			padding-bottom: 10px;
			overflow: hidden;
			display: flex;
			flex-direction: column;
		}

		.app-el-scrollbar-wrapper {
			height: 0;
			flex: 1;

			.table-inner-btn {
				margin-left: 5px;
				color: #204a9c;
			}
		}

		.approved-tabs-wrapper {
			position: absolute;
			left: 150px;
			bottom: 0;

			:deep(.el-tabs__item) {
				font-size: 14px;
				color: #666;

				&.is-active {
					color: var(--pitaya-sidebar-menu-active-text-color);
				}
			}

			:deep(.el-tabs__active-bar) {
				background-color: var(--pitaya-header-bg-color);
			}

			:deep(.el-tabs__header) {
				margin-bottom: 0;
			}

			:deep(.el-tabs__nav-wrap) {
				&::after {
					width: 0;
					height: 0;
				}
			}
		}
	}
}

.drawer-box {
	height: 100%;
	display: flex;

	.drawer-box-left {
		display: flex;
		flex-direction: column;
		height: 100%;
		// border-right: 1px solid #ccc;
		width: calc(100% - 400px);
		.drawer-content {
			flex: 1;
		}

		.btn-groups {
			margin-top: 10px;
			display: flex;
			flex-direction: row-reverse;
		}
	}
	.drawer-box-left::after {
		content: '';
		position: absolute;
		right: 0;
		top: -10px;    /* 顶部向外扩展20px */
		bottom: -10px;  /* 底部向外扩展20px */
		width: 1px;
		background-color: #ccc; /* 边框颜色 */
	}
	.drawer-box-right {
		width: 400px;
		padding: 0 10px;
	}
}
</style>
