<!-- 审批意见扩展 包括意见信息、流程图 -->
<script setup lang="ts">
import { inject, onMounted } from "vue"
import { getTasks } from "@/app/platform/api/examineApproved/index"
import BPMNPreview from "@/app/platform/views/system/bpmn/preview.vue"
import { User, CircleCheck, CircleClose } from "@element-plus/icons-vue"
import { useUserStore } from "@/app/platform/store/modules/user"
import { getBaseRoleDetail } from "../../api/system/baseRole"
import { events } from "@/app/platform/utils/bus"

const emit = defineEmits<{
	(e: "onPass", inputForm: any): void
	(e: "onReject", inputForm: any): void
}>()
interface Props {
	processDefinitionId: string
	processInstanceId: string
	type: string
	isMySatrt?: boolean
	taskId?: string
}

const iconList = { check: CircleCheck, close: CircleClose }
const userStore = useUserStore()
const { userInfo } = storeToRefs(userStore)
const props = defineProps<Props>()

const processDefinitionId = toRef(props, "processDefinitionId")
const processInstanceId = toRef(props, "processInstanceId")
const taskId = toRef(props, "taskId")
const showApproved = toRef(props, "isMySatrt")

// 审批意见相关 ↓
const commonOptions = [
	{
		value: "同意",
		label: "同意"
	},
	{
		value: "不同意",
		label: "不同意"
	},
	{
		value: "请 XX 批阅",
		label: "请 XX 批阅"
	}
]
const opinionsData = reactive({
	history: [],
	current: "",
	input: "",
	result: true
})
function handleCurrentChange(params: string) {
	opinionsData.input = params
	if (params == "不同意") {
		btnList.value[0].disabled=true
		btnList.value[1].disabled=false
	}else if (params == "同意")  {
		btnList.value[0].disabled=false
		btnList.value[1].disabled=true
	}else{
		btnList.value[0].disabled=false
		btnList.value[1].disabled=false
	}
}
// 审批意见扩展 ↑

// 流程图↓
const processData = reactive({
	history: [{}]
})
const tableTitle = ref({
	name: ["历史流程跟踪"],
	icon: ["fas", "square-share-nodes"]
})
const tableColumn = ref<any[]>([
	{ prop: "processName", label: "名称" },
	{ prop: "processInstanceId", label: "流程实例ID", width: 100 },
	{ prop: "beginTime", label: "开始时间" },
	{ prop: "endTime", label: "结束时间" },
	{ prop: "processor", label: "负责人" },
	{ prop: "result", label: "处理结果" },
	{ prop: "opinion", label: "处理意见" }
])
// 流程图↑

// 根据type判断是创建/编辑/审批
const _editAllDisabled = computed(() => props.type != "done")

// onMounted(() => {
// 	//getHistoryData({ processInstanceId: props.processInstanceId })
// 	//getHistoryProcessData({ processInstanceId: props.processInstanceId })
// 	getTaskList()
// })

const taskList = ref<any[]>([])
const loading = ref(false)
function getTaskList() {
	loading.value = true
	getTasks({ processInstanceId: props.processInstanceId })
		.then((res: any) => {
			taskList.value = res.sort(
				(a: { timeStart: number }, b: { timeStart: number }) => {
					return a.timeStart - b.timeStart
				}
			)
		})
		.finally(() => {
			loading.value = false
		})
}
const title = {
	name: ["审批意见"],
	icon: ["fas", "square-share-nodes"]
}
const btnList = ref([
	{
		name: "通过",
		icon: ["fas", "check"],
		disabled: false
	},
	{
		name: "驳回",
		icon: ["fas", "times"],
		disabled: false
	}
])
const { greet, updateGreet } = inject("greet")
const onBtnClick = (btnName: any) => {
	// 审批确认前执行函数
	//在通过驳回前,调用注入方法，通过返回promise确定成功，失败。
	// 使用 inject 注入方法
	if (greet && greet.value) {
		greet
			.value()
			.then((res: any) => {
				if (res == "success") {
					doBtnClick(btnName)
				}
			})
			.catch((err: any) => {
				ElMessage.warning(err)
			})
	} else {
		doBtnClick(btnName)
	}
}

const doBtnClick = (btnName: any) => {
	if (opinionsData.input == "") {
		ElMessage.warning("请填写/选择审批意见")
		return
	}
	if (btnName === "通过") {
		opinionsData.result = true
		emit("onPass", opinionsData)
	}
	if (btnName === "驳回") {
		opinionsData.result = false
		emit("onReject", opinionsData)
	}
}

const getTimeLineIcon = (index: any, item: any) => {
	if (item.timeEnd) {
		if (item.formData && item.formData.rejectTaskId) {
			return iconList.close
		} else {
			return iconList.check
		}
	} else {
		return iconList.check
	}
}
const getTimeLineColor = (index: any, item: any) => {
	if (item.timeEnd) {
		if (item.formData && item.formData.rejectTaskId) {
			if (item.formData.rejectTaskId === item.taskId) {
				return "red"
			} else {
				return "#ccc"
			}
		} else {
			return "green"
		}
	} else {
		return "orange"
	}
}
const titleBtnList: any[] = [
	{
		name: "查看流程图",
		icon: ["fas", "fa-code-compare"]
	}
]
const bpmnDrawerState = ref(false)
const handleTitleClick = () => {
	bpmnDrawerState.value = true
}

const hasApproved = (data: any) => {
	if (showApproved.value) {
		return false
	}
	if (props.taskId !== data.taskId) {
		return false
	}
	if (data.timeEnd) {
		return false
	}
	return true
}
const userDrawerState = ref(false)
const roleUsers = ref([])
const userDrawerTitle = ref({
	name: ["当前角色关联用户"],
	icon: ["fas", "square-share-nodes"]
})
const baseUserColumnsLoading = ref(false)
const handleCandidateGroup = (data: any) => {
	userDrawerState.value = true
	baseUserColumnsLoading.value = true
	getBaseRoleDetail({ id: data.id })
		.then((res) => {
			roleUsers.value = res.baseUserList
		})
		.finally(() => {
			baseUserColumnsLoading.value = false
		})
}
const baseUserColumns = [
	{ prop: "username", label: "用户账号", width: 140 },
	{ prop: "realname", label: "用户姓名", width: 100 },
	{ prop: "orgName", label: "部门", minWidth: 200 }
]
const userDrawerBtnList = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	}
]
const userDrawerBtnClick = () => {
	userDrawerState.value = false
}
const userTableHeight = computed(() => {
	return document.body.clientHeight - 130
})
defineExpose({
	opinionsData,
	processData,
	getTaskList
})
</script>

<template>
	<div class="approved-extend-root" v-loading="loading">
		<Title
			:title="title"
			:button="titleBtnList"
			@on-btn-click="handleTitleClick"
		/>
		<div class="approved-extend-content">
			<div class="opinion">
				<el-timeline>
					<el-timeline-item
						v-for="(item, index) in taskList"
						:key="item.taskId"
						:timestamp="item.timeStart"
						:icon="getTimeLineIcon(index, item)"
						hide-timestamp
						:color="getTimeLineColor(index, item)"
						size="large"
					>
						<div class="timeline-content">
							<div class="timeline-title">{{ item.name }}</div>
							<div class="timeline-foot">
								{{ item.timeEnd ? item.timeEnd : "" }}
								<div
									v-if="!!item.orgId_view && item.orgId_view !== 'null'"
									class="depart_content"
								>
									<font-awesome-icon :icon="['fas', 'sitemap']" />
									<el-tooltip :content="item.orgId_view" placement="top">
										{{ item.orgId_view }}
									</el-tooltip>
								</div>
								<div v-if="item.assignee_view" class="user_content">
									<el-icon> <User /> </el-icon>
									<el-tooltip :content="item.assignee_view" placement="top">
										{{ item.assignee_view }}
									</el-tooltip>
								</div>
								<template
									v-if="item.candidateGroup"
								>
									<div style="justify-content: flex-start">
										<el-button v-btn link 
										class="el-timeline-button"
										@click="handleCandidateGroup(role)"
										v-for="(role, index) in item.candidateGroup"
										:key="index + '-roleName'">
											<font-awesome-icon
												:icon="['fas', 'fa-users']"
												style="color: var(--pitaya-btn-background)"
											/>
											<el-tooltip :content="role.roleName" placement="top">
												<span class="table-inner-btn">{{ role.roleName }}</span>
											</el-tooltip>
										</el-button>
									</div>
								</template>
								<template v-if="item.candidateUser">
									<div style="justify-content: flex-start">
										<el-button
											v-btn
											link
											class="el-timeline-button"
											v-for="(cuser, cindex) in item.candidateUser"
											:key="cindex + '-cuser'"
										>
											<font-awesome-icon
												:icon="['fas', 'fa-user']"
												style="color: var(--pitaya-btn-background)"
											/>
											<el-tooltip :content="cuser.realname" placement="top">
												<span class="table-inner-btn">{{ cuser.realname }}</span>
											</el-tooltip>
										</el-button>
									</div>
								</template>
							</div>
							<template v-if="hasApproved(item)">
								<div class="opinion-select">
									<div class="opinion-select-title">审批意见：</div>
									<el-select
										class="opinion-select-select"
										v-model="opinionsData.current"
										placeholder="请选择"
										@change="handleCurrentChange"
									>
										<el-option
											v-for="item in commonOptions"
											:key="item.value"
											:label="item.label"
											:value="item.value"
										/>
									</el-select>
								</div>
								<el-input
									class="opinion-input"
									v-model="opinionsData.input"
									:autosize="{ minRows: 4, maxRows: 8 }"
									type="textarea"
									placeholder="请输入"
								/>
								<ButtonList
									style="margin-top: 10px"
									:button="btnList"
									@on-btn-click="onBtnClick"
								/>
							</template>
							<div v-else class="timeline-comment">
								<template v-if="item.comment">
									审批意见：{{ item.comment }}
								</template>
							</div>
						</div>
					</el-timeline-item>
				</el-timeline>
			</div>
			<Drawer
				v-model:drawer="bpmnDrawerState"
				size="930"
				destroy-on-close
				@close="() => (bpmnDrawerState = false)"
				show-close
			>
				<div>
					<Title
						:title="{ name: ['流程图'], icon: ['fas', 'square-share-nodes'] }"
					/>
					<el-scrollbar height="calc(100vh - 100px)" style="padding-top: 10px">
						<BPMNPreview
							:processInstanceId="props.processInstanceId"
							:processDefinitionId="props.processDefinitionId"
						/>
						<!-- <div class="process-histable-table">
							<Title :title="tableTitle" />
							<PitayaTable2
								ref="refInitiateTable"
								:table-data="processData.history"
								:columns="tableColumn"
								:need-index="true"
							/>
						</div> -->
					</el-scrollbar>
				</div>
			</Drawer>
			<NewDrawer
				v-model:drawer="userDrawerState"
				size="620"
				destroy-on-close
				:title="userDrawerTitle"
			>
				<template #content>
					<PitayaTable
						:columns="baseUserColumns"
						:table-data="roleUsers"
						:max-height="userTableHeight"
						:table-loading="baseUserColumnsLoading"
					/>
				</template>
				<template #footer>
					<ButtonList
						:button="userDrawerBtnList"
						@onBtnClick="userDrawerBtnClick"
					/>
				</template>
			</NewDrawer>
		</div>
	</div>
</template>

<style scoped lang="scss">
.timeline-content {
	position: relative;
	font-size: 12px;

	.timeline-foot {
		display: flex;
		justify-content: space-between;
		align-items: baseline;
		color: var(--el-text-color-secondary);
	}

	.timeline-comment {
		margin-top: 2px;
	}

	.depart_content {
		position: absolute;
		top: 0;
		left: 130px;
		width: 120px;
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
		text-align: left;
		cursor: pointer;
	}

	.user_content {
		width: 70px;
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
		text-align: left;
		cursor: pointer;
	}
}

.approved-extend-root {
	display: flex;
	flex-direction: column;

	.approved-tabs-wrapper {
		:deep(.el-tabs__item) {
			font-size: 14px;
			color: #666;

			&.is-active {
				color: var(--pitaya-sidebar-menu-active-text-color);
			}
		}

		:deep(.el-tabs__active-bar) {
			background-color: var(--pitaya-header-bg-color);
		}

		:deep(.el-tabs__header) {
			margin-bottom: 0;
		}

		:deep(.el-tabs__nav-wrap) {
			&::after {
				width: 0;
				height: 0;
			}
		}
	}

	.approved-extend-content {
		height: 94vh;
		overflow-y: scroll;
		.opinion {
			padding: 20px 10px 0 10px;

			.opinion-select {
				margin-top: 10px;
				display: flex;
				font-size: 12px;
				align-items: baseline;
				color: var(--el-input-text-color, var(--el-text-color-regular));
				border: 1px 1px 0 1px #ccc solid;
				border-top-left-radius: var(
					--el-input-border-radius,
					var(--el-border-radius-base)
				);
				border-top-right-radius: var(
					--el-input-border-radius,
					var(--el-border-radius-base)
				);
				background-color: #f2f3f5;

				.opinion-select-title {
					padding: 0 10px;
					line-height: 32px;
					width: 80px;
				}

				.opinion-select-select {
					width: calc(100% - 80px);
				}
			}

			:deep(.el-textarea__inner) {
				box-shadow: 0 0 0 1px
					var(--el-input-border-color, var(--el-border-color)) inset !important;
				border-radius: unset !important;
				border-bottom-left-radius: var(
					--el-input-border-radius,
					var(--el-border-radius-base)
				) !important;
				border-bottom-right-radius: var(
					--el-input-border-radius,
					var(--el-border-radius-base)
				) !important;
			}
		}

		.process {
			margin-top: 10px;
		}
	}
}
.approved-extend-content::-webkit-scrollbar {
	display: none;
}
.el-timeline-button{
	display: flex;
	align-items:flex-start;
	justify-content: flex-start;
	margin-left: 0px !important;
	span{
		max-width: 300px;
		text-align: left;
		overflow: hidden;
		text-overflow: ellipsis;
	}
}
</style>
