<template>
	<div class="app-container-row">
		<div class="app-left-container">
			<div class="app-content-wrapper">
				<div class="app-content-group">
          <!-- 树状结构 -->
					<ModelFrame>
						<Title :title="treeTitle" />
						<div class="app-el-scrollbar-wrapper">
							<PitayaTree
								ref="majorTreeRef"
								:treeData="treeData"
								:treeProps="treeProps"
								:needCheckBox="false"
								node-key="id"
								@onTreeClick="clickTreeNode"
								:tree-loading="treeLoading"
								:currentNodeKey="defaultCheckedKeys"
								:default-expanded-keys="defaultExpandedKeys"
								:expand-on-click-node="true"
							/>
						</div>
					</ModelFrame>
				</div>
			</div>
		</div>
		<div class="right-model-frame">
      <ModelFrame>
				<Query
					class="ml10"
					:queryArrList="searchArrList"
					@getQueryData="getSearchData"
				/>
			</ModelFrame>
      <ModelFrame class="content">
			<Title
				:title="expertManagementTitle"
				:button="managementContainerBtn"
				@onBtnClick="showExpertManagementDrawer"
			/>
			<el-scrollbar>
				<PitayaTable
					ref="expertManagementTableRef"
					:customizeHeightNumber="0"
					:columns="tableColumns"
					:tableData="tableData"
					:total="total"
					:single-select="false"
					:need-selection="true"
					@on-current-page-change="onChangeCurrentPageChange"
					:table-loading="tableLoading"
				>
					<template #empName="{ rowData }">
						<div>{{ rowData.vsystemBaseUser.empName }}</div>
					</template>
					<template #postName="{ rowData }">
						<div>{{ rowData.vsystemBaseUser.postName || '-' }}</div>
					</template>
					<template #orgName="{ rowData }">
						<div>{{ rowData.vsystemOrganization?.orgName || '-' }}</div>
					</template>
					<template #fcell="{ rowData }">
						<div>{{ rowData.vsystemBaseUser.fcell }}</div>
					</template>

					<template #expertise="{ rowData }">
						<div
							class="border-bottom-text"
							@click="getRelatedDeviceData(rowData)"
						>
							{{ rowData.vequipmentClasses ? rowData.vequipmentClasses.length : '0' }}
						</div>
					</template>

					<template #lines="{ rowData }">
						<div
							class="border-bottom-text"
							@click="getRelatedLineData(rowData)"
						>
							{{ rowData.lines ? rowData.lines.length || '' : '0' }}
						</div>
					</template>

					<template #expertStatus="{ rowData }">
						<el-tag
							:class="[
								'state-txt',
								rowData.expertStatus === 'open'
									? 'status-item-open'
									: 'status-item-off'
							]"
							>
								{{ rowData.expertStatus === 'open' ? "启用" : "停用" }}
							</el-tag>
					</template>
					<template #operations="{ rowData }">
						<el-button v-btn link @click="editLine(rowData)">
							<font-awesome-icon :icon="['fas', 'pen-to-square']" style="color: var(--pitaya-btn-background)" />
							<span class="table-inner-btn">编辑</span>
						</el-button>
						<!-- <el-button v-btn link @click="viewLine(rowData)">
							<font-awesome-icon :icon="['fas', 'eye']" style="color: var(--pitaya-btn-background)" />
							<span class="table-inner-btn">查看</span>
						</el-button> -->
						<el-button v-btn link @click="deleteLine(rowData)">
							<font-awesome-icon :icon="['fas', 'trash-can']" style="color: var(--pitaya-btn-background)" />
							<span class="table-inner-btn">移除</span>
						</el-button>
					</template>
					<template #footerOperateLeft>
						<ButtonList
							class="btn-list"
							:is-not-radius="true"
							:button="bottomBtnArray"
							:loading="bottomBtnLoading"
							@on-btn-click="clickBottomBtn"
						/>
					</template>
				</PitayaTable>
			</el-scrollbar>
		</ModelFrame>
    <!-- 选取专家弹窗 -->
		<Drawer
			size="1030"
			v-model:drawer="showChooseExpertManagementDrawer"
			:destroyOnClose="true"
		>
			<ChooseExpertManagement :majorCode="majorCode" @selectSuccess="hideChooseExpertManagementDrawer"/>
		</Drawer>

		<!-- 编辑/查看专家详细信息 -->
		<Drawer
			size="1230"
			v-model:drawer="showEditExpertManagementDrawer"
			:destroyOnClose="true"
		>
			<editExpertManagementInfo :majorCode="majorCode" :editId="editId" :empNo="empNo" :isView="isView" @editSuccess="hideEditExpertManagementDrawer" />
		</Drawer>

		<!-- 关联设备分级 -->
		<Drawer
			size="400"
			v-model:drawer="showRelatedDeviceDrawer"
		>
			<Title :title="relevanceDeviceTitle" />
			<PitayaTable
				:columns="relatedDeviceTableColumn"
				:customizeHeightNumber="0"
				:table-data="relatedDeviceDrawerTableData"
				:need-pagination="false"
				:need-index="true"
			/>
		</Drawer>

		<!-- 关联线路 -->
		<Drawer
			size="320"
			v-model:drawer="showRelatedLineDrawer"
		>
			<Title :title="relevanceLineTitle" />
			<PitayaTable
				:columns="relatedLineTableColumn"
				:table-data="relatedLineDrawerTableData"
				:need-pagination="false"
				:need-index="true"
			/>
		</Drawer>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { ElMessage } from "element-plus"
import { expertManagementApi } from "@/app/platform/api/configuration/expertManagement"
import ChooseExpertManagement from "./components/ChooseExpertManagement.vue"
import editExpertManagementInfo from "./components/editExpertManagementInfo.vue"
import { usePagination } from "@/app/platform/hooks/usePagination"
import { _matchFormProp } from "@/app/platform/utils/permission"
import { CustomMessageBox } from "@/app/platform/utils/message"
import { useLine } from "@/app/platform/hooks/useLine"
import { useDevice } from "@/app/platform/hooks/useDevice"

onMounted(() => {
	getTree() //获取树
})

const treeTitle = {
	name: ["专业类别"],
	icon: ["fas", "square-share-nodes"]
} // 树状图标题
const treeData = ref<any[]>([]) // 树状图数据
const treeProps = {
	children: "children",
	label: "name"
} // 树状图参数配置
const treeLoading = ref(false) // 控制树的loading状态
const defaultExpandedKeys = ref<any>([]) //默认展开的key
const defaultCheckedKeys = ref<any>()
const majorTreeRef = ref<any>(null)
const useLineStore = useLine()
const useDeviceStore = useDevice()

// 检索项列表
const queryLineArr = ref<any[]>([])
const searchArrList = ref<any[]>([
	{
		name: "所属线路",
		key: "lineCode",
		placeholder: "请选择所属线路",
		enableFuzzy: false,
		type: "select",
		children: []
	},
	{
		name: "设备分级",
		key: "equipmentLevelCode",
		placeholder: "请选择设备分级",
		type: "elTreeSelect",
		children: []
	}
])
provide('receiveQueryLineArr', (value: any[]) => {
	queryLineArr.value = value
})
const searchData = ref<any>({}) // 检索项数据
const expertManagementTitle = reactive({
	name: ['请选择专业'],
	icon: ["fas", "square-share-nodes"]
}) // 表格title 
const managementContainerBtn = [
	{
		name: "选取专家",
		roles: "configuration:expertManagement:btn:add",
		icon: ["fas", "square-plus"]
	}
] // 表格交互按钮
const tableColumns: TableColumnType[] = [
	{ label: "专家姓名", prop: "empName", needSlot: true, width: 85 },
	{ label: "专家职位", prop: "postName", needSlot: true, width: 75 },
	{ label: "所属部门", prop: "orgName",needSlot: true, width: 95 },
	{ label: "联系电话", prop: "fcell", needSlot: true, width: 120 },
	{ label: "关联专业", prop: "majorName", minWidth: 120 },
	{ label: "关联线路", prop: "lines", needSlot: true, width: 85 },
	{ label: "关联设备分级", prop: "expertise", needSlot: true, width: 100 },
	{ label: "专家状态", prop: "expertStatus", needSlot: true, width: 85 },
	{ label: "入库时间", prop: "createdDate", width: 160 },
  { label: "更新时间", prop: "lastModifiedDate", width: 160 },
	{
		label: "操作",
		prop: "operations",
		fixed: "right",
		needSlot: true,
		width: 160
	}
] //表格列
const tableData = ref<any[]>([]) //表格数据
const total = ref<number>(0)
// 表格配置项
const tableLoading = ref<boolean>(false)
const usePaginationStore = usePagination()
const usePaginationStoreForData = usePaginationStore.paginationData
const pageSize = ref<number>(usePaginationStoreForData.pageSize)
const currentPage = ref<number>(usePaginationStoreForData.currentPage)
const expertManagementTableRef = ref<any>()

// 关联设备分级
const showRelatedDeviceDrawer = ref<boolean>(false)
const relevanceDeviceTitle = {
	name: ["关联设备分级"],
	icon: ["fas", "square-share-nodes"]
}
const relatedDeviceTableColumn: TableColumnType[] = [
	{
		label: "设备名称",
		prop: "composeName"
	}
]
const relatedDeviceDrawerTableData = ref<any[]>([])

// 关联线路
const showRelatedLineDrawer = ref<boolean>(false)
const relevanceLineTitle = {
	name: ["关联线路"],
	icon: ["fas", "square-share-nodes"]
}
const relatedLineTableColumn: TableColumnType[] = [
	{
		label: "线路名称",
		prop: "name"
	}
]
const relatedLineDrawerTableData = ref<any[]>([])

// 启用停用按钮
const bottomBtnArray = ref([
	{
		name: "启用",
		icon: ["fas", "circle-check"]
	},
	{
		name: "停用",
		icon: ["fas", "power-off"]
	}
])
const bottomBtnLoading = ref<boolean>(false) //启用停用按钮状态
const showChooseExpertManagementDrawer = ref<boolean>(false) //控制选取专家弹窗状态
const majorCode = ref<number>(0) //选中专业的id
const editId = ref<number>(0) //要编辑信息的id
const empNo = ref<string>('') //要编辑信息的empNo
const isView = ref<boolean>(false) //是否为查看
const showEditExpertManagementDrawer = ref<boolean>(false) //控制编辑专家弹窗状态

/**
 * @description 获取最底层数据
 */
const getDeepData = (data: any[], list: any[]) => {
	data.forEach((item) => {
    if (item.children && item.children.length > 0) {
      getDeepData(item.children, list);
    } else {
      list.push(item);
    }
  });
  return list;
}

/**
 * @description 获取树的数据
 */
function getTree() {
	treeLoading.value = true
	expertManagementApi
		.getProfessionTreeApi({ searchValue: undefined })
		.then((res: any) => {
			const gdTree = res[0].children.find((item: any) => item.name == '供电')
			gdTree.pid = null
			treeData.value = [gdTree]
			defaultExpandedKeys.value = [gdTree.id]
			defaultCheckedKeys.value = gdTree.children[0].id
			majorCode.value = gdTree.children[0].id
			expertManagementTitle.name = [`${gdTree.children[0].name}专家库`]
			getExpertList()
			// 反显检索项数据源及侧边树数据
			searchArrList.value[0].children = useLineStore.linesArr
			searchArrList.value[1].children = useDeviceStore.deviceArr
		})
		.catch((err) => {
			throw new Error("getProfessionTreeApi():::" + err)
		})
		.finally(() => {
			treeLoading.value = false
		})
}

/**
 * @description 点击树状节点
 * @param selectData 选中的节点
 * @param nodeData 节点信息
 */
const clickTreeNode = (selectData: any, nodeData: any) => {
  if(selectData.children == null) {
		majorCode.value = selectData.id
		expertManagementTitle.name = [`${selectData.name}专家库`]
		getExpertList()
	}
}

/**
 * @description 获取专家列表
 */
const getExpertList = () => {
	tableLoading.value = true
	expertManagementApi
		.getExpertInfoListApi({ majorCode: majorCode.value, currentPage: currentPage.value - 1, pageSize: pageSize.value, ...searchData.value })
		.then((res: any) => {
			tableData.value = res.rows
			total.value = res.total
			expertManagementTableRef.value.clearSelectedTableData()
		})
		.catch((err) => {
			throw new Error("getExpertInfoListApi():::" + err)
		})
		.finally(() => {
			tableLoading.value = false
		})
}

/**
 * @description 选择搜索参数回显
 * @param queryParams 检索参数
 */
const getSearchData = (queryParams?: any) => {
	searchData.value.lineCode = queryParams.lineCode
		? queryParams.lineCode
		: ""
	searchData.value.equipmentLevelCode = queryParams.equipmentLevelCode
			? queryParams.equipmentLevelCode
			: ""
	getExpertList()
}

/**
 * @description 显示选取专家弹窗
 */
const showExpertManagementDrawer = () => {
	if(majorCode.value === 0){
		ElMessage.warning("请先选择专业")
		return false
	}
  showChooseExpertManagementDrawer.value = true
}

/**
 * @description 隐藏选取专家弹窗
 */
const hideChooseExpertManagementDrawer = () => {
	showChooseExpertManagementDrawer.value = false
	getExpertList()
}

/**
 * @description 隐藏编辑专家弹窗
 */
const hideEditExpertManagementDrawer = () => {
	showEditExpertManagementDrawer.value = false
	getExpertList()
}

/**
 * @description 切换分页
 * @param pageData 
 */
const onChangeCurrentPageChange = (pageData: any) => {
	pageSize.value = pageData.pageSize
	currentPage.value = pageData.currentPage
	getExpertList()
}

/**
 * @description 获取关联线路
 * @param data 点击的行数据
 */
const getRelatedLineData = (data: any) => {
	relatedLineDrawerTableData.value = data.lines
	showRelatedLineDrawer.value = true
}

/**
 * @description 获取关联设备分级
 * @param data 点击的行数据
 */
const getRelatedDeviceData = (data: any) => {
	relatedDeviceDrawerTableData.value = data.vequipmentClasses
	showRelatedDeviceDrawer.value = true
}

/**
 * @description 编辑数据
 * @param row 选择的行
 */
const editLine = (row: any) => {
	editId.value = row.id
	empNo.value = row.empNo
	isView.value = false
	showEditExpertManagementDrawer.value = true
}

/**
 * @description 查看数据
 * @param row 选择的行
 */
const viewLine = (row: any) => {
	editId.value = row.id
	empNo.value = row.empNo
	isView.value = true
	showEditExpertManagementDrawer.value = true
}


/**
 * @description 删除数据
 * @param row 选择的行
 */
const deleteLine = (row: any) => {
	CustomMessageBox({ message: "确定要移除这行数据吗？" }, (res: boolean) => {
		if (res) {
			expertManagementApi
			.deleteExpertInfoApi({ empNo: row.empNo })
			.then((res: any) => {
				ElMessage.success('删除成功')
			})
			.catch((err) => {
				throw new Error("deleteExpertInfoApi():::" + err)
			})
			.finally(() => {
				getExpertList()
			})
		}
	})
}

/**
 * @description 启用停用
 * @param btnName 
 */
const clickBottomBtn = (btnName: any) => {
	if (bottomBtnLoading.value) return
	const selectNodes = expertManagementTableRef.value.pitayaTableRef.getSelectionRows()
	const selectEmpNos = selectNodes.map((item: { empNo: any }) => item.empNo)
	const status = btnName === "启用" ? 'open' : 'off'
	if(selectEmpNos && selectEmpNos.length === 0) {
		ElMessage.warning('请选择您要更改状态的信息')
		return false
	}
	bottomBtnLoading.value = true
	expertManagementApi.updateExpertStatusApi({empCodes: selectEmpNos, status: status })
		.then(() => {
			ElMessage.success(`${btnName}成功`)
			getExpertList()
		})
		.finally(() => {
			bottomBtnLoading.value = false
		})
}
</script>

<style lang="scss" scoped>
.app-container-row {
	flex-direction: row;
	.app-left-container {
		height: 100%;
		width: 20%;
		min-width: 300px;
		flex: 0 0 20%;
		display: flex;
		flex-direction: column;
		margin-right: 10px;
	}
}
.app-content-wrapper {
	height: 0;
	flex: 1;
	.app-content-group {
		height: 100%;
		:deep(.model-frame-wrapper) {
			height: 100%;
			padding-bottom: 10px;
			overflow: hidden;
			display: flex;
			flex-direction: column;
		}
		.app-el-scrollbar-wrapper {
			height: 0;
			flex: 1;
			.table-inner-btn {
				margin-left: 5px;
				color: #204a9c;
			}
		}
	}
}

.border-bottom-text {
	position: relative;
	color: #204a9c;
	text-decoration: underline;
	cursor: pointer;
}

.status-item-open{
	background: rgb(75, 174, 137);
	color: #fff;
	border: 0;
}

.status-item-off{
	background: rgb(245, 155, 34);
	color: #fff;
	border: 0;
}
</style>
