<!--
 * @Author: liulianming
 * @Date: 2025-07-11 16:03:24
 * @LastEditors: liulianming
 * @LastEditTime: 2025-07-30 21:43:20
 * @Description:
-->

<template>
	<div class="app-container">
		<!-- 左侧 tree 筛选 -->
		<model-frame class="left-frame">
			<div class="drawer-container">
				<div class="drawer-column" style="width: 100%">
					<div class="rows" style="padding-bottom: 20px">
						<Title :title="containerLeftTitle" />

						<pitaya-tree
							ref="treeRef"
							v-model:treeBizId="treeBizId"
							v-loading="treeLoading"
							:tree-data="treeData"
							:tree-props="{
								label: 'subitemName',
								children: 'children'
							}"
							:needCheckBox="false"
							check-strictly
							:expand-on-click-node="false"
							:need-single-select="false"
							:defaultExpandedKeys="defaultExpandedKeys"
							node-key="id"
							@onTreeClick="costCategoryTreeClick"
						/>
					</div>
				</div>
			</div>
		</model-frame>

		<div class="right-frame">
			<model-frame class="top-frame">
				<Query
					:query-arr-list="queryConf"
					class="ml10"
					@get-query-data="handleQuery"
				/>
			</model-frame>
			<model-frame class="bottom-frame">
				<PitayaTable
					ref="tableRef"
					:columns="tableProp"
					:table-data="tableData"
					:customizeHeightNumber="0"
					:needSelection="false"
					:single-select="false"
					:need-index="true"
					:need-pagination="true"
					:total="pageTotal"
					@onSelectionChange="selectedTableList = $event"
					@on-current-page-change="onCurrentPageChange"
					:table-loading="tableLoading"
					@on-table-sort-change="handleSortChange"
				>
					<template #status="{ rowData }">
						<dict-tag
							:options="getCostCategoryStatus()"
							:value="rowData.status"
						/>
					</template>

					<template #operations="{ rowData }">
						<slot
							v-if="
								(rowData.status == ICostCategoryStatus.Drafted &&
									isCheckPermission(powerList.systemCostCategoryBtnEdit) &&
									hasPermi(rowData.createdBy)) ||
								isCheckPermission(powerList.systemCostCategoryBtnPreview) ||
								(rowData.status == ICostCategoryStatus.Drafted &&
									isCheckPermission(powerList.systemCostCategoryBtnDrop) &&
									hasPermi(rowData.createdBy))
							"
						>
							<el-button
								v-btn
								link
								@click.stop="handleRowEdit(rowData)"
								:disabled="checkPermission(powerList.systemCostCategoryBtnEdit)"
								v-if="
									rowData.status == ICostCategoryStatus.Drafted &&
									isCheckPermission(powerList.systemCostCategoryBtnEdit) &&
									hasPermi(rowData.createdBy)
								"
							>
								<font-awesome-icon :icon="['fas', 'pen-to-square']" />
								<span class="table-inner-btn">编辑</span>
							</el-button>

							<el-button
								v-btn
								link
								@click.stop="handleRowView(rowData)"
								:disabled="
									checkPermission(powerList.systemCostCategoryBtnPreview)
								"
								v-if="isCheckPermission(powerList.systemCostCategoryBtnPreview)"
							>
								<font-awesome-icon :icon="['fas', 'eye']" />
								<span class="table-inner-btn">查看</span>
							</el-button>

							<el-button
								v-btn
								link
								@click.stop="handleRowDel(rowData)"
								:disabled="checkPermission(powerList.systemCostCategoryBtnDrop)"
								v-if="
									rowData.status == ICostCategoryStatus.Drafted &&
									isCheckPermission(powerList.systemCostCategoryBtnDrop) &&
									hasPermi(rowData.createdBy)
								"
							>
								<font-awesome-icon :icon="['fas', 'trash-can']" />
								<span class="table-inner-btn">移除</span>
							</el-button>
						</slot>
						<slot v-else>---</slot>
					</template>
					<template #footerOperateLeft>
						<ButtonList
							class="btn-list"
							:is-not-radius="true"
							:button="tableFooterBtns"
							:loading="tbBtnLoading"
							@on-btn-click="handleChangeStatus"
						/>
					</template>
				</PitayaTable>
			</model-frame>
		</div>

		<!-- 新建/编辑 -->
		<Drawer
			:size="modalSize.sm"
			v-model:drawer="editorVisible"
			:destroyOnClose="true"
		>
			<cost-category-editor
				:id="editorRow.id"
				:mode="editorMode"
				:current-node="currentTreeNode"
				@update="
					() => {
						getTreeData()
						fetchTableData()
					}
				"
				@close="editorVisible = false"
			/>
		</Drawer>

		<!-- 查看 -->
		<Drawer
			:size="modalSize.sm"
			v-model:drawer="detailVisible"
			:destroyOnClose="true"
		>
			<cost-category-detail
				:id="editorRow.id"
				:mode="editorMode"
				:current-node="currentTreeNode"
				@close="detailVisible = false"
			/>
		</Drawer>

		<!-- 添加上传对话框 -->
		<el-dialog
			v-model="uploadVisible"
			title="导入GIS信息"
			width="500px"
			:destroy-on-close="true"
		>
			<UploadFile
				upload-url="/baseline/system/surveyingMappingObject/batchImportFile"
				@success="handleUploadSuccess"
				@close="uploadVisible = false"
			/>
		</el-dialog>

		<!-- 预览弹窗 -->
		<Drawer
			:size="modalSize.lg"
			v-model:drawer="previewVisible"
			:destroyOnClose="true"
		>
			<gis-preview :preview-data="tableData" :treeData="treeData" />
		</Drawer>
	</div>
</template>
<script setup lang="ts">
import PitayaTree from "@/compontents/PitayaTree.vue"
import { map } from "lodash-es"
import {
	checkPermission,
	isCheckPermission
} from "@/app/platform/utils/permission"
const { getDictByCodeList, dictOptions } = useDictInit()
import {
	batchEnableCostCategory,
	batchFreezeCostCategory,
	batchThawCostCategory,
	delSystemCostCategory,
	getSystemCostCategoryPaged,
	getSystemCostCategoryTree
} from "@/app/baseline/api/system-cost-category"
import { useTbInit } from "@/app/baseline/views/components/tableBase"
import { ICostCategoryStatus } from "@/app/baseline/utils/types/system-cost-category"
import { IModalType } from "@/app/baseline/utils/types/common"
import { useMessageBoxInit } from "@/app/baseline/views/components/messageBox"
import costCategoryEditor from "@/app/baseline/views/plan/costCategory/costCategoryEditor.vue"
import costCategoryDetail from "@/app/baseline/views/plan/costCategory/costCategoryDetail.vue"
import { modalSize } from "@/app/baseline/utils/layout-config"
import { getCostCategoryStatus } from "@/app/baseline/api/dict"
import DictTag from "@/app/baseline/views/components/dictTag.vue"
import XEUtils from "xe-utils"
import { hasPermi } from "@/app/baseline/utils"
import { powerList } from "@/app/baseline/views/components/define.d"
import { querySetting } from "@/compontents/Query.vue"
import {
	getConfigurationGisInformationPaged,
	getConfigurationGisInformationCate
} from "@/app/baseline/api/configuration"
import UploadFile from "./components/UploadFile.vue"
import GisPreview from "./components/GisPreview.vue"
import { useDictInit } from "@/app/baseline/views/components/dictBase"

const { showDelConfirm, showWarnConfirm } = useMessageBoxInit()

// 添加上传对话框控制变量
const uploadVisible = ref(false)

// 预览弹窗控制变量
const previewVisible = ref(false)

const treeRef = ref<InstanceType<typeof PitayaTree>>()

const treeLoading = ref(false)

const containerLeftTitle = {
	name: ["GIS信息分类"],
	icon: ["fas", "square-share-nodes"]
}

const containerRightTitle = ref<any>({
	name: ["GIS信息分类"],
	icon: ["fas", "square-share-nodes"]
})

const treeData = ref<Record<string, any>[]>([])

const queryConf = computed<querySetting[]>(() => [
	{
		name: "关键字",
		key: "keyword",
		type: "input",
		placeholder: "请输入查询关键字"
	}
])

// tree当前选中的节点
const currentTreeNode = ref<any>()

const checkedId = ref<any[]>([])

/**
 * 树形筛选 check handler
 */
function costCategoryTreeClick(data: any, treeData: any) {
	nextTick(() => {
		// checkedId.value = treeRef.value?.getCheckedKeys() || []
		currentPage.value = 1
		tableRef.value.resetCurrentPage()
		// 更新面包屑
		containerRightTitle.value.name = JSON.parse(
			JSON.stringify(containerLeftTitle)
		).name.concat(getTreeTitle(treeData, "subitemName"))
		currentTreeNode.value = data

		// 如果点击的是根节点"GIS信息分类"，则传递空值
		if (data.id === "gis-root") {
			fetchParam.value.objectTypeCode = ""
		} else {
			fetchParam.value.objectTypeCode = data.subitemValue
		}
		fetchTableData()
	})
}

function handleQuery(data?: any) {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		...data
	}

	fetchTableData()
}

const treeBizId = ref()
const defaultExpandedKeys = ref<any[]>([])

async function getTreeData() {
	treeLoading.value = true

	try {
		const res = await getDictByCodeList(["GIS_CLASSIFICATION"])
		// 创建根节点"GIS信息分类"，将原始数据作为子节点
		const rootNode = {
			id: "gis-root",
			subitemName: "GIS信息分类",
			dataDictionaryCode: "GIS_ROOT",
			children: dictOptions.value.GIS_CLASSIFICATION || []
		}

		treeData.value = [rootNode]

		// 设置默认展开根节点
		defaultExpandedKeys.value = ["gis-root"]

		// 如果没有当前选中节点，默认选中根节点
		if (!currentTreeNode.value?.id) {
			currentTreeNode.value = rootNode
		}
	} finally {
		treeLoading.value = false
	}
}

onMounted(async () => {
	// fetchParam.value.sord = "desc"
	// fetchParam.value.sidx = "createdDate"
	fetchParam.value.objectTypeCode = ""
	treeRef.value?.setCheckedKeys([])
	await getTreeData()

	// 默认选中根节点
	if (treeData.value.length > 0) {
		treeBizId.value = "gis-root"
		currentTreeNode.value = treeData.value[0]
	}

	handleQuery()
})

const tbBtnLoading = ref(false)
const {
	tableProp,
	tableData,
	tableRef,
	tableLoading,
	currentPage,
	pageTotal,
	selectedTableList,
	fetchParam,
	fetchTableData,
	fetchFunc,
	onCurrentPageChange
} = useTbInit()

tableProp.value = [
	{ label: "绘制对象类型", prop: "objectType", width: 120 },
	{ label: "绘制对象类型编码", prop: "objectTypeCode", width: 140 },
	{ label: "绘制对象名称", prop: "objectLabel", minWidth: 120 },
	{ label: "坐标点数量", prop: "plotPointCount", minWidth: 120 },
	{ label: "高德绘制类型", prop: "amapObjectType", minWidth: 120 },
	{ label: "绘制坐标点", prop: "plotPoint", minWidth: 120 },
	{ label: "备注说明", prop: "remark" }
]

/**
 * table 底部 按钮 配置
 */
const tableFooterBtns = computed(() => {
	const CanImport = selectedTableList.value.every(
		(item: Record<string, any>) => item.status === ICostCategoryStatus.Started
	)

	const CanPreview = selectedTableList.value.every(
		(item: any) => item.status === ICostCategoryStatus.Drafted
	)

	return [
		{
			name: "导入",
			roles: powerList.gisInformationBtnImport,
			icon: ["fas", "file-import"]
		},
		{
			name: "预览",
			roles: powerList.gisInformationBtnPreview,
			icon: ["fas", "eye"]
		}
	]
})

/**
 * 导入/预览 操作
 * @param status
 */
async function handleChangeStatus(btnName?: string) {
	if (btnName === "导入") {
		uploadVisible.value = true
		return
	}

	if (btnName === "预览") {
		previewVisible.value = true
		return
	}

	const idList = map(selectedTableList.value, ({ id }) => id)
	tbBtnLoading.value = true

	try {
		if (btnName === "预览") {
			console.log("预览操作", idList)
		}
	} finally {
		tbBtnLoading.value = false
	}
}

// 添加上传成功处理函数
const handleUploadSuccess = () => {
	uploadVisible.value = false
	fetchTableData()
}

fetchFunc.value = getConfigurationGisInformationPaged

const editorVisible = ref(false)
const detailVisible = ref(false)
const editorMode = ref(IModalType.create)
const editorRow = ref()

/**
 * 添加
 */
function handleAdd() {
	if (!currentTreeNode.value) {
		return ElMessage.warning("请选择费用类别")
	}

	editorRow.value = {}
	editorVisible.value = true
	editorMode.value = IModalType.create
}

/**
 * 查看
 * @param rowData
 */
function handleRowView(rowData: any) {
	editorRow.value = { ...rowData }
	detailVisible.value = true
	editorMode.value = IModalType.view
}

/**
 * 编辑
 * @param rowData
 */
function handleRowEdit(rowData: any) {
	editorRow.value = { ...rowData }
	editorVisible.value = true
	editorMode.value = IModalType.edit
}

async function handleRowDel(rowData: any) {
	await showDelConfirm()
	await delSystemCostCategory(rowData.id)

	ElMessage.success("操作成功")
	await getTreeData()
	fetchTableData()
}

/**
 * 按时间排序
 * @param column
 * @param prop 字段名称
 * @param order ascending 升序 descending 降序
 */
function handleSortChange(column: any, prop: any, order: any) {
	currentPage.value = 1

	tableRef.value?.resetCurrentPage()

	fetchParam.value = {
		...fetchParam.value,
		sord:
			order === "descending" ? "desc" : order === "ascending" ? "asc" : "desc",
		sidx: order ? prop : "createdDate" // 排序字段
	}

	fetchTableData()
}
</script>
<style lang="scss" scoped>
@import "@/app/baseline/assets/css/index.scss";

:deep(.gis-preview-dialog) {
	.el-dialog__body {
		height: 600px;
		padding: 10px;
	}
}
</style>
