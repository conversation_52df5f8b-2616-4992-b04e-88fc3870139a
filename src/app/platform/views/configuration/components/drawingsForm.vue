<template>
  <!-- 新增/编辑图纸资料弹窗 -->
  <Drawer
		size="310"
		v-model:drawer="showAddDrawingsDrawer"
		:destroyOnClose="true"
	>
		<Title :title="drawingsDrawerTitle" />
		<el-form
			class="form-container"
			ref="drawingsFormRef"
			label-position="top"
			label-width="100px"
			:model="drawingsForm"
			:rules="rules"
		>
			<el-form-item label="资料分类" prop="drawType">
				<el-select
					style="width: 100%"
					v-model="drawingsForm.drawType"
					placeholder="请选择资料分类"
				>
					<el-option
						v-for="item in dataClassificationArr"
						:key="item.id"
						:label="item.subitemName"
						:value="item.subitemValue"
					/>
				</el-select>
			</el-form-item>
      <el-form-item label="关联线路" prop="lineCode">
				<el-select
					style="width: 100%"
					v-model="drawingsForm.lineCode"
					placeholder="请选择关联线路"
				>
					<el-option
						v-for="item in useLineStoreForData"
						:key="item.value"
						:label="item.label"
						:value="item.value"
					/>
				</el-select>
			</el-form-item>
      <el-form-item label="关联位置" prop="locComposeNoName">
				<el-input
					@click="showChooseLocation"
					v-model="drawingsForm.locComposeNoName"
					readonly
					placeholder="请选择关联位置"
				>
					<template #append>
						<font-awesome-icon
							:icon="['fas', 'location-dot']"
							style="color: #ccc"
							@click="showChooseLocation"
						/>
					</template>
				</el-input>
			</el-form-item>
      <el-form-item label="关联设备分级" prop="equipmentLevelCodeName">
				<el-input
					@click="showChooseDevice"
					v-model="drawingsForm.equipmentLevelCodeName"
					readonly
					placeholder="请选择关联设备分级"
				>
					<template #append>
						<font-awesome-icon
							:icon="['fas', 'location-dot']"
							style="color: #ccc"
							@click="showChooseDevice"
						/>
					</template>
				</el-input>
			</el-form-item>
      <el-form-item label="资料标题" prop="title">
				<el-input
		  		v-model.trim="drawingsForm.title"
		  		maxlength="50"
		  		:show-word-limit="true"
					:disabled="true"
		  	/>
			</el-form-item>
      <el-form-item label="上传资料" prop="drawUrl">
        <UploadFile
					:file-list="fileList"
					:action="`/pitaya/system/common/upload?businessType=0`"
					:viewDesc="true"
					:maxCount="1"
					:accept="'.pdf'"
					:allExtensions="['.pdf']"
					:allowsize="100"
					@onSuccess="handleSuccess"
					@handle-remove="handleRemove"
					listType="text"
				/>
			</el-form-item>
		</el-form>
		<div class="btn-list">
			<ButtonList :loading="btnLoading" :button="submitFormBtn" @onBtnClick="submitForm" />
		</div>
	</Drawer>

	<!-- 选择关联位置树 -->
	<Drawer
		size="330"
		v-model:drawer="showChooseLocationDrawer"
		:destroyOnClose="true"
	>
		<Title :title="chooseLocationTitle" />
		<section class="tree_container">
			<PitayaTree
				ref="treeRef"
				:need-check-box="true"
				:tree-data="locationTreeData"
				:tree-props="locationTreeProps"
				:tree-loading="locationTreeLoading"
				nodeKey="locComposeNo"
			/>
		</section>
		<div class="btn-list">
			<ButtonList :button="chooseLocationBtn" @onBtnClick="chooseLocation" />
		</div>
	</Drawer>

	<!-- 选择设备分级树 -->
	<Drawer
		v-show="showChooseDeviceDrawer"
		class="inner-drawer drawer-hidden-box"
		:size="300"
		v-model:drawer="showChooseDeviceDrawer"
	>
		<Title :title="choosedeviceTitle" />
		<PitayaTree
			ref="deviceTreeRef"
			:need-check-box="true"
			:tree-data="deviceTreeData"
			:tree-props="deviceTreeProps"
			:tree-loading="deviceTreeLoading"
			nodeKey="composeCode"
		/>
		<div class="btn-list">
			<ButtonList :button="chooseDeviceBtn" @onBtnClick="chooseDevice" />
		</div>
	</Drawer>
</template>

<script lang="ts" setup>
import { FormInstance, FormRules, ElMessage, UploadUserFile } from "element-plus"
import { useLine } from "@/app/platform/hooks/useLine"
import { DictApi } from "@/app/baseline/api/dict"
import { cloneDeep } from "lodash-es"
import { drawingsApi } from "@/app/platform/api/configuration/drawing"


const showAddDrawingsDrawer = ref<boolean>(false) //控制新增图纸资料弹窗
const drawingsDrawerTitle = ref<any>({
	name: [],
	icon: ["fas", "square-share-nodes"]
})
// 关联线路数据源
const useLineStore = useLine()
const useLineStoreForData = ref(useLineStore.linesArr)
// 表单相关
const drawingsFormRef = ref<FormInstance>()
const drawingsForm: anyKey = reactive({
	drawType: null,
  lineCode: null,
	locComposeNoName: null,
	locComposeNo: null,
	equipmentLevelCode: null,
	equipmentLevelCodeName: null,
	title: null,
	drawUrl: null
})
const rules = reactive<FormRules<typeof drawingsForm>>({
	drawType: [
	  { required: true, message: "请选择资料分类", trigger: "change" }
	],
  lineCode: [
	  { required: true, message: "请选择关联线路", trigger: "change" }
	],
	locComposeNoName: [
		{ required: true, message: "请选择关联位置", trigger: "change" }
	],
	equipmentLevelCodeName: [
		{ required: true, message: "请选择关联设备分级", trigger: "change" }
	],
	title: [
		{ required: true, message: "请输入资料标题", trigger: "change" }
	],
	drawUrl: [
		{ required: true, message: "请上传资料", trigger: "change" }
	]
})
const fileList = ref<UploadUserFile[]>([])
const dataClassificationArr = ref<any[]>([])
const submitFormBtn = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "提交",
		icon: ["fas", "floppy-disk"]
	}
]
const btnLoading = ref<boolean>(false)

const props = defineProps({
  editId: {
    type: Number,
    default: 0
  },
  majorCode: {
    type: Number,
    default: ''
  }
})

const emit = defineEmits([
	"submitSuccess"
])

// 选择关联位置弹窗
interface Tree {
	id: number | string
	label: string
	children?: Tree[]
}

// 关联位置相关
const treeRef = ref<any>()
const showChooseLocationDrawer = ref<boolean>(false)
const selectIds = ref<string[]>([]) //选中的关联位置
const selectCodes = ref<string[]>([]) //选中的设备分级
const chooseLocationTitle = {
	name: ["选择关联位置"],
	icon: ["fas", "square-share-nodes"]
}
const chooseLocationBtn = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "保存",
		icon: ["fas", "floppy-disk"]
	}
]
const locationTreeLoading = ref(false)
const locationTreeData = ref<Tree[]>([])
const locationTreeProps = {
	children: "children",
	label: "newLocComposeName"
}

// 设备分级弹窗相关
interface Tree {
	id: number | string
	label: string
	children?: Tree[]
}
const deviceTreeProps = {
	children: "children",
	label: "selfName"
}

const choosedeviceTitle = {
	name: ["选择设备分级"],
	icon: ["fas", "square-share-nodes"]
}
const chooseDeviceBtn = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "确定",
		icon: ["fas", "floppy-disk"]
	}
]
const deviceTreeRef = ref<any>(null)
const deviceTreeLoading = ref(false)
const showChooseDeviceDrawer = ref<boolean>(false)
const deviceTreeData = ref<Tree[]>([])

/**
 * @description 递归处理数据, 为 cascade 的每个 options 添加 label 和 value 属性
 */
const addValueAndLabelForTree = (arr: any[]): any => {
  arr = arr.map(node => {
    if (node.hasOwnProperty('children')) {
      node.children = addValueAndLabelForTree(node.children)
    }
    return {
      ...node,
      newLocComposeName: `${node.locComposeNameBelong}(${node.locComposeNoBeLong})`
    }
  })
  return arr
}

/**
 * @description 获取设备分级最底层数据
 */
const getDeepData = (data: any[]) => {
	let list: any = []
	data.forEach((item) => {
    if (item.children && item.children.length > 0) {
      getDeepData(item.children);
    } else {
      list.push({...item, newLocComposeName: `${item.locComposeName}(${item.locComposeNoBeLong})`});
    }
  });
  return list;
}

/**
 * @description 获取资料分类
 */
const getDatumType = () => {
	DictApi.getDictByCode("DATUM_TYPE")
	.then((res) => {
		dataClassificationArr.value = res as any
	})
}

/**
 * @description 编辑表单反显
 */
const showForm = (editId: number) => {
  // 新增弹窗展示
  if(!editId){
		getDatumType()
		drawingsFormRef.value?.resetFields()
		Object.assign(drawingsForm, {
			drawType: null,
		  lineCode: null,
			locComposeNoName: null,
			locComposeNo: null,
			equipmentLevelCode: null,
			equipmentLevelCodeName: null,
			title: null,
			drawUrl: null
		})
		fileList.value = []
		selectIds.value = []
		selectCodes.value = []
		showAddDrawingsDrawer.value = true
		drawingsDrawerTitle.value.name = ['新增图纸资料']
    return false
  }

  // 编辑弹窗反显数据展示
	drawingsDrawerTitle.value.name = ['编辑图纸资料']
  drawingsApi
	.getDrawByIdApi({ id: editId })
	.then((res: any) => {
		const editData = cloneDeep(res)
		editData.locComposeNoName = editData.vpostionList.map((item: { locComposeName: string }) => item.locComposeName)
		editData.locComposeNo = editData.vpostionList.map((item: { locComposeNo: string }) => item.locComposeNo)
		selectIds.value = editData.vpostionList.map((item: { locComposeNo: string }) => item.locComposeNo)
		editData.equipmentLevelCodeName = editData.vequipmentClasses.map((item: { composeName: string }) => item.composeName)
		editData.equipmentLevelCode = editData.equipmentLevelCode.split(',')
		selectCodes.value = editData.equipmentLevelCode
		delete editData.vpostionList
		delete editData.vequipmentClasses
		delete editData.line
		Object.assign(drawingsForm, editData)
		fileList.value = [{
  	  name: editData.title + '.pdf',
  	  url: editData.drawUrl
  	}]
		getLocationTreeData(res.lineCode)
		getDatumType()
		showAddDrawingsDrawer.value = true
  })
}

/**
 * @description 获取关联位置树的数据
 */
const getLocationTreeData = (lineId: any) => {
	locationTreeLoading.value = true
	drawingsApi.getPositionTreeApi({lineId: lineId})
		.then((res: any) => {
			locationTreeData.value = []
			const map = new Map(Object.entries(res))
			map.forEach((value: any, key) => {
				locationTreeData.value = locationTreeData.value.concat(addValueAndLabelForTree(value))
			})
		})
		.finally(() => {
			locationTreeLoading.value = false
		})
}

/**
 * @description 获取设备分级树的数据
 */
const getDeviceTreeData = () => {
	deviceTreeLoading.value = true
	drawingsApi.getVEquipmentClassApi(
		objectToFormData({
			currentPage: 1,
			pageSize: 500
		})
	)
		.then((res: any) => {
			deviceTreeData.value = res
		})
		.finally(() => {
			deviceTreeLoading.value = false
		})
}

/**
 * @description 关联位置弹窗展示
 */
const showChooseLocation = () => {
	if(drawingsForm.lineCode == null){
		ElMessage.warning('请先选择线路')
		return
	}else{
		getLocationTreeData(drawingsForm.lineCode)
	}
	showChooseLocationDrawer.value = true
	nextTick(() => {
		if (selectIds.value.length > 0) {
			treeRef.value.PitayaTreeRef.setCheckedKeys(selectIds.value, true)
		}
	})
}

/**
 * @description 关联设备分级弹窗
 */
const showChooseDevice = () => {
	getDeviceTreeData()
	showChooseDeviceDrawer.value = true
	nextTick(() => {
		if (selectCodes.value.length > 0) {
			deviceTreeRef.value.PitayaTreeRef.setCheckedKeys(selectCodes.value, true)
		}
	})
}

/**
 * @description 选择关联位置
 */
const chooseLocation = (btnName: string | undefined) => {
	if (btnName === "保存") {
		// 将勾选的key和节点存入
		const currentSelectKeys = treeRef.value.PitayaTreeRef.getCheckedKeys()
		const currentSelectNodes = treeRef.value.PitayaTreeRef.getCheckedNodes()
		selectIds.value = currentSelectKeys
		drawingsForm.locComposeNo = currentSelectKeys
		drawingsForm.locComposeNoName = currentSelectNodes.map((item: { locComposeName: string }) => item.locComposeName)
	}
	showChooseLocationDrawer.value = false
}

/**
 * @description 选择设备分级
 */
const chooseDevice = (btnName: string | undefined) => {
	if (btnName === "确定") {
		// 将勾选的key和节点存入
		const currentSelectKeys = deviceTreeRef.value.PitayaTreeRef.getCheckedKeys()
		const currentSelectNodes = deviceTreeRef.value.PitayaTreeRef.getCheckedNodes()
		selectCodes.value = getDeepData(currentSelectNodes).map((item: any) => item.composeCode)
		drawingsForm.equipmentLevelCode = getDeepData(currentSelectNodes).map((item: any) => item.composeCode)
		drawingsForm.equipmentLevelCodeName = getDeepData(currentSelectNodes).map((item: { composeName: string }) => item.composeName)
	}
	showChooseDeviceDrawer.value = false
}

/**
 * @description 附件上传成功 回调
 * @param response
 */
const handleSuccess = (response: Record<string, any>) => {
	drawingsForm.title = response.data.customFileName
	drawingsForm.drawUrl = response.data.filePath
}

/**
 * @description 手动删除附件 回调
 * @param uploadFile
 * @param fileList
 */
const handleRemove = (uploadFile: any, fileList: any) => {
	drawingsForm.drawUrl = ""
	drawingsForm.title = ""
	fileList.value = []
	setTimeout(() => {
		drawingsForm.value?.clearValidate()
	})
}

/**
 * @description 提交表单
 */
const submitForm = (btnName: string | undefined) => {
	if (btnName === "提交") {
		drawingsFormRef.value?.validate((valid) => {
      if(valid){
				drawingsForm.locComposeNo = drawingsForm.locComposeNo.join(',')
				drawingsForm.equipmentLevelCode = drawingsForm.equipmentLevelCode.join(',')
				if(drawingsForm.id){
					btnLoading.value = true
					drawingsApi
					.editDrawApi({ majorCode: props.majorCode, ...drawingsForm })
					.then((res: any) => {
						ElMessage.success("编辑成功")
					})
					.catch((err) => {
						throw new Error("editDrawApi():::" + err)
					})
					.finally(() => {
						emit('submitSuccess')
						drawingsFormRef.value?.resetFields()
						btnLoading.value = false
    				showAddDrawingsDrawer.value = false
					})
					return false
				}

				btnLoading.value = true
				drawingsApi
				.addDrawApi({ majorCode: props.majorCode, ...drawingsForm })
				.then((res: any) => {
					ElMessage.success("新增成功")
				})
				.catch((err) => {
					throw new Error("addDrawApi():::" + err)
				})
				.finally(() => {
					emit('submitSuccess')
					drawingsFormRef.value?.resetFields()
					btnLoading.value = false
    			showAddDrawingsDrawer.value = false
				})
      }
    })
	}else{
    drawingsFormRef.value?.resetFields()
    showAddDrawingsDrawer.value = false
  }
}

defineExpose({
	showForm: showForm
})
</script>

<style lang="scss" scoped>
.form-container {
  padding: 6px;
  box-sizing: border-box;
}
.btn-list {
	width: 290px;
	display: flex;
	justify-content: flex-end;
	position: fixed;
	right: 10px;
	bottom: 0px;
	z-index: 99;
	padding: 10px 10px 10px 0;
	box-sizing: border-box;
	border-top: 1px solid #ccc;
	background-color: #fff;
}
</style>