<template>
	<div class="ChooseExpertManagement_container">
		<div class="ChooseExpertManagement-left">
			<Title :title="treeTitle" />
			<div class="app-el-scrollbar-wrapper">
				<el-scrollbar>
					<PitayaTree
						:treeData="organizationTreeData"
						:treeProps="organizationTreeProps"
						:needCheckBox="false"
						@onTreeClick="clickTreeNode"
						:tree-loading="organizationTreeLoading"
					/>
				</el-scrollbar>
			</div>
		</div>
		<div class="ChooseExpertManagement-right">
			<section class="flex_child">
				<Title :title="expertManagementTitle" />
				<PitayaTable
					ref="expertManagementTableRef"
					:customizeHeightNumber="0"
					:columns="tableColumns"
					:table-data="tableData"
					select-key="empNo"
					:need-index="true"
					:single-select="false"
					:need-selection="true"
					:need-pagination="true"
					:total="total"
					:table-loading="tableLoading"
					:selectedTableData="selectedExpertManagement"
					@on-current-page-change="onChangeCurrentPageChange"
					@onSelectionChange="selectExpertManagement"
				>
					<template #postName="{ rowData }">
						<div>{{ rowData.postName || '-' }}</div>
					</template>
				</PitayaTable>
			</section>
			<section class="bottom_line">
				<p>当前共选中{{ selectExpertManagementNum }}位专家</p>
				<ButtonList
					class="btn-list"
					:button="rightBtns"
					:loading="btnLoading"
					@on-btn-click="rightBtnClick"
				/>
			</section>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { expertManagementApi } from "@/app/platform/api/configuration/expertManagement"
import { usePagination } from "@/app/platform/hooks/usePagination"
const treeTitle = {
	name: ["组织架构"],
	icon: ["fas", "square-share-nodes"]
} // 树状图标题
const organizationTreeData = ref<any[]>([]) // 树状图数据
const organizationTreeProps = {
	children: "children",
	label: "orgName"
} // 树状图参数配置
const organizationTreeLoading = ref(false) // 控制树的loading状态

const expertManagementTitle = {
	name: ['可选专家列表'],
	icon: ["fas", "square-share-nodes"]
} // 表格title 
const tableColumns: TableColumnType[] = [
	{ label: "姓名", prop: "empName" },
	{ label: "职位", prop: "postName", needSlot: true },
	{ label: "所属部门", prop: "orgName" },
	{ label: "联系电话", prop: "fcell" }
] //表格列
const orgCode = ref<any>('') //选择的部门
const tableData = ref<any[]>([]) //表格数据
const total = ref<number>(0)
// 表格配置项
const tableLoading = ref<boolean>(false)
const usePaginationStore = usePagination()
const usePaginationStoreForData = usePaginationStore.paginationData
const pageSize = ref<number>(usePaginationStoreForData.pageSize)
const currentPage = ref<number>(usePaginationStoreForData.currentPage)
const expertManagementTableRef = ref<any>()
const selectExpertManagementNum = ref<number>(0)
const selectedExpertManagement = ref<any[]>([]) //已选中表格数据

// 底部按钮配置项
const rightBtns = [
	{ name: "确认选中", icon: ["fas", "circle-check"] }
]
const btnLoading = ref(false)

const emit = defineEmits([
	"selectSuccess"
])

const props = defineProps({
	majorCode: {
		type: Number,
		default: null
	}
})

onMounted(() => {
	getTree()
})

/**
 * @description 获取树的数据
 */
const getTree = () => {
	organizationTreeLoading.value = true
	expertManagementApi
		.getOrganListApi({})
		.then((res: any) => {
			organizationTreeData.value = res
		})
		.catch((err) => {
			throw new Error("getOrganListApi():::" + err)
		})
		.finally(() => {
			organizationTreeLoading.value = false
		})
}

/**
 * @description 获取可选择的专家列表
 */
const getCanSelectExpertmentList = () => {
	tableLoading.value = true
	expertManagementApi
		.getExpertListApi({ orgCode: orgCode.value, currentPage: currentPage.value, pageSize: pageSize.value })
		.then((res: any) => {
			tableData.value = res.rows
			total.value = res.total
		})
		.catch((err) => {
			throw new Error("getExpertListApi():::" + err)
		})
		.finally(() => {
			tableLoading.value = false
		})
}

/**
 * @description 点击树状节点
 * @param selectData 选中的节点
 * @param nodeData 节点信息
 */
const clickTreeNode = (selectData: any, nodeData: any) => {
  orgCode.value = selectData.orgCode
	getCanSelectExpertmentList()
}

/**
 * @description 切换分页
 * @param pageData 
 */
const onChangeCurrentPageChange = (pageData: any) => {
	pageSize.value = pageData.pageSize
	currentPage.value = pageData.currentPage
	getCanSelectExpertmentList()
}

/**
 * @description 选择专家
 */
const selectExpertManagement = (rowList: any[]) => {
	selectedExpertManagement.value = rowList
	selectExpertManagementNum.value = rowList.length
}

/**
 * @description 点击右下角按钮
 */
const rightBtnClick = () => {
	if(selectedExpertManagement.value.length == 0){
		ElMessage.warning("请选取专家")
		return false;
	}
	
	const arr = selectedExpertManagement.value.map(item => ({ empNo: item.empNo, majorCode: props.majorCode }))
	btnLoading.value = true
	expertManagementApi
		.confirmSelectExpertManagementApi(arr)
		.then(() => {
			ElMessage.success("选取成功")
		})
		.catch((err) => {
			throw new Error("confirmSelectExpertManagementApi():::" + err)
		})
		.finally(() => {
			emit('selectSuccess')
			btnLoading.value = false
		})
}

</script>

<style lang="scss" scoped>
.ChooseExpertManagement_container {
	display: flex;
	align-items: center;
	height: 100%;
	.ChooseExpertManagement-left {
		width: 310px;
		height: 100%;
		position: relative;
		padding-right: 10px;
		box-sizing: border-box;

		.app-el-scrollbar-wrapper{
			height: 98%;
		}
	}
	.ChooseExpertManagement-left::after {
		content: "";
		position: absolute;
		right: 0;
		top: -10px;
		width: 1px;
		height: calc(100% + 20px);
		background-color: #ccc;
	}
	.ChooseExpertManagement-right {
		width: calc(100% - 320px);
		height: 100%;
		position: relative;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		// margin-top: 15px;
		margin-left: 10px;
		.bottom_line{
			width: 100%;
			display: flex;
			justify-content: space-between;
			align-items: center;
			border-top: 1px solid #ccc;
			letter-spacing: 1px;

			p{
				font-size: 12px;
			}
		}
		.btn-list {
			display: flex;
			justify-content: flex-end;
			padding: 10px 10px 0 0;
			box-sizing: border-box;
		}
	}
}
</style>