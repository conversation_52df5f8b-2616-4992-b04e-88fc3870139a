<!--
 * @Author: liulianming
 * @Date: 2025-07-14 14:55:11
 * @Description: GIS信息预览组件
-->
<template>
	<div class="gis-preview">
		<div class="legend-panel">
			<!-- 故障信息展示 -->
			<!--			<div class="fault-info-section">-->
			<!--				<div class="section-header">故障信息</div>-->
			<!--				<div class="fault-info-grid">-->
			<!--					<div class="info-item">-->
			<!--						<span class="info-label">故障编号:</span>-->
			<!--						<span class="info-value">{{ faultInfo.faultNumber || '-' }}</span>-->
			<!--					</div>-->
			<!--					<div class="info-item">-->
			<!--						<span class="info-label">故障名称:</span>-->
			<!--						<span class="info-value">{{ faultInfo.faultName || '-' }}</span>-->
			<!--					</div>-->
			<!--					<div class="info-item">-->
			<!--						<span class="info-label">故障位置:</span>-->
			<!--						<span class="info-value">{{ faultInfo.faultLocation || '-' }}</span>-->
			<!--					</div>-->
			<!--				</div>-->
			<!--			</div>-->

			<!-- 图层控制 -->
			<div class="layer-control-section">
				<div class="section-header">预览</div>
				<div class="layer-types-horizontal">
					<div
						class="layer-type-item"
						v-for="(item, index) in availableLayers"
						:key="item.layerKey"
					>
						<label class="checkbox-wrapper">
							<input
								type="checkbox"
								:checked="visibleLayers[item.layerKey]"
								@change="toggleLayer(item.layerKey, $event)"
							/>
							<span class="checkmark"></span>
							<span class="layer-name">{{ item.layerName }}</span>
						</label>
					</div>
				</div>
			</div>
		</div>
		<div id="container" class="map-container"></div>
	</div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, ref, computed } from "vue"
import AMapLoader from "@amap/amap-jsapi-loader"

const props = defineProps<{
	previewData: any[]
	treeData: any[]
}>()

let map: any = null
let AMapInstance: any = null
// 存储所有GIS对象的引用，按 objectType 分组
const gisObjects = ref<{ [key: string]: any[] }>({})
// 标记地图是否已被销毁
const isMapDestroyed = ref(false)
// 控制每个图层的可见性
const visibleLayers = ref<{ [key: string]: boolean }>({})

// 统一的颜色
const UNIFIED_COLOR = "rgb(32, 74, 156)"

// 图标配置
const ICON_CONFIG = {
	XNJ: {
		svg: `<svg width="24" height="24" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
			<defs>
				<style>.a{fill:#fff;opacity:0;}.b{fill:${UNIFIED_COLOR};}</style>
			</defs>
			<g transform="translate(231.945 -890.659)">
				<rect class="a" width="16" height="16" transform="translate(-231.945 890.659)"/>
				<path class="b" d="M-173.049,906.325a7.342,7.342,0,0,0-7.333,7.333,7.342,7.342,0,0,0,7.333,7.334,7.342,7.342,0,0,0,7.334-7.334A7.342,7.342,0,0,0-173.049,906.325Zm0,13.667a6.341,6.341,0,0,1-6.333-6.334,6.34,6.34,0,0,1,6.333-6.333,6.341,6.341,0,0,1,6.334,6.333A6.342,6.342,0,0,1-173.049,919.992Zm4.239-3.334h-.239v-6h.239a.5.5,0,0,0,.5-.5.5.5,0,0,0-.5-.5h-.239v-.239a.5.5,0,0,0-.5-.5.5.5,0,0,0-.5.5v.239h-6v-.239a.5.5,0,0,0-.5-.5.5.5,0,0,0-.5.5v.239h-.239a.5.5,0,0,0-.5.5.5.5,0,0,0,.5.5h.239v6h-.239a.5.5,0,0,0-.5.5.5.5,0,0,0,.5.5h.239v.238a.5.5,0,0,0,.5.5.5.5,0,0,0,.5-.5v-.238h6v.238a.5.5,0,0,0,.5.5.5.5,0,0,0,.5-.5v-.238h.239a.5.5,0,0,0,.5-.5A.5.5,0,0,0-168.81,916.658Zm-7.239,0v-6h6v6Zm4.583-3.079c.213,0,.256-.043.288-.293a1.57,1.57,0,0,0,.512.181c-.064.468-.24.586-.741.586h-1.056c-.709,0-.9-.139-.9-.624l-.853.069-.038-.426.891-.069v-.267h-1.029v.72a5.5,5.5,0,0,1-.544,2.692a2.735,2.735,0,0,0-.543-.331,4.691,4.691,0,0,0,.463-2.367v-1.22h1.647v-1.061h.629v.336h1.7v.5h-1.7v.224h1.53l.1-.026.491.122a4.527,4.527,0,0,1-.347.927l-.559-.191c.037-.091.074-.2.112-.326h-1.354v.219l1.13-.085.037.416-1.167.095v.022c0,.154.048.176.357.176Zm-.5,1.945h1.285v.533h-3.95v-.533h1.119v-1.407h.587v1.407h.362v-1.407h.6Zm-1.967-1.21a4.03,4.03,0,0,1,.374.9l-.539.208a4.237,4.237,0,0,0-.336-.928Zm3.039.192c-.224.325-.448.688-.629.912l-.438-.182a5.4,5.4,0,0,0,.454-.938Z" transform="translate(-50.896 -15)"/>
			</g>
		</svg>`,
		size: 24
	},
	DLJ: {
		svg: `<svg width="24" height="24" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
			<defs>
				<style>.a{fill:#fff;opacity:0;}.b{fill:${UNIFIED_COLOR};}</style>
			</defs>
			<g transform="translate(558.073 -926.024)">
				<rect class="a" width="16" height="16" transform="translate(-558.073 926.024)"/>
				<path class="b" d="M-550.073,926.691a7.342,7.342,0,0,0-7.334,7.333,7.342,7.342,0,0,0,7.334,7.334,7.342,7.342,0,0,0,7.333-7.334A7.342,7.342,0,0,0-550.073,926.691Zm0,13.667a6.341,6.341,0,0,1-6.334-6.334,6.341,6.341,0,0,1,6.334-6.333,6.34,6.34,0,0,1,6.333,6.333A6.34,6.34,0,0,1-550.073,940.358Zm4.607-3.834h-1.106v-5h1.106a.5.5,0,0,0,.5-.5.5.5,0,0,0-.5-.5h-1.106v-1.106a.5.5,0,0,0-.5-.5.5.5,0,0,0-.5.5v1.106h-5v-1.106a.5.5,0,0,0-.5-.5.5.5,0,0,0-.5.5v1.106h-1.107a.5.5,0,0,0-.5.5.5.5,0,0,0,.5.5h1.107v5h-1.107a.5.5,0,0,0-.5.5.5.5,0,0,0,.5.5h1.107v1.107a.5.5,0,0,0,.5.5.5.5,0,0,0,.5-.5v-1.107h5v1.107a.5.5,0,0,0,.5.5.5.5,0,0,0,.5-.5v-1.107h1.106a.5.5,0,0,0,.5-.5A.5.5,0,0,0-545.466,936.524Zm-7.106,0v-5h5v5Z"/>
			</g>
		</svg>`,
		size: 24
	},
	BDZ: {
		svg: `<svg width="24" height="24" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg"><defs><style>.a{fill:#fff;opacity:0;}.b{fill:#409eff;}</style></defs><rect class="a" width="16" height="16"/><path class="b" d="M-626.756,913.252a6.34,6.34,0,0,1,6.333,6.333,6.34,6.34,0,0,1-6.333,6.334,6.34,6.34,0,0,1-6.333-6.334,6.34,6.34,0,0,1,6.333-6.333m0-1a7.333,7.333,0,0,0-7.333,7.333,7.334,7.334,0,0,0,7.333,7.334,7.334,7.334,0,0,0,7.333-7.334,7.333,7.333,0,0,0-7.333-7.333Zm0,3.333a4,4,0,0,1,4,4,4,4,0,0,1-4,4,4,4,0,0,1-4-4,4,4,0,0,1,4-4m0-1a5.006,5.006,0,0,0-5,5,5.006,5.006,0,0,0,5,5,5.006,5.006,0,0,0,5-5,5.006,5.006,0,0,0-5-5Zm2.333,5a2.336,2.336,0,0,0-2.333-2.333,2.336,2.336,0,0,0-2.333,2.333,2.336,2.336,0,0,0,2.333,2.334A2.336,2.336,0,0,0-624.423,919.585Z" transform="translate(634.756 -911.585)"/></svg>`,
		size: 24
	},
	PDS: {
		svg: `<svg width="24" height="24" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg"><defs><style>.a{fill:#fff;opacity:0;}.b{fill:#409eff;}</style></defs><g transform="translate(588.76 -900.683)"><rect class="a" width="16" height="16" transform="translate(-588.76 900.683)"/><path class="b" d="M-574.927,915.016h-11.667a.5.5,0,0,1-.5-.5V902.849a.5.5,0,0,1,.5-.5h11.667a.5.5,0,0,1,.5.5v11.667A.5.5,0,0,1-574.927,915.016Zm-11.167-1h10.667V903.349h-10.667Zm3.356-4.747h-.913v1.786h-.926v-4.744h1.839a1.462,1.462,0,0,1,1.566,1.479A1.461,1.461,0,0,1-582.738,909.269Zm-.046-2.132h-.867v1.3h.867a.627.627,0,0,0,.686-.647A.632.632,0,0,0-582.784,907.137Zm5.359,3.465a1.731,1.731,0,0,1-1.253.453h-1.712v-4.744h1.712a1.731,1.731,0,0,1,1.253.453,2.385,2.385,0,0,1,.479,1.9A2.476,2.476,0,0,1-577.425,910.6Zm-.647-3.171a.824.824,0,0,0-.693-.294h-.7v3.092h.7a.826.826,0,0,0,.693-.293c.174-.214.2-.554.2-1.273S-577.9,907.644-578.072,907.431Z"/></g></svg>`,
		size: 24
	}
}

// 创建标记内容的函数
const createMarkerContent = (objectTypeCode: string) => {
	console.log("objectTypeCode", objectTypeCode)
	const markerContent = document.createElement("div")

	// 查找匹配的图标配置
	const iconConfig = Object.entries(ICON_CONFIG).find(
		([key]) => objectTypeCode === key || objectTypeCode.includes(key)
	)

	if (iconConfig) {
		// 使用SVG图标
		const [, config] = iconConfig
		markerContent.className = "custom-marker svg-marker"
		markerContent.innerHTML = config.svg
		markerContent.style.width = `${config.size}px`
		markerContent.style.height = `${config.size}px`
	} else {
		// 使用默认的圆形标记
		markerContent.className = "custom-marker"
		markerContent.style.backgroundColor = UNIFIED_COLOR
		markerContent.style.border = "2px solid white"
		markerContent.style.borderRadius = "50%"
		markerContent.style.width = "14px"
		markerContent.style.height = "14px"
	}

	return markerContent
}

// 获取标记偏移量的函数
const getMarkerOffset = (objectTypeCode: string) => {
	// 检查是否有对应的SVG图标配置
	const hasIcon = Object.keys(ICON_CONFIG).some(
		(key) => objectTypeCode === key || objectTypeCode.includes(key)
	)

	if (hasIcon) {
		return new AMapInstance.Pixel(-12, -12) // SVG图标居中
	} else {
		return new AMapInstance.Pixel(-7, -7) // 圆形标记居中
	}
}

// 计算可用的图层列表
const availableLayers = computed(() => {
	if (!props.previewData?.length) return []

	// 从 previewData 中获取所有唯一的 objectTypeCode
	const uniqueObjectTypes = [
		...new Set(props.previewData.map((item) => item.objectTypeCode))
	]
	return uniqueObjectTypes.map((objectTypeCode) => ({
		layerKey: objectTypeCode,
		layerName: props.treeData[0].children.find(
			(item: any) => item.subitemValue === objectTypeCode
		).subitemName
	}))
})

// 计算故障信息
const faultInfo = computed(() => {
	if (!props.previewData?.length) {
		return {
			faultNumber: "",
			faultName: "",
			faultLocation: ""
		}
	}

	// 从第一条数据中提取故障信息，或者根据实际数据结构调整
	const firstItem = props.previewData[0]
	return {
		faultNumber: firstItem.faultNumber || firstItem.id || "",
		faultName: firstItem.faultName || firstItem.objectLabel || "",
		faultLocation: firstItem.faultLocation || firstItem.remark || ""
	}
})

// 初始化图层可见性
const initVisibleLayers = () => {
	const layers: { [key: string]: boolean } = {}

	// 从 previewData 中获取所有唯一的 objectTypeCode
	if (props.previewData?.length) {
		const uniqueObjectTypes = [
			...new Set(props.previewData.map((item) => item.objectTypeCode))
		]
		uniqueObjectTypes.forEach((objectTypeCode) => {
			layers[objectTypeCode] = true // 默认所有图层都可见
		})
	}

	visibleLayers.value = layers
}

// 切换图层显示/隐藏
const toggleLayer = (layerName: string, event: Event) => {
	const target = event.target as HTMLInputElement
	visibleLayers.value[layerName] = target.checked

	// 更新地图上对应类型的对象显示状态
	updateLayerVisibility(layerName, target.checked)
}

// 更新图层在地图上的可见性
const updateLayerVisibility = (layerName: string, visible: boolean) => {
	if (!isMapValid() || !gisObjects.value[layerName]) {
		console.log("Early return from updateLayerVisibility")
		return
	}

	const objects = gisObjects.value[layerName]
	console.log("Processing objects:", objects.length)

	objects.forEach((obj, index) => {
		try {
			if (visible) {
				console.log(`Showing object ${index}`)
				if (obj.show) {
					obj.show()
				} else {
					// 如果没有 show 方法，使用 add
					safeMapCall(() => map.add(obj))
				}
			} else {
				console.log(`Hiding object ${index}`)
				if (obj.hide) {
					obj.hide()
				} else {
					// 如果没有 hide 方法，使用 remove
					safeMapCall(() => map.remove(obj))
				}
			}
		} catch (error) {
			console.error(`Error toggling visibility for object ${index}:`, error)
		}
	})

	// 更新视图以适应可见对象
	setTimeout(() => {
		safeSetFitView()
	}, 100)
}

// 检查地图实例是否有效
const isMapValid = () => {
	try {
		return (
			map &&
			!isMapDestroyed.value &&
			typeof map.getContainer === "function" &&
			map.getContainer() &&
			typeof map.getView === "function" &&
			map.getView()
		)
	} catch (error) {
		console.error("Map validity check failed:", error)
		return false
	}
}

// 安全地调用地图方法
const safeMapCall = (callback: () => void) => {
	if (!map) {
		console.warn("Map instance is not initialized")
		return
	}

	if (!isMapValid()) {
		console.warn("Map is not in a valid state")
		return
	}

	try {
		callback()
	} catch (error) {
		console.error("Map operation failed:", error)
	}
}

// 安全地调整地图视图
const safeSetFitView = () => {
	if (!map || !AMapInstance) return

	try {
		// 获取所有可见的对象，按类型分组
		const visibleObjectsByType: { [key: string]: any[] } = {}
		let hasPolyline = false
		let hasAnyObject = false

		Object.entries(gisObjects.value).forEach(([type, objects]) => {
			// 只处理可见图层的对象
			if (objects && objects.length > 0 && visibleLayers.value[type]) {
				visibleObjectsByType[type] = objects
				hasAnyObject = true

				// 检查是否有 polyline 类型
				objects.forEach((obj) => {
					if (obj instanceof AMapInstance.Polyline) {
						hasPolyline = true
					}
				})
			}
		})

		// 如果没有任何对象，设置默认视图
		if (!hasAnyObject) {
			if (isMapValid()) {
				map.setCenter([116.397428, 39.90923])
				map.setZoom(11)
			}
			return
		}

		// 如果没有 polyline 对象，手动计算边界并设置视图
		if (!hasPolyline) {
			// 收集所有可见对象的坐标点
			const allPoints: any[] = []

			Object.values(visibleObjectsByType).forEach((objects) => {
				objects.forEach((obj) => {
					try {
						if (obj instanceof AMapInstance.Polygon) {
							// 对于多边形，获取所有路径点
							const path = obj.getPath()
							if (path && path.length) {
								allPoints.push(...path)
							}
						} else if (obj instanceof AMapInstance.Marker) {
							// 对于标记，获取位置点
							const position = obj.getPosition()
							if (position) {
								allPoints.push(position)
							}
						}
					} catch (e) {
						console.error("Failed to get object points:", e)
					}
				})
			})

			// 如果有点，计算边界并设置视图
			if (allPoints.length > 0) {
				try {
					// 计算边界
					let minLng = Infinity,
						maxLng = -Infinity,
						minLat = Infinity,
						maxLat = -Infinity

					allPoints.forEach((point) => {
						if (
							point &&
							typeof point.getLng === "function" &&
							typeof point.getLat === "function"
						) {
							const lng = point.getLng()
							const lat = point.getLat()

							minLng = Math.min(minLng, lng)
							maxLng = Math.max(maxLng, lng)
							minLat = Math.min(minLat, lat)
							maxLat = Math.max(maxLat, lat)
						}
					})

					// 确保边界有效
					if (
						minLng !== Infinity &&
						maxLng !== -Infinity &&
						minLat !== Infinity &&
						maxLat !== -Infinity
					) {
						// 创建边界矩形
						const bounds = new AMapInstance.Bounds(
							new AMapInstance.LngLat(minLng, minLat),
							new AMapInstance.LngLat(maxLng, maxLat)
						)

						// 设置视图到这个边界，添加一些边距
						setTimeout(() => {
							if (isMapValid()) {
								map.setBounds(bounds, true, [60, 60, 60, 60])
							}
						}, 100)
						return
					}
				} catch (e) {
					console.error("Failed to calculate bounds:", e)
				}
			}

			// 如果上面的方法失败，设置默认视图
			setTimeout(() => {
				if (isMapValid()) {
					map.setCenter([116.397428, 39.90923])
					map.setZoom(11)
				}
			}, 100)
			return
		}

		// 如果有 polyline 对象，使用标准的 setFitView
		setTimeout(() => {
			if (isMapValid()) {
				try {
					// 获取所有可见对象
					const allVisibleObjects = Object.values(visibleObjectsByType).flat()
					map.setFitView(allVisibleObjects, false, [60, 60, 60, 60])
				} catch (e) {
					console.error("Failed to set fit view:", e)
					// 回退到默认视图
					if (isMapValid()) {
						map.setCenter([116.397428, 39.90923])
						map.setZoom(11)
					}
				}
			}
		}, 100)
	} catch (error) {
		console.error("Failed to set fit view:", error)
		// 最终回退：设置默认视图
		try {
			if (isMapValid()) {
				map.setCenter([116.397428, 39.90923])
				map.setZoom(11)
			}
		} catch (e) {
			console.error("Failed to set default view:", e)
		}
	}
}

// 初始化地图
const initMap = async () => {
	if (isMapValid()) return
	console.log(666)

	isMapDestroyed.value = false
	try {
		AMapLoader.load({
			key: import.meta.env.VITE_AMAP_KEY,
			version: "2.0",
			plugins: ["AMap.PolygonEditor", "AMap.PolylineEditor"]
		})
			.then((AMap) => {
				console.log(2312321321312321)

				if (isMapDestroyed.value) return

				AMapInstance = AMap
				map = new AMap.Map("container", {
					zoom: 11,
					center: [116.397428, 39.90923]
				})

				// 确保地图加载完成后再渲染对象
				map.on("complete", () => {
					renderGisObjects()
				})

				// 如果地图已经完成初始化，直接渲染
				if (map.getStatus().complete) {
					renderGisObjects()
				}
			})
			.catch((error) => {
				console.error("Failed to load AMap:", error)
			})
	} catch (error) {
		console.error("Failed to load AMap:", error)
	}
}

// 渲染GIS对象
const renderGisObjects = () => {
	if (!isMapValid() || !props.previewData?.length || !AMapInstance) return

	// 初始化图层可见性（在有数据时）
	initVisibleLayers()

	// 清除所有现有对象
	Object.values(gisObjects.value).forEach((objects) => {
		objects.forEach((obj) => {
			safeMapCall(() => map.remove(obj))
		})
	})

	// 重置对象存储
	gisObjects.value = {}

	props.previewData.forEach((item) => {
		const points = JSON.parse(item.plotPoint || "[]")
		if (!points.length) return

		// 将坐标数组转换为 LngLat 对象数组
		const lngLatPoints = points.map(
			([lng, lat]: [number, number]) => new AMapInstance.LngLat(lng, lat)
		)
		console.log(points)

		let gisObject
		switch (item.amapObjectType.toLowerCase()) {
			case "polygon":
				gisObject = new AMapInstance.Polygon({
					path: lngLatPoints,
					strokeColor: UNIFIED_COLOR,
					strokeWeight: 2,
					strokeOpacity: 0.6,
					fillOpacity: 0.4,
					fillColor: UNIFIED_COLOR,
					zIndex: 50
				})
				break
			case "ployline":
				gisObject = new AMapInstance.Polyline({
					path: lngLatPoints,
					strokeColor: UNIFIED_COLOR,
					strokeWeight: 2,
					strokeOpacity: 0.8,
					zIndex: 50
				})
				break
			case "polyline":
				gisObject = new AMapInstance.Polyline({
					path: lngLatPoints,
					strokeColor: UNIFIED_COLOR,
					strokeWeight: 2,
					strokeOpacity: 0.8,
					zIndex: 50
				})
				break
			case "marker":
				const [lng, lat] = points[0]
				const position = new AMapInstance.LngLat(lng, lat)

				// 创建标记内容和偏移量
				const markerContent = createMarkerContent(item.objectTypeCode)
				const offset = getMarkerOffset(item.objectTypeCode)

				gisObject = new AMapInstance.Marker({
					position,
					content: markerContent,
					title: item.objectLabel,
					offset: offset
				})
				break
		}

		// 直接使用 objectTypeCode 作为键
		if (gisObject && item.objectTypeCode) {
			const layerKey = item.objectTypeCode

			console.log(
				`Storing object for layer: ${layerKey}, type: ${item.amapObjectType}`
			)

			gisObjects.value[layerKey] = gisObjects.value[layerKey] || []
			gisObjects.value[layerKey].push(gisObject)

			// 总是将对象添加到地图上
			console.log(`Adding object to map for layer: ${layerKey}`)
			safeMapCall(() => map.add(gisObject))

			// 然后根据图层可见性控制显示/隐藏
			if (!visibleLayers.value[layerKey]) {
				console.log(`Layer ${layerKey} is not visible, hiding object`)
				if (gisObject.hide) {
					gisObject.hide()
				}
			}
		}
	})

	// 打印最终的 gisObjects 结构
	console.log("Final gisObjects structure:", {
		keys: Object.keys(gisObjects.value),
		counts: Object.fromEntries(
			Object.entries(gisObjects.value).map(([key, objects]) => [
				key,
				objects.length
			])
		),
		visibleLayers: visibleLayers.value
	})

	// 自适应显示所有点位
	safeSetFitView()
}

onMounted(() => {
	console.log("地图初始化")
	initMap()
})

onUnmounted(() => {
	if (map) {
		try {
			// 移除所有覆盖物
			Object.values(gisObjects.value).forEach((objects) => {
				objects.forEach((obj) => {
					if (obj) map.remove(obj)
				})
			})
			// 清空存储
			gisObjects.value = {}
			// 销毁地图
			map.destroy()
		} catch (error) {
			console.error("Error during map cleanup:", error)
		} finally {
			isMapDestroyed.value = true
			map = null
			AMapInstance = null
		}
	}
})
</script>

<style lang="scss" scoped>
.gis-preview {
	width: 100%;
	height: 100%;
	position: relative;

	.legend-panel {
		position: absolute;
		top: 10px;
		left: 10px;
		z-index: 100;
		background: #0a4e9a;
		padding: 8px;
		box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
		min-width: 320px;
		max-width: 500px;

		.section-header {
			font-weight: 600;
			font-size: 14px;
			color: #fff;
			margin-bottom: 8px;
			padding-bottom: 8px;
			border-bottom: 1px solid #3468b7;
		}

		.fault-info-section {
			margin-bottom: 20px;

			.fault-info-grid {
				display: grid;
				grid-template-columns: 1fr 1fr;
				gap: 8px 16px;

				.info-item {
					display: flex;
					align-items: center;
					min-height: 24px;

					.info-label {
						font-size: 12px;
						color: #666;
						white-space: nowrap;
						margin-right: 4px;
						min-width: 60px;
					}

					.info-value {
						font-size: 12px;
						color: #333;
						font-weight: 500;
						flex: 1;
						word-break: break-all;
					}

					&:nth-child(3) {
						grid-column: 1 / -1; /* 故障位置占满整行 */
					}
				}
			}
		}

		.layer-control-section {
			.layer-types-horizontal {
				display: flex;
				flex-wrap: wrap;
				gap: 12px;

				.layer-type-item {
					.checkbox-wrapper {
						display: flex;
						align-items: center;
						cursor: pointer;
						user-select: none;
						position: relative;
						white-space: nowrap;

						input[type="checkbox"] {
							position: absolute;
							opacity: 0;
							cursor: pointer;
							height: 0;
							width: 0;
						}

						.checkmark {
							height: 16px;
							width: 16px;
							background-color: #fff;
							border: 2px solid #ddd;
							border-radius: 3px;
							margin-right: 6px;
							position: relative;
							transition: all 0.2s ease;
							flex-shrink: 0;

							&::after {
								content: "";
								position: absolute;
								display: none;
								left: 4px;
								top: 1px;
								width: 4px;
								height: 8px;
								border: solid white;
								border-width: 0 2px 2px 0;
								transform: rotate(45deg);
							}
						}

						input:checked ~ .checkmark {
							background-color: #1791fc;
							border-color: #1791fc;

							&::after {
								display: block;
							}
						}

						input:hover ~ .checkmark {
							border-color: #1791fc;
						}

						.layer-name {
							font-size: 13px;
							color: #fff;
							font-weight: 500;
						}
					}
				}
			}
		}
	}

	.map-container {
		width: 100%;
		height: 100%;
	}
}

/* 全局样式，用于地图标记 */
:global(.custom-marker) {
	display: flex;
	align-items: center;
	justify-content: center;
}

:global(.svg-marker) {
	background: transparent !important;
	border: none !important;
}

:global(.svg-marker img) {
	display: block;
}
</style>
