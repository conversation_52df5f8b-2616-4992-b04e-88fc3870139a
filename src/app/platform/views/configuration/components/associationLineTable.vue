<template>
	<div class="line-table">
		<PitayaTable
			:need-index="true"
			:table-data="lineTableData"
			:columns="lineTableColumn"
			:customizeHeightNumber="0"
			:need-pagination="false"
			:table-loading="tableLoading"
		>
			<template #name="{ rowData }">
				<div>
					<el-tag
						class="line-item-btn"
						:style="{
							backgroundColor: rowData.colour,
							color: '#FFF',
							border: 'unset'
						}"
					>
            {{ rowData.name }}
          </el-tag>
				</div>
			</template>
			<template #operations="{ rowData }">
				<el-button v-btn color="var(--pitaya-btn-background)" link v-show="!(props.isView)">
					<font-awesome-icon
						:icon="['fas', 'trash-can']"
						style="color: var(--pitaya-btn-background)"
					/>
					<span class="table-inner-btn" @click="deleteRow(rowData)">移除</span>
				</el-button>
				<p v-show="props.isView">不可操作</p>
			</template>
			<template #footerOperateLeft>
				<el-button v-btn style="border-radius: 0" @click="showChooseLine" v-show="!(props.isView)">
					<font-awesome-icon
						:icon="['fas', 'square-plus']"
						style="color: #fff"
					/>
					<span class="choose-line-btn">选择线路</span>
				</el-button>
			</template>
		</PitayaTable>
		<Drawer
			v-show="showChooseLineDrawer"
			class="inner-drawer drawer-hidden-box"
			:size="300"
			v-model:drawer="showChooseLineDrawer"
		>
			<Title :title="chooseLineTitle" />
			<PitayaTree
				ref="treeRef"
				:need-check-box="true"
				:tree-data="lineTreeData"
				:tree-props="lineTreeProps"
				:filter-node-method="filterNodes"
				:tree-loading="lineTreeLoading"
			/>
			<div class="btn-list">
				<ButtonList :button="chooseLineBtn" @onBtnClick="chooseLine" />
			</div>
		</Drawer>
	</div>
</template>

<script lang="ts" setup>
import { expertManagementApi } from "@/app/platform/api/configuration/expertManagement"
import { remove, map } from "lodash-es"
import { CustomMessageBox } from "@/app/platform/utils/message"

interface Tree {
	id: number | string
	label: string
	children?: Tree[]
}
const lineTreeProps = {
	children: "children",
	label: "name"
}
const lineTableColumn: TableColumnType[] = [
	{ label: "线路编码", prop: "code" },
	{ label: "线路名称", prop: "name", needSlot: true, minWidth: 150 },
	{ label: "起始站", prop: "firstStation", width: 120 },
	{ label: "终点站", prop: "lastStation", width: 120 },
	{
		label: "操作",
		prop: "operations",
		fixed: "right",
		width: 100,
		needSlot: true
	}
]
const chooseLineTitle = {
	name: ["选择线路"],
	icon: ["fas", "square-share-nodes"]
}
const chooseLineBtn = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "确定",
		icon: ["fas", "floppy-disk"]
	}
]
const lineTreeLoading = ref(false)
const lineTableData = ref<any[]>([])
const showChooseLineDrawer = ref<boolean>(false)
const lineTreeData = ref<Tree[]>([])
const treeRef = ref<any>()
const selectIds = ref<string[]>([])
const tableLoading = ref(false)
const receiveQueryLineArr:any = inject('receiveQueryLineArr')
const props = defineProps({
	lines: {
		type: Array,
		default: []
	},
	isView: {
		type: Boolean,
		default: false
	}
})

watch(
	() => props.lines,
	(val: any) => {
		lineTableData.value = val
		selectIds.value = val && val.length > 0 ? map(val, (item) => item.id) : []
	},
	{ immediate: true }
)

/**
 * @description 删除选择的线路
 * @param row 选择的行
 */
const deleteRow = (row: any) => {
	CustomMessageBox({ message: "确定要移除吗?" }, (res: boolean) => {
		if (res) {
			const newKeysArr = remove(selectIds.value, (id) => row.id !== id)
			const newSelectNodes = remove(
				lineTableData.value,
				(node) => row.id !== node.id
			)
			selectIds.value = newKeysArr
			lineTableData.value = newSelectNodes
		}
	})
}

/**
 * @description 展示选择线路弹窗
 */
const showChooseLine = () => {
	getLineTreeData()
	showChooseLineDrawer.value = true
	nextTick(() => {
		if (selectIds.value.length > 0) {
			treeRef.value.PitayaTreeRef.setCheckedKeys(selectIds.value, true)
		}
	})
}

/**
 * @description 获取线路树的数据
 */
const getLineTreeData = () => {
	lineTreeLoading.value = true
	expertManagementApi.getLineListApi(
		objectToFormData({
			currentPage: 1,
			pageSize: 500
		})
	)
		.then((res: any) => {
			lineTreeData.value = res.rows ?? []
			receiveQueryLineArr(lineTreeData.value)
		})
		.finally(() => {
			lineTreeLoading.value = false
		})
}

/**
 * @description 选择线路
 */
const chooseLine = (btnName: string | undefined) => {
	if (btnName === "确定") {
		// 将勾选的key和节点存入
		const currentSelectKeys = treeRef.value.PitayaTreeRef.getCheckedKeys()
		const currentSelectNodes = treeRef.value.PitayaTreeRef.getCheckedNodes()
		selectIds.value = currentSelectKeys
		lineTableData.value = currentSelectNodes
	}
	showChooseLineDrawer.value = false
}

/**
 * @description 过滤名称
 */
const filterNodes = (value: string, data: anyKey) => {
	if (!value) return true
	return data.label.includes(value)
}

defineExpose({
	selectIds: selectIds,
	lineTreeData: lineTreeData.value
})
defineOptions({
	name: "associationLineTable"
})
</script>

<style lang="scss" scoped>
:deep(.el-tree-node__content) {
	height: 32px;
}
:deep(.el-tree-node__label) {
	font-size: var(--pitaya-fs-12);
}
.choose-line-btn {
	margin-left: 5px;
	color: #fff;
	font-weight: 500;
}
.inner-drawer {
	position: relative;
	height: 100%;
	.tree-search {
		margin-top: 10px;
		padding: 0 10px;
	}
	.el-tree {
		margin-top: 10px;
	}
	.pitaya-tree-container {
		padding-bottom: 53px;
	}
	.btn-list {
		display: flex;
		justify-content: flex-end;
		position: fixed;
		right: 10px;
		bottom: 0px;
		z-index: 99;
		padding: 10px 10px 10px 0;
		box-sizing: border-box;
		width: 290px;
		border-top: 1px solid #ccc;
		background-color: #fff;
	}
}
</style>