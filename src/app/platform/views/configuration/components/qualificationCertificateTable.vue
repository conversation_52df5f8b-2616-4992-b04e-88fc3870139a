<template>
	<div class="certificate-table">
		<PitayaTable
			:need-index="true"
			:customizeHeightNumber="0"
			:table-data="certificateTableData"
			:columns="certificateTableColumn"
			:need-pagination="false"
			:table-loading="tableLoading"
		>
			<template #operations="{ rowData }">
        <el-button v-btn link @click="viewCertificate(rowData)" v-show="!(props.isView)">
					<font-awesome-icon :icon="['fas', 'eye']" style="color: var(--pitaya-btn-background)" />
					<span class="table-inner-btn">查看</span>
				</el-button>
				<el-button v-btn link color="var(--pitaya-btn-background)" v-show="!(props.isView)">
					<font-awesome-icon :icon="['fas', 'trash-can']"	style="color: var(--pitaya-btn-background)"	/>
					<span class="table-inner-btn" @click="deleteRow(rowData)">移除</span>
				</el-button>
				<p v-show="props.isView">不可操作</p>
			</template>
			<template #footerOperateLeft>
				<el-button v-btn style="border-radius: 0" @click="showSubmitCertificate" v-show="!(props.isView)">
					<font-awesome-icon :icon="['fas', 'square-plus']"	style="color: #fff"	/>
					<span class="choose-certificate-btn">上传资质</span>
				</el-button>
			</template>
		</PitayaTable>
		<Drawer
			v-show="showSubmitCertificateDrawer"
			class="inner-drawer drawer-hidden-box"
			:size="300"
			v-model:drawer="showSubmitCertificateDrawer"
		>
			<Title :title="submitCertificateTitle" />
			<section class="form_content">
        <el-form
			  	class="form-container"
			  	ref="certificateFormRef"
			  	label-position="top"
			  	label-width="100px"
			  	:model="certificateForm"
			  	:rules="rules"
			  >
			  	<el-form-item label="证书名称" prop="certificateName">
			  		<el-input
			  			v-model.trim="certificateForm.certificateName"
			  			:formatter="(value: string) => value.trim()"
			  			maxlength="50"
			  			:show-word-limit="true"
			  			placeholder="请输入证书名称"
			  		/>
			  	</el-form-item>
          <el-form-item label="上传资质证书" prop="certificateUrl">
            <UploadFile
							ref="uploadFileRef"
							:action="`/pitaya/system/common/upload?businessType=0`"
							:viewDesc="true"
							:maxCount="1"
							:accept="'.png,.jpeg,.jpg'"
							:allExtensions="['.png','.jpg','.jpeg']"
							:allowsize="100"
							@onSuccess="handleSuccess"
							@handle-remove="handleRemove"
							listType="text"
						/>
			  	</el-form-item>
			  </el-form>
      </section>
			<div class="btn-list" v-show="!(props.isView)">
				<ButtonList	:loading="btnLoading" :button="submitCertificateBtn" @onBtnClick="submitCertificate" />
			</div>
		</Drawer>

		<!-- 查看专家资质证书 -->
		 <Drawer
			class="inner-drawer drawer-hidden-box"
			:size="300"
			v-model:drawer="showViewCertificateDrawer"
		>
			<Title :title="viewCertificateTitle" />
			<section class="form_content">
        <el-form
			  	class="form-container"
			  	ref="certificateFormRef"
			  	label-position="top"
			  	label-width="100px"
			  	:model="viewCertificateForm"
			  >
			  	<el-form-item label="证书名称" prop="certificateName">
			  		<el-input v-model.trim="viewCertificateForm.certificateName" :disabled="true"/>
			  	</el-form-item>
          <el-form-item label="上传资质证书" prop="certificateUrl">
            <img :src="viewCertificateForm.certificateUrl" />
			  	</el-form-item>
			  </el-form>
      </section>
		</Drawer>
	</div>
</template>

<script lang="ts" setup>
import { CustomMessageBox } from "@/app/platform/utils/message"
import type { FormInstance, FormRules } from "element-plus"
import { expertManagementApi } from "@/app/platform/api/configuration/expertManagement"

const certificateTableColumn: TableColumnType[] = [
	{ label: "证书名称", prop: "certificateName" },
	{ label: "格式", prop: "format" },
	{ label: "上传人", prop: "uploader" },
	{ label: "上传时间", prop: "uploadTime", minWidth: 150 },
	{
		label: "操作",
		prop: "operations",
		fixed: "right",
		width: 200,
		needSlot: true
	}
]
const submitCertificateTitle = {
	name: ["新增资质证书"],
	icon: ["fas", "square-share-nodes"]
}
const viewCertificateTitle = {
	name: ["查看资质证书"],
	icon: ["fas", "square-share-nodes"]
}
const submitCertificateBtn = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "提交",
		icon: ["fas", "floppy-disk"]
	}
]
const certificateTableData = ref<any[]>([])
const certificateTableLength = ref<number>(0)
const showSubmitCertificateDrawer = ref<boolean>(false)
const showViewCertificateDrawer = ref<boolean>(false)
const tableLoading = ref(false)
// 表单相关
const certificateFormRef = ref<FormInstance>()
const uploadFileRef = ref<any>(null)
const certificateForm: anyKey = reactive({
	certificateName: null,
  certificateUrl: ''
})
const viewCertificateForm: anyKey = reactive({
	certificateName: null,
  certificateUrl: '',
	format: ""
})
const rules = reactive<FormRules<typeof certificateForm>>({
	certificateName: [
	  { required: true, message: "请输入证书名称", trigger: "blur" }
	],
  certificateUrl: [
	  { required: true, message: "请上传资质证书", trigger: "change" }
	],
})
const btnLoading = ref<boolean>(false)
const props = defineProps({
	empNo: {
		type: String,
		default: null
	},
	isView: {
		type: Boolean,
		default: false
	}
})

onMounted(() => {
	getCertificateList()
})

const emit = defineEmits([
	"uploadSuccess"
])

/**
 * @description 获取证书列表
 */
const getCertificateList = () => {
	tableLoading.value = true
	expertManagementApi
		.getCertificateListApi({ empNo: props.empNo })
		.then((res: any) => {
			certificateTableData.value = res
			certificateTableLength.value = res.length
		})
		.catch((err) => {
			throw new Error("getCertificateListApi():::" + err)
		})
		.finally(() => {
			tableLoading.value = false
		})
}

/**
 * 附件上传成功 回调
 * @param response
 */
const handleSuccess = (response: Record<string, any>) => {
	certificateForm.format = response.data.fileType
	certificateForm.certificateUrl = response.data.filePath
}
/**
 * 手动删除附件 回调
 * @param uploadFile
 * @param fileList
 */
const handleRemove = (uploadFile: any, fileList: any) => {
	certificateForm.certificateUrl = ""
	certificateForm.format = ""
	setTimeout(() => {
		certificateFormRef.value?.clearValidate()
	})
}

/**
 * @description 删除上传的资质
 * @param row 选择的行
 */
const deleteRow = (row: any) => {
	CustomMessageBox({ message: "确定要移除吗?" }, (res: boolean) => {
		if (res) {
			expertManagementApi
				.deleteCertificateApi({ empNo: row.empNo, id: row.id })
				.then((res: any) => {
					ElMessage.success("移除成功")
				})
				.catch((err) => {
					throw new Error("deleteCertificateApi():::" + err)
				})
				.finally(() => {
					getCertificateList()
				})
		}
	})
}

/**
 * @description 展示上传资质弹窗
 */
const showSubmitCertificate = () => {
	showSubmitCertificateDrawer.value = true
}

/**
 * @description 上传资质
 */
const submitCertificate = (btnName: string | undefined) => {
	if (btnName === "提交") {
		certificateFormRef.value?.validate((valid) => {
      if(valid){
				btnLoading.value = true
				expertManagementApi
				.addCertificateApi({ empNo: props.empNo, ...certificateForm })
				.then((res: any) => {
					ElMessage.success("上传成功")
					uploadFileRef.value.clearFileList()
				})
				.catch((err) => {
					throw new Error("addCertificateApi():::" + err)
				})
				.finally(() => {
					showSubmitCertificateDrawer.value = false
					getCertificateList()
    			certificateFormRef.value?.resetFields()
					btnLoading.value = false
				})
      }
    })
	}else{
    certificateFormRef.value?.resetFields()
    showSubmitCertificateDrawer.value = false
  }
}

/**
 * @description 查看证书
 * @param row 要查看的列
 */
const viewCertificate = (row: any) => {
	Object.assign(viewCertificateForm, row)
	showViewCertificateDrawer.value = true
}
defineExpose({
	certificateTableLength: certificateTableLength
})
defineOptions({
	name: "qualificationCertificateTable"
})
</script>

<style lang="scss" scoped>
:deep(.el-tree-node__content) {
	height: 32px;
}
:deep(.el-tree-node__label) {
	font-size: var(--pitaya-fs-12);
}
.choose-device-btn {
	margin-left: 5px;
	color: #fff;
	font-weight: 500;
}
.inner-drawer {
	position: relative;
	height: 100%;
	.tree-search {
		margin-top: 10px;
		padding: 0 10px;
	}
	.el-tree {
		margin-top: 10px;
	}
	.pitaya-tree-container {
		padding-bottom: 53px;
	}
  .form_content{
    width: 100%;
    padding: 4px;
    padding-top: 10px;
    box-sizing: border-box;
  }
	.btn-list {
		display: flex;
		justify-content: flex-end;
		position: fixed;
		right: 10px;
		bottom: 0px;
		z-index: 99;
		padding: 10px 10px 10px 0;
		box-sizing: border-box;
		width: 290px;
		border-top: 1px solid #ccc;
		background-color: #fff;
	}
}
</style>