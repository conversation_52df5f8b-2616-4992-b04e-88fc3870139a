<!--
 * @Author: liulianming
 * @Date: 2025-07-13 23:06:37
 * @LastEditors: liulianming
 * @LastEditTime: 2025-07-14 15:36:31
 * @Description: 文件上传组件
-->
<template>
	<div class="upload-container">
		<el-upload
			class="upload-demo"
			action="#"
			:on-success="handleSuccess"
			:on-error="handleError"
			:before-upload="beforeUpload"
			:on-change="handleChange"
			:headers="headers"
			accept=".json"
			:limit="1"
			:auto-upload="false"
			name="file"
			ref="uploadRef"
			:on-exceed="handleExceed"
			:http-request="noop"
		>
			<template #trigger>
				<el-button type="primary">选择文件</el-button>
			</template>
			<template #tip>
				<div class="el-upload__tip">只能上传JSON文件,大小不能超过2M</div>
			</template>
		</el-upload>
		<!-- <div class="upload-footer">
			<el-button @click="$emit('close')">取消</el-button>
			<el-button type="primary" @click="submitUpload" :loading="uploading">
				{{ uploading ? "上传中..." : "确认上传" }}
			</el-button>
		</div> -->
	</div>
</template>

<script setup lang="ts">
import { ref } from "vue"
import {
	genFileId,
	UploadUserFile,
	type UploadInstance,
	type UploadProps,
	type UploadRawFile,
	type UploadRequestHandler,
	type UploadStatus
} from "element-plus"
import {
	globalUploadApi
} from "@/app/baseline/api/configuration"
import { getIdempotentToken } from "@/app/baseline/utils/validate"
import {
	IIdempotentTokenType,
	IIdempotentTokenTypePre
} from "@/app/baseline/utils/types/common"

const props = defineProps<{
	uploadUrl: string
}>()

const emit = defineEmits<{
	(e: "success"): void
	(e: "close"): void
}>()

const uploadRef = ref<UploadInstance>()
const uploading = ref(false)
const fileList = ref<any[]>([])
const timer = ref<any>(null)

// 添加文件改变事件处理
const handleChange = (file: UploadUserFile, fileList: UploadUserFile[]) => {
	console.log("file", file)

	// 文件校验
	if (file.raw) {
		// 检查文件类型
		const isJson = file.raw.type === "application/json" || file.name.toLowerCase().endsWith('.json')
		if (!isJson) {
			ElMessage.error("只能上传 JSON 文件!")
			uploadRef.value?.clearFiles()
			return
		}

		// 检查文件大小 (2MB = 2 * 1024 * 1024 bytes)
		const maxSize = 2 * 1024 * 1024
		if (file.raw.size > maxSize) {
			ElMessage.error("文件大小不能超过 2MB!")
			uploadRef.value?.clearFiles()
			return
		}
	}

	const idempotentToken = getIdempotentToken(
		IIdempotentTokenTypePre.apply,
		IIdempotentTokenType.common
	)
	console.log("idempotentToken", idempotentToken)
	fileList.map((item) => {
		item.status = "uploading" as UploadStatus
		item.percentage = 0
	})
	//防抖 只触发最后一次
	clearTimeout(timer.value)
	timer.value = setTimeout(() => {
		globalUploadApi(
			fileList,
			{},
			props.uploadUrl,
			idempotentToken
		)
			.then((res: any) => {
				const { data } = res
				fileList.map((item) => {
					item.status = "success" as UploadStatus
					item.percentage = 100
				})
				emit("success")
			})
			.catch((error) => {
				fileList.map((item) => {
					item.status = "error" as UploadStatus
					item.percentage = 0
				})
				ElMessage.error(error.message || "上传失败")
			})
			.finally(() => {
				timer.value = null
			})
	}, 300)
}

// 获取请求头，可以根据需要添加token等认证信息
const headers = {
	// 'Authorization': 'Bearer ' + getToken()
}

const beforeUpload: UploadProps["beforeUpload"] = (file) => {
	// 检查文件类型
	const isJson = file.type === "application/json" || file.name.toLowerCase().endsWith('.json')
	if (!isJson) {
		ElMessage.error("只能上传 JSON 文件!")
		return false
	}

	// 检查文件大小 (2MB = 2 * 1024 * 1024 bytes)
	const maxSize = 2 * 1024 * 1024
	if (file.size > maxSize) {
		ElMessage.error("文件大小不能超过 2MB!")
		return false
	}

	return true
}

const handleSuccess = (response: any) => {
	uploading.value = false
	if (response.code === 200) {
		ElMessage.success("上传成功")
		emit("success")
	} else {
		ElMessage.error(response.msg || "上传失败")
	}
}

const handleError = () => {
	uploading.value = false
	ElMessage.error("上传失败")
}

const submitUpload = () => {
	console.log("🚀 ~ submitUpload ~ fileList:", fileList.value)
	if (!fileList.value.length) {
		ElMessage.warning("请选择要上传的文件")
		return
	}
	uploading.value = true
	uploadRef.value?.submit()
}
const handleExceed: UploadProps["onExceed"] = (files) => {
	uploadRef.value!.clearFiles()
	const file = files[0] as UploadRawFile
	file.uid = genFileId()
	uploadRef.value!.handleStart(file)
}

const noop: UploadRequestHandler = () => {
	return Promise.resolve()
}
</script>

<style lang="scss" scoped>
.upload-container {
	padding: 20px;
	.upload-footer {
		margin-top: 20px;
		text-align: right;
		.el-button {
			margin-left: 10px;
		}
	}
}
</style>
