<template>
	<div class="device-table">
		<PitayaTable
			:need-index="true"
			:table-data="deviceTableData"
			:customizeHeightNumber="0"
			:columns="deviceTableColumn"
			:need-pagination="false"
			:table-loading="tableLoading"
		>
			<template #operations="{ rowData }">
				<el-button v-btn color="var(--pitaya-btn-background)" link v-show="!(props.isView)">
					<font-awesome-icon
						:icon="['fas', 'trash-can']"
						style="color: var(--pitaya-btn-background)"
					/>
					<span class="table-inner-btn" @click="deleteRow(rowData)">移除</span>
				</el-button>
				<p v-show="props.isView">不可操作</p>
			</template>
			<template #footerOperateLeft>
				<el-button v-btn style="border-radius: 0" @click="showChooseDevice" v-show="!(props.isView)">
					<font-awesome-icon
						:icon="['fas', 'square-plus']"
						style="color: #fff"
					/>
					<span class="choose-device-btn">选择设备分级</span>
				</el-button>
			</template>
		</PitayaTable>
		<Drawer
			v-show="showChooseDeviceDrawer"
			class="inner-drawer drawer-hidden-box"
			:size="300"
			v-model:drawer="showChooseDeviceDrawer"
		>
			<Title :title="choosedeviceTitle" />
			<PitayaTree
				ref="treeRef"
				:need-check-box="true"
				:tree-data="deviceTreeData"
				:tree-props="deviceTreeProps"
				:tree-loading="deviceTreeLoading"
				nodeKey="composeCode"
			/>
			<div class="btn-list">
				<ButtonList :button="chooseDeviceBtn" @onBtnClick="chooseDevice" />
			</div>
		</Drawer>
	</div>
</template>

<script lang="ts" setup>
import { remove, map } from "lodash-es"
import { CustomMessageBox } from "@/app/platform/utils/message"
import { expertManagementApi } from "@/app/platform/api/configuration/expertManagement"

interface Tree {
	id: number | string
	label: string
	children?: Tree[]
}
const deviceTreeProps = {
	children: "children",
	label: "selfName"
}
const deviceTableColumn: TableColumnType[] = [
	{ label: "分级名称", prop: "selfName" },
	{ label: "合成名称", prop: "composeName", minWidth: 150 },
	{
		label: "操作",
		prop: "operations",
		fixed: "right",
		width: 100,
		needSlot: true
	}
]
const choosedeviceTitle = {
	name: ["选择设备分级"],
	icon: ["fas", "square-share-nodes"]
}
const chooseDeviceBtn = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "确定",
		icon: ["fas", "floppy-disk"]
	}
]
const deviceTreeLoading = ref(false)
const deviceTableData = ref<any[]>([])
const showChooseDeviceDrawer = ref<boolean>(false)
const deviceTreeData = ref<Tree[]>([])
const treeRef = ref<any>()
const selectCodes = ref<string[]>([])
const tableLoading = ref(false)
const props = defineProps({
	vequipmentClasses: {
		type: Array,
		default: []
	},
	isView: {
		type: Boolean,
		default: false
	}
})

watch(
	() => props.vequipmentClasses,
	(val: any) => {
		deviceTableData.value = val
		selectCodes.value = val && val.length > 0 ? map(val, (item) => item.composeCode) : []
	},
	{ immediate: true }
)

/**
 * @description 删除选择的设备
 * @param row 选择的行
 */
const deleteRow = (row: any) => {
	CustomMessageBox({ message: "确定要移除吗?" }, (res: boolean) => {
		if (res) {
			const newKeysArr = remove(selectCodes.value, (id) => row.composeCode !== id)
			const newSelectNodes = remove(
				deviceTableData.value,
				(node) => row.composeCode !== node.composeCode
			)
			selectCodes.value = newKeysArr
			deviceTableData.value = newSelectNodes
		}
	})
}

/**
 * @description 展示选择设备分级弹窗
 */
const showChooseDevice = () => {
	getDeviceTreeData()
	showChooseDeviceDrawer.value = true
	nextTick(() => {
		if (selectCodes.value.length > 0) {
			treeRef.value.PitayaTreeRef.setCheckedKeys(selectCodes.value, true)
		}
	})
}

/**
 * @description 获取最底层数据
 */
const getDeepData = (data: any[]) => {
	let list: any = []
	data.forEach((item) => {
    if (item.children && item.children.length > 0) {
      getDeepData(item.children);
    } else {
      list.push(item);
    }
  });
  return list;
}

/**
 * @description 获取设备分级树的数据
 */
const getDeviceTreeData = () => {
	deviceTreeLoading.value = true
	expertManagementApi.getVEquipmentClassApi(
		objectToFormData({
			currentPage: 1,
			pageSize: 500
		})
	)
		.then((res: any) => {
			deviceTreeData.value = res
		})
		.finally(() => {
			deviceTreeLoading.value = false
		})
}

/**
 * @description 选择设备
 */
const chooseDevice = (btnName: string | undefined) => {
	if (btnName === "确定") {
		// 将勾选的key和节点存入
		const currentSelectKeys = treeRef.value.PitayaTreeRef.getCheckedKeys()
		const currentSelectNodes = treeRef.value.PitayaTreeRef.getCheckedNodes()
		deviceTableData.value = getDeepData(currentSelectNodes)
		selectCodes.value = deviceTableData.value.map((item: any) => item.composeCode)
	}
	showChooseDeviceDrawer.value = false
}

defineExpose({
	selectCodes: selectCodes
})
defineOptions({
	name: "associationDeviceTable"
})
</script>

<style lang="scss" scoped>
:deep(.el-tree-node__content) {
	height: 32px;
}
:deep(.el-tree-node__label) {
	font-size: var(--pitaya-fs-12);
}
.choose-device-btn {
	margin-left: 5px;
	color: #fff;
	font-weight: 500;
}
.inner-drawer {
	position: relative;
	height: 100%;
	.tree-search {
		margin-top: 10px;
		padding: 0 10px;
	}
	.el-tree {
		margin-top: 10px;
	}
	.pitaya-tree-container {
		padding-bottom: 53px;
	}
	.btn-list {
		display: flex;
		justify-content: flex-end;
		position: fixed;
		right: 10px;
		bottom: 0px;
		z-index: 99;
		padding: 10px 10px 10px 0;
		box-sizing: border-box;
		width: 290px;
		border-top: 1px solid #ccc;
		background-color: #fff;
	}
}
</style>