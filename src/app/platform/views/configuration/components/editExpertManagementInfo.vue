<template>
	<div class="editExpertManagement_container">
		<div class="editExpertManagement-left">
			<section class="flex_child">
				<Title :title="leftTitle" />
				<table class="info_table" cellspacing="0">
					<tr>
						<td>专家姓名</td>
						<td>{{ expertManagementBaseInfo.empName }}</td>
					</tr>
					<tr>
						<td>专家职位</td>
						<td>{{ expertManagementBaseInfo.fempTypeName || '-' }}</td>
					</tr>
					<tr>
						<td>所属部门</td>
						<td>{{ expertManagementBaseInfo.orgName }}</td>
					</tr>
					<tr>
						<td>联系电话</td>
						<td>{{ expertManagementBaseInfo.fcell }}</td>
					</tr>
				</table>
			</section>
			<ButtonList
				v-show="!(props.isView)"
				class="btn-list"
				:button="leftBtns"
				:loading="btnLoading"
				@on-btn-click="leftBtnClick"
			/>
		</div>
		<div class="editExpertManagement-right">
			<Title :title="rightTitle">
				<Tabs
					style="position: absolute; left: 140px"
					:tabs="['关联线路', '关联设备分级', '专家资质证书']"
					@on-tab-change="changeTabs"
				/>
			</Title>
			<div v-show="activeTab === 0" class="company-drawer-table">
				<associationLineTable ref="associationLineTableRef" :lines="lines" :isView="props.isView"	/>
			</div>
			<div v-show="activeTab === 1" class="company-drawer-table">
				<associationDeviceTable ref="associationDeviceTableRef" :vequipmentClasses="vequipmentClasses" :isView="props.isView" />
			</div>
			<div v-show="activeTab === 2" class="company-drawer-table">
				<qualificationCertificateTable ref="qualificationCertificateTableRef" :empNo="props.empNo" :isView="props.isView" />
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import associationLineTable from "./associationLineTable.vue"
import associationDeviceTable from "./associationDeviceTable.vue"
import qualificationCertificateTable from "./qualificationCertificateTable.vue"
import { expertManagementApi } from "@/app/platform/api/configuration/expertManagement"
import { _matchFormProp } from "@/app/platform/utils/permission"

const props = defineProps({
	majorCode: {
		type: Number,
		default: null
	},
	editId: {
		type: Number,
		default: null
	},
	empNo: {
		type: String,
		default: null
	},
	isView: {
		type: Boolean,
		default: false
	}
})

const emit = defineEmits([
	"editSuccess"
])

onMounted(() => {
	getExpertInfo()
})

const leftTitle = {
	name: ["专家信息"],
	icon: ["fas", "square-share-nodes"]
}

// 扩展栏
const rightTitle = {
	name: ["扩展信息"],
	icon: ["fas", "square-share-nodes"]
}
//扩展栏标签页切换
const activeTab = ref<number>(0)

// 底部按钮配置项
const leftBtns = [
	{ name: "保存", icon: ["fas", "floppy-disk"] }
]
const btnLoading = ref<boolean>(false)
let expertManagementBaseInfo = ref<any>({})
let lines = ref<any[]>([])
let vequipmentClasses  = ref<any[]>([])
const associationLineTableRef = ref<any>(null)
const associationDeviceTableRef = ref<any>(null)
const qualificationCertificateTableRef = ref<any>(null)

/**
 * @description 获取专家基础信息
 */
const getExpertInfo = () => {
	expertManagementApi
		.getExpertInfoApi({ id: props.editId })
		.then((res: any) => {
			expertManagementBaseInfo.value = { ...res.vsystemBaseUser, orgName: res.vsystemOrganization.orgName }
			lines.value = res.lines
			vequipmentClasses.value = res.vequipmentClasses
		})
		.catch((err) => {
			throw new Error("getExpertInfoApi():::" + err)
		})
}

/**
 * @description 点击左下角按钮
 */
const leftBtnClick = () => {
	// if(associationLineTableRef.value.selectIds.length === 0){
	// 	ElMessage.warning("请关联线路")
	// 	return false
	// }

	// if(associationDeviceTableRef.value.selectCodes.length === 0){
	// 	ElMessage.warning("请关联设备分级")
	// 	return false
	// }
	
	// if(qualificationCertificateTableRef.value.certificateTableLength === 0){
	// 	ElMessage.warning("请上传资质证书")
	// 	return false
	// }
	const lineCode = associationLineTableRef.value.selectIds.join(',')
	const equipmentLevelCode = associationDeviceTableRef.value.selectCodes.join(',')
	expertManagementApi
		.updateExpertApi({ id: props.editId, lineCode, equipmentLevelCode, empNo: props.empNo, majorCode: props.majorCode })
		.then((res: any) => {
			ElMessage.success('更新成功')
		})
		.catch((err) => {
			throw new Error("updateExpertApi():::" + err)
		})
		.finally(() => {
			emit('editSuccess')
		})
}

/**
 * @description 切换tab页
 * @param index 选择的下标
 */
const changeTabs = (index: number) => {
	activeTab.value = index
}

</script>

<style lang="scss" scoped>
.editExpertManagement_container {
	display: flex;
	align-items: center;
	height: 100%;
	.editExpertManagement-left {
		width: 310px;
		height: 100%;
		position: relative;
		padding-right: 10px;
		box-sizing: border-box;
		display: flex;
		flex-direction: column;
		justify-content: space-between;

		.btn-list {
			display: flex;
			justify-content: flex-end;
			padding: 10px 10px 0 0;
			box-sizing: border-box;
		}
	}
	.editExpertManagement-left::after {
		content: "";
		position: absolute;
		right: 0;
		top: -10px;
		width: 1px;
		height: calc(100% + 20px);
		background-color: #ccc;
	}
	.editExpertManagement-right {
		width: calc(100% - 320px);
		height: 100%;
		// margin-top: 15px;
		margin-left: 10px;
	}
	.info_table{
		width: 100%;
		margin-top: 10px;
		border: 1px solid #ccc;

		tr {
			box-sizing: border-box;
			td{
				font-size: 12px;
				height: 30px;
				color: 000;
				border-bottom: 1px solid #ccc;
				padding-left: 4px;
				box-sizing: border-box;
			}
			td:first-child{
				width: 45%;
				text-align: center;
				background: #f9f9f9;
				font-weight: bold;
				box-sizing: border-box;
				border-right: 1px solid #ccc;
				padding-left: 0;
			}
			&:last-child{
				td {
					border-bottom: 0;
				}
			}
		}
	}
}
</style>