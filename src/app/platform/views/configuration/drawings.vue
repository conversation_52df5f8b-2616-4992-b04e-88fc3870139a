<template>
	<div class="app-container-row">
		<div class="app-left-container">
			<div class="app-content-wrapper">
				<div class="app-content-group">
          <!-- 树状结构 -->
					<ModelFrame>
						<Title :title="treeTitle" />
						<div class="app-el-scrollbar-wrapper">
							<PitayaTree
								ref="majorTreeRef"
								:treeData="treeData"
								:treeProps="treeProps"
								:needCheckBox="false"
								node-key="id"
								@onTreeClick="clickTreeNode"
								:tree-loading="treeLoading"
								:currentNodeKey="defaultCheckedKeys"
								:default-expanded-keys="defaultExpandedKeys"
								:expand-on-click-node="true"
							/>
						</div>
					</ModelFrame>
				</div>
			</div>
		</div>
		<div class="right-model-frame">
      <ModelFrame>
				<Query
					class="ml10"
					:queryArrList="searchArrList"
					@getQueryData="getSearchData"
				/>
			</ModelFrame>
      <ModelFrame class="content">
			<Title
				:title="drawingsTitle"
				:button="drawingsTitleBtn"
				@onBtnClick="showDrawingsDrawer"
			/>
			<el-scrollbar>
				<PitayaTable
					ref="drawingTableRef"
					:customizeHeightNumber="0"
					:columns="tableColumns"
					:tableData="tableData"
					:total="total"
					:single-select="true"
					:need-selection="true"
					@on-current-page-change="onChangeCurrentPageChange"
					:table-loading="tableLoading"
				>

					<template #vpostionList="{ rowData }">
						<div
							class="border-bottom-text"
							@click="getRelatedPositionData(rowData)"
						>
							{{ rowData.vpostionList ? rowData.vpostionList.length : '0' }}
						</div>
					</template>

					<template #vequipmentClasses="{ rowData }">
						<div
							class="border-bottom-text"
							@click="getRelatedDeviceData(rowData)"
						>
							{{ rowData.vequipmentClasses ? rowData.vequipmentClasses.length : '0' }}
						</div>
					</template>

					<template #operations="{ rowData }">
						<el-button v-btn link @click="editLine(rowData)">
							<font-awesome-icon
								:icon="['fas', 'pen-to-square']"
								style="color: var(--pitaya-btn-background)"
							/>
							<span class="table-inner-btn">编辑</span>
						</el-button>
						<el-button v-btn link @click="downLoad(rowData)">
							<font-awesome-icon
								:icon="['fas', 'download']"
								style="color: var(--pitaya-btn-background)"
							/>
							<span class="table-inner-btn">下载</span>
						</el-button>
						<el-button v-btn link @click="deleteLine(rowData)">
							<font-awesome-icon
								:icon="['fas', 'trash-can']"
								style="color: var(--pitaya-btn-background)"
							/>
							<span class="table-inner-btn">移除</span>
						</el-button>
					</template>
					<!-- <template #footerOperateLeft>
						<ButtonList
							class="btn-list"
							:is-not-radius="true"
							:button="bottomBtnArray"
							:loading="bottomBtnLoading"
							@on-btn-click="clickBottomBtn"
						/>
					</template> -->
				</PitayaTable>
			</el-scrollbar>
		</ModelFrame>
		</div>
		<!-- 新增/编辑图纸资料 -->
		<drawingsForm ref="drawingsFormRef" :majorCode="majorCode" @submit-success="submitSuccess"></drawingsForm>

		<!-- 关联设备分级 -->
		<Drawer
			size="400"
			v-model:drawer="showRelatedDeviceDrawer"
		>
			<Title :title="relevanceDeviceTitle" />
			<PitayaTable
				:columns="relatedDeviceTableColumn"
				:customizeHeightNumber="0"
				:table-data="relatedDeviceDrawerTableData"
				:need-pagination="false"
				:need-index="true"
			/>
		</Drawer>

		<!-- 关联位置 -->
		<Drawer
			:size="relatedPositionDrawerSize"
			v-model:drawer="showRelatedPositionDrawer"
		>
			<Title :title="relevancePosition" />
			<PitayaTable
				:columns="relatedPositionTableColumn"
				:table-data="relatedPositionDrawerTableData"
				:need-pagination="false"
				:need-index="true"
			/>
		</Drawer>
	</div>
</template>

<script lang="ts" setup>
import { ElMessage } from "element-plus"
import { drawingsApi } from "@/app/platform/api/configuration/drawing"
import { usePagination } from "@/app/platform/hooks/usePagination"
import { _matchFormProp } from "@/app/platform/utils/permission"
import { CustomMessageBox } from "@/app/platform/utils/message"
import { useLine } from "@/app/platform/hooks/useLine"
import { useDevice } from "@/app/platform/hooks/useDevice"
import drawingsForm from './components/drawingsForm.vue'
import XEUtils from "xe-utils"

onMounted(() => {
	getTree() //获取树
})

const treeTitle = {
	name: ["专业类别"],
	icon: ["fas", "square-share-nodes"]
} // 树状图标题
const treeData = ref<any[]>([]) // 树状图数据
const treeProps = {
	children: "children",
	label: "name"
} // 树状图参数配置
const treeLoading = ref(false) // 控制树的loading状态
const defaultExpandedKeys = ref<any>([]) //默认展开的key
const defaultCheckedKeys = ref<any>([])
const majorTreeRef = ref<any>(null)
const treeKeyword = ref<string>("")
const useLineStore = useLine()
const useDeviceStore = useDevice()
const majorCode = ref<number>(0) //选中专业的id

// 检索项列表
const searchArrList = ref<any[]>([
	{
		name: "所属线路",
		key: "lineCode",
		placeholder: "请选择所属线路",
		enableFuzzy: false,
		type: "select",
		children: []
	},
	{
		name: "设备分级",
		key: "equipmentLevelCode",
		placeholder: "请选择设备分级",
		type: "elTreeSelect",
		children: []
	},
  {
		name: "",
		key: "title",
		placeholder: "请输入查询关键字",
		enableFuzzy: false,
		type: "input"
	},
])
const searchData = ref<any>({}) // 检索项数据
const drawingsTitle = {
	name: ['图纸资料'],
	icon: ["fas", "square-share-nodes"]
} // 表格title 
const drawingsTitleBtn = [
	{
		name: "新增图纸资料",
		roles: "configuration:drawings:btn:add",
		icon: ["fas", "square-plus"]
	}
] // 表格交互按钮
const tableColumns: TableColumnType[] = [
	{ label: "资料编号", prop: "drawCode", width: 180 },
	{ label: "资料标题", prop: "title"},
	{ label: "关联专业", prop: "majorName" },
	{ label: "关联线路", prop: "lineName" },
	{ label: "关联位置", prop: "vpostionList", needSlot: true },
	{ label: "关联设备分级", prop: "vequipmentClasses", needSlot: true },
	{
		label: "操作",
		prop: "operations",
		fixed: "right",
		needSlot: true,
		width: 230
	}
] //表格列
const tableData = ref<any[]>([]) //表格数据
const total = ref<number>(0)
// 表格配置项
const tableLoading = ref<boolean>(false)
const usePaginationStore = usePagination()
const usePaginationStoreForData = usePaginationStore.paginationData
const pageSize = ref<number>(usePaginationStoreForData.pageSize)
const currentPage = ref<number>(usePaginationStoreForData.currentPage)
const drawingTableRef = ref<any>()
const drawingsFormRef = ref<any>(null)

// 导入按钮
const bottomBtnArray = ref([
	{
		name: "导入",
		roles: "configuration:drawings:btn:enable",
		icon: ["fas", "power-off"],
		disabled: true
	}
])
const bottomBtnLoading = ref<boolean>(false) //启用停用按钮状态

// 关联设备分级
const showRelatedDeviceDrawer = ref<boolean>(false)
const relevanceDeviceTitle = {
	name: ["关联设备分级"],
	icon: ["fas", "square-share-nodes"]
}
const relatedDeviceTableColumn: TableColumnType[] = [
	{
		label: "专业名称",
		prop: "majorName"
	},
	{
		label: "分级名称",
		prop: "selfName"
	}
]
const relatedDeviceDrawerTableData = ref<any[]>([])

/**
 * 关联位置
 */
const relatedPositionDrawerSize = ref(320)
const showRelatedPositionDrawer = ref(false)
const relevancePosition = {
	name: ["关联位置"],
	icon: ["fas", "square-share-nodes"]
}
const relatedPositionTableColumn: TableColumnType[] = [
	{
		label: "关联位置",
		prop: "locComposeName",
	}
]
const relatedPositionDrawerTableData = ref<any[]>([])

/**
 * @description 获取树的数据
 */
function getTree() {
	treeLoading.value = true
	drawingsApi
		.getProfessionTreeApi({ searchValue: treeKeyword.value })
		.then((res: any) => {
			const gdTree = res[0].children.find((item: any) => item.name == '供电')
			gdTree.pid = null
			treeData.value = [gdTree]
			defaultExpandedKeys.value = [gdTree.id]
			defaultCheckedKeys.value = gdTree.children[0].id
			majorCode.value = gdTree.children[0].id
			getDrawingList()
			searchArrList.value[0].children = useLineStore.linesArr
			searchArrList.value[1].children = useDeviceStore.deviceArr
		})
		.catch((err) => {
			throw new Error("getProfessionTreeApi():::" + err)
		})
		.finally(() => {
			treeLoading.value = false
		})
}

/**
 * @description 点击树状节点
 * @param selectData 选中的节点
 * @param selectState 点击的点击选中状态
 */
const clickTreeNode = (selectData: any, selectState: any) => {
	if(selectData.children && selectData.children.length > 0){
		return false
	}
  majorCode.value = selectData.id
	getDrawingList()
}

/**
 * @description 选择搜索参数回显
 * @param queryParams 检索参数
 */
const getSearchData = (queryParams?: any) => {
	searchData.value.lineCode = queryParams.lineCode
		? queryParams.lineCode
		: ""
	searchData.value.equipmentLevelCode = queryParams.equipmentLevelCode
			? queryParams.equipmentLevelCode
			: ""
	searchData.value.title = queryParams.title
		? queryParams.title
		: ""
	getDrawingList()
}

/**
 * @description 获取图纸资料列表
 */
const getDrawingList = () => {
	tableLoading.value = true
	drawingsApi
		.getDrawListApi({ majorCode: majorCode.value, currentPage: currentPage.value - 1, pageSize: pageSize.value, ...searchData.value })
		.then((res: any) => {
			tableData.value = res.rows.map((item: { line: { name: any } }) => ({ ...item, lineName: item.line ? item.line.name : '' }))
			total.value = res.total
			drawingTableRef.value.clearSelectedTableData()
		})
		.catch((err) => {
			throw new Error("getDrawListApi():::" + err)
		})
		.finally(() => {
			tableLoading.value = false
		})
}

/**
 * @description 显示新增图纸资料弹窗
 */
const showDrawingsDrawer = () => {
	if(majorCode.value === 0){
		ElMessage.warning("请先选择专业")
		return false
	}
  drawingsFormRef.value.showForm()
}

/**
 * @description 切换分页
 * @param pageData 
 */
const onChangeCurrentPageChange = (pageData: any) => {
	pageSize.value = pageData.pageSize
	currentPage.value = pageData.currentPage
	getDrawingList()
}

/**
 * @description 获取关联线路
 * @param data 点击的行数据
 */
const getRelatedLineData = (data: any) => {
	console.log('关联线路', data)
}

/**
 * @description 获取关联设备分级
 * @param data 点击的行数据
 */
const getRelatedDeviceData = (data: any) => {
	relatedDeviceDrawerTableData.value = data.vequipmentClasses.map((item: any) => ({...item, majorName: data.majorName}))
	showRelatedDeviceDrawer.value = true
}

// 关联位置详情
const getRelatedPositionData = (data: any) => {
	let list = XEUtils.sortBy(data.vpostionList, (item: any) => item.locPositionId)
	relatedPositionDrawerTableData.value = XEUtils.toArrayTree(list, {
		parentKey: "parentId"
	})
	showRelatedPositionDrawer.value = true
}

/**
 * @description 编辑数据
 * @param row 选择的行
 */
const editLine = (row: any) => {
	const editId = row.id
	drawingsFormRef.value.showForm(editId)
}

/**
 * @description 下载文件
 */
const downLoad = (row: any) => {
	const url = `${import.meta.env.VITE_APP_BASE_DOWNLOAD_URL}${row.drawUrl}`
	console.log(url)
	// location.href = url
}

/**
 * @description 表单提交成功
 */
const submitSuccess = () => {
	getDrawingList()
}

/**
 * @description 删除数据
 * @param row 选择的行
 */
const deleteLine = (row: any) => {
	CustomMessageBox({ message: "确定要移除这行数据吗？" }, (res: boolean) => {
		if (res) {
			drawingsApi
			.deleteDrawApi({ id: row.id })
			.then((res: any) => {
				ElMessage.success('删除成功')
			})
			.catch((err) => {
				throw new Error("deleteDrawApi():::" + err)
			})
			.finally(() => {
				getDrawingList()
			})
		}
	})
}

/**
 * @description 导入
 * @param btnName 
 */
const clickBottomBtn = (btnName: any) => {
	
}
</script>

<style lang="scss" scoped>
.app-container-row {
	flex-direction: row;
	.app-left-container {
		height: 100%;
		width: 20%;
		min-width: 300px;
		flex: 0 0 20%;
		display: flex;
		flex-direction: column;
		margin-right: 10px;
	}
}
.app-content-wrapper {
	height: 0;
	flex: 1;
	.app-content-group {
		height: 100%;
		:deep(.model-frame-wrapper) {
			height: 100%;
			padding-bottom: 10px;
			overflow: hidden;
			display: flex;
			flex-direction: column;
		}
		.app-el-scrollbar-wrapper {
			height: 0;
			flex: 1;
			.table-inner-btn {
				margin-left: 5px;
				color: #204a9c;
			}
		}
	}
}

.border-bottom-text {
	position: relative;
	color: #204a9c;
	text-decoration: underline;
	cursor: pointer;
}

.btn-list {
	width: 290px;
	display: flex;
	justify-content: flex-end;
	position: fixed;
	right: 10px;
	bottom: 0px;
	z-index: 99;
	padding: 10px 10px 10px 0;
	box-sizing: border-box;
	border-top: 1px solid #ccc;
	background-color: #fff;
}

.tree_container{
	height: 90%;
	overflow: auto;
}
</style>
