<template>
	<!-- 搜索框 + 表格框  -->
	<div class="app-container">
		<ModelFrame style="padding: 20px; height: 260px">
			<div style="margin-bottom: 10px">
				<el-button @click="autoUload = true">裁剪</el-button>
				<el-button @click="autoUload = false">不裁剪</el-button>
			</div>
			<Upload
				:list-type="''"
				:maxCount="9"
				:file-list="files"
				:accept="assetFileType"
				:autoUload="autoUload"
				:data="{
					businessType: 7
				}"
				:multiple="false"
			/>
		</ModelFrame>
	</div>
</template>

<script setup lang="ts">
import { ref } from "vue"
const autoUload = ref(false)
/**
 * 上传组件相关参数
 */
const files = ref<any[]>([])
// 限制文件上传类型
const assetFileType = "image/jpg, image/jpeg, image/png, video/*"
</script>
<style lang="scss" scoped></style>
