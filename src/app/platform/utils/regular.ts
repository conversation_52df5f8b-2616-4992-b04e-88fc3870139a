// 正则表达式
export const reg = {
	// 手机号
	phone: /^1[3456789]\d{9}$/,
	// 邮箱
	email: /^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/,
	// 密码
	password: /^[a-zA-Z0-9_-]{6,16}$/,
	//  字母和数字
	letterAndNumber: /[^a-zA-Z0-9~!@#$%^&*()]+$/gi,
	// 数字
	number: /\D+$/g,
	// 小数
	smallNumber: /^([1-9][0-9]*)+(.[0-9][1,9])?$/

}

	//  字母和数字
export function letterAndNumber(value: string) {
	return value.replace(reg.letterAndNumber, "")
}

export function number(value: number) {
	return Number((value + "").replace(reg.number, ""))
}

export function smallNumber(value: number) {
	return Number((value + "").replace(reg.smallNumber, ""))
}

export function phone(params: string, value: string) {
	return reg.phone.test(value)
}

export function email(params: string, value: string) {
	return reg.email.test(value)
}

export function password(params: string, value: string) {
	return reg.password.test(value)
}

