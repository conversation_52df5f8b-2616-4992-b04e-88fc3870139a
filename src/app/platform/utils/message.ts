import { ElMessageBoxOptions,ElMessageBox } from "element-plus"

// import type { Action } from "element-plus"
/**
 * 自定义messageBox
 * @param config 配置项
 */

// 默认配置（需要其他配置项自行扩展）
const defaultConfig: ElMessageBoxOptions = {
	type: "warning",
	title: "系统消息",
	message: "内容",
	showCancelButton: true,
	autofocus: false,
	confirmButtonText:"确定",
	cancelButtonText:"取消",
}

export const CustomMessageBox = (
	config: ElMessageBoxOptions,
	callback?: (value: boolean) => void
) => {
	// 覆盖默认配置
	const conf = { ...defaultConfig, ...config }
	const { title, message } = conf
	delete conf.title
	delete conf.message
	ElMessageBox({
		...conf,
		dangerouslyUseHTMLString: true,
		message: "<div class='message-content'>" + title + "</div>" + message
	})
		.then(() => {
			// 点击确定按钮
			if (callback) callback(true)
		})
		.catch(() => {
			// 点击取消按钮
			if (callback) callback(false)
		})
}
