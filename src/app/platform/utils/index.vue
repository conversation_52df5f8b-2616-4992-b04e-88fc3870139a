<script lang="ts" setup name="UserInfoBar">
import JSEncrypt from "jsencrypt"
import { changePassword<PERSON><PERSON> } from "@/app/platform/api/login"
import { useUserStore } from "@/app/platform/store/modules/user"
import { ElMessage, FormInstance, FormRules } from "element-plus"
import { baseUserApi } from "@/app/platform/api/system/baseUser"
import router, { resetRouter } from "@/router"
import { usePermissionStore } from "@/app/platform/store/modules/permission"
import { getMsgListApi, readAll } from "@/app/platform/api/system/msg"
import { getBatchSystemParameters } from "@/app/platform/api/system/parameter"
import WebSocket from "@/app/platform/hooks/pfWebsocket.js"
import { ElLoading } from "element-plus"
import { CircleCheckFilled } from "@element-plus/icons-vue"
import EmptyMsg from "@/assets/images/empty/empty_msg.svg?component"
const userStore = useUserStore()
const { userInfo } = storeToRefs(userStore)

const messageByPosition = ref<number>(3)
const messageBySystem = ref<number>(0)

// 注销退出
const onLogout = () => {
	userStore.logout()
}

// #region 修改密码
const showChangePDrawer = ref(false)
const baseTableData = ref({
	"CAPTCHA": "false",
    "REPLICATED_LOGIN": "false",
    "UPPER_WORD": "false",
    "SPECIAL_CHAR": "false",
    "NUM": "false",
    "LENGTH": '0',
    "WORK_GROUP_CYCLE_TIME": '0',
    "LOWER_WORD": "false",
    "ENTERPRISE_SCAN": "false"
})
const changePDrawerTitle = {
	name: ["修改密码"],
	icon: ["fas", "square-share-nodes"]
}
const changeRDrawerTitle = {
	name: ["个人信息"],
	icon: ["fas", "square-share-nodes"]
}
const activeId = ref<string>("")
const baseChangePFD = reactive<anyKey>({})
const baseChangePFRef = ref<FormInstance>()
const baseChangePFR: FormRules = {
	oldPass: [{ required: true, message: "请输入旧密码", trigger: "blur" }],
	newPass: [
		{
			required: true,
			min: 5,
			max: 16,
			message: "请输入新密码",
			trigger: "blur",
			validator: (rule, value, callback) => {
				if (!value) {
					callback(new Error('请输入新密码'))
					return
				}
				
				// 获取密码规则
				const passwordRules = {
					lower: baseTableData.value&&baseTableData.value.LOWER_WORD == 'true',
					upper: baseTableData.value&&baseTableData.value.UPPER_WORD == 'true',
					special: baseTableData.value&&baseTableData.value.SPECIAL_CHAR == 'true',
					number: baseTableData.value&&baseTableData.value.NUM == 'true',
					length: parseInt(baseTableData.value&&baseTableData.value.LENGTH || '0')
				}

				// 提示
				let hint = "请输入"
				if (passwordRules.length > 0) {
					hint += `${passwordRules.length}~16位`
				}
				hint += "新密码"
				
				const requirements = []
				if (passwordRules.lower) requirements.push("小写字母")
				if (passwordRules.upper) requirements.push("大写字母")
				if (passwordRules.special) requirements.push("特殊字符")
				if (passwordRules.number) requirements.push("数字")
				
				if (requirements.length) {
					hint += `，密码需包含${requirements.join('、')}`
				}

				// 更新
				rule.message = hint

				// 校验
				if (passwordRules.lower && !/[a-z]/.test(value)) {
					callback(new Error(hint))
					return
				}
				if (passwordRules.upper && !/[A-Z]/.test(value)) {
					callback(new Error(hint))
					return
				}
				if (passwordRules.special && !/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(value)) {
					callback(new Error(hint))
					return
				}
				if (passwordRules.number && !/[0-9]/.test(value)) {
					callback(new Error(hint))
					return
				}
				if (passwordRules.length > 0 && value.length < passwordRules.length) {
					callback(new Error(hint))
					return
				}
			
				callback()
			}
		}
	],
	confirmPass: [{ required: true, message: "请确认新密码", trigger: "blur" }]
}
const changePDrawerBtnList = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "保存",
		icon: ["fas", "floppy-disk"]
	}
]
function onChangePDrawerClick(btnName: string | undefined) {
	if (btnName === "取消") return (showChangePDrawer.value = false)
	onSavePassword()
}
/** 密码加密*/
const doRSA = (pwd: string) => {
	const publicKey =
		"MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDCNWMv9Jv1APv8mQyagbHsZ+NAdstANZ0gox1ncXNec1aFSKnlG7vJNK9fwkVH4FvIGwDJH481uhk8mokEcxvtlvwCYGlZXYJaxo0s3sdiSJtyBaEmOoCU7Ni6z8oq3hxBqYfCeO+iKqEcySIJqrBmnaV8qrfDhhoSfGkqpvp+CwIDAQAB"
	const encryptor = new JSEncrypt()
	encryptor.setPublicKey(publicKey)
	const encryptPassword = encryptor.encrypt(pwd)
	if (!encryptPassword) {
		ElMessage.warning("登录密码加密错误")
		return ""
	} else {
		return encryptPassword
	}
}
function onSavePassword() {
	if (baseChangePFRef.value) {
		baseChangePFRef.value.validate((valid) => {
			if (valid) {
				const { oldPass, newPass, confirmPass } = baseChangePFD
				const oldPassPassword = doRSA(oldPass)
				const newPassPassword = doRSA(newPass)
				if (!oldPassPassword || !newPassPassword) {
					return
				}
				if (newPass !== confirmPass) {
					return ElMessage.warning("密码输入不一致")
				}

				changePasswordApi({ oldPassword: oldPassPassword, newPassword: newPassPassword })
					.then((res: anyKey) => {
						ElMessage({
							message: "密码修改成功",
							type: "success"
						})
						onLogout()
					})
					.catch((err: anyKey) => {
						throw new Error("changePasswordApi():::" + err)
					})
			} else {
				return false
			}
		})
	}
}
// #endregion

// 失败弹窗↓
const showSystemMessageDrawer = ref<boolean>(false)
const systemMessageDrawerTitle = {
	name: ["失败"],
	icon: ["fas", "square-share-nodes"]
}
const systemMessageReadStateOptions = ref<any[]>([
	{ id: 0, name: "全部消息", value: null },
	{ id: 2, name: "未读消息", value: "2" },
	{ id: 1, name: "已读消息", value: "1" }
])
const systemMessageReadState = ref<any>(null)
const systemMessageData = ref<any>([])
const pageInfo = ref<any>({
	currentPage: 1,
	pageSize: 3,
	pagerCount: 7,
	total: 10
})
const _readStateIconColor = computed(() => {
	return function (state: string) {
		return state == "1" ? "#cccccc" : "#2b4997"
	}
})

const openSystemMessageDrawer = () => {
	showSystemMessageDrawer.value = true
	pageInfo.value.currentPage = 1
	pageInfo.value.total = 10
	// messageBySystem.value = 0
	systemMessageData.value = []
	getMsgList()
}
// 每次切换读取状态时，销毁无线滚动列表
const msgListShow = ref<boolean>(true)
function handleSystemMessageReadStateChange(val: any) {
	msgListShow.value = false
	setTimeout(() => {
		pageInfo.value.currentPage = 1
		pageInfo.value.total = 10
		systemMessageReadState.value = val
		systemMessageData.value = []
		msgListShow.value = true
		getMsgList()
	}, 100)
}
// 消息模块无线滚动
const loading = ref(false)
const noMore = computed(
	() => systemMessageData.value.length >= pageInfo.value.total
)
const disabled = computed(() => loading.value || noMore.value)
function getMsgList() {
	loading.value = true
	getMsgListApi({
		currentPage: pageInfo.value.currentPage,
		pageSize: pageInfo.value.pageSize,
		status: systemMessageReadState.value
	})
		.then((res: any) => {
			if (res.rows && res.rows.length) {
				systemMessageData.value.push(...res.rows)
				pageInfo.value.currentPage++
				pageInfo.value.total = res.records
			} else {
				systemMessageData.value = []
				pageInfo.value.currentPage = 1
				pageInfo.value.total = 0
			}
		})
		.finally(() => {
			loading.value = false
		})
}
function handleSystemMessageReadAll() {
	msgListShow.value = false
	readAll()
		.then((res: any) => {
			if (res) {
				messageBySystem.value = 0
				pageInfo.value.total = 0
				systemMessageData.value = []
				pageInfo.value.currentPage = 1
				getMsgList()
			}
		})
		.finally(() => {
			msgListShow.value = true
		})
}
// 失败弹窗↑

// #region 修改权限
const showChangeRoleDrawer = ref<boolean>(false)
const roleSelectOptions = ref<any[]>([])
const changeRolerawerBtnList = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "切换",
		icon: ["fas", "fa-exchange-alt"]
	}
]

const showRoleInfoDrawer = ref(false)
const openUserRole = () => {
	showRoleInfoDrawer.value = true
}


const systemParameterListInit = () => {
	getBatchSystemParameters({})
		.then((res: any) => {
			if (res) {
				baseTableData.value = res
			}
		})
		.finally(() => {})
}
// 密码根据系统参数，权限进行校验
const changePassword = () =>{
	// 清空之前缓存的值
	Object.keys(baseChangePFD).forEach(key => {
		delete baseChangePFD[key]
	})
	showChangePDrawer.value = true
	systemParameterListInit()
}
// #region websocket
const socket = new WebSocket()
onMounted(() => {
	socket.subscribeEvent(
		{},
		`/user/${userInfo.value.userName}/queue/message`,
		(state: string, res: any) => {
			if (state == "success") {
				const data = JSON.parse(res.body)
				if (data.notReadNum) {
					messageBySystem.value = data.notReadNum
				}
			}
		}
	)
})
onUnmounted(() => {
	// 销毁websocket
	socket.destory()
})

// #endregion
defineOptions({
	name: "UserInfoBar"
})
</script>
<template>
	<div class="user-info-bar-container">
		<div class="info-list">
			<div class="info-item" @click="openUserRole">
				<font-awesome-icon :icon="['fas', 'building']" style="color: #ffffff" />
				<span class="info-name">{{ userInfo.companyName }}</span>
			</div>
			<div class="info-item" v-if="userInfo.orgName">
				<font-awesome-icon
					:icon="['fas', 'tag']"
					rotation="270"
					style="color: #ffffff"
				/>
				<span class="info-name">{{ userInfo.orgName }}</span>
			</div>
			<div class="info-item">
				<font-awesome-icon
					:icon="['fas', 'circle-user']"
					style="color: #ffffff"
				/>
				<span class="info-name">{{ userInfo.resultRoleList.roleName }}</span>
				<div
					class="badge"
					style="background-color: var(--pitaya-badge-warning)"
					v-if="userInfo.roleListAll && userInfo.roleListAll.length > 1"
				>
					{{ userInfo.roleListAll.length }}
				</div>
				<div
					v-if="false"
					class="badge"
					style="background-color: var(--pitaya-badge-warning)"
				>
					{{ messageByPosition }}
				</div>
			</div>
			<div class="info-item" @click="openSystemMessageDrawer">
				<font-awesome-icon :icon="['fas', 'message']" style="color: #ffffff" />
				<span class="info-name">失败</span>
				<div
					class="badge"
					style="background-color: var(--pitaya-badge-error)"
					v-if="messageBySystem > 0"
				>
					{{ messageBySystem }}
				</div>
			</div>
			<div class="info-item" @click="changePassword">
				<font-awesome-icon :icon="['fas', 'lock']" style="color: #ffffff" />
				<span class="info-name">修改密码</span>
			</div>
			<div class="info-item" @click="onLogout">
				<font-awesome-icon
					:icon="['fas', 'power-off']"
					style="color: #ffffff"
				/>
				<span class="info-name">注销退出</span>
			</div>
		</div>
		<Drawer
			:size="310"
			:destroyOnClose="true"
			v-model:drawer="showChangePDrawer"
			@close="baseChangePFRef!.resetFields()"
		>
			<div class="common-from-wrapper common-from-only">
				<Title :title="changePDrawerTitle" />
				<div class="common-from-group">
					<el-scrollbar>
						<el-form
							ref="baseChangePFRef"
							class="el-form-wrapper"
							:model="baseChangePFD"
							:rules="baseChangePFR"
							label-width="auto"
							label-position="top"
						>
							<el-form-item label="旧密码" prop="oldPass" required>
								<el-input
									v-model.trim="baseChangePFD.oldPass"
									type="password"
									placeholder="请输入旧密码"
									autocomplete="new-password"
									show-password
									@input="baseChangePFD.oldPass = baseChangePFD.oldPass.replace(/\s+/g, '')"
								/>
							</el-form-item>
							<el-form-item label="新密码" prop="newPass" required>
								<el-input
									v-model.trim="baseChangePFD.newPass"
									type="password"
									placeholder="请输入新密码"
									autocomplete="new-password"
									show-password
									@input="baseChangePFD.newPass = baseChangePFD.newPass.replace(/\s+/g, '')"
								/>
							</el-form-item>
							<el-form-item label="确认新密码" prop="confirmPass" required>
								<el-input 
								v-model.trim="baseChangePFD.confirmPass"
								type="password"
								maxlength="16"
								placeholder="请输入新密码"
								autocomplete="new-password"
								show-password
								@input="baseChangePFD.confirmPass = baseChangePFD.confirmPass.replace(/\s+/g, '')"/>
							</el-form-item>
						</el-form>
					</el-scrollbar>
				</div>
				<div class="btn-groups">
					<ButtonList
						:button="changePDrawerBtnList"
						@onBtnClick="onChangePDrawerClick"
					/>
				</div>
			</div>
		</Drawer>
		<!-- 失败弹窗 -->
		<Drawer :size="650" destroyOnClose v-model:drawer="showSystemMessageDrawer">
			<div class="common-from-wrapper common-from-only">
				<Title :title="systemMessageDrawerTitle" />
				<div class="common-from-group">
					<el-scrollbar>
						<div class="drawer-select-role-wrapper sys-msg-title-box">
							<el-select
								v-model="systemMessageReadState"
								filterable
								style="width: 120px"
								@change="handleSystemMessageReadStateChange"
								><template #prefix>
									<span style="padding-left: 5px">
										<font-awesome-icon :icon="['fas', 'bell']" />
									</span>
								</template>
								<el-option
									v-for="item in systemMessageReadStateOptions"
									:key="item.roloId"
									:label="item.name"
									:value="item.value"
								/>
							</el-select>
							<el-button type="primary" @click="handleSystemMessageReadAll"
								><font-awesome-icon :icon="['fas', 'list-check']" /> &nbsp;
								一键已读</el-button
							>
						</div>
						<div
							v-if="msgListShow"
							class="msg-list"
							v-infinite-scroll="getMsgList"
							:infinite-scroll-disabled="disabled"
						>
							<template v-if="systemMessageData.length !== 0">
								<div
									class="msg-item"
									:class="{ read: msg.status == '1' }"
									v-for="(msg, index) in systemMessageData"
									:key="index"
								>
									<div class="msg-title">
										<font-awesome-icon
											:icon="['fes', 'bell']"
											:style="{ color: _readStateIconColor(msg.status) }"
										/>
										<div class="text">{{ msg.title }}</div>
									</div>
									<div class="msg-content">
										<div class="text">{{ msg.message }}</div>
										<div class="date">
											{{ msg.lastModifiedDate }} &nbsp;
											{{ msg.lastModifiedBy_view }}
										</div>
									</div>
								</div>
							</template>
							<!-- <el-empty v-else description="未查询到相关信息" /> -->
							<div class="empty_table" v-else>
								<EmptyMsg class="empty_img" />
								<p>未查询到相关信息</p>
							</div>
						</div>
					</el-scrollbar>
				</div>
			</div>
		</Drawer>
		<!-- 用户信息弹窗 -->
		<Drawer
			:size="310"
			:destroyOnClose="true"
			v-model:drawer="showRoleInfoDrawer"
		>
			<div class="common-from-wrapper common-from-only">
				<Title :title="changeRDrawerTitle" />
				<div class="common-from-group">
					<el-scrollbar>
						<ul class="drawer-select-role-wrapper">
							<li class="drawer-li">
								<span class="c-3 w50">姓名：</span>
								<span class="c-6">{{ userInfo.realName }}</span>
							</li>
							<li class="drawer-li">
								<span class="c-3 w50">公司：</span>
								<span class="c-6">{{ userInfo.companyName }}</span>
							</li>
							<li class="drawer-li">
								<span class="c-3 w50">部门：</span>
								<span class="c-6">{{ userInfo.orgName }}</span>
							</li>
							<li class="drawer-li">
								<span class="c-3 w50">岗位：</span>
								<span class="c-6">{{ userInfo.station }}</span>
							</li>
							<li class="drawer-li">
								<span class="c-3 w50">工号：</span>
								<span class="c-6">{{ userInfo.employeeNumber }}</span>
							</li>
							<li class="drawer-li">
								<span class="c-3 w50">手机：</span>
								<span class="c-6">{{ userInfo.phone }}</span>
							</li>
						
							<li class="drawer-li" v-if="userInfo&&userInfo?.roleListAll.length>0">
								<span class="c-3 w50">角色：</span>
								<span class="role">
									<span class="c-6" style="text-align: left;" v-for="item in userInfo.roleListAll">{{ item.roleName }}</span>
								</span>
							</li>
							<li class="drawer-li" v-if="userInfo&&userInfo?.teamdList.length>0">
								<span class="c-3 w50">班组：</span>
								<span class="role">
									<span class="c-6" style="text-align: left;" v-for="item in userInfo.teamdList">{{ item.orgName }}</span>
								</span>
							</li>
						</ul>
					</el-scrollbar>
				</div>
			</div>
		</Drawer>
	</div>
</template>
<style lang="scss" scoped>
.empty_table {
	padding-bottom: 40px;
	text-align: center;
	.empty_img {
		width: 150px;
	}
	p {
		color: #666666;
		font-size: 12px;
		text-align: center;
	}
}
.user-info-bar-container {
	height: var(--pitaya-header-height);
	.info-list {
		display: flex;
		align-items: center;
		height: 100%;
		.info-item {
			position: relative;
			margin-left: 15px;
			font-size: 14px;
			color: #fff;
			.info-name {
				margin-left: 7px;
				cursor: pointer;
			}
			.badge {
				position: absolute;
				top: -3px;
				right: -6px;
				min-width: var(--pitaya-fs-12);
				min-height: var(--pitaya-fs-12);
				background: red;
				border-radius: 50%;
				line-height: var(--pitaya-fs-12);
				text-align: center;
				font-size: 9px;
			}
		}
	}
}

.common-from-wrapper {
	height: 100%;
	width: 0;
	flex: 1;
	display: flex;
	flex-direction: column;
	padding: 10px;
	.drawer-content-wrapper {
		position: absolute;
		left: 130px;
		&::after {
			content: "";
			width: 100%;
			height: 2px;
			background-color: var(--pitaya-btn-background);
			position: absolute;
			bottom: -10px;
			left: 50%;
			transform: translateX(-50%);
		}
	}
	&.common-from-left {
		width: 310px;
		flex: 0 0 310px;
	}
	&.common-from-only {
		width: 100%;
		padding: 0;
	}
	.common-from-group {
		height: 0;
		flex: 1;
		.el-form-wrapper {
			padding-left: 10px;
			padding-right: 10px;
		}
	}
	.btn-groups {
		display: flex;
		justify-content: flex-end;
		padding-top: 10px;
		padding-right: 10px;
		border-top: 1px solid var(--pitaya-border-color);
	}

	.footer-operate-pagination {
		display: flex;
		align-items: center;
		flex-direction: row-reverse;
		.jumper-slot-btn {
			display: flex;
			align-items: center;
			justify-content: center;
			border: 1px solid var(--pitaya-border-color);
			border-left: none;
			width: 32px;
			height: 32px;
			font-size: var(--pitaya-fs-12);
			color: #fff;
			background-color: var(--pitaya-btn-background);
			border-radius: 0;
			cursor: pointer;
			&:hover {
				background-color: var(--pitaya-hover-btn-background);
			}
			&:active {
				background-color: var(--pitaya-active-btn-background);
			}
		}
		:deep(.el-input__wrapper) {
			padding: 1px 10px;
		}
		:deep(.el-pager) {
			.number,
			.more {
				border: 1px solid var(--pitaya-border-color);
				border-left: none;
				border-radius: 0;
			}
			.is-active {
				background-color: #0a4e9a;
				color: #fff;
			}
		}
		:deep(.el-pager) {
			li {
				font-size: var(--pitaya-fs-12);
			}
		}
		:deep(.btn-prev),
		:deep(.btn-next) {
			height: 32px;
			width: 70px;
			text-align: center;
			line-height: 32px;
			border: 1px solid var(--pitaya-border-color) !important;
			background-color: var(--pitaya-btn-background);
			color: #fff;
			border-radius: 0;
			span {
				font-size: var(--pitaya-fs-12);
			}
			&:hover {
				background-color: var(--pitaya-hover-btn-background);
			}
			&:active {
				background-color: var(--pitaya-active-btn-background);
			}
		}
		:deep(.btn-next) {
			border-left: none !important;
		}
		:deep(.btn-prev) {
			border-right: none !important;
		}
		:deep(.el-pagination__sizes) {
			margin-left: 10px;
			.el-input {
				width: 100px;
				font-size: var(--pitaya-fs-12);
			}
			.el-input__wrapper {
				border-radius: 0px;
			}
		}
		:deep(.el-pagination__jump) {
			margin-left: 10px;
			.el-pagination__goto,
			.el-pagination__classifier {
				display: none;
			}
			.el-input__wrapper {
				padding: 0 10px;
				border-radius: 0px;
				box-shadow: none;
				border: 1px solid var(--pitaya-border-color);
				border-right: none;
			}
		}
	}

	.msg-list {
		margin-top: 10px;
		padding: 10px 10px 0;
		box-sizing: border-box;
		border-top: 1px solid var(--pitaya-border-color);
		.read {
			color: #999;
		}
		.msg-item {
			margin-bottom: 10px;
			padding-bottom: 10px;
			box-sizing: border-box;
			border-bottom: 1px solid var(--pitaya-border-color);
			.msg-title {
				display: flex;
				align-items: center;
				.text {
					margin-left: 10px;
					font-size: 14px;
					font-weight: 600;
				}
			}
			.msg-content {
				margin-left: 24px;
				margin-top: 10px;
				.text {
					font-size: 14px;
					line-height: 20px;
				}
				.date {
					margin-top: 5px;
					font-size: 12px;
				}
			}
		}
	}
}
</style>
<style lang="scss" scoped>
.drawer-select-role-wrapper {
	padding: 10px 10px 0 10px;
	.drawer-li {
		padding: 10px 0;
		width: 100%;
		line-height: 20px;
		display: flex;
		align-items: baseline;
		border-bottom: 1px solid #f4f4f4;
		span {
			font-size: var(--pitaya-fs-12);
		}
		.w50 {
			width: 50px;
		}
		.c-3 {
			color: #333;
		}
		.c-6 {
			color: #666;
		}
		.role{
			display: flex;
			flex-direction: column;
		}
	}
	.drawer-li:nth-child(1){
		margin-top: -10px;
	}

}
.sys-msg-title-box {
	display: flex;
	justify-content: space-between;
}

.drawer-select-role-title {
	margin-bottom: 10px;
	font-size: var(--pitaya-fs-12);
}
.drawer-select-btns {
	position: fixed;
	bottom: 10px;
	right: 10px;
}
.blur {
	filter: grayscale(90%);
}
</style>
@/app/platform/store/modules/user@/app/platform/store/modules/permission
@/app/platform/hooks/webscoket@/app/platform/hooks/webscoket.js
@/app/platform/api/login@/app/platform/api/system/baseUser@/app/platform/api/system/msg
