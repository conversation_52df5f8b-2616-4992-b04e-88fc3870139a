import {
	getDictionaryTerm,
	listTreeDictionaryByCode
} from "@/app/platform/api/system/dictionary"

/**
 * 匹配回显字典字段
 * @date 2023-08-24
 * @param {any} value 字典项数值
 * @param {any} options 获取对应字典项接口res或下拉选options
 * @returns {any} 返回字典字段
 */
export function getDicDescEcho(
	value: string | number,
	options: Ref<anyKey[]> | any[]
): string {
	value = value + ""
	options = unref(options)

	let desc = "---"

	const ret = options.find((item: any) => {
		return (item.value || item.subitemValue) == value
	})

	desc = ret ? ret.label || ret.subitemName : desc
	return desc
}

/**
 * 获取对应字典项
 * @date 2024-12-23
 * @param {any} code 字典编码
 * @param {any} type 获取对应字典项缓存
 */

//示例
// getDictionaryInfo("MENU_LEVEL6", "dataDictionary").then((res) => {
// 	console.log("获取到了", res)
// })
export async function getDictionaryInfo(code: string, type: string) {
	if (localStorage.getItem(type)!='null' && localStorage.getItem(type)!='undefined') {
		let list = JSON.parse(localStorage.getItem(type)!)

		if (Object.keys(list).includes(code)) {
			console.log("code名称匹配返回字段", code, list[code])
			//code名称匹配返回字段
			return list[code].dataDictionarySubitems
		} else {
			//没有匹配到code名称重新调接口查找
			let data = await getDictionaryTermData(code)
			console.log("没有匹配到code名称重新调接口查找datan", data)
			return data
		}
	} else {
		let data = await getDictionaryTermData(code)
		console.log("字典缓存不存在", data)
		return data
	}
}

async function getDictionaryTermData(code: any) {
	// Simulate API call with a delay
	return getDictionaryTerm({
		dataDictionaryCode: code
	})
		.then((res: any) => {
			if (res && res.length) {
				const children: any[] = []
				res.forEach((item: any) => {
					children.push({
						label: item.subitemName,
						value: item.subitemValue
					})
				})
				console.log("没有匹配到code名称重新调接口查找children")
				return children
			}
		})
		.catch((error) => {
			throw new Error("changePasswordApi():::" + error)
		})
}

/**
 * 获取对应字典树
 * @date 2024-12-23
 * @param {any} code 字典编码
 * @param {any} type 获取对应字典项缓存
 */
//示例
// getDictionaryTree("JOBCATEGORY3", "treeDictionary").then((res) => {
// 	console.log("获取到了树", res)
// })
export async function getDictionaryTree(code: string, type: string) {
	console.log('localStorage.getItem(type)tree',localStorage.getItem(type))
	if (localStorage.getItem(type)!='null' && localStorage.getItem(type)!='undefined') {
		let list = JSON.parse(localStorage.getItem(type)!)
		if (Object.keys(list).includes(code)) {
			console.log("code名称匹配返回字段", code, list[code])
			//code名称匹配返回字段
			return list[code].treeDictionarySubitems
		} else {
			//没有匹配到code名称重新调接口查找
			let data = await listTreeDictionaryByCodeData(code)
			console.log("没有匹配到code名称重新调接口查找", data)
			return data
		}
	} else {
		let data = await listTreeDictionaryByCodeData(code)
		console.log("字典缓存不存在", data)
		return data
	}
}
async function listTreeDictionaryByCodeData(code: any) {
	return listTreeDictionaryByCode({
		treeDictionaryCode: code,
		containDisable: true
	})
		.then((res: any) => {
			if (res && res.length) {
				console.log("没有匹配到code名称重新调接口查找children", res)
				return res
			}
		})
		.catch((error) => {
			throw new Error("changePasswordApi():::" + error)
		})
}
