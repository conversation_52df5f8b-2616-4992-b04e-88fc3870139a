import axios, { type AxiosInstance, type AxiosRequestConfig } from "axios"
import { useUserStoreHook } from "@/app/platform/store/modules/user"
import { ElMessage } from "element-plus"
import { get, merge } from "lodash-es"
import { getToken } from "./cache/cookies"
import { debounce } from "lodash-es"
import qs from "qs"

/** 创建请求实例 */
function createService() {
	// 创建一个 axios 实例命名为 service
	const service = axios.create()
	// 请求拦截
	service.interceptors.request.use(
		(config) => config,
		// 发送失败
		(error) => Promise.reject(error)
	)
	// 防抖函数，确保在指定时间内只执行一次
	const debounceMessage = debounce(
		(message) => {
			ElMessage.error({
				message: message,
				grouping: false
			})
		},
		5000,
		{
			leading: true,
			trailing: false
		}
	) // 1分钟
	// 响应拦截（可根据具体业务作出相应的调整）
	service.interceptors.response.use(
		(response) => {
			// apiData 是 api 返回的数据
			const apiData = response.data
			// 二进制数据则直接返回
			const responseType = response.request?.responseType
			if (responseType === "blob" || responseType === "arraybuffer")
				return apiData
			// 这个 code 是和后端约定的业务 code
			const code = apiData.code || apiData.status
			const requestUrl = response.request.responseURL
			const isLoginUrl = /\/login/.test(requestUrl)

			if (requestUrl.indexOf("material/materialProcureInfo/excelError") > -1) {
				return apiData
			}
			if (isLoginUrl) return apiData
			// 如果没有 code, 代表这不是项目后端开发的 api
			if (code === undefined) {
				//ElMessage.error("请联系管理员")
				// return Promise.reject(new Error("非本系统的接口"))
				return Promise.reject(new Error("非本系统的接口"))
			}
			switch (code) {
				case "200":
				case 200:
					// 业务正常
					return apiData.data
				case "999":
					// 自定义异常处理
					return Promise.reject(apiData.message || apiData.msg || "Error")
				default:
					// 不是正确的 code
					const msg = apiData.message || apiData.msg || "Error"
					if (apiData.alertType) {
						switch (apiData.alertType) {
							case "success":
								ElMessage.success({
									message: msg,
									grouping: false
								})
								break
							case "warning":
								ElMessage.warning({
									message: msg,
									grouping: false
								})
								break
							case "info":
								ElMessage.info({
									message: msg,
									grouping: false
								})
								break
							case "error":
								ElMessage.error({
									message: msg,
									grouping: false
								})
								break
						}
					} else {
						ElMessage.error({
							message: msg,
							grouping: false
						})
					}
					return Promise.reject(new Error(msg))
			}
		},
		(error) => {
			if (axios.isCancel(error)) {
				// console.log(`请求到已被取消`, error.message)
			} else {
				// status 是 HTTP 状态码
				const status = get(error, "response.status")
				const resHeaders = error.response
					? error.response.headers
					: error.response
				if (
					error.code === "ECONNABORTED" ||
					error.message === "Network Error" ||
					error.message.includes("timeout")
				) {
					ElMessage.error({
						message: "请求超时",
						grouping: false
					})
					return Promise.reject(error)
				}
				switch (status) {
					case 400:
						error.message = "请求错误"
						break
					case 401:
						// 用户无权限时（会重定向到登录页）
						useUserStoreHook().resetToken()
						break
					case 403:
						const data = error.response ? error.response.data : null
						// 检查响应体中是否有msg字段，并据此设置error.message
						if (data && typeof data === "object" && data.msg) {
							if (data.msg === "Bad credentials") {
								error.message = "账号或密码错误"
							} else {
								if (error.message.includes("密码错误")) {
									error.message = "账号密码错误或登录已过期"
									useUserStoreHook().resetToken()
								} else {
									console.log("===token失效,object", error, "data", data)
									error.message = "登录已过期，请重新登录"
									useUserStoreHook().resetToken()
									window.location.href = "/login"
								}
							}
						} else {
							console.log("===token失效,--else", error)
							error.message = "登录已过期，请重新登录"
							// Token失效
							useUserStoreHook().resetToken()
							window.location.href = "/login"
						}
						break
					case 404:
						error.message = "请求地址出错"
						break
					case 408:
						error.message = "请求超时"
						break
					case 466:
						error.message = "验证码错误，请核对后重新输入"
						break
					case 500:
						error.message = "服务器内部错误"
						break
					case 501:
						error.message = "服务未实现"
						break
					case 502:
						error.message = "网络通讯异常!"
						break
					case 503:
						error.message = "服务不可用"
						break
					case 504:
						error.message = "网关超时"
						break
					case 505:
						error.message = "HTTP 版本不受支持"
						break
					case 990:
						error.message = "该账号所在公司已停用"
						break
					case 991:
						error.message = "账号或密码错误"
						break
					case 992:
						error.message = "该账号已停用"
						break
					case 998:
						const dataM = error.response ? error.response.data : null
						if (dataM && typeof dataM === "object" && dataM.msg) {
							error.message = dataM.msg || error.message
							useUserStoreHook().resetToken()
						} else {
							error.message = error.msg || error.message
							useUserStoreHook().resetToken()
						}
						break
					default:
						break
				}
				if (
					status === 403 &&
					error.message !== "账号或密码错误" &&
					error.message !== "账号密码错误或登录已过期"
				) {
					debounceMessage(error.message)
				} else if (error.response?.data?.alertType) {
					const alertType = error.response.data.alertType
					switch (alertType) {
						case "success":
							ElMessage.success({
								message: error.message,
								grouping: false
							})
							break
						case "warning":
							ElMessage.warning({
								message: error.message,
								grouping: false
							})
							break
						case "info":
							ElMessage.info({
								message: error.message,
								grouping: false
							})
							break
						case "error":
							ElMessage.error({
								message: error.message,
								grouping: false
							})
							break
					}
				} else {
					ElMessage.error({
						message: error.message,
						grouping: false
					})
				}
				return Promise.reject(error)
			}
		}
	)
	return service
}

/** 创建请求方法 */
function createRequest(service: AxiosInstance) {
	return function <T>(config: AxiosRequestConfig): Promise<T> {
		const token = getToken()
		const defaultConfig = {
			headers: {
				// 携带 Token
				Authorization: token ? `Bearer ${token}` : undefined,
				"Content-Type": "application/json"
			},
			timeout: 60000,
			baseURL: import.meta.env.VITE_BASE_API,
			data: {},
			paramsSerializer: function (params: any) {
				return qs.stringify(params)
			}
		}
		// 将默认配置 defaultConfig 和传入的自定义配置 config 进行合并成为 mergeConfig
		const mergeConfig = merge(defaultConfig, config)
		return service(mergeConfig)
	}
}

/** 用于网络请求的实例 */
const service = createService()
/** 用于网络请求的方法 */
export const request = createRequest(service)
