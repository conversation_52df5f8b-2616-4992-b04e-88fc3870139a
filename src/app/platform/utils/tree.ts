import { cloneDeep } from "lodash-es"

/** 获取选中数据包括父节点
 * treeRefVal: tree的ref属性值
 * key1: 当前节点标识（子集的父级节点标识）
 * key2: 父级节点标识
 * cKey: 子集的key
 * callback: 回调函数 包含三参数 第一个参数：返回全选与半选的树型结构，第二个参数：返回全选的id，第三个参数：返回半选的id
 */
export const getNodeList = (
	treeRefVal: any,
	callback: Function,
	key1 = "id",
	key2 = "parentId",
	cKey = "children"
) => {
	let selectList: any[] = []
	let checkedKeys: any[] = []
	let bCheckedKeys: any[] = []
	setTimeout(() => {
		const checkedNodesInit = treeRefVal.getCheckedNodes(false, true)
		const checkedNodes = JSON.parse(JSON.stringify(checkedNodesInit))
		if (checkedNodes && checkedNodes.length) {
			checkedNodes[0][cKey] = []
			const arr = [checkedNodes[0]]
			for (let i = 1; i < checkedNodes.length; i++) {
				const item = checkedNodes[i]
				if (arr[0][key1] === item[key2]) {
					item[cKey] = []
					arr[0][cKey].push(item)
				} else {
					addChildren(arr[0][cKey], item, key1, key2, cKey)
				}
			}
			selectList = arr
		} else {
			selectList = []
		}
		checkedKeys = treeRefVal.getCheckedKeys()
		bCheckedKeys = treeRefVal.getHalfCheckedKeys()
		callback(selectList, checkedKeys, bCheckedKeys)
	}, 200)
}

const addChildren = (
	arr: any[],
	item: anyKey,
	key1: string,
	key2: string,
	cKey: string
) => {
	for (let j = 0; j < arr.length; j++) {
		if (!arr[j][cKey] || !arr[j][cKey].length) {
			arr[j][cKey] = []
		}
		if (arr[j][key1] && arr[j][key1] === item[key2]) {
			item[cKey] = []
			arr[j][cKey].push(item)
		} else {
			addChildren(arr[j][cKey], item, key1, key2, cKey)
		}
	}
}

/** 移除树形结构某一项，如果无同级则移除则连带上一级移除
 * treeData: tree型数组
 * val: 移除标识的值
 * key: 当前需要移除节点标识key
 * pkey: 父级节点标识key
 * cKey: 子集的key
 */

export const removeTreeItem = (
	treeData: any[],
	val: string | number,
	key = "id",
	pkey = "parentId",
	cKey = "children"
) => {
	const data = JSON.parse(JSON.stringify(treeData))
	if (!data || !data.length) {
		return []
	}
	let initVal = val
	while (initVal !== checkoutOnlyFuc(data, initVal, key, pkey, cKey)) {
		initVal = checkoutOnlyFuc(data, initVal, key, pkey, cKey)
	}

	if (initVal == "0" || initVal === null) {
		return []
	}
	return loopFuc(data, initVal, key, pkey, cKey)
}

/** 移除树形结构某一项
 * treeData: tree型数组
 * val: 移除标识的值
 * key: 当前需要移除节点标识key
 * pkey: 父级节点标识key
 * cKey: 子集的key
 */
export const removeTreeNode = (
	treeData: any[],
	val: string | number,
	key = "id",
	pkey = "parentId",
	cKey = "children"
) => {
	const data = JSON.parse(JSON.stringify(treeData))
	if (!data || !data.length) {
		return []
	}
	const initVal = val

	if (initVal == "0" || initVal === null) {
		return []
	}
	return loopFuc(data, initVal, key, pkey, cKey)
}

const loopFuc = (
	data: any[],
	val: string | number,
	key: string,
	pkey: string,
	cKey: string
) => {
	const retList: any[] = []

	for (let i = 0; i < data.length; i++) {
		const item = data[i]
		if (item[key] === val) {
			data.splice(i--, 1)
		} else {
			if (item[cKey] && item[cKey].length) {
				item[cKey] = loopFuc(item[cKey], val, key, pkey, cKey)
			}
			retList.push(item)
		}
	}
	return retList
}

const checkoutOnlyFuc = (
	data: any[],
	val: string | number,
	key: string,
	pkey: string,
	cKey: string
) => {
	let retId: string | number = val

	for (let i = 0; i < data.length; i++) {
		const item = data[i]

		if (item[key] === val) {
			if (data.length < 2) {
				retId = item[pkey]
			}
		} else {
			if (item[cKey] && item[cKey].length) {
				const cRetId = checkoutOnlyFuc(item[cKey], val, key, pkey, cKey)
				retId = cRetId ? cRetId : retId
			}
		}
	}
	return retId
}

/**
 * @description 获取树型数据中某个key组成的一维数组
 * @param treeData: tree型数组
 * @param key: 当前需要移除节点标识key
 * @param cKey: 子集的key
 */
export const getIdList = (treeData: any[], key = "id", cKey = "children") => {
	let retId: any[] = []
	if (!treeData || !treeData.length) {
		return retId
	}
	for (let i = 0; i < treeData.length; i++) {
		const element = treeData[i]
		if (element[key] || element[key] === 0) {
			retId.push(element[key])
		}
		if (element[cKey] && element[cKey].length) {
			retId = retId.concat(getIdList(element[cKey], key, cKey))
		}
	}
	return retId
}

/**
 * @description 对比两个一维数组，返回交集
 * @param ad1: 一维数组
 * @param ad2: 一维数组
 */
export const secIdList = (ad1: any[], ad2: any[]) => {
	const retId: any[] = []
	if (!ad1 || !ad1.length || !ad2 || !ad2.length) {
		return retId
	}
	for (let i = 0; i < ad1.length; i++) {
		const element = ad1[i]
		if (
			ad2.find((item) => {
				return element === item
			})
		) {
			retId.push(element)
		}
	}
	return retId
}

/**
 * 一维数组去重
 */
export const onlyItemArr = (arr: any[]) => {
	if (!arr || !arr.length) {
		return []
	}
	const retArr: any[] = []
	retArr.push(arr[0])
	arr.forEach((item) => {
		if (
			!retArr.find((ri) => {
				return item === ri
			})
		) {
			retArr.push(item)
		}
	})
	return retArr
}

/**
 * 查找所有最底层节点,返回key
 */
export const findAllEndNode = (
	treeNode: any[],
	key = "id",
	childKey = "children"
) => {
	const arrTemp: any[] = treeToFlat2(treeNode)
	const arrRes: any[] = []
	arrTemp.forEach((item: any) => {
		if (item[childKey] === null || item[childKey] === undefined) {
			arrRes.push(item[key])
		}
	})
	return arrRes
}
// 查找父节点，返回数组对象
export const findAllEndNodeV2 = (treeNode: any[], childKey = "children") => {
	const arrTemp: any[] = treeToFlat2(treeNode)
	const arrRes: any[] = []
	arrTemp.forEach((item: any) => {
		if (item[childKey] === null) {
			arrRes.push(item)
		}
	})
	return arrRes
}

function treeToFlat2(data: any) {
	const result: any = []
	data.forEach((item: any) => {
		const obj = {
			...item
		}
		result.push(obj)
		if (item.children?.length) {
			result.push(...treeToFlat2(item.children))
		}
	})
	return result
}

/**
 * @description 树勾选（父子不关联下，子关联父，父不关联子）
 * @param treeNode 树节点ref
 * @param allData 所有的数据
 * @param treeData 当前树节点的数据
 * @param checked 是否选中
 * @param idKey id对应的key
 * @param parKey 父节点id对应的key
 * @param ckey 子节点对应的key
 */
export const selectTreeNodeByNoRelevance = (
	treeNode: any,
	allData: any,
	treeData: any,
	checked: boolean,
	idKey = "id",
	parKey = "pid",
	cKey = "children"
) => {
	if (checked === false) {
		//如果当前节点有子集
		if (treeData[cKey]) {
			//循环子集将他们的选中取消
			treeData[cKey].map((item: anyKey) => {
				treeNode.setChecked(item[idKey], false)
			})
		}
	} else {
		//否则(为选中状态)
		//判断父节点id是否为空
		if (treeData[parKey] !== null) {
			//如果不为空则将其选中
			const allParentNodes = findAllParentNode(allData, treeData[idKey])
			allParentNodes.forEach((item: any) => {
				treeNode.setChecked(item, true)
			})
		}
	}
}
/**
 * 查找所有直系节点V2
 * @param data 树结构数据
 * @param key 要查找的节点id
 * @returns 所有直系节点，不包含自定义的根节点
 */
export const findAllParentNode = (data: any, key: string) => {
	const arr: any[] = []
	const fn = (data: any, key: string) => {
		const p = getParentNode(data, key)
		if (p && p.id) {
			arr.push(p)
			fn(data, p.id)
		}
	}
	fn(data, key)
	return arr
}
/**
 * 查找所有直系节点V2
 * @param data 树结构数据
 * @param key 要查找的节点id
 * @returns 所有直系节点，包含自定义的根节点
 */
export const findAllParentNodeV2 = (data: any, key: string) => {
	const arr: any[] = []
	const fn = (data: any, key: string) => {
		const p = getParentNode(data, key)
		if (p && p.id) {
			arr.push(p)
			fn(data, p.id)
		}
	}
	fn(data, key)
	arr.push(data[0])
	return arr
}
/**
 * @description 根据id查找父节点
 * @param data 树结构数据
 * @param key 子节点id
 * @param ckey 节点id对应的key
 * @returns 该子节点的父
 */
export const getParentNode = (data: any, key: string, ckey = "id") => {
	for (let i = 0; i < data.length; i++) {
		if (data[i].children?.length) {
			if (data[i].children.some((item: any) => item[ckey] == key)) {
				return data[i]
			} else {
				const temp: any = getParentNode(data[i].children, key, ckey)
				if (temp) return temp
			}
		}
	}
	return null
}

/**
 * 向上累加
 * @param data 树数据
 * @param key 关键字段
 * @returns 处理后的树数据
 */
export function upwardAccumulationByOneKey(data: any, key: string): any {
	for (let i = 0; i < data.length; i++) {
		if (data[i].children) {
			upwardAccumulationByOneKey(data[i].children, key)
			data[i].userNUm = data[i].userNUm + totalChildren(data[i].children, key)
		}
	}
	return data
}

/**
 * @param data
 * @param key
 * @returns 子节点【key】对应总数
 */
function totalChildren(data: any, key: string) {
	let total = 0
	for (let i = 0; i < data.length; i++) {
		total += data[i][key]
	}
	return total
}

/**
 * 通过某key获取值的一位数组
 * @param srouce 源数据(根节点不是‘全部XX’)
 * @param key 关键字
 * @param childKey 下一层级关键字
 * @param takeoutRoot 是否需要移除根节点 默认false不需要
 * @param returnRoot 返回是否要拼接根节点 默认false不需要
 * @returns result 结果集
 */
export function getValueArrarByKey(
	source: any,
	key: string,
	childKey: string,
	takeoutRoot = false,
	returnRoot = false
) {
	const result: any[] | undefined = []
	const realSource = takeoutRoot ? source[childKey] : source
	recursionChildrenInValueByKey(realSource, key, childKey, result)
	return returnRoot ? (source[childKey] = result) : result
}
/**
 * getValueArrarByKey 方法中使用的递归
 * @param srouce 源数据
 * @param key 关键字
 * @param childKey 下一层级关键字
 * @param result 结果集
 */
function recursionChildrenInValueByKey(
	source: any[],
	key: string,
	childKey: string,
	result: any[] = []
) {
	source.forEach((item) => {
		result.push(item[key])
		if (item[childKey]) {
			recursionChildrenInValueByKey(item[childKey], key, childKey, result)
		}
	})
}
