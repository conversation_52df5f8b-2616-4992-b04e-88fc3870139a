import { useUserStoreHook } from "@/app/platform/store/modules/user"
import { asyncRoutes } from "@/router"
import { cloneDeep, pull } from "lodash-es"
import { Router } from "vue-router"

/** 全局权限判断函数，和权限指令 v-permission 功能类似 */
// export const checkPermission = (permissionRoles: string[]): boolean => {
// 	if (Array.isArray(permissionRoles) && permissionRoles.length > 0) {
// 		const { roles } = useUserStoreHook()
// 		return roles.some((role: any) => permissionRoles.includes(role))
// 	} else {
// 		return false
// 	}
// }

// 判断当前按钮是否显示或可用
export function checkPermission(permissionRoles: string) {
	const { userInfo } = useUserStoreHook()
	const list = userInfo.roleList[0].privillegesList
	return list.some((role: any) => {
		if (permissionRoles === role.privillegPurview) {
			// invisible   直接不可见;
			//disable   可见但是不可用;
			//visible   可见又可用;
			if (role.privilegeType == "disable") {
				return true
			} else if (role.privilegeType == "visible") {
				return false
			}
		}
	})
}
//判断按钮否展示
export function isCheckPermission(permissionRoles: string) {
	const { userInfo } = useUserStoreHook()
	const list = userInfo.roleList[0].privillegesList

	return list.some((role: any) => {
		if (permissionRoles == role.privillegPurview) {
			// invisible   直接不可见;
			// disable   可见但是不可用;
			// visible   可见又可用;
			if (role.privilegeType === "invisible") {
				return false
			} else {
				return true
			}
		}
	})
}
//根据当前按钮权限返回按钮状态
export function checkPermissionType(permissionRoles: string) {
	const { userInfo } = useUserStoreHook()
	let type = ""
	if (userInfo.roleList.length > 0) {
		const list = userInfo.roleList.flatMap((role: any) => role.privillegesList)
		list.some((role: any) => {
			if (permissionRoles === role.privillegPurview) {
				// invisible   直接不可见;
				//disable   可见但是不可用;
				//visible   可见又可用;
				if (type == "visible") {
					type = "visible"
				} else if (type === "disable") {
					if (role.privilegeType == "visible") {
						type = "visible"
					}
				} else {
					type = role.privilegeType
				}
			}
		})
	}
	return type
}

/**
 * 无参数时校验当前页面是否存在按钮权限，存在则返回权限字段集合，传入按钮名称时匹配当前按钮权限并返回
 * <AUTHOR>
 * @date 2023-08-16
 */
export const matchPermissionBtnListV2 = (
	btnName?: string
): string[] | [] | false => {
	let btnPermission
	const {
		roles,
		userInfo: { defaultRoleId }
	} = useUserStoreHook()

	const router = useRouter()
	const matchStr =
		"btn-role:" + router.currentRoute.value.path.split("/").at(-1)

	let roleList = cloneDeep(roles)
	if (roleList && roleList.length > 0) {
		// 判断当前页面是否包含按钮权限
		const hasBtnPermissionStr = roleList.some((role: string) =>
			role.includes(matchStr)
		)
		if (hasBtnPermissionStr) {
			// 超级管理员角色 全部按钮可见可用
			// 若为超级管理员角色，去除所有unusable权限
			if (defaultRoleId == "ROLE_ADMIN") {
				roleList = roleList.filter((role: string) => !role.includes("unusable"))
			}
			// 若为指令调用，则匹配 btnName 对应权限并返回
			if (btnName) {
				btnPermission = roleList.filter((role: string) =>
					role.includes(btnName)
				)
				// 若同一按钮存在多个权限，去除unusable
				if (btnPermission && btnPermission.length >= 2) {
					btnPermission = btnPermission.filter(
						(role: string) => !role.includes("unusable")
					)
				}
			} else {
				btnPermission = roleList.filter((role: string) =>
					role.includes(matchStr)
				)
				// 校验是否存在同一按钮多个权限，存在则去除unusable
				btnPermission = filterRepeatItem(btnPermission)
			}
		} else {
			btnPermission = []
		}

		return btnPermission
	} else {
		return false
	}
}

/**
 * 获取当前页面匹配到的 按钮权限
 * <AUTHOR>
 * @param btnName 权限标识
 * @returns 对应标识的权限
 */
export const matchPermissionBtnList = (
	btnName: string,
	router?: Router
): string[] | [] => {
	let btnPermission
	const {
		roles,
		userInfo: { defaultRoleId }
	} = useUserStoreHook()

	// 超管角色无限制
	if (defaultRoleId == "ROLE_ADMIN") return (btnPermission = [])

	const roleList = cloneDeep(roles)
	// const router = useRouter()
	if (!router) router = useRouter()

	// 如下多种情况
	// 1.无角色授权人员可用可见
	if (!roleList || roleList.length == 0) return (btnPermission = [])

	// 2.有角色授权人员可用可见 页面按钮均可见可用
	// 2-1.判断是否有当前页面授权
	const matchMenuStr =
		"menu-role:" + router.currentRoute.value.path.split("/").at(-1)
	// 2-1.没有页面授权
	if (!roleList.some((role: string) => role.includes(matchMenuStr)))
		return (btnPermission = [])

	// 2-2.没授权人员且设置按钮权限 动态展示
	const matchBtnStr =
		"btn-role:" +
		router.currentRoute.value.path.split("/").at(-1) +
		"-" +
		btnName
	btnPermission = roleList.filter((role: string) => role.includes(matchBtnStr))

	return btnPermission
}

/**
 * 辅助函数：查询同一按钮是否存在多个权限，是则返回去除unusable的权限列表
 * <AUTHOR>
 * @date 2023-08-17
 */
function filterRepeatItem(array: any[]) {
	const temp: any[] = []
	const res: any[] = []

	const btnNameList = array.map((item) => item.split(":")[1].split("-")[1])
	btnNameList.forEach((item) => {
		if (temp.includes(item)) {
			res.push(item)
		} else {
			temp.push(item)
		}
	})

	const flatArr = [...new Set(res)]

	flatArr.forEach((item) => {
		array.forEach((role) => {
			if (role.includes(item) && role.includes("unusable")) {
				pull(array, role)
			}
		})
	})

	return array
}

// 权限配置对象：变更与权限匹配相关的新增权限表单字段修改此处即可
const permissionRoleOptions: anyKey = {
	menuParent: {
		prop: "parent"
	},
	menuLevel: {
		prop: "menuLevel",
		dic: {
			"1": "top-menu-role",
			"2": "first-menu-role",
			"3": "second-menu-role",
			"4": "btn-role"
		}
	},
	menuName: {
		prop: "menuName"
	},
	name: {
		prop: "name"
	},
	component: {
		prop: "component"
	},
	path: {
		prop: "path"
	},
	menuBtnText: {
		prop: "menuBtnText"
	},
	menuDisplayType: {
		prop: "menuDisplayType",
		dic: {
			"1": "usable",
			"2": "unusable"
		}
	}
}

// 获取新增权限字段表单prop
export function _matchFormProp(key: string | Ref<string>, prop?: string) {
	const curProp = prop || "prop"
	key = unref(key)
	return permissionRoleOptions[key]
		? permissionRoleOptions[key][curProp]
		: undefined
}

/**
 * 辅助函数：平铺传入的路由为一维数组
 * @date 2023-08-24
 * @param {any} routes 需要平铺的路由
 * @param {any} res 存储结果的数组
 * @returns {any} 处理后的数组
 */
function getAllsyncRoutes(routes: anyKey[], res: any[]) {
	routes.forEach((route) => {
		res.push(route)
		if (route.children) {
			getAllsyncRoutes(route.children, res)
		}
	})
}

/**
 * 新增权限时生成权限字段
 * @date 2023-08-24
 * @param {any} formData 新增权限表单对象
 * @returns {any} 权限字段
 */
export function generatePermissionKey(formData: anyKey | Ref<anyKey>) {
	formData = unref(formData)
	const allAsyncRoutes: anyKey[] = []
	getAllsyncRoutes(asyncRoutes, allAsyncRoutes)

	const menuLevel = formData[_matchFormProp("menuLevel")]
	const menuDisplayType = formData[_matchFormProp("menuDisplayType")]
	const parent = formData[_matchFormProp("menuParent")]
	const menuBtnText = formData[_matchFormProp("menuBtnText")]
	const menuName = formData[_matchFormProp("menuName")]

	const roleStr = _matchFormProp("menuLevel", "dic")[menuLevel]
	let routeStr = "",
		permissionStr = ""

	// 按钮权限字段处理
	if (menuLevel === "4") {
		routeStr =
			allAsyncRoutes
				.find((route) => route.meta?.title === parent)
				?.path.split("/")
				.at(-1) || ""
		permissionStr = roleStr + ":" + `${routeStr}-${menuBtnText.split("-")[1]}`
		// -${_matchFormProp("menuDisplayType", "dic")[menuDisplayType]}
	} else {
		// 路由权限字段处理
		routeStr =
			allAsyncRoutes
				.find((route) => route.meta.title === menuName)
				?.path.split("/")
				.at(-1) || ""

		permissionStr = roleStr + ":" + routeStr
	}
	return permissionStr
}
