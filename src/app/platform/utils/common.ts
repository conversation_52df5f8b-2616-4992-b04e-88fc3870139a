import dayjs from "dayjs"
import { removeConfigLayout } from "@/app/platform/utils/cache/local-storage"
import { MaybeRef } from "vue"

// 字典项数据源接口类型
interface DictionaryList {
	label: string
	code: string
	children: any[]
}

/** 格式化时间 */
export const formatDateTime = (time: string | number | Date) => {
	return time ? dayjs(new Date(time)).format("YYYY-MM-DD HH:mm:ss") : "N/A"
}

/** 用 JS 获取全局 css 变量 */
export const getCssVariableValue = (cssVariableName: string) => {
	let cssVariableValue = ""
	try {
		// 没有拿到值时，会返回空串
		cssVariableValue = getComputedStyle(
			document.documentElement
		).getPropertyValue(cssVariableName)
	} catch (error) {
		// console.error(error)
	}
	return cssVariableValue
}

/** 用 JS 设置全局 CSS 变量 */
export const setCssVariableValue = (
	cssVariableName: string,
	cssVariableValue: string
) => {
	try {
		document.documentElement.style.setProperty(
			cssVariableName,
			cssVariableValue
		)
	} catch (error) {
		// console.error(error)
	}
}

/** 重置项目配置 */
export const resetConfigLayout = () => {
	removeConfigLayout()
	location.reload()
}

/**
 * 获取tree当前节点标题已经往上查找父级标题
 * obj：当前节点
 * key：所需标题的key
 */

export const getTreeTitle = (obj: any, key: string) => {
	let ret: string[] = []
	if (
		obj.data &&
		Object.prototype.toString.call(obj.data) === "[object Object]"
	) {
		ret.push(obj.data[key])
	}
	if (
		obj.parent &&
		obj.parent.data &&
		Object.prototype.toString.call(obj.parent.data) === "[object Object]"
	) {
		ret = ret.concat(getTreeTitle(obj.parent, key).reverse())
	}
	return ret.reverse()
}

/**
 * 给tree数据重命名唯一key
 * tree：树结构数据
 * key1：需要重命名的key
 * key2: 重命名后的key
 * cKey: 子集的key
 */

export const checkTree = (
	tree: any[],
	key1: string,
	key2: string,
	cKey: string
) => {
	if (tree[0] && tree[0][key2]) {
		return tree
	}
	const retTree = tree.map((item) => {
		item[key2] = item[key1]
		if (item[cKey] && item[cKey].length) {
			item[cKey] = checkTree(item[cKey], key1, key2, cKey)
		}
		delete item[key1]
		return item
	})
	return retTree
}

/**
 * 给tree数据某个key得值复制到另一个的key上
 * tree：树结构数据
 * key1：提供值的key
 * key2: 接收的key
 * cKey: 子集的key
 */

export const ctrlVTree = (
	tree: any[],
	key1: string,
	key2: string,
	cKey: string = "children"
) => {
	const retTree = tree.map((item) => {
		item[key2] = JSON.parse(JSON.stringify(item[key1]))
		if (item[cKey] && item[cKey].length) {
			item[cKey] = ctrlVTree(item[cKey], key1, key2, cKey)
		}
		return item
	})
	return retTree
}

/**
 * 给tree数据某个key得值复制到另一个key的值后面
 * tree：树结构数据
 * key1：提供值的key
 * key2: 接收的key
 * cKey: 子集的key
 */

export const copeCVTree = (
	tree: any[],
	key1: string,
	key2: string,
	cKey: string = "children"
) => {
	if (!tree || !tree.length) {
		return tree
	}
	const retTree = tree.map((item) => {
		if (item[key1] !== "0" && item[key1]) {
			item[key2] = `${item[key2]} (${JSON.parse(JSON.stringify(item[key1]))})`
		}
		if (item[cKey] && item[cKey].length) {
			item[cKey] = copeCVTree(item[cKey], key1, key2, cKey)
		}
		return item
	})
	return retTree
}

/**
 * 更新面包屑title
 * @date 2023-08-29
 * @param {any} orgTitle 初始化title对象
 * @param {any} options 配置对象：初始化时传入新值val，树点击时传入当前节点
 * @returns {any} newTitleObj 更新后的title对象
 */
export function refreshTitle(orgTitle: MaybeRef<anyKey>, options: anyKey) {
	const { val = "", curNode } = options
	orgTitle = unref(orgTitle)
	const titleObj = JSON.parse(JSON.stringify(orgTitle))
	if (curNode) {
		return markRaw({
			...orgTitle,
			name: titleObj.name.concat(getTreeTitle(curNode, "name"))
		})
	} else {
		titleObj.name.push(val)
		return markRaw({
			...orgTitle,
			name: titleObj.name
		})
	}
}

/**
 * 随机生成0~61位数字+大小写字母组合字符串
 * @date 2023-08-23
 * @param {string | number} n 生成字符数
 * @returns {string} 随机字符
 */
export function generateMixed(n: number | string) {
	n = +n
	const chars = [
		"0",
		"1",
		"2",
		"3",
		"4",
		"5",
		"6",
		"7",
		"8",
		"9",
		"A",
		"B",
		"C",
		"D",
		"E",
		"F",
		"G",
		"H",
		"I",
		"J",
		"K",
		"L",
		"M",
		"N",
		"O",
		"P",
		"Q",
		"R",
		"S",
		"T",
		"U",
		"V",
		"W",
		"X",
		"Y",
		"Z",
		"a",
		"b",
		"c",
		"d",
		"e",
		"f",
		"g",
		"h",
		"i",
		"j",
		"k",
		"l",
		"m",
		"n",
		"o",
		"p",
		"q",
		"r",
		"s",
		"t",
		"u",
		"v",
		"w",
		"x",
		"y",
		"z"
	]
	let resStr = ""
	for (var i = 0; i < n; i++) {
		var id = Math.ceil(Math.random() * 61)
		resStr += chars[id]
	}
	return resStr
}

/**
 * 将数组中具有相同指定键名的元素筛选到单独数组，并返回结果数组
 * @date 2023-08-24
 * @param {any} arr 原数组
 * @param {any} str 分类键名
 */
export function sortArr(arr: any[], str: string): any[] {
	const _arr = []
	let _t = []
	// 临时变量
	let _tmp

	// 按照特定的参数将数组排序将具有相同值得排在一起
	arr = arr.sort((a: any, b: any) => {
		const s = a[str].split("-")[0]
		const t = b[str].split("-")[0]

		return s < t ? -1 : 1
	})

	if (arr.length) {
		_tmp = arr[0][str].split("-")[0]
	}
	// 将相同类别的对象添加到统一个数组
	for (const i in arr) {
		if (arr[i][str].split("-")[0] === _tmp) {
			_t.push(arr[i])
		} else {
			_tmp = arr[i][str].split("-")[0]
			_arr.push(_t)
			_t = [arr[i]]
		}
	}

	_arr.push(_t)
	return _arr
}

// 字典项数据源接口类型
interface DictionaryList {
	label: string
	code: string
	children: any[]
}

/**
 * 公共工具函数
 */
const imageExtensions = [".jpg", ".jpeg", ".png", ".jpeg"] // 图片扩展名
const videoExtensions = [".mp4", ".mov", ".rmvb"] // 视频扩展名
const audioExtensions = [
	".mp3",
	".wav",
	".ogg",
	".oga",
	".aac",
	".m4a",
	".webm",
	".flac"
	// ".amr"
] // 音频扩展名

// 获取文件下载链接
export const getFileHttpUrl = (filePath: string) => {
	return (
		import.meta.env.VITE_BASE_API +
		import.meta.env.VITE_APP_BASE_DOWNLOAD_URL +
		filePath
	)
}

// 获取文件扩展名
export const getFileExtension = (filename: string) => {
	const dotIndex = filename.lastIndexOf(".")
	return dotIndex !== -1 ? filename.substring(dotIndex) : ""
}

/**
 * 图片类型判断
 * @param filename 文件名
 * @returns boolean
 */
export const isImageFile = (filename: string) => {
	const extension = getFileExtension(filename).toLowerCase()
	return imageExtensions.includes(extension)
}

/**
 * 视频类型判断
 * @param filename 文件名
 * @returns boolean
 */
export const isVideoFile = (filename: string) => {
	const extension = getFileExtension(filename).toLowerCase()
	return videoExtensions.includes(extension)
}

/**
 * 音频类型判断
 * @param filename 文件名
 * @returns boolean
 */
export const isAudioFile = (filename: string) => {
	const extension = getFileExtension(filename).toLowerCase()
	return audioExtensions.includes(extension)
}
/*
	判断.amr文件
*/
export const isAudioFileAmr = (filename: string) => {
	const extension = getFileExtension(filename).toLowerCase()
	return extension == ".amr"
}
/**
 * 从数据源，根据字典值code，获取字典code值对应的字典项数组（通常用于渲染字典下拉项列表）
 * @param code 字典code值
 * @param dictionaryList 数据源
 * @returns 字典code值对应的所有字典项数组
 */
export const getOneDic = (code: string, dictionaryList: DictionaryList[]) => {
	let ret: any[] = []
	const curDic = dictionaryList.filter((item: any) => {
		return item.code === code
	})
	if (curDic && curDic.length) {
		ret = curDic[0].children
	}
	return ret
}

/**
 *  根据文件后缀判断文件分类并展示不同ui：图片类，文件类
 * @param filename 文件名
 * @returns boolean true:是图片 false：不是图片
 */
export const getFileType = (filename: string) => {
	const extension = getFileExtension(filename).toLowerCase()
	const data = [...imageExtensions, ...videoExtensions, ...audioExtensions]
	return data.includes(extension)
}

/**
 * @param filePath 文件url
 */
export const commonDownLoadFile = (filePath: string) => {
	const httpUrl = getFileHttpUrl(filePath)
	// 创建一个新的a标签元素
	const aLink = document.createElement("a")

	// 假设你有一个文件URL或Blob对象
	const fileUrlOrBlob = httpUrl // 替换为实际文件URL或Blob对象

	// 对于远程URL资源
	if (typeof fileUrlOrBlob === "string") {
		// 设置a标签的href属性为文件URL
		aLink.href = fileUrlOrBlob

		// 如果要提供自定义的文件名，可以设置download属性
		// aLink.download = "下载.txt" // 替换为你想要的文件名

		// 隐式地点击这个a标签以触发下载
		document.body.appendChild(aLink)
		aLink.click()
		document.body.removeChild(aLink) // 清理DOM
	}

	// 对于Blob对象或者Data URLs
	// if (fileUrlOrBlob instanceof Blob) {
	// 	// 创建一个URL对象指向Blob数据
	// 	const objectUrl = URL.createObjectURL(fileUrlOrBlob)
	// 	aLink.href = objectUrl
	// 	// aLink.download = "customFileName.pdf" // 同样设置自定义文件名

	// 	// 添加到DOM并触发点击
	// 	document.body.appendChild(aLink)
	// 	aLink.click()

	// 	// 清理
	// 	document.body.removeChild(aLink)
	// 	URL.revokeObjectURL(objectUrl) // 释放内存引用
	// }
}
