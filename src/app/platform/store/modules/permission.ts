import { ref } from "vue"
import store from "@/app/platform/store"
import { defineStore } from "pinia"
import { type RouteRecordRaw } from "vue-router"
import router, { constantRoutes, asyncRoutes } from "@/router"
import asyncRouteSettings from "@/config/asyncRoute"
import { useUserStoreHook } from "./user"
const viewModules = import.meta.glob("@/app/**/*.vue")

//设置动态路由页面路由
const setrouter = (data: {
	map: (arg0: (item: any) => void) => void
	children: any
	component: any
}) => {
	data.map((item: any) => {
		if (item.children) {
			let component = viewModules["/src/app" + item.component]
			item.component = component
			setrouter(item.children)
		} else {
			let component = viewModules["/src/app" + item.component]
			item.component = component
		}
	})
	return data
}

export const usePermissionStore = defineStore("permission", () => {
	const routes = ref<RouteRecordRaw[]>([])
	const dynamicRoutes = ref<RouteRecordRaw[]>([])
	let serverInfo = ref<RouteRecordRaw[]>([])

	// 初始化权限路由
	const setRoutes = async () => {
		const userStore = useUserStoreHook()
		if (asyncRouteSettings.open) {
			// 注意：角色必须是一个数组！ 例如: ['admin'] 或 ['developer', 'editor']
			await userStore.getInfo()
			const { roles, userInfo } = userStore
			// 根据角色生成可访问的 Routes（可访问路由 = 常驻路由 + 有访问权限的动态路由）

			//设置动态路由页面路由
			setrouter(userInfo.menus)
			// const accessedRoutes = filterAsyncRoutes(userInfo.menus, roles)
			const accessedRoutes = userInfo.menus.filter((menu: RouteRecordRaw) => menu.meta?.alwaysShow !== false)
			routes.value = accessedRoutes.concat(constantRoutes)
			dynamicRoutes.value = accessedRoutes
			serverInfo.value = userStore.userInfo.menus
		} else {
			// 没有开启动态路由功能，则启用默认角色
			userStore.setRoles(asyncRouteSettings.defaultRoles)
			const accessedRoutes = asyncRoutes
			routes.value = accessedRoutes.concat(constantRoutes)
			dynamicRoutes.value = accessedRoutes
		}

		// 清空默认路由
		router.getRoutes().forEach((route) => {
			const { name } = route
			if (name) {
				router.hasRoute(name) && router.removeRoute(name)
			}
		})

		// 将'有访问权限的动态路由' 添加到 Router 中
		for (let index = routes.value.length - 1; index >= 0; index--) {
			if (
				router
					.getRoutes()
					.find((route) => route.path === routes.value[index].path)
			)
				continue
			router.addRoute(routes.value[index])
		}

		// for (let index = serverInfo.value.length - 1; index >= 0; index--) {
		// 	if (
		// 		router
		// 			.getRoutes()
		// 			.find((route) => route.path === serverInfo.value[index].path)
		// 	)
		// 		continue
		// 	router.addRoute(serverInfo.value[index])
		// }
		// 修改前
		// const redirectedRoute =
		// 	routes.value[0].children && routes.value[0].children.length > 0
		// 		? routes.value[0].children[0].path //routes.value[0].path + "/" + routes.value[0].children[0].path
		// 		: routes.value[0].path
		// 修改后 
		const redirectedRoute = routes.value[0]?.children?.[0]?.children?.[0]
		? routes.value[0].children[0].children[0].path
		: routes.value[0].children?.[0]?.path || routes.value[0].path;

		router.addRoute({
			path: "/",
			name: "/",
			redirect: redirectedRoute,
			meta: {
				hidden: true
			}
		})

		// router.addRoute({
		// 	path: "/:pathMatch(.*)*",
		// 	redirect: "/404",
		// 	name: "ErrorPage",
		// 	meta: {
		// 		hidden: true
		// 	}
		// })
	}

	return { routes, dynamicRoutes, setRoutes }
})

/** 在 setup 外使用 */
export function usePermissionStoreHook() {
	return usePermissionStore(store)
}
