// import { permission } from "./../../directives/permission/index"
import { ref } from "vue"
import store from "@/app/platform/store"
import { defineStore } from "pinia"
import { usePermissionStore } from "./permission"
import {
	getToken,
	removeToken,
	setToken
} from "@/app/platform/utils/cache/cookies"
import router, { resetRouter } from "@/router"
import { loginApi, getUserInfoApi, logoutApi } from "@/app/platform/api/login"
import { type LoginRequestData } from "@/app/platform/api/login/types/login"
import { type RouteRecordRaw } from "vue-router"
import asyncRouteSettings from "@/config/asyncRoute"
import { getIdList } from "@/app/platform/utils/tree"
import { find } from "lodash-es"

export const useUserStore = defineStore("user", () => {
	const token = ref<string>(getToken() || "")
	const roles = ref<any>(null)
	const username = ref<string>("")
	const userInfo = ref<any>({})
	const serverInfo = ref<any>([])
	const dataDictionary = ref<any>([])
	const treeDictionary = ref<any>([])
	const userAvatar = ref<string>("")
	const isSuperAdmin = ref(false)

	const permissionStore = usePermissionStore()

	/** 设置角色数组 */
	const setRoles = (value: string[]) => {
		roles.value = value
	}
	/** 登录 */
	const login = async (data: LoginRequestData) => {
		const res: any = await loginApi(data)
		setToken(res.access_token)
		token.value = res.access_token
	}

	/** 获取用户详情 */
	const getInfo = async () => {
		const data: any = await getUserInfoApi()
		userInfo.value = data
		userInfo.value.menus = data.menus.filter(
			(item: any) => item.component !== "pda"
		)
		isSuperAdmin.value = !!data.admin
		let resultRoleList: any
		setDictionary(data)
		if (data.defaultRoleId) {
			resultRoleList = find(
				data.roleList,
				(o) => o.roloId === data.defaultRoleId
			)

			if (!resultRoleList) {
				resultRoleList = data.roleList.at(-1)
			}
		} else {
			resultRoleList = data.roleList.at(-1)
		}

		if (!resultRoleList) {
			resultRoleList = {}
		}

		userInfo.value = {
			...userInfo.value,
			resultRoleList: resultRoleList
		}
		const roloIdList: anyKey[] = []
		const privillegPurviewList = getIdList(
			[resultRoleList],
			"privillegPurview",
			"privillegesList"
		)

		const allRoloIdList = onlyItemArr(roloIdList.concat(privillegPurviewList))
		// 验证返回的 roles 是否为一个非空数组，否则塞入一个没有任何作用的默认角色，防止路由守卫逻辑进入无限循环
		allRoloIdList.push(...asyncRouteSettings.defaultRoles)
		roles.value = allRoloIdList
	}
	/** 切换角色 */
	const changeRoles = async (role: string) => {
		const newToken = "token-" + role
		token.value = newToken
		setToken(newToken)
		await getInfo()
		// permissionStore.setRoutes(roles.value)
		resetRouter()
		permissionStore.dynamicRoutes.forEach((item: RouteRecordRaw) => {
			router.addRoute(item)
		})
	}
	/** 登出 */
	const logout = () => {
		logoutApi({clientType:1}).finally(() => {
			removeToken()
			resetRouter()
			token.value = ""
			roles.value = null
			router.push("/login")
		})
	}
	/** 重置 Token */
	const resetToken = () => {
		console.log('===重置token')
		removeToken()
		resetRouter()
		token.value = ""
		roles.value = null
		router.push("/login")
	}

	/** 设置字典缓存 */
	const setDictionary = (data: any) => {
		dataDictionary.value = data?.dataDictionary
		treeDictionary.value = data?.treeDictionary
		localStorage.setItem("dataDictionary", JSON.stringify(dataDictionary.value))
		localStorage.setItem("treeDictionary", JSON.stringify(treeDictionary.value))

	}

	return {
		token,
		roles,
		username,
		userInfo,
		serverInfo,
		userAvatar,
		setRoles,
		login,
		getInfo,
		changeRoles,
		logout,
		resetToken,
		isSuperAdmin,
		setDictionary
	}
})

/** 在 setup 外使用 */
export function useUserStoreHook() {
	return useUserStore(store)
}
