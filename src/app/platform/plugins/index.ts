import type { Plugin } from "vite"
import vue from "@vitejs/plugin-vue"
import vueJsx from "@vitejs/plugin-vue-jsx"
import { configAutoComponentsPlugin } from "./autoComponents"
import { configAutoImportPlugin } from "./autoImport"
import {
	createStyleImportPlugin,
	VxeTableResolve
} from "vite-plugin-style-import"

export const vitePlugins: (Plugin | Plugin[])[] = [
	vue(),
	vueJsx(),
	configAutoComponentsPlugin,
	configAutoImportPlugin,
	createStyleImportPlugin({
		resolves: [VxeTableResolve()]
	})
]
