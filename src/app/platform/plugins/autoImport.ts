import AutoImport from "unplugin-auto-import/vite"
import IconsResolver from "unplugin-icons/resolver"
import { ElementPlusResolver } from "unplugin-vue-components/resolvers"

export const configAutoImportPlugin = AutoImport({
	include: [/\.[tj]sx?$/, /\.vue$/, /\.vue\?vue/, /\.md$/],
	imports: ["vue", "vue-router", "pinia"],
	// 自动导入Element icon
	resolvers: [
		ElementPlusResolver(),
		IconsResolver({
			prefix: "Icon"
		})
	],
	// 指定引入根目录下的 requests，utils 目录内的所有函数
	dirs: ["./src/requests/**", "./src/utils/**", "./src/app/platform/utils/**"],
	// 指定生成的 d.ts 文件位置与文件名
	dts: "types/auto-import.d.ts",

	// 配置开启 eslint
	eslintrc: {
		enabled: true
	}
})
