/*
 * @Description: 自动导入
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-07-10 10:31:02
 * @LastEditTime: 2023-07-17 16:22:15
 * @LastEditors: FengJianKai
 */
/**
 * 自动导入组件，可直接调用组件不用再引入
 */
import Components from "unplugin-vue-components/vite"
import { ElementPlusResolver } from "unplugin-vue-components/resolvers"
import IconsResolver from "unplugin-icons/resolver"

export const configAutoComponentsPlugin = Components({
	dirs: [
		"src/compontents",
		"src/app/platform/layout"
	], // 自动导入的文件，可自行更改或添加
	resolvers: [
		ElementPlusResolver(),
		IconsResolver({
			enabledCollections: ["ep"]
		})
	],
	extensions: ["vue", "tsx"],
	// 指定生成的 d.ts 文件位置与文件名
	dts: "types/components.d.ts"
})
