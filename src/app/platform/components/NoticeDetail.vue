<script lang="ts" setup>
import { ref } from 'vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  noticeData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['close'])

function handleClose() {
  emit('close')
}
</script>

<template>
  <Drawer class="drawer-hidden-box" 
  :destroyOnClose="true" 
  v-model="props.visible" 
  size="780" drawer 
  @close="handleClose">
    <div class="form-body">
      <Title :title="{name: ['通知公告'], icon: ['fas', 'square-share-nodes']}"/>
      <el-scrollbar>
        <div class="noticeDetail">
          <div class="noticeTittle">{{ noticeData.noticeTitle }}</div>
          <div class="noticeCenterTime">{{ noticeData.publishTime }}
            <span>{{ noticeData.createdByName }}</span>
          </div>
          <div class="preformatted" v-html="noticeData.noticeContent"></div>
        </div>
      </el-scrollbar>
      <div class="btn-groups">
        <ButtonList :button="[{name: '取消', icon: ['fas', 'circle-minus']}]" @on-btn-click="handleClose" />
      </div>
    </div>
  </Drawer>
</template>

<style lang="scss" scoped>
.form-body {
	height: 100%;
	width: 100%;
	display: flex;
	flex-direction: column;
	.common-from-group {
		height: 0;
		flex: 1;
		padding: 0 10px;
	}
	.btn-groups {
		display: flex;
		justify-content: flex-end;
		padding: 10px 10px 0 0;
		border-top: 1px solid var(--pitaya-border-color);
	}

  .noticeDetail {
    padding: 20px;

    .noticeTittle {
      font-size: 23px;
      font-family: "Microsoft YaHei", 微软雅黑;
      text-align: left;
      color: rgb(51, 51, 51);
      letter-spacing: 0px;
    }

    .noticeCenterTime {
      font-size: 12px;
      color: rgb(153, 153, 153);
      margin: 13px 0;

      span {
        margin-right: 10px;
      }
    }

    .preformatted {
      white-space: pre-line;
      font-size: 14px;
      color: rgba(85, 85, 85, 1);
      line-height: 25px;
    }
  }

  .btn-groups {
    border-top: 1px solid #ccc;
    padding-top: 10px;
    padding-right: 10px;
    display: flex;
    justify-content: flex-end;
  }
}
</style>
