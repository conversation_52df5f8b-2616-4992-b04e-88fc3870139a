
import { expertManagementApi } from "../api/configuration/expertManagement"
import { objectToFormData } from "../utils/objectToFromData"
import { ref } from 'vue'
interface linesArr{
	label: string,
	value: string | number
}

/**
 * @description 获取最底层数据
 */
function getDeepData(data: any[], list: any[]) {
	data.forEach((item) => {
    if (item.children && item.children.length > 0) {
      getDeepData(item.children, list);
    } else {
      list.push(item);
    }
  });
  return list;
}

export function useLine() {
	const linesArr = ref<linesArr[]>([])

	expertManagementApi.getLineListApi(
		objectToFormData({
			currentPage: 1,
			pageSize: 500
		})
	)
		.then((res: any) => {
			const list: any[] = []
			linesArr.value = getDeepData(res.rows, list).map(item => ({ label: item.name, value: item.id }))
		})

	return { linesArr }
}
