import { reactive } from "vue"

interface DefaultPaginationData {
	currentPage: number
	pageSize: number
}

interface PaginationData {
	currentPage?: number
	pageSize?: number
}

/** 默认的分页参数 */
const defaultPaginationData: DefaultPaginationData = {
	currentPage: 1,
	pageSize: 20
}

export function usePagination(initialPaginationData: PaginationData = {}) {
	/** 合并分页参数 */
	const paginationData = reactive({
		...defaultPaginationData,
		...initialPaginationData
	})
	/** 改变当前页码 */
	const handleCurrentChange = (value: number) => {
		paginationData.currentPage = value
	}
	/** 改变当前一页最大数据量 */
	const handleSizeChange = (value: number) => {
		paginationData.pageSize = value
	}

	return { paginationData, handleCurrentChange, handleSizeChange }
}
