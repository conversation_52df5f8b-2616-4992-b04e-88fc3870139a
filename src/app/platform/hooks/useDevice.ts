
import { expertManagementApi } from "../api/configuration/expertManagement"
import { objectToFormData } from "../utils/objectToFromData"
import { ref } from 'vue'
interface linesArr{
	label: string,
	value: string
}

/**
 * @description 递归处理数据, 为 cascade 的每个 options 添加 label 和 value 属性
 */
const addValueAndLabelForTree = (arr: any[]): any => {
  arr = arr.map(node => {
    if (node.hasOwnProperty('children')) {
      node.children = addValueAndLabelForTree(node.children)
    }
    return {
      ...node,
      label: node['composeName'],
      value: node['composeCode'],
    }
  })
  return arr
}

export function useDevice(){
	const deviceArr = ref<linesArr[]>([])

  expertManagementApi.getVEquipmentClassApi(
		objectToFormData({
			currentPage: 1,
			pageSize: 500
		})
	)
		.then((res: any) => {
			deviceArr.value = addValueAndLabelForTree(res)
		})

		return { deviceArr }
}