import SockJS from "sockjs-client/dist/sockjs.min.js"
import { Stomp } from "@stomp/stompjs"

// 请求地址
const baseUrl = import.meta.env.VITE_APP_BASE_WS

class WebSocket {
	//构造函数
	constructor() {
		this.callback = null // 回调函数
		this.stompClient == null
		this.url = null
		this.headers = null
	}

	/** socket连接 subscribeEvent*/
	subscribeEvent(headers = {}, url, callback) {
		if (this.stompClient != null) {
			try {
				this.stompClient.disconnect()
			} catch (e) {
				// console.log(e)
			}
		}
		//连接SockJS
		// 获取STOMP子协议的客户端对象
		this.stompClient = Stomp.over(() => {
			return new SockJS(baseUrl)
		})
		this.url = url
		if (headers) {
			this.headers = headers
		}
		if (callback) {
			this.callback = callback
		}
		this.stompClient.heartbeat.outgoing = 5000
		this.stompClient.heartbeat.incoming = 0
		this.stompClient.reconnect_delay = 10000
		//日志不打印
		this.stompClient.debug = (res) => {
			// console.debug(res + "\n")
		}
		this.stompClient.connect(
			headers,
			() => {
				this.stompClient.subscribe(this.url, (response) => {
					callback("success", response)
				})
			},
			(err) => {
				console.info("连接失败", err)
			}
		)
	}
	destory() {
		this.stompClient.disconnect()
	}
}
export default WebSocket
