
import { expertManagementApi } from "../api/configuration/expertManagement"
import { objectToFormData } from "../utils/objectToFromData"
import { ref } from 'vue'
interface linesArr{
	label: string,
	value: string
}

/**
 * @description 获取最底层数据
 */
const getDeepData = (data: any[], list: any[]) => {
	data.forEach((item) => {
    if (item.children && item.children.length > 0) {
      getDeepData(item.children, list);
    } else {
      list.push(item);
    }
  });
  return list;
}

export function useMajor(needFilter = false){
	const majorArr = ref<linesArr[]>([])

  expertManagementApi.getProfessionTreeApi(
		objectToFormData({
			currentPage: 1,
			pageSize: 500
		})
	)
		.then((res: any) => {
			const list: any[] = []
			majorArr.value = getDeepData(res, list).map(item => ({ label: item.name, value: item.id, composeCode: item.composeCode, majorCode: item.id }))
			if(needFilter){
				majorArr.value = majorArr.value.filter((item: any) => item.composeCode.includes('GD-'))
			}
		})

		return { majorArr }
}