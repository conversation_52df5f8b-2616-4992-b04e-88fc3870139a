<script lang="ts" setup>
import { reactive, ref, watch } from "vue"
import {
	createTableDataApi,
	deleteTableData<PERSON><PERSON>,
	updateTableDataApi,
	getTableData<PERSON><PERSON>
} from "@/app/platform/api/table"
import { type GetTableData } from "@/app/platform/api/table/types/table"
import {
	type FormInstance,
	type FormRules,
	ElMessage,
	ElMessageBox
} from "element-plus"
import {
	Search,
	Refresh,
	CirclePlus,
	Delete,
	Download,
	RefreshRight
} from "@element-plus/icons-vue"
import { usePagination } from "@/app/platform/hooks/usePagination"

defineOptions({
	// 命名当前组件
	name: "ElementPlus"
})

const loading = ref<boolean>(false)
const { paginationData, handleCurrentChange, handleSizeChange } =
	usePagination()

//#region 增
const dialogVisible = ref<boolean>(false)
const formRef = ref<FormInstance | null>(null)
const formData = reactive({
	username: "",
	password: ""
})
const formRules: FormRules = reactive({
	username: [{ required: true, trigger: "blur", message: "请输入用户名" }],
	password: [{ required: true, trigger: "blur", message: "请输入密码" }]
})
const handleCreate = () => {
	formRef.value?.validate((valid: boolean, fields) => {
		if (valid) {
			if (currentUpdateId.value === undefined) {
				createTableDataApi(formData)
					.then(() => {
						ElMessage.success("新增成功")
						getTableData()
					})
					.finally(() => {
						dialogVisible.value = false
					})
			} else {
				updateTableDataApi({
					id: currentUpdateId.value,
					username: formData.username
				})
					.then(() => {
						ElMessage.success("修改成功")
						getTableData()
					})
					.finally(() => {
						dialogVisible.value = false
					})
			}
		} else {
			console.error("表单校验不通过", fields)
		}
	})
}
const resetForm = () => {
	currentUpdateId.value = undefined
	formData.username = ""
	formData.password = ""
}
//#endregion

//#region 删
const handleDelete = (row: GetTableData) => {
	ElMessageBox.confirm(`正在删除用户：${row.username}，确定删除？`, "提示", {
		confirmButtonText: "确定",
		cancelButtonText: "取消",
		autofocus: false,
		type: "warning"
	}).then(() => {
		deleteTableDataApi(row.id).then(() => {
			ElMessage.success("删除成功")
			getTableData()
		})
	})
}
//#endregion

//#region 改
const currentUpdateId = ref<undefined | string>(undefined)
const handleUpdate = (row: GetTableData) => {
	currentUpdateId.value = row.id
	formData.username = row.username
	dialogVisible.value = true
}
//#endregion

//#region 查
const tableData = ref<GetTableData[]>([])
const searchFormRef = ref<FormInstance | null>(null)
const searchData = reactive({
	username: "",
	phone: ""
})
const getTableData = () => {
	loading.value = true
	getTableDataApi({
		currentPage: paginationData.currentPage,
		size: paginationData.pageSize,
		username: searchData.username || undefined,
		phone: searchData.phone || undefined
	})
		.then((res) => {
			paginationData.total = res.data.total
			tableData.value = res.data.list
		})
		.catch(() => {
			tableData.value = []
		})
		.finally(() => {
			loading.value = false
		})
}
const handleSearch = () => {
	paginationData.currentPage === 1
		? getTableData()
		: (paginationData.currentPage = 1)
}
const resetSearch = () => {
	searchFormRef.value?.resetFields()
	handleSearch()
}
//#endregion

/** 监听分页参数的变化 */
watch(
	[() => paginationData.currentPage, () => paginationData.pageSize],
	getTableData,
	{ immediate: true }
)
</script>

<template>
	<div class="app-container">
		<el-card v-loading="loading" shadow="never" class="search-wrapper">
			<el-form ref="searchFormRef" :inline="true" :model="searchData">
				<el-form-item prop="username" label="用户名">
					<el-input v-model="searchData.username" placeholder="请输入" />
				</el-form-item>
				<el-form-item prop="phone" label="手机号">
					<el-input v-model="searchData.phone" placeholder="请输入" />
				</el-form-item>
				<el-form-item>
					<el-button v-btn type="primary" :icon="Search" @click="handleSearch"
						>查询</el-button
					>
					<el-button v-btn :icon="Refresh" @click="resetSearch">重置</el-button>
				</el-form-item>
			</el-form>
		</el-card>
		<el-card v-loading="loading" shadow="never">
			<div class="toolbar-wrapper">
				<div>
					<el-button
						v-btn
						type="primary"
						:icon="CirclePlus"
						@click="dialogVisible = true"
						>新增用户</el-button
					>
					<el-button v-btn type="danger" :icon="Delete">批量删除</el-button>
				</div>
				<div>
					<el-tooltip content="下载">
						<el-button v-btn type="primary" :icon="Download" circle />
					</el-tooltip>
					<el-tooltip content="刷新当前页">
						<el-button
							v-btn
							type="primary"
							:icon="RefreshRight"
							circle
							@click="getTableData"
						/>
					</el-tooltip>
				</div>
			</div>
			<div class="table-wrapper">
				<el-table :data="tableData">
					<el-table-column type="selection" width="50" align="center" />
					<el-table-column prop="username" label="用户名" align="center" />
					<el-table-column prop="roles" label="角色" align="center">
						<template #default="scope">
							<el-tag v-if="scope.row.roles === 'admin'" effect="plain"
								>admin</el-tag
							>
							<el-tag v-else type="warning" effect="plain">{{
								scope.row.roles
							}}</el-tag>
						</template>
					</el-table-column>
					<el-table-column prop="phone" label="手机号" align="center" />
					<el-table-column prop="email" label="邮箱" align="center" />
					<el-table-column prop="status" label="状态" align="center">
						<template #default="scope">
							<el-tag v-if="scope.row.status" type="success" effect="plain"
								>启用</el-tag
							>
							<el-tag v-else type="danger" effect="plain">禁用</el-tag>
						</template>
					</el-table-column>
					<el-table-column prop="createTime" label="创建时间" align="center" />
					<el-table-column
						fixed="right"
						label="操作"
						width="150"
						align="center"
					>
						<template #default="scope">
							<el-button
								v-btn
								type="primary"
								text
								bg
								size="small"
								@click="handleUpdate(scope.row)"
								>修改</el-button
							>
							<el-button
								v-btn
								type="danger"
								text
								bg
								size="small"
								@click="handleDelete(scope.row)"
								>删除</el-button
							>
						</template>
					</el-table-column>
				</el-table>
			</div>
			<div class="pager-wrapper">
				<el-pagination
					background
					:layout="paginationData.layout"
					:page-sizes="paginationData.pageSizes"
					:total="paginationData.total"
					:page-size="paginationData.pageSize"
					:currentPage="paginationData.currentPage"
					@size-change="handleSizeChange"
					@current-change="handleCurrentChange"
				/>
			</div>
		</el-card>
		<!-- 新增/修改 -->
		<el-dialog
			v-model="dialogVisible"
			:title="currentUpdateId === undefined ? '新增用户' : '修改用户'"
			@close="resetForm"
			width="30%"
		>
			<el-form
				ref="formRef"
				:model="formData"
				:rules="formRules"
				label-width="100px"
				label-position="left"
			>
				<el-form-item prop="username" label="用户名">
					<el-input v-model="formData.username" placeholder="请输入" />
				</el-form-item>
				<el-form-item
					prop="password"
					label="密码"
					v-if="currentUpdateId === undefined"
				>
					<el-input v-model="formData.password" placeholder="请输入" />
				</el-form-item>
			</el-form>
			<template #footer>
				<el-button v-btn @click="dialogVisible = false">取消</el-button>
				<el-button v-btn type="primary" @click="handleCreate">确定</el-button>
			</template>
		</el-dialog>
	</div>
</template>

<style lang="scss" scoped>
.search-wrapper {
	margin-bottom: 20px;
	:deep(.el-card__body) {
		padding-bottom: 2px;
	}
}

.toolbar-wrapper {
	display: flex;
	justify-content: space-between;
	margin-bottom: 20px;
}

.table-wrapper {
	margin-bottom: 20px;
}

.pager-wrapper {
	display: flex;
	justify-content: flex-end;
}
</style>
