<template>
	<div class="app-container">
		<SwitchRoles @change="handleRolesChange" />
		<el-tag type="warning" size="large">当前页面只有 admin 权限可见，切换权限后将不能进入该页面</el-tag>
	</div>
</template>

<script lang="ts" setup>
import { useRouter } from "vue-router"
import SwitchRoles from "./components/SwitchRoles.vue"
import { ref } from "vue"

const router = useRouter()
const handleRolesChange = () => {
	router.push({ path: "/403" })
}
const testTableData = ref<any[]>([
	{
		id: 1,
		date: "2016-05-02",
		name: "王小虎",
		address: "上海市普陀区金沙江路 1518 弄",
		hasChildren: true
	},
	{
		id: 2,
		date: "2016-05-04",
		name: "王小虎",
		address: "上海市普陀区金沙江路 1517 弄"
	},
	{
		id: 3,
		date: "2016-05-01",
		name: "王小虎",
		address: "上海市普陀区金沙江路 1519 弄",
		children: [
			{
				id: 31,
				date: "2016-05-01",
				name: "王小虎",
				address: "上海市普陀区金沙江路 1519 弄"
			},
			{
				id: 32,
				date: "2016-05-01",
				name: "王小虎",
				address: "上海市普陀区金沙江路 1519 弄"
			}
		]
	},
	{
		id: 4,
		date: "2016-05-03",
		name: "王小虎",
		address: "上海市普陀区金沙江路 1516 弄"
	}
])

// name: string, // 字段名称
// key: string, // 字段接收key
// type: string, // 表单类型 startAndEndTime dateTime input select cascader （有其他他特殊需求可自行扩展）
// placeholder: string, // 空选项描述
// children?: options[],
// disabledDate?: Function

const disabledDate = (time: Date) => {
	return time.getTime() > Date.now()
}

const queryArrList = [
	{
		name: "设备名称",
		key: "name",
		type: "input",
		placeholder: "请输入查询关键字"
	},
	{
		name: "生产日期",
		key: "time",
		type: "startAndEndTime",
		disabledDate
	},
	{
		name: "购买日期",
		key: "payTime",
		type: "dateTime",
		placeholder: "请选择购买日期"
	},
	{
		name: "设备类型",
		key: "type",
		type: "select",
		placeholder: "请选择设备类型",
		children: [
			{
				label: "t1",
				value: "t1"
			},
			{
				label: "t2",
				value: "t2"
			}
		]
	},
	{
		name: "设备产地",
		key: "address",
		type: "cascader",
		placeholder: "请选择设备产地",
		children: [
			{
				label: "中国",
				value: "中国",
				children: [
					{
						label: "北京",
						value: "北京"
					},
					{
						label: "上海",
						value: "上海"
					}
				]
			},
			{
				label: "外国",
				value: "外国",
				children: [
					{
						label: "美国",
						value: "美国"
					},
					{
						label: "德国",
						value: "德国"
					}
				]
			}
		]
	}
]

</script>

<style lang="scss" scoped>
.el-tag {
	margin-top: 15px;
}
</style>
