/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// noinspection JSUnusedGlobalSymbols
// Generated by unplugin-auto-import
export {}
declare global {
  const EffectScope: typeof import('vue')['EffectScope']
  const ElMessage: typeof import('element-plus/es')['ElMessage']
  const ElMessageBox: typeof import('element-plus/es')['ElMessageBox']
  const _matchFormProp: typeof import('../app/platform/utils/permission')['_matchFormProp']
  const acceptHMRUpdate: typeof import('pinia')['acceptHMRUpdate']
  const checkPermission: typeof import('../app/platform/utils/permission')['checkPermission']
  const checkPermissionType: typeof import('../app/platform/utils/permission')['checkPermissionType']
  const checkTree: typeof import('../app/platform/utils/common')['checkTree']
  const computed: typeof import('vue')['computed']
  const copeCVTree: typeof import('../app/platform/utils/common')['copeCVTree']
  const createApp: typeof import('vue')['createApp']
  const createPinia: typeof import('pinia')['createPinia']
  const ctrlVTree: typeof import('../app/platform/utils/common')['ctrlVTree']
  const customRef: typeof import('vue')['customRef']
  const defineAsyncComponent: typeof import('vue')['defineAsyncComponent']
  const defineComponent: typeof import('vue')['defineComponent']
  const defineStore: typeof import('pinia')['defineStore']
  const effectScope: typeof import('vue')['effectScope']
  const email: typeof import('../app/platform/utils/regular')['email']
  const events: typeof import('../app/platform/utils/bus')['events']
  const findAllEndNode: typeof import('../app/platform/utils/tree')['findAllEndNode']
  const findAllEndNodeV2: typeof import('../app/platform/utils/tree')['findAllEndNodeV2']
  const findAllParentNode: typeof import('../app/platform/utils/tree')['findAllParentNode']
  const findAllParentNodeV2: typeof import('../app/platform/utils/tree')['findAllParentNodeV2']
  const formatDateTime: typeof import('../app/platform/utils/common')['formatDateTime']
  const generateMixed: typeof import('../app/platform/utils/common')['generateMixed']
  const generatePermissionKey: typeof import('../app/platform/utils/permission')['generatePermissionKey']
  const getActivePinia: typeof import('pinia')['getActivePinia']
  const getActiveThemeName: typeof import('../app/platform/utils/cache/local-storage')['getActiveThemeName']
  const getCachedViews: typeof import('../app/platform/utils/cache/local-storage')['getCachedViews']
  const getConfigLayout: typeof import('../app/platform/utils/cache/local-storage')['getConfigLayout']
  const getCssVariableValue: typeof import('../app/platform/utils/common')['getCssVariableValue']
  const getCurrentInstance: typeof import('vue')['getCurrentInstance']
  const getCurrentScope: typeof import('vue')['getCurrentScope']
  const getDicDescEcho: typeof import('../app/platform/utils/dicEcho')['getDicDescEcho']
  const getIdList: typeof import('../app/platform/utils/tree')['getIdList']
  const getNodeList: typeof import('../app/platform/utils/tree')['getNodeList']
  const getParentNode: typeof import('../app/platform/utils/tree')['getParentNode']
  const getSidebarStatus: typeof import('../app/platform/utils/cache/local-storage')['getSidebarStatus']
  const getToken: typeof import('../app/platform/utils/cache/cookies')['getToken']
  const getTreeTitle: typeof import('../app/platform/utils/common')['getTreeTitle']
  const getValueArrarByKey: typeof import('../app/platform/utils/tree')['getValueArrarByKey']
  const getVisitedViews: typeof import('../app/platform/utils/cache/local-storage')['getVisitedViews']
  const h: typeof import('vue')['h']
  const inject: typeof import('vue')['inject']
  const is24H: typeof import('../app/platform/utils/validate')['is24H']
  const isArray: typeof import('../app/platform/utils/validate')['isArray']
  const isCheckPermission: typeof import('../app/platform/utils/permission')['isCheckPermission']
  const isChineseIdCard: typeof import('../app/platform/utils/validate')['isChineseIdCard']
  const isDomain: typeof import('../app/platform/utils/validate')['isDomain']
  const isEmail: typeof import('../app/platform/utils/validate')['isEmail']
  const isEmpty: typeof import('../app/platform/utils/validate')['isEmpty']
  const isExternal: typeof import('../app/platform/utils/validate')['isExternal']
  const isIPv4: typeof import('../app/platform/utils/validate')['isIPv4']
  const isLicensePlate: typeof import('../app/platform/utils/validate')['isLicensePlate']
  const isMAC: typeof import('../app/platform/utils/validate')['isMAC']
  const isPhoneNumber: typeof import('../app/platform/utils/validate')['isPhoneNumber']
  const isProxy: typeof import('vue')['isProxy']
  const isReactive: typeof import('vue')['isReactive']
  const isReadonly: typeof import('vue')['isReadonly']
  const isRef: typeof import('vue')['isRef']
  const isString: typeof import('../app/platform/utils/validate')['isString']
  const isUrl: typeof import('../app/platform/utils/validate')['isUrl']
  const isUrlPort: typeof import('../app/platform/utils/validate')['isUrlPort']
  const isVersion: typeof import('../app/platform/utils/validate')['isVersion']
  const letterAndNumber: typeof import('../app/platform/utils/regular')['letterAndNumber']
  const mapActions: typeof import('pinia')['mapActions']
  const mapGetters: typeof import('pinia')['mapGetters']
  const mapState: typeof import('pinia')['mapState']
  const mapStores: typeof import('pinia')['mapStores']
  const mapWritableState: typeof import('pinia')['mapWritableState']
  const markRaw: typeof import('vue')['markRaw']
  const matchPermissionBtnList: typeof import('../app/platform/utils/permission')['matchPermissionBtnList']
  const matchPermissionBtnListV2: typeof import('../app/platform/utils/permission')['matchPermissionBtnListV2']
  const nextTick: typeof import('vue')['nextTick']
  const number: typeof import('../app/platform/utils/regular')['number']
  const objectToFormData: typeof import('../app/platform/utils/objectToFromData')['objectToFormData']
  const onMounted: typeof import('vue')['onMounted']
  const onBeforeMount: typeof import('vue')['onBeforeMount']
  const onBeforeRouteLeave: typeof import('vue-router')['onBeforeRouteLeave']
  const onBeforeRouteUpdate: typeof import('vue-router')['onBeforeRouteUpdate']
  const onBeforeUnmount: typeof import('vue')['onBeforeUnmount']
  const onBeforeUpdate: typeof import('vue')['onBeforeUpdate']
  const onDeactivated: typeof import('vue')['onDeactivated']
  const onErrorCaptured: typeof import('vue')['onErrorCaptured']
  const onMounted: typeof import('vue')['onMounted']
  const onRenderTracked: typeof import('vue')['onRenderTracked']
  const onRenderTriggered: typeof import('vue')['onRenderTriggered']
  const onScopeDispose: typeof import('vue')['onScopeDispose']
  const onServerPrefetch: typeof import('vue')['onServerPrefetch']
  const onUnmounted: typeof import('vue')['onUnmounted']
  const onUpdated: typeof import('vue')['onUpdated']
  const onlyItemArr: typeof import('../app/platform/utils/tree')['onlyItemArr']
  const password: typeof import('../app/platform/utils/regular')['password']
  const phone: typeof import('../app/platform/utils/regular')['phone']
  const provide: typeof import('vue')['provide']
  const reactive: typeof import('vue')['reactive']
  const readonly: typeof import('vue')['readonly']
  const ref: typeof import('vue')['ref']
  const refreshTitle: typeof import('../app/platform/utils/common')['refreshTitle']
  const reg: typeof import('../app/platform/utils/regular')['reg']
  const removeConfigLayout: typeof import('../app/platform/utils/cache/local-storage')['removeConfigLayout']
  const removeToken: typeof import('../app/platform/utils/cache/cookies')['removeToken']
  const removeTreeItem: typeof import('../app/platform/utils/tree')['removeTreeItem']
  const request: typeof import('../app/platform/utils/service')['request']
  const resetConfigLayout: typeof import('../app/platform/utils/common')['resetConfigLayout']
  const resolveComponent: typeof import('vue')['resolveComponent']
  const secIdList: typeof import('../app/platform/utils/tree')['secIdList']
  const selectTreeNodeByNoRelevance: typeof import('../app/platform/utils/tree')['selectTreeNodeByNoRelevance']
  const setActivePinia: typeof import('pinia')['setActivePinia']
  const setActiveThemeName: typeof import('../app/platform/utils/cache/local-storage')['setActiveThemeName']
  const setCachedViews: typeof import('../app/platform/utils/cache/local-storage')['setCachedViews']
  const setConfigLayout: typeof import('../app/platform/utils/cache/local-storage')['setConfigLayout']
  const setCssVariableValue: typeof import('../app/platform/utils/common')['setCssVariableValue']
  const setMapStoreSuffix: typeof import('pinia')['setMapStoreSuffix']
  const setSidebarStatus: typeof import('../app/platform/utils/cache/local-storage')['setSidebarStatus']
  const setToken: typeof import('../app/platform/utils/cache/cookies')['setToken']
  const setVisitedViews: typeof import('../app/platform/utils/cache/local-storage')['setVisitedViews']
  const shallowReactive: typeof import('vue')['shallowReactive']
  const shallowReadonly: typeof import('vue')['shallowReadonly']
  const shallowRef: typeof import('vue')['shallowRef']
  const smallNumber: typeof import('../app/platform/utils/regular')['smallNumber']
  const sortArr: typeof import('../app/platform/utils/common')['sortArr']
  const storeToRefs: typeof import('pinia')['storeToRefs']
  const toRaw: typeof import('vue')['toRaw']
  const toRef: typeof import('vue')['toRef']
  const toRefs: typeof import('vue')['toRefs']
  const toValue: typeof import('vue')['toValue']
  const triggerRef: typeof import('vue')['triggerRef']
  const unref: typeof import('vue')['unref']
  const upwardAccumulationByOneKey: typeof import('../app/platform/utils/tree')['upwardAccumulationByOneKey']
  const useAttrs: typeof import('vue')['useAttrs']
  const useCssModule: typeof import('vue')['useCssModule']
  const useCssVars: typeof import('vue')['useCssVars']
  const useLink: typeof import('vue-router')['useLink']
  const useRoute: typeof import('vue-router')['useRoute']
  const useRouter: typeof import('vue-router')['useRouter']
  const useSlots: typeof import('vue')['useSlots']
  const validateCodeName: typeof import('../app/platform/utils/validate')['validateCodeName']
  const validateEmpty: typeof import('../app/platform/utils/validate')['validateEmpty']
  const validateLengthAndEmpty: typeof import('../app/platform/utils/validate')['validateLengthAndEmpty']
  const versionList: typeof import('../app/platform/utils/version')['versionList']
  const watch: typeof import('vue')['watch']
  const watchEffect: typeof import('vue')['watchEffect']
  const watchPostEffect: typeof import('vue')['watchPostEffect']
  const watchSyncEffect: typeof import('vue')['watchSyncEffect']
}
// for type re-export
declare global {
  // @ts-ignore
  export type { Component, ComponentPublicInstance, ComputedRef, ExtractDefaultPropTypes, ExtractPropTypes, ExtractPublicPropTypes, InjectionKey, PropType, Ref, VNode, WritableComputedRef } from 'vue'
  import('vue')
}
