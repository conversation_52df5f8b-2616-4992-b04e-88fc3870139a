<!--
//地图组建使用方法
<Amap :AMapData="AMapData"></Amap>

const AMapData = reactive({
	height:'300'
	center: [116.397428, 39.90923],//中🆕位置
	startPoint: [116.35, 39.89],//开始位置
	endPoint: [116.41, 39.92],//结束位置
	wayPoints: [
		[116.28, 39.92],
		[116.38, 39.92],
		[116.48, 39.92],
	]//途经位置
})

 -->
 <template>
	<div class="app-container" v-loading="loading">
		<div style="background-color: #fff">
			<div id="container" :style="{ height: props.AMapData.height + 'px' }" />
		</div>
	</div>
</template>

<script setup>
import AMapLoader from "@amap/amap-jsapi-loader"
import { defineProps } from "vue"

const mapConfig = {
	securityJsCode: "15e05f5b850d441d20b0bd119d4cc703",
	key: "6f76f9aa65af269a6941f5f837b3db92"
}

const IconConfig = {
	size: [25, 34],
	image:
		"https://a.amap.com/jsapi_demos/static/demo-center/icons/dir-marker.png",
	imageSize: [135, 40],
	imageOffset: [0, -3],
	imageO: { in: -10, out: -95, way: -53 },
	offset: [-13, -30]
}

const props = defineProps({
	AMapData: {
		type: Object,
		default: () => {}
	}
})
console.log("地图接收参数", props.AMapData)
const loading = ref(true)
const map = ref({})
const markers = reactive([]) // 用于存储标记实例的数组

const createMarker = (longitude, latitude, execType) => {
	const icon = new window.AMap.Icon({
		size: new window.AMap.Size(IconConfig.size[0], IconConfig.size[1]),
		image: IconConfig.image,
		imageSize: new AMap.Size(IconConfig.imageSize[0], IconConfig.imageSize[1]),
		imageOffset: new AMap.Pixel(
			IconConfig.imageO[execType] || IconConfig.imageO.way,
			IconConfig.imageOffset[1]
		)
	})
	// 将 icon 传入 marker
	return new window.AMap.Marker({
		position: new AMap.LngLat(longitude, latitude),
		icon,
		offset: new AMap.Pixel(IconConfig.offset[0], IconConfig.offset[1])
	})
}

const initMap = () => {
	window._AMapSecurityConfig = {
		securityJsCode: mapConfig.securityJsCode
	}

	AMapLoader.load({
		key: mapConfig.key, // 申请好的Web端开发者Key，首次调用 load 时必填
		version: "2.0" // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
	})
		.then((AMap) => {
			loading.value = false
			map.value = new AMap.Map("container", {
				//设置地图容器id
				zoom: 13, //初始化地图级别
				center: [props.AMapData.center[0], props.AMapData.center[1]] //初始化地图中心点位置
			})

			// 初始化-途径-标记的方法
			props.AMapData.wayPoints.forEach((item) => {
				const marker = createMarker(item[0], item[1], "way")
				markers.push(marker) // 将标记实例添加到数组中
			})
			// 开始点
			props.AMapData.startPoint.forEach((item) => {
				const marker = createMarker(item[0], item[1], "in")
				markers.push(marker) // 将标记实例添加到数组中
			})
			//结束点
			props.AMapData.endPoint.forEach((item) => {
				const marker = createMarker(item[0], item[1], "out")
				markers.push(marker) // 将标记实例添加到数组中
			})
			// 将 markers 添加到地图
			map.value.add(markers)
			/**
			 *
			 * 将缩略图控件添加到地图实例中
			 *
			 * ***/
			map.value.plugin(["AMap.Scale", "AMap.ToolBar"], () => {
				map.value.addControl(
					new AMap.ToolBar({
						visible: true,
						position: {
							top: "10px",
							right: "40px"
						}
					})
				)
				map.value.addControl(
					new AMap.Scale({
						visible: true,
						position: {
							top: "250px",
							right: "40px"
						}
					})
				)
			})
		})
		.catch((e) => {
			console.log(e)
		})
}

onMounted(() => {
	//调用初始化方法
	initMap()
})

onUnmounted(() => {
	map.value?.destroy()
})
</script>

<style scoped lang="scss">
.app-container {
	width: 100%;
	height: 100%;
}

#container {
	padding: 0px;
	margin: 0px;
	width: 100%;
	min-height: 100px;
	/* height: 100vh; */
}
/* 隐藏高德logo  */
:deep(.amap-logo) {
	display: none !important;
}
/* 隐藏高德版权  */
:deep(.amap-copyright) {
	display: none !important;
}
</style>
