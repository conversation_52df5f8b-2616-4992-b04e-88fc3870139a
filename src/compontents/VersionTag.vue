<template>
	<span v-if="props.value == '-1'">---</span>
	<el-tag
		v-else
		class="f12 release"
		:class="preBpmStsColor[props.bpmSts]?.style"
	>
		{{ props.label ? props.label : props.value }}
	</el-tag>
</template>
<script setup lang="ts">
interface Props {
	value: string
	label?: string
	bpmSts: number
}
const preBpmSts = ["草稿", "审核中", "已发布", "已废弃"]

const preBpmStsColor = [
	{ name: "草稿", style: "release-draft" },
	{ name: "审核中", style: "release-examine" },
	{ name: "已发布", style: "release-publish" },
	{ name: "已废弃", style: "release-waste" }
]
const props = defineProps<Props>()

defineOptions({
	name: "VersionTag"
})
</script>
