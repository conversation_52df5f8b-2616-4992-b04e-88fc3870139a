<script lang="ts" setup>
import { Close } from "@element-plus/icons-vue"

interface Props {
	size: string | number
	drawer: boolean
	destroyOnClose?: boolean //是否在关闭时销毁内部元素,
	showClose?: boolean //是否显示关闭按钮
	title: any
	loading: boolean // 抽屉loading状态
}

const props = withDefaults(defineProps<Props>(), {
	destroyOnClose: false,
	showClose: true,
	title: {
		name: ["标题"],
		icon: ["fas", "square-share-nodes"]
	},
	loading: false,
})
const size = toRef(props, "size")
const showClose = toRef(props, "showClose")
// 表格定义
const emit = defineEmits(["update:drawer", "close", "updateTab"])

const drawer = computed({
	get() {
		return props.drawer
	},
	set(value) {
		emit("update:drawer", value)
	}
})

const onClose = () => {
	drawer.value = false
	emit("close")
}

//标签切换
function onTabChange(tabIndex: number) {
	emit("updateTab", tabIndex)
}
const tabList = ref([])
const paramsFn =(data:any)=>{
	tabList.value=data
}
defineExpose({
	paramsFn
})
</script>

<template>
	<el-drawer
		v-model="drawer"
		:with-header="false"
		:size="size"
		:append-to-body="true"
		:destroy-on-close="destroyOnClose"
		@close="onClose"
	>
		<!-- 标题 -->
		<Title :title="title" class="title">
			<Tabs @onTabChange="onTabChange" class="abs-tab" :tabs="tabList" />
		</Title>
		<!-- 关闭按钮 -->
		<button
			v-if="showClose"
			class="el-drawer__close-btn"
			type="button"
			@click="onClose"
		>
			<el-icon>
				<Close />
			</el-icon>
		</button>
		<!-- 内容 -->
		<div class="drawer-content-wrapper" v-loading="loading">
			<el-scrollbar>
				<slot name="content" />
			</el-scrollbar>
		</div>
		<!-- 底部操作按钮 -->
		<div class="drawer-btn-wrapper">
			<slot name="footer" />
		</div>
	</el-drawer>
</template>

<style lang="scss" scoped>
.drawer-content-wrapper {
	padding: 10px 5px 10px 10px;
	height: calc(100% - 73px);
	:deep(.el-form-wrapper) {
		padding-top: 0;
	}
	:deep(.pitaya-table) {
		margin-top: 0;
		padding: 0;
	}
	.el-scrollbar {
		padding-right: 5px;
	}
}

.drawer-btn-wrapper {
	width: calc(100% - 20px);
	background-color: #fff;
	position: absolute;
	bottom: 0;
	left: 10px;
	// min-height: 50px;
	padding: 10px 0;
	display: flex;
	align-items: center;
	justify-content: flex-end;
	padding-right: 10px;
	z-index: 10;
	border-top: 1px solid var(--pitaya-border-color);
}

.el-drawer__close-btn {
	position: absolute;
	z-index: 999;
	top: 10px;
	right: 10px;
	display: inline-flex;
	border: none;
	cursor: pointer;
	font-size: var(--el-font-size-extra-large);
	color: #ccc;
	background-color: transparent;
	outline: none;
}

:deep(.el-scrollbar__bar) {
	&.is-horizontal {
		display: none;
	}
}

// 树
:deep(.tree-search) {
	margin-top: 0 !important;
}

:deep(.pitaya-tree-container) {
	padding: 0 !important;
}
.title {
	justify-content: start;
}
.abs-tab {
	margin-left: 40px;
}
</style>
