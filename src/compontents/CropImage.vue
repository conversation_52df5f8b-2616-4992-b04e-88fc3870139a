<script lang="ts" setup>
import "cropperjs/dist/cropper.css"
import Cropper from "cropperjs"
import { commonUploadFile } from "@/app/platform/api/system/processDesign"



const emit = defineEmits(["cropImageFile", "prev"])


interface Props {
	fileList: any[]
	data:{}
}

const props = withDefaults(defineProps<Props>(), {
	fileList: () => [],
	data:()=>{}
})

const fileList = toRef(props, "fileList")
const currentIndex = toRef(props, "currentIndex")
const dialogVisible = ref(false)

// 打开预览窗口
const dataFile =ref({})
const open = (file, fileList) => {
	dataFile.value=file
	dialogVisible.value = true
	handleChange(file)
}

// 关闭预览窗口
const close = () => {
	dialogVisible.value = false
}

function E(selector) {
	return document.querySelector(selector)
}

const imageUrl = ref("")
const cropper = ref(null)
const croppedCanvas = ref(null)

// 处理文件变化
const handleChange = (file) => {
	const reader = new FileReader()
	reader.onload = (e) => {
		imageUrl.value = e.target.result
		setTimeout(() => {
			setupCropper()
		}, 1000)
	}
	reader.readAsDataURL(file.raw)
}
// 初始化 Cropper
const cropAspectRatio = ref("1") // 默认1:1
const setupCropper = () => {
	if (cropper.value) {
		cropper.value.destroy()
	}
	const image = document.querySelector("#image")
	const aspectRatio =cropAspectRatio.value
		cropper.value = new Cropper(image, {
			aspectRatio:aspectRatio,
			viewMode: 1,
			background: false,
			autoCropArea: 1,
			dragMode: "move"
		})
}

// 重置 Cropper
const resetCropper = (data) => {
	cropAspectRatio.value = data
	if (cropper.value) {
		cropper.value
			.reset()
			.clear()
			.setAspectRatio(parseFloat(cropAspectRatio.value))
		nextTick(() => {
			setupCropper()
		})
	}
}

// 裁剪图片
const cropImage = () => {
	if (cropper.value) {
		const formData = new FormData()
		const canvas = cropper.value
			.getCroppedCanvas({ width: 160, height: 160 })
			.toBlob(
				(blob) => {
					// 这里可以处理裁剪后的图片，比如上传等
					const name = dataFile.value.name
					formData.append(
						"file",
						new File(
							[blob],
							name,
							{ type: "text/plain" }
						)
					)
					console.log('ssdfsdfasfas',props.data)
					formData.append("businessType", props.data.businessType)
					commonUploadFile(formData).then((res) => {
						emit("cropImageFile", res)
						dialogVisible.value = false
					})
				},
				"image/jpeg",
				0.8
			)
	}
}

defineExpose({
	open,
	close
})
</script>
<template>
	<!-- 媒体预览 -->
	<el-dialog v-model="dialogVisible" :show-close="false" class="media-preview">
		<template #header>
			<div class="my-header">
				<el-icon class="close-icon" @click="close"><Close /></el-icon>
			</div>
		</template>
		<!-- 图片 -->
		<img
			v-if="imageUrl"
			id="image"
			class="preview-warapper"
			ref="image"
			:src="imageUrl"
			alt="图片预览"
		/>
		<div class="preview-btns">
			<el-button type="primary" @click="resetCropper('1')">裁剪 1:1 </el-button
			><el-button type="primary" @click="resetCropper('1.33')"
				>裁剪 4:3 </el-button
			><el-button type="primary" @click="resetCropper('1.77')"
				>裁剪 16:9 </el-button
			><el-button type="primary" @click="resetCropper('1.60')"
				>裁剪 16:10</el-button
			>
		</div>
		<div class="preview-btns">
			<el-button type="primary" @click="cropImage()"
				>确定</el-button
			>
		</div>
	</el-dialog>
</template>

<style lang="scss" scoped>
.preview-warapper {
	 width: 50%;
}

.preview-btns {
	display: flex;
	justify-content: center;
	margin-top: 10px;
}

.my-header {
	text-align: right;
	cursor: pointer;
	.close-icon {
		font-size: 24px;
		color: #999;
		border-radius: 3px;
		height: 50px;
		width: 50px;
		margin-top: 10px;
		&:hover {
			box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
		}
	}
}
</style>
<style>
.media-preview{
	width: 50%;
}
.cropper-container {
	/* 设置你的样式 */
	width: 100% !important; 
}
</style>
