<script lang="ts" setup>
import { useUserStore } from "@/app/platform/store/modules/user"
import { baseUserApi } from "@/app/platform/api/system/baseUser"
interface Props {
	title?: string // 标题
	selectedUsers: any[] // 回显用户数组
	queryArrList?: any[] // 自定义查询条件配置
}

const props = withDefaults(defineProps<Props>(), {
	title: "选择用户",
	selectedUsers: () => [],
	queryArrList: undefined,
})

const emit = defineEmits(["onSubmit"])

const { title, selectedUsers } = toRefs(props)
const userStore = useUserStore()
const { userInfo } = storeToRefs(userStore) // 登录用户信息
const drawerState = ref<boolean>(false)
const drawerLoading = ref<boolean>(false)
const drawerTitle = reactive({
	name: ["选择用户"],
	icon: ["fas", "square-share-nodes"]
})
const queryArrList = ref<any[]>(props.queryArrList||[
	{
		name: "手机号码",
		key: "phone",
		type: "input",
		enableFuzzy: false,
		placeholder: "请输入查询关键字"
	},
	{
		name: "用户姓名",
		key: "realname",
		type: "input",
		enableFuzzy: false,
		placeholder: "请输入查询关键字"
	},
	{
		name: "员工工号",
		key: "employeeNumber",
		type: "input",
		enableFuzzy: false,
		placeholder: "请输入查询关键字"
	}
])
const btnList = ref([
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "保存",
		icon: ["fas", "floppy-disk"],
		disabled: false
	}
])
const tableRef = ref()
const tableData = ref<any[]>([])
const dataTotal = ref<number>(0)
const tableLoading = ref<boolean>(false)
const tableColumns: TableColumnType[] = [
	{
		label: "用户账号",
		prop: "username",
		width: 120,
		fixed: "left",
		align: "left"
	},
	{ label: "用户姓名", prop: "realname", width: 90 },
	{ label: "手机号码", prop: "phone", width: 120 },
	{ label: "员工工号", prop: "employeeNumber", width: 130, align: "left" },
	{ label: "性别", prop: "sex", width: 60, needSlot: true },
	// { label: "企业微信", prop: "enterpriseWeChat", width: 85, align: "left" },
	{ label: "公司", prop: "companyName" },
	{ label: "部门", prop: "orgAllName", align: "left" },
	{ label: "角色", prop: "baseRoleLists", align: "left", needSlot: true},
	{ label: "岗位", prop: "station", fixed: "right", align: "left" }
	// { label: "角色信息", prop: "baseRoleNum", width: 95, needSlot: true },
	// {
	// 	label: "工作班组",
	// 	prop: "organizationNum",
	// 	width: 95,
	// 	needSlot: true,
	// 	fixed: "right"
	// }
	// { label: "状态", prop: "enabled", width: 85, needSlot: true },
	// {
	// 	label: "操作",
	// 	prop: "operations",
	// 	width: 180,
	// 	needSlot: true,
	// 	fixed: "right"
	// }
]
// 表格勾选暂存
const selectedRows = ref<any[]>([])
// 查询参数
const queryParams = reactive({
	enabled: true, // 是否启用
	companyId: userInfo.value.companyId, // 登陆用户公司id
	phone: null,
	realname: null,
	employeeNumber: null,
	currentPage: 1,
	pageSize: 20
})

// 回调：表格条件查询
const getQueryData = (queryData: any) => {
	// 动态设置查询参数
	queryArrList.value.forEach((item: { key: string }) => {
		if (queryData[item.key] !== undefined) {
			queryParams[item.key as keyof typeof queryParams] = queryData[item.key]
		}
	})
	queryParams.currentPage = 1
	tableRef.value.resetCurrentPage()
	getUserList(true)
}

// 回调：表格分页查询
const onCurrentPageChange = (pageData: any) => {
	queryParams.currentPage = pageData.currentPage
	queryParams.pageSize = pageData.pageSize
	getUserList()
}

// 回调：表格勾选
const getSelectedTableData = (rowList: any[]) => {
	selectedRows.value = rowList
}

// 回调：底部按钮操作
const onBtnClick = (btnName?: string | undefined) => {
	if (btnName === "保存") {
		if (!selectedRows.value.length) {
			ElMessage.warning("请选择用户")
			return false
		}
		// 传递所选用户信息
		emit("onSubmit", selectedRows.value[0])
		drawerState.value = false
	} else {
		drawerState.value = false
	}
}

// 加载用户列表
const getUserList = (isClear?: boolean) => {
	tableLoading.value = true
	baseUserApi
		.getBaseUserListApi({
			...queryParams,
			companyId: queryParams.companyId || userInfo.value.companyId
		})
		.then((res: anyKey) => {
			if (res.rows && res.rows.length > 0) {
				const { rows, records } = res
				tableData.value = rows
				dataTotal.value = records
				// 清除勾选
				if (isClear) {
					tableRef.value.clearSelectedTableData()
				}
				// 回显已选用户
				if (selectedUsers.value.length) {
					const ids: number[] = []
					tableData.value.forEach((item) => {
						if (selectedUsers.value.includes(item.username)) {
							ids.push(item.id)
						}
					})
					refreshTableSelectedItem(tableData.value, ids, selectedRows.value)
				}
			} else {
				tableData.value = []
				dataTotal.value = 0
			}
		})
		.finally(() => {
			tableLoading.value = false
		})
}

// 打开
const open = (data:any) => {
	queryParams.currentPage = 1
	queryParams.pageSize = 20
	getUserList()
	if(data.queryList && data.queryList.length) queryArrList.value = data.queryList
	drawerState.value = true
}

// 回调：抽屉关闭
const onClose = () => {
	selectedRows.value = []
	// 清空内容
	queryParams.phone = ''
	queryParams.realname =''
	queryParams.employeeNumber = ''
	queryArrList.value.forEach((item: { key: string }) => {
		queryParams[item.key as keyof typeof queryParams] = ''
	})
}

// 回显表格选中状态
const refreshTableSelectedItem = (
	tableData: any[],
	keys: any[],
	selectedRows: any[]
) => {
	if (keys && keys.length > 0) {
		tableData.forEach((row) => {
			keys.forEach((key) => {
				if (row.id === key) {
					selectedRows.push(row)
				}
			})
		})
	} else {
		selectedRows = []
	}
}

/**
 * 钩子函数
 */

watchEffect(() => {
	// 改变标题
	if (title.value) {
		drawerTitle.name[0] = title.value
	}
})

defineExpose({
	open
})
</script>
<template>
	<NewDrawer
		size="1100"
		:destroyOnClose="true"
		v-model:drawer="drawerState"
		:title="drawerTitle"
		:loading="drawerLoading"
		@close="onClose"
	>
		<template #content>
			<Query :queryArrList="queryArrList" @getQueryData="getQueryData" />
			<PitayaTable
				ref="tableRef"
				:columns="tableColumns"
				:table-data="tableData"
				:need-index="true"
				:single-select="true"
				:need-selection="true"
				:need-pagination="true"
				:total="dataTotal"
				:table-loading="tableLoading"
				:selectedTableData="selectedRows"
				:max-height="672"
				@on-current-page-change="onCurrentPageChange"
				@onSelectionChange="getSelectedTableData"
			>
				<template #sex="{ rowData }">
					<span>{{ rowData.sex == 1 ? "男" : "女" }}</span>
				</template>
				<template #baseRoleLists="{ rowData }">
					<span>{{ (rowData.baseRoleLists || []).map(l => l.roleName).join('，') }}</span>
				</template>
			</PitayaTable>
		</template>
		<template #footer>
			<ButtonList
				:is-not-radius="true"
				:button="btnList"
				@on-btn-click="onBtnClick"
			/>
		</template>
	</NewDrawer>
</template>

<style lang="scss" scoped></style>
<style lang="scss" scoped></style>
