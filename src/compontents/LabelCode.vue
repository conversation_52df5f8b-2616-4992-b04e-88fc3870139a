<template>
	<div class="label-body">
		<!-- 位置码 -->
		<template v-if="props.label?.type == 1">
			<div class="loction loction-left-part">
				<!-- 北京地铁logo -->
				<img src="~@/icons/svg/logo.svg" alt="logo" class="label-logo" />
				<!-- 公司名称 -->
				<span class="label-comp" v-if="getLength(props.label?.companyName) == 0"
					>&emsp;</span
				>
				<span
					v-else-if="getLength(props.label?.companyName) > 3"
					:class="`label-comp label-comp-l-${getLength(
						props.label?.companyName
					)}`"
				>
					{{
						props.label?.companyName.substr(
							0,
							getLength(props.label?.companyName)
						)
					}}
				</span>
				<span v-else>
					<span
						:class="`label-comp label-comp-l-${getLength(
							props.label?.companyName
						)}`"
					>
						{{
							props.label?.companyName.substr(
								0,
								getLength(props.label?.companyName) - 1
							)
						}}
					</span>
					<span class="label-comp">
						{{
							props.label?.companyName.substr(
								getLength(props.label?.companyName) - 1,
								1
							)
						}}
					</span>
				</span>
			</div>
			<!-- 标签码 -->
			<div class="loction loction-right-part">
				<div class="label-type">
					<img
						class="type-img"
						alt="位置"
						src="@/assets/images/qrcode-loc.png"
					/>
					<span class="type-text">位置</span>
				</div>
				<vue-qrcode :value="props.label?.qrCode" class="label-qrcode" />
			</div>
		</template>
		<!-- 设备码 -->
		<template v-else>
			<!-- 标签码 -->
			<div class="device device-left-part">
				<div class="label-type">
					<img
						class="type-img"
						alt="设备"
						src="@/assets/images/qrcode-eq.png"
					/>
					<span class="type-text">设备</span>
				</div>
				<vue-qrcode :value="props.label?.qrCode" class="label-qrcode" />
			</div>
			<div class="device device-right-part">
				<!-- 北京地铁logo -->
				<img src="~@/icons/svg/logo.svg" alt="logo" class="label-logo" />
				<!-- 公司名称 -->
				<span class="label-comp" v-if="getLength(props.label?.companyName) == 0"
					>&emsp;</span
				>
				<span
					v-else-if="getLength(props.label?.companyName) > 3"
					:class="`label-comp label-comp-l-${getLength(
						props.label?.companyName
					)}`"
				>
					{{
						props.label?.companyName.substr(
							0,
							getLength(props.label?.companyName)
						)
					}}
				</span>
				<span v-else>
					<span
						:class="`label-comp label-comp-l-${getLength(
							props.label?.companyName
						)}`"
					>
						{{
							props.label?.companyName.substr(
								0,
								getLength(props.label?.companyName) - 1
							)
						}}
					</span>
					<span class="label-comp">
						{{
							props.label?.companyName.substr(
								getLength(props.label?.companyName) - 1,
								1
							)
						}}
					</span>
				</span>
			</div>
		</template>
	</div>
</template>

<script setup lang="ts">
import VueQrcode from "@chenfengyuan/vue-qrcode"
const props = defineProps(["label"])
function getLength(text: string) {
	if (!text) {
		return 0
	} else if (text.length > 7) {
		return 7
	} else {
		return text.length
	}
}
</script>

<style lang="scss" scoped>
// 标签码卡片样式
.label-body {
	width: 80mm;
	max-width: 80mm;
	height: 40mm;
	max-height: 40mm;
	display: flex;
	height: max-content;
	width: max-content;
	align-items: center;

	.loction,
	.device {
		.label-logo {
			width: 25mm;
			height: 6.3mm;
			margin-bottom: 4.5mm;
		}
		.label-comp {
			font-family: 思源黑;
			font-size: 14pt;
			font-weight: bold;
		}
		.label-type {
			.type-img {
				width: 4.4mm;
				height: 4.4mm;
				transform-origin: center center;
				transform: scale(1.44);
			}
			.type-text {
				font-family: 思源黑;
				font-size: 12pt;
				font-weight: bold;
			}
		}
		.label-qrcode {
			width: 25mm !important;
			height: 25mm !important;
			border: 1px solid #000;
		}
	}

	.loction-left-part,
	.device-right-part {
		display: flex;
		flex-direction: column;
		width: max-content;
		height: max-content;
		padding: 12mm 6mm 12mm 8mm;
	}
	.device-right-part {
		padding: 12mm 8mm 12mm 6mm;
	}
	.loction-right-part,
	.device-left-part {
		display: flex;
		width: max-content;
		height: max-content;
		padding-right: 8mm;
		.label-type {
			background-color: #000;
			height: 25mm;
			width: 32px;
			padding: 0 2mm;
			.type-img {
				display: block;
				margin-top: 5mm;
			}
			.type-text {
				// 文本竖向排列
				writing-mode: vertical-rl;
				color: #fff;
				display: block;
				position: absolute;
				bottom: 5mm;
				left: 50%;
				transform: translateX(-50%);
			}
		}
	}
	.device-left-part {
		padding-left: 8mm;
		padding-right: 0;
	}
}

// .label-body {
// 	width: 80mm;
// 	max-width: 80mm;
// 	height: 40mm;
// 	max-height: 40mm;
// 	// padding: 7.5mm 8mm;
// 	display: flex;
// 	align-items: center;
// 	flex-direction: row;
// 	.label-body-left {
// 		padding-right: 6mm;
// 		display: flex;
// 		flex-direction: column;
// 	}
// 	.label-body-right {
// 		display: flex;
// 	}
// 	.label-logo {
// 		margin-bottom: 4.5mm;
// 	}
// 	.label-comp {
// 		display: inline-block;
// 		width: 25mm;
// 		text-align: center;
// 		word-break: keep-all;
// 		white-space: nowrap;
// 		word-wrap: normal;
// 		font-family: 思源黑;
// 		font-weight: bold;
// 		font-size: 14pt !important;
// 		color: #000;
// 		word-break: keep-all;
// 	}
// 	.label-comp-l-2 {
// 		letter-spacing: 42pt;
// 	}
// 	.label-comp-l-3 {
// 		letter-spacing: 14pt;
// 	}
// 	.label-comp-l-4 {
// 		letter-spacing: 5pt;
// 	}
// 	.label-comp-l-6 {
// 		font-size: 12pt !important;
// 	}
// 	.label-comp-l-7 {
// 		font-size: 12pt !important;
// 	}
// 	.label-type {
// 		display: flex;
// 		flex-direction: column;
// 		justify-content: space-between;
// 		width: 8mm;
// 		height: 25mm;
// 		padding: 5mm 2mm;
// 		background-color: #000;
// 		color: #fff;
// 		font-family: 思源黑;
// 		font-weight: bold;
// 		font-size: 12pt !important;
// 		line-height: 13pt;
// 		-webkit-print-color-adjust: exact;
// 		span {
// 			writing-mode: vertical-lr;
// 			letter-spacing: 1pt;
// 		}
// 	}
// 	.label-type-img {
// 		width: 4.4mm;
// 		height: 4.4mm;
// 		position: relative;
// 		transform-origin: center center;
// 		transform: scale(1.44);
// 	}
// 	.label-qrcode {
// 		border: 1px solid #000;
// 		width: 25mm !important;
// 		height: 25mm !important;
// 	}
// }
// .label-body-reverse {
// 	flex-direction: row-reverse;
// 	justify-content: flex-end;
// 	.label-body-left {
// 		padding-right: 0;
// 		padding-left: 6mm;
// 	}
// }
</style>
