<template>
	<div>
		<el-rate
			v-model="value"
			:size="size"
			:colors="colors"
			:allow-half="isAllowHalf"
			:disabled="disabled"
			score-template="{value} points"
			clearable
			@change="onChange"
		/>
	</div>
</template>

<script setup lang="ts">
interface Props {
	value?: number
	size?: string
	colors:	any[]
	isAllowHalf?: boolean
	disabled: boolean
}

const props = withDefaults(defineProps<Props>(), {
	value: null,
	size: "small", //small large
	colors: () => [],
	isAllowHalf: true,
})

const { value, size, colors, isAllowHalf,  disabled } = toRefs(props)
const emit = defineEmits([
	"onRateChange",
])

const onChange=(e)=>{
	emit("onRateChange", e)
}
</script>

<style lang="scss" scoped>
.el-rate .el-rate__icon {
	color: #61ddaa;
}
</style>
