<!-- 日历组建 -->
<!--  -->
<template>
	<div class="content_box">
		<FullCalendar ref="fullCalendar" :options="calendarOptions">
			<template v-slot:dayHeaderContent="arg">{{ arg.text }}</template>
			<template v-slot:dayCellContent="arg">
				<a class="fc-daygrid-day-number">{{ arg.dayNumberText }}</a>
				<a title="sdfas" class="statistics">日期提示</a>
			</template>
			<template v-slot:eventContent="arg">
				<a-card class="inst-card">
					<p :title="arg.event.title">{{ arg.event.title }}</p>
					<p
						:title="
							`计划号：${arg.event.extendedProps.sts}\n` +
							`计划名称：${arg.event.extendedProps.sts}\n` +
							(arg.event.extendedProps.sts
								? `标准工作：${arg.event.extendedProps.sts}\n`
								: '') +
							(arg.event.extendedProps.sts
								? `状  态：${arg.event.extendedProps.sts}`
								: '')
						"
					>
						工作内容
					</p>
				</a-card>
			</template>
		</FullCalendar>
	</div>
</template>

<script lang="ts" setup>
import "@fullcalendar/core/locales/zh-cn"
import FullCalendar from "@fullcalendar/vue3"
import dayGridPlugin from "@fullcalendar/daygrid"
import timeGridPlugin from "@fullcalendar/timegrid"
import interactionPlugin from "@fullcalendar/interaction"
import { getInfoTableApi } from "@/app/platform/api/location"
// import listPlugin from "@fullcalendar/list";

const props = defineProps({
	events: {
		type: Array,
		default: () => []
	}
})

const emit = defineEmits(["onDateClick"])

const calendarOptions = reactive({
	allDay: true,
	allDayText: "",
	contentHeight: "auto",
	dayMaxEventRows: true, // 单元格最多显示日程数
	handleWindowResize: true,
	windowResizeDelay: 500,
	themeSystem: "bootstrap5",
	locale: "zh-cn", // 切换语言，当前为中文
	unselectAuto: false, //当点击页⾯⽇历以外的位置时，是否⾃动取消当前的选中状态。false是不取消
	dayMaxEvents: true, // allow "more" link when too many events,只能选中或拖动一次
	displayEventTime: false, // 是否显示时
	// datesSet: handleCalendarChange,
	customButtons: {
		myCustomButton: {
			text: "按钮",
			click: function () {
				alert("clicked the custom button!")
			}
		}
	},
	// 按钮文字
	buttonText: {
		today: "回到今天",
		month: "月",
		week: "周",
		day: "日"
	},
	// 按钮提示
	buttonHints: {
		next: "下一$0",
		prev: "上一$0",
		today: "回到本$0"
	},
	viewHint: "$0视图",
	weekNumberCalculation: "ISO",
	initialView: "dayGridWeek", // 初始化展示周
	plugins: [dayGridPlugin, timeGridPlugin, interactionPlugin],
	headerToolbar: {
		left: "dayGridDay,dayGridWeek,dayGridMonth",
		center: "title",
		right: "prev,today,next"
	},
	color: "yellow", // an option!
	textColor: "black", // an option!
	events: [], //事件事件+文本
	// eventColor: "#378006", //事件背景颜色
	eventDisplay: "block",
	views: {
		month: {
			dayMaxEventRows: 4,
			dayMaxEvents: 2
		},
		week: {
			dayMaxEventRows: 2,
			dayMaxEvents: 2
		},
		day: {
			dayMaxEventRows: 3,
			dayMaxEvents: 2
		}
	},
	moreLinkContent: function (args) {
		return "+" + args.num + " 更多"
	},

	eventClick: (info: {
		event: { title: string }
		el: { style: { borderColor: string } }
	}) => {
		eventClick(info)
	},
	dateClick: (info: { dateStr: string }) => {
		onDateClick(info)
	},
	editable: true
})

onMounted(() => {
	console.log("calendarOptions", calendarOptions)
	calendarOptions.events = props.events
})

const onDateClick=(info)=>{
	alert("Clicked onssss: " + info.dateStr)
	emit("onDateClick", info)

}
const onEventClick=(info)=>{
	alert("Event: " + info.event.title)
	info.el.style.borderColor = "red"
	emit("onEventClick", info)

}

</script>
<style lang="scss" scoped>
:deep(.ant-table colgroup > col.ant-table-selection-col) {
	width: 40px;
}

:deep(.ant-row .ant-form-item) {
	margin-bottom: 0;
}

:deep(.ant-form-item-label),
:deep(.ant-form-item-control){
	line-height: 35px;
}

:deep(.ant-card-body) {
	padding: 0;
}

/* ************* 日历样式重构 **************** */
// 按钮样式贴近antdv  颜色贴近主题
:deep(.fc .fc-button-primary) {
	color: rgba(0, 0, 0, 0.65) !important;
	background-color: #fff !important;
	border: 1px solid #d9d9d9 !important;
	outline: none;
	transition: all 0.3s;
	text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.12);
	box-shadow: 0 2px 0 rgba(0, 0, 0, 0.05);
	height: 24px;
	padding: 0 7px;
	font-size: 12px;
	border-radius: 4px;

	// 激活状态
	&:active {
		color: #fff !important;
		background: #096dd9 !important;
		border-color: #096dd9 !important;
	}

	// 鼠标滑过、聚焦
	&:hover,
	&:focus {
		color: #fff !important;
		background: #40a9ff !important;
		border-color: #40a9ff !important;
		box-shadow: inherit !important;
	}
}

// 活动按钮样式
:deep(.fc .fc-button-active) {
	color: #fff !important;
	background: #1890ff !important;
	border-color: #1890ff !important;
}

// 禁用按钮样式
:deep(.fc-button[disabled]),
:deep(.fc-button[disabled]:hover) {
	color: rgba(0, 0, 0, 0.25) !important;
	background-color: #f5f5f5 !important;
	border-color: #d9d9d9 !important;
	text-shadow: none !important;
	box-shadow: none !important;
}

// 单元格 鼠标滑过效果
:deep(.fc td[role="gridcell"]) {
	transition: all 0.3s;
	&:hover {
		background: #e6f7ff;

		.statistics {
			opacity: 1;
		}
	}
}

// 单元格 当天 样式调整

:deep(.fc .fc-day-today) {
	background: #e6f7ff !important;
	border-top: solid 1px #1890ff !important;
}

:deep(.fc .fc-daygrid-day.fc-day-today > div) {
	background: #e6f7ff !important;
	border-top: solid 1px #1890ff !important;
}
.fc .fc-daygrid-day.fc-day-today > div {
	background: #e6f7ff !important;
	border-top: solid 1px #1890ff !important;
}

// 单元格 头部 样式 包括统计数值
:deep(.fc-daygrid-day-top) {
	span {
		width: 100%;
		text-align: left;
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.statistics {
		color: #666;
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
		opacity: 0;
		transition: all 0.3s;
	}
}

/* 计划实例卡片样式 */
.inst-card {
	cursor: move;

	p {
		margin: 0;
		overflow: hidden;
		text-overflow: ellipsis;
	}
}

.inst-color-wait_dispatch {
	background-color: #fff;
	// border: 1px solid #d9d9d9;
}

.inst-color-dispatched {
	background: #f6ffed;
	border-color: #b7eb8f;
}

.inst-color-cancel {
	background: #f5f5f5;
	border-color: #dcdbdb;
}

.inst-color-work_cancel .inst-color-overdue {
	background: #fff1f0;
	border-color: #ffa39e;
}

.inst-color-finish {
	background-color: #e6f7ff;
	border: #91d5ff;
}

.inst-color-wait_apply_point {
	background: #fff7e6;
	border-color: #ffd591;
}

.inst-color-plan_clash {
	background: #f9f0ff;
	border-color: #d3adf7;
}

:deep(.ant-radio-group) {
	display: flex;
}

.radio-card {
	height: 4px;
}

:deep(.fc .fc-toolbar.fc-header-toolbar) {
	margin: 6px 0;
}

:deep(.fc .fc-toolbar-title) {
	font-size: 12px;
}

* {
	font-size: 12px !important;
}
// :deep(.fc-h-event .fc-event-main) {
// 	color: red;
// }
</style>
