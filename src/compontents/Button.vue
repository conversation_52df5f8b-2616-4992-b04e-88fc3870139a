<!--****
**  虚线框按钮
**  引用方法
**  1: import Button from "@/compontents/Button.vue"
**  2: title：文本名称
**  3：icon ：icon 名称
**  4：width ：宽度
**  5: @onBtnClick=""
**
**	<Button title="新建版本" @onBtnClick=""></Button>
**
**** -->
<template>
	<div>
		<div class="dashed-box" :class="disabled ? 'disabled btn-isDisabled' : ''" @click="onSubmit(e)" :style="{ width: width }">
			<slot name="icon">
				<font-awesome-icon
					:icon="['fas', icon]"
					style="color: var(--pitaya-btn-background)"
				/>
			</slot>
			<span class="ml10"> {{ title }}</span>
		</div>
	</div>
</template>
<script setup lang="ts">
import { defineProps } from "vue"

interface Props {
	title?: string
	icon?: string
	width?: string
	disabled?: boolean
}

const props = withDefaults(defineProps<Props>(), {
	title: () => "",
	icon: () => "circle-plus",
	width: () => "100%",
	disabled: false
})

const { title, icon } = toRefs(props)

const emit = defineEmits<{
	(e: "onBtnClick", btnName: string | undefined): void
}>()

const onSubmit = (e: string) => {
	if(props.disabled) return;
	emit("onBtnClick", e)
}
</script>
<style lang="scss" scoped>
.dashed-box {
	height: 40px;
	line-height: 40px;
	padding: 0 20px;
	cursor: pointer;
	font-size: 14px;
	margin-top: var(--pitaya-fs-12);
	text-align: center;
	color: var(--pitaya-header-bg-color);
	border: 1px dashed var(--pitaya-border-color);
}
.ml10 {
	margin-left: 10px;
}

.btn-isDisabled{
	svg{
		color: var(--pitaya-disabled-color) !important;
	}
	cursor: not-allowed;
}
</style>
