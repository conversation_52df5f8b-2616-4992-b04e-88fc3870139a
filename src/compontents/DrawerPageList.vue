<script setup lang="ts">
import { usePagination } from "@/app/platform/hooks/usePagination"

interface Props {
	ruleQueryArrList: any[]
	queryApi: Promise<any>
	columns: any[]
	drawerTitle: Object
	staticParam: Object
	openQuery: boolean
	isShowSlot: boolean
	selected: any[]
}
const props = defineProps<Props>()
const drawerLoading = ref<boolean>(false)
const tableData = ref<any[]>([])
const usePaginationStore = usePagination()
const usePaginationStoreForData = usePaginationStore.paginationData
const pageSize = ref<number>(usePaginationStoreForData.pageSize)
const pageTotal = ref<number>(0)
const currentPage = ref<number>(usePaginationStoreForData.currentPage)
const queryParam = ref<anyKey>({})
const getQueryData = (param: any) => {
	queryParam.value = param
	currentPage.value = 1
	loadData()
}
const selectedRows = ref<any[]>([])
const tempRows = ref<any[]>([])
const deviceBtnList = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "确定",
		icon: ["fas", "floppy-disk"]
	}
]

const loadData = () => {
	drawerLoading.value = true
	console.log({ ...queryParam }, "queryParam")
	console.log({ ...props.staticParam }, "staticParam")
	props
		.queryApi({
			...queryParam.value,
			...props.staticParam,
			pageSize: pageSize.value,
			currentPage: currentPage.value
		})
		.then((res: any) => {
			console.log(res, "修程结果数据")
			tableData.value = res.rows
			pageTotal.value = res.records
		})
		.finally(() => {
			drawerLoading.value = false
		})
}
const onCurrentPageChange = (pageData: any) => {
	pageSize.value = pageData.pageSize
	currentPage.value = pageData.currentPage
	loadData()
}
const onDeviceBtnClick = (btnName: string | undefined) => {
	if (btnName === "确定") {
		emit("onBtnClick", selectedRows.value)
	} else {
		if (tempRows.value.length) {
			const row = tableData.value.find(
				(item: any) => item.id === tempRows.value[0].id
			)
			emit("onBtnClick", [row])
		} else {
			emit("onBtnClick", [])
		}
	}
}
// 故障设备表格勾选回调
const onSelectionChange = (rowList: any[]) => {
	selectedRows.value = rowList
}
onMounted(() => {
	if (props.openQuery) {
		loadData()
	}
	tempRows.value = props.selected
})
/**
 * emit
 * */
const emit = defineEmits<{
	(e: "onBtnClick", selectNodes: any[]): void
}>()
</script>
<template>
	<div class="common-from-wrapper rule-drawer" v-loading="drawerLoading">
		<Title :title="props.drawerTitle" />
		<div class="commonQuery-d-wrapper">
			<Query
				:queryArrList="ruleQueryArrList"
				@getQueryData="getQueryData"
				namePlace="drawer-query"
			/>
		</div>
		<div class="common-from-group device" style="padding: 0px">
			<el-scrollbar>
				<PitayaTable
					ref="deviceTableRef"
					@on-current-page-change="onCurrentPageChange"
					@on-selection-change="onSelectionChange"
					:table-data="tableData"
					:columns="props.columns"
					:needSelection="true"
					:singleSelect="true"
					:total="pageTotal"
					style="padding-bottom: 10px"
					:table-loading="drawerLoading"
					:selectedTableData="props.selected"
					:max-height="672"
				>
					<template #cycleNumber="{ rowData }">
						<span v-if="props.isShowSlot && rowData.cycleNumber"
							>{{ rowData.cycleNumber }}{{ rowData.cycleUnit_view }}</span
						>
						<span v-else>---</span>
					</template>
				</PitayaTable>
			</el-scrollbar>
		</div>
		<div class="btn-groups">
			<ButtonList :button="deviceBtnList" @onBtnClick="onDeviceBtnClick" />
		</div>
	</div>
</template>
<style lang="scss" scoped>
// 故障设备抽屉样式
.rule-drawer {
	.commonQuery-d-wrapper {
		padding: 15px 10px 0px;
	}
	.btn-groups {
		position: absolute;
		bottom: 0;
		left: 0;
		right: 0;
		width: 100%;
	}
}

.common-from-wrapper {
	height: 100%;
	width: 100%;
	display: flex;
	flex-direction: column;
	.btn-groups {
		display: flex;
		justify-content: flex-end;
		padding-top: 10px;
		padding-right: 10px;
		border-top: 1px solid var(--pitaya-border-color);
	}
}
</style>
