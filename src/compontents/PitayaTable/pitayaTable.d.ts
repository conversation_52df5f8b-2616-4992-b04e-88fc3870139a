// 表头类型
interface TableColumnType {
	prop: string
	label: string
	width?: string | number
	minWidth?: string | number
	needSlot?: boolean
	align?: "center" | "left" | "right"
	class?: "tree-cell-flex" | ""
	fixed?: "left" | "right"
	sortable?: "custom" | boolean
	showOverflowTooltip?: boolean
}
// 表格树类型
interface TreeProps {
	children: string
	hasChildren: string
}
// 表格ref类型
interface PitayaTableRef {
	pitayaTableRef: InstanceType<typeof ElTable>
}
