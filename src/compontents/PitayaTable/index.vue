<script lang="ts" setup name="PitayaTable">
import { usePagination } from "@/app/platform/hooks/usePagination"
import { ElTable as elTableType } from "element-plus"
import { useQueryStore } from "@/app/platform/store/queryComponent"
import { useRoute } from "vue-router"
import EmptyData from "@/assets/images/empty/empty_data.svg?component"
import { debounce } from 'lodash-es'; 

const currentRoute = useRoute()

const queryStore = useQueryStore()
const { tableClassName } = storeToRefs(queryStore)

interface Props {
	customizeHeightNumber:number, // 在目前表格以及分页基础上增减高，为了适应弹窗底部以及缺陷-故障调度底部
	tableData: any[] // 表格数据
	selectedTableData?: any[] // 默认选中的列表
	columns: TableColumnType[] // 表头数据
	tableTreeProps?: TreeProps | object // 树配置
	needRowSelection?: boolean // 当前行是否需要勾选
	needSelection?: boolean // 是否需要勾选
	needPagination?: boolean // 是否需要分页
	needIndex?: boolean // 是否需要序号
	total?: number
	class?: string
	pageSize?: number
	pagerCount?: number
	layout?: string
	singleSelect?: boolean // 是否单选,
	isHideHeaderCheckBox?: boolean // 是否隐藏头部多选复选框
	selectKey?: string // 回显关键字
	tableLoading?: boolean // 表格加载状态
	maxHeight?: number | string // 表格最大高度，计算方法：表格行数（包含表头） * 行高（固定32px）
	spanMethod?: any
	cellClassName?: any
	cellMouseEnter?: any
	cellMouseLeave?: any
	disabledIds?:any[] // 禁用特定行选择
}

const emit = defineEmits([
	"onSelectionChange",
	"onCurrentPageChange",
	"onTableSortChange"
])
const props = withDefaults(defineProps<Props>(), {
	customizeHeightNumber:0,
	needRowSelection: true, //是否需要行选中
	needSelection: false,
	needPagination: true,
	needIndex: true,
	total: 0,
	pageSize: 20,
	layout: "prev, pager, next, sizes,total, jumper, slot",
	pagerCount: 5,
	class: "",
	selectKey: "id",
	tableData: () => {
		return []
	},
	selectedTableData: () => {
		return []
	},
	tableTreeProps: () => {
		return {}
	},
	singleSelect: false,
	tableLoading: false,
	spanMethod: () => {},
	cellClassName: () => {},
	cellMouseEnter: () => {},
	cellMouseLeave: () => {}
})
const usePaginationStore = usePagination({
	pageSize: props.pageSize,
	currentPage: 1
})
const usePaginationStoreForData = usePaginationStore.paginationData
const tableDataSelf = toRef(props, "tableData")
const columnsSelf = toRef(props, "columns")
const treePropsSelf = toRef(props, "tableTreeProps")
const total = toRef(props, "total")
const layout = toRef(props, "layout")
const pagerCount = toRef(props, "pagerCount")
const selectedTableData = toRef(props, "selectedTableData")
const singleSelect = toRef(props, "singleSelect")
const selectKey = toRef(props, "selectKey")
const tableLoading = toRef(props, "tableLoading")
const pitayaTableRef = ref<InstanceType<typeof elTableType>>()
const handleSpanMethod = toRef(props, "spanMethod")
const tableCellClassName = toRef(props, "cellClassName")
const handleCellMouseEnter = toRef(props, "cellMouseEnter")
const handleCellMouseLeave = toRef(props, "cellMouseLeave")

// 懒加载
const tableDataOnLoad = (row: any, _treeNode: any, resolve: any) => {
	resolve([])
}

let timer: any = null
// 勾选回调
const handleSelectionChange = (selection: any) => {
	if (timer) {
		clearTimeout(timer)
	}
	timer = setTimeout(() => {
		emit("onSelectionChange", getSelectedTable())
	}, 200)
}

const currentPagechange = (value: number) => {
	usePaginationStore.handleCurrentChange(value)
	pitayaTableRef.value?.setScrollTop(0)
	emit("onCurrentPageChange", usePaginationStore.paginationData)
}

const handleSizeChange = (value: number) => {
	usePaginationStore.handleSizeChange(value)
	usePaginationStore.handleCurrentChange(1)
	pitayaTableRef.value?.setScrollTop(0)
	emit("onCurrentPageChange", usePaginationStore.paginationData)
}
// 序号
const getTableIndex = (index: any) => {
	const { pageSize, currentPage } = usePaginationStoreForData
	return index + 1 + (currentPage - 1) * pageSize
}
// 排序
const onSortChange = ({
	column,
	prop,
	order
}: {
	column: any
	prop: any
	order: any
}) => {
	emit("onTableSortChange", column, prop, order)
}

const selectedTable = ref<any[]>([])

const select = (selection: any, row: any) => {
	if (props.disabledIds && props.disabledIds.includes(row.id)) {
		return
	}
	if (singleSelect.value) {
		pitayaTableRef.value?.clearSelection()
		selectedTable.value = []
		if (
			selection &&
			selection.length &&
			selection.find((item: any) => {
				return item[selectKey.value] === row[selectKey.value]
			})
		) {
			selectedTable.value = []
			selectedTable.value.push(row)
			pitayaTableRef.value!.toggleRowSelection(row, true)
		} else {
			selectedTable.value = []
		}
		return
	}
	if (
		selection &&
		selection.length &&
		selection.find((item: any) => {
			return item[selectKey.value] === row[selectKey.value]
		})
	) {
		selectedTable.value.push(row)
	} else {
		selectedTable.value = selectedTable.value.filter((table: any) => {
			return table[selectKey.value] !== row[selectKey.value]
		})
	}
}

const selectAll = (selection: any) => {
	if (singleSelect.value) {
		pitayaTableRef.value?.clearSelection()
		return
	}
	if (selection && selection.length) {
			selection.forEach((elt: any) => {
				if (!props.disabledIds || !props.disabledIds.includes(elt.id)) {
					const repeatTable =
						selectedTable.value.filter((table: any) => {
							return table[selectKey.value] === elt[selectKey.value]
						}) || []
					if (!repeatTable || !repeatTable.length) {
					selectedTable.value.push(elt)
				}
			}
		})
	} else {
		tableDataSelf.value.forEach((elt: any) => {
			selectedTable.value = selectedTable.value.filter((table: any) => {
				return table[selectKey.value] !== elt[selectKey.value]
			})
		})
	}
}
const currentRowKey = ref()

// 返回所有选中数据
const getSelectedTable = () => {
	return selectedTable.value
}

// 清除选中
const clearSelectedTableData = () => {
	selectedTable.value = []
	pitayaTableRef.value?.clearSelection()
}

const resetCurrentPage = () => {
	usePaginationStoreForData.currentPage = 1
}

// 暴露ref
defineOptions({
	name: "PitayaTable"
})

const maxTableHeight = ref<string | number | undefined>(window.innerHeight)
const updateTableHeight = () => {
  let newHeight;
  const BASE_HEIGHT = 275;	// 顶部尾部固定高
  const ROW_HEIGHT = 44;  // 搜索条件一行的高度
  const CUSTOMIZE_HEIGHT = props.customizeHeightNumber?props.customizeHeightNumber:0 // 个别样式，活着弹窗需要进行调整高度
  // 下边是搜索条件的高度。为了解决基线缺陷-问题，故障查看详情时，弹窗内有表格和搜索，引起的外部分页高度变化问题
  let queryHeight = 0;
  const element:any = document.querySelector('.common-query-all');
  if(element&&element?.offsetHeight){
	queryHeight = element?.offsetHeight + 21   // 增加的是默认padding和border高度
  }
  switch (tableClassName.value) {
    case "one-query-rows":
      newHeight = 0;
      break;
    case "two-query-rows":
      newHeight = ROW_HEIGHT;
      break;
    case "three-query-rows":
      newHeight = ROW_HEIGHT * 2;
      break;
    case "four-query-rows":
      newHeight = ROW_HEIGHT * 3;
      break;
    case "five-query-rows":
      newHeight = ROW_HEIGHT * 4;
      break;
	case "six-query-rows":
      newHeight = ROW_HEIGHT * 5;
	  break;
	case "seven-query-rows":
      newHeight = ROW_HEIGHT * 6;
      break;
    default:
	  maxTableHeight.value = Number(window.innerHeight - 210 - queryHeight + CUSTOMIZE_HEIGHT);
	return;
  }
  maxTableHeight.value = Number(window.innerHeight - (BASE_HEIGHT + newHeight) + CUSTOMIZE_HEIGHT);
};
watchEffect(() => {
	if (
		props.maxHeight &&
		typeof props.maxHeight === "number" &&
		props.maxHeight > 0
	) {
		maxTableHeight.value = props.maxHeight
	}
	// 清除maxTableHeight
	if (props.maxHeight === "clearMaxHeight") {
		maxTableHeight.value = undefined
	}
	updateTableHeight()
})
const debouncedUpdateTableHeight = debounce(updateTableHeight, 100);
onMounted(() => {
  window.addEventListener('resize', debouncedUpdateTableHeight);
  updateTableHeight(); // 初始化表格高度
});
onUnmounted(() => {
  window.removeEventListener('resize', debouncedUpdateTableHeight);
});
const sizes = computed(() => {
	if ([10, 20, 30, 40, 50, 100].includes(usePaginationStoreForData.pageSize)) {
		return [10, 20, 30, 40, 50, 100]
	} else {
		return [
			10,
			20,
			30,
			40,
			50,
			Number(usePaginationStoreForData.pageSize),
			100
		].sort((a, b) => a - b)
	}
})

const handleRowClick = (row: any | undefined, event: any) => {
	//存储当前id,用于返回后依旧显示当前行高亮
	localStorage.setItem("currentRowKey", row.id)

	/***
	 *
	 * 用户点击后选中select框
	 *
	 *
	 * ****/
	//点击当前行后，勾选
	// 启用select
	if (event.label === "操作") return
	if (props.needSelection && props.needRowSelection && (!props.disabledIds || !props.disabledIds.includes(row.id))) {
		const exists = selectedTable.value.some(
			(item) => item[selectKey.value] === row[selectKey.value]
		)
		//如果没有选中过，添加
		if (!exists) {
			//如果是单选，要清空上一次选择
			if (singleSelect.value) {
				pitayaTableRef.value?.clearSelection()
				selectedTable.value = []
			}
			selectedTable.value.push(row)
			pitayaTableRef.value!.toggleRowSelection(row, true)
		} else {
			//选中过移除
			selectedTable.value = selectedTable.value.filter(
				(item) => item[selectKey.value] !== row[selectKey.value]
			)
			pitayaTableRef.value!.toggleRowSelection(row, false)
		}
	}
}
const setCurrent = () => {
	const savedRowKey = localStorage.getItem("currentRowKey")
	if (savedRowKey) {
		currentRowKey.value = savedRowKey
	}
}
const clearCurrentRowKey = () => {
	localStorage.removeItem("currentRowKey")
}
watch(
	[() => tableDataSelf.value, () => selectedTable.value],
	() => {
		if (
			selectedTable.value &&
			selectedTable.value.length &&
			tableDataSelf.value &&
			tableDataSelf.value.length
		) {
			setTimeout(() => {
				selectedTable.value.forEach((sedRow: any) => {
					tableDataSelf.value.forEach((row: any) => {
						if (sedRow[selectKey.value] === row[selectKey.value]) {
							pitayaTableRef.value!.toggleRowSelection(row, true)
						}
					})
				})
			}, 500)
		} else {
			if (tableDataSelf.value && tableDataSelf.value.length) {
				setCurrent()
			}
		}
	},
	{
		deep: true,
		immediate: true
	}
)
onMounted(() => {
	nextTick(() => {
		clearSelectedTableData()
		selectedTable.value = selectedTableData.value || []
	})
})

defineExpose({
	pitayaTableRef,
	currentPagechange,
	handleSizeChange,
	getSelectedTable,
	clearSelectedTableData,
	resetCurrentPage,
	clearCurrentRowKey
})
</script>
<template>
	<div
		:class="[
			'pitaya-table',
			singleSelect || isHideHeaderCheckBox ? 'only-select-wrapper' : '',
			tableClassName
		]"
	>
		<div class="table-class">
			<el-table
				border
				highlight-current-row
				:current-row-key="currentRowKey"
				@row-click="handleRowClick"
				:cell-class-name="tableCellClassName"
				@cell-mouse-enter="handleCellMouseEnter"
				@cell-mouse-leave="handleCellMouseLeave"
				lazy
				row-key="id"
				ref="pitayaTableRef"
				:data="tableDataSelf"
				:tree-props="treePropsSelf"
				:load="tableDataOnLoad"
				:show-overflow-tooltip="true"
				default-expand-all
				@selection-change="handleSelectionChange"
				@select="select"
				@select-all="selectAll"
				@sort-change="onSortChange"
				v-loading="tableLoading"
				style="--el-table-border-color: #ccc"
				:max-height="maxTableHeight"
				:span-method="handleSpanMethod"
			>
				<template #empty>
					<!-- <el-empty :image-size="150" description="未查询到相关信息" /> -->
					<div class="empty_table">
						<EmptyData class="empty_img" />
						<p>未查询到相关数据</p>
					</div>
				</template>
				<el-table-column
					v-if="props.needSelection"
					type="selection"
					width="32"
					align="center"
					:selectable="(row) => !props.disabledIds || !props.disabledIds.includes(row.id)"
					fixed
				/>
				<el-table-column
					v-if="props.needIndex"
					align="center"
					type="index"
					:index="getTableIndex"
					label="序号"
					width="65"
					fixed
				/>
				<el-table-column
					v-for="(column, index) in columnsSelf"
					:key="index"
					:align="column.align ?? 'center'"
					:label="column.label"
					:prop="column.prop"
					:width="column.width ?? ''"
					:min-width="column.minWidth ?? ''"
					:class-name="column.class"
					:fixed="column.fixed"
					:sortable="column.sortable"
					:show-overflow-tooltip="column.showOverflowTooltip ?? true"
					header-align="center"
					empty-text="暂无数据"
				>
					<template #header v-if="$slots[`header-${column.prop}`]">
						<slot :name="`header-${column.prop}`" :column="column"></slot>
					</template>
					<template #default="scope">
						<div class="column-inner-item" v-if="!column.needSlot">
							{{
								scope.row[column.prop] ||
								(scope.row[column.prop] === 0 ? 0 : "---")
							}}
						</div>
						<slot
							v-if="column.needSlot"
							:name="column.prop"
							:rowData="scope.row"
							:$index="scope.$index"
						/>
					</template>
				</el-table-column>
			</el-table>
		</div>
		<div class="table-footer-operate">
			<div class="footer-operate-left">
				<slot name="footerOperateLeft" />
			</div>
			<div
				class="footer-operate-pagination"
				v-if="props.needPagination && props.total && props.total > 0"
			>
				<el-pagination
					@current-change="currentPagechange"
					@size-change="handleSizeChange"
					v-model:current-page="usePaginationStoreForData.currentPage"
					v-model:page-size="usePaginationStoreForData.pageSize"
					:total="total"
					:page-sizes="sizes"
					:pager-count="pagerCount"
					:layout="layout"
					prev-text="上一页"
					next-text="下一页"
				>
					<button class="jumper-slot-btn">GO</button>
				</el-pagination>
			</div>
		</div>
	</div>
</template>
<style lang="scss" scoped>
.empty_table {
	line-height: 0rem;
	padding-bottom: 40px;
	.empty_img {
		width: 150px;
	}
	p {
		color: #666666;
		font-size: 12px;
		text-align: center;
	}
}
.pitaya-table {
	margin-top: 10px;
	padding: 0 10px;
	// height: 82vh; // 默认没有查询区域
	//overflow: auto;

	&.one-query-rows {
		// height: 76vh;
		overflow-y: hidden;
	}

	&.two-query-rows {
		// height: 71vh;
		overflow-y: hidden;
	}

	&.three-query-rows {
		// height: 66vh;
		overflow-y: hidden;
	}

	.table-class {
		width: 100%;
	}

	::v-deep(th.el-table__cell) {
		background-color: #f6f6f6 !important;
		color: #000;
		user-select: inherit;
	}
	::deep(.cell) {
		user-select: inherit !important;
	}
	::v-deep(.cell > div) {
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		user-select: inherit;
	}
	::v-deep(.tree-cell-flex:not(.is-center)) {
		.cell.el-tooltip {
			display: flex;
			align-items: center;
		}
	}
	.table-footer-operate {
		display: flex;
		justify-content: space-between;
		margin-top: 10px;
		// padding-bottom: 50px;

		.footer-operate-pagination {
			display: flex;
			align-items: center;
			:deep(.el-pagination__total) {
				height: 32px;
				padding: 1px 10px;
				margin-left: 0px;
				border: 1px solid #dcdfe6;
				border-left: none;
				font-size: var(--pitaya-fs-12);
				display: flex;
				align-items: center;
			}

			.jumper-slot-btn {
				display: flex;
				align-items: center;
				justify-content: center;
				border: 1px solid var(--pitaya-border-color);
				border-left: none;
				width: 32px;
				height: 32px;
				font-size: var(--pitaya-fs-12);
				color: #fff;
				background-color: var(--pitaya-btn-background);
				border-radius: 0;
				cursor: pointer;
				&:hover {
					background-color: var(--pitaya-hover-btn-background);
				}
				&:active {
					background-color: var(--pitaya-active-btn-background);
				}
			}
			:deep(.el-input__wrapper) {
				padding: 1px 10px;
			}
			:deep(.el-pager) {
				.number,
				.more {
					border: 1px solid var(--pitaya-border-color);
					border-left: none;
					border-radius: 0;
				}
				.is-active {
					background-color: #0a4e9a;
					color: #fff;
					border-color: #0a4e9a;
				}
			}
			:deep(.el-pager) {
				li {
					font-size: var(--pitaya-fs-12);
				}
			}
			:deep(.btn-prev),
			:deep(.btn-next) {
				height: 32px;
				width: 70px;
				text-align: center;
				line-height: 32px;
				border: 1px solid var(--pitaya-btn-background) !important;
				background-color: var(--pitaya-btn-background);
				color: #fff;
				border-radius: 0;
				span {
					font-size: var(--pitaya-fs-12);
				}
				&:hover {
					background-color: var(--pitaya-hover-btn-background);
				}
				&:active {
					background-color: var(--pitaya-active-btn-background);
				}
				&:disabled {
					border: 1px solid var(--pitaya-border-color) !important;
				}
			}
			:deep(.btn-next) {
				border-left: none !important;
			}
			:deep(.btn-prev) {
				border-right: none !important;
			}
			:deep(.el-pagination__sizes) {
				margin-left: 10px;
				.el-input {
					width: 100px;
					font-size: var(--pitaya-fs-12);
				}
				.el-input__wrapper {
					border-radius: 0px;
				}
			}
			:deep(.el-pagination__jump) {
				margin-left: 10px;
				.el-pagination__goto,
				.el-pagination__classifier {
					display: none;
				}
				.el-input__wrapper {
					padding: 0 10px;
					border-radius: 0px;
					box-shadow: none;
					border: 1px solid var(--pitaya-border-color);
					border-right: none;
				}
			}
		}
	}
}

.pitaya-table {
	::v-deep(td.hover-cell) {
		background-color: #e3f2fe !important;
	}
}

// 解决表格底部边框被遮挡的问题
:deep(.el-table__empty-block) {
	margin-bottom: 1px;
}
:deep(.el-checkbox__input.is-disabled .el-checkbox__inner){
	background:#f6f6f6 !important;
}
// :deep(.el-table-fixed-column--left){
// 	background:rgba(var(--parent-bg-rgb), 1) !important;
// }
// :deep(.el-table-fixed-column--right){
// 	background:rgba(var(--parent-bg-rgb), 1) !important;
// }
</style>
@/app/platform/hooks/usePagination
