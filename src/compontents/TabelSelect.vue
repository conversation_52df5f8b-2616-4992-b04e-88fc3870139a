<!-- 
{
		name: "上报人",
		key: "tableUsername",
		placeholder: "请选择",
		type: "tableSelect",
		tableInfo: [{
			title:'修改信息',//弹窗信息
			tableApi: baseUserApi.getBaseUserListApi,//表格接口
			tableColumns: [{ label: "公司", prop: "companyName" }], //表格信息
			queryArrList: [{name: "手机号码1",key: "phone",type: "input",placeholder: "请输入查询关键字"}],//查询列表信息
						tabList : ["标准工作", "工作组合"],
			params: {
				companyId: 1080, 
				currentPage: 1,
				pageSize: 20
			},   //表格接口参数
			queryParams: {
				phone: null,
				realname: null,
				employeeNumber: null
			},  //查询参数
			labelName:{
				key:'id',//回显标识
				label:'realname',//input框要展示的字段
				value:'username'//要给后台传的字段
			}//input 回显内容名称
		}]
	},
 -->

<script lang="ts" setup>
interface Props {
	tableDataKeys: any[] // 回显用户数组
}

const props = withDefaults(defineProps<Props>(), {
	tableDataKeys: () => {
		return []
	}
})

const { tableDataKeys } = toRefs(props)

const emit = defineEmits(["onSubmit"])

const drawerTitle = reactive({
	name: ["选择"],
	icon: ["fas", "square-share-nodes"]
})

const btnList = ref([
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "保存",
		icon: ["fas", "floppy-disk"],
		disabled: false
	}
])
const tableRef = ref()
const tableApi = ref()
const tabIndex = ref(0)
let tableInfo = reactive({})
const tableData = ref<any[]>([])
const dataTotal = ref<number>(0)
const selectedRows = ref<any[]>([])
const drawerState = ref<boolean>(false)
const tableLoading = ref<boolean>(false)
const drawerLoading = ref<boolean>(false)

// 回调：表格条件查询
const getQueryData = (queryData: any) => {
	tableInfo.queryParams = { ...queryData }
	tableInfo.params.currentPage = 1
	tableRef.value.resetCurrentPage()
	getUserList(true)
}

// 回调：表格分页查询
const onCurrentPageChange = (pageData: any) => {
	tableInfo.params.currentPage = pageData.currentPage
	tableInfo.params.pageSize = pageData.pageSize
	getUserList()
}

// 回调：表格勾选
const getSelectedTableData = (rowList: any[]) => {
	selectedRows.value = rowList
}

// 回调：底部按钮操作
const onBtnClick = (btnName?: string | undefined) => {
	if (btnName === "保存") {
		if (!selectedRows.value.length) {
			ElMessage.warning("选项不能为空")
			return
		}
		// 传递所选用户信息
		emit("onSubmit", selectedRows.value[0])
		drawerState.value = false
	} else {
		drawerState.value = false
	}
}

// 加载列表
const getUserList = (isClear?: boolean) => {
	tableLoading.value = true
	tableData.value = []
	tableApi
		.value({
			...tableInfo.queryParams,
			...tableInfo.params
		})
		.then((res: anyKey) => {
			if (res.rows && res.rows.length > 0) {
				const { rows, records } = res
				dataTotal.value = records
				rows.map((item: any) => {
					item.key = item[tableInfo.labelName.key]
					item.label = item[tableInfo.labelName.label]
					item.value = item[tableInfo.labelName.value]
					tableData.value.push(item)
				})
				// 回显已选信息
				if (tableDataKeys.value.length) {
					const ids: number[] = []
					tableData.value.forEach((item) => {
						if (tableDataKeys.value.includes(item.value)) {
							ids.push(item.key)
						}
					})
					refreshTableSelectedItem(tableData.value, ids, selectedRows.value)
				}
				// 清除勾选
				if (isClear) {
					tableRef.value.clearSelectedTableData()
				}
			} else {
				tableData.value = []
				dataTotal.value = 0
			}
		})
		.finally(() => {
			tableLoading.value = false
		})
}

// 打开
let tableInit = reactive([])
const refNewDrawer = ref()
const open = (data: any) => {
	if (data) {
		tableInit = data
		tabIndex.value = 0
	}
	tableInfo = tableInit?.tableInfo[tabIndex.value]
	tableApi.value = tableInfo.tableApi
	drawerTitle.name[0] = tableInfo.title
	drawerState.value = true
	refNewDrawer.value.paramsFn(tableInfo.tabList)
	getUserList()
}

// 回调：抽屉关闭
const onClose = () => {
	tableInfo.queryParams = {}
	selectedRows.value = []
}

// 回显表格选中状态
const refreshTableSelectedItem = (
	tableData: any[],
	keys: any[],
	selectedRows: any[]
) => {
	if (keys && keys.length > 0) {
		tableData.forEach((row) => {
			keys.forEach((key) => {
				if (row.key === key) {
					selectedRows.push(row)
				}
			})
		})
	} else {
		selectedRows = []
	}
}

//扩展tab
const updateTab = (index: any) => {
	tabIndex.value = index
	tableRef.value.resetCurrentPage()
	open()
}

defineExpose({
	open
})
</script>
<template>
	<NewDrawer
		ref="refNewDrawer"
		:size="tableInfo?.width||1100"
		:destroyOnClose="true"
		v-model:drawer="drawerState"
		:title="drawerTitle"
		:loading="drawerLoading"
		:tabList="tableInfo?.tabList"
		@close="onClose"
		@updateTab="updateTab"
	>
		<template #content>
			<Query
				v-if="tableInfo.queryArrList&&tableInfo.queryArrList.length"
				:numInRow="3"
				:queryBtnColSpan="6"
				:queryArrList="tableInfo.queryArrList"
				@getQueryData="getQueryData"
			/>
			<PitayaTable
				ref="tableRef"
				:columns="tableInfo.tableColumns"
				:table-data="tableData"
				:need-index="true"
				:single-select="true"
				:need-selection="true"
				:need-pagination="true"
				:total="dataTotal"
				:table-loading="tableLoading"
				:selectedTableData="selectedRows"
				:max-height="672"
				@on-current-page-change="onCurrentPageChange"
				@onSelectionChange="getSelectedTableData"
			>
			</PitayaTable>
		</template>
		<template #footer>
			<ButtonList
				:is-not-radius="true"
				:button="btnList"
				@on-btn-click="onBtnClick"
			/>
		</template>
	</NewDrawer>
</template>

<style lang="scss" scoped></style>
