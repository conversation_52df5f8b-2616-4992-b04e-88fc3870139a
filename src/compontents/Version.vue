<!-- 版本列表-新建版本 -->
<template>
	<div class="left-model">
		<!-- 按钮 -->
		<Button :title="title" @onBtnClick="onCreateBtn"></Button>
		<!-- 列表 -->
		<ul class="ul">
			<li
				class="flex-dr li"
				:class="[id == item ? 'hover' : '']"
				v-for="item in versionList"
				@click="onClickBtn(item)"
			>
				<div class="f12 flex-dc">
					<span>版本号：20230301-V3版本</span>
					<span class="c-9">发布时间：2023-02-21 12:23:21</span>
				</div>
				<div class="f12 release release1">待发布</div>
			</li>
		</ul>
	</div>
</template>
<script lang="ts" setup>
import Button from "@/compontents/Button.vue"

interface Props {
	title?: "" | undefined,
	versionList: [{}]
}
const props = defineProps<Props>()
const versionList = toRef(props, "versionList")
const title = toRef(props, "title")


const emit = defineEmits<{
	(e: "onBtnClick", btnName:[] | undefined): void
}>()

const onCreateBtn = () => {
	console.log("新建版本")
}

const id = ref(0)
const onClickBtn = (item: any) => {
	console.log("itemd", item)
	id.value = item
	emit("onBtnClick", item)
}
</script>
<style lang="scss" scoped>
.left-model {
	// overflow: scroll;
	padding-bottom: 200px;
}

.btn-groups {
	position: absolute;
	bottom: 10px;
	right: 10px;
	display: flex;
	justify-content: flex-end;
	padding-top: 10px;
	box-sizing: border-box;
	width: 100%;
	border-top: 1px solid #ccc;
}

.dashed-box {
	height: 35px;
	line-height: 35px;
	font-size: 14px;
	margin-top: var(--pitaya-fs-12);
	text-align: center;
	color: var(--pitaya-header-bg-color);
	border: 1px dashed var(--el-border-color);
}

.ml10 {
	margin-left: 10px;
}

.flex-dr {
	display: flex;
	flex-direction: row;
	justify-content: space-between;
	align-items: center;
}

.flex-dc {
	display: flex;
	flex-direction: column;
}

.ul {
	// max-height: 20%;
	// overflow: scroll;
	// height: 100vh;
	// padding-bottom: 300px;
}

.li:hover {
	color: #666666;
	background: #ecf5ff;
}

.hover {
	color: #666666;
	background: #ecf5ff;
}

.li {
	line-height: 24px;
	padding: 10px 0px;
	border-bottom: 1px dashed var(--el-border-color);

	.release {
		min-width: 60px;
		padding: 0px 10px;
		border-radius: 3px;
		font-size: 12px;
		text-align: center;
		border: 1px solid var(--el-border-color);
		// color: #666666 ;
		// 	background: #F6F6F6;
	}

	.release1 {
		color: #4bae89;
		background: #e6fef0;
		border: 1px solid #4bae89;
	}

	.release2 {
		color: #e6a23c;
		background: #fdf6ec;
		border: 1px solid #f3d19e;
	}
}

.f12 {
	font-size: 12px;
}

.c-9 {
	color: #999;
}

.mt20 {
	margin-top: 20px;
}

.cp {
	cursor: pointer;
}
</style>
