<script setup lang="ts">
interface Props {
	tabs: string[]
	disabled?: boolean // tab 是否禁用 cursor:not-allowed
	activeIndex?: number | null // 设置当前tab的索引
}

const props = withDefaults(defineProps<Props>(), {
	tabs: () => [],
	activeIndex: null,
	disabled: false
})

const emit = defineEmits<{
	(e: "onTabChange", tabIndex: number): void
}>()

const activeTab = ref<number>(0)
const { tabs } = toRefs(props)
const clickTab = (tab: string, index: number) => {
	if (props.disabled) return
	activeTab.value = index
	emit("onTabChange", index)
}

/**
 * 钩子函数
 */

watchEffect(() => {
	if (typeof props.activeIndex === "number") {
		activeTab.value = props.activeIndex
	}
})

defineExpose({
	clickTab
})
</script>
<template>
	<div class="pitaya-tabs">
		<div
			class="pitaya-tab-panel"
			v-for="(tab, index) in tabs"
			:key="index"
			:class="{
				'is-active': activeTab === index,
				'is-disabled': disabled
			}"
			@click="clickTab(tab, index)"
		>
			{{ tab }}
		</div>
	</div>
</template>
<style lang="scss" scoped>
.pitaya-tabs {
	display: flex;
	justify-content: left;
	.pitaya-tab-panel {
		margin-right: 40px;
		color: #666666;
		cursor: pointer;
	}
	.is-disabled {
		cursor: not-allowed;
	}
	.is-active {
		color: var(--pitaya-btn-background);
	}
	.is-active::after {
		content: "";
		position: absolute;
		left: 0;
		bottom: -10px;
		width: 100%;
		height: 2px;
		background-color: var(--pitaya-btn-background);
	}
}
</style>
