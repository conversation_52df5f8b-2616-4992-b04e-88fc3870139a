<script lang="ts" setup>
import { Close } from "@element-plus/icons-vue"

interface Props {
	isBeforeClose?: boolean // 是否在关闭前做逻辑判断
	size: string | number
	drawer: boolean
	destroyOnClose?: boolean //是否在关闭时销毁内部元素,
	showClose?: boolean //是否显示关闭按钮
}

const props = withDefaults(defineProps<Props>(), {
	isBeforeClose: false,
	destroyOnClose: false,
	showClose: true
})
const size = toRef(props, "size")
const showClose = toRef(props, "showClose")

const emit = defineEmits(["update:drawer", "close", "beforeClose"])

const drawer = computed({
	get() {
		return props.drawer
	},
	set(value) {
		emit("update:drawer", value)
	}
})

const onClose = () => {
	if (props.isBeforeClose) {
		emit("beforeClose")
	} else {
		drawer.value = false
		emit("close")
	}
}

/**
 * 关闭前置逻辑较验
 * @param done
 */
const handleBeforeClose = (done: any) => {
	if (props.isBeforeClose) {
		emit("beforeClose")
	} else {
		done()
	}
}
</script>

<template>
	<el-drawer
		v-model="drawer"
		:with-header="false"
		:size="size"
		:append-to-body="true"
		:destroy-on-close="destroyOnClose"
		:before-close="handleBeforeClose"
	>
		<button
			v-if="showClose"
			class="el-drawer__close-btn"
			type="button"
			@click="onClose"
		>
			<el-icon>
				<Close />
			</el-icon>
		</button>
		<slot />
	</el-drawer>
</template>

<style lang="scss" scoped>
.el-drawer__close-btn {
	position: absolute;
	z-index: 999;
	top: 10px;
	right: 10px;
	display: inline-flex;
	border: none;
	cursor: pointer;
	font-size: var(--el-font-size-extra-large);
	// font-size: var(--pitaya-fs-14);
	color: #ccc;
	background-color: transparent;
	outline: none;
}
</style>
