
<!-- 设备履历 -维修 -->
<!--
	//引用模版
const ledgerLogList = ref<any[]>([
	{ name: '故障提报' },
	{ name: '故障接报' },
	{ name: '故障派单' },
	{ name: '工单接单' },
	{ name: '故障定性' },
	{ name: '工单结单' },
	{ name: '故障关闭' },
])

	<Steps :ledgerLogList="ledgerLogList">
			<template #title="{ rowData }">
				2023-07-14 18:25:37金安桥站电扶梯日巡检
			</template>
			<template #titleBtn="{ rowData }">
				<span :class="['state-txt', 'oper_' + '1']" class="mr5">装配</span>
			</template>
			<template #content="{ rowData }">
				<div class="ldri-tle-desc">
					<div style=" display: inline-block;">
						<span :class="['state-txt', 'oper_' + '1']" class="mr5 ">装配</span>
					</div>
					<span> 主设备名称：KDZ5型电客车601车 装配位置：KDZ5型电客车-TC01车-车底-1位 操作人员：高宏宇
					</span>
				</div>
				<div class="ldri-tle-desc">
					<div style=" display: inline-block;">
						<span :class="['state-txt', 'oper_' + '1']" class="mr5 ">装配</span>
					</div>
					<span> 主设备名称：KDZ5型电客车601车 装配位置：KDZ5型电客车-TC01车-车底-1位 操作人员：高宏宇
					</span>
				</div>
			</template>
		</Steps>

 -->
<template>
	<div class>
		<div class="common-from-group">
			<div class="ledger-details-r-list" v-infinite-scroll="load">
				<el-scrollbar height="90%">
					<el-timeline style="margin-top: 10px;">
						<el-timeline-item
							class="ledger-details-r-item"
							v-for="(item, index) in ledgerLogList"
							:key="index"
							placement="top"
							hide-timestamp
							:icon="Clock"
							color="#F59B22"
						>
							<div class="ldri-tle-wrapper">
								<div class="ldri-tle">
									<slot name="title" :rowData="item">
										<!-- 2023-10-13 11:22:13 -->
									</slot>
								</div>
								<div class="ldri-tag">
									<slot name="titleBtn" :rowData="item"></slot>
									<!--
								<span :class="['state-txt', 'oper_' + '1']" class="mr5">装配</span>
									<span :class="['state-txt', 'oper_' + '2']">拆卸</span>-->
								</div>
							</div>

							<div>
								<div class="ldri-tle-desc">
									<slot name="content" :rowData="item"></slot>
								<!-- 主设备名称：顶部 -->
							</div>
							<!-- <div class="ldri-tle-desc">
								装配位置：空调系统-空调
								</div> -->
							</div>
						</el-timeline-item>
					</el-timeline>
					<div class="ledger-details-not-data" v-if="noMore">--暂无更多--</div>
				</el-scrollbar>
			</div>
		</div>
	</div>
</template>
<script setup lang="ts">
import { Clock } from "@element-plus/icons-vue"
interface Props {
	ledgerLogList?: Object[]
	noMore?: boolean
}
const props = defineProps<Props>()
const emits = defineEmits(["getMore"])
const load = () => {
	if (props.noMore) return
	emits("getMore")
}
</script>
<style lang="scss" scoped>
.el-timeline {
	margin-left: 10px;
}
.el-timeline-item {
	:deep(.el-timeline-item__tail) {
		left: 8px;
		border: dashed 1px #f59b22;
	}
	:deep(.el-timeline-item__node) {
		left: 0px;
		width: 18px;
		height: 18px;
		.el-icon {
			font-size: 14px;
		}
	}
}
.timeline-item {
	font-size: 12px;
	h1 {
		font-size: 14px;
		color: #f59b22;
		font-weight: bold;
		margin-right: 20px;
	}
	.item-title {
		display: flex;
		align-items: center;
		span {
			color: #999;
			margin-left: 10px;
		}
	}
	.item-context {
		margin: 10px 0px;
		width: 300px;
		li {
			line-height: 18px;
			margin-bottom: 5px;
		}
	}
}
.common-from-group {
	height: 70vh;
	flex: 1;
	overflow-y: scroll;

	.ledger-details-r-list {
		.ledger-details-r-item {
			padding-left: 0px;
			padding-bottom: 20px;

			.ldri-tle-wrapper {
				display: flex;
				align-items: center;

				.ldri-tle {
					color: #f59b22;
					font-weight: bold;
					font-size: var(--pitaya-fs-14);
					padding-right: 10px;
				}
				.ldri-tag {
					display: flex;
				}
			}
			.ldri-tle-desc{
				margin-top: 5px;
				line-height: 25px;
				font-weight: bold;
				color:#333;
				font-size: var(--pitaya-fs-12);
			}
		}

		.ledger-details-not-data {
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: var(--pitaya-fs-14);
			color: var(--pitaya-border-color);
		}
	}

	.ledger-info-wrapper {
		padding-top: 15px;

		.base-title-wrapper {
			font-size: 14px;
			color: #333;
			font-weight: bold;
			padding-bottom: 10px;
		}

		.ledger-info-box {
			border: 1px solid var(--pitaya-border-color);
			border-bottom: none;

			.ledger-info-item {
				border-bottom: 1px solid var(--pitaya-border-color);
				display: flex;

				.ledger-info-li {
					width: 0;
					flex: 1;
					display: flex;
					align-items: center;
					font-size: var(--pitaya-fs-12);
					color: #333;
					border-left: 1px solid var(--pitaya-border-color);

					&:first-child {
						border-left: none;
					}

					.ledger-info-li-l {
						width: 100px;
						padding: 0 10px;
						background-color: #f5f7fb;
						border-right: 1px solid var(--pitaya-border-color);
						display: flex;
						align-items: center;
						height: 100%;
						min-height: 31px;
					}

					.ledger-info-li-r {
						width: 0;
						flex: 1;
						display: flex;
						align-items: center;
						height: 100%;
						padding: 4px 10px;
						word-wrap: break-word;
						word-break: break-all;
					}
				}
			}
		}
	}
}

.el-divider--horizontal {
	margin: 10px 0 15px 0 !important;
}
</style>
