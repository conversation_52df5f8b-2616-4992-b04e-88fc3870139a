<template>
	<div class="profession-drawer-container">
		<div class="drawer-left">
			<div class="drawer-left-form">
				<PitayaTree
					:treeData="TreeData"
					:treeProps="TreeProp"
					:needCheckBox="true"
					:checkStrictly="true"
					:needSingleSelect="!multiSelect"
					v-model:treeBizId="treeBizId"
					:default-expanded-keys="defaultExpandedKeys"
					:checked-keys="checkedId"
					ref="pitayaTreeRef"
					nodekey="id"
					@onTreeClick="treeNodeClick"
					:tree-loading="treeLoading"
				/>
			</div>
			<div class="btn-groups">
				<ButtonList :button="btnList" @onBtnClick="onBtnClick" />
			</div>
		</div>
	</div>
</template>
<script lang="ts" setup>
import { DepartmentApi } from "@/app/platform/api/system/department"
/**
 * props*/
interface Props {
	multiSelect?: boolean
	drawerState?: boolean
	type?: string
	checkedKeys?: string[]
	companyId?: number
	params?: any
	defaultExpandedKeys?: number[]  // 添加类型定义
	defaultSelectedKeys?: number[] | string[]   // 添加类型定义
}
const props = defineProps<Props>()
const treeBizId = ref()
const drawerState = toRef(props, "drawerState")
const multiSelect = toRef(props, "multiSelect")
const checkedKeys = toRef(props, "checkedKeys")
const params = toRef(props, "params")

/**
 * emit
 * */
const emit = defineEmits<{
	(e: "onBtnClick", selectIds: string[] | undefined, selectNodes: any[]): void
}>()

/**
 * 基本数据列表
 * */
const TreeData = ref<any[]>([])
const pitayaTreeRef = ref<any>()

const treeList = ref<any[]>([]) //保存用户当前点击树信息
const treeListData = ref("") //保存用户当前点击树信息
const checkedId = ref<number[]>(props.defaultSelectedKeys || []) // 初始化选中值
// 默认展开
const defaultExpandedKeys = ref<any[]>([])
/**
 * 对象列表
 * */
// 部门树ref
const TreeProp = {
	children: "children",
	label: "name",
	disabled: 'disabled',

}
const btnList = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "保存",
		icon: ["fas", "floppy-disk"]
	}
]

/**
 *  函数
 */
const treeLoading =ref(false)
const getTreeData = () => {
	TreeData.value = []
	treeLoading.value=true
	DepartmentApi.getDepartmentTreeWithRole(params.value).then((res: any) => {
		if (props.companyId) {
			TreeData.value = res.filter(
				(item: any) => item.companyId === props.companyId
			)[0].children
		} else {
			TreeData.value = res
		}

		nextTick(() => {
			if (checkedId.value) {
				pitayaTreeRef!.value.setCheckedKeys(checkedId.value)
				pitayaTreeRef!.value.PitayaTreeRef.setCurrentKey(checkedId.value[0])
			}
		})
	}).finally(()=>{
		treeLoading.value=false
	})
}

// 树点击
const treeNodeClick = (data: any, treeData: any) => {
	if(data.disabled)return
	treeList.value = data
	treeListData.value = treeData
	pitayaTreeRef!.value.setCheckedKeys([data.id])
}

// 按钮点击
const onBtnClick = (btnName: string | undefined) => {
	if (btnName === "保存") {
		treeList.value = pitayaTreeRef!.value.PitayaTreeRef.getCheckedNodes()[0]
		checkedId.value = pitayaTreeRef!.value.PitayaTreeRef.getCheckedKeys()
		emit(
			"onBtnClick",
			treeList.value,
			pitayaTreeRef!.value.getCheckedNodes(false)
		)
		return
	}
	if (btnName === "取消") {
		checkedId.value = []
		emit("onBtnClick")
		return
	}
}

/**
 * Watch
 * */
watch(
	[() => drawerState.value],
	(newVal: any[]) => {
		if (newVal) {
			getTreeData()
		}
	},
	{
		immediate: true
	}
)
// 监听默认选中值变化
watch(
	() => props.defaultSelectedKeys,
	(newVal) => {
	  if (newVal) {
		checkedId.value = [...newVal]
		pitayaTreeRef.value?.setCheckedKeys(newVal)
	  }
	}
  )
</script>

<style lang="scss" scoped>
.profession-drawer-container {
	display: flex;
	align-items: center;
	position: relative;
	height: calc(100vh - 20px);
	padding: 0;
	overflow: hidden;

	.drawer-left {
		position: relative;
		padding-right: 10px;
		box-sizing: border-box;
		width: 100%;
		height: 100%;

		.drawer-left-form {
			height: 100%;
			padding-bottom: 10px;
			// margin-top: 10px;
			width: 100%;
			height: calc(100% - 22px);
		}
	}
}

.btn-groups {
	position: absolute;
	bottom: 0px;
	left: 0px;
	right: 0px;
	z-index: 99;
	display: flex;
	justify-content: flex-end;
	padding-top: 10px;
	padding-right: 10px;
	box-sizing: border-box;
	width: auto;
	border-top: 1px solid #ccc;
	background: #fff;
}
</style>
