<script lang="ts" setup name="PitayaTable">
import { usePagination } from "@/app/platform/hooks/usePagination"
import { ElTable as elTableType } from "element-plus"
import { findIndex, pullAllBy, uniqBy, pullAllWith, isEqual } from "lodash-es"

interface Props {
	tableData: any[] // 表格数据
	selectedTableData?: any[] // 默认选中的列表
	columns: TableColumnType[] // 表头数据
	tableTreeProps?: TreeProps | object // 树配置
	needSelection?: boolean // 是否需要勾选
	needPagination?: boolean // 是否需要分页
	needIndex?: boolean // 是否需要序号
	total?: number
	class?: string
	pageSize?: number
	pagerCount?: number
	layout?: string
	singleSelect?: boolean // 是否单选
	selectKey?: string //回显关键字
	tableLoading?: boolean //表格加载状态
}

const emit = defineEmits([
	"onSelectionChange",
	"onCurrentPageChange",
	"onTableSortChange"
])
const props = withDefaults(defineProps<Props>(), {
	selectKey: "",
	needSelection: false,
	needPagination: true,
	needIndex: true,
	total: 0,
	layout: "prev, pager, next, sizes, jumper, slot",
	pagerCount: 5,
	class: "",
	tableData: () => {
		return []
	},
	selectedTableData: () => {
		return []
	},
	tableTreeProps: () => {
		return {}
	},
	singleSelect: false,
	tableLoading: false
})
const usePaginationStore = usePagination()
const usePaginationStoreForData = usePaginationStore.paginationData

const tableDataSelf = toRef(props, "tableData")
const columnsSelf = toRef(props, "columns")
const treePropsSelf = toRef(props, "tableTreeProps")
const total = toRef(props, "total")
const layout = toRef(props, "layout")
const pagerCount = toRef(props, "pagerCount")
const selectedTableData = toRef(props, "selectedTableData")
const singleSelect = toRef(props, "singleSelect")
const tableLoading = toRef(props, "tableLoading")

const pitayaTableRef = ref<InstanceType<typeof elTableType>>()
// 懒加载
const tableDataOnLoad = (row: any, _treeNode: any, resolve: any) => {
	console.log(row)
	resolve([])
}

let timer: any = null
// 勾选回调
const handleSelectionChange = (selection) => {
	if (timer) {
		clearTimeout(timer)
	}
	timer = setTimeout(() => {
		emit("onSelectionChange", pitayaTableRef.value!.getSelectionRows())
	}, 200)
}

const currentPagechange = (page: number) => {
	usePaginationStore.handleCurrentChange(page)
	emit("onCurrentPageChange", usePaginationStore.paginationData)
}

const handleSizeChange = (value: number) => {
	usePaginationStore.handleSizeChange(value)
	usePaginationStore.handleCurrentChange(1)
	emit("onCurrentPageChange", usePaginationStore.paginationData)
}
// 序号
const getTableIndex = (index: any) => {
	const { pageSize, currentPage } = usePaginationStoreForData
	return index + 1 + (currentPage - 1) * pageSize
}
// 排序
const onSortChange = ({
	column,
	prop,
	order
}: {
	column: any
	prop: any
	order: any
}) => {
	emit("onTableSortChange", column, prop, order)
}

const selectedTable = ref<any[]>([])

const select = (selection: any, row: any) => {
	pullAllWith(selection, thisOpenSelectTableData, isEqual)
	// 判断被勾选的行 是移除还是选中
	const findIndexResult = findIndex(selection, (item) => {
		return item[props.selectKey] === row[props.selectKey]
	})
	if (findIndexResult == -1) {
		pullAllBy(selectedTableData.value, [row], props.selectKey)
	} else {
		if (singleSelect.value) {
			pitayaTableRef.value?.clearSelection()
			pitayaTableRef.value?.toggleRowSelection(row, undefined)
			selectedTableData.value = []
			pullAllBy(
				selectedTableData.value,
				[selectedTableData.value[0]],
				props.selectKey
			)
			selectedTableData.value.push(row)
		} else {
			selectedTableData.value.push(row)
		}
	}
	console.log("select", selectedTableData.value)
}

const selectAll = (selection: any) => {
	pullAllWith(selection, thisOpenSelectTableData, isEqual)
	if (singleSelect.value) {
		pitayaTableRef.value?.clearSelection()
		return
	}
	if (selection && selection.length) {
		selectedTableData.value.push(...selection)
	} else {
		pullAllBy(selectedTableData.value, tableDataSelf.value, props.selectKey)
	}
}

// 进入页面所选择勾选的项
let thisOpenSelectTableData = []
watch(
	() => selectedTableData.value,
	(newVal: any[]) => {
		thisOpenSelectTableData = newVal

		nextTick(() => {
			newVal.forEach((row) => {
				pitayaTableRef.value!.toggleRowSelection(row, undefined)
			})
		})
	},
	{
		immediate: true
	}
)

watch(
	[() => tableDataSelf.value],
	() => {
		// 回显选中数据
		setTimeout(() => {
			if (
				selectedTable.value &&
				selectedTable.value.length &&
				tableDataSelf.value &&
				tableDataSelf.value.length
			) {
				// selectedTable.value.forEach((sedRow: any) => {
				// 	tableDataSelf.value.forEach((row: any) => {
				// 		if (sedRow.id === row.id) {
				// 			pitayaTableRef.value!.toggleRowSelection(row, true)
				// 		}
				// 	})
				// })
			}
		}, 200)
	},
	{
		deep: true,
		immediate: true
	}
)

// 返回所有选中数据
const getSelectedTable = () => {
	// 调用这个方法 通常是在点击 保存 按钮的时候，当前页重置为 1 职位下次显示使用
	usePaginationStoreForData.currentPage = 1
	return uniqBy(selectedTableData.value, props.selectKey)
}
// 清除选中
const clearSelectedTableData = () => {
	pitayaTableRef.value!.clearSelection()
}
// 重置页码信息
const resetPageInfo = () => {
	usePaginationStoreForData.currentPage = 1
}
// 恢复之前选中项
const recoveryOperation = () => {
	selectedTableData.value = thisOpenSelectTableData
}

// 暴露ref
defineOptions({
	name: "PitayaTable"
})

const maxTableHeight = ref(704)

onMounted(() => {
	clearSelectedTableData()
})

defineExpose({
	pitayaTableRef,
	currentPagechange,
	handleSizeChange,
	getSelectedTable,
	clearSelectedTableData,
	resetPageInfo,
	recoveryOperation
})
</script>
<template>
	<div :class="['pitaya-table', singleSelect ? 'only-select-wrapper' : '']">
		<el-table
			border
			lazy
			row-key="id"
			ref="pitayaTableRef"
			:data="tableDataSelf"
			:tree-props="treePropsSelf"
			:load="tableDataOnLoad"
			:show-overflow-tooltip="true"
			default-expand-all
			@selection-change="handleSelectionChange"
			@select="select"
			@select-all="selectAll"
			@sort-change="onSortChange"
			v-loading="tableLoading"
			style="--el-table-border-color: #ccc"
			:max-height="maxTableHeight"
		>
			<el-table-column
				v-if="props.needSelection"
				type="selection"
				width="32"
				align="center"
				fixed
			/>
			<el-table-column
				v-if="props.needIndex"
				align="center"
				type="index"
				:index="getTableIndex"
				label="序号"
				width="65"
				fixed
			/>
			<el-table-column
				v-for="(column, index) in columnsSelf"
				:key="index"
				:align="column.align ?? 'center'"
				:label="column.label"
				:prop="column.prop"
				:width="column.width ?? ''"
				:min-width="column.minWidth ?? ''"
				:class-name="column.class"
				:fixed="column.fixed"
				:sortable="column.sortable"
				header-align="center"
				empty-text="暂无数据"
			>
				<template #default="scope">
					<div class="column-inner-item" v-if="!column.needSlot">
						{{
							scope.row[column.prop] ||
							(scope.row[column.prop] === 0 ? 0 : "---")
						}}
					</div>
					<slot
						v-if="column.needSlot"
						:name="column.prop"
						:rowData="scope.row"
						:$index="scope.$index"
					/>
				</template>
			</el-table-column>
		</el-table>
		<div class="table-footer-operate">
			<div class="footer-operate-left">
				<slot name="footerOperateLeft" />
			</div>
			<div
				class="footer-operate-pagination"
				v-if="props.needPagination && props.total && props.total > 0"
			>
				<el-pagination
					@current-change="currentPagechange"
					@size-change="handleSizeChange"
					v-model:current-page="usePaginationStoreForData.currentPage"
					v-model:page-size="usePaginationStoreForData.pageSize"
					:total="total"
					:page-sizes="[10, 20, 30, 40, 50, 100]"
					:pager-count="pagerCount"
					:layout="layout"
					prev-text="上一页"
					next-text="下一页"
				>
					<button class="jumper-slot-btn">GO</button>
				</el-pagination>
			</div>
		</div>
	</div>
</template>
<style lang="scss" scoped>
.pitaya-table {
	margin-top: var(--pitaya-fs-12);
	padding: 0 10px;
	height: 704px;
	overflow: auto;
	::v-deep(th.el-table__cell) {
		background-color: #f6f6f6 !important;
		color: #000;
		user-select: inherit;
	}
	::deep(.cell) {
		user-select: inherit !important;
	}
	::v-deep(.cell > div) {
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		user-select: inherit;
	}
	::v-deep(.tree-cell-flex:not(.is-center)) {
		.cell.el-tooltip {
			display: flex;
			align-items: center;
		}
	}
	.table-footer-operate {
		display: flex;
		justify-content: space-between;
		margin-top: 10px;
		padding-bottom: 10px;
		.footer-operate-pagination {
			display: flex;
			align-items: center;
			.jumper-slot-btn {
				display: flex;
				align-items: center;
				justify-content: center;
				border: 1px solid var(--pitaya-border-color);
				border-left: none;
				width: 32px;
				height: 32px;
				font-size: var(--pitaya-fs-12);
				color: #fff;
				background-color: var(--pitaya-btn-background);
				border-radius: 0;
				cursor: pointer;
				&:hover {
					background-color: var(--pitaya-hover-btn-background);
				}
				&:active {
					background-color: var(--pitaya-active-btn-background);
				}
			}
			:deep(.el-input__wrapper) {
				padding: 1px 10px;
			}
			:deep(.el-pager) {
				.number,
				.more {
					border: 1px solid var(--pitaya-border-color);
					border-left: none;
					border-radius: 0;
				}
				.is-active {
					background-color: #0a4e9a;
					color: #fff;
					border-color: #0a4e9a;
				}
			}
			:deep(.el-pager) {
				li {
					font-size: var(--pitaya-fs-12);
				}
			}
			:deep(.btn-prev),
			:deep(.btn-next) {
				height: 32px;
				width: 70px;
				text-align: center;
				line-height: 32px;
				border: 1px solid var(--pitaya-btn-background) !important;
				background-color: var(--pitaya-btn-background);
				color: #fff;
				border-radius: 0;
				span {
					font-size: var(--pitaya-fs-12);
				}
				&:hover {
					background-color: var(--pitaya-hover-btn-background);
				}
				&:active {
					background-color: var(--pitaya-active-btn-background);
				}
				&:disabled {
					border: 1px solid var(--pitaya-border-color) !important;
				}
			}
			:deep(.btn-next) {
				border-left: none !important;
			}
			:deep(.btn-prev) {
				border-right: none !important;
			}
			:deep(.el-pagination__sizes) {
				margin-left: 10px;
				.el-input {
					width: 100px;
					font-size: var(--pitaya-fs-12);
				}
				.el-input__wrapper {
					border-radius: 0px;
				}
			}
			:deep(.el-pagination__jump) {
				margin-left: 10px;
				.el-pagination__goto,
				.el-pagination__classifier {
					display: none;
				}
				.el-input__wrapper {
					padding: 0 10px;
					border-radius: 0px;
					box-shadow: none;
					border: 1px solid var(--pitaya-border-color);
					border-right: none;
				}
			}
		}
	}
}
</style>
@/app/platform/hooks/usePagination
