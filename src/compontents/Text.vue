<script setup lang="ts">
const props = defineProps({
	value: {
		type: String,
		default: ""
	},
	maxLength: {
		type: Number,
		default: 20
	}
})
defineOptions({
	name: "TextView"
})
</script>
<template>
	<span v-if="props.value.length <= props.maxLength">{{ props.value }}</span>
	<el-tooltip
		popper-class="w350"
		class="box-item"
		effect="dark"
		v-else
		:content="props.value"
	>
		{{ props.value.slice(0, 20) + "..." }}
	</el-tooltip>
</template>
