<script setup lang="ts">
import {
	reactive,
	toRef,
	ref,
	watchEffect,
	computed,
	watch,
	onMounted,
	onUnmounted,
	nextTick
} from "vue"
import { storeToRefs } from "pinia"
import { ArrowUp, ArrowDown } from "@element-plus/icons-vue"
import { useQueryStore } from "@/app/platform/store/queryComponent"
import { useRoute } from "vue-router"
import pinyin from "pinyin"
import UserSelect from "./UserSelect.vue"
import TabelSelect from "./TabelSelect.vue"
import { ElMessage } from "element-plus"

type anyKey = Record<string, any>
export interface querySetting {
	key: string
	name?: string
	type: string
	placeholder?: string
	disabled?: boolean
	children?: Array<{ label: string; value: any }>
	[key: string]: any
}

const currentRoute = useRoute()

interface Props {
	queryArrList: querySetting[]
	// 每行显示多少个，超出该数量，显示“展开”按钮
	numInRow?: number
	queryBtnColSpan?: number // 查询按钮区域自适应宽度
	namePlace?: string // 一个页面中使用多个Query组件时，为不同的Query组件设置不同的命名空间
}

const props = withDefaults(defineProps<Props>(), {
	numInRow: 4, // 默认一行展示4个（遵循Grid布局）
	queryBtnColSpan: 4
})

const queryStore = useQueryStore()
const { expand, tableClassName } = storeToRefs(queryStore)

const queryArrList = toRef(props, "queryArrList")
const namePlace = toRef(props, "namePlace")
console.log("表格对象", props)

const queryData = reactive<anyKey>({})
const customQueryData = reactive<anyKey>({})
// 是否展开
const expandState = ref(false)
const drawerTreeSize = ref(310)
// 树抽屉状态
const drawerTreeState = ref(false)
const drawerTree = ref()
// 树选中id值
const selectIds = ref<string[]>([])
const selectIdsObj = ref<anyKey>({})

const drawerTreeTitle = ref({
	name: ["选择专业"],
	icon: ["fas", "square-share-nodes"]
})
const treeApi = ref<Function | undefined>(() => {})
const checkStrictly = ref(false)
const needSingleSelect = ref(false)
const tempItem = ref<Partial<querySetting>>({}) // 存储当前打开drawerTree的item

/**
 * 用户选择相关参数
 */
const userSelectRef = ref()

const drawerTableSize = ref(720)
const drawerTableState = ref(false)
const columns = ref<any[]>([])
const tableRef = ref()
const tableLoading = ref(false)
const dataTotal = ref(0)
const tableData = ref<any[]>([])
const selectedData = ref<any[]>([])
const drawerTableTitle = ref({
	name: ["请选择"],
	icon: ["fas", "square-share-nodes"]
})
const drawerBtnList = [
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "保存",
		icon: ["fas", "floppy-disk"]
	}
]

const emit = defineEmits<{
	(e: "getQueryData", queryData: anyKey, customQueryData: anyKey): void
	(e: "singleQueryData", key: string, queryData: anyKey): void
}>()
const handleReset = () => {
	Object.keys(queryData).map((key) => (queryData[key] = ""))
	Object.keys(customQueryData).map((key) => (customQueryData[key] = null))
	selectIdsObj.value = {}
	queryData.currentPage = 1
	emit("getQueryData", queryData, customQueryData)
	drawerTree.value?.resetChecked()
}
// 仅清空查询参数，不执行查询（避免多次查询）
const clearQueryData = (item: any) => {
	if (item) {
		queryData.currentPage = 1
		const formData = {
			...queryData
		}

		Object.keys(selectIdsObj.value).map((item: any) => {
			formData[item] = Array.isArray(selectIdsObj.value[item])
				? selectIdsObj.value[item].join(",")
				: selectIdsObj.value[item]
		})
		// 模糊查询
		queryArrList.value.forEach((v) => {
			if (v.type === "input" && v.enableFuzzy !== false) {
				if (formData[v.key]) {
					formData[v.key] = `*${formData[v.key].trim()}*`
				}
			}
		})

		Object.keys(formData).map((key) => {
			if (item.key == key) {
				formData[key] = ""
				customQueryData[key] = null
				if (selectIdsObj.value[key]) {
					Array.isArray(selectIdsObj.value[key])
						? (selectIdsObj.value[key] = [])
						: (selectIdsObj.value[key] = "")
				}
			}
		})
		emit("getQueryData", formData, customQueryData)
	} else {
		Object.keys(queryData).map((key) => (queryData[key] = ""))
		Object.keys(customQueryData).map((key) => (customQueryData[key] = null))
		selectIdsObj.value = {}
	}
}
//获取到queryArrList，处理组件，初始化值事件
const queryArrListInit = () => {
	queryArrList.value.map((item) => {
		if (item.paramsData) {
			queryData[`${item.key}`] = item.paramsData
		}
	})
}
queryArrListInit()
const onSubmit = () => {
	queryData.currentPage = 1
	const formData = {
		...queryData
	}
	Object.keys(selectIdsObj.value).map((item: any) => {
		formData[item] = Array.isArray(selectIdsObj.value[item])
			? selectIdsObj.value[item].join(",")
			: selectIdsObj.value[item]
	})
	// 模糊查询
	queryArrList.value.forEach((v) => {
		if (v.type === "input" && v.enableFuzzy !== false) {
			if (formData[v.key]) {
				formData[v.key] = `*${formData[v.key].trim()}*`
			}
		}
	})
	// console.log("查询参数", formData)
	emit("getQueryData", formData, customQueryData)
}
const btnList = [
	{
		name: "查询",
		icon: ["fas", "magnifying-glass"]
	},
	{
		name: "重置",
		icon: ["fas", "rotate"]
	}
]

watch(
	// 切换模块，展开自动收起
	() => currentRoute.path,
	() => {
		if (expandState.value) {
			try {
				handleExpand()
			} catch (err) {
				console.log("Query组件：", err)
			}
		}
	},
	{ immediate: true, deep: true }
)

const onTremBtnClick = (btnName: string | undefined) => {
	if (btnName === "查询") {
		onSubmit()
		return
	}

	if (btnName === "重置") {
		handleReset()
		return
	}
}

// 展开/收起
// Query状态管理
const handleExpand = () => {
	expand.value = !expand.value
	expandState.value = !expandState.value

	if (expandState.value) {
		if (props.numInRow) {
			// 一行
			if (queryArrList.value.length <= props.numInRow) {
				tableClassName.value = "one-query-rows"
			}
			// 二行
			if (
				queryArrList.value.length > props.numInRow &&
				queryArrList.value.length <= props.numInRow * 2
			) {
				tableClassName.value = "two-query-rows"
			}
			// 三行
			if (
				queryArrList.value.length > 2 * props.numInRow &&
				queryArrList.value.length <= props.numInRow * 3
			) {
				tableClassName.value = "three-query-rows"
			}
			// 四行
			if (
				queryArrList.value.length > 3 * props.numInRow &&
				queryArrList.value.length <= props.numInRow * 4
			) {
				tableClassName.value = "four-query-rows"
			}
			// 五行
			if (
				queryArrList.value.length > 4 * props.numInRow &&
				queryArrList.value.length <= props.numInRow * 5
			) {
				tableClassName.value = "five-query-rows"
			}
			// 六行
			if (
				queryArrList.value.length > 4 * props.numInRow &&
				queryArrList.value.length <= props.numInRow * 6
			) {
				tableClassName.value = "six-query-rows"
			}
		} else {
			tableClassName.value = "one-query-rows"
		}
	} else {
		tableClassName.value = "one-query-rows"
	}
	let hiddenArr = null
	if (namePlace.value) {
		// 选择隐藏的项
		hiddenArr = document.querySelectorAll(
			`.${namePlace.value} .query-item-disabled`
		) as NodeListOf<HTMLElement>
	} else {
		// 选择隐藏的项
		hiddenArr = document.querySelectorAll(
			".query-item-disabled"
		) as NodeListOf<HTMLElement>
	}
	if (expandState.value) {
		hiddenArr.forEach((item) => {
			item.style.display = "block"
		})
	} else {
		hiddenArr.forEach((item) => {
			item.style.display = "none"
		})
	}
}

watchEffect(() => {
	if (!expandState.value) {
		tableClassName.value = "one-query-rows"
	}
})
// 当组件被插入到 DOM 中时调用。
onMounted(async () => {
	console.log("Query组件已加载至DOM")
	tableClassName.value = "one-query-rows"
})

// 当组件从 DOM 中被移除时调用。
onUnmounted(async () => {
	console.log("Query组件已从DOM卸载")
	tableClassName.value = ""
})

const openDrawerTree = (item: querySetting) => {
	tempItem.value = {}
	tempItem.value = item
	drawerTreeState.value = true
	treeApi.value = item.treeApi
	drawerTreeTitle.value.name = [`${item.name}`]
	checkStrictly.value = true
	if (item.name === "设备分级") {
		needSingleSelect.value = true // 暂时为单选，后续可能要支持多选，改为false即可
	} else {
		needSingleSelect.value = item.needSingleSelect ?? true
	}
}

// 接收树查询ID值
const onTreeBtnClick = (data: string[] | undefined, selectNodes: any[]) => {
	emit("singleQueryData", tempItem.value.key, selectNodes)
	if (selectNodes) {
		if (tempItem.value.customQuery) {
			customQueryData[`${tempItem.value.key}`] = selectNodes
			queryData[`${tempItem.value.key}`] = selectNodes
				.map((item) => item.allName)
				.join(", ")
			drawerTreeState.value = false
			return
		}
		// 查询项显示名称处理
		queryData[`${tempItem.value.key}`] = selectNodes
			.map((item) => item.allName)
			.join(", ")
		// 接口所需参数处理
		if (tempItem.value.replaceIdTo) {
			selectIds.value = selectNodes
				.filter((item) => item.id !== 0)
				.map((item) => item[tempItem.value.replaceIdTo])
		} else {
			selectIds.value = selectNodes
				.filter((item) => item.id !== 0)
				.map((item) => item.id)
		}
		selectIdsObj.value[tempItem.value.key] = selectIds.value
		// 一种查询条件多参数情况
		if (tempItem.value.extraParams) {
			for (const attr in tempItem.value.extraParams) {
				// 设备分级pathId
				if (attr === "deviceLevelPath") {
					selectIdsObj.value[attr] = selectNodes[0].pathId
				}
			}
		}
	} else {
		if (queryData[`${tempItem.value.key}`]) {
			drawerTree.value?.resetChecked()
			if (drawerTree.value && drawerTree.value.pitayaTreeRef) {
				drawerTree.value?.pitayaTreeRef.setCheckedKeys(
					selectIdsObj.value[tempItem.value.key]
				)
			}
		} else {
			selectIds.value = []
			selectIdsObj.value[tempItem.value.key] = []
			drawerTree.value?.resetChecked()
		}
	}
	drawerTreeState.value = false
}

/**
 * 抽屉关闭前回调
 */
function handleBeforeClose() {
	if (selectIdsObj.value[tempItem.value.key]) {
		drawerTree.value?.resetChecked()
		if (drawerTree.value && drawerTree.value.pitayaTreeRef) {
			drawerTree.value?.pitayaTreeRef.setCheckedKeys(
				selectIdsObj.value[tempItem.value.key]
			)
		}
	} else {
		selectIds.value = []
		selectIdsObj.value[tempItem.value.key] = []
		drawerTree.value?.resetChecked()
	}
	drawerTreeState.value = false
}

// 抽屉表格按钮点击回调
const onDrawerBtnClick = (btnName: string) => {
	if (btnName === "保存") {
		if (!selectedData.value.length) {
			ElMessage.warning("请勾选数据")
			return
		}
		queryData[`${tempItem.value.key}`] = selectedData.value
			.map((item) => item.code)
			.join(", ")
		selectIds.value = selectedData.value
			.filter((item) => item.id !== 0)
			.map((item) => item.id)
		selectIdsObj.value[tempItem.value.key] = selectIds.value
	}
	drawerTableState.value = false
}

// 一个计算属性 ref
const checkedKeys = computed(() => {
	return selectIdsObj.value[tempItem.value?.key] || []
})

const tableKeys = computed(() => {
	return selectIdsObj.value[tempItem.value?.key] || []
})

// 专门用于存储搜索文本
const searchText = ref("")

// 拼音搜索过滤方法
const handleFilter = (val: string, item: any) => {
	if (item.type !== "inputRealname") return
	searchText.value = val.trim().toLowerCase()
	if (!item.multiple) {
		queryData[`${item.key}`] = searchText.value
	}
	return searchText.value
}

// 获取过滤后的选项
const filteredOptions = (item: any) => {
	if (item.type !== "inputRealname") return item.children || []

	const input = searchText.value

	if (!input) return item.children || []

	return item.children.filter((option: any) => {
		const label = option.label

		// 1. 中文完全匹配
		if (label.toLowerCase() === input) {
			return true
		}

		// 2. 中文部分匹配
		if (label.toLowerCase().includes(input)) {
			return true
		}

		// 3. 拼音全拼匹配
		const fullPinyin = pinyin(label, {
			style: pinyin.STYLE_NORMAL,
			heteronym: false
		})
			.reduce((acc, cur) => acc.concat(cur), [])
			.join("")

		if (
			fullPinyin.toLowerCase() === input ||
			fullPinyin.toLowerCase().includes(input)
		) {
			return true
		}

		// 4. 拼音首字母匹配
		const firstLetterPinyin = pinyin(label, {
			style: pinyin.STYLE_FIRST_LETTER,
			heteronym: false
		})
			.reduce((acc, cur) => acc.concat(cur), [])
			.join("")

		if (
			firstLetterPinyin.toLowerCase() === input ||
			firstLetterPinyin.toLowerCase().includes(input)
		) {
			return true
		}

		return false
	})
}

const onCurrentPageChange = (pageData: any) => {
	console.log(pageData)
}
// 抽屉表格勾选回调
const getSelectionTableList = (rowList: any) => {
	selectedData.value = rowList
}

const handleDrawerTreeClose = () => {
	drawerTreeTitle.value.name = [""]
}

// 打开用户选择弹窗
const openUserSelect = (item: any) => {
	tempItem.value = Object.assign({}, item)
	userSelectRef.value.open(item)
}

// 回调：用户选择
const onUserSubmit = (user: any) => {
	// console.log(tempItem.value)
	// console.log("所选用户信息", user)
	if (tempItem.value.replaceIdTo) {
		queryData[`${tempItem.value.key}`] = user.realname
		selectIds.value = [user[tempItem.value.replaceIdTo]]
	} else {
		queryData[`${tempItem.value.key}`] = user.realname
		selectIds.value = [user.username]
	}
	selectIdsObj.value[tempItem.value.key] = selectIds.value
}

/***
 *
 *
 * 抽屉表格相关参数
 *
 *
 */
const tableSelectRef = ref()

//设备表格回显key
const tableDataKeys = computed(() => {
	return selectIdsObj.value[tableItem.value?.key] || []
})

// 回调：表格选择
const onTableSubmit = (data: any) => {
	queryData[`${tableItem.value.key}`] = data.label
	selectIds.value = [data.value]
	selectIdsObj.value[tableItem.value.key] = selectIds.value
}

// 打开抽屉表格
const tableItem = ref<Partial<querySetting>>({})
const openDrawerTable = (item: any) => {
	tableItem.value = Object.assign({}, item)
	tableSelectRef.value.open(item)
}
/**
 * 快捷选项数组
 * days - 包含天数的数组，例如 [7, 14, 30, 90]
 * 返回快捷选项对象数组
 */
const generateShortcuts = (days: number[]) => {
	return days.map((day: number) => ({
		text: `最近 ${day} 天`,
		value: () => {
			const end = new Date()
			const start = new Date(end)
			start.setDate(end.getDate() - (day - 1))
			return [start, end]
		}
	}))
}
defineExpose({
	handleReset,
	clearQueryData,
	queryData
})
</script>

<template>
	<div>
		<!-- 查询条件>3 需要展开/收起按钮 -->
		<div
			class="common-query common-query-all"
			:class="namePlace"
			v-if="queryArrList.length > 3"
			@keyup.enter="onSubmit"
		>
			<el-row style="width: 100%">
				<!-- 控件区域 -->
				<el-col class="constrols" :span="24 - queryBtnColSpan">
					<el-row>
						<el-col
							:span="24 / numInRow"
							v-for="(item, index) in queryArrList"
							:key="index"
							:class="{ 'query-item-disabled': index + 1 > numInRow }"
						>
							<div class="query-item">
								<span
									class="query-label col"
									v-if="item.name"
									:title="item.name"
								>
									{{
										item.name.length <= 6
											? item.name
											: item.name.slice(0, 6) + "..."
									}}
								</span>
								<el-date-picker
									class="my-query-picker"
									v-if="item.type === 'startAndEndTime'"
									v-model="queryData[`${item.key}`]"
									type="daterange"
									value-format="YYYY-MM-DD"
									range-separator="至"
									:disabled-date="item.disabledDate"
									start-placeholder="开始时间"
									end-placeholder="结束时间"
									clearable
									style="width: 100%"
									:disabled="item.disabled"
									@clear="clearQueryData(item)"
									:shortcuts="
										item.shortcuts ? generateShortcuts(item.shortcuts) : []
									"
								/>
								<el-date-picker
									class="my-query-picker"
									v-if="item.type === 'dateTime'"
									v-model="queryData[`${item.key}`]"
									type="date"
									value-format="YYYY-MM-DD"
									:placeholder="item.placeholder"
									:disabled-date="item.disabledDate"
									style="width: 100%"
									:disabled="item.disabled"
									@clear="clearQueryData(item)"
								/>
								<el-date-picker
									class="my-query-picker"
									v-if="item.type === 'year'"
									v-model="queryData[`${item.key}`]"
									type="year"
									value-format="YYYY"
									:placeholder="item.placeholder"
									:disabled-date="item.disabledDate"
									clearable
									style="width: 100%"
									:disabled="item.disabled"
									@clear="clearQueryData(item)"
								/>
								<el-input
									class="my-query-input"
									v-if="item.type === 'input'"
									style="width: 100%"
									v-model.trim="queryData[`${item.key}`]"
									:placeholder="item.placeholder"
									clearable
									:disabled="item.disabled"
									@clear="clearQueryData(item)"
								/>
								<el-tree-select
									class="my-query-select"
									v-if="item.type === 'elTreeSelect'"
									style="width: 100%"
									v-model="queryData[`${item.key}`]"
									:placeholder="item.placeholder"
									:data="item.children"
									:check-strictly="item.checkStrictly"
									clearable
									:disabled="item.disabled"
									@clear="clearQueryData(item)"
								/>
								<el-select
									class="my-query-select"
									v-if="item.type === 'select'"
									style="width: 100%"
									v-model="queryData[`${item.key}`]"
									:placeholder="item.placeholder"
									clearable
									:disabled="item.disabled"
									@clear="clearQueryData(item)"
									filerable
								>
									<el-option
										v-for="(citem, cindex) in item.children"
										:key="`${index}_${cindex}`"
										:label="citem.label"
										:value="citem.value"
									/>
								</el-select>
								<el-select
									class="my-query-select"
									v-if="item.type === 'inputRealname'"
									style="width: 100%"
									v-model="queryData[`${item.key}`]"
									:placeholder="item.placeholder"
									clearable
									:disabled="item.disabled"
									@clear="clearQueryData(item)"
									filterable
									:collapse-tags="item.collapseTags || false"
									:multiple="item.multiple || false"
									:filter-method="(val: string) => handleFilter(val, item)"
									@change="
										(val) => {
											if (item.multiple && Array.isArray(val)) {
												const selectedItems = val
													.map((v) =>
														item.children?.find((opt) => opt.value === v)
													)
													.filter(Boolean)
												nextTick(() => {
													queryData[item.key] = selectedItems.map(
														(item) => item.label
													)
													searchText.value = ''
												})
											} else if (!item.multiple && typeof val === 'string') {
												const selected = item.children?.find(
													(opt) => opt.value === val
												)
												if (selected) {
													nextTick(() => {
														queryData[item.key] = selected.label
														searchText.value = ''
													})
												} else {
													queryData[item.key] = ''
												}
											}
										}
									"
								>
									<el-option
										v-for="citem in filteredOptions(item)"
										:key="citem.value"
										:label="citem.label"
										:value="citem.value"
										@click.native="searchText.value = ''"
									/>
								</el-select>
								<el-input
									class="my-query-input"
									v-if="item.type === 'treeSelect'"
									style="width: 100%"
									v-model="queryData[`${item.key}`]"
									:placeholder="item.placeholder"
									clearable
									@click="openDrawerTree(item)"
									:readonly="item.readonly"
									:disabled="item.disabled"
									@clear="clearQueryData(item)"
								>
									<template #append>
										<el-button
											v-btn
											link
											@click="item.disabled ? '' : openDrawerTree(item)"
										>
											<font-awesome-icon
												:icon="['fas', 'desktop']"
												style="color: var(--pitaya-place-font-color)"
											/>
										</el-button>
									</template>
								</el-input>

								<el-cascader
									v-if="item.type === 'cascader'"
									v-model="queryData[`${item.key}`]"
									:placeholder="item.placeholder"
									:options="item.children"
									clearable
									style="width: 100%"
									:disabled="item.disabled"
									@clear="clearQueryData(item)"
								/>
								<el-input
									class="my-query-input"
									v-if="item.type === 'userSelect'"
									style="width: 100%"
									v-model="queryData[`${item.key}`]"
									:placeholder="item.placeholder"
									clearable
									@click="openUserSelect(item)"
									readonly
									:disabled="item.disabled"
									@clear="clearQueryData(item)"
								>
									<template #append>
										<el-button v-btn link @click="openUserSelect(item)">
											<font-awesome-icon
												:icon="['fas', 'desktop']"
												style="color: var(--pitaya-place-font-color)"
											/>
										</el-button>
									</template>
								</el-input>
								<el-input
									class="my-query-input"
									v-if="item.type === 'tableSelect'"
									style="width: 100%"
									v-model="queryData[`${item.key}`]"
									:placeholder="item.placeholder"
									clearable
									@click="openDrawerTable(item)"
									readonly
									:disabled="item.disabled"
									@clear="clearQueryData(item)"
								>
									<template #append>
										<el-button v-btn link @click="openDrawerTable(item)">
											<font-awesome-icon
												:icon="['fas', 'desktop']"
												style="color: var(--pitaya-place-font-color)"
											/>
										</el-button>
									</template>
								</el-input>
							</div>
						</el-col>
					</el-row>
				</el-col>
				<!-- 查询按钮区域 -->
				<el-col class="action-btns" :span="queryBtnColSpan">
					<span class="query-btn-wrapper">
						<ButtonList :button="btnList" @onBtnClick="onTremBtnClick" />
						<a
							class="expand"
							@click="handleExpand"
							v-if="queryArrList.length > numInRow"
						>
							<el-icon>
								<component :is="expandState ? ArrowUp : ArrowDown" />
							</el-icon>
							<!-- {{ expandState ? "收起" : "展开" }} -->
						</a>
					</span>
				</el-col>
			</el-row>
		</div>
		<!-- 查询条件<=3 不需要展开/收起按钮 -->
		<div class="common-query-wrapper common-query-all" v-else>
			<div
				@keyup.enter="onSubmit"
				class="query-item notCol"
				v-for="(item, index) in queryArrList"
				:key="index"
			>
				<span class="query-label" v-if="item.name" :title="item.name">
					{{
						item.name.length <= 6 ? item.name : item.name.slice(0, 6) + "..."
					}}
				</span>
				<el-date-picker
					class="my-query-picker"
					v-if="item.type === 'startAndEndTime'"
					v-model="queryData[`${item.key}`]"
					type="daterange"
					value-format="YYYY-MM-DD"
					range-separator="至"
					:disabled-date="item.disabledDate"
					start-placeholder="开始时间"
					end-placeholder="结束时间"
					clearable
					:disabled="item.disabled"
					style="width: 252px"
					@clear="clearQueryData(item)"
					:shortcuts="item.shortcuts ? generateShortcuts(item.shortcuts) : []"
				/>
				<el-date-picker
					class="my-query-picker"
					v-if="item.type === 'dateTime'"
					v-model="queryData[`${item.key}`]"
					type="date"
					value-format="YYYY-MM-DD"
					:placeholder="item.placeholder"
					:disabled-date="item.disabledDate"
					clearable
					:disabled="item.disabled"
					style="width: 200px"
					@clear="clearQueryData(item)"
				/>
				<el-date-picker
					class="my-query-picker"
					v-if="item.type === 'year'"
					v-model="queryData[`${item.key}`]"
					type="year"
					value-format="YYYY"
					:placeholder="item.placeholder"
					:disabled-date="item.disabledDate"
					clearable
					style="width: 200px"
					:disabled="item.disabled"
					@clear="clearQueryData(item)"
				/>
				<el-input
					class="my-query-input"
					v-if="item.type === 'input'"
					:style="{ width: item.width ? item.width + 'px' : 200 + 'px' }"
					v-model.trim="queryData[`${item.key}`]"
					:placeholder="item.placeholder"
					clearable
					:disabled="item.disabled"
					@clear="clearQueryData(item)"
				/>
				<el-tree-select
					class="my-query-select"
					v-if="item.type === 'elTreeSelect'"
					:style="{ width: item.width ? item.width + 'px' : 200 + 'px' }"
					v-model="queryData[`${item.key}`]"
					:placeholder="item.placeholder"
					:data="item.children"
					:check-strictly="item.checkStrictly"
					clearable
					:disabled="item.disabled"
					@clear="clearQueryData(item)"
				/>
				<el-select
					class="my-query-select"
					v-if="item.type === 'select'"
					:style="{ width: item.width ? item.width + 'px' : 200 + 'px' }"
					v-model="queryData[`${item.key}`]"
					:placeholder="item.placeholder"
					clearable
					:disabled="item.disabled"
					@clear="clearQueryData(item)"
					filerable
				>
					<el-option
						v-for="(citem, cindex) in item.children"
						:key="`${index}_${cindex}`"
						:label="citem.label"
						:value="citem.value"
					/>
				</el-select>
				<el-select
					class="my-query-select"
					v-if="item.type === 'inputRealname'"
					:style="{ width: item.width ? item.width + 'px' : 200 + 'px' }"
					v-model="queryData[`${item.key}`]"
					:placeholder="item.placeholder"
					clearable
					:disabled="item.disabled"
					@clear="clearQueryData(item)"
					filterable
					:collapse-tags="item.collapseTags || false"
					:multiple="item.multiple || false"
					:filter-method="(val: string) => handleFilter(val, item)"
					@change="
						(val) => {
							if (item.multiple && Array.isArray(val)) {
								const selectedItems = val
									.map((v) => item.children?.find((opt) => opt.value === v))
									.filter(Boolean)
								nextTick(() => {
									queryData[item.key] = selectedItems.map((item) => item.label)
									searchText.value = ''
								})
							} else if (!item.multiple && typeof val === 'string') {
								const selected = item.children?.find((opt) => opt.value === val)
								if (selected) {
									nextTick(() => {
										queryData[item.key] = selected.label
										searchText.value = ''
									})
								} else {
									queryData[item.key] = ''
								}
							}
						}
					"
				>
					<el-option
						v-for="citem in filteredOptions(item)"
						:key="citem.value"
						:label="citem.label"
						:value="citem.value"
						@click.native="searchText.value = ''"
					/>
				</el-select>
				<el-input
					class="my-query-input"
					v-if="item.type === 'treeSelect'"
					:style="{ width: item.width ? item.width + 'px' : 200 + 'px' }"
					v-model="queryData[`${item.key}`]"
					:placeholder="item.placeholder"
					clearable
					@click="openDrawerTree(item)"
					:readonly="item.readonly"
					:disabled="item.disabled"
					@clear="clearQueryData(item)"
				>
					<template #append>
						<el-button
							v-btn
							link
							@click="item.disabled ? '' : openDrawerTree(item)"
						>
							<font-awesome-icon
								:icon="['fas', 'desktop']"
								style="color: var(--pitaya-place-font-color)"
							/>
						</el-button>
					</template>
				</el-input>
				<el-input
					class="my-query-input"
					v-if="item.type === 'tableSelect'"
					:style="{ width: item.width ? item.width + 'px' : 200 + 'px' }"
					v-model="queryData[`${item.key}`]"
					:placeholder="item.placeholder"
					clearable
					@click="openDrawerTable(item)"
					readonly
					:disabled="item.disabled"
					@clear="clearQueryData(item)"
				>
					<template #append>
						<el-button v-btn link @click="openDrawerTable(item)">
							<font-awesome-icon
								:icon="item?.icon || ['fas', 'desktop']"
								style="color: var(--pitaya-place-font-color)"
							/>
						</el-button>
					</template>
				</el-input>
				<el-cascader
					v-if="item.type === 'cascader'"
					v-model="queryData[`${item.key}`]"
					:placeholder="item.placeholder"
					:options="item.children"
					clearable
					:disabled="item.disabled"
					:style="{ width: item.width ? item.width + 'px' : 200 + 'px' }"
					@clear="clearQueryData(item)"
				/>
				<el-input
					class="my-query-input"
					v-if="item.type === 'userSelect'"
					:style="{ width: item.width ? item.width + 'px' : 200 + 'px' }"
					v-model="queryData[`${item.key}`]"
					:placeholder="item.placeholder"
					clearable
					@click="openUserSelect(item)"
					readonly
					:disabled="item.disabled"
					@clear="clearQueryData(item)"
				>
					<template #append>
						<el-button v-btn link @click="openUserSelect(item)">
							<font-awesome-icon
								:icon="['fas', 'desktop']"
								style="color: var(--pitaya-place-font-color)"
							/>
						</el-button>
					</template>
				</el-input>
			</div>
			<span class="query-btn-wrapper">
				<ButtonList :button="btnList" @onBtnClick="onTremBtnClick" />
			</span>
		</div>
		<!-- 抽屉树 -->
		<Drawer
			class="inner-drawer drawer-hidden-box"
			:destroyOnClose="true"
			:size="drawerTreeSize"
			v-model:drawer="drawerTreeState"
			@close="handleDrawerTreeClose"
			:isBeforeClose="true"
			@before-close="handleBeforeClose"
		>
			<Title :title="drawerTreeTitle" />
			<DrawerTree
				ref="drawerTree"
				:title="drawerTreeTitle.name[0]"
				:drawerState="drawerTreeState"
				@onBtnClick="onTreeBtnClick"
				:multi-select="true"
				:checked-keys="checkedKeys"
				:apifunc="treeApi"
				:checkStrictly="checkStrictly"
				:needSingleSelect="needSingleSelect"
				:type="treeApi.name === 'getInfoTreeApi' ? '位置信息' : ''"
				:params="tempItem.queryParams ? tempItem.queryParams : {}"
			/>
		</Drawer>
		<!-- 抽屉表格 -->
		<Drawer
			class="inner-drawer"
			:size="drawerTableSize"
			v-model:drawer="drawerTableState"
		>
			<div class="common-from-wrapper common-from-only">
				<Title :title="drawerTableTitle" />
				<div class="common-from-group" style="padding: 0px">
					<el-scrollbar>
						<PitayaTable
							ref="tableRef"
							:columns="columns"
							:table-data="tableData"
							:need-index="true"
							:single-select="true"
							:need-selection="true"
							:need-pagination="true"
							:total="dataTotal"
							@on-current-page-change="onCurrentPageChange"
							:table-loading="tableLoading"
							@onSelectionChange="getSelectionTableList"
							:selectedTableData="selectedData"
						/>
					</el-scrollbar>
				</div>
				<div class="btn-groups">
					<ButtonList :button="drawerBtnList" @onBtnClick="onDrawerBtnClick" />
				</div>
			</div>
		</Drawer>
		<!-- 用户选择 -->
		<UserSelect
			ref="userSelectRef"
			@on-submit="onUserSubmit"
			:selected-users="tableKeys"
		/>
		<!-- 表格  -->
		<TabelSelect
			ref="tableSelectRef"
			@on-submit="onTableSubmit"
			:tableDataKeys="tableDataKeys"
		/>
	</div>
</template>

<style lang="scss" scoped>
@import "@/styles/common-from-wrapper.scss";
.common-query-wrapper,
.common-query {
	display: flex;
	flex-wrap: wrap;

	.constrols {
		.query-item-disabled {
			display: none;
		}
		.el-col {
			padding-bottom: 10px;
		}
	}

	.query-item {
		height: 100%;
		display: flex;
		align-items: center;
		margin-right: 10px;
		border: 1px solid #ccc;
		border-radius: var(--el-input-border-radius, var(--el-border-radius-base));

		&.notCol {
			margin-right: 10px;
		}

		.my-query-input {
			:deep(.el-input__wrapper) {
				box-shadow: none !important;
			}
			:deep(.el-input-group__append) {
				border: none;
				border-left: 1px solid #ccc;
				box-shadow: none !important;
			}
		}
		.my-query-select {
			:deep(.el-input__wrapper) {
				box-shadow: none !important;
			}
			:deep(.el-input.is-focus .el-input__wrapper) {
				box-shadow: none !important;
			}
			:deep(.el-input__wrapper.is-focus) {
				box-shadow: none !important;
			}
		}

		:deep(.my-query-picker) {
			box-shadow: none !important;
		}
		:deep(.my-query-picker .el-input__wrapper) {
			box-shadow: none !important;
		}
		:deep(.my-query-picker .el-range-separator) {
			border: none !important;
			background: none !important;
		}

		.query-label {
			min-width: 90px;
			font-size: var(--pitaya-place-font-size);
			color: var(--pitaya-place-font-color) !important;
			text-align: center;
			height: 32px;
			line-height: 32px;
			border-right: 1px solid #ccc;
			border-radius: var(
				--el-input-border-radius,
				var(--el-border-radius-base)
			);
			background-color: #f6f6f6 !important;

			&.col {
				width: 120px;
			}
		}

		:deep(.el-input__wrapper) {
			box-shadow: 0 0 0 1px
				var(--pitaya-border-color, var(--pitaya-border-color)) inset;
		}

		:deep(.el-date-editor--daterange) {
			width: 250px;
		}
		:deep(.el-input),
		:deep(.el-range-input) {
			font-size: var(--pitaya-place-font-size) !important;
		}
		:deep(.el-date-editor) {
			.el-input__icon {
				width: 0;
				flex: 0;
			}
			.el-range__icon {
				display: none;
			}
			.clear-icon {
				width: 14px;
				font-size: 14px;
			}
			.el-range__close-icon--hidden {
				display: block;
			}
			.el-range__close-icon {
				display: block;
				position: absolute;
				right: 20px;
				top: 8px;
			}

			&.el-input__wrapper {
				padding: 0;
			}
			.el-range-input {
				width: 0;
				flex: 1;
			}
			.el-range-separator {
				flex: 0 0 32px;
				background-color: #f6f6f6;
				border: 1px solid var(--pitaya-border-color);
				border-radius: 3px;
				font-size: var(--pitaya-fs-12);
				color: #666666;
				width: 32px;
			}
		}
		.el-row {
			width: 100%;

			.el-col {
				display: flex;
				align-self: center;
				justify-content: flex-end;
			}
		}
	}
	.query-btn-wrapper {
		margin-bottom: 10px;
		// padding-left: 10px;
		display: flex;
		align-items: center;

		a.expand {
			color: #204a9c;
			font-size: 14px;
			// padding: 0 15px;
			cursor: pointer;
			user-select: none;

			width: 30px;
			height: 30px;
			border: 1px solid #204a9c;
			display: flex;
			align-items: center;
			justify-content: center;
			border-radius: 3px;
			margin-left: 12px;
		}
	}
}

.common-query-wrapper {
	display: flex;
	flex-wrap: wrap;
}
</style>
@/app/platform/store/queryComponent
