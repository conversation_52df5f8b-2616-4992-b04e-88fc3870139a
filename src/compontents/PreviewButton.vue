<template>
  <div :class="['preview-button',customClass]" @click="handleClick">
    <slot></slot>
    <!-- 图片预览 -->
    <el-image-viewer
        v-if="showImagePreview"
        :zoom-rate="1.2"
        @close="closePreview"
        :url-list="imgPreviewList"
        hide-on-click-modal
        teleported
        :z-index="9999"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { ElMessage } from "element-plus";

const imgPreviewList = ref<any>([])
const showImagePreview = ref(false)
const props = defineProps({
    customClass: {  // 自定义样式
      type: String,
      default: ''
    },
    fileUrl: {   // 当前查看的内容，数组图片['',''],单个图片，单个pdf等
        type: [String, Array],
        required: true
    }
});
// 文件类型
const fileType = computed(() => {
    if (Array.isArray(props.fileUrl)) {
        return 'image-array';
    }
    const extension = props.fileUrl.split('.').pop()?.toLowerCase() || '';
    if (['png', 'jpg', 'jpeg', 'gif'].includes(extension)) {
        return 'image';
    } else if (['pdf', 'doc', 'docx'].includes(extension)) {
        return 'document';
    }
    return 'unknown';
});

const handleClick = async () => {
    const type = fileType.value;
    if(type === 'image-array'){
        imgPreviewList.value = type === props.fileUrl
        showImagePreview.value = true
    } else if (type === 'document' || type === 'image') {
        window.open(import.meta.env.VITE_BASE_API + "/pitaya/system/common/staticShow/" + props.fileUrl, '_blank');
    } else {
        ElMessage.warning('不支持的文件类型');
    }
};
// 关闭预览
const closePreview = () => {
  imgPreviewList.value = []
  showImagePreview.value = false
}
</script>
<style scoped lang="scss">
.preview-button{
    display:inline;
    cursor: pointer;
    .thumbnail {
        width: 100px; /* 设置合适的大小 */
        height: auto;
        margin-top: 10px;
        cursor: pointer; /* 显示手型光标 */
    }
}
</style>