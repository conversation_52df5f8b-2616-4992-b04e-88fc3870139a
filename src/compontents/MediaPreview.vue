<script lang="ts" setup>
import {
	getFileHttpUrl,
	isImageFile,
	isVideoFile
} from "@/app/platform/utils/common"
import { Close } from "@element-plus/icons-vue"

const emit = defineEmits(["next", "prev"])
const videoRef = ref()

interface Props {
	fileList: any[]
	currentIndex: number
}

const props = withDefaults(defineProps<Props>(), {
	fileList: () => [],
	currentIndex: 0
})

const fileList = toRef(props, "fileList")
const currentIndex = toRef(props, "currentIndex")
const dialogVisible = ref(false)

// 下一张预览
const next = () => {
	console.log(currentIndex.value)
	emit("next")
	if (videoRef.value) {
		videoRef.value.pause()  
		videoRef.value.load()   
	}
}
// 上一张预览
const prev = () => {
	console.log(currentIndex.value)
	emit("prev")
	if (videoRef.value) {
		videoRef.value.pause()  
		videoRef.value.load()   
	}
}

// 打开预览窗口
const open = () => {
	dialogVisible.value = true
}

// 关闭预览窗口
const close = () => {
	dialogVisible.value = false
}

/**
 * 钩子函数
 */
watchEffect(() => {
	console.log(currentIndex.value)
})

defineExpose({
	open,
	close
})
</script>
<template>
	<!-- 图片/视频预览 -->
	<el-dialog v-model="dialogVisible" :show-close="false" class="media-preview">
		<template #header>
			<div class="my-header">
				<el-icon class="close-icon" @click="close"><Close /></el-icon>
			</div>
		</template>
		<img
			v-if="isImageFile(fileList[currentIndex].filePath)"
			class="preview-warapper"
			w-full
			:src="getFileHttpUrl(fileList[currentIndex].filePath)"
			alt="图片预览"
		/>
		<video
			ref="videoRef"
			class="preview-warapper"
			controls
			v-if="isVideoFile(fileList[currentIndex].filePath)"
		>
			<source
				:src="getFileHttpUrl(fileList[currentIndex].filePath)"
				type="video/webm"
			/>
		</video>
		<div class="preview-btns">
			<el-button type="primary" @click="prev()" :disabled="currentIndex === 0"
				>上一个</el-button
			>
			<el-button
				type="primary"
				@click="next()"
				:disabled="currentIndex === fileList.length - 1"
				>下一个</el-button
			>
		</div>
	</el-dialog>
</template>

<style lang="scss" scoped>
.media-preview{
	height: 720px;
}
.preview-warapper {
	width: 100%;
	max-height: 62vh;
}

.preview-btns {
	display: flex;
	justify-content: center;
	margin-top: 10px;
}

.my-header {
	text-align: right;
	cursor: pointer;
	.close-icon {
		font-size: 24px;
		color: #999;
		border-radius: 3px;
		height: 50px;
		width: 50px;
		margin-top: 10px;
		&:hover {
			box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
		}
	}
}
</style>
