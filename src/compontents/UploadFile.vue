<template>
	<div class="file-upload">
		<el-upload
			:action="action"
			:data="data"
			:headers="headers"
			:limit="maxCount"
			:multiple="multiple"
			:accept="accept"
			:list-type="listType"
			:before-upload="onBeforeUpload"
			:on-remove="handleRemove"
			:on-exceed="handleExceed"
			:on-error="handleError"
			:on-success="onSuccess"
			:disabled="disabled"
			v-model:file-list="fileList"
			:show-file-list="false"
			:http-request="customUpload"
			style="width: 100%; overflow: hidden"
		>
			<template v-if="viewDesc" #tip>
				<div class="el-upload__tip">
				只能上传{{ allExtensions.join("、") }}文件，不超过{{
					props.allowsize
				}}MB。
				</div>
			</template>
			<template #trigger>
				<el-button
					v-btn
					class="el-upload-button"
					type="primary"
					:style="{
						borderRadius: isBorderRadius ? '5px' : '0'
					}"
					v-loading="loading"
					><el-icon><UploadFilled /></el-icon>附件上传</el-button
				>
			</template>
			<template v-if="viewFile">
				<div
					class="file-view-wrapper"
					v-for="(file, index) in fileList"
					:key="index"
					@click="commonDownLoadFile(file.response.data.filePath)"
				>
					<font-awesome-icon :icon="awesomeIcon" />
					<span class="file-name-text">{{ file.name }}</span>
					<font-awesome-icon
						v-if="!loading" 
						class="awesom-del-icon"
						:icon="awesomeDelIcon"
						@click="handleRemove($event, file)"
					/>
				</div>
			</template>
		</el-upload>
	</div>
</template>

<script lang="ts" setup>
import Cookies from "js-cookie"
import { UploadFilled } from "@element-plus/icons-vue"
import type { UploadProps, UploadUserFile, UploadFile } from "element-plus"
import {
	getFileExtension,
	commonDownLoadFile
} from "@/app/platform/utils/common"
import CacheKey from "@/constants/cacheKey"

interface Props {
	action?: string // 上传地址
	maxCount?: number // 上传数量限制
	accept?: string // 上传文件类型
	multiple?: boolean // 是否多选
	data?: any // 上传时附带的额外参数
	disabled?: boolean // 是否禁用上传
	listType?: 'text' | 'picture' | 'picture-card' | ''
	allExtensions?: Array<string>
	allowsize: number
	fileType?: string  //上传文件类型 'image' ｜ 'file'
	fileList?: any[] //已上传文件,
	viewDesc?: boolean
	viewFile?: boolean  // 是否显示已上传文件列表
	isBorderRadius?: boolean // 是否圆角
	autoUpload?: boolean
}

const awesomeIcon = ["fas", "paperclip"]
const awesomeDelIcon = ["fas", "xmark"]

const emit = defineEmits(["onSuccess", "handleRemove", "update:files"])
const props = withDefaults(defineProps<Props>(), {
	action: import.meta.env.VITE_BASE_API + import.meta.env.VITE_APP_BASE_UPLOAD_URL,
	maxCount: 9,
	accept: "image/png,image/jpeg,imgage/jpg,video/*",
	multiple: false,
	disabled: false,
	listType: "picture-card",
	allExtensions: () => [".jpg", ".jpeg", ".png", ".mp4", ".mov", ".rmvb"],
	fileType: "file",
	allowsize: 100,
	viewDesc: false,
	viewFile: true,
	isBorderRadius: true,
	autoUpload: true
})

const loading = ref(false)
const maxCount = toRef(props, "maxCount")
const data = toRef(props, "data")
const disabled = toRef(props, "disabled")
const listType = toRef(props, "listType")
const viewFile = toRef(props, "viewFile")
const multiple = toRef(props, "multiple")
const isBorderRadius = toRef(props, "isBorderRadius")
const headers = reactive({
	Authorization: "Bearer " + Cookies.get(CacheKey.TOKEN)
})

const fileList = ref<any>([])

const handleRemove: UploadProps["onRemove"] = (uploadFile, uploadFiles) => {
	if (uploadFile.raw) {
		(uploadFile.raw as unknown as Event)?.stopPropagation?.()
	}
	fileList.value = fileList.value.filter(
		(_f: UploadFile) =>
			(_f.url || (_f?.response as {data?: {filePath?: string}})?.data?.filePath) !==
			(uploadFile?.url || (uploadFile?.response as {data?: {filePath?: string}})?.data?.filePath)
	)
	emit("handleRemove", uploadFile, fileList)
}

const handleExceed: UploadProps["onExceed"] = (files, uploadFiles) => {
	ElMessage.warning(`最多上传${maxCount.value}条`)
}

const onSuccess: UploadProps["onSuccess"] = (response, uploadFile, uploadFiles) => {
	if (uploadFile.status === "success" && response.data) {
		loading.value = false
		uploadFile.response = response
		emit("onSuccess", response, fileList)
	} else {
		loading.value = false
		ElMessage.error(response.msg)
		fileList.value = fileList.value.filter((f: UploadFile) => f.uid !== uploadFile.uid)
	}
}

const handleError: UploadProps["onError"] = (error: Error) => {
	loading.value = false
	ElMessage.error("上传失败！")
}

const onBeforeUpload = (file: File) => {
	const extension = getFileExtension(file.name).toLowerCase()
	if (!props.allExtensions.includes(extension)) {
		ElMessage.error(`不支持 ${extension} 格式文件！`)
		return false
	}
	if (file.size / Math.pow(1024, 2) > props.allowsize) {
		ElMessage.error(`文件大小不能超过 ${props.allowsize}MB!`)
		return false
	}
	loading.value = true
	return true
}

const clearFileList = () => {
	fileList.value = []
}

let currentBatch: any[] = []
let isUploading = false

const customUpload = async (options: any) => {
	const { file, onSuccess, onError } = options
	currentBatch.push({ file, onSuccess, onError })
	emit("update:files", currentBatch.map(item => item.file))
	
	if (props.autoUpload) {
		if (!isUploading) {
			isUploading = true
		}
		setTimeout(async () => {
			try {
				const batchFiles = [...currentBatch]
				currentBatch = []
				const uploadUrl = batchFiles.length > 1 
					? `/pitaya/system/common/attachment/uploadBatch`
					: props.action
				const formData = new FormData()
				
				batchFiles.forEach((files, index) => {
					formData.append(batchFiles.length > 1 ? `file` : 'file', files.file)
				})

				if (props.data) {
					Object.entries(props.data).forEach(([key, value]) => {
						formData.append(key, value as string)
					})
				}

				if (batchFiles.length) {
					const response = await fetch(uploadUrl, {
						method: 'POST',
						headers: headers,
						body: formData
					})
					
					if (response.status == 200) {
						loading.value = false
						const responseData = await response.json()
						emit("onSuccess", responseData, fileList)
					}
				}
			} catch(error) {
				console.error('Upload error:', error)
				loading.value = false
			} finally {
				isUploading = false
			}
		}, 500)
	}else{
		loading.value = false
	}
}

const handleSubmit = async () => {
	if (!currentBatch.length) {
		ElMessage.warning('没有待上传的文件')
		return
	}
	
	try {
		isUploading = true
		const batchFiles = [...currentBatch]
		currentBatch = []
		
		const uploadUrl = batchFiles.length > 1 
			? `/pitaya/system/common/attachment/uploadBatch`
			: props.action
		
		const formData = new FormData()
		batchFiles.forEach(file => {
			formData.append(batchFiles.length > 1 ? 'file' : 'file', file.file)
		})

		if (props.data) {
			Object.entries(props.data).forEach(([key, value]) => {
				formData.append(key, value as string)
			})
		}

		const response = await fetch(uploadUrl, {
			method: 'POST',
			headers: headers,
			body: formData
		})
		
		if (response.status == 200) {
			loading.value = false
			const responseData = await response.json()
			emit("onSuccess", responseData, fileList)
			return responseData
		}
	} catch(error) {
		console.error('Submit error:', error)
		loading.value = false
		ElMessage.error('上传失败')
		throw error
	} finally {
		isUploading = false
	}
}

onMounted(() => {
	fileList.value.push(...(props.fileList || []))
})

defineExpose({
	clearFileList,
	handleSubmit
})
</script>

<style lang="scss" scoped>
.file-upload {
	width: 100%;
	display: flex;
	flex-wrap: wrap;
	
	:deep(.el-upload) {
		width: 100%;
	}
	
	.file-view-wrapper:hover {
		background-color: var(--el-fill-color-light);
		cursor: pointer;
		user-select: none;
	}
	
	.file-view-wrapper {
		display: flex;
		align-items: center;
		padding: 0 5px;
		margin: 5px 0;
		border-radius: 3px;
		font-size: var(--pitaya-fs-12);
		color: var(--pitaya-header-bg-color);
		
		.file-name-text {
			width: 0;
			flex: 1;
			overflow: hidden;
			white-space: nowrap;
			text-overflow: ellipsis;
			padding: 0 5px;
		}
		
		.awesom-del-icon {
			display: none;
			cursor: pointer;
		}
		
		&:hover {
			.awesom-del-icon {
				display: block;
			}
		}
	}
	
	.el-upload__tip {
		word-break: break-all;
		color: rgb(153, 153, 153);
		font-size: 12px;
		line-height: 20px;
		margin-top: 0;
	}
}
</style>