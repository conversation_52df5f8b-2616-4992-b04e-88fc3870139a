<!-- 项点检规列表 -->
<template>
	<div>
		<ul class="drawer-left">
			<el-input
				class="tree-search"
				v-model="value"
				:suffix-icon="Search"
				placeholder="请输入查询关键字"
			/>
			<li
				class="flex-dr li"
				:class="[id == item ? 'hover' : '']"
				v-for="item in 15"
				@click="onClickBtn(item)"
			>
				<div class="f12 flex-dr">
					<slot name="headerIcon">
						<!-- <div class="f12 cp flex-dr "><el-tooltip content="Top center" placement="top">
						<font-awesome-icon :icon="['fas', 'bell']" style="color: var(--pitaya-btn-background);margin-left: 10px" />
					</el-tooltip>
				</div> -->
					</slot>
					自动润滑油罐油位润滑系统 (2)
					<slot name="mainIcon">
						<!-- <div class="f12 cp flex-dr "><el-tooltip content="Top center" placement="top">
						<font-awesome-icon :icon="['fas', 'bell']" style="color: var(--pitaya-btn-background);margin-left: 10px" />
					</el-tooltip>
				</div> -->
					</slot>
				</div>
				<!-- 按钮 -->
				<slot name="footerIcon">
					<!-- <div class="f12 cp flex-dr">
					<font-awesome-icon :icon="['fas', 'pen-to-square']" style="color: #D0D0D0;margin:0 10px " />
					<font-awesome-icon :icon="['fas', 'trash-can']" style="color: #D0D0D0" />
				</div> -->
				</slot>
			</li>
		</ul>
	</div>
</template>

<script lang="ts" setup>
import { Search } from "@element-plus/icons-vue"

const value = ref("")

const id = ref(0)

const onClickBtn = (item: any) => {
	console.log("itemd", item)
	id.value = item
}
</script>
<style lang="scss" scoped>
.drawer-left {
	position: relative;
	padding-bottom: 200px;
	box-sizing: border-box;
	// width: 300px;
	height: 90vh;
	// overflow-y: scroll;

	.tree-search {
		margin-top: var(--pitaya-fs-12);
		margin-bottom: var(--pitaya-fs-12);
	}

	.li {
		line-height: 20px;
		padding: 15px 0;
		border-bottom: 1px dashed var(--el-border-color);
	}

	.flex-dc {
		display: flex;
		flex-direction: column;
	}

	.li:hover {
		color: #666666;
		background: #ecf5ff;
	}

	.hover {
		color: #666666;
		background: #ecf5ff;
	}
}

.flex-dr {
	display: flex;
	flex-direction: row;
	justify-content: space-between;
	align-items: center;
}

.f12 {
	font-size: 12px;
}

.c-9 {
	color: #999;
}

.mt20 {
	margin-top: 20px;
}

.cp {
	cursor: pointer;
}
</style>
