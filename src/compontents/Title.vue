<script setup lang="ts">
interface btnObj {
	name: string
	permissionType?: string
	icon?: string[]
	roles: string
	disabled?: boolean
}

interface titleObj {
	name?: string[]
	icon?: string[]
}

interface Props {
	title: titleObj
	button?: btnObj[]
	showClose?: boolean
}

const props = withDefaults(defineProps<Props>(), {
	button: () => [],
	showClose: false
})
const handleClose = () => {
	emit("onBtnClick", "close")
}
const { title, button } = toRefs(props)

const emit = defineEmits<{
	(e: "onBtnClick", btnName: string | undefined): void
}>()
const router = useRouter()
const onSubmit = (e: string) => {
	// if (!_btnClickable(e)) return
	emit("onBtnClick", e)
}

// function _btnClickable(btnName: string) {
// 	console.log('ddddd',btnName)
// 	const style = checkPermissionType(btnName)
// 	if (style == "invisible") return false
// 	if (style == "disable") return false
// 	return true
// }

const _btnStyle = computed(() => {
	return function (btnName: string) {
		const style = checkPermissionType(btnName)
		// invisible   直接不可见;
		//disable   可见但是不可用;
		//visible   可见又可用;
		if (style == "invisible") return "permission-hidden-btn"
		if (style == "disable") return "unusableBtn"
	}
})

// #region 按钮权限校验
// const permissionBtnList = matchPermissionBtnList()
// if (permissionBtnList && permissionBtnList.length) {
// 	permissionBtnList.forEach((item: string) => {
// 		const roleName = item.split(":")[1].split("-")[1]
// 		const roleType = item.split(":")[1].split("-")[2]
// 		button.value.forEach((btn: btnObj) => {
// 			if (btn.name === roleName) {
// 				btn.permissionType = roleType
// 			}
// 		})
// 	})
// }
// #endregion
</script>

<template>
	<div
		class="common-title-wrapper"
		v-if="(title.name && title.name.length) || (button && button.length)"
	>
		<div class="common-title-left" v-if="title.name && title.name.length">
			<font-awesome-icon
				v-if="title.icon"
				class="place-icon"
				:icon="title.icon"
			/>
			<div class="place-list">
				<template v-for="(name, index) in title.name" :key="index">
					<span>{{ name }}</span>
					<span class="place-space" v-if="index < title.name.length - 1">
						>
					</span>
				</template>
			</div>
			<!-- <slot name="title" :rowData="item" /> -->
			<slot name="title" />
		</div>
		<slot />
		<div class="common-title-right" v-if="button && button.length">
			<div
				:class="[
					'btn-item',
					_btnStyle(btn.roles),
					btn.disabled ? 'unusableBtn' : ''
				]"
				v-for="(btn, index) in button"
				:key="index"
				@click="onSubmit(btn.name)"
			>
				<font-awesome-icon
					v-if="btn.icon"
					class="place-icon"
					:icon="btn.icon"
				/>
				<span>{{ btn.name }}</span>
			</div>
		</div>
	</div>
</template>

<style lang="scss" scoped>
.common-title-wrapper {
	display: flex;
	flex-wrap: wrap;
	align-items: center;
	justify-content: space-between;
	font-size: 14px;
	color: var(--pitaya-header-bg-color);
	border-bottom: 1px solid #ccc;
	padding:0 10px  10px 0;
	.common-title-left {
		display: flex;
		align-items: center;
	}

	.common-title-right {
		display: flex;
		align-items: center;
		justify-content: flex-end;
		.btn-item {
			display: flex;
			align-items: center;
			cursor: pointer;
			margin-right: 10px;
		}
	}

	.place-icon {
		color: var(--pitaya-header-bg-color);
		margin: 0 5px 0 10px;
	}
	.place-space {
		padding: 0 5px;
	}
}
</style>
