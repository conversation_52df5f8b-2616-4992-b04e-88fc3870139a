<template>
	<div class="profession-drawer-container pitaya-tree">
		<div class="drawer-left">
			<PitayaTree
				:treeData="TreeData"
				:treeProps="_treeProps"
				:needCheckBox="true"
				:needSingleSelect="needSingleSelect"
				v-model:treeBizId="treeBizId"
				:defaultExpandedKeys="defaultExpandedKeys"
				ref="pitayaTreeRef"
				@onTreeClick="treeClick"
				@onTreeChange="treeClick"
				:tree-loading="treeLoading"
				:checkStrictly="checkStrictly"
				:params="params"
				:node-key="nodeKey"
			/>
			<div class="btn-groups">
				<ButtonList :button="btnList" @onBtnClick="onBtnClick" />
			</div>
		</div>
	</div>
</template>
<script lang="ts" setup>
import { watch, toRef, ref } from "vue"
import XEUtils from "xe-utils"

/**
 * props*/
interface Props {
	title?: string
	multiSelect?: boolean
	drawerState?: boolean
	type?: string
	checkedKeys?: string[]
	checkStrictly?: boolean
	needSingleSelect?: boolean
	apifunc: (params?: any) => Promise<void>
	params?: any
	treeProps?: any
	confirmBtnText: string
	cancelBtnText: string
}

const props = withDefaults(defineProps<Props>(), {
	treeProps: {
		children: "children",
		label: "name"
	}
})
const nodeKey = ref<string>("id")
const treeBizId = ref()

const drawerState = toRef(props, "drawerState")
const title = toRef(props, "title")
const type = toRef(props, "type")
const params = toRef(props, "params")
const treeProps = toRef(props, "treeProps")
const confirmBtnText = toRef(props, "confirmBtnText")
const cancelBtnText = toRef(props, "cancelBtnText")

const _treeProps = ref({
	children: "children",
	label: "name"
})
const treeIds = ref<number[]>([])

const multiSelect = toRef(props, "multiSelect")
const needSingleSelect = toRef(props, "needSingleSelect")
const checkStrictly = toRef(props, "checkStrictly")
const checkedKeys = toRef(props, "checkedKeys")
const treeLoading = ref(false)

/**
 * emit
 * */
const emit = defineEmits<{
	(e: "onBtnClick", selectIds?: string[] | undefined, selectNodes?: any[]): void
}>()

/**
 * 基本数据列表
 * */
const TreeData = ref<any[]>([])
const pitayaTreeRef = ref<any>()

const treeList = ref("") //保存用户当前点击树信息
const treeListData = ref("") //保存用户当前点击树信息
/**
 * 对象列表
 * */
// 部门树ref

const btnList = ref([
	{
		name: "取消",
		icon: ["fas", "circle-minus"]
	},
	{
		name: "确定",
		icon: ["fas", "floppy-disk"]
	}
])

// 默认展开节点
const defaultExpandedKeys = ref<any[]>([])
const locationTreeProps = {
	children: "children",
	label: (data: any) => {
		const { name, parentId, allName } = data
		let resultLabel

		if (!treeIds.value.includes(parentId)) {
			resultLabel = allName ? `${name}(${allName})` : name
		} else {
			resultLabel = name
		}
		return resultLabel
	}
}

// 接受不同的apifunc查询树
const getTreeData = () => {
	TreeData.value = []
	defaultExpandedKeys.value = []
	treeLoading.value = true
	if (treeProps.value) {
		_treeProps.value = treeProps.value
	}
	props
		.apifunc(params.value)
		.then((res: any) => {
			// 特殊情况，通过type判断做特殊处理
			if (type.value == "专业") {
				TreeData.value = res
				if (checkedKeys.value) {
					pitayaTreeRef.value.setCheckedKeys(checkedKeys.value, true)
				}
			} else if (type.value == "引入项点维规") {
				nodeKey.value = "nodeKey"
				res.map((point: any) => {
					point.nodeKey = point.id
					if (point.options && point.options.length > 0) {
						point.options.map((option: any) => {
							option.pointName = option.optionName
							option.pid = point.id
							option.nodeKey = point.id + "-" + option.id
						})
					}
				})
				TreeData.value = res
				if (checkedKeys.value) {
					pitayaTreeRef.value.setCheckedKeys(checkedKeys.value)
				}
			} else if (type.value == "位置信息") {
				TreeData.value = res.children ? res.children : res
				// 存储所有节点id
				treeIds.value = XEUtils.toTreeArray(TreeData.value).map((item) => {
					if (item.id) {
						return item.id
					}
				})
				_treeProps.value.label = locationTreeProps.label
				if (checkedKeys.value) {
					pitayaTreeRef.value.setCheckedKeys(checkedKeys.value)
				}
			} else {
				TreeData.value = res.children ? res.children : res
				if (checkedKeys.value) {
					pitayaTreeRef.value.setCheckedKeys(checkedKeys.value)
				}
			}
			if (checkedKeys.value) {
				defaultExpandedKeys.value =
					checkedKeys.value && checkedKeys.value[0]
						? checkedKeys.value
						: [TreeData.value[0].id]
			} else {
				defaultExpandedKeys.value = treeBizId.value
					? [treeBizId.value]
					: [TreeData.value[0].id]
			}
		})
		.finally(() => {
			treeLoading.value = false
		})
}

// 树点击
const treeClick = (data: any, treeData: any) => {
	treeList.value = data
	treeListData.value = treeData
}

// 按钮点击
const onBtnClick = (btnName: string | undefined) => {
	if (btnName && ["确定", "提交"].includes(btnName)) {
		emit(
			"onBtnClick",
			pitayaTreeRef.value.getCheckedNodes(),
			pitayaTreeRef!.value.getCheckedNodes(false),
			pitayaTreeRef.value.getDataObjIncludeCheckedNodes()
		)
		return
	}
	if (btnName === "取消") {
		emit("onBtnClick")
		return
	}
}

const resetChecked = () => {
	pitayaTreeRef.value!.setCheckedKeys([], false)
}

/**
 * Watch
 * */
watch(
	[() => title.value],
	(newVal: any[]) => {
		if (newVal) {
			getTreeData()
		}
	},
	{
		immediate: true
	}
)

watchEffect(() => {
	// 底部操作按钮文本
	if (cancelBtnText.value) {
		btnList.value[0].name = cancelBtnText.value
	}
	if (confirmBtnText.value) {
		btnList.value[1].name = confirmBtnText.value
	}
})

defineExpose({
	resetChecked,
	pitayaTreeRef
})
</script>

<style lang="scss" scoped>
.pitaya-tree {
	display: flex;
	align-items: center;
	position: relative;
	height: calc(100% - 30px);
	overflow: hidden;

	.drawer-left {
		position: relative;
		padding-right: 10px;
		box-sizing: border-box;
		width: 100%;
		height: 100%;
		padding-bottom: 33px;
		overflow: hidden;

		.drawer-left-form {
			height: 100%;
			padding: 0 10px;
			margin-top: 10px;
		}
	}

	.btn-groups {
		position: absolute;
		bottom: 0;
		left: 0;
		right: 0;
		z-index: 99;
		display: flex;
		justify-content: flex-end;
		padding-top: 10px;
		padding-right: 10px;
		box-sizing: border-box;
		width: auto;
		border-top: 1px solid #ccc;
		background: #fff;
	}
	.el-drawer__body {
		overflow: hidden;
	}
}
</style>
