<template>
	<div class="file-upload-wrapper">
		<div v-for="(item, index) in fileList" :key="index">
			<!-- getFileType(item.filePath):true 是图片 false:不是图片  -->
			<div class="file-box" v-if="getFileType(item.filePath)">
				<el-icon color="#4BAE89" class="result-tag"
					><CircleCheckFilled
				/></el-icon>
				<img
					v-if="isImageFile(item.filePath)"
					class="thumbnail"
					:src="getFileHttpUrl(item.filePath)"
				/>
				<img
					v-else-if="isVideoFile(item.filePath)"
					class="thumbnail"
					src="@/assets/images/video.png"
				/>
				<img
					v-else-if="isAudioFile(item.filePath)"
					class="thumbnail"
					src="@/assets/images/video.png"
				/>
				<span class="file-item-actions">
					<!-- 预览 -->
					<span class="action-preview" @click="handlePreview(item)">
						<el-icon color="#FFFFFF"><View /></el-icon>
					</span>

					<!-- 下载 -->
					<span
						class="action-download"
						@click="commonDownLoadFile(item.filePath)"
					>
						<el-icon color="#FFFFFF"><Download /></el-icon>
					</span>
					<!-- 删除 -->
					<span class="action-delete" @click="handleDelete(item)">
						<el-icon color="#FFFFFF"><Delete /></el-icon>
					</span>
				</span>
			</div>
			<div v-else>
				<!-- 文件 -->
				<div class="file-box flex-c">
					<el-icon color="#4BAE89" class="result-tag"
						><CircleCheckFilled
					/></el-icon>
					<el-icon color="#204a9cb3" class="result-icon"><Document /></el-icon>
				</div>
				<el-tooltip
					class="box-item"
					effect="dark"
					:content="item.filePath"
					placement="top-start"
				>
					<span class="file-url">
						{{ item.filePath }}
					</span>
				</el-tooltip>
			</div>
		</div>
		<el-upload
			:action="action"
			:data="data"
			:headers="headers"
			:accept="accept"
			:list-type="listType"
			:before-upload="onBeforeUpload"
			:on-remove="handleRemove"
			:on-exceed="handleExceed"
			:before-remove="beforeRemove"
			:on-success="onSuccess"
			:on-change="handleChange"  
			:show-file-list="false"
			:disabled="disabled"
			:auto-upload="!autoUload"

		>
			<template v-if="listType === '' && maxPictureCount">
				<el-icon v-loading="loading" class="upload-icon"><Plus /></el-icon>
			</template>
			<template #file="{ file }">
				<div>
					<img class="el-upload-list__item-thumbnail" :src="file.url" alt="" />
					<span class="el-upload-list__item-actions">
						<span class="el-upload-list__item-preview">
							<el-icon><zoom-in /></el-icon>
						</span>
						<span v-if="!disabled" class="el-upload-list__item-delete">
							<el-icon><Download /></el-icon>
						</span>
						<span v-if="!disabled" class="el-upload-list__item-delete">
							<el-icon><Delete /></el-icon>
						</span>
					</span>
				</div>
			</template>
		</el-upload>
		<!-- 图片/视频预览 -->
		<MediaPreview
			ref="mediaPreview"
			:file-list="fileList"
			:current-index="currentIndex"
			@next="handleNext"
			@prev="handlePrev"
		/>
		<!-- 图片/裁切 -->
		<cropImage
			ref="cropImageDrawer"
			:file-list="fileList"
			:data="data"
			@cropImageFile="cropImageFile"
		/>
	</div>
</template>

<script lang="ts" setup>
import { Plus } from "@element-plus/icons-vue"
import Cookies from "js-cookie"
import {
	Document,
	ZoomIn,
	Delete,
	Download,
	CircleCheckFilled,
	View
} from "@element-plus/icons-vue"

import type { UploadProps } from "element-plus"
import {
	getFileType,
	getFileHttpUrl,
	isImageFile,
	isVideoFile,
	isAudioFile,
	getFileExtension,
	commonDownLoadFile
} from "@/app/platform/utils/common"
import MediaPreview from "./MediaPreview.vue"
import cropImage from "./CropImage.vue"
import CacheKey from "@/constants/cacheKey"

interface Props {
	fileList?: any[] // 文件上传列表
	action?: string // 上传地址
	maxCount?: number // 上传数量限制
	accept?: string // 上传文件类型
	multiple?: boolean // 是否多选
	data?: any // 上传时附带的额外参数
	disabled?: boolean // 是否禁用上传
	listType?: string // 上传按钮类型控制 'text' | 'picture' | 'picture-card' | ''
	allExtensions?: string[],
	autoUload?:boolean // 是否自动上传  否：自动上传，  是：配合裁剪手动上传
}

const emit = defineEmits(["onSuccess"])
const props = withDefaults(defineProps<Props>(), {
	fileList: () => [],
	action:
		import.meta.env.VITE_BASE_API + import.meta.env.VITE_APP_BASE_UPLOAD_URL,
	maxCount: 9,
	accept: "image/png,image/jpeg,imgage/jpg,video/*",
	multiple: false,
	disabled: false,
	listType: "picture-card",
	allExtensions: () => {
		return [".jpg", ".jpeg", ".png", ".mp4", ".mov", ".rmvb"]
	},
	autoUload:false
})
const currentIndex = ref<number>(0)
const loading = ref(false)
const mediaPreview = ref()

const maxCount = toRef(props, "maxCount")
const fileList = toRef(props, "fileList")
const data = toRef(props, "data")
const disabled = toRef(props, "disabled")
const listType = toRef(props, "listType")
const autoUload = toRef(props, "autoUload")
const headers = reactive({
	Authorization: "Bearer " + Cookies.get(CacheKey.TOKEN)
})
const fileListTemp = ref<any[]>([]) // 临时文件列表

// 手动移除文件
const handleRemove: UploadProps["onRemove"] = (uploadFile, uploadFiles) => {
	console.log(uploadFile, uploadFiles)
}

// 预览
const handlePreview = (item: any) => {
	const index = fileList.value.findIndex((v) => v.id === item.id)
	currentIndex.value = index
	mediaPreview.value?.open()
}

const maxPictureCount = computed(() => {
	return fileList.value.length < maxCount.value ? true : false;
})


// 删除
const handleDelete = (file: any) => {
	fileListTemp.value = fileList.value
	const index = fileListTemp.value.findIndex((item: any) => item.id === file.id)
	fileListTemp.value.splice(index, 1)
	emit("onSuccess", fileListTemp)
}

const beforeRemove: UploadProps["beforeRemove"] = (uploadFile, uploadFiles) => {
	// return ElMessageBox.confirm(
	// 	`Cancel the transfer of ${uploadFile.name} ?`
	// ).then(
	// 	() => true,
	// 	() => false
	// )
}
   // 处理文件变化  
   const cropImageDrawer =ref()
   const handleChange = (file: any, fileList: any) => {  
	if(autoUload.value){
		cropImageDrawer.value?.open(file,fileList)
	}
};  
  

const onSuccess: UploadProps["onSuccess"] = (files, uploadFiles) => {
	if (uploadFiles.status === "success" && files.data) {
		fileList.value.push(files.data)
		loading.value = false
		emit("onSuccess", fileList)
	}
}
const onBeforeUpload = (file: File) => {
	const extension = getFileExtension(file.name).toLowerCase()
	if (!props.allExtensions.includes(extension)) {
		ElMessage.error(`不支持 ${extension} 格式文件！`)
		return false
	}
	if (file.size / Math.pow(1024, 2) > 100) {
		ElMessage.error(`文件大小不能超过 100MB!`)
		return false
	}
	loading.value = true
	return true
}

// 下一个预览
const handleNext = () => {
	if (currentIndex.value < fileList.value.length - 1) {
		currentIndex.value++
	} else {
		ElMessage.warning("最后一张啦")
	}
}

// 上一个预览
const handlePrev = () => {
	if (currentIndex.value > 0) {
		currentIndex.value--
	} else {
		ElMessage.warning("已经到第一张啦")
	}
}


//裁剪
const cropImageFile = (file)=>{
	console.log('fileList.value.length ',fileList.value.length )
	fileList.value.push(file)
	console.log('cropImageFile',fileList.value)
	emit("onSuccess", fileList)
}
</script>
<style lang="scss" scoped>
.file-upload-wrapper {
	width: 100%;
	display: flex;
	flex-wrap: wrap;
	.file-box {
		width: 148px;
		height: 148px;
		background-color: #fafafa;
		margin-right: 10px;
		border: 1px solid #cdd0d6;
		border-radius: 3px;
		margin-bottom: 10px;
		.thumbnail {
			width: 100%;
			height: 100%;
			object-fit: contain;
			cursor: pointer;
			border-radius: 3px;
		}

		.result-tag {
			position: absolute;
			top: 5px;
			right: 5px;
			z-index: 10;
		}

		.result-icon {
			font-size: 70px;
			z-index: 10;
		}
	}
	.file-url {
		width: 200px;
		color: #333;
		font-size: 12px;
		overflow: hidden;
		display: inline-block;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		text-overflow: ellipsis;
		cursor: pointer;
	}
	.flex-c {
		display: flex;
		justify-content: center;
		align-items: center;
	}
	.upload-icon {
		width: 148px;
		height: 148px;
		background-color: #fafafa;
		border-radius: 3px;
		border: 1px dashed #ccc;
		cursor: pointer;
		&:hover {
			box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
		}
	}
}

.preview-btns {
	display: flex;
	justify-content: center;
	margin-top: 10px;
}
</style>
