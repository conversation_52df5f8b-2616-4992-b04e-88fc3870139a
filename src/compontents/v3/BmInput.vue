<template>
	<el-input
		class="bm-input"
		v-model="innerValue"
		:placeholder="placeholder"
		:disabled="disabled"
		:readonly="readonly"
		:maxlength="maxlength"
		:show-word-limit="showWordLimit"
		@input="onInput"
		@click="onClick"
		@change="$emit('onChange')"
	>
		<template #suffix>
			<el-icon
				@click="onClear"
				v-show="!props.disabled && props.showClear && !!props.modelValue"
			>
				<Close />
			</el-icon>
		</template>
		<template #append v-if="showAppend">
			<el-button v-btn link @click="appendClick" :disabled="disabled">
				<font-awesome-icon
					:icon="['fas', 'fa-th-list']"
					style="color: var(--pitaya-place-font-color)"
				/>
			</el-button>
		</template>
	</el-input>
</template>
<script setup lang="ts">
import { Close } from "@element-plus/icons-vue"
const emits = defineEmits([
	"update:modelValue",
	"onChange",
	"onClear",
	"click",
	"onAppendClick"
])
const props = defineProps({
	modelValue: {
		type: String,
		default: ""
	},
	showClear: {
		type: Boolean,
		default: false
	},
	showAppend: {
		type: Boolean,
		default: false
	},
	placeholder: {
		type: String,
		default: ""
	},
	disabled: {
		type: Boolean,
		default: false
	},
	readonly: {
		type: Boolean,
		default: false
	},
	maxlength: {
		type: Number,
		default: 0
	},
	showWordLimit: {
		type: Boolean,
		default: false
	}
})
const innerValue = ref(props.modelValue)
const onInput = (value: string) => {
	innerValue.value = value
	emits("update:modelValue", value)
}
const onClick = () => {
	emits("click")
}
const onClear = () => {
	innerValue.value = ""
	emits("update:modelValue", "")
	emits("onClear")
}
const appendClick = () => {
	emits("onAppendClick")
}
watch(
	() => props.modelValue,
	(val) => {
		innerValue.value = val
	}
)
</script>
