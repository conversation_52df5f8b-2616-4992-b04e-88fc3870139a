<script lang="ts" setup>
import { Search, FolderOpened, Tickets } from "@element-plus/icons-vue"
import { ElTree as ElementTree } from "element-plus"
import { TreeNodeData } from "element-plus/es/components/tree/src/tree.type"
import {
	cloneDeep,
	differenceBy,
	findIndex,
	intersectionBy,
	isNumber
} from "lodash-es"
import { toRef, ref, watch, nextTick } from "vue"
import { map } from "xe-utils"
import EmptySearch from "@/assets/images/empty/empty_search.svg?component"

interface Props {
	treeData: any[] 
	treeProps: anyKey
	needSearch?: boolean // 是否需要搜索
	needCheckBox?: boolean//是否需要选择
	defaultExpandedKeys?: any[] //默认展开的节点的 key 的数组
	defaultCheckedKeys?: any[]  //默认勾选的节点的 key 的数组	
	currentNodeKey?: number | string //当前选中节点
	treeBizId?: number | string // 单选选中样式
	onTreeSearch?: Function // 搜索回调
	expandOnClickNode?: boolean // 点击是否展开
	checkStrictly?: boolean // 是否严格遵守父子不关联
	needSingleSelect?: boolean // 是否需要单选
	nodeKey?: string // 节点唯一值
	treeLoading?: boolean // 树加载状态
	pageName?: string // 页面名称
}

const props = withDefaults(defineProps<Props>(), {
	needSearch: true,
	needCheckBox: true,
	defaultExpandedKeys: () => [],
	defaultCheckedKeys: () => [],
	expandOnClickNode: false,
	checkStrictly: false,
	needSingleSelect: false,
	nodeKey: "id",
	treeLoading: false
})
const emits = defineEmits([
	"onTreeClick",
	"onTreeCheck",
	"onTreeChange",
	"update:treeBizId",
	"onScrollLoad",
	"nodeExpand"
])

const treeDataSelf = toRef(() => props.treeData)
const treeBizId = toRef(() => props.treeBizId)
const defaultExpandedKeys = toRef(() => props.defaultExpandedKeys)
const defaultCheckedKeys = toRef(() => props.defaultCheckedKeys)
const propsSelf = toRef(props, "treeProps")
const needCheckBoxSelf = toRef(() => props.needCheckBox)
const nodeKey = toRef(props, "nodeKey")
const treeLoading = toRef(props, "treeLoading")
const filterText = ref<string>("")
const PitayaTreeRef = ref<InstanceType<typeof ElementTree>>()

// 节点点击
const handleNodeClick = (data: any, node: any) => {
	if (data.isDevice) {
		return false
	}
	emits("update:treeBizId", data[nodeKey.value])
	emits("onTreeClick", data, node)
}
const handleNodeExpand = (data: any, node: any) => {
	emits("nodeExpand", data, node)
}
// 勾选
const handleCheckChange = (
	data: any,
	checked: boolean,
	indeterminate: boolean
) => {
	emits("onTreeChange", data, checked, indeterminate)
}

// 点击节点复选框之后触发
const onCheck = (node: any, list: any) => {
	// 实现单选
	if (list.checkedKeys.length == 2 && props.needSingleSelect) {
		nextTick(() => {
			PitayaTreeRef.value!.setCheckedKeys([node[nodeKey.value]])
		})
	}
	emits("onTreeCheck", node, list)
}
// 滚动加载
const onScrollLoad = () => {
	emits("onScrollLoad")
}

watch(filterText, (val) => {
	if (props.onTreeSearch) {
		props.onTreeSearch(val)
		return
	}
	PitayaTreeRef.value!.filter(val)
})

/**
 * 取消原始组件父子联动
 * 父不关联子，子关联父
 * @param childId 勾选的子数据id
 */
function clickChildToParent(child: any, checked = true) {
	PitayaTreeRef.value?.setChecked(child[nodeKey.value], checked, false)
	if (checked && child.pid)
		clickChildToParent(PitayaTreeRef.value?.getNode(child.pid).data, true)
}

// TODO
let filterDataResult: any
let filterCheckedNodes: TreeNodeData[]
/**
 * @description 只返回勾选节点的数据对象
 * @returns 数组
 */
function getDataObjIncludeCheckedNodes() {
	if (!PitayaTreeRef.value) throw new Error("未找到树组件")

	filterCheckedNodes = PitayaTreeRef.value.getCheckedNodes(false, true)
	// console.log("ssh:::数据源和选中节点", treeDataSelf.value, filterCheckedNodes)
	const copyData = cloneDeep(treeDataSelf.value)
	const filterKey = props.treeProps.children
	// 把最外层数据 构造成和children一样，为了替换children属性对应值
	filterDataResult = { [filterKey]: [] }
	filterNotCheckData(copyData, filterCheckedNodes, filterDataResult, filterKey)
	// console.log("ssh:::copyData", filterDataResult)
	return filterDataResult[filterKey]
}

/**
 * @description 只返回勾选节点的数据对象
 * @returns 数组
 */
function getDataObjIncludeCheckedNodes2() {
	if (!PitayaTreeRef.value) throw new Error("未找到树组件")
	const sourceFilterCheckedNodes = PitayaTreeRef.value.getCheckedNodes(
		false,
		true
	)
	filterCheckedNodes = PitayaTreeRef.value.getCheckedNodes(false, true)
	// console.log("ssh:::数据源和选中节点", treeDataSelf.value, filterCheckedNodes)
	const filterKey = props.treeProps.children
	const copyData = cloneDeep(treeDataSelf.value[0][filterKey])
	// 把最外层数据 构造成和children一样，为了替换children属性对应值
	filterDataResult = { [filterKey]: [] }
	filterNotCheckData(copyData, filterCheckedNodes, filterDataResult, filterKey)
	hasChild(
		filterDataResult[filterKey],
		filterDataResult,
		filterKey,
		sourceFilterCheckedNodes
	)
	return [{ name: "全部位置", children: filterDataResult[filterKey] }]
}

/**
 * 判断子是否被勾选
 * @param data 子数据
 * @param parentData 父数据
 * @param key 子数据key
 * @param sourceFilter 勾选数据
 */
function hasChild(data: any, parentData: any, key: string, sourceFilter: any) {
	map(data, (item: any) => {
		const index = findIndex(sourceFilter, (filter: any) => {
			return item[nodeKey.value] == filter[nodeKey.value]
		})
		if (index >= 0) hasChild(item[key], item, key, sourceFilter)
		else parentData[key] = []
	})
	return false
}

/**
 * @description 过滤数据源
 * @param {Array} data 当前层级数据源
 * @param {Array} checkedNodes 选中节点
 * @param {Array} hierarchy 当前层级
 * @returns
 */
function filterNotCheckData(
	data: any,
	checkedNodes: any,
	hierarchy: any,
	filterKey: string
) {
	if (checkedNodes.length == 0) {
		// 没有勾选的子项了
		return
	}
	// 获取 当前层级和勾选节点一致的数组
	const tempData = cloneDeep(intersectionBy(data, checkedNodes, nodeKey.value))
	// 获取 剩余勾选节点
	filterCheckedNodes = differenceBy(checkedNodes, data, nodeKey.value)
	// 将结果替换当前层级chirldMenu节点
	hierarchy[filterKey] = tempData
	// console.log("ssh:::当前层一致节点数组", filterDataResult)
	// 下层如有子节点 继续向下递归
	for (let i = 0; i < tempData.length; i++) {
		const children = tempData[i][filterKey]
		if (!children) {
			continue
		}
		if (filterCheckedNodes.length == 0) {
			// 没有勾选的子项了 把当前层的子项置空
			hierarchy[filterKey][i][filterKey] = []
			continue
		}
		filterNotCheckData(
			children,
			filterCheckedNodes,
			hierarchy[filterKey][i],
			filterKey
		)
	}
}
//若节点可用被选中 (show-checkbox 为 true), 它将返回当前选中节点 key 的数组
const getCheckedKeys = (leafOnly = false) => {
	return PitayaTreeRef.value?.getCheckedKeys(leafOnly)
}
//如果节点可以被选中，(show-checkbox 为 true), 本方法将返回当前选中节点的数组
const getCheckedNodes = (leafOnly = false) => {
	return PitayaTreeRef.value?.getCheckedNodes(leafOnly)
}
//设置目前选中的节点，使用此方法必须设置 node-key 属性	
const setCheckedKeys = (keys: string[], leafOnly = false) => {
	PitayaTreeRef.value?.setCheckedKeys(keys, leafOnly)
}
//通过 key 设置某个节点的当前选中状态，使用此方法必须设置 node-key  属性	
const setCurrentKey = (key: string) => {
	PitayaTreeRef.value?.setCurrentKey(key)
}

defineExpose({
	PitayaTreeRef,
	filterText,
	getCheckedKeys,
	getCheckedNodes,
	setCheckedKeys,
	setCurrentKey,
	clickChildToParent,
	getDataObjIncludeCheckedNodes,
	getDataObjIncludeCheckedNodes2
})
defineOptions({
	name: "PitayaLazyTree"
})
function getIcon(params: string) {
	switch (params) {
		case "0":
			return "gears"
		case "1":
			return "folder-minus"
		case "2":
		case "3":
			return "file"
		case "4":
			return "circle-dot"
		default:
			return ""
	}
}
</script>
<template>
	<div class="pitaya-tree-container">
		<el-input
			v-if="props.needSearch"
			class="tree-search"
			clearable
			v-model.trim="filterText"
			:suffix-icon="Search"
			placeholder="请输入查询关键字"
		/>
		<div class="tree-div">
			<el-scrollbar>
				<el-tree
					class="tree-container"
					ref="PitayaTreeRef"
					:data="treeDataSelf"
					:props="propsSelf"
					:node-key="nodeKey"
					:highlight-current="true"
					v-infinite-scroll="onScrollLoad"
					:show-checkbox="needCheckBoxSelf"
					:default-expanded-keys="defaultExpandedKeys"
					:default-checked-keys="defaultCheckedKeys"
					:expand-on-click-node="props.expandOnClickNode"
					:check-strictly="props.checkStrictly"
					@node-click="handleNodeClick"
					@node-expand="handleNodeExpand"
					@check-change="handleCheckChange"
					:current-node-key="currentNodeKey"
					v-loading="treeLoading"
					@check="onCheck"
				>
					<template #default="{ node, data }">
						<div
							:class="[
								'custom-tree-node',
								treeBizId === data[nodeKey] ? 'sled-tree-node' : ''
							]"
						>
						
							<div class="tree-label">
								<font-awesome-icon
									v-if="data.menuLevel"
									:icon="['fas', getIcon(data.menuLevel)]"
									:style="{ color: '#2b4997' }"
								/>
								<span class="tree-label-text" v-html="node.label"></span>
							</div>
						</div>
					</template>
					<template #empty>
						<div class="tc">
							<EmptySearch class="empty_img" />
							<p>未查询到相关信息</p>
						</div>
					</template>
				</el-tree>
			</el-scrollbar>
		</div>
	</div>
</template>
<style lang="scss" scoped>
.tc {
	color: #666666;
	font-size: 12px;
	text-align: center;
}
.empty_img {
	width: 150px;
}
.pitaya-tree-container {
	padding: 0 10px;
	height: 100%;
	display: flex;
	flex-direction: column;
	.tree-search {
		margin-top: var(--pitaya-margins-base);
	}
	.tree-div {
		height: calc(100% - 65px);

		.tree-container {
			height: 100%;
			margin-top: 10px;
			box-sizing: border-box;
		}
	}
}
:deep(.el-tree-node__content):has(.sled-tree-node) {
	background-color: var(--el-fill-color-light) !important;
}
:deep(.el-tree-node__content) {
	height: 32px;
}
.tree-label-text {
	margin: 0 5px;
	font-size: var(--pitaya-fs-12);
	color: #666;
}
.tree-label {
	display: flex;
	align-items: center;
}
:deep(.svg-inline--fa) {
	height: 0.8em;
}
.el-tree {
	height: 50px;
}
// 横向滚动
:deep(.el-tree) {
	& > .el-tree-node {
		min-width: 100%;
		display: inline-block;
	}
}

</style>
