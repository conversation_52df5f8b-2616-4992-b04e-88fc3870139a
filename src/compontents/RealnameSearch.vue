<script setup lang="ts">
import { ref, computed, watch, nextTick } from "vue"
import pinyin from 'pinyin'

interface Option {
  value: any
  label: string
}

interface Props {
  options: Option[]
  placeholder?: string
  disabled?: boolean
  modelValue?: string | string[]
  width?: string
  multiple?: boolean
  collapseTags?:boolean
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '输入中文或拼音',
  disabled: false,
  modelValue: '',
  width: '100%',
  collapseTags:false
})

const emit = defineEmits<{
  (e: 'update:modelValue', value: string | string[]): void
  (e: 'change', value: string | string[], selectedOption?: Option | Option[]): void
  (e: 'select', selectedOption: Option | Option[]): void
}>()

const collapseTags = ref(props.collapseTags)
const inputValue = ref(props.modelValue)
const searchText = ref('') // 专门用于搜索的文本

// 监听外部modelValue变化
watch(() => props.modelValue, (newVal) => {
  inputValue.value = newVal
}, { deep: true })

// 拼音搜索过滤方法
const handleFilter = (val: string) => {
  searchText.value = val.trim().toLowerCase()
  if (!props.multiple) {
    inputValue.value = searchText.value
  }
  return searchText.value
}

// 获取过滤后的选项
const filteredOptions = computed(() => {
  const input = searchText.value
  if (!input) return props.options
  
  return props.options.filter((option) => {
    const label = option.label
    
    // 1. 中文完全匹配
    if (label.toLowerCase() === input) {
      return true
    }
    
    // 2. 中文部分匹配
    if (label.toLowerCase().includes(input)) {
      return true
    }
    
    // 3. 拼音全拼匹配
    const fullPinyin = pinyin(label, { 
      style: pinyin.STYLE_NORMAL,
      heteronym: false
    }).reduce((acc, cur) => acc.concat(cur), []).join('')
    
    if (fullPinyin.toLowerCase() === input || fullPinyin.toLowerCase().includes(input)) {
      return true
    }
    
    // 4. 拼音首字母匹配
    const firstLetterPinyin = pinyin(label, { 
      style: pinyin.STYLE_FIRST_LETTER,
      heteronym: false
    }).reduce((acc, cur) => acc.concat(cur), []).join('')
    
    if (firstLetterPinyin.toLowerCase() === input || firstLetterPinyin.toLowerCase().includes(input)) {
      return true
    }

    return false
  })
})

const handleChange = (val: string | string[]) => {
  if (props.multiple && Array.isArray(val)) {
    const selectedItems = val.map(v => props.options.find(opt => opt.value === v)).filter(Boolean)
    nextTick(() => {
      inputValue.value = selectedItems.map(item => item.label)
      emit('update:modelValue', selectedItems.map(item => item.value))
      emit('change', selectedItems.map(item => item.value), selectedItems)
      emit('select', selectedItems.map(item => ({
        value: item.value,
        label: item.label
      })))
    })
  } else if (!props.multiple && typeof val === 'string') {
    const selected = props.options.find(opt => opt.value === val)
    if (selected) {
      nextTick(() => {
        inputValue.value = selected.label
        emit('update:modelValue', selected.value)
        emit('change', selected.value, selected)
        emit('select', selected)
      })
    } else {
      nextTick(() => {
        inputValue.value = ''
        emit('update:modelValue', '')
        emit('change', '')
      })
    }
  }
}
</script>

<template>
    <el-select
      v-model="inputValue"
      :placeholder="placeholder"
      :disabled="disabled"
      filterable
      clearable
      :filter-method="handleFilter"
      @change="handleChange"
      :multiple="multiple"
      :collapse-tags="collapseTags"
    >
    <el-option
      v-for="(item) in filteredOptions"
      :key="item.value"
      :label="item.label"
      :value="item.value"
    />
  </el-select>
</template>

<style lang="scss" scoped>
.el-select {
  width: v-bind('props.width');
}
</style>
