<script lang="ts" setup>
import {
	checkPermission,
	isCheckPermission
} from "@/app/platform/utils/permission"
import { debounce } from "lodash-es"

interface btnObj {
	name?: string
	icon?: string[]
	class?: string
	roles?: string // 权限控制
	disabled?: boolean // 禁用控制
	hidden?: boolean // 隐藏控制
}

interface Props {
	button: btnObj[]
	isNotRadius?: boolean
	loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
	loading: false
})
const button = toRef(props, "button")
const isNotRadius = toRef(props, "isNotRadius")
const loading = toRef(props, "loading")
const emit = defineEmits<{
	(e: "onBtnClick", btnName: string | undefined): void
}>()

const onClick = (btn: string | undefined) => {
	emit("onBtnClick", btn)
}

// 防抖处理
const debouncedBtnClick = debounce(onClick, 300, {
	leading: false,
	trailing: true
})
</script>

<template>
	<div class="common-btn-list-wrapper">
		<template v-for="(btn, index) in button" :key="index">
			<template v-if="btn.roles">
				<el-button
					v-if="isCheckPermission(btn.roles)"
					:disabled="checkPermission(btn.roles) || btn.disabled"
					:loading="loading"
					:key="index"
					type="primary"
					:class="[
						isNotRadius ? 'button-not-radius' : '',
						btn.class,
						btn.hidden ? 'button-is-hidden' : ''
					]"
					@focus="(e: any) => e.target.blur()"
					@click="debouncedBtnClick(btn.name)"
				>
					<font-awesome-icon
						v-if="btn.icon && btn.icon.length"
						:icon="btn.icon"
					/>
					<span class="btn-text">{{ btn.name }}</span>
				</el-button>
			</template>

			<el-button
				v-else
				:disabled="btn.disabled"
				:loading="loading"
				type="primary"
				:color="btn.class"
				:class="[
					isNotRadius ? 'button-not-radius' : '',
					btn.class,
					btn.hidden ? 'button-is-hidden' : ''
				]"
				@focus="(e: any) => e.target.blur()"
				@click="debouncedBtnClick(btn.name)"
			>
				<!-- @click="onClick(btn.name)" -->
				<font-awesome-icon
					v-if="btn.icon && btn.icon.length"
					:icon="btn.icon"
				/>
				<span class="btn-text">{{ btn.name }}</span>
			</el-button>
		</template>

		<slot></slot>
	</div>
</template>

<style lang="scss" scoped>
.common-btn-list-wrapper {
	display: flex;
	flex-wrap: wrap;
	:deep(.el-button.button-not-radius) {
		border-radius: 0;
	}

	:deep(.el-button.button-not-radius:not(.is-disabled)) {
		// border: 1px solid var(--pitaya-active-btn-background) !important;
		border-left: 1px solid transparent !important;
		border-right: 1px solid transparent !important;
	}
	.btn-text {
		margin-left: 5px;
		padding-top: 1px;
	}
}

:deep(.el-button.is-disabled) {
	background-color: #f6f6f6;
	color: #999 !important;
	border: 1px solid #dcdfe6 !important;
}

.button-is-hidden {
	display: none;
}
</style>
