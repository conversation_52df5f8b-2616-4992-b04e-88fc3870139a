import router from "@/router"
import { useUserStoreHook } from "@/app/platform/store/modules/user"
import { useBackBtnStore } from "@/app/platform/store/modules/backBtn"
import { usePermissionStoreHook } from "@/app/platform/store/modules/permission"
import { ElMessage } from "element-plus"
import { getToken } from "@/app/platform/utils/cache/cookies"
import isWhiteList from "@/config/whiteList"
import NProgress from "nprogress"
import "nprogress/nprogress.css"

NProgress.configure({ showSpinner: false })

router.beforeEach(async (to, _from, next) => {
	NProgress.start()
	const userStore = useUserStoreHook()
	const backBtnStore = useBackBtnStore()
	const permissionStore = usePermissionStoreHook()
	backBtnStore.backBtnState.name = ""
	backBtnStore.backBtnState.state = false

	// 判断该用户是否登录
	if (getToken()) {
		//如果有权限，并且权限列表不为空，循环动态列表匹配路径
		const isAuthenticated = permissionStore.routes
		if (isAuthenticated.length > 0) {
			//匹配成功，当前页面可进入
			if (containsExactPath(permissionStore.routes, to.path)) {
				if (to.path === "/login") {
					// 如果已经登录，并准备进入 Login 页面，则重定向到主页
					next({ path: "/" })
					NProgress.done()
				} else {
					// 检查用户是否已获得其权限角色
					if (!userStore.roles) {
						try {
							await permissionStore.setRoutes()
							next({ ...to, replace: true })
						} catch (err: any) {
							// 过程中发生任何错误，都直接重置 Token，并重定向到登录页面
							userStore.resetToken()
							next("/login")
							NProgress.done()
							if (err.message == "您无权限登陆系统") return
							ElMessage.error(err.message || "路由守卫过程发生错误")
						}
					} else {
						next()
					}
				}
			} else {
				//匹配失败，跳转到登录页面
				// userStore.resetToken()
				next("/403")
				NProgress.done()
				// ElMessage.error( "匹配失败，跳转到登录页面")
			}
		} else {
			if (!userStore.roles) {
				try {
					await permissionStore.setRoutes()
					next({ ...to, replace: true })
				} catch (err: any) {
					// 过程中发生任何错误，都直接重置 Token，并重定向到登录页面
					userStore.resetToken()
					next("/login")
					NProgress.done()
					if (err.message == "您无权限登陆系统") return
					ElMessage.error(err.message || "路由守卫过程发生错误")
				}
			} else {
				next()
			}
		}
	} else {
		// 如果没有 Token
		if (isWhiteList(to)) {
			// 如果在免登录的白名单中，则直接进入
			next()
		} else {
			// 其他没有访问权限的页面将被重定向到登录页面
			next("/login")
			NProgress.done()
		}
	}
})

router.afterEach(() => {
	NProgress.done()
})

const containsExactPath = (routers: any, topath: any) => {
	// 递归函数来检查路径是否存在于路由树中
	function checkPath(routes: any, targetPath: any) {
		for (let route of routes) {
			// 构建当前路由的完整路径（但由于我们要精确匹配，所以这里直接使用 route.path）
			if (route.path === targetPath) {
				// 找到了完全匹配的路径
				return true
			}
			// 如果当前路由有子路由，则递归检查子路由
			if (route.children && route.children.length > 0) {
				// 注意：这里我们不拼接路径，因为我们要精确匹配完整的 path
				let found = checkPath(route.children, targetPath)
				if (found) {
					return true
				}
			}
		}
		// 没有找到匹配的路径
		return false
	}

	// 调用递归函数来检查路径
	return checkPath(routers, topath)
}
