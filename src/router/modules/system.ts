import { type RouteRecordRaw } from "vue-router"
const Layouts = () =>
	import("../../app/platform/layout/layouts/index.vue")
const asyncRoutes: RouteRecordRaw[] = [
	{
		path: "/system",
		name: "system",
		component: Layouts,
		meta: {
			title: "系统",
			awesomeIcon: ["fas", "gear"],
			roles: ["top-menu-role:system"]
		},
		children: [
			{
				path: "line",
				component: () =>
					import("../../app/platform/views/system/line.vue"),
				name: "systemLine",
				meta: {
					activeMenuTop: "/system",
					title: "线路管理",
					awesomeIcon: ["fas", "rectangle-list"],
					roles: ["first-menu-role:line"]
				}
			},
			{
				path: "company",
				component: () =>
					import("@/app/platform/views/system/company.vue"),
				name: "systemCompany",
				meta: {
					activeMenuTop: "/system",
					title: "公司管理",
					awesomeIcon: ["fas", "city"],
					roles: ["first-menu-role:company"]
				}
			},
			{
				path: "profession",
				component: () =>
					import("@/app/platform/views/system/profession.vue"),
				name: "systemProfession",
				meta: {
					activeMenuTop: "/system",
					title: "专业管理",
					awesomeIcon: ["fas", "glasses"],
					roles: ["first-menu-role:profession"]
				}
			},
			{
				path: "department",
				component: () =>
					import("@/app/platform/views/system/department.vue"),
				name: "systemDepartment",
				meta: {
					activeMenuTop: "/system",
					title: "部门管理",
					awesomeIcon: ["fas", "sitemap"],
					roles: ["first-menu-role:department"]
				}
			},
			{
				path: "team",
				component: () =>
					import("@/app/platform/views/system/teamGroup.vue"),
				name: "systemTeam",
				meta: {
					activeMenuTop: "/system",
					title: "班组管理",
					awesomeIcon: ["fas", "user-group"],
					roles: ["first-menu-role:team"]
				}
			},
			{
				path: "baseUser",
				component: () =>
					import("@/app/platform/views/system/account.vue"),
				name: "systemBaseUser",
				meta: {
					activeMenuTop: "/system",
					title: "账号管理",
					awesomeIcon: ["fas", "user"],
					roles: ["first-menu-role:baseUser"]
				}
			},
			{
				path: "baseRole",
				component: () =>
					import("@/app/platform/views/system/baseRole.vue"),
				name: "systemBaseRole",
				meta: {
					activeMenuTop: "/system",
					title: "角色管理",
					awesomeIcon: ["fas", "user-check"],
					roles: ["first-menu-role:baseRole"]
				}
			},
			{
				path: "requestMap",
				component: () =>
					import("@/app/platform/views/system/requestMap.vue"),
				name: "systemRequestMap",
				meta: {
					activeMenuTop: "/system",
					title: "权限管理",
					awesomeIcon: ["fas", "copyright"],
					roles: ["first-menu-role:requestMap"]
				}
			},
			{
				path: "labelCode",
				component: () =>
					import("@/app/platform/views/system/labelCode.vue"),
				name: "systemLabelCode",
				meta: {
					activeMenuTop: "/system",
					title: "标签管理",
					awesomeIcon: ["fas", "tags"],
					roles: ["first-menu-role:labelCode"]
				}
			},
			{
				path: "asset",
				component: () =>
					import("@/app/platform/views/system/asset.vue"),
				name: "systemAsset",
				meta: {
					activeMenuTop: "/system",
					title: "资产分类",
					awesomeIcon: ["fas", "layer-group"],
					roles: ["first-menu-role:asset"]
				}
			},
			{
				path: "device",
				name: "systemDevice",
				meta: {
					activeMenuTop: "/system",
					title: "终端管理",
					awesomeIcon: ["fas", "robot"],
					roles: ["first-menu-role:device"],
					alwaysShow: true
				},
				children: [
					{
						path: "auth",
						component: () =>
							import("@/app/platform/views/system/auth.vue"),
						name: "systemDeviceuth",
						meta: {
							activeMenuTop: "/system",
							title: "终端授权",
							roles: ["second-menu-role:auth"]
						}
					},
					{
						path: "version",
						component: () =>
							import("@/app/platform/views/system/version.vue"),
						name: "systemDeviceVersion",
						meta: {
							activeMenuTop: "/system",
							title: "版本更新",
							roles: ["second-menu-role:version"]
						}
					}
				]
			},
			{
				path: "dictionary",
				component: () =>
					import("@/app/platform/views/system/dictionary.vue"),
				name: "systemDictionary",
				meta: {
					activeMenuTop: "/system",
					title: "数据字典",
					awesomeIcon: ["fas", "book"],
					roles: ["first-menu-role:dictionary"]
				}
			},
			{
				path: "parameter",
				component: () =>
					import("@/app/platform/views/system/parameter.vue"),
				name: "systemParameter",
				meta: {
					activeMenuTop: "/system",
					title: "系统参数",
					awesomeIcon: ["fas", "sliders"],
					roles: ["first-menu-role:parameter"]
				}
			},
			{
				path: "processDesign",
				component: () =>
					import(
						"@/app/platform/views/system/bpmn/processDesign.vue"
					),
				name: "ProcessDesign",
				meta: {
					activeMenuTop: "/system",
					title: "流程设计",
					awesomeIcon: ["fas", "code-compare"],
					roles: ["first-menu-role:processDesign"]
				}
			},
			{
				path: "processCallback",
				component: () =>
					import(
						"@/app/platform/views/system/bpmn/processCallback.vue"
					),
				name: "ProcessCallback",
				meta: {
					activeMenuTop: "/system",
					title: "流程回调",
					awesomeIcon: ["fas", "rectangle-list"],
					roles: ["first-menu-role:processCallback"]
				}
			},
			{
				path: "bpmn",
				component: () =>
					import("@/app/platform/views/system/bpmn/index.vue"),
				name: "Bpmn",
				meta: {
					activeMenuTop: "/system/processDesign",
					title: "流程图",
					hidden: true,
					awesomeIcon: ["fas", "rectangle-list"]
				}
			}
			// {
			// 	path: "initiate",
			// 	component: () =>
			// 		import(
			// 			"@/app/platform/views/examineApprove/initiate.vue"
			// 		),
			// 	name: "Approved",
			// 	meta: {
			// 		activeMenuTop: "/system",
			// 		title: "我的发起",
			// 		hidden: true,
			// 		awesomeIcon: ["fas", "rectangle-list"],
			// 		roles: ["first-menu-role:line"]
			// 	}
			// },
			// {
			// 	path: "approved",
			// 	component: () =>
			// 		import(
			// 			"@/app/platform/views/examineApprove/approved.vue"
			// 		),
			// 	name: "Approved",
			// 	meta: {
			// 		activeMenuTop: "/system",
			// 		title: "我的审批",
			// 		hidden: true,
			// 		awesomeIcon: ["fas", "rectangle-list"],
			// 		roles: ["first-menu-role:line"]
			// 	}
			// }
		]
	}
]

export default asyncRoutes
