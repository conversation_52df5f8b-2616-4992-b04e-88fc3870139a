import { type RouteRecordRaw } from "vue-router"
const Layouts = () =>
	import("@/app/platform/layout/layouts/index.vue")

const asyncRoutes: RouteRecordRaw[] = [
	{
		path: "/approved",
		name: "Approved",
		component: Layouts,
		meta: {
			title: "审批",
			awesomeIcon: ["fas", "gear"],
			roles: ["top-menu-role:approved"]
		},
		children: [
			{
				path: "myInitiate",
				component: () =>
					import(
						"@/app/platform/views/examineApprove/initiate.vue"
					),
				name: "MyInitiate",
				meta: {
					activeMenuTop: "/approved",
					title: "我发起的",
					awesomeIcon: ["fas", "rectangle-list"],
					roles: ["first-menu-role:myInitiate"]
				}
			},
			{
				path: "myApproved",
				component: () =>
					import(
						"@/app/platform/views/examineApprove/approved.vue"
					),
				name: "MyApproved",
				meta: {
					activeMenuTop: "/approved",
					title: "我审批的",
					awesomeIcon: ["fas", "city"],
					roles: ["first-menu-role:myApproved"]
				}
			}
		]
	}
]

export default asyncRoutes
