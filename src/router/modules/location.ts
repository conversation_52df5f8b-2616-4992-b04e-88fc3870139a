import { type RouteRecordRaw } from "vue-router"
const Layouts = () =>
	import("@/app/platform/layout/layouts/index.vue")

const asyncRoutes: RouteRecordRaw[] = [
	{
		path: "/location",
		name: "location",
		component: Layouts,
		meta: {
			title: "位置",
			awesomeIcon: ["fas", "location-dot"],
			roles: ["top-menu-role:location"]
		},
		children: [
			{
				path: "grade",
				component: () =>
					import("@/app/platform/views/location/level.vue"),
				name: "locationGrade",
				meta: {
					activeMenuTop: "/location",
					title: "位置分级",
					awesomeIcon: ["fas", "network-wired"],
					roles: ["first-menu-role:grade"]
				}
			},
			{
				path: "info",
				component: () =>
					import("@/app/platform/views/location/info.vue"),
				name: "locationInfo",
				meta: {
					activeMenuTop: "/location",
					title: "位置信息",
					awesomeIcon: ["fas", "map-location-dot"],
					roles: ["first-menu-role:info"]
				}
			}
		]
	}
]

export default asyncRoutes
