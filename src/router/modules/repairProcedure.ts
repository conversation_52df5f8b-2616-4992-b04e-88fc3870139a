import { type RouteRecordRaw } from "vue-router"
const Layouts = () =>
	import("@/app/platform/layout/layouts/index.vue")

const asyncRoutes: RouteRecordRaw[] = [
	{
		path: "/deviceBase",
    name: "deviceBase",
    component: Layouts,
		meta: {
			title: "修程",
			awesomeIcon: ["fas", "wrench"],
			// roles: ["top-menu-role:system"]
		},
		children: [
      {
				path: "inspectionGauge",
        name: "deviceBaseInspectionGauge",
				component: () =>
					import("@/app/platform/views/repairProcedure/InspectionGauge/index.vue"),
				meta: {
					activeMenuTop: "/deviceBase",
					title: "项点维规",
					awesomeIcon: ["fas", "project-diagram"],
					// roles: ["first-menu-role:line"]
				}
			},
      {
				path: "repairProcedure",
        name: "deviceBaseRepairProcedure",
				component: () =>
        import("@/app/platform/views/repairProcedure/repairProcedure/index.vue"),
				meta: {
					activeMenuTop: "/deviceBase",
					title: "修程管理",
					awesomeIcon: ["fas", "cloud"],
					// roles: ["first-menu-role:line"]
				}
			},
			{
				path: "standardOperation",
        name: "deviceBaseStandardOperation",
				component: () =>
        import("@/app/platform/views/repairProcedure/standardOperation/index.vue"),
				meta: {
					activeMenuTop: "/deviceBase",
					title: "标准工作",
					awesomeIcon: ["fas", "registered"],
					// roles: ["first-menu-role:line"]
				}
			},
			{
				path: "standardOperation/group",
        name: "deviceBaseStandardOperationGroup",
				component: () =>
        import("@/app/platform/views/repairProcedure/group/index.vue"),
				meta: {
					activeMenuTop: "/deviceBase",
					title: "组合标准工作",
					awesomeIcon: ["fas", "clone"],
					// roles: ["first-menu-role:line"]
				}
			},
			{
				path: "standardOperation/formTemplate",
        name: "deviceBaseStandardOperationFormTemplate",
				component: () =>
        import("@/app/platform/views/repairProcedure/formTemplate/index.vue"),
				meta: {
					activeMenuTop: "/deviceBase",
					title: "记录表单",
					awesomeIcon: ["fas", "file-alt"],
					// roles: ["first-menu-role:line"]
				}
			},
		]
	}
]

export default asyncRoutes
