import { type RouteRecordRaw } from "vue-router"
const Layouts = () =>
	import("../../app/platform/layout/layouts/index.vue")

const asyncRoutes: RouteRecordRaw[] = [
	{
		path: "/device",
		name: "device",
		component: Layouts,
		meta: {
			title: "设备",
			awesomeIcon: ["fas", "desktop"],
			roles: ["top-menu-role:device"]
		},
		children: [
			{
				path: "grade",
				component: () =>
					import("../../app/platform/views/device/grade.vue"),
				name: "deviceGrade",
				meta: {
					activeMenuTop: "/device",
					title: "设备分级",
					awesomeIcon: ["fas", "window-restore"],
					roles: ["first-menu-role:grade"]
				}
			},
			{
				path: "deconstruction",
				component: () =>
					import("@/app/platform/views/device/deconstruction.vue"),
				name: "deviceDeconstruction",
				meta: {
					activeMenuTop: "/device",
					title: "设备解构",
					awesomeIcon: ["fas", "layer-group"],
					roles: ["first-menu-role:deconstruction"]
				}
			},
			{
				path: "ledger",
				component: () =>
					import("@/app/platform/views/device/ledger.vue"),
				name: "deviceLedger",
				meta: {
					activeMenuTop: "/device",
					title: "设备台账",
					awesomeIcon: ["fas", "pen-to-square"],
					roles: ["first-menu-role:ledger"]
				}
			}
		]
	}
]

export default asyncRoutes
