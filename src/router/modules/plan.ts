import { type RouteRecordRaw } from "vue-router"
const Layouts = () =>
	import("@/app/platform/layout/layouts/index.vue")

const asyncRoutes: RouteRecordRaw[] = [
	{
		path: "/plan",
		name: "plan",
		component: Layouts,
		meta: {
			title: "计划",
			awesomeIcon: ["fas", "desktop"],
			roles: ["top-menu-role:device"]
		},
		children: [
			{
				path: "/plan/workStation",
				component: () =>
					import("@/app/platform/views/plan/index.vue"),
				name: "planWorkStation",
				meta: {
					activeMenuTop: "/plan",
					title: "工作台",
					awesomeIcon: ["fas", "layer-group"],
					roles: ["first-menu-role:deconstruction"]
				}
			}
		]
	}
]

export default asyncRoutes
