import { type RouteRecordRaw } from "vue-router"
const Layouts = () =>
	import("@/app/platform/layout/layouts/index.vue")

const asyncRoutes: RouteRecordRaw[] = [
	{
		path: "/purchase",
		name: "purchase",
		component: Layouts,
		meta: {
			title: "采购",
			awesomeIcon: ["fas", "desktop"],
			roles: ["top-menu-role:device"]
		},
		children: [
			{
				path: "/purchase/plan",
				component: () =>
					import("@/app/baseline/views/purchase/plan.vue"),
				name: "purchase_plan",
				meta: {
					activeMenuTop: "/plan",
					title: "工作台",
					awesomeIcon: ["fas", "layer-group"],
					roles: ["first-menu-role:deconstruction"]
				}
			}
		]
	}
]

export default [asyncRoutes]
