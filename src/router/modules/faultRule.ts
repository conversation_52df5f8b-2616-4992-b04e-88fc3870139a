import { type RouteRecordRaw } from "vue-router"
const Layouts = () =>
	import("@/app/platform/layout/layouts/index.vue")

const asyncRoutes: RouteRecordRaw[] = [
	{
		path: "/faultRule",
    name: "faultRule",
    component: Layouts,
		meta: {
			title: "故障",
			awesomeIcon: ["fas", "camera"],
			// roles: ["top-menu-role:system"]
		},
		children: [
      {
				path: "degree",
        name: "faultRuleDegree",
				component: () =>
					import("@/app/platform/views/faultRule/degree/index.vue"),
				meta: {
					activeMenuTop: "/faultRule",
					title: "影响程度",
					awesomeIcon: ["fas", "pen"],
					// roles: ["first-menu-role:line"]
				}
			},
      {
				path: "classification",
        name: "faultRuleClassification",
				component: () =>
        import("@/app/platform/views/faultRule/classification/index.vue"),
				meta: {
					activeMenuTop: "/faultRule",
					title: "故障分类",
					awesomeIcon: ["fas", "camera"],
					// roles: ["first-menu-role:line"]
				}
			},
			{
				path: "faultManag",
        name: "faultRaultManag",
				component: () =>
        import("@/app/platform/views/faultRule/faultManag/index.vue"),
				meta: {
					activeMenuTop: "/faultRule",
					title: "常见故障",
					awesomeIcon: ["fas", "pen-square"],
					// roles: ["first-menu-role:line"]
				}
			},
			{
				path: "report",
        name: "faultRuleReport",
				component: () =>
        import("@/app/platform/views/faultRule/report/index.vue"),
				meta: {
					activeMenuTop: "/faultRule",
					title: "故障上报",
					awesomeIcon: ["fas", "save"],
					// roles: ["first-menu-role:line"]
				}
			},
			{
				path: "dispatchingDesk",
        name: "faultRuleDispatchingDesk",
				component: () =>
        import("@/app/platform/views/faultRule/dispatchingDesk/index.vue"),
				meta: {
					activeMenuTop: "/faultRule",
					title: "故障调度",
					awesomeIcon: ["fas", "archive"],
					// roles: ["first-menu-role:line"]
				}
			},
		]
	}
]

export default asyncRoutes
