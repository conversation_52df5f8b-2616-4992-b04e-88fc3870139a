import {
	type RouteRecordRaw,
	createRouter,
	createWebHashHistory,
	createWebHistory
} from "vue-router"
import system from "./modules/system"
import location from "./modules/location"
import device from "./modules/device"
import repair from "./modules/repairProcedure"
import faultRule from "./modules/faultRule"
import plan from "./modules/plan"
import approved from "./modules/approved"



/** 常驻路由 */
export const constantRoutes: RouteRecordRaw[] = [
	{
		path: "/403",
		name: "403",
		component: () =>
			import("@/app/platform/views/errorPage/403.vue"),
		meta: {
			hidden: true
		}
	},
	{
		path: "/404",
		name: "404",
		component: () =>
			import("@/app/platform/views/errorPage/404.vue"),
		meta: {
			hidden: true
		}
	},

	{
		path: "/login",
		component: () => import("@/app/platform/views/login/index.vue"),
		meta: {
			hidden: true
		}
	}
]

// 顶部菜单路由
 //export const asyncRoutes: RouteRecordRaw[] = [...location, ...device, ...system,...repair,...approved,...faultRule,...style,...plan]
export const asyncRoutes: RouteRecordRaw[] = []

const router = createRouter({
	history:
		import.meta.env.VITE_ROUTER_HISTORY === "hash"
			? createWebHashHistory(import.meta.env.VITE_PUBLIC_PATH)
			: createWebHistory(import.meta.env.VITE_PUBLIC_PATH),
	routes: constantRoutes
})

/** 重置路由 */
export function resetRouter() {
	// 注意：所有动态路由路由必须带有 Name 属性，否则可能会不能完全重置干净
	try {
		router.getRoutes().forEach((route) => {
			if (route.name) {
				router.removeRoute(route?.name)
			}
		})
	} catch {
		// 强制刷新浏览器也行，只是交互体验不是很好
		window.location.reload()
	}
}

export default router
