/** 自定义 Element Plus 样式 */

// 表格
.el-table {
	// 表头
	th.el-table__cell {
		background-color: var(--el-fill-color-light) !important;
	}
}

// 分页
.el-pagination {
	// 参考 Bootstrap 的响应式设计 WIDTH = 768
	@media screen and (max-width: 768px) {
		.el-pagination__total,
		.el-pagination__sizes,
		.el-pagination__jump,
		.btn-prev,
		.btn-next {
			display: none !important;
		}
	}
}

// tooltip最大显示宽度
.is-dark {
	max-width: 350px;
}

// el-input 图标后缀
.el-input-group__append {
	padding: 0 16px !important;
	background-color: #f5f7fa !important;
}

// el-input 选择框后缀
.select-append {
	.el-input-group__append {
		padding: 0 20px !important;
	}
}

// el-input icon后缀
.icon-append {
	.el-input-group__append {
		padding: 0 6.5px !important;
	}
}
// el-input 文字后缀
.text-append {
	.el-input-group__append {
		padding: 0 9px !important;
	}
}

.el-input-number {
	.el-input__inner {
		text-align: left !important;
	}
}

.el-input-number__decrease,
.el-input-number__increase {
	background-color: #f5f7fa !important;
}

.el-overlay {
	overflow: hidden !important;
}

.el-text.el-text--primary {
	color: #204a9c !important;
}

.el-button+.el-button {
    margin-left: 10px !important;
}

// radio样式
.el-radio__input.is-checked+.el-radio__label{
	color: var(--pitaya-checked-bg-color)!important;
}
.el-radio__input.is-checked .el-radio__inner{
	border-color:var(--pitaya-checked-bg-color)!important;
    background: var(--pitaya-checked-bg-color)!important;
}

// checkbox样式
.el-checkbox__input.is-checked+.el-checkbox__label{
	color: var(--pitaya-checked-bg-color)!important;
}
.el-checkbox__input.is-checked .el-checkbox__inner{
	border-color: var(--pitaya-checked-bg-color)!important;
    background: var(--pitaya-checked-bg-color)!important;
}

// table checkbox样式
.el-checkbox__input.is-indeterminate .el-checkbox__inner{
	border-color: var(--pitaya-checked-bg-color)!important;
    background: var(--pitaya-checked-bg-color)!important;
}