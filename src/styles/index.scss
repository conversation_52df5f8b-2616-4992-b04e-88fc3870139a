// 全局 CSS 变量
@import "./variables.css";
// Element Plus
@import "./element-plus.scss";
// Vxe Table
@import "./vxe-table.scss";
// mixin
@import "./mixins.scss";

// 业务页面几乎都应该在根元素上挂载 class="app-container"，以保持页面美观
// 上下布局  = 搜索框 + 表格框  app-container  content
.app-container {
	padding: 10px;
	height: 0;
	flex: 1;
	overflow: hidden;
	display: flex;
	flex-direction: column;
	.content {
		height: 0;
		flex: 1;
		margin-top: 10px;
		overflow: hidden;
	}
	// 右侧容器-带有查询区域
	.app-right-container {
		&.has-query {
			.app-content-wrapper {
				margin-top: 10px;
			}
		}
	}
	// 左侧（树）容器-设定最小宽度
	.app-left-container {
		min-width: 300px;
	}
}
//左右布局  =  树 + 【搜索+表格】
.app-container-row {
	padding: 10px;
	height: 0;
	flex: 1;
	overflow: hidden;
	display: flex;
	flex-direction: row;
	// justify-content: space-between;

	.left-model-frame {
		min-width: 300px;
		width: 20%;
		height: 100%;
		margin-right: 10px;
	}
	.right-model-frame {
		width: calc(80% - 10px);
		display: flex;
		flex-direction: column;
		height: 100%;
		.content {
			height: 0;
			flex: 1;
			margin-top: 10px;
			overflow: hidden;
		}
	}
}

html {
	height: 100%;
}

body {
	margin: 0;
	height: 100%;
	background-color: var(--pitaya-body-bg-color);
	-moz-osx-font-smoothing: grayscale;
	-webkit-font-smoothing: antialiased;
	font-family:  "Microsoft YaHei", "Microsoft YaHei UI", "PingFang SC","Arial", "sans-serif";

	@include scrollbar();
}

#app {
	height: 100%;
}

*,
*::before,
*::after {
	box-sizing: border-box;
	padding: 0;
	margin: 0;
	font-style: normal;
	list-style: none;
	position: relative;
	font-weight: normal;
	outline: none;
	// user-select: none;
}

a,
a:focus,
a:hover {
	color: inherit;
	outline: none;
	text-decoration: none;
}

div:focus {
	outline: none;
}

.el-drawer__body {
	padding: 10px !important;
}

.el-form-item {
	font-size: var(--pitaya-fs-12) !important;
}

.el-form--default.el-form--label-top .el-form-item .el-form-item__label {
	margin-bottom: 5px !important;
}

.el-form-wrapper {
	padding-top: 10px;
}

.el-pagination button[disabled] {
	background-color: #fff !important;
	color: #ccc !important;
}

.table-inner-btn {
	margin-left: 5px;
	color: #204a9c;
}

button {
	--el-button-bg-color: var(--pitaya-btn-background) !important;
	--el-button-hover-bg-color: var(--pitaya-hover-btn-background) !important;
	--el-button-active-bg-color: var(--pitaya-active-btn-background) !important;
	border: none !important;
}

.upload-apk-wrapper {
	flex: 1;
	.el-upload-list {
		margin-top: 0;
	}
}

.el-upload.el-upload--text {
	width: 100%;
}

.el-upload-button {
	flex: 1;
	background-color: var(--pitaya-btn-background) !important;
	border-color: var(--pitaya-btn-background) !important;
	.el-icon {
		margin-right: 5px;
	}
}

.el-table {
	font-size: var(--pitaya-fs-12) !important;
}

.el-table th .cell {
	font-weight: bold !important;
}

.el-table .el-table__cell {
	padding: 4px 0 !important;
}

.el-table-column--selection {
	// display: flex;
	// justify-content: center;
	.cell {
		padding: 0 !important;
		display: flex;
		justify-content: center;
		&.el-tooltip {
			min-width: 30px !important;
		}
	}
}

.el-select-dropdown__item {
	font-size: var(--pitaya-fs-12) !important;
}

.el-button {
	font-size: var(--pitaya-fs-12) !important;
	color: #fff !important;
}

.el-input .el-input__inner {
	font-size: var(--pitaya-fs-12) !important;
}
.el-input__prefix-inner {
	font-size: var(--pitaya-fs-12) !important;
}

.el-input.is-disabled .el-input__inner {
	color: var(--pitaya-place-font-color) !important;
	-webkit-text-fill-color: var(--pitaya-place-font-color) !important;
}

.el-form-item__label {
	font-size: var(--pitaya-fs-12) !important;
	color: var(--pitaya-place-font-color) !important;
}

// 表格中边框颜色设置
// .el-table td.el-table__cell,
// .el-table th.el-table__cell.is-leaf,
// table,
// thead,
// tbody,
// .el-input,
// .el-table--border,
// .el-textarea.is-disabled .el-textarea__inner {
// 	border-color: var(--pitaya-border-color) !important;
// }

.el-table__border-left-patch,
.el-table--border .el-table__inner-wrapper::after,
.el-table--border::after,
.el-table--border::before,
// .el-table__inner-wrapper::before {
// 	background-color: var(--pitaya-border-color) !important;
// }

.el-table {
	color: var(--pitaya-table-font-color) !important;
}

// .el-select:hover:not(.el-select--disabled) .el-input__wrapper,
// .el-textarea__inner,
// .el-input.is-disabled .el-input__wrapper {
// 	box-shadow: 0 0 0 1px var(--pitaya-border-color) inset !important;
// }

.el-textarea__inner {
	font-size: var(--pitaya-fs-12) !important;
}

.el-textarea.is-disabled .el-textarea__inner {
	font-size: var(--pitaya-fs-12) !important;
	color: var(--pitaya-place-font-color) !important;
	-webkit-text-fill-color: var(--pitaya-place-font-color) !important;
}

// .el-form-item {
// 	margin-bottom: 10px !important;
// }

// .el-form-item__error {
// 	transform: scale(0.8);
// 	transform-origin: 0 50%;
// }

// 隐藏按钮
.permission-hidden-btn {
	display: none !important;
}

// 禁用按钮样式
.unusableBtn {
	* {
		color: var(--pitaya-place-font-color) !important;
	}
	cursor: not-allowed !important;
	pointer-events: none !important;
	user-select: none !important;
}

.common-btn-list-wrapper {
	.unusableBtn {
		* {
			color: var(--pitaya-border-color) !important;
		}
		background-color: rgba(48, 75, 136, 0.5) !important;
	}
}

.drawer-hidden-box .el-drawer__body {
	overflow: hidden !important;
}

.app-tabs-wrapper {
	position: absolute;
	left: 150px;
	bottom: 0;
	.el-tabs__item {
		font-size: 14px;
		color: #666;
		&.is-active {
			color: var(--pitaya-sidebar-menu-active-text-color);
		}
	}
	.el-tabs__active-bar {
		background-color: var(--pitaya-header-bg-color);
	}
	.el-tabs__header {
		margin-bottom: 0;
	}
	.el-tabs__nav-wrap {
		&::after {
			width: 0;
			height: 0;
		}
	}
}

.only-select-wrapper {
	th.el-table-column--selection {
		.el-checkbox {
			// display: none !important;
			visibility: hidden;
		}
	}
}

.app-content-wrapper {
	height: 0;
	flex: 1;
	// margin-top: 10px;
	.app-content-group {
		height: 100%;
		:deep(.model-frame-wrapper) {
			height: 100%;
			padding-bottom: 10px;
			overflow: hidden;
			display: flex;
			flex-direction: column;
		}
		.app-el-scrollbar-wrapper {
			height: 0;
			flex: 1;
			.table-inner-btn {
				margin-left: 5px;
				color: #204a9c;
			}
		}
	}
}
.table-inner-btn {
	padding-top: 2px;
}

.el-popper.is-dark{
	max-height: 200px;
	overflow-y: auto;
	&::-webkit-scrollbar {
		width: 0px;
		height: 0px;
	}
	// 滚动条上的滚动滑块
	&::-webkit-scrollbar-thumb {
		border-radius: 0px;
		background-color: transparent;
	}
	&::-webkit-scrollbar-thumb:hover {
		background-color: transparent;
	}
	&::-webkit-scrollbar-thumb:active {
		background-color: transparent;
	}
	// 当同时有垂直滚动条和水平滚动条时交汇的部分
	&::-webkit-scrollbar-corner {
		background-color: transparent;
	}
}

.el-popup-parent--hidden {
	width: 100% !important;
}

.model-frame-wrapper {
	padding-bottom: 0px !important;
}

// 修复日期范围选择框底部边框展示不全的问题
.el-picker__popper {
	max-width: unset;
}

// 媒体预览组件，dialog组件样式
.media-preview {
	.el-dialog__body {
		padding-top: 0 !important;
	}
	.el-dialog__header {
		padding-top: 0 !important;
		margin-right: 0 !important;
	}
}
.cell:has(.el-tag) {
	text-overflow: clip !important;
	overflow: hidden !important;
	justify-content: center;
}

// 表格中颜色定义
.el-tag {
	//上下0 左右13px
	min-width: 50px;
	padding: 0 10px !important;
	line-height: 20px !important;
	height: 22px !important;
	border-radius: 3px !important;
	// 设备重要等级
	&.level_A {
		color: #e25e59;
		border-color: #e25e59;
		background-color: rgba(226, 94, 89, 0.1);
	}
	&.level_B {
		color: #f59b22;
		border-color: #f59b22;
		background-color: rgba(245, 155, 34, 0.1);
	}
	&.level_C {
		color: #658da6;
		border-color: #658da6;
		background-color: rgba(101, 141, 166, 0.1);
	}
	&.level_D {
		color: #28c1c1;
		border-color: #28c1c1;
		background-color: rgba(40, 193, 193, 0.1);
	}
	&.type_2 {
		color: #009dff;
		border-color: #009dff;
		background-color: rgba(0, 157, 255, 0.1);
	}
	&.type_1 {
		color: #a66593;
		border-color: #a66593;
		background-color: rgba(166, 101, 147, 0.1);
	}
	&.type_zh {
		color: #a6bc5e;
		border-color: #a6bc5e;
		background-color: rgba(166, 188, 94, 0.1);
	}
	// 已启用/已停用
	&.enable_false {
		color: #e25e59;
		border-color: #e25e59;
		background-color: rgba(226, 94, 89, 0.1);
	}
	&.enable_true {
		color: #4bae89;
		border-color: #4bae89;
		background-color: rgba(75, 174, 137, 0.1);
	}
	// 运行状态
	&.oper_normal {
		color: #4bae89;
		border-color: #4bae89;
		background-color: rgba(75, 174, 137, 0.1);
	}
	&.oper_fault {
		color: #e25e59;
		border-color: #e25e59;
		background-color: rgba(226, 94, 89, 0.1);
	}
	&.oper_wait_repair {
		color: #658da6;
		border-color: #658da6;
		background-color: rgba(101, 141, 166, 0.1);
	}
	&.oper_repairing {
		color: #a66593;
		border-color: #a66593;
		background-color: rgba(166, 101, 147, 0.1);
	}
	&.oper_block_up {
		color: #d0d0d0;
		border-color: #d0d0d0;
		background-color: rgba(208, 208, 208, 0.1);
	}
	// 资产状态
	&.state_free {
		color: #009dff;
		border-color: #009dff;
		background-color: rgba(0, 157, 255, 0.1);
	}
	&.state_scrap {
		color: #d0d0d0;
		border-color: #d0d0d0;
		background-color: rgba(208, 208, 208, 0.1);
	}
	&.state_forbidden {
		color: #658da6;
		border-color: #658da6;
		background-color: rgba(101, 141, 166, 0.1);
	}
	&.state_inuse {
		color: #4bae89;
		border-color: #4bae89;
		background-color: rgba(75, 174, 137, 0.1);
	}
	// 故障分类
	&.level_0 {
		color: #fff;
		border-color: #e25e59;
		background-color: #e25e59;
	}
	&.level_1 {
		color: #fff;
		border-color: #f59b22;
		background-color: #f59b22;
	}
	&.level_2 {
		color: #fff;
		border-color: #009dff;
		background-color: #009dff;
	}
	// 故障影响程度
	&.fault_hinder_level_0 {
		color: #fff;
		border-color: #e25e59;
		background-color: #e25e59;
	}
	&.fault_hinder_level_1 {
		color: #fff;
		border-color: #f59b22;
		background-color: #f59b22;
	}
	&.fault_hinder_level_2 {
		color: #fff;
		border-color: #009dff;
		background-color: #009dff;
	}
	&.fault_hinder_level_3 {
		color: #fff;
		border-color: #4bae89;
		background-color: #4bae89;
	}
	// 故障告警状态
	&.fault_alarm_warning_0 {
		color: #fff;
		border-color: #e25e59;
		background-color: #e25e59;
	}
	&.fault_alarm_warning_1 {
		color: #fff;
		border-color: #4bae89;
		background-color: #4bae89;
	}
	// 告警级别
	&.fault_alarm_level_1 {
		color: #e6a23c;
		background: #fdf6ec;
		border: 1px solid #f3d19e;
	}
	&.fault_alarm_level_2 {
		color: #28c1c1;
		border-color: #28c1c1;
		background-color: rgba(40, 193, 193, 0.1);
	}
	&.fault_alarm_level_3 {
		color: #009dff;
		border-color: #009dff;
		background-color: rgba(0, 157, 255, 0.1);
	}
	// 故障上报/故障处置状态
	&.fault_report_sts_0 {
		color: #e25e59;
		border-color: #e25e59;
		background-color: rgba(226, 94, 89, 0.1);
	}
	&.fault_report_sts_1 {
		color: #f59b22;
		border-color: #f59b22;
		background-color: rgba(245, 155, 34, 0.1);
	}
	&.fault_report_sts_2 {
		color: #4bae89;
		border-color: #4bae89;
		background-color: rgba(75, 174, 137, 0.1);
	}
	&.fault_report_sts_3 {
		color: #009dff;
		border-color: #009dff;
		background-color: rgba(0, 157, 255, 0.1);
	}
	&.fault_report_sts_4 {
		color: #28c1c1;
		border-color: #28c1c1;
		background-color: rgba(40, 193, 193, 0.1);
	}
	&.comp_sts_1 {
		color: #f59b22;
		border-color: #f59b22;
		background-color: rgba(245, 155, 34, 0.1);
	}
	&.comp_sts_2 {
		color: #4bae89;
		background: #e6fef0;
		border: 1px solid #4bae89;
	}
	&.plan_exec_sts_0 {
		color: #f59b22;
		border-color: #f59b22;
		background-color: rgba(245, 155, 34, 0.1);
	}
	&.plan_exec_sts_1 {
		color: #009dff;
		border-color: #009dff;
		background-color: rgba(0, 157, 255, 0.1);
	}
	&.plan_exec_sts_2 {
		color: #d0d0d0;
		border-color: #d0d0d0;
		background-color: rgba(208, 208, 208, 0.1);
	}
	&.group_sts_0 {
		color: #f59b22;
		border-color: #f59b22;
		background-color: rgba(245, 155, 34, 0.1);
	}
	&.group_sts_1 {
		color: #4bae89;
		background: #e6fef0;
		border: 1px solid #4bae89;
	}

	&.fault_report_sts_5 {
		color: #d0d0d0;
		border-color: #d0d0d0;
		background-color: rgba(208, 208, 208, 0.1);
	}
	&.fault_report_sts_6 {
		color: #d0d0d0;
		border-color: #d0d0d0;
		background-color: rgba(208, 208, 208, 0.1);
	}
	&.release-waste {
		border: 1px solid var(--el-border-color);
		color: #666666;
		background: #f6f6f6;
	}

	&.release-publish {
		color: #4bae89;
		background: #e6fef0;
		border: 1px solid #4bae89;
	}

	&.release-examine {
		color: #e6a23c;
		background: #fdf6ec;
		border: 1px solid #f3d19e;
	}

	&.release-draft {
		color: #e25e59;
		background: #f9e7e7;
		border: 1px solid #e25e59;
	}
	// 版本发布状态
	// 草稿
	&.release-0 {
		color: #e25e59;
		background: #f9e7e7;
		border: 1px solid #e25e59;
		// color: #4bae89;
		// background: #e6fef0;
		// border: 1px solid #4bae89;
	}
	// 审核中
	&.release-1 {
		color: #e6a23c;
		background: #fdf6ec;
		border: 1px solid #f3d19e;
	}
	// 已发布
	&.release-2 {
		color: #4bae89;
		background: #e6fef0;
		border: 1px solid #4bae89;
	}
	// 已废弃
	&.release-3 {
		border: 1px solid var(--el-border-color);
		color: #666666;
		background: #f6f6f6;
	}
	// 驳回
	&.release-4 {
		color: #e25e59;
		background: #f9e7e7;
		border: 1px solid #e25e59;
	}
	// 工单状态
	// 待接单
	&.job-sts-0 {
		color: #e6a23c;
		background: #fdf6ec;
		border: 1px solid #f3d19e;
	}
	// 执行中
	&.job-sts-1 {
		color: #009dff;
		border-color: #009dff;
		background-color: rgba(0, 157, 255, 0.1);
	}
	// 已完成
	&.job-sts-2 {
		color: #4bae89;
		background: #e6fef0;
		border: 1px solid #4bae89;
	}
	// 已取消
	&.job-sts-3 {
		border: 1px solid var(--el-border-color);
		color: #666666;
		background: #f6f6f6;
	}
	// 接单中
	&.job-sts-4 {
		color: #e6a23c;
		background: #fdf6ec;
		border: 1px solid #f3d19e;
	}
	// 故障定性-处置结果
	// 未修复
	&.job-result-0 {
		color: #e25e59;
		background: #f9e7e7;
		border: 1px solid #e25e59;
	}
	// 已修复
	&.job-result-1 {
		color: #4bae89;
		background: #e6fef0;
		border: 1px solid #4bae89;
	}
	//计划执行
	&.plan_execution_sts_0 {
		color: #f59b22;
		border-color: #f59b22;
		background-color: rgba(245, 155, 34, 0.1);
	}
	&.plan_execution_sts_1 {
		color: #009dff;
		border-color: #009dff;
		background-color: rgba(0, 157, 255, 0.1);
	}
	&.plan_execution_sts_2 {
		color: #4bae89;
		background: #e6fef0;
		border: 1px solid #4bae89;
	}
}

.full-width-popper {
	max-width: unset !important;
}
.w350 {
	max-width: 350px;
}
.span-text-right {
	text-align: right;
}

//线路统一样式
.line-item-btn {
	margin: 0 auto;
	height: 22px;
	color: #fff;
	padding: 0 10px;
	border-radius: 3px;
	display: inline-flex;
	font-size: var(--pitaya-fs-12);
}

// 上传操作按钮公共样式
.file-item-actions {
	position: absolute;
	width: 100%;
	height: 100%;
	left: 0;
	top: 0;
	cursor: default;
	display: inline-flex;
	justify-content: center;
	align-items: center;
	color: #fff;
	opacity: 0;
	font-size: 16px;
	background-color: var(--el-overlay-color-lighter);
	transition: opacity var(--el-transition-duration);
	&:hover {
		opacity: 1;
	}
	span {
		display: inline-flex;
		cursor: pointer;

		&.action-download {
			margin-left: 10px;
		}
		&.action-delete {
			margin-left: 10px;
		}
	}
}
// 自定义MessageBox样式
.el-message-box__status {
	font-size: 48px !important;
}
.el-message-box__status.el-icon {
	top: 30px !important;
}

.el-message-box__status + .el-message-box__message {
	margin-left: 30px !important;
}

// 抽屉表格不限制固定高度
.drawer-scrollbar {
	height: calc(100% - 30px) !important;
	.pitaya-table {
		height: auto !important;
	}
}

.btn-groups {
	background-color: #fff;
	position: relative;
	z-index: 10;
}

// type="number"类型输入框样式
.numInput {
	.el-input__wrapper {
		padding: 1px 1px 1px 11px;
	}
}

.el-input-group__append {
	.el-input__wrapper {
		padding: 1px 11px;
	}
}

// 流程步骤条样式
.el-step {
	.el-step__main {
		.el-step__title.is-success {
			color: var(--el-success-color);
		}
	}
	.el-step__head.is-success {
		color: var(--el-success-color);
		border-color: var(--el-success-color);
	}
}

// 系统消息弹窗样式
.el-message-box__content {
	padding: 20px 10px;
	.el-message-box__container {
		display: flex;
		align-items: center;
	}
	.el-message-box__status {
		top: 0 !important;
		position: relative;
		transform: unset;
	}
	.el-message-box__message {
		padding: 0;
		margin-left: 10px !important;
		.message-content {
			font-weight: bold;
			font-size: 16px;
			line-height: 24px;
		}
	}
}

.ml10{
	margin-left: 10px !important;
}
.el-message{
	align-items:flex-start;
	.el-message__icon{
		margin-top:3px;
	}
	.el-message--info{
		--el-message-bg-color: rgb(223, 243, 255);
		--el-message-text-color: rgb(0, 82, 217);
	}
	.el-message__content{
		line-height: 22px;
		font-size: 14px;
	}
}
