/** 全局 CSS 变量，这种变量不仅可以在 CSS 和 SCSS 中使用，还可以导入到 JS 中使用 */
:root {
	/** 全局背景色 */
	--pitaya-body-bg-color: #f2f3f5;
	/** 模块背景色 */
	--pitaya-model-bg-color: #ffffff;
	/** 全局边框颜色 */
	--pitaya-border-color: #cccccc;
	/** 全局disabled颜色 */
	--pitaya-disabled-color: #999;
	/** 表格字体颜色 */
	--pitaya-table-font-color: #333333;
	/** Header 区域 = NavigationBar 组件 + TagsView 组件 */
	--pitaya-header-height: 50px;
	--pitaya-header-bg-color: #204a9c;
	/** NavigationBar 组件 */
	--pitaya-navigationbar-height: 50px;
	/** Sidebar 组件 */
	--pitaya-sidebar-width: 130px;
	--pitaya-sidebar-hide-width: 58px;
	--pitaya-sidebar-menu-item-height: 32px;
	--pitaya-sidebar-menu-tip-line-bg-color: #204a9c;
	--pitaya-sidebar-menu-bg-color: #ffffff;
    /** 修改右侧菜单选中色值 */
	--pitaya-sidebar-menu-hover-bg-color: #e3f2fe;   
	--pitaya-sidebar-menu-text-color: #666666;
	--pitaya-sidebar-menu-active-text-color: rgb(32, 74, 156, 1.0);
    /** radio/checkbox选中色值 */
	--pitaya-checked-bg-color: #0F5FAB;

	/** place 组件 */
	--pitaya-place-height: 32px;
	--pitaya-place-font-size: 12px;
	--pitaya-place-font-color: #666666;
	--pitaya-place-icon-color: #f59b22;
	/* 顶部badge颜色 */
	--pitaya-badge-warning: #f59b22;
	--pitaya-badge-error: #ff0000;
	/* 全局按钮背景色 */
	--pitaya-btn-background: rgb(32, 74, 156, 0.9);
	--pitaya-active-btn-background: rgb(32, 74, 156, 1.0);
	--pitaya-hover-btn-background: rgb(32, 74, 156, 0.7);
  	/* 全局字体大小 */
  	--pitaya-fs-12: 12px;
  	--pitaya-fs-14: 14px;
  	--pitaya-fs-16: 16px;
  	--pitaya-fs-18: 18px;
  	--pitaya-fs-20: 20px;
	--el-border-radius-base:3px;
	/** 修改表格hover色值 */
	--el-fill-color-light: #e3f2fe;
	/** */
	--el-disabled-bg-color: #f5f7fa;
	/** 提示颜色 */
	--el-success-color: #4BAE89;
	--el-warning-color: #F59B22;
	--el-error-color: #E25E59;
	--el-info-color: #009DFF;
	/** 系统（内外）边距 */
	--pitaya-margins-base: 10px;
}


.c-3{color: #333333!important;}
.f12{font-size: 12px!important;}
.f14{font-size: 14px!important;}
.mt10{margin-top: 10px!important}
.mt20{margin-top: 20px!important;}
.mt30{margin-top: 30px!important;}
.mb10{margin-bottom: 10px!important;}
.mb20{margin-bottom: 20px!important;}
.mb30{margin-bottom: 30px!important;}
.mb50{margin-bottom: 50px!important;}
.mr5{margin-right:  5px!important;}
.mr10{margin-right: 10px!important;}
.mr20{margin-right: 20px!important;}
.mr30{margin-right: 30px!important;}
.ml5{margin-left:  5px!important;}
.ml10{margin-left: 10px!important;}
.ml20{margin-left: 20px!important;}
.ml30{margin-left: 30px!important;}
.disabled{color:var(--pitaya-disabled-color)!important;}




