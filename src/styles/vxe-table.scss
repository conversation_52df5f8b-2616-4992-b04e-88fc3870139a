/** 自定义 Vxe Table 样式 */

.vxe-grid {
  // 表单
  &--form-wrapper {
    .vxe-form {
      padding: 10px 20px !important;
      margin-bottom: 20px !important;
    }
  }

  // 工具栏
  &--toolbar-wrapper {
    .vxe-toolbar {
      padding: 20px !important;
    }
  }

  // 分页
  &--pager-wrapper {
    .vxe-pager {
      height: 70px !important;
      padding: 0 20px !important;
      &--wrapper {
        // 参考 Bootstrap 的响应式设计 WIDTH = 768
        @media screen and (max-width: 768px) {
          .vxe-pager--total,
          .vxe-pager--sizes,
          .vxe-pager--jump,
          .vxe-pager--jump-prev,
          .vxe-pager--jump-next {
            display: none !important;
          }
        }
      }
    }
  }
}



/** 自定义 Vue steps样式 */

.ldri-tle-desc {
	line-height: 30px;
	font-size: var(--pitaya-fs-12);
	color:var(--pitaya-place-font-color);
	word-wrap: break-word;
}

.el-tag {
	&.level_A {
		color: #e25e59;
		border-color: #e25e59;
		background-color: #f9e7e7;
	}

	&.level_B {
		color: #4BAE89;
		border-color: #4BAE89;
		background-color: #E0FFEF;
	}

	&.level_C {
		color: #009dff;
		border-color: #009dff;
		background-color: #dff3ff;
	}

	&.level_D {
		color: #28c1c1;
		border-color: #28c1c1;
		background-color: #e5ffff;
	}

	&.type_0 {
		color: #e25e59;
		border-color: #e25e59;
		background-color: #f9e7e7;
	}

	&.type_1 {
		color: #4bae89;
		border-color: #4bae89;
		background-color: #e0ffef;
	}

	&.oper_0 {
		color: #4bae89;
		border-color: #4bae89;
		background-color: #e0ffef;
	}

	&.oper_1 {
		color: #956a8f;
		border-color: #956a8f;
		background-color: #fff6fd;
	}

	&.oper_2 {
		color: #e25e59;
		border-color: #e25e59;
		background-color: #f9e7e7;
	}

	&.oper_3 {
		color: #f59b22;
		border-color: #f59b22;
		background-color: #fcf3e6;
	}

	&.oper_4 {
		color: #009dff;
		border-color: #009dff;
		background-color: #dff3ff;
	}

	&.oper_5 {
		color: #0f5fab;
		border-color: #0f5fab;
		background-color: #dfeaf3;
	}

	&.state_0 {
		color: #4bae89;
		border-color: #4bae89;
		background-color: #e0ffef;
	}

	&.state_1 {
		color: #009dff;
		border-color: #009dff;
		background-color: #dff3ff;
	}

	&.state_2 {
		color: #e25e59;
		border-color: #e25e59;
		background-color: #f9e7e7;
	}

	&.state_3 {
		color: #f59b22;
		border-color: #f59b22;
		background-color: #fcf3e6;
	}
}

.img {
	width: 80%;
	height: 100px;
}
