# 项目概况
远程技术支持系统

# 更新日志
+ v1.4.7
  + 【优化】计划—需求清单支持多次编辑
  + 【优化】库存—盘点任务复核优化
  + 【优化】采购—采购订单：关闭原因优化
  + 【优化】全局【提交审核】时，做事务限制预防多人操作导致数据重复
  + 【企微端】审批模块增加合并计划审批
  + 【企微端】确认到货-采购订单明细顶部增加物资的筛选
  + 【优化】库存模块“冻结量”支持下钻查看冻结详情
  + 【前端优化】低值-盘点任务-文字调整
  + 【前端优化】编码申请页面优化
  + 【优化】计划模块：数据表结构及表字段优化
  + 【前端优化】库存—调拨申请的审批中，支持查看审批流
  + 【优化】废旧 交旧申请 预估回收重量字段优化
  + 【企微】库存盘点库存功能及 逻辑优化
  + 【前端优化】需求计划—搜索条件及体验优化
  + 【前端优化】物资管理系统全局幂等校验
  + 【优化】物资 、计划模块代码优化与规范
  + 【缺陷修复】编码申请：相似物资匹配不到数据
  + 【缺陷修复】编码申请：查看页面，相似物资查询结果没有滚动条，无法查看到全部数据
  + 【优化】物资模块  编码申请相似物资索引更新优化
  + 【前端优化】采购：发票管理，新建红字发票，选择蓝色发票后，对应按钮去掉
  + 【前端优化】废旧：返修申请的已审批增加上传附件功能
  + 【前端优化】全局：审批中、已审批、已驳回申请单据支持查看审批流
  + 【优化】废旧：交旧申请关联领料单数据查看优化
  + 【优化】采购：采购订单确认到货支持上传附件功能
  + 【优化】库存： 待处理的入库申请支持上传附件
  + 【优化】库存：接收入库【待入库】支持上传附件
  + 【优化】数据库需求清单明细冗余字段优化
  + 【优化】 计划模块：采购计划物资优化
  + 【优化】采购模块代码优化与规范
  + 【优化】库存模块部分代码优化与规范
  + 【缺陷修复】发票管理：添加开票物资带入了不应该带入的数据
  + 【缺陷修复】盘点报告：数据展示不全
  + 【优化】低值盘点任务查询只能盘点人或者监盘人可以看到
  + 【优化】库存盘点计划仓库明细列表增加“盘点结果”字段
  + 【优化】报废申请选择物资页面增加仓库筛选
  + 【优化】采购订单-增加“操作人”筛选条件（此操作人指的是采购订单创建生成人）
  + 【新增】采购订单确认时生成采购订单的二维码，生成规则为：CGDD-采购订单号
  + 【新增】采购订单-批量通知供应商功能
  + 【新增】返修台账-增加物资的在库时长
  + 【新增】盘点计划在需要复核时选择盘点范围
  + 【新增】盘点任务复核时，按照复核范围复核
  + 【优化】盘点任务初盘提交时，如果有复核则把初盘数量存入复核数量当做默认值
  + 【新增】领用出库时生成领用申请明细的二维码，生成规则为：LY-领用申请单号-物资编码
  + 【新增】低值领用物资支持标签打印
  + 【新增】质量检验-增加转派功能
  + 【新增】编码手册—增加导入更新预估采购单价功能
  + 【新增】入库申请-增加关闭功能
  + 【新增】采购项目-支持批量生成月度订货计划
  + 【优化】编码分类冻结、解冻规则优化
  + 【优化】交旧申请和退库申请关联领料单数据查看优化
  + 【优化】其他入库申请单物资明细表数据优化
  + 【优化】全局：针对表单表头排序优化
  + 【优化】已关闭的采购订单操作人取采购订单的关闭人
  + 【新增】需求清单支持复制功能

+ v1.4.6（2025-06-09）
  + 【优化】计划模块增加物资的库存数量
  + 【优化】计划模块的查看页面优化
  + 【优化】需求计划明细查询结果增加字段：库存数量
  + 【优化】需求清单草稿的只有创建人可以看到
  + 【新增】低值领用分配记录
  + 【优化】计划模块 需求计划添加物资优化
  + 【优化】计划模块 需求清单优化
  + 【优化】库存-调拨入库支持批量关闭
  + 【优化】计划模块的查看页面优化
  + 【新增】领料出库支持批量出库、批量关闭功能
  + 【优化】低值领用申请再次分配数据计算逻辑优化
  + 【优化】发票管理-采购订单和开票物资数据优化
  + 【优化】数据库脚本管理优化,引入flyway
  + 【优化】采购模块 订货计划添加提前订货计划优化
  + 【优化】低值模块 领用申请查看页面优化
  + 【优化】框架升级为3.11.9
  + 【优化】计划模块 需求清单 数据权限优化
  + 【缺陷修复】 编码申请：相似物资展示数据错误
  + 【优化】领料申请锁机制优化
  + 【优化】库存模块 库存查询 冻结量待优化
  + 【优化】低值-低值台账 冻结数量
  + 【优化】废旧版块 交旧申请优化
  
+ v1.4.5（2025-05-23）
  + 【新增】引入了基于 sql 拦截器的,分表机制
  + 【优化】返修申请添加物资时生成批次
  + 【优化】在库物资交旧无低值，修复在库物资交旧进入低值的数据
  + 【优化】交旧入库批次和完修入库接收入库单批次要有来源货位，用于后续入库时存入返修申请明细的已入库数量
  + 【优化】相似度依赖版本改为 1.1.1
  + 【前端优化】 编码申请：label、version、technicalParameter 过滤多个空格且都有值时更新相似物资
  + 【前端优化】 物资编码/货位码 UI 调整
  + 【前端优化】 交旧申请：物资明细增加清空仓库信息
  + 【前端优化】 质量检检：推送入库提示信息根据前置业务类型判断
  + 【增加】 代码格式化配置文化：prettier.config.cjs 文件，删除 prettier.config.js 文件
  + 【缺陷修复】 交旧申请：批量选择返修库前置较验条件
  + 【缺陷修复】 报废申请：物资明细删除时清空 errorGoodsIdList itemid

+ v1.4.4（2025-04-15）
  + 【优化】框架版本升级
  + 【优化】相似度依赖升级
  + 【优化】领料申请：审批通过时，在领料单生成领料码，生成规则为：LLSQ-领料单编号
  + 【优化】领用申请：审批通过时，在生成的领料单中生成领料码，生成规则为：LLSQ-领料单编号
  + 【优化】物资码：物资码生成规则修改为：WZ-物资编码
  + 【优化】货位码：货位码生成规则修改为：HW-仓库编号-区域编号-货位编号
  + 【优化】到货码：到货码生成规则修改为：DH-采购订单编号-采购订单物资明细的物资编码
  + 【优化】物资性质是 “A 类-维修件” 入库时不计入低值台账
  + 【优化】其他入库申请：备品备件入库、随车配件入库、实物物资入库不可选“A 类-维修件”物资
  + 【前端优化】全局 提交审核 增加页面 loading
+ v1.4.3（2025-04-10）
  + 【优化】需求计划、合并计划、采购计划：详情页可查看审批进度信息及流程图
  + 【优化】采购订单：采购员、操作人可对当前数据进行编辑
  + 【优化】发票管理：选择的采购订单过滤合同和过滤没有入库的
  + 【优化】接收入库：如果仓库就一个货位，货位自动填入
  + 【优化】接收入库：支持批量入库
  + 【优化】接收入库：入库仓库，改为不可修改
  + 【优化】领料申请：选择物资时可查询出低值物资，但不可使用
  + 【优化】库存盘点计划：全盘且不盘点库存为 0 的物资时，过滤仓库下没有物资的仓库
  + 【优化】库存盘点计划：增加字段：是否复核，如果是，复核人员为必填；如果否，复核人员选框去掉；默认为是
  + 【优化】库存盘点任务：改成分阶段初盘、复核、完成
  + 【优化】交旧申请：在库物资交旧可添加低值物资，但只能报废不能返修，且在审批通过后见低值台账数量
  + 【优化】领用申请：低值台账的已领用数量改成在出库时添加
  + 【优化】领用申请：领用分配必须在出库后分配
  + 【优化】领用申请：在分配后归还或交旧前，可重新分配
  + 【优化】归还申请：归还申请的入库仓库取自关联领用申请的出库仓库
+ v1.4.2（2025-04-03）
  + 【新增】定时任务：添加定时任务【完成到截止日期的年度计划】
  + 【优化】编码申请：使用年限改为使用期限，取值改成少于一年，一年以上
  + 【优化】编码手册：物资编码表操作状态枚举值修改
  + 【优化】编码手册：操作审批表结构优化
  + 【优化】编码申请：预估采购单价校验必须大于等于 1
  + 【优化】订货计划：确认时可移除不需要采购的物资
  + 【优化】需求计划：引入需求清单时，校验异常提示去掉
  + 【优化】全局：“质保有效期”更改为“质保期”
  + 【优化】退库入库：支持物资的批量入库
  + 【优化】订货计划：增加批量生成采购订单功能
  + 【优化】年度计划：年度计划填报日期控制优化
  + 【优化】调拨申请：库外移库根据是否跨成本中心，支持配置两支审批流
  + 【优化】领用申请：审批通过时生成的领料申请的“领料人”取低值领用申请的“领用人”
  + 【优化】其他入库申请：入库类型为备品备件及随车配件时，支持可关联专业及线路
  + 【优化】质量检验：增加推送质检时间并排序
  + 【优化】交旧入库、退库入库、接收入库仓库只有一个货位时自动填入货位
  + 【优化】采购订单：确认到货表单数据优化
  + 【优化】交旧申请、退库申请：选择领料单时，只能选择本部门及以下部门的领料单
  + 【优化】采购订单：采购订单-处理中增加数据权限-库管员权限
  + 【优化】插入事务时，存入采购订单号
+ v1.4.1（2025-03-27）
  + 【新增】成本中心管理
  + 【优化】采购订单：确认到货页面功能优化
  + 【优化】阈值管理调整为系统参数配置
  + 【优化】全局调整提示样式
  + 【优化】领用申请：低值领用分配，支持跨部门分配使用人
  + 【优化】低值盘点任务：盘点明细查询结果增加字段“部门”
  + 【优化】全局优化物资性质的标签
  + 【优化】交旧申请：物资明细表单增加批量选择待修仓库功能
  + 【优化】订货计划：已确认表单增加“更新时间”的排序
  + 【优化】入库申请、其他入库申请的数据权限
  + 【优化】交旧申请：低值、领料、返修交旧需要排除不可回收的物资
  + 【优化】需求清单：启用时物资数量为 0 取消校验
  + 【优化】库存查询：按仓库查询树，过滤掉“停用”“待启用”仓库，过滤掉“待启用”成本中心
+ v1.4.0（2025-03-21）
  + 【新增】采购订单：支持生成到货码
  + 【新增】库存查询：支持物资一类一码的生成和打印
  + 【新增】仓库管理：支持货位码的生成和打印
  + 【优化】库存模块筛选样式统一优化
  + 【优化】仓库管理：新建/编辑、查看仓库的前端页面优化
  + 【优化】库存查询：数据查询的筛选条件优化
  + 【优化】退货出库、返修出库、调拨出库、其他出库的数据权限
  + 【优化】企业微信端接口、界面及交互优化
+ v1.3.2（2025-03-14）
  + 【优化】退货出库、返修出库、调拨出库、其他出库的数据权限
  + 【优化】发票管理：创建/编辑发票页面的开票金额计算优化
  + 【优化】库存：业务管理中的添加附件功能
  + 【优化】审批：编码申请审批页面优化
  + 【优化】库存查询：用户可自主选择查看库存为 0 的物资
  + 【优化】交旧入库：入库单中的物资支持批量入库
  + 【优化】入库申请/其他入库/退库申请：推送质检优化调整
  + 【新增】物资分类属性自定义管理
  + 【优化】交旧申请：增加申请时间的筛选条件
  + 【优化】交旧申请：物资明细表单增加批量选择废旧仓库操作
  + 【优化】盘点计划：仓库明细表单增加盘点状态字段（盘点中/已完成/未开始）
  + 【优化】盘点计划：新建/编辑盘点计划的仓库明细增加批量移除
  + 【优化】接收入库：待入库增加推送入库时间
+ v1.3.1（2025-03-05）
  + 调拨入库库管员权限按照入库仓库查询
+ v1.3.0（2025-03-05）
  + 全局优化：针对表单表头的字段支持排序功能
  + 【优化】接收入库：待入库表单增加申请时间
  + 【优化】其他入库：物资明细增加批量移除
  + 【优化】仓库管理：待启用仓库支持移除功能
  + 【优化】仓库管理：库管员、审核员支持下钻查看
  + 【优化】仓库管理：库管员支持多选
  + 【新增】段区管理
  + 【优化】其他入库：物资明细增加批量移除功能
  + 【优化】编码申请：信息填写优化
  + 【优化】盘点计划：盘点计划批量移除仓库
  + 【优化】入库申请：【待处理】单据数据支持批量推送质检
  + 【优化】仓库管理：添加库管员和审核员时，单选框改为多选框
  + 【优化】退库申请：已完成页签下查看详情页增加质检员字段，取值为推送质检时记录的质检员
  + 【优化】其他入库：已完成页签下查看详情页增加质检员字段，取值为推送质检时记录的质检员
+ v1.2.15（2025-02-25）
  + 接收入库-待入库数据权限优化
  + 退库入库-待入库数据权限优化
  + 交旧入库-待入库数据权限优化
  + 调拨入库-待入库数据权限优化
  + 低值领用申请优化
  + 新增发票信息页面
  + 低值盘点任务：低值盘点物资明细字段名称优化和取值逻辑修改
  + 采购订单：添加供应商及合同编号的筛选
  + 采购订单：支持多个采购订单指定采购员操作
+ v1.2.14（2025-02-14）
  + 修改退库入库查询条件不生效的问题
  + 修改在库物资交旧可选物资查询条件的问题
  + 交旧申请：去掉备用物资的业务功能
+ v1.2.13（2025-01-20）
  + 采购入库质检推送全部是不合格时，更新采购订单的数量
  + 日结定时任务加 Transactional
+ v1.2.12（2024-01-16）
  + 发票管理：选择供应商 UI 交互调整；
  + 报表增加权限配置
+ v1.2.11（2024-01-16）
  + 报表 sortedBy 必填项
+ v1.2.10（2024-01-14）
  + 交旧入库货位详情 缺陷修复
+ v1.2.9（2024-01-13）
  + 领用分配页面 tab 切换 查询条件缺陷修复
  + 入库申请 关联采购订单页面 tab 切换查询条件 缺陷 修复
+ v1.2.8（2024-01-12）
  + 仓库类型增加实物仓库，并增加相关业务类型
+ v1.2.7（2024-01-04）
  + 修改主要材质和辅助材质为一个数据字典
  + 领料申请 领料用途 过滤低值易耗选项
+ v1.2.6（2024-01-02）
  + 更新采购订单时使用 getuserinfo 保存处理
+ v1.2.5（2024-01-02）
  + 物资编码手册采购信息新增修改接口添加
+ v1.2.4（2024-12-23）
  + 部分查询条件回调方法增加 tableRef.value?.resetCurrentPage()；
  + 部分 onCurrentPageChange 方法调用错误问题；
  + 提交审核时，异常异常明细标红时重置第一页；
  + 保存草稿/添加明细时 重置第一页；
  + 编码手册变更状态时 替换物资编码改成非必填；提交审核前 增加判断是否保存主表
  + 物资清单明细 增加物资状态、审批状态
+ v1.2.3（2024-12-20）
  + 申请物资编码时相似物资查询列表展示增加【物资分类编码】【物资分类名称】字段
  + 采购计划增加采购数量
  + 采购计划获取跨年问题的修复
  + 仓库选择器的整合
+ v1.2.2（2024-12-19）
  + 编码申请：
  + 增加拍摄指南页面
  + 编码申请时，去除物资名称不可重复的校验
  + 编码手册：
  + 编码手册增加相似物资查询页签
  + 增加所属公司、规格型号、技术参数、创建时间的查询条件
  + 年度计划：
  + 年度计划新增、编辑、查看时增加【填报截止窗口期】字段，为下拉选项，选择内容包括：需求计划、采购计划，在新增需求计划、采购计划时，需要根据所关联的年度计划中选择的【填报截止窗口期】和【计划填报截止日期】进行校验
  + 需求计划：
  + 年度需求计划物资明细增加需求清单号字段
  + 新建需求计划引入需求清单时，展示该公司启用状态的全部需求清单，去除专业的过滤条件，1. 需求清单页面去掉【清单状态】的筛选条件
  + 合并计划：
  + 合并计划添加需求计划时，去除过滤本部门数据的限制
  + 关联的需求计划增加需求清单号字段
  + 采购计划：
  + 关联的需求计划增加需求清单号字段
  + 采购项目：
  + 查看关联需求计划页面增加需求清单编号字段
  + 订货计划：
  + 查看关联需求计划页面增加需求清单编号字段
  + 仓库管理：
  + 区域树增加区域编码
  + 只有待启用状态 可编辑/删除；启用/盘点/冻结状态 不可编辑/删除，只能新建；停用状态 只有查看
  + 仓库编码优化，采用分公司编码-6 位数字或字母组成
  + 前端校验改为后端校验，启用和停用时不符合条件的给出提示
  + 库存查询：
  + 增加规格型号、物资性质、技术参数的查询条件
  + 接收入库：
  + 本次入库数量默认带出待入库数量，可进行编辑
  + 退库入库：
  + 本次入库数量默认带出待入库数量，可进行编辑
  + 交旧入库：
  + 本次入库数量默认带出待入库数量，可进行编辑
  + 采购订单：
  + 通知供应商页面的订单明细展示优化：采购总金额修改为【订货金额】取每行物资的订货金额总和
  + 临时计划移除和关闭功能调整
  + 退库申请：
  + 关联领料申请单时增加领料部门和申请部门字段和查询条件
  + 交旧申请：
  + 关联领料申请单时增加领料部门和申请部门字段和查询条件
  + 低值台账：
  + 增加调拨记录页面
  + 领用申请：
  + 已审批查看和分配的相关附件页面增加【上传附件】按钮
  + 废旧台账：
  + 已报废数量名称修改为已处置数量
  + 报废处置：
  + 去除报废询价和报废处置只能上传一个附件的限制
  + 数据权限梳理
  + 编码手册——接收人配置——登录人添加接收人，选择本公司人员
  + 退库申请——选择领料单号——不区分部门，可选择本公司数据
  + 交旧申请——领料单交旧——关联申请单——不区分部门，可选择本公司数据
  + 交旧申请——低值物资交旧——关联申请单——与归还同一接口
  + 归还申请——领用申请——谁领用 谁归还
  + 交旧申请——技术鉴定人——选择本公司人员
  + 低值盘点计划——添加部门——选择部门及该部门的下级部门
  + 低值盘点计划——领用申请——选择部门及该部门的下级部门
  + 低值盘点计划——盘点负责人——选择部门及该部门的下级部门
  + 低值盘点任务——指定盘点人员——本部门及下级部门
  + 低值盘点任务——监盘人员——非本部门及下级部门
  + 库存盘点计划——盘点人、复核人——本公司
  + 仓库管理——库管员、审核员——本公司
  + 领料申请——领料人——本公司
  + 编码申请——草稿箱、审批中、已审批数据仅展示自己数据
  + 供应商管理——集团通用，不区分公司
  + 维修公司管理——集团通用，不区分公司
  + 需求清单：用户可以操作本人创建的数据（启用，停用，编辑），其他用户只允许查看
  + 编码分类：用户可以操作本人创建的数据（启用，停用，编辑），其他用户只允许查看
  + 计划年度：用户可以操作本人创建的数据（启用，停用，编辑），其他用户只允许查看
  + 阈值管理：用户可以操作本人创建的数据（启用，停用，编辑），其他用户只允许查看
  + 费用类别：用户可以操作本人创建的数据（启用，停用，编辑），其他用户只允许查看
  + 仓库管理：用户可以操作本人创建的数据（启用，停用，编辑），其他用户只允许查看
  + 审批管理：
  + 审批通过后给申请人发送消息
  + 全局组件 全局接口优化
  + 解决多账号同时操作一条数据导致数据异常问题
  + 解决全局新增明细需要校验数据重复问题
  + 明细表排序优化，且全局物资明细增加字段：物资名称、物资编码、规格型号、物资性质
  + 全局增加提交审核时的二次确认弹窗提醒
+ v1.2.1（2024-12-09）
  + 费用类别：系统需要支持费用类别的新增、编辑、查看、启用、冻结和解冻
  + 审批管理：审批类消息同步发送 PC 端及企业微信端
  + 返修出库；关闭返修出库样式修改
  + 退货出库：关闭退货出库样式修改
  + 领料出库：关闭领料出库单样式修改
  + 采购订单：关闭采购订单样式修改
  + 采购项目：关闭采购项目样式修改
  + 出库管理：出库库存不足物资需标红显示
  + 合并计划：草稿状态或已驳回状态下，AB 需求计划可以选择到，只要合并计划关联的需求计划没有提交审批或审批通过，新建合并计划都可以选择
  + 全局组件：接口优化
+ v1.2.0（2024-10-31）
  + 审批管理：
  + 年度需求计划与临时计划审批流拆分
  + 编码手册：
  + 系统支持更新时间和创建时间进行正序和倒序的排序
  + 盘点计划：
  + 差异处理编辑和审批页面主表增加差异金额字段，差异金额=盘盈金额-盘亏金额
  + 抽样盘点改名为自动抽盘，专项盘点改名为手动抽盘，手动抽盘需要增加字段和计算逻辑
  + 其他入库：
  + 交旧完修入库--字段及计算逻辑调整
  + 领料申请：
  + 在已审批的领料申请查看页面增加已交旧数量和已退库数量字段
  + 交旧申请：
  + 领料单交旧--选择物资页面增加出库数量、已交旧数量、已退库数量、可交旧数量字段
  + 历史物资交旧改名为物资编码交旧，相关查询条件同步修改
  + 交旧申请时系统需要根据物资是否可回收字段增加过滤条件，对于不可回收的物资进行过滤，添加物资时选择不到
  + 主表需要增加技术鉴定人字段，子表需要增加损坏描述和报废原因字段
  + 交旧申请需要增加辅助材质字段
  + 新增返修物资交旧业务流程和逻辑
  + 新增在库物资交旧业务流程和逻辑
  + 交旧申请填写的预估回收重量为各分公司独立数据，不进行更新编码手册的预估回收重量
  + 其他出库：
  + 在库物资交旧审批通过后，系统自动生成其他出库记录，关联在库物资交旧已审批单据
  + 返修申请：
  + 已审批的返修申请查看页面字段调整
  + 废旧台账：
  + 废旧仓库中的物资超过一年，系统需要提醒用户及时进行废旧处置
  + 增加辅助材质字段
  + 已报废数量取值逻辑修改为，所有处置申请中，该物资已处置完成的数量的和
  + 报废申请：
  + 预估回收重量字段改为预估回收总重量，取交旧申请填写的预估回收重量 X 报废数量的总和
  + 报废处置：
  + 待评估改名为待询价，评估改名为报废询价
  + 全局数据：
  + 全局核对单价及金额数据存储
  + 技术优化：
  + 分类树懒加载优化
  + 页面 UI 展示优化
  + 文字输入框及校验优化
  + 数值输入框及校验优化
  + 全局幂等处理优化
  + 全局组件：
  + 段区需要支持各分公司独立配置
  + 数据权限：
  + 草稿箱也就是待提交、已驳回的数据只有自己能看到
  + 线路和专业只能选择本公司所关联的数据
+ v1.1.0（2024-10-31）
  + 编码分类：
  + 针对物资编码分类的启用、冻结、解冻、作废增加校验条件，符合条件的物资分类可进行相应操作
  + 编码申请：
  + 编码申请时进行根据物资名称、规格型号、技术参数及图片进行相似物资查询，并将已达相似度阈值设定的查询结果进行展示和提醒
  + 在编码申请时增加辅助材质字段，在编码手册及后续模块进行展示
  + 废旧物资分类和废旧物资材质进行匹配和校验
  + 系统支持根据相似物资阈值管理进行智能分类推荐
  + 编码手册：
  + 针对物资编码的冻结、解冻、作废、更新物资性质增加校验条件及审批流，并增加审批状态
  + 全局业务申请对物资编码的冻结及作废状态进行校验，不符合条件的物资编码不可使用并给出提示
  + 主表需要增加“申请人、创建时间、所属公司”字段，操作数据权限需要根据登录用户进行区分
  + 进行物资编码查看时，展示最新采购信息的供应商信息
  + 阈值管理：
  + 增加阈值管理配置及启用和停用功能，系统可根据配置进行相似物资查询展示
  + 年度计划：
  + 年度计划启用规则修改，去除一年创建个年度计划的限制，修改为一年只能有一个进行中的年度计划
  + 年度计划日期选择前端页面调整
  + 年度计划未启用时，在需求计划不进行查询
  + 年度计划未启用时，在需求计划不进行查询
  + 采购计划：
  + 审批页面，需求数量与采购数量不一致的物资需要标红显示
  + 新建、编辑页面增加在途数量字段，取值逻辑同平衡利库算法中的在途数量一致
  + 采购计划明细表需要增加需求计划名称、需求计划号、需求计划 id、需求计划明细 ID 字段
  + 采购项目：
  + 新增采购项目的关闭功能
  + 仓库管理：
  + 针对仓库的启用、冻结、解冻、停用进项校验，不符合条件的不允许操作并给出提示
  + 货位编码拼接规则修改
  + 库存查询：
  + 库存查询全部页签下增加查询条件，默认展示数据量优化
  + 库存查询数据逻辑调整，按照公司、批次、货位维度分别展示和调整
  + 盘点计划：
  + 差异处理主表需要增加盘盈金额、盘亏金额，子表需要增加差异金额字段
  + 对于全盘和抽盘，盘点库存中的物资（不包括库存为 0 的物资），但对于动态盘（有出入库业务的物资的盘点）需要盘点 0 库存物资。需要用户主动选择是否盘点库存为 0 的物资
  + 接收入库：
  + 入库区域和货位细节优化
  + 退库入库：
  + 入库区域和货位细节优化
  + 调拨入库：
  + 入库区域和货位细节优化
  + 交旧入库：
  + 入库区域和货位细节优化
  + 全局前端：
  + 全局前端页面优化，调整相应展示格式及页面提醒
  + 全局校验：
  + 全局校验优化，对于相应问题明细进行标红展示
  + 移动端：
  + 对于仓库货位支持输入货位码&扫码查询
  + 对于物资支持输入物资编码&扫描二维码查询
  + 支持订单到货确认
  + 支持入库申请、退库申请、其他入库分配质检员并推送质检
  + 支持质量检验，一键质检合格&部分质检
  + 支持接收入库
  + 支持交旧入库
  + 支持退库入库
  + 支持库存盘点
  + 支持领料出库（一键出库&部分出库）和关闭
  + 支持退货出库和关闭
  + 支持返修出库和关闭
  + 支持领料申请、调拨申请、其他入库、退库申请、采购计划、合并计划的快捷审批
+ v1.0.0（2024-06-30）
  + 物资模块功能开发
  + 计划模块功能开发
  + 采购模块功能开发
  + 库存模块功能开发
  + 废旧模块功能开发
  + 低值模块功能开发
  + 报表功能模块开发

# 目录结构

```
根目录
├─public                      			# 静态资源目录
├─src                          			# 源代码目录
│  ├─app
│  │  ├─baseline               			# 物资基线产品源代码根目录
│  │  │  ├─api                 			# 访问服务端接口API目录
│  │  │  │  ├─material         			# 物资模块API目录
│  │  │  │  ├─purchase         			# 采购模块API目录
│  │  │  │  └─plan             			# 计划模块API目录
│  │  │  ├─assets              			# 样式 字体等静态资源
│  │  │  │  └─css              			# 基线产品样式文件目录
│  │  │  ├─utils               			# 基线产品全局公用方法
│  │  │  └─views               			# 基线产品所有页面
│  │  │      ├─components      			# 基线产品给公共组件目录
│  │  │      ├─material        			# 物资模块源代码目录
│  │  │      │  │─components   			# 物资模块子组件源代码目录
│  │  │      │  │    ├─apply   			# 物资模块-编码申请子组件源代码目录
│  │  │      │  │    └─manual  			# 物资模块-编码手册子组件源代码目录
│  │  │      │  ├─matApply.vue			# 物资模块-编码申请入口页
│  │  │      │  ├─matManual.vue 		# 物资模块-编码手册入口页
│  │  │      │  └─matType.vue 			# 物资模块-编码分类入口页
│  │  │      ├─plan            			# 计划模块源代码目录
│  │  │      │  │─components   			# 计划模块子组件源代码目录
│  │  │      │  │    ├─merge   			# 计划模块-合并计划子组件源代码目录
│  │  │      │  │    ├─need    			# 计划模块-需求清单子组件源代码目录
│  │  │      │  │    ├─plan    			# 计划模块-需求计划子组件源代码目录
│  │  │      │  │    └─purchase			# 计划模块-采购计划子组件源代码目录
│  │  │      │  ├─mergePlan.vue			# 计划模块-合并计划入口页
│  │  │      │  ├─needList.vue			# 计划模块-需求清单入口页
│  │  │      │  ├─needPlan.vue			# 计划模块-需求计划入口页
│  │  │      │  └─purchasePlan.vue	# 计划模块-采购计划入口页
│  │  │      ├─purchase            	# 采购模块源代码目录
│  │  │      │  │─contract					# 采购模块-采购合同子组件源代码目录
│  │  │      │  │─distribution			# 采购模块-采购分包子组件源代码目录
│  │  │      │  │─invoice						# 采购模块-发票信息子组件源代码目录
│  │  │      │  │─order   					# 采购模块-采购订单子组件源代码目录
│  │  │      │  │─plan   						# 采购模块-订货计划确认模块子组件源代码目录
│  │  │      │  │─planEdit   				# 采购模块-订货计划管理模块子组件源代码目录
│  │  │      │  │─project   				# 采购模块-采购项目模块子组件源代码目录
│  │  │      │  │─supplier   				# 采购模块-供应商模块子组件源代码目录
│  │  │      │  ├─contract.vue			# 采购模块-采购合同入口页
│  │  │      │  ├─order.vue					# 采购模块-采购订单入口页
│  │  │      │  ├─distribution.vue	# 采购模块-采购分包入口页
│  │  │      │  ├─invoice.vue				# 采购模块-发票信息入口页
│  │  │      │  ├─plan.vue					# 采购模块-订货计划确认入口页
│  │  │      │  │─planEdit.vue			# 采购模块-订货计划管理入口页
│  │  │      │  ├─project.vue				# 采购模块-采购项目入口页
│  │  │      │  └─supplier.vue			# 采购模块-供应商入口页
│  │  │      └─store           			# 库存模块源代码目录
│  │  │          │─components  			# 库存模块子组件源代码目录
│  │  │          └─components  			# 库存模块-仓库管理入口页
│  │  ├─platform               			# 基础框架系统和地铁基本功能管理系统
│  │  └─project	               			# 物资项目系统目录
│  └─……	                       			# 基础框架目录
└─……                           			# 基础框架目录

```

# 技术架构

- 开发环境
  - 编译工具：Node.js 18.19.1
- 技术栈（企业微信）
  - 基础框架：Vue 3.3.4
  - UI 框架：Element-plus 2.3.7

# 功能模块

```
远程技术支持系统
├─物资
│   ├──物资编码
│   │     ├──编码分类
│   │     ├──编码申请
│   │     ├──编码手册
│   │     └──阈值管理
│─计划
│    ├──需求清单
│    ├──年度计划
│    ├──需求计划
│    ├──合并计划
│    └──采购计划
│─采购
│    ├──采购分包
│    ├──采购项目
│    ├──订货计划
│    ├──采购订单
│    ├──采购合同
│    ├──厂商管理
│    └──发票管理
│─库存
│    ├──仓库管理
│    ├──库存查询
│    │──业务管理
│    │   ├──入库申请
│    │   ├──其它入库
│    │   ├──领料申请
│    │   ├──退库申请
│    │   └──退货申请
│    │──入库管理
│    │   ├──质量检验
│    │   ├──接收入库
│    │   ├──交旧入库
│    │   └──退库入库
│    │──出库管理
│    │   ├──领料出库
│    │   ├──退货出库
│    │   └──返修出库
│    │──调拨管理
│    │   ├──调拨申请
│    │   ├──调拨出库
│    │   └──调拨入库
│    └──盘点管理
│        ├──盘点计划
│        ├──盘点任务
│        ├──盘亏出库
│        └──盘盈入库
│─废旧
│    │──交旧申请
│    │──返修台账
│    │──返修申请
│    │──厂商管理
│    │──废旧台账
│    │──废旧申请
│    └──废旧处置
│─低值
│    │──低值台账
│    │──领用申请
│    │──归还申请
│    └──低值盘点
│        ├──盘点计划
│        ├──盘点任务
│        └──盘点报告
│─报表
│    ├──综合看板
```

# 部署说明

- 部署环境（推荐）
  - 操作系统：AnolisOS 8.8 GA
  - 服务器配置：8 核 CPU / 16GB 内存 / 100GB 以上可用磁盘空间
- 部署步骤

  - 环境配置文件说明（以开发环境.env.development 文件为例）

    ```
    VITE_BASE_API = http://127.0.0.1:9700       #开发环境接口地址
    ```

    说明：只需修改上述配置文件中的 VITE_BASE_API 配置项即可。

  - 应用的开发与构建（基于项目根目录）
    启动本地开发环境
    ```
    npm install     #安装依赖模块
    npm run dev     #启动本地开发调试
    ```
    打包编译测试环境
    ```
    npm install         #安装依赖模块
    npm run build:test  #测试环境项目构建
    ```
    打包编译正式环境
    ```
    npm install         #安装依赖模块
    npm run build:prod  #正式环境项目构建
    ```
  - 客户端部署
    - 测试环境访问地址
    - 正式环境访问地址 待定

# 注意事项

1. 建议不要改变环境变量 NODE_ENV 的配置参数；
2. 本地开发时 devServer 默认为 9700 端口。
