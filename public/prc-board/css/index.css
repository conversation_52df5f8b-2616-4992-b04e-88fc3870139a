/* 定义CSS变量 */
:root {
	--font-size: clamp(16px, 1vw, 18px);
	--primary-color: #00a1fc;
	--bg-color: #01254a;
	--panel-bg: #01325c;
	--completed-color: #4bae89;
	--active-color: #db8e26;
	--border-radius: 4px;
	--spacing-sm: 0.625rem;
	--spacing-md: clamp(1rem, 1.5vw, 1.5rem);
	--spacing-lg: 1.25rem;
}

/* 基础样式重置 */
* {
    word-break: break-word;
    overflow-wrap: break-word;
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

* {
	word-break: break-word;
	overflow-wrap: break-word;
	box-sizing: border-box;
	margin: 0;
	padding: 0;
}

body {
	font-family: system-ui, -apple-system, "Microsoft YaHei", sans-serif;
	background: var(--bg-color);
	color: #fff;
	min-height: 100vh;
	line-height: 1.5;
	font-size: var(--font-size);
}

.app {
	display: grid;
	grid-template-columns: minmax(18vw, 380px) 1fr minmax(18vw, 380px);
	padding: var(--spacing-md) var(--spacing-md) 0;
	width: min(100vw, 2560px);
	margin: 0 auto;
	box-sizing: border-box;
}

@media (min-width: 2560px) {
	:root { --spacing-md: 1.75rem }
	.app { grid-template-columns: 18% 1fr 18% }
}

/* 面板通用样式 */
.panel {
    background: var(--panel-bg);
    border: 2px solid var(--primary-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-md);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    min-width: 0;
	  height: calc(100vh - 10rem);
}

.panel-title {
    font-size: 1.125rem;
    font-weight: bold;
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--primary-color);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 头部区域 */

.header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start; /* 修改为顶部对齐 */
    padding: 2rem 2rem 0.625rem; /* 减少顶部内边距 */
    color: #fff;
    font: bold 1.5rem/1 sans-serif; /* 调整行高 */
    height: 7rem;
    background: url('../icon/BANNER.png') center/cover no-repeat;
    position: relative;
}

.header::before {
    content: '';
    position: absolute;
    inset: 0;
    background-color: rgba(0,0,0,0.3);
    z-index: -1;
}

[class^="header-"] {
    flex: 1 1 0;
    min-width: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding: 0.2rem 0.5rem 0; /* 调整文字垂直间距 */
    margin-top: -0.2rem; /* 上移文字 */
}

.header-center {
    font-size: 2.375rem;
    text-align: center;
}

.header-right {
    text-align: right;
}

/* 左侧流程样式 */
.process-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    gap: var(--spacing-sm);
    overflow: hidden;
}

.process-step-box {
    display: flex;
    align-items: center;
    flex-direction: column;
    position: relative;
    margin-bottom: var(--spacing-sm);
}

.icon-shuangsanjiao {
    font-size: 3.25rem;
    position: absolute;
    bottom: 3.2rem;
    color: var(--primary-color);
}

.process-step-box:last-child .icon-shuangsanjiao {
    display: none;
}

.process-step {
    position: relative;
    background: var(--primary-color);
    border-radius: 8px;
    padding: var(--spacing-sm) var(--spacing-md);
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 1.875rem 1.25rem 1.875rem;
    width: 80%;
    min-height: 3.75rem;
    text-align: center;
    flex-wrap: wrap;
    word-break: break-word;
}

.process-step.completed {
    background: var(--completed-color);
}

.process-step.active {
    background: var(--active-color);
}

.process-name {
    font-weight: bold;
    font-size: 1.5rem;
}

.check-icon {
    color: white;
    font-size: 1.25rem;
    font-weight: bold;
    margin-left: var(--spacing-sm);
}

/* 中间任务面板 */
.task-panel {
    height: 100%;
    overflow-y: auto;
    padding-right: 0.3125rem;
    flex: 1;
}

.process-title {
    color: #049dff;
    font-weight: bold;
    font-size: 1.25rem;
    margin: 0 0 var(--spacing-sm) 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
}

.process-cont {
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border: 1px dashed #33a7fd;
    border-top: none !important;
}

.task-panel-box .process-cont.completed {
    border-color: var(--completed-color);
}

.task-panel-box .process-cont.active {
    border-color: var(--active-color);
}

.process-time {
    font-size: 1.25rem;
    color: #fff;
    font-weight: bold;
    white-space: nowrap;
    display: flex;
    align-items: center;
}

.icon-time {
    margin-right: 0.3rem;
}

.task-item-old {
	background: #0f5fab;
	color: white;
	font-weight: bold;
	font-size: 1.125rem;
	padding: 0.375rem var(--spacing-md);
	border-radius: var(--border-radius);
	margin: var(--spacing-sm) 0;
	display: flex;
	justify-content: space-between;
	align-items: center;
	flex-wrap: wrap;
}


.task-box {
	background: #0f5fab;
	border-radius: var(--border-radius);
	margin: var(--spacing-sm) 0;
	padding: 0.375rem var(--spacing-md);
	display: flex;
}

.task-box .task-item {
	color: white;
	font-weight: bold;
	font-size: 1.125rem;
	padding-right: 0;
	display: flex;
	justify-content: flex-start; /* 改为左对齐 */
	align-items: center;
	flex-wrap: wrap;
	width: 90%;
}

.task-box .task-view {
	margin: auto; /* 自动左外边距实现右对齐 */
	font-size: 14px;
	padding: 4px 10px;
	height: 2em;
	background: #fff;
	color: var(--primary-color);
	border-radius: var(--border-radius);
	display: flex;
	align-items: center;
	cursor: pointer;
	white-space: nowrap;
}

.task-box .task-time {
	font-size: 1.125rem;
	color: white;
	font-weight: bold;
	margin-left: auto; /* 自动左外边距实现右对齐 */
	padding-left: 10px;
	display: flex;
	align-items: center;
	white-space: nowrap;
}

.task-cont {
    display: flex;
    flex-direction: row;
    align-items: start;
    padding: 0 20px;
}

.step-item-box {
    display: flex;
    flex-direction: column;
    margin-left: .5rem;
    flex: 1;
}

.operator {
    color: white;
    font-size: 1.2rem;
    margin: 0.3125rem 0;
    display: flex;
    align-items: center;
    width: 200px;
    /*background-color: #4CAF50;*/
    height: 2.5rem;
    padding: 0 0.5rem;
    justify-content: space-between;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.operator-icon {
    margin-right: 0.5rem;
    display: flex;
    align-items: center;
}

.role-name {
    flex-grow: 1;
}

.arrow-icon {
    margin-left: auto;
    color: red;
    display: flex;
    align-items: center;
    /*background-color: #7d4150;*/
    padding: 0 0.3rem;
}

.step-item {
    color: #fff;
    font-size: 1.2rem;
    line-height: 1.6;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    width: 100%;
    margin: 0.5rem 0;
}

.action-buttons {
    margin-left: auto; /* 关键属性，将元素推到右侧 */
    min-width: 160px ;
    height: 1.6rem;
    /*border: #4CAF50 1px solid;*/
}
.step-btn {
    margin-left: 20px;
    cursor: pointer;
    white-space: nowrap; /* 关键属性，禁止文字换行 */
}

.step-item-title {
    color: #fff;
}

.step-item-title-finsh {
	color: #4bae7d;
	/*white-space: nowrap; !* 关键属性，禁止文字换行 *!*/
}

.step-item-title-unfinsh {
	color: #FFB800;
	/*white-space: nowrap; !* 关键属性，禁止文字换行 *!*/
}

.step-item-staus {
    padding: 0.125rem var(--spacing-sm);
    color: #fff;
    margin-left: 0.25rem;
    font-size: 0.875rem;
    border-radius: var(--border-radius);
	  white-space: nowrap; /* 关键属性，禁止文字换行 */
}

.step-item-staus-finsh {
    color: #fff;
    background-color: var(--completed-color);
}

.step-item-staus-unfinsh {
    color: #fff;
    background-color: #FFB800;
}

/* 右侧面板样式 */
.personnel-status-online {
    height: 100%;
    overflow-y: auto;
    padding: 0 10px;
    color: #4bae7d;
}

/* 右侧面板样式 */
.personnel-status-outline {
    height: 100%;
    overflow-y: auto;
    padding: 0 10px;
    color: #FFB800;
}

.personnel-online {
    color: #fff;
    margin-left: 1rem;
    background-color: #199c26;
    white-space: nowrap;
    text-align: center;
    border-radius: 0.2rem;
}

.personnel-outline {
    color: #fff;
    background-color: #FFB800;
    margin-left: 1rem;
    white-space: nowrap;
    text-align: center;
    border-radius: 0.2rem;
    cursor: pointer;
}

.personnel-item {
    display: flex;
    align-items: center;
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid rgba(0, 161, 252, 0.3);
    flex-wrap: wrap;
}

.avatar {
    width: 2.25rem;
    height: 2.25rem;
    border-radius: 50%;
    margin-right: var(--spacing-sm);
    background: rgba(0, 161, 252, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: var(--primary-color);
    flex-shrink: 0;
}

.status-dot {
    width: 0.625rem;
    height: 0.625rem;
    border-radius: 50%;
    margin-right: 0.3125rem;
    flex-shrink: 0;
}

.status-available {
    background: #4CAF50;
}

.status-busy {
    background: #FFB800;
}

.status-offline {
    background: #ccc;
}

.personnel-item>div:nth-child(2) {
    flex: 1;
    min-width: 0;
}

.personnel-item div:nth-child(2) div:first-child {
    font-size: 1.5rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.personnel-item div:nth-child(2) div:last-child {
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.7);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.personnel-item>div:nth-child(3) {
    display: flex;
    align-items: center;
    white-space: nowrap;
}

/* 人员定位 */
.personnel-container {
    margin-top: var(--spacing-lg);
    font-size: 1.125rem;
}

.personnel-group {
    display: flex;
    flex-direction: row;
    color: #fff;
    border-bottom: 1px solid #33a7fd;
    padding-bottom: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
    align-items: center;
    justify-content: space-between;
}

.personnel-tab {
    cursor: pointer;
}
.category-title {
    padding: 0.25rem var(--spacing-sm);
    white-space: nowrap;
    display: flex;
    align-items: center;
}
.category {
    padding: 0.25rem var(--spacing-sm);
    white-space: nowrap;
    display: flex;
    align-items: center;
}

.category:hover {
    color: #33a7fd;
    background: #042948;
}

.isActive {
    color: #33a7fd;
    background: #042948;
}

.list-personnel-item {
    display: flex;
    align-items: center;
    border-bottom: 1px solid #33a7fd;
    padding-bottom: var(--spacing-sm);
    margin-top: 0.375rem;
    flex-wrap: wrap;
    justify-content: space-between;
}

.list-personnel-item:last-child {
    border: none;
}

.personnel-title {
    white-space: nowrap;
    display: flex;
    align-items: center;
}

/* 右侧面板组件 */
.panel-time-box {
    background: var(--panel-bg);
    padding: var(--spacing-sm) var(--spacing-md) var(--spacing-md);
    border: 1px solid var(--primary-color);
    border-radius: var(--border-radius);
}

.panel-time-title {
    font-size: 1.25rem;
    border-bottom: 1px solid #0290ff;
    color: #0290ff;
    padding-bottom: var(--spacing-sm);
    font-weight: bolder;
    margin: 0;
}

.panel-time {
    color: #fff;
    font-size: 3.75rem;
    font-weight: bolder;
    text-align: center;
    margin-top: var(--spacing-sm);
}

.panel-radius {
    margin-top: var(--spacing-lg);
    background: var(--panel-bg);
    padding: 0.3125rem var(--spacing-md) var(--spacing-md);
    border: 1px solid var(--primary-color);
    border-radius: var(--border-radius);
    display: flex;
    height: fit-content;
    justify-content: center;
    color: var(--primary-color);
    align-items: center;
}

.radius-box {
    flex: 1;
    text-align: center;
    margin: var(--spacing-sm) 0.3125rem 0;
    font-size: 1.125rem;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.radius-box .el-progress {
    margin-top: var(--spacing-sm);
    width: 100%;
}

.panel-btn-box {
    display: flex;
    margin-top: var(--spacing-lg);
    flex-wrap: wrap;
    gap: 0.5rem;
}

.panel-btn-box button {
    flex: 1;
    min-width: 7.5rem;
    padding: 1.875rem 1.25rem;
    background: #1270d6;
    color: #fff;
    border: none;
    border-radius: var(--border-radius);
    font-size: 1.25rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

.panel-perpel {
    margin-top: var(--spacing-lg);
    background: var(--panel-bg);
    padding: 0.3125rem var(--spacing-md) var(--spacing-md);
    border: 1px solid var(--primary-color);
    border-radius: var(--border-radius);
    overflow-y: scroll;
    max-height: 13.125rem;
}

.perpel-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid var(--primary-color);
    padding-bottom: var(--spacing-sm);
    color: var(--primary-color);
    font-weight: bold;
    font-size: 1.25rem;
}

.perple-cont-box {
    margin: var(--spacing-sm);
    padding: 0.5rem;
    background: #7d4150;
    color: #fff;
    font-size: 1rem;
    display: flex;
    border: 1px solid var(--primary-color);
    align-items: center;
}

.panel-task {
    margin-top: var(--spacing-lg);
    background: var(--panel-bg);
    padding: 0.3125rem var(--spacing-md) var(--spacing-md);
    border: 1px solid var(--primary-color);
    border-radius: var(--border-radius);
    color: var(--primary-color);
    height: -webkit-fill-available;
    flex: 1;
    overflow-y: scroll;
}

.task-title {
    width: 100%;
    border-bottom: 1px solid var(--primary-color);
    padding-bottom: var(--spacing-sm);
    color: var(--primary-color);
    font-weight: bold;
    font-size: 1.25rem;
    display: flex;
    align-items: center;
}

.panel-task-cont {
    font-size: 1rem;
    padding: var(--spacing-sm);
    color: #fff;
}

.task-cont-text {
    border-bottom: 1px dashed var(--primary-color);
    padding-bottom: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
    font-size: 1.2rem;
    line-height: 1.6;
}

/* 文本溢出处理 */
.multiline-div {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
    display: block;
}

/* 动画效果 */
.track {
    position: relative;
    width: 100%;
    height: 6px;
    background-color: #1270d6;
    border-radius: 2px;
    margin: 0 auto;
    overflow: visible;
    margin-top: 10px;
}

.slider {
    position: absolute;
    width: 12px;
    height: 6px;
    top: 50%;
    transform: translateY(-50%);
    animation-timing-function: ease-in-out;
    animation-iteration-count: infinite;
}

.slider.left {
    background-color: #fff;
    left: 100%;
    margin-left: -5px;
    animation: moveLeft 5.5s infinite alternate;
}

.slider.right {
    background-color: #fff;
    right: 100%;
    margin-right: -5px;
    animation: moveRight 5.5s infinite alternate;
}

@keyframes moveLeft {
    0% { transform: translateY(-50%) translateX(0); }
    100% { transform: translateY(-50%) translateX(-145px); }
}

@keyframes moveRight {
    0% { transform: translateY(-50%) translateX(0); }
    100% { transform: translateY(-50%) translateX(145px); }
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-track {
    background: rgba(0, 161, 252, 0.1);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb {
    background: rgba(0, 161, 252, 0.3);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 161, 252, 0.5);
}

img.user-icon {
    margin-right: 0.3rem;
}

/*loading*/
.loading-wrapper {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translateX(-50%);
    z-index: 10000;
    pointer-events: none;
}

.loading-spinner {
    display: flex;
    align-items: center;
    color: #fff;
    background: rgba(1,37,74,0.3);
    padding: 8px 16px;
    border-radius: 4px;
    backdrop-filter: blur(5px);
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
}

.spinner {
    border: 3px solid #fff;
    border-top: 3px solid #01254a;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    animation: spin 1s linear infinite;
    margin-right: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.fade-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(1,37,74,0.1);
    opacity: 0;
    transition: opacity 0.3s;
}

.fade-overlay.active {
    opacity: 1;
}

/*更换流程 弹窗*/
.main-list-table {
    width:100%;
    height: calc(100vh - 250px);
    overflow-y: auto;
    /*background: #ffff00;*/
}
.main-list-table .hidden-header {
    display: none;
}
.main-list-table .el-table__header th {
    padding: 0;
}
.main-list-table .el-table__row {
    cursor: pointer;
    /*border: 30px solid rgba(0, 0, 0, 0.99);*/
    margin: 5px 0;
}
.main-list-table .el-table__row td{
    border: none;
    background: none;
}
.main-list-table .el-table__row:hover {
    /*background-color: #f5f700;*/
}
.main-list-table .current-row {
    /*background-color: #409eff !important;*/
    position: relative;
}
.main-list-table .current-row::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}
.footer-actions {
    position: sticky;
    /*bottom: 0;*/
    /*background: #ff0000;*/
    /*box-shadow: 0 -1px 4px rgba(0,0,0,0.1);*/
    display: flex;
    justify-content: flex-end;
    /*margin-top: 20px;*/
    padding: 5px 20px 5px 20px;
	border-top: 1px solid #e4e4e4;
}

.document-list {
	padding-bottom: 16px;
	height: 100%;

	.document-item {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 8px 16px;
		background: #f8f9fe;
		border-radius: 4px;
		margin-bottom: 12px;
		cursor: pointer;
		transition: all 0.3s;
		border: 1px solid transparent;
		&:hover {
			background: #eef1fd;
		}

		&.is-selected {
			background: #e6f1fe;
			border: 1px solid #409eff;
		}

		.document-icon {
			margin-right: 12px;
			color: #409eff;
			font-size: 32px;
		}

		.document-info {
			flex: 1;

			.document-title {
				font-size: 14px;
				color: #303133;
				margin-bottom: 8px;
				line-height: 1.4;
			}

			.document-meta {
				font-size: 12px;
				color: #909399;

				.document-id {
					margin-right: 16px;
				}
			}
		}
	}
}
.no-data {
	text-align: center;
	padding: 32px;
	color: #909399;
	font-size: 14px;
}

.el-drawer__body{
	padding: 8px 20px;
}
.el-drawer__header{
	border-bottom: 1px solid #e4e4e4;
	padding: 10px 0;
	margin: 0 20px;
}
.title-icon{
	width: 18px;
	height: 18px;
	display: flex;
	align-items: center;
	justify-content: center;
	background: #0a4e9a;
	border-radius: 50%;
}
.el-form--inline.el-form--label-top .el-form-item {
	margin-right: 0px !important;
}
.gis-preview {
	width: 100%;
	height: 100%;
	position: relative;

	.legend-panel {
		position: absolute;
		top: 10px;
		left: 10px;
		z-index: 100;

		box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
		min-width: 320px;
		max-width: 500px;

		.section-header {
			margin-bottom: 4px;
			padding-bottom: 4px;
			border-bottom: 1px solid #3468b7;
		}
		.section-item{
			font-size: 12px;
			color: #fff;
		}

		.fault-info-section {
			margin-bottom: 20px;

			.fault-info-grid {
				display: grid;
				grid-template-columns: 1fr 1fr;
				gap: 8px 16px;

				.info-item {
					display: flex;
					align-items: center;
					min-height: 24px;

					.info-label {
						font-size: 12px;
						color: #666;
						white-space: nowrap;
						margin-right: 4px;
						min-width: 60px;
					}

					.info-value {
						font-size: 12px;
						color: #333;
						font-weight: 500;
						flex: 1;
						word-break: break-all;
					}

					&:nth-child(3) {
						grid-column: 1 / -1; /* 故障位置占满整行 */
					}
				}
			}
		}

		.layer-control-section {
			background: #0a4e9a;
			padding: 4px;
			.layer-types-horizontal {
				display: flex;
				flex-wrap: wrap;
				gap: 12px;

				.layer-type-item {
					.checkbox-wrapper {
						display: flex;
						align-items: center;
						cursor: pointer;
						user-select: none;
						position: relative;
						white-space: nowrap;

						input[type="checkbox"] {
							position: absolute;
							opacity: 0;
							cursor: pointer;
							height: 0;
							width: 0;
						}

						.checkmark {
							height: 16px;
							width: 16px;
							background-color: #fff;
							border: 2px solid #ddd;
							border-radius: 3px;
							margin-right: 6px;
							position: relative;
							transition: all 0.2s ease;
							flex-shrink: 0;

							&::after {
								content: "";
								position: absolute;
								display: none;
								left: 4px;
								top: 1px;
								width: 4px;
								height: 8px;
								border: solid white;
								border-width: 0 2px 2px 0;
								transform: rotate(45deg);
							}
						}

						input:checked ~ .checkmark {
							background-color: #1791fc;
							border-color: #1791fc;

							&::after {
								display: block;
							}
						}

						input:hover ~ .checkmark {
							border-color: #1791fc;
						}

						.layer-name {
							font-size: 12px;
							color: #fff;
						}
					}
				}
			}
		}
	}

	.map-container {
		width: 100%;
		height: 100%;
	}
}

/* 全局样式，用于地图标记 */
:global(.custom-marker) {
	display: flex;
	align-items: center;
	justify-content: center;
}

:global(.svg-marker) {
	background: transparent !important;
	border: none !important;
}

:global(.svg-marker img) {
	display: block;
}
