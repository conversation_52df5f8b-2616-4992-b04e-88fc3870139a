const { reactive } = Vue;
export const treeDataTpl = reactive({
    id: 0,
    flowTitle: '-',
    disposalLevel: '-',
    executeTime: '0',
    endStatus: '0',
    progress: 0,
    processTreeFlow: [
        {
            processId: 1,
            name: 'P1 首报启动',
            pStatus: '2',
            processTime: '10',
            taskTreeVos: []
        },
        {
            processId: 2,
            name: 'P2 现场赶赴',
            pStatus: '1',
            processTime: '0',
            taskTreeVos: []
        },
        {
            processId: 3,
            name: 'P3 出动到位',
            pStatus: '0',
            processTime: '0',
            taskTreeVos: []
        },
        {
            processId: 4,
            name: 'P4 应急处置',
            pStatus: '0',
            processTime: '0',
            taskTreeVos: []
        },
        {
            processId: 5,
            name: 'P5 事件终报',
            pStatus: '0',
            processTime: '0',
            taskTreeVos: []
        }
    ],
    drawInfo: [],
    expertInfo: [],
    leaderInfo: [],
    repairmanInfo: []
});
