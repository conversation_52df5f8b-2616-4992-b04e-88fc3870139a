import { apiService } from "../utils/http.js"
import { utils } from "../utils/utils.js"
const { ref, watch, toRefs } = Vue
const { ElLoading, ElMessage, ElIcon } = ElementPlus
const { Document, Check, Search, Share } = ElementPlusIconsVue

export default {
	template: `
    <el-drawer
        v-model="modelValue"
        title="标准处置流程选择"
        direction="rtl"
        size="50%"
        @closed="resetForm"
    >
			<template #header="{ close, titleId, titleClass }">
				<div style="display: flex;align-items: center">
					<div class="title-icon">
						<el-icon size="14" color="#fff"><Share /></el-icon>
					</div>
					<div :id="titleId" :class="titleClass" style="color: #0a4e9a;margin-left: 4px">标准处置流程选择</div>
				</div>

			</template>
        <div v-if="isLoading" class="loading-wrapper">
            <div class="loading-spinner">
                <div class="spinner"></div>
                <span>加载中...</span>
            </div>
            <div class="fade-overlay"></div>
        </div>
        <div class="drawer-content">
            <el-form inline class="search-container"  label-position="right">
                <el-form-item label="故障位置">
									<el-tree-select
										class="my-query-select"
										v-model="form.locComposeNo"
										placeholder="请选择"
										:data="locComposeNoList"
										:check-strictly="true"
										clearable
									/>
                </el-form-item>
<!--                <el-form-item label="车站" style="width: 180px">-->
<!--                    <el-select v-model="form.station" placeholder="请选择车站">-->
<!--                    <el-option label="6号线" value="station1" />-->
<!--                    <el-option label="8号线" value="station2" />-->
<!--                    </el-select>-->
<!--                </el-form-item>-->
                <el-form-item label="关键词" style="width: 180px">
                    <el-input v-model="form.key" />
                </el-form-item>
                <el-form-item>
                    <el-button style="background: #0a4e9a;color: #fff" @click="clickSubmitBtn" icon="Search" :loading="isLoading">查询</el-button>
                    <el-button style="background: #0a4e9a;color: #fff" @click="resetForm" icon="Refresh">重置</el-button>
                </el-form-item>
            </el-form>
					<el-scrollbar height="calc(100vh - 160px)">
					<div v-loading="isLoading" element-loading-text="加载中...">

						<div class="document-list" v-if="tableData?.length > 0">
							<div
								v-for="doc in tableData"
								:key="doc.id"
								class="document-item"
								:class="{ 'is-selected': selectedDocument?.id === doc.id }"
								@click="handleDocumentClick(doc)"
							>
								<div class="document-icon">
									<el-icon><Document /></el-icon>
<!--									<font-awesome-icon :icon="['fas', 'file-lines']" />-->
								</div>
								<div class="document-info">
									<div class="document-title">{{ doc.mainTitle }}</div>
									<div class="document-meta">
\t\t\t\t\t\t\t\t\t\t<span class="document-id"
										>预案编号: {{ doc.mainCode }}</span
										>
										<span class="document-time"
										>发布时间: {{ doc.createdDate }}</span
										>
									</div>
								</div>
							</div>
						</div>
						<div v-if="tableData?.length === 0" class="no-data">
							暂无数据
						</div>

					</div>
					</el-scrollbar>

<!--            <el-table-->
<!--              :data="tableData"-->
<!--              class="main-list-table"-->
<!--              @row-click="handleRowClick"-->
<!--              highlight-current-row-->
<!--              :row-class-name="tableRowClassName"-->
<!--            >-->
<!--                &lt;!&ndash; 不需要显示 表头 header&ndash;&gt;-->
<!--                <el-table-column width="50">-->
<!--                    <template #header>-->
<!--                        <span class="hidden-header">表头1</span>-->
<!--                    </template>-->
<!--                    <template #default>-->
<!--                        <el-icon><Document /></el-icon>-->
<!--                    </template>-->
<!--                </el-table-column>-->
<!--                <el-table-column>-->
<!--                    <template #header>-->
<!--                        <span class="hidden-header">表头2</span>-->
<!--                    </template>-->
<!--                    <template #default="{row}">-->
<!--                        <div class="ellipsis">{{ row.mainTitle }}</div>-->
<!--                        <div style="font-size:12px;color:#999">-->
<!--                            {{ row.mainCode }} | {{ row.createdDate }}-->
<!--                        </div>-->
<!--                    </template>-->
<!--                </el-table-column>-->
<!--            </el-table>-->
            <div class="footer-actions">
                <!-- 我需要 提交按钮居右，按钮增加一个确认图标-->
                <el-button
                  @click="submitForm"
                  :icon="Check"
									style="background: #0a4e9a;color: #fff"
                >
                  提交
                </el-button>
                <el-button @click="$emit('update:modelValue', false)">取消</el-button>
            </div>
        </div>
    </el-drawer>
  `,
	props: {
		modelValue: Boolean,
		postData: [Number, Object] // 支持数字和对象类型
	},
	emits: ["update:modelValue", "selected", "close"],
	setup(props, { emit }) {
		const form = ref({
			line: "",
			station: "",
			key: ""
		})
		const tableData = ref([])
		const executeMainId = ref(0)
		const isLoading = ref(false)
		const currentRow = ref("")
		const setMain = ref({})

		const localData = ref(props.postData)
		const isActive = ref(false)
		watch(
			() => props.postData,
			(val) => {
				if (!isActive.value) return
				if (val && !utils.deepEqual(val, localData.value)) {
					console.log("数据变更处理", val)
					localData.value = val
					executeMainId.value = val.id
					getFlowSetData()
				}
			},
			{ deep: true }
		)

		watch(
			() => props.modelValue,
			(open) => {
				isActive.value = open
				if (open) {
					console.log("抽屉激活状态")
					getLocComposeTree()
				}
			}
		)
		const documentListLoading = ref(false)
		// 选中的文档
		const selectedDocument = ref()
		const locComposeNoList = ref([])
		/**
		 * @description 获取关联位置树的数据
		 */
		const getLocComposeTree = async () => {
			const formData = new FormData()
			formData.append("currentPage", 1)
			formData.append("pageSize", 500)
			const res = await apiService
				.getLocComposeTreeApi(formData)
				.then((res) => {
					locComposeNoList.value = []
					const map = new Map(Object.entries(res.data))
					map.forEach((value, key) => {
						locComposeNoList.value = addValueAndLabelForTree(value)
					})
				})
		}
		/**
		 * @description 递归处理数据, 为 cascade 的每个 options 添加 label 和 value 属性
		 */
		const addValueAndLabelForTree = (arr) => {
			arr = arr.map((node) => {
				if (node.hasOwnProperty("children")) {
					node.children = addValueAndLabelForTree(node.children)
				}
				return {
					...node,
					label: `${node.locComposeNameBelong}(${node.locComposeNoBeLong})`,
					value: node["locComposeNo"]
				}
			})
			return arr
		}
		// 处理文档点击
		const handleDocumentClick = (doc) => {
			selectedDocument.value = doc
			setMain.value = { ...doc }
		}
		const getFlowSetData = async () => {
			console.log("getFlowSetData:: 加载流程数据")
			isLoading.value = true
			try {
				const res = await apiService.getProcessTreeFlowSetMain({
					locComposeNo: form.value.locComposeNo,
					disposalLevel: "",
					lineId: form.value.line,
					status: "",
					mainTitle: form.value.key
				})
				console.log("getProcessTreeFlowSetMain", res.data)
				if (res.code == 200) {
					tableData.value = res.data
				} else {
					ElMessage.warning(`数据获取失败: ${res.msg}`)
				}
			} catch (error) {
				if (error.name !== "AbortError") {
					ElMessage.warning(`数据获取失败: ${error.message}`)
				}
			} finally {
				isLoading.value = false
			}
		}

		const clickSubmitBtn = () => {
			getFlowSetData()
		}

		const resetForm = () => {
			form.value = { line: "", station: "", key: "" }
			//getFlowSetData();
		}

		const handleRowClick = (row) => {
			//ElMessage.warning(`handleRowClick: ${ row.id }`)
			//setMain.value = row;
			console.log("handleRowClick row", row)
			setMain.value = { ...row }
		}
		const tableRowClassName = (row) => {
			return row === currentRow ? "current-row" : ""
		}

		const submitForm = async () => {
			console.log("setMain", setMain)
			console.log("executeMainId", executeMainId)
			if (!setMain.value.id) {
				ElMessage.warning("请先选择流程")
				return
			}
			emit("update:modelValue", false)
			isLoading.value = true
			try {
				const res = await apiService.changeProcess({
					executeMainId: executeMainId.value,
					setMainId: setMain.value.id,
					mainTitle: setMain.value.mainTitle,
					disposalLevel: setMain.value.disposalLevel
				})
				if (res.code && res.code == 200) {
					ElMessage.success("提交成功")
					emit("selected", res.data.id)
					emit("update:modelValue", false)
				} else {
					ElMessage.warning(`提交失败: ${res.msg}`)
				}
			} catch (error) {
				if (error.name !== "AbortError") {
					ElMessage.warning(`数据获取失败: ${error.message}`)
				}
			} finally {
				isLoading.value = false
			}
		}

		return {
			form,
			tableData,
			isLoading,
			clickSubmitBtn,
			resetForm,
			handleRowClick,
			tableRowClassName,
			currentRow,
			submitForm,
			handleDocumentClick,
			selectedDocument,
			locComposeNoList
		}
	}
}
