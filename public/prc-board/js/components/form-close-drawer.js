import { jsObjSdk } from "../utils/js-obj-sdk.js"
import { apiService } from "../utils/http.js"
import { utils } from "../utils/utils.js"
const { ref, watch, toRefs } = Vue
const { ElLoading, ElMessage, ElIcon } = ElementPlus
const { Document, Check } = ElementPlusIconsVue
export default {
	template: `
    <el-drawer v-model="modelValue" direction="rtl" size="300">
			<template #header="{ close, titleId, titleClass }">
				<div style="display: flex;align-items: center">
					<div class="title-icon">
						<el-icon size="14" color="#fff"><Share /></el-icon>
					</div>
					<div :id="titleId" :class="titleClass" style="color: #0a4e9a;margin-left: 4px">关闭原因</div>
				</div>
			</template>
        <div class="drawer-content" style="height: calc(100vh - 110px)">
            <el-form :inline="true" :model="form" :rules="rules" label-width="80px" label-position="top">
                <el-form-item label="关闭原因填写" prop="reason"	style="width: 100%">
                    <el-input
                        v-model="form.reason"
                        type="textarea"
                        :rows="3"
                        placeholder="请输入内容"
                    />
                </el-form-item>

            </el-form>
        </div>
			<div style="text-align: right;border-top: 1px solid #e4e4e4;padding-top: 10px">
				<el-button @click="resetForm" icon="RemoveFilled" style="background: #0a4e9a;color: #fff">取消</el-button>
				<el-button
					style="background: #0a4e9a;color: #fff"
					@click="submitForm"
					icon="CircleCheck"
					:loading="submitting"
				>
					提交
				</el-button>
			</div>
    </el-drawer>
    `,
	props: {
		modelValue: Boolean,
		postData: Object
	},
	data() {
		return {
			//全局-处置流程数据
			dataTreeApi: {},
			form: {
				reason: ""
			},
			submitting: false,
			rules: {
				reason: [{ required: true, message: "请输入内容", trigger: "blur" }]
			}
		}
	},
	watch: {
		postData: {
			immediate: true,
			handler(newVal) {
				if (newVal) {
					this.dataTreeApi = newVal
				}
			}
		}
	},
	methods: {
		async submitForm() {
			if (!this.form.reason) {
				this.$message.error("请输入关闭原因")
				return
			}
			this.submitting = true
			try {
				const mainId = this.dataTreeApi.id || 0
				const executeTime = this.dataTreeApi.executeTime //TODO 关闭时间 未实现
				const endStatus = utils.findFirstTaskByStatus(
					this.dataTreeApi.processTreeFlow,
					0
				)
					? 2
					: 1
				const res = await apiService.submitCloseAndSave({
					processId: Number(mainId),
					filePath: null,
					executeTime: executeTime,
					endStatus: endStatus,
					reason: this.form.reason
				})
				console.log("submitCloseAndSave::", res)
				if (res.code && res.code == 200) {
					ElMessage.success("提交成功")
					emit("selected", res.data)
					emit("update:modelValue", false)
					//this.resetForm()
					jsObjSdk.appExit() //通知winform程序关闭
				} else {
					ElMessage.warning(`提交失败: ${res.msg}`)
				}
			} catch (error) {
				if (error.name !== "AbortError") {
					ElMessage.warning(`数据获取失败: ${error.message}`)
					console.log(`数据获取失败: ${error.message}`)
				}
			} finally {
				this.submitting = false
			}
		},
		resetForm() {
			this.form = { reason: "" }
			this.$emit("update:modelValue", false)
		}
	}
}
