const { ref,reactive } = Vue;
// 常量
class Constant {
    constructor() {
        this.positionList = ref([
            { id: "repairmanInfo", name: "检修师" },
            { id: "expertInfo", name: "专家" },
            { id: "leaderInfo", name: "领导" }
        ]);
        this.POSITION_KEYS = {
            0: 'repairmanInfo',
            1: 'expertInfo',
            2: 'leaderInfo'
        };
        this.dialogState = reactive({
            visible: false,
            config: {
                title: '操作确认',
                message: '您确认执行此操作吗？',
                buttons: [
                    {
                        text: '取消',
                        action: 'cancel',
                        type: '',
                        show: true,
                        handler: () => dialogState.visible = false
                    },
                    {
                        text: '确认',
                        action: 'confirm',
                        type: 'primary',
                        show: true,
                        handler: () => {
                            console.log('执行确认操作')
                            dialogState.visible = false
                        }
                    }
                ]
            }
        })
    }
}
export const constant = new Constant();
