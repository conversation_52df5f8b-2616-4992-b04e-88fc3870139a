const VITE_BASE_API = "http://192.168.31.10:9731/"

function getFile(filePath) {
	return request({
		url: "/pitaya/system/common/static/" + filePath,
		method: "get",
		responseType: "blob"
	})
}
const downloadFile = (filePath) => {
	const url = buildPreviewUrl() + filePath
	const link = document.createElement("a")
	link.href = url
	link.download = url.split("/").pop() || ""
	document.body.appendChild(link)
	link.click()
	document.body.removeChild(link)
}
function buildPreviewUrl() {
	const baseApi = VITE_BASE_API
	const prefix = baseApi.endsWith("/") ? baseApi : `${baseApi}/`
	const previewUrl = prefix + "pitaya/system/common/static/"
	return previewUrl
}
export const FileApi = {
	downloadFile,
	buildPreviewUrl
}
