import { jsObjSdk } from "../utils/js-obj-sdk.js"
// 创建axios实例
const http = axios.create({
	baseURL: "http://150.129.138.142:10025", //local-dev
	// baseURL: 'http://172.16.20.224:9401/baseline/system/', //test
	timeout: 10000,
	headers: {
		"Content-Type": "application/json"
	}
})

// 请求拦截器
http.interceptors.request.use((config) => {
	const token = localStorage.getItem("token")
	if (!token) {
		return Promise.reject(new Error("token不存在"))
	}
	config.headers.Authorization = token
	return config
})

// 响应拦截器
http.interceptors.response.use(
	(response) => {
		const { code, msg, data } = response.data || {}
		if (code !== undefined && code != 200) {
			const error = new Error(msg || "请求失败")
			error.code = code
			error.response = response
			console.error(`[HTTP Error] Code: ${code}, Message: ${msg}`)
			return Promise.reject(error)
		}
		//return data !== undefined ? data : response.data;
		return response.data
	},
	(error) => {
		let message = "网络错误"
		const status = error.response?.status
		if (status) {
			const messages = {
				400: "参数错误",
				401: "请重新登录",
				403: "无权限",
				404: "资源不存在",
				500: "服务器错误"
			}
			message =
				messages[status] || error.response.data?.msg || `错误码: ${status}`
		} else {
			message = error.message || message
		}
		return Promise.reject(new Error(message))
	}
)

// API服务层
class ApiService {
	constructor() {
		this.http = http
	}

	isHttp() {
		return true
		// const protocol = window.location.protocol; // 协议 (e.g. "https:")
		// if (protocol === 'https:' || protocol === 'http:') {
		// 	return true;
		// }
		// return false;
	}

	/**
	 * 更新步骤状态
	 * POST /baseline/system/tracking/updateStepStatus
	 *
	 * 2、API 确认和跳过
	 * action: confirm
	 * @param id: 步骤ID data.processTreeFlow.taskTreeVos.taskStep.id
	 * @param executeStatus: 状态:0待执行、1执行中、2确认、3跳过
	 * @param executeTime: 步骤执行时间（秒）
	 */
	async submitStepConfirm(formData) {
		return this.isHttp()
			? this.http.post("baseline/system/tracking/updateStepStatus", formData)
			: jsObjSdk.postTrackingTree(
					"baseline/system/tracking/updateStepStatus",
					formData
			  )
		//return jsObjSdk.postTrackingTree('tracking/updateStepStatus', formData)
	}

	/**
	 * 应急追踪树就位准备
	 * POST /baseline/system/tracking/readyPlace
	 *
	 * 3、API 人员确认就位按钮
	 * act: inPlace
	 * @param id: 步骤ID data.processTreeFlow.taskTreeVos.taskStep.id
	 * @param executeStatus: 状态:0待执行、1执行中、2确认、3跳过
	 * @param executeTime: 步骤执行时间（秒）
	 */
	async submitUserInPlace(formData) {
		return this.isHttp()
			? this.http.post("baseline/system/tracking/readyPlace", formData)
			: jsObjSdk.postTrackingTree(
					"baseline/system/tracking/readyPlace",
					formData
			  )
	}

	/**
	 * 应急追踪树关闭保存
	 * POST /baseline/system/tracking/closeProcessStatus
	 *
	 * 4、API 关闭保存按钮
	 * act: closeAndSave
	 * @param id: 流程ID data.id
	 * @param reason: 关闭原因
	 * @param executeTime: 整体执行时间（秒） data.executeTime
	 * @param endStatus: data.endStatus 终止状态: 0未终止、1正常终止、2异常终止(未完成全部步骤)
	 */
	async submitCloseAndSave(formData) {
		return this.isHttp()
			? this.http.post("baseline/system/tracking/closeProcessStatus", formData)
			: jsObjSdk.postTrackingTree(
					"baseline/system/tracking/closeProcessStatus",
					formData
			  )
		//return jsObjSdk.postTrackingTree('tracking/closeProcessStatus', formData)
	}

	/**
	 * 获取应急追踪树数据
	 * GET /baseline/system/tracking/getTrackingTree
	 * 1、API 获取应急追踪树数据
	 * @param id 流程ID data.id
	 */
	async getProcessDetail(id) {
		return this.isHttp()
			? this.http.get("baseline/system/tracking/getTrackingTree", {
					params: { processId: id }
			  })
			: jsObjSdk.getTrackingTree("baseline/system/tracking/getTrackingTree", {
					processId: id
			  })
	}

	/**
	 * GET /baseline/system/rpstssProcessTreeFlowSetMain/list
	 * 5、API 应急追踪树-流程编制-主表-列表查询
	 */
	async getProcessTreeFlowSetMain(params) {
		return this.isHttp()
			? this.http.get("baseline/system/rpstssProcessTreeFlowSetMain/list", {
					params
			  })
			: jsObjSdk.getTrackingTree(
					"baseline/system/rpstssProcessTreeFlowSetMain/list",
					params
			  )
	}

	/**
	 * GET /baseline/system/positions/tree
	 * 5、API 应急追踪树-更换流程- 故障位置
	 */
	async getLocComposeTreeApi(formData) {
		return this.isHttp()
			? this.http.get("positions/tree", formData)
			: jsObjSdk.postTrackingTree("positions/tree", formData)
	}

	/**
	 * POST /baseline/system/rpstssProcessTreeFlowExecuteMain/changeProcess
	 * 6、API 更换流程
	 */
	async changeProcess(formData) {
		return this.isHttp()
			? this.http.post(
					"baseline/system/rpstssProcessTreeFlowExecuteMain/changeProcess",
					formData
			  )
			: jsObjSdk.postTrackingTree(
					"baseline/system/rpstssProcessTreeFlowExecuteMain/changeProcess",
					formData
			  )
	}

	// 获取URL参数
	async getUrlParams() {
		try {
			const queryString = window.location.search
			if (!queryString) {
				throw new Error("URL中缺少查询参数")
			}
			const urlParams = new URLSearchParams(queryString)
			const token = urlParams.get("token")?.trim() || ""
			const baseId = urlParams.get("id")?.trim() || ""

			//if (!token) throw new Error('token不能为空');
			if (!baseId) throw new Error("id不能为空")

			localStorage.setItem("token", token)
			localStorage.setItem("baseId", baseId)
			return { token, baseId }
		} catch (error) {
			console.error("参数解析错误:", error)
			return Promise.reject(error)
		}
	}
}
// 导出实例
export const apiService = new ApiService()
export default http
