const httpBase = "/baseline/system/";
class JsObjSdk {
    objectToQueryString(obj) {
        return Object.keys(obj)
            .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(obj[key])}`)
            .join('&');
    }

    appExit() {
        try {
            var response = JsObj.stopRecording();
            console.log("JsObj::stopRecording:", response)
						setTimeout(() => {
							var response = JsObjSdk.appExit();//返回一个“关闭成功”的字符串
							console.log("JsObj::appExit", response)
							return result;
						}, 5000);
        } catch (error) {
            console.error('appExit:', error)
        }
    }

    getTrackingTree(path, params) {
        try {
            const uri = httpBase + path;
            const queryString = this.objectToQueryString(params);
            console.log("JsObj::getTrackingTree:", uri, "?", queryString)
            var response = JsObj.getTrackingTree(queryString , uri)
            console.log("response", response)
            var json = JSON.parse(response)
            console.log("json", json)
            return json;
        } catch (error) {
            console.error('JsObj::getTrackingTree:', error)
        }
    }

    postTrackingTree(path, jsonData) {
        try {
            const uri = httpBase + path;
            console.log("JsObj::post uri:", uri, ", data=", jsonData)
            var response = JsObj.postTrackingTree(jsonData, uri)
            console.log("response", response)
            var json = JSON.parse(response)
            console.log("json", json)
            return json;
        } catch (error) {
            console.error('JsObj::postTrackingTree:', error)
        }
    }

    startRecording(id) {
        try {
            const filepath = JsObj.startRecording(id);
            console.log("startRecording::filepath=", filepath)
            return filepath;
        } catch (error) {
            console.error('JsObj::startRecording:', error)
        }
    }

}
export const jsObjSdk = new JsObjSdk();

