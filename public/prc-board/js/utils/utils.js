const { nextTick } = Vue;

class Utils {
    formatTime(timestamp) {
        const date = new Date(timestamp)
        return {
            hours: date.getHours().toString().padStart(2, '0'),
            minutes: date.getMinutes().toString().padStart(2, '0'),
            seconds: date.getSeconds().toString().padStart(2, '0'),
            toString() {
                return `${this.hours}:${this.minutes}:${this.seconds}`
            }
        }
    }

    secondsFormatTime(seconds) {
        const hrs = Math.floor(seconds / 3600);
        const mins = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;
        return `${hrs.toString().padStart(2,'0')}:${mins.toString().padStart(2,'0')}:${secs.toString().padStart(2,'0')}`;
    }

    getSystemTime() {
        return new Date().toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false
        })
    }

    statusText(status) {
        const statusMap = {
            1: '执行中',
            2: '已执行',
            3: '已跳过'
        }
        return statusMap[status] || ''
    }

    stepTitleClass(step) {
        return [
            'step-item-title',
            step.executeStatus > 1 ? 'step-item-title-finsh' : '',
            step.executeStatus == 1 ? 'step-item-title-unfinsh' : ''
        ]
    }

    statusClass(step) {
        return [
            'step-item-staus',
            step.executeStatus > 1 ? 'step-item-staus-finsh' : '',
            step.executeStatus == 1 ? 'step-item-staus-unfinsh' : ''
        ]
    }

    //页面状态
    shouldShowRole(index, step, taskSteps) {
        // 添加参数校验和防御性编程
        if (!taskSteps || !Array.isArray(taskSteps)) return false
        return index == 0 || step.roleName != taskSteps[index - 1]?.roleName
    }

    //页面状态
    findFirstTaskByStatus(data, status) {
        //console.log("findFirstTaskByStatus: 开始查找");
        // 外层循环：遍历一级任务
        for (let firstLevelIndex = 0; firstLevelIndex < data.length; firstLevelIndex++) {
            const firstLevel = data[firstLevelIndex];
            //console.log(`一级任务 ${firstLevelIndex + 1}:`, firstLevel);
            if (!firstLevel.taskTreeVos || firstLevel.taskTreeVos.length === 0) {
                //console.log(`- 跳过，无子任务`);
                continue;
            }
            // 内层循环：遍历二级任务
            for (let secondLevelIndex = 0; secondLevelIndex < firstLevel.taskTreeVos.length; secondLevelIndex++) {
                const secondLevel = firstLevel.taskTreeVos[secondLevelIndex];
                //console.log(`  二级任务 ${secondLevelIndex + 1}:`, secondLevel);
                // 状态处理
                const statusList = Array.isArray(status) ? status : [status];
                const statusSet = new Set(statusList.map(s => s.toString()));

                // 查找符合条件的步骤及其索引
                const foundStepIndex = secondLevel.taskStep.findIndex(step => statusSet.has(step.executeStatus) );
                const foundStep = foundStepIndex !== -1 ? secondLevel.taskStep[foundStepIndex] : null;

                if (foundStep) {
                    //console.log(`findFirstTaskByStatus: status = ${status} 找到匹配项`);
                    return {
                        itemIndex: {
                            processIndex: firstLevelIndex,
                            taskIndex: secondLevelIndex,
                            stepIndex: foundStepIndex
                        },
                        processItem: {
                            index: firstLevelIndex,
                            processId: firstLevel.processId,
                            name: firstLevel.name,
                            pStatus: firstLevel.pStatus,
                            processTime: firstLevel.processTime
                        },
                        taskItem: {
                            index: secondLevelIndex,
                            taskId: secondLevel.taskId,
                            taskContent: secondLevel.taskContent,
                            taskTime: secondLevel.taskTime
                        },
                        taskStepItem: {
                            index: foundStepIndex,
                            stepId: foundStep.stepId,
                            stepContent: foundStep.stepContent,
                            roleName: foundStep.roleName,
                            executeStatus: foundStep.executeStatus,
                            sort: foundStep.sort,
                            executeTime: foundStep.executeTime
                        },
                        taskPoint: secondLevel.taskPoint
                    };
                }
            }
        }
        //console.log(`findFirstTaskByStatus: status = ${status} 未找到匹配项`);
        return null;
    }

    // 使用优先级顺序查找任务（执行中 -> 待执行 -> 已执行）
    findTaskByPriority(data, priorityOrder) {
        const executeTask = {};
        for (const status of priorityOrder) {
            executeTask.value = this.findFirstTaskByStatus(data, status);
            if (executeTask.value) break;
        }
        return executeTask.value;
    }

    //更新员工状态
    updateUserInfo(id, userList, newData) {
        const list = userList.value || userList; // 兼容ref和普通数组
        const index = list.findIndex(item => item.id === id);
        if (index !== -1) {
            list[index] = { ...list[index], ...newData };
        }
        return { value: list }; // 保持返回值结构一致
    }


    /**
     * 计算流程完成百分比
     * @param {Array} processTreeFlow 流程树数组
     * @param {Number} status 完成状态码
     * @returns {String} 百分比字符串(带%符号)
     */
    calculateProgress(processTreeFlow, status) {
        try {
            const total = this.countValidNodes(processTreeFlow);
            const completed = this.countNodesByStatus(processTreeFlow, status);
            return Math.round((completed / total) * 100);
        } catch (e) {
            console.error('进度计算错误:', e);
            return '0%';
        }
    }

    /**
     * 统计有效节点数量
     * @param {Array} processTreeFlow 流程树数组
     * @returns {Number} 有效节点数(至少返回1)
     */
    countValidNodes(processTreeFlow) {
        if (!Array.isArray(processTreeFlow)) return 1;
        return Math.max(1, processTreeFlow.filter(
            node => node?.taskTreeVos != null
        ).length);
    }

    /**
     * 统计符合状态的节点数量
     * @param {Array} processTreeFlow 流程树数组
     * @param {Number} status 目标状态码
     * @returns {Number} 匹配数量
     */
    countNodesByStatus(processTreeFlow, status) {
        if (!Array.isArray(processTreeFlow)) return 0;
        return processTreeFlow.filter(node =>
            Number(node?.pStatus) === Number(status)
        ).length;
    }


	/**
	 * 初始化流程树状态
	 * @param {Object} data - 原始流程树数据
	 * @returns {Object} 处理后的流程树
	 */
	initProcessTree(data) {
		if (!data?.processTreeFlow) return data;
		data.processTreeFlow.forEach(process => {
			// 默认设置流程为已完成
			process.pStatus = process.taskTreeVos ? 2 : 0;
			process.taskTreeVos?.forEach(task => {
				// 默认设置任务为已完成
				task.tStatus = 2;
				if (!task.taskStep?.length) return;
				// 使用更高效的数组遍历方法
				const hasNotStarted = task.taskStep.some(step => step.executeStatus == 0);
				const hasInProgress = task.taskStep.some(step => step.executeStatus == 1);
				const hasCompleted = task.taskStep.some(step => step.executeStatus == 2);
				if (hasInProgress) {
					//console.log("hasInProgress if 01 存在进行中的步骤");
					process.pStatus = 1;
					task.tStatus = 1;
				} else if (hasNotStarted) {
					if (hasCompleted) {
						//console.log("hasCompleted if 02 不存在进行中的，存在未开始的，并且 存在已完成的");
						process.pStatus = 1;
						task.tStatus = 1;
					} else {
						//console.log("hasCompleted else 03 不存在进行中的，存在未开始的，并且 不存在已完成的");
						process.pStatus = 0;
						task.tStatus = 0;
					}
				}
			});
		});
		return data;
	}


    /**
     * 滚动到指定流程节点
     * @param {String} dynamicId 动态生成的元素ID
     * @param {Number} delay 延迟时间(ms)
     */
    scrollToProcess(dynamicId, delay = 100) {
        nextTick(() => {
            setTimeout(() => {
                const el = document.getElementById(dynamicId);
                el?.scrollIntoView({ behavior: 'smooth' });
            }, delay);
        });
    }

    deepEqual(a, b) {
        return JSON.stringify(a) === JSON.stringify(b)
    }

}
export const utils = new Utils();

