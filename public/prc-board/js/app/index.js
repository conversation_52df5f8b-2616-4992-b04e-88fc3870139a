import { apiService } from "../utils/http.js"
import { treeDataTpl } from "../base/treeDataTpl.js"
import { utils } from "../utils/utils.js"
import { constant } from "../utils/constant.js"
import ListChangeDrawer from "../components/list-change-drawer.js"
import FormCloseDrawer from "../components/form-close-drawer.js"
import GisPreviewDrawer from "../components/gis-preview-drawer.js"
import { jsObjSdk } from "../utils/js-obj-sdk.js"
import { FileApi } from "../utils/file.js"
import { gisData, treeData } from "../base/gisDataMock.js" //GIS mock 数据
console.log("treeData", treeData)
// import { WarningFilled } from "./unpkg.com/@element-plus/icons-vue@2.3.1/dist/index.iife.min.js"
const {
	createApp,
	ref,
	reactive,
	computed,
	onMounted,
	onUnmounted,
	watch,
	toRaw
} = Vue
const { ElLoading, ElMessage } = ElementPlus
const app = createApp({
	components: {
		ListChangeDrawer,
		FormCloseDrawer,
		GisInformation: GisPreviewDrawer
	},
	setup() {
		const isLoading = ref(false)
		const dialogState = constant.dialogState
		//组件控制
		const showChangeList = ref(false)
		const showCloseForm = ref(false)
		//系统时间
		const systemTime = ref("")
		const showGisInformation = ref(false)
		//全局-处置流程数据
		const dataTreeApi = reactive({
			systemTime,
			...treeDataTpl,
			executeTimeFormat: computed(() =>
				utils.secondsFormatTime(dataTreeApi.executeTime)
			),
			_progress: computed(() =>
				utils.calculateProgress(dataTreeApi.processTreeFlow, 2)
			)
			// _progress: 0,
			// get progress() {
			//     return utils.calculateProgress(this.processTreeFlow, 2)
			// },
			// set progress(val) {
			//     this._progress = val
			// }
		})
		//中间-当前执行数据
		const dataCurrentStep = reactive({
			itemIndex: null,
			processItem: null,
			taskItem: null,
			taskStepItem: null
		})
		//左侧-选项卡-当前角色
		const userActivePosIndex = ref("0") //当前角色
		//左侧-人员列表
		const userInfo = reactive({
			positionList: constant.positionList,
			userList: null
		})
		//右侧-要素
		const dataPointSelected = reactive({
			taskItem: null,
			taskPoint: null
		})

		/**
		 * 1、API 获取应急追踪树数据
		 */
		const apiGetProcessDetail = async (id) => {
			isLoading.value = true
			let { data } = await apiService.getProcessDetail(id)
			data = utils.initProcessTree(data)
			Object.assign(dataTreeApi, data)
			Object.assign(userInfo, { userList: data.repairmanInfo })
			const lastStatus = await reloadTree(dataTreeApi)
			if (lastStatus < 2) calculateTotalTme()
			isLoading.value = false
			if (!apiService.isHttp()) jsObjSdk.startRecording(id) //开始录制
		}

		/**
		 * 2、API 确认和跳过
		 */
		const clickDoStepConfirmBtn = async (stepId, executeStatus) => {
			try {
				const stepItem = getExecuteStep(dataTreeApi, dataCurrentStep.itemIndex)
				if (!stepItem || stepItem.stepId != stepId) {
					ElMessage.warning(`未找到步骤ID为 ${stepId} 的信息`)
					return false
				}
				const executeTime = Number(stepItem.executeTime) || 0
				isLoading.value = true
				const res = await apiService.submitStepConfirm({
					id: dataTreeApi.id, //??
					taskId: dataCurrentStep.taskItem.taskId, //??
					stepId: stepItem.stepId,
					taskTime: String(executeTime), //??
					executeStatus: String(executeStatus) //??
				})

				//TODO 更新前端显示数据
				//执行中步骤
				getExecuteStep(dataTreeApi, dataCurrentStep.itemIndex).executeStatus =
					executeStatus
				getExecuteStep(dataTreeApi, dataCurrentStep.itemIndex).executeTime =
					executeTime

				const lastStatus = await reloadTree(dataTreeApi)
				// if(lastStatus < 2) calculateTotalTme();

				isLoading.value = false
				console.log("submitStepConfirm::", res)
				ElMessage.success(
					executeStatus == 2
						? "已确认"
						: executeStatus == 3
						? "已跳过"
						: "其它-" + executeStatus
				)
				isLoading.value = false
			} catch (error) {
				// 忽略取消请求的错误
				if (error.name !== "AbortError") {
					ElMessage.warning(`数据获取失败: ${error.message}`)
					console.error(`数据获取失败: ${error.message}`)
				}
				isLoading.value = false
			}
		}

		/**
		 * 3、API 人员确认就位按钮
		 */
		const clickSetUserInPlaceBtn = async (userActivePosIndex, personId) => {
			if (userActivePosIndex == 0) return
			isLoading.value = true
			// try {
			console.log({
				id: personId,
				type: userActivePosIndex
			})
			// {
			//     "id": 0,
			//     "empType": "string"
			// }
			const res = await apiService.submitUserInPlace({
				id: personId,
				empType: String(userActivePosIndex)
			})
			console.log("submitUserInPlace res", res)
			if (res.code == 200) {
				const key = constant.POSITION_KEYS[userActivePosIndex]
				const userList = dataTreeApi?.[key] || []
				utils.updateUserInfo(personId, userList, { inPosition: "1" })
				ElMessage.success("已就位")
			}
			isLoading.value = false
			// } catch (error) {
			//     // 忽略取消请求的错误
			//     if (error.name !== 'AbortError') {
			//         ElMessage.warning(`数据获取失败: ${error.message}`)
			//         console.error(`数据获取失败: ${error.message}`)
			//     }
			//     isLoading.value = false;
			// }
		}

		//处理左侧
		//左侧-人员列表
		function clickChangePositionTab(index) {
			if (index < 0 || index >= Object.keys(constant.POSITION_KEYS).length) {
				console.warn("Invalid position index")
				return
			}
			const key = constant.POSITION_KEYS[index]
			Object.assign(userInfo, {
				userList: dataTreeApi?.[key] || []
			})
			userActivePosIndex.value = index
		}

		//顶部-系统时间
		const updateSystemTime = () => {
			systemTime.value = utils.getSystemTime()
			setTimeout(updateSystemTime, 1000)
		}

		//右侧-关闭保存按钮
		const clickCloseAndSaveBtn = () => {
			console.log("clickCloseAndSaveBtn")
			// 清空原有按钮再添加
			dialogState.config = {
				title: "系统提示",
				message: "选择关闭保存，还是更换标准处置流程？",
				buttons: [
					{
						text: "关闭保存",
						action: "toCloseAndSave",
						type: "",
						show: true,
						icon: "MessageBox",
						handler: () => {
							console.log("执行关闭保存")
							dialogState.visible = false
							showCloseForm.value = true
						}
					},
					{
						text: "更换流程",
						action: "toChangeList",
						type: "primary",
						show: true,
						icon: "Switch",
						handler: () => {
							console.log("执行更换流程")
							dialogState.visible = false
							showChangeList.value = true
						}
					}
				]
			}
			dialogState.visible = true
		}

		//右侧-关闭-弹窗
		// function clickCloseAndSaveAfterDialog() {
		//     console.log("clickCloseAndSaveAfterDialog");
		//     apiCloseAndSave();
		//     dialogShow.value = false
		// }

		//右侧 远程支持
		const clickRemoteSupportBtn = async (mainId) => {
			if (mainId != dataTreeApi.id) {
				ElMessage.warning(`id error`)
				return
			}
			// ElMessage.warning({
			//     message: '启动远程支持会话失败',
			//     duration: 3000,  // 显示5秒（默认3000毫秒）
			//     offset: 40,      // 距离顶部的偏移量
			//     grouping: true   // 合并相同内容的提示
			// })
			ElMessage.warning({
				message: "启动远程支持会话失败",
				duration: 0, // 永久显示，需要手动关闭
				showClose: true, // 显示关闭按钮
				customClass: "remote-support-alert", // 自定义样式类
				onClose: () => {
					console.log("用户关闭了远程支持警告")
					// 这里可以添加关闭后的回调逻辑
				}
			})
		}

		//右侧-显示要素
		function clickShowTaskPointBtn(taskPoint, selectedTask) {
			Object.assign(dataPointSelected, {
				taskItem: selectedTask,
				taskPoint: taskPoint
			})
		}

		//右侧-累计用时(总时间)
		const calculateTotalTme = () => {
			dataTreeApi.executeTime = (Number(dataTreeApi.executeTime) || 0) + 1
			setTimeout(calculateTotalTme, 1000)
		}

		//中间-当前执行任务计时
		const calculateTaskTime = (stepId, processTreeFlow) => {
			let totalTime = 0
			// 遍历四级嵌套结构
			processTreeFlow?.forEach((process) => {
				process.taskTreeVos?.forEach((task) => {
					const steps = task.taskStep
					if (!steps || !steps.length) return
					// 检查当前taskStep是否包含目标stepId
					const targetStep = steps.find((step) => step.stepId === stepId)
					if (targetStep) {
						// 累加所有同级步骤的executeTime
						totalTime = steps.reduce((sum, step) => {
							return sum + (parseInt(step.executeTime) || 0)
						}, 0)
					}
				})
			})
			return totalTime
		}

		//中间-当前执行步骤计时
		let executeStepTimer = null
		const updateExecuteStepTime = () => {
			// 清除已有计时器避免重复
			if (executeStepTimer) clearTimeout(executeStepTimer)
			const currentStep = getExecuteStep(dataTreeApi, dataCurrentStep.itemIndex)
			// 仅当状态为执行中时才计时
			if (currentStep.executeStatus === 1) {
				currentStep.executeTime = (Number(currentStep.executeTime) || 0) + 1
				const stepId = currentStep.stepId
				getExecuteTask(dataTreeApi, dataCurrentStep.itemIndex).taskTime =
					calculateTaskTime(stepId, dataTreeApi.processTreeFlow)
				// 设置新的计时器
				executeStepTimer = setTimeout(updateExecuteStepTime, 1000)
			}
		}

		//全局-状态改变时 重新加载数据
		const reloadTree = async (dataTree) => {
			const prevStep = { ...dataCurrentStep }
			const nextStep = utils.findTaskByPriority(
				dataTree.processTreeFlow,
				[1, 0, 2, 3]
			)
			// 处理无后续任务的情况
			if (!nextStep) {
				console.log("prevStep", prevStep)
				if (prevStep.itemIndex !== null) {
					getExecutePocess(dataTreeApi, prevStep.itemIndex).pStatus = 2
				}
				return -1
			}
			// 更新当前任务数据
			Object.assign(dataCurrentStep, nextStep)
			Object.assign(dataPointSelected, {
				processItem: nextStep.processItem,
				taskItem: nextStep.taskItem,
				taskPoint: nextStep.taskPoint
			})
			const nextStatus = Number(nextStep.taskStepItem.executeStatus)
			if (nextStatus === 0) {
				getExecuteStep(dataTreeApi, nextStep.itemIndex).executeStatus = 1
				getExecutePocess(dataTreeApi, nextStep.itemIndex).pStatus = 1
				getExecuteTask(dataTreeApi, nextStep.itemIndex).tStatus = 1
			}
			if (nextStatus <= 1) {
				updateExecuteStepTime()
				//执行中流程
				getExecutePocess(dataTreeApi, nextStep.itemIndex).pStatus = 1
				if (
					prevStep.itemIndex != null &&
					nextStep.itemIndex.processIndex != prevStep.itemIndex.processIndex
				) {
					//两次流程不一样，说明上一次已结束
					getExecutePocess(dataTreeApi, prevStep.itemIndex).pStatus = 2
				}
			}
			//页面滚动
			utils.scrollToProcess("process-item-" + nextStep.processItem.processId)
			return nextStatus
		}

		//中间-确认/跳过-执行中任务
		function getExecuteTask(dataTree, itemIndex) {
			return dataTree.processTreeFlow[itemIndex.processIndex].taskTreeVos[
				itemIndex.taskIndex
			]
		}

		//中间-确认/跳过-执行中步骤
		function getExecuteStep(dataTree, itemIndex) {
			return dataTree.processTreeFlow[itemIndex.processIndex].taskTreeVos[
				itemIndex.taskIndex
			].taskStep[itemIndex.stepIndex]
		}

		//中间-确认/跳过-执行中流程
		function getExecutePocess(dataTree, itemIndex) {
			return dataTree.processTreeFlow[itemIndex.processIndex]
		}

		// 初始化应用
		const initApp = async () => {
			try {
				const params = await apiService.getUrlParams()
				await apiGetProcessDetail(params.baseId)
			} catch (err) {
				console.error("初始化失败:", err)
				ElMessage.error({
					message: `初始化失败: ${err.message}`,
					duration: 5000
				})
				throw err // 重新抛出错误
			}
		}

		onMounted(() => {
			initApp()
			updateSystemTime()
			//console.log("dataTreeApi", dataTreeApi);
		})

		const handleChangeSelected = async (baseId) => {
			console.log("接收到选择数据:", baseId)
			const token = localStorage.getItem("token")
			window.open(`./index.html?id=${baseId}&token=${token}`, "_parent")
		}
		const showGisFun = () => {
			showGisInformation.value = true
		}
		// 返回响应式状态
		return {
			utils,
			FileApi,
			constant,
			isLoading,
			dataTree: dataTreeApi,
			dataPointSelected,
			clickShowTaskPointBtn,
			clickDoStepConfirmBtn,
			clickCloseAndSaveBtn,
			clickRemoteSupportBtn,
			//左侧用户
			userInfo,
			userActivePosIndex,
			userShowVideoStatus: computed(() => userActivePosIndex.value === 0), //维修师摄像头
			clickChangePositionTab,
			clickSetUserInPlaceBtn, //就位
			//处理组件
			showChangeList,
			showCloseForm,
			dialogState,
			handleChangeSelected,
			showGisInformation,
			showGisFun,
			gisData,
			treeData
		}
	}
})
app.use(ElementPlus)

// 调试：检查图标是否正确加载
console.log("ElementPlusIconsVue:", window.ElementPlusIconsVue)
if (window.ElementPlusIconsVue) {
	const availableIcons = Object.keys(window.ElementPlusIconsVue)
	console.log("Available icons count:", availableIcons.length)
	console.log("Available icons:", availableIcons)
	console.log(
		"ChatLineSquare available:",
		!!window.ElementPlusIconsVue.ChatLineSquare
	)
	console.log("Message available:", !!window.ElementPlusIconsVue.Message)
	console.log(
		"ChatDotSquare available:",
		!!window.ElementPlusIconsVue.ChatDotSquare
	)

	// 注册所有图标
	for (const [key, component] of Object.entries(window.ElementPlusIconsVue)) {
		app.component(key, component)
	}
} else {
	console.error(
		"ElementPlusIconsVue not found! Check if the CDN is loaded correctly."
	)
}

app.mount("#app")
