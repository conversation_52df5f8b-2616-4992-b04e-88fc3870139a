<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>应急处置可视化看板</title>
    <link rel="stylesheet" href="./unpkg.com/element-plus@2.10.4/dist/index.css">

    <!-- Vue 3 生产环境版本 -->
    <script src="./unpkg.com/vue@3.5.17/dist/vue.global.prod.js"></script>
    <!-- Element Plus 完整包 -->
    <script src="./unpkg.com/element-plus@2.3.3/dist/index.full.min.js"></script>
    <!-- 添加axios CDN -->
    <script src="./unpkg.com/axios@1.10.0/dist/axios.min.js"></script>
    <!-- icons-vue 完整包 -->
    <script src="./unpkg.com/@element-plus/icons-vue@2.3.1/dist/index.iife.min.js"></script>
    <link rel="stylesheet" href="./css/index.css">
		<script src="./js/utils/amap-loader.js"></script>
    <style>
        /*弹窗提示*/

    </style>
</head>
<body>
<div id="app">
    <el-dialog v-model="dialogState.visible"
               width="20%"
               :close-on-click-modal="false"
               :close-on-press-escape="false"
               @close="dialogState.visible = false">
			<div style="display: flex;align-items: center">
				<img src="./icon/warning.png" alt="" style="width: 40px;height: 40px;">
				<div style="margin-left: 10px">
					<div style="font-weight: bold;font-size: 14px">系统提示</div>
					<p style="font-size: 12px">{{ dialogState.config.message }}</p>
				</div>

			</div>
			<div style="text-align: right; margin-top: 20px">
				<template v-for="btn in dialogState.config.buttons" :key="btn.action">
					<el-button
						v-if="btn.show"
						:type="btn.type"
						@click="btn.handler"
						:icon="btn.icon"
						style="margin: 0 10px;background: rgb(0,82,152);color: #fff;font-size: 12px">
						{{ btn.text }}
					</el-button>
				</template>
			</div>

    </el-dialog>

    <div v-if="isLoading" class="loading-wrapper">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <span>加载中...</span>
        </div>
        <div class="fade-overlay"></div>
    </div>
    <header class="header">
        <div class="header-left">{{ dataTree.flowTitle }} 【{{ dataTree.disposalLevel }}】</div>
        <div class="header-center">应急追踪树</div>
        <div class="header-right">{{ dataTree.systemTime }}</div>
    </header>

    <main class="app">
        <aside class="panel">
            <div class="process-container">
                <!--左上-流程-->
                <div>
                    <div v-for="(process, index) in dataTree.processTreeFlow" :key="index" class="process-step-box">
                        <div :class="['process-step',{'completed': process.pStatus == 2,'active': process.pStatus == 1}]">
                            <div>
                                <div class="process-name">{{ process.name }}</div>
                            </div>
                            <img :src="`./icon/status_arrow-${process.pStatus}.png`" v-if="process.pStatus != 0"  style="margin-left: 10px;">
                        </div>
                        <div v-if="index < dataTree.processTreeFlow.length -1">
                            <img :src="`./icon/triangle-${process.pStatus}.png`">
                        </div>
                    </div>
                </div>

                <section class="personnel-container">
                    <div class="personnel-group">
                        <div class="category-title">
                            <img src="./icon/user.png" class="user-icon"> 人员定位
                        </div>
                        <div v-for="(group, index) in userInfo.positionList" :key="group.id" class="personnel-tab" @click="clickChangePositionTab(index)" >
                            <div :class="['category', { userActivePosIndex: userActivePosIndex === index }]">
                                {{ group.name }}
                            </div>
                        </div>
                        <img src="./icon/map.png" alt="地图" @click="showGisFun">
                    </div>

                    <div class="personnel-list">
                        <div v-for="person in userInfo.userList" :key="person.id" class="list-personnel-item">
                            <div class="personnel-name">
                                <img src="./icon/user.png" class="user-icon"> {{ person.empName }}
                            </div>
                            <div class="personnel-title">
                                <img v-if="userShowVideoStatus" src="./icon/video.png" alt="摄像头"> {{ person.roleName }}
                            </div>
                            <div v-if="person.inPosition == 1" class="personnel-status-online" :class="userActivePosIndex != 0 ? 'personnel-online':''"> 已就位 </div>
                            <div v-if="person.inPosition == 0" class="personnel-status-outline"
                                 :class="userActivePosIndex != 0 ? 'personnel-outline':''"
                                 @click="clickSetUserInPlaceBtn(userActivePosIndex, person.id)"> 未就位 </div>
                        </div>
                    </div>
                </section>

            </div>
        </aside>

        <section class="panel" style="margin: 0 1.25rem;">
            <div class="task-panel">
                <div v-for="(process, pIndex) in dataTree.processTreeFlow" :key="pIndex" class="task-panel-box">
                    <div class="process-title" :id="'process-item-' + process.processId">
                        {{ process.name }}
<!--                        ({{ process.processId }})-->
                        <span class="process-time" v-if="process.processTime > 0"><img src="./icon/11.png" class="icon-time"> {{ utils.secondsFormatTime(process.processTime) }}</span>
                    </div>
                    <div v-if="process.taskTreeVos?.length">
                        <!--TODO 控制完成 边框变绿 缺少 task 完成状态 task.pStatus == 2-->
                        <div v-for="(task, tIndex) in process.taskTreeVos" :key="`task-${tIndex}`" :class="['process-cont',{'completed': task.tStatus == 2,'active': task.tStatus == 1}]">
                            <div class="task-box">
                                <div class="task-item">
                                    {{ task.taskContent }}
<!--                                    ({{ task.taskId }})-->
                                </div>
                                <div class="task-view" @click="clickShowTaskPointBtn(task.taskPoint, task)"><img src="./icon/21.png">查看要素</div>
                                <div class="task-time" v-if="task.taskTime > 0"><img src="./icon/11.png" class="icon-time"> {{ utils.secondsFormatTime(task.taskTime) }}</div>
                            </div>
                            <div v-for="(step, sIndex) in task.taskStep" :key="`step-${sIndex}`" class="task-cont">
                                <div class="operator">
                                    <template v-if="utils.shouldShowRole(sIndex, step, task.taskStep)">
                                        <span class="operator-icon"><img src="./icon/5.png"></span>
                                        <span class="role-name">{{ step.roleName }}</span>
                                    </template>
                                    <span class="arrow-icon"><img src="./icon/7.png"></span>
                                </div>
                                <div class="step-item-box">
                                    <div class="step-item">
                                        <div :class="utils.stepTitleClass(step)">
                                            {{ step.stepContent }}
<!--                                            ({{ step.stepId }})-->
<!--                                            {{ utils.secondsFormatTime(step.executeTime) }}-->
                                            <span v-if="step.executeStatus" :class="utils.statusClass(step)"> {{ utils.statusText(step.executeStatus) }} </span>
                                        </div>
                                        <div class="action-buttons" v-if="step.executeStatus == 1">
                                            <span class="step-btn" @click="clickDoStepConfirmBtn(step.stepId,3)"><img src="./icon/12.png">跳过</span>
                                            <span class="step-btn" @click="clickDoStepConfirmBtn(step.stepId,2)"><img src="./icon/2.png">确认</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div v-else>
                        <div class="process-cont">
                            <div class="task-box">
                                <div class="task-item">
                                    -
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <aside class="panel">
            <div class="panel-time-box">
                <p class="panel-time-title"><img src="./icon/3.png" class="user-icon">累计用时</p>
                <div class="panel-time">
                    {{ dataTree.executeTimeFormat }}
                </div>
            </div>
            <div class="panel-radius">
                <div class="radius-box">
                    <div>
                        <img src="./icon/17.png">
                        视频录制中
                    </div>
                    <div class="track">
                        <div class="slider left"></div>
                        <div class="slider right"></div>
                    </div>
                </div>
                <div class="radius-box">
                    <div>
                        <img src="./icon/77.png">
                        流程进行中
                    </div>
                    <el-progress :percentage="dataTree._progress" :show-text="false" color="#1270d6"/>
                </div>
            </div>
            <div class="panel-btn-box">
                <el-button @click="clickRemoteSupportBtn(dataTree.id)">
                    <img src="./icon/to_chat.png">
                    远程支持
                </el-button>
                <el-button @click="clickCloseAndSaveBtn()">
                    <img src="./icon/6.png">
                    关闭存档
                </el-button>
            </div>
            <div class="panel-perpel">
                <div class="perpel-title">
                    <div>
                        <img src="./icon/peper.png">
                        图纸资料（{{ dataTree.drawInfo.length }}）
                    </div>
                    <div>PDF</div>
                </div>
                <div v-for="(item,index) in dataTree.drawInfo" :key="index" class="perple-cont">
                    <div class="perple-cont-box">
                        <div>
                            <img src="./icon/37.png">
                        </div>
                        <div style="overflow: hidden;cursor: pointer" @click="FileApi.downloadFile(item.drawUrl)">
                            <div class="multiline-div">{{ item.title }}</div>
                            <div style="font-size: 1rem;" class="multiline-div">图纸编号：{{item.code}}</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="panel-task">
                <div class="task-title">
                    <div>
                        <img src="./icon/66.png">
                        {{ dataPointSelected.processItem?.name.replace(/^P\d+\s/, '') }}-{{ dataPointSelected.taskItem?.taskContent.split(' ')[0] }}-任务要素
										</div>
                </div>
                <div v-if="dataPointSelected.taskPoint?.length" class="panel-task-cont">
                    <div v-for="(item,index) in dataPointSelected.taskPoint" :key="index" class="task-cont-text">
                        {{ item.pointContent }}
                    </div>
                </div>
            </div>
        </aside>
    </main>
    <list-change-drawer
            v-model="showChangeList"
            :post-data="{ id: dataTree.id }"
            @selected="handleChangeSelected"
            @close="showChangeList = false"></list-change-drawer>
    <form-close-drawer
            v-model="showCloseForm"
            :post-data="dataTree"
            @close="showCloseForm = false"></form-close-drawer>
		<el-drawer v-model="showGisInformation" direction="rtl" size="90%" v-if="showGisInformation">
		 <gis-information :preview-data="gisData.data.rows" :tree-data="treeData.data" ></gis-information>
		</el-drawer>
</div>
<script type="module" src="./js/app/index.js"></script>
</body>
</html>
