###
# 自定义的环境变量（命名必须以 VITE_ 开头）
## 系统标题
VITE_SYS_TITLE = '供电远程支持系统'
VITE_SYS_ENTITLE = 'Power Supply Remote Support System'
VITE_SYS_VERSION = '测试版'
VITE_SYS_APP_MODE = 'BASELINE'
VITE_SYS_LOGO = ''

## 后端接口公共路径（如果解决跨域问题采用反向代理就只需写公共路径）
VITE_BASE_API = http://172.16.20.224:9401
#VITE_BASE_API = http://192.168.31.10:9401

## 系统通用上传地址
VITE_APP_BASE_UPLOAD_URL = /pitaya/system/common/upload

## 系统通用下载地址
VITE_APP_BASE_DOWNLOAD_URL = /pitaya/system/common/static/

## 路由模式 hash 或 html5
VITE_ROUTER_HISTORY = 'hash'

## 开发环境地址前缀（一般 '/'，'./' 都可以）
VITE_PUBLIC_PATH = '/'

## webscoket地址
VITE_APP_BASE_WS = http://172.16.20.224:9401/websocket

## webscoket 失败重试次数
VITE_APP_BASE_WS_Number_Failures = 5

## websocket 心跳时间
VITE_APP_BASE_WS_TIMER = 5000

##高德地图key
VITE_AMAP_KEY = "7d71c09f69eab6fb826909264fb32d97"

## 录屏软件地址
VITE_RPSTSS_APP_PATH = "rpstssApp://"
